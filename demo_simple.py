#!/usr/bin/env python3
"""
Simplified PosterCraft Demo
A minimal version that focuses on FLUX image generation
"""

import gradio as gr
import torch
from diffusers import FluxPipeline
from PIL import Image
import os
import logging
from pathlib import Path

# Configuration with fallback models
FLUX_MODELS = [
    "black-forest-labs/FLUX.1-schnell",  # Open model, no auth required
    "stabilityai/stable-diffusion-xl-base-1.0",  # Fallback
]

# Try to import local config
try:
    from local_config import get_model_paths
    local_paths = get_model_paths()
    if local_paths.get('flux'):
        FLUX_MODELS.insert(0, local_paths['flux'])
        print(f"Found local FLUX model: {local_paths['flux']}")
except ImportError:
    print("Local config not available, using default models")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def try_load_flux_model(model_list):
    """Try to load a FLUX model from a list of candidates"""
    for model_path in model_list:
        try:
            logger.info(f"Trying to load FLUX model: {model_path}")
            
            # Check if it's a local path
            if os.path.exists(str(model_path)):
                logger.info(f"Loading from local path: {model_path}")
            
            pipe = FluxPipeline.from_pretrained(
                model_path,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None
            )
            
            logger.info(f"✅ Successfully loaded FLUX model: {model_path}")
            return pipe
            
        except Exception as e:
            logger.warning(f"Failed to load FLUX model from {model_path}: {e}")
            continue
    
    raise Exception("Could not load any FLUX model from the provided list")

class SimplePosterGenerator:
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        if self.device == "cpu":
            logger.warning("CUDA not available. Falling back to CPU.")
        
        # Load FLUX model
        try:
            logger.info("Loading FLUX pipeline...")
            self.pipe = try_load_flux_model(FLUX_MODELS)
            
            # Move to device if needed
            if self.device == "cuda" and hasattr(self.pipe, 'to'):
                self.pipe = self.pipe.to(self.device)
                
            logger.info("✅ FLUX pipeline loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load FLUX pipeline: {e}")
            self.pipe = None
    
    def generate_poster(self, prompt: str, **kwargs) -> Image.Image:
        """Generate a poster image"""
        if not self.pipe:
            raise Exception("FLUX pipeline not available")
        
        try:
            # Generate image
            result = self.pipe(
                prompt=prompt,
                height=kwargs.get('height', 1024),
                width=kwargs.get('width', 768),
                num_inference_steps=kwargs.get('steps', 4),  # FLUX.1-schnell works well with 4 steps
                guidance_scale=kwargs.get('guidance', 0.0),  # FLUX.1-schnell doesn't need guidance
                generator=torch.Generator(device=self.device).manual_seed(kwargs.get('seed', 42))
            )
            
            return result.images[0]
        except Exception as e:
            logger.error(f"Error generating poster: {e}")
            raise

def create_interface():
    """Create the Gradio interface"""
    try:
        generator = SimplePosterGenerator()
    except Exception as e:
        logger.error(f"Failed to initialize PosterGenerator: {e}")
        return None
    
    def generate_wrapper(prompt, height, width, steps, guidance, seed):
        try:
            if not prompt.strip():
                return None, "Please enter a prompt"
            
            image = generator.generate_poster(
                prompt=prompt,
                height=int(height),
                width=int(width),
                steps=int(steps),
                guidance=float(guidance),
                seed=int(seed)
            )
            
            return image, "Image generated successfully!"
        except Exception as e:
            return None, f"Error: {str(e)}"
    
    # Create interface
    with gr.Blocks(title="PosterCraft - Simple") as demo:
        gr.Markdown("# 🎨 PosterCraft (Simplified)")
        gr.Markdown("Generate beautiful posters using FLUX models")
        
        with gr.Row():
            with gr.Column():
                prompt = gr.Textbox(
                    label="Prompt",
                    placeholder="Describe the poster you want to create...",
                    lines=3,
                    value="A beautiful movie poster with vibrant colors and modern typography"
                )
                
                with gr.Row():
                    height = gr.Slider(512, 1536, 1024, step=64, label="Height")
                    width = gr.Slider(512, 1536, 768, step=64, label="Width")
                
                with gr.Row():
                    steps = gr.Slider(1, 20, 4, step=1, label="Steps")
                    guidance = gr.Slider(0.0, 10.0, 0.0, step=0.1, label="Guidance")
                    seed = gr.Number(42, label="Seed")
                
                generate_btn = gr.Button("🎨 Generate Poster", variant="primary")
            
            with gr.Column():
                output_image = gr.Image(label="Generated Poster")
                status = gr.Textbox(label="Status", interactive=False)
        
        generate_btn.click(
            generate_wrapper,
            inputs=[prompt, height, width, steps, guidance, seed],
            outputs=[output_image, status]
        )
        
        # Add examples
        gr.Examples(
            examples=[
                ["A vintage travel poster for Tokyo with cherry blossoms", 1024, 768, 4, 0.0, 42],
                ["Modern minimalist poster for a tech conference", 1024, 768, 4, 0.0, 123],
                ["Retro movie poster in 1980s style with neon colors", 1024, 768, 4, 0.0, 456],
            ],
            inputs=[prompt, height, width, steps, guidance, seed],
        )
    
    return demo

def main():
    """Main function"""
    demo = create_interface()
    if demo:
        logger.info("🚀 Starting PosterCraft interface...")
        demo.launch(
            server_name="0.0.0.0",
            server_port=7862,
            share=True,
            show_error=True
        )
    else:
        logger.error("Failed to create interface")

if __name__ == "__main__":
    main()