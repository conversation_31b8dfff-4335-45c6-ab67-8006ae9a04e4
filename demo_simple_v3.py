#!/usr/bin/env python3
"""
PosterCraft - Simple Gradio 3.x Version
Minimal working poster generation interface
"""

import gradio as gr
import sys
import time

def get_system_info():
    """Get system information"""
    info = f"""🎨 PosterCraft System Status
========================================
Python: {sys.version.split()[0]}
Gradio: {gr.__version__}
Status: ✅ Running
Time: {time.strftime('%Y-%m-%d %H:%M:%S')}

📁 Features Available:
✅ Demo poster generation
✅ Prompt enhancement
✅ System information
✅ User interface

🎯 Ready to use!"""
    return info

def enhance_prompt(basic_prompt):
    """Enhanced prompt generation"""
    if not basic_prompt.strip():
        return "Please enter a basic poster idea!"
    
    enhanced = f"""Professional poster design: {basic_prompt}

🎨 Enhanced Elements:
• Bold, eye-catching typography
• Vibrant color palette
• Clear visual hierarchy
• Professional layout
• High-resolution quality"""
    
    if "music" in basic_prompt.lower():
        enhanced += "\n• Dynamic musical elements\n• Rhythm-inspired design"
    elif "movie" in basic_prompt.lower():
        enhanced += "\n• Cinematic atmosphere\n• Dramatic composition"
    elif "event" in basic_prompt.lower():
        enhanced += "\n• Professional appearance\n• Clear information layout"
    
    return enhanced

def generate_demo_poster(prompt, width, height, steps, guidance, seed):
    """Generate demo poster"""
    if not prompt.strip():
        return None, "❌ Please enter a prompt!"
    
    try:
        from PIL import Image, ImageDraw
        
        # Create image
        img = Image.new('RGB', (int(width), int(height)), color='lightblue')
        draw = ImageDraw.Draw(img)
        
        # Add gradient effect
        for y in range(int(height)):
            r = int(100 + (y / height) * 155)
            g = int(150 + (y / height) * 105)
            b = int(200 + (y / height) * 55)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # Add text
        draw.text((50, 50), "POSTERCRAFT DEMO", fill='white')
        draw.text((50, 100), f"Prompt: {prompt[:50]}{'...' if len(prompt) > 50 else ''}", fill='black')
        draw.text((50, 150), f"Size: {width}x{height} | Steps: {steps} | Seed: {seed}", fill='darkblue')
        draw.text((50, height-100), "✅ Demo generated successfully!", fill='red')
        draw.text((50, height-70), "Note: This is a demo placeholder.", fill='red')
        
        return img, f"✅ Demo poster generated at {time.strftime('%H:%M:%S')}"
        
    except Exception as e:
        return None, f"❌ Error: {str(e)}"

# Create simple interface
def create_poster_tab():
    """Create poster generation tab"""
    with gr.Column():
        gr.Markdown("## Create Your Poster")
        
        prompt_input = gr.Textbox(
            label="Poster Description",
            placeholder="Describe your poster...",
            lines=3,
            value="Modern tech conference poster"
        )
        
        with gr.Row():
            width_slider = gr.Slider(512, 1536, 1024, step=64, label="Width")
            height_slider = gr.Slider(512, 1536, 768, step=64, label="Height")
        
        with gr.Row():
            steps_slider = gr.Slider(1, 20, 4, step=1, label="Steps")
            guidance_slider = gr.Slider(0.0, 2.0, 0.0, step=0.1, label="Guidance")
        
        seed_input = gr.Number(label="Seed", value=42, precision=0)
        generate_btn = gr.Button("🎨 Generate Poster")
        
        image_output = gr.Image(label="Generated Poster")
        status_output = gr.Textbox(label="Status", lines=2)
        
        generate_btn.click(
            generate_demo_poster,
            inputs=[prompt_input, width_slider, height_slider, steps_slider, guidance_slider, seed_input],
            outputs=[image_output, status_output]
        )

def create_enhancer_tab():
    """Create prompt enhancer tab"""
    with gr.Column():
        gr.Markdown("## AI Prompt Enhancement")
        
        basic_input = gr.Textbox(
            label="Basic Idea",
            placeholder="Enter a simple poster idea...",
            lines=3,
            value="Music festival poster"
        )
        enhance_btn = gr.Button("✨ Enhance")
        enhanced_output = gr.Textbox(label="Enhanced Prompt", lines=10)
        
        enhance_btn.click(enhance_prompt, inputs=basic_input, outputs=enhanced_output)

def create_info_tab():
    """Create system info tab"""
    with gr.Column():
        gr.Markdown("## System Status")
        
        info_btn = gr.Button("🔍 Check Status")
        info_output = gr.Textbox(label="System Information", lines=12)
        
        info_btn.click(get_system_info, outputs=info_output)

# Main interface
with gr.Blocks(title="🎨 PosterCraft") as demo:
    gr.Markdown("# 🎨 PosterCraft - AI Poster Generator")
    gr.Markdown("Professional poster generation powered by AI")
    
    with gr.Tabs():
        with gr.TabItem("🎨 Poster Generator"):
            create_poster_tab()
        
        with gr.TabItem("✨ Prompt Enhancer"):
            create_enhancer_tab()
        
        with gr.TabItem("ℹ️ System Info"):
            create_info_tab()
        
        with gr.TabItem("📖 About"):
            gr.Markdown("""
            ## About PosterCraft
            
            **PosterCraft** is an AI-powered poster generation tool.
            
            ### Features
            - 🎨 AI poster generation
            - ✨ Smart prompt enhancement
            - 🎯 Customizable parameters
            - 💾 High-quality output
            
            ### Current Status
            - ✅ Interface operational
            - 🔄 Demo mode active
            - 📥 Ready to use
            
            ### Usage Tips
            1. Be specific in descriptions
            2. Use style keywords
            3. Experiment with parameters
            4. Try the prompt enhancer
            """)

if __name__ == "__main__":
    print("🚀 Starting PosterCraft Simple v3 Demo...")
    demo.launch(
        server_name="0.0.0.0",
        server_port=7867,
        share=True,
        show_error=True
    )
