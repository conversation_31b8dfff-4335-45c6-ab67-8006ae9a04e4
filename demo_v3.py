#!/usr/bin/env python3
"""
PosterCraft - Gradio 3.x Compatible Version
Stable poster generation interface
"""

import gradio as gr
import sys
import os
import time
from pathlib import Path

def get_system_info():
    """Get system information"""
    info = []
    info.append("🎨 PosterCraft System Status")
    info.append("=" * 40)
    info.append(f"Python: {sys.version.split()[0]}")
    
    try:
        import torch
        info.append(f"PyTorch: {torch.__version__}")
        info.append(f"CUDA Available: {torch.cuda.is_available()}")
    except ImportError:
        info.append("PyTorch: Not available")
    
    try:
        import diffusers
        info.append(f"Diffusers: {diffusers.__version__}")
    except ImportError:
        info.append("Diffusers: Not available")
    
    try:
        import transformers
        info.append(f"Transformers: {transformers.__version__}")
    except ImportError:
        info.append("Transformers: Not available")
    
    # Check models
    info.append("\n📁 Model Status:")
    qwen_path = Path.home() / ".cache/huggingface/hub/models--Qwen--Qwen2.5-3B"
    flux_path = Path.home() / ".cache/huggingface/hub/models--black-forest-labs--FLUX.1-schnell"
    
    info.append(f"Qwen2.5-3B: {'✅ Available' if qwen_path.exists() else '❌ Not found'}")
    info.append(f"FLUX.1-schnell: {'✅ Available' if flux_path.exists() else '❌ Not found'}")
    
    return "\n".join(info)

def enhance_prompt(basic_prompt):
    """Enhanced prompt generation"""
    if not basic_prompt.strip():
        return "Please enter a basic poster idea!"
    
    enhanced = f"Professional poster design: {basic_prompt}\n\n"
    enhanced += "🎨 Enhanced Elements:\n"
    enhanced += "• Bold, eye-catching typography\n"
    enhanced += "• Vibrant color palette\n"
    enhanced += "• Clear visual hierarchy\n"
    enhanced += "• Professional layout\n"
    enhanced += "• High-resolution quality\n"
    
    # Add specific suggestions
    if "music" in basic_prompt.lower():
        enhanced += "• Dynamic musical elements\n"
        enhanced += "• Rhythm-inspired design\n"
    elif "movie" in basic_prompt.lower():
        enhanced += "• Cinematic atmosphere\n"
        enhanced += "• Dramatic composition\n"
    elif "event" in basic_prompt.lower():
        enhanced += "• Professional appearance\n"
        enhanced += "• Clear information layout\n"
    
    return enhanced

def generate_demo_poster(prompt, width, height, steps, guidance, seed):
    """Generate demo poster"""
    if not prompt.strip():
        return None, "❌ Please enter a prompt!"
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create image
        img = Image.new('RGB', (int(width), int(height)), color='lightblue')
        draw = ImageDraw.Draw(img)
        
        # Add gradient effect
        for y in range(int(height)):
            r = int(100 + (y / height) * 155)
            g = int(150 + (y / height) * 105)
            b = int(200 + (y / height) * 55)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # Add text
        title = "POSTERCRAFT DEMO"
        draw.text((50, 50), title, fill='white')
        
        prompt_text = f"Prompt: {prompt[:50]}{'...' if len(prompt) > 50 else ''}"
        draw.text((50, 100), prompt_text, fill='black')
        
        params = f"Size: {width}x{height} | Steps: {steps} | Seed: {seed}"
        draw.text((50, 150), params, fill='darkblue')
        
        # Add status
        status_text = "✅ Demo generated successfully!"
        draw.text((50, height-100), status_text, fill='red')
        
        note = "Note: This is a demo placeholder."
        draw.text((50, height-70), note, fill='red')
        
        return img, f"✅ Demo poster generated at {time.strftime('%H:%M:%S')}"
        
    except Exception as e:
        return None, f"❌ Error: {str(e)}"

# Create interface using Gradio 3.x syntax
def create_interface():
    with gr.Blocks(title="🎨 PosterCraft") as demo:
        
        gr.Markdown("# 🎨 PosterCraft - AI Poster Generator")
        gr.Markdown("Professional poster generation powered by AI")
        
        with gr.Tab("🎨 Poster Generator"):
            gr.Markdown("## Create Your Poster")
            
            with gr.Row():
                with gr.Column():
                    prompt_input = gr.Textbox(
                        label="Poster Description",
                        placeholder="Describe your poster...",
                        lines=3,
                        value="Modern tech conference poster"
                    )
                    
                    with gr.Row():
                        width_slider = gr.Slider(512, 1536, 1024, step=64, label="Width")
                        height_slider = gr.Slider(512, 1536, 768, step=64, label="Height")
                    
                    with gr.Row():
                        steps_slider = gr.Slider(1, 20, 4, step=1, label="Steps")
                        guidance_slider = gr.Slider(0.0, 2.0, 0.0, step=0.1, label="Guidance")
                    
                    seed_input = gr.Number(label="Seed", value=42, precision=0)
                    generate_btn = gr.Button("🎨 Generate Poster", variant="primary")
                
                with gr.Column():
                    image_output = gr.Image(label="Generated Poster", height=400)
                    status_output = gr.Textbox(label="Status", lines=2)
            
            generate_btn.click(
                generate_demo_poster,
                inputs=[prompt_input, width_slider, height_slider, steps_slider, guidance_slider, seed_input],
                outputs=[image_output, status_output]
            )
        
        with gr.Tab("✨ Prompt Enhancer"):
            gr.Markdown("## AI Prompt Enhancement")
            
            with gr.Row():
                with gr.Column():
                    basic_input = gr.Textbox(
                        label="Basic Idea",
                        placeholder="Enter a simple poster idea...",
                        lines=3,
                        value="Music festival poster"
                    )
                    enhance_btn = gr.Button("✨ Enhance", variant="primary")
                
                with gr.Column():
                    enhanced_output = gr.Textbox(
                        label="Enhanced Prompt",
                        lines=10
                    )
            
            enhance_btn.click(enhance_prompt, inputs=basic_input, outputs=enhanced_output)
        
        with gr.Tab("ℹ️ System Info"):
            gr.Markdown("## System Status")
            
            info_btn = gr.Button("🔍 Check Status", variant="secondary")
            info_output = gr.Textbox(label="System Information", lines=12)
            
            info_btn.click(get_system_info, outputs=info_output)
        
        with gr.Tab("📖 About"):
            gr.Markdown("""
            ## About PosterCraft
            
            **PosterCraft** is an AI-powered poster generation tool.
            
            ### Features
            - 🎨 AI poster generation
            - ✨ Smart prompt enhancement
            - 🎯 Customizable parameters
            - 💾 High-quality output
            
            ### Current Status
            - ✅ Interface operational
            - 🔄 Demo mode active
            - 📥 Models downloaded
            
            ### Usage Tips
            1. Be specific in descriptions
            2. Use style keywords
            3. Experiment with parameters
            4. Try the prompt enhancer
            """)
    
    return demo

if __name__ == "__main__":
    print("🚀 Starting PosterCraft v3 Demo...")
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7866,
        share=True,
        show_error=True
    )
