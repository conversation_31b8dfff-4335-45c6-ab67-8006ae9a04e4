#!/usr/bin/env python3
"""
Working PosterCraft Demo - Simplified version that actually works
"""

import gradio as gr
import torch
import sys
import os
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_system_info():
    """Get basic system information"""
    info = []
    info.append(f"Python version: {sys.version}")
    info.append(f"PyTorch version: {torch.__version__}")
    info.append(f"CUDA available: {torch.cuda.is_available()}")
    info.append(f"Current working directory: {os.getcwd()}")
    
    try:
        import diffusers
        info.append(f"Diffusers version: {diffusers.__version__}")
    except ImportError:
        info.append("Diffusers: Not available")
    
    try:
        import transformers
        info.append(f"Transformers version: {transformers.__version__}")
    except ImportError:
        info.append("Transformers: Not available")
    
    try:
        import protobuf
        info.append(f"Protobuf: Available")
    except ImportError:
        info.append("Protobuf: Not available")
    
    return "\n".join(info)

def simple_text_generator(prompt):
    """Simple text generation function"""
    if not prompt:
        return "Please enter a prompt!"
    
    # Simple text processing
    enhanced_prompt = f"Enhanced poster prompt: {prompt}"
    enhanced_prompt += "\n\nSuggested elements:"
    enhanced_prompt += "\n- Bold typography"
    enhanced_prompt += "\n- Vibrant colors"
    enhanced_prompt += "\n- Clear composition"
    enhanced_prompt += "\n- Professional layout"
    
    return enhanced_prompt

def mock_image_generator(prompt, width, height, steps, guidance):
    """Mock image generation function"""
    if not prompt:
        return None, "Please enter a prompt!"
    
    # Create a simple placeholder image
    try:
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # Create a colorful gradient background
        img = Image.new('RGB', (int(width), int(height)), color='lightblue')
        draw = ImageDraw.Draw(img)
        
        # Add some text
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        # Add title
        title = "PosterCraft Demo"
        if font:
            draw.text((50, 50), title, fill='darkblue', font=font)
        else:
            draw.text((50, 50), title, fill='darkblue')
        
        # Add prompt text (truncated)
        prompt_text = prompt[:50] + "..." if len(prompt) > 50 else prompt
        if font:
            draw.text((50, 100), f"Prompt: {prompt_text}", fill='black', font=font)
        else:
            draw.text((50, 100), f"Prompt: {prompt_text}", fill='black')
        
        # Add parameters
        params_text = f"Size: {width}x{height}, Steps: {steps}, Guidance: {guidance}"
        if font:
            draw.text((50, 150), params_text, fill='gray', font=font)
        else:
            draw.text((50, 150), params_text, fill='gray')
        
        # Add note
        note = "Note: This is a demo placeholder."
        note2 = "Real AI generation requires model setup."
        if font:
            draw.text((50, height-100), note, fill='red', font=font)
            draw.text((50, height-70), note2, fill='red', font=font)
        else:
            draw.text((50, height-100), note, fill='red')
            draw.text((50, height-70), note2, fill='red')
        
        return img, "Demo image generated successfully!"
        
    except Exception as e:
        return None, f"Error creating demo image: {str(e)}"

def create_interface():
    """Create the Gradio interface"""
    
    with gr.Blocks(title="PosterCraft - Working Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🎨 PosterCraft - Working Demo")
        gr.Markdown("This is a working demonstration of the PosterCraft interface.")
        
        with gr.Tab("System Info"):
            gr.Markdown("## System Information")
            info_btn = gr.Button("Get System Info", variant="primary")
            info_output = gr.Textbox(
                label="System Information", 
                lines=10, 
                max_lines=15,
                interactive=False
            )
            info_btn.click(get_system_info, outputs=info_output)
        
        with gr.Tab("Text Enhancement"):
            gr.Markdown("## Prompt Enhancement")
            gr.Markdown("Enter a basic poster idea and get an enhanced prompt.")
            
            with gr.Row():
                with gr.Column():
                    text_input = gr.Textbox(
                        label="Basic Poster Idea",
                        placeholder="e.g., Music festival poster",
                        lines=3
                    )
                    text_btn = gr.Button("Enhance Prompt", variant="primary")
                
                with gr.Column():
                    text_output = gr.Textbox(
                        label="Enhanced Prompt",
                        lines=8,
                        interactive=False
                    )
            
            text_btn.click(simple_text_generator, inputs=text_input, outputs=text_output)
        
        with gr.Tab("Image Generation"):
            gr.Markdown("## Poster Generation (Demo)")
            gr.Markdown("This tab shows the interface layout. Real generation requires model setup.")
            
            with gr.Row():
                with gr.Column(scale=1):
                    prompt_input = gr.Textbox(
                        label="Poster Prompt",
                        placeholder="Describe your poster...",
                        lines=4
                    )
                    
                    with gr.Row():
                        width_slider = gr.Slider(
                            minimum=512, maximum=1536, value=1024, step=64,
                            label="Width"
                        )
                        height_slider = gr.Slider(
                            minimum=512, maximum=1536, value=768, step=64,
                            label="Height"
                        )
                    
                    steps_slider = gr.Slider(
                        minimum=1, maximum=20, value=4, step=1,
                        label="Steps"
                    )
                    
                    guidance_slider = gr.Slider(
                        minimum=0.0, maximum=2.0, value=0.0, step=0.1,
                        label="Guidance Scale"
                    )
                    
                    generate_btn = gr.Button("🎨 Generate Demo Poster", variant="primary")
                
                with gr.Column(scale=2):
                    image_output = gr.Image(label="Generated Poster", height=400)
                    status_output = gr.Textbox(label="Status", lines=2, interactive=False)
            
            generate_btn.click(
                mock_image_generator,
                inputs=[prompt_input, width_slider, height_slider, steps_slider, guidance_slider],
                outputs=[image_output, status_output]
            )
        
        with gr.Tab("About"):
            gr.Markdown("""
            ## About PosterCraft
            
            PosterCraft is an AI-powered poster generation tool that combines:
            - **FLUX.1** models for high-quality image generation
            - **Qwen** models for intelligent prompt enhancement
            - **Gradio** interface for easy interaction
            
            ### Current Status
            - ✅ Environment setup complete
            - ✅ Dependencies installed
            - ✅ Interface working
            - ⚠️ Model loading in progress
            
            ### Next Steps
            1. Complete model downloads
            2. Fix model loading issues
            3. Enable real AI generation
            
            ### Usage Tips
            - Start with simple, clear descriptions
            - Use specific art styles (vintage, modern, minimalist)
            - Include color preferences
            - Specify the poster type (movie, event, product)
            """)
    
    return demo

if __name__ == "__main__":
    logger.info("🚀 Starting PosterCraft Working Demo...")
    
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=True,
        show_error=True,
        debug=False
    )
