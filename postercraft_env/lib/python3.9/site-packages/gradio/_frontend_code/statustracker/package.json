{"name": "@gradio/statustracker", "version": "0.6.0", "description": "Gradio UI packages", "type": "module", "main": "./index.ts", "author": "", "license": "ISC", "main_changeset": true, "exports": {".": "./index.ts", "./package.json": "./package.json", "./interactive": "./interactive/index.ts", "./static": "./static/index.ts", "./example": "./example/index.ts"}, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/utils": "workspace:^"}, "devDependencies": {"@gradio/preview": "workspace:^"}}