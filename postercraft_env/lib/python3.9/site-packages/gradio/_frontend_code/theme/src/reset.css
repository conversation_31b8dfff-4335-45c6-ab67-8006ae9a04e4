.gradio-container,
*,
::before,
::after {
	box-sizing: border-box;
	border-width: 0;
	border-style: solid;
}

html {
	-webkit-text-size-adjust: 100%;
	line-height: 1.5;
	font-family: var(--font-sans);
	-moz-tab-size: 4;
	tab-size: 2;
}

body {
	margin: 0;
	line-height: inherit;
}

hr {
	border-top-width: 1px;
	height: 0;
	color: inherit;
}

abbr:where([title]) {
	text-decoration: underline dotted;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: inherit;
	font-size: inherit;
}

a {
	color: inherit;
	text-decoration: inherit;
}

b,
strong {
	font-weight: bolder;
}

code,
kbd,
samp,
pre {
	font-family: -var(--font-mono);
}

small {
	font-size: 80%;
}

sub,
sup {
	position: relative;
	vertical-align: baseline;
	font-size: 75%;
	line-height: 0;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

table {
	border-color: inherit;

	text-indent: 0;
}

button,
input,
optgroup,
select,
textarea {
	margin: 0;
	padding: 0;
	color: inherit;
	font-weight: inherit;
	font-size: 100%;
	line-height: inherit;
	font-family: inherit;
}

button,
select {
	text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
	-webkit-appearance: button;
	background-image: none;
	background-color: transparent;
}

:-moz-focusring {
	outline: auto;
}

:-moz-ui-invalid {
	box-shadow: none;
}

progress {
	vertical-align: baseline;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}

[type="search"] {
	-webkit-appearance: textfield;
	outline-offset: -2px;
}

::-webkit-search-decoration {
	-webkit-appearance: none;
}

::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit;
}

summary {
	display: list-item;
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
	margin: 0;
}

fieldset {
	margin: 0;
	padding: 0;
}

legend {
	padding: 0;
}

ol,
ul,
menu {
	margin: 0;
	padding: 0;
}

textarea {
	resize: vertical;
}

input::placeholder,
textarea::placeholder {
	opacity: 1;
	color: --color-var(--color-grey-400);
}

button,
[role="button"] {
	cursor: pointer;
}

:disabled {
	cursor: default;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
	display: block;
	vertical-align: middle;
}

img,
video {
	max-width: 100%;
	height: auto;
	margin: 0;
}

[hidden] {
	display: none;
}

[type="text"],
[type="email"],
[type="url"],
[type="password"],
[type="number"],
[type="date"],
[type="datetime-local"],
[type="month"],
[type="search"],
[type="tel"],
[type="time"],
[type="week"],
[multiple],
textarea,
select {
	--tw-shadow: 0 0 #0000;
	appearance: none;
	border-width: 1px;
	border-color: #6b7280;
	border-radius: 0px;
	background-color: #fff;
	padding-top: 0.5rem;
	padding-right: 0.75rem;
	padding-bottom: 0.5rem;
	padding-left: 0.75rem;
	font-size: 1rem;
	line-height: 1.5rem;
}

[type="checkbox"],
[type="radio"] {
	color-adjust: exact;
	display: inline-block;
	flex-shrink: 0;
	vertical-align: middle;
	appearance: none;
	border-width: 1px;
	background-origin: border-box;

	padding: 0;
	width: 1rem;
	height: 1rem;
	color: #2563eb;
	user-select: none;
}
[type="checkbox"]:checked {
	background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

[type="radio"]:checked {
	background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

select {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
	background-position: right 0.5rem center;
	background-size: 1.5em 1.5em;
	background-repeat: no-repeat;
	padding-right: 2.5rem;
}

[type="checkbox"]:checked,
[type="radio"]:checked {
	background-position: center;
	background-size: 100% 100%;
	background-repeat: no-repeat;
}

[type="checkbox"]:checked:hover,
[type="checkbox"]:checked:focus,
[type="radio"]:checked:hover,
[type="radio"]:checked:focus {
	border-color: transparent;
}

[type="checkbox"]:focus-visible,
[type="checkbox"]:focus-visible,
[type="radio"]:focus-visible,
[type="radio"]:focus-visible {
	outline: none;
}
