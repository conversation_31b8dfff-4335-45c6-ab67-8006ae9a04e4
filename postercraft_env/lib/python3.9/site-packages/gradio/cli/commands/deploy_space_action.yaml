name: Run Python script

on:
  push:
    branches:
      - $branch

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'

    - name: Install Gradio
      run: python -m pip install gradio

    - name: Log in to Hugging Face
      run: python -c 'import huggingface_hub; huggingface_hub.login(token="${{ secrets.hf_token }}")'

    - name: Deploy to Spaces
      run: gradio deploy
