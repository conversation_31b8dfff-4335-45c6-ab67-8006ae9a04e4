from gradio.components.annotated_image import AnnotatedImage
from gradio.components.audio import Audio
from gradio.components.base import (
    Component,
    FormComponent,
    StreamingInput,
    StreamingOutput,
    _Keywords,
    component,
    get_component_instance,
)
from gradio.components.button import Button
from gradio.components.chatbot import <PERSON><PERSON><PERSON>, ChatMessage, MessageDict
from gradio.components.checkbox import Checkbox
from gradio.components.checkboxgroup import CheckboxGroup
from gradio.components.clear_button import ClearButton
from gradio.components.code import Code
from gradio.components.color_picker import ColorPicker
from gradio.components.dataframe import Dataframe
from gradio.components.dataset import Dataset
from gradio.components.datetime import DateTime
from gradio.components.download_button import DownloadButton
from gradio.components.dropdown import Dropdown
from gradio.components.duplicate_button import DuplicateButton
from gradio.components.fallback import Fallback
from gradio.components.file import File
from gradio.components.file_explorer import FileExplorer
from gradio.components.gallery import Gallery
from gradio.components.highlighted_text import HighlightedText
from gradio.components.html import HTML
from gradio.components.image import Image
from gradio.components.image_editor import ImageEditor
from gradio.components.json_component import JSO<PERSON>
from gradio.components.label import Label
from gradio.components.login_button import LoginButton
from gradio.components.logout_button import LogoutButton
from gradio.components.markdown import Markdown
from gradio.components.model3d import Model3D
from gradio.components.multimodal_textbox import MultimodalTextbox
from gradio.components.native_plot import BarPlot, LinePlot, NativePlot, ScatterPlot
from gradio.components.number import Number
from gradio.components.paramviewer import ParamViewer
from gradio.components.plot import Plot
from gradio.components.radio import Radio
from gradio.components.slider import Slider
from gradio.components.state import State
from gradio.components.textbox import Textbox
from gradio.components.timer import Timer
from gradio.components.upload_button import UploadButton
from gradio.components.video import Video
from gradio.layouts import Form

Text = Textbox
DataFrame = Dataframe
Highlightedtext = HighlightedText
Annotatedimage = AnnotatedImage
Highlight = HighlightedText
Checkboxgroup = CheckboxGroup
Json = JSON

__all__ = [
    "Audio",
    "BarPlot",
    "Button",
    "Chatbot",
    "ChatMessage",
    "ClearButton",
    "Component",
    "component",
    "get_component_instance",
    "_Keywords",
    "Checkbox",
    "CheckboxGroup",
    "Code",
    "ColorPicker",
    "Dataframe",
    "DataFrame",
    "Dataset",
    "DownloadButton",
    "DuplicateButton",
    "Fallback",
    "Form",
    "FormComponent",
    "Gallery",
    "HTML",
    "FileExplorer",
    "Image",
    "JSON",
    "Json",
    "Label",
    "LinePlot",
    "LoginButton",
    "LogoutButton",
    "Markdown",
    "MessageDict",
    "Textbox",
    "DateTime",
    "Dropdown",
    "Model3D",
    "File",
    "HighlightedText",
    "AnnotatedImage",
    "CheckboxGroup",
    "Text",
    "Highlightedtext",
    "Annotatedimage",
    "Highlight",
    "Checkboxgroup",
    "Number",
    "Plot",
    "Radio",
    "ScatterPlot",
    "Slider",
    "State",
    "Timer",
    "UploadButton",
    "Video",
    "StreamingInput",
    "StreamingOutput",
    "ImageEditor",
    "ParamViewer",
    "MultimodalTextbox",
    "NativePlot",
]
