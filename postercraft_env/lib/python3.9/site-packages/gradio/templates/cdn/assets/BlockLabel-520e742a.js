import{S as m,e as _,s as h,m as r,F as d,o as g,t as w,g as u,N as i,h as I,j as c,G as k,x as B,w as S,u as j,k as q,H as C}from"./index-7674dbb6.js";import"./Button-770df9ba.js";function F(s){let e,n,o,b,f,t;return o=new s[1]({}),{c(){e=r("label"),n=r("span"),d(o.$$.fragment),b=g(),f=w(s[0]),u(n,"class","svelte-1b6s6s"),u(e,"for",""),u(e,"data-testid","block-label"),u(e,"class","svelte-1b6s6s"),i(e,"hide",!s[2]),i(e,"sr-only",!s[2]),i(e,"float",s[4]),i(e,"hide-label",s[3])},m(a,l){I(a,e,l),c(e,n),k(o,n,null),c(e,b),c(e,f),t=!0},p(a,[l]){(!t||l&1)&&B(f,a[0]),(!t||l&4)&&i(e,"hide",!a[2]),(!t||l&4)&&i(e,"sr-only",!a[2]),(!t||l&16)&&i(e,"float",a[4]),(!t||l&8)&&i(e,"hide-label",a[3])},i(a){t||(S(o.$$.fragment,a),t=!0)},o(a){j(o.$$.fragment,a),t=!1},d(a){a&&q(e),C(o)}}}function G(s,e,n){let{label:o=null}=e,{Icon:b}=e,{show_label:f=!0}=e,{disable:t=!1}=e,{float:a=!0}=e;return s.$$set=l=>{"label"in l&&n(0,o=l.label),"Icon"in l&&n(1,b=l.Icon),"show_label"in l&&n(2,f=l.show_label),"disable"in l&&n(3,t=l.disable),"float"in l&&n(4,a=l.float)},[o,b,f,t,a]}class N extends m{constructor(e){super(),_(this,e,G,F,h,{label:0,Icon:1,show_label:2,disable:3,float:4})}}export{N as B};
//# sourceMappingURL=BlockLabel-520e742a.js.map
