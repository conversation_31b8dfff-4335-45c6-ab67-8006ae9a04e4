const VERSION_RE = new RegExp("3.50.0/", "g");function import_fix(mod, base) {const url =  new URL(mod, base); return import(`https://gradio.s3-us-west-2.amazonaws.com/3.50.0/${url.pathname?.startsWith('/') ? url.pathname.substring(1).replace(VERSION_RE, "") : url.pathname.replace(VERSION_RE, "")}`);}import{n as $,i as fn,a as un,l as dn,c as pn,d as mn,b as gn,S as oe,e as se,s as re,f as pe,g as u,h as b,j as p,k as v,m as T,o as F,t as O,p as ge,q as Ge,r as ne,u as C,v as le,w as P,x as G,y as hn,z as vn,A as bn,B as kn,C as je,D as Pe,E as ve,F as Q,G as K,H as Z,I as te,J as Ft,K as de,L as wn,_ as N,M as Ae,N as Le,O as he,P as En,Q as _e,R as ye,T as We,U as Je,V as yn,W as jn,X as An,Y as On,Z as Ln,$ as Tn,a0 as Pn,a1 as In,a2 as Rn,a3 as Qe,a4 as Dn,a5 as Vn,a6 as qn,a7 as Ke}from"./index-7674dbb6.js";import{c as Cn,f as Ze,B as Ve,a as Nn}from"./Button-770df9ba.js";function Sn(l,e,t,n){if(!e)return $;const i=l.getBoundingClientRect();if(e.left===i.left&&e.right===i.right&&e.top===i.top&&e.bottom===i.bottom)return $;const{delay:o=0,duration:s=300,easing:r=fn,start:_=un()+o,end:a=_+s,tick:c=$,css:f}=t(l,{from:e,to:i},n);let d=!0,g=!1,j;function A(){f&&(j=pn(l,0,1,s,o,r,f)),o||(g=!0)}function w(){f&&mn(l,j),d=!1}return dn(m=>{if(!g&&m>=_&&(g=!0),g&&m>=a&&(c(1,0),w()),!d)return!1;if(g){const E=m-_,L=0+1*r(E/s);c(L,1-L)}return!0}),A(),c(0,1),w}function zn(l){const e=getComputedStyle(l);if(e.position!=="absolute"&&e.position!=="fixed"){const{width:t,height:n}=e,i=l.getBoundingClientRect();l.style.position="absolute",l.style.width=t,l.style.height=n,Mn(l,i)}}function Mn(l,e){const t=l.getBoundingClientRect();if(e.left!==t.left||e.top!==t.top){const n=getComputedStyle(l),i=n.transform==="none"?"":n.transform;l.style.transform=`${i} translate(${e.left-t.left}px, ${e.top-t.top}px)`}}function Bn(l,{from:e,to:t},n={}){const i=getComputedStyle(l),o=i.transform==="none"?"":i.transform,[s,r]=i.transformOrigin.split(" ").map(parseFloat),_=e.left+e.width*s/t.width-(t.left+s),a=e.top+e.height*r/t.height-(t.top+r),{delay:c=0,duration:f=g=>Math.sqrt(g)*120,easing:d=Cn}=n;return{delay:c,duration:gn(f)?f(Math.sqrt(_*_+a*a)):f,easing:d,css:(g,j)=>{const A=j*_,w=j*a,m=g+j*e.width/t.width,E=g+j*e.height/t.height;return`transform: ${o} translate(${A}px, ${w}px) scale(${m}, ${E});`}}}function Un(l){let e,t;return{c(){e=pe("svg"),t=pe("path"),u(t,"stroke-linecap","round"),u(t,"stroke-linejoin","round"),u(t,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"viewBox","0 0 24 24"),u(e,"width","100%"),u(e,"height","100%"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"aria-hidden","true"),u(e,"stroke-width","2"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(n,i){b(n,e,i),p(e,t)},p:$,i:$,o:$,d(n){n&&v(e)}}}let Fn=class extends oe{constructor(e){super(),se(this,e,null,Un,re,{})}};function Hn(l){let e,t;return{c(){e=pe("svg"),t=pe("path"),u(t,"stroke-linecap","round"),u(t,"stroke-linejoin","round"),u(t,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"viewBox","0 0 24 24"),u(e,"width","100%"),u(e,"height","100%"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"aria-hidden","true"),u(e,"stroke-width","2"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(n,i){b(n,e,i),p(e,t)},p:$,i:$,o:$,d(n){n&&v(e)}}}class Gn extends oe{constructor(e){super(),se(this,e,null,Hn,re,{})}}function Wn(l){let e,t;return{c(){e=pe("svg"),t=pe("path"),u(t,"stroke-linecap","round"),u(t,"stroke-linejoin","round"),u(t,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"stroke-width","2"),u(e,"viewBox","0 0 24 24"),u(e,"width","100%"),u(e,"height","100%"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"aria-hidden","true"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(n,i){b(n,e,i),p(e,t)},p:$,i:$,o:$,d(n){n&&v(e)}}}class Jn extends oe{constructor(e){super(),se(this,e,null,Wn,re,{})}}function Qn(l){let e,t;return e=new Fn({}),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Kn(l){let e,t;return e=new Gn({}),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Zn(l){let e,t;return e=new Jn({}),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Yn(l){let e,t,n,i,o,s,r,_,a,c,f,d,g,j,A,w,m,E,L,k,y,R,q,M,J,V,fe,D;const H=[Zn,Kn,Qn],Y=[];function ce(I,x){return I[1]==="warning"?0:I[1]==="info"?1:I[1]==="error"?2:-1}return~(n=ce(l))&&(i=Y[n]=H[n](l)),{c(){e=T("div"),t=T("div"),i&&i.c(),s=F(),r=T("div"),_=T("div"),a=O(l[1]),f=F(),d=T("div"),g=O(l[0]),w=F(),m=T("button"),E=T("span"),E.textContent="×",k=F(),y=T("div"),u(t,"class",o="toast-icon "+l[1]+" svelte-z3l7qj"),u(_,"class",c="toast-title "+l[1]+" svelte-z3l7qj"),u(d,"class",j="toast-text "+l[1]+" svelte-z3l7qj"),u(r,"class",A="toast-details "+l[1]+" svelte-z3l7qj"),u(E,"aria-hidden","true"),u(m,"class",L="toast-close "+l[1]+" svelte-z3l7qj"),u(m,"type","button"),u(m,"aria-label","Close"),u(m,"data-testid","toast-close"),u(y,"class",R="timer "+l[1]+" svelte-z3l7qj"),u(e,"class",q="toast-body "+l[1]+" svelte-z3l7qj"),u(e,"role","alert"),u(e,"data-testid","toast-body")},m(I,x){b(I,e,x),p(e,t),~n&&Y[n].m(t,null),p(e,s),p(e,r),p(r,_),p(_,a),p(r,f),p(r,d),p(d,g),p(e,w),p(e,m),p(m,E),p(e,k),p(e,y),V=!0,fe||(D=[ge(m,"click",l[2]),ge(e,"click",Ge(l[4])),ge(e,"keydown",Ge(l[5]))],fe=!0)},p(I,[x]){let be=n;n=ce(I),n!==be&&(i&&(ne(),C(Y[be],1,1,()=>{Y[be]=null}),le()),~n?(i=Y[n],i||(i=Y[n]=H[n](I),i.c()),P(i,1),i.m(t,null)):i=null),(!V||x&2&&o!==(o="toast-icon "+I[1]+" svelte-z3l7qj"))&&u(t,"class",o),(!V||x&2)&&G(a,I[1]),(!V||x&2&&c!==(c="toast-title "+I[1]+" svelte-z3l7qj"))&&u(_,"class",c),(!V||x&1)&&G(g,I[0]),(!V||x&2&&j!==(j="toast-text "+I[1]+" svelte-z3l7qj"))&&u(d,"class",j),(!V||x&2&&A!==(A="toast-details "+I[1]+" svelte-z3l7qj"))&&u(r,"class",A),(!V||x&2&&L!==(L="toast-close "+I[1]+" svelte-z3l7qj"))&&u(m,"class",L),(!V||x&2&&R!==(R="timer "+I[1]+" svelte-z3l7qj"))&&u(y,"class",R),(!V||x&2&&q!==(q="toast-body "+I[1]+" svelte-z3l7qj"))&&u(e,"class",q)},i(I){V||(P(i),I&&hn(()=>{V&&(J&&J.end(1),M=vn(e,Ze,{duration:200,delay:100}),M.start())}),V=!0)},o(I){C(i),M&&M.invalidate(),I&&(J=bn(e,Ze,{duration:200})),V=!1},d(I){I&&v(e),~n&&Y[n].d(),I&&J&&J.end(),fe=!1,kn(D)}}}function Xn(l,e,t){let{message:n=""}=e,{type:i}=e,{id:o}=e;const s=je();function r(){s("close",o)}Pe(()=>{setTimeout(()=>{r()},1e4)});function _(c){ve.call(this,l,c)}function a(c){ve.call(this,l,c)}return l.$$set=c=>{"message"in c&&t(0,n=c.message),"type"in c&&t(1,i=c.type),"id"in c&&t(3,o=c.id)},[n,i,r,o,_,a]}class $n extends oe{constructor(e){super(),se(this,e,Xn,Yn,re,{message:0,type:1,id:3})}}function Ye(l,e,t){const n=l.slice();return n[2]=e[t].type,n[3]=e[t].message,n[4]=e[t].id,n}function Xe(l,e){let t,n,i,o,s=$,r;return n=new $n({props:{type:e[2],message:e[3],id:e[4]}}),n.$on("close",e[1]),{key:l,first:null,c(){t=T("div"),Q(n.$$.fragment),i=F(),de(t,"width","100%"),this.first=t},m(_,a){b(_,t,a),K(n,t,null),p(t,i),r=!0},p(_,a){e=_;const c={};a&1&&(c.type=e[2]),a&1&&(c.message=e[3]),a&1&&(c.id=e[4]),n.$set(c)},r(){o=t.getBoundingClientRect()},f(){zn(t),s()},a(){s(),s=Sn(t,o,Bn,{duration:300})},i(_){r||(P(n.$$.fragment,_),r=!0)},o(_){C(n.$$.fragment,_),r=!1},d(_){_&&v(t),Z(n)}}}function xn(l){let e,t=[],n=new Map,i,o=te(l[0]);const s=r=>r[4];for(let r=0;r<o.length;r+=1){let _=Ye(l,o,r),a=s(_);n.set(a,t[r]=Xe(a,_))}return{c(){e=T("div");for(let r=0;r<t.length;r+=1)t[r].c();u(e,"class","toast-wrap svelte-pu0yf1")},m(r,_){b(r,e,_);for(let a=0;a<t.length;a+=1)t[a]&&t[a].m(e,null);i=!0},p(r,[_]){if(_&1){o=te(r[0]),ne();for(let a=0;a<t.length;a+=1)t[a].r();t=Ft(t,_,s,1,r,o,n,e,wn,Xe,null,Ye);for(let a=0;a<t.length;a+=1)t[a].a();le()}},i(r){if(!i){for(let _=0;_<o.length;_+=1)P(t[_]);i=!0}},o(r){for(let _=0;_<t.length;_+=1)C(t[_]);i=!1},d(r){r&&v(e);for(let _=0;_<t.length;_+=1)t[_].d()}}}function el(l){l.length>0&&"parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0)}function tl(l,e,t){let{messages:n=[]}=e;function i(o){ve.call(this,l,o)}return l.$$set=o=>{"messages"in o&&t(0,n=o.messages)},l.$$.update=()=>{l.$$.dirty&1&&el(n)},[n,i]}class nl extends oe{constructor(e){super(),se(this,e,tl,xn,re,{messages:0})}}const $e={accordion:{static:()=>N(()=>import("./index-1712e61d.js"),["assets/index-1712e61d.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/StaticColumn-2df50ccb.js","assets/StaticColumn-2853eb31.css","assets/index-8f1feca1.css"])},annotatedimage:{static:()=>N(()=>import("./index-e59eb4de.js"),["assets/index-e59eb4de.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/Empty-89f2f53e.js","assets/Image-b5c3e889.js","assets/index-f0e43e7d.css"])},audio:{static:()=>N(()=>import("./index-27d3ecad.js"),["assets/index-27d3ecad.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/IconButton-a4282a0e.js","assets/Empty-89f2f53e.js","assets/ShareButton-c5d88eaa.js","assets/utils-c3e3db58.js","assets/Download-036e6033.js","assets/utils-08c16748.js","assets/index-4e2a7646.css"]),interactive:()=>N(()=>import("./index-6bc4eff2.js"),["assets/index-6bc4eff2.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/UploadText-455dd9a9.js","assets/Upload-59d47275.js","assets/ModifyUpload-b9d1b06a.js","assets/IconButton-a4282a0e.js","assets/Undo-e443528b.js","assets/BlockLabel-520e742a.js","assets/utils-08c16748.js","assets/file-url-43dd5b28.js","assets/index-e73c6c79.css"])},box:{static:()=>N(()=>import("./index-30a87b3f.js"),["assets/index-30a87b3f.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css"])},button:{static:()=>N(()=>import("./index-612895e5.js"),["assets/index-612895e5.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css"])},chatbot:{static:()=>N(()=>import("./index-da243c51.js"),["assets/index-da243c51.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/utils-c3e3db58.js","assets/index-2f00b72c.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/ShareButton-c5d88eaa.js","assets/IconButton-a4282a0e.js","assets/StaticMarkdown-0958b32f.js","assets/StaticMarkdown-02163af3.css","assets/Copy-bc542573.js","assets/BlockLabel-520e742a.js","assets/index-33bad8d4.css"])},checkbox:{static:()=>N(()=>import("./index-91dd0935.js"),["assets/index-91dd0935.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Checkbox-58eced4a.js","assets/Checkbox-51c40da3.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/Info-47344107.js"]),interactive:()=>N(()=>import("./index-30bae568.js"),["assets/index-30bae568.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Checkbox-58eced4a.js","assets/Checkbox-51c40da3.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/Info-47344107.js"])},checkboxgroup:{static:()=>N(()=>import("./index-cd6b39e7.js"),["assets/index-cd6b39e7.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Checkboxgroup-42ce18d6.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Checkboxgroup-8f24ac6d.css"]),interactive:()=>N(()=>import("./index-e43e107f.js"),["assets/index-e43e107f.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Checkboxgroup-42ce18d6.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Checkboxgroup-8f24ac6d.css"])},code:{static:()=>N(()=>import("./index-9b466906.js"),["assets/index-9b466906.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Widgets.svelte_svelte_type_style_lang-0c1b2b47.js","assets/Widgets-4ccfb72c.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/Copy-bc542573.js","assets/Download-036e6033.js","assets/BlockLabel-520e742a.js","assets/Empty-89f2f53e.js"]),interactive:()=>N(()=>import("./index-22064243.js"),["assets/index-22064243.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Widgets.svelte_svelte_type_style_lang-0c1b2b47.js","assets/Widgets-4ccfb72c.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js"])},colorpicker:{static:()=>N(()=>import("./index-2d6a6334.js"),["assets/index-2d6a6334.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Colorpicker-dc61d968.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Colorpicker-cd311153.css"]),interactive:()=>N(()=>import("./index-ad7d34f4.js"),["assets/index-ad7d34f4.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Colorpicker-dc61d968.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Colorpicker-cd311153.css"])},column:{static:()=>N(()=>import("./index-f86b542e.js"),["assets/index-f86b542e.js","assets/StaticColumn-2df50ccb.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/StaticColumn-2853eb31.css"])},dataframe:{static:()=>N(()=>import("./index-9f71a59c.js"),["assets/index-9f71a59c.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/Table-575bd406.js","assets/index-2f00b72c.js","assets/utils-c3e3db58.js","assets/Upload-59d47275.js","assets/StaticMarkdown-0958b32f.js","assets/StaticMarkdown-02163af3.css","assets/dsv-576afacd.js","assets/Table-6d6f4ad8.css"]),interactive:()=>N(()=>import("./index-70a68e95.js"),["assets/index-70a68e95.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/Table-575bd406.js","assets/index-2f00b72c.js","assets/utils-c3e3db58.js","assets/Upload-59d47275.js","assets/StaticMarkdown-0958b32f.js","assets/StaticMarkdown-02163af3.css","assets/dsv-576afacd.js","assets/Table-6d6f4ad8.css"])},dataset:{static:()=>N(()=>import("./index-b534a906.js"),["assets/index-b534a906.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/file-url-43dd5b28.js","assets/Player.svelte_svelte_type_style_lang-de2d6160.js","assets/Player-afca2092.css","assets/csv-b0b7514a.js","assets/dsv-576afacd.js","assets/index-8d377615.css"])},dropdown:{static:()=>N(()=>import("./index-d4432834.js"),["assets/index-d4432834.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Multiselect-f9fdc060.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Multiselect-fc493e4f.css"]),interactive:()=>N(()=>import("./index-d6283151.js"),["assets/index-d6283151.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Multiselect-f9fdc060.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Multiselect-fc493e4f.css"])},file:{static:()=>N(()=>import("./index-e2033f22.js"),["assets/index-e2033f22.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/Empty-89f2f53e.js","assets/File-29fa02e0.js","assets/FilePreview-4d8ed5e5.js","assets/FilePreview-f49dff58.css"]),interactive:()=>N(()=>import("./index-ad0cea76.js"),["assets/index-ad0cea76.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Upload-59d47275.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/ModifyUpload-b9d1b06a.js","assets/IconButton-a4282a0e.js","assets/Undo-e443528b.js","assets/BlockLabel-520e742a.js","assets/File-29fa02e0.js","assets/FilePreview-4d8ed5e5.js","assets/FilePreview-f49dff58.css","assets/UploadText-455dd9a9.js"])},form:{static:()=>N(()=>import("./index-09fd6ec8.js"),["assets/index-09fd6ec8.js","assets/StaticForm-0610b7e2.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/StaticForm-3812b7f1.css"])},gallery:{static:()=>N(()=>import("./index-38ada136.js"),["assets/index-38ada136.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/IconButton-a4282a0e.js","assets/Empty-89f2f53e.js","assets/ShareButton-c5d88eaa.js","assets/utils-c3e3db58.js","assets/ModifyUpload-b9d1b06a.js","assets/Undo-e443528b.js","assets/Download-036e6033.js","assets/Image-b5c3e889.js","assets/index-45f3e04b.css"])},group:{static:()=>N(()=>import("./index-8d387471.js"),["assets/index-8d387471.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/index-37519934.css"])},highlightedtext:{static:()=>N(()=>import("./index-2de34455.js"),["assets/index-2de34455.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/color-253fe719.js","assets/utils-9ea7750b.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/Empty-89f2f53e.js","assets/index-9d08c7d8.css"]),interactive:()=>N(()=>import("./index-586f720f.js"),["assets/index-586f720f.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/color-253fe719.js","assets/utils-9ea7750b.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/Empty-89f2f53e.js","assets/index-b8808b2f.css"])},fileexplorer:{static:()=>N(()=>import("./index-79cdedb6.js"),["assets/index-79cdedb6.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/File-29fa02e0.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/DirectoryExplorer-ec1b0254.js","assets/Empty-89f2f53e.js","assets/DirectoryExplorer-ee671302.css"]),interactive:()=>N(()=>import("./index-7f60fa03.js"),["assets/index-7f60fa03.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/File-29fa02e0.js","assets/DirectoryExplorer-ec1b0254.js","assets/Empty-89f2f53e.js","assets/DirectoryExplorer-ee671302.css"])},html:{static:()=>N(()=>import("./index-fc01452b.js"),["assets/index-fc01452b.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/index-329f8260.css"])},image:{static:()=>N(()=>import("./index-105fcce0.js"),["assets/index-105fcce0.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/utils-c3e3db58.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/IconButton-a4282a0e.js","assets/Empty-89f2f53e.js","assets/ShareButton-c5d88eaa.js","assets/Download-036e6033.js","assets/Image-b5c3e889.js","assets/utils-90f3612b.js","assets/index-f62e764d.css"]),interactive:()=>N(()=>import("./index-5f5c53dd.js"),["assets/index-5f5c53dd.js","assets/InteractiveImage-88c0f0dc.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/Image-b5c3e889.js","assets/utils-90f3612b.js","assets/IconButton-a4282a0e.js","assets/ModifyUpload-b9d1b06a.js","assets/Undo-e443528b.js","assets/Upload-59d47275.js","assets/UploadText-455dd9a9.js","assets/InteractiveImage-b496c98d.css"])},interpretation:{static:()=>N(()=>import("./index-17f81bc5.js"),["assets/index-17f81bc5.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/index-6acaa952.css"]),interactive:()=>N(()=>import("./index-17f81bc5.js"),["assets/index-17f81bc5.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/index-6acaa952.css"])},json:{static:()=>N(()=>import("./index-89aeea2d.js"),["assets/index-89aeea2d.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/Copy-bc542573.js","assets/Empty-89f2f53e.js","assets/BlockLabel-520e742a.js","assets/index-b658ebcd.css"])},label:{static:()=>N(()=>import("./index-4e42194a.js"),["assets/index-4e42194a.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/Empty-89f2f53e.js","assets/index-d4781e2f.css"])},markdown:{static:()=>N(()=>import("./index-e529d2b4.js"),["assets/index-e529d2b4.js","assets/StaticMarkdown-0958b32f.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/utils-c3e3db58.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/StaticMarkdown-02163af3.css"])},model3d:{static:()=>N(()=>import("./index-9505482f.js"),["assets/index-9505482f.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/Empty-89f2f53e.js","assets/File-29fa02e0.js","assets/IconButton-a4282a0e.js","assets/Download-036e6033.js","assets/Undo-e443528b.js","assets/babylonjs.loaders.min-628a697c.js","assets/index-5cbd5f30.css"]),interactive:()=>N(()=>import("./index-162792c0.js"),["assets/index-162792c0.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/UploadText-455dd9a9.js","assets/Upload-59d47275.js","assets/ModifyUpload-b9d1b06a.js","assets/IconButton-a4282a0e.js","assets/Undo-e443528b.js","assets/BlockLabel-520e742a.js","assets/File-29fa02e0.js","assets/babylonjs.loaders.min-628a697c.js","assets/index-9c329a98.css"])},number:{static:()=>N(()=>import("./index-cb134468.js"),["assets/index-cb134468.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Number-04def8a9.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Number-76c3ee3f.css"]),interactive:()=>N(()=>import("./index-3a436d1d.js"),["assets/index-3a436d1d.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Number-04def8a9.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Number-76c3ee3f.css"])},plot:{static:()=>N(()=>import("./index-c6b23b0c.js"),["assets/index-c6b23b0c.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/color-253fe719.js","assets/linear-bcbcf466.js","assets/dsv-576afacd.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/Empty-89f2f53e.js","assets/BlockLabel-520e742a.js","assets/index-61df0c8a.css"])},radio:{static:()=>N(()=>import("./index-8b98be64.js"),["assets/index-8b98be64.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Radio-a873dcad.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Radio-38bb7cb5.css"]),interactive:()=>N(()=>import("./index-bdc62011.js"),["assets/index-bdc62011.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Radio-a873dcad.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Radio-38bb7cb5.css"])},row:{static:()=>N(()=>import("./index-973a999e.js"),["assets/index-973a999e.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/index-93c91554.css"])},slider:{static:()=>N(()=>import("./index-948debff.js"),["assets/index-948debff.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Range-26958c1b.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Range-2e491be4.css"]),interactive:()=>N(()=>import("./index-38792395.js"),["assets/index-38792395.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Range-26958c1b.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Range-2e491be4.css"])},state:{static:()=>N(()=>import("./index-87fe92d0.js"),["assets/index-87fe92d0.js","assets/index-7674dbb6.js","assets/index-642268e4.css"])},statustracker:{static:()=>N(()=>import("./index-893322f7.js"),["assets/index-893322f7.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css"])},tabs:{static:()=>N(()=>import("./index-674385d9.js"),["assets/index-674385d9.js","assets/StaticTabs-08740167.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/StaticTabs-42a53876.css"])},tabitem:{static:()=>N(()=>import("./index-93666f8e.js"),["assets/index-93666f8e.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/StaticTabs-08740167.js","assets/StaticTabs-42a53876.css","assets/StaticColumn-2df50ccb.js","assets/StaticColumn-2853eb31.css","assets/index-d43fcb36.css"])},textbox:{static:()=>N(()=>import("./index-dcfd2011.js"),["assets/index-dcfd2011.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Textbox-5df53a1e.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Copy-bc542573.js","assets/Textbox-dde6f8cc.css"]),interactive:()=>N(()=>import("./index-5fe76eb2.js"),["assets/index-5fe76eb2.js","assets/InteractiveTextbox-8ea757fe.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Textbox-5df53a1e.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Copy-bc542573.js","assets/Textbox-dde6f8cc.css"])},timeseries:{static:()=>N(()=>import("./index-49d17dd5.js"),["assets/index-49d17dd5.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/Empty-89f2f53e.js","assets/Chart-51698b0c.js","assets/color-253fe719.js","assets/csv-b0b7514a.js","assets/dsv-576afacd.js","assets/linear-bcbcf466.js","assets/Chart-02ddc6a9.css"]),interactive:()=>N(()=>import("./index-6d5c4e04.js"),["assets/index-6d5c4e04.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Upload-59d47275.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/ModifyUpload-b9d1b06a.js","assets/IconButton-a4282a0e.js","assets/Undo-e443528b.js","assets/BlockLabel-520e742a.js","assets/Chart-51698b0c.js","assets/color-253fe719.js","assets/csv-b0b7514a.js","assets/dsv-576afacd.js","assets/linear-bcbcf466.js","assets/Chart-02ddc6a9.css","assets/UploadText-455dd9a9.js","assets/index-69616613.css"])},uploadbutton:{static:()=>N(()=>import("./index-e49740e7.js"),["assets/index-e49740e7.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/UploadButton-64870218.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/UploadButton-03d58ab8.css"]),interactive:()=>N(()=>import("./index-afc543b3.js"),["assets/index-afc543b3.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/UploadButton-64870218.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/UploadButton-03d58ab8.css"])},video:{static:()=>N(()=>import("./index-38c75842.js"),["assets/index-38c75842.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockLabel-520e742a.js","assets/IconButton-a4282a0e.js","assets/Empty-89f2f53e.js","assets/ShareButton-c5d88eaa.js","assets/utils-c3e3db58.js","assets/Download-036e6033.js","assets/Player-8ce27b89.js","assets/Undo-e443528b.js","assets/Player.svelte_svelte_type_style_lang-de2d6160.js","assets/file-url-43dd5b28.js","assets/Player-afca2092.css","assets/index-022688c7.css"]),interactive:()=>N(()=>import("./index-16770ed8.js"),["assets/index-16770ed8.js","assets/index-7674dbb6.js","assets/index-642268e4.css","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/UploadText-455dd9a9.js","assets/Upload-59d47275.js","assets/ModifyUpload-b9d1b06a.js","assets/IconButton-a4282a0e.js","assets/Undo-e443528b.js","assets/BlockLabel-520e742a.js","assets/Player-8ce27b89.js","assets/Player.svelte_svelte_type_style_lang-de2d6160.js","assets/file-url-43dd5b28.js","assets/Player-afca2092.css","assets/InteractiveImage-88c0f0dc.js","assets/Image-b5c3e889.js","assets/utils-90f3612b.js","assets/InteractiveImage-b496c98d.css","assets/index-633cd86d.css"])}};function ll(l){let e,t,n,i;return{c(){e=pe("svg"),t=pe("g"),n=pe("path"),i=pe("path"),u(n,"d","M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z"),u(i,"d","M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z"),u(e,"width","100%"),u(e,"height","100%"),u(e,"viewBox","0 0 5 5"),u(e,"version","1.1"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),u(e,"xml:space","preserve"),de(e,"fill","currentColor"),de(e,"fill-rule","evenodd"),de(e,"clip-rule","evenodd"),de(e,"stroke-linejoin","round"),de(e,"stroke-miterlimit","2")},m(o,s){b(o,e,s),p(e,t),p(t,n),p(t,i)},p:$,i:$,o:$,d(o){o&&v(e)}}}class Ht extends oe{constructor(e){super(),se(this,e,null,ll,re,{})}}function il(l){let e,t,n,i,o,s,r,_,a,c,f,d,g,j,A;return d=new Ht({}),{c(){e=T("div"),t=T("h1"),t.textContent="API Docs",n=F(),i=T("p"),o=O(`No API Routes found for
		`),s=T("code"),r=O(l[0]),_=F(),a=T("p"),a.innerHTML=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,c=F(),f=T("button"),Q(d.$$.fragment),u(s,"class","svelte-e1ha0f"),u(i,"class","attention svelte-e1ha0f"),u(e,"class","wrap prose svelte-e1ha0f"),u(f,"class","svelte-e1ha0f")},m(w,m){b(w,e,m),p(e,t),p(e,n),p(e,i),p(i,o),p(i,s),p(s,r),p(e,_),p(e,a),b(w,c,m),b(w,f,m),K(d,f,null),g=!0,j||(A=ge(f,"click",l[2]),j=!0)},p(w,[m]){(!g||m&1)&&G(r,w[0])},i(w){g||(P(d.$$.fragment,w),g=!0)},o(w){C(d.$$.fragment,w),g=!1},d(w){w&&(v(e),v(c),v(f)),Z(d),j=!1,A()}}}function ol(l,e,t){const n=je();let{root:i}=e;const o=()=>n("close");return l.$$set=s=>{"root"in s&&t(0,i=s.root)},[i,n,o]}class sl extends oe{constructor(e){super(),se(this,e,ol,il,re,{root:0})}}function Te(l,e,t=null){return e===void 0?t==="py"?"None":null:e==="string"||e==="str"?t===null?l:'"'+l+'"':e==="number"?t===null?parseFloat(l):l:e==="boolean"||e=="bool"?t==="py"?(l=String(l),l==="true"?"True":"False"):t==="js"?l:l==="true":e==="List[str]"?(l=JSON.stringify(l),l):t===null?l===""?null:JSON.parse(l):typeof l=="string"?l===""?t==="py"?"None":"null":l:JSON.stringify(l)}const Gt="https://gradio.s3-us-west-2.amazonaws.com/3.50.0/assets/api-logo-5346f193.svg";function xe(l){let e;return{c(){e=O("s")},m(t,n){b(t,e,n)},d(t){t&&v(e)}}}function rl(l){let e,t,n,i,o,s,r,_,a,c,f,d,g,j,A,w,m,E,L,k=l[1]>1&&xe();return w=new Ht({}),{c(){e=T("h2"),t=T("img"),i=F(),o=T("div"),s=O(`API documentation
		`),r=T("div"),_=O(l[0]),a=F(),c=T("span"),f=T("span"),d=O(l[1]),g=O(" API endpoint"),k&&k.c(),j=F(),A=T("button"),Q(w.$$.fragment),Ae(t.src,n=Gt)||u(t,"src",n),u(t,"alt",""),u(t,"class","svelte-3n2nxs"),u(r,"class","url svelte-3n2nxs"),u(f,"class","url svelte-3n2nxs"),u(c,"class","counts svelte-3n2nxs"),u(e,"class","svelte-3n2nxs"),u(A,"class","svelte-3n2nxs")},m(y,R){b(y,e,R),p(e,t),p(e,i),p(e,o),p(o,s),p(o,r),p(r,_),p(e,a),p(e,c),p(c,f),p(f,d),p(c,g),k&&k.m(c,null),b(y,j,R),b(y,A,R),K(w,A,null),m=!0,E||(L=ge(A,"click",l[3]),E=!0)},p(y,[R]){(!m||R&1)&&G(_,y[0]),(!m||R&2)&&G(d,y[1]),y[1]>1?k||(k=xe(),k.c(),k.m(c,null)):k&&(k.d(1),k=null)},i(y){m||(P(w.$$.fragment,y),m=!0)},o(y){C(w.$$.fragment,y),m=!1},d(y){y&&(v(e),v(j),v(A)),k&&k.d(),Z(w),E=!1,L()}}}function al(l,e,t){let{root:n}=e,{api_count:i}=e;const o=je(),s=()=>o("close");return l.$$set=r=>{"root"in r&&t(0,n=r.root),"api_count"in r&&t(1,i=r.api_count)},[n,i,o,s]}class _l extends oe{constructor(e){super(),se(this,e,al,rl,re,{root:0,api_count:1})}}class et{#e;#t;constructor(e,t,n,i,o){this.#e=e,this.theme=n,this.version=i,this.#t=t,this.root=o}dispatch(e,t){const n=new CustomEvent("gradio",{bubbles:!0,detail:{data:t,id:this.#e,event:e}});this.#t.dispatchEvent(n)}}function tt(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].type,n[6]=e[t].python_type,n[7]=e[t].component,n[8]=e[t].serializer,n[10]=t,n}function nt(l){let e;return{c(){e=O("(")},m(t,n){b(t,e,n)},d(t){t&&v(e)}}}function cl(l){let e=l[2][l[10]].type+"",t,n,i=l[2][l[10]].description&&lt(l);return{c(){t=O(e),i&&i.c(),n=_e()},m(o,s){b(o,t,s),i&&i.m(o,s),b(o,n,s)},p(o,s){s&4&&e!==(e=o[2][o[10]].type+"")&&G(t,e),o[2][o[10]].description?i?i.p(o,s):(i=lt(o),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null)},d(o){o&&(v(t),v(n)),i&&i.d(o)}}}function fl(l){let e=l[6].type+"",t,n,i=l[6]?.description&&it(l);return{c(){t=O(e),i&&i.c(),n=_e()},m(o,s){b(o,t,s),i&&i.m(o,s),b(o,n,s)},p(o,s){s&2&&e!==(e=o[6].type+"")&&G(t,e),o[6]?.description?i?i.p(o,s):(i=it(o),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null)},d(o){o&&(v(t),v(n)),i&&i.d(o)}}}function lt(l){let e,t=l[2][l[10]].description+"",n,i;return{c(){e=O(" ("),n=O(t),i=O(")")},m(o,s){b(o,e,s),b(o,n,s),b(o,i,s)},p(o,s){s&4&&t!==(t=o[2][o[10]].description+"")&&G(n,t)},d(o){o&&(v(e),v(n),v(i))}}}function it(l){let e,t=l[6].description+"",n,i;return{c(){e=O(" ("),n=O(t),i=O(")")},m(o,s){b(o,e,s),b(o,n,s),b(o,i,s)},p(o,s){s&2&&t!==(t=o[6].description+"")&&G(n,t)},d(o){o&&(v(e),v(n),v(i))}}}function ot(l){let e;return{c(){e=O(",")},m(t,n){b(t,e,n)},d(t){t&&v(e)}}}function st(l){let e,t,n,i,o=l[4]+"",s,r,_=l[7]+"",a,c;function f(A,w){return A[3]==="python"?fl:cl}let d=f(l),g=d(l),j=l[1].length>1&&ot();return{c(){e=T("div"),t=T("span"),n=O("# "),g.c(),i=O(`
						representing output in '`),s=O(o),r=O("' "),a=O(_),c=O(`
						component`),j&&j.c(),u(t,"class","desc svelte-1c7hj3i"),u(e,"class","svelte-1c7hj3i"),Le(e,"second-level",l[1].length>1)},m(A,w){b(A,e,w),p(e,t),p(t,n),g.m(t,null),p(t,i),p(t,s),p(t,r),p(t,a),p(t,c),j&&j.m(e,null)},p(A,w){d===(d=f(A))&&g?g.p(A,w):(g.d(1),g=d(A),g&&(g.c(),g.m(t,i))),w&2&&o!==(o=A[4]+"")&&G(s,o),w&2&&_!==(_=A[7]+"")&&G(a,_),A[1].length>1?j||(j=ot(),j.c(),j.m(e,null)):j&&(j.d(1),j=null),w&2&&Le(e,"second-level",A[1].length>1)},d(A){A&&v(e),g.d(),j&&j.d()}}}function rt(l){let e;return{c(){e=O(")")},m(t,n){b(t,e,n)},d(t){t&&v(e)}}}function at(l){let e,t,n;return t=new En({props:{margin:!1}}),{c(){e=T("div"),Q(t.$$.fragment),u(e,"class","load-wrap svelte-1c7hj3i")},m(i,o){b(i,e,o),K(t,e,null),n=!0},i(i){n||(P(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){i&&v(e),Z(t)}}}function ul(l){let e,t,n,i,o,s,r=l[1].length>1&&nt(),_=te(l[1]),a=[];for(let d=0;d<_.length;d+=1)a[d]=st(tt(l,_,d));let c=l[1].length>1&&rt(),f=l[0]&&at();return{c(){e=T("div"),t=T("div"),r&&r.c(),n=F();for(let d=0;d<a.length;d+=1)a[d].c();i=F(),c&&c.c(),o=F(),f&&f.c(),u(t,"class","svelte-1c7hj3i"),Le(t,"hide",l[0]),u(e,"class","response-wrap svelte-1c7hj3i")},m(d,g){b(d,e,g),p(e,t),r&&r.m(t,null),p(t,n);for(let j=0;j<a.length;j+=1)a[j]&&a[j].m(t,null);p(t,i),c&&c.m(t,null),p(e,o),f&&f.m(e,null),s=!0},p(d,g){if(d[1].length>1?r||(r=nt(),r.c(),r.m(t,n)):r&&(r.d(1),r=null),g&14){_=te(d[1]);let j;for(j=0;j<_.length;j+=1){const A=tt(d,_,j);a[j]?a[j].p(A,g):(a[j]=st(A),a[j].c(),a[j].m(t,i))}for(;j<a.length;j+=1)a[j].d(1);a.length=_.length}d[1].length>1?c||(c=rt(),c.c(),c.m(t,null)):c&&(c.d(1),c=null),(!s||g&1)&&Le(t,"hide",d[0]),d[0]?f?g&1&&P(f,1):(f=at(),f.c(),P(f,1),f.m(e,null)):f&&(ne(),C(f,1,1,()=>{f=null}),le())},i(d){s||(P(f),s=!0)},o(d){C(f),s=!1},d(d){d&&v(e),r&&r.d(),he(a,d),c&&c.d(),f&&f.d()}}}function dl(l){let e,t,n,i;return n=new Ve({props:{$$slots:{default:[ul]},$$scope:{ctx:l}}}),{c(){e=T("h4"),e.innerHTML=`<div class="toggle-icon svelte-1c7hj3i"><div class="toggle-dot svelte-1c7hj3i"></div></div>
	Return Type(s)`,t=F(),Q(n.$$.fragment),u(e,"class","svelte-1c7hj3i")},m(o,s){b(o,e,s),b(o,t,s),K(n,o,s),i=!0},p(o,[s]){const r={};s&2063&&(r.$$scope={dirty:s,ctx:o}),n.$set(r)},i(o){i||(P(n.$$.fragment,o),i=!0)},o(o){C(n.$$.fragment,o),i=!1},d(o){o&&(v(e),v(t)),Z(n,o)}}}function pl(l,e,t){let{is_running:n}=e,{endpoint_returns:i}=e,{js_returns:o}=e,{current_language:s}=e;return l.$$set=r=>{"is_running"in r&&t(0,n=r.is_running),"endpoint_returns"in r&&t(1,i=r.endpoint_returns),"js_returns"in r&&t(2,o=r.js_returns),"current_language"in r&&t(3,s=r.current_language)},[n,i,o,s]}class Wt extends oe{constructor(e){super(),se(this,e,pl,dl,re,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}}function ml(l){let e;return{c(){e=O(l[0])},m(t,n){b(t,e,n)},p(t,n){n&1&&G(e,t[0])},d(t){t&&v(e)}}}function gl(l){let e,t;return e=new Nn({props:{size:"sm",$$slots:{default:[ml]},$$scope:{ctx:l}}}),e.$on("click",l[1]),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,[i]){const o={};i&9&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function hl(l,e,t){let{code:n}=e,i="copy";function o(){navigator.clipboard.writeText(n),t(0,i="copied!"),setTimeout(()=>{t(0,i="copy")},1500)}return l.$$set=s=>{"code"in s&&t(2,n=s.code)},[i,o,n]}class Ie extends oe{constructor(e){super(),se(this,e,hl,gl,re,{code:2})}}function vl(l){let e,t,n,i,o,s;return t=new Ie({props:{code:ct}}),{c(){e=T("div"),Q(t.$$.fragment),n=F(),i=T("div"),o=T("pre"),o.textContent=`$ ${ct}`,u(e,"class","copy svelte-hq8ezf"),u(o,"class","svelte-hq8ezf")},m(r,_){b(r,e,_),K(t,e,null),b(r,n,_),b(r,i,_),p(i,o),s=!0},p:$,i(r){s||(P(t.$$.fragment,r),s=!0)},o(r){C(t.$$.fragment,r),s=!1},d(r){r&&(v(e),v(n),v(i)),Z(t)}}}function bl(l){let e,t,n,i,o,s;return t=new Ie({props:{code:_t}}),{c(){e=T("div"),Q(t.$$.fragment),n=F(),i=T("div"),o=T("pre"),o.textContent=`$ ${_t}`,u(e,"class","copy svelte-hq8ezf"),u(o,"class","svelte-hq8ezf")},m(r,_){b(r,e,_),K(t,e,null),b(r,n,_),b(r,i,_),p(i,o),s=!0},p:$,i(r){s||(P(t.$$.fragment,r),s=!0)},o(r){C(t.$$.fragment,r),s=!1},d(r){r&&(v(e),v(n),v(i)),Z(t)}}}function kl(l){let e,t,n,i;const o=[bl,vl],s=[];function r(_,a){return _[0]==="python"?0:_[0]==="javascript"?1:-1}return~(t=r(l))&&(n=s[t]=o[t](l)),{c(){e=T("code"),n&&n.c(),u(e,"class","svelte-hq8ezf")},m(_,a){b(_,e,a),~t&&s[t].m(e,null),i=!0},p(_,a){let c=t;t=r(_),t===c?~t&&s[t].p(_,a):(n&&(ne(),C(s[c],1,1,()=>{s[c]=null}),le()),~t?(n=s[t],n?n.p(_,a):(n=s[t]=o[t](_),n.c()),P(n,1),n.m(e,null)):n=null)},i(_){i||(P(n),i=!0)},o(_){C(n),i=!1},d(_){_&&v(e),~t&&s[t].d()}}}function wl(l){let e,t;return e=new Ve({props:{$$slots:{default:[kl]},$$scope:{ctx:l}}}),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,[i]){const o={};i&3&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}let _t="pip install gradio_client",ct="npm i -D @gradio/client";function El(l,e,t){let{current_language:n}=e;return l.$$set=i=>{"current_language"in i&&t(0,n=i.current_language)},[n]}class yl extends oe{constructor(e){super(),se(this,e,El,wl,re,{current_language:0})}}function jl(l){let e,t,n,i;return{c(){e=T("h3"),t=O(`fn_index:
		`),n=T("span"),i=O(l[1]),u(n,"class","post svelte-41kcm6"),u(e,"class","svelte-41kcm6")},m(o,s){b(o,e,s),p(e,t),p(e,n),p(n,i)},p(o,s){s&2&&G(i,o[1])},d(o){o&&v(e)}}}function Al(l){let e,t,n,i="/"+l[0],o;return{c(){e=T("h3"),t=O(`api_name:
		`),n=T("span"),o=O(i),u(n,"class","post svelte-41kcm6"),u(e,"class","svelte-41kcm6")},m(s,r){b(s,e,r),p(e,t),p(e,n),p(n,o)},p(s,r){r&1&&i!==(i="/"+s[0])&&G(o,i)},d(s){s&&v(e)}}}function Ol(l){let e;function t(o,s){return o[2]?Al:jl}let n=t(l),i=n(l);return{c(){i.c(),e=_e()},m(o,s){i.m(o,s),b(o,e,s)},p(o,[s]){n===(n=t(o))&&i?i.p(o,s):(i.d(1),i=n(o),i&&(i.c(),i.m(e.parentNode,e)))},i:$,o:$,d(o){o&&v(e),i.d(o)}}}function Ll(l,e,t){let{api_name:n=null}=e,{fn_index:i=null}=e,{named:o}=e;return l.$$set=s=>{"api_name"in s&&t(0,n=s.api_name),"fn_index"in s&&t(1,i=s.fn_index),"named"in s&&t(2,o=s.named)},[n,i,o]}class Jt extends oe{constructor(e){super(),se(this,e,Ll,Ol,re,{api_name:0,fn_index:1,named:2})}}function ft(l,e,t){const n=l.slice();return n[14]=e[t].label,n[15]=e[t].type,n[16]=e[t].python_type,n[17]=e[t].component,n[18]=e[t].example_input,n[19]=e[t].serializer,n[21]=t,n}function ut(l,e,t){const n=l.slice();return n[14]=e[t].label,n[15]=e[t].type,n[16]=e[t].python_type,n[17]=e[t].component,n[18]=e[t].example_input,n[19]=e[t].serializer,n[21]=t,n}function dt(l,e,t){const n=l.slice();return n[14]=e[t].label,n[15]=e[t].type,n[16]=e[t].python_type,n[17]=e[t].component,n[18]=e[t].example_input,n[19]=e[t].serializer,n[21]=t,n}function Tl(l){let e,t;return e=new Jt({props:{named:l[6],fn_index:l[1]}}),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const o={};i&64&&(o.named=n[6]),i&2&&(o.fn_index=n[1]),e.$set(o)},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Pl(l){let e,t;return e=new Jt({props:{named:l[6],api_name:l[0].api_name}}),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const o={};i&64&&(o.named=n[6]),i&1&&(o.api_name=n[0].api_name),e.$set(o)},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Il(l){let e,t,n,i,o,s,r,_,a,c,f,d,g,j,A;t=new Ie({props:{code:l[9]?.innerText}});let w=te(l[11]),m=[];for(let q=0;q<w.length;q+=1)m[q]=pt(ut(l,w,q));function E(q,M){return q[6]?Vl:Dl}let L=E(l),k=L(l),y=te(l[4]),R=[];for(let q=0;q<y.length;q+=1)R[q]=gt(ft(l,y,q));return{c(){e=T("div"),Q(t.$$.fragment),n=F(),i=T("div"),o=T("pre"),s=O(`import { client } from "@gradio/client";
`);for(let q=0;q<m.length;q+=1)m[q].c();r=O(`
const app = await client(`),_=T("span"),a=O('"'),c=O(l[2]),f=O('"'),d=O(`);
const result = await app.predict(`),k.c(),g=O(", [");for(let q=0;q<R.length;q+=1)R[q].c();j=O(`
	]);

console.log(result.data);
`),u(e,"class","copy svelte-1d98qmk"),u(_,"class","token string svelte-1d98qmk"),u(o,"class","svelte-1d98qmk")},m(q,M){b(q,e,M),K(t,e,null),b(q,n,M),b(q,i,M),p(i,o),p(o,s);for(let J=0;J<m.length;J+=1)m[J]&&m[J].m(o,null);p(o,r),p(o,_),p(_,a),p(_,c),p(_,f),p(o,d),k.m(o,null),p(o,g);for(let J=0;J<R.length;J+=1)R[J]&&R[J].m(o,null);p(o,j),l[13](i),A=!0},p(q,M){const J={};if(M&512&&(J.code=q[9]?.innerText),t.$set(J),M&2048){w=te(q[11]);let V;for(V=0;V<w.length;V+=1){const fe=ut(q,w,V);m[V]?m[V].p(fe,M):(m[V]=pt(fe),m[V].c(),m[V].m(o,r))}for(;V<m.length;V+=1)m[V].d(1);m.length=w.length}if((!A||M&4)&&G(c,q[2]),L===(L=E(q))&&k?k.p(q,M):(k.d(1),k=L(q),k&&(k.c(),k.m(o,g))),M&1072){y=te(q[4]);let V;for(V=0;V<y.length;V+=1){const fe=ft(q,y,V);R[V]?R[V].p(fe,M):(R[V]=gt(fe),R[V].c(),R[V].m(o,j))}for(;V<R.length;V+=1)R[V].d(1);R.length=y.length}},i(q){A||(P(t.$$.fragment,q),A=!0)},o(q){C(t.$$.fragment,q),A=!1},d(q){q&&(v(e),v(n),v(i)),Z(t),he(m,q),k.d(),he(R,q),l[13](null)}}}function Rl(l){let e,t,n,i,o,s,r,_,a,c,f,d,g,j;t=new Ie({props:{code:l[8]?.innerText}});let A=te(l[4]),w=[];for(let k=0;k<A.length;k+=1)w[k]=bt(dt(l,A,k));function m(k,y){return k[6]?Sl:Nl}let E=m(l),L=E(l);return{c(){e=T("div"),Q(t.$$.fragment),n=F(),i=T("div"),o=T("pre"),s=O(`from gradio_client import Client

client = Client(`),r=T("span"),_=O('"'),a=O(l[2]),c=O('"'),f=O(`)
result = client.predict(`);for(let k=0;k<w.length;k+=1)w[k].c();d=_e(),L.c(),g=O(`
)
print(result)`),u(e,"class","copy svelte-1d98qmk"),u(r,"class","token string svelte-1d98qmk"),u(o,"class","svelte-1d98qmk")},m(k,y){b(k,e,y),K(t,e,null),b(k,n,y),b(k,i,y),p(i,o),p(o,s),p(o,r),p(r,_),p(r,a),p(r,c),p(o,f);for(let R=0;R<w.length;R+=1)w[R]&&w[R].m(o,null);p(o,d),L.m(o,null),p(o,g),l[12](i),j=!0},p(k,y){const R={};if(y&256&&(R.code=k[8]?.innerText),t.$set(R),(!j||y&4)&&G(a,k[2]),y&26){A=te(k[4]);let q;for(q=0;q<A.length;q+=1){const M=dt(k,A,q);w[q]?w[q].p(M,y):(w[q]=bt(M),w[q].c(),w[q].m(o,d))}for(;q<w.length;q+=1)w[q].d(1);w.length=A.length}E===(E=m(k))&&L?L.p(k,y):(L.d(1),L=E(k),L&&(L.c(),L.m(o,g)))},i(k){j||(P(t.$$.fragment,k),j=!0)},o(k){C(t.$$.fragment,k),j=!1},d(k){k&&(v(e),v(n),v(i)),Z(t),he(w,k),L.d(),l[12](null)}}}function pt(l){let e,t,n,i=l[18]+"",o,s,r=l[17]+"",_,a,c,f;return{c(){e=O(`
const response_`),t=O(l[21]),n=O(' = await fetch("'),o=O(i),s=O(`");
const example`),_=O(r),a=O(" = await response_"),c=O(l[21]),f=O(`.blob();
						`)},m(d,g){b(d,e,g),b(d,t,g),b(d,n,g),b(d,o,g),b(d,s,g),b(d,_,g),b(d,a,g),b(d,c,g),b(d,f,g)},p:$,d(d){d&&(v(e),v(t),v(n),v(o),v(s),v(_),v(a),v(c),v(f))}}}function Dl(l){let e;return{c(){e=O(l[1])},m(t,n){b(t,e,n)},p(t,n){n&2&&G(e,t[1])},d(t){t&&v(e)}}}function Vl(l){let e,t=l[0].api_name+"",n,i;return{c(){e=O('"/'),n=O(t),i=O('"')},m(o,s){b(o,e,s),b(o,n,s),b(o,i,s)},p(o,s){s&1&&t!==(t=o[0].api_name+"")&&G(n,t)},d(o){o&&(v(e),v(n),v(i))}}}function ql(l){let e,t,n=Te(l[18],l[16].type,"js")+"",i,o,s,r,_=l[5][l[21]].type+"",a,c,f,d=l[14]+"",g,j,A=l[17]+"",w,m,E=l[5][l[21]].description&&mt(l);return{c(){e=O(`		
				`),t=T("span"),i=O(n),o=O(", "),s=T("span"),r=O("// "),a=O(_),c=O(" "),E&&E.c(),f=O(" in '"),g=O(d),j=O("' "),w=O(A),m=O(" component"),u(t,"class","example-inputs svelte-1d98qmk"),u(s,"class","desc svelte-1d98qmk")},m(L,k){b(L,e,k),b(L,t,k),p(t,i),b(L,o,k),b(L,s,k),p(s,r),p(s,a),p(s,c),E&&E.m(s,null),p(s,f),p(s,g),p(s,j),p(s,w),p(s,m)},p(L,k){k&16&&n!==(n=Te(L[18],L[16].type,"js")+"")&&G(i,n),k&32&&_!==(_=L[5][L[21]].type+"")&&G(a,_),L[5][L[21]].description?E?E.p(L,k):(E=mt(L),E.c(),E.m(s,f)):E&&(E.d(1),E=null),k&16&&d!==(d=L[14]+"")&&G(g,d),k&16&&A!==(A=L[17]+"")&&G(w,A)},d(L){L&&(v(e),v(t),v(o),v(s)),E&&E.d()}}}function Cl(l){let e,t,n,i=l[17]+"",o,s,r,_,a=l[14]+"",c,f,d=l[17]+"",g,j;return{c(){e=O(`
				`),t=T("span"),n=O("example"),o=O(i),s=O(", "),r=T("span"),_=O("	// blob in '"),c=O(a),f=O("' "),g=O(d),j=O(" component"),u(t,"class","example-inputs svelte-1d98qmk"),u(r,"class","desc svelte-1d98qmk")},m(A,w){b(A,e,w),b(A,t,w),p(t,n),p(t,o),b(A,s,w),b(A,r,w),p(r,_),p(r,c),p(r,f),p(r,g),p(r,j)},p(A,w){w&16&&i!==(i=A[17]+"")&&G(o,i),w&16&&a!==(a=A[14]+"")&&G(c,a),w&16&&d!==(d=A[17]+"")&&G(g,d)},d(A){A&&(v(e),v(t),v(s),v(r))}}}function mt(l){let e,t=l[5][l[21]].description+"",n,i;return{c(){e=O("("),n=O(t),i=O(")")},m(o,s){b(o,e,s),b(o,n,s),b(o,i,s)},p(o,s){s&32&&t!==(t=o[5][o[21]].description+"")&&G(n,t)},d(o){o&&(v(e),v(n),v(i))}}}function gt(l){let e,t;function n(s,r){return r&16&&(e=null),e==null&&(e=!!s[10].includes(s[17])),e?Cl:ql}let i=n(l,-1),o=i(l);return{c(){o.c(),t=_e()},m(s,r){o.m(s,r),b(s,t,r)},p(s,r){i===(i=n(s,r))&&o?o.p(s,r):(o.d(1),o=i(s),o&&(o.c(),o.m(t.parentNode,t)))},d(s){s&&v(t),o.d(s)}}}function ht(l){let e;return{c(){e=T("span"),e.textContent="ERROR",u(e,"class","error svelte-1d98qmk")},m(t,n){b(t,e,n)},d(t){t&&v(e)}}}function vt(l){let e,t=l[16].description+"",n,i;return{c(){e=O("("),n=O(t),i=O(")")},m(o,s){b(o,e,s),b(o,n,s),b(o,i,s)},p(o,s){s&16&&t!==(t=o[16].description+"")&&G(n,t)},d(o){o&&(v(e),v(n),v(i))}}}function bt(l){let e,t,n=Te(l[18],l[16].type,"py")+"",i,o,s,r,_=l[16].type+"",a,c,f,d=l[14]+"",g,j,A=l[17]+"",w,m,E=l[3][l[1]][l[21]]&&ht(),L=l[16].description&&vt(l);return{c(){e=O(`
		`),t=T("span"),i=O(n),o=O(","),E&&E.c(),s=T("span"),r=O("	# "),a=O(_),c=O(" "),L&&L.c(),f=O(" in '"),g=O(d),j=O("' "),w=O(A),m=O(" component"),u(t,"class","example-inputs svelte-1d98qmk"),u(s,"class","desc svelte-1d98qmk")},m(k,y){b(k,e,y),b(k,t,y),p(t,i),b(k,o,y),E&&E.m(k,y),b(k,s,y),p(s,r),p(s,a),p(s,c),L&&L.m(s,null),p(s,f),p(s,g),p(s,j),p(s,w),p(s,m)},p(k,y){y&16&&n!==(n=Te(k[18],k[16].type,"py")+"")&&G(i,n),k[3][k[1]][k[21]]?E||(E=ht(),E.c(),E.m(s.parentNode,s)):E&&(E.d(1),E=null),y&16&&_!==(_=k[16].type+"")&&G(a,_),k[16].description?L?L.p(k,y):(L=vt(k),L.c(),L.m(s,f)):L&&(L.d(1),L=null),y&16&&d!==(d=k[14]+"")&&G(g,d),y&16&&A!==(A=k[17]+"")&&G(w,A)},d(k){k&&(v(e),v(t),v(o),v(s)),E&&E.d(k),L&&L.d()}}}function Nl(l){let e,t;return{c(){e=O(`
		fn_index=`),t=O(l[1])},m(n,i){b(n,e,i),b(n,t,i)},p(n,i){i&2&&G(t,n[1])},d(n){n&&(v(e),v(t))}}}function Sl(l){let e,t=l[0].api_name+"",n,i;return{c(){e=O(`
		api_name="/`),n=O(t),i=O('"')},m(o,s){b(o,e,s),b(o,n,s),b(o,i,s)},p(o,s){s&1&&t!==(t=o[0].api_name+"")&&G(n,t)},d(o){o&&(v(e),v(n),v(i))}}}function zl(l){let e,t,n,i;const o=[Rl,Il],s=[];function r(_,a){return _[7]==="python"?0:_[7]==="javascript"?1:-1}return~(t=r(l))&&(n=s[t]=o[t](l)),{c(){e=T("code"),n&&n.c(),u(e,"class","svelte-1d98qmk")},m(_,a){b(_,e,a),~t&&s[t].m(e,null),i=!0},p(_,a){let c=t;t=r(_),t===c?~t&&s[t].p(_,a):(n&&(ne(),C(s[c],1,1,()=>{s[c]=null}),le()),~t?(n=s[t],n?n.p(_,a):(n=s[t]=o[t](_),n.c()),P(n,1),n.m(e,null)):n=null)},i(_){i||(P(n),i=!0)},o(_){C(n),i=!1},d(_){_&&v(e),~t&&s[t].d()}}}function Ml(l){let e,t,n,i,o,s;const r=[Pl,Tl],_=[];function a(c,f){return c[6]?0:1}return t=a(l),n=_[t]=r[t](l),o=new Ve({props:{$$slots:{default:[zl]},$$scope:{ctx:l}}}),{c(){e=T("div"),n.c(),i=F(),Q(o.$$.fragment),u(e,"class","container svelte-1d98qmk")},m(c,f){b(c,e,f),_[t].m(e,null),p(e,i),K(o,e,null),s=!0},p(c,[f]){let d=t;t=a(c),t===d?_[t].p(c,f):(ne(),C(_[d],1,1,()=>{_[d]=null}),le(),n=_[t],n?n.p(c,f):(n=_[t]=r[t](c),n.c()),P(n,1),n.m(e,i));const g={};f&16778239&&(g.$$scope={dirty:f,ctx:c}),o.$set(g)},i(c){s||(P(n),P(o.$$.fragment,c),s=!0)},o(c){C(n),C(o.$$.fragment,c),s=!1},d(c){c&&v(e),_[t].d(),Z(o)}}}function Bl(l,e,t){let{dependency:n}=e,{dependency_index:i}=e,{root:o}=e,{dependency_failures:s}=e,{endpoint_parameters:r}=e,{js_parameters:_}=e,{named:a}=e,{current_language:c}=e,f,d,g=["Audio","File","Image","Video"],j=r.filter(m=>g.includes(m.component));function A(m){ye[m?"unshift":"push"](()=>{f=m,t(8,f)})}function w(m){ye[m?"unshift":"push"](()=>{d=m,t(9,d)})}return l.$$set=m=>{"dependency"in m&&t(0,n=m.dependency),"dependency_index"in m&&t(1,i=m.dependency_index),"root"in m&&t(2,o=m.root),"dependency_failures"in m&&t(3,s=m.dependency_failures),"endpoint_parameters"in m&&t(4,r=m.endpoint_parameters),"js_parameters"in m&&t(5,_=m.js_parameters),"named"in m&&t(6,a=m.named),"current_language"in m&&t(7,c=m.current_language)},[n,i,o,s,r,_,a,c,f,d,g,j,A,w]}class Qt extends oe{constructor(e){super(),se(this,e,Bl,Ml,re,{dependency:0,dependency_index:1,root:2,dependency_failures:3,endpoint_parameters:4,js_parameters:5,named:6,current_language:7})}}const Ul="https://gradio.s3-us-west-2.amazonaws.com/3.50.0/assets/python-20e39c92.svg",Fl="https://gradio.s3-us-west-2.amazonaws.com/3.50.0/assets/javascript-850cf94b.svg";function kt(l,e,t){const n=l.slice();return n[18]=e[t],n[20]=t,n}function wt(l,e,t){const n=l.slice();return n[18]=e[t],n[20]=t,n}function Et(l,e,t){const n=l.slice();return n[22]=e[t][0],n[23]=e[t][1],n}function yt(l){let e,t,n,i,o;const s=[Gl,Hl],r=[];function _(a,c){return c&32&&(e=null),e==null&&(e=!!(Object.keys(a[5].named_endpoints).length+Object.keys(a[5].unnamed_endpoints).length)),e?0:1}return t=_(l,-1),n=r[t]=s[t](l),{c(){n.c(),i=_e()},m(a,c){r[t].m(a,c),b(a,i,c),o=!0},p(a,c){let f=t;t=_(a,c),t===f?r[t].p(a,c):(ne(),C(r[f],1,1,()=>{r[f]=null}),le(),n=r[t],n?n.p(a,c):(n=r[t]=s[t](a),n.c()),P(n,1),n.m(i.parentNode,i))},i(a){o||(P(n),o=!0)},o(a){C(n),o=!1},d(a){a&&v(i),r[t].d(a)}}}function Hl(l){let e,t;return e=new sl({props:{root:l[0]}}),e.$on("close",l[12]),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const o={};i&1&&(o.root=n[0]),e.$set(o)},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Gl(l){let e,t,n,i,o,s,r,_,a,c,f,d=Object.keys(l[5].named_endpoints).length,g,j,A=Object.keys(l[5].unnamed_endpoints).length,w,m;t=new _l({props:{root:l[0],api_count:Object.keys(l[5].named_endpoints).length+Object.keys(l[5].unnamed_endpoints).length}}),t.$on("close",l[10]);let E=te(l[7]),L=[];for(let D=0;D<E.length;D+=1)L[D]=jt(Et(l,E,D));c=new yl({props:{current_language:l[2]}});let k=d&&At(),y=te(l[1]),R=[];for(let D=0;D<y.length;D+=1)R[D]=Lt(wt(l,y,D));const q=D=>C(R[D],1,1,()=>{R[D]=null});let M=A&&Tt(),J=te(l[1]),V=[];for(let D=0;D<J.length;D+=1)V[D]=It(kt(l,J,D));const fe=D=>C(V[D],1,1,()=>{V[D]=null});return{c(){e=T("div"),Q(t.$$.fragment),n=F(),i=T("div"),o=T("div"),o.innerHTML=`<p>Use the <a href="https://gradio.app/docs/#python-client" target="_blank"><code class="library svelte-bdjvpc">gradio_client</code></a>
					Python library or the
					<a href="https://gradio.app/docs/#javascript-client" target="_blank"><code class="library svelte-bdjvpc">@gradio/client</code></a> Javascript package to query the demo via API.</p>`,s=F(),r=T("div"),_=T("div");for(let D=0;D<L.length;D+=1)L[D].c();a=F(),Q(c.$$.fragment),f=F(),k&&k.c(),g=F();for(let D=0;D<R.length;D+=1)R[D].c();j=F(),M&&M.c(),w=F();for(let D=0;D<V.length;D+=1)V[D].c();u(e,"class","banner-wrap svelte-bdjvpc"),u(o,"class","client-doc svelte-bdjvpc"),u(_,"class","snippets svelte-bdjvpc"),u(r,"class","endpoint svelte-bdjvpc"),u(i,"class","docs-wrap svelte-bdjvpc")},m(D,H){b(D,e,H),K(t,e,null),b(D,n,H),b(D,i,H),p(i,o),p(i,s),p(i,r),p(r,_);for(let Y=0;Y<L.length;Y+=1)L[Y]&&L[Y].m(_,null);p(r,a),K(c,r,null),p(r,f),k&&k.m(r,null),p(r,g);for(let Y=0;Y<R.length;Y+=1)R[Y]&&R[Y].m(r,null);p(r,j),M&&M.m(r,null),p(r,w);for(let Y=0;Y<V.length;Y+=1)V[Y]&&V[Y].m(r,null);m=!0},p(D,H){const Y={};if(H&1&&(Y.root=D[0]),H&32&&(Y.api_count=Object.keys(D[5].named_endpoints).length+Object.keys(D[5].unnamed_endpoints).length),t.$set(Y),H&132){E=te(D[7]);let I;for(I=0;I<E.length;I+=1){const x=Et(D,E,I);L[I]?L[I].p(x,H):(L[I]=jt(x),L[I].c(),L[I].m(_,null))}for(;I<L.length;I+=1)L[I].d(1);L.length=E.length}const ce={};if(H&4&&(ce.current_language=D[2]),c.$set(ce),H&32&&(d=Object.keys(D[5].named_endpoints).length),d?k||(k=At(),k.c(),k.m(r,g)):k&&(k.d(1),k=null),H&127){y=te(D[1]);let I;for(I=0;I<y.length;I+=1){const x=wt(D,y,I);R[I]?(R[I].p(x,H),P(R[I],1)):(R[I]=Lt(x),R[I].c(),P(R[I],1),R[I].m(r,j))}for(ne(),I=y.length;I<R.length;I+=1)q(I);le()}if(H&32&&(A=Object.keys(D[5].unnamed_endpoints).length),A?M||(M=Tt(),M.c(),M.m(r,w)):M&&(M.d(1),M=null),H&127){J=te(D[1]);let I;for(I=0;I<J.length;I+=1){const x=kt(D,J,I);V[I]?(V[I].p(x,H),P(V[I],1)):(V[I]=It(x),V[I].c(),P(V[I],1),V[I].m(r,null))}for(ne(),I=J.length;I<V.length;I+=1)fe(I);le()}},i(D){if(!m){P(t.$$.fragment,D),P(c.$$.fragment,D);for(let H=0;H<y.length;H+=1)P(R[H]);for(let H=0;H<J.length;H+=1)P(V[H]);m=!0}},o(D){C(t.$$.fragment,D),C(c.$$.fragment,D),R=R.filter(Boolean);for(let H=0;H<R.length;H+=1)C(R[H]);V=V.filter(Boolean);for(let H=0;H<V.length;H+=1)C(V[H]);m=!1},d(D){D&&(v(e),v(n),v(i)),Z(t),he(L,D),Z(c),k&&k.d(),he(R,D),M&&M.d(),he(V,D)}}}function jt(l){let e,t,n,i,o=l[22]+"",s,r,_,a,c;function f(){return l[11](l[22])}return{c(){e=T("li"),t=T("img"),i=F(),s=O(o),r=F(),Ae(t.src,n=l[23])||u(t,"src",n),u(t,"alt",""),u(t,"class","svelte-bdjvpc"),u(e,"class",_="snippet "+(l[2]===l[22]?"current-lang":"inactive-lang")+" svelte-bdjvpc")},m(d,g){b(d,e,g),p(e,t),p(e,i),p(e,s),p(e,r),a||(c=ge(e,"click",f),a=!0)},p(d,g){l=d,g&4&&_!==(_="snippet "+(l[2]===l[22]?"current-lang":"inactive-lang")+" svelte-bdjvpc")&&u(e,"class",_)},d(d){d&&v(e),a=!1,c()}}}function At(l){let e;return{c(){e=T("h2"),e.textContent="Named Endpoints",u(e,"class","header svelte-bdjvpc")},m(t,n){b(t,e,n)},d(t){t&&v(e)}}}function Ot(l){let e,t,n,i,o;return t=new Qt({props:{named:!0,endpoint_parameters:l[5].named_endpoints["/"+l[18].api_name].parameters,js_parameters:l[6].named_endpoints["/"+l[18].api_name].parameters,dependency:l[18],dependency_index:l[20],current_language:l[2],root:l[0],dependency_failures:l[4]}}),i=new Wt({props:{endpoint_returns:l[5].named_endpoints["/"+l[18].api_name].returns,js_returns:l[6].named_endpoints["/"+l[18].api_name].returns,is_running:l[3],current_language:l[2]}}),{c(){e=T("div"),Q(t.$$.fragment),n=F(),Q(i.$$.fragment),u(e,"class","endpoint-container svelte-bdjvpc")},m(s,r){b(s,e,r),K(t,e,null),p(e,n),K(i,e,null),o=!0},p(s,r){const _={};r&34&&(_.endpoint_parameters=s[5].named_endpoints["/"+s[18].api_name].parameters),r&66&&(_.js_parameters=s[6].named_endpoints["/"+s[18].api_name].parameters),r&2&&(_.dependency=s[18]),r&4&&(_.current_language=s[2]),r&1&&(_.root=s[0]),r&16&&(_.dependency_failures=s[4]),t.$set(_);const a={};r&34&&(a.endpoint_returns=s[5].named_endpoints["/"+s[18].api_name].returns),r&66&&(a.js_returns=s[6].named_endpoints["/"+s[18].api_name].returns),r&8&&(a.is_running=s[3]),r&4&&(a.current_language=s[2]),i.$set(a)},i(s){o||(P(t.$$.fragment,s),P(i.$$.fragment,s),o=!0)},o(s){C(t.$$.fragment,s),C(i.$$.fragment,s),o=!1},d(s){s&&v(e),Z(t),Z(i)}}}function Lt(l){let e,t,n=l[18].api_name&&Ot(l);return{c(){n&&n.c(),e=_e()},m(i,o){n&&n.m(i,o),b(i,e,o),t=!0},p(i,o){i[18].api_name?n?(n.p(i,o),o&2&&P(n,1)):(n=Ot(i),n.c(),P(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(P(n),t=!0)},o(i){C(n),t=!1},d(i){i&&v(e),n&&n.d(i)}}}function Tt(l){let e;return{c(){e=T("h2"),e.textContent="Unnamed Endpoints",u(e,"class","header svelte-bdjvpc")},m(t,n){b(t,e,n)},d(t){t&&v(e)}}}function Pt(l){let e,t,n,i,o,s;return t=new Qt({props:{named:!1,endpoint_parameters:l[5].unnamed_endpoints[l[20]].parameters,js_parameters:l[6].unnamed_endpoints[l[20]].parameters,dependency:l[18],dependency_index:l[20],current_language:l[2],root:l[0],dependency_failures:l[4]}}),i=new Wt({props:{endpoint_returns:l[5].unnamed_endpoints[l[20]].returns,js_returns:l[6].unnamed_endpoints[l[20]].returns,is_running:l[3],current_language:l[2]}}),{c(){e=T("div"),Q(t.$$.fragment),n=F(),Q(i.$$.fragment),o=F(),u(e,"class","endpoint-container svelte-bdjvpc")},m(r,_){b(r,e,_),K(t,e,null),p(e,n),K(i,e,null),p(e,o),s=!0},p(r,_){const a={};_&32&&(a.endpoint_parameters=r[5].unnamed_endpoints[r[20]].parameters),_&64&&(a.js_parameters=r[6].unnamed_endpoints[r[20]].parameters),_&2&&(a.dependency=r[18]),_&4&&(a.current_language=r[2]),_&1&&(a.root=r[0]),_&16&&(a.dependency_failures=r[4]),t.$set(a);const c={};_&32&&(c.endpoint_returns=r[5].unnamed_endpoints[r[20]].returns),_&64&&(c.js_returns=r[6].unnamed_endpoints[r[20]].returns),_&8&&(c.is_running=r[3]),_&4&&(c.current_language=r[2]),i.$set(c)},i(r){s||(P(t.$$.fragment,r),P(i.$$.fragment,r),s=!0)},o(r){C(t.$$.fragment,r),C(i.$$.fragment,r),s=!1},d(r){r&&v(e),Z(t),Z(i)}}}function It(l){let e,t,n=l[5].unnamed_endpoints[l[20]]&&Pt(l);return{c(){n&&n.c(),e=_e()},m(i,o){n&&n.m(i,o),b(i,e,o),t=!0},p(i,o){i[5].unnamed_endpoints[i[20]]?n?(n.p(i,o),o&32&&P(n,1)):(n=Pt(i),n.c(),P(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(P(n),t=!0)},o(i){C(n),t=!1},d(i){i&&v(e),n&&n.d(i)}}}function Wl(l){let e,t,n=l[5]&&yt(l);return{c(){n&&n.c(),e=_e()},m(i,o){n&&n.m(i,o),b(i,e,o),t=!0},p(i,[o]){i[5]?n?(n.p(i,o),o&32&&P(n,1)):(n=yt(i),n.c(),P(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(P(n),t=!0)},o(i){C(n),t=!1},d(i){i&&v(e),n&&n.d(i)}}}function Jl(l,e,t){let{instance_map:n}=e,{dependencies:i}=e,{root:o}=e,{app:s}=e;o===""&&(o=location.protocol+"//"+location.host+location.pathname),o.endsWith("/")||(o+="/");let r="python";const _=[["python",Ul],["javascript",Fl]];let a=!1;i.map(E=>E.inputs.map(L=>{let k=n[L].documentation?.example_data;return k===void 0?k="":typeof k=="object"&&(k=JSON.stringify(k)),k})),i.map(E=>new Array(E.outputs.length));let c=i.map(E=>new Array(E.inputs.length).fill(!1));async function f(){return await(await fetch(o+"info")).json()}async function d(){return await s.view_api()}let g,j;f().then(E=>t(5,g=E)),d().then(E=>t(6,j=E)),Pe(()=>(document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0),()=>{document.body.style.overflow="auto"}));function A(E){ve.call(this,l,E)}const w=E=>t(2,r=E);function m(E){ve.call(this,l,E)}return l.$$set=E=>{"instance_map"in E&&t(8,n=E.instance_map),"dependencies"in E&&t(1,i=E.dependencies),"root"in E&&t(0,o=E.root),"app"in E&&t(9,s=E.app)},[o,i,r,a,c,g,j,_,n,s,A,w,m]}class Ql extends oe{constructor(e){super(),se(this,e,Jl,Wl,re,{instance_map:8,dependencies:1,root:0,app:9})}}function Rt(l,e,t){const n=l.slice();return n[9]=e[t].component,n[18]=e[t].id,n[2]=e[t].props,n[19]=e[t].children,n[20]=e[t].has_modes,n}function Dt(l){let e=[],t=new Map,n,i,o=te(l[1]);const s=r=>r[18];for(let r=0;r<o.length;r+=1){let _=Rt(l,o,r),a=s(_);t.set(a,e[r]=Vt(a,_))}return{c(){for(let r=0;r<e.length;r+=1)e[r].c();n=_e()},m(r,_){for(let a=0;a<e.length;a+=1)e[a]&&e[a].m(r,_);b(r,n,_),i=!0},p(r,_){_&235&&(o=te(r[1]),ne(),e=Ft(e,_,s,1,r,o,t,n.parentNode,Tn,Vt,n,Rt),le())},i(r){if(!i){for(let _=0;_<o.length;_+=1)P(e[_]);i=!0}},o(r){for(let _=0;_<e.length;_+=1)C(e[_]);i=!1},d(r){r&&v(n);for(let _=0;_<e.length;_+=1)e[_].d(r)}}}function Vt(l,e){let t,n,i;return n=new Kt({props:{component:e[9],target:e[6],id:e[18],props:e[2],root:e[3],instance_map:e[0],children:e[19],dynamic_ids:e[5],has_modes:e[20],theme_mode:e[7]}}),n.$on("destroy",e[12]),n.$on("mount",e[13]),{key:l,first:null,c(){t=_e(),Q(n.$$.fragment),this.first=t},m(o,s){b(o,t,s),K(n,o,s),i=!0},p(o,s){e=o;const r={};s&2&&(r.component=e[9]),s&64&&(r.target=e[6]),s&2&&(r.id=e[18]),s&2&&(r.props=e[2]),s&8&&(r.root=e[3]),s&1&&(r.instance_map=e[0]),s&2&&(r.children=e[19]),s&32&&(r.dynamic_ids=e[5]),s&2&&(r.has_modes=e[20]),s&128&&(r.theme_mode=e[7]),n.$set(r)},i(o){i||(P(n.$$.fragment,o),i=!0)},o(o){C(n.$$.fragment,o),i=!1},d(o){o&&v(t),Z(n,o)}}}function Kl(l){let e,t,n=l[1]&&l[1].length&&Dt(l);return{c(){n&&n.c(),e=_e()},m(i,o){n&&n.m(i,o),b(i,e,o),t=!0},p(i,o){i[1]&&i[1].length?n?(n.p(i,o),o&2&&P(n,1)):(n=Dt(i),n.c(),P(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(P(n),t=!0)},o(i){C(n),t=!1},d(i){i&&v(e),n&&n.d(i)}}}function Zl(l){let e,t,n,i;const o=[{elem_id:"elem_id"in l[2]&&l[2].elem_id||`component-${l[4]}`},{elem_classes:"elem_classes"in l[2]&&l[2].elem_classes||[]},{target:l[6]},l[2],{theme_mode:l[7]},{root:l[3]},{gradio:new et(l[4],l[6],l[7],l[8],l[3])}];function s(a){l[15](a)}var r=l[9];function _(a){let c={$$slots:{default:[Kl]},$$scope:{ctx:a}};for(let f=0;f<o.length;f+=1)c=Ln(c,o[f]);return a[0][a[4]].props.value!==void 0&&(c.value=a[0][a[4]].props.value),{props:c}}return r&&(e=We(r,_(l)),l[14](e),ye.push(()=>Je(e,"value",s)),e.$on("prop_change",l[10])),{c(){e&&Q(e.$$.fragment),n=_e()},m(a,c){e&&K(e,a,c),b(a,n,c),i=!0},p(a,[c]){const f=c&476?yn(o,[c&20&&{elem_id:"elem_id"in a[2]&&a[2].elem_id||`component-${a[4]}`},c&4&&{elem_classes:"elem_classes"in a[2]&&a[2].elem_classes||[]},c&64&&{target:a[6]},c&4&&jn(a[2]),c&128&&{theme_mode:a[7]},c&8&&{root:a[3]},c&472&&{gradio:new et(a[4],a[6],a[7],a[8],a[3])}]):{};if(c&8388843&&(f.$$scope={dirty:c,ctx:a}),!t&&c&17&&(t=!0,f.value=a[0][a[4]].props.value,An(()=>t=!1)),c&512&&r!==(r=a[9])){if(e){ne();const d=e;C(d.$$.fragment,1,0,()=>{Z(d,1)}),le()}r?(e=We(r,_(a)),a[14](e),ye.push(()=>Je(e,"value",s)),e.$on("prop_change",a[10]),Q(e.$$.fragment),P(e.$$.fragment,1),K(e,n.parentNode,n)):e=null}else r&&e.$set(f)},i(a){i||(e&&P(e.$$.fragment,a),i=!0)},o(a){e&&C(e.$$.fragment,a),i=!1},d(a){a&&v(n),l[14](null),e&&Z(e,a)}}}function Yl(l,e,t){let{root:n}=e,{component:i}=e,{instance_map:o}=e,{id:s}=e,{props:r}=e,{children:_}=e,{dynamic_ids:a}=e,{parent:c=null}=e,{target:f}=e,{theme_mode:d}=e,{version:g}=e;const j=je();let A=[];Pe(()=>{j("mount",s);for(const y of A)j("mount",y.id);return()=>{j("destroy",s);for(const y of A)j("mount",y.id)}}),On("BLOCK_KEY",c);function w(y){for(const R in y.detail)t(0,o[s].props[R]=y.detail[R],o)}function m(y){ve.call(this,l,y)}function E(y){ve.call(this,l,y)}function L(y){ye[y?"unshift":"push"](()=>{o[s].instance=y,t(0,o)})}function k(y){l.$$.not_equal(o[s].props.value,y)&&(o[s].props.value=y,t(0,o))}return l.$$set=y=>{"root"in y&&t(3,n=y.root),"component"in y&&t(9,i=y.component),"instance_map"in y&&t(0,o=y.instance_map),"id"in y&&t(4,s=y.id),"props"in y&&t(2,r=y.props),"children"in y&&t(1,_=y.children),"dynamic_ids"in y&&t(5,a=y.dynamic_ids),"parent"in y&&t(11,c=y.parent),"target"in y&&t(6,f=y.target),"theme_mode"in y&&t(7,d=y.theme_mode),"version"in y&&t(8,g=y.version)},l.$$.update=()=>{l.$$.dirty&3&&t(1,_=_&&_.filter(y=>{const R=o[y.id].type!=="statustracker";return R||A.push(y),R})),l.$$.dirty&19&&o[s].type==="form"&&(_?.every(y=>!y.props.visible)?t(2,r.visible=!1,r):t(2,r.visible=!0,r))},[o,_,r,n,s,a,f,d,g,i,w,c,m,E,L,k]}class Kt extends oe{constructor(e){super(),se(this,e,Yl,Zl,re,{root:3,component:9,instance_map:0,id:4,props:2,children:1,dynamic_ids:5,parent:11,target:6,theme_mode:7,version:8})}}function Xl(l){let e,t;return e=new Kt({props:{component:l[0].component,id:l[0].id,props:l[0].props,children:l[0].children,dynamic_ids:l[1],instance_map:l[2],root:l[3],target:l[4],theme_mode:l[5],version:l[6]}}),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.component=n[0].component),i&1&&(o.id=n[0].id),i&1&&(o.props=n[0].props),i&1&&(o.children=n[0].children),i&2&&(o.dynamic_ids=n[1]),i&4&&(o.instance_map=n[2]),i&8&&(o.root=n[3]),i&16&&(o.target=n[4]),i&32&&(o.theme_mode=n[5]),i&64&&(o.version=n[6]),e.$set(o)},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function $l(l,e,t){let{rootNode:n}=e,{dynamic_ids:i}=e,{instance_map:o}=e,{root:s}=e,{target:r}=e,{theme_mode:_}=e,{version:a}=e;const c=je();return Pe(()=>{c("mount")}),l.$$set=f=>{"rootNode"in f&&t(0,n=f.rootNode),"dynamic_ids"in f&&t(1,i=f.dynamic_ids),"instance_map"in f&&t(2,o=f.instance_map),"root"in f&&t(3,s=f.root),"target"in f&&t(4,r=f.target),"theme_mode"in f&&t(5,_=f.theme_mode),"version"in f&&t(6,a=f.version)},[n,i,o,s,r,_,a]}class xl extends oe{constructor(e){super(),se(this,e,$l,Xl,re,{rootNode:0,dynamic_ids:1,instance_map:2,root:3,target:4,theme_mode:5,version:6})}}const ei="https://gradio.s3-us-west-2.amazonaws.com/3.50.0/assets/logo-0a070fcf.svg";const{document:Ee}=Vn;function qt(l){return Ee.title=l[3],{c:$,m:$,d:$}}function Ct(l){let e,t,n,i;return{c(){e=T("script"),e.innerHTML="",n=F(),i=T("script"),i.textContent=`window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag("js", new Date());
			gtag("config", "UA-156449732-1");`,e.async=!0,e.defer=!0,Ae(e.src,t="https://www.googletagmanager.com/gtag/js?id=UA-156449732-1")||u(e,"src",t)},m(o,s){b(o,e,s),b(o,n,s),b(o,i,s)},d(o){o&&(v(e),v(n),v(i))}}}function Nt(l){let e,t;return e=new xl({props:{rootNode:l[14],dynamic_ids:l[16],instance_map:l[17],root:l[1],target:l[5],theme_mode:l[10],version:l[12]}}),e.$on("mount",l[22]),e.$on("destroy",l[30]),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const o={};i[0]&16384&&(o.rootNode=n[14]),i[0]&65536&&(o.dynamic_ids=n[16]),i[0]&131072&&(o.instance_map=n[17]),i[0]&2&&(o.root=n[1]),i[0]&32&&(o.target=n[5]),i[0]&1024&&(o.theme_mode=n[10]),i[0]&4096&&(o.version=n[12]),e.$set(o)},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function St(l){let e,t,n,i=l[19]("common.built_with_gradio")+"",o,s,r,_,a,c=l[6]&&zt(l);return{c(){e=T("footer"),c&&c.c(),t=F(),n=T("a"),o=O(i),s=F(),r=T("img"),Ae(r.src,_=ei)||u(r,"src",_),u(r,"alt",a=l[19]("common.logo")),u(r,"class","svelte-1ax1toq"),u(n,"href","https://gradio.app"),u(n,"class","built-with svelte-1ax1toq"),u(n,"target","_blank"),u(n,"rel","noreferrer"),u(e,"class","svelte-1ax1toq")},m(f,d){b(f,e,d),c&&c.m(e,null),p(e,t),p(e,n),p(n,o),p(n,s),p(n,r)},p(f,d){f[6]?c?c.p(f,d):(c=zt(f),c.c(),c.m(e,t)):c&&(c.d(1),c=null),d[0]&524288&&i!==(i=f[19]("common.built_with_gradio")+"")&&G(o,i),d[0]&524288&&a!==(a=f[19]("common.logo"))&&u(r,"alt",a)},d(f){f&&v(e),c&&c.d()}}}function zt(l){let e,t=l[19]("errors.use_via_api")+"",n,i,o,s,r,_,a,c,f;return{c(){e=T("button"),n=O(t),i=F(),o=T("img"),_=F(),a=T("div"),a.textContent="·",Ae(o.src,s=Gt)||u(o,"src",s),u(o,"alt",r=l[19]("common.logo")),u(o,"class","svelte-1ax1toq"),u(e,"class","show-api svelte-1ax1toq"),u(a,"class","svelte-1ax1toq")},m(d,g){b(d,e,g),p(e,n),p(e,i),p(e,o),b(d,_,g),b(d,a,g),c||(f=ge(e,"click",l[31]),c=!0)},p(d,g){g[0]&524288&&t!==(t=d[19]("errors.use_via_api")+"")&&G(n,t),g[0]&524288&&r!==(r=d[19]("common.logo"))&&u(o,"alt",r)},d(d){d&&(v(e),v(_),v(a)),c=!1,f()}}}function Mt(l){let e,t,n,i,o,s,r,_;return o=new Ql({props:{instance_map:l[17],dependencies:l[2],root:l[1],app:l[11]}}),o.$on("close",l[33]),{c(){e=T("div"),t=T("div"),n=F(),i=T("div"),Q(o.$$.fragment),u(t,"class","backdrop svelte-1ax1toq"),u(i,"class","api-docs-wrap svelte-1ax1toq"),u(e,"class","api-docs svelte-1ax1toq")},m(a,c){b(a,e,c),p(e,t),p(e,n),p(e,i),K(o,i,null),s=!0,r||(_=ge(t,"click",l[32]),r=!0)},p(a,c){const f={};c[0]&131072&&(f.instance_map=a[17]),c[0]&4&&(f.dependencies=a[2]),c[0]&2&&(f.root=a[1]),c[0]&2048&&(f.app=a[11]),o.$set(f)},i(a){s||(P(o.$$.fragment,a),s=!0)},o(a){C(o.$$.fragment,a),s=!1},d(a){a&&v(e),Z(o),r=!1,_()}}}function Bt(l){let e,t;return e=new nl({props:{messages:l[18]}}),e.$on("close",l[21]),{c(){Q(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const o={};i[0]&262144&&(o.messages=n[18]),e.$set(o)},i(n){t||(P(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function ti(l){let e,t,n,i,o,s,r,_,a,c,f=l[8]&&qt(l),d=l[4]&&Ct(),g=l[0]&&Nt(l),j=l[7]&&St(l),A=l[15]&&l[0]&&Mt(l),w=l[18]&&Bt(l);return{c(){f&&f.c(),e=_e(),d&&d.c(),t=_e(),n=F(),i=T("div"),o=T("div"),g&&g.c(),s=F(),j&&j.c(),r=F(),A&&A.c(),_=F(),w&&w.c(),a=_e(),u(o,"class","contain"),de(o,"flex-grow",l[9]?"1":"auto"),u(i,"class","wrap svelte-1ax1toq"),de(i,"min-height",l[9]?"100%":"auto")},m(m,E){f&&f.m(Ee.head,null),p(Ee.head,e),d&&d.m(Ee.head,null),p(Ee.head,t),b(m,n,E),b(m,i,E),p(i,o),g&&g.m(o,null),p(i,s),j&&j.m(i,null),b(m,r,E),A&&A.m(m,E),b(m,_,E),w&&w.m(m,E),b(m,a,E),c=!0},p(m,E){m[8]?f||(f=qt(m),f.c(),f.m(e.parentNode,e)):f&&(f.d(1),f=null),m[4]?d||(d=Ct(),d.c(),d.m(t.parentNode,t)):d&&(d.d(1),d=null),m[0]?g?(g.p(m,E),E[0]&1&&P(g,1)):(g=Nt(m),g.c(),P(g,1),g.m(o,null)):g&&(ne(),C(g,1,1,()=>{g=null}),le()),E[0]&512&&de(o,"flex-grow",m[9]?"1":"auto"),m[7]?j?j.p(m,E):(j=St(m),j.c(),j.m(i,null)):j&&(j.d(1),j=null),E[0]&512&&de(i,"min-height",m[9]?"100%":"auto"),m[15]&&m[0]?A?(A.p(m,E),E[0]&32769&&P(A,1)):(A=Mt(m),A.c(),P(A,1),A.m(_.parentNode,_)):A&&(ne(),C(A,1,1,()=>{A=null}),le()),m[18]?w?(w.p(m,E),E[0]&262144&&P(w,1)):(w=Bt(m),w.c(),P(w,1),w.m(a.parentNode,a)):w&&(ne(),C(w,1,1,()=>{w=null}),le())},i(m){c||(P(g),P(A),P(w),c=!0)},o(m){C(g),C(A),C(w),c=!1},d(m){m&&(v(n),v(i),v(r),v(_),v(a)),f&&f.d(m),v(e),d&&d.d(m),v(t),g&&g.d(),j&&j.d(),A&&A.d(m),w&&w.d(m)}}}const ni=/^'([^]+)'$/,li=15,ii=10;function Ut(l,e,t){for(const n of t)for(const i of n[e])if(i===l)return!0;return!1}function oi(l){return Array.isArray(l)&&l.length===0||l===""||l===0||!l}function si(l){return"detail"in l}function ri(l,e,t){let n,i,o=$,s=()=>(o(),o=qn(M,h=>t(29,i=h)),M),r;Pn(l,In,h=>t(19,r=h)),l.$$.on_destroy.push(()=>o()),Rn();let{root:_}=e,{components:a}=e,{layout:c}=e,{dependencies:f}=e,{title:d="Gradio"}=e,{analytics_enabled:g=!1}=e,{target:j}=e,{autoscroll:A}=e,{show_api:w=!0}=e,{show_footer:m=!0}=e,{control_page_title:E=!1}=e,{app_mode:L}=e,{theme_mode:k}=e,{app:y}=e,{space_id:R}=e,{version:q}=e,M=Qe();s();let J={id:c.id,type:"column",props:{mode:"static"},has_modes:!1,instance:null,component:null};const V=Object.getPrototypeOf(async function(){}).constructor;f.forEach(h=>{if(h.js){const U=h.backend_fn?h.inputs.length===1:h.outputs.length===1;try{h.frontend_fn=new V("__fn_args",`let result = await (${h.js})(...__fn_args);
					return (${U} && !Array.isArray(result)) ? [result] : result;`)}catch(S){console.error("Could not parse custom js method."),console.error(S)}}});let D=new URLSearchParams(window.location.search).get("view")==="api"&&w;function H(h){t(15,D=h);let U=new URLSearchParams(window.location.search);h?U.set("view","api"):U.delete("view"),history.replaceState(null,"","?"+U.toString())}let Y=new Set,ce;async function I(h,U){try{const S=await $e[h][U]();return{name:h,component:S}}catch(S){if(U==="interactive")try{const B=await $e[h].static();return{name:h,component:B}}catch(B){throw console.error(`failed to load: ${h}`),console.error(B),B}else throw console.error(`failed to load: ${h}`),console.error(S),S}}let x=new Set,be=new Map;async function qe(h,U,S,B){t(0,Oe=!1);let X=S[h.id];const ae=(await B.get(`${X.type}_${U.get(h.id)||"static"}`)).component;X.component=ae.default,h.children&&(X.children=h.children.map(z=>S[z.id]),await Promise.all(h.children.map(z=>qe(z,U,S,B))))}let{ready:Oe=!1}=e,{render_complete:Re=!1}=e;function Zt(){s(t(13,M=Qe())),f.forEach((z,W)=>{M.register(W,z.inputs,z.outputs)});const h=new Set;for(const z of a){const{id:W,props:ee}=z;(Ut(W,"inputs",f)||!Ut(W,"outputs",f)&&oi(ee?.value))&&h.add(W)}t(16,Y=h);const U={id:c.id,type:"column",props:{mode:"static"},has_modes:!1,instance:null,component:null};a.push(U);const S=new Set,B=new Map,X=new Map,ae=a.reduce((z,W)=>(z[W.id]=W,z),{});a.forEach(z=>{if(z.props.interactive===!1?z.props.mode="static":z.props.interactive===!0||Y.has(z.id)?z.props.mode="interactive":z.props.mode="static",z.props.server_fns){let ee={};z.props.server_fns.forEach(ue=>{ee[ue]=async(...me)=>(me.length===1&&(me=me[0]),await y.component_server(z.id,ue,me))}),z.props.server=ee}X.set(z.id,z.props.mode);const W=I(z.type,z.props.mode);S.add(W),B.set(`${z.type}_${z.props.mode}`,W)}),Promise.all(Array.from(S)).then(()=>{qe(c,X,ae,B).then(async()=>{t(0,Oe=!0),x=S,be=B,t(17,ce=ae),t(14,J=U)}).catch(z=>{console.error(z)})})}async function Yt(h,U){let S=U==="dynamic"?"interactive":U;if(h.props.mode===S)return;h.props.mode=S;const B=I(h.type,h.props.mode);x.add(B),be.set(`${h.type}_${h.props.mode}`,B),B.then(X=>{h.component=X.component.default,t(14,J)})}function Ce(h,U){const S=f[U].outputs;h?.forEach((B,X)=>{const ae=ce[S[X]];if(ae.props.value_is_output=!0,typeof B=="object"&&B!==null&&B.__type__==="update")for(const[z,W]of Object.entries(B))z!=="__type__"&&(z==="mode"&&Yt(ae,W),ae.props[z]=W);else ae.props.value=B}),t(14,J)}let Ne=new Map;function Se(h,U,S){h?.props||(h.props={}),h.props[U]=S,t(14,J)}let ze=[],ie=[];function ke(h,U,S){return{message:h,fn_index:U,type:S,id:++Xt}}let Xt=-1,De=!1;document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&(De=!0)});const $t=r("blocks.long_requests_queue"),xt=r("blocks.connection_can_break"),en=r("blocks.lost_connection"),Me=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);let Be=!1,Ue=!1;async function we(h,U=null){let S=f[h];const B=M.get_status_for_fn(h);if(t(18,ie=ie.filter(({fn_index:z})=>z!==h)),S.cancels&&await Promise.all(S.cancels.map(async z=>{const W=Ne.get(z);return W?.cancel(),W})),B==="pending"||B==="generating")return;let X={fn_index:h,data:S.inputs.map(z=>ce[z].props.value),event_data:S.collects_event_data?U:null};S.frontend_fn?S.frontend_fn(X.data.concat(S.outputs.map(z=>ce[z].props.value))).then(z=>{S.backend_fn?(X.data=z,ae()):Ce(z,h)}):S.backend_fn&&ae();function ae(){const z=y.submit(X.fn_index,X.data,X.event_data).on("data",({data:W,fn_index:ee})=>{Ce(W,ee)}).on("status",({fn_index:W,...ee})=>{Ke().then(()=>{if(M.update({...ee,status:ee.stage,progress:ee.progress_data,fn_index:W}),!Be&&R!==null&&ee.position!==void 0&&ee.position>=2&&ee.eta!==void 0&&ee.eta>li&&(Be=!0,t(18,ie=[ke($t,W,"warning"),...ie])),!Ue&&Me&&ee.eta!==void 0&&ee.eta>ii&&(Ue=!0,t(18,ie=[ke(xt,W,"warning"),...ie])),ee.stage==="complete"&&(f.map(async(ue,me)=>{ue.trigger_after===W&&we(me)}),z.destroy()),ee.broken&&Me&&De)window.setTimeout(()=>{t(18,ie=[ke(en,W,"error"),...ie])},0),we(h,U),De=!1;else if(ee.stage==="error"){if(ee.message){const ue=ee.message.replace(ni,(me,He)=>He);t(18,ie=[ke(ue,W,"error"),...ie])}f.map(async(ue,me)=>{ue.trigger_after===W&&!ue.trigger_only_on_success&&we(me)}),z.destroy()}})}).on("log",({log:W,fn_index:ee,level:ue})=>{t(18,ie=[ke(W,ee,ue),...ie])});Ne.set(h,z)}}function tn(h,U){if(R===null)return;const S=new URL(`https://huggingface.co/spaces/${R}/discussions/new`);h!==void 0&&h.length>0&&S.searchParams.set("title",h),S.searchParams.set("description",U),window.open(S.toString(),"_blank")}function nn(h){const U=h.detail;t(18,ie=ie.filter(S=>S.id!==U))}const ln=h=>!!(h&&new URL(h,location.href).origin!==location.origin);async function on(){await Ke();for(var h=j.getElementsByTagName("a"),U=0;U<h.length;U++){const S=h[U].getAttribute("target"),B=h[U].getAttribute("href");ln(B)&&S!=="_blank"&&h[U].setAttribute("target","_blank")}f.forEach((S,B)=>{S.targets.length===1&&S.targets[0][1]==="load"&&we(B)}),j.addEventListener("gradio",S=>{if(!si(S))throw new Error("not a custom event");const{id:B,event:X,data:ae}=S.detail;if(X==="share"){const{title:z,description:W}=ae;tn(z,W)}else X==="error"?t(18,ie=[ke(ae,-1,"error"),...ie]):n[B]?.[X]?.forEach(W=>{we(W,ae)})}),t(24,Re=!0)}function Fe(h){ze=ze.map(U=>U.filter(S=>S!==h))}function sn(h){for(const S in h){let B=h[S],X=f[B.fn_index];B.scroll_to_output=X.scroll_to_output,B.show_progress=X.show_progress,Se(ce[S],"loading_status",B)}const U=M.get_inputs_to_update();for(const[S,B]of U)Se(ce[S],"pending",B==="pending")}const rn=({detail:h})=>Fe(h),an=()=>{H(!D)},_n=()=>{H(!1)},cn=()=>{H(!1)};return l.$$set=h=>{"root"in h&&t(1,_=h.root),"components"in h&&t(25,a=h.components),"layout"in h&&t(26,c=h.layout),"dependencies"in h&&t(2,f=h.dependencies),"title"in h&&t(3,d=h.title),"analytics_enabled"in h&&t(4,g=h.analytics_enabled),"target"in h&&t(5,j=h.target),"autoscroll"in h&&t(27,A=h.autoscroll),"show_api"in h&&t(6,w=h.show_api),"show_footer"in h&&t(7,m=h.show_footer),"control_page_title"in h&&t(8,E=h.control_page_title),"app_mode"in h&&t(9,L=h.app_mode),"theme_mode"in h&&t(10,k=h.theme_mode),"app"in h&&t(11,y=h.app),"space_id"in h&&t(28,R=h.space_id),"version"in h&&t(12,q=h.version),"ready"in h&&t(0,Oe=h.ready),"render_complete"in h&&t(24,Re=h.render_complete)},l.$$.update=()=>{l.$$.dirty[0]&134217728&&Dn.update(h=>({...h,autoscroll:A})),l.$$.dirty[0]&100663296&&Zt(),l.$$.dirty[0]&4&&(n=f.reduce((h,U,S)=>(U.targets.forEach(([B,X])=>{h[B]||(h[B]={}),h[B]?.[X]?h[B][X].push(S):h[B][X]=[S]}),h),{})),l.$$.dirty[0]&536870912&&sn(i)},[Oe,_,f,d,g,j,w,m,E,L,k,y,q,M,J,D,Y,ce,ie,r,H,nn,on,Fe,Re,a,c,A,R,i,rn,an,_n,cn]}class ai extends oe{constructor(e){super(),se(this,e,ri,ti,re,{root:1,components:25,layout:26,dependencies:2,title:3,analytics_enabled:4,target:5,autoscroll:27,show_api:6,show_footer:7,control_page_title:8,app_mode:9,theme_mode:10,app:11,space_id:28,version:12,ready:0,render_complete:24},null,[-1,-1])}}const ui=Object.freeze(Object.defineProperty({__proto__:null,default:ai},Symbol.toStringTag,{value:"Module"}));export{ui as B,nl as T};
//# sourceMappingURL=Blocks-e0da70dc.js.map
