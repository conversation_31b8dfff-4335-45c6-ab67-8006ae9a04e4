import{S as g,e as m,s as C,m as o,o as y,t as z,g as f,N as r,h as w,j as h,p as b,x as E,n as k,k as S,B as T,C as j,an as q}from"./index-7674dbb6.js";function x(s){let t,e,l,c,_,u,i;return{c(){t=o("label"),e=o("input"),l=y(),c=o("span"),_=z(s[2]),e.disabled=s[1],f(e,"type","checkbox"),f(e,"name","test"),f(e,"data-testid","checkbox"),f(e,"class","svelte-3pzdsv"),f(c,"class","ml-2 svelte-3pzdsv"),f(t,"class","svelte-3pzdsv"),r(t,"disabled",s[1])},m(n,d){w(n,t,d),h(t,e),e.checked=s[0],h(t,l),h(t,c),h(c,_),u||(i=[b(e,"change",s[5]),b(e,"keydown",s[6]),b(e,"input",s[7])],u=!0)},p(n,[d]){d&2&&(e.disabled=n[1]),d&1&&(e.checked=n[0]),d&4&&E(_,n[2]),d&2&&r(t,"disabled",n[1])},i:k,o:k,d(n){n&&S(t),u=!1,T(i)}}}function B(s,t,e){let{value:l}=t,{value_is_output:c=!1}=t,{disabled:_=!1}=t,{label:u}=t;const i=j();function n(){i("change",l),c||i("input")}q(()=>{e(4,c=!1)});function d(){l=this.checked,e(0,l)}const p=a=>{a.key==="Enter"&&(e(0,l=!l),i("select",{index:0,value:u,selected:l}))},v=a=>{e(0,l=a.currentTarget.checked),i("select",{index:0,value:u,selected:a.currentTarget.checked})};return s.$$set=a=>{"value"in a&&e(0,l=a.value),"value_is_output"in a&&e(4,c=a.value_is_output),"disabled"in a&&e(1,_=a.disabled),"label"in a&&e(2,u=a.label)},s.$$.update=()=>{s.$$.dirty&1&&n()},[l,_,u,i,c,d,p,v]}class N extends g{constructor(t){super(),m(this,t,B,x,C,{value:0,value_is_output:4,disabled:1,label:2})}}export{N as C};
//# sourceMappingURL=Checkbox-58eced4a.js.map
