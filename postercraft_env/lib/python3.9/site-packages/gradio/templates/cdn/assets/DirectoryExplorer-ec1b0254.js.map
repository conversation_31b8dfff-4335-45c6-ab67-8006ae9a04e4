{"version": 3, "file": "DirectoryExplorer-ec1b0254.js", "sources": ["../../../../node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/dist/index.mjs", "../../../../js/fileexplorer/shared/ArrowIcon.svelte", "../../../../js/fileexplorer/shared/Checkbox.svelte", "../../../../js/fileexplorer/icons/light-file.svg", "../../../../js/fileexplorer/shared/FileTree.svelte", "../../../../js/fileexplorer/shared/utils.ts", "../../../../js/fileexplorer/shared/DirectoryExplorer.svelte"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 14 17\"\n\tversion=\"1.1\"\n\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;\"\n>\n\t<g transform=\"matrix(1,0,0,1,-10.6667,-7.73588)\">\n\t\t<path\n\t\t\td=\"M12.7,24.033C12.256,24.322 11.806,24.339 11.351,24.084C10.896,23.829 10.668,23.434 10.667,22.9L10.667,9.1C10.667,8.567 10.895,8.172 11.351,7.916C11.807,7.66 12.256,7.677 12.7,7.967L23.567,14.867C23.967,15.133 24.167,15.511 24.167,16C24.167,16.489 23.967,16.867 23.567,17.133L12.7,24.033Z\"\n\t\t\tstyle=\"fill:currentColor;fill-rule:nonzero;\"\n\t\t/>\n\t</g>\n</svg>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\texport let value: boolean;\n\texport let disabled: boolean;\n\n\tconst dispatch = createEventDispatcher<{ change: boolean }>();\n</script>\n\n<input\n\tbind:checked={value}\n\ttype=\"checkbox\"\n\ton:click={() => dispatch(\"change\", value)}\n\t{disabled}\n\tclass:disabled={disabled && !value}\n/>\n\n<style>\n\tinput {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: 1px solid var(--checkbox-border-color);\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground-color: var(--checkbox-background-color);\n\t\tline-height: var(--line-sm);\n\t\twidth: 18px !important;\n\t\theight: 18px !important;\n\t}\n\n\tinput:checked,\n\tinput:checked:hover,\n\tinput:checked:focus {\n\t\tborder-color: var(--checkbox-border-color-selected);\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t}\n\n\tinput:hover {\n\t\tborder-color: var(--checkbox-border-color-hover);\n\t\tbackground-color: var(--checkbox-background-color-hover);\n\t}\n\n\tinput:focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t\tbackground-color: var(--checkbox-background-color-focus);\n\t}\n\n\t.disabled {\n\t\tcursor: not-allowed;\n\t\tborder-color: var(--checkbox-border-color-hover);\n\t\tbackground-color: var(--checkbox-background-color-hover);\n\t}\n\n\tinput:disabled:checked,\n\tinput:disabled:checked:hover,\n\t.disabled:checked:focus {\n\t\topacity: 0.8 !important;\n\t\tcursor: not-allowed;\n\t}\n</style>\n", "export default \"__VITE_ASSET__7ae45149__\"", "<script lang=\"ts\">\n\timport type { Node } from \"./utils\";\n\timport { createEventDispatcher, tick } from \"svelte\";\n\n\timport Arrow from \"./ArrowIcon.svelte\";\n\timport Checkbox from \"./Checkbox.svelte\";\n\timport FileIcon from \"../icons/light-file.svg\";\n\n\texport let mode: \"static\" | \"interactive\";\n\texport let tree: Node[] = [];\n\texport let icons: any = {};\n\texport let node_indices: number[] = [];\n\texport let file_count: \"single\" | \"multiple\" = \"multiple\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tcheck: { node_indices: number[]; checked: boolean };\n\t}>();\n\n\tasync function dispatch_change(i: number): Promise<void> {\n\t\tawait tick();\n\n\t\tdispatch(\"check\", {\n\t\t\tnode_indices: [...node_indices, i],\n\t\t\tchecked: !tree[i].checked\n\t\t});\n\t}\n</script>\n\n<ul>\n\t{#each tree as { type, path, children, children_visible, checked }, i}\n\t\t<li>\n\t\t\t<span class=\"wrap\">\n\t\t\t\t<Checkbox\n\t\t\t\t\tdisabled={mode === \"static\" ||\n\t\t\t\t\t\t(type === \"folder\" && file_count === \"single\")}\n\t\t\t\t\tbind:value={checked}\n\t\t\t\t\ton:change={() => dispatch_change(i)}\n\t\t\t\t/>\n\n\t\t\t\t{#if type === \"folder\"}\n\t\t\t\t\t<span\n\t\t\t\t\t\tclass=\"icon\"\n\t\t\t\t\t\tclass:hidden={!tree[i].children_visible}\n\t\t\t\t\t\ton:click|stopPropagation={() =>\n\t\t\t\t\t\t\t(tree[i].children_visible = !tree[i].children_visible)}\n\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\ton:keydown={({ key }) =>\n\t\t\t\t\t\t\tkey === \" \" &&\n\t\t\t\t\t\t\t(tree[i].children_visible = !tree[i].children_visible)}\n\t\t\t\t\t\t><Arrow /></span\n\t\t\t\t\t>\n\t\t\t\t{:else}\n\t\t\t\t\t<span class=\"file-icon\">\n\t\t\t\t\t\t<img src={FileIcon} alt=\"file icon\" />\n\t\t\t\t\t</span>\n\t\t\t\t{/if}\n\t\t\t\t{path}\n\t\t\t</span>\n\t\t\t{#if children && children_visible}\n\t\t\t\t<svelte:self\n\t\t\t\t\ttree={children}\n\t\t\t\t\t{icons}\n\t\t\t\t\ton:check\n\t\t\t\t\tnode_indices={[...node_indices, i]}\n\t\t\t\t\t{mode}\n\t\t\t\t\t{file_count}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</li>\n\t{/each}\n</ul>\n\n<style>\n\t.icon {\n\t\tdisplay: inline-block;\n\t\twidth: 18px;\n\t\theight: 18px;\n\t\tpadding: 3px 2px 3px 3px;\n\t\tmargin: 0;\n\t\tflex-grow: 0;\n\t\tdisplay: inline-flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: 2px;\n\t\tcursor: pointer;\n\t\ttransition: 0.1s;\n\t}\n\n\t.file-icon {\n\t\tdisplay: inline-block;\n\t\theight: 20px;\n\t\tmargin-left: -1px;\n\t\t/* height: 20px; */\n\t\t/* padding: 3px 3px 3px 3px; */\n\t\tmargin: 0;\n\t\tflex-grow: 0;\n\t\tdisplay: inline-flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\n\t\ttransition: 0.1s;\n\t}\n\n\t.file-icon img {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.icon:hover {\n\t\tbackground: #eee;\n\t}\n\n\t.icon:hover :global(> *) {\n\t\tcolor: var(--block-info-text-color);\n\t}\n\n\t.icon :global(> *) {\n\t\ttransform: rotate(90deg);\n\t\ttransform-origin: 40% 50%;\n\t\ttransition: 0.2s;\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.hidden :global(> *) {\n\t\ttransform: rotate(0);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tul {\n\t\tmargin-left: 26px;\n\t\tpadding-left: 0;\n\t\tlist-style: none;\n\t}\n\n\tli {\n\t\tmargin-left: 0;\n\t\tpadding-left: 0;\n\t\t/* display: flex; */\n\t\talign-items: center;\n\t\tmargin: 8px 0;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--scale-00);\n\t}\n\n\t.wrap {\n\t\tdisplay: flex;\n\t\tgap: 8px;\n\t\talign-items: center;\n\t}\n</style>\n", "import { writable, type Readable } from \"svelte/store\";\nimport { dequal } from \"dequal\";\nexport interface Node {\n\ttype: \"file\" | \"folder\";\n\tpath: string;\n\tchildren?: Node[];\n\tchecked: boolean;\n\tchildren_visible: boolean;\n\tlast?: Node | null;\n\tparent: Node | null;\n\tprevious?: Node | null;\n}\n\nexport type SerialisedNode = Omit<\n\tNode,\n\t\"checked\" | \"children_visible\" | \"children\"\n> & { children?: SerialisedNode[] };\n\ninterface FSStore {\n\tsubscribe: Readable<Node[] | null>[\"subscribe\"];\n\tcreate_fs_graph: (serialised_node: SerialisedNode[]) => void;\n\n\tset_checked: (\n\t\tindices: number[],\n\t\tchecked: boolean,\n\t\tchecked_paths: string[][],\n\t\tfile_count: \"single\" | \"multiple\"\n\t) => string[][];\n\tset_checked_from_paths: (checked_paths: string[][]) => string[][];\n}\n\nexport const make_fs_store = (): FSStore => {\n\tconst { subscribe, set, update } = writable<Node[] | null>(null);\n\tlet root: Node = {\n\t\ttype: \"folder\",\n\t\tpath: \"\",\n\t\tchecked: false,\n\t\tchildren_visible: false,\n\t\tparent: null\n\t};\n\n\tfunction create_fs_graph(serialised_node: SerialisedNode[]): void {\n\t\troot.children = process_tree(serialised_node);\n\t\tset(root.children);\n\t}\n\n\tlet old_checked_paths: string[][] = [];\n\n\tfunction set_checked_from_paths(checked_paths: string[][]): string[][] {\n\t\tif (dequal(checked_paths, old_checked_paths)) {\n\t\t\treturn checked_paths;\n\t\t}\n\t\told_checked_paths = checked_paths;\n\t\tcheck_node_and_children(root.children, false, []);\n\t\tconst new_checked_paths: string[][] = [];\n\t\tconst seen_nodes = new Set();\n\t\tfor (let i = 0; i < checked_paths.length; i++) {\n\t\t\tlet _node = root;\n\t\t\tlet _path = [];\n\t\t\tfor (let j = 0; j < checked_paths[i].length; j++) {\n\t\t\t\tif (!_node?.children) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t_path.push(checked_paths[i][j]);\n\t\t\t\t_node = _node.children!.find((v) => v.path === checked_paths[i][j])!;\n\t\t\t}\n\n\t\t\tif (!_node) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t_node.checked = true;\n\t\t\tensure_visible(_node);\n\t\t\tconst nodes = check_node_and_children(_node.children, true, [_node]);\n\t\t\tcheck_parent(_node);\n\n\t\t\tnodes.forEach((node) => {\n\t\t\t\tconst path = get_full_path(node);\n\t\t\t\tif (seen_nodes.has(path.join(\"/\"))) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (node.type === \"file\") {\n\t\t\t\t\tnew_checked_paths.push(path);\n\t\t\t\t}\n\t\t\t\tseen_nodes.add(path.join(\"/\"));\n\t\t\t});\n\t\t}\n\n\t\tset(root.children!);\n\n\t\treturn new_checked_paths;\n\t}\n\n\tfunction set_checked(\n\t\tindices: number[],\n\t\tchecked: boolean,\n\t\tchecked_paths: string[][],\n\t\tfile_count: \"single\" | \"multiple\"\n\t): string[][] {\n\t\tlet _node = root;\n\n\t\tif (file_count === \"single\") {\n\t\t\tcheck_node_and_children(root.children, false, []);\n\t\t\tset(root.children!);\n\t\t}\n\n\t\tfor (let i = 0; i < indices.length; i++) {\n\t\t\t_node = _node.children![indices[i]];\n\t\t}\n\n\t\t_node.checked = checked;\n\t\tconst nodes = check_node_and_children(_node.children, checked, [_node]);\n\n\t\tlet new_checked_paths = new Map(checked_paths.map((v) => [v.join(\"/\"), v]));\n\n\t\tfor (let i = 0; i < nodes.length; i++) {\n\t\t\tconst _path = get_full_path(nodes[i]);\n\t\t\tif (!checked) {\n\t\t\t\tnew_checked_paths.delete(_path.join(\"/\"));\n\t\t\t} else if (checked) {\n\t\t\t\tif (file_count === \"single\") {\n\t\t\t\t\tnew_checked_paths = new Map();\n\t\t\t\t}\n\n\t\t\t\tif (nodes[i].type === \"file\") {\n\t\t\t\t\tnew_checked_paths.set(_path.join(\"/\"), _path);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tcheck_parent(_node);\n\t\tset(root.children!);\n\t\told_checked_paths = Array.from(new_checked_paths).map((v) => v[1]);\n\t\treturn old_checked_paths;\n\t}\n\n\treturn {\n\t\tsubscribe,\n\t\tcreate_fs_graph,\n\t\tset_checked,\n\t\tset_checked_from_paths\n\t};\n};\n\nfunction ensure_visible(node: Node): void {\n\tif (node.parent) {\n\t\tnode.parent.children_visible = true;\n\t\tensure_visible(node.parent);\n\t}\n}\n\nfunction process_tree(\n\tnode: SerialisedNode[],\n\tdepth = 0,\n\tpath_segments: string[] = [],\n\tparent: Node | null = null\n): Node[] {\n\tconst folders: Node[] = [];\n\tconst files: Node[] = [];\n\n\tfor (let i = 0; i < node.length; i++) {\n\t\tlet n: (typeof node)[number] = node[i];\n\n\t\tif (n.type === \"file\") {\n\t\t\tlet index = files.findIndex(\n\t\t\t\t(v) => v.path.toLocaleLowerCase() >= n.path.toLocaleLowerCase()\n\t\t\t);\n\n\t\t\tconst _node: Node = {\n\t\t\t\tchildren: undefined,\n\t\t\t\ttype: \"file\",\n\t\t\t\tpath: n.path,\n\t\t\t\tchecked: false,\n\t\t\t\tchildren_visible: false,\n\t\t\t\tparent: parent\n\t\t\t};\n\n\t\t\tfiles.splice(index === -1 ? files.length : index, 0, _node);\n\t\t} else {\n\t\t\tlet index = folders.findIndex(\n\t\t\t\t(v) => v.path.toLocaleLowerCase() >= n.path.toLocaleLowerCase()\n\t\t\t);\n\n\t\t\tconst _node: Node = {\n\t\t\t\ttype: \"folder\",\n\t\t\t\tpath: n.path,\n\t\t\t\tchecked: false,\n\t\t\t\tchildren_visible: false,\n\t\t\t\tparent: parent\n\t\t\t};\n\n\t\t\tconst children = process_tree(\n\t\t\t\tn.children!,\n\t\t\t\tdepth + 1,\n\t\t\t\t[...path_segments, n.path],\n\t\t\t\t_node\n\t\t\t);\n\n\t\t\t_node.children = children;\n\n\t\t\tfolders.splice(index === -1 ? folders.length : index, 0, _node);\n\t\t}\n\t}\n\n\tconst last = files[files.length - 1] || folders[folders.length - 1];\n\n\tfor (let i = 0; i < folders.length; i++) {\n\t\tfolders[i].last = last;\n\t\tfolders[i].previous = folders[i - 1] || null;\n\t}\n\n\tfor (let i = 0; i < files.length; i++) {\n\t\tif (i === 0) {\n\t\t\tfiles[i].previous = folders[folders.length - 1] || null;\n\t\t} else {\n\t\t\tfiles[i].previous = files[i - 1] || null;\n\t\t}\n\t\tfiles[i].last = last;\n\t}\n\n\treturn Array().concat(folders, files);\n}\n\nfunction get_full_path(node: Node, path: string[] = []): string[] {\n\tconst new_path = [node.path, ...path];\n\n\tif (node.parent) {\n\t\treturn get_full_path(node.parent, new_path);\n\t}\n\treturn new_path;\n}\n\nfunction check_node_and_children(\n\tnode: Node[] | null | undefined,\n\tchecked: boolean,\n\tchecked_nodes: Node[]\n): Node[] {\n\t// console.log(node, checked);\n\tif (node === null || node === undefined) return checked_nodes;\n\tfor (let i = 0; i < node.length; i++) {\n\t\tnode[i].checked = checked;\n\t\tchecked_nodes.push(node[i]);\n\t\tif (checked) ensure_visible(node[i]);\n\n\t\tchecked_nodes.concat(\n\t\t\tcheck_node_and_children(node[i].children, checked, checked_nodes)\n\t\t);\n\t}\n\n\treturn checked_nodes;\n}\n\nfunction check_parent(node: Node | null | undefined): void {\n\tif (node === null || node === undefined || !node.parent) return;\n\tlet _node = node.last;\n\tlet nodes_checked = [];\n\twhile (_node) {\n\t\tnodes_checked.push(_node.checked);\n\t\t_node = _node.previous;\n\t}\n\n\tif (nodes_checked.every((v) => v === true)) {\n\t\tnode.parent!.checked = true;\n\t\tcheck_parent(node?.parent);\n\t} else if (nodes_checked.some((v) => v === false)) {\n\t\tnode.parent!.checked = false;\n\t\tcheck_parent(node?.parent);\n\t}\n}\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { dequal } from \"dequal\";\n\timport FileTree from \"./FileTree.svelte\";\n\timport { make_fs_store } from \"./utils\";\n\timport { File } from \"@gradio/icons\";\n\timport { Empty } from \"@gradio/atoms\";\n\n\texport let mode: \"static\" | \"interactive\";\n\texport let server: any;\n\texport let file_count: \"single\" | \"multiple\" = \"multiple\";\n\n\texport let value: string[][] = [];\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: typeof value;\n\t}>();\n\tconst tree = make_fs_store();\n\n\tserver.ls().then((v: any) => {\n\t\ttree.create_fs_graph(v);\n\t});\n\n\t$: value.length && $tree && set_checked_from_paths();\n\n\tfunction set_checked_from_paths(): void {\n\t\tvalue = file_count === \"single\" ? [value[0] || []] : value;\n\t\tvalue = tree.set_checked_from_paths(value);\n\t\tif (!dequal(value, old_value)) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\", value);\n\t\t}\n\t}\n\n\tlet old_value: typeof value = [];\n\tfunction handle_select({\n\t\tnode_indices,\n\t\tchecked\n\t}: {\n\t\tnode_indices: number[];\n\t\tchecked: boolean;\n\t}): void {\n\t\tvalue = tree.set_checked(node_indices, checked, value, file_count);\n\t\tif (!dequal(value, old_value)) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\", value);\n\t\t}\n\t}\n</script>\n\n{#if $tree && $tree.length}\n\t<div class=\"file-wrap\">\n\t\t<FileTree\n\t\t\ttree={$tree}\n\t\t\t{mode}\n\t\t\ton:check={({ detail }) => handle_select(detail)}\n\t\t\t{file_count}\n\t\t/>\n\t</div>\n{:else}\n\t<Empty unpadded_box={true} size=\"large\"><File /></Empty>\n{/if}\n\n<style>\n\t.file-wrap {\n\t\theight: 100%;\n\t\toverflow: auto;\n\t}\n</style>\n"], "names": ["has", "find", "iter", "tar", "key", "dequal", "foo", "bar", "ctor", "len", "tmp", "insert", "target", "svg", "anchor", "append", "g", "path", "toggle_class", "input", "ctx", "value", "$$props", "disabled", "dispatch", "createEventDispatcher", "FileIcon", "attr", "img", "img_src_value", "span", "dirty", "filetree_changes", "if_block1", "create_if_block", "li", "set_data", "t2", "t2_value", "i", "ul", "each_blocks", "mode", "tree", "icons", "node_indices", "file_count", "dispatch_change", "tick", "change_handler", "keydown_handler", "make_fs_store", "subscribe", "set", "update", "writable", "root", "create_fs_graph", "serialised_node", "process_tree", "old_checked_paths", "set_checked_from_paths", "checked_paths", "check_node_and_children", "new_checked_paths", "seen_nodes", "_node", "_path", "j", "ensure_visible", "nodes", "check_parent", "node", "get_full_path", "set_checked", "indices", "checked", "v", "depth", "path_segments", "parent", "folders", "files", "n", "index", "children", "last", "new_path", "checked_nodes", "nodes_checked", "div", "server", "$$invalidate", "old_value", "handle_select", "detail", "$tree"], "mappings": "maAAA,IAAIA,EAAM,OAAO,UAAU,eAE3B,SAASC,GAAKC,EAAMC,EAAKC,EAAK,CAC7B,IAAKA,KAAOF,EAAK,OAChB,GAAIG,EAAOD,EAAKD,CAAG,EAAG,OAAOC,CAE/B,CAEO,SAASC,EAAOC,EAAKC,EAAK,CAChC,IAAIC,EAAMC,EAAKC,EACf,GAAIJ,IAAQC,EAAK,MAAO,GAExB,GAAID,GAAOC,IAAQC,EAAKF,EAAI,eAAiBC,EAAI,YAAa,CAC7D,GAAIC,IAAS,KAAM,OAAOF,EAAI,YAAcC,EAAI,UAChD,GAAIC,IAAS,OAAQ,OAAOF,EAAI,aAAeC,EAAI,WAEnD,GAAIC,IAAS,MAAO,CACnB,IAAKC,EAAIH,EAAI,UAAYC,EAAI,OAC5B,KAAOE,KAASJ,EAAOC,EAAIG,CAAG,EAAGF,EAAIE,CAAG,CAAC,GAAE,CAE5C,OAAOA,IAAQ,GAGhB,GAAID,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EACFC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACH,EAAI,IAAIG,CAAG,EAAG,MAAO,GAE3B,MAAO,GAGR,GAAIF,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EAAI,CAAC,EACPC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACL,EAAOI,EAAI,CAAC,EAAGF,EAAI,IAAIG,CAAG,CAAC,EAC/B,MAAO,GAGT,MAAO,GAGR,GAAIF,IAAS,YACZF,EAAM,IAAI,WAAWA,CAAG,EACxBC,EAAM,IAAI,WAAWA,CAAG,UACdC,IAAS,SAAU,CAC7B,IAAKC,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAI,QAAQG,CAAG,IAAMF,EAAI,QAAQE,CAAG,GAAE,CAEvD,OAAOA,IAAQ,GAGhB,GAAI,YAAY,OAAOH,CAAG,EAAG,CAC5B,IAAKG,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAIG,CAAG,IAAMF,EAAIE,CAAG,GAAE,CAEvC,OAAOA,IAAQ,GAGhB,GAAI,CAACD,GAAQ,OAAOF,GAAQ,SAAU,CACrCG,EAAM,EACN,IAAKD,KAAQF,EAEZ,GADIN,EAAI,KAAKM,EAAKE,CAAI,GAAK,EAAEC,GAAO,CAACT,EAAI,KAAKO,EAAKC,CAAI,GACnD,EAAEA,KAAQD,IAAQ,CAACF,EAAOC,EAAIE,CAAI,EAAGD,EAAIC,CAAI,CAAC,EAAG,MAAO,GAE7D,OAAO,OAAO,KAAKD,CAAG,EAAE,SAAWE,GAIrC,OAAOH,IAAQA,GAAOC,IAAQA,CAC/B,8qBCnFAI,EAaKC,EAAAC,EAAAC,CAAA,EANJC,EAKGF,EAAAG,CAAA,EAJFD,EAGCC,EAAAC,CAAA,qNCEcC,EAAAC,EAAA,WAAAC,OAAaA,EAAK,CAAA,CAAA,UALnCT,EAMCC,EAAAO,EAAAL,CAAA,YALcM,EAAK,CAAA,sGAALA,EAAK,CAAA,QAIHF,EAAAC,EAAA,WAAAC,OAAaA,EAAK,CAAA,CAAA,4DAXvB,MAAAC,CAAc,EAAAC,GACd,SAAAC,CAAiB,EAAAD,EAEtB,MAAAE,EAAWC,iBAIHJ,EAAK,KAAA,2BAEHG,EAAS,SAAUH,CAAK,kLCXzC,MAAeK,GAAA,yTCsDCA,EAAQ,GAAAC,EAAAC,EAAA,MAAAC,CAAA,uGADnBlB,EAEMC,EAAAkB,EAAAhB,CAAA,EADLC,EAAqCe,EAAAF,CAAA,mRAZtBR,EAAI,CAAA,EAACA,EAAC,EAAA,CAAA,EAAE,gBAAgB,UAFxCT,EAWAC,EAAAkB,EAAAhB,CAAA,0GATgBM,EAAI,CAAA,EAACA,EAAC,EAAA,CAAA,EAAE,gBAAgB,uJAmBlCA,EAAQ,EAAA,aAGI,aAAA,CAAA,GAAAA,KAAcA,EAAC,EAAA,CAAA,gIAH3BA,EAAQ,EAAA,uBAGIW,EAAA,IAAAC,EAAA,aAAA,CAAA,GAAAZ,KAAcA,EAAC,EAAA,CAAA,+KAPjCA,EAAI,EAAA,EAAA,mGAxBMA,EAAI,CAAA,IAAK,UACjBA,QAAS,UAAYA,EAAU,CAAA,IAAK,UAC1BA,EAAO,EAAA,IAAA,iBAAPA,EAAO,EAAA,2GAIf,OAAAA,QAAS,SAAQ,0BAoBlB,IAAAa,EAAAb,OAAYA,EAAgB,EAAA,GAAAc,GAAAd,CAAA,4KA7BlCT,EAuCIC,EAAAuB,EAAArB,CAAA,EAtCHC,EA2BMoB,EAAAL,CAAA,6HAzBMV,EAAI,CAAA,IAAK,UACjBA,QAAS,UAAYA,EAAU,CAAA,IAAK,iCAC1BA,EAAO,EAAA,4KAsBnBA,EAAI,EAAA,EAAA,KAAAgB,GAAAC,EAAAC,CAAA,EAEDlB,OAAYA,EAAgB,EAAA,qPA9B5BA,EAAI,CAAA,CAAA,uBAAT,OAAImB,GAAA,4JADP5B,EA2CIC,EAAA4B,EAAA1B,CAAA,+EA1CIM,EAAI,CAAA,CAAA,oBAAT,OAAImB,GAAA,EAAA,2GAAJ,OAAIA,EAAAE,EAAA,OAAAF,GAAA,yCAAJ,OAAIA,GAAA,kIArBK,KAAAG,CAA8B,EAAApB,GAC9B,KAAAqB,EAAI,EAAA,EAAArB,GACJ,MAAAsB,EAAK,EAAA,EAAAtB,GACL,aAAAuB,EAAY,EAAA,EAAAvB,EACZ,CAAA,WAAAwB,EAAoC,UAAU,EAAAxB,EAEnD,MAAAE,EAAWC,IAIF,eAAAsB,EAAgBR,EAAS,OACjCS,GAAI,EAEVxB,EAAS,QAAO,CACf,aAAY,CAAA,GAAMqB,EAAcN,CAAC,EACjC,QAAU,CAAAI,EAAKJ,CAAC,EAAE,oDAaC,MAAAU,EAAAV,GAAAQ,EAAgBR,CAAC,WAQ/BI,EAAKJ,CAAC,EAAE,kBAAoBI,EAAKJ,CAAC,EAAE,iBAAgBI,CAAA,EAGvCO,EAAA,CAAAX,EAAA,CAAA,IAAAnC,CAAG,IACjBA,IAAQ,SACPuC,EAAKJ,CAAC,EAAE,iBAAoB,CAAAI,EAAKJ,CAAC,EAAE,iBAAgBI,CAAA,+VClBrD,MAAMQ,GAAgB,IAAe,CAC3C,KAAM,CAAE,UAAAC,EAAW,IAAAC,EAAK,OAAAC,CAAO,EAAIC,GAAwB,IAAI,EAC/D,IAAIC,EAAa,CAChB,KAAM,SACN,KAAM,GACN,QAAS,GACT,iBAAkB,GAClB,OAAQ,IAAA,EAGT,SAASC,EAAgBC,EAAyC,CAC5DF,EAAA,SAAWG,GAAaD,CAAe,EAC5CL,EAAIG,EAAK,QAAQ,CAClB,CAEA,IAAII,EAAgC,CAAA,EAEpC,SAASC,EAAuBC,EAAuC,CAClE,GAAAzD,EAAOyD,EAAeF,CAAiB,EACnC,OAAAE,EAEYF,EAAAE,EACpBC,EAAwBP,EAAK,SAAU,GAAO,CAAE,CAAA,EAChD,MAAMQ,EAAgC,CAAA,EAChCC,MAAiB,IACvB,QAAS1B,EAAI,EAAGA,EAAIuB,EAAc,OAAQvB,IAAK,CAC9C,IAAI2B,EAAQV,EACRW,EAAQ,CAAA,EACZ,QAASC,EAAI,EAAGA,EAAIN,EAAcvB,CAAC,EAAE,OAAQ6B,IACvCF,GAAO,WAGZC,EAAM,KAAKL,EAAcvB,CAAC,EAAE6B,CAAC,CAAC,EACtBF,EAAAA,EAAM,SAAU,KAAM,GAAM,EAAE,OAASJ,EAAcvB,CAAC,EAAE6B,CAAC,CAAC,GAGnE,GAAI,CAACF,EACJ,SAGDA,EAAM,QAAU,GAChBG,EAAeH,CAAK,EACpB,MAAMI,EAAQP,EAAwBG,EAAM,SAAU,GAAM,CAACA,CAAK,CAAC,EACnEK,EAAaL,CAAK,EAEZI,EAAA,QAASE,GAAS,CACjB,MAAAvD,EAAOwD,EAAcD,CAAI,EAC3BP,EAAW,IAAIhD,EAAK,KAAK,GAAG,CAAC,IAG7BuD,EAAK,OAAS,QACjBR,EAAkB,KAAK/C,CAAI,EAE5BgD,EAAW,IAAIhD,EAAK,KAAK,GAAG,CAAC,EAAA,CAC7B,EAGF,OAAAoC,EAAIG,EAAK,QAAS,EAEXQ,CACR,CAEA,SAASU,EACRC,EACAC,EACAd,EACAhB,EACa,CACb,IAAIoB,EAAQV,EAERV,IAAe,WAClBiB,EAAwBP,EAAK,SAAU,GAAO,CAAE,CAAA,EAChDH,EAAIG,EAAK,QAAS,GAGnB,QAASjB,EAAI,EAAGA,EAAIoC,EAAQ,OAAQpC,IACnC2B,EAAQA,EAAM,SAAUS,EAAQpC,CAAC,CAAC,EAGnC2B,EAAM,QAAUU,EAChB,MAAMN,EAAQP,EAAwBG,EAAM,SAAUU,EAAS,CAACV,CAAK,CAAC,EAEtE,IAAIF,EAAoB,IAAI,IAAIF,EAAc,IAAKe,GAAM,CAACA,EAAE,KAAK,GAAG,EAAGA,CAAC,CAAC,CAAC,EAE1E,QAAStC,EAAI,EAAGA,EAAI+B,EAAM,OAAQ/B,IAAK,CACtC,MAAM4B,EAAQM,EAAcH,EAAM/B,CAAC,CAAC,EAC/BqC,EAEMA,IACN9B,IAAe,WAClBkB,MAAwB,KAGrBM,EAAM/B,CAAC,EAAE,OAAS,QACrByB,EAAkB,IAAIG,EAAM,KAAK,GAAG,EAAGA,CAAK,GAP7CH,EAAkB,OAAOG,EAAM,KAAK,GAAG,CAAC,EAY1C,OAAAI,EAAaL,CAAK,EAClBb,EAAIG,EAAK,QAAS,EACEI,EAAA,MAAM,KAAKI,CAAiB,EAAE,IAAKa,GAAMA,EAAE,CAAC,CAAC,EAC1DjB,CACR,CAEO,MAAA,CACN,UAAAR,EACA,gBAAAK,EACA,YAAAiB,EACA,uBAAAb,CAAA,CAEF,EAEA,SAASQ,EAAeG,EAAkB,CACrCA,EAAK,SACRA,EAAK,OAAO,iBAAmB,GAC/BH,EAAeG,EAAK,MAAM,EAE5B,CAEA,SAASb,GACRa,EACAM,EAAQ,EACRC,EAA0B,CAAC,EAC3BC,EAAsB,KACb,CACT,MAAMC,EAAkB,CAAA,EAClBC,EAAgB,CAAA,EAEtB,QAAS3C,EAAI,EAAGA,EAAIiC,EAAK,OAAQjC,IAAK,CACjC,IAAA4C,EAA2BX,EAAKjC,CAAC,EAEjC,GAAA4C,EAAE,OAAS,OAAQ,CACtB,IAAIC,EAAQF,EAAM,UAChBL,GAAMA,EAAE,KAAK,qBAAuBM,EAAE,KAAK,kBAAkB,CAAA,EAG/D,MAAMjB,EAAc,CACnB,SAAU,OACV,KAAM,OACN,KAAMiB,EAAE,KACR,QAAS,GACT,iBAAkB,GAClB,OAAAH,CAAA,EAGDE,EAAM,OAAOE,IAAU,GAAKF,EAAM,OAASE,EAAO,EAAGlB,CAAK,MACpD,CACN,IAAIkB,EAAQH,EAAQ,UAClBJ,GAAMA,EAAE,KAAK,qBAAuBM,EAAE,KAAK,kBAAkB,CAAA,EAG/D,MAAMjB,EAAc,CACnB,KAAM,SACN,KAAMiB,EAAE,KACR,QAAS,GACT,iBAAkB,GAClB,OAAAH,CAAA,EAGKK,EAAW1B,GAChBwB,EAAE,SACFL,EAAQ,EACR,CAAC,GAAGC,EAAeI,EAAE,IAAI,EACzBjB,CAAA,EAGDA,EAAM,SAAWmB,EAEjBJ,EAAQ,OAAOG,IAAU,GAAKH,EAAQ,OAASG,EAAO,EAAGlB,CAAK,GAI1D,MAAAoB,EAAOJ,EAAMA,EAAM,OAAS,CAAC,GAAKD,EAAQA,EAAQ,OAAS,CAAC,EAElE,QAAS1C,EAAI,EAAGA,EAAI0C,EAAQ,OAAQ1C,IAC3B0C,EAAA1C,CAAC,EAAE,KAAO+C,EAClBL,EAAQ1C,CAAC,EAAE,SAAW0C,EAAQ1C,EAAI,CAAC,GAAK,KAGzC,QAASA,EAAI,EAAGA,EAAI2C,EAAM,OAAQ3C,IAC7BA,IAAM,EACT2C,EAAM3C,CAAC,EAAE,SAAW0C,EAAQA,EAAQ,OAAS,CAAC,GAAK,KAEnDC,EAAM3C,CAAC,EAAE,SAAW2C,EAAM3C,EAAI,CAAC,GAAK,KAE/B2C,EAAA3C,CAAC,EAAE,KAAO+C,EAGjB,OAAO,MAAM,EAAE,OAAOL,EAASC,CAAK,CACrC,CAEA,SAAST,EAAcD,EAAYvD,EAAiB,GAAc,CACjE,MAAMsE,EAAW,CAACf,EAAK,KAAM,GAAGvD,CAAI,EAEpC,OAAIuD,EAAK,OACDC,EAAcD,EAAK,OAAQe,CAAQ,EAEpCA,CACR,CAEA,SAASxB,EACRS,EACAI,EACAY,EACS,CAEL,GAAAhB,GAAS,KAAmC,OAAAgB,EAChD,QAASjD,EAAI,EAAGA,EAAIiC,EAAK,OAAQjC,IAC3BiC,EAAAjC,CAAC,EAAE,QAAUqC,EACJY,EAAA,KAAKhB,EAAKjC,CAAC,CAAC,EACtBqC,GAAwBP,EAAAG,EAAKjC,CAAC,CAAC,EAErBiD,EAAA,OACbzB,EAAwBS,EAAKjC,CAAC,EAAE,SAAUqC,EAASY,CAAa,CAAA,EAI3D,OAAAA,CACR,CAEA,SAASjB,EAAaC,EAAqC,CAC1D,GAAIA,GAAS,MAA8B,CAACA,EAAK,OAAQ,OACzD,IAAIN,EAAQM,EAAK,KACbiB,EAAgB,CAAA,EACpB,KAAOvB,GACQuB,EAAA,KAAKvB,EAAM,OAAO,EAChCA,EAAQA,EAAM,SAGXuB,EAAc,MAAOZ,GAAMA,IAAM,EAAI,GACxCL,EAAK,OAAQ,QAAU,GACvBD,EAAaC,GAAM,MAAM,GACfiB,EAAc,KAAMZ,GAAMA,IAAM,EAAK,IAC/CL,EAAK,OAAQ,QAAU,GACvBD,EAAaC,GAAM,MAAM,EAE3B,6DChNsB,6SAPbpD,EAAK,CAAA,mIAFbT,EAOKC,EAAA8E,EAAA5E,CAAA,mDALGM,EAAK,CAAA,6XAHTA,EAAK,CAAA,GAAIA,EAAK,CAAA,EAAC,OAAM,mUA1Cd,KAAAsB,CAA8B,EAAApB,GAC9B,OAAAqE,CAAW,EAAArE,EACX,CAAA,WAAAwB,EAAoC,UAAU,EAAAxB,GAE9C,MAAAD,EAAK,EAAA,EAAAC,EAEV,MAAAE,EAAWC,IAGXkB,EAAOQ,yBAEbwC,EAAO,GAAE,EAAG,KAAMd,GAAM,CACvBlC,EAAK,gBAAgBkC,CAAC,aAKdhB,GAAsB,KAC9BxC,EAAQyB,IAAe,UAAYzB,EAAM,CAAC,GAAA,EAAA,EAAWA,CAAK,EAC1DuE,EAAA,EAAAvE,EAAQsB,EAAK,uBAAuBtB,CAAK,CAAA,EACpChB,EAAOgB,EAAOwE,CAAS,IAC3BA,EAAYxE,EACZG,EAAS,SAAUH,CAAK,OAItBwE,EAAS,CAAA,WACJC,EAAa,CACrB,aAAAjD,EACA,QAAA+B,GAAO,KAKPvD,EAAQsB,EAAK,YAAYE,EAAc+B,EAASvD,EAAOyB,CAAU,CAAA,EAC5DzC,EAAOgB,EAAOwE,CAAS,IAC3BA,EAAYxE,EACZG,EAAS,SAAUH,CAAK,aAUX,OAAA0E,CAAM,IAAOD,EAAcC,CAAM,gLAhC7C1E,EAAM,QAAU2E,GAASnC", "x_google_ignoreList": [0]}