import{S as h,e as c,s as f,f as o,g as t,h as d,j as l,n as r,k as u}from"./index-7674dbb6.js";function g(i){let e,s,n;return{c(){e=o("svg"),s=o("path"),n=o("polyline"),t(s,"d","M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"),t(n,"points","13 2 13 9 20 9"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-file")},m(a,p){d(a,e,p),l(e,s),l(e,n)},p:r,i:r,o:r,d(a){a&&u(e)}}}class v extends h{constructor(e){super(),c(this,e,null,g,f,{})}}export{v as F};
//# sourceMappingURL=File-29fa02e0.js.map
