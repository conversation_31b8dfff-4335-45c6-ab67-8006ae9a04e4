import{S as v,e as x,s as $,f as fe,g as w,h as R,j as C,n as W,k as H,y as Be,m as J,o as U,Q as Fe,p as I,w as q,r as X,u as M,v as Y,B as ee,C as he,I as oe,K as G,ai as ae,ao as me,O as Ne,t as le,N as z,x as ne,R as ue,F as K,G as Q,ap as ie,H as P,an as Se,a0 as Ge,a1 as Ke}from"./index-7674dbb6.js";import{b as be}from"./Button-770df9ba.js";import{B as qe}from"./BlockTitle-2eb1c338.js";function Qe(n){let l,e;return{c(){l=fe("svg"),e=fe("path"),w(e,"d","M5 8l4 4 4-4z"),w(l,"class","dropdown-arrow svelte-p5edak"),w(l,"xmlns","http://www.w3.org/2000/svg"),w(l,"width","18"),w(l,"height","18"),w(l,"viewBox","0 0 18 18")},m(f,o){R(f,l,o),C(l,e)},p:W,i:W,o:W,d(f){f&&H(l)}}}class Ce extends v{constructor(l){super(),x(this,l,null,Qe,$,{})}}function Pe(n){let l,e;return{c(){l=fe("svg"),e=fe("path"),w(e,"d","M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"),w(l,"xmlns","http://www.w3.org/2000/svg"),w(l,"width","16"),w(l,"height","16"),w(l,"viewBox","0 0 24 24")},m(f,o){R(f,l,o),C(l,e)},p:W,i:W,o:W,d(f){f&&H(l)}}}class ze extends v{constructor(l){super(),x(this,l,null,Pe,$,{})}}function we(n,l,e){const f=n.slice();return f[24]=l[e],f}function ge(n){let l,e,f,o,i,u=oe(n[1]),s=[];for(let t=0;t<u.length;t+=1)s[t]=ke(we(n,u,t));return{c(){l=J("ul");for(let t=0;t<s.length;t+=1)s[t].c();w(l,"class","options svelte-1aonegi"),w(l,"role","listbox"),G(l,"top",n[9]),G(l,"bottom",n[10]),G(l,"max-height",`calc(${n[11]}px - var(--window-padding))`),G(l,"width",n[8]+"px")},m(t,_){R(t,l,_);for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(l,null);n[21](l),f=!0,o||(i=I(l,"mousedown",ae(n[20])),o=!0)},p(t,_){if(_&51){u=oe(t[1]);let c;for(c=0;c<u.length;c+=1){const p=we(t,u,c);s[c]?s[c].p(p,_):(s[c]=ke(p),s[c].c(),s[c].m(l,null))}for(;c<s.length;c+=1)s[c].d(1);s.length=u.length}_&512&&G(l,"top",t[9]),_&1024&&G(l,"bottom",t[10]),_&2048&&G(l,"max-height",`calc(${t[11]}px - var(--window-padding))`),_&256&&G(l,"width",t[8]+"px")},i(t){f||(t&&Be(()=>{f&&(e||(e=me(l,be,{duration:200,y:5},!0)),e.run(1))}),f=!0)},o(t){t&&(e||(e=me(l,be,{duration:200,y:5},!1)),e.run(0)),f=!1},d(t){t&&H(l),Ne(s,t),n[21](null),t&&e&&e.end(),o=!1,i()}}}function ke(n){let l,e,f,o=n[0][n[24]][0]+"",i,u,s,t,_;return{c(){l=J("li"),e=J("span"),e.textContent="✓",f=U(),i=le(o),u=U(),w(e,"class","inner-item svelte-1aonegi"),z(e,"hide",!n[4].includes(n[24])),w(l,"class","item svelte-1aonegi"),w(l,"data-index",s=n[24]),w(l,"aria-label",t=n[0][n[24]][0]),w(l,"data-testid","dropdown-option"),w(l,"role","option"),w(l,"aria-selected",_=n[4].includes(n[24])),z(l,"selected",n[4].includes(n[24])),z(l,"active",n[24]===n[5]),z(l,"bg-gray-100",n[24]===n[5]),z(l,"dark:bg-gray-600",n[24]===n[5])},m(c,p){R(c,l,p),C(l,e),C(l,f),C(l,i),C(l,u)},p(c,p){p&18&&z(e,"hide",!c[4].includes(c[24])),p&3&&o!==(o=c[0][c[24]][0]+"")&&ne(i,o),p&2&&s!==(s=c[24])&&w(l,"data-index",s),p&3&&t!==(t=c[0][c[24]][0])&&w(l,"aria-label",t),p&18&&_!==(_=c[4].includes(c[24]))&&w(l,"aria-selected",_),p&18&&z(l,"selected",c[4].includes(c[24])),p&34&&z(l,"active",c[24]===c[5]),p&34&&z(l,"bg-gray-100",c[24]===c[5]),p&34&&z(l,"dark:bg-gray-600",c[24]===c[5])},d(c){c&&H(l)}}}function Ve(n){let l,e,f,o,i;Be(n[18]);let u=n[2]&&!n[3]&&ge(n);return{c(){l=J("div"),e=U(),u&&u.c(),f=Fe(),w(l,"class","reference")},m(s,t){R(s,l,t),n[19](l),R(s,e,t),u&&u.m(s,t),R(s,f,t),o||(i=[I(window,"scroll",n[13]),I(window,"resize",n[18])],o=!0)},p(s,[t]){s[2]&&!s[3]?u?(u.p(s,t),t&12&&q(u,1)):(u=ge(s),u.c(),q(u,1),u.m(f.parentNode,f)):u&&(X(),M(u,1,1,()=>{u=null}),Y())},i(s){q(u)},o(s){M(u)},d(s){s&&(H(l),H(e),H(f)),n[19](null),u&&u.d(s),o=!1,ee(i)}}}function We(n,l,e){let{choices:f}=l,{filtered_indices:o}=l,{show_options:i=!1}=l,{disabled:u=!1}=l,{selected_indices:s=[]}=l,{active_index:t=null}=l,_,c,p,N,E,T,k,m,h,y;function d(){const{top:D,bottom:V}=E.getBoundingClientRect();e(15,_=D),e(16,c=y-V)}let a=null;function g(){i&&(a!==null&&clearTimeout(a),a=setTimeout(()=>{d(),a=null},10))}const S=he();function B(){e(12,y=window.innerHeight)}function b(D){ue[D?"unshift":"push"](()=>{E=D,e(6,E)})}const A=D=>S("change",D);function j(D){ue[D?"unshift":"push"](()=>{T=D,e(7,T)})}return n.$$set=D=>{"choices"in D&&e(0,f=D.choices),"filtered_indices"in D&&e(1,o=D.filtered_indices),"show_options"in D&&e(2,i=D.show_options),"disabled"in D&&e(3,u=D.disabled),"selected_indices"in D&&e(4,s=D.selected_indices),"active_index"in D&&e(5,t=D.active_index)},n.$$.update=()=>{if(n.$$.dirty&229588){if(i&&E){if(T&&s.length>0){let V=T.querySelectorAll("li");for(const F of Array.from(V))if(F.getAttribute("data-index")===s[0].toString()){T?.scrollTo?.(0,F.offsetTop);break}}d();const D=E.parentElement?.getBoundingClientRect();e(17,p=D?.height||0),e(8,N=D?.width||0)}c>_?(e(9,k=`${_}px`),e(11,h=c),e(10,m=null)):(e(10,m=`${c+p}px`),e(11,h=_-p),e(9,k=null))}},[f,o,i,u,s,t,E,T,N,k,m,h,y,g,S,_,c,p,B,b,A,j]}class Me extends v{constructor(l){super(),x(this,l,We,Ve,$,{choices:0,filtered_indices:1,show_options:2,disabled:3,selected_indices:4,active_index:5})}}function Xe(n,l){return(n%l+l)%l}function Te(n,l){return n.reduce((e,f,o)=>((!l||f[0].toLowerCase().includes(l.toLowerCase()))&&e.push(o),e),[])}function Je(n,l,e){n("change",l),e||n("input")}function Re(n,l,e){if(n.key==="Escape")return[!1,l];if((n.key==="ArrowDown"||n.key==="ArrowUp")&&e.length>=0)if(l===null)l=n.key==="ArrowDown"?e[0]:e[e.length-1];else{const f=e.indexOf(l),o=n.key==="ArrowUp"?-1:1;l=e[Xe(f+o,e.length)]}return[!0,l]}function Ye(n){let l;return{c(){l=le(n[0])},m(e,f){R(e,l,f)},p(e,f){f[0]&1&&ne(l,e[0])},d(e){e&&H(l)}}}function pe(n){let l,e;return l=new Ce({}),{c(){K(l.$$.fragment)},m(f,o){Q(l,f,o),e=!0},i(f){e||(q(l.$$.fragment,f),e=!0)},o(f){M(l.$$.fragment,f),e=!1},d(f){P(l,f)}}}function Ze(n){let l,e,f,o,i,u,s,t,_,c,p,N,E,T;e=new qe({props:{show_label:n[4],info:n[1],$$slots:{default:[Ye]},$$scope:{ctx:n}}});let k=!n[3]&&pe();return p=new Me({props:{show_options:n[12],choices:n[2],filtered_indices:n[9],disabled:n[3],selected_indices:n[10]===null?[]:[n[10]],active_index:n[14]}}),p.$on("change",n[15]),{c(){l=J("label"),K(e.$$.fragment),f=U(),o=J("div"),i=J("div"),u=J("div"),s=J("input"),_=U(),k&&k.c(),c=U(),K(p.$$.fragment),w(s,"class","border-none svelte-tq78c3"),s.disabled=n[3],w(s,"autocomplete","off"),s.readOnly=t=!n[7],z(s,"subdued",!n[13].includes(n[8])&&!n[6]),w(u,"class","secondary-wrap svelte-tq78c3"),w(i,"class","wrap-inner svelte-tq78c3"),z(i,"show_options",n[12]),w(o,"class","wrap svelte-tq78c3"),w(l,"class","svelte-tq78c3"),z(l,"container",n[5])},m(m,h){R(m,l,h),Q(e,l,null),C(l,f),C(l,o),C(o,i),C(i,u),C(u,s),ie(s,n[8]),n[28](s),C(u,_),k&&k.m(u,null),C(o,c),Q(p,o,null),N=!0,E||(T=[I(s,"input",n[27]),I(s,"keydown",n[18]),I(s,"blur",n[17]),I(s,"focus",n[16])],E=!0)},p(m,h){const y={};h[0]&16&&(y.show_label=m[4]),h[0]&2&&(y.info=m[1]),h[0]&1|h[1]&1&&(y.$$scope={dirty:h,ctx:m}),e.$set(y),(!N||h[0]&8)&&(s.disabled=m[3]),(!N||h[0]&128&&t!==(t=!m[7]))&&(s.readOnly=t),h[0]&256&&s.value!==m[8]&&ie(s,m[8]),(!N||h[0]&8512)&&z(s,"subdued",!m[13].includes(m[8])&&!m[6]),m[3]?k&&(X(),M(k,1,1,()=>{k=null}),Y()):k?h[0]&8&&q(k,1):(k=pe(),k.c(),q(k,1),k.m(u,null)),(!N||h[0]&4096)&&z(i,"show_options",m[12]);const d={};h[0]&4096&&(d.show_options=m[12]),h[0]&4&&(d.choices=m[2]),h[0]&512&&(d.filtered_indices=m[9]),h[0]&8&&(d.disabled=m[3]),h[0]&1024&&(d.selected_indices=m[10]===null?[]:[m[10]]),h[0]&16384&&(d.active_index=m[14]),p.$set(d),(!N||h[0]&32)&&z(l,"container",m[5])},i(m){N||(q(e.$$.fragment,m),q(k),q(p.$$.fragment,m),N=!0)},o(m){M(e.$$.fragment,m),M(k),M(p.$$.fragment,m),N=!1},d(m){m&&H(l),P(e),n[28](null),k&&k.d(),P(p),E=!1,ee(T)}}}function ve(n,l,e){let{label:f}=l,{info:o=void 0}=l,{value:i=[]}=l,u=[],{value_is_output:s=!1}=l,{choices:t}=l,_,{disabled:c=!1}=l,{show_label:p}=l,{container:N=!0}=l,{allow_custom_value:E=!1}=l,{filterable:T=!0}=l,k,m=!1,h,y,d="",a="",g=!1,S=[],B=null,b=null,A;const j=he();i?(A=t.map(O=>O[1]).indexOf(i),b=A,b===-1?(u=i,b=null):([d,u]=t[b],a=d)):t.length>0&&(A=0,b=0,[d,i]=t[b],u=i,a=d);function D(){i===void 0?e(8,d=""):y.includes(i)?e(8,d=h[y.indexOf(i)]):E?e(8,d=i):e(8,d="")}function V(O){if(e(10,b=parseInt(O.detail.target.dataset.index)),isNaN(b)){e(10,b=null);return}e(12,m=!1),e(14,B=null),k.blur()}function F(O){e(9,S=t.map((de,ce)=>ce)),e(12,m=!0),j("focus")}function Z(){E||e(8,d=h[y.indexOf(i)]),e(19,i=d),e(12,m=!1),e(14,B=null),j("blur")}function re(O){e(12,[m,B]=Re(O,B,S),m,(e(14,B),e(2,t),e(22,_),e(8,d),e(24,a),e(6,E),e(9,S),e(10,b),e(26,A),e(25,g),e(23,y))),O.key==="Enter"&&(B!==null?(e(10,b=B),e(12,m=!1),k.blur(),e(14,B=null)):h.includes(d)?(e(10,b=h.indexOf(d)),e(12,m=!1),e(14,B=null),k.blur()):E&&(e(19,i=d),e(10,b=null),e(12,m=!1),e(14,B=null),k.blur()))}Se(()=>{e(20,s=!1),e(25,g=!0)});function te(){d=this.value,e(8,d),e(10,b),e(26,A),e(25,g),e(2,t),e(23,y)}function se(O){ue[O?"unshift":"push"](()=>{k=O,e(11,k)})}return n.$$set=O=>{"label"in O&&e(0,f=O.label),"info"in O&&e(1,o=O.info),"value"in O&&e(19,i=O.value),"value_is_output"in O&&e(20,s=O.value_is_output),"choices"in O&&e(2,t=O.choices),"disabled"in O&&e(3,c=O.disabled),"show_label"in O&&e(4,p=O.show_label),"container"in O&&e(5,N=O.container),"allow_custom_value"in O&&e(6,E=O.allow_custom_value),"filterable"in O&&e(7,T=O.filterable)},n.$$.update=()=>{n.$$.dirty[0]&4&&(e(13,h=t.map(O=>O[0])),e(23,y=t.map(O=>O[1]))),n.$$.dirty[0]&109052932&&b!==A&&b!==null&&g&&(e(8,[d,i]=t[b],d,(e(19,i),e(10,b),e(26,A),e(25,g),e(2,t),e(23,y))),e(26,A=b),j("select",{index:b,value:y[b],selected:!0})),n.$$.dirty[0]&3670016&&i!=u&&(D(),Je(j,i,s),e(21,u=i)),n.$$.dirty[0]&20972356&&(t!==_||d!==a)&&(e(9,S=Te(t,d)),e(22,_=t),e(24,a=d),!E&&S.length>0&&e(14,B=S[0]))},[f,o,t,c,p,N,E,T,d,S,b,k,m,h,B,V,F,Z,re,i,s,u,_,y,a,g,A,te,se]}class ol extends v{constructor(l){super(),x(this,l,ve,Ze,$,{label:0,info:1,value:19,value_is_output:20,choices:2,disabled:3,show_label:4,container:5,allow_custom_value:6,filterable:7},null,[-1,-1])}}function ye(n,l,e){const f=n.slice();return f[39]=l[e],f}function xe(n){let l;return{c(){l=le(n[0])},m(e,f){R(e,l,f)},p(e,f){f[0]&1&&ne(l,e[0])},d(e){e&&H(l)}}}function $e(n){let l=n[39]+"",e;return{c(){e=le(l)},m(f,o){R(f,e,o)},p(f,o){o[0]&2048&&l!==(l=f[39]+"")&&ne(e,l)},d(f){f&&H(e)}}}function el(n){let l=n[14][n[39]]+"",e;return{c(){e=le(l)},m(f,o){R(f,e,o)},p(f,o){o[0]&18432&&l!==(l=f[14][f[39]]+"")&&ne(e,l)},d(f){f&&H(e)}}}function Oe(n){let l,e,f,o,i,u;e=new ze({});function s(){return n[30](n[39])}function t(..._){return n[31](n[39],..._)}return{c(){l=J("div"),K(e.$$.fragment),w(l,"class","token-remove svelte-7c67e4"),w(l,"role","button"),w(l,"tabindex","0"),w(l,"title",f=n[16]("common.remove")+" "+n[39])},m(_,c){R(_,l,c),Q(e,l,null),o=!0,i||(u=[I(l,"click",ae(s)),I(l,"keydown",ae(t))],i=!0)},p(_,c){n=_,(!o||c[0]&67584&&f!==(f=n[16]("common.remove")+" "+n[39]))&&w(l,"title",f)},i(_){o||(q(e.$$.fragment,_),o=!0)},o(_){M(e.$$.fragment,_),o=!1},d(_){_&&H(l),P(e),i=!1,ee(u)}}}function Ae(n){let l,e,f,o;function i(_,c){return typeof _[39]=="number"?el:$e}let u=i(n),s=u(n),t=!n[4]&&Oe(n);return{c(){l=J("div"),e=J("span"),s.c(),f=U(),t&&t.c(),w(e,"class","svelte-7c67e4"),w(l,"class","token svelte-7c67e4")},m(_,c){R(_,l,c),C(l,e),s.m(e,null),C(l,f),t&&t.m(l,null),o=!0},p(_,c){u===(u=i(_))&&s?s.p(_,c):(s.d(1),s=u(_),s&&(s.c(),s.m(e,null))),_[4]?t&&(X(),M(t,1,1,()=>{t=null}),Y()):t?(t.p(_,c),c[0]&16&&q(t,1)):(t=Oe(_),t.c(),q(t,1),t.m(l,null))},i(_){o||(q(t),o=!0)},o(_){M(t),o=!1},d(_){_&&H(l),s.d(),t&&t.d()}}}function De(n){let l,e,f,o=n[11].length>0&&Ee(n);return e=new Ce({}),{c(){o&&o.c(),l=U(),K(e.$$.fragment)},m(i,u){o&&o.m(i,u),R(i,l,u),Q(e,i,u),f=!0},p(i,u){i[11].length>0?o?(o.p(i,u),u[0]&2048&&q(o,1)):(o=Ee(i),o.c(),q(o,1),o.m(l.parentNode,l)):o&&(X(),M(o,1,1,()=>{o=null}),Y())},i(i){f||(q(o),q(e.$$.fragment,i),f=!0)},o(i){M(o),M(e.$$.fragment,i),f=!1},d(i){i&&H(l),o&&o.d(i),P(e,i)}}}function Ee(n){let l,e,f,o,i,u;return e=new ze({}),{c(){l=J("div"),K(e.$$.fragment),w(l,"role","button"),w(l,"tabindex","0"),w(l,"class","token-remove remove-all svelte-7c67e4"),w(l,"title",f=n[16]("common.clear"))},m(s,t){R(s,l,t),Q(e,l,null),o=!0,i||(u=[I(l,"click",n[20]),I(l,"keydown",n[34])],i=!0)},p(s,t){(!o||t[0]&65536&&f!==(f=s[16]("common.clear")))&&w(l,"title",f)},i(s){o||(q(e.$$.fragment,s),o=!0)},o(s){M(e.$$.fragment,s),o=!1},d(s){s&&H(l),P(e),i=!1,ee(u)}}}function ll(n){let l,e,f,o,i,u,s,t,_,c,p,N,E,T,k;e=new qe({props:{show_label:n[5],info:n[1],$$slots:{default:[xe]},$$scope:{ctx:n}}});let m=oe(n[11]),h=[];for(let a=0;a<m.length;a+=1)h[a]=Ae(ye(n,m,a));const y=a=>M(h[a],1,1,()=>{h[a]=null});let d=!n[4]&&De(n);return N=new Me({props:{show_options:n[13],choices:n[3],filtered_indices:n[10],disabled:n[4],selected_indices:n[11],active_index:n[15]}}),N.$on("change",n[19]),{c(){l=J("label"),K(e.$$.fragment),f=U(),o=J("div"),i=J("div");for(let a=0;a<h.length;a+=1)h[a].c();u=U(),s=J("div"),t=J("input"),c=U(),d&&d.c(),p=U(),K(N.$$.fragment),w(t,"class","border-none svelte-7c67e4"),t.disabled=n[4],w(t,"autocomplete","off"),t.readOnly=_=!n[8],z(t,"subdued",!n[14].includes(n[9])&&!n[7]||n[11].length===n[2]),w(s,"class","secondary-wrap svelte-7c67e4"),w(i,"class","wrap-inner svelte-7c67e4"),z(i,"show_options",n[13]),w(o,"class","wrap svelte-7c67e4"),w(l,"class","svelte-7c67e4"),z(l,"container",n[6])},m(a,g){R(a,l,g),Q(e,l,null),C(l,f),C(l,o),C(o,i);for(let S=0;S<h.length;S+=1)h[S]&&h[S].m(i,null);C(i,u),C(i,s),C(s,t),ie(t,n[9]),n[33](t),C(s,c),d&&d.m(s,null),C(o,p),Q(N,o,null),E=!0,T||(k=[I(t,"input",n[32]),I(t,"keydown",n[22]),I(t,"blur",n[17]),I(t,"focus",n[21])],T=!0)},p(a,g){const S={};if(g[0]&32&&(S.show_label=a[5]),g[0]&2&&(S.info=a[1]),g[0]&1|g[1]&2048&&(S.$$scope={dirty:g,ctx:a}),e.$set(S),g[0]&346128){m=oe(a[11]);let b;for(b=0;b<m.length;b+=1){const A=ye(a,m,b);h[b]?(h[b].p(A,g),q(h[b],1)):(h[b]=Ae(A),h[b].c(),q(h[b],1),h[b].m(i,u))}for(X(),b=m.length;b<h.length;b+=1)y(b);Y()}(!E||g[0]&16)&&(t.disabled=a[4]),(!E||g[0]&256&&_!==(_=!a[8]))&&(t.readOnly=_),g[0]&512&&t.value!==a[9]&&ie(t,a[9]),(!E||g[0]&19076)&&z(t,"subdued",!a[14].includes(a[9])&&!a[7]||a[11].length===a[2]),a[4]?d&&(X(),M(d,1,1,()=>{d=null}),Y()):d?(d.p(a,g),g[0]&16&&q(d,1)):(d=De(a),d.c(),q(d,1),d.m(s,null)),(!E||g[0]&8192)&&z(i,"show_options",a[13]);const B={};g[0]&8192&&(B.show_options=a[13]),g[0]&8&&(B.choices=a[3]),g[0]&1024&&(B.filtered_indices=a[10]),g[0]&16&&(B.disabled=a[4]),g[0]&2048&&(B.selected_indices=a[11]),g[0]&32768&&(B.active_index=a[15]),N.$set(B),(!E||g[0]&64)&&z(l,"container",a[6])},i(a){if(!E){q(e.$$.fragment,a);for(let g=0;g<m.length;g+=1)q(h[g]);q(d),q(N.$$.fragment,a),E=!0}},o(a){M(e.$$.fragment,a),h=h.filter(Boolean);for(let g=0;g<h.length;g+=1)M(h[g]);M(d),M(N.$$.fragment,a),E=!1},d(a){a&&H(l),P(e),Ne(h,a),n[33](null),d&&d.d(),P(N),T=!1,ee(k)}}}function nl(n,l,e){let f;Ge(n,Ke,r=>e(16,f=r));let{label:o}=l,{info:i=void 0}=l,{value:u=[]}=l,s=[],{value_is_output:t=!1}=l,{max_choices:_=null}=l,{choices:c}=l,p,{disabled:N=!1}=l,{show_label:E}=l,{container:T=!0}=l,{allow_custom_value:k=!1}=l,{filterable:m=!0}=l,h,y="",d="",a=!1,g,S,B=[],b=null,A=[],j=[];const D=he();Array.isArray(u)&&u.forEach(r=>{const L=c.map(_e=>_e[1]).indexOf(r);L!==-1?A.push(L):A.push(r)});function V(){k||e(9,y=""),k&&y!==""&&(Z(y),e(9,y="")),e(13,a=!1),e(15,b=null),D("blur")}function F(r){e(11,A=A.filter(L=>L!==r)),D("select",{index:typeof r=="number"?r:-1,value:typeof r=="number"?S[r]:r,selected:!1})}function Z(r){(_===null||A.length<_)&&(e(11,A=[...A,r]),D("select",{index:typeof r=="number"?r:-1,value:typeof r=="number"?S[r]:r,selected:!0})),A.length===_&&(e(13,a=!1),e(15,b=null),h.blur())}function re(r){const L=parseInt(r.detail.target.dataset.index);te(L)}function te(r){A.includes(r)?F(r):Z(r),e(9,y="")}function se(r){e(11,A=[]),e(9,y=""),r.preventDefault()}function O(r){e(10,B=c.map((L,_e)=>_e)),(_===null||A.length<_)&&e(13,a=!0),D("focus")}function de(r){e(13,[a,b]=Re(r,b,B),a,(e(15,b),e(3,c),e(26,p),e(9,y),e(27,d),e(7,k),e(10,B))),r.key==="Enter"&&(b!==null?te(b):k&&(Z(y),e(9,y=""))),r.key==="Backspace"&&y===""&&e(11,A=[...A.slice(0,-1)]),A.length===_&&(e(13,a=!1),e(15,b=null))}function ce(){u===void 0?e(11,A=[]):Array.isArray(u)&&e(11,A=u.map(r=>{const L=S.indexOf(r);if(L!==-1)return L;if(k)return r}).filter(r=>r!==void 0))}Se(()=>{e(24,t=!1)});const He=r=>F(r),Ie=(r,L)=>{L.key==="Enter"&&F(r)};function Le(){y=this.value,e(9,y)}function Ue(r){ue[r?"unshift":"push"](()=>{h=r,e(12,h)})}const je=r=>{r.key==="Enter"&&se(r)};return n.$$set=r=>{"label"in r&&e(0,o=r.label),"info"in r&&e(1,i=r.info),"value"in r&&e(23,u=r.value),"value_is_output"in r&&e(24,t=r.value_is_output),"max_choices"in r&&e(2,_=r.max_choices),"choices"in r&&e(3,c=r.choices),"disabled"in r&&e(4,N=r.disabled),"show_label"in r&&e(5,E=r.show_label),"container"in r&&e(6,T=r.container),"allow_custom_value"in r&&e(7,k=r.allow_custom_value),"filterable"in r&&e(8,m=r.filterable)},n.$$.update=()=>{n.$$.dirty[0]&8&&(e(14,g=c.map(r=>r[0])),e(28,S=c.map(r=>r[1]))),n.$$.dirty[0]&201328264&&(c!==p||y!==d)&&(e(10,B=Te(c,y)),e(26,p=c),e(27,d=y),k||e(15,b=B[0])),n.$$.dirty[0]&805308416&&JSON.stringify(A)!=JSON.stringify(j)&&(e(23,u=A.map(r=>typeof r=="number"?S[r]:r)),e(29,j=A.slice())),n.$$.dirty[0]&58720256&&JSON.stringify(u)!=JSON.stringify(s)&&(Je(D,u,t),e(25,s=Array.isArray(u)?u.slice():u)),n.$$.dirty[0]&8388608&&ce()},[o,i,_,c,N,E,T,k,m,y,B,A,h,a,g,b,f,V,F,re,se,O,de,u,t,s,p,d,S,j,He,Ie,Le,Ue,je]}class ul extends v{constructor(l){super(),x(this,l,nl,ll,$,{label:0,info:1,value:23,value_is_output:24,max_choices:2,choices:3,disabled:4,show_label:5,container:6,allow_custom_value:7,filterable:8},null,[-1,-1])}}export{ol as D,ul as M};
//# sourceMappingURL=Multiselect-f9fdc060.js.map
