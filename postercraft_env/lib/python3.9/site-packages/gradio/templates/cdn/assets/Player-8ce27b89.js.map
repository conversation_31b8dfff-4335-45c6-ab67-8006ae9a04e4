{"version": 3, "file": "Player-8ce27b89.js", "sources": ["../../../../js/icons/src/Maximise.svelte", "../../../../js/icons/src/Pause.svelte", "../../../../js/icons/src/Play.svelte", "../../../../js/icons/src/Video.svelte", "../../../../js/video/shared/Player.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\td=\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\"\n\t/>\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<rect x=\"6\" y=\"4\" width=\"4\" height=\"16\" />\n\t<rect x=\"14\" y=\"4\" width=\"4\" height=\"16\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<polygon points=\"5 3 19 12 5 21 5 3\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-video\"\n>\n\t<polygon points=\"23 7 16 12 23 17 23 7\" />\n\t<rect x=\"1\" y=\"5\" width=\"15\" height=\"14\" rx=\"2\" ry=\"2\" />\n</svg>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { Play, Pause, <PERSON>ise, Undo } from \"@gradio/icons\";\n\timport Video from \"./Video.svelte\";\n\n\texport let src: string;\n\texport let subtitle: string | null = null;\n\texport let mirror: boolean;\n\texport let autoplay: boolean;\n\texport let label = \"test\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tstop: undefined;\n\t\tend: undefined;\n\t}>();\n\n\tlet time = 0;\n\tlet duration: number;\n\tlet paused = true;\n\tlet video: HTMLVideoElement;\n\n\tfunction handleMove(e: TouchEvent | MouseEvent): void {\n\t\tif (!duration) return;\n\n\t\tif (e.type === \"click\") {\n\t\t\thandle_click(e as MouseEvent);\n\t\t\treturn;\n\t\t}\n\n\t\tif (e.type !== \"touchmove\" && !((e as MouseEvent).buttons & 1)) return;\n\n\t\tconst clientX =\n\t\t\te.type === \"touchmove\"\n\t\t\t\t? (e as TouchEvent).touches[0].clientX\n\t\t\t\t: (e as MouseEvent).clientX;\n\t\tconst { left, right } = (\n\t\t\te.currentTarget as HTMLProgressElement\n\t\t).getBoundingClientRect();\n\t\ttime = (duration * (clientX - left)) / (right - left);\n\t}\n\n\tasync function play_pause(): Promise<void> {\n\t\tif (document.fullscreenElement != video) {\n\t\t\tconst isPlaying =\n\t\t\t\tvideo.currentTime > 0 &&\n\t\t\t\t!video.paused &&\n\t\t\t\t!video.ended &&\n\t\t\t\tvideo.readyState > video.HAVE_CURRENT_DATA;\n\n\t\t\tif (!isPlaying) {\n\t\t\t\tawait video.play();\n\t\t\t} else video.pause();\n\t\t}\n\t}\n\n\tfunction handle_click(e: MouseEvent): void {\n\t\tconst { left, right } = (\n\t\t\te.currentTarget as HTMLProgressElement\n\t\t).getBoundingClientRect();\n\t\ttime = (duration * (e.clientX - left)) / (right - left);\n\t}\n\n\tfunction format(seconds: number): string {\n\t\tif (isNaN(seconds) || !isFinite(seconds)) return \"...\";\n\n\t\tconst minutes = Math.floor(seconds / 60);\n\t\tlet _seconds: number | string = Math.floor(seconds % 60);\n\t\tif (_seconds < 10) _seconds = `0${_seconds}`;\n\n\t\treturn `${minutes}:${_seconds}`;\n\t}\n\n\tfunction handle_end(): void {\n\t\tdispatch(\"stop\");\n\t\tdispatch(\"end\");\n\t}\n\n\tfunction open_full_screen(): void {\n\t\tvideo.requestFullscreen();\n\t}\n</script>\n\n<div class=\"wrap\">\n\t<div class:mirror>\n\t\t<Video\n\t\t\t{src}\n\t\t\tpreload=\"auto\"\n\t\t\t{autoplay}\n\t\t\ton:click={play_pause}\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:ended={handle_end}\n\t\t\tbind:currentTime={time}\n\t\t\tbind:duration\n\t\t\tbind:paused\n\t\t\tbind:node={video}\n\t\t\tdata-testid={`${label}-player`}\n\t\t>\n\t\t\t<track kind=\"captions\" src={subtitle} default />\n\t\t</Video>\n\t</div>\n\n\t<div class=\"controls\">\n\t\t<div class=\"inner\">\n\t\t\t<span\n\t\t\t\trole=\"button\"\n\t\t\t\ttabindex=\"0\"\n\t\t\t\tclass=\"icon\"\n\t\t\t\taria-label=\"play-pause-replay-button\"\n\t\t\t\ton:click={play_pause}\n\t\t\t\ton:keydown={play_pause}\n\t\t\t>\n\t\t\t\t{#if time === duration}\n\t\t\t\t\t<Undo />\n\t\t\t\t{:else if paused}\n\t\t\t\t\t<Play />\n\t\t\t\t{:else}\n\t\t\t\t\t<Pause />\n\t\t\t\t{/if}\n\t\t\t</span>\n\n\t\t\t<span class=\"time\">{format(time)} / {format(duration)}</span>\n\n\t\t\t<!-- TODO: implement accessible video timeline for 4.0 -->\n\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->\n\t\t\t<progress\n\t\t\t\tvalue={time / duration || 0}\n\t\t\t\ton:mousemove={handleMove}\n\t\t\t\ton:touchmove|preventDefault={handleMove}\n\t\t\t\ton:click|stopPropagation|preventDefault={handle_click}\n\t\t\t/>\n\n\t\t\t<div\n\t\t\t\trole=\"button\"\n\t\t\t\ttabindex=\"0\"\n\t\t\t\tclass=\"icon\"\n\t\t\t\taria-label=\"full-screen\"\n\t\t\t\ton:click={open_full_screen}\n\t\t\t\ton:keypress={open_full_screen}\n\t\t\t>\n\t\t\t\t<Maximise />\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>\n\n<style lang=\"postcss\">\n\tspan {\n\t\ttext-shadow: 0 0 8px rgba(0, 0, 0, 0.5);\n\t}\n\n\tprogress {\n\t\tmargin-right: var(--size-3);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-2);\n\t}\n\n\tprogress::-webkit-progress-bar {\n\t\tborder-radius: 2px;\n\t\tbackground-color: rgba(255, 255, 255, 0.2);\n\t\toverflow: hidden;\n\t}\n\n\tprogress::-webkit-progress-value {\n\t\tbackground-color: rgba(255, 255, 255, 0.9);\n\t}\n\n\t.mirror {\n\t\ttransform: scaleX(-1);\n\t}\n\n\t.controls {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\topacity: 0;\n\t\ttransition: 500ms;\n\t\tmargin: var(--size-2);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--color-grey-800);\n\t\tpadding: var(--size-2) var(--size-1);\n\t\twidth: calc(100% - 0.375rem * 2);\n\t\twidth: calc(100% - var(--size-2) * 2);\n\t}\n\t.wrap:hover .controls {\n\t\topacity: 1;\n\t}\n\n\t.inner {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding-right: var(--size-2);\n\t\tpadding-left: var(--size-2);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.icon {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tcursor: pointer;\n\t\twidth: var(--size-6);\n\t\tcolor: white;\n\t}\n\n\t.time {\n\t\tflex-shrink: 0;\n\t\tmargin-right: var(--size-3);\n\t\tmargin-left: var(--size-3);\n\t\tcolor: white;\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\t.wrap {\n\t\tposition: relative;\n\t\tbackground-color: var(--background-fill-secondary);\n\t\theight: var(--size-full);\n\t\twidth: var(--size-full);\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "rect0", "rect1", "polygon", "rect", "ctx", "attr", "track", "track_src_value", "t2_value", "format", "t4_value", "div4", "div0", "div3", "div2", "span0", "span1", "progress", "div1", "current", "dirty", "set_data", "t2", "t4", "seconds", "minutes", "_seconds", "src", "$$props", "subtitle", "mirror", "autoplay", "label", "dispatch", "createEventDispatcher", "time", "duration", "paused", "video", "handleMove", "e", "handle_click", "clientX", "left", "right", "play_pause", "handle_end", "open_full_screen", "value"], "mappings": "qxBAAAA,EAcKC,EAAAC,EAAAC,CAAA,EAHJC,EAECF,EAAAG,CAAA,iiBCbFL,EAaKC,EAAAC,EAAAC,CAAA,EAFJC,EAAyCF,EAAAI,CAAA,EACzCF,EAA0CF,EAAAK,CAAA,4bCZ3CP,EAYKC,EAAAC,EAAAC,CAAA,EADJC,EAAsCF,EAAAM,CAAA,6kBCXvCR,EAcKC,EAAAC,EAAAC,CAAA,EAFJC,EAAyCF,EAAAM,CAAA,EACzCJ,EAAwDF,EAAAO,CAAA,iLCuF1BC,EAAQ,CAAA,CAAA,GAAAC,EAAAC,EAAA,MAAAC,CAAA,uBAApCb,EAA+CC,EAAAW,EAAAT,CAAA,2BAAnBO,EAAQ,CAAA,CAAA,skBAuBhBI,EAAAC,EAAOL,EAAI,CAAA,CAAA,EAAA,OAAMM,EAAAD,EAAOL,EAAQ,CAAA,CAAA,EAAA,uLAzBpCA,EAAK,CAAA,mDAJHA,EAAI,CAAA,IAAA,uBAAJA,EAAI,CAAA,mEAGXA,EAAK,CAAA,IAAA,gBAALA,EAAK,CAAA,8JAPNA,EAAU,EAAA,CAAA,yDAGVA,EAAU,EAAA,CAAA,4CAqBd,OAAAA,OAASA,EAAQ,CAAA,EAAA,EAEZA,EAAM,CAAA,EAAA,yKAOgB,KAAG,uRAM5BA,EAAI,CAAA,EAAGA,EAAQ,CAAA,GAAI,6PA7C9BV,EA+DKC,EAAAgB,EAAAd,CAAA,EA9DJC,EAiBKa,EAAAC,CAAA,qBAELd,EA0CKa,EAAAE,CAAA,EAzCJf,EAwCKe,EAAAC,CAAA,EAvCJhB,EAeMgB,EAAAC,CAAA,wBAENjB,EAA4DgB,EAAAE,CAAA,8BAK5DlB,EAKCgB,EAAAG,CAAA,SAEDnB,EASKgB,EAAAI,CAAA,wCAjCMd,EAAU,EAAA,CAAA,gBACRA,EAAU,EAAA,CAAA,kBAkBRA,EAAU,CAAA,CAAA,qBACKA,EAAU,CAAA,CAAA,CAAA,oBACEA,EAAY,EAAA,CAAA,CAAA,CAAA,cAQ3CA,EAAgB,EAAA,CAAA,iBACbA,EAAgB,EAAA,CAAA,mGA3CdA,EAAK,CAAA,iFAJHA,EAAI,CAAA,gIAGXA,EAAK,CAAA,iLA0BI,CAAAe,GAAAC,EAAA,KAAAZ,KAAAA,EAAAC,EAAOL,EAAI,CAAA,CAAA,EAAA,KAAAiB,GAAAC,EAAAd,CAAA,GAAM,CAAAW,GAAAC,EAAA,KAAAV,KAAAA,EAAAD,EAAOL,EAAQ,CAAA,CAAA,EAAA,KAAAiB,GAAAE,EAAAb,CAAA,oBAM5CN,EAAI,CAAA,EAAGA,EAAQ,CAAA,GAAI,kLAjEpB,SAAAK,EAAOe,EAAe,CAC1B,GAAA,MAAMA,CAAO,GAAA,CAAM,SAASA,CAAO,QAAU,MAE3C,MAAAC,EAAU,KAAK,MAAMD,EAAU,EAAE,EACnC,IAAAE,EAA4B,KAAK,MAAMF,EAAU,EAAE,EACnD,OAAAE,EAAW,KAAIA,MAAeA,KAExB,GAAAD,KAAWC,2BAlEX,IAAAC,CAAW,EAAAC,EACX,CAAA,SAAAC,EAA0B,IAAI,EAAAD,GAC9B,OAAAE,CAAe,EAAAF,GACf,SAAAG,CAAiB,EAAAH,EACjB,CAAA,MAAAI,EAAQ,MAAM,EAAAJ,EAEnB,MAAAK,EAAWC,KAOb,IAAAC,EAAO,EACPC,EACAC,EAAS,GACTC,EAEK,SAAAC,EAAWC,EAA0B,KACxCJ,EAAQ,UAETI,EAAE,OAAS,QAAO,CACrBC,EAAaD,CAAe,YAIzBA,EAAE,OAAS,eAAkBA,EAAiB,QAAU,GAAC,OAEvD,MAAAE,EACLF,EAAE,OAAS,YACPA,EAAiB,QAAQ,CAAC,EAAE,QAC5BA,EAAiB,QACd,CAAA,KAAAG,EAAM,MAAAC,CAAK,EAClBJ,EAAE,cACD,4BACFL,EAAQC,GAAYM,EAAUC,IAAUC,EAAQD,EAAI,iBAGtCE,GAAU,CACpB,SAAS,mBAAqBP,IAEhCA,EAAM,YAAc,GAAC,CACpBA,EAAM,SACNA,EAAM,OACPA,EAAM,WAAaA,EAAM,kBAInBA,EAAM,QADN,MAAAA,EAAM,QAKN,SAAAG,EAAaD,EAAa,CAC1B,KAAA,CAAA,KAAAG,EAAM,MAAAC,CAAK,EAClBJ,EAAE,cACD,4BACFL,EAAQC,GAAYI,EAAE,QAAUG,IAAUC,EAAQD,EAAI,WAa9CG,GAAU,CAClBb,EAAS,MAAM,EACfA,EAAS,KAAK,WAGNc,GAAgB,CACxBT,EAAM,kBAAiB,gBAcJH,EAAIa,yEAGXV,EAAKU"}