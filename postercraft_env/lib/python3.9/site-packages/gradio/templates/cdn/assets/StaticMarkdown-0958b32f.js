import{aA as Ar,aB as Ei,S as Rn,e as Fn,s as In,m as Ln,g as F0,N as Q0,h as Et,n as li,k as Bt,an as dl,R as fl,t as pl,x as gl,Q as vl,aq as bl,F as Tr,G as Mr,af as yl,w as zr,u as Er,H as Br,C as xl,Z as wl,ae as kl,o as Sl,V as Al,W as Tl}from"./index-7674dbb6.js";import{c as Ml}from"./utils-c3e3db58.js";import{B as zl}from"./Button-770df9ba.js";/*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE */const{entries:Bi,setPrototypeOf:oi,isFrozen:El,getPrototypeOf:Bl,getOwnPropertyDescriptor:Cl}=Object;let{freeze:Qe,seal:k0,create:Dl}=Object,{apply:En,construct:Bn}=typeof Reflect<"u"&&Reflect;En||(En=function(u,h,m){return u.apply(h,m)});Qe||(Qe=function(u){return u});k0||(k0=function(u){return u});Bn||(Bn=function(u,h){return new u(...h)});const _l=v0(Array.prototype.forEach),ui=v0(Array.prototype.pop),Ut=v0(Array.prototype.push),wr=v0(String.prototype.toLowerCase),xn=v0(String.prototype.toString),Nl=v0(String.prototype.match),w0=v0(String.prototype.replace),Rl=v0(String.prototype.indexOf),Fl=v0(String.prototype.trim),l0=v0(RegExp.prototype.test),Gt=Il(TypeError);function v0(f){return function(u){for(var h=arguments.length,m=new Array(h>1?h-1:0),v=1;v<h;v++)m[v-1]=arguments[v];return En(f,u,m)}}function Il(f){return function(){for(var u=arguments.length,h=new Array(u),m=0;m<u;m++)h[m]=arguments[m];return Bn(f,h)}}function ve(f,u,h){var m;h=(m=h)!==null&&m!==void 0?m:wr,oi&&oi(f,null);let v=u.length;for(;v--;){let y=u[v];if(typeof y=="string"){const z=h(y);z!==y&&(El(u)||(u[v]=z),y=z)}f[y]=!0}return f}function zt(f){const u=Dl(null);for(const[h,m]of Bi(f))u[h]=m;return u}function br(f,u){for(;f!==null;){const m=Cl(f,u);if(m){if(m.get)return v0(m.get);if(typeof m.value=="function")return v0(m.value)}f=Bl(f)}function h(m){return console.warn("fallback value for",m),null}return h}const hi=Qe(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),wn=Qe(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),kn=Qe(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ll=Qe(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Sn=Qe(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Ol=Qe(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),ci=Qe(["#text"]),mi=Qe(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),An=Qe(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),di=Qe(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),yr=Qe(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),ql=k0(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Pl=k0(/<%[\w\W]*|[\w\W]*%>/gm),Hl=k0(/\${[\w\W]*}/gm),Ul=k0(/^data-[\-\w.\u00B7-\uFFFF]/),Gl=k0(/^aria-[\-\w]+$/),Ci=k0(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Vl=k0(/^(?:\w+script|data):/i),Wl=k0(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Di=k0(/^html$/i);var fi=Object.freeze({__proto__:null,MUSTACHE_EXPR:ql,ERB_EXPR:Pl,TMPLIT_EXPR:Hl,DATA_ATTR:Ul,ARIA_ATTR:Gl,IS_ALLOWED_URI:Ci,IS_SCRIPT_OR_DATA:Vl,ATTR_WHITESPACE:Wl,DOCTYPE_NAME:Di});const $l=()=>typeof window>"u"?null:window,Yl=function(u,h){if(typeof u!="object"||typeof u.createPolicy!="function")return null;let m=null;const v="data-tt-policy-suffix";h&&h.hasAttribute(v)&&(m=h.getAttribute(v));const y="dompurify"+(m?"#"+m:"");try{return u.createPolicy(y,{createHTML(z){return z},createScriptURL(z){return z}})}catch{return console.warn("TrustedTypes policy "+y+" could not be created."),null}};function _i(){let f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:$l();const u=ae=>_i(ae);if(u.version="3.0.3",u.removed=[],!f||!f.document||f.document.nodeType!==9)return u.isSupported=!1,u;const h=f.document,m=h.currentScript;let{document:v}=f;const{DocumentFragment:y,HTMLTemplateElement:z,Node:T,Element:C,NodeFilter:P,NamedNodeMap:X=f.NamedNodeMap||f.MozNamedAttrMap,HTMLFormElement:ce,DOMParser:te,trustedTypes:U}=f,ie=C.prototype,le=br(ie,"cloneNode"),oe=br(ie,"nextSibling"),Y=br(ie,"childNodes"),_=br(ie,"parentNode");if(typeof z=="function"){const ae=v.createElement("template");ae.content&&ae.content.ownerDocument&&(v=ae.content.ownerDocument)}let M,F="";const{implementation:D,createNodeIterator:I,createDocumentFragment:j,getElementsByTagName:ne}=v,{importNode:$}=h;let me={};u.isSupported=typeof Bi=="function"&&typeof _=="function"&&D&&D.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:ge,ERB_EXPR:Le,TMPLIT_EXPR:Ne,DATA_ATTR:We,ARIA_ATTR:Ct,IS_SCRIPT_OR_DATA:Dt,ATTR_WHITESPACE:J0}=fi;let{IS_ALLOWED_URI:Oe}=fi,Se=null;const b0=ve({},[...hi,...wn,...kn,...Sn,...ci]);let K=null;const Ve=ve({},[...mi,...An,...di,...yr]);let De=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),He=null,o0=null,Je=!0,S0=!0,I0=!1,et=!0,u0=!1,e0=!1,tt=!1,B0=!1,L0=!1,dt=!1,rt=!1,Wt=!0,O0=!1;const h0="user-content-";let q0=!0,P0=!1,H0={},A0=null;const ft=ve({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let $t=null;const Yt=ve({},["audio","video","img","source","image","track"]);let pt=null;const _t=ve({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),nt="http://www.w3.org/1998/Math/MathML",gt="http://www.w3.org/2000/svg",a0="http://www.w3.org/1999/xhtml";let U0=a0,vt=!1,ze=null;const V=ve({},[nt,gt,a0],xn);let je;const jt=["application/xhtml+xml","text/html"],Xt="text/html";let qe,c0=null;const Nt=v.createElement("form"),Zt=function(k){return k instanceof RegExp||k instanceof Function},Rt=function(k){if(!(c0&&c0===k)){if((!k||typeof k!="object")&&(k={}),k=zt(k),je=jt.indexOf(k.PARSER_MEDIA_TYPE)===-1?je=Xt:je=k.PARSER_MEDIA_TYPE,qe=je==="application/xhtml+xml"?xn:wr,Se="ALLOWED_TAGS"in k?ve({},k.ALLOWED_TAGS,qe):b0,K="ALLOWED_ATTR"in k?ve({},k.ALLOWED_ATTR,qe):Ve,ze="ALLOWED_NAMESPACES"in k?ve({},k.ALLOWED_NAMESPACES,xn):V,pt="ADD_URI_SAFE_ATTR"in k?ve(zt(_t),k.ADD_URI_SAFE_ATTR,qe):_t,$t="ADD_DATA_URI_TAGS"in k?ve(zt(Yt),k.ADD_DATA_URI_TAGS,qe):Yt,A0="FORBID_CONTENTS"in k?ve({},k.FORBID_CONTENTS,qe):ft,He="FORBID_TAGS"in k?ve({},k.FORBID_TAGS,qe):{},o0="FORBID_ATTR"in k?ve({},k.FORBID_ATTR,qe):{},H0="USE_PROFILES"in k?k.USE_PROFILES:!1,Je=k.ALLOW_ARIA_ATTR!==!1,S0=k.ALLOW_DATA_ATTR!==!1,I0=k.ALLOW_UNKNOWN_PROTOCOLS||!1,et=k.ALLOW_SELF_CLOSE_IN_ATTR!==!1,u0=k.SAFE_FOR_TEMPLATES||!1,e0=k.WHOLE_DOCUMENT||!1,L0=k.RETURN_DOM||!1,dt=k.RETURN_DOM_FRAGMENT||!1,rt=k.RETURN_TRUSTED_TYPE||!1,B0=k.FORCE_BODY||!1,Wt=k.SANITIZE_DOM!==!1,O0=k.SANITIZE_NAMED_PROPS||!1,q0=k.KEEP_CONTENT!==!1,P0=k.IN_PLACE||!1,Oe=k.ALLOWED_URI_REGEXP||Ci,U0=k.NAMESPACE||a0,De=k.CUSTOM_ELEMENT_HANDLING||{},k.CUSTOM_ELEMENT_HANDLING&&Zt(k.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(De.tagNameCheck=k.CUSTOM_ELEMENT_HANDLING.tagNameCheck),k.CUSTOM_ELEMENT_HANDLING&&Zt(k.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(De.attributeNameCheck=k.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),k.CUSTOM_ELEMENT_HANDLING&&typeof k.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(De.allowCustomizedBuiltInElements=k.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),u0&&(S0=!1),dt&&(L0=!0),H0&&(Se=ve({},[...ci]),K=[],H0.html===!0&&(ve(Se,hi),ve(K,mi)),H0.svg===!0&&(ve(Se,wn),ve(K,An),ve(K,yr)),H0.svgFilters===!0&&(ve(Se,kn),ve(K,An),ve(K,yr)),H0.mathMl===!0&&(ve(Se,Sn),ve(K,di),ve(K,yr))),k.ADD_TAGS&&(Se===b0&&(Se=zt(Se)),ve(Se,k.ADD_TAGS,qe)),k.ADD_ATTR&&(K===Ve&&(K=zt(K)),ve(K,k.ADD_ATTR,qe)),k.ADD_URI_SAFE_ATTR&&ve(pt,k.ADD_URI_SAFE_ATTR,qe),k.FORBID_CONTENTS&&(A0===ft&&(A0=zt(A0)),ve(A0,k.FORBID_CONTENTS,qe)),q0&&(Se["#text"]=!0),e0&&ve(Se,["html","head","body"]),Se.table&&(ve(Se,["tbody"]),delete He.tbody),k.TRUSTED_TYPES_POLICY){if(typeof k.TRUSTED_TYPES_POLICY.createHTML!="function")throw Gt('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof k.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Gt('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');M=k.TRUSTED_TYPES_POLICY,F=M.createHTML("")}else M===void 0&&(M=Yl(U,m)),M!==null&&typeof F=="string"&&(F=M.createHTML(""));Qe&&Qe(k),c0=k}},Ze=ve({},["mi","mo","mn","ms","mtext"]),m0=ve({},["foreignobject","desc","title","annotation-xml"]),T0=ve({},["title","style","font","a","script"]),G0=ve({},wn);ve(G0,kn),ve(G0,Ll);const bt=ve({},Sn);ve(bt,Ol);const Nr=function(k){let O=_(k);(!O||!O.tagName)&&(O={namespaceURI:U0,tagName:"template"});const W=wr(k.tagName),be=wr(O.tagName);return ze[k.namespaceURI]?k.namespaceURI===gt?O.namespaceURI===a0?W==="svg":O.namespaceURI===nt?W==="svg"&&(be==="annotation-xml"||Ze[be]):!!G0[W]:k.namespaceURI===nt?O.namespaceURI===a0?W==="math":O.namespaceURI===gt?W==="math"&&m0[be]:!!bt[W]:k.namespaceURI===a0?O.namespaceURI===gt&&!m0[be]||O.namespaceURI===nt&&!Ze[be]?!1:!bt[W]&&(T0[W]||!G0[W]):!!(je==="application/xhtml+xml"&&ze[k.namespaceURI]):!1},C0=function(k){Ut(u.removed,{element:k});try{k.parentNode.removeChild(k)}catch{k.remove()}},Ft=function(k,O){try{Ut(u.removed,{attribute:O.getAttributeNode(k),from:O})}catch{Ut(u.removed,{attribute:null,from:O})}if(O.removeAttribute(k),k==="is"&&!K[k])if(L0||dt)try{C0(O)}catch{}else try{O.setAttribute(k,"")}catch{}},at=function(k){let O,W;if(B0)k="<remove></remove>"+k;else{const Ie=Nl(k,/^[\r\n\t ]+/);W=Ie&&Ie[0]}je==="application/xhtml+xml"&&U0===a0&&(k='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+k+"</body></html>");const be=M?M.createHTML(k):k;if(U0===a0)try{O=new te().parseFromString(be,je)}catch{}if(!O||!O.documentElement){O=D.createDocument(U0,"template",null);try{O.documentElement.innerHTML=vt?F:be}catch{}}const x=O.body||O.documentElement;return k&&W&&x.insertBefore(v.createTextNode(W),x.childNodes[0]||null),U0===a0?ne.call(O,e0?"html":"body")[0]:e0?O.documentElement:x},Ce=function(k){return I.call(k.ownerDocument||k,k,P.SHOW_ELEMENT|P.SHOW_COMMENT|P.SHOW_TEXT,null,!1)},i=function(k){return k instanceof ce&&(typeof k.nodeName!="string"||typeof k.textContent!="string"||typeof k.removeChild!="function"||!(k.attributes instanceof X)||typeof k.removeAttribute!="function"||typeof k.setAttribute!="function"||typeof k.namespaceURI!="string"||typeof k.insertBefore!="function"||typeof k.hasChildNodes!="function")},o=function(k){return typeof T=="object"?k instanceof T:k&&typeof k=="object"&&typeof k.nodeType=="number"&&typeof k.nodeName=="string"},q=function(k,O,W){me[k]&&_l(me[k],be=>{be.call(u,O,W,c0)})},d=function(k){let O;if(q("beforeSanitizeElements",k,null),i(k))return C0(k),!0;const W=qe(k.nodeName);if(q("uponSanitizeElement",k,{tagName:W,allowedTags:Se}),k.hasChildNodes()&&!o(k.firstElementChild)&&(!o(k.content)||!o(k.content.firstElementChild))&&l0(/<[/\w]/g,k.innerHTML)&&l0(/<[/\w]/g,k.textContent))return C0(k),!0;if(!Se[W]||He[W]){if(!He[W]&&Ee(W)&&(De.tagNameCheck instanceof RegExp&&l0(De.tagNameCheck,W)||De.tagNameCheck instanceof Function&&De.tagNameCheck(W)))return!1;if(q0&&!A0[W]){const be=_(k)||k.parentNode,x=Y(k)||k.childNodes;if(x&&be){const Ie=x.length;for(let A=Ie-1;A>=0;--A)be.insertBefore(le(x[A],!0),oe(k))}}return C0(k),!0}return k instanceof C&&!Nr(k)||(W==="noscript"||W==="noembed")&&l0(/<\/no(script|embed)/i,k.innerHTML)?(C0(k),!0):(u0&&k.nodeType===3&&(O=k.textContent,O=w0(O,ge," "),O=w0(O,Le," "),O=w0(O,Ne," "),k.textContent!==O&&(Ut(u.removed,{element:k.cloneNode()}),k.textContent=O)),q("afterSanitizeElements",k,null),!1)},w=function(k,O,W){if(Wt&&(O==="id"||O==="name")&&(W in v||W in Nt))return!1;if(!(S0&&!o0[O]&&l0(We,O))){if(!(Je&&l0(Ct,O))){if(!K[O]||o0[O]){if(!(Ee(k)&&(De.tagNameCheck instanceof RegExp&&l0(De.tagNameCheck,k)||De.tagNameCheck instanceof Function&&De.tagNameCheck(k))&&(De.attributeNameCheck instanceof RegExp&&l0(De.attributeNameCheck,O)||De.attributeNameCheck instanceof Function&&De.attributeNameCheck(O))||O==="is"&&De.allowCustomizedBuiltInElements&&(De.tagNameCheck instanceof RegExp&&l0(De.tagNameCheck,W)||De.tagNameCheck instanceof Function&&De.tagNameCheck(W))))return!1}else if(!pt[O]){if(!l0(Oe,w0(W,J0,""))){if(!((O==="src"||O==="xlink:href"||O==="href")&&k!=="script"&&Rl(W,"data:")===0&&$t[k])){if(!(I0&&!l0(Dt,w0(W,J0,"")))){if(W)return!1}}}}}}return!0},Ee=function(k){return k.indexOf("-")>0},J=function(k){let O,W,be,x;q("beforeSanitizeAttributes",k,null);const{attributes:Ie}=k;if(!Ie)return;const A={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:K};for(x=Ie.length;x--;){O=Ie[x];const{name:i0,namespaceURI:yt}=O;if(W=i0==="value"?O.value:Fl(O.value),be=qe(i0),A.attrName=be,A.attrValue=W,A.keepAttr=!0,A.forceKeepAttr=void 0,q("uponSanitizeAttribute",k,A),W=A.attrValue,A.forceKeepAttr||(Ft(i0,k),!A.keepAttr))continue;if(!et&&l0(/\/>/i,W)){Ft(i0,k);continue}u0&&(W=w0(W,ge," "),W=w0(W,Le," "),W=w0(W,Ne," "));const xt=qe(k.nodeName);if(w(xt,be,W)){if(O0&&(be==="id"||be==="name")&&(Ft(i0,k),W=h0+W),M&&typeof U=="object"&&typeof U.getAttributeType=="function"&&!yt)switch(U.getAttributeType(xt,be)){case"TrustedHTML":{W=M.createHTML(W);break}case"TrustedScriptURL":{W=M.createScriptURL(W);break}}try{yt?k.setAttributeNS(yt,i0,W):k.setAttribute(i0,W),ui(u.removed)}catch{}}}q("afterSanitizeAttributes",k,null)},$e=function ae(k){let O;const W=Ce(k);for(q("beforeSanitizeShadowDOM",k,null);O=W.nextNode();)q("uponSanitizeShadowNode",O,null),!d(O)&&(O.content instanceof y&&ae(O.content),J(O));q("afterSanitizeShadowDOM",k,null)};return u.sanitize=function(ae){let k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},O,W,be,x;if(vt=!ae,vt&&(ae="<!-->"),typeof ae!="string"&&!o(ae))if(typeof ae.toString=="function"){if(ae=ae.toString(),typeof ae!="string")throw Gt("dirty is not a string, aborting")}else throw Gt("toString is not a function");if(!u.isSupported)return ae;if(tt||Rt(k),u.removed=[],typeof ae=="string"&&(P0=!1),P0){if(ae.nodeName){const i0=qe(ae.nodeName);if(!Se[i0]||He[i0])throw Gt("root node is forbidden and cannot be sanitized in-place")}}else if(ae instanceof T)O=at("<!---->"),W=O.ownerDocument.importNode(ae,!0),W.nodeType===1&&W.nodeName==="BODY"||W.nodeName==="HTML"?O=W:O.appendChild(W);else{if(!L0&&!u0&&!e0&&ae.indexOf("<")===-1)return M&&rt?M.createHTML(ae):ae;if(O=at(ae),!O)return L0?null:rt?F:""}O&&B0&&C0(O.firstChild);const Ie=Ce(P0?ae:O);for(;be=Ie.nextNode();)d(be)||(be.content instanceof y&&$e(be.content),J(be));if(P0)return ae;if(L0){if(dt)for(x=j.call(O.ownerDocument);O.firstChild;)x.appendChild(O.firstChild);else x=O;return(K.shadowroot||K.shadowrootmod)&&(x=$.call(h,x,!0)),x}let A=e0?O.outerHTML:O.innerHTML;return e0&&Se["!doctype"]&&O.ownerDocument&&O.ownerDocument.doctype&&O.ownerDocument.doctype.name&&l0(Di,O.ownerDocument.doctype.name)&&(A="<!DOCTYPE "+O.ownerDocument.doctype.name+`>
`+A),u0&&(A=w0(A,ge," "),A=w0(A,Le," "),A=w0(A,Ne," ")),M&&rt?M.createHTML(A):A},u.setConfig=function(ae){Rt(ae),tt=!0},u.clearConfig=function(){c0=null,tt=!1},u.isValidAttribute=function(ae,k,O){c0||Rt({});const W=qe(ae),be=qe(k);return w(W,be,O)},u.addHook=function(ae,k){typeof k=="function"&&(me[ae]=me[ae]||[],Ut(me[ae],k))},u.removeHook=function(ae){if(me[ae])return ui(me[ae])},u.removeHooks=function(ae){me[ae]&&(me[ae]=[])},u.removeAllHooks=function(){me={}},u}var pi=_i(),Ni={exports:{}},Tn={exports:{}},gi;function jl(){return gi||(gi=1,function(f,u){(function(m,v){f.exports=v()})(typeof self<"u"?self:Ar,function(){return function(){var h={};(function(){h.d=function(s,e){for(var t in e)h.o(e,t)&&!h.o(s,t)&&Object.defineProperty(s,t,{enumerable:!0,get:e[t]})}})(),function(){h.o=function(s,e){return Object.prototype.hasOwnProperty.call(s,e)}}();var m={};h.d(m,{default:function(){return ul}});var v=function s(e,t){this.position=void 0;var r="KaTeX parse error: "+e,n,a=t&&t.loc;if(a&&a.start<=a.end){var l=a.lexer.input;n=a.start;var c=a.end;n===l.length?r+=" at end of input: ":r+=" at position "+(n+1)+": ";var p=l.slice(n,c).replace(/[^]/g,"$&̲"),b;n>15?b="…"+l.slice(n-15,n):b=l.slice(0,n);var S;c+15<l.length?S=l.slice(c,c+15)+"…":S=l.slice(c),r+=b+p+S}var B=new Error(r);return B.name="ParseError",B.__proto__=s.prototype,B.position=n,B};v.prototype.__proto__=Error.prototype;var y=v,z=function(e,t){return e.indexOf(t)!==-1},T=function(e,t){return e===void 0?t:e},C=/([A-Z])/g,P=function(e){return e.replace(C,"-$1").toLowerCase()},X={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},ce=/[&><"']/g;function te(s){return String(s).replace(ce,function(e){return X[e]})}var U=function s(e){return e.type==="ordgroup"||e.type==="color"?e.body.length===1?s(e.body[0]):e:e.type==="font"?s(e.body):e},ie=function(e){var t=U(e);return t.type==="mathord"||t.type==="textord"||t.type==="atom"},le=function(e){if(!e)throw new Error("Expected non-null, but got "+String(e));return e},oe=function(e){var t=/^\s*([^\\/#]*?)(?::|&#0*58|&#x0*3a)/i.exec(e);return t!=null?t[1]:"_relative"},Y={contains:z,deflt:T,escape:te,hyphenate:P,getBaseElem:U,isCharacterBox:ie,protocolFromUrl:oe},_={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:function(e){return"#"+e}},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:function(e,t){return t.push(e),t}},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:function(e){return Math.max(0,e)},cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:function(e){return Math.max(0,e)},cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:function(e){return Math.max(0,e)},cli:"-e, --max-expand <n>",cliProcessor:function(e){return e==="Infinity"?1/0:parseInt(e)}},globalGroup:{type:"boolean",cli:!1}};function M(s){if(s.default)return s.default;var e=s.type,t=Array.isArray(e)?e[0]:e;if(typeof t!="string")return t.enum[0];switch(t){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}var F=function(){function s(t){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,t=t||{};for(var r in _)if(_.hasOwnProperty(r)){var n=_[r];this[r]=t[r]!==void 0?n.processor?n.processor(t[r]):t[r]:M(n)}}var e=s.prototype;return e.reportNonstrict=function(r,n,a){var l=this.strict;if(typeof l=="function"&&(l=l(r,n,a)),!(!l||l==="ignore")){if(l===!0||l==="error")throw new y("LaTeX-incompatible input and strict mode is set to 'error': "+(n+" ["+r+"]"),a);l==="warn"?typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(n+" ["+r+"]")):typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+l+"': "+n+" ["+r+"]"))}},e.useStrictBehavior=function(r,n,a){var l=this.strict;if(typeof l=="function")try{l=l(r,n,a)}catch{l="error"}return!l||l==="ignore"?!1:l===!0||l==="error"?!0:l==="warn"?(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(n+" ["+r+"]")),!1):(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+l+"': "+n+" ["+r+"]")),!1)},e.isTrusted=function(r){r.url&&!r.protocol&&(r.protocol=Y.protocolFromUrl(r.url));var n=typeof this.trust=="function"?this.trust(r):this.trust;return!!n},s}(),D=function(){function s(t,r,n){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=t,this.size=r,this.cramped=n}var e=s.prototype;return e.sup=function(){return We[Ct[this.id]]},e.sub=function(){return We[Dt[this.id]]},e.fracNum=function(){return We[J0[this.id]]},e.fracDen=function(){return We[Oe[this.id]]},e.cramp=function(){return We[Se[this.id]]},e.text=function(){return We[b0[this.id]]},e.isTight=function(){return this.size>=2},s}(),I=0,j=1,ne=2,$=3,me=4,ge=5,Le=6,Ne=7,We=[new D(I,0,!1),new D(j,0,!0),new D(ne,1,!1),new D($,1,!0),new D(me,2,!1),new D(ge,2,!0),new D(Le,3,!1),new D(Ne,3,!0)],Ct=[me,ge,me,ge,Le,Ne,Le,Ne],Dt=[ge,ge,ge,ge,Ne,Ne,Ne,Ne],J0=[ne,$,me,ge,Le,Ne,Le,Ne],Oe=[$,$,ge,ge,Ne,Ne,Ne,Ne],Se=[j,j,$,$,ge,ge,Ne,Ne],b0=[I,j,ne,$,ne,$,ne,$],K={DISPLAY:We[I],TEXT:We[ne],SCRIPT:We[me],SCRIPTSCRIPT:We[Le]},Ve=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function De(s){for(var e=0;e<Ve.length;e++)for(var t=Ve[e],r=0;r<t.blocks.length;r++){var n=t.blocks[r];if(s>=n[0]&&s<=n[1])return t.name}return null}var He=[];Ve.forEach(function(s){return s.blocks.forEach(function(e){return He.push.apply(He,e)})});function o0(s){for(var e=0;e<He.length;e+=2)if(s>=He[e]&&s<=He[e+1])return!0;return!1}var Je=80,S0=function(e,t){return"M95,"+(622+e+t)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+e/2.075+" -"+e+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+e)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},I0=function(e,t){return"M263,"+(601+e+t)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+e/2.084+" -"+e+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+e)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},et=function(e,t){return"M983 "+(10+e+t)+`
l`+e/3.13+" -"+e+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+e)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},u0=function(e,t){return"M424,"+(2398+e+t)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+e/4.223+" -"+e+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+e)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+e)+" "+t+`
h400000v`+(40+e)+"h-400000z"},e0=function(e,t){return"M473,"+(2713+e+t)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+e/5.298+" -"+e+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+e)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+e)+" "+t+"h400000v"+(40+e)+"H1017.7z"},tt=function(e){var t=e/2;return"M400000 "+e+" H0 L"+t+" 0 l65 45 L145 "+(e-80)+" H400000z"},B0=function(e,t,r){var n=r-54-t-e;return"M702 "+(e+t)+"H400000"+(40+e)+`
H742v`+n+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+t+"H400000v"+(40+e)+"H742z"},L0=function(e,t,r){t=1e3*t;var n="";switch(e){case"sqrtMain":n=S0(t,Je);break;case"sqrtSize1":n=I0(t,Je);break;case"sqrtSize2":n=et(t,Je);break;case"sqrtSize3":n=u0(t,Je);break;case"sqrtSize4":n=e0(t,Je);break;case"sqrtTall":n=B0(t,Je,r)}return n},dt=function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t+" H367z M367 0 H410 V"+t+" H367z");case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t+" H478z M478 0 H521 V"+t+" H478z");default:return""}},rt={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`},Wt=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+` v585 h43z
M367 15 v585 v`+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v602 h84z
M403 1759 V0 H319 V1759 v`+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v602 h84z
M347 1759 V0 h-84 V1759 v`+t+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(t+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(t+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(t+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(t+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}},O0=function(){function s(t){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=t,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}var e=s.prototype;return e.hasClass=function(r){return Y.contains(this.classes,r)},e.toNode=function(){for(var r=document.createDocumentFragment(),n=0;n<this.children.length;n++)r.appendChild(this.children[n].toNode());return r},e.toMarkup=function(){for(var r="",n=0;n<this.children.length;n++)r+=this.children[n].toMarkup();return r},e.toText=function(){var r=function(a){return a.toText()};return this.children.map(r).join("")},s}(),h0={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},q0={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},P0={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function H0(s,e){h0[s]=e}function A0(s,e,t){if(!h0[e])throw new Error("Font metrics not found for font: "+e+".");var r=s.charCodeAt(0),n=h0[e][r];if(!n&&s[0]in P0&&(r=P0[s[0]].charCodeAt(0),n=h0[e][r]),!n&&t==="text"&&o0(r)&&(n=h0[e][77]),n)return{depth:n[0],height:n[1],italic:n[2],skew:n[3],width:n[4]}}var ft={};function $t(s){var e;if(s>=5?e=0:s>=3?e=1:e=2,!ft[e]){var t=ft[e]={cssEmPerMu:q0.quad[e]/18};for(var r in q0)q0.hasOwnProperty(r)&&(t[r]=q0[r][e])}return ft[e]}var Yt=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],pt=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],_t=function(e,t){return t.size<2?e:Yt[e-1][t.size-1]},nt=function(){function s(t){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=t.style,this.color=t.color,this.size=t.size||s.BASESIZE,this.textSize=t.textSize||this.size,this.phantom=!!t.phantom,this.font=t.font||"",this.fontFamily=t.fontFamily||"",this.fontWeight=t.fontWeight||"",this.fontShape=t.fontShape||"",this.sizeMultiplier=pt[this.size-1],this.maxSize=t.maxSize,this.minRuleThickness=t.minRuleThickness,this._fontMetrics=void 0}var e=s.prototype;return e.extend=function(r){var n={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var a in r)r.hasOwnProperty(a)&&(n[a]=r[a]);return new s(n)},e.havingStyle=function(r){return this.style===r?this:this.extend({style:r,size:_t(this.textSize,r)})},e.havingCrampedStyle=function(){return this.havingStyle(this.style.cramp())},e.havingSize=function(r){return this.size===r&&this.textSize===r?this:this.extend({style:this.style.text(),size:r,textSize:r,sizeMultiplier:pt[r-1]})},e.havingBaseStyle=function(r){r=r||this.style.text();var n=_t(s.BASESIZE,r);return this.size===n&&this.textSize===s.BASESIZE&&this.style===r?this:this.extend({style:r,size:n})},e.havingBaseSizing=function(){var r;switch(this.style.id){case 4:case 5:r=3;break;case 6:case 7:r=1;break;default:r=6}return this.extend({style:this.style.text(),size:r})},e.withColor=function(r){return this.extend({color:r})},e.withPhantom=function(){return this.extend({phantom:!0})},e.withFont=function(r){return this.extend({font:r})},e.withTextFontFamily=function(r){return this.extend({fontFamily:r,font:""})},e.withTextFontWeight=function(r){return this.extend({fontWeight:r,font:""})},e.withTextFontShape=function(r){return this.extend({fontShape:r,font:""})},e.sizingClasses=function(r){return r.size!==this.size?["sizing","reset-size"+r.size,"size"+this.size]:[]},e.baseSizingClasses=function(){return this.size!==s.BASESIZE?["sizing","reset-size"+this.size,"size"+s.BASESIZE]:[]},e.fontMetrics=function(){return this._fontMetrics||(this._fontMetrics=$t(this.size)),this._fontMetrics},e.getColor=function(){return this.phantom?"transparent":this.color},s}();nt.BASESIZE=6;var gt=nt,a0={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},U0={ex:!0,em:!0,mu:!0},vt=function(e){return typeof e!="string"&&(e=e.unit),e in a0||e in U0||e==="ex"},ze=function(e,t){var r;if(e.unit in a0)r=a0[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if(e.unit==="mu")r=t.fontMetrics().cssEmPerMu;else{var n;if(t.style.isTight()?n=t.havingStyle(t.style.text()):n=t,e.unit==="ex")r=n.fontMetrics().xHeight;else if(e.unit==="em")r=n.fontMetrics().quad;else throw new y("Invalid unit: '"+e.unit+"'");n!==t&&(r*=n.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*r,t.maxSize)},V=function(e){return+e.toFixed(4)+"em"},je=function(e){return e.filter(function(t){return t}).join(" ")},jt=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");var n=t.getColor();n&&(this.style.color=n)}},Xt=function(e){var t=document.createElement(e);t.className=je(this.classes);for(var r in this.style)this.style.hasOwnProperty(r)&&(t.style[r]=this.style[r]);for(var n in this.attributes)this.attributes.hasOwnProperty(n)&&t.setAttribute(n,this.attributes[n]);for(var a=0;a<this.children.length;a++)t.appendChild(this.children[a].toNode());return t},qe=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+Y.escape(je(this.classes))+'"');var r="";for(var n in this.style)this.style.hasOwnProperty(n)&&(r+=Y.hyphenate(n)+":"+this.style[n]+";");r&&(t+=' style="'+Y.escape(r)+'"');for(var a in this.attributes)this.attributes.hasOwnProperty(a)&&(t+=" "+a+'="'+Y.escape(this.attributes[a])+'"');t+=">";for(var l=0;l<this.children.length;l++)t+=this.children[l].toMarkup();return t+="</"+e+">",t},c0=function(){function s(t,r,n,a){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,jt.call(this,t,n,a),this.children=r||[]}var e=s.prototype;return e.setAttribute=function(r,n){this.attributes[r]=n},e.hasClass=function(r){return Y.contains(this.classes,r)},e.toNode=function(){return Xt.call(this,"span")},e.toMarkup=function(){return qe.call(this,"span")},s}(),Nt=function(){function s(t,r,n,a){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,jt.call(this,r,a),this.children=n||[],this.setAttribute("href",t)}var e=s.prototype;return e.setAttribute=function(r,n){this.attributes[r]=n},e.hasClass=function(r){return Y.contains(this.classes,r)},e.toNode=function(){return Xt.call(this,"a")},e.toMarkup=function(){return qe.call(this,"a")},s}(),Zt=function(){function s(t,r,n){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=r,this.src=t,this.classes=["mord"],this.style=n}var e=s.prototype;return e.hasClass=function(r){return Y.contains(this.classes,r)},e.toNode=function(){var r=document.createElement("img");r.src=this.src,r.alt=this.alt,r.className="mord";for(var n in this.style)this.style.hasOwnProperty(n)&&(r.style[n]=this.style[n]);return r},e.toMarkup=function(){var r="<img  src='"+this.src+" 'alt='"+this.alt+"' ",n="";for(var a in this.style)this.style.hasOwnProperty(a)&&(n+=Y.hyphenate(a)+":"+this.style[a]+";");return n&&(r+=' style="'+Y.escape(n)+'"'),r+="'/>",r},s}(),Rt={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"},Ze=function(){function s(t,r,n,a,l,c,p,b){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=t,this.height=r||0,this.depth=n||0,this.italic=a||0,this.skew=l||0,this.width=c||0,this.classes=p||[],this.style=b||{},this.maxFontSize=0;var S=De(this.text.charCodeAt(0));S&&this.classes.push(S+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=Rt[this.text])}var e=s.prototype;return e.hasClass=function(r){return Y.contains(this.classes,r)},e.toNode=function(){var r=document.createTextNode(this.text),n=null;this.italic>0&&(n=document.createElement("span"),n.style.marginRight=V(this.italic)),this.classes.length>0&&(n=n||document.createElement("span"),n.className=je(this.classes));for(var a in this.style)this.style.hasOwnProperty(a)&&(n=n||document.createElement("span"),n.style[a]=this.style[a]);return n?(n.appendChild(r),n):r},e.toMarkup=function(){var r=!1,n="<span";this.classes.length&&(r=!0,n+=' class="',n+=Y.escape(je(this.classes)),n+='"');var a="";this.italic>0&&(a+="margin-right:"+this.italic+"em;");for(var l in this.style)this.style.hasOwnProperty(l)&&(a+=Y.hyphenate(l)+":"+this.style[l]+";");a&&(r=!0,n+=' style="'+Y.escape(a)+'"');var c=Y.escape(this.text);return r?(n+=">",n+=c,n+="</span>",n):c},s}(),m0=function(){function s(t,r){this.children=void 0,this.attributes=void 0,this.children=t||[],this.attributes=r||{}}var e=s.prototype;return e.toNode=function(){var r="http://www.w3.org/2000/svg",n=document.createElementNS(r,"svg");for(var a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&n.setAttribute(a,this.attributes[a]);for(var l=0;l<this.children.length;l++)n.appendChild(this.children[l].toNode());return n},e.toMarkup=function(){var r='<svg xmlns="http://www.w3.org/2000/svg"';for(var n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&(r+=" "+n+"='"+this.attributes[n]+"'");r+=">";for(var a=0;a<this.children.length;a++)r+=this.children[a].toMarkup();return r+="</svg>",r},s}(),T0=function(){function s(t,r){this.pathName=void 0,this.alternate=void 0,this.pathName=t,this.alternate=r}var e=s.prototype;return e.toNode=function(){var r="http://www.w3.org/2000/svg",n=document.createElementNS(r,"path");return this.alternate?n.setAttribute("d",this.alternate):n.setAttribute("d",rt[this.pathName]),n},e.toMarkup=function(){return this.alternate?"<path d='"+this.alternate+"'/>":"<path d='"+rt[this.pathName]+"'/>"},s}(),G0=function(){function s(t){this.attributes=void 0,this.attributes=t||{}}var e=s.prototype;return e.toNode=function(){var r="http://www.w3.org/2000/svg",n=document.createElementNS(r,"line");for(var a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&n.setAttribute(a,this.attributes[a]);return n},e.toMarkup=function(){var r="<line";for(var n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&(r+=" "+n+"='"+this.attributes[n]+"'");return r+="/>",r},s}();function bt(s){if(s instanceof Ze)return s;throw new Error("Expected symbolNode but got "+String(s)+".")}function Nr(s){if(s instanceof c0)return s;throw new Error("Expected span<HtmlDomNode> but got "+String(s)+".")}var C0={bin:1,close:1,inner:1,open:1,punct:1,rel:1},Ft={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},at={math:{},text:{}},Ce=at;function i(s,e,t,r,n,a){at[s][n]={font:e,group:t,replace:r},a&&r&&(at[s][r]=at[s][n])}var o="math",q="text",d="main",w="ams",Ee="accent-token",J="bin",$e="close",ae="inner",k="mathord",O="op-token",W="open",be="punct",x="rel",Ie="spacing",A="textord";i(o,d,x,"≡","\\equiv",!0),i(o,d,x,"≺","\\prec",!0),i(o,d,x,"≻","\\succ",!0),i(o,d,x,"∼","\\sim",!0),i(o,d,x,"⊥","\\perp"),i(o,d,x,"⪯","\\preceq",!0),i(o,d,x,"⪰","\\succeq",!0),i(o,d,x,"≃","\\simeq",!0),i(o,d,x,"∣","\\mid",!0),i(o,d,x,"≪","\\ll",!0),i(o,d,x,"≫","\\gg",!0),i(o,d,x,"≍","\\asymp",!0),i(o,d,x,"∥","\\parallel"),i(o,d,x,"⋈","\\bowtie",!0),i(o,d,x,"⌣","\\smile",!0),i(o,d,x,"⊑","\\sqsubseteq",!0),i(o,d,x,"⊒","\\sqsupseteq",!0),i(o,d,x,"≐","\\doteq",!0),i(o,d,x,"⌢","\\frown",!0),i(o,d,x,"∋","\\ni",!0),i(o,d,x,"∝","\\propto",!0),i(o,d,x,"⊢","\\vdash",!0),i(o,d,x,"⊣","\\dashv",!0),i(o,d,x,"∋","\\owns"),i(o,d,be,".","\\ldotp"),i(o,d,be,"⋅","\\cdotp"),i(o,d,A,"#","\\#"),i(q,d,A,"#","\\#"),i(o,d,A,"&","\\&"),i(q,d,A,"&","\\&"),i(o,d,A,"ℵ","\\aleph",!0),i(o,d,A,"∀","\\forall",!0),i(o,d,A,"ℏ","\\hbar",!0),i(o,d,A,"∃","\\exists",!0),i(o,d,A,"∇","\\nabla",!0),i(o,d,A,"♭","\\flat",!0),i(o,d,A,"ℓ","\\ell",!0),i(o,d,A,"♮","\\natural",!0),i(o,d,A,"♣","\\clubsuit",!0),i(o,d,A,"℘","\\wp",!0),i(o,d,A,"♯","\\sharp",!0),i(o,d,A,"♢","\\diamondsuit",!0),i(o,d,A,"ℜ","\\Re",!0),i(o,d,A,"♡","\\heartsuit",!0),i(o,d,A,"ℑ","\\Im",!0),i(o,d,A,"♠","\\spadesuit",!0),i(o,d,A,"§","\\S",!0),i(q,d,A,"§","\\S"),i(o,d,A,"¶","\\P",!0),i(q,d,A,"¶","\\P"),i(o,d,A,"†","\\dag"),i(q,d,A,"†","\\dag"),i(q,d,A,"†","\\textdagger"),i(o,d,A,"‡","\\ddag"),i(q,d,A,"‡","\\ddag"),i(q,d,A,"‡","\\textdaggerdbl"),i(o,d,$e,"⎱","\\rmoustache",!0),i(o,d,W,"⎰","\\lmoustache",!0),i(o,d,$e,"⟯","\\rgroup",!0),i(o,d,W,"⟮","\\lgroup",!0),i(o,d,J,"∓","\\mp",!0),i(o,d,J,"⊖","\\ominus",!0),i(o,d,J,"⊎","\\uplus",!0),i(o,d,J,"⊓","\\sqcap",!0),i(o,d,J,"∗","\\ast"),i(o,d,J,"⊔","\\sqcup",!0),i(o,d,J,"◯","\\bigcirc",!0),i(o,d,J,"∙","\\bullet",!0),i(o,d,J,"‡","\\ddagger"),i(o,d,J,"≀","\\wr",!0),i(o,d,J,"⨿","\\amalg"),i(o,d,J,"&","\\And"),i(o,d,x,"⟵","\\longleftarrow",!0),i(o,d,x,"⇐","\\Leftarrow",!0),i(o,d,x,"⟸","\\Longleftarrow",!0),i(o,d,x,"⟶","\\longrightarrow",!0),i(o,d,x,"⇒","\\Rightarrow",!0),i(o,d,x,"⟹","\\Longrightarrow",!0),i(o,d,x,"↔","\\leftrightarrow",!0),i(o,d,x,"⟷","\\longleftrightarrow",!0),i(o,d,x,"⇔","\\Leftrightarrow",!0),i(o,d,x,"⟺","\\Longleftrightarrow",!0),i(o,d,x,"↦","\\mapsto",!0),i(o,d,x,"⟼","\\longmapsto",!0),i(o,d,x,"↗","\\nearrow",!0),i(o,d,x,"↩","\\hookleftarrow",!0),i(o,d,x,"↪","\\hookrightarrow",!0),i(o,d,x,"↘","\\searrow",!0),i(o,d,x,"↼","\\leftharpoonup",!0),i(o,d,x,"⇀","\\rightharpoonup",!0),i(o,d,x,"↙","\\swarrow",!0),i(o,d,x,"↽","\\leftharpoondown",!0),i(o,d,x,"⇁","\\rightharpoondown",!0),i(o,d,x,"↖","\\nwarrow",!0),i(o,d,x,"⇌","\\rightleftharpoons",!0),i(o,w,x,"≮","\\nless",!0),i(o,w,x,"","\\@nleqslant"),i(o,w,x,"","\\@nleqq"),i(o,w,x,"⪇","\\lneq",!0),i(o,w,x,"≨","\\lneqq",!0),i(o,w,x,"","\\@lvertneqq"),i(o,w,x,"⋦","\\lnsim",!0),i(o,w,x,"⪉","\\lnapprox",!0),i(o,w,x,"⊀","\\nprec",!0),i(o,w,x,"⋠","\\npreceq",!0),i(o,w,x,"⋨","\\precnsim",!0),i(o,w,x,"⪹","\\precnapprox",!0),i(o,w,x,"≁","\\nsim",!0),i(o,w,x,"","\\@nshortmid"),i(o,w,x,"∤","\\nmid",!0),i(o,w,x,"⊬","\\nvdash",!0),i(o,w,x,"⊭","\\nvDash",!0),i(o,w,x,"⋪","\\ntriangleleft"),i(o,w,x,"⋬","\\ntrianglelefteq",!0),i(o,w,x,"⊊","\\subsetneq",!0),i(o,w,x,"","\\@varsubsetneq"),i(o,w,x,"⫋","\\subsetneqq",!0),i(o,w,x,"","\\@varsubsetneqq"),i(o,w,x,"≯","\\ngtr",!0),i(o,w,x,"","\\@ngeqslant"),i(o,w,x,"","\\@ngeqq"),i(o,w,x,"⪈","\\gneq",!0),i(o,w,x,"≩","\\gneqq",!0),i(o,w,x,"","\\@gvertneqq"),i(o,w,x,"⋧","\\gnsim",!0),i(o,w,x,"⪊","\\gnapprox",!0),i(o,w,x,"⊁","\\nsucc",!0),i(o,w,x,"⋡","\\nsucceq",!0),i(o,w,x,"⋩","\\succnsim",!0),i(o,w,x,"⪺","\\succnapprox",!0),i(o,w,x,"≆","\\ncong",!0),i(o,w,x,"","\\@nshortparallel"),i(o,w,x,"∦","\\nparallel",!0),i(o,w,x,"⊯","\\nVDash",!0),i(o,w,x,"⋫","\\ntriangleright"),i(o,w,x,"⋭","\\ntrianglerighteq",!0),i(o,w,x,"","\\@nsupseteqq"),i(o,w,x,"⊋","\\supsetneq",!0),i(o,w,x,"","\\@varsupsetneq"),i(o,w,x,"⫌","\\supsetneqq",!0),i(o,w,x,"","\\@varsupsetneqq"),i(o,w,x,"⊮","\\nVdash",!0),i(o,w,x,"⪵","\\precneqq",!0),i(o,w,x,"⪶","\\succneqq",!0),i(o,w,x,"","\\@nsubseteqq"),i(o,w,J,"⊴","\\unlhd"),i(o,w,J,"⊵","\\unrhd"),i(o,w,x,"↚","\\nleftarrow",!0),i(o,w,x,"↛","\\nrightarrow",!0),i(o,w,x,"⇍","\\nLeftarrow",!0),i(o,w,x,"⇏","\\nRightarrow",!0),i(o,w,x,"↮","\\nleftrightarrow",!0),i(o,w,x,"⇎","\\nLeftrightarrow",!0),i(o,w,x,"△","\\vartriangle"),i(o,w,A,"ℏ","\\hslash"),i(o,w,A,"▽","\\triangledown"),i(o,w,A,"◊","\\lozenge"),i(o,w,A,"Ⓢ","\\circledS"),i(o,w,A,"®","\\circledR"),i(q,w,A,"®","\\circledR"),i(o,w,A,"∡","\\measuredangle",!0),i(o,w,A,"∄","\\nexists"),i(o,w,A,"℧","\\mho"),i(o,w,A,"Ⅎ","\\Finv",!0),i(o,w,A,"⅁","\\Game",!0),i(o,w,A,"‵","\\backprime"),i(o,w,A,"▲","\\blacktriangle"),i(o,w,A,"▼","\\blacktriangledown"),i(o,w,A,"■","\\blacksquare"),i(o,w,A,"⧫","\\blacklozenge"),i(o,w,A,"★","\\bigstar"),i(o,w,A,"∢","\\sphericalangle",!0),i(o,w,A,"∁","\\complement",!0),i(o,w,A,"ð","\\eth",!0),i(q,d,A,"ð","ð"),i(o,w,A,"╱","\\diagup"),i(o,w,A,"╲","\\diagdown"),i(o,w,A,"□","\\square"),i(o,w,A,"□","\\Box"),i(o,w,A,"◊","\\Diamond"),i(o,w,A,"¥","\\yen",!0),i(q,w,A,"¥","\\yen",!0),i(o,w,A,"✓","\\checkmark",!0),i(q,w,A,"✓","\\checkmark"),i(o,w,A,"ℶ","\\beth",!0),i(o,w,A,"ℸ","\\daleth",!0),i(o,w,A,"ℷ","\\gimel",!0),i(o,w,A,"ϝ","\\digamma",!0),i(o,w,A,"ϰ","\\varkappa"),i(o,w,W,"┌","\\@ulcorner",!0),i(o,w,$e,"┐","\\@urcorner",!0),i(o,w,W,"└","\\@llcorner",!0),i(o,w,$e,"┘","\\@lrcorner",!0),i(o,w,x,"≦","\\leqq",!0),i(o,w,x,"⩽","\\leqslant",!0),i(o,w,x,"⪕","\\eqslantless",!0),i(o,w,x,"≲","\\lesssim",!0),i(o,w,x,"⪅","\\lessapprox",!0),i(o,w,x,"≊","\\approxeq",!0),i(o,w,J,"⋖","\\lessdot"),i(o,w,x,"⋘","\\lll",!0),i(o,w,x,"≶","\\lessgtr",!0),i(o,w,x,"⋚","\\lesseqgtr",!0),i(o,w,x,"⪋","\\lesseqqgtr",!0),i(o,w,x,"≑","\\doteqdot"),i(o,w,x,"≓","\\risingdotseq",!0),i(o,w,x,"≒","\\fallingdotseq",!0),i(o,w,x,"∽","\\backsim",!0),i(o,w,x,"⋍","\\backsimeq",!0),i(o,w,x,"⫅","\\subseteqq",!0),i(o,w,x,"⋐","\\Subset",!0),i(o,w,x,"⊏","\\sqsubset",!0),i(o,w,x,"≼","\\preccurlyeq",!0),i(o,w,x,"⋞","\\curlyeqprec",!0),i(o,w,x,"≾","\\precsim",!0),i(o,w,x,"⪷","\\precapprox",!0),i(o,w,x,"⊲","\\vartriangleleft"),i(o,w,x,"⊴","\\trianglelefteq"),i(o,w,x,"⊨","\\vDash",!0),i(o,w,x,"⊪","\\Vvdash",!0),i(o,w,x,"⌣","\\smallsmile"),i(o,w,x,"⌢","\\smallfrown"),i(o,w,x,"≏","\\bumpeq",!0),i(o,w,x,"≎","\\Bumpeq",!0),i(o,w,x,"≧","\\geqq",!0),i(o,w,x,"⩾","\\geqslant",!0),i(o,w,x,"⪖","\\eqslantgtr",!0),i(o,w,x,"≳","\\gtrsim",!0),i(o,w,x,"⪆","\\gtrapprox",!0),i(o,w,J,"⋗","\\gtrdot"),i(o,w,x,"⋙","\\ggg",!0),i(o,w,x,"≷","\\gtrless",!0),i(o,w,x,"⋛","\\gtreqless",!0),i(o,w,x,"⪌","\\gtreqqless",!0),i(o,w,x,"≖","\\eqcirc",!0),i(o,w,x,"≗","\\circeq",!0),i(o,w,x,"≜","\\triangleq",!0),i(o,w,x,"∼","\\thicksim"),i(o,w,x,"≈","\\thickapprox"),i(o,w,x,"⫆","\\supseteqq",!0),i(o,w,x,"⋑","\\Supset",!0),i(o,w,x,"⊐","\\sqsupset",!0),i(o,w,x,"≽","\\succcurlyeq",!0),i(o,w,x,"⋟","\\curlyeqsucc",!0),i(o,w,x,"≿","\\succsim",!0),i(o,w,x,"⪸","\\succapprox",!0),i(o,w,x,"⊳","\\vartriangleright"),i(o,w,x,"⊵","\\trianglerighteq"),i(o,w,x,"⊩","\\Vdash",!0),i(o,w,x,"∣","\\shortmid"),i(o,w,x,"∥","\\shortparallel"),i(o,w,x,"≬","\\between",!0),i(o,w,x,"⋔","\\pitchfork",!0),i(o,w,x,"∝","\\varpropto"),i(o,w,x,"◀","\\blacktriangleleft"),i(o,w,x,"∴","\\therefore",!0),i(o,w,x,"∍","\\backepsilon"),i(o,w,x,"▶","\\blacktriangleright"),i(o,w,x,"∵","\\because",!0),i(o,w,x,"⋘","\\llless"),i(o,w,x,"⋙","\\gggtr"),i(o,w,J,"⊲","\\lhd"),i(o,w,J,"⊳","\\rhd"),i(o,w,x,"≂","\\eqsim",!0),i(o,d,x,"⋈","\\Join"),i(o,w,x,"≑","\\Doteq",!0),i(o,w,J,"∔","\\dotplus",!0),i(o,w,J,"∖","\\smallsetminus"),i(o,w,J,"⋒","\\Cap",!0),i(o,w,J,"⋓","\\Cup",!0),i(o,w,J,"⩞","\\doublebarwedge",!0),i(o,w,J,"⊟","\\boxminus",!0),i(o,w,J,"⊞","\\boxplus",!0),i(o,w,J,"⋇","\\divideontimes",!0),i(o,w,J,"⋉","\\ltimes",!0),i(o,w,J,"⋊","\\rtimes",!0),i(o,w,J,"⋋","\\leftthreetimes",!0),i(o,w,J,"⋌","\\rightthreetimes",!0),i(o,w,J,"⋏","\\curlywedge",!0),i(o,w,J,"⋎","\\curlyvee",!0),i(o,w,J,"⊝","\\circleddash",!0),i(o,w,J,"⊛","\\circledast",!0),i(o,w,J,"⋅","\\centerdot"),i(o,w,J,"⊺","\\intercal",!0),i(o,w,J,"⋒","\\doublecap"),i(o,w,J,"⋓","\\doublecup"),i(o,w,J,"⊠","\\boxtimes",!0),i(o,w,x,"⇢","\\dashrightarrow",!0),i(o,w,x,"⇠","\\dashleftarrow",!0),i(o,w,x,"⇇","\\leftleftarrows",!0),i(o,w,x,"⇆","\\leftrightarrows",!0),i(o,w,x,"⇚","\\Lleftarrow",!0),i(o,w,x,"↞","\\twoheadleftarrow",!0),i(o,w,x,"↢","\\leftarrowtail",!0),i(o,w,x,"↫","\\looparrowleft",!0),i(o,w,x,"⇋","\\leftrightharpoons",!0),i(o,w,x,"↶","\\curvearrowleft",!0),i(o,w,x,"↺","\\circlearrowleft",!0),i(o,w,x,"↰","\\Lsh",!0),i(o,w,x,"⇈","\\upuparrows",!0),i(o,w,x,"↿","\\upharpoonleft",!0),i(o,w,x,"⇃","\\downharpoonleft",!0),i(o,d,x,"⊶","\\origof",!0),i(o,d,x,"⊷","\\imageof",!0),i(o,w,x,"⊸","\\multimap",!0),i(o,w,x,"↭","\\leftrightsquigarrow",!0),i(o,w,x,"⇉","\\rightrightarrows",!0),i(o,w,x,"⇄","\\rightleftarrows",!0),i(o,w,x,"↠","\\twoheadrightarrow",!0),i(o,w,x,"↣","\\rightarrowtail",!0),i(o,w,x,"↬","\\looparrowright",!0),i(o,w,x,"↷","\\curvearrowright",!0),i(o,w,x,"↻","\\circlearrowright",!0),i(o,w,x,"↱","\\Rsh",!0),i(o,w,x,"⇊","\\downdownarrows",!0),i(o,w,x,"↾","\\upharpoonright",!0),i(o,w,x,"⇂","\\downharpoonright",!0),i(o,w,x,"⇝","\\rightsquigarrow",!0),i(o,w,x,"⇝","\\leadsto"),i(o,w,x,"⇛","\\Rrightarrow",!0),i(o,w,x,"↾","\\restriction"),i(o,d,A,"‘","`"),i(o,d,A,"$","\\$"),i(q,d,A,"$","\\$"),i(q,d,A,"$","\\textdollar"),i(o,d,A,"%","\\%"),i(q,d,A,"%","\\%"),i(o,d,A,"_","\\_"),i(q,d,A,"_","\\_"),i(q,d,A,"_","\\textunderscore"),i(o,d,A,"∠","\\angle",!0),i(o,d,A,"∞","\\infty",!0),i(o,d,A,"′","\\prime"),i(o,d,A,"△","\\triangle"),i(o,d,A,"Γ","\\Gamma",!0),i(o,d,A,"Δ","\\Delta",!0),i(o,d,A,"Θ","\\Theta",!0),i(o,d,A,"Λ","\\Lambda",!0),i(o,d,A,"Ξ","\\Xi",!0),i(o,d,A,"Π","\\Pi",!0),i(o,d,A,"Σ","\\Sigma",!0),i(o,d,A,"Υ","\\Upsilon",!0),i(o,d,A,"Φ","\\Phi",!0),i(o,d,A,"Ψ","\\Psi",!0),i(o,d,A,"Ω","\\Omega",!0),i(o,d,A,"A","Α"),i(o,d,A,"B","Β"),i(o,d,A,"E","Ε"),i(o,d,A,"Z","Ζ"),i(o,d,A,"H","Η"),i(o,d,A,"I","Ι"),i(o,d,A,"K","Κ"),i(o,d,A,"M","Μ"),i(o,d,A,"N","Ν"),i(o,d,A,"O","Ο"),i(o,d,A,"P","Ρ"),i(o,d,A,"T","Τ"),i(o,d,A,"X","Χ"),i(o,d,A,"¬","\\neg",!0),i(o,d,A,"¬","\\lnot"),i(o,d,A,"⊤","\\top"),i(o,d,A,"⊥","\\bot"),i(o,d,A,"∅","\\emptyset"),i(o,w,A,"∅","\\varnothing"),i(o,d,k,"α","\\alpha",!0),i(o,d,k,"β","\\beta",!0),i(o,d,k,"γ","\\gamma",!0),i(o,d,k,"δ","\\delta",!0),i(o,d,k,"ϵ","\\epsilon",!0),i(o,d,k,"ζ","\\zeta",!0),i(o,d,k,"η","\\eta",!0),i(o,d,k,"θ","\\theta",!0),i(o,d,k,"ι","\\iota",!0),i(o,d,k,"κ","\\kappa",!0),i(o,d,k,"λ","\\lambda",!0),i(o,d,k,"μ","\\mu",!0),i(o,d,k,"ν","\\nu",!0),i(o,d,k,"ξ","\\xi",!0),i(o,d,k,"ο","\\omicron",!0),i(o,d,k,"π","\\pi",!0),i(o,d,k,"ρ","\\rho",!0),i(o,d,k,"σ","\\sigma",!0),i(o,d,k,"τ","\\tau",!0),i(o,d,k,"υ","\\upsilon",!0),i(o,d,k,"ϕ","\\phi",!0),i(o,d,k,"χ","\\chi",!0),i(o,d,k,"ψ","\\psi",!0),i(o,d,k,"ω","\\omega",!0),i(o,d,k,"ε","\\varepsilon",!0),i(o,d,k,"ϑ","\\vartheta",!0),i(o,d,k,"ϖ","\\varpi",!0),i(o,d,k,"ϱ","\\varrho",!0),i(o,d,k,"ς","\\varsigma",!0),i(o,d,k,"φ","\\varphi",!0),i(o,d,J,"∗","*",!0),i(o,d,J,"+","+"),i(o,d,J,"−","-",!0),i(o,d,J,"⋅","\\cdot",!0),i(o,d,J,"∘","\\circ",!0),i(o,d,J,"÷","\\div",!0),i(o,d,J,"±","\\pm",!0),i(o,d,J,"×","\\times",!0),i(o,d,J,"∩","\\cap",!0),i(o,d,J,"∪","\\cup",!0),i(o,d,J,"∖","\\setminus",!0),i(o,d,J,"∧","\\land"),i(o,d,J,"∨","\\lor"),i(o,d,J,"∧","\\wedge",!0),i(o,d,J,"∨","\\vee",!0),i(o,d,A,"√","\\surd"),i(o,d,W,"⟨","\\langle",!0),i(o,d,W,"∣","\\lvert"),i(o,d,W,"∥","\\lVert"),i(o,d,$e,"?","?"),i(o,d,$e,"!","!"),i(o,d,$e,"⟩","\\rangle",!0),i(o,d,$e,"∣","\\rvert"),i(o,d,$e,"∥","\\rVert"),i(o,d,x,"=","="),i(o,d,x,":",":"),i(o,d,x,"≈","\\approx",!0),i(o,d,x,"≅","\\cong",!0),i(o,d,x,"≥","\\ge"),i(o,d,x,"≥","\\geq",!0),i(o,d,x,"←","\\gets"),i(o,d,x,">","\\gt",!0),i(o,d,x,"∈","\\in",!0),i(o,d,x,"","\\@not"),i(o,d,x,"⊂","\\subset",!0),i(o,d,x,"⊃","\\supset",!0),i(o,d,x,"⊆","\\subseteq",!0),i(o,d,x,"⊇","\\supseteq",!0),i(o,w,x,"⊈","\\nsubseteq",!0),i(o,w,x,"⊉","\\nsupseteq",!0),i(o,d,x,"⊨","\\models"),i(o,d,x,"←","\\leftarrow",!0),i(o,d,x,"≤","\\le"),i(o,d,x,"≤","\\leq",!0),i(o,d,x,"<","\\lt",!0),i(o,d,x,"→","\\rightarrow",!0),i(o,d,x,"→","\\to"),i(o,w,x,"≱","\\ngeq",!0),i(o,w,x,"≰","\\nleq",!0),i(o,d,Ie," ","\\ "),i(o,d,Ie," ","\\space"),i(o,d,Ie," ","\\nobreakspace"),i(q,d,Ie," ","\\ "),i(q,d,Ie," "," "),i(q,d,Ie," ","\\space"),i(q,d,Ie," ","\\nobreakspace"),i(o,d,Ie,null,"\\nobreak"),i(o,d,Ie,null,"\\allowbreak"),i(o,d,be,",",","),i(o,d,be,";",";"),i(o,w,J,"⊼","\\barwedge",!0),i(o,w,J,"⊻","\\veebar",!0),i(o,d,J,"⊙","\\odot",!0),i(o,d,J,"⊕","\\oplus",!0),i(o,d,J,"⊗","\\otimes",!0),i(o,d,A,"∂","\\partial",!0),i(o,d,J,"⊘","\\oslash",!0),i(o,w,J,"⊚","\\circledcirc",!0),i(o,w,J,"⊡","\\boxdot",!0),i(o,d,J,"△","\\bigtriangleup"),i(o,d,J,"▽","\\bigtriangledown"),i(o,d,J,"†","\\dagger"),i(o,d,J,"⋄","\\diamond"),i(o,d,J,"⋆","\\star"),i(o,d,J,"◃","\\triangleleft"),i(o,d,J,"▹","\\triangleright"),i(o,d,W,"{","\\{"),i(q,d,A,"{","\\{"),i(q,d,A,"{","\\textbraceleft"),i(o,d,$e,"}","\\}"),i(q,d,A,"}","\\}"),i(q,d,A,"}","\\textbraceright"),i(o,d,W,"{","\\lbrace"),i(o,d,$e,"}","\\rbrace"),i(o,d,W,"[","\\lbrack",!0),i(q,d,A,"[","\\lbrack",!0),i(o,d,$e,"]","\\rbrack",!0),i(q,d,A,"]","\\rbrack",!0),i(o,d,W,"(","\\lparen",!0),i(o,d,$e,")","\\rparen",!0),i(q,d,A,"<","\\textless",!0),i(q,d,A,">","\\textgreater",!0),i(o,d,W,"⌊","\\lfloor",!0),i(o,d,$e,"⌋","\\rfloor",!0),i(o,d,W,"⌈","\\lceil",!0),i(o,d,$e,"⌉","\\rceil",!0),i(o,d,A,"\\","\\backslash"),i(o,d,A,"∣","|"),i(o,d,A,"∣","\\vert"),i(q,d,A,"|","\\textbar",!0),i(o,d,A,"∥","\\|"),i(o,d,A,"∥","\\Vert"),i(q,d,A,"∥","\\textbardbl"),i(q,d,A,"~","\\textasciitilde"),i(q,d,A,"\\","\\textbackslash"),i(q,d,A,"^","\\textasciicircum"),i(o,d,x,"↑","\\uparrow",!0),i(o,d,x,"⇑","\\Uparrow",!0),i(o,d,x,"↓","\\downarrow",!0),i(o,d,x,"⇓","\\Downarrow",!0),i(o,d,x,"↕","\\updownarrow",!0),i(o,d,x,"⇕","\\Updownarrow",!0),i(o,d,O,"∐","\\coprod"),i(o,d,O,"⋁","\\bigvee"),i(o,d,O,"⋀","\\bigwedge"),i(o,d,O,"⨄","\\biguplus"),i(o,d,O,"⋂","\\bigcap"),i(o,d,O,"⋃","\\bigcup"),i(o,d,O,"∫","\\int"),i(o,d,O,"∫","\\intop"),i(o,d,O,"∬","\\iint"),i(o,d,O,"∭","\\iiint"),i(o,d,O,"∏","\\prod"),i(o,d,O,"∑","\\sum"),i(o,d,O,"⨂","\\bigotimes"),i(o,d,O,"⨁","\\bigoplus"),i(o,d,O,"⨀","\\bigodot"),i(o,d,O,"∮","\\oint"),i(o,d,O,"∯","\\oiint"),i(o,d,O,"∰","\\oiiint"),i(o,d,O,"⨆","\\bigsqcup"),i(o,d,O,"∫","\\smallint"),i(q,d,ae,"…","\\textellipsis"),i(o,d,ae,"…","\\mathellipsis"),i(q,d,ae,"…","\\ldots",!0),i(o,d,ae,"…","\\ldots",!0),i(o,d,ae,"⋯","\\@cdots",!0),i(o,d,ae,"⋱","\\ddots",!0),i(o,d,A,"⋮","\\varvdots"),i(o,d,Ee,"ˊ","\\acute"),i(o,d,Ee,"ˋ","\\grave"),i(o,d,Ee,"¨","\\ddot"),i(o,d,Ee,"~","\\tilde"),i(o,d,Ee,"ˉ","\\bar"),i(o,d,Ee,"˘","\\breve"),i(o,d,Ee,"ˇ","\\check"),i(o,d,Ee,"^","\\hat"),i(o,d,Ee,"⃗","\\vec"),i(o,d,Ee,"˙","\\dot"),i(o,d,Ee,"˚","\\mathring"),i(o,d,k,"","\\@imath"),i(o,d,k,"","\\@jmath"),i(o,d,A,"ı","ı"),i(o,d,A,"ȷ","ȷ"),i(q,d,A,"ı","\\i",!0),i(q,d,A,"ȷ","\\j",!0),i(q,d,A,"ß","\\ss",!0),i(q,d,A,"æ","\\ae",!0),i(q,d,A,"œ","\\oe",!0),i(q,d,A,"ø","\\o",!0),i(q,d,A,"Æ","\\AE",!0),i(q,d,A,"Œ","\\OE",!0),i(q,d,A,"Ø","\\O",!0),i(q,d,Ee,"ˊ","\\'"),i(q,d,Ee,"ˋ","\\`"),i(q,d,Ee,"ˆ","\\^"),i(q,d,Ee,"˜","\\~"),i(q,d,Ee,"ˉ","\\="),i(q,d,Ee,"˘","\\u"),i(q,d,Ee,"˙","\\."),i(q,d,Ee,"¸","\\c"),i(q,d,Ee,"˚","\\r"),i(q,d,Ee,"ˇ","\\v"),i(q,d,Ee,"¨",'\\"'),i(q,d,Ee,"˝","\\H"),i(q,d,Ee,"◯","\\textcircled");var i0={"--":!0,"---":!0,"``":!0,"''":!0};i(q,d,A,"–","--",!0),i(q,d,A,"–","\\textendash"),i(q,d,A,"—","---",!0),i(q,d,A,"—","\\textemdash"),i(q,d,A,"‘","`",!0),i(q,d,A,"‘","\\textquoteleft"),i(q,d,A,"’","'",!0),i(q,d,A,"’","\\textquoteright"),i(q,d,A,"“","``",!0),i(q,d,A,"“","\\textquotedblleft"),i(q,d,A,"”","''",!0),i(q,d,A,"”","\\textquotedblright"),i(o,d,A,"°","\\degree",!0),i(q,d,A,"°","\\degree"),i(q,d,A,"°","\\textdegree",!0),i(o,d,A,"£","\\pounds"),i(o,d,A,"£","\\mathsterling",!0),i(q,d,A,"£","\\pounds"),i(q,d,A,"£","\\textsterling",!0),i(o,w,A,"✠","\\maltese"),i(q,w,A,"✠","\\maltese");for(var yt='0123456789/@."',xt=0;xt<yt.length;xt++){var Rr=yt.charAt(xt);i(o,d,A,Rr,Rr)}for(var Un='0123456789!@*()-=+";:?/.,',Fr=0;Fr<Un.length;Fr++){var Gn=Un.charAt(Fr);i(q,d,A,Gn,Gn)}for(var Kt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",Ir=0;Ir<Kt.length;Ir++){var Qt=Kt.charAt(Ir);i(o,d,k,Qt,Qt),i(q,d,A,Qt,Qt)}i(o,w,A,"C","ℂ"),i(q,w,A,"C","ℂ"),i(o,w,A,"H","ℍ"),i(q,w,A,"H","ℍ"),i(o,w,A,"N","ℕ"),i(q,w,A,"N","ℕ"),i(o,w,A,"P","ℙ"),i(q,w,A,"P","ℙ"),i(o,w,A,"Q","ℚ"),i(q,w,A,"Q","ℚ"),i(o,w,A,"R","ℝ"),i(q,w,A,"R","ℝ"),i(o,w,A,"Z","ℤ"),i(q,w,A,"Z","ℤ"),i(o,d,k,"h","ℎ"),i(q,d,k,"h","ℎ");for(var de="",t0=0;t0<Kt.length;t0++){var Ue=Kt.charAt(t0);de=String.fromCharCode(55349,56320+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de),de=String.fromCharCode(55349,56372+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de),de=String.fromCharCode(55349,56424+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de),de=String.fromCharCode(55349,56580+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de),de=String.fromCharCode(55349,56736+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de),de=String.fromCharCode(55349,56788+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de),de=String.fromCharCode(55349,56840+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de),de=String.fromCharCode(55349,56944+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de),t0<26&&(de=String.fromCharCode(55349,56632+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de),de=String.fromCharCode(55349,56476+t0),i(o,d,k,Ue,de),i(q,d,A,Ue,de))}de=String.fromCharCode(55349,56668),i(o,d,k,"k",de),i(q,d,A,"k",de);for(var it=0;it<10;it++){var V0=it.toString();de=String.fromCharCode(55349,57294+it),i(o,d,k,V0,de),i(q,d,A,V0,de),de=String.fromCharCode(55349,57314+it),i(o,d,k,V0,de),i(q,d,A,V0,de),de=String.fromCharCode(55349,57324+it),i(o,d,k,V0,de),i(q,d,A,V0,de),de=String.fromCharCode(55349,57334+it),i(o,d,k,V0,de),i(q,d,A,V0,de)}for(var Lr="ÐÞþ",Or=0;Or<Lr.length;Or++){var Jt=Lr.charAt(Or);i(o,d,k,Jt,Jt),i(q,d,A,Jt,Jt)}var er=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["","",""],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Vn=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],Vi=function(e,t){var r=e.charCodeAt(0),n=e.charCodeAt(1),a=(r-55296)*1024+(n-56320)+65536,l=t==="math"?0:1;if(119808<=a&&a<120484){var c=Math.floor((a-119808)/26);return[er[c][2],er[c][l]]}else if(120782<=a&&a<=120831){var p=Math.floor((a-120782)/10);return[Vn[p][2],Vn[p][l]]}else{if(a===120485||a===120486)return[er[0][2],er[0][l]];if(120486<a&&a<120782)return["",""];throw new y("Unsupported character: "+e)}},tr=function(e,t,r){return Ce[r][e]&&Ce[r][e].replace&&(e=Ce[r][e].replace),{value:e,metrics:A0(e,t,r)}},y0=function(e,t,r,n,a){var l=tr(e,t,r),c=l.metrics;e=l.value;var p;if(c){var b=c.italic;(r==="text"||n&&n.font==="mathit")&&(b=0),p=new Ze(e,c.height,c.depth,b,c.skew,c.width,a)}else typeof console<"u"&&console.warn("No character metrics "+("for '"+e+"' in style '"+t+"' and mode '"+r+"'")),p=new Ze(e,0,0,0,0,0,a);if(n){p.maxFontSize=n.sizeMultiplier,n.style.isTight()&&p.classes.push("mtight");var S=n.getColor();S&&(p.style.color=S)}return p},Wi=function(e,t,r,n){return n===void 0&&(n=[]),r.font==="boldsymbol"&&tr(e,"Main-Bold",t).metrics?y0(e,"Main-Bold",t,r,n.concat(["mathbf"])):e==="\\"||Ce[t][e].font==="main"?y0(e,"Main-Regular",t,r,n):y0(e,"AMS-Regular",t,r,n.concat(["amsrm"]))},$i=function(e,t,r,n,a){return a!=="textord"&&tr(e,"Math-BoldItalic",t).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},Yi=function(e,t,r){var n=e.mode,a=e.text,l=["mord"],c=n==="math"||n==="text"&&t.font,p=c?t.font:t.fontFamily;if(a.charCodeAt(0)===55349){var b=Vi(a,n),S=b[0],B=b[1];return y0(a,S,n,t,l.concat(B))}else if(p){var R,N;if(p==="boldsymbol"){var L=$i(a,n,t,l,r);R=L.fontName,N=[L.fontClass]}else c?(R=Yn[p].fontName,N=[p]):(R=rr(p,t.fontWeight,t.fontShape),N=[p,t.fontWeight,t.fontShape]);if(tr(a,R,n).metrics)return y0(a,R,n,t,l.concat(N));if(i0.hasOwnProperty(a)&&R.slice(0,10)==="Typewriter"){for(var G=[],Z=0;Z<a.length;Z++)G.push(y0(a[Z],R,n,t,l.concat(N)));return $n(G)}}if(r==="mathord")return y0(a,"Math-Italic",n,t,l.concat(["mathnormal"]));if(r==="textord"){var re=Ce[n][a]&&Ce[n][a].font;if(re==="ams"){var se=rr("amsrm",t.fontWeight,t.fontShape);return y0(a,se,n,t,l.concat("amsrm",t.fontWeight,t.fontShape))}else if(re==="main"||!re){var he=rr("textrm",t.fontWeight,t.fontShape);return y0(a,he,n,t,l.concat(t.fontWeight,t.fontShape))}else{var we=rr(re,t.fontWeight,t.fontShape);return y0(a,we,n,t,l.concat(we,t.fontWeight,t.fontShape))}}else throw new Error("unexpected type: "+r+" in makeOrd")},ji=function(e,t){if(je(e.classes)!==je(t.classes)||e.skew!==t.skew||e.maxFontSize!==t.maxFontSize)return!1;if(e.classes.length===1){var r=e.classes[0];if(r==="mbin"||r==="mord")return!1}for(var n in e.style)if(e.style.hasOwnProperty(n)&&e.style[n]!==t.style[n])return!1;for(var a in t.style)if(t.style.hasOwnProperty(a)&&e.style[a]!==t.style[a])return!1;return!0},Xi=function(e){for(var t=0;t<e.length-1;t++){var r=e[t],n=e[t+1];r instanceof Ze&&n instanceof Ze&&ji(r,n)&&(r.text+=n.text,r.height=Math.max(r.height,n.height),r.depth=Math.max(r.depth,n.depth),r.italic=n.italic,e.splice(t+1,1),t--)}return e},qr=function(e){for(var t=0,r=0,n=0,a=0;a<e.children.length;a++){var l=e.children[a];l.height>t&&(t=l.height),l.depth>r&&(r=l.depth),l.maxFontSize>n&&(n=l.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=n},r0=function(e,t,r,n){var a=new c0(e,t,r,n);return qr(a),a},Wn=function(e,t,r,n){return new c0(e,t,r,n)},Zi=function(e,t,r){var n=r0([e],[],t);return n.height=Math.max(r||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),n.style.borderBottomWidth=V(n.height),n.maxFontSize=1,n},Ki=function(e,t,r,n){var a=new Nt(e,t,r,n);return qr(a),a},$n=function(e){var t=new O0(e);return qr(t),t},Qi=function(e,t){return e instanceof O0?r0([],[e],t):e},Ji=function(e){if(e.positionType==="individualShift"){for(var t=e.children,r=[t[0]],n=-t[0].shift-t[0].elem.depth,a=n,l=1;l<t.length;l++){var c=-t[l].shift-a-t[l].elem.depth,p=c-(t[l-1].elem.height+t[l-1].elem.depth);a=a+c,r.push({type:"kern",size:p}),r.push(t[l])}return{children:r,depth:n}}var b;if(e.positionType==="top"){for(var S=e.positionData,B=0;B<e.children.length;B++){var R=e.children[B];S-=R.type==="kern"?R.size:R.elem.height+R.elem.depth}b=S}else if(e.positionType==="bottom")b=-e.positionData;else{var N=e.children[0];if(N.type!=="elem")throw new Error('First child must have type "elem".');if(e.positionType==="shift")b=-N.elem.depth-e.positionData;else if(e.positionType==="firstBaseline")b=-N.elem.depth;else throw new Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:b}},es=function(e,t){for(var r=Ji(e),n=r.children,a=r.depth,l=0,c=0;c<n.length;c++){var p=n[c];if(p.type==="elem"){var b=p.elem;l=Math.max(l,b.maxFontSize,b.height)}}l+=2;var S=r0(["pstrut"],[]);S.style.height=V(l);for(var B=[],R=a,N=a,L=a,G=0;G<n.length;G++){var Z=n[G];if(Z.type==="kern")L+=Z.size;else{var re=Z.elem,se=Z.wrapperClasses||[],he=Z.wrapperStyle||{},we=r0(se,[S,re],void 0,he);we.style.top=V(-l-L-re.depth),Z.marginLeft&&(we.style.marginLeft=Z.marginLeft),Z.marginRight&&(we.style.marginRight=Z.marginRight),B.push(we),L+=re.height+re.depth}R=Math.min(R,L),N=Math.max(N,L)}var pe=r0(["vlist"],B);pe.style.height=V(N);var Te;if(R<0){var xe=r0([],[]),Me=r0(["vlist"],[xe]);Me.style.height=V(-R);var _e=r0(["vlist-s"],[new Ze("​")]);Te=[r0(["vlist-r"],[pe,_e]),r0(["vlist-r"],[Me])]}else Te=[r0(["vlist-r"],[pe])];var Ye=r0(["vlist-t"],Te);return Te.length===2&&Ye.classes.push("vlist-t2"),Ye.height=N,Ye.depth=-R,Ye},ts=function(e,t){var r=r0(["mspace"],[],t),n=ze(e,t);return r.style.marginRight=V(n),r},rr=function(e,t,r){var n="";switch(e){case"amsrm":n="AMS";break;case"textrm":n="Main";break;case"textsf":n="SansSerif";break;case"texttt":n="Typewriter";break;default:n=e}var a;return t==="textbf"&&r==="textit"?a="BoldItalic":t==="textbf"?a="Bold":t==="textit"?a="Italic":a="Regular",n+"-"+a},Yn={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},jn={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},rs=function(e,t){var r=jn[e],n=r[0],a=r[1],l=r[2],c=new T0(n),p=new m0([c],{width:V(a),height:V(l),style:"width:"+V(a),viewBox:"0 0 "+1e3*a+" "+1e3*l,preserveAspectRatio:"xMinYMin"}),b=Wn(["overlay"],[p],t);return b.height=l,b.style.height=V(l),b.style.width=V(a),b},E={fontMap:Yn,makeSymbol:y0,mathsym:Wi,makeSpan:r0,makeSvgSpan:Wn,makeLineSpan:Zi,makeAnchor:Ki,makeFragment:$n,wrapFragment:Qi,makeVList:es,makeOrd:Yi,makeGlue:ts,staticSvg:rs,svgData:jn,tryCombineChars:Xi},Re={number:3,unit:"mu"},st={number:4,unit:"mu"},D0={number:5,unit:"mu"},ns={mord:{mop:Re,mbin:st,mrel:D0,minner:Re},mop:{mord:Re,mop:Re,mrel:D0,minner:Re},mbin:{mord:st,mop:st,mopen:st,minner:st},mrel:{mord:D0,mop:D0,mopen:D0,minner:D0},mopen:{},mclose:{mop:Re,mbin:st,mrel:D0,minner:Re},mpunct:{mord:Re,mop:Re,mrel:D0,mopen:Re,mclose:Re,mpunct:Re,minner:Re},minner:{mord:Re,mop:Re,mbin:st,mrel:D0,mopen:Re,mpunct:Re,minner:Re}},as={mord:{mop:Re},mop:{mord:Re,mop:Re},mbin:{},mrel:{},mopen:{},mclose:{mop:Re},mpunct:{},minner:{mop:Re}},Xn={},nr={},ar={};function Q(s){for(var e=s.type,t=s.names,r=s.props,n=s.handler,a=s.htmlBuilder,l=s.mathmlBuilder,c={type:e,numArgs:r.numArgs,argTypes:r.argTypes,allowedInArgument:!!r.allowedInArgument,allowedInText:!!r.allowedInText,allowedInMath:r.allowedInMath===void 0?!0:r.allowedInMath,numOptionalArgs:r.numOptionalArgs||0,infix:!!r.infix,primitive:!!r.primitive,handler:n},p=0;p<t.length;++p)Xn[t[p]]=c;e&&(a&&(nr[e]=a),l&&(ar[e]=l))}function lt(s){var e=s.type,t=s.htmlBuilder,r=s.mathmlBuilder;Q({type:e,names:[],props:{numArgs:0},handler:function(){throw new Error("Should never be called.")},htmlBuilder:t,mathmlBuilder:r})}var ir=function(e){return e.type==="ordgroup"&&e.body.length===1?e.body[0]:e},Pe=function(e){return e.type==="ordgroup"?e.body:[e]},_0=E.makeSpan,is=["leftmost","mbin","mopen","mrel","mop","mpunct"],ss=["rightmost","mrel","mclose","mpunct"],ls={display:K.DISPLAY,text:K.TEXT,script:K.SCRIPT,scriptscript:K.SCRIPTSCRIPT},os={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},Ge=function(e,t,r,n){n===void 0&&(n=[null,null]);for(var a=[],l=0;l<e.length;l++){var c=ye(e[l],t);if(c instanceof O0){var p=c.children;a.push.apply(a,p)}else a.push(c)}if(E.tryCombineChars(a),!r)return a;var b=t;if(e.length===1){var S=e[0];S.type==="sizing"?b=t.havingSize(S.size):S.type==="styling"&&(b=t.havingStyle(ls[S.style]))}var B=_0([n[0]||"leftmost"],[],t),R=_0([n[1]||"rightmost"],[],t),N=r==="root";return Zn(a,function(L,G){var Z=G.classes[0],re=L.classes[0];Z==="mbin"&&Y.contains(ss,re)?G.classes[0]="mord":re==="mbin"&&Y.contains(is,Z)&&(L.classes[0]="mord")},{node:B},R,N),Zn(a,function(L,G){var Z=Pr(G),re=Pr(L),se=Z&&re?L.hasClass("mtight")?as[Z][re]:ns[Z][re]:null;if(se)return E.makeGlue(se,b)},{node:B},R,N),a},Zn=function s(e,t,r,n,a){n&&e.push(n);for(var l=0;l<e.length;l++){var c=e[l],p=Kn(c);if(p){s(p.children,t,r,null,a);continue}var b=!c.hasClass("mspace");if(b){var S=t(c,r.node);S&&(r.insertAfter?r.insertAfter(S):(e.unshift(S),l++))}b?r.node=c:a&&c.hasClass("newline")&&(r.node=_0(["leftmost"])),r.insertAfter=function(B){return function(R){e.splice(B+1,0,R),l++}}(l)}n&&e.pop()},Kn=function(e){return e instanceof O0||e instanceof Nt||e instanceof c0&&e.hasClass("enclosing")?e:null},us=function s(e,t){var r=Kn(e);if(r){var n=r.children;if(n.length){if(t==="right")return s(n[n.length-1],"right");if(t==="left")return s(n[0],"left")}}return e},Pr=function(e,t){return e?(t&&(e=us(e,t)),os[e.classes[0]]||null):null},It=function(e,t){var r=["nulldelimiter"].concat(e.baseSizingClasses());return _0(t.concat(r))},ye=function(e,t,r){if(!e)return _0();if(nr[e.type]){var n=nr[e.type](e,t);if(r&&t.size!==r.size){n=_0(t.sizingClasses(r),[n],t);var a=t.sizeMultiplier/r.sizeMultiplier;n.height*=a,n.depth*=a}return n}else throw new y("Got group of unknown type: '"+e.type+"'")};function sr(s,e){var t=_0(["base"],s,e),r=_0(["strut"]);return r.style.height=V(t.height+t.depth),t.depth&&(r.style.verticalAlign=V(-t.depth)),t.children.unshift(r),t}function Hr(s,e){var t=null;s.length===1&&s[0].type==="tag"&&(t=s[0].tag,s=s[0].body);var r=Ge(s,e,"root"),n;r.length===2&&r[1].hasClass("tag")&&(n=r.pop());for(var a=[],l=[],c=0;c<r.length;c++)if(l.push(r[c]),r[c].hasClass("mbin")||r[c].hasClass("mrel")||r[c].hasClass("allowbreak")){for(var p=!1;c<r.length-1&&r[c+1].hasClass("mspace")&&!r[c+1].hasClass("newline");)c++,l.push(r[c]),r[c].hasClass("nobreak")&&(p=!0);p||(a.push(sr(l,e)),l=[])}else r[c].hasClass("newline")&&(l.pop(),l.length>0&&(a.push(sr(l,e)),l=[]),a.push(r[c]));l.length>0&&a.push(sr(l,e));var b;t?(b=sr(Ge(t,e,!0)),b.classes=["tag"],a.push(b)):n&&a.push(n);var S=_0(["katex-html"],a);if(S.setAttribute("aria-hidden","true"),b){var B=b.children[0];B.style.height=V(S.height+S.depth),S.depth&&(B.style.verticalAlign=V(-S.depth))}return S}function Qn(s){return new O0(s)}var d0=function(){function s(t,r,n){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=t,this.attributes={},this.children=r||[],this.classes=n||[]}var e=s.prototype;return e.setAttribute=function(r,n){this.attributes[r]=n},e.getAttribute=function(r){return this.attributes[r]},e.toNode=function(){var r=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&r.setAttribute(n,this.attributes[n]);this.classes.length>0&&(r.className=je(this.classes));for(var a=0;a<this.children.length;a++)r.appendChild(this.children[a].toNode());return r},e.toMarkup=function(){var r="<"+this.type;for(var n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&(r+=" "+n+'="',r+=Y.escape(this.attributes[n]),r+='"');this.classes.length>0&&(r+=' class ="'+Y.escape(je(this.classes))+'"'),r+=">";for(var a=0;a<this.children.length;a++)r+=this.children[a].toMarkup();return r+="</"+this.type+">",r},e.toText=function(){return this.children.map(function(r){return r.toText()}).join("")},s}(),Lt=function(){function s(t){this.text=void 0,this.text=t}var e=s.prototype;return e.toNode=function(){return document.createTextNode(this.text)},e.toMarkup=function(){return Y.escape(this.toText())},e.toText=function(){return this.text},s}(),hs=function(){function s(t){this.width=void 0,this.character=void 0,this.width=t,t>=.05555&&t<=.05556?this.character=" ":t>=.1666&&t<=.1667?this.character=" ":t>=.2222&&t<=.2223?this.character=" ":t>=.2777&&t<=.2778?this.character="  ":t>=-.05556&&t<=-.05555?this.character=" ⁣":t>=-.1667&&t<=-.1666?this.character=" ⁣":t>=-.2223&&t<=-.2222?this.character=" ⁣":t>=-.2778&&t<=-.2777?this.character=" ⁣":this.character=null}var e=s.prototype;return e.toNode=function(){if(this.character)return document.createTextNode(this.character);var r=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return r.setAttribute("width",V(this.width)),r},e.toMarkup=function(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+V(this.width)+'"/>'},e.toText=function(){return this.character?this.character:" "},s}(),H={MathNode:d0,TextNode:Lt,SpaceNode:hs,newDocumentFragment:Qn},f0=function(e,t,r){return Ce[t][e]&&Ce[t][e].replace&&e.charCodeAt(0)!==55349&&!(i0.hasOwnProperty(e)&&r&&(r.fontFamily&&r.fontFamily.slice(4,6)==="tt"||r.font&&r.font.slice(4,6)==="tt"))&&(e=Ce[t][e].replace),new H.TextNode(e)},Ur=function(e){return e.length===1?e[0]:new H.MathNode("mrow",e)},Gr=function(e,t){if(t.fontFamily==="texttt")return"monospace";if(t.fontFamily==="textsf")return t.fontShape==="textit"&&t.fontWeight==="textbf"?"sans-serif-bold-italic":t.fontShape==="textit"?"sans-serif-italic":t.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(t.fontShape==="textit"&&t.fontWeight==="textbf")return"bold-italic";if(t.fontShape==="textit")return"italic";if(t.fontWeight==="textbf")return"bold";var r=t.font;if(!r||r==="mathnormal")return null;var n=e.mode;if(r==="mathit")return"italic";if(r==="boldsymbol")return e.type==="textord"?"bold":"bold-italic";if(r==="mathbf")return"bold";if(r==="mathbb")return"double-struck";if(r==="mathfrak")return"fraktur";if(r==="mathscr"||r==="mathcal")return"script";if(r==="mathsf")return"sans-serif";if(r==="mathtt")return"monospace";var a=e.text;if(Y.contains(["\\imath","\\jmath"],a))return null;Ce[n][a]&&Ce[n][a].replace&&(a=Ce[n][a].replace);var l=E.fontMap[r].fontName;return A0(a,l,n)?E.fontMap[r].variant:null},n0=function(e,t,r){if(e.length===1){var n=Be(e[0],t);return r&&n instanceof d0&&n.type==="mo"&&(n.setAttribute("lspace","0em"),n.setAttribute("rspace","0em")),[n]}for(var a=[],l,c=0;c<e.length;c++){var p=Be(e[c],t);if(p instanceof d0&&l instanceof d0){if(p.type==="mtext"&&l.type==="mtext"&&p.getAttribute("mathvariant")===l.getAttribute("mathvariant")){var b;(b=l.children).push.apply(b,p.children);continue}else if(p.type==="mn"&&l.type==="mn"){var S;(S=l.children).push.apply(S,p.children);continue}else if(p.type==="mi"&&p.children.length===1&&l.type==="mn"){var B=p.children[0];if(B instanceof Lt&&B.text==="."){var R;(R=l.children).push.apply(R,p.children);continue}}else if(l.type==="mi"&&l.children.length===1){var N=l.children[0];if(N instanceof Lt&&N.text==="̸"&&(p.type==="mo"||p.type==="mi"||p.type==="mn")){var L=p.children[0];L instanceof Lt&&L.text.length>0&&(L.text=L.text.slice(0,1)+"̸"+L.text.slice(1),a.pop())}}}a.push(p),l=p}return a},W0=function(e,t,r){return Ur(n0(e,t,r))},Be=function(e,t){if(!e)return new H.MathNode("mrow");if(ar[e.type]){var r=ar[e.type](e,t);return r}else throw new y("Got group of unknown type: '"+e.type+"'")};function Jn(s,e,t,r,n){var a=n0(s,t),l;a.length===1&&a[0]instanceof d0&&Y.contains(["mrow","mtable"],a[0].type)?l=a[0]:l=new H.MathNode("mrow",a);var c=new H.MathNode("annotation",[new H.TextNode(e)]);c.setAttribute("encoding","application/x-tex");var p=new H.MathNode("semantics",[l,c]),b=new H.MathNode("math",[p]);b.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),r&&b.setAttribute("display","block");var S=n?"katex":"katex-mathml";return E.makeSpan([S],[b])}var ea=function(e){return new gt({style:e.displayMode?K.DISPLAY:K.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},ta=function(e,t){if(t.displayMode){var r=["katex-display"];t.leqno&&r.push("leqno"),t.fleqn&&r.push("fleqn"),e=E.makeSpan(r,[e])}return e},cs=function(e,t,r){var n=ea(r),a;if(r.output==="mathml")return Jn(e,t,n,r.displayMode,!0);if(r.output==="html"){var l=Hr(e,n);a=E.makeSpan(["katex"],[l])}else{var c=Jn(e,t,n,r.displayMode,!1),p=Hr(e,n);a=E.makeSpan(["katex"],[c,p])}return ta(a,r)},ms=function(e,t,r){var n=ea(r),a=Hr(e,n),l=E.makeSpan(["katex"],[a]);return ta(l,r)},ds={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},fs=function(e){var t=new H.MathNode("mo",[new H.TextNode(ds[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},ps={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},gs=function(e){return e.type==="ordgroup"?e.body.length:1},vs=function(e,t){function r(){var p=4e5,b=e.label.slice(1);if(Y.contains(["widehat","widecheck","widetilde","utilde"],b)){var S=e,B=gs(S.base),R,N,L;if(B>5)b==="widehat"||b==="widecheck"?(R=420,p=2364,L=.42,N=b+"4"):(R=312,p=2340,L=.34,N="tilde4");else{var G=[1,1,2,2,3,3][B];b==="widehat"||b==="widecheck"?(p=[0,1062,2364,2364,2364][G],R=[0,239,300,360,420][G],L=[0,.24,.3,.3,.36,.42][G],N=b+G):(p=[0,600,1033,2339,2340][G],R=[0,260,286,306,312][G],L=[0,.26,.286,.3,.306,.34][G],N="tilde"+G)}var Z=new T0(N),re=new m0([Z],{width:"100%",height:V(L),viewBox:"0 0 "+p+" "+R,preserveAspectRatio:"none"});return{span:E.makeSvgSpan([],[re],t),minWidth:0,height:L}}else{var se=[],he=ps[b],we=he[0],pe=he[1],Te=he[2],xe=Te/1e3,Me=we.length,_e,Ye;if(Me===1){var s0=he[3];_e=["hide-tail"],Ye=[s0]}else if(Me===2)_e=["halfarrow-left","halfarrow-right"],Ye=["xMinYMin","xMaxYMin"];else if(Me===3)_e=["brace-left","brace-center","brace-right"],Ye=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+Me+" children.");for(var Fe=0;Fe<Me;Fe++){var ot=new T0(we[Fe]),p0=new m0([ot],{width:"400em",height:V(xe),viewBox:"0 0 "+p+" "+Te,preserveAspectRatio:Ye[Fe]+" slice"}),Ke=E.makeSvgSpan([_e[Fe]],[p0],t);if(Me===1)return{span:Ke,minWidth:pe,height:xe};Ke.style.height=V(xe),se.push(Ke)}return{span:E.makeSpan(["stretchy"],se,t),minWidth:pe,height:xe}}}var n=r(),a=n.span,l=n.minWidth,c=n.height;return a.height=c,a.style.height=V(c),l>0&&(a.style.minWidth=V(l)),a},bs=function(e,t,r,n,a){var l,c=e.height+e.depth+r+n;if(/fbox|color|angl/.test(t)){if(l=E.makeSpan(["stretchy",t],[],a),t==="fbox"){var p=a.color&&a.getColor();p&&(l.style.borderColor=p)}}else{var b=[];/^[bx]cancel$/.test(t)&&b.push(new G0({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&b.push(new G0({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var S=new m0(b,{width:"100%",height:V(c)});l=E.makeSvgSpan([],[S],a)}return l.height=c,l.style.height=V(c),l},N0={encloseSpan:bs,mathMLnode:fs,svgSpan:vs};function fe(s,e){if(!s||s.type!==e)throw new Error("Expected node of type "+e+", but got "+(s?"node of type "+s.type:String(s)));return s}function Vr(s){var e=lr(s);if(!e)throw new Error("Expected node of symbol group type, but got "+(s?"node of type "+s.type:String(s)));return e}function lr(s){return s&&(s.type==="atom"||Ft.hasOwnProperty(s.type))?s:null}var Wr=function(e,t){var r,n,a;e&&e.type==="supsub"?(n=fe(e.base,"accent"),r=n.base,e.base=r,a=Nr(ye(e,t)),e.base=n):(n=fe(e,"accent"),r=n.base);var l=ye(r,t.havingCrampedStyle()),c=n.isShifty&&Y.isCharacterBox(r),p=0;if(c){var b=Y.getBaseElem(r),S=ye(b,t.havingCrampedStyle());p=bt(S).skew}var B=n.label==="\\c",R=B?l.height+l.depth:Math.min(l.height,t.fontMetrics().xHeight),N;if(n.isStretchy)N=N0.svgSpan(n,t),N=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"elem",elem:N,wrapperClasses:["svg-align"],wrapperStyle:p>0?{width:"calc(100% - "+V(2*p)+")",marginLeft:V(2*p)}:void 0}]},t);else{var L,G;n.label==="\\vec"?(L=E.staticSvg("vec",t),G=E.svgData.vec[1]):(L=E.makeOrd({mode:n.mode,text:n.label},t,"textord"),L=bt(L),L.italic=0,G=L.width,B&&(R+=L.depth)),N=E.makeSpan(["accent-body"],[L]);var Z=n.label==="\\textcircled";Z&&(N.classes.push("accent-full"),R=l.height);var re=p;Z||(re-=G/2),N.style.left=V(re),n.label==="\\textcircled"&&(N.style.top=".2em"),N=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:-R},{type:"elem",elem:N}]},t)}var se=E.makeSpan(["mord","accent"],[N],t);return a?(a.children[0]=se,a.height=Math.max(se.height,a.height),a.classes[0]="mord",a):se},ra=function(e,t){var r=e.isStretchy?N0.mathMLnode(e.label):new H.MathNode("mo",[f0(e.label,e.mode)]),n=new H.MathNode("mover",[Be(e.base,t),r]);return n.setAttribute("accent","true"),n},ys=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(function(s){return"\\"+s}).join("|"));Q({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:function(e,t){var r=ir(t[0]),n=!ys.test(e.funcName),a=!n||e.funcName==="\\widehat"||e.funcName==="\\widetilde"||e.funcName==="\\widecheck";return{type:"accent",mode:e.parser.mode,label:e.funcName,isStretchy:n,isShifty:a,base:r}},htmlBuilder:Wr,mathmlBuilder:ra}),Q({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:function(e,t){var r=t[0],n=e.parser.mode;return n==="math"&&(e.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+e.funcName+" works only in text mode"),n="text"),{type:"accent",mode:n,label:e.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:Wr,mathmlBuilder:ra}),Q({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=e.funcName,a=t[0];return{type:"accentUnder",mode:r.mode,label:n,base:a}},htmlBuilder:function(e,t){var r=ye(e.base,t),n=N0.svgSpan(e,t),a=e.label==="\\utilde"?.12:0,l=E.makeVList({positionType:"top",positionData:r.height,children:[{type:"elem",elem:n,wrapperClasses:["svg-align"]},{type:"kern",size:a},{type:"elem",elem:r}]},t);return E.makeSpan(["mord","accentunder"],[l],t)},mathmlBuilder:function(e,t){var r=N0.mathMLnode(e.label),n=new H.MathNode("munder",[Be(e.base,t),r]);return n.setAttribute("accentunder","true"),n}});var or=function(e){var t=new H.MathNode("mpadded",e?[e]:[]);return t.setAttribute("width","+0.6em"),t.setAttribute("lspace","0.3em"),t};Q({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler:function(e,t,r){var n=e.parser,a=e.funcName;return{type:"xArrow",mode:n.mode,label:a,body:t[0],below:r[0]}},htmlBuilder:function(e,t){var r=t.style,n=t.havingStyle(r.sup()),a=E.wrapFragment(ye(e.body,n,t),t),l=e.label.slice(0,2)==="\\x"?"x":"cd";a.classes.push(l+"-arrow-pad");var c;e.below&&(n=t.havingStyle(r.sub()),c=E.wrapFragment(ye(e.below,n,t),t),c.classes.push(l+"-arrow-pad"));var p=N0.svgSpan(e,t),b=-t.fontMetrics().axisHeight+.5*p.height,S=-t.fontMetrics().axisHeight-.5*p.height-.111;(a.depth>.25||e.label==="\\xleftequilibrium")&&(S-=a.depth);var B;if(c){var R=-t.fontMetrics().axisHeight+c.height+.5*p.height+.111;B=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:S},{type:"elem",elem:p,shift:b},{type:"elem",elem:c,shift:R}]},t)}else B=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:S},{type:"elem",elem:p,shift:b}]},t);return B.children[0].children[0].children[1].classes.push("svg-align"),E.makeSpan(["mrel","x-arrow"],[B],t)},mathmlBuilder:function(e,t){var r=N0.mathMLnode(e.label);r.setAttribute("minsize",e.label.charAt(0)==="x"?"1.75em":"3.0em");var n;if(e.body){var a=or(Be(e.body,t));if(e.below){var l=or(Be(e.below,t));n=new H.MathNode("munderover",[r,l,a])}else n=new H.MathNode("mover",[r,a])}else if(e.below){var c=or(Be(e.below,t));n=new H.MathNode("munder",[r,c])}else n=or(),n=new H.MathNode("mover",[r,n]);return n}});var xs=E.makeSpan;function na(s,e){var t=Ge(s.body,e,!0);return xs([s.mclass],t,e)}function aa(s,e){var t,r=n0(s.body,e);return s.mclass==="minner"?t=new H.MathNode("mpadded",r):s.mclass==="mord"?s.isCharacterBox?(t=r[0],t.type="mi"):t=new H.MathNode("mi",r):(s.isCharacterBox?(t=r[0],t.type="mo"):t=new H.MathNode("mo",r),s.mclass==="mbin"?(t.attributes.lspace="0.22em",t.attributes.rspace="0.22em"):s.mclass==="mpunct"?(t.attributes.lspace="0em",t.attributes.rspace="0.17em"):s.mclass==="mopen"||s.mclass==="mclose"?(t.attributes.lspace="0em",t.attributes.rspace="0em"):s.mclass==="minner"&&(t.attributes.lspace="0.0556em",t.attributes.width="+0.1111em")),t}Q({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler:function(e,t){var r=e.parser,n=e.funcName,a=t[0];return{type:"mclass",mode:r.mode,mclass:"m"+n.slice(5),body:Pe(a),isCharacterBox:Y.isCharacterBox(a)}},htmlBuilder:na,mathmlBuilder:aa});var ur=function(e){var t=e.type==="ordgroup"&&e.body.length?e.body[0]:e;return t.type==="atom"&&(t.family==="bin"||t.family==="rel")?"m"+t.family:"mord"};Q({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler:function(e,t){var r=e.parser;return{type:"mclass",mode:r.mode,mclass:ur(t[0]),body:Pe(t[1]),isCharacterBox:Y.isCharacterBox(t[1])}}}),Q({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler:function(e,t){var r=e.parser,n=e.funcName,a=t[1],l=t[0],c;n!=="\\stackrel"?c=ur(a):c="mrel";var p={type:"op",mode:a.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:n!=="\\stackrel",body:Pe(a)},b={type:"supsub",mode:l.mode,base:p,sup:n==="\\underset"?null:l,sub:n==="\\underset"?l:null};return{type:"mclass",mode:r.mode,mclass:c,body:[b],isCharacterBox:Y.isCharacterBox(b)}},htmlBuilder:na,mathmlBuilder:aa}),Q({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser;return{type:"pmb",mode:r.mode,mclass:ur(t[0]),body:Pe(t[0])}},htmlBuilder:function(e,t){var r=Ge(e.body,t,!0),n=E.makeSpan([e.mclass],r,t);return n.style.textShadow="0.02em 0.01em 0.04px",n},mathmlBuilder:function(e,t){var r=n0(e.body,t),n=new H.MathNode("mstyle",r);return n.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),n}});var ws={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},ia=function(){return{type:"styling",body:[],mode:"math",style:"display"}},sa=function(e){return e.type==="textord"&&e.text==="@"},ks=function(e,t){return(e.type==="mathord"||e.type==="atom")&&e.text===t};function Ss(s,e,t){var r=ws[s];switch(r){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return t.callFunction(r,[e[0]],[e[1]]);case"\\uparrow":case"\\downarrow":{var n=t.callFunction("\\\\cdleft",[e[0]],[]),a={type:"atom",text:r,mode:"math",family:"rel"},l=t.callFunction("\\Big",[a],[]),c=t.callFunction("\\\\cdright",[e[1]],[]),p={type:"ordgroup",mode:"math",body:[n,l,c]};return t.callFunction("\\\\cdparent",[p],[])}case"\\\\cdlongequal":return t.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{var b={type:"textord",text:"\\Vert",mode:"math"};return t.callFunction("\\Big",[b],[])}default:return{type:"textord",text:" ",mode:"math"}}}function As(s){var e=[];for(s.gullet.beginGroup(),s.gullet.macros.set("\\cr","\\\\\\relax"),s.gullet.beginGroup();;){e.push(s.parseExpression(!1,"\\\\")),s.gullet.endGroup(),s.gullet.beginGroup();var t=s.fetch().text;if(t==="&"||t==="\\\\")s.consume();else if(t==="\\end"){e[e.length-1].length===0&&e.pop();break}else throw new y("Expected \\\\ or \\cr or \\end",s.nextToken)}for(var r=[],n=[r],a=0;a<e.length;a++){for(var l=e[a],c=ia(),p=0;p<l.length;p++)if(!sa(l[p]))c.body.push(l[p]);else{r.push(c),p+=1;var b=Vr(l[p]).text,S=new Array(2);if(S[0]={type:"ordgroup",mode:"math",body:[]},S[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(b)>-1))if("<>AV".indexOf(b)>-1)for(var B=0;B<2;B++){for(var R=!0,N=p+1;N<l.length;N++){if(ks(l[N],b)){R=!1,p=N;break}if(sa(l[N]))throw new y("Missing a "+b+" character to complete a CD arrow.",l[N]);S[B].body.push(l[N])}if(R)throw new y("Missing a "+b+" character to complete a CD arrow.",l[p])}else throw new y('Expected one of "<>AV=|." after @',l[p]);var L=Ss(b,S,s),G={type:"styling",body:[L],mode:"math",style:"display"};r.push(G),c=ia()}a%2===0?r.push(c):r.shift(),r=[],n.push(r)}s.gullet.endGroup(),s.gullet.endGroup();var Z=new Array(n[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:n,arraystretch:1,addJot:!0,rowGaps:[null],cols:Z,colSeparationType:"CD",hLinesBeforeRow:new Array(n.length+1).fill([])}}Q({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=e.funcName;return{type:"cdlabel",mode:r.mode,side:n.slice(4),label:t[0]}},htmlBuilder:function(e,t){var r=t.havingStyle(t.style.sup()),n=E.wrapFragment(ye(e.label,r,t),t);return n.classes.push("cd-label-"+e.side),n.style.bottom=V(.8-n.depth),n.height=0,n.depth=0,n},mathmlBuilder:function(e,t){var r=new H.MathNode("mrow",[Be(e.label,t)]);return r=new H.MathNode("mpadded",[r]),r.setAttribute("width","0"),e.side==="left"&&r.setAttribute("lspace","-1width"),r.setAttribute("voffset","0.7em"),r=new H.MathNode("mstyle",[r]),r.setAttribute("displaystyle","false"),r.setAttribute("scriptlevel","1"),r}}),Q({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler:function(e,t){var r=e.parser;return{type:"cdlabelparent",mode:r.mode,fragment:t[0]}},htmlBuilder:function(e,t){var r=E.wrapFragment(ye(e.fragment,t),t);return r.classes.push("cd-vert-arrow"),r},mathmlBuilder:function(e,t){return new H.MathNode("mrow",[Be(e.fragment,t)])}}),Q({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){for(var r=e.parser,n=fe(t[0],"ordgroup"),a=n.body,l="",c=0;c<a.length;c++){var p=fe(a[c],"textord");l+=p.text}var b=parseInt(l),S;if(isNaN(b))throw new y("\\@char has non-numeric argument "+l);if(b<0||b>=1114111)throw new y("\\@char with invalid code point "+l);return b<=65535?S=String.fromCharCode(b):(b-=65536,S=String.fromCharCode((b>>10)+55296,(b&1023)+56320)),{type:"textord",mode:r.mode,text:S}}});var la=function(e,t){var r=Ge(e.body,t.withColor(e.color),!1);return E.makeFragment(r)},oa=function(e,t){var r=n0(e.body,t.withColor(e.color)),n=new H.MathNode("mstyle",r);return n.setAttribute("mathcolor",e.color),n};Q({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler:function(e,t){var r=e.parser,n=fe(t[0],"color-token").color,a=t[1];return{type:"color",mode:r.mode,color:n,body:Pe(a)}},htmlBuilder:la,mathmlBuilder:oa}),Q({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler:function(e,t){var r=e.parser,n=e.breakOnTokenText,a=fe(t[0],"color-token").color;r.gullet.macros.set("\\current@color",a);var l=r.parseExpression(!0,n);return{type:"color",mode:r.mode,color:a,body:l}},htmlBuilder:la,mathmlBuilder:oa}),Q({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler:function(e,t,r){var n=e.parser,a=n.gullet.future().text==="["?n.parseSizeGroup(!0):null,l=!n.settings.displayMode||!n.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:n.mode,newLine:l,size:a&&fe(a,"size").value}},htmlBuilder:function(e,t){var r=E.makeSpan(["mspace"],[],t);return e.newLine&&(r.classes.push("newline"),e.size&&(r.style.marginTop=V(ze(e.size,t)))),r},mathmlBuilder:function(e,t){var r=new H.MathNode("mspace");return e.newLine&&(r.setAttribute("linebreak","newline"),e.size&&r.setAttribute("height",V(ze(e.size,t)))),r}});var $r={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},ua=function(e){var t=e.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(t))throw new y("Expected a control sequence",e);return t},Ts=function(e){var t=e.gullet.popToken();return t.text==="="&&(t=e.gullet.popToken(),t.text===" "&&(t=e.gullet.popToken())),t},ha=function(e,t,r,n){var a=e.gullet.macros.get(r.text);a==null&&(r.noexpand=!0,a={tokens:[r],numArgs:0,unexpandable:!e.gullet.isExpandable(r.text)}),e.gullet.macros.set(t,a,n)};Q({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler:function(e){var t=e.parser,r=e.funcName;t.consumeSpaces();var n=t.fetch();if($r[n.text])return(r==="\\global"||r==="\\\\globallong")&&(n.text=$r[n.text]),fe(t.parseFunction(),"internal");throw new y("Invalid token after macro prefix",n)}}),Q({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler:function(e){var t=e.parser,r=e.funcName,n=t.gullet.popToken(),a=n.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(a))throw new y("Expected a control sequence",n);for(var l=0,c,p=[[]];t.gullet.future().text!=="{";)if(n=t.gullet.popToken(),n.text==="#"){if(t.gullet.future().text==="{"){c=t.gullet.future(),p[l].push("{");break}if(n=t.gullet.popToken(),!/^[1-9]$/.test(n.text))throw new y('Invalid argument number "'+n.text+'"');if(parseInt(n.text)!==l+1)throw new y('Argument number "'+n.text+'" out of order');l++,p.push([])}else{if(n.text==="EOF")throw new y("Expected a macro definition");p[l].push(n.text)}var b=t.gullet.consumeArg(),S=b.tokens;return c&&S.unshift(c),(r==="\\edef"||r==="\\xdef")&&(S=t.gullet.expandTokens(S),S.reverse()),t.gullet.macros.set(a,{tokens:S,numArgs:l,delimiters:p},r===$r[r]),{type:"internal",mode:t.mode}}}),Q({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler:function(e){var t=e.parser,r=e.funcName,n=ua(t.gullet.popToken());t.gullet.consumeSpaces();var a=Ts(t);return ha(t,n,a,r==="\\\\globallet"),{type:"internal",mode:t.mode}}}),Q({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler:function(e){var t=e.parser,r=e.funcName,n=ua(t.gullet.popToken()),a=t.gullet.popToken(),l=t.gullet.popToken();return ha(t,n,l,r==="\\\\globalfuture"),t.gullet.pushToken(l),t.gullet.pushToken(a),{type:"internal",mode:t.mode}}});var Ot=function(e,t,r){var n=Ce.math[e]&&Ce.math[e].replace,a=A0(n||e,t,r);if(!a)throw new Error("Unsupported symbol "+e+" and font size "+t+".");return a},Yr=function(e,t,r,n){var a=r.havingBaseStyle(t),l=E.makeSpan(n.concat(a.sizingClasses(r)),[e],r),c=a.sizeMultiplier/r.sizeMultiplier;return l.height*=c,l.depth*=c,l.maxFontSize=a.sizeMultiplier,l},ca=function(e,t,r){var n=t.havingBaseStyle(r),a=(1-t.sizeMultiplier/n.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=V(a),e.height-=a,e.depth+=a},Ms=function(e,t,r,n,a,l){var c=E.makeSymbol(e,"Main-Regular",a,n),p=Yr(c,t,n,l);return r&&ca(p,n,t),p},zs=function(e,t,r,n){return E.makeSymbol(e,"Size"+t+"-Regular",r,n)},ma=function(e,t,r,n,a,l){var c=zs(e,t,a,n),p=Yr(E.makeSpan(["delimsizing","size"+t],[c],n),K.TEXT,n,l);return r&&ca(p,n,K.TEXT),p},jr=function(e,t,r){var n;t==="Size1-Regular"?n="delim-size1":n="delim-size4";var a=E.makeSpan(["delimsizinginner",n],[E.makeSpan([],[E.makeSymbol(e,t,r)])]);return{type:"elem",elem:a}},Xr=function(e,t,r){var n=h0["Size4-Regular"][e.charCodeAt(0)]?h0["Size4-Regular"][e.charCodeAt(0)][4]:h0["Size1-Regular"][e.charCodeAt(0)][4],a=new T0("inner",dt(e,Math.round(1e3*t))),l=new m0([a],{width:V(n),height:V(t),style:"width:"+V(n),viewBox:"0 0 "+1e3*n+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),c=E.makeSvgSpan([],[l],r);return c.height=t,c.style.height=V(t),c.style.width=V(n),{type:"elem",elem:c}},Zr=.008,hr={type:"kern",size:-1*Zr},Es=["|","\\lvert","\\rvert","\\vert"],Bs=["\\|","\\lVert","\\rVert","\\Vert"],da=function(e,t,r,n,a,l){var c,p,b,S,B="",R=0;c=b=S=e,p=null;var N="Size1-Regular";e==="\\uparrow"?b=S="⏐":e==="\\Uparrow"?b=S="‖":e==="\\downarrow"?c=b="⏐":e==="\\Downarrow"?c=b="‖":e==="\\updownarrow"?(c="\\uparrow",b="⏐",S="\\downarrow"):e==="\\Updownarrow"?(c="\\Uparrow",b="‖",S="\\Downarrow"):Y.contains(Es,e)?(b="∣",B="vert",R=333):Y.contains(Bs,e)?(b="∥",B="doublevert",R=556):e==="["||e==="\\lbrack"?(c="⎡",b="⎢",S="⎣",N="Size4-Regular",B="lbrack",R=667):e==="]"||e==="\\rbrack"?(c="⎤",b="⎥",S="⎦",N="Size4-Regular",B="rbrack",R=667):e==="\\lfloor"||e==="⌊"?(b=c="⎢",S="⎣",N="Size4-Regular",B="lfloor",R=667):e==="\\lceil"||e==="⌈"?(c="⎡",b=S="⎢",N="Size4-Regular",B="lceil",R=667):e==="\\rfloor"||e==="⌋"?(b=c="⎥",S="⎦",N="Size4-Regular",B="rfloor",R=667):e==="\\rceil"||e==="⌉"?(c="⎤",b=S="⎥",N="Size4-Regular",B="rceil",R=667):e==="("||e==="\\lparen"?(c="⎛",b="⎜",S="⎝",N="Size4-Regular",B="lparen",R=875):e===")"||e==="\\rparen"?(c="⎞",b="⎟",S="⎠",N="Size4-Regular",B="rparen",R=875):e==="\\{"||e==="\\lbrace"?(c="⎧",p="⎨",S="⎩",b="⎪",N="Size4-Regular"):e==="\\}"||e==="\\rbrace"?(c="⎫",p="⎬",S="⎭",b="⎪",N="Size4-Regular"):e==="\\lgroup"||e==="⟮"?(c="⎧",S="⎩",b="⎪",N="Size4-Regular"):e==="\\rgroup"||e==="⟯"?(c="⎫",S="⎭",b="⎪",N="Size4-Regular"):e==="\\lmoustache"||e==="⎰"?(c="⎧",S="⎭",b="⎪",N="Size4-Regular"):(e==="\\rmoustache"||e==="⎱")&&(c="⎫",S="⎩",b="⎪",N="Size4-Regular");var L=Ot(c,N,a),G=L.height+L.depth,Z=Ot(b,N,a),re=Z.height+Z.depth,se=Ot(S,N,a),he=se.height+se.depth,we=0,pe=1;if(p!==null){var Te=Ot(p,N,a);we=Te.height+Te.depth,pe=2}var xe=G+he+we,Me=Math.max(0,Math.ceil((t-xe)/(pe*re))),_e=xe+Me*pe*re,Ye=n.fontMetrics().axisHeight;r&&(Ye*=n.sizeMultiplier);var s0=_e/2-Ye,Fe=[];if(B.length>0){var ot=_e-G-he,p0=Math.round(_e*1e3),Ke=Wt(B,Math.round(ot*1e3)),X0=new T0(B,Ke),kt=(R/1e3).toFixed(3)+"em",St=(p0/1e3).toFixed(3)+"em",gn=new m0([X0],{width:kt,height:St,viewBox:"0 0 "+R+" "+p0}),Z0=E.makeSvgSpan([],[gn],n);Z0.height=p0/1e3,Z0.style.width=kt,Z0.style.height=St,Fe.push({type:"elem",elem:Z0})}else{if(Fe.push(jr(S,N,a)),Fe.push(hr),p===null){var K0=_e-G-he+2*Zr;Fe.push(Xr(b,K0,n))}else{var g0=(_e-G-he-we)/2+2*Zr;Fe.push(Xr(b,g0,n)),Fe.push(hr),Fe.push(jr(p,N,a)),Fe.push(hr),Fe.push(Xr(b,g0,n))}Fe.push(hr),Fe.push(jr(c,N,a))}var Ht=n.havingBaseStyle(K.TEXT),vn=E.makeVList({positionType:"bottom",positionData:s0,children:Fe},Ht);return Yr(E.makeSpan(["delimsizing","mult"],[vn],Ht),K.TEXT,n,l)},Kr=80,Qr=.08,Jr=function(e,t,r,n,a){var l=L0(e,n,r),c=new T0(e,l),p=new m0([c],{width:"400em",height:V(t),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return E.makeSvgSpan(["hide-tail"],[p],a)},Cs=function(e,t){var r=t.havingBaseSizing(),n=va("\\surd",e*r.sizeMultiplier,ga,r),a=r.sizeMultiplier,l=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),c,p=0,b=0,S=0,B;return n.type==="small"?(S=1e3+1e3*l+Kr,e<1?a=1:e<1.4&&(a=.7),p=(1+l+Qr)/a,b=(1+l)/a,c=Jr("sqrtMain",p,S,l,t),c.style.minWidth="0.853em",B=.833/a):n.type==="large"?(S=(1e3+Kr)*qt[n.size],b=(qt[n.size]+l)/a,p=(qt[n.size]+l+Qr)/a,c=Jr("sqrtSize"+n.size,p,S,l,t),c.style.minWidth="1.02em",B=1/a):(p=e+l+Qr,b=e+l,S=Math.floor(1e3*e+l)+Kr,c=Jr("sqrtTall",p,S,l,t),c.style.minWidth="0.742em",B=1.056),c.height=b,c.style.height=V(p),{span:c,advanceWidth:B,ruleWidth:(t.fontMetrics().sqrtRuleThickness+l)*a}},fa=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],Ds=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],pa=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],qt=[0,1.2,1.8,2.4,3],_s=function(e,t,r,n,a){if(e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle"),Y.contains(fa,e)||Y.contains(pa,e))return ma(e,t,!1,r,n,a);if(Y.contains(Ds,e))return da(e,qt[t],!1,r,n,a);throw new y("Illegal delimiter: '"+e+"'")},Ns=[{type:"small",style:K.SCRIPTSCRIPT},{type:"small",style:K.SCRIPT},{type:"small",style:K.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],Rs=[{type:"small",style:K.SCRIPTSCRIPT},{type:"small",style:K.SCRIPT},{type:"small",style:K.TEXT},{type:"stack"}],ga=[{type:"small",style:K.SCRIPTSCRIPT},{type:"small",style:K.SCRIPT},{type:"small",style:K.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],Fs=function(e){if(e.type==="small")return"Main-Regular";if(e.type==="large")return"Size"+e.size+"-Regular";if(e.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+e.type+"' here.")},va=function(e,t,r,n){for(var a=Math.min(2,3-n.style.size),l=a;l<r.length&&r[l].type!=="stack";l++){var c=Ot(e,Fs(r[l]),"math"),p=c.height+c.depth;if(r[l].type==="small"){var b=n.havingBaseStyle(r[l].style);p*=b.sizeMultiplier}if(p>t)return r[l]}return r[r.length-1]},ba=function(e,t,r,n,a,l){e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle");var c;Y.contains(pa,e)?c=Ns:Y.contains(fa,e)?c=ga:c=Rs;var p=va(e,t,c,n);return p.type==="small"?Ms(e,p.style,r,n,a,l):p.type==="large"?ma(e,p.size,r,n,a,l):da(e,t,r,n,a,l)},Is=function(e,t,r,n,a,l){var c=n.fontMetrics().axisHeight*n.sizeMultiplier,p=901,b=5/n.fontMetrics().ptPerEm,S=Math.max(t-c,r+c),B=Math.max(S/500*p,2*S-b);return ba(e,B,!0,n,a,l)},R0={sqrtImage:Cs,sizedDelim:_s,sizeToMaxHeight:qt,customSizedDelim:ba,leftRightDelim:Is},ya={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},Ls=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function cr(s,e){var t=lr(s);if(t&&Y.contains(Ls,t.text))return t;throw t?new y("Invalid delimiter '"+t.text+"' after '"+e.funcName+"'",s):new y("Invalid delimiter type '"+s.type+"'",s)}Q({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:function(e,t){var r=cr(t[0],e);return{type:"delimsizing",mode:e.parser.mode,size:ya[e.funcName].size,mclass:ya[e.funcName].mclass,delim:r.text}},htmlBuilder:function(e,t){return e.delim==="."?E.makeSpan([e.mclass]):R0.sizedDelim(e.delim,e.size,t,e.mode,[e.mclass])},mathmlBuilder:function(e){var t=[];e.delim!=="."&&t.push(f0(e.delim,e.mode));var r=new H.MathNode("mo",t);e.mclass==="mopen"||e.mclass==="mclose"?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r.setAttribute("stretchy","true");var n=V(R0.sizeToMaxHeight[e.size]);return r.setAttribute("minsize",n),r.setAttribute("maxsize",n),r}});function xa(s){if(!s.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}Q({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:function(e,t){var r=e.parser.gullet.macros.get("\\current@color");if(r&&typeof r!="string")throw new y("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:e.parser.mode,delim:cr(t[0],e).text,color:r}}}),Q({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:function(e,t){var r=cr(t[0],e),n=e.parser;++n.leftrightDepth;var a=n.parseExpression(!1);--n.leftrightDepth,n.expect("\\right",!1);var l=fe(n.parseFunction(),"leftright-right");return{type:"leftright",mode:n.mode,body:a,left:r.text,right:l.delim,rightColor:l.color}},htmlBuilder:function(e,t){xa(e);for(var r=Ge(e.body,t,!0,["mopen","mclose"]),n=0,a=0,l=!1,c=0;c<r.length;c++)r[c].isMiddle?l=!0:(n=Math.max(r[c].height,n),a=Math.max(r[c].depth,a));n*=t.sizeMultiplier,a*=t.sizeMultiplier;var p;if(e.left==="."?p=It(t,["mopen"]):p=R0.leftRightDelim(e.left,n,a,t,e.mode,["mopen"]),r.unshift(p),l)for(var b=1;b<r.length;b++){var S=r[b],B=S.isMiddle;B&&(r[b]=R0.leftRightDelim(B.delim,n,a,B.options,e.mode,[]))}var R;if(e.right===".")R=It(t,["mclose"]);else{var N=e.rightColor?t.withColor(e.rightColor):t;R=R0.leftRightDelim(e.right,n,a,N,e.mode,["mclose"])}return r.push(R),E.makeSpan(["minner"],r,t)},mathmlBuilder:function(e,t){xa(e);var r=n0(e.body,t);if(e.left!=="."){var n=new H.MathNode("mo",[f0(e.left,e.mode)]);n.setAttribute("fence","true"),r.unshift(n)}if(e.right!=="."){var a=new H.MathNode("mo",[f0(e.right,e.mode)]);a.setAttribute("fence","true"),e.rightColor&&a.setAttribute("mathcolor",e.rightColor),r.push(a)}return Ur(r)}}),Q({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:function(e,t){var r=cr(t[0],e);if(!e.parser.leftrightDepth)throw new y("\\middle without preceding \\left",r);return{type:"middle",mode:e.parser.mode,delim:r.text}},htmlBuilder:function(e,t){var r;if(e.delim===".")r=It(t,[]);else{r=R0.sizedDelim(e.delim,1,t,e.mode,[]);var n={delim:e.delim,options:t};r.isMiddle=n}return r},mathmlBuilder:function(e,t){var r=e.delim==="\\vert"||e.delim==="|"?f0("|","text"):f0(e.delim,e.mode),n=new H.MathNode("mo",[r]);return n.setAttribute("fence","true"),n.setAttribute("lspace","0.05em"),n.setAttribute("rspace","0.05em"),n}});var en=function(e,t){var r=E.wrapFragment(ye(e.body,t),t),n=e.label.slice(1),a=t.sizeMultiplier,l,c=0,p=Y.isCharacterBox(e.body);if(n==="sout")l=E.makeSpan(["stretchy","sout"]),l.height=t.fontMetrics().defaultRuleThickness/a,c=-.5*t.fontMetrics().xHeight;else if(n==="phase"){var b=ze({number:.6,unit:"pt"},t),S=ze({number:.35,unit:"ex"},t),B=t.havingBaseSizing();a=a/B.sizeMultiplier;var R=r.height+r.depth+b+S;r.style.paddingLeft=V(R/2+b);var N=Math.floor(1e3*R*a),L=tt(N),G=new m0([new T0("phase",L)],{width:"400em",height:V(N/1e3),viewBox:"0 0 400000 "+N,preserveAspectRatio:"xMinYMin slice"});l=E.makeSvgSpan(["hide-tail"],[G],t),l.style.height=V(R),c=r.depth+b+S}else{/cancel/.test(n)?p||r.classes.push("cancel-pad"):n==="angl"?r.classes.push("anglpad"):r.classes.push("boxpad");var Z=0,re=0,se=0;/box/.test(n)?(se=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness),Z=t.fontMetrics().fboxsep+(n==="colorbox"?0:se),re=Z):n==="angl"?(se=Math.max(t.fontMetrics().defaultRuleThickness,t.minRuleThickness),Z=4*se,re=Math.max(0,.25-r.depth)):(Z=p?.2:0,re=Z),l=N0.encloseSpan(r,n,Z,re,t),/fbox|boxed|fcolorbox/.test(n)?(l.style.borderStyle="solid",l.style.borderWidth=V(se)):n==="angl"&&se!==.049&&(l.style.borderTopWidth=V(se),l.style.borderRightWidth=V(se)),c=r.depth+re,e.backgroundColor&&(l.style.backgroundColor=e.backgroundColor,e.borderColor&&(l.style.borderColor=e.borderColor))}var he;if(e.backgroundColor)he=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:l,shift:c},{type:"elem",elem:r,shift:0}]},t);else{var we=/cancel|phase/.test(n)?["svg-align"]:[];he=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:0},{type:"elem",elem:l,shift:c,wrapperClasses:we}]},t)}return/cancel/.test(n)&&(he.height=r.height,he.depth=r.depth),/cancel/.test(n)&&!p?E.makeSpan(["mord","cancel-lap"],[he],t):E.makeSpan(["mord"],[he],t)},tn=function(e,t){var r=0,n=new H.MathNode(e.label.indexOf("colorbox")>-1?"mpadded":"menclose",[Be(e.body,t)]);switch(e.label){case"\\cancel":n.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":n.setAttribute("notation","downdiagonalstrike");break;case"\\phase":n.setAttribute("notation","phasorangle");break;case"\\sout":n.setAttribute("notation","horizontalstrike");break;case"\\fbox":n.setAttribute("notation","box");break;case"\\angl":n.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(r=t.fontMetrics().fboxsep*t.fontMetrics().ptPerEm,n.setAttribute("width","+"+2*r+"pt"),n.setAttribute("height","+"+2*r+"pt"),n.setAttribute("lspace",r+"pt"),n.setAttribute("voffset",r+"pt"),e.label==="\\fcolorbox"){var a=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness);n.setAttribute("style","border: "+a+"em solid "+String(e.borderColor))}break;case"\\xcancel":n.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return e.backgroundColor&&n.setAttribute("mathbackground",e.backgroundColor),n};Q({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler:function(e,t,r){var n=e.parser,a=e.funcName,l=fe(t[0],"color-token").color,c=t[1];return{type:"enclose",mode:n.mode,label:a,backgroundColor:l,body:c}},htmlBuilder:en,mathmlBuilder:tn}),Q({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler:function(e,t,r){var n=e.parser,a=e.funcName,l=fe(t[0],"color-token").color,c=fe(t[1],"color-token").color,p=t[2];return{type:"enclose",mode:n.mode,label:a,backgroundColor:c,borderColor:l,body:p}},htmlBuilder:en,mathmlBuilder:tn}),Q({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler:function(e,t){var r=e.parser;return{type:"enclose",mode:r.mode,label:"\\fbox",body:t[0]}}}),Q({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=e.funcName,a=t[0];return{type:"enclose",mode:r.mode,label:n,body:a}},htmlBuilder:en,mathmlBuilder:tn}),Q({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler:function(e,t){var r=e.parser;return{type:"enclose",mode:r.mode,label:"\\angl",body:t[0]}}});var wa={};function M0(s){for(var e=s.type,t=s.names,r=s.props,n=s.handler,a=s.htmlBuilder,l=s.mathmlBuilder,c={type:e,numArgs:r.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:n},p=0;p<t.length;++p)wa[t[p]]=c;a&&(nr[e]=a),l&&(ar[e]=l)}var ka={};function g(s,e){ka[s]=e}var x0=function(){function s(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}return s.range=function(t,r){return r?!t||!t.loc||!r.loc||t.loc.lexer!==r.loc.lexer?null:new s(t.loc.lexer,t.loc.start,r.loc.end):t&&t.loc},s}(),$0=function(){function s(t,r){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=t,this.loc=r}var e=s.prototype;return e.range=function(r,n){return new s(n,x0.range(this,r))},s}();function Sa(s){var e=[];s.consumeSpaces();var t=s.fetch().text;for(t==="\\relax"&&(s.consume(),s.consumeSpaces(),t=s.fetch().text);t==="\\hline"||t==="\\hdashline";)s.consume(),e.push(t==="\\hdashline"),s.consumeSpaces(),t=s.fetch().text;return e}var mr=function(e){var t=e.parser.settings;if(!t.displayMode)throw new y("{"+e.envName+"} can be used only in display mode.")};function rn(s){if(s.indexOf("ed")===-1)return s.indexOf("*")===-1}function Y0(s,e,t){var r=e.hskipBeforeAndAfter,n=e.addJot,a=e.cols,l=e.arraystretch,c=e.colSeparationType,p=e.autoTag,b=e.singleRow,S=e.emptySingleRow,B=e.maxNumCols,R=e.leqno;if(s.gullet.beginGroup(),b||s.gullet.macros.set("\\cr","\\\\\\relax"),!l){var N=s.gullet.expandMacroAsText("\\arraystretch");if(N==null)l=1;else if(l=parseFloat(N),!l||l<0)throw new y("Invalid \\arraystretch: "+N)}s.gullet.beginGroup();var L=[],G=[L],Z=[],re=[],se=p!=null?[]:void 0;function he(){p&&s.gullet.macros.set("\\@eqnsw","1",!0)}function we(){se&&(s.gullet.macros.get("\\df@tag")?(se.push(s.subparse([new $0("\\df@tag")])),s.gullet.macros.set("\\df@tag",void 0,!0)):se.push(!!p&&s.gullet.macros.get("\\@eqnsw")==="1"))}for(he(),re.push(Sa(s));;){var pe=s.parseExpression(!1,b?"\\end":"\\\\");s.gullet.endGroup(),s.gullet.beginGroup(),pe={type:"ordgroup",mode:s.mode,body:pe},t&&(pe={type:"styling",mode:s.mode,style:t,body:[pe]}),L.push(pe);var Te=s.fetch().text;if(Te==="&"){if(B&&L.length===B){if(b||c)throw new y("Too many tab characters: &",s.nextToken);s.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}s.consume()}else if(Te==="\\end"){we(),L.length===1&&pe.type==="styling"&&pe.body[0].body.length===0&&(G.length>1||!S)&&G.pop(),re.length<G.length+1&&re.push([]);break}else if(Te==="\\\\"){s.consume();var xe=void 0;s.gullet.future().text!==" "&&(xe=s.parseSizeGroup(!0)),Z.push(xe?xe.value:null),we(),re.push(Sa(s)),L=[],G.push(L),he()}else throw new y("Expected & or \\\\ or \\cr or \\end",s.nextToken)}return s.gullet.endGroup(),s.gullet.endGroup(),{type:"array",mode:s.mode,addJot:n,arraystretch:l,body:G,cols:a,rowGaps:Z,hskipBeforeAndAfter:r,hLinesBeforeRow:re,colSeparationType:c,tags:se,leqno:R}}function nn(s){return s.slice(0,1)==="d"?"display":"text"}var z0=function(e,t){var r,n,a=e.body.length,l=e.hLinesBeforeRow,c=0,p=new Array(a),b=[],S=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),B=1/t.fontMetrics().ptPerEm,R=5*B;if(e.colSeparationType&&e.colSeparationType==="small"){var N=t.havingStyle(K.SCRIPT).sizeMultiplier;R=.2778*(N/t.sizeMultiplier)}var L=e.colSeparationType==="CD"?ze({number:3,unit:"ex"},t):12*B,G=3*B,Z=e.arraystretch*L,re=.7*Z,se=.3*Z,he=0;function we(gr){for(var vr=0;vr<gr.length;++vr)vr>0&&(he+=.25),b.push({pos:he,isDashed:gr[vr]})}for(we(l[0]),r=0;r<e.body.length;++r){var pe=e.body[r],Te=re,xe=se;c<pe.length&&(c=pe.length);var Me=new Array(pe.length);for(n=0;n<pe.length;++n){var _e=ye(pe[n],t);xe<_e.depth&&(xe=_e.depth),Te<_e.height&&(Te=_e.height),Me[n]=_e}var Ye=e.rowGaps[r],s0=0;Ye&&(s0=ze(Ye,t),s0>0&&(s0+=se,xe<s0&&(xe=s0),s0=0)),e.addJot&&(xe+=G),Me.height=Te,Me.depth=xe,he+=Te,Me.pos=he,he+=xe+s0,p[r]=Me,we(l[r+1])}var Fe=he/2+t.fontMetrics().axisHeight,ot=e.cols||[],p0=[],Ke,X0,kt=[];if(e.tags&&e.tags.some(function(gr){return gr}))for(r=0;r<a;++r){var St=p[r],gn=St.pos-Fe,Z0=e.tags[r],K0=void 0;Z0===!0?K0=E.makeSpan(["eqn-num"],[],t):Z0===!1?K0=E.makeSpan([],[],t):K0=E.makeSpan([],Ge(Z0,t,!0),t),K0.depth=St.depth,K0.height=St.height,kt.push({type:"elem",elem:K0,shift:gn})}for(n=0,X0=0;n<c||X0<ot.length;++n,++X0){for(var g0=ot[X0]||{},Ht=!0;g0.type==="separator";){if(Ht||(Ke=E.makeSpan(["arraycolsep"],[]),Ke.style.width=V(t.fontMetrics().doubleRuleSep),p0.push(Ke)),g0.separator==="|"||g0.separator===":"){var vn=g0.separator==="|"?"solid":"dashed",At=E.makeSpan(["vertical-separator"],[],t);At.style.height=V(he),At.style.borderRightWidth=V(S),At.style.borderRightStyle=vn,At.style.margin="0 "+V(-S/2);var ai=he-Fe;ai&&(At.style.verticalAlign=V(-ai)),p0.push(At)}else throw new y("Invalid separator type: "+g0.separator);X0++,g0=ot[X0]||{},Ht=!1}if(!(n>=c)){var Tt=void 0;(n>0||e.hskipBeforeAndAfter)&&(Tt=Y.deflt(g0.pregap,R),Tt!==0&&(Ke=E.makeSpan(["arraycolsep"],[]),Ke.style.width=V(Tt),p0.push(Ke)));var Mt=[];for(r=0;r<a;++r){var fr=p[r],pr=fr[n];if(pr){var hl=fr.pos-Fe;pr.depth=fr.depth,pr.height=fr.height,Mt.push({type:"elem",elem:pr,shift:hl})}}Mt=E.makeVList({positionType:"individualShift",children:Mt},t),Mt=E.makeSpan(["col-align-"+(g0.align||"c")],[Mt]),p0.push(Mt),(n<c-1||e.hskipBeforeAndAfter)&&(Tt=Y.deflt(g0.postgap,R),Tt!==0&&(Ke=E.makeSpan(["arraycolsep"],[]),Ke.style.width=V(Tt),p0.push(Ke)))}}if(p=E.makeSpan(["mtable"],p0),b.length>0){for(var cl=E.makeLineSpan("hline",t,S),ml=E.makeLineSpan("hdashline",t,S),bn=[{type:"elem",elem:p,shift:0}];b.length>0;){var ii=b.pop(),si=ii.pos-Fe;ii.isDashed?bn.push({type:"elem",elem:ml,shift:si}):bn.push({type:"elem",elem:cl,shift:si})}p=E.makeVList({positionType:"individualShift",children:bn},t)}if(kt.length===0)return E.makeSpan(["mord"],[p],t);var yn=E.makeVList({positionType:"individualShift",children:kt},t);return yn=E.makeSpan(["tag"],[yn],t),E.makeFragment([p,yn])},Os={c:"center ",l:"left ",r:"right "},E0=function(e,t){for(var r=[],n=new H.MathNode("mtd",[],["mtr-glue"]),a=new H.MathNode("mtd",[],["mml-eqn-num"]),l=0;l<e.body.length;l++){for(var c=e.body[l],p=[],b=0;b<c.length;b++)p.push(new H.MathNode("mtd",[Be(c[b],t)]));e.tags&&e.tags[l]&&(p.unshift(n),p.push(n),e.leqno?p.unshift(a):p.push(a)),r.push(new H.MathNode("mtr",p))}var S=new H.MathNode("mtable",r),B=e.arraystretch===.5?.1:.16+e.arraystretch-1+(e.addJot?.09:0);S.setAttribute("rowspacing",V(B));var R="",N="";if(e.cols&&e.cols.length>0){var L=e.cols,G="",Z=!1,re=0,se=L.length;L[0].type==="separator"&&(R+="top ",re=1),L[L.length-1].type==="separator"&&(R+="bottom ",se-=1);for(var he=re;he<se;he++)L[he].type==="align"?(N+=Os[L[he].align],Z&&(G+="none "),Z=!0):L[he].type==="separator"&&Z&&(G+=L[he].separator==="|"?"solid ":"dashed ",Z=!1);S.setAttribute("columnalign",N.trim()),/[sd]/.test(G)&&S.setAttribute("columnlines",G.trim())}if(e.colSeparationType==="align"){for(var we=e.cols||[],pe="",Te=1;Te<we.length;Te++)pe+=Te%2?"0em ":"1em ";S.setAttribute("columnspacing",pe.trim())}else e.colSeparationType==="alignat"||e.colSeparationType==="gather"?S.setAttribute("columnspacing","0em"):e.colSeparationType==="small"?S.setAttribute("columnspacing","0.2778em"):e.colSeparationType==="CD"?S.setAttribute("columnspacing","0.5em"):S.setAttribute("columnspacing","1em");var xe="",Me=e.hLinesBeforeRow;R+=Me[0].length>0?"left ":"",R+=Me[Me.length-1].length>0?"right ":"";for(var _e=1;_e<Me.length-1;_e++)xe+=Me[_e].length===0?"none ":Me[_e][0]?"dashed ":"solid ";return/[sd]/.test(xe)&&S.setAttribute("rowlines",xe.trim()),R!==""&&(S=new H.MathNode("menclose",[S]),S.setAttribute("notation",R.trim())),e.arraystretch&&e.arraystretch<1&&(S=new H.MathNode("mstyle",[S]),S.setAttribute("scriptlevel","1")),S},Aa=function(e,t){e.envName.indexOf("ed")===-1&&mr(e);var r=[],n=e.envName.indexOf("at")>-1?"alignat":"align",a=e.envName==="split",l=Y0(e.parser,{cols:r,addJot:!0,autoTag:a?void 0:rn(e.envName),emptySingleRow:!0,colSeparationType:n,maxNumCols:a?2:void 0,leqno:e.parser.settings.leqno},"display"),c,p=0,b={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&t[0].type==="ordgroup"){for(var S="",B=0;B<t[0].body.length;B++){var R=fe(t[0].body[B],"textord");S+=R.text}c=Number(S),p=c*2}var N=!p;l.body.forEach(function(re){for(var se=1;se<re.length;se+=2){var he=fe(re[se],"styling"),we=fe(he.body[0],"ordgroup");we.body.unshift(b)}if(N)p<re.length&&(p=re.length);else{var pe=re.length/2;if(c<pe)throw new y("Too many math in a row: "+("expected "+c+", but got "+pe),re[0])}});for(var L=0;L<p;++L){var G="r",Z=0;L%2===1?G="l":L>0&&N&&(Z=1),r[L]={type:"align",align:G,pregap:Z,postgap:0}}return l.colSeparationType=N?"align":"alignat",l};M0({type:"array",names:["array","darray"],props:{numArgs:1},handler:function(e,t){var r=lr(t[0]),n=r?[t[0]]:fe(t[0],"ordgroup").body,a=n.map(function(c){var p=Vr(c),b=p.text;if("lcr".indexOf(b)!==-1)return{type:"align",align:b};if(b==="|")return{type:"separator",separator:"|"};if(b===":")return{type:"separator",separator:":"};throw new y("Unknown column alignment: "+b,c)}),l={cols:a,hskipBeforeAndAfter:!0,maxNumCols:a.length};return Y0(e.parser,l,nn(e.envName))},htmlBuilder:z0,mathmlBuilder:E0}),M0({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler:function(e){var t={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[e.envName.replace("*","")],r="c",n={hskipBeforeAndAfter:!1,cols:[{type:"align",align:r}]};if(e.envName.charAt(e.envName.length-1)==="*"){var a=e.parser;if(a.consumeSpaces(),a.fetch().text==="["){if(a.consume(),a.consumeSpaces(),r=a.fetch().text,"lcr".indexOf(r)===-1)throw new y("Expected l or c or r",a.nextToken);a.consume(),a.consumeSpaces(),a.expect("]"),a.consume(),n.cols=[{type:"align",align:r}]}}var l=Y0(e.parser,n,nn(e.envName)),c=Math.max.apply(Math,[0].concat(l.body.map(function(p){return p.length})));return l.cols=new Array(c).fill({type:"align",align:r}),t?{type:"leftright",mode:e.mode,body:[l],left:t[0],right:t[1],rightColor:void 0}:l},htmlBuilder:z0,mathmlBuilder:E0}),M0({type:"array",names:["smallmatrix"],props:{numArgs:0},handler:function(e){var t={arraystretch:.5},r=Y0(e.parser,t,"script");return r.colSeparationType="small",r},htmlBuilder:z0,mathmlBuilder:E0}),M0({type:"array",names:["subarray"],props:{numArgs:1},handler:function(e,t){var r=lr(t[0]),n=r?[t[0]]:fe(t[0],"ordgroup").body,a=n.map(function(c){var p=Vr(c),b=p.text;if("lc".indexOf(b)!==-1)return{type:"align",align:b};throw new y("Unknown column alignment: "+b,c)});if(a.length>1)throw new y("{subarray} can contain only one column");var l={cols:a,hskipBeforeAndAfter:!1,arraystretch:.5};if(l=Y0(e.parser,l,"script"),l.body.length>0&&l.body[0].length>1)throw new y("{subarray} can contain only one column");return l},htmlBuilder:z0,mathmlBuilder:E0}),M0({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler:function(e){var t={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},r=Y0(e.parser,t,nn(e.envName));return{type:"leftright",mode:e.mode,body:[r],left:e.envName.indexOf("r")>-1?".":"\\{",right:e.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:z0,mathmlBuilder:E0}),M0({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:Aa,htmlBuilder:z0,mathmlBuilder:E0}),M0({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler:function(e){Y.contains(["gather","gather*"],e.envName)&&mr(e);var t={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:rn(e.envName),emptySingleRow:!0,leqno:e.parser.settings.leqno};return Y0(e.parser,t,"display")},htmlBuilder:z0,mathmlBuilder:E0}),M0({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:Aa,htmlBuilder:z0,mathmlBuilder:E0}),M0({type:"array",names:["equation","equation*"],props:{numArgs:0},handler:function(e){mr(e);var t={autoTag:rn(e.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:e.parser.settings.leqno};return Y0(e.parser,t,"display")},htmlBuilder:z0,mathmlBuilder:E0}),M0({type:"array",names:["CD"],props:{numArgs:0},handler:function(e){return mr(e),As(e.parser)},htmlBuilder:z0,mathmlBuilder:E0}),g("\\nonumber","\\gdef\\@eqnsw{0}"),g("\\notag","\\nonumber"),Q({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler:function(e,t){throw new y(e.funcName+" valid only within array environment")}});var qs=wa,Ta=qs;Q({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler:function(e,t){var r=e.parser,n=e.funcName,a=t[0];if(a.type!=="ordgroup")throw new y("Invalid environment name",a);for(var l="",c=0;c<a.body.length;++c)l+=fe(a.body[c],"textord").text;if(n==="\\begin"){if(!Ta.hasOwnProperty(l))throw new y("No such environment: "+l,a);var p=Ta[l],b=r.parseArguments("\\begin{"+l+"}",p),S=b.args,B=b.optArgs,R={mode:r.mode,envName:l,parser:r},N=p.handler(R,S,B);r.expect("\\end",!1);var L=r.nextToken,G=fe(r.parseFunction(),"environment");if(G.name!==l)throw new y("Mismatch: \\begin{"+l+"} matched by \\end{"+G.name+"}",L);return N}return{type:"environment",mode:r.mode,name:l,nameGroup:a}}});var Ma=function(e,t){var r=e.font,n=t.withFont(r);return ye(e.body,n)},za=function(e,t){var r=e.font,n=t.withFont(r);return Be(e.body,n)},Ea={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};Q({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:function(e,t){var r=e.parser,n=e.funcName,a=ir(t[0]),l=n;return l in Ea&&(l=Ea[l]),{type:"font",mode:r.mode,font:l.slice(1),body:a}},htmlBuilder:Ma,mathmlBuilder:za}),Q({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=t[0],a=Y.isCharacterBox(n);return{type:"mclass",mode:r.mode,mclass:ur(n),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:n}],isCharacterBox:a}}}),Q({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:function(e,t){var r=e.parser,n=e.funcName,a=e.breakOnTokenText,l=r.mode,c=r.parseExpression(!0,a),p="math"+n.slice(1);return{type:"font",mode:l,font:p,body:{type:"ordgroup",mode:r.mode,body:c}}},htmlBuilder:Ma,mathmlBuilder:za});var Ba=function(e,t){var r=t;return e==="display"?r=r.id>=K.SCRIPT.id?r.text():K.DISPLAY:e==="text"&&r.size===K.DISPLAY.size?r=K.TEXT:e==="script"?r=K.SCRIPT:e==="scriptscript"&&(r=K.SCRIPTSCRIPT),r},an=function(e,t){var r=Ba(e.size,t.style),n=r.fracNum(),a=r.fracDen(),l;l=t.havingStyle(n);var c=ye(e.numer,l,t);if(e.continued){var p=8.5/t.fontMetrics().ptPerEm,b=3.5/t.fontMetrics().ptPerEm;c.height=c.height<p?p:c.height,c.depth=c.depth<b?b:c.depth}l=t.havingStyle(a);var S=ye(e.denom,l,t),B,R,N;e.hasBarLine?(e.barSize?(R=ze(e.barSize,t),B=E.makeLineSpan("frac-line",t,R)):B=E.makeLineSpan("frac-line",t),R=B.height,N=B.height):(B=null,R=0,N=t.fontMetrics().defaultRuleThickness);var L,G,Z;r.size===K.DISPLAY.size||e.size==="display"?(L=t.fontMetrics().num1,R>0?G=3*N:G=7*N,Z=t.fontMetrics().denom1):(R>0?(L=t.fontMetrics().num2,G=N):(L=t.fontMetrics().num3,G=3*N),Z=t.fontMetrics().denom2);var re;if(B){var he=t.fontMetrics().axisHeight;L-c.depth-(he+.5*R)<G&&(L+=G-(L-c.depth-(he+.5*R))),he-.5*R-(S.height-Z)<G&&(Z+=G-(he-.5*R-(S.height-Z)));var we=-(he-.5*R);re=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:S,shift:Z},{type:"elem",elem:B,shift:we},{type:"elem",elem:c,shift:-L}]},t)}else{var se=L-c.depth-(S.height-Z);se<G&&(L+=.5*(G-se),Z+=.5*(G-se)),re=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:S,shift:Z},{type:"elem",elem:c,shift:-L}]},t)}l=t.havingStyle(r),re.height*=l.sizeMultiplier/t.sizeMultiplier,re.depth*=l.sizeMultiplier/t.sizeMultiplier;var pe;r.size===K.DISPLAY.size?pe=t.fontMetrics().delim1:r.size===K.SCRIPTSCRIPT.size?pe=t.havingStyle(K.SCRIPT).fontMetrics().delim2:pe=t.fontMetrics().delim2;var Te,xe;return e.leftDelim==null?Te=It(t,["mopen"]):Te=R0.customSizedDelim(e.leftDelim,pe,!0,t.havingStyle(r),e.mode,["mopen"]),e.continued?xe=E.makeSpan([]):e.rightDelim==null?xe=It(t,["mclose"]):xe=R0.customSizedDelim(e.rightDelim,pe,!0,t.havingStyle(r),e.mode,["mclose"]),E.makeSpan(["mord"].concat(l.sizingClasses(t)),[Te,E.makeSpan(["mfrac"],[re]),xe],t)},sn=function(e,t){var r=new H.MathNode("mfrac",[Be(e.numer,t),Be(e.denom,t)]);if(!e.hasBarLine)r.setAttribute("linethickness","0px");else if(e.barSize){var n=ze(e.barSize,t);r.setAttribute("linethickness",V(n))}var a=Ba(e.size,t.style);if(a.size!==t.style.size){r=new H.MathNode("mstyle",[r]);var l=a.size===K.DISPLAY.size?"true":"false";r.setAttribute("displaystyle",l),r.setAttribute("scriptlevel","0")}if(e.leftDelim!=null||e.rightDelim!=null){var c=[];if(e.leftDelim!=null){var p=new H.MathNode("mo",[new H.TextNode(e.leftDelim.replace("\\",""))]);p.setAttribute("fence","true"),c.push(p)}if(c.push(r),e.rightDelim!=null){var b=new H.MathNode("mo",[new H.TextNode(e.rightDelim.replace("\\",""))]);b.setAttribute("fence","true"),c.push(b)}return Ur(c)}return r};Q({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:function(e,t){var r=e.parser,n=e.funcName,a=t[0],l=t[1],c,p=null,b=null,S="auto";switch(n){case"\\dfrac":case"\\frac":case"\\tfrac":c=!0;break;case"\\\\atopfrac":c=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":c=!1,p="(",b=")";break;case"\\\\bracefrac":c=!1,p="\\{",b="\\}";break;case"\\\\brackfrac":c=!1,p="[",b="]";break;default:throw new Error("Unrecognized genfrac command")}switch(n){case"\\dfrac":case"\\dbinom":S="display";break;case"\\tfrac":case"\\tbinom":S="text";break}return{type:"genfrac",mode:r.mode,continued:!1,numer:a,denom:l,hasBarLine:c,leftDelim:p,rightDelim:b,size:S,barSize:null}},htmlBuilder:an,mathmlBuilder:sn}),Q({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:function(e,t){var r=e.parser;e.funcName;var n=t[0],a=t[1];return{type:"genfrac",mode:r.mode,continued:!0,numer:n,denom:a,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),Q({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler:function(e){var t=e.parser,r=e.funcName,n=e.token,a;switch(r){case"\\over":a="\\frac";break;case"\\choose":a="\\binom";break;case"\\atop":a="\\\\atopfrac";break;case"\\brace":a="\\\\bracefrac";break;case"\\brack":a="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:t.mode,replaceWith:a,token:n}}});var Ca=["display","text","script","scriptscript"],Da=function(e){var t=null;return e.length>0&&(t=e,t=t==="."?null:t),t};Q({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler:function(e,t){var r=e.parser,n=t[4],a=t[5],l=ir(t[0]),c=l.type==="atom"&&l.family==="open"?Da(l.text):null,p=ir(t[1]),b=p.type==="atom"&&p.family==="close"?Da(p.text):null,S=fe(t[2],"size"),B,R=null;S.isBlank?B=!0:(R=S.value,B=R.number>0);var N="auto",L=t[3];if(L.type==="ordgroup"){if(L.body.length>0){var G=fe(L.body[0],"textord");N=Ca[Number(G.text)]}}else L=fe(L,"textord"),N=Ca[Number(L.text)];return{type:"genfrac",mode:r.mode,numer:n,denom:a,continued:!1,hasBarLine:B,barSize:R,leftDelim:c,rightDelim:b,size:N}},htmlBuilder:an,mathmlBuilder:sn}),Q({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler:function(e,t){var r=e.parser;e.funcName;var n=e.token;return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:fe(t[0],"size").value,token:n}}}),Q({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:function(e,t){var r=e.parser;e.funcName;var n=t[0],a=le(fe(t[1],"infix").size),l=t[2],c=a.number>0;return{type:"genfrac",mode:r.mode,numer:n,denom:l,continued:!1,hasBarLine:c,barSize:a,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:an,mathmlBuilder:sn});var _a=function(e,t){var r=t.style,n,a;e.type==="supsub"?(n=e.sup?ye(e.sup,t.havingStyle(r.sup()),t):ye(e.sub,t.havingStyle(r.sub()),t),a=fe(e.base,"horizBrace")):a=fe(e,"horizBrace");var l=ye(a.base,t.havingBaseStyle(K.DISPLAY)),c=N0.svgSpan(a,t),p;if(a.isOver?(p=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:.1},{type:"elem",elem:c}]},t),p.children[0].children[0].children[1].classes.push("svg-align")):(p=E.makeVList({positionType:"bottom",positionData:l.depth+.1+c.height,children:[{type:"elem",elem:c},{type:"kern",size:.1},{type:"elem",elem:l}]},t),p.children[0].children[0].children[0].classes.push("svg-align")),n){var b=E.makeSpan(["mord",a.isOver?"mover":"munder"],[p],t);a.isOver?p=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:b},{type:"kern",size:.2},{type:"elem",elem:n}]},t):p=E.makeVList({positionType:"bottom",positionData:b.depth+.2+n.height+n.depth,children:[{type:"elem",elem:n},{type:"kern",size:.2},{type:"elem",elem:b}]},t)}return E.makeSpan(["mord",a.isOver?"mover":"munder"],[p],t)},Ps=function(e,t){var r=N0.mathMLnode(e.label);return new H.MathNode(e.isOver?"mover":"munder",[Be(e.base,t),r])};Q({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=e.funcName;return{type:"horizBrace",mode:r.mode,label:n,isOver:/^\\over/.test(n),base:t[0]}},htmlBuilder:_a,mathmlBuilder:Ps}),Q({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:function(e,t){var r=e.parser,n=t[1],a=fe(t[0],"url").url;return r.settings.isTrusted({command:"\\href",url:a})?{type:"href",mode:r.mode,href:a,body:Pe(n)}:r.formatUnsupportedCmd("\\href")},htmlBuilder:function(e,t){var r=Ge(e.body,t,!1);return E.makeAnchor(e.href,[],r,t)},mathmlBuilder:function(e,t){var r=W0(e.body,t);return r instanceof d0||(r=new d0("mrow",[r])),r.setAttribute("href",e.href),r}}),Q({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:function(e,t){var r=e.parser,n=fe(t[0],"url").url;if(!r.settings.isTrusted({command:"\\url",url:n}))return r.formatUnsupportedCmd("\\url");for(var a=[],l=0;l<n.length;l++){var c=n[l];c==="~"&&(c="\\textasciitilde"),a.push({type:"textord",mode:"text",text:c})}var p={type:"text",mode:r.mode,font:"\\texttt",body:a};return{type:"href",mode:r.mode,href:n,body:Pe(p)}}}),Q({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler:function(e,t){var r=e.parser;return{type:"hbox",mode:r.mode,body:Pe(t[0])}},htmlBuilder:function(e,t){var r=Ge(e.body,t,!1);return E.makeFragment(r)},mathmlBuilder:function(e,t){return new H.MathNode("mrow",n0(e.body,t))}}),Q({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:function(e,t){var r=e.parser,n=e.funcName;e.token;var a=fe(t[0],"raw").string,l=t[1];r.settings.strict&&r.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var c,p={};switch(n){case"\\htmlClass":p.class=a,c={command:"\\htmlClass",class:a};break;case"\\htmlId":p.id=a,c={command:"\\htmlId",id:a};break;case"\\htmlStyle":p.style=a,c={command:"\\htmlStyle",style:a};break;case"\\htmlData":{for(var b=a.split(","),S=0;S<b.length;S++){var B=b[S].split("=");if(B.length!==2)throw new y("Error parsing key-value for \\htmlData");p["data-"+B[0].trim()]=B[1].trim()}c={command:"\\htmlData",attributes:p};break}default:throw new Error("Unrecognized html command")}return r.settings.isTrusted(c)?{type:"html",mode:r.mode,attributes:p,body:Pe(l)}:r.formatUnsupportedCmd(n)},htmlBuilder:function(e,t){var r=Ge(e.body,t,!1),n=["enclosing"];e.attributes.class&&n.push.apply(n,e.attributes.class.trim().split(/\s+/));var a=E.makeSpan(n,r,t);for(var l in e.attributes)l!=="class"&&e.attributes.hasOwnProperty(l)&&a.setAttribute(l,e.attributes[l]);return a},mathmlBuilder:function(e,t){return W0(e.body,t)}}),Q({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:function(e,t){var r=e.parser;return{type:"htmlmathml",mode:r.mode,html:Pe(t[0]),mathml:Pe(t[1])}},htmlBuilder:function(e,t){var r=Ge(e.html,t,!1);return E.makeFragment(r)},mathmlBuilder:function(e,t){return W0(e.mathml,t)}});var ln=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new y("Invalid size: '"+e+"' in \\includegraphics");var r={number:+(t[1]+t[2]),unit:t[3]};if(!vt(r))throw new y("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r};Q({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:function(e,t,r){var n=e.parser,a={number:0,unit:"em"},l={number:.9,unit:"em"},c={number:0,unit:"em"},p="";if(r[0])for(var b=fe(r[0],"raw").string,S=b.split(","),B=0;B<S.length;B++){var R=S[B].split("=");if(R.length===2){var N=R[1].trim();switch(R[0].trim()){case"alt":p=N;break;case"width":a=ln(N);break;case"height":l=ln(N);break;case"totalheight":c=ln(N);break;default:throw new y("Invalid key: '"+R[0]+"' in \\includegraphics.")}}}var L=fe(t[0],"url").url;return p===""&&(p=L,p=p.replace(/^.*[\\/]/,""),p=p.substring(0,p.lastIndexOf("."))),n.settings.isTrusted({command:"\\includegraphics",url:L})?{type:"includegraphics",mode:n.mode,alt:p,width:a,height:l,totalheight:c,src:L}:n.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:function(e,t){var r=ze(e.height,t),n=0;e.totalheight.number>0&&(n=ze(e.totalheight,t)-r);var a=0;e.width.number>0&&(a=ze(e.width,t));var l={height:V(r+n)};a>0&&(l.width=V(a)),n>0&&(l.verticalAlign=V(-n));var c=new Zt(e.src,e.alt,l);return c.height=r,c.depth=n,c},mathmlBuilder:function(e,t){var r=new H.MathNode("mglyph",[]);r.setAttribute("alt",e.alt);var n=ze(e.height,t),a=0;if(e.totalheight.number>0&&(a=ze(e.totalheight,t)-n,r.setAttribute("valign",V(-a))),r.setAttribute("height",V(n+a)),e.width.number>0){var l=ze(e.width,t);r.setAttribute("width",V(l))}return r.setAttribute("src",e.src),r}}),Q({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler:function(e,t){var r=e.parser,n=e.funcName,a=fe(t[0],"size");if(r.settings.strict){var l=n[1]==="m",c=a.value.unit==="mu";l?(c||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" supports only mu units, "+("not "+a.value.unit+" units")),r.mode!=="math"&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" works only in math mode")):c&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:a.value}},htmlBuilder:function(e,t){return E.makeGlue(e.dimension,t)},mathmlBuilder:function(e,t){var r=ze(e.dimension,t);return new H.SpaceNode(r)}}),Q({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser,n=e.funcName,a=t[0];return{type:"lap",mode:r.mode,alignment:n.slice(5),body:a}},htmlBuilder:function(e,t){var r;e.alignment==="clap"?(r=E.makeSpan([],[ye(e.body,t)]),r=E.makeSpan(["inner"],[r],t)):r=E.makeSpan(["inner"],[ye(e.body,t)]);var n=E.makeSpan(["fix"],[]),a=E.makeSpan([e.alignment],[r,n],t),l=E.makeSpan(["strut"]);return l.style.height=V(a.height+a.depth),a.depth&&(l.style.verticalAlign=V(-a.depth)),a.children.unshift(l),a=E.makeSpan(["thinbox"],[a],t),E.makeSpan(["mord","vbox"],[a],t)},mathmlBuilder:function(e,t){var r=new H.MathNode("mpadded",[Be(e.body,t)]);if(e.alignment!=="rlap"){var n=e.alignment==="llap"?"-1":"-0.5";r.setAttribute("lspace",n+"width")}return r.setAttribute("width","0px"),r}}),Q({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler:function(e,t){var r=e.funcName,n=e.parser,a=n.mode;n.switchMode("math");var l=r==="\\("?"\\)":"$",c=n.parseExpression(!1,l);return n.expect(l),n.switchMode(a),{type:"styling",mode:n.mode,style:"text",body:c}}}),Q({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler:function(e,t){throw new y("Mismatched "+e.funcName)}});var Na=function(e,t){switch(t.style.size){case K.DISPLAY.size:return e.display;case K.TEXT.size:return e.text;case K.SCRIPT.size:return e.script;case K.SCRIPTSCRIPT.size:return e.scriptscript;default:return e.text}};Q({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:function(e,t){var r=e.parser;return{type:"mathchoice",mode:r.mode,display:Pe(t[0]),text:Pe(t[1]),script:Pe(t[2]),scriptscript:Pe(t[3])}},htmlBuilder:function(e,t){var r=Na(e,t),n=Ge(r,t,!1);return E.makeFragment(n)},mathmlBuilder:function(e,t){var r=Na(e,t);return W0(r,t)}});var Ra=function(e,t,r,n,a,l,c){e=E.makeSpan([],[e]);var p=r&&Y.isCharacterBox(r),b,S;if(t){var B=ye(t,n.havingStyle(a.sup()),n);S={elem:B,kern:Math.max(n.fontMetrics().bigOpSpacing1,n.fontMetrics().bigOpSpacing3-B.depth)}}if(r){var R=ye(r,n.havingStyle(a.sub()),n);b={elem:R,kern:Math.max(n.fontMetrics().bigOpSpacing2,n.fontMetrics().bigOpSpacing4-R.height)}}var N;if(S&&b){var L=n.fontMetrics().bigOpSpacing5+b.elem.height+b.elem.depth+b.kern+e.depth+c;N=E.makeVList({positionType:"bottom",positionData:L,children:[{type:"kern",size:n.fontMetrics().bigOpSpacing5},{type:"elem",elem:b.elem,marginLeft:V(-l)},{type:"kern",size:b.kern},{type:"elem",elem:e},{type:"kern",size:S.kern},{type:"elem",elem:S.elem,marginLeft:V(l)},{type:"kern",size:n.fontMetrics().bigOpSpacing5}]},n)}else if(b){var G=e.height-c;N=E.makeVList({positionType:"top",positionData:G,children:[{type:"kern",size:n.fontMetrics().bigOpSpacing5},{type:"elem",elem:b.elem,marginLeft:V(-l)},{type:"kern",size:b.kern},{type:"elem",elem:e}]},n)}else if(S){var Z=e.depth+c;N=E.makeVList({positionType:"bottom",positionData:Z,children:[{type:"elem",elem:e},{type:"kern",size:S.kern},{type:"elem",elem:S.elem,marginLeft:V(l)},{type:"kern",size:n.fontMetrics().bigOpSpacing5}]},n)}else return e;var re=[N];if(b&&l!==0&&!p){var se=E.makeSpan(["mspace"],[],n);se.style.marginRight=V(l),re.unshift(se)}return E.makeSpan(["mop","op-limits"],re,n)},Fa=["\\smallint"],wt=function(e,t){var r,n,a=!1,l;e.type==="supsub"?(r=e.sup,n=e.sub,l=fe(e.base,"op"),a=!0):l=fe(e,"op");var c=t.style,p=!1;c.size===K.DISPLAY.size&&l.symbol&&!Y.contains(Fa,l.name)&&(p=!0);var b;if(l.symbol){var S=p?"Size2-Regular":"Size1-Regular",B="";if((l.name==="\\oiint"||l.name==="\\oiiint")&&(B=l.name.slice(1),l.name=B==="oiint"?"\\iint":"\\iiint"),b=E.makeSymbol(l.name,S,"math",t,["mop","op-symbol",p?"large-op":"small-op"]),B.length>0){var R=b.italic,N=E.staticSvg(B+"Size"+(p?"2":"1"),t);b=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:b,shift:0},{type:"elem",elem:N,shift:p?.08:0}]},t),l.name="\\"+B,b.classes.unshift("mop"),b.italic=R}}else if(l.body){var L=Ge(l.body,t,!0);L.length===1&&L[0]instanceof Ze?(b=L[0],b.classes[0]="mop"):b=E.makeSpan(["mop"],L,t)}else{for(var G=[],Z=1;Z<l.name.length;Z++)G.push(E.mathsym(l.name[Z],l.mode,t));b=E.makeSpan(["mop"],G,t)}var re=0,se=0;return(b instanceof Ze||l.name==="\\oiint"||l.name==="\\oiiint")&&!l.suppressBaseShift&&(re=(b.height-b.depth)/2-t.fontMetrics().axisHeight,se=b.italic),a?Ra(b,r,n,t,c,se,re):(re&&(b.style.position="relative",b.style.top=V(re)),b)},Pt=function(e,t){var r;if(e.symbol)r=new d0("mo",[f0(e.name,e.mode)]),Y.contains(Fa,e.name)&&r.setAttribute("largeop","false");else if(e.body)r=new d0("mo",n0(e.body,t));else{r=new d0("mi",[new Lt(e.name.slice(1))]);var n=new d0("mo",[f0("⁡","text")]);e.parentIsSupSub?r=new d0("mrow",[r,n]):r=Qn([r,n])}return r},Hs={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};Q({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:function(e,t){var r=e.parser,n=e.funcName,a=n;return a.length===1&&(a=Hs[a]),{type:"op",mode:r.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:a}},htmlBuilder:wt,mathmlBuilder:Pt}),Q({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:function(e,t){var r=e.parser,n=t[0];return{type:"op",mode:r.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:Pe(n)}},htmlBuilder:wt,mathmlBuilder:Pt});var Us={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};Q({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler:function(e){var t=e.parser,r=e.funcName;return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:wt,mathmlBuilder:Pt}),Q({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler:function(e){var t=e.parser,r=e.funcName;return{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:wt,mathmlBuilder:Pt}),Q({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler:function(e){var t=e.parser,r=e.funcName,n=r;return n.length===1&&(n=Us[n]),{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:wt,mathmlBuilder:Pt});var Ia=function(e,t){var r,n,a=!1,l;e.type==="supsub"?(r=e.sup,n=e.sub,l=fe(e.base,"operatorname"),a=!0):l=fe(e,"operatorname");var c;if(l.body.length>0){for(var p=l.body.map(function(R){var N=R.text;return typeof N=="string"?{type:"textord",mode:R.mode,text:N}:R}),b=Ge(p,t.withFont("mathrm"),!0),S=0;S<b.length;S++){var B=b[S];B instanceof Ze&&(B.text=B.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}c=E.makeSpan(["mop"],b,t)}else c=E.makeSpan(["mop"],[],t);return a?Ra(c,r,n,t,t.style,0,0):c},Gs=function(e,t){for(var r=n0(e.body,t.withFont("mathrm")),n=!0,a=0;a<r.length;a++){var l=r[a];if(!(l instanceof H.SpaceNode))if(l instanceof H.MathNode)switch(l.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{var c=l.children[0];l.children.length===1&&c instanceof H.TextNode?c.text=c.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):n=!1;break}default:n=!1}else n=!1}if(n){var p=r.map(function(B){return B.toText()}).join("");r=[new H.TextNode(p)]}var b=new H.MathNode("mi",r);b.setAttribute("mathvariant","normal");var S=new H.MathNode("mo",[f0("⁡","text")]);return e.parentIsSupSub?new H.MathNode("mrow",[b,S]):H.newDocumentFragment([b,S])};Q({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=e.funcName,a=t[0];return{type:"operatorname",mode:r.mode,body:Pe(a),alwaysHandleSupSub:n==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:Ia,mathmlBuilder:Gs}),g("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@"),lt({type:"ordgroup",htmlBuilder:function(e,t){return e.semisimple?E.makeFragment(Ge(e.body,t,!1)):E.makeSpan(["mord"],Ge(e.body,t,!0),t)},mathmlBuilder:function(e,t){return W0(e.body,t,!0)}}),Q({type:"overline",names:["\\overline"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=t[0];return{type:"overline",mode:r.mode,body:n}},htmlBuilder:function(e,t){var r=ye(e.body,t.havingCrampedStyle()),n=E.makeLineSpan("overline-line",t),a=t.fontMetrics().defaultRuleThickness,l=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*a},{type:"elem",elem:n},{type:"kern",size:a}]},t);return E.makeSpan(["mord","overline"],[l],t)},mathmlBuilder:function(e,t){var r=new H.MathNode("mo",[new H.TextNode("‾")]);r.setAttribute("stretchy","true");var n=new H.MathNode("mover",[Be(e.body,t),r]);return n.setAttribute("accent","true"),n}}),Q({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser,n=t[0];return{type:"phantom",mode:r.mode,body:Pe(n)}},htmlBuilder:function(e,t){var r=Ge(e.body,t.withPhantom(),!1);return E.makeFragment(r)},mathmlBuilder:function(e,t){var r=n0(e.body,t);return new H.MathNode("mphantom",r)}}),Q({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser,n=t[0];return{type:"hphantom",mode:r.mode,body:n}},htmlBuilder:function(e,t){var r=E.makeSpan([],[ye(e.body,t.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(var n=0;n<r.children.length;n++)r.children[n].height=0,r.children[n].depth=0;return r=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t),E.makeSpan(["mord"],[r],t)},mathmlBuilder:function(e,t){var r=n0(Pe(e.body),t),n=new H.MathNode("mphantom",r),a=new H.MathNode("mpadded",[n]);return a.setAttribute("height","0px"),a.setAttribute("depth","0px"),a}}),Q({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser,n=t[0];return{type:"vphantom",mode:r.mode,body:n}},htmlBuilder:function(e,t){var r=E.makeSpan(["inner"],[ye(e.body,t.withPhantom())]),n=E.makeSpan(["fix"],[]);return E.makeSpan(["mord","rlap"],[r,n],t)},mathmlBuilder:function(e,t){var r=n0(Pe(e.body),t),n=new H.MathNode("mphantom",r),a=new H.MathNode("mpadded",[n]);return a.setAttribute("width","0px"),a}}),Q({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler:function(e,t){var r=e.parser,n=fe(t[0],"size").value,a=t[1];return{type:"raisebox",mode:r.mode,dy:n,body:a}},htmlBuilder:function(e,t){var r=ye(e.body,t),n=ze(e.dy,t);return E.makeVList({positionType:"shift",positionData:-n,children:[{type:"elem",elem:r}]},t)},mathmlBuilder:function(e,t){var r=new H.MathNode("mpadded",[Be(e.body,t)]),n=e.dy.number+e.dy.unit;return r.setAttribute("voffset",n),r}}),Q({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0},handler:function(e){var t=e.parser;return{type:"internal",mode:t.mode}}}),Q({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},handler:function(e,t,r){var n=e.parser,a=r[0],l=fe(t[0],"size"),c=fe(t[1],"size");return{type:"rule",mode:n.mode,shift:a&&fe(a,"size").value,width:l.value,height:c.value}},htmlBuilder:function(e,t){var r=E.makeSpan(["mord","rule"],[],t),n=ze(e.width,t),a=ze(e.height,t),l=e.shift?ze(e.shift,t):0;return r.style.borderRightWidth=V(n),r.style.borderTopWidth=V(a),r.style.bottom=V(l),r.width=n,r.height=a+l,r.depth=-l,r.maxFontSize=a*1.125*t.sizeMultiplier,r},mathmlBuilder:function(e,t){var r=ze(e.width,t),n=ze(e.height,t),a=e.shift?ze(e.shift,t):0,l=t.color&&t.getColor()||"black",c=new H.MathNode("mspace");c.setAttribute("mathbackground",l),c.setAttribute("width",V(r)),c.setAttribute("height",V(n));var p=new H.MathNode("mpadded",[c]);return a>=0?p.setAttribute("height",V(a)):(p.setAttribute("height",V(a)),p.setAttribute("depth",V(-a))),p.setAttribute("voffset",V(a)),p}});function La(s,e,t){for(var r=Ge(s,e,!1),n=e.sizeMultiplier/t.sizeMultiplier,a=0;a<r.length;a++){var l=r[a].classes.indexOf("sizing");l<0?Array.prototype.push.apply(r[a].classes,e.sizingClasses(t)):r[a].classes[l+1]==="reset-size"+e.size&&(r[a].classes[l+1]="reset-size"+t.size),r[a].height*=n,r[a].depth*=n}return E.makeFragment(r)}var Oa=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],Vs=function(e,t){var r=t.havingSize(e.size);return La(e.body,r,t)};Q({type:"sizing",names:Oa,props:{numArgs:0,allowedInText:!0},handler:function(e,t){var r=e.breakOnTokenText,n=e.funcName,a=e.parser,l=a.parseExpression(!1,r);return{type:"sizing",mode:a.mode,size:Oa.indexOf(n)+1,body:l}},htmlBuilder:Vs,mathmlBuilder:function(e,t){var r=t.havingSize(e.size),n=n0(e.body,r),a=new H.MathNode("mstyle",n);return a.setAttribute("mathsize",V(r.sizeMultiplier)),a}}),Q({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:function(e,t,r){var n=e.parser,a=!1,l=!1,c=r[0]&&fe(r[0],"ordgroup");if(c)for(var p="",b=0;b<c.body.length;++b){var S=c.body[b];if(p=S.text,p==="t")a=!0;else if(p==="b")l=!0;else{a=!1,l=!1;break}}else a=!0,l=!0;var B=t[0];return{type:"smash",mode:n.mode,body:B,smashHeight:a,smashDepth:l}},htmlBuilder:function(e,t){var r=E.makeSpan([],[ye(e.body,t)]);if(!e.smashHeight&&!e.smashDepth)return r;if(e.smashHeight&&(r.height=0,r.children))for(var n=0;n<r.children.length;n++)r.children[n].height=0;if(e.smashDepth&&(r.depth=0,r.children))for(var a=0;a<r.children.length;a++)r.children[a].depth=0;var l=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t);return E.makeSpan(["mord"],[l],t)},mathmlBuilder:function(e,t){var r=new H.MathNode("mpadded",[Be(e.body,t)]);return e.smashHeight&&r.setAttribute("height","0px"),e.smashDepth&&r.setAttribute("depth","0px"),r}}),Q({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler:function(e,t,r){var n=e.parser,a=r[0],l=t[0];return{type:"sqrt",mode:n.mode,body:l,index:a}},htmlBuilder:function(e,t){var r=ye(e.body,t.havingCrampedStyle());r.height===0&&(r.height=t.fontMetrics().xHeight),r=E.wrapFragment(r,t);var n=t.fontMetrics(),a=n.defaultRuleThickness,l=a;t.style.id<K.TEXT.id&&(l=t.fontMetrics().xHeight);var c=a+l/4,p=r.height+r.depth+c+a,b=R0.sqrtImage(p,t),S=b.span,B=b.ruleWidth,R=b.advanceWidth,N=S.height-B;N>r.height+r.depth+c&&(c=(c+N-r.height-r.depth)/2);var L=S.height-r.height-c-B;r.style.paddingLeft=V(R);var G=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+L)},{type:"elem",elem:S},{type:"kern",size:B}]},t);if(e.index){var Z=t.havingStyle(K.SCRIPTSCRIPT),re=ye(e.index,Z,t),se=.6*(G.height-G.depth),he=E.makeVList({positionType:"shift",positionData:-se,children:[{type:"elem",elem:re}]},t),we=E.makeSpan(["root"],[he]);return E.makeSpan(["mord","sqrt"],[we,G],t)}else return E.makeSpan(["mord","sqrt"],[G],t)},mathmlBuilder:function(e,t){var r=e.body,n=e.index;return n?new H.MathNode("mroot",[Be(r,t),Be(n,t)]):new H.MathNode("msqrt",[Be(r,t)])}});var qa={display:K.DISPLAY,text:K.TEXT,script:K.SCRIPT,scriptscript:K.SCRIPTSCRIPT};Q({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler:function(e,t){var r=e.breakOnTokenText,n=e.funcName,a=e.parser,l=a.parseExpression(!0,r),c=n.slice(1,n.length-5);return{type:"styling",mode:a.mode,style:c,body:l}},htmlBuilder:function(e,t){var r=qa[e.style],n=t.havingStyle(r).withFont("");return La(e.body,n,t)},mathmlBuilder:function(e,t){var r=qa[e.style],n=t.havingStyle(r),a=n0(e.body,n),l=new H.MathNode("mstyle",a),c={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]},p=c[e.style];return l.setAttribute("scriptlevel",p[0]),l.setAttribute("displaystyle",p[1]),l}});var Ws=function(e,t){var r=e.base;if(r)if(r.type==="op"){var n=r.limits&&(t.style.size===K.DISPLAY.size||r.alwaysHandleSupSub);return n?wt:null}else if(r.type==="operatorname"){var a=r.alwaysHandleSupSub&&(t.style.size===K.DISPLAY.size||r.limits);return a?Ia:null}else{if(r.type==="accent")return Y.isCharacterBox(r.base)?Wr:null;if(r.type==="horizBrace"){var l=!e.sub;return l===r.isOver?_a:null}else return null}else return null};lt({type:"supsub",htmlBuilder:function(e,t){var r=Ws(e,t);if(r)return r(e,t);var n=e.base,a=e.sup,l=e.sub,c=ye(n,t),p,b,S=t.fontMetrics(),B=0,R=0,N=n&&Y.isCharacterBox(n);if(a){var L=t.havingStyle(t.style.sup());p=ye(a,L,t),N||(B=c.height-L.fontMetrics().supDrop*L.sizeMultiplier/t.sizeMultiplier)}if(l){var G=t.havingStyle(t.style.sub());b=ye(l,G,t),N||(R=c.depth+G.fontMetrics().subDrop*G.sizeMultiplier/t.sizeMultiplier)}var Z;t.style===K.DISPLAY?Z=S.sup1:t.style.cramped?Z=S.sup3:Z=S.sup2;var re=t.sizeMultiplier,se=V(.5/S.ptPerEm/re),he=null;if(b){var we=e.base&&e.base.type==="op"&&e.base.name&&(e.base.name==="\\oiint"||e.base.name==="\\oiiint");(c instanceof Ze||we)&&(he=V(-c.italic))}var pe;if(p&&b){B=Math.max(B,Z,p.depth+.25*S.xHeight),R=Math.max(R,S.sub2);var Te=S.defaultRuleThickness,xe=4*Te;if(B-p.depth-(b.height-R)<xe){R=xe-(B-p.depth)+b.height;var Me=.8*S.xHeight-(B-p.depth);Me>0&&(B+=Me,R-=Me)}var _e=[{type:"elem",elem:b,shift:R,marginRight:se,marginLeft:he},{type:"elem",elem:p,shift:-B,marginRight:se}];pe=E.makeVList({positionType:"individualShift",children:_e},t)}else if(b){R=Math.max(R,S.sub1,b.height-.8*S.xHeight);var Ye=[{type:"elem",elem:b,marginLeft:he,marginRight:se}];pe=E.makeVList({positionType:"shift",positionData:R,children:Ye},t)}else if(p)B=Math.max(B,Z,p.depth+.25*S.xHeight),pe=E.makeVList({positionType:"shift",positionData:-B,children:[{type:"elem",elem:p,marginRight:se}]},t);else throw new Error("supsub must have either sup or sub.");var s0=Pr(c,"right")||"mord";return E.makeSpan([s0],[c,E.makeSpan(["msupsub"],[pe])],t)},mathmlBuilder:function(e,t){var r=!1,n,a;e.base&&e.base.type==="horizBrace"&&(a=!!e.sup,a===e.base.isOver&&(r=!0,n=e.base.isOver)),e.base&&(e.base.type==="op"||e.base.type==="operatorname")&&(e.base.parentIsSupSub=!0);var l=[Be(e.base,t)];e.sub&&l.push(Be(e.sub,t)),e.sup&&l.push(Be(e.sup,t));var c;if(r)c=n?"mover":"munder";else if(e.sub)if(e.sup){var S=e.base;S&&S.type==="op"&&S.limits&&t.style===K.DISPLAY||S&&S.type==="operatorname"&&S.alwaysHandleSupSub&&(t.style===K.DISPLAY||S.limits)?c="munderover":c="msubsup"}else{var b=e.base;b&&b.type==="op"&&b.limits&&(t.style===K.DISPLAY||b.alwaysHandleSupSub)||b&&b.type==="operatorname"&&b.alwaysHandleSupSub&&(b.limits||t.style===K.DISPLAY)?c="munder":c="msub"}else{var p=e.base;p&&p.type==="op"&&p.limits&&(t.style===K.DISPLAY||p.alwaysHandleSupSub)||p&&p.type==="operatorname"&&p.alwaysHandleSupSub&&(p.limits||t.style===K.DISPLAY)?c="mover":c="msup"}return new H.MathNode(c,l)}}),lt({type:"atom",htmlBuilder:function(e,t){return E.mathsym(e.text,e.mode,t,["m"+e.family])},mathmlBuilder:function(e,t){var r=new H.MathNode("mo",[f0(e.text,e.mode)]);if(e.family==="bin"){var n=Gr(e,t);n==="bold-italic"&&r.setAttribute("mathvariant",n)}else e.family==="punct"?r.setAttribute("separator","true"):(e.family==="open"||e.family==="close")&&r.setAttribute("stretchy","false");return r}});var Pa={mi:"italic",mn:"normal",mtext:"normal"};lt({type:"mathord",htmlBuilder:function(e,t){return E.makeOrd(e,t,"mathord")},mathmlBuilder:function(e,t){var r=new H.MathNode("mi",[f0(e.text,e.mode,t)]),n=Gr(e,t)||"italic";return n!==Pa[r.type]&&r.setAttribute("mathvariant",n),r}}),lt({type:"textord",htmlBuilder:function(e,t){return E.makeOrd(e,t,"textord")},mathmlBuilder:function(e,t){var r=f0(e.text,e.mode,t),n=Gr(e,t)||"normal",a;return e.mode==="text"?a=new H.MathNode("mtext",[r]):/[0-9]/.test(e.text)?a=new H.MathNode("mn",[r]):e.text==="\\prime"?a=new H.MathNode("mo",[r]):a=new H.MathNode("mi",[r]),n!==Pa[a.type]&&a.setAttribute("mathvariant",n),a}});var on={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},un={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};lt({type:"spacing",htmlBuilder:function(e,t){if(un.hasOwnProperty(e.text)){var r=un[e.text].className||"";if(e.mode==="text"){var n=E.makeOrd(e,t,"textord");return n.classes.push(r),n}else return E.makeSpan(["mspace",r],[E.mathsym(e.text,e.mode,t)],t)}else{if(on.hasOwnProperty(e.text))return E.makeSpan(["mspace",on[e.text]],[],t);throw new y('Unknown type of space "'+e.text+'"')}},mathmlBuilder:function(e,t){var r;if(un.hasOwnProperty(e.text))r=new H.MathNode("mtext",[new H.TextNode(" ")]);else{if(on.hasOwnProperty(e.text))return new H.MathNode("mspace");throw new y('Unknown type of space "'+e.text+'"')}return r}});var Ha=function(){var e=new H.MathNode("mtd",[]);return e.setAttribute("width","50%"),e};lt({type:"tag",mathmlBuilder:function(e,t){var r=new H.MathNode("mtable",[new H.MathNode("mtr",[Ha(),new H.MathNode("mtd",[W0(e.body,t)]),Ha(),new H.MathNode("mtd",[W0(e.tag,t)])])]);return r.setAttribute("width","100%"),r}});var Ua={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},Ga={"\\textbf":"textbf","\\textmd":"textmd"},$s={"\\textit":"textit","\\textup":"textup"},Va=function(e,t){var r=e.font;return r?Ua[r]?t.withTextFontFamily(Ua[r]):Ga[r]?t.withTextFontWeight(Ga[r]):t.withTextFontShape($s[r]):t};Q({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler:function(e,t){var r=e.parser,n=e.funcName,a=t[0];return{type:"text",mode:r.mode,body:Pe(a),font:n}},htmlBuilder:function(e,t){var r=Va(e,t),n=Ge(e.body,r,!0);return E.makeSpan(["mord","text"],n,r)},mathmlBuilder:function(e,t){var r=Va(e,t);return W0(e.body,r)}}),Q({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser;return{type:"underline",mode:r.mode,body:t[0]}},htmlBuilder:function(e,t){var r=ye(e.body,t),n=E.makeLineSpan("underline-line",t),a=t.fontMetrics().defaultRuleThickness,l=E.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:a},{type:"elem",elem:n},{type:"kern",size:3*a},{type:"elem",elem:r}]},t);return E.makeSpan(["mord","underline"],[l],t)},mathmlBuilder:function(e,t){var r=new H.MathNode("mo",[new H.TextNode("‾")]);r.setAttribute("stretchy","true");var n=new H.MathNode("munder",[Be(e.body,t),r]);return n.setAttribute("accentunder","true"),n}}),Q({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler:function(e,t){var r=e.parser;return{type:"vcenter",mode:r.mode,body:t[0]}},htmlBuilder:function(e,t){var r=ye(e.body,t),n=t.fontMetrics().axisHeight,a=.5*(r.height-n-(r.depth+n));return E.makeVList({positionType:"shift",positionData:a,children:[{type:"elem",elem:r}]},t)},mathmlBuilder:function(e,t){return new H.MathNode("mpadded",[Be(e.body,t)],["vcenter"])}}),Q({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler:function(e,t,r){throw new y("\\verb ended by end of line instead of matching delimiter")},htmlBuilder:function(e,t){for(var r=Wa(e),n=[],a=t.havingStyle(t.style.text()),l=0;l<r.length;l++){var c=r[l];c==="~"&&(c="\\textasciitilde"),n.push(E.makeSymbol(c,"Typewriter-Regular",e.mode,a,["mord","texttt"]))}return E.makeSpan(["mord","text"].concat(a.sizingClasses(t)),E.tryCombineChars(n),a)},mathmlBuilder:function(e,t){var r=new H.TextNode(Wa(e)),n=new H.MathNode("mtext",[r]);return n.setAttribute("mathvariant","monospace"),n}});var Wa=function(e){return e.body.replace(/ /g,e.star?"␣":" ")},Ys=Xn,j0=Ys,$a=`[ \r
	]`,js="\\\\[a-zA-Z@]+",Xs="\\\\[^\uD800-\uDFFF]",Zs="("+js+")"+$a+"*",Ks=`\\\\(
|[ \r	]+
?)[ \r	]*`,hn="[̀-ͯ]",Qs=new RegExp(hn+"+$"),Js="("+$a+"+)|"+(Ks+"|")+"([!-\\[\\]-‧‪-퟿豈-￿]"+(hn+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(hn+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+Zs)+("|"+Xs+")"),Ya=function(){function s(t,r){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=t,this.settings=r,this.tokenRegex=new RegExp(Js,"g"),this.catcodes={"%":14,"~":13}}var e=s.prototype;return e.setCatcode=function(r,n){this.catcodes[r]=n},e.lex=function(){var r=this.input,n=this.tokenRegex.lastIndex;if(n===r.length)return new $0("EOF",new x0(this,n,n));var a=this.tokenRegex.exec(r);if(a===null||a.index!==n)throw new y("Unexpected character: '"+r[n]+"'",new $0(r[n],new x0(this,n,n+1)));var l=a[6]||a[3]||(a[2]?"\\ ":" ");if(this.catcodes[l]===14){var c=r.indexOf(`
`,this.tokenRegex.lastIndex);return c===-1?(this.tokenRegex.lastIndex=r.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=c+1,this.lex()}return new $0(l,new x0(this,n,this.tokenRegex.lastIndex))},s}(),el=function(){function s(t,r){t===void 0&&(t={}),r===void 0&&(r={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=r,this.builtins=t,this.undefStack=[]}var e=s.prototype;return e.beginGroup=function(){this.undefStack.push({})},e.endGroup=function(){if(this.undefStack.length===0)throw new y("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var r=this.undefStack.pop();for(var n in r)r.hasOwnProperty(n)&&(r[n]==null?delete this.current[n]:this.current[n]=r[n])},e.endGroups=function(){for(;this.undefStack.length>0;)this.endGroup()},e.has=function(r){return this.current.hasOwnProperty(r)||this.builtins.hasOwnProperty(r)},e.get=function(r){return this.current.hasOwnProperty(r)?this.current[r]:this.builtins[r]},e.set=function(r,n,a){if(a===void 0&&(a=!1),a){for(var l=0;l<this.undefStack.length;l++)delete this.undefStack[l][r];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][r]=n)}else{var c=this.undefStack[this.undefStack.length-1];c&&!c.hasOwnProperty(r)&&(c[r]=this.current[r])}n==null?delete this.current[r]:this.current[r]=n},s}(),tl=ka,rl=tl;g("\\noexpand",function(s){var e=s.popToken();return s.isExpandable(e.text)&&(e.noexpand=!0,e.treatAsRelax=!0),{tokens:[e],numArgs:0}}),g("\\expandafter",function(s){var e=s.popToken();return s.expandOnce(!0),{tokens:[e],numArgs:0}}),g("\\@firstoftwo",function(s){var e=s.consumeArgs(2);return{tokens:e[0],numArgs:0}}),g("\\@secondoftwo",function(s){var e=s.consumeArgs(2);return{tokens:e[1],numArgs:0}}),g("\\@ifnextchar",function(s){var e=s.consumeArgs(3);s.consumeSpaces();var t=s.future();return e[0].length===1&&e[0][0].text===t.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}}),g("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),g("\\TextOrMath",function(s){var e=s.consumeArgs(2);return s.mode==="text"?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});var ja={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};g("\\char",function(s){var e=s.popToken(),t,r="";if(e.text==="'")t=8,e=s.popToken();else if(e.text==='"')t=16,e=s.popToken();else if(e.text==="`")if(e=s.popToken(),e.text[0]==="\\")r=e.text.charCodeAt(1);else{if(e.text==="EOF")throw new y("\\char` missing argument");r=e.text.charCodeAt(0)}else t=10;if(t){if(r=ja[e.text],r==null||r>=t)throw new y("Invalid base-"+t+" digit "+e.text);for(var n;(n=ja[s.future().text])!=null&&n<t;)r*=t,r+=n,s.popToken()}return"\\@char{"+r+"}"});var cn=function(e,t,r){var n=e.consumeArg().tokens;if(n.length!==1)throw new y("\\newcommand's first argument must be a macro name");var a=n[0].text,l=e.isDefined(a);if(l&&!t)throw new y("\\newcommand{"+a+"} attempting to redefine "+(a+"; use \\renewcommand"));if(!l&&!r)throw new y("\\renewcommand{"+a+"} when command "+a+" does not yet exist; use \\newcommand");var c=0;if(n=e.consumeArg().tokens,n.length===1&&n[0].text==="["){for(var p="",b=e.expandNextToken();b.text!=="]"&&b.text!=="EOF";)p+=b.text,b=e.expandNextToken();if(!p.match(/^\s*[0-9]+\s*$/))throw new y("Invalid number of arguments: "+p);c=parseInt(p),n=e.consumeArg().tokens}return e.macros.set(a,{tokens:n,numArgs:c}),""};g("\\newcommand",function(s){return cn(s,!1,!0)}),g("\\renewcommand",function(s){return cn(s,!0,!1)}),g("\\providecommand",function(s){return cn(s,!0,!0)}),g("\\message",function(s){var e=s.consumeArgs(1)[0];return console.log(e.reverse().map(function(t){return t.text}).join("")),""}),g("\\errmessage",function(s){var e=s.consumeArgs(1)[0];return console.error(e.reverse().map(function(t){return t.text}).join("")),""}),g("\\show",function(s){var e=s.popToken(),t=e.text;return console.log(e,s.macros.get(t),j0[t],Ce.math[t],Ce.text[t]),""}),g("\\bgroup","{"),g("\\egroup","}"),g("~","\\nobreakspace"),g("\\lq","`"),g("\\rq","'"),g("\\aa","\\r a"),g("\\AA","\\r A"),g("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`©}"),g("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),g("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`®}"),g("ℬ","\\mathscr{B}"),g("ℰ","\\mathscr{E}"),g("ℱ","\\mathscr{F}"),g("ℋ","\\mathscr{H}"),g("ℐ","\\mathscr{I}"),g("ℒ","\\mathscr{L}"),g("ℳ","\\mathscr{M}"),g("ℛ","\\mathscr{R}"),g("ℭ","\\mathfrak{C}"),g("ℌ","\\mathfrak{H}"),g("ℨ","\\mathfrak{Z}"),g("\\Bbbk","\\Bbb{k}"),g("·","\\cdotp"),g("\\llap","\\mathllap{\\textrm{#1}}"),g("\\rlap","\\mathrlap{\\textrm{#1}}"),g("\\clap","\\mathclap{\\textrm{#1}}"),g("\\mathstrut","\\vphantom{(}"),g("\\underbar","\\underline{\\text{#1}}"),g("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}'),g("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}"),g("\\ne","\\neq"),g("≠","\\neq"),g("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}"),g("∉","\\notin"),g("≘","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}"),g("≙","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}"),g("≚","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}"),g("≛","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}"),g("≝","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}"),g("≞","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}"),g("≟","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}"),g("⟂","\\perp"),g("‼","\\mathclose{!\\mkern-0.8mu!}"),g("∌","\\notni"),g("⌜","\\ulcorner"),g("⌝","\\urcorner"),g("⌞","\\llcorner"),g("⌟","\\lrcorner"),g("©","\\copyright"),g("®","\\textregistered"),g("️","\\textregistered"),g("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}'),g("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}'),g("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}'),g("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}'),g("\\vdots","\\mathord{\\varvdots\\rule{0pt}{15pt}}"),g("⋮","\\vdots"),g("\\varGamma","\\mathit{\\Gamma}"),g("\\varDelta","\\mathit{\\Delta}"),g("\\varTheta","\\mathit{\\Theta}"),g("\\varLambda","\\mathit{\\Lambda}"),g("\\varXi","\\mathit{\\Xi}"),g("\\varPi","\\mathit{\\Pi}"),g("\\varSigma","\\mathit{\\Sigma}"),g("\\varUpsilon","\\mathit{\\Upsilon}"),g("\\varPhi","\\mathit{\\Phi}"),g("\\varPsi","\\mathit{\\Psi}"),g("\\varOmega","\\mathit{\\Omega}"),g("\\substack","\\begin{subarray}{c}#1\\end{subarray}"),g("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax"),g("\\boxed","\\fbox{$\\displaystyle{#1}$}"),g("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),g("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),g("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");var Xa={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};g("\\dots",function(s){var e="\\dotso",t=s.expandAfterFuture().text;return t in Xa?e=Xa[t]:(t.slice(0,4)==="\\not"||t in Ce.math&&Y.contains(["bin","rel"],Ce.math[t].group))&&(e="\\dotsb"),e});var mn={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};g("\\dotso",function(s){var e=s.future().text;return e in mn?"\\ldots\\,":"\\ldots"}),g("\\dotsc",function(s){var e=s.future().text;return e in mn&&e!==","?"\\ldots\\,":"\\ldots"}),g("\\cdots",function(s){var e=s.future().text;return e in mn?"\\@cdots\\,":"\\@cdots"}),g("\\dotsb","\\cdots"),g("\\dotsm","\\cdots"),g("\\dotsi","\\!\\cdots"),g("\\dotsx","\\ldots\\,"),g("\\DOTSI","\\relax"),g("\\DOTSB","\\relax"),g("\\DOTSX","\\relax"),g("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),g("\\,","\\tmspace+{3mu}{.1667em}"),g("\\thinspace","\\,"),g("\\>","\\mskip{4mu}"),g("\\:","\\tmspace+{4mu}{.2222em}"),g("\\medspace","\\:"),g("\\;","\\tmspace+{5mu}{.2777em}"),g("\\thickspace","\\;"),g("\\!","\\tmspace-{3mu}{.1667em}"),g("\\negthinspace","\\!"),g("\\negmedspace","\\tmspace-{4mu}{.2222em}"),g("\\negthickspace","\\tmspace-{5mu}{.277em}"),g("\\enspace","\\kern.5em "),g("\\enskip","\\hskip.5em\\relax"),g("\\quad","\\hskip1em\\relax"),g("\\qquad","\\hskip2em\\relax"),g("\\tag","\\@ifstar\\tag@literal\\tag@paren"),g("\\tag@paren","\\tag@literal{({#1})}"),g("\\tag@literal",function(s){if(s.macros.get("\\df@tag"))throw new y("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"}),g("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),g("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),g("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),g("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),g("\\newline","\\\\\\relax"),g("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var Za=V(h0["Main-Regular"]["T".charCodeAt(0)][1]-.7*h0["Main-Regular"]["A".charCodeAt(0)][1]);g("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+Za+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}"),g("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+Za+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}"),g("\\hspace","\\@ifstar\\@hspacer\\@hspace"),g("\\@hspace","\\hskip #1\\relax"),g("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),g("\\ordinarycolon",":"),g("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),g("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}'),g("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}'),g("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}'),g("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}'),g("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}'),g("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}'),g("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}'),g("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}'),g("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}'),g("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}'),g("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}'),g("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}'),g("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}'),g("∷","\\dblcolon"),g("∹","\\eqcolon"),g("≔","\\coloneqq"),g("≕","\\eqqcolon"),g("⩴","\\Coloneqq"),g("\\ratio","\\vcentcolon"),g("\\coloncolon","\\dblcolon"),g("\\colonequals","\\coloneqq"),g("\\coloncolonequals","\\Coloneqq"),g("\\equalscolon","\\eqqcolon"),g("\\equalscoloncolon","\\Eqqcolon"),g("\\colonminus","\\coloneq"),g("\\coloncolonminus","\\Coloneq"),g("\\minuscolon","\\eqcolon"),g("\\minuscoloncolon","\\Eqcolon"),g("\\coloncolonapprox","\\Colonapprox"),g("\\coloncolonsim","\\Colonsim"),g("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),g("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),g("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),g("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),g("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}"),g("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}"),g("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}"),g("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}"),g("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}"),g("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}"),g("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}"),g("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}"),g("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}"),g("\\gvertneqq","\\html@mathml{\\@gvertneqq}{≩}"),g("\\lvertneqq","\\html@mathml{\\@lvertneqq}{≨}"),g("\\ngeqq","\\html@mathml{\\@ngeqq}{≱}"),g("\\ngeqslant","\\html@mathml{\\@ngeqslant}{≱}"),g("\\nleqq","\\html@mathml{\\@nleqq}{≰}"),g("\\nleqslant","\\html@mathml{\\@nleqslant}{≰}"),g("\\nshortmid","\\html@mathml{\\@nshortmid}{∤}"),g("\\nshortparallel","\\html@mathml{\\@nshortparallel}{∦}"),g("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{⊈}"),g("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{⊉}"),g("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{⊊}"),g("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{⫋}"),g("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{⊋}"),g("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{⫌}"),g("\\imath","\\html@mathml{\\@imath}{ı}"),g("\\jmath","\\html@mathml{\\@jmath}{ȷ}"),g("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}"),g("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}"),g("⟦","\\llbracket"),g("⟧","\\rrbracket"),g("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}"),g("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}"),g("⦃","\\lBrace"),g("⦄","\\rBrace"),g("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}"),g("⦵","\\minuso"),g("\\darr","\\downarrow"),g("\\dArr","\\Downarrow"),g("\\Darr","\\Downarrow"),g("\\lang","\\langle"),g("\\rang","\\rangle"),g("\\uarr","\\uparrow"),g("\\uArr","\\Uparrow"),g("\\Uarr","\\Uparrow"),g("\\N","\\mathbb{N}"),g("\\R","\\mathbb{R}"),g("\\Z","\\mathbb{Z}"),g("\\alef","\\aleph"),g("\\alefsym","\\aleph"),g("\\Alpha","\\mathrm{A}"),g("\\Beta","\\mathrm{B}"),g("\\bull","\\bullet"),g("\\Chi","\\mathrm{X}"),g("\\clubs","\\clubsuit"),g("\\cnums","\\mathbb{C}"),g("\\Complex","\\mathbb{C}"),g("\\Dagger","\\ddagger"),g("\\diamonds","\\diamondsuit"),g("\\empty","\\emptyset"),g("\\Epsilon","\\mathrm{E}"),g("\\Eta","\\mathrm{H}"),g("\\exist","\\exists"),g("\\harr","\\leftrightarrow"),g("\\hArr","\\Leftrightarrow"),g("\\Harr","\\Leftrightarrow"),g("\\hearts","\\heartsuit"),g("\\image","\\Im"),g("\\infin","\\infty"),g("\\Iota","\\mathrm{I}"),g("\\isin","\\in"),g("\\Kappa","\\mathrm{K}"),g("\\larr","\\leftarrow"),g("\\lArr","\\Leftarrow"),g("\\Larr","\\Leftarrow"),g("\\lrarr","\\leftrightarrow"),g("\\lrArr","\\Leftrightarrow"),g("\\Lrarr","\\Leftrightarrow"),g("\\Mu","\\mathrm{M}"),g("\\natnums","\\mathbb{N}"),g("\\Nu","\\mathrm{N}"),g("\\Omicron","\\mathrm{O}"),g("\\plusmn","\\pm"),g("\\rarr","\\rightarrow"),g("\\rArr","\\Rightarrow"),g("\\Rarr","\\Rightarrow"),g("\\real","\\Re"),g("\\reals","\\mathbb{R}"),g("\\Reals","\\mathbb{R}"),g("\\Rho","\\mathrm{P}"),g("\\sdot","\\cdot"),g("\\sect","\\S"),g("\\spades","\\spadesuit"),g("\\sub","\\subset"),g("\\sube","\\subseteq"),g("\\supe","\\supseteq"),g("\\Tau","\\mathrm{T}"),g("\\thetasym","\\vartheta"),g("\\weierp","\\wp"),g("\\Zeta","\\mathrm{Z}"),g("\\argmin","\\DOTSB\\operatorname*{arg\\,min}"),g("\\argmax","\\DOTSB\\operatorname*{arg\\,max}"),g("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits"),g("\\bra","\\mathinner{\\langle{#1}|}"),g("\\ket","\\mathinner{|{#1}\\rangle}"),g("\\braket","\\mathinner{\\langle{#1}\\rangle}"),g("\\Bra","\\left\\langle#1\\right|"),g("\\Ket","\\left|#1\\right\\rangle");var Ka=function(e){return function(t){var r=t.consumeArg().tokens,n=t.consumeArg().tokens,a=t.consumeArg().tokens,l=t.consumeArg().tokens,c=t.macros.get("|"),p=t.macros.get("\\|");t.macros.beginGroup();var b=function(N){return function(L){e&&(L.macros.set("|",c),a.length&&L.macros.set("\\|",p));var G=N;if(!N&&a.length){var Z=L.future();Z.text==="|"&&(L.popToken(),G=!0)}return{tokens:G?a:n,numArgs:0}}};t.macros.set("|",b(!1)),a.length&&t.macros.set("\\|",b(!0));var S=t.consumeArg().tokens,B=t.expandTokens([].concat(l,S,r));return t.macros.endGroup(),{tokens:B.reverse(),numArgs:0}}};g("\\bra@ket",Ka(!1)),g("\\bra@set",Ka(!0)),g("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}"),g("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}"),g("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}"),g("\\angln","{\\angl n}"),g("\\blue","\\textcolor{##6495ed}{#1}"),g("\\orange","\\textcolor{##ffa500}{#1}"),g("\\pink","\\textcolor{##ff00af}{#1}"),g("\\red","\\textcolor{##df0030}{#1}"),g("\\green","\\textcolor{##28ae7b}{#1}"),g("\\gray","\\textcolor{gray}{#1}"),g("\\purple","\\textcolor{##9d38bd}{#1}"),g("\\blueA","\\textcolor{##ccfaff}{#1}"),g("\\blueB","\\textcolor{##80f6ff}{#1}"),g("\\blueC","\\textcolor{##63d9ea}{#1}"),g("\\blueD","\\textcolor{##11accd}{#1}"),g("\\blueE","\\textcolor{##0c7f99}{#1}"),g("\\tealA","\\textcolor{##94fff5}{#1}"),g("\\tealB","\\textcolor{##26edd5}{#1}"),g("\\tealC","\\textcolor{##01d1c1}{#1}"),g("\\tealD","\\textcolor{##01a995}{#1}"),g("\\tealE","\\textcolor{##208170}{#1}"),g("\\greenA","\\textcolor{##b6ffb0}{#1}"),g("\\greenB","\\textcolor{##8af281}{#1}"),g("\\greenC","\\textcolor{##74cf70}{#1}"),g("\\greenD","\\textcolor{##1fab54}{#1}"),g("\\greenE","\\textcolor{##0d923f}{#1}"),g("\\goldA","\\textcolor{##ffd0a9}{#1}"),g("\\goldB","\\textcolor{##ffbb71}{#1}"),g("\\goldC","\\textcolor{##ff9c39}{#1}"),g("\\goldD","\\textcolor{##e07d10}{#1}"),g("\\goldE","\\textcolor{##a75a05}{#1}"),g("\\redA","\\textcolor{##fca9a9}{#1}"),g("\\redB","\\textcolor{##ff8482}{#1}"),g("\\redC","\\textcolor{##f9685d}{#1}"),g("\\redD","\\textcolor{##e84d39}{#1}"),g("\\redE","\\textcolor{##bc2612}{#1}"),g("\\maroonA","\\textcolor{##ffbde0}{#1}"),g("\\maroonB","\\textcolor{##ff92c6}{#1}"),g("\\maroonC","\\textcolor{##ed5fa6}{#1}"),g("\\maroonD","\\textcolor{##ca337c}{#1}"),g("\\maroonE","\\textcolor{##9e034e}{#1}"),g("\\purpleA","\\textcolor{##ddd7ff}{#1}"),g("\\purpleB","\\textcolor{##c6b9fc}{#1}"),g("\\purpleC","\\textcolor{##aa87ff}{#1}"),g("\\purpleD","\\textcolor{##7854ab}{#1}"),g("\\purpleE","\\textcolor{##543b78}{#1}"),g("\\mintA","\\textcolor{##f5f9e8}{#1}"),g("\\mintB","\\textcolor{##edf2df}{#1}"),g("\\mintC","\\textcolor{##e0e5cc}{#1}"),g("\\grayA","\\textcolor{##f6f7f7}{#1}"),g("\\grayB","\\textcolor{##f0f1f2}{#1}"),g("\\grayC","\\textcolor{##e3e5e6}{#1}"),g("\\grayD","\\textcolor{##d6d8da}{#1}"),g("\\grayE","\\textcolor{##babec2}{#1}"),g("\\grayF","\\textcolor{##888d93}{#1}"),g("\\grayG","\\textcolor{##626569}{#1}"),g("\\grayH","\\textcolor{##3b3e40}{#1}"),g("\\grayI","\\textcolor{##21242c}{#1}"),g("\\kaBlue","\\textcolor{##314453}{#1}"),g("\\kaGreen","\\textcolor{##71B307}{#1}");var Qa={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0},nl=function(){function s(t,r,n){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=r,this.expansionCount=0,this.feed(t),this.macros=new el(rl,r.macros),this.mode=n,this.stack=[]}var e=s.prototype;return e.feed=function(r){this.lexer=new Ya(r,this.settings)},e.switchMode=function(r){this.mode=r},e.beginGroup=function(){this.macros.beginGroup()},e.endGroup=function(){this.macros.endGroup()},e.endGroups=function(){this.macros.endGroups()},e.future=function(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]},e.popToken=function(){return this.future(),this.stack.pop()},e.pushToken=function(r){this.stack.push(r)},e.pushTokens=function(r){var n;(n=this.stack).push.apply(n,r)},e.scanArgument=function(r){var n,a,l;if(r){if(this.consumeSpaces(),this.future().text!=="[")return null;n=this.popToken();var c=this.consumeArg(["]"]);l=c.tokens,a=c.end}else{var p=this.consumeArg();l=p.tokens,n=p.start,a=p.end}return this.pushToken(new $0("EOF",a.loc)),this.pushTokens(l),n.range(a,"")},e.consumeSpaces=function(){for(;;){var r=this.future();if(r.text===" ")this.stack.pop();else break}},e.consumeArg=function(r){var n=[],a=r&&r.length>0;a||this.consumeSpaces();var l=this.future(),c,p=0,b=0;do{if(c=this.popToken(),n.push(c),c.text==="{")++p;else if(c.text==="}"){if(--p,p===-1)throw new y("Extra }",c)}else if(c.text==="EOF")throw new y("Unexpected end of input in a macro argument, expected '"+(r&&a?r[b]:"}")+"'",c);if(r&&a)if((p===0||p===1&&r[b]==="{")&&c.text===r[b]){if(++b,b===r.length){n.splice(-b,b);break}}else b=0}while(p!==0||a);return l.text==="{"&&n[n.length-1].text==="}"&&(n.pop(),n.shift()),n.reverse(),{tokens:n,start:l,end:c}},e.consumeArgs=function(r,n){if(n){if(n.length!==r+1)throw new y("The length of delimiters doesn't match the number of args!");for(var a=n[0],l=0;l<a.length;l++){var c=this.popToken();if(a[l]!==c.text)throw new y("Use of the macro doesn't match its definition",c)}}for(var p=[],b=0;b<r;b++)p.push(this.consumeArg(n&&n[b+1]).tokens);return p},e.expandOnce=function(r){var n=this.popToken(),a=n.text,l=n.noexpand?null:this._getExpansion(a);if(l==null||r&&l.unexpandable){if(r&&l==null&&a[0]==="\\"&&!this.isDefined(a))throw new y("Undefined control sequence: "+a);return this.pushToken(n),!1}if(this.expansionCount++,this.expansionCount>this.settings.maxExpand)throw new y("Too many expansions: infinite loop or need to increase maxExpand setting");var c=l.tokens,p=this.consumeArgs(l.numArgs,l.delimiters);if(l.numArgs){c=c.slice();for(var b=c.length-1;b>=0;--b){var S=c[b];if(S.text==="#"){if(b===0)throw new y("Incomplete placeholder at end of macro body",S);if(S=c[--b],S.text==="#")c.splice(b+1,1);else if(/^[1-9]$/.test(S.text)){var B;(B=c).splice.apply(B,[b,2].concat(p[+S.text-1]))}else throw new y("Not a valid argument number",S)}}}return this.pushTokens(c),c.length},e.expandAfterFuture=function(){return this.expandOnce(),this.future()},e.expandNextToken=function(){for(;;)if(this.expandOnce()===!1){var r=this.stack.pop();return r.treatAsRelax&&(r.text="\\relax"),r}throw new Error},e.expandMacro=function(r){return this.macros.has(r)?this.expandTokens([new $0(r)]):void 0},e.expandTokens=function(r){var n=[],a=this.stack.length;for(this.pushTokens(r);this.stack.length>a;)if(this.expandOnce(!0)===!1){var l=this.stack.pop();l.treatAsRelax&&(l.noexpand=!1,l.treatAsRelax=!1),n.push(l)}return n},e.expandMacroAsText=function(r){var n=this.expandMacro(r);return n&&n.map(function(a){return a.text}).join("")},e._getExpansion=function(r){var n=this.macros.get(r);if(n==null)return n;if(r.length===1){var a=this.lexer.catcodes[r];if(a!=null&&a!==13)return}var l=typeof n=="function"?n(this):n;if(typeof l=="string"){var c=0;if(l.indexOf("#")!==-1)for(var p=l.replace(/##/g,"");p.indexOf("#"+(c+1))!==-1;)++c;for(var b=new Ya(l,this.settings),S=[],B=b.lex();B.text!=="EOF";)S.push(B),B=b.lex();S.reverse();var R={tokens:S,numArgs:c};return R}return l},e.isDefined=function(r){return this.macros.has(r)||j0.hasOwnProperty(r)||Ce.math.hasOwnProperty(r)||Ce.text.hasOwnProperty(r)||Qa.hasOwnProperty(r)},e.isExpandable=function(r){var n=this.macros.get(r);return n!=null?typeof n=="string"||typeof n=="function"||!n.unexpandable:j0.hasOwnProperty(r)&&!j0[r].primitive},s}(),Ja=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,dr=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9","ₐ":"a","ₑ":"e","ₕ":"h","ᵢ":"i","ⱼ":"j","ₖ":"k","ₗ":"l","ₘ":"m","ₙ":"n","ₒ":"o","ₚ":"p","ᵣ":"r","ₛ":"s","ₜ":"t","ᵤ":"u","ᵥ":"v","ₓ":"x","ᵦ":"β","ᵧ":"γ","ᵨ":"ρ","ᵩ":"ϕ","ᵪ":"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","¹":"1","²":"2","³":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9","ᴬ":"A","ᴮ":"B","ᴰ":"D","ᴱ":"E","ᴳ":"G","ᴴ":"H","ᴵ":"I","ᴶ":"J","ᴷ":"K","ᴸ":"L","ᴹ":"M","ᴺ":"N","ᴼ":"O","ᴾ":"P","ᴿ":"R","ᵀ":"T","ᵁ":"U","ⱽ":"V","ᵂ":"W","ᵃ":"a","ᵇ":"b","ᶜ":"c","ᵈ":"d","ᵉ":"e","ᶠ":"f","ᵍ":"g",ʰ:"h","ⁱ":"i",ʲ:"j","ᵏ":"k",ˡ:"l","ᵐ":"m",ⁿ:"n","ᵒ":"o","ᵖ":"p",ʳ:"r",ˢ:"s","ᵗ":"t","ᵘ":"u","ᵛ":"v",ʷ:"w",ˣ:"x",ʸ:"y","ᶻ":"z","ᵝ":"β","ᵞ":"γ","ᵟ":"δ","ᵠ":"ϕ","ᵡ":"χ","ᶿ":"θ"}),dn={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},ei={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"},ti=function(){function s(t,r){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new nl(t,r,this.mode),this.settings=r,this.leftrightDepth=0}var e=s.prototype;return e.expect=function(r,n){if(n===void 0&&(n=!0),this.fetch().text!==r)throw new y("Expected '"+r+"', got '"+this.fetch().text+"'",this.fetch());n&&this.consume()},e.consume=function(){this.nextToken=null},e.fetch=function(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken},e.switchMode=function(r){this.mode=r,this.gullet.switchMode(r)},e.parse=function(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var r=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),r}finally{this.gullet.endGroups()}},e.subparse=function(r){var n=this.nextToken;this.consume(),this.gullet.pushToken(new $0("}")),this.gullet.pushTokens(r);var a=this.parseExpression(!1);return this.expect("}"),this.nextToken=n,a},e.parseExpression=function(r,n){for(var a=[];;){this.mode==="math"&&this.consumeSpaces();var l=this.fetch();if(s.endOfExpression.indexOf(l.text)!==-1||n&&l.text===n||r&&j0[l.text]&&j0[l.text].infix)break;var c=this.parseAtom(n);if(c){if(c.type==="internal")continue}else break;a.push(c)}return this.mode==="text"&&this.formLigatures(a),this.handleInfixNodes(a)},e.handleInfixNodes=function(r){for(var n=-1,a,l=0;l<r.length;l++)if(r[l].type==="infix"){if(n!==-1)throw new y("only one infix operator per group",r[l].token);n=l,a=r[l].replaceWith}if(n!==-1&&a){var c,p,b=r.slice(0,n),S=r.slice(n+1);b.length===1&&b[0].type==="ordgroup"?c=b[0]:c={type:"ordgroup",mode:this.mode,body:b},S.length===1&&S[0].type==="ordgroup"?p=S[0]:p={type:"ordgroup",mode:this.mode,body:S};var B;return a==="\\\\abovefrac"?B=this.callFunction(a,[c,r[n],p],[]):B=this.callFunction(a,[c,p],[]),[B]}else return r},e.handleSupSubscript=function(r){var n=this.fetch(),a=n.text;this.consume(),this.consumeSpaces();var l=this.parseGroup(r);if(!l)throw new y("Expected group after '"+a+"'",n);return l},e.formatUnsupportedCmd=function(r){for(var n=[],a=0;a<r.length;a++)n.push({type:"textord",mode:"text",text:r[a]});var l={type:"text",mode:this.mode,body:n},c={type:"color",mode:this.mode,color:this.settings.errorColor,body:[l]};return c},e.parseAtom=function(r){var n=this.parseGroup("atom",r);if(this.mode==="text")return n;for(var a,l;;){this.consumeSpaces();var c=this.fetch();if(c.text==="\\limits"||c.text==="\\nolimits"){if(n&&n.type==="op"){var p=c.text==="\\limits";n.limits=p,n.alwaysHandleSupSub=!0}else if(n&&n.type==="operatorname")n.alwaysHandleSupSub&&(n.limits=c.text==="\\limits");else throw new y("Limit controls must follow a math operator",c);this.consume()}else if(c.text==="^"){if(a)throw new y("Double superscript",c);a=this.handleSupSubscript("superscript")}else if(c.text==="_"){if(l)throw new y("Double subscript",c);l=this.handleSupSubscript("subscript")}else if(c.text==="'"){if(a)throw new y("Double superscript",c);var b={type:"textord",mode:this.mode,text:"\\prime"},S=[b];for(this.consume();this.fetch().text==="'";)S.push(b),this.consume();this.fetch().text==="^"&&S.push(this.handleSupSubscript("superscript")),a={type:"ordgroup",mode:this.mode,body:S}}else if(dr[c.text]){var B=dr[c.text],R=Ja.test(c.text);for(this.consume();;){var N=this.fetch().text;if(!dr[N]||Ja.test(N)!==R)break;this.consume(),B+=dr[N]}var L=new s(B,this.settings).parse();R?l={type:"ordgroup",mode:"math",body:L}:a={type:"ordgroup",mode:"math",body:L}}else break}return a||l?{type:"supsub",mode:this.mode,base:n,sup:a,sub:l}:n},e.parseFunction=function(r,n){var a=this.fetch(),l=a.text,c=j0[l];if(!c)return null;if(this.consume(),n&&n!=="atom"&&!c.allowedInArgument)throw new y("Got function '"+l+"' with no arguments"+(n?" as "+n:""),a);if(this.mode==="text"&&!c.allowedInText)throw new y("Can't use function '"+l+"' in text mode",a);if(this.mode==="math"&&c.allowedInMath===!1)throw new y("Can't use function '"+l+"' in math mode",a);var p=this.parseArguments(l,c),b=p.args,S=p.optArgs;return this.callFunction(l,b,S,a,r)},e.callFunction=function(r,n,a,l,c){var p={funcName:r,parser:this,token:l,breakOnTokenText:c},b=j0[r];if(b&&b.handler)return b.handler(p,n,a);throw new y("No function handler for "+r)},e.parseArguments=function(r,n){var a=n.numArgs+n.numOptionalArgs;if(a===0)return{args:[],optArgs:[]};for(var l=[],c=[],p=0;p<a;p++){var b=n.argTypes&&n.argTypes[p],S=p<n.numOptionalArgs;(n.primitive&&b==null||n.type==="sqrt"&&p===1&&c[0]==null)&&(b="primitive");var B=this.parseGroupOfType("argument to '"+r+"'",b,S);if(S)c.push(B);else if(B!=null)l.push(B);else throw new y("Null argument, please report this as a bug")}return{args:l,optArgs:c}},e.parseGroupOfType=function(r,n,a){switch(n){case"color":return this.parseColorGroup(a);case"size":return this.parseSizeGroup(a);case"url":return this.parseUrlGroup(a);case"math":case"text":return this.parseArgumentGroup(a,n);case"hbox":{var l=this.parseArgumentGroup(a,"text");return l!=null?{type:"styling",mode:l.mode,body:[l],style:"text"}:null}case"raw":{var c=this.parseStringGroup("raw",a);return c!=null?{type:"raw",mode:"text",string:c.text}:null}case"primitive":{if(a)throw new y("A primitive argument cannot be optional");var p=this.parseGroup(r);if(p==null)throw new y("Expected group as "+r,this.fetch());return p}case"original":case null:case void 0:return this.parseArgumentGroup(a);default:throw new y("Unknown group type as "+r,this.fetch())}},e.consumeSpaces=function(){for(;this.fetch().text===" ";)this.consume()},e.parseStringGroup=function(r,n){var a=this.gullet.scanArgument(n);if(a==null)return null;for(var l="",c;(c=this.fetch()).text!=="EOF";)l+=c.text,this.consume();return this.consume(),a.text=l,a},e.parseRegexGroup=function(r,n){for(var a=this.fetch(),l=a,c="",p;(p=this.fetch()).text!=="EOF"&&r.test(c+p.text);)l=p,c+=l.text,this.consume();if(c==="")throw new y("Invalid "+n+": '"+a.text+"'",a);return a.range(l,c)},e.parseColorGroup=function(r){var n=this.parseStringGroup("color",r);if(n==null)return null;var a=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(n.text);if(!a)throw new y("Invalid color: '"+n.text+"'",n);var l=a[0];return/^[0-9a-f]{6}$/i.test(l)&&(l="#"+l),{type:"color-token",mode:this.mode,color:l}},e.parseSizeGroup=function(r){var n,a=!1;if(this.gullet.consumeSpaces(),!r&&this.gullet.future().text!=="{"?n=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):n=this.parseStringGroup("size",r),!n)return null;!r&&n.text.length===0&&(n.text="0pt",a=!0);var l=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(n.text);if(!l)throw new y("Invalid size: '"+n.text+"'",n);var c={number:+(l[1]+l[2]),unit:l[3]};if(!vt(c))throw new y("Invalid unit: '"+c.unit+"'",n);return{type:"size",mode:this.mode,value:c,isBlank:a}},e.parseUrlGroup=function(r){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var n=this.parseStringGroup("url",r);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),n==null)return null;var a=n.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:a}},e.parseArgumentGroup=function(r,n){var a=this.gullet.scanArgument(r);if(a==null)return null;var l=this.mode;n&&this.switchMode(n),this.gullet.beginGroup();var c=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var p={type:"ordgroup",mode:this.mode,loc:a.loc,body:c};return n&&this.switchMode(l),p},e.parseGroup=function(r,n){var a=this.fetch(),l=a.text,c;if(l==="{"||l==="\\begingroup"){this.consume();var p=l==="{"?"}":"\\endgroup";this.gullet.beginGroup();var b=this.parseExpression(!1,p),S=this.fetch();this.expect(p),this.gullet.endGroup(),c={type:"ordgroup",mode:this.mode,loc:x0.range(a,S),body:b,semisimple:l==="\\begingroup"||void 0}}else if(c=this.parseFunction(n,r)||this.parseSymbol(),c==null&&l[0]==="\\"&&!Qa.hasOwnProperty(l)){if(this.settings.throwOnError)throw new y("Undefined control sequence: "+l,a);c=this.formatUnsupportedCmd(l),this.consume()}return c},e.formLigatures=function(r){for(var n=r.length-1,a=0;a<n;++a){var l=r[a],c=l.text;c==="-"&&r[a+1].text==="-"&&(a+1<n&&r[a+2].text==="-"?(r.splice(a,3,{type:"textord",mode:"text",loc:x0.range(l,r[a+2]),text:"---"}),n-=2):(r.splice(a,2,{type:"textord",mode:"text",loc:x0.range(l,r[a+1]),text:"--"}),n-=1)),(c==="'"||c==="`")&&r[a+1].text===c&&(r.splice(a,2,{type:"textord",mode:"text",loc:x0.range(l,r[a+1]),text:c+c}),n-=1)}},e.parseSymbol=function(){var r=this.fetch(),n=r.text;if(/^\\verb[^a-zA-Z]/.test(n)){this.consume();var a=n.slice(5),l=a.charAt(0)==="*";if(l&&(a=a.slice(1)),a.length<2||a.charAt(0)!==a.slice(-1))throw new y(`\\verb assertion failed --
                    please report what input caused this bug`);return a=a.slice(1,-1),{type:"verb",mode:"text",body:a,star:l}}ei.hasOwnProperty(n[0])&&!Ce[this.mode][n[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+n[0]+'" used in math mode',r),n=ei[n[0]]+n.slice(1));var c=Qs.exec(n);c&&(n=n.substring(0,c.index),n==="i"?n="ı":n==="j"&&(n="ȷ"));var p;if(Ce[this.mode][n]){this.settings.strict&&this.mode==="math"&&Lr.indexOf(n)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+n[0]+'" used in math mode',r);var b=Ce[this.mode][n].group,S=x0.range(r),B;if(C0.hasOwnProperty(b)){var R=b;B={type:"atom",mode:this.mode,family:R,loc:S,text:n}}else B={type:b,mode:this.mode,loc:S,text:n};p=B}else if(n.charCodeAt(0)>=128)this.settings.strict&&(o0(n.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+n[0]+'" used in math mode',r):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+n[0]+'"'+(" ("+n.charCodeAt(0)+")"),r)),p={type:"textord",mode:"text",loc:x0.range(r),text:n};else return null;if(this.consume(),c)for(var N=0;N<c[0].length;N++){var L=c[0][N];if(!dn[L])throw new y("Unknown accent ' "+L+"'",r);var G=dn[L][this.mode]||dn[L].text;if(!G)throw new y("Accent "+L+" unsupported in "+this.mode+" mode",r);p={type:"accent",mode:this.mode,loc:x0.range(r),label:G,isStretchy:!1,isShifty:!0,base:p}}return p},s}();ti.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var al=function(e,t){if(!(typeof e=="string"||e instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var r=new ti(e,t);delete r.gullet.macros.current["\\df@tag"];var n=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new y("\\tag works only in display equations");n=[{type:"tag",mode:"text",body:n,tag:r.subparse([new $0("\\df@tag")])}]}return n},fn=al,ri=function(e,t,r){t.textContent="";var n=pn(e,r).toNode();t.appendChild(n)};typeof document<"u"&&document.compatMode!=="CSS1Compat"&&(typeof console<"u"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),ri=function(){throw new y("KaTeX doesn't work in quirks mode.")});var il=function(e,t){var r=pn(e,t).toMarkup();return r},sl=function(e,t){var r=new F(t);return fn(e,r)},ni=function(e,t,r){if(r.throwOnError||!(e instanceof y))throw e;var n=E.makeSpan(["katex-error"],[new Ze(t)]);return n.setAttribute("title",e.toString()),n.setAttribute("style","color:"+r.errorColor),n},pn=function(e,t){var r=new F(t);try{var n=fn(e,r);return cs(n,e,r)}catch(a){return ni(a,e,r)}},ll=function(e,t){var r=new F(t);try{var n=fn(e,r);return ms(n,e,r)}catch(a){return ni(a,e,r)}},ol={version:"0.16.7",render:ri,renderToString:il,ParseError:y,SETTINGS_SCHEMA:_,__parse:sl,__renderToDomTree:pn,__renderToHTMLTree:ll,__setFontMetrics:H0,__defineSymbol:i,__defineFunction:Q,__defineMacro:g,__domTree:{Span:c0,Anchor:Nt,SymbolNode:Ze,SvgNode:m0,PathNode:T0,LineNode:G0}},ul=ol;return m=m.default,m}()})}(Tn)),Tn.exports}(function(f,u){(function(m,v){f.exports=v(jl())})(typeof self<"u"?self:Ar,function(h){return function(){var m={771:function(T){T.exports=h}},v={};function y(T){var C=v[T];if(C!==void 0)return C.exports;var P=v[T]={exports:{}};return m[T](P,P.exports,y),P.exports}(function(){y.n=function(T){var C=T&&T.__esModule?function(){return T.default}:function(){return T};return y.d(C,{a:C}),C}})(),function(){y.d=function(T,C){for(var P in C)y.o(C,P)&&!y.o(T,P)&&Object.defineProperty(T,P,{enumerable:!0,get:C[P]})}}(),function(){y.o=function(T,C){return Object.prototype.hasOwnProperty.call(T,C)}}();var z={};return function(){y.d(z,{default:function(){return Y}});var T=y(771),C=y.n(T),P=function(M,F,D){for(var I=D,j=0,ne=M.length;I<F.length;){var $=F[I];if(j<=0&&F.slice(I,I+ne)===M)return I;$==="\\"?I++:$==="{"?j++:$==="}"&&j--,I++}return-1},X=function(M){return M.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},ce=/^\\begin{/,te=function(M,F){for(var D,I=[],j=new RegExp("("+F.map(function(ge){return X(ge.left)}).join("|")+")");D=M.search(j),D!==-1;){D>0&&(I.push({type:"text",data:M.slice(0,D)}),M=M.slice(D));var ne=F.findIndex(function(ge){return M.startsWith(ge.left)});if(D=P(F[ne].right,M,F[ne].left.length),D===-1)break;var $=M.slice(0,D+F[ne].right.length),me=ce.test($)?$:M.slice(F[ne].left.length,D);I.push({type:"math",data:me,rawData:$,display:F[ne].display}),M=M.slice(D+F[ne].right.length)}return M!==""&&I.push({type:"text",data:M}),I},U=te,ie=function(M,F){var D=U(M,F.delimiters);if(D.length===1&&D[0].type==="text")return null;for(var I=document.createDocumentFragment(),j=0;j<D.length;j++)if(D[j].type==="text")I.appendChild(document.createTextNode(D[j].data));else{var ne=document.createElement("span"),$=D[j].data;F.displayMode=D[j].display;try{F.preProcess&&($=F.preProcess($)),C().render($,ne,F)}catch(me){if(!(me instanceof C().ParseError))throw me;F.errorCallback("KaTeX auto-render: Failed to parse `"+D[j].data+"` with ",me),I.appendChild(document.createTextNode(D[j].rawData));continue}I.appendChild(ne)}return I},le=function _(M,F){for(var D=0;D<M.childNodes.length;D++){var I=M.childNodes[D];if(I.nodeType===3){for(var j=I.textContent,ne=I.nextSibling,$=0;ne&&ne.nodeType===Node.TEXT_NODE;)j+=ne.textContent,ne=ne.nextSibling,$++;var me=ie(j,F);if(me){for(var ge=0;ge<$;ge++)I.nextSibling.remove();D+=me.childNodes.length-1,M.replaceChild(me,I)}else D+=$}else I.nodeType===1&&function(){var Le=" "+I.className+" ",Ne=F.ignoredTags.indexOf(I.nodeName.toLowerCase())===-1&&F.ignoredClasses.every(function(We){return Le.indexOf(" "+We+" ")===-1});Ne&&_(I,F)}()}},oe=function(M,F){if(!M)throw new Error("No element provided to render");var D={};for(var I in F)F.hasOwnProperty(I)&&(D[I]=F[I]);D.delimiters=D.delimiters||[{left:"$$",right:"$$",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"\\begin{equation}",right:"\\end{equation}",display:!0},{left:"\\begin{align}",right:"\\end{align}",display:!0},{left:"\\begin{alignat}",right:"\\end{alignat}",display:!0},{left:"\\begin{gather}",right:"\\end{gather}",display:!0},{left:"\\begin{CD}",right:"\\end{CD}",display:!0},{left:"\\[",right:"\\]",display:!0}],D.ignoredTags=D.ignoredTags||["script","noscript","style","textarea","pre","code","option"],D.ignoredClasses=D.ignoredClasses||[],D.errorCallback=D.errorCallback||console.error,D.macros=D.macros||{},le(M,D)},Y=oe}(),z=z.default,z}()})})(Ni);var Xl=Ni.exports;const Zl=Ei(Xl);var Kl=(f,u,h)=>{if(!u.has(f))throw TypeError("Cannot "+h)},vi=(f,u,h)=>{if(u.has(f))throw TypeError("Cannot add the same private member more than once");u instanceof WeakSet?u.add(f):u.set(f,h)},Cn=(f,u,h)=>(Kl(f,u,"access private method"),h);function On(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!1,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!1,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}var mt=On();function Ri(f){mt=f}var Fi=/[&<>"']/,Ql=new RegExp(Fi.source,"g"),Ii=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Jl=new RegExp(Ii.source,"g"),eo={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},bi=f=>eo[f];function Xe(f,u){if(u){if(Fi.test(f))return f.replace(Ql,bi)}else if(Ii.test(f))return f.replace(Jl,bi);return f}var to=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function Li(f){return f.replace(to,(u,h)=>(h=h.toLowerCase(),h==="colon"?":":h.charAt(0)==="#"?h.charAt(1)==="x"?String.fromCharCode(parseInt(h.substring(2),16)):String.fromCharCode(+h.substring(1)):""))}var ro=/(^|[^\[])\^/g;function Ae(f,u){f=typeof f=="string"?f:f.source,u=u||"";const h={replace:(m,v)=>(v=typeof v=="object"&&"source"in v?v.source:v,v=v.replace(ro,"$1"),f=f.replace(m,v),h),getRegex:()=>new RegExp(f,u)};return h}var no=/[^\w:]/g,ao=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function yi(f,u,h){if(f){let m;try{m=decodeURIComponent(Li(h)).replace(no,"").toLowerCase()}catch{return null}if(m.indexOf("javascript:")===0||m.indexOf("vbscript:")===0||m.indexOf("data:")===0)return null}u&&!ao.test(h)&&(h=oo(u,h));try{h=encodeURI(h).replace(/%25/g,"%")}catch{return null}return h}var xr={},io=/^[^:]+:\/*[^/]*$/,so=/^([^:]+:)[\s\S]*$/,lo=/^([^:]+:\/*[^/]*)[\s\S]*$/;function oo(f,u){xr[" "+f]||(io.test(f)?xr[" "+f]=f+"/":xr[" "+f]=kr(f,"/",!0)),f=xr[" "+f];const h=f.indexOf(":")===-1;return u.substring(0,2)==="//"?h?u:f.replace(so,"$1")+u:u.charAt(0)==="/"?h?u:f.replace(lo,"$1")+u:f+u}var Cr={exec:()=>null};function xi(f,u){const h=f.replace(/\|/g,(y,z,T)=>{let C=!1,P=z;for(;--P>=0&&T[P]==="\\";)C=!C;return C?"|":" |"}),m=h.split(/ \|/);let v=0;if(m[0].trim()||m.shift(),m.length>0&&!m[m.length-1].trim()&&m.pop(),m.length>u)m.splice(u);else for(;m.length<u;)m.push("");for(;v<m.length;v++)m[v]=m[v].trim().replace(/\\\|/g,"|");return m}function kr(f,u,h){const m=f.length;if(m===0)return"";let v=0;for(;v<m;){const y=f.charAt(m-v-1);if(y===u&&!h)v++;else if(y!==u&&h)v++;else break}return f.slice(0,m-v)}function uo(f,u){if(f.indexOf(u[1])===-1)return-1;const h=f.length;let m=0,v=0;for(;v<h;v++)if(f[v]==="\\")v++;else if(f[v]===u[0])m++;else if(f[v]===u[1]&&(m--,m<0))return v;return-1}function ho(f,u){!f||f.silent||(u&&console.warn("marked(): callback is deprecated since version 5.0.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/using_pro#async"),(f.sanitize||f.sanitizer)&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options"),(f.highlight||f.langPrefix!=="language-")&&console.warn("marked(): highlight and langPrefix parameters are deprecated since version 5.0.0, should not be used and will be removed in the future. Instead use https://www.npmjs.com/package/marked-highlight."),f.mangle&&console.warn("marked(): mangle parameter is enabled by default, but is deprecated since version 5.0.0, and will be removed in the future. To clear this warning, install https://www.npmjs.com/package/marked-mangle, or disable by setting `{mangle: false}`."),f.baseUrl&&console.warn("marked(): baseUrl parameter is deprecated since version 5.0.0, should not be used and will be removed in the future. Instead use https://www.npmjs.com/package/marked-base-url."),f.smartypants&&console.warn("marked(): smartypants parameter is deprecated since version 5.0.0, should not be used and will be removed in the future. Instead use https://www.npmjs.com/package/marked-smartypants."),f.xhtml&&console.warn("marked(): xhtml parameter is deprecated since version 5.0.0, should not be used and will be removed in the future. Instead use https://www.npmjs.com/package/marked-xhtml."),(f.headerIds||f.headerPrefix)&&console.warn("marked(): headerIds and headerPrefix parameters enabled by default, but are deprecated since version 5.0.0, and will be removed in the future. To clear this warning, install  https://www.npmjs.com/package/marked-gfm-heading-id, or disable by setting `{headerIds: false}`."))}function wi(f,u,h,m){const v=u.href,y=u.title?Xe(u.title):null,z=f[1].replace(/\\([\[\]])/g,"$1");if(f[0].charAt(0)!=="!"){m.state.inLink=!0;const T={type:"link",raw:h,href:v,title:y,text:z,tokens:m.inlineTokens(z)};return m.state.inLink=!1,T}return{type:"image",raw:h,href:v,title:y,text:Xe(z)}}function co(f,u){const h=f.match(/^(\s+)(?:```)/);if(h===null)return u;const m=h[1];return u.split(`
`).map(v=>{const y=v.match(/^\s+/);if(y===null)return v;const[z]=y;return z.length>=m.length?v.slice(m.length):v}).join(`
`)}var Dr=class{constructor(f){this.options=f||mt}space(f){const u=this.rules.block.newline.exec(f);if(u&&u[0].length>0)return{type:"space",raw:u[0]}}code(f){const u=this.rules.block.code.exec(f);if(u){const h=u[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:u[0],codeBlockStyle:"indented",text:this.options.pedantic?h:kr(h,`
`)}}}fences(f){const u=this.rules.block.fences.exec(f);if(u){const h=u[0],m=co(h,u[3]||"");return{type:"code",raw:h,lang:u[2]?u[2].trim().replace(this.rules.inline._escapes,"$1"):u[2],text:m}}}heading(f){const u=this.rules.block.heading.exec(f);if(u){let h=u[2].trim();if(/#$/.test(h)){const m=kr(h,"#");(this.options.pedantic||!m||/ $/.test(m))&&(h=m.trim())}return{type:"heading",raw:u[0],depth:u[1].length,text:h,tokens:this.lexer.inline(h)}}}hr(f){const u=this.rules.block.hr.exec(f);if(u)return{type:"hr",raw:u[0]}}blockquote(f){const u=this.rules.block.blockquote.exec(f);if(u){const h=u[0].replace(/^ *>[ \t]?/gm,""),m=this.lexer.state.top;this.lexer.state.top=!0;const v=this.lexer.blockTokens(h);return this.lexer.state.top=m,{type:"blockquote",raw:u[0],tokens:v,text:h}}}list(f){let u=this.rules.block.list.exec(f);if(u){let h,m,v,y,z,T,C,P,X,ce,te,U,ie=u[1].trim();const le=ie.length>1,oe={type:"list",raw:"",ordered:le,start:le?+ie.slice(0,-1):"",loose:!1,items:[]};ie=le?`\\d{1,9}\\${ie.slice(-1)}`:`\\${ie}`,this.options.pedantic&&(ie=le?ie:"[*+-]");const Y=new RegExp(`^( {0,3}${ie})((?:[	 ][^\\n]*)?(?:\\n|$))`);for(;f&&(U=!1,!(!(u=Y.exec(f))||this.rules.block.hr.test(f)));){if(h=u[0],f=f.substring(h.length),P=u[2].split(`
`,1)[0].replace(/^\t+/,M=>" ".repeat(3*M.length)),X=f.split(`
`,1)[0],this.options.pedantic?(y=2,te=P.trimLeft()):(y=u[2].search(/[^ ]/),y=y>4?1:y,te=P.slice(y),y+=u[1].length),T=!1,!P&&/^ *$/.test(X)&&(h+=X+`
`,f=f.substring(X.length+1),U=!0),!U){const M=new RegExp(`^ {0,${Math.min(3,y-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),F=new RegExp(`^ {0,${Math.min(3,y-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),D=new RegExp(`^ {0,${Math.min(3,y-1)}}(?:\`\`\`|~~~)`),I=new RegExp(`^ {0,${Math.min(3,y-1)}}#`);for(;f&&(ce=f.split(`
`,1)[0],X=ce,this.options.pedantic&&(X=X.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!(D.test(X)||I.test(X)||M.test(X)||F.test(f)));){if(X.search(/[^ ]/)>=y||!X.trim())te+=`
`+X.slice(y);else{if(T||P.search(/[^ ]/)>=4||D.test(P)||I.test(P)||F.test(P))break;te+=`
`+X}!T&&!X.trim()&&(T=!0),h+=ce+`
`,f=f.substring(ce.length+1),P=X.slice(y)}}oe.loose||(C?oe.loose=!0:/\n *\n *$/.test(h)&&(C=!0)),this.options.gfm&&(m=/^\[[ xX]\] /.exec(te),m&&(v=m[0]!=="[ ] ",te=te.replace(/^\[[ xX]\] +/,""))),oe.items.push({type:"list_item",raw:h,task:!!m,checked:v,loose:!1,text:te}),oe.raw+=h}oe.items[oe.items.length-1].raw=h.trimRight(),oe.items[oe.items.length-1].text=te.trimRight(),oe.raw=oe.raw.trimRight();const _=oe.items.length;for(z=0;z<_;z++)if(this.lexer.state.top=!1,oe.items[z].tokens=this.lexer.blockTokens(oe.items[z].text,[]),!oe.loose){const M=oe.items[z].tokens.filter(D=>D.type==="space"),F=M.length>0&&M.some(D=>/\n.*\n/.test(D.raw));oe.loose=F}if(oe.loose)for(z=0;z<_;z++)oe.items[z].loose=!0;return oe}}html(f){const u=this.rules.block.html.exec(f);if(u){const h={type:"html",block:!0,raw:u[0],pre:!this.options.sanitizer&&(u[1]==="pre"||u[1]==="script"||u[1]==="style"),text:u[0]};if(this.options.sanitize){const m=this.options.sanitizer?this.options.sanitizer(u[0]):Xe(u[0]),v=h;v.type="paragraph",v.text=m,v.tokens=this.lexer.inline(m)}return h}}def(f){const u=this.rules.block.def.exec(f);if(u){const h=u[1].toLowerCase().replace(/\s+/g," "),m=u[2]?u[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",v=u[3]?u[3].substring(1,u[3].length-1).replace(this.rules.inline._escapes,"$1"):u[3];return{type:"def",tag:h,raw:u[0],href:m,title:v}}}table(f){const u=this.rules.block.table.exec(f);if(u){const h={type:"table",header:xi(u[1]).map(m=>({text:m})),align:u[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:u[3]&&u[3].trim()?u[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(h.header.length===h.align.length){h.raw=u[0];let m=h.align.length,v,y,z,T;for(v=0;v<m;v++)/^ *-+: *$/.test(h.align[v])?h.align[v]="right":/^ *:-+: *$/.test(h.align[v])?h.align[v]="center":/^ *:-+ *$/.test(h.align[v])?h.align[v]="left":h.align[v]=null;for(m=h.rows.length,v=0;v<m;v++)h.rows[v]=xi(h.rows[v],h.header.length).map(C=>({text:C}));for(m=h.header.length,y=0;y<m;y++)h.header[y].tokens=this.lexer.inline(h.header[y].text);for(m=h.rows.length,y=0;y<m;y++)for(T=h.rows[y],z=0;z<T.length;z++)T[z].tokens=this.lexer.inline(T[z].text);return h}}}lheading(f){const u=this.rules.block.lheading.exec(f);if(u)return{type:"heading",raw:u[0],depth:u[2].charAt(0)==="="?1:2,text:u[1],tokens:this.lexer.inline(u[1])}}paragraph(f){const u=this.rules.block.paragraph.exec(f);if(u){const h=u[1].charAt(u[1].length-1)===`
`?u[1].slice(0,-1):u[1];return{type:"paragraph",raw:u[0],text:h,tokens:this.lexer.inline(h)}}}text(f){const u=this.rules.block.text.exec(f);if(u)return{type:"text",raw:u[0],text:u[0],tokens:this.lexer.inline(u[0])}}escape(f){const u=this.rules.inline.escape.exec(f);if(u)return{type:"escape",raw:u[0],text:Xe(u[1])}}tag(f){const u=this.rules.inline.tag.exec(f);if(u)return!this.lexer.state.inLink&&/^<a /i.test(u[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(u[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(u[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(u[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:u[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(u[0]):Xe(u[0]):u[0]}}link(f){const u=this.rules.inline.link.exec(f);if(u){const h=u[2].trim();if(!this.options.pedantic&&/^</.test(h)){if(!/>$/.test(h))return;const y=kr(h.slice(0,-1),"\\");if((h.length-y.length)%2===0)return}else{const y=uo(u[2],"()");if(y>-1){const T=(u[0].indexOf("!")===0?5:4)+u[1].length+y;u[2]=u[2].substring(0,y),u[0]=u[0].substring(0,T).trim(),u[3]=""}}let m=u[2],v="";if(this.options.pedantic){const y=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(m);y&&(m=y[1],v=y[3])}else v=u[3]?u[3].slice(1,-1):"";return m=m.trim(),/^</.test(m)&&(this.options.pedantic&&!/>$/.test(h)?m=m.slice(1):m=m.slice(1,-1)),wi(u,{href:m&&m.replace(this.rules.inline._escapes,"$1"),title:v&&v.replace(this.rules.inline._escapes,"$1")},u[0],this.lexer)}}reflink(f,u){let h;if((h=this.rules.inline.reflink.exec(f))||(h=this.rules.inline.nolink.exec(f))){let m=(h[2]||h[1]).replace(/\s+/g," ");if(m=u[m.toLowerCase()],!m){const v=h[0].charAt(0);return{type:"text",raw:v,text:v}}return wi(h,m,h[0],this.lexer)}}emStrong(f,u,h=""){let m=this.rules.inline.emStrong.lDelim.exec(f);if(!m||m[3]&&h.match(/[\p{L}\p{N}]/u))return;if(!(m[1]||m[2]||"")||!h||this.rules.inline.punctuation.exec(h)){const y=m[0].length-1;let z,T,C=y,P=0;const X=m[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(X.lastIndex=0,u=u.slice(-1*f.length+y);(m=X.exec(u))!=null;){if(z=m[1]||m[2]||m[3]||m[4]||m[5]||m[6],!z)continue;if(T=z.length,m[3]||m[4]){C+=T;continue}else if((m[5]||m[6])&&y%3&&!((y+T)%3)){P+=T;continue}if(C-=T,C>0)continue;T=Math.min(T,T+C+P);const ce=f.slice(0,y+m.index+T+1);if(Math.min(y,T)%2){const U=ce.slice(1,-1);return{type:"em",raw:ce,text:U,tokens:this.lexer.inlineTokens(U)}}const te=ce.slice(2,-2);return{type:"strong",raw:ce,text:te,tokens:this.lexer.inlineTokens(te)}}}}codespan(f){const u=this.rules.inline.code.exec(f);if(u){let h=u[2].replace(/\n/g," ");const m=/[^ ]/.test(h),v=/^ /.test(h)&&/ $/.test(h);return m&&v&&(h=h.substring(1,h.length-1)),h=Xe(h,!0),{type:"codespan",raw:u[0],text:h}}}br(f){const u=this.rules.inline.br.exec(f);if(u)return{type:"br",raw:u[0]}}del(f){const u=this.rules.inline.del.exec(f);if(u)return{type:"del",raw:u[0],text:u[2],tokens:this.lexer.inlineTokens(u[2])}}autolink(f,u){const h=this.rules.inline.autolink.exec(f);if(h){let m,v;return h[2]==="@"?(m=Xe(this.options.mangle?u(h[1]):h[1]),v="mailto:"+m):(m=Xe(h[1]),v=m),{type:"link",raw:h[0],text:m,href:v,tokens:[{type:"text",raw:m,text:m}]}}}url(f,u){let h;if(h=this.rules.inline.url.exec(f)){let m,v;if(h[2]==="@")m=Xe(this.options.mangle?u(h[0]):h[0]),v="mailto:"+m;else{let y;do y=h[0],h[0]=this.rules.inline._backpedal.exec(h[0])[0];while(y!==h[0]);m=Xe(h[0]),h[1]==="www."?v="http://"+h[0]:v=h[0]}return{type:"link",raw:h[0],text:m,href:v,tokens:[{type:"text",raw:m,text:m}]}}}inlineText(f,u){const h=this.rules.inline.text.exec(f);if(h){let m;return this.lexer.state.inRawBlock?m=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(h[0]):Xe(h[0]):h[0]:m=Xe(this.options.smartypants?u(h[0]):h[0]),{type:"text",raw:h[0],text:m}}}},ue={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:Cr,lheading:/^((?:(?!^bull ).|\n(?!\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};ue._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;ue._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;ue.def=Ae(ue.def).replace("label",ue._label).replace("title",ue._title).getRegex();ue.bullet=/(?:[*+-]|\d{1,9}[.)])/;ue.listItemStart=Ae(/^( *)(bull) */).replace("bull",ue.bullet).getRegex();ue.list=Ae(ue.list).replace(/bull/g,ue.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+ue.def.source+")").getRegex();ue._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";ue._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;ue.html=Ae(ue.html,"i").replace("comment",ue._comment).replace("tag",ue._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();ue.lheading=Ae(ue.lheading).replace(/bull/g,ue.bullet).getRegex();ue.paragraph=Ae(ue._paragraph).replace("hr",ue.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ue._tag).getRegex();ue.blockquote=Ae(ue.blockquote).replace("paragraph",ue.paragraph).getRegex();ue.normal={...ue};ue.gfm={...ue.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"};ue.gfm.table=Ae(ue.gfm.table).replace("hr",ue.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ue._tag).getRegex();ue.gfm.paragraph=Ae(ue._paragraph).replace("hr",ue.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",ue.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ue._tag).getRegex();ue.pedantic={...ue.normal,html:Ae(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",ue._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Cr,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Ae(ue.normal._paragraph).replace("hr",ue.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",ue.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};var ee={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:Cr,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,rDelimAst:/^[^_*]*?__[^_*]*?\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\*)[punct](\*+)(?=[\s]|$)|[^punct\s](\*+)(?!\*)(?=[punct\s]|$)|(?!\*)[punct\s](\*+)(?=[^punct\s])|[\s](\*+)(?!\*)(?=[punct])|(?!\*)[punct](\*+)(?!\*)(?=[punct])|[^punct\s](\*+)(?=[^punct\s])/,rDelimUnd:/^[^_*]*?\*\*[^_*]*?_[^_*]*?(?=\*\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\s]|$)|[^punct\s](_+)(?!_)(?=[punct\s]|$)|(?!_)[punct\s](_+)(?=[^punct\s])|[\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:Cr,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^((?![*_])[\spunctuation])/};ee._punctuation="\\p{P}$+<=>`^|~";ee.punctuation=Ae(ee.punctuation,"u").replace(/punctuation/g,ee._punctuation).getRegex();ee.blockSkip=/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g;ee.anyPunctuation=/\\[punct]/g;ee._escapes=/\\([punct])/g;ee._comment=Ae(ue._comment).replace("(?:-->|$)","-->").getRegex();ee.emStrong.lDelim=Ae(ee.emStrong.lDelim,"u").replace(/punct/g,ee._punctuation).getRegex();ee.emStrong.rDelimAst=Ae(ee.emStrong.rDelimAst,"gu").replace(/punct/g,ee._punctuation).getRegex();ee.emStrong.rDelimUnd=Ae(ee.emStrong.rDelimUnd,"gu").replace(/punct/g,ee._punctuation).getRegex();ee.anyPunctuation=Ae(ee.anyPunctuation,"gu").replace(/punct/g,ee._punctuation).getRegex();ee._escapes=Ae(ee._escapes,"gu").replace(/punct/g,ee._punctuation).getRegex();ee._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;ee._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;ee.autolink=Ae(ee.autolink).replace("scheme",ee._scheme).replace("email",ee._email).getRegex();ee._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;ee.tag=Ae(ee.tag).replace("comment",ee._comment).replace("attribute",ee._attribute).getRegex();ee._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;ee._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;ee._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;ee.link=Ae(ee.link).replace("label",ee._label).replace("href",ee._href).replace("title",ee._title).getRegex();ee.reflink=Ae(ee.reflink).replace("label",ee._label).replace("ref",ue._label).getRegex();ee.nolink=Ae(ee.nolink).replace("ref",ue._label).getRegex();ee.reflinkSearch=Ae(ee.reflinkSearch,"g").replace("reflink",ee.reflink).replace("nolink",ee.nolink).getRegex();ee.normal={...ee};ee.pedantic={...ee.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:Ae(/^!?\[(label)\]\((.*?)\)/).replace("label",ee._label).getRegex(),reflink:Ae(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",ee._label).getRegex()};ee.gfm={...ee.normal,escape:Ae(ee.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};ee.gfm.url=Ae(ee.gfm.url,"i").replace("email",ee.gfm._extended_email).getRegex();ee.breaks={...ee.gfm,br:Ae(ee.br).replace("{2,}","*").getRegex(),text:Ae(ee.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};function mo(f){return f.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function ki(f){let u="",h,m;const v=f.length;for(h=0;h<v;h++)m=f.charCodeAt(h),Math.random()>.5&&(m="x"+m.toString(16)),u+="&#"+m+";";return u}var ut=class Dn{constructor(u){this.tokens=[],this.tokens.links=Object.create(null),this.options=u||mt,this.options.tokenizer=this.options.tokenizer||new Dr,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const h={block:ue.normal,inline:ee.normal};this.options.pedantic?(h.block=ue.pedantic,h.inline=ee.pedantic):this.options.gfm&&(h.block=ue.gfm,this.options.breaks?h.inline=ee.breaks:h.inline=ee.gfm),this.tokenizer.rules=h}static get rules(){return{block:ue,inline:ee}}static lex(u,h){return new Dn(h).lex(u)}static lexInline(u,h){return new Dn(h).inlineTokens(u)}lex(u){u=u.replace(/\r\n|\r/g,`
`),this.blockTokens(u,this.tokens);let h;for(;h=this.inlineQueue.shift();)this.inlineTokens(h.src,h.tokens);return this.tokens}blockTokens(u,h=[]){this.options.pedantic?u=u.replace(/\t/g,"    ").replace(/^ +$/gm,""):u=u.replace(/^( *)(\t+)/gm,(T,C,P)=>C+"    ".repeat(P.length));let m,v,y,z;for(;u;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(T=>(m=T.call({lexer:this},u,h))?(u=u.substring(m.raw.length),h.push(m),!0):!1))){if(m=this.tokenizer.space(u)){u=u.substring(m.raw.length),m.raw.length===1&&h.length>0?h[h.length-1].raw+=`
`:h.push(m);continue}if(m=this.tokenizer.code(u)){u=u.substring(m.raw.length),v=h[h.length-1],v&&(v.type==="paragraph"||v.type==="text")?(v.raw+=`
`+m.raw,v.text+=`
`+m.text,this.inlineQueue[this.inlineQueue.length-1].src=v.text):h.push(m);continue}if(m=this.tokenizer.fences(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.heading(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.hr(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.blockquote(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.list(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.html(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.def(u)){u=u.substring(m.raw.length),v=h[h.length-1],v&&(v.type==="paragraph"||v.type==="text")?(v.raw+=`
`+m.raw,v.text+=`
`+m.raw,this.inlineQueue[this.inlineQueue.length-1].src=v.text):this.tokens.links[m.tag]||(this.tokens.links[m.tag]={href:m.href,title:m.title});continue}if(m=this.tokenizer.table(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.lheading(u)){u=u.substring(m.raw.length),h.push(m);continue}if(y=u,this.options.extensions&&this.options.extensions.startBlock){let T=1/0;const C=u.slice(1);let P;this.options.extensions.startBlock.forEach(X=>{P=X.call({lexer:this},C),typeof P=="number"&&P>=0&&(T=Math.min(T,P))}),T<1/0&&T>=0&&(y=u.substring(0,T+1))}if(this.state.top&&(m=this.tokenizer.paragraph(y))){v=h[h.length-1],z&&v.type==="paragraph"?(v.raw+=`
`+m.raw,v.text+=`
`+m.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=v.text):h.push(m),z=y.length!==u.length,u=u.substring(m.raw.length);continue}if(m=this.tokenizer.text(u)){u=u.substring(m.raw.length),v=h[h.length-1],v&&v.type==="text"?(v.raw+=`
`+m.raw,v.text+=`
`+m.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=v.text):h.push(m);continue}if(u){const T="Infinite loop on byte: "+u.charCodeAt(0);if(this.options.silent){console.error(T);break}else throw new Error(T)}}return this.state.top=!0,h}inline(u,h=[]){return this.inlineQueue.push({src:u,tokens:h}),h}inlineTokens(u,h=[]){let m,v,y,z=u,T,C,P;if(this.tokens.links){const X=Object.keys(this.tokens.links);if(X.length>0)for(;(T=this.tokenizer.rules.inline.reflinkSearch.exec(z))!=null;)X.includes(T[0].slice(T[0].lastIndexOf("[")+1,-1))&&(z=z.slice(0,T.index)+"["+"a".repeat(T[0].length-2)+"]"+z.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(T=this.tokenizer.rules.inline.blockSkip.exec(z))!=null;)z=z.slice(0,T.index)+"["+"a".repeat(T[0].length-2)+"]"+z.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(T=this.tokenizer.rules.inline.anyPunctuation.exec(z))!=null;)z=z.slice(0,T.index)+"++"+z.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;u;)if(C||(P=""),C=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(X=>(m=X.call({lexer:this},u,h))?(u=u.substring(m.raw.length),h.push(m),!0):!1))){if(m=this.tokenizer.escape(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.tag(u)){u=u.substring(m.raw.length),v=h[h.length-1],v&&m.type==="text"&&v.type==="text"?(v.raw+=m.raw,v.text+=m.text):h.push(m);continue}if(m=this.tokenizer.link(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.reflink(u,this.tokens.links)){u=u.substring(m.raw.length),v=h[h.length-1],v&&m.type==="text"&&v.type==="text"?(v.raw+=m.raw,v.text+=m.text):h.push(m);continue}if(m=this.tokenizer.emStrong(u,z,P)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.codespan(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.br(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.del(u)){u=u.substring(m.raw.length),h.push(m);continue}if(m=this.tokenizer.autolink(u,ki)){u=u.substring(m.raw.length),h.push(m);continue}if(!this.state.inLink&&(m=this.tokenizer.url(u,ki))){u=u.substring(m.raw.length),h.push(m);continue}if(y=u,this.options.extensions&&this.options.extensions.startInline){let X=1/0;const ce=u.slice(1);let te;this.options.extensions.startInline.forEach(U=>{te=U.call({lexer:this},ce),typeof te=="number"&&te>=0&&(X=Math.min(X,te))}),X<1/0&&X>=0&&(y=u.substring(0,X+1))}if(m=this.tokenizer.inlineText(y,mo)){u=u.substring(m.raw.length),m.raw.slice(-1)!=="_"&&(P=m.raw.slice(-1)),C=!0,v=h[h.length-1],v&&v.type==="text"?(v.raw+=m.raw,v.text+=m.text):h.push(m);continue}if(u){const X="Infinite loop on byte: "+u.charCodeAt(0);if(this.options.silent){console.error(X);break}else throw new Error(X)}}return h}},_r=class{constructor(f){this.options=f||mt}code(f,u,h){const m=(u||"").match(/\S*/)[0];if(this.options.highlight){const v=this.options.highlight(f,m);v!=null&&v!==f&&(h=!0,f=v)}return f=f.replace(/\n$/,"")+`
`,m?'<pre><code class="'+this.options.langPrefix+Xe(m)+'">'+(h?f:Xe(f,!0))+`</code></pre>
`:"<pre><code>"+(h?f:Xe(f,!0))+`</code></pre>
`}blockquote(f){return`<blockquote>
${f}</blockquote>
`}html(f,u){return f}heading(f,u,h,m){if(this.options.headerIds){const v=this.options.headerPrefix+m.slug(h);return`<h${u} id="${v}">${f}</h${u}>
`}return`<h${u}>${f}</h${u}>
`}hr(){return this.options.xhtml?`<hr/>
`:`<hr>
`}list(f,u,h){const m=u?"ol":"ul",v=u&&h!==1?' start="'+h+'"':"";return"<"+m+v+`>
`+f+"</"+m+`>
`}listitem(f,u,h){return`<li>${f}</li>
`}checkbox(f){return"<input "+(f?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(f){return`<p>${f}</p>
`}table(f,u){return u&&(u=`<tbody>${u}</tbody>`),`<table>
<thead>
`+f+`</thead>
`+u+`</table>
`}tablerow(f){return`<tr>
${f}</tr>
`}tablecell(f,u){const h=u.header?"th":"td";return(u.align?`<${h} align="${u.align}">`:`<${h}>`)+f+`</${h}>
`}strong(f){return`<strong>${f}</strong>`}em(f){return`<em>${f}</em>`}codespan(f){return`<code>${f}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(f){return`<del>${f}</del>`}link(f,u,h){if(f=yi(this.options.sanitize,this.options.baseUrl,f),f===null)return h;let m='<a href="'+f+'"';return u&&(m+=' title="'+u+'"'),m+=">"+h+"</a>",m}image(f,u,h){if(f=yi(this.options.sanitize,this.options.baseUrl,f),f===null)return h;let m=`<img src="${f}" alt="${h}"`;return u&&(m+=` title="${u}"`),m+=this.options.xhtml?"/>":">",m}text(f){return f}},qn=class{strong(f){return f}em(f){return f}codespan(f){return f}del(f){return f}html(f){return f}text(f){return f}link(f,u,h){return""+h}image(f,u,h){return""+h}br(){return""}},Pn=class{constructor(){this.seen={}}serialize(f){return f.toLowerCase().trim().replace(/<[!\/a-z].*?>/ig,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(f,u){let h=f,m=0;if(this.seen.hasOwnProperty(h)){m=this.seen[f];do m++,h=f+"-"+m;while(this.seen.hasOwnProperty(h))}return u||(this.seen[f]=m,this.seen[h]=0),h}slug(f,u={}){const h=this.serialize(f);return this.getNextSafeSlug(h,u.dryrun)}},ht=class _n{constructor(u){this.options=u||mt,this.options.renderer=this.options.renderer||new _r,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new qn,this.slugger=new Pn}static parse(u,h){return new _n(h).parse(u)}static parseInline(u,h){return new _n(h).parseInline(u)}parse(u,h=!0){let m="",v,y,z,T,C,P,X,ce,te,U,ie,le,oe,Y,_,M,F,D,I;const j=u.length;for(v=0;v<j;v++){if(U=u[v],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[U.type]&&(I=this.options.extensions.renderers[U.type].call({parser:this},U),I!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(U.type))){m+=I||"";continue}switch(U.type){case"space":continue;case"hr":{m+=this.renderer.hr();continue}case"heading":{m+=this.renderer.heading(this.parseInline(U.tokens),U.depth,Li(this.parseInline(U.tokens,this.textRenderer)),this.slugger);continue}case"code":{m+=this.renderer.code(U.text,U.lang,!!U.escaped);continue}case"table":{for(ce="",X="",T=U.header.length,y=0;y<T;y++)X+=this.renderer.tablecell(this.parseInline(U.header[y].tokens),{header:!0,align:U.align[y]});for(ce+=this.renderer.tablerow(X),te="",T=U.rows.length,y=0;y<T;y++){for(P=U.rows[y],X="",C=P.length,z=0;z<C;z++)X+=this.renderer.tablecell(this.parseInline(P[z].tokens),{header:!1,align:U.align[z]});te+=this.renderer.tablerow(X)}m+=this.renderer.table(ce,te);continue}case"blockquote":{te=this.parse(U.tokens),m+=this.renderer.blockquote(te);continue}case"list":{for(ie=U.ordered,le=U.start,oe=U.loose,T=U.items.length,te="",y=0;y<T;y++)_=U.items[y],M=_.checked,F=_.task,Y="",_.task&&(D=this.renderer.checkbox(!!M),oe?_.tokens.length>0&&_.tokens[0].type==="paragraph"?(_.tokens[0].text=D+" "+_.tokens[0].text,_.tokens[0].tokens&&_.tokens[0].tokens.length>0&&_.tokens[0].tokens[0].type==="text"&&(_.tokens[0].tokens[0].text=D+" "+_.tokens[0].tokens[0].text)):_.tokens.unshift({type:"text",text:D}):Y+=D),Y+=this.parse(_.tokens,oe),te+=this.renderer.listitem(Y,F,!!M);m+=this.renderer.list(te,ie,le);continue}case"html":{m+=this.renderer.html(U.text,U.block);continue}case"paragraph":{m+=this.renderer.paragraph(this.parseInline(U.tokens));continue}case"text":{for(te=U.tokens?this.parseInline(U.tokens):U.text;v+1<j&&u[v+1].type==="text";)U=u[++v],te+=`
`+(U.tokens?this.parseInline(U.tokens):U.text);m+=h?this.renderer.paragraph(te):te;continue}default:{const ne='Token with "'+U.type+'" type was not found.';if(this.options.silent)return console.error(ne),"";throw new Error(ne)}}}return m}parseInline(u,h){h=h||this.renderer;let m="",v,y,z;const T=u.length;for(v=0;v<T;v++){if(y=u[v],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[y.type]&&(z=this.options.extensions.renderers[y.type].call({parser:this},y),z!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(y.type))){m+=z||"";continue}switch(y.type){case"escape":{m+=h.text(y.text);break}case"html":{m+=h.html(y.text);break}case"link":{m+=h.link(y.href,y.title,this.parseInline(y.tokens,h));break}case"image":{m+=h.image(y.href,y.title,y.text);break}case"strong":{m+=h.strong(this.parseInline(y.tokens,h));break}case"em":{m+=h.em(this.parseInline(y.tokens,h));break}case"codespan":{m+=h.codespan(y.text);break}case"br":{m+=h.br();break}case"del":{m+=h.del(this.parseInline(y.tokens,h));break}case"text":{m+=h.text(y.text);break}default:{const C='Token with "'+y.type+'" type was not found.';if(this.options.silent)return console.error(C),"";throw new Error(C)}}}return m}},Vt=class{constructor(f){this.options=f||mt}preprocess(f){return f}postprocess(f){return f}};Vt.passThroughHooks=new Set(["preprocess","postprocess"]);var Sr,Nn,Hn,Oi,fo=class{constructor(...f){vi(this,Sr),vi(this,Hn),this.defaults=On(),this.options=this.setOptions,this.parse=Cn(this,Sr,Nn).call(this,ut.lex,ht.parse),this.parseInline=Cn(this,Sr,Nn).call(this,ut.lexInline,ht.parseInline),this.Parser=ht,this.parser=ht.parse,this.Renderer=_r,this.TextRenderer=qn,this.Lexer=ut,this.lexer=ut.lex,this.Tokenizer=Dr,this.Slugger=Pn,this.Hooks=Vt,this.use(...f)}walkTokens(f,u){let h=[];for(const m of f)switch(h=h.concat(u.call(this,m)),m.type){case"table":{for(const v of m.header)h=h.concat(this.walkTokens(v.tokens,u));for(const v of m.rows)for(const y of v)h=h.concat(this.walkTokens(y.tokens,u));break}case"list":{h=h.concat(this.walkTokens(m.items,u));break}default:this.defaults.extensions&&this.defaults.extensions.childTokens&&this.defaults.extensions.childTokens[m.type]?this.defaults.extensions.childTokens[m.type].forEach(v=>{h=h.concat(this.walkTokens(m[v],u))}):m.tokens&&(h=h.concat(this.walkTokens(m.tokens,u)))}return h}use(...f){const u=this.defaults.extensions||{renderers:{},childTokens:{}};return f.forEach(h=>{const m={...h};if(m.async=this.defaults.async||m.async||!1,h.extensions&&(h.extensions.forEach(v=>{if(!v.name)throw new Error("extension name required");if("renderer"in v){const y=u.renderers[v.name];y?u.renderers[v.name]=function(...z){let T=v.renderer.apply(this,z);return T===!1&&(T=y.apply(this,z)),T}:u.renderers[v.name]=v.renderer}if("tokenizer"in v){if(!v.level||v.level!=="block"&&v.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");u[v.level]?u[v.level].unshift(v.tokenizer):u[v.level]=[v.tokenizer],v.start&&(v.level==="block"?u.startBlock?u.startBlock.push(v.start):u.startBlock=[v.start]:v.level==="inline"&&(u.startInline?u.startInline.push(v.start):u.startInline=[v.start]))}"childTokens"in v&&v.childTokens&&(u.childTokens[v.name]=v.childTokens)}),m.extensions=u),h.renderer){const v=this.defaults.renderer||new _r(this.defaults);for(const y in h.renderer){const z=v[y];v[y]=(...T)=>{let C=h.renderer[y].apply(v,T);return C===!1&&(C=z.apply(v,T)),C}}m.renderer=v}if(h.tokenizer){const v=this.defaults.tokenizer||new Dr(this.defaults);for(const y in h.tokenizer){const z=v[y];v[y]=(...T)=>{let C=h.tokenizer[y].apply(v,T);return C===!1&&(C=z.apply(v,T)),C}}m.tokenizer=v}if(h.hooks){const v=this.defaults.hooks||new Vt;for(const y in h.hooks){const z=v[y];Vt.passThroughHooks.has(y)?v[y]=T=>{if(this.defaults.async)return Promise.resolve(h.hooks[y].call(v,T)).then(P=>z.call(v,P));const C=h.hooks[y].call(v,T);return z.call(v,C)}:v[y]=(...T)=>{let C=h.hooks[y].apply(v,T);return C===!1&&(C=z.apply(v,T)),C}}m.hooks=v}if(h.walkTokens){const v=this.defaults.walkTokens;m.walkTokens=function(y){let z=[];return z.push(h.walkTokens.call(this,y)),v&&(z=z.concat(v.call(this,y))),z}}this.defaults={...this.defaults,...m}}),this}setOptions(f){return this.defaults={...this.defaults,...f},this}};Sr=new WeakSet;Nn=function(f,u){return(h,m,v)=>{typeof m=="function"&&(v=m,m=null);const y={...m},z={...this.defaults,...y},T=Cn(this,Hn,Oi).call(this,!!z.silent,!!z.async,v);if(typeof h>"u"||h===null)return T(new Error("marked(): input parameter is undefined or null"));if(typeof h!="string")return T(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(h)+", string expected"));if(ho(z,v),z.hooks&&(z.hooks.options=z),v){const C=z.highlight;let P;try{z.hooks&&(h=z.hooks.preprocess(h)),P=f(h,z)}catch(te){return T(te)}const X=te=>{let U;if(!te)try{z.walkTokens&&this.walkTokens(P,z.walkTokens),U=u(P,z),z.hooks&&(U=z.hooks.postprocess(U))}catch(ie){te=ie}return z.highlight=C,te?T(te):v(null,U)};if(!C||C.length<3||(delete z.highlight,!P.length))return X();let ce=0;this.walkTokens(P,te=>{te.type==="code"&&(ce++,setTimeout(()=>{C(te.text,te.lang,(U,ie)=>{if(U)return X(U);ie!=null&&ie!==te.text&&(te.text=ie,te.escaped=!0),ce--,ce===0&&X()})},0))}),ce===0&&X();return}if(z.async)return Promise.resolve(z.hooks?z.hooks.preprocess(h):h).then(C=>f(C,z)).then(C=>z.walkTokens?Promise.all(this.walkTokens(C,z.walkTokens)).then(()=>C):C).then(C=>u(C,z)).then(C=>z.hooks?z.hooks.postprocess(C):C).catch(T);try{z.hooks&&(h=z.hooks.preprocess(h));const C=f(h,z);z.walkTokens&&this.walkTokens(C,z.walkTokens);let P=u(C,z);return z.hooks&&(P=z.hooks.postprocess(P)),P}catch(C){return T(C)}}};Hn=new WeakSet;Oi=function(f,u,h){return m=>{if(m.message+=`
Please report this to https://github.com/markedjs/marked.`,f){const v="<p>An error occurred:</p><pre>"+Xe(m.message+"",!0)+"</pre>";if(u)return Promise.resolve(v);if(h){h(null,v);return}return v}if(u)return Promise.reject(m);if(h){h(m);return}throw m}};var ct=new fo;function ke(f,u,h){return ct.parse(f,u,h)}ke.options=ke.setOptions=function(f){return ct.setOptions(f),ke.defaults=ct.defaults,Ri(ke.defaults),ke};ke.getDefaults=On;ke.defaults=mt;ke.use=function(...f){return ct.use(...f),ke.defaults=ct.defaults,Ri(ke.defaults),ke};ke.walkTokens=function(f,u){return ct.walkTokens(f,u)};ke.parseInline=ct.parseInline;ke.Parser=ht;ke.parser=ht.parse;ke.Renderer=_r;ke.TextRenderer=qn;ke.Lexer=ut;ke.lexer=ut.lex;ke.Tokenizer=Dr;ke.Slugger=Pn;ke.Hooks=Vt;ke.parse=ke;ke.options;ke.setOptions;ke.use;ke.walkTokens;ke.parseInline;ht.parse;ut.lex;function po(f){if(typeof f=="function"&&(f={highlight:f}),!f||typeof f.highlight!="function")throw new Error("Must provide highlight function");return typeof f.langPrefix!="string"&&(f.langPrefix="language-"),{async:!!f.async,walkTokens(u){if(u.type!=="code")return;const h=go(u);if(f.async)return Promise.resolve(f.highlight(u.text,h)).then(Si(u));const m=f.highlight(u.text,h);Si(u)(m)},renderer:{code(u,h,m){const v=(h||"").match(/\S*/)[0],y=v?` class="${f.langPrefix}${Ti(v)}"`:"";return u=u.replace(/\n$/,""),`<pre><code${y}>${m?u:Ti(u,!0)}
</code></pre>`}}}}function go(f){return(f.lang||"").match(/\S*/)[0]}function Si(f){return u=>{typeof u=="string"&&u!==f.text&&(f.escaped=!0,f.text=u)}}const qi=/[&<>"']/,vo=new RegExp(qi.source,"g"),Pi=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,bo=new RegExp(Pi.source,"g"),yo={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Ai=f=>yo[f];function Ti(f,u){if(u){if(qi.test(f))return f.replace(vo,Ai)}else if(Pi.test(f))return f.replace(bo,Ai);return f}var Hi={exports:{}};(function(f){var u=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{};/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */var h=function(m){var v=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,y=0,z={},T={manual:m.Prism&&m.Prism.manual,disableWorkerMessageHandler:m.Prism&&m.Prism.disableWorkerMessageHandler,util:{encode:function _(M){return M instanceof C?new C(M.type,_(M.content),M.alias):Array.isArray(M)?M.map(_):M.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(_){return Object.prototype.toString.call(_).slice(8,-1)},objId:function(_){return _.__id||Object.defineProperty(_,"__id",{value:++y}),_.__id},clone:function _(M,F){F=F||{};var D,I;switch(T.util.type(M)){case"Object":if(I=T.util.objId(M),F[I])return F[I];D={},F[I]=D;for(var j in M)M.hasOwnProperty(j)&&(D[j]=_(M[j],F));return D;case"Array":return I=T.util.objId(M),F[I]?F[I]:(D=[],F[I]=D,M.forEach(function(ne,$){D[$]=_(ne,F)}),D);default:return M}},getLanguage:function(_){for(;_;){var M=v.exec(_.className);if(M)return M[1].toLowerCase();_=_.parentElement}return"none"},setLanguage:function(_,M){_.className=_.className.replace(RegExp(v,"gi"),""),_.classList.add("language-"+M)},currentScript:function(){if(typeof document>"u")return null;if("currentScript"in document&&1<2)return document.currentScript;try{throw new Error}catch(D){var _=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(D.stack)||[])[1];if(_){var M=document.getElementsByTagName("script");for(var F in M)if(M[F].src==_)return M[F]}return null}},isActive:function(_,M,F){for(var D="no-"+M;_;){var I=_.classList;if(I.contains(M))return!0;if(I.contains(D))return!1;_=_.parentElement}return!!F}},languages:{plain:z,plaintext:z,text:z,txt:z,extend:function(_,M){var F=T.util.clone(T.languages[_]);for(var D in M)F[D]=M[D];return F},insertBefore:function(_,M,F,D){D=D||T.languages;var I=D[_],j={};for(var ne in I)if(I.hasOwnProperty(ne)){if(ne==M)for(var $ in F)F.hasOwnProperty($)&&(j[$]=F[$]);F.hasOwnProperty(ne)||(j[ne]=I[ne])}var me=D[_];return D[_]=j,T.languages.DFS(T.languages,function(ge,Le){Le===me&&ge!=_&&(this[ge]=j)}),j},DFS:function _(M,F,D,I){I=I||{};var j=T.util.objId;for(var ne in M)if(M.hasOwnProperty(ne)){F.call(M,ne,M[ne],D||ne);var $=M[ne],me=T.util.type($);me==="Object"&&!I[j($)]?(I[j($)]=!0,_($,F,null,I)):me==="Array"&&!I[j($)]&&(I[j($)]=!0,_($,F,ne,I))}}},plugins:{},highlightAll:function(_,M){T.highlightAllUnder(document,_,M)},highlightAllUnder:function(_,M,F){var D={callback:F,container:_,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};T.hooks.run("before-highlightall",D),D.elements=Array.prototype.slice.apply(D.container.querySelectorAll(D.selector)),T.hooks.run("before-all-elements-highlight",D);for(var I=0,j;j=D.elements[I++];)T.highlightElement(j,M===!0,D.callback)},highlightElement:function(_,M,F){var D=T.util.getLanguage(_),I=T.languages[D];T.util.setLanguage(_,D);var j=_.parentElement;j&&j.nodeName.toLowerCase()==="pre"&&T.util.setLanguage(j,D);var ne=_.textContent,$={element:_,language:D,grammar:I,code:ne};function me(Le){$.highlightedCode=Le,T.hooks.run("before-insert",$),$.element.innerHTML=$.highlightedCode,T.hooks.run("after-highlight",$),T.hooks.run("complete",$),F&&F.call($.element)}if(T.hooks.run("before-sanity-check",$),j=$.element.parentElement,j&&j.nodeName.toLowerCase()==="pre"&&!j.hasAttribute("tabindex")&&j.setAttribute("tabindex","0"),!$.code){T.hooks.run("complete",$),F&&F.call($.element);return}if(T.hooks.run("before-highlight",$),!$.grammar){me(T.util.encode($.code));return}if(M&&m.Worker){var ge=new Worker(T.filename);ge.onmessage=function(Le){me(Le.data)},ge.postMessage(JSON.stringify({language:$.language,code:$.code,immediateClose:!0}))}else me(T.highlight($.code,$.grammar,$.language))},highlight:function(_,M,F){var D={code:_,grammar:M,language:F};if(T.hooks.run("before-tokenize",D),!D.grammar)throw new Error('The language "'+D.language+'" has no grammar.');return D.tokens=T.tokenize(D.code,D.grammar),T.hooks.run("after-tokenize",D),C.stringify(T.util.encode(D.tokens),D.language)},tokenize:function(_,M){var F=M.rest;if(F){for(var D in F)M[D]=F[D];delete M.rest}var I=new ce;return te(I,I.head,_),X(_,I,M,I.head,0),ie(I)},hooks:{all:{},add:function(_,M){var F=T.hooks.all;F[_]=F[_]||[],F[_].push(M)},run:function(_,M){var F=T.hooks.all[_];if(!(!F||!F.length))for(var D=0,I;I=F[D++];)I(M)}},Token:C};m.Prism=T;function C(_,M,F,D){this.type=_,this.content=M,this.alias=F,this.length=(D||"").length|0}C.stringify=function _(M,F){if(typeof M=="string")return M;if(Array.isArray(M)){var D="";return M.forEach(function(me){D+=_(me,F)}),D}var I={type:M.type,content:_(M.content,F),tag:"span",classes:["token",M.type],attributes:{},language:F},j=M.alias;j&&(Array.isArray(j)?Array.prototype.push.apply(I.classes,j):I.classes.push(j)),T.hooks.run("wrap",I);var ne="";for(var $ in I.attributes)ne+=" "+$+'="'+(I.attributes[$]||"").replace(/"/g,"&quot;")+'"';return"<"+I.tag+' class="'+I.classes.join(" ")+'"'+ne+">"+I.content+"</"+I.tag+">"};function P(_,M,F,D){_.lastIndex=M;var I=_.exec(F);if(I&&D&&I[1]){var j=I[1].length;I.index+=j,I[0]=I[0].slice(j)}return I}function X(_,M,F,D,I,j){for(var ne in F)if(!(!F.hasOwnProperty(ne)||!F[ne])){var $=F[ne];$=Array.isArray($)?$:[$];for(var me=0;me<$.length;++me){if(j&&j.cause==ne+","+me)return;var ge=$[me],Le=ge.inside,Ne=!!ge.lookbehind,We=!!ge.greedy,Ct=ge.alias;if(We&&!ge.pattern.global){var Dt=ge.pattern.toString().match(/[imsuy]*$/)[0];ge.pattern=RegExp(ge.pattern.source,Dt+"g")}for(var J0=ge.pattern||ge,Oe=D.next,Se=I;Oe!==M.tail&&!(j&&Se>=j.reach);Se+=Oe.value.length,Oe=Oe.next){var b0=Oe.value;if(M.length>_.length)return;if(!(b0 instanceof C)){var K=1,Ve;if(We){if(Ve=P(J0,Se,_,Ne),!Ve||Ve.index>=_.length)break;var Je=Ve.index,De=Ve.index+Ve[0].length,He=Se;for(He+=Oe.value.length;Je>=He;)Oe=Oe.next,He+=Oe.value.length;if(He-=Oe.value.length,Se=He,Oe.value instanceof C)continue;for(var o0=Oe;o0!==M.tail&&(He<De||typeof o0.value=="string");o0=o0.next)K++,He+=o0.value.length;K--,b0=_.slice(Se,He),Ve.index-=Se}else if(Ve=P(J0,0,b0,Ne),!Ve)continue;var Je=Ve.index,S0=Ve[0],I0=b0.slice(0,Je),et=b0.slice(Je+S0.length),u0=Se+b0.length;j&&u0>j.reach&&(j.reach=u0);var e0=Oe.prev;I0&&(e0=te(M,e0,I0),Se+=I0.length),U(M,e0,K);var tt=new C(ne,Le?T.tokenize(S0,Le):S0,Ct,S0);if(Oe=te(M,e0,tt),et&&te(M,Oe,et),K>1){var B0={cause:ne+","+me,reach:u0};X(_,M,F,Oe.prev,Se,B0),j&&B0.reach>j.reach&&(j.reach=B0.reach)}}}}}}function ce(){var _={value:null,prev:null,next:null},M={value:null,prev:_,next:null};_.next=M,this.head=_,this.tail=M,this.length=0}function te(_,M,F){var D=M.next,I={value:F,prev:M,next:D};return M.next=I,D.prev=I,_.length++,I}function U(_,M,F){for(var D=M.next,I=0;I<F&&D!==_.tail;I++)D=D.next;M.next=D,D.prev=M,_.length-=I}function ie(_){for(var M=[],F=_.head.next;F!==_.tail;)M.push(F.value),F=F.next;return M}if(!m.document)return m.addEventListener&&(T.disableWorkerMessageHandler||m.addEventListener("message",function(_){var M=JSON.parse(_.data),F=M.language,D=M.code,I=M.immediateClose;m.postMessage(T.highlight(D,T.languages[F],F)),I&&m.close()},!1)),T;var le=T.util.currentScript();le&&(T.filename=le.src,le.hasAttribute("data-manual")&&(T.manual=!0));function oe(){T.manual||T.highlightAll()}if(!T.manual){var Y=document.readyState;Y==="loading"||Y==="interactive"&&le&&le.defer?document.addEventListener("DOMContentLoaded",oe):window.requestAnimationFrame?window.requestAnimationFrame(oe):window.setTimeout(oe,16)}return T}(u);f.exports&&(f.exports=h),typeof Ar<"u"&&(Ar.Prism=h),h.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},h.languages.markup.tag.inside["attr-value"].inside.entity=h.languages.markup.entity,h.languages.markup.doctype.inside["internal-subset"].inside=h.languages.markup,h.hooks.add("wrap",function(m){m.type==="entity"&&(m.attributes.title=m.content.replace(/&amp;/,"&"))}),Object.defineProperty(h.languages.markup.tag,"addInlined",{value:function(v,y){var z={};z["language-"+y]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:h.languages[y]},z.cdata=/^<!\[CDATA\[|\]\]>$/i;var T={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:z}};T["language-"+y]={pattern:/[\s\S]+/,inside:h.languages[y]};var C={};C[v]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return v}),"i"),lookbehind:!0,greedy:!0,inside:T},h.languages.insertBefore("markup","cdata",C)}}),Object.defineProperty(h.languages.markup.tag,"addAttribute",{value:function(m,v){h.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+m+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[v,"language-"+v],inside:h.languages[v]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),h.languages.html=h.languages.markup,h.languages.mathml=h.languages.markup,h.languages.svg=h.languages.markup,h.languages.xml=h.languages.extend("markup",{}),h.languages.ssml=h.languages.xml,h.languages.atom=h.languages.xml,h.languages.rss=h.languages.xml,function(m){var v=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;m.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+v.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+v.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+v.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+v.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:v,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},m.languages.css.atrule.inside.rest=m.languages.css;var y=m.languages.markup;y&&(y.tag.addInlined("style","css"),y.tag.addAttribute("style","css"))}(h),h.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},h.languages.javascript=h.languages.extend("clike",{"class-name":[h.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source)+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),h.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,h.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:h.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:h.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:h.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:h.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:h.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),h.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:h.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),h.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),h.languages.markup&&(h.languages.markup.tag.addInlined("script","javascript"),h.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),h.languages.js=h.languages.javascript,function(){if(typeof h>"u"||typeof document>"u")return;Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var m="Loading…",v=function(le,oe){return"✖ Error "+le+" while fetching file: "+oe},y="✖ Error: File does not exist or is empty",z={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},T="data-src-status",C="loading",P="loaded",X="failed",ce="pre[data-src]:not(["+T+'="'+P+'"]):not(['+T+'="'+C+'"])';function te(le,oe,Y){var _=new XMLHttpRequest;_.open("GET",le,!0),_.onreadystatechange=function(){_.readyState==4&&(_.status<400&&_.responseText?oe(_.responseText):_.status>=400?Y(v(_.status,_.statusText)):Y(y))},_.send(null)}function U(le){var oe=/^\s*(\d+)\s*(?:(,)\s*(?:(\d+)\s*)?)?$/.exec(le||"");if(oe){var Y=Number(oe[1]),_=oe[2],M=oe[3];return _?M?[Y,Number(M)]:[Y,void 0]:[Y,Y]}}h.hooks.add("before-highlightall",function(le){le.selector+=", "+ce}),h.hooks.add("before-sanity-check",function(le){var oe=le.element;if(oe.matches(ce)){le.code="",oe.setAttribute(T,C);var Y=oe.appendChild(document.createElement("CODE"));Y.textContent=m;var _=oe.getAttribute("data-src"),M=le.language;if(M==="none"){var F=(/\.(\w+)$/.exec(_)||[,"none"])[1];M=z[F]||F}h.util.setLanguage(Y,M),h.util.setLanguage(oe,M);var D=h.plugins.autoloader;D&&D.loadLanguages(M),te(_,function(I){oe.setAttribute(T,P);var j=U(oe.getAttribute("data-range"));if(j){var ne=I.split(/\r\n?|\n/g),$=j[0],me=j[1]==null?ne.length:j[1];$<0&&($+=ne.length),$=Math.max(0,Math.min($-1,ne.length)),me<0&&(me+=ne.length),me=Math.max(0,Math.min(me,ne.length)),I=ne.slice($,me).join(`
`),oe.hasAttribute("data-start")||oe.setAttribute("data-start",String($+1))}Y.textContent=I,h.highlightElement(Y)},function(I){oe.setAttribute(T,X),Y.textContent=I})}}),h.plugins.fileHighlight={highlight:function(oe){for(var Y=(oe||document).querySelectorAll(ce),_=0,M;M=Y[_++];)h.highlightElement(M)}};var ie=!1;h.fileHighlight=function(){ie||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),ie=!0),h.plugins.fileHighlight.highlight.apply(this,arguments)}}()})(Hi);var xo=Hi.exports;const Mn=Ei(xo);Prism.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0,greedy:!0},"string-interpolation":{pattern:/(?:f|fr|rf)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|br|rb)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|br|rb)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/m,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:_(?=\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:False|None|True)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?(?!\w)/i,operator:/[-+%=]=?|!=|:=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/};Prism.languages.python["string-interpolation"].inside.interpolation.inside.rest=Prism.languages.python;Prism.languages.py=Prism.languages.python;(function(f){var u=/\\(?:[^a-z()[\]]|[a-z*]+)/i,h={"equation-command":{pattern:u,alias:"regex"}};f.languages.latex={comment:/%.*/,cdata:{pattern:/(\\begin\{((?:lstlisting|verbatim)\*?)\})[\s\S]*?(?=\\end\{\2\})/,lookbehind:!0},equation:[{pattern:/\$\$(?:\\[\s\S]|[^\\$])+\$\$|\$(?:\\[\s\S]|[^\\$])+\$|\\\([\s\S]*?\\\)|\\\[[\s\S]*?\\\]/,inside:h,alias:"string"},{pattern:/(\\begin\{((?:align|eqnarray|equation|gather|math|multline)\*?)\})[\s\S]*?(?=\\end\{\2\})/,lookbehind:!0,inside:h,alias:"string"}],keyword:{pattern:/(\\(?:begin|cite|documentclass|end|label|ref|usepackage)(?:\[[^\]]+\])?\{)[^}]+(?=\})/,lookbehind:!0},url:{pattern:/(\\url\{)[^}]+(?=\})/,lookbehind:!0},headline:{pattern:/(\\(?:chapter|frametitle|paragraph|part|section|subparagraph|subsection|subsubparagraph|subsubsection|subsubsubparagraph)\*?(?:\[[^\]]+\])?\{)[^}]+(?=\})/,lookbehind:!0,alias:"class-name"},function:{pattern:u,alias:"selector"},punctuation:/[[\]{}&]/},f.languages.tex=f.languages.latex,f.languages.context=f.languages.latex})(Prism);const wo=`<svg
xmlns="http://www.w3.org/2000/svg"
width="100%"
height="100%"
viewBox="0 0 32 32"
><path
  fill="currentColor"
  d="M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z"
/><path fill="currentColor" d="M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z" /></svg>`,ko=`<svg
xmlns="http://www.w3.org/2000/svg"
width="100%"
height="100%"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="3"
stroke-linecap="round"
stroke-linejoin="round"><polyline points="20 6 9 17 4 12" /></svg>`,Mi=`<button title="copy" class="copy_code_button">
<span class="copy-text">${wo}</span>
<span class="check">${ko}</span>
</button>`,Ui=/[&<>"']/,So=new RegExp(Ui.source,"g"),Gi=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Ao=new RegExp(Gi.source,"g"),To={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},zi=f=>To[f]||"";function zn(f,u){if(u){if(Ui.test(f))return f.replace(So,zi)}else if(Gi.test(f))return f.replace(Ao,zi);return f}const Mo={code(f,u,h){const m=(u??"").match(/\S*/)?.[0]??"";if(this.options.highlight){const v=this.options.highlight(f,m);v!=null&&v!==f&&(h=!0,f=v)}return f=f.replace(/\n$/,"")+`
`,m?'<div class="code_wrap">'+Mi+'<pre><code class="'+this.options.langPrefix+zn(m)+'">'+(h?f:zn(f,!0))+`</code></pre></div>
`:'<div class="code_wrap">'+Mi+"<pre><code>"+(h?f:zn(f,!0))+`</code></pre></div>
`}};ke.use({gfm:!0,pedantic:!1,headerIds:!1,mangle:!1},po({highlight:(f,u)=>Mn.languages[u]?Mn.highlight(f,Mn.languages[u],u):f}),{renderer:Mo});function zo(f){let u;return{c(){u=pl(f[3])},m(h,m){Et(h,u,m)},p(h,m){m&8&&gl(u,h[3])},d(h){h&&Bt(u)}}}function Eo(f){let u,h;return{c(){u=new bl(!1),h=vl(),u.a=h},m(m,v){u.m(f[3],m,v),Et(m,h,v)},p(m,v){v&8&&u.p(m[3])},d(m){m&&(Bt(h),u.d())}}}function Bo(f){let u;function h(y,z){return y[1]?Eo:zo}let m=h(f),v=m(f);return{c(){u=Ln("span"),v.c(),F0(u,"class","md svelte-r3x3aw"),Q0(u,"chatbot",f[0])},m(y,z){Et(y,u,z),v.m(u,null),f[8](u)},p(y,[z]){m===(m=h(y))&&v?v.p(y,z):(v.d(1),v=m(y),v&&(v.c(),v.m(u,null))),z&1&&Q0(u,"chatbot",y[0])},i:li,o:li,d(y){y&&Bt(u),v.d(),f[8](null)}}}function Co(f,u,h){let{chatbot:m=!0}=u,{message:v}=u,{sanitize_html:y=!0}=u,{latex_delimiters:z=[]}=u,{render_markdown:T=!0}=u,{line_breaks:C=!0}=u,P,X;ke.use({breaks:C}),pi.addHook("afterSanitizeAttributes",function(ie){"target"in ie&&(ie.setAttribute("target","_blank"),ie.setAttribute("rel","noopener noreferrer"))});function ce(ie){return T&&(ie=ke.parse(ie)),y&&(ie=pi.sanitize(ie)),ie}async function te(ie){z.length>0&&ie&&Zl(P,{delimiters:z,throwOnError:!1})}dl(()=>te(v));function U(ie){fl[ie?"unshift":"push"](()=>{P=ie,h(2,P)})}return f.$$set=ie=>{"chatbot"in ie&&h(0,m=ie.chatbot),"message"in ie&&h(4,v=ie.message),"sanitize_html"in ie&&h(5,y=ie.sanitize_html),"latex_delimiters"in ie&&h(6,z=ie.latex_delimiters),"render_markdown"in ie&&h(1,T=ie.render_markdown),"line_breaks"in ie&&h(7,C=ie.line_breaks)},f.$$.update=()=>{f.$$.dirty&16&&(v&&v.trim()?h(3,X=ce(v)):h(3,X=""))},[m,T,P,X,v,y,z,C,U]}class Do extends Rn{constructor(u){super(),Fn(this,u,Co,Bo,In,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7})}}const _o=Do;function No(f){let u,h,m,v,y,z,T;return h=new _o({props:{message:f[3],latex_delimiters:f[8],sanitize_html:f[6],line_breaks:f[7],chatbot:!1}}),{c(){u=Ln("div"),Tr(h.$$.fragment),F0(u,"id",f[0]),F0(u,"class",m="prose "+f[1].join(" ")+" svelte-aww9vd"),F0(u,"data-testid","markdown"),F0(u,"dir",v=f[5]?"rtl":"ltr"),Q0(u,"min",f[4]),Q0(u,"hide",!f[2])},m(C,P){Et(C,u,P),Mr(h,u,null),y=!0,z||(T=yl(Ml.call(null,u)),z=!0)},p(C,[P]){const X={};P&8&&(X.message=C[3]),P&256&&(X.latex_delimiters=C[8]),P&64&&(X.sanitize_html=C[6]),P&128&&(X.line_breaks=C[7]),h.$set(X),(!y||P&1)&&F0(u,"id",C[0]),(!y||P&2&&m!==(m="prose "+C[1].join(" ")+" svelte-aww9vd"))&&F0(u,"class",m),(!y||P&32&&v!==(v=C[5]?"rtl":"ltr"))&&F0(u,"dir",v),(!y||P&18)&&Q0(u,"min",C[4]),(!y||P&6)&&Q0(u,"hide",!C[2])},i(C){y||(zr(h.$$.fragment,C),y=!0)},o(C){Er(h.$$.fragment,C),y=!1},d(C){C&&Bt(u),Br(h),z=!1,T()}}}function Ro(f,u,h){let{elem_id:m=""}=u,{elem_classes:v=[]}=u,{visible:y=!0}=u,{value:z}=u,{min_height:T=!1}=u,{rtl:C=!1}=u,{sanitize_html:P=!0}=u,{line_breaks:X=!1}=u;const ce=xl();let{latex_delimiters:te}=u;return f.$$set=U=>{"elem_id"in U&&h(0,m=U.elem_id),"elem_classes"in U&&h(1,v=U.elem_classes),"visible"in U&&h(2,y=U.visible),"value"in U&&h(3,z=U.value),"min_height"in U&&h(4,T=U.min_height),"rtl"in U&&h(5,C=U.rtl),"sanitize_html"in U&&h(6,P=U.sanitize_html),"line_breaks"in U&&h(7,X=U.line_breaks),"latex_delimiters"in U&&h(8,te=U.latex_delimiters)},f.$$.update=()=>{f.$$.dirty&8&&ce("change")},[m,v,y,z,T,C,P,X,te]}class Fo extends Rn{constructor(u){super(),Fn(this,u,Ro,No,In,{elem_id:0,elem_classes:1,visible:2,value:3,min_height:4,rtl:5,sanitize_html:6,line_breaks:7,latex_delimiters:8})}}function Io(f){let u,h,m,v,y;const z=[f[4],{variant:"center"}];let T={};for(let C=0;C<z.length;C+=1)T=wl(T,z[C]);return u=new kl({props:T}),v=new Fo({props:{min_height:f[4]&&f[4].status!=="complete",value:f[3],elem_id:f[0],elem_classes:f[1],visible:f[2],rtl:f[5],latex_delimiters:f[9],sanitize_html:f[6],line_breaks:f[7]}}),v.$on("change",f[11]),{c(){Tr(u.$$.fragment),h=Sl(),m=Ln("div"),Tr(v.$$.fragment),F0(m,"class","svelte-1ed2p3z"),Q0(m,"pending",f[4]?.status==="pending")},m(C,P){Mr(u,C,P),Et(C,h,P),Et(C,m,P),Mr(v,m,null),y=!0},p(C,P){const X=P&16?Al(z,[Tl(C[4]),z[1]]):{};u.$set(X);const ce={};P&16&&(ce.min_height=C[4]&&C[4].status!=="complete"),P&8&&(ce.value=C[3]),P&1&&(ce.elem_id=C[0]),P&2&&(ce.elem_classes=C[1]),P&4&&(ce.visible=C[2]),P&32&&(ce.rtl=C[5]),P&512&&(ce.latex_delimiters=C[9]),P&64&&(ce.sanitize_html=C[6]),P&128&&(ce.line_breaks=C[7]),v.$set(ce),(!y||P&16)&&Q0(m,"pending",C[4]?.status==="pending")},i(C){y||(zr(u.$$.fragment,C),zr(v.$$.fragment,C),y=!0)},o(C){Er(u.$$.fragment,C),Er(v.$$.fragment,C),y=!1},d(C){C&&(Bt(h),Bt(m)),Br(u,C),Br(v)}}}function Lo(f){let u,h;return u=new zl({props:{visible:f[2],elem_id:f[0],elem_classes:f[1],container:!1,$$slots:{default:[Io]},$$scope:{ctx:f}}}),{c(){Tr(u.$$.fragment)},m(m,v){Mr(u,m,v),h=!0},p(m,[v]){const y={};v&4&&(y.visible=m[2]),v&1&&(y.elem_id=m[0]),v&2&&(y.elem_classes=m[1]),v&5119&&(y.$$scope={dirty:v,ctx:m}),u.$set(y)},i(m){h||(zr(u.$$.fragment,m),h=!0)},o(m){Er(u.$$.fragment,m),h=!1},d(m){Br(u,m)}}}function Oo(f,u,h){let{label:m}=u,{elem_id:v=""}=u,{elem_classes:y=[]}=u,{visible:z=!0}=u,{value:T=""}=u,{loading_status:C}=u,{rtl:P=!1}=u,{sanitize_html:X=!0}=u,{line_breaks:ce=!1}=u,{gradio:te}=u,{latex_delimiters:U}=u;const ie=()=>te.dispatch("change");return f.$$set=le=>{"label"in le&&h(10,m=le.label),"elem_id"in le&&h(0,v=le.elem_id),"elem_classes"in le&&h(1,y=le.elem_classes),"visible"in le&&h(2,z=le.visible),"value"in le&&h(3,T=le.value),"loading_status"in le&&h(4,C=le.loading_status),"rtl"in le&&h(5,P=le.rtl),"sanitize_html"in le&&h(6,X=le.sanitize_html),"line_breaks"in le&&h(7,ce=le.line_breaks),"gradio"in le&&h(8,te=le.gradio),"latex_delimiters"in le&&h(9,U=le.latex_delimiters)},f.$$.update=()=>{f.$$.dirty&1280&&te.dispatch("change")},[v,y,z,T,C,P,X,ce,te,U,m,ie]}class qo extends Rn{constructor(u){super(),Fn(this,u,Oo,Lo,In,{label:10,elem_id:0,elem_classes:1,visible:2,value:3,loading_status:4,rtl:5,sanitize_html:6,line_breaks:7,gradio:8,latex_delimiters:9})}}const Go=qo;export{_o as M,Go as S};
//# sourceMappingURL=StaticMarkdown-0958b32f.js.map
