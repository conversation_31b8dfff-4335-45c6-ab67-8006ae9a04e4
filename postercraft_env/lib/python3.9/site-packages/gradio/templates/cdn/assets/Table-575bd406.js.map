{"version": 3, "file": "Table-575bd406.js", "sources": ["../../../../js/dataframe/shared/EditableCell.svelte", "../../../../js/dataframe/shared/VirtualTable.svelte", "../../../../js/dataframe/shared/Table.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { ActionReturn } from \"svelte/action\";\n\timport { MarkdownCode } from \"@gradio/markdown\";\n\n\texport let edit: boolean;\n\texport let value: string | number = \"\";\n\texport let display_value: string | null = null;\n\texport let styling = \"\";\n\texport let header = false;\n\texport let datatype:\n\t\t| \"str\"\n\t\t| \"markdown\"\n\t\t| \"html\"\n\t\t| \"number\"\n\t\t| \"bool\"\n\t\t| \"date\" = \"str\";\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let clear_on_focus = false;\n\texport let select_on_focus = false;\n\texport let line_breaks = true;\n\texport let editable = true;\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let el: HTMLInputElement | null;\n\t$: _value = value;\n\n\tfunction use_focus(node: HTMLInputElement): ActionReturn {\n\t\tif (clear_on_focus) {\n\t\t\t_value = \"\";\n\t\t}\n\t\tif (select_on_focus) {\n\t\t\tnode.select();\n\t\t}\n\n\t\tnode.focus();\n\n\t\treturn {};\n\t}\n</script>\n\n{#if edit}\n\t<input\n\t\tbind:this={el}\n\t\tbind:value={_value}\n\t\tclass:header\n\t\ttabindex=\"-1\"\n\t\ton:blur={({ currentTarget }) => {\n\t\t\tvalue = currentTarget.value;\n\t\t\tdispatch(\"blur\");\n\t\t}}\n\t\tuse:use_focus\n\t\ton:keydown\n\t/>\n{/if}\n\n<span\n\ton:dblclick\n\ttabindex=\"-1\"\n\trole=\"button\"\n\tclass:edit\n\ton:focus|preventDefault\n\tstyle={styling}\n>\n\t{#if datatype === \"html\"}\n\t\t{@html value}\n\t{:else if datatype === \"markdown\"}\n\t\t<MarkdownCode\n\t\t\tmessage={value.toLocaleString()}\n\t\t\t{latex_delimiters}\n\t\t\t{line_breaks}\n\t\t\tchatbot={false}\n\t\t/>\n\t{:else}\n\t\t{editable ? value : display_value || value}\n\t{/if}\n</span>\n\n<style>\n\tinput {\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tbottom: var(--size-2);\n\t\tleft: var(--size-2);\n\t\tflex: 1 1 0%;\n\t\ttransform: translateX(-0.1px);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t}\n\n\tspan {\n\t\tflex: 1 1 0%;\n\t\toutline: none;\n\t\tpadding: var(--size-2);\n\t}\n\n\t.header {\n\t\ttransform: translateX(0);\n\t\tfont: var(--weight-bold);\n\t}\n\n\t.edit {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount, tick } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let items: any[][] = [];\n\n\texport let table_width: number;\n\texport let max_height: number;\n\texport let actual_height: number;\n\texport let table_scrollbar_width: number;\n\texport let start = 0;\n\texport let end = 0;\n\texport let selected: number | false;\n\tlet height = \"100%\";\n\n\tlet average_height: number;\n\tlet bottom = 0;\n\tlet contents: HTMLTableSectionElement;\n\tlet head_height = 0;\n\tlet foot_height = 0;\n\tlet height_map: number[] = [];\n\tlet mounted: boolean;\n\tlet rows: HTMLCollectionOf<HTMLTableRowElement>;\n\tlet top = 0;\n\tlet viewport: HTMLTableElement;\n\tlet viewport_height = 0;\n\tlet visible: { index: number; data: any[] }[] = [];\n\n\t$: if (mounted) requestAnimationFrame(() => refresh_height_map(sortedItems));\n\n\tlet content_height = 0;\n\tasync function refresh_height_map(_items: typeof items): Promise<void> {\n\t\tif (viewport_height === 0 || table_width === 0) {\n\t\t\treturn;\n\t\t}\n\t\tconst { scrollTop } = viewport;\n\t\ttable_scrollbar_width = viewport.offsetWidth - viewport.clientWidth;\n\n\t\tcontent_height = top - (scrollTop - head_height);\n\t\tlet i = start;\n\n\t\twhile (content_height < max_height && i < _items.length) {\n\t\t\tlet row = rows[i - start];\n\t\t\tif (!row) {\n\t\t\t\tend = i + 1;\n\t\t\t\tawait tick(); // render the newly visible row\n\t\t\t\trow = rows[i - start];\n\t\t\t}\n\t\t\tlet _h = row?.getBoundingClientRect().height;\n\t\t\tif (!_h) {\n\t\t\t\t_h = average_height;\n\t\t\t}\n\t\t\tconst row_height = (height_map[i] = _h);\n\t\t\tcontent_height += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tend = i;\n\t\tconst remaining = _items.length - end;\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tcontent_height += scrollbar_height;\n\t\t}\n\n\t\tlet filtered_height_map = height_map.filter((v) => typeof v === \"number\");\n\t\taverage_height =\n\t\t\tfiltered_height_map.reduce((a, b) => a + b, 0) /\n\t\t\tfiltered_height_map.length;\n\n\t\tbottom = remaining * average_height;\n\t\theight_map.length = _items.length;\n\t\tawait tick();\n\t\tif (!max_height) {\n\t\t\tactual_height = content_height + 1;\n\t\t} else if (content_height < max_height) {\n\t\t\tactual_height = content_height + 2;\n\t\t} else {\n\t\t\tactual_height = max_height;\n\t\t}\n\n\t\tawait tick();\n\t}\n\n\t$: scroll_and_render(selected);\n\tasync function scroll_and_render(n: number | false): Promise<void> {\n\t\trequestAnimationFrame(async () => {\n\t\t\tif (typeof n !== \"number\") return;\n\t\t\tconst direction = typeof n !== \"number\" ? false : is_in_view(n);\n\t\t\tif (direction === true) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (direction === \"back\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" });\n\t\t\t}\n\n\t\t\tif (direction === \"forwards\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" }, true);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction is_in_view(n: number): \"back\" | \"forwards\" | true {\n\t\tconst current = rows && rows[n - start];\n\t\tif (!current && n < start) {\n\t\t\treturn \"back\";\n\t\t}\n\t\tif (!current && n >= end - 1) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\tconst { top: viewport_top } = viewport.getBoundingClientRect();\n\t\tconst { top, bottom } = current.getBoundingClientRect();\n\n\t\tif (top - viewport_top < 37) {\n\t\t\treturn \"back\";\n\t\t}\n\n\t\tif (bottom - viewport_top > viewport_height) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\treturn true;\n\t}\n\n\tfunction get_computed_px_amount(elem: HTMLElement, property: string): number {\n\t\tif (!elem) {\n\t\t\treturn 0;\n\t\t}\n\t\tconst compStyle = getComputedStyle(elem);\n\n\t\tlet x = parseInt(compStyle.getPropertyValue(property));\n\t\treturn x;\n\t}\n\n\tasync function handle_scroll(e: Event): Promise<void> {\n\t\tconst scroll_top = viewport.scrollTop;\n\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tconst is_start_overflow = sortedItems.length < start;\n\n\t\tconst row_top_border = get_computed_px_amount(rows[1], \"border-top-width\");\n\n\t\tconst actual_border_collapsed_width = 0;\n\n\t\tif (is_start_overflow) {\n\t\t\tawait scroll_to_index(sortedItems.length - 1, { behavior: \"auto\" });\n\t\t}\n\n\t\tlet new_start = 0;\n\t\t// acquire height map for currently visible rows\n\t\tfor (let v = 0; v < rows.length; v += 1) {\n\t\t\theight_map[start + v] = rows[v].getBoundingClientRect().height;\n\t\t}\n\t\tlet i = 0;\n\t\t// start from top: thead, with its borders, plus the first border to afterwards neglect\n\t\tlet y = head_height + row_top_border / 2;\n\t\tlet row_heights = [];\n\t\t// loop items to find new start\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\trow_heights[i] = row_height;\n\t\t\t// we only want to jump if the full (incl. border) row is away\n\t\t\tif (y + row_height + actual_border_collapsed_width > scroll_top) {\n\t\t\t\t// this is the last index still inside the viewport\n\t\t\t\tnew_start = i;\n\t\t\t\ttop = y - (head_height + row_top_border / 2);\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tnew_start = Math.max(0, new_start);\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t\tif (y > scroll_top + viewport_height) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tstart = new_start;\n\t\tend = i;\n\t\tconst remaining = sortedItems.length - end;\n\t\tif (end === 0) {\n\t\t\tend = 10;\n\t\t}\n\t\taverage_height = (y - head_height) / end;\n\t\tlet remaining_height = remaining * average_height; // 0\n\t\t// compute height map for remaining items\n\t\twhile (i < sortedItems.length) {\n\t\t\ti += 1;\n\t\t\theight_map[i] = average_height;\n\t\t}\n\t\tbottom = remaining_height;\n\t\tif (!isFinite(bottom)) {\n\t\t\tbottom = 200000;\n\t\t}\n\t}\n\n\texport async function scroll_to_index(\n\t\tindex: number,\n\t\topts: ScrollToOptions,\n\t\talign_end = false\n\t): Promise<void> {\n\t\tawait tick();\n\n\t\tconst _itemHeight = average_height;\n\n\t\tlet distance = index * _itemHeight;\n\t\tif (align_end) {\n\t\t\tdistance = distance - viewport_height + _itemHeight + head_height;\n\t\t}\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tdistance += scrollbar_height;\n\t\t}\n\n\t\tconst _opts = {\n\t\t\ttop: distance,\n\t\t\tbehavior: \"smooth\" as ScrollBehavior,\n\t\t\t...opts\n\t\t};\n\n\t\tviewport.scrollTo(_opts);\n\t}\n\n\t$: sortedItems = items;\n\n\t$: visible = sortedItems.slice(start, end).map((data, i) => {\n\t\treturn { index: i + start, data };\n\t});\n\n\tonMount(() => {\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tmounted = true;\n\t\trefresh_height_map(items);\n\t});\n</script>\n\n<svelte-virtual-table-viewport>\n\t<table\n\t\tclass=\"table\"\n\t\tbind:this={viewport}\n\t\tbind:offsetHeight={viewport_height}\n\t\ton:scroll={handle_scroll}\n\t\tstyle=\"height: {height}; --bw-svt-p-top: {top}px; --bw-svt-p-bottom: {bottom}px; --bw-svt-head-height: {head_height}px; --bw-svt-foot-height: {foot_height}px; --bw-svt-avg-row-height: {average_height}px\"\n\t>\n\t\t<thead class=\"thead\" bind:offsetHeight={head_height}>\n\t\t\t<slot name=\"thead\" />\n\t\t</thead>\n\t\t<tbody bind:this={contents} class=\"tbody\">\n\t\t\t{#if visible.length && visible[0].data.length}\n\t\t\t\t{#each visible as item (item.data[0].id)}\n\t\t\t\t\t<slot name=\"tbody\" item={item.data} index={item.index}>\n\t\t\t\t\t\tMissing Table Row\n\t\t\t\t\t</slot>\n\t\t\t\t{/each}\n\t\t\t{/if}\n\t\t</tbody>\n\t\t<tfoot class=\"tfoot\" bind:offsetHeight={foot_height}>\n\t\t\t<slot name=\"tfoot\" />\n\t\t</tfoot>\n\t</table>\n</svelte-virtual-table-viewport>\n\n<style type=\"text/css\">\n\ttable {\n\t\tposition: relative;\n\t\toverflow-y: scroll;\n\t\toverflow-x: scroll;\n\t\t-webkit-overflow-scrolling: touch;\n\t\tmax-height: 100vh;\n\t\tbox-sizing: border-box;\n\t\tdisplay: block;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t\twidth: 100%;\n\t\tscroll-snap-type: x proximity;\n\t}\n\ttable :is(thead, tfoot, tbody) {\n\t\tdisplay: table;\n\t\ttable-layout: fixed;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\n\ttbody {\n\t\toverflow-x: scroll;\n\t\toverflow-y: hidden;\n\t}\n\n\ttable tbody {\n\t\tpadding-top: var(--bw-svt-p-top);\n\t\tpadding-bottom: var(--bw-svt-p-bottom);\n\t}\n\ttbody {\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tborder: 0px solid currentColor;\n\t}\n\n\ttbody > :global(tr:last-child) {\n\t\tborder: none;\n\t}\n\n\ttable :global(td) {\n\t\tscroll-snap-align: start;\n\t}\n\n\ttbody > :global(tr:nth-child(even)) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: var(--layer-1);\n\t\tbox-shadow: var(--shadow-drop);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick, onMount } from \"svelte\";\n\timport { dsvFormat } from \"d3-dsv\";\n\timport { dequal } from \"dequal/lite\";\n\timport { copy } from \"@gradio/utils\";\n\timport { Upload } from \"@gradio/upload\";\n\timport { BaseButton } from \"@gradio/button/static\";\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { _ } from \"svelte-i18n\";\n\timport VirtualTable from \"./VirtualTable.svelte\";\n\timport type {\n\t\tHeaders,\n\t\tHeadersWithIDs,\n\t\tData,\n\t\tMetadata,\n\t\tDatatype\n\t} from \"../shared/utils\";\n\n\texport let datatype: Datatype | Datatype[];\n\texport let label: string | null = null;\n\texport let headers: Headers = [];\n\tlet values: (string | number)[][];\n\texport let value: { data: Data; headers: Headers; metadata: Metadata } | null;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\n\texport let editable = true;\n\texport let wrap = false;\n\texport let height = 500;\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\n\tlet selected: false | [number, number] = false;\n\tlet display_value: string[][] | null = value?.metadata?.display_value ?? null;\n\tlet styling: string[][] | null = value?.metadata?.styling ?? null;\n\n\t$: {\n\t\tif (value) {\n\t\t\theaders = value.headers;\n\t\t\tvalues = value.data;\n\t\t\tdisplay_value = value?.metadata?.display_value ?? null;\n\t\t\tstyling = value?.metadata?.styling ?? null;\n\t\t} else if (values === null) {\n\t\t\tvalues = [];\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: {\n\t\t\tdata: (string | number)[][];\n\t\t\theaders: string[];\n\t\t\tmetadata: Metadata;\n\t\t};\n\t\tselect: SelectData;\n\t}>();\n\n\tlet editing: false | [number, number] = false;\n\n\tconst get_data_at = (row: number, col: number): string | number =>\n\t\tdata?.[row]?.[col]?.value;\n\t$: {\n\t\tif (selected !== false) {\n\t\t\tconst [row, col] = selected;\n\t\t\tif (!isNaN(row) && !isNaN(col)) {\n\t\t\t\tdispatch(\"select\", { index: [row, col], value: get_data_at(row, col) });\n\t\t\t}\n\t\t}\n\t}\n\tlet els: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLInputElement }\n\t> = {};\n\n\tlet data_binding: Record<string, (typeof data)[0][0]> = {};\n\n\tfunction make_id(): string {\n\t\treturn Math.random().toString(36).substring(2, 15);\n\t}\n\tfunction make_headers(_head: Headers): HeadersWithIDs {\n\t\tlet _h = _head || [];\n\t\tif (col_count[1] === \"fixed\" && _h.length < col_count[0]) {\n\t\t\tconst fill = Array(col_count[0] - _h.length)\n\t\t\t\t.fill(\"\")\n\t\t\t\t.map((_, i) => `${i + _h.length}`);\n\t\t\t_h = _h.concat(fill);\n\t\t}\n\n\t\tif (!_h || _h.length === 0) {\n\t\t\treturn Array(col_count[0])\n\t\t\t\t.fill(0)\n\t\t\t\t.map((_, i) => {\n\t\t\t\t\tconst _id = make_id();\n\t\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\t\treturn { id: _id, value: JSON.stringify(i + 1) };\n\t\t\t\t});\n\t\t}\n\t\treturn _h.map((h, i) => {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\treturn { id: _id, value: h ?? \"\" };\n\t\t});\n\t}\n\n\tfunction process_data(_values: (string | number)[][]): {\n\t\tvalue: string | number;\n\t\tid: string;\n\t}[][] {\n\t\tconst data_row_length = _values.length;\n\t\treturn Array(\n\t\t\trow_count[1] === \"fixed\"\n\t\t\t\t? row_count[0]\n\t\t\t\t: data_row_length < row_count[0]\n\t\t\t\t? row_count[0]\n\t\t\t\t: data_row_length\n\t\t)\n\t\t\t.fill(0)\n\t\t\t.map((_, i) =>\n\t\t\t\tArray(\n\t\t\t\t\tcol_count[1] === \"fixed\"\n\t\t\t\t\t\t? col_count[0]\n\t\t\t\t\t\t: data_row_length > 0\n\t\t\t\t\t\t? _values[0].length\n\t\t\t\t\t\t: headers.length\n\t\t\t\t)\n\t\t\t\t\t.fill(0)\n\t\t\t\t\t.map((_, j) => {\n\t\t\t\t\t\tconst id = make_id();\n\t\t\t\t\t\tels[id] = els[id] || { input: null, cell: null };\n\t\t\t\t\t\tconst obj = { value: _values?.[i]?.[j] ?? \"\", id };\n\t\t\t\t\t\tdata_binding[id] = obj;\n\t\t\t\t\t\treturn obj;\n\t\t\t\t\t})\n\t\t\t);\n\t}\n\n\tlet _headers = make_headers(headers);\n\tlet old_headers: string[] | undefined;\n\n\t$: {\n\t\tif (!dequal(headers, old_headers)) {\n\t\t\ttrigger_headers();\n\t\t}\n\t}\n\n\tfunction trigger_headers(): void {\n\t\t_headers = make_headers(headers);\n\n\t\told_headers = headers.slice();\n\t}\n\t$: if (!dequal(values, old_val)) {\n\t\tdata = process_data(values as (string | number)[][]);\n\t\told_val = values as (string | number)[][];\n\t}\n\n\tlet data: { id: string; value: string | number }[][] = [[]];\n\n\tlet old_val: undefined | (string | number)[][] = undefined;\n\n\t$: _headers &&\n\t\tdispatch(\"change\", {\n\t\t\tdata: data.map((r) => r.map(({ value }) => value)),\n\t\t\theaders: _headers.map((h) => h.value),\n\t\t\tmetadata: editable\n\t\t\t\t? null\n\t\t\t\t: { display_value: display_value, styling: styling }\n\t\t});\n\n\tfunction get_sort_status(\n\t\tname: string,\n\t\t_sort?: number,\n\t\tdirection?: SortDirection\n\t): \"none\" | \"ascending\" | \"descending\" {\n\t\tif (!_sort) return \"none\";\n\t\tif (headers[_sort] === name) {\n\t\t\tif (direction === \"asc\") return \"ascending\";\n\t\t\tif (direction === \"des\") return \"descending\";\n\t\t}\n\n\t\treturn \"none\";\n\t}\n\n\tfunction get_current_indices(id: string): [number, number] {\n\t\treturn data.reduce(\n\t\t\t(acc, arr, i) => {\n\t\t\t\tconst j = arr.reduce(\n\t\t\t\t\t(_acc, _data, k) => (id === _data.id ? k : _acc),\n\t\t\t\t\t-1\n\t\t\t\t);\n\n\t\t\t\treturn j === -1 ? acc : [i, j];\n\t\t\t},\n\t\t\t[-1, -1]\n\t\t);\n\t}\n\n\tasync function start_edit(i: number, j: number): Promise<void> {\n\t\tif (!editable || dequal(editing, [i, j])) return;\n\n\t\tediting = [i, j];\n\t}\n\n\tfunction move_cursor(\n\t\tkey: \"ArrowRight\" | \"ArrowLeft\" | \"ArrowDown\" | \"ArrowUp\",\n\t\tcurrent_coords: [number, number]\n\t): void {\n\t\tconst dir = {\n\t\t\tArrowRight: [0, 1],\n\t\t\tArrowLeft: [0, -1],\n\t\t\tArrowDown: [1, 0],\n\t\t\tArrowUp: [-1, 0]\n\t\t}[key];\n\n\t\tconst i = current_coords[0] + dir[0];\n\t\tconst j = current_coords[1] + dir[1];\n\n\t\tif (i < 0 && j <= 0) {\n\t\t\tselected_header = j;\n\t\t\tselected = false;\n\t\t} else {\n\t\t\tconst is_data = data[i]?.[j];\n\t\t\tselected = is_data ? [i, j] : selected;\n\t\t}\n\t}\n\n\tlet clear_on_focus = false;\n\t// eslint-disable-next-line complexity\n\tasync function handle_keydown(event: KeyboardEvent): Promise<void> {\n\t\tif (selected_header !== false && header_edit === false) {\n\t\t\tswitch (event.key) {\n\t\t\t\tcase \"ArrowDown\":\n\t\t\t\t\tselected = [0, selected_header];\n\t\t\t\t\tselected_header = false;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"ArrowLeft\":\n\t\t\t\t\tselected_header =\n\t\t\t\t\t\tselected_header > 0 ? selected_header - 1 : selected_header;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"ArrowRight\":\n\t\t\t\t\tselected_header =\n\t\t\t\t\t\tselected_header < _headers.length - 1\n\t\t\t\t\t\t\t? selected_header + 1\n\t\t\t\t\t\t\t: selected_header;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"Escape\":\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tselected_header = false;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"Enter\":\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (!selected) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst [i, j] = selected;\n\n\t\tswitch (event.key) {\n\t\t\tcase \"ArrowRight\":\n\t\t\tcase \"ArrowLeft\":\n\t\t\tcase \"ArrowDown\":\n\t\t\tcase \"ArrowUp\":\n\t\t\t\tif (editing) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tmove_cursor(event.key, [i, j]);\n\t\t\t\tbreak;\n\n\t\t\tcase \"Escape\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tediting = false;\n\t\t\t\tbreak;\n\t\t\tcase \"Enter\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tevent.preventDefault();\n\n\t\t\t\tif (event.shiftKey) {\n\t\t\t\t\tadd_row(i);\n\t\t\t\t\tawait tick();\n\n\t\t\t\t\tselected = [i + 1, j];\n\t\t\t\t} else {\n\t\t\t\t\tif (dequal(editing, [i, j])) {\n\t\t\t\t\t\tediting = false;\n\t\t\t\t\t\tawait tick();\n\t\t\t\t\t\tselected = [i, j];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tediting = [i, j];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\tcase \"Backspace\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (!editing) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tdata[i][j].value = \"\";\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"Delete\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (!editing) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tdata[i][j].value = \"\";\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"Tab\":\n\t\t\t\tlet direction = event.shiftKey ? -1 : 1;\n\n\t\t\t\tlet is_data_x = data[i][j + direction];\n\t\t\t\tlet is_data_y =\n\t\t\t\t\tdata?.[i + direction]?.[direction > 0 ? 0 : _headers.length - 1];\n\n\t\t\t\tif (is_data_x || is_data_y) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tselected = is_data_x\n\t\t\t\t\t\t? [i, j + direction]\n\t\t\t\t\t\t: [i + direction, direction > 0 ? 0 : _headers.length - 1];\n\t\t\t\t}\n\t\t\t\tediting = false;\n\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (\n\t\t\t\t\t(!editing || (editing && dequal(editing, [i, j]))) &&\n\t\t\t\t\tevent.key.length === 1\n\t\t\t\t) {\n\t\t\t\t\tclear_on_focus = true;\n\t\t\t\t\tediting = [i, j];\n\t\t\t\t}\n\t\t}\n\t}\n\n\tasync function handle_cell_click(i: number, j: number): Promise<void> {\n\t\tif (dequal(editing, [i, j])) return;\n\t\theader_edit = false;\n\t\tselected_header = false;\n\t\tediting = false;\n\t\tselected = [i, j];\n\t\tawait tick();\n\t\tparent.focus();\n\t}\n\n\ttype SortDirection = \"asc\" | \"des\";\n\tlet sort_direction: SortDirection | undefined;\n\tlet sort_by: number | undefined;\n\n\tfunction handle_sort(col: number): void {\n\t\tif (typeof sort_by !== \"number\" || sort_by !== col) {\n\t\t\tsort_direction = \"asc\";\n\t\t\tsort_by = col;\n\t\t} else {\n\t\t\tif (sort_direction === \"asc\") {\n\t\t\t\tsort_direction = \"des\";\n\t\t\t} else if (sort_direction === \"des\") {\n\t\t\t\tsort_direction = \"asc\";\n\t\t\t}\n\t\t}\n\t}\n\n\tlet header_edit: number | false;\n\n\tlet select_on_focus = false;\n\tlet selected_header: number | false = false;\n\tasync function edit_header(i: number, _select = false): Promise<void> {\n\t\tif (!editable || col_count[1] !== \"dynamic\" || header_edit === i) return;\n\t\tselected = false;\n\t\tselected_header = i;\n\t\theader_edit = i;\n\t\tselect_on_focus = _select;\n\t}\n\n\tfunction end_header_edit(event: KeyboardEvent): void {\n\t\tif (!editable) return;\n\n\t\tswitch (event.key) {\n\t\t\tcase \"Escape\":\n\t\t\tcase \"Enter\":\n\t\t\tcase \"Tab\":\n\t\t\t\tevent.preventDefault();\n\t\t\t\tselected = false;\n\t\t\t\tselected_header = header_edit;\n\t\t\t\theader_edit = false;\n\t\t\t\tparent.focus();\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\tasync function add_row(index?: number): Promise<void> {\n\t\tparent.focus();\n\n\t\tif (row_count[1] !== \"dynamic\") return;\n\t\tif (data.length === 0) {\n\t\t\tvalues = [Array(headers.length).fill(\"\")];\n\t\t\treturn;\n\t\t}\n\n\t\tdata.splice(\n\t\t\tindex ? index + 1 : data.length,\n\t\t\t0,\n\t\t\tArray(data[0].length)\n\t\t\t\t.fill(0)\n\t\t\t\t.map((_, i) => {\n\t\t\t\t\tconst _id = make_id();\n\n\t\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\t\treturn { id: _id, value: \"\" };\n\t\t\t\t})\n\t\t);\n\n\t\tdata = data;\n\t\tselected = [index ? index + 1 : data.length - 1, 0];\n\t}\n\n\tasync function add_col(): Promise<void> {\n\t\tparent.focus();\n\t\tif (col_count[1] !== \"dynamic\") return;\n\t\tfor (let i = 0; i < data.length; i++) {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\tdata[i].push({ id: _id, value: \"\" });\n\t\t}\n\n\t\theaders.push(`Header ${headers.length + 1}`);\n\n\t\tdata = data;\n\t\theaders = headers;\n\n\t\tawait tick();\n\n\t\trequestAnimationFrame(() => {\n\t\t\tedit_header(headers.length - 1, true);\n\t\t\tconst new_w = parent.querySelectorAll(\"tbody\")[1].offsetWidth;\n\t\t\tparent.querySelectorAll(\"table\")[1].scrollTo({ left: new_w });\n\t\t});\n\t}\n\n\tfunction handle_click_outside(event: Event): void {\n\t\tevent.stopImmediatePropagation();\n\t\tconst [trigger] = event.composedPath() as HTMLElement[];\n\t\tif (parent.contains(trigger)) {\n\t\t\treturn;\n\t\t}\n\n\t\tediting = false;\n\t\theader_edit = false;\n\t\tselected_header = false;\n\t\tselected = false;\n\t}\n\n\tfunction guess_delimitaor(\n\t\ttext: string,\n\t\tpossibleDelimiters: string[]\n\t): string[] {\n\t\treturn possibleDelimiters.filter(weedOut);\n\n\t\tfunction weedOut(delimiter: string): boolean {\n\t\t\tvar cache = -1;\n\t\t\treturn text.split(\"\\n\").every(checkLength);\n\n\t\t\tfunction checkLength(line: string): boolean {\n\t\t\t\tif (!line) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tvar length = line.split(delimiter).length;\n\t\t\t\tif (cache < 0) {\n\t\t\t\t\tcache = length;\n\t\t\t\t}\n\t\t\t\treturn cache === length && length > 1;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction data_uri_to_blob(data_uri: string): Blob {\n\t\tconst byte_str = atob(data_uri.split(\",\")[1]);\n\t\tconst mime_str = data_uri.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\n\t\tconst ab = new ArrayBuffer(byte_str.length);\n\t\tconst ia = new Uint8Array(ab);\n\n\t\tfor (let i = 0; i < byte_str.length; i++) {\n\t\t\tia[i] = byte_str.charCodeAt(i);\n\t\t}\n\n\t\treturn new Blob([ab], { type: mime_str });\n\t}\n\n\tfunction blob_to_string(blob: Blob): void {\n\t\tconst reader = new FileReader();\n\n\t\tfunction handle_read(e: ProgressEvent<FileReader>): void {\n\t\t\tif (!e?.target?.result || typeof e.target.result !== \"string\") return;\n\n\t\t\tconst [delimiter] = guess_delimitaor(e.target.result, [\",\", \"\\t\"]);\n\n\t\t\tconst [head, ...rest] = dsvFormat(delimiter).parseRows(e.target.result);\n\n\t\t\t_headers = make_headers(\n\t\t\t\tcol_count[1] === \"fixed\" ? head.slice(0, col_count[0]) : head\n\t\t\t);\n\n\t\t\tvalues = rest;\n\t\t\treader.removeEventListener(\"loadend\", handle_read);\n\t\t}\n\n\t\treader.addEventListener(\"loadend\", handle_read);\n\n\t\treader.readAsText(blob);\n\t}\n\n\tlet dragging = false;\n\n\tlet t_width = 0;\n\n\tfunction get_max(\n\t\t_d: { value: any; id: string }[][]\n\t): { value: any; id: string }[] {\n\t\tlet max = _d[0].slice();\n\t\tfor (let i = 0; i < _d.length; i++) {\n\t\t\tfor (let j = 0; j < _d[i].length; j++) {\n\t\t\t\tif (`${max[j].value}`.length < `${_d[i][j].value}`.length) {\n\t\t\t\t\tmax[j] = _d[i][j];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn max;\n\t}\n\n\t$: max = get_max(data);\n\n\t$: cells[0] && set_cell_widths();\n\tlet cells: HTMLTableCellElement[] = [];\n\tlet parent: HTMLDivElement;\n\tlet table: HTMLTableElement;\n\n\tfunction set_cell_widths(): void {\n\t\tconst widths = cells.map((el, i) => {\n\t\t\treturn el?.clientWidth || 0;\n\t\t});\n\t\tif (widths.length === 0) return;\n\t\tfor (let i = 0; i < widths.length; i++) {\n\t\t\tparent.style.setProperty(\n\t\t\t\t`--cell-width-${i}`,\n\t\t\t\t`${widths[i] - scrollbar_width / widths.length}px`\n\t\t\t);\n\t\t}\n\t}\n\n\tlet table_height: number = height;\n\tlet scrollbar_width = 0;\n\n\tfunction sort_data(\n\t\t_data: typeof data,\n\t\t_display_value: string[][] | null,\n\t\t_styling: string[][] | null,\n\t\tcol?: number,\n\t\tdir?: SortDirection\n\t): void {\n\t\tlet id = null;\n\t\t//Checks if the selected cell is still in the data\n\t\tif (selected && selected[0] in data && selected[1] in data[selected[0]]) {\n\t\t\tid = data[selected[0]][selected[1]].id;\n\t\t}\n\t\tif (typeof col !== \"number\" || !dir) {\n\t\t\treturn;\n\t\t}\n\t\tconst indices = [...Array(_data.length).keys()];\n\n\t\tif (dir === \"asc\") {\n\t\t\tindices.sort((i, j) =>\n\t\t\t\t_data[i][col].value < _data[j][col].value ? -1 : 1\n\t\t\t);\n\t\t} else if (dir === \"des\") {\n\t\t\tindices.sort((i, j) =>\n\t\t\t\t_data[i][col].value > _data[j][col].value ? -1 : 1\n\t\t\t);\n\t\t} else {\n\t\t\treturn;\n\t\t}\n\n\t\t// sort all the data and metadata based on the values in the data\n\t\tconst temp_data = [..._data];\n\t\tconst temp_display_value = _display_value ? [..._display_value] : null;\n\t\tconst temp_styling = _styling ? [..._styling] : null;\n\t\tindices.forEach((originalIndex, sortedIndex) => {\n\t\t\t_data[sortedIndex] = temp_data[originalIndex];\n\t\t\tif (_display_value && temp_display_value)\n\t\t\t\t_display_value[sortedIndex] = temp_display_value[originalIndex];\n\t\t\tif (_styling && temp_styling)\n\t\t\t\t_styling[sortedIndex] = temp_styling[originalIndex];\n\t\t});\n\n\t\tdata = data;\n\n\t\tif (id) {\n\t\t\tconst [i, j] = get_current_indices(id);\n\t\t\tselected = [i, j];\n\t\t}\n\t}\n\n\t$: sort_data(data, display_value, styling, sort_by, sort_direction);\n\n\t$: selected_index = !!selected && selected[0];\n\n\tlet is_visible = false;\n\tonMount(() => {\n\t\tconst observer = new IntersectionObserver((entries, observer) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting && !is_visible) {\n\t\t\t\t\tset_cell_widths();\n\t\t\t\t\tdata = data;\n\t\t\t\t}\n\n\t\t\t\tis_visible = entry.isIntersecting;\n\t\t\t});\n\t\t});\n\n\t\tobserver.observe(parent);\n\n\t\treturn () => {\n\t\t\tobserver.disconnect();\n\t\t};\n\t});\n</script>\n\n<svelte:window\n\ton:click={handle_click_outside}\n\ton:touchstart={handle_click_outside}\n\ton:resize={() => set_cell_widths()}\n/>\n\n<div class:label={label && label.length !== 0} use:copy>\n\t{#if label && label.length !== 0}\n\t\t<p>\n\t\t\t{label}\n\t\t</p>\n\t{/if}\n\t<div\n\t\tbind:this={parent}\n\t\tclass=\"table-wrap\"\n\t\tclass:dragging\n\t\tclass:no-wrap={!wrap}\n\t\tstyle=\"height:{table_height}px\"\n\t\ton:keydown={(e) => handle_keydown(e)}\n\t\trole=\"grid\"\n\t\ttabindex=\"0\"\n\t>\n\t\t<table\n\t\t\tbind:clientWidth={t_width}\n\t\t\tbind:this={table}\n\t\t\tclass:fixed-layout={column_widths.length != 0}\n\t\t>\n\t\t\t{#if label && label.length !== 0}\n\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t{/if}\n\t\t\t<thead>\n\t\t\t\t<tr>\n\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t<th\n\t\t\t\t\t\t\tclass:editing={header_edit === i}\n\t\t\t\t\t\t\taria-sort={get_sort_status(value, sort_by, sort_direction)}\n\t\t\t\t\t\t\tstyle:width={column_widths.length ? column_widths[i] : undefined}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\theader\n\t\t\t\t\t\t\t\t\tedit={false}\n\t\t\t\t\t\t\t\t\tel={null}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass:sorted={sort_by === i}\n\t\t\t\t\t\t\t\t\tclass:des={sort_by === i && sort_direction === \"des\"}\n\t\t\t\t\t\t\t\t\tclass=\"sort-button {sort_direction} \"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 9 7\"\n\t\t\t\t\t\t\t\t\t\tfill=\"none\"\n\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<path d=\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</th>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</thead>\n\t\t\t<tbody>\n\t\t\t\t<tr>\n\t\t\t\t\t{#each max as { value, id }, j (id)}\n\t\t\t\t\t\t<td tabindex=\"-1\" bind:this={cells[j]}>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\tedit={false}\n\t\t\t\t\t\t\t\t\tel={null}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</tbody>\n\t\t</table>\n\t\t<Upload\n\t\t\tflex={false}\n\t\t\tcenter={false}\n\t\t\tboundedheight={false}\n\t\t\tdisable_click={true}\n\t\t\ton:load={(e) => blob_to_string(data_uri_to_blob(e.detail.data))}\n\t\t\tbind:dragging\n\t\t>\n\t\t\t<VirtualTable\n\t\t\t\tbind:items={data}\n\t\t\t\ttable_width={t_width}\n\t\t\t\tmax_height={height}\n\t\t\t\tbind:actual_height={table_height}\n\t\t\t\tbind:table_scrollbar_width={scrollbar_width}\n\t\t\t\tselected={selected_index}\n\t\t\t>\n\t\t\t\t{#if label && label.length !== 0}\n\t\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t\t{/if}\n\t\t\t\t<tr slot=\"thead\">\n\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t<th\n\t\t\t\t\t\t\tclass:focus={header_edit === i || selected_header === i}\n\t\t\t\t\t\t\taria-sort={get_sort_status(value, sort_by, sort_direction)}\n\t\t\t\t\t\t\tstyle=\"width: var(--cell-width-{i});\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\tbind:value={_headers[i].value}\n\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\tedit={header_edit === i}\n\t\t\t\t\t\t\t\t\ton:keydown={end_header_edit}\n\t\t\t\t\t\t\t\t\ton:dblclick={() => edit_header(i)}\n\t\t\t\t\t\t\t\t\t{select_on_focus}\n\t\t\t\t\t\t\t\t\theader\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass:sorted={sort_by === i}\n\t\t\t\t\t\t\t\t\tclass:des={sort_by === i && sort_direction === \"des\"}\n\t\t\t\t\t\t\t\t\tclass=\"sort-button {sort_direction} \"\n\t\t\t\t\t\t\t\t\ton:click={() => handle_sort(i)}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 9 7\"\n\t\t\t\t\t\t\t\t\t\tfill=\"none\"\n\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<path d=\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</th>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\n\t\t\t\t<tr slot=\"tbody\" let:item let:index class:row_odd={index % 2 === 0}>\n\t\t\t\t\t{#each item as { value, id }, j (id)}\n\t\t\t\t\t\t<td\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ton:touchstart={() => start_edit(index, j)}\n\t\t\t\t\t\t\ton:click={() => handle_cell_click(index, j)}\n\t\t\t\t\t\t\ton:dblclick={() => start_edit(index, j)}\n\t\t\t\t\t\t\tstyle:width=\"var(--cell-width-{j})\"\n\t\t\t\t\t\t\tstyle={styling?.[index]?.[j] || \"\"}\n\t\t\t\t\t\t\tclass:focus={dequal(selected, [index, j])}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\tbind:value={data[index][j].value}\n\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\tdisplay_value={display_value?.[index]?.[j]}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\t\tedit={dequal(editing, [index, j])}\n\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\ton:blur={() => ((clear_on_focus = false), parent.focus())}\n\t\t\t\t\t\t\t\t\t{clear_on_focus}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</VirtualTable>\n\t\t</Upload>\n\t</div>\n\t{#if editable}\n\t\t<div class=\"controls-wrap\">\n\t\t\t{#if row_count[1] === \"dynamic\"}\n\t\t\t\t<span class=\"button-wrap\">\n\t\t\t\t\t<BaseButton\n\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\ton:click={(e) => (e.stopPropagation(), add_row())}\n\t\t\t\t\t>\n\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\trole=\"img\"\n\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\tpreserveAspectRatio=\"xMidYMid meet\"\n\t\t\t\t\t\t\tviewBox=\"0 0 32 32\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<path\n\t\t\t\t\t\t\t\tfill=\"currentColor\"\n\t\t\t\t\t\t\t\td=\"M24.59 16.59L17 24.17V4h-2v20.17l-7.59-7.58L6 18l10 10l10-10l-1.41-1.41z\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t{$_(\"dataframe.new_row\")}\n\t\t\t\t\t</BaseButton>\n\t\t\t\t</span>\n\t\t\t{/if}\n\t\t\t{#if col_count[1] === \"dynamic\"}\n\t\t\t\t<span class=\"button-wrap\">\n\t\t\t\t\t<BaseButton\n\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\ton:click={(e) => (e.stopPropagation(), add_col())}\n\t\t\t\t\t>\n\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\trole=\"img\"\n\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\tpreserveAspectRatio=\"xMidYMid meet\"\n\t\t\t\t\t\t\tviewBox=\"0 0 32 32\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<path\n\t\t\t\t\t\t\t\tfill=\"currentColor\"\n\t\t\t\t\t\t\t\td=\"m18 6l-1.43 1.393L24.15 15H4v2h20.15l-7.58 7.573L18 26l10-10L18 6z\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t{$_(\"dataframe.new_column\")}\n\t\t\t\t\t</BaseButton>\n\t\t\t\t</span>\n\t\t\t{/if}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.button-wrap:hover svg {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.button-wrap svg {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: -5px;\n\t}\n\n\t.label p {\n\t\tposition: relative;\n\t\tz-index: var(--layer-4);\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--block-label-text-size);\n\t}\n\n\t.table-wrap {\n\t\tposition: relative;\n\t\ttransition: 150ms;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\toverflow: hidden;\n\t}\n\n\t.table-wrap:focus-within {\n\t\toutline: none;\n\t\tbackground-color: none;\n\t}\n\n\t.dragging {\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.no-wrap {\n\t\twhite-space: nowrap;\n\t}\n\n\ttable {\n\t\tposition: absolute;\n\t\topacity: 0;\n\t\ttransition: 150ms;\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t}\n\n\tdiv:not(.no-wrap) td {\n\t\toverflow-wrap: anywhere;\n\t}\n\n\tdiv.no-wrap td {\n\t\toverflow-x: hidden;\n\t}\n\n\ttable.fixed-layout {\n\t\ttable-layout: fixed;\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: var(--layer-1);\n\t\tbox-shadow: var(--shadow-drop);\n\t}\n\n\ttr {\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t}\n\n\ttr > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth,\n\ttd {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\toutline: none;\n\t\tbox-shadow: inset 0 0 0 1px var(--ring-color);\n\t\tpadding: 0;\n\t}\n\n\tth:first-child {\n\t\tborder-top-left-radius: var(--table-radius);\n\t}\n\n\tth:last-child {\n\t\tborder-top-right-radius: var(--table-radius);\n\t}\n\n\tth.focus,\n\ttd.focus {\n\t\t--ring-color: var(--color-accent);\n\t}\n\n\ttr:last-child td:first-child {\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t}\n\n\ttr:last-child td:last-child {\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\ttr th {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\tth svg {\n\t\tfill: currentColor;\n\t\tfont-size: 10px;\n\t}\n\n\t.sort-button {\n\t\tdisplay: flex;\n\t\tflex: none;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttransition: 150ms;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tline-height: var(--text-sm);\n\t}\n\n\t.sort-button:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.des {\n\t\ttransform: scaleY(-1);\n\t}\n\n\t.sort-button.sorted {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.editing {\n\t\tbackground: var(--table-editing);\n\t}\n\n\t.cell-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\toutline: none;\n\t\theight: var(--size-full);\n\t\tmin-height: var(--size-9);\n\t}\n\n\t.controls-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tpadding-top: var(--size-2);\n\t}\n\n\t.controls-wrap > * + * {\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.row_odd {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.row_odd.focus {\n\t\tbackground: var(--background-fill-primary);\n\t}\n</style>\n"], "names": ["insert", "target", "input", "anchor", "ctx", "set_data", "t", "t_value", "dirty", "markdowncode_changes", "create_if_block_2", "span", "edit", "$$props", "value", "display_value", "styling", "header", "datatype", "latex_delimiters", "clear_on_focus", "select_on_focus", "line_breaks", "editable", "dispatch", "createEventDispatcher", "el", "use_focus", "node", "$$invalidate", "_value", "$$value", "currentTarget", "get_key", "i", "create_if_block", "height", "svelte_virtual_table_viewport", "append", "table", "thead", "tbody", "tfoot", "get_computed_px_amount", "elem", "property", "compStyle", "items", "table_width", "max_height", "actual_height", "table_scrollbar_width", "start", "end", "selected", "average_height", "bottom", "contents", "head_height", "foot_height", "height_map", "mounted", "rows", "top", "viewport", "viewport_height", "visible", "content_height", "refresh_height_map", "_items", "scrollTop", "row", "tick", "_h", "row_height", "remaining", "scrollbar_height", "filtered_height_map", "v", "a", "b", "scroll_and_render", "n", "direction", "is_in_view", "scroll_to_index", "current", "viewport_top", "handle_scroll", "e", "scroll_top", "is_start_overflow", "sortedItems", "row_top_border", "actual_border_collapsed_width", "new_start", "y", "row_heights", "remaining_height", "index", "opts", "align_end", "_itemHeight", "distance", "_opts", "onMount", "data", "p", "caption", "toggle_class", "div0", "attr", "th", "th_aria_sort_value", "set_style", "div1", "svg", "path", "td", "div", "if_block", "create_if_block_3", "editablecell_changes", "tr", "dequal", "editablecell_props", "td_style_value", "create_if_block_1", "t1_value", "t1", "if_block0", "create_if_block_5", "if_block1", "create_if_block_4", "table_1", "tr0", "tr1", "make_id", "guess_delimitaor", "text", "possibleDelimiters", "weedOut", "delimiter", "cache", "checkLength", "line", "length", "data_uri_to_blob", "data_uri", "byte_str", "mime_str", "ab", "ia", "label", "headers", "values", "col_count", "row_count", "wrap", "column_widths", "editing", "get_data_at", "col", "els", "make_headers", "_head", "fill", "_", "_id", "h", "process_data", "_values", "data_row_length", "j", "id", "_headers", "old_headers", "trigger_headers", "old_val", "get_sort_status", "name", "_sort", "get_current_indices", "acc", "arr", "_acc", "_data", "k", "start_edit", "move_cursor", "key", "current_coords", "dir", "selected_header", "is_data", "handle_keydown", "event", "header_edit", "add_row", "is_data_x", "is_data_y", "handle_cell_click", "parent", "sort_direction", "sort_by", "handle_sort", "edit_header", "_select", "end_header_edit", "add_col", "new_w", "handle_click_outside", "trigger", "blob_to_string", "blob", "reader", "handle_read", "head", "rest", "dsvFormat", "dragging", "t_width", "get_max", "_d", "max", "cells", "set_cell_widths", "widths", "scrollbar_width", "table_height", "sort_data", "_display_value", "_styling", "indices", "temp_data", "temp_display_value", "temp_styling", "originalIndex", "sortedIndex", "is_visible", "observer", "entries", "entry", "$$self", "value$1", "blur_handler", "dblclick_handler_1", "click_handler_1", "click_handler_2", "click_handler_3", "r", "selected_index"], "mappings": "mxBA+CCA,EAWCC,EAAAC,EAAAC,CAAA,gBATYC,EAAM,EAAA,CAAA,8HAANA,EAAM,EAAA,QAANA,EAAM,EAAA,CAAA,wFA8BjBA,EAAQ,CAAA,EAAGA,EAAK,CAAA,EAAGA,MAAiBA,EAAK,CAAA,GAAA,gEAAzCA,EAAQ,CAAA,EAAGA,EAAK,CAAA,EAAGA,MAAiBA,EAAK,CAAA,GAAA,KAAAC,GAAAC,EAAAC,CAAA,2EANhC,QAAAH,KAAM,eAAc,iDAGpB,uEAHAI,EAAA,IAAAC,EAAA,QAAAL,KAAM,kPAHTA,EAAK,CAAA,EAAAH,EAAAE,CAAA,4BAALC,EAAK,CAAA,CAAA,uEAxBTA,EAAI,CAAA,GAAAM,GAAAN,CAAA,0CAuBH,OAAAA,OAAa,OAAM,EAEdA,OAAa,WAAU,6HAJ1BA,EAAO,CAAA,CAAA,6EANfJ,EAoBMC,EAAAU,EAAAR,CAAA,6FAnCDC,EAAI,CAAA,iOAqBDA,EAAO,CAAA,CAAA,kJA9DH,KAAAQ,CAAa,EAAAC,EACb,CAAA,MAAAC,EAAyB,EAAE,EAAAD,EAC3B,CAAA,cAAAE,EAA+B,IAAI,EAAAF,EACnC,CAAA,QAAAG,EAAU,EAAE,EAAAH,EACZ,CAAA,OAAAI,EAAS,EAAK,EAAAJ,EACd,CAAA,SAAAK,EAMC,KAAK,EAAAL,GACN,iBAAAM,CAIR,EAAAN,EACQ,CAAA,eAAAO,EAAiB,EAAK,EAAAP,EACtB,CAAA,gBAAAQ,EAAkB,EAAK,EAAAR,EACvB,CAAA,YAAAS,EAAc,EAAI,EAAAT,EAClB,CAAA,SAAAU,EAAW,EAAI,EAAAV,EAEpB,MAAAW,EAAWC,SAEN,GAAAC,CAA2B,EAAAb,EAG7B,SAAAc,EAAUC,EAAsB,QACpCR,GACHS,EAAA,GAAAC,EAAS,EAAE,EAERT,GACHO,EAAK,OAAM,EAGZA,EAAK,MAAK,+IAQCF,EAAEK,wBACDD,EAAM,KAAA,+BAGN,cAAAE,KAAa,KACxBlB,EAAQkB,EAAc,KAAK,EAC3BR,EAAS,MAAM,0eAxBhBK,EAAA,GAAEC,EAAShB,CAAK,8YCkOY,KAAAV,MAAK,KAAa,MAAAA,MAAK,yEAD1CA,EAAO,CAAA,CAAA,EAAU,MAAA6B,EAAA7B,GAAAA,EAAK,EAAA,EAAA,KAAK,CAAC,EAAE,mBAAnC,OAAI8B,GAAA,EAAA,qMAAC9B,EAAO,CAAA,CAAA,sFAAZ,OAAI8B,GAAA,0JACiD;AAAA,MAEtD,6bAJG9B,EAAO,CAAA,EAAC,QAAUA,KAAQ,CAAC,EAAE,KAAK,QAAM+B,GAAA/B,CAAA,6XAN9BgC,EAAM,uBAAoBhC,EAAG,CAAA,EAAA,IAAA,0BAAyBA,EAAM,CAAA,EAAA,IAAA,6BAA4BA,EAAW,CAAA,EAAA,IAAA,6BAA4BA,EAAW,CAAA,EAAA,IAAA,gCAA+BA,EAAc,CAAA,EAAA,IAAA,gCANzMJ,EAwB+BC,EAAAoC,EAAAlC,CAAA,EAvB9BmC,EAsBOD,EAAAE,CAAA,EAfND,EAEOC,EAAAC,CAAA,8CACPF,EAQOC,EAAAE,CAAA,iCACPH,EAEOC,EAAAG,CAAA,+FAjBItC,EAAa,CAAA,CAAA,4FAOlBA,EAAO,CAAA,EAAC,QAAUA,KAAQ,CAAC,EAAE,KAAK,6NANEA,EAAG,CAAA,EAAA,IAAA,wCAAyBA,EAAM,CAAA,EAAA,IAAA,2CAA4BA,EAAW,CAAA,EAAA,IAAA,4CAA4BA,EAAW,CAAA,EAAA,IAAA,8CAA+BA,EAAc,CAAA,EAAA,IAAA,6JA3OpM,IAAAgC,GAAS,gBAgHJO,GAAuBC,EAAmBC,EAAgB,KAC7DD,QACG,SAEFE,EAAY,iBAAiBF,CAAI,SAE/B,SAASE,EAAU,iBAAiBD,CAAQ,CAAA,uDA/H1C,MAAAE,EAAK,EAAA,EAAAlC,GAEL,YAAAmC,CAAmB,EAAAnC,GACnB,WAAAoC,CAAkB,EAAApC,GAClB,cAAAqC,CAAqB,EAAArC,GACrB,sBAAAsC,CAA6B,EAAAtC,EAC7B,CAAA,MAAAuC,EAAQ,CAAC,EAAAvC,EACT,CAAA,IAAAwC,EAAM,CAAC,EAAAxC,GACP,SAAAyC,CAAwB,EAAAzC,EAG/B0C,EACAC,EAAS,EACTC,EACAC,EAAc,EACdC,EAAc,EACdC,EAAU,CAAA,EACVC,EACAC,EACAC,EAAM,EACNC,EACAC,EAAkB,EAClBC,EAAO,CAAA,EAIPC,GAAiB,EACN,eAAAC,GAAmBC,EAAoB,CACjD,GAAAJ,IAAoB,GAAKjB,IAAgB,SAGrC,KAAA,CAAA,UAAAsB,GAAcN,EACtBnC,EAAA,GAAAsB,EAAwBa,EAAS,YAAcA,EAAS,WAAW,EAEnEG,GAAiBJ,GAAOO,EAAYZ,GAChC,IAAAxB,EAAIkB,EAED,KAAAe,GAAiBlB,GAAcf,EAAImC,EAAO,QAAM,CAClD,IAAAE,EAAMT,EAAK5B,EAAIkB,CAAK,EACnBmB,SACJlB,EAAMnB,EAAI,CAAC,QACLsC,GAAI,EACVD,EAAMT,EAAK5B,EAAIkB,CAAK,GAEjB,IAAAqB,EAAKF,GAAK,sBAAqB,EAAG,OACjCE,IACJA,EAAKlB,GAEA,MAAAmB,EAAcd,EAAW1B,CAAC,EAAIuC,EACpCN,IAAkBO,EAClBxC,GAAK,EAGNL,EAAA,GAAAwB,EAAMnB,CAAC,EACD,MAAAyC,EAAYN,EAAO,OAAShB,EAE5BuB,EAAmBZ,EAAS,aAAeA,EAAS,aACtDY,EAAmB,IACtBT,IAAkBS,OAGfC,GAAsBjB,EAAW,OAAQkB,GAAC,OAAYA,GAAM,QAAQ,EACxEjD,EAAA,EAAA0B,EACCsB,GAAoB,OAAM,CAAEE,EAAGC,IAAMD,EAAIC,EAAG,CAAC,EAC7CH,GAAoB,MAAM,MAE3BrB,EAASmB,EAAYpB,CAAc,EACnCK,EAAW,OAASS,EAAO,aACrBG,GAAI,EACLvB,EAEMkB,GAAiBlB,OAC3BC,EAAgBiB,GAAiB,CAAC,EAElCtC,EAAA,GAAAqB,EAAgBD,CAAU,OAJ1BC,EAAgBiB,GAAiB,CAAC,QAO7BK,GAAI,EAII,eAAAS,EAAkBC,EAAiB,CACjD,sBAAqB,SAAA,CACT,GAAA,OAAAA,GAAM,SAAQ,aACnBC,EAAS,OAAUD,GAAM,SAAW,GAAQE,GAAWF,CAAC,EAC1DC,IAAc,KAGdA,IAAc,QACX,MAAAE,EAAgBH,EAAK,CAAA,SAAU,SAAS,CAAA,EAG3CC,IAAc,YACX,MAAAE,EAAgBH,EAAC,CAAI,SAAU,SAAS,EAAI,EAAI,KAKhD,SAAAE,GAAWF,EAAS,CACtB,MAAAI,EAAUxB,GAAQA,EAAKoB,EAAI9B,CAAK,MACjCkC,GAAWJ,EAAI9B,QACZ,OAEH,GAAA,CAAAkC,GAAWJ,GAAK7B,EAAM,QACnB,WAGA,KAAA,CAAA,IAAKkC,CAAiB,EAAAvB,EAAS,sBAAqB,EACpD,CAAA,IAAAD,EAAK,OAAAP,CAAW,EAAA8B,EAAQ,sBAAqB,SAEjDvB,EAAMwB,EAAe,GACjB,OAGJ/B,EAAS+B,EAAetB,EACpB,WAGD,GAaO,eAAAuB,GAAcC,EAAQ,OAC9BC,EAAa1B,EAAS,UAE5BF,EAAOL,EAAS,SACV,MAAAkC,EAAoBC,EAAY,OAASxC,EAEzCyC,EAAiBlD,GAAuBmB,EAAK,CAAC,EAAG,kBAAkB,EAEnEgC,EAAgC,EAElCH,SACGN,EAAgBO,EAAY,OAAS,EAAC,CAAI,SAAU,MAAM,CAAA,EAG7D,IAAAG,GAAY,UAEPjB,GAAI,EAAGA,GAAIhB,EAAK,OAAQgB,IAAK,EACrClB,EAAWR,EAAQ0B,EAAC,EAAIhB,EAAKgB,EAAC,EAAE,sBAAqB,EAAG,OAErD,IAAA5C,EAAI,EAEJ8D,EAAItC,EAAcmC,EAAiB,EACnCI,EAAW,CAAA,OAER/D,EAAI0D,EAAY,QAAM,CACtB,MAAAlB,GAAad,EAAW1B,CAAC,GAAKqB,EAGhC,GAFJ0C,EAAY/D,CAAC,EAAIwC,GAEbsB,EAAItB,GAAaoB,EAAgCJ,EAAU,CAE9DK,GAAY7D,EACZL,EAAA,EAAAkC,EAAMiC,GAAKtC,EAAcmC,EAAiB,EAAC,QAG5CG,GAAKtB,GACLxC,GAAK,MAGN6D,GAAY,KAAK,IAAI,EAAGA,EAAS,EAC1B7D,EAAI0D,EAAY,QAAM,CACtB,MAAAlB,GAAad,EAAW1B,CAAC,GAAKqB,KACpCyC,GAAKtB,GACLxC,GAAK,EACD8D,EAAIN,EAAazB,QAItBpC,EAAA,GAAAuB,EAAQ2C,EAAS,EACjBlE,EAAA,GAAAwB,EAAMnB,CAAC,EACD,MAAAyC,GAAYiB,EAAY,OAASvC,EACnCA,IAAQ,GACXxB,EAAA,GAAAwB,EAAM,EAAE,EAETxB,EAAA,EAAA0B,GAAkByC,EAAItC,GAAeL,CAAG,MACpC6C,GAAmBvB,GAAYpB,OAE5BrB,EAAI0D,EAAY,QACtB1D,GAAK,EACL0B,EAAW1B,CAAC,EAAIqB,EAEjB1B,EAAA,EAAA2B,EAAS0C,EAAgB,EACpB,SAAS1C,CAAM,GACnB3B,EAAA,EAAA2B,EAAS,GAAM,EAIK,eAAA6B,EACrBc,EACAC,EACAC,EAAY,GAAK,OAEX7B,GAAI,EAEJ,MAAA8B,EAAc/C,MAEhBgD,EAAWJ,EAAQG,EACnBD,IACHE,EAAWA,EAAWtC,EAAkBqC,EAAc5C,GAGjD,MAAAkB,GAAmBZ,EAAS,aAAeA,EAAS,aACtDY,GAAmB,IACtB2B,GAAY3B,UAGP4B,EAAK,CACV,IAAKD,EACL,SAAU,SACP,GAAAH,GAGJpC,EAAS,SAASwC,CAAK,EASxBC,GAAO,IAAA,CACN3C,EAAOL,EAAS,SAChB5B,EAAA,GAAAgC,EAAU,EAAI,EACdO,GAAmBrB,CAAK,iBAYgBW,EAAW,KAAA,8DAGjCD,EAAQ1B,wBASc4B,EAAW,KAAA,+DAjBxCK,EAAQjC,yBACAkC,EAAe,KAAA,+ZAjBlCpC,EAAA,GAAE+D,EAAc7C,CAAK,yBAzMfc,GAAS,sBAA4B,IAAAO,GAAmBwB,CAAW,CAAA,wBAwDvEX,EAAkB3B,CAAQ,yBAmJ1BzB,EAAA,EAAAqC,EAAU0B,EAAY,MAAMxC,EAAOC,CAAG,EAAE,IAAK,CAAAqD,EAAMxE,KAC5C,CAAA,MAAOA,EAAIkB,EAAO,KAAAsD,CAAI,0tBC4Z7BtG,EAAK,CAAA,CAAA,wCADPJ,EAEGC,EAAA0G,EAAAxG,CAAA,8BADDC,EAAK,CAAA,CAAA,wEAmBqBA,EAAK,CAAA,CAAA,iCAA/BJ,EAAyCC,EAAA2G,EAAAzG,CAAA,8BAAfC,EAAK,CAAA,CAAA,uJAgBpB,MACF,mWAMgBA,EAAc,EAAA,EAAA,iBAAA,EAFpByG,EAAAC,EAAA,SAAA1G,QAAYA,EAAC,EAAA,CAAA,EAChByG,EAAAC,EAAA,MAAA1G,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,0CAf3C2G,EAAAC,EAAA,YAAAC,EAAA7G,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,CAAA,gCAD1CyG,EAAAG,EAAA,UAAA5G,QAAgBA,EAAC,EAAA,CAAA,EAEnB8G,EAAAF,EAAA,QAAA5G,KAAc,OAASA,KAAcA,EAAC,EAAA,CAAA,EAAI,MAAS,uBAHjEJ,EA+BIC,EAAA+G,EAAA7G,CAAA,EA1BHmC,EAyBK0E,EAAAG,CAAA,sBAfJ7E,EAcK6E,EAAAL,CAAA,EATJxE,EAQKwE,EAAAM,CAAA,EADJ9E,EAAsD8E,EAAAC,CAAA,oLATnCjH,EAAc,EAAA,EAAA,uDAFpByG,EAAAC,EAAA,SAAA1G,QAAYA,EAAC,EAAA,CAAA,qBAChByG,EAAAC,EAAA,MAAA1G,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,GAf3C,CAAAkF,GAAA9E,EAAA,CAAA,EAAA,QAAAyG,KAAAA,EAAA7G,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,4CAD1CyG,EAAAG,EAAA,UAAA5G,QAAgBA,EAAC,EAAA,CAAA,cAEnB8G,EAAAF,EAAA,QAAA5G,KAAc,OAASA,KAAcA,EAAC,EAAA,CAAA,EAAI,MAAS,6MAyCpD,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,EAAA,CAAA,EAAIA,EAAQ,CAAA,OACpD,MACF,4OARPJ,EAWIC,EAAAqH,EAAAnH,CAAA,EAVHmC,EASKgF,EAAAC,CAAA,2KAJO,MAAM,QAAQnH,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,EAAA,CAAA,EAAIA,EAAQ,CAAA,2LA2BpCA,EAAK,CAAA,CAAA,iCAA/BJ,EAAyCC,EAAA2G,EAAAzG,CAAA,8BAAfC,EAAK,CAAA,CAAA,uCAD3BoH,EAAApH,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACqH,GAAArH,CAAA,kEAA3BA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,0RAgBpB,KAAAA,QAAgBA,EAAC,EAAA,mCAJXA,EAAQ,EAAA,EAACA,EAAC,EAAA,CAAA,EAAE,QAAK,iBAAjBA,EAAQ,EAAA,EAACA,EAAC,EAAA,CAAA,EAAE,OACfA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,kGAILA,EAAe,EAAA,CAAA,wZAYPA,EAAc,EAAA,EAAA,iBAAA,EAFpByG,EAAAC,EAAA,SAAA1G,QAAYA,EAAC,EAAA,CAAA,EAChByG,EAAAC,EAAA,MAAA1G,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,0CArB3C2G,EAAAC,EAAA,YAAAC,EAAA7G,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,CAAA,kCACzBA,EAAC,EAAA,EAAA,GAAA,gCAFpByG,EAAAG,EAAA,QAAA5G,EAAgB,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAoBA,EAAC,EAAA,CAAA,uBADxDJ,EAsCIC,EAAA+G,EAAA7G,CAAA,EAjCHmC,EAgCK0E,EAAAG,CAAA,sBAhBJ7E,EAeK6E,EAAAL,CAAA,EATJxE,EAQKwE,EAAAM,CAAA,EADJ9E,EAAsD8E,EAAAC,CAAA,mIAvBjD7G,EAAA,CAAA,EAAA,UAAAkH,EAAA,KAAAtH,QAAgBA,EAAC,EAAA,yEAJXA,EAAQ,EAAA,EAACA,EAAC,EAAA,CAAA,EAAE,iDACfA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,qEAgBGA,EAAc,EAAA,EAAA,uDAFpByG,EAAAC,EAAA,SAAA1G,QAAYA,EAAC,EAAA,CAAA,qBAChByG,EAAAC,EAAA,MAAA1G,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,GArB3C,CAAAkF,GAAA9E,EAAA,CAAA,EAAA,QAAAyG,KAAAA,EAAA7G,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,0EACzBA,EAAC,EAAA,EAAA,GAAA,uBAFpByG,EAAAG,EAAA,QAAA5G,EAAgB,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAoBA,EAAC,EAAA,CAAA,6IAFlDA,EAAQ,EAAA,CAAA,aAAsBA,EAAE,EAAA,kBAArC,OAAI8B,GAAA,EAAA,kKADPlC,EA0CIC,EAAA0H,EAAAxH,CAAA,8FAzCIC,EAAQ,EAAA,CAAA,8EAAb,OAAI8B,GAAA,6KAkD2B9B,EAAC,EAAA,gGAQfA,EAAa,EAAA,IAAGA,EAAK,EAAA,CAAA,IAAIA,EAAC,EAAA,CAAA,uDAInC,KAAAwH,GAAOxH,EAAU,EAAA,EAAA,CAAAA,MAAOA,EAAC,EAAA,CAAA,CAAA,WACrB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,EAAA,CAAA,EAAIA,EAAQ,CAAA,wBAP9CA,EAAK,EAAA,EAAAA,EAAO,EAAA,CAAA,EAAAA,OAAG,QAAK,SAApByH,EAAA,MAAAzH,EAAK,EAAA,EAAAA,EAAO,EAAA,CAAA,EAAAA,OAAG,OAClBA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,gWANZ2G,EAAAO,EAAA,QAAAQ,EAAA1H,EAAU,EAAA,IAAAA,EAAS,EAAA,CAAA,IAAAA,QAAM,EAAE,gCACrByG,EAAAS,EAAA,QAAAM,GAAOxH,EAAW,EAAA,EAAA,CAAAA,MAAOA,EAAC,EAAA,CAAA,CAAA,CAAA,sCAPxCJ,EAuBIC,EAAAqH,EAAAnH,CAAA,EAdHmC,EAaKgF,EAAAC,CAAA,+KATYnH,EAAa,EAAA,IAAGA,EAAK,EAAA,CAAA,IAAIA,EAAC,EAAA,CAAA,gGAInCI,EAAA,CAAA,EAAA,OAAAA,EAAA,CAAA,EAAA,WAAAkH,EAAA,KAAAE,GAAOxH,EAAU,EAAA,EAAA,CAAAA,MAAOA,EAAC,EAAA,CAAA,CAAA,oCACrB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,EAAA,CAAA,EAAIA,EAAQ,CAAA,8EAP9CsH,EAAA,MAAAtH,EAAK,EAAA,EAAAA,EAAO,EAAA,CAAA,EAAAA,OAAG,8DAClBA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,+BANZ,CAAAkF,GAAA9E,EAAA,CAAA,EAAA,KAAAA,EAAA,CAAA,EAAA,UAAAsH,KAAAA,EAAA1H,EAAU,EAAA,IAAAA,EAAS,EAAA,CAAA,IAAAA,QAAM,oDACnByG,EAAAS,EAAA,QAAAM,GAAOxH,EAAW,EAAA,EAAA,CAAAA,MAAOA,EAAC,EAAA,CAAA,CAAA,CAAA,uLARlCA,EAAI,EAAA,CAAA,aAAsBA,EAAE,EAAA,kBAAjC,OAAI8B,GAAA,EAAA,wKAD4C9B,EAAK,EAAA,EAAG,IAAM,CAAC,UAAlEJ,EA2BIC,EAAA0H,EAAAxH,CAAA,4GA1BIC,EAAI,EAAA,CAAA,mFADuCA,EAAK,EAAA,EAAG,IAAM,CAAC,+BAC/D,OAAI8B,GAAA,yOAtDM9B,EAAO,EAAA,aACRA,EAAM,CAAA,WAGRA,EAAc,EAAA,+JALZA,EAAI,EAAA,IAAA,iBAAJA,EAAI,EAAA,GAGIA,EAAY,EAAA,IAAA,yBAAZA,EAAY,EAAA,GACJA,EAAe,EAAA,IAAA,iCAAfA,EAAe,EAAA,qOAH9BA,EAAO,EAAA,2BACRA,EAAM,CAAA,uBAGRA,EAAc,EAAA,kGALZA,EAAI,EAAA,0DAGIA,EAAY,EAAA,mEACJA,EAAe,EAAA,sIAmFvCA,EAAS,CAAA,EAAC,CAAC,IAAM,WAASM,GAAAN,CAAA,IA0B1BA,EAAS,CAAA,EAAC,CAAC,IAAM,WAAS2H,GAAA3H,CAAA,oGA3BhCJ,EAqDKC,EAAAsH,EAAApH,CAAA,oDApDCC,EAAS,CAAA,EAAC,CAAC,IAAM,2GA0BjBA,EAAS,CAAA,EAAC,CAAC,IAAM,kaAzBrBJ,EAuBMC,EAAAU,EAAAR,CAAA,4MAFH6H,EAAA5H,MAAG,mBAAmB,EAAA,icAfvBJ,EAcKC,EAAAmH,EAAAjH,CAAA,EAJJmC,EAGC8E,EAAAC,CAAA,4BAED7G,EAAA,CAAA,EAAA,GAAAwH,KAAAA,EAAA5H,MAAG,mBAAmB,EAAA,KAAAC,GAAA4H,EAAAD,CAAA,iQAKzBhI,EAuBMC,EAAAU,EAAAR,CAAA,4MAFH6H,EAAA5H,MAAG,sBAAsB,EAAA,2bAf1BJ,EAcKC,EAAAmH,EAAAjH,CAAA,EAJJmC,EAGC8E,EAAAC,CAAA,4BAED7G,EAAA,CAAA,EAAA,GAAAwH,KAAAA,EAAA5H,MAAG,sBAAsB,EAAA,KAAAC,GAAA4H,EAAAD,CAAA,mHAhO1BE,EAAA9H,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAAC+H,GAAA/H,CAAA,EAoBzBgI,EAAAhI,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACiI,GAAAjI,CAAA,OAKvBA,EAAQ,EAAA,CAAA,cAAsBA,EAAE,EAAA,kBAArC,OAAI8B,GAAA,EAAA,yDAsCC9B,EAAG,EAAA,CAAA,aAAsBA,EAAE,EAAA,mBAAhC,OAAI8B,GAAA,EAAA,oFAkBF,UACE,iBACO,iBACA,+JA0FZ9B,EAAQ,CAAA,GAAA+B,GAAA/B,CAAA,oZA5JSA,EAAa,CAAA,EAAC,QAAU,CAAC,wDAR/BA,EAAY,EAAA,EAAA,IAAA,8EADXA,EAAI,CAAA,CAAA,gCAVJyG,EAAAM,EAAA,QAAA/G,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,CAAC,UAA7CJ,EAuOKC,EAAAkH,EAAAhH,CAAA,wBAjOJmC,EAwKK6E,EAAAL,CAAA,EA9JJxE,EAgEOwE,EAAAwB,CAAA,wBAxDNhG,EAqCOgG,EAAA9F,CAAA,EApCNF,EAmCIE,EAAA+F,CAAA,0DAELjG,EAiBOgG,EAAA7F,CAAA,EAhBNH,EAeIG,EAAA+F,CAAA,mKAnFGpI,EAAoB,EAAA,CAAA,yBACfA,EAAoB,EAAA,CAAA,qFAK9BA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,yDAoBxBA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,oFAKtBA,EAAQ,EAAA,CAAA,6EAsCRA,EAAG,EAAA,CAAA,qFA7CQA,EAAa,CAAA,EAAC,QAAU,CAAC,6KAR/BA,EAAY,EAAA,EAAA,IAAA,2EADXA,EAAI,CAAA,CAAA,EAqKhBA,EAAQ,CAAA,qHA/KIyG,EAAAM,EAAA,QAAA/G,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,CAAC,+BA0BtC,OAAI8B,GAAA,2BAsCJ,OAAIA,GAAA,8TAhnBDuG,IAAO,QACR,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,EAAG,EAAE,WAwXzCC,GACRC,EACAC,EAA4B,QAErBA,EAAmB,OAAOC,CAAO,EAE/B,SAAAA,EAAQC,EAAiB,CAC7B,IAAAC,KACG,OAAAJ,EAAK,MAAM;AAAA,CAAI,EAAE,MAAMK,CAAW,EAEhC,SAAAA,EAAYC,EAAY,KAC3BA,QACG,GAGJ,IAAAC,EAASD,EAAK,MAAMH,CAAS,EAAE,OAC/B,OAAAC,EAAQ,IACXA,EAAQG,GAEFH,IAAUG,GAAUA,EAAS,IAK9B,SAAAC,GAAiBC,EAAgB,OACnCC,EAAW,KAAKD,EAAS,MAAM,GAAG,EAAE,CAAC,CAAA,EACrCE,EAAWF,EAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAE5DG,EAAS,IAAA,YAAYF,EAAS,MAAM,EACpCG,EAAE,IAAO,WAAWD,CAAE,UAEnBrH,EAAI,EAAGA,EAAImH,EAAS,OAAQnH,IACpCsH,EAAGtH,CAAC,EAAImH,EAAS,WAAWnH,CAAC,EAGnB,OAAA,IAAA,KAAM,CAAAqH,CAAE,EAAK,CAAA,KAAMD,CAAQ,CAAA,yDA1d5B,SAAApI,CAA+B,EAAAL,EAC/B,CAAA,MAAA4I,EAAuB,IAAI,EAAA5I,GAC3B,QAAA6I,EAAO,EAAA,EAAA7I,EACd8I,GACO,MAAA7I,CAAkE,EAAAD,GAClE,UAAA+I,CAAwC,EAAA/I,GACxC,UAAAgJ,CAAwC,EAAAhJ,GACxC,iBAAAM,CAIR,EAAAN,EAEQ,CAAA,SAAAU,EAAW,EAAI,EAAAV,EACf,CAAA,KAAAiJ,EAAO,EAAK,EAAAjJ,EACZ,CAAA,OAAAuB,EAAS,GAAG,EAAAvB,EACZ,CAAA,YAAAS,EAAc,EAAI,EAAAT,GAClB,cAAAkJ,EAAa,EAAA,EAAAlJ,EAEpByC,EAAqC,GACrCvC,EAAmCD,GAAO,UAAU,eAAiB,KACrEE,EAA6BF,GAAO,UAAU,SAAW,KAavD,MAAAU,EAAWC,KASb,IAAAuI,EAAoC,SAElCC,EAAW,CAAI1F,EAAa2F,IACjCxD,IAAOnC,CAAG,IAAI2F,CAAG,GAAG,UASjBC,EAAG,CAAA,EAUE,SAAAC,GAAaC,EAAc,CAC/B,IAAA5F,EAAK4F,GAAK,MACVT,EAAU,CAAC,IAAM,SAAWnF,EAAG,OAASmF,EAAU,CAAC,EAAA,OAChDU,EAAO,MAAMV,EAAU,CAAC,EAAInF,EAAG,MAAM,EACzC,KAAK,EAAE,EACP,IAAG,CAAE8F,EAAGrI,IAAC,GAAQA,EAAIuC,EAAG,QAAM,EAChCA,EAAKA,EAAG,OAAO6F,CAAI,EAGf,MAAA,CAAA7F,GAAMA,EAAG,SAAW,EACjB,MAAMmF,EAAU,CAAC,CACtB,EAAA,KAAK,CAAC,EACN,IAAK,CAAAW,EAAGrI,IAAC,CACH,MAAAsI,EAAM/B,iBACZ0B,EAAIK,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAL,CAAA,GAC3B,GAAIK,EAAK,MAAO,KAAK,UAAUtI,EAAI,CAAC,KAGzCuC,EAAG,IAAK,CAAAgG,EAAGvI,IAAC,CACZ,MAAAsI,EAAM/B,iBACZ0B,EAAIK,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAL,CAAA,EAC3B,CAAA,GAAIK,EAAK,MAAOC,GAAK,EAAE,IAIzB,SAAAC,GAAaC,EAA8B,OAI7CC,EAAkBD,EAAQ,OACzB,OAAA,MACNd,EAAU,CAAC,IAAM,SAEde,EAAkBf,EAAU,CAAC,EAD7BA,EAAU,CAAC,EAGXe,CAAe,EAEjB,KAAK,CAAC,EACN,IAAK,CAAAL,EAAGrI,IACR,MACC0H,EAAU,CAAC,IAAM,QACdA,EAAU,CAAC,EACXgB,EAAkB,EAClBD,EAAQ,CAAC,EAAE,OACXjB,EAAQ,MAAM,EAEhB,KAAK,CAAC,EACN,IAAK,CAAAa,EAAGM,KAAC,CACH,MAAAC,GAAKrC,KACX,OAAA5G,EAAA,GAAAsI,EAAIW,EAAE,EAAIX,EAAIW,EAAE,GAAO,CAAA,MAAO,KAAM,KAAM,IAAI,EAAAX,CAAA,EACrC,CAAK,MAAOQ,IAAUzI,CAAC,IAAI2I,EAAC,GAAK,GAAI,GAAAC,WAO/CC,EAAWX,GAAaV,CAAO,EAC/BsB,YAQKC,IAAe,MACvBF,EAAWX,GAAaV,CAAO,CAAA,OAE/BsB,GAActB,EAAQ,MAAK,CAAA,MAOxBhD,EAAI,CAAA,CAAA,CAAA,EAEJwE,EAWK,SAAAC,EACRC,EACAC,EACAlG,EAAyB,CAEpB,GAAA,CAAAkG,QAAc,UACf3B,EAAQ2B,CAAK,IAAMD,EAAI,IACtBjG,IAAc,MAAK,MAAS,eAC5BA,IAAc,MAAK,MAAS,mBAG1B,OAGC,SAAAmG,EAAoBR,EAAU,CAC/B,OAAApE,EAAK,QACV6E,EAAKC,EAAKtJ,IAAC,OACL2I,EAAIW,EAAI,QACZC,GAAMC,GAAOC,KAAOb,IAAOY,GAAM,GAAKC,GAAIF,GAAI,EAC7C,EAGI,OAAAZ,OAAWU,EAAO,CAAArJ,EAAG2I,CAAC,GAE5B,CAAA,KAAK,kBAIMe,GAAW,EAAWf,EAAS,CACxC,CAAAtJ,GAAYqG,GAAOoC,EAAU,CAAA,EAAGa,CAAC,CAAA,QAEtCb,EAAO,CAAI,EAAGa,CAAC,CAAA,WAGPgB,GACRC,EACAC,EAAgC,OAE1BC,EAAG,CACR,WAAU,CAAG,EAAG,CAAC,EACjB,UAAS,CAAG,EAAC,EAAI,EACjB,UAAS,CAAG,EAAG,CAAC,EAChB,QAAO,CAAA,GAAO,CAAC,GACdF,CAAG,EAEC5J,EAAI6J,EAAe,CAAC,EAAIC,EAAI,CAAC,EAC7BnB,EAAIkB,EAAe,CAAC,EAAIC,EAAI,CAAC,EAE/B,GAAA9J,EAAI,GAAK2I,GAAK,EACjBhJ,EAAA,GAAAoK,EAAkBpB,CAAC,EACnBhJ,EAAA,GAAAyB,EAAW,EAAK,OAEV,MAAA4I,GAAUxF,EAAKxE,CAAC,IAAI2I,CAAC,EAC3BhJ,EAAA,GAAAyB,EAAW4I,GAAO,CAAIhK,EAAG2I,CAAC,EAAIvH,CAAQ,GAIpC,IAAAlC,EAAiB,GAEN,eAAA+K,EAAeC,EAAoB,CAC7C,GAAAH,IAAoB,IAASI,IAAgB,GACxC,OAAAD,EAAM,IAAG,KACX,iBACJ9I,EAAQ,CAAI,EAAG2I,CAAe,CAAA,EAC9BpK,EAAA,GAAAoK,EAAkB,EAAK,aAEnB,iBACJA,EACCA,EAAkB,EAAIA,EAAkB,EAAIA,CAAe,aAExD,aACJpK,EAAA,GAAAoK,EACCA,EAAkBlB,EAAS,OAAS,EACjCkB,EAAkB,EAClBA,CAAe,aAEf,SACJG,EAAM,eAAc,EACpBvK,EAAA,GAAAoK,EAAkB,EAAK,YAEnB,QACJG,EAAM,eAAc,YAIlB9I,eAIEpB,EAAG2I,CAAC,EAAIvH,EAEP,OAAA8I,EAAM,IAAG,KACX,iBACA,gBACA,gBACA,aACApC,EAAO,MACXoC,EAAM,eAAc,EACpBP,GAAYO,EAAM,IAAM,CAAAlK,EAAG2I,CAAC,CAAA,YAGxB,aACCtJ,EAAQ,MACb6K,EAAM,eAAc,EACpBvK,EAAA,GAAAmI,EAAU,EAAK,YAEX,YACCzI,EAAQ,MACb6K,EAAM,eAAc,EAEhBA,EAAM,UACTE,GAAQpK,CAAC,QACHsC,GAAI,EAEV3C,EAAA,GAAAyB,EAAY,CAAApB,EAAI,EAAG2I,CAAC,CAAA,GAEhBjD,GAAOoC,EAAU,CAAA9H,EAAG2I,CAAC,CAAA,GACxBhJ,EAAA,GAAAmI,EAAU,EAAK,QACTxF,GAAI,OACVlB,EAAQ,CAAIpB,EAAG2I,CAAC,CAAA,QAEhBb,EAAO,CAAI9H,EAAG2I,CAAC,CAAA,YAKb,gBACCtJ,EAAQ,MACRyI,IACJoC,EAAM,eAAc,EACpBvK,EAAA,GAAA6E,EAAKxE,CAAC,EAAE2I,CAAC,EAAE,MAAQ,GAAEnE,CAAA,aAGlB,aACCnF,EAAQ,MACRyI,IACJoC,EAAM,eAAc,EACpBvK,EAAA,GAAA6E,EAAKxE,CAAC,EAAE2I,CAAC,EAAE,MAAQ,GAAEnE,CAAA,aAGlB,MACA,IAAAvB,EAAYiH,EAAM,SAAY,GAAI,EAElCG,EAAY7F,EAAKxE,CAAC,EAAE2I,EAAI1F,CAAS,EACjCqH,GACH9F,IAAOxE,EAAIiD,CAAS,IAAIA,EAAY,EAAI,EAAI4F,EAAS,OAAS,CAAC,GAE5DwB,GAAaC,MAChBJ,EAAM,eAAc,EACpBvK,EAAA,GAAAyB,EAAWiJ,GACPrK,EAAG2I,EAAI1F,CAAS,EAChB,CAAAjD,EAAIiD,EAAWA,EAAY,EAAI,EAAI4F,EAAS,OAAS,CAAC,CAAA,GAE3DlJ,EAAA,GAAAmI,EAAU,EAAK,oBAIVzI,EAAQ,OAEV,CAAAyI,GAAYA,GAAWpC,GAAOoC,EAAU,CAAA9H,EAAG2I,CAAC,KAC9CuB,EAAM,IAAI,SAAW,IAErBvK,EAAA,GAAAT,EAAiB,EAAI,OACrB4I,EAAO,CAAI9H,EAAG2I,CAAC,CAAA,mBAKJ4B,EAAkB,EAAW5B,EAAS,CAChDjD,GAAOoC,EAAU,CAAA,EAAGa,CAAC,CAAA,IACzBhJ,EAAA,GAAAwK,EAAc,EAAK,EACnBxK,EAAA,GAAAoK,EAAkB,EAAK,EACvBpK,EAAA,GAAAmI,EAAU,EAAK,OACf1G,EAAQ,CAAI,EAAGuH,CAAC,CAAA,QACVrG,GAAI,EACVkI,GAAO,MAAK,OAITC,EACAC,EAEK,SAAAC,GAAY3C,EAAW,CACpB,OAAA0C,GAAY,UAAYA,IAAY1C,GAC9CrI,EAAA,GAAA8K,EAAiB,KAAK,EACtB9K,EAAA,GAAA+K,EAAU1C,CAAG,GAETyC,IAAmB,MACtB9K,EAAA,GAAA8K,EAAiB,KAAK,EACZA,IAAmB,OAC7B9K,EAAA,GAAA8K,EAAiB,KAAK,MAKrBN,EAEAhL,EAAkB,GAClB4K,EAAkC,GACvB,eAAAa,GAAY,EAAWC,EAAU,GAAK,EAC/CxL,GAAYqI,EAAU,CAAC,IAAM,WAAayC,IAAgB,IAC/DxK,EAAA,GAAAyB,EAAW,EAAK,EAChBzB,EAAA,GAAAoK,EAAkB,CAAC,EACnBpK,EAAA,GAAAwK,EAAc,CAAC,EACfxK,EAAA,GAAAR,EAAkB0L,CAAO,GAGjB,SAAAC,GAAgBZ,EAAoB,IACvC7K,EAEG,OAAA6K,EAAM,IAAG,KACX,aACA,YACA,MACJA,EAAM,eAAc,EACpBvK,EAAA,GAAAyB,EAAW,EAAK,EAChBzB,EAAA,GAAAoK,EAAkBI,CAAW,EAC7BxK,EAAA,GAAAwK,EAAc,EAAK,EACnBK,GAAO,MAAK,SAKA,eAAAJ,GAAQnG,EAAc,IACpCuG,GAAO,MAAK,EAER7C,EAAU,CAAC,IAAM,cACjBnD,EAAK,SAAW,EAAC,MACpBiD,EAAM,CAAI,MAAMD,EAAQ,MAAM,EAAE,KAAK,EAAE,CAAA,CAAA,SAIxChD,EAAK,OACJP,EAAQA,EAAQ,EAAIO,EAAK,OACzB,EACA,MAAMA,EAAK,CAAC,EAAE,MAAM,EAClB,KAAK,CAAC,EACN,IAAK,CAAA6D,EAAGrI,IAAC,CACH,MAAAsI,EAAM/B,iBAEZ0B,EAAIK,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAL,CAAA,EAC3B,CAAA,GAAIK,EAAK,MAAO,EAAE,qCAK9B3I,EAAA,GAAAyB,EAAY,CAAA6C,EAAQA,EAAQ,EAAIO,EAAK,OAAS,EAAG,CAAC,CAAA,kBAGpCuG,IAAO,IACrBP,GAAO,MAAK,EACR9C,EAAU,CAAC,IAAM,mBACZ,EAAI,EAAG,EAAIlD,EAAK,OAAQ,IAAC,CAC3B,MAAA8D,EAAM/B,UACZ0B,EAAIK,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAL,CAAA,EACpCzD,EAAK,CAAC,EAAE,KAAI,CAAG,GAAI8D,EAAK,MAAO,EAAE,CAAA,EAGlCd,EAAQ,KAAI,UAAWA,EAAQ,OAAS,GAAC,gEAKnClF,GAAI,EAEV,sBAAqB,IAAA,CACpBsI,GAAYpD,EAAQ,OAAS,EAAG,EAAI,QAC9BwD,EAAQR,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,YAClDA,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,SAAQ,CAAG,KAAMQ,CAAK,CAAA,KAInD,SAAAC,GAAqBf,EAAY,CACzCA,EAAM,yBAAwB,QACvBgB,CAAO,EAAIhB,EAAM,aAAY,EAChCM,GAAO,SAASU,CAAO,IAI3BvL,EAAA,GAAAmI,EAAU,EAAK,EACfnI,EAAA,GAAAwK,EAAc,EAAK,EACnBxK,EAAA,GAAAoK,EAAkB,EAAK,EACvBpK,EAAA,GAAAyB,EAAW,EAAK,GAyCR,SAAA+J,GAAeC,EAAU,CAC3B,MAAAC,MAAa,WAEV,SAAAC,EAAY/H,EAA4B,KAC3CA,GAAG,QAAQ,QAAM,OAAWA,EAAE,OAAO,QAAW,SAAQ,aAEtDqD,CAAS,EAAIJ,GAAiBjD,EAAE,OAAO,OAAM,CAAG,IAAK,GAAI,CAAA,EAEzD,CAAAgI,GAAS,GAAAC,EAAI,EAAIC,GAAU7E,CAAS,EAAE,UAAUrD,EAAE,OAAO,MAAM,EAEtE5D,EAAA,GAAAkJ,EAAWX,GACVR,EAAU,CAAC,IAAM,QAAU6D,GAAK,MAAM,EAAG7D,EAAU,CAAC,CAAA,EAAK6D,EAAI,CAAA,EAG9D5L,EAAA,GAAA8H,EAAS+D,EAAI,EACbH,EAAO,oBAAoB,UAAWC,CAAW,EAGlDD,EAAO,iBAAiB,UAAWC,CAAW,EAE9CD,EAAO,WAAWD,CAAI,EAGnB,IAAAM,GAAW,GAEXC,GAAU,EAEL,SAAAC,GACRC,EAAkC,CAE9B,IAAAC,EAAMD,EAAG,CAAC,EAAE,MAAK,UACZ7L,EAAI,EAAGA,EAAI6L,EAAG,OAAQ7L,YACrB2I,EAAI,EAAGA,EAAIkD,EAAG7L,CAAC,EAAE,OAAQ2I,IAC1B,GAAAmD,EAAInD,CAAC,EAAE,QAAQ,OAAM,GAAMkD,EAAG7L,CAAC,EAAE2I,CAAC,EAAE,QAAQ,SAClDmD,EAAInD,CAAC,EAAIkD,EAAG7L,CAAC,EAAE2I,CAAC,UAKZmD,MAMJC,GAAK,CAAA,EACLvB,GACAnK,YAEK2L,IAAe,CACjB,MAAAC,EAASF,GAAM,IAAK,CAAAvM,EAAIQ,IACtBR,GAAI,aAAe,MAEvByM,EAAO,SAAW,UACbjM,EAAI,EAAGA,EAAIiM,EAAO,OAAQjM,IAClCwK,GAAO,MAAM,4BACIxK,IAAC,GACdiM,EAAOjM,CAAC,EAAIkM,GAAkBD,EAAO,UAAM,EAK7C,IAAAE,GAAuBjM,EACvBgM,GAAkB,WAEbE,GACR5C,EACA6C,EACAC,EACAtE,EACA8B,EAAmB,CAEf,IAAAlB,GAAK,QAELxH,GAAYA,EAAS,CAAC,IAAKoD,GAAQpD,EAAS,CAAC,IAAKoD,EAAKpD,EAAS,CAAC,CAAA,IACpEwH,GAAKpE,EAAKpD,EAAS,CAAC,CAAA,EAAGA,EAAS,CAAC,CAAA,EAAG,WAE1B4G,GAAQ,UAAQ,CAAK8B,SAG1B,MAAAyC,OAAc,MAAM/C,EAAM,MAAM,EAAE,KAAI,CAAA,EAExC,GAAAM,IAAQ,MACXyC,GAAQ,KAAI,CAAEvM,GAAG2I,KAChBa,EAAMxJ,EAAC,EAAEgI,CAAG,EAAE,MAAQwB,EAAMb,EAAC,EAAEX,CAAG,EAAE,MAAK,GAAQ,CAAC,UAEzC8B,IAAQ,MAClByC,GAAQ,KAAI,CAAEvM,GAAG2I,KAChBa,EAAMxJ,EAAC,EAAEgI,CAAG,EAAE,MAAQwB,EAAMb,EAAC,EAAEX,CAAG,EAAE,MAAK,GAAQ,CAAC,cAO9C,MAAAwE,OAAgBhD,CAAK,EACrBiD,GAAqBJ,EAAqB,CAAA,GAAAA,CAAc,EAAI,KAC5DK,GAAeJ,EAAe,CAAA,GAAAA,CAAQ,EAAI,QAChDC,GAAQ,QAAS,CAAAI,GAAeC,KAAW,CAC1CpD,EAAMoD,EAAW,EAAIJ,GAAUG,EAAa,EACxCN,GAAkBI,KACrBJ,EAAeO,EAAW,EAAIH,GAAmBE,EAAa,GAC3DL,GAAYI,KACfJ,EAASM,EAAW,EAAIF,GAAaC,EAAa,qCAKhD/D,GAAE,CACE,KAAA,CAAA5I,GAAG2I,EAAC,EAAIS,EAAoBR,EAAE,OACrCxH,EAAQ,CAAIpB,GAAG2I,EAAC,CAAA,GAQd,IAAAkE,GAAa,GACjBtI,GAAO,IAAA,CACA,MAAAuI,EAAe,IAAA,qBAAsB,CAAAC,EAASD,IAAQ,CAC3DC,EAAQ,QAASC,GAAK,CACjBA,EAAM,gBAAc,CAAKH,KAC5Bb,sCAIDa,GAAaG,EAAM,mBAIrB,OAAAF,EAAS,QAAQtC,EAAM,OAGtBsC,EAAS,WAAU,kBAQJd,kDAoEiBD,GAAMpD,CAAC,EAAA9I,2BAhDrB8L,GAAO,KAAA,gEACdtL,GAAKR,gCA2IEoN,EAAA,GAAA,UAAAzI,EAAKP,CAAK,EAAE0E,CAAC,EAAE,MAAKuE,CAAA,IAApB1I,EAAKP,CAAK,EAAE0E,CAAC,EAAE,MAAKuE,mEACvBjF,EAAIW,CAAE,EAAE,MAAKhK,CAAA,IAAbqJ,EAAIW,CAAE,EAAE,MAAKhK,WAOL,MAAAuO,GAAA,KAAAxN,EAAA,GAAAT,EAAiB,EAAK,EAAGsL,GAAO,MAAK,aAjBnCd,GAAWzF,EAAO0E,CAAC,YACxB4B,EAAkBtG,EAAO0E,CAAC,YACvBe,GAAWzF,EAAO0E,CAAC,kCAzCxBE,EAAS7I,CAAC,EAAE,MAAKpB,CAAA,IAAjBiK,EAAS7I,CAAC,EAAE,MAAKpB,2CACpBqJ,EAAIW,CAAE,EAAE,MAAKhK,CAAA,IAAbqJ,EAAIW,CAAE,EAAE,MAAKhK,WAKH,MAAAwO,GAAA,GAAAxC,GAAY,CAAC,EAYhByC,GAAA,GAAA1C,GAAY,CAAC,iBArCtBnG,EAAI0I,iDAGIf,GAAYvN,0BACJsN,GAAetN,iDARlC2E,GAAM4H,GAAelE,GAAiB1D,EAAE,OAAO,IAAI,CAAA,6CA/EnDiH,GAAM3K,sBAKJ0D,GAAM0G,EAAe1G,CAAC,EA0KpB+J,GAAA/J,IAAOA,EAAE,kBAAmB6G,GAAO,GA0BnCmD,GAAAhK,IAAOA,EAAE,kBAAmBwH,GAAO,wfAtyB9CnM,QACH4I,EAAU5I,EAAM,OAAO,OACvB6I,EAAS7I,EAAM,IAAI,EACnBe,EAAA,GAAAd,EAAgBD,GAAO,UAAU,eAAiB,IAAI,EACtDe,EAAA,GAAAb,EAAUF,GAAO,UAAU,SAAW,IAAI,GAChC6I,IAAW,WACrBA,EAAM,CAAA,CAAA,uBAkBHrG,IAAa,GAAK,OACdiB,EAAK2F,CAAG,EAAI5G,EACd,CAAA,MAAMiB,CAAG,GAAM,CAAA,MAAM2F,CAAG,GAC5B1I,EAAS,SAAQ,CAAI,MAAK,CAAG+C,EAAK2F,CAAG,EAAG,MAAOD,EAAY1F,EAAK2F,CAAG,2BA2EhEtC,GAAO8B,EAASsB,EAAW,GAC/BC,6BASMrD,GAAO+B,EAAQuB,CAAO,SAC7BxE,EAAOgE,GAAaf,CAA+B,CAAA,EACnD9H,EAAA,GAAAqJ,EAAUvB,CAA+B,yBAOvCoB,GACFvJ,EAAS,SAAQ,CAChB,KAAMkF,EAAK,IAAKgJ,GAAMA,EAAE,IAAO,CAAA,CAAA,MAAA5O,KAAYA,CAAK,CAAA,EAChD,QAASiK,EAAS,IAAKN,GAAMA,EAAE,KAAK,EACpC,SAAUlJ,EACP,KACiB,CAAA,cAAAR,EAAwB,QAAAC,CAAO,yBAgXlDa,EAAA,GAAAmM,EAAMF,GAAQpH,CAAI,CAAA,wBAElBuH,GAAM,CAAC,GAAKC,2BAsEZI,GAAU5H,EAAM3F,EAAeC,EAAS4L,EAASD,CAAc,2BAE/DgD,EAAc,CAAA,CAAKrM,GAAYA,EAAS,CAAC,CAAA"}