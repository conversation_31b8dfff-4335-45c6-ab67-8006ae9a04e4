import{S as He,e as Ce,s as Ee,m as E,F as Q,o as x,g as f,N as W,h as g,G as R,j as ze,r as Y,u as v,v as A,w as y,k,H as I,C as Ne,am as Be,an as De,t as Se,x as Ke,a7 as X,E as H,R as K,ap as C,af as Le,p as b,b as Ue,B as U,Q as $,n as L,y as je,z as qe}from"./index-7674dbb6.js";import{f as Fe}from"./Button-770df9ba.js";import{B as Ge}from"./BlockTitle-2eb1c338.js";import{C as Qe,a as Re}from"./Copy-bc542573.js";function Ye(t){let e;return{c(){e=Se(t[3])},m(l,o){g(l,e,o)},p(l,o){o[0]&8&&Ke(e,l[3])},d(l){l&&k(e)}}}function Ae(t){let e,l,o,i,s,a,d,c,r=t[6]&&t[10]&&Z(t);return{c(){r&&r.c(),e=x(),l=E("textarea"),f(l,"data-testid","textbox"),f(l,"class","scroll-hide svelte-1f354aw"),f(l,"dir",o=t[11]?"rtl":"ltr"),f(l,"placeholder",t[2]),f(l,"rows",t[1]),l.disabled=t[5],l.autofocus=t[12],f(l,"style",i=t[13]?"text-align: "+t[13]:"")},m(u,_){r&&r.m(u,_),g(u,e,_),g(u,l,_),C(l,t[0]),t[38](l),a=!0,t[12]&&l.focus(),d||(c=[Le(s=t[20].call(null,l,t[0])),b(l,"input",t[37]),b(l,"keypress",t[18]),b(l,"blur",t[29]),b(l,"select",t[17]),b(l,"focus",t[30]),b(l,"scroll",t[19])],d=!0)},p(u,_){u[6]&&u[10]?r?(r.p(u,_),_[0]&1088&&y(r,1)):(r=Z(u),r.c(),y(r,1),r.m(e.parentNode,e)):r&&(Y(),v(r,1,1,()=>{r=null}),A()),(!a||_[0]&2048&&o!==(o=u[11]?"rtl":"ltr"))&&f(l,"dir",o),(!a||_[0]&4)&&f(l,"placeholder",u[2]),(!a||_[0]&2)&&f(l,"rows",u[1]),(!a||_[0]&32)&&(l.disabled=u[5]),(!a||_[0]&4096)&&(l.autofocus=u[12]),(!a||_[0]&8192&&i!==(i=u[13]?"text-align: "+u[13]:""))&&f(l,"style",i),s&&Ue(s.update)&&_[0]&1&&s.update.call(null,u[0]),_[0]&1&&C(l,u[0])},i(u){a||(y(r),a=!0)},o(u){v(r),a=!1},d(u){u&&(k(e),k(l)),r&&r.d(u),t[38](null),d=!1,U(c)}}}function Ie(t){let e;function l(s,a){if(s[9]==="text")return Ve;if(s[9]==="password")return Pe;if(s[9]==="email")return Oe}let o=l(t),i=o&&o(t);return{c(){i&&i.c(),e=$()},m(s,a){i&&i.m(s,a),g(s,e,a)},p(s,a){o===(o=l(s))&&i?i.p(s,a):(i&&i.d(1),i=o&&o(s),i&&(i.c(),i.m(e.parentNode,e)))},i:L,o:L,d(s){s&&k(e),i&&i.d(s)}}}function Z(t){let e,l,o,i;const s=[Me,Je],a=[];function d(c,r){return c[15]?0:1}return e=d(t),l=a[e]=s[e](t),{c(){l.c(),o=$()},m(c,r){a[e].m(c,r),g(c,o,r),i=!0},p(c,r){let u=e;e=d(c),e===u?a[e].p(c,r):(Y(),v(a[u],1,1,()=>{a[u]=null}),A(),l=a[e],l?l.p(c,r):(l=a[e]=s[e](c),l.c()),y(l,1),l.m(o.parentNode,o))},i(c){i||(y(l),i=!0)},o(c){v(l),i=!1},d(c){c&&k(o),a[e].d(c)}}}function Je(t){let e,l,o,i,s;return l=new Qe({}),{c(){e=E("button"),Q(l.$$.fragment),f(e,"aria-label","Copy"),f(e,"aria-roledescription","Copy text"),f(e,"class","svelte-1f354aw")},m(a,d){g(a,e,d),R(l,e,null),o=!0,i||(s=b(e,"click",t[16]),i=!0)},p:L,i(a){o||(y(l.$$.fragment,a),o=!0)},o(a){v(l.$$.fragment,a),o=!1},d(a){a&&k(e),I(l),i=!1,s()}}}function Me(t){let e,l,o,i;return l=new Re({}),{c(){e=E("button"),Q(l.$$.fragment),f(e,"aria-label","Copied"),f(e,"aria-roledescription","Text copied"),f(e,"class","svelte-1f354aw")},m(s,a){g(s,e,a),R(l,e,null),i=!0},p:L,i(s){i||(y(l.$$.fragment,s),s&&(o||je(()=>{o=qe(e,Fe,{duration:300}),o.start()})),i=!0)},o(s){v(l.$$.fragment,s),i=!1},d(s){s&&k(e),I(l)}}}function Oe(t){let e,l,o;return{c(){e=E("input"),f(e,"data-testid","textbox"),f(e,"type","email"),f(e,"class","scroll-hide svelte-1f354aw"),f(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[12],f(e,"autocomplete","email")},m(i,s){g(i,e,s),C(e,t[0]),t[36](e),t[12]&&e.focus(),l||(o=[b(e,"input",t[35]),b(e,"keypress",t[18]),b(e,"blur",t[27]),b(e,"select",t[17]),b(e,"focus",t[28])],l=!0)},p(i,s){s[0]&4&&f(e,"placeholder",i[2]),s[0]&32&&(e.disabled=i[5]),s[0]&4096&&(e.autofocus=i[12]),s[0]&1&&e.value!==i[0]&&C(e,i[0])},d(i){i&&k(e),t[36](null),l=!1,U(o)}}}function Pe(t){let e,l,o;return{c(){e=E("input"),f(e,"data-testid","password"),f(e,"type","password"),f(e,"class","scroll-hide svelte-1f354aw"),f(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[12],f(e,"autocomplete","")},m(i,s){g(i,e,s),C(e,t[0]),t[34](e),t[12]&&e.focus(),l||(o=[b(e,"input",t[33]),b(e,"keypress",t[18]),b(e,"blur",t[25]),b(e,"select",t[17]),b(e,"focus",t[26])],l=!0)},p(i,s){s[0]&4&&f(e,"placeholder",i[2]),s[0]&32&&(e.disabled=i[5]),s[0]&4096&&(e.autofocus=i[12]),s[0]&1&&e.value!==i[0]&&C(e,i[0])},d(i){i&&k(e),t[34](null),l=!1,U(o)}}}function Ve(t){let e,l,o,i,s;return{c(){e=E("input"),f(e,"data-testid","textbox"),f(e,"type","text"),f(e,"class","scroll-hide svelte-1f354aw"),f(e,"dir",l=t[11]?"rtl":"ltr"),f(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[12],f(e,"style",o=t[13]?"text-align: "+t[13]:"")},m(a,d){g(a,e,d),C(e,t[0]),t[32](e),t[12]&&e.focus(),i||(s=[b(e,"input",t[31]),b(e,"keypress",t[18]),b(e,"blur",t[23]),b(e,"select",t[17]),b(e,"focus",t[24])],i=!0)},p(a,d){d[0]&2048&&l!==(l=a[11]?"rtl":"ltr")&&f(e,"dir",l),d[0]&4&&f(e,"placeholder",a[2]),d[0]&32&&(e.disabled=a[5]),d[0]&4096&&(e.autofocus=a[12]),d[0]&8192&&o!==(o=a[13]?"text-align: "+a[13]:"")&&f(e,"style",o),d[0]&1&&e.value!==a[0]&&C(e,a[0])},d(a){a&&k(e),t[32](null),i=!1,U(s)}}}function We(t){let e,l,o,i,s,a;l=new Ge({props:{show_label:t[6],info:t[4],$$slots:{default:[Ye]},$$scope:{ctx:t}}});const d=[Ie,Ae],c=[];function r(u,_){return u[1]===1&&u[8]===1?0:1}return i=r(t),s=c[i]=d[i](t),{c(){e=E("label"),Q(l.$$.fragment),o=x(),s.c(),f(e,"class","svelte-1f354aw"),W(e,"container",t[7])},m(u,_){g(u,e,_),R(l,e,null),ze(e,o),c[i].m(e,null),a=!0},p(u,_){const m={};_[0]&64&&(m.show_label=u[6]),_[0]&16&&(m.info=u[4]),_[0]&8|_[1]&131072&&(m.$$scope={dirty:_,ctx:u}),l.$set(m);let z=i;i=r(u),i===z?c[i].p(u,_):(Y(),v(c[z],1,1,()=>{c[z]=null}),A(),s=c[i],s?s.p(u,_):(s=c[i]=d[i](u),s.c()),y(s,1),s.m(e,null)),(!a||_[0]&128)&&W(e,"container",u[7])},i(u){a||(y(l.$$.fragment,u),y(s),a=!0)},o(u){v(l.$$.fragment,u),v(s),a=!1},d(u){u&&k(e),I(l),c[i].d()}}}function Xe(t,e,l){let{value:o=""}=e,{value_is_output:i=!1}=e,{lines:s=1}=e,{placeholder:a="Type here..."}=e,{label:d}=e,{info:c=void 0}=e,{disabled:r=!1}=e,{show_label:u=!0}=e,{container:_=!0}=e,{max_lines:m}=e,{type:z="text"}=e,{show_copy_button:J=!1}=e,{rtl:M=!1}=e,{autofocus:O=!1}=e,{text_align:P=void 0}=e,{autoscroll:D=!0}=e,h,j=!1,q,F,V=0,G=!1;const N=Ne();Be(()=>{F=h&&h.offsetHeight+h.scrollTop>h.scrollHeight-100});const ee=()=>{F&&D&&!G&&h.scrollTo(0,h.scrollHeight)};function le(){N("change",o),i||N("input")}De(()=>{F&&D&&ee(),l(21,i=!1)});async function te(){"clipboard"in navigator&&(await navigator.clipboard.writeText(o),ne())}function ne(){l(15,j=!0),q&&clearTimeout(q),q=setTimeout(()=>{l(15,j=!1)},1e3)}function ie(n){const p=n.target,T=p.value,w=[p.selectionStart,p.selectionEnd];N("select",{value:T.substring(...w),index:w})}async function se(n){await X(),(n.key==="Enter"&&n.shiftKey&&s>1||n.key==="Enter"&&!n.shiftKey&&s===1&&m>=1)&&(n.preventDefault(),N("submit"))}function oe(n){const p=n.target,T=p.scrollTop;T<V&&(G=!0),V=T;const w=p.scrollHeight-p.clientHeight;T>=w&&(G=!1)}async function S(n){if(await X(),s===m||!_)return;let p=m===void 0?!1:m===void 0?21*11:21*(m+1),T=21*(s+1);const w=n.target;w.style.height="1px";let B;p&&w.scrollHeight>p?B=p:w.scrollHeight<T?B=T:B=w.scrollHeight,w.style.height=`${B}px`}function ae(n,p){if(s!==m&&(n.style.overflowY="scroll",n.addEventListener("input",S),!!p.trim()))return S({target:n}),{destroy:()=>n.removeEventListener("input",S)}}function ue(n){H.call(this,t,n)}function fe(n){H.call(this,t,n)}function re(n){H.call(this,t,n)}function _e(n){H.call(this,t,n)}function ce(n){H.call(this,t,n)}function de(n){H.call(this,t,n)}function be(n){H.call(this,t,n)}function he(n){H.call(this,t,n)}function me(){o=this.value,l(0,o)}function pe(n){K[n?"unshift":"push"](()=>{h=n,l(14,h)})}function ge(){o=this.value,l(0,o)}function ke(n){K[n?"unshift":"push"](()=>{h=n,l(14,h)})}function we(){o=this.value,l(0,o)}function ye(n){K[n?"unshift":"push"](()=>{h=n,l(14,h)})}function ve(){o=this.value,l(0,o)}function Te(n){K[n?"unshift":"push"](()=>{h=n,l(14,h)})}return t.$$set=n=>{"value"in n&&l(0,o=n.value),"value_is_output"in n&&l(21,i=n.value_is_output),"lines"in n&&l(1,s=n.lines),"placeholder"in n&&l(2,a=n.placeholder),"label"in n&&l(3,d=n.label),"info"in n&&l(4,c=n.info),"disabled"in n&&l(5,r=n.disabled),"show_label"in n&&l(6,u=n.show_label),"container"in n&&l(7,_=n.container),"max_lines"in n&&l(8,m=n.max_lines),"type"in n&&l(9,z=n.type),"show_copy_button"in n&&l(10,J=n.show_copy_button),"rtl"in n&&l(11,M=n.rtl),"autofocus"in n&&l(12,O=n.autofocus),"text_align"in n&&l(13,P=n.text_align),"autoscroll"in n&&l(22,D=n.autoscroll)},t.$$.update=()=>{t.$$.dirty[0]&1&&o===null&&l(0,o=""),t.$$.dirty[0]&16643&&h&&s!==m&&S({target:h}),t.$$.dirty[0]&1&&le()},[o,s,a,d,c,r,u,_,m,z,J,M,O,P,h,j,te,ie,se,oe,ae,i,D,ue,fe,re,_e,ce,de,be,he,me,pe,ge,ke,we,ye,ve,Te]}class ll extends He{constructor(e){super(),Ce(this,e,Xe,We,Ee,{value:0,value_is_output:21,lines:1,placeholder:2,label:3,info:4,disabled:5,show_label:6,container:7,max_lines:8,type:9,show_copy_button:10,rtl:11,autofocus:12,text_align:13,autoscroll:22},null,[-1,-1])}}export{ll as T};
//# sourceMappingURL=Textbox-5df53a1e.js.map
