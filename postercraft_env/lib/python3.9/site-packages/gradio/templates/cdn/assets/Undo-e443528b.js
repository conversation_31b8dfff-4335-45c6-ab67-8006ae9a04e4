import{S as c,e as h,s as d,f as a,g as t,h as f,j as l,n as r,k as u}from"./index-7674dbb6.js";function g(i){let e,s,n;return{c(){e=a("svg"),s=a("polyline"),n=a("path"),t(s,"points","1 4 1 10 7 10"),t(n,"d","M3.51 15a9 9 0 1 0 2.13-9.36L1 10"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-rotate-ccw")},m(o,p){f(o,e,p),l(e,s),l(e,n)},p:r,i:r,o:r,d(o){o&&u(e)}}}class m extends c{constructor(e){super(),h(this,e,null,g,d,{})}}export{m as U};
//# sourceMappingURL=Undo-e443528b.js.map
