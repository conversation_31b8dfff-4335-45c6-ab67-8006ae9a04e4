{"version": 3, "file": "color-253fe719.js", "sources": ["../../../../js/utils/src/color.ts"], "sourcesContent": ["import { colors, ordered_colors } from \"@gradio/theme\";\n\nexport const get_next_color = (index: number): keyof typeof colors => {\n\treturn ordered_colors[index % ordered_colors.length];\n};\n"], "names": ["get_next_color", "index", "ordered_colors"], "mappings": "yCAEa,MAAAA,EAAkBC,GACvBC,EAAeD,EAAQC,EAAe,MAAM"}