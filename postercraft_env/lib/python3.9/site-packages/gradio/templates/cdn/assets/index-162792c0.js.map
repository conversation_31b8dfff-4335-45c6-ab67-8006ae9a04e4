{"version": 3, "file": "index-162792c0.js", "sources": ["../../../../js/model3D/interactive/Model3DUpload.svelte", "../../../../js/model3D/interactive/InteractiveModel3d.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, tick, onMount } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\timport { add_new_model, reset_camera_position } from \"../shared/utils\";\n\n\texport let value: null | FileData;\n\texport let clear_color: [number, number, number, number] = [0, 0, 0, 0];\n\texport let label = \"\";\n\texport let show_label: boolean;\n\texport let zoom_speed = 1;\n\n\t// alpha, beta, radius\n\texport let camera_position: [number | null, number | null, number | null] = [\n\t\tnull,\n\t\tnull,\n\t\tnull\n\t];\n\n\tlet mounted = false;\n\tlet canvas: HTMLCanvasElement;\n\tlet scene: BABYLON.Scene;\n\tlet engine: BABYLON.Engine;\n\n\tfunction reset_scene(): void {\n\t\tscene = add_new_model(\n\t\t\tcanvas,\n\t\t\tscene,\n\t\t\tengine,\n\t\t\tvalue,\n\t\t\tclear_color,\n\t\t\tcamera_position,\n\t\t\tzoom_speed\n\t\t);\n\t}\n\n\tonMount(() => {\n\t\tif (value != null) {\n\t\t\treset_scene();\n\t\t}\n\t\tmounted = true;\n\t});\n\n\t$: ({ data, is_file, name } = value || {\n\t\tdata: undefined,\n\t\tis_file: undefined,\n\t\tname: undefined\n\t});\n\n\t$: canvas && mounted && data != null && is_file && reset_scene();\n\n\tasync function handle_upload({\n\t\tdetail\n\t}: CustomEvent<FileData>): Promise<void> {\n\t\tvalue = detail;\n\t\tawait tick();\n\t\treset_scene();\n\t\tdispatch(\"change\", value);\n\t}\n\n\tasync function handle_clear(): Promise<void> {\n\t\tif (scene && engine) {\n\t\t\tscene.dispose();\n\t\t\tengine.dispose();\n\t\t}\n\t\tvalue = null;\n\t\tawait tick();\n\t\tdispatch(\"clear\");\n\t}\n\n\tasync function handle_undo(): Promise<void> {\n\t\treset_camera_position(scene, camera_position, zoom_speed);\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData | null;\n\t\tclear: undefined;\n\t\tdrag: boolean;\n\t}>();\n\n\tlet dragging = false;\n\n\timport * as BABYLON from \"babylonjs\";\n\timport * as BABYLON_LOADERS from \"babylonjs-loaders\";\n\n\tBABYLON_LOADERS.OBJFileLoader.IMPORT_VERTEX_COLORS = true;\n\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel {show_label} Icon={File} label={label || \"3D Model\"} />\n\n{#if value === null}\n\t<Upload on:load={handle_upload} filetype=\".obj, .gltf, .glb\" bind:dragging>\n\t\t<slot />\n\t</Upload>\n{:else}\n\t<div class=\"input-model\">\n\t\t<ModifyUpload\n\t\t\tundoable\n\t\t\ton:clear={handle_clear}\n\t\t\ton:undo={handle_undo}\n\t\t\tabsolute\n\t\t/>\n\t\t<canvas bind:this={canvas} />\n\t</div>\n{/if}\n\n<style>\n\t.input-model {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\tcanvas {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\toverflow: hidden;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport Model3DUpload from \"./Model3DUpload.svelte\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData = null;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let clear_color: [number, number, number, number];\n\texport let loading_status: LoadingStatus;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tclear: never;\n\t}>;\n\texport let zoom_speed = 1;\n\texport let height: number | undefined = undefined;\n\n\t// alpha, beta, radius\n\texport let camera_position: [number | null, number | null, number | null] = [\n\t\tnull,\n\t\tnull,\n\t\tnull\n\t];\n\n\tlet _value: null | FileData;\n\t$: _value = normalise_file(value, root, root_url);\n\n\tlet dragging = false;\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\t{height}\n>\n\t<StatusTracker {...loading_status} />\n\n\t<Model3DUpload\n\t\t{label}\n\t\t{show_label}\n\t\t{clear_color}\n\t\tvalue={_value}\n\t\t{camera_position}\n\t\t{zoom_speed}\n\t\ton:change={({ detail }) => (value = detail)}\n\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\ton:change={({ detail }) => gradio.dispatch(\"change\", detail)}\n\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t>\n\t\t<UploadText type=\"file\" />\n\t</Model3DUpload>\n</Block>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "append", "canvas_1", "File", "dirty", "blocklabel_changes", "value", "$$props", "clear_color", "label", "show_label", "zoom_speed", "camera_position", "mounted", "canvas", "scene", "engine", "reset_scene", "add_new_model", "onMount", "$$invalidate", "handle_upload", "detail", "tick", "dispatch", "handle_clear", "handle_undo", "reset_camera_position", "createEventDispatcher", "dragging", "BABYLON_LOADERS.OBJFileLoader", "$$value", "data", "is_file", "name", "block_changes", "elem_id", "elem_classes", "visible", "root", "root_url", "loading_status", "container", "scale", "min_width", "gradio", "height", "_value", "change_handler_1", "normalise_file"], "mappings": "0vBAsGaA,EAAY,CAAA,CAAA,eACbA,EAAW,CAAA,CAAA,uIAJtBC,EAQKC,EAAAC,EAAAC,CAAA,qBADJC,EAA4BF,EAAAG,CAAA,yVAXZN,EAAa,CAAA,CAAA,4iBAHAO,GAAa,MAAAP,MAAS,mDAEhD,OAAAA,OAAU,KAAI,2KAFyBQ,EAAA,IAAAC,EAAA,MAAAT,MAAS,6UApFzC,MAAAU,CAAsB,EAAAC,EACtB,CAAA,YAAAC,GAAiD,EAAG,EAAG,EAAG,CAAC,CAAA,EAAAD,EAC3D,CAAA,MAAAE,EAAQ,EAAE,EAAAF,GACV,WAAAG,CAAmB,EAAAH,EACnB,CAAA,WAAAI,EAAa,CAAC,EAAAJ,EAGd,CAAA,gBAAAK,EACV,CAAA,KACA,KACA,IAAA,CAAA,EAAAL,EAGGM,EAAU,GACVC,EACAC,EACAC,WAEKC,GAAW,CACnBF,EAAQG,GACPJ,EACAC,EACAC,EACAV,EACAE,EACAI,EACAD,CAAU,EAIZQ,EAAO,IAAA,CACFb,GAAS,MACZW,IAEDG,EAAA,GAAAP,EAAU,EAAI,IAWA,eAAAQ,GACd,OAAAC,GAAM,CAENF,EAAA,EAAAd,EAAQgB,CAAM,QACRC,EAAI,EACVN,IACAO,EAAS,SAAUlB,CAAK,iBAGVmB,GAAY,CACtBV,GAASC,IACZD,EAAM,QAAO,EACbC,EAAO,QAAO,GAEfI,EAAA,EAAAd,EAAQ,IAAI,QACNiB,EAAI,EACVC,EAAS,OAAO,iBAGFE,GAAW,CACzBC,GAAsBZ,EAAOH,EAAiBD,CAAU,EAGnD,MAAAa,EAAWI,IAMb,IAAAC,EAAW,GAKfC,GAA6B,cAAC,qBAAuB,qEAmBjChB,EAAMiB,4TA7DzBX,KAAK,CAAA,KAAAY,EAAM,QAAAC,EAAS,KAAAC,CAAI,EAAK5B,GAAK,CAClC,KAAM,OACN,QAAS,OACT,KAAM,8CAGJQ,GAAUD,GAAWmB,GAAQ,MAAQC,GAAWhB,EAAW,iBAsC3DO,EAAS,OAAQK,CAAQ,qZCjCTjC,EAAc,CAAA,CAAA,kJAMzBA,EAAM,EAAA,2RANKA,EAAc,CAAA,CAAA,CAAA,CAAA,uHAMzBA,EAAM,EAAA,qTAhBL,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACxBA,EAAQ,EAAA,EAAG,QAAU,eACzB,0NAFAQ,EAAA,IAAA+B,EAAA,QAAAvC,EAAU,CAAA,IAAA,KAAO,SAAW,iCACxBA,EAAQ,EAAA,EAAG,QAAU,kTApCvB,GAAA,CAAA,QAAAwC,EAAU,EAAE,EAAA7B,GACZ,aAAA8B,EAAY,EAAA,EAAA9B,EACZ,CAAA,QAAA+B,EAAU,EAAI,EAAA/B,EACd,CAAA,MAAAD,EAAyB,IAAI,EAAAC,GAC7B,KAAAgC,CAAY,EAAAhC,GACZ,SAAAiC,CAAuB,EAAAjC,GACvB,YAAAC,CAA6C,EAAAD,GAC7C,eAAAkC,CAA6B,EAAAlC,GAC7B,MAAAE,CAAa,EAAAF,GACb,WAAAG,CAAmB,EAAAH,EACnB,CAAA,UAAAmC,EAAY,EAAI,EAAAnC,EAChB,CAAA,MAAAoC,EAAuB,IAAI,EAAApC,EAC3B,CAAA,UAAAqC,EAAgC,MAAS,EAAArC,GACzC,OAAAsC,CAGT,EAAAtC,EACS,CAAA,WAAAI,EAAa,CAAC,EAAAJ,EACd,CAAA,OAAAuC,EAA6B,MAAS,EAAAvC,EAGtC,CAAA,gBAAAK,EACV,CAAA,KACA,KACA,IAAA,CAAA,EAAAL,EAGGwC,EAGAlB,EAAW,aAwBA,OAAAP,CAAM,IAAAF,EAAA,EAAQd,EAAQgB,CAAM,MAC9B,OAAAA,CAAM,IAAAF,EAAA,GAAQS,EAAWP,CAAM,EAC7B0B,EAAA,CAAA,CAAA,OAAA1B,KAAauB,EAAO,SAAS,SAAUvB,CAAM,QAC3CuB,EAAO,SAAS,OAAO,ipBA7BvCzB,EAAA,GAAE2B,EAASE,GAAe3C,EAAOiC,EAAMC,CAAQ,CAAA"}