import{S as oe,e as ae,s as fe,m as w,g as k,h as g,n as W,k as h,C as re,I as B,o as O,O as K,t as L,j as H,x as Q,p as C,B as _e,Q as V,N as y,K as F,F as E,G as I,w as p,u as j,H as q,a0 as ue,a1 as me,Z as de,ae as ge,V as he,W as be,r as Z,v as A}from"./index-7674dbb6.js";import{g as ke}from"./color-253fe719.js";import{c as ve,m as we,T as ce}from"./utils-9ea7750b.js";import{B as pe}from"./Button-770df9ba.js";import{B as je}from"./BlockLabel-520e742a.js";import{E as ze}from"./Empty-89f2f53e.js";function J(t,l,n){const e=t.slice();e[17]=l[n][0],e[26]=l[n][1];const s=typeof e[26]=="string"?parseInt(e[26]):e[26];return e[27]=s,e}function P(t,l,n){const e=t.slice();return e[17]=l[n][0],e[18]=l[n][1],e[20]=n,e}function R(t,l,n){const e=t.slice();return e[21]=l[n],e[23]=n,e}function U(t,l,n){const e=t.slice();return e[18]=l[n][0],e[24]=l[n][1],e[20]=n,e}function Ne(t){let l,n,e=t[1]&&X(),s=B(t[0]),o=[];for(let i=0;i<s.length;i+=1)o[i]=Y(J(t,s,i));return{c(){e&&e.c(),l=O(),n=w("div");for(let i=0;i<o.length;i+=1)o[i].c();k(n,"class","textfield svelte-ju12zg"),k(n,"data-testid","highlighted-text:textfield")},m(i,f){e&&e.m(i,f),g(i,l,f),g(i,n,f);for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(n,null)},p(i,f){if(i[1]?e||(e=X(),e.c(),e.m(l.parentNode,l)):e&&(e.d(1),e=null),f&1){s=B(i[0]);let a;for(a=0;a<s.length;a+=1){const u=J(i,s,a);o[a]?o[a].p(u,f):(o[a]=Y(u),o[a].c(),o[a].m(n,null))}for(;a<o.length;a+=1)o[a].d(1);o.length=s.length}},d(i){i&&(h(l),h(n)),e&&e.d(i),K(o,i)}}}function He(t){let l,n,e=t[1]&&x(t),s=B(t[0]),o=[];for(let i=0;i<s.length;i+=1)o[i]=ie(P(t,s,i));return{c(){e&&e.c(),l=O(),n=w("div");for(let i=0;i<o.length;i+=1)o[i].c();k(n,"class","textfield svelte-ju12zg")},m(i,f){e&&e.m(i,f),g(i,l,f),g(i,n,f);for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(n,null)},p(i,f){if(i[1]?e?e.p(i,f):(e=x(i),e.c(),e.m(l.parentNode,l)):e&&(e.d(1),e=null),f&111){s=B(i[0]);let a;for(a=0;a<s.length;a+=1){const u=P(i,s,a);o[a]?o[a].p(u,f):(o[a]=ie(u),o[a].c(),o[a].m(n,null))}for(;a<o.length;a+=1)o[a].d(1);o.length=s.length}},d(i){i&&(h(l),h(n)),e&&e.d(i),K(o,i)}}}function X(t){let l;return{c(){l=w("div"),l.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",k(l,"class","color-legend svelte-ju12zg"),k(l,"data-testid","highlighted-text:color-legend")},m(n,e){g(n,l,e)},d(n){n&&h(l)}}}function Y(t){let l,n,e=t[17]+"",s,o,i;return{c(){l=w("span"),n=w("span"),s=L(e),o=O(),k(n,"class","text svelte-ju12zg"),k(l,"class","textspan score-text svelte-ju12zg"),k(l,"style",i="background-color: rgba("+(t[27]&&t[27]<0?"128, 90, 213,"+-t[27]:"239, 68, 60,"+t[27])+")")},m(f,a){g(f,l,a),H(l,n),H(n,s),H(l,o)},p(f,a){a&1&&e!==(e=f[17]+"")&&Q(s,e),a&1&&i!==(i="background-color: rgba("+(f[27]&&f[27]<0?"128, 90, 213,"+-f[27]:"239, 68, 60,"+f[27])+")")&&k(l,"style",i)},d(f){f&&h(l)}}}function x(t){let l,n=B(Object.entries(t[5])),e=[];for(let s=0;s<n.length;s+=1)e[s]=$(U(t,n,s));return{c(){l=w("div");for(let s=0;s<e.length;s+=1)e[s].c();k(l,"class","category-legend svelte-ju12zg"),k(l,"data-testid","highlighted-text:category-legend")},m(s,o){g(s,l,o);for(let i=0;i<e.length;i+=1)e[i]&&e[i].m(l,null)},p(s,o){if(o&416){n=B(Object.entries(s[5]));let i;for(i=0;i<n.length;i+=1){const f=U(s,n,i);e[i]?e[i].p(f,o):(e[i]=$(f),e[i].c(),e[i].m(l,null))}for(;i<e.length;i+=1)e[i].d(1);e.length=n.length}},d(s){s&&h(l),K(e,s)}}}function $(t){let l,n=t[18]+"",e,s,o,i;function f(){return t[10](t[18])}function a(){return t[11](t[18])}return{c(){l=w("div"),e=L(n),s=O(),k(l,"class","category-label svelte-ju12zg"),k(l,"style","background-color:"+t[24].secondary)},m(u,_){g(u,l,_),H(l,e),H(l,s),o||(i=[C(l,"mouseover",f),C(l,"focus",a),C(l,"mouseout",t[12]),C(l,"blur",t[13])],o=!0)},p(u,_){t=u},d(u){u&&h(l),o=!1,_e(i)}}}function ee(t){let l,n,e=t[21]+"",s,o,i,f,a=!t[1]&&t[18]!==null&&le(t);function u(){return t[14](t[20],t[17],t[18])}return{c(){l=w("span"),n=w("span"),s=L(e),o=O(),a&&a.c(),k(n,"class","text svelte-ju12zg"),y(n,"no-label",t[18]&&!t[5][t[18]]),k(l,"class","textspan svelte-ju12zg"),y(l,"no-cat",t[18]===null||t[3]&&t[3]!==t[18]),y(l,"hl",t[18]!==null),y(l,"selectable",t[2]),F(l,"background-color",t[18]===null||t[3]&&t[3]!==t[18]?"":t[5][t[18]].secondary)},m(_,m){g(_,l,m),H(l,n),H(n,s),H(l,o),a&&a.m(l,null),i||(f=C(l,"click",u),i=!0)},p(_,m){t=_,m&1&&e!==(e=t[21]+"")&&Q(s,e),m&33&&y(n,"no-label",t[18]&&!t[5][t[18]]),!t[1]&&t[18]!==null?a?a.p(t,m):(a=le(t),a.c(),a.m(l,null)):a&&(a.d(1),a=null),m&9&&y(l,"no-cat",t[18]===null||t[3]&&t[3]!==t[18]),m&1&&y(l,"hl",t[18]!==null),m&4&&y(l,"selectable",t[2]),m&9&&F(l,"background-color",t[18]===null||t[3]&&t[3]!==t[18]?"":t[5][t[18]].secondary)},d(_){_&&h(l),a&&a.d(),i=!1,f()}}}function le(t){let l,n,e=t[18]+"",s;return{c(){l=L(` 
								`),n=w("span"),s=L(e),k(n,"class","label svelte-ju12zg"),F(n,"background-color",t[18]===null||t[3]&&t[3]!==t[18]?"":t[5][t[18]].primary)},m(o,i){g(o,l,i),g(o,n,i),H(n,s)},p(o,i){i&1&&e!==(e=o[18]+"")&&Q(s,e),i&9&&F(n,"background-color",o[18]===null||o[3]&&o[3]!==o[18]?"":o[5][o[18]].primary)},d(o){o&&(h(l),h(n))}}}function te(t){let l;return{c(){l=w("br")},m(n,e){g(n,l,e)},d(n){n&&h(l)}}}function ne(t){let l=t[21].trim()!=="",n,e=t[23]<G(t[17]).length-1,s,o=l&&ee(t),i=e&&te();return{c(){o&&o.c(),n=O(),i&&i.c(),s=V()},m(f,a){o&&o.m(f,a),g(f,n,a),i&&i.m(f,a),g(f,s,a)},p(f,a){a&1&&(l=f[21].trim()!==""),l?o?o.p(f,a):(o=ee(f),o.c(),o.m(n.parentNode,n)):o&&(o.d(1),o=null),a&1&&(e=f[23]<G(f[17]).length-1),e?i||(i=te(),i.c(),i.m(s.parentNode,s)):i&&(i.d(1),i=null)},d(f){f&&(h(n),h(s)),o&&o.d(f),i&&i.d(f)}}}function ie(t){let l,n=B(G(t[17])),e=[];for(let s=0;s<n.length;s+=1)e[s]=ne(R(t,n,s));return{c(){for(let s=0;s<e.length;s+=1)e[s].c();l=V()},m(s,o){for(let i=0;i<e.length;i+=1)e[i]&&e[i].m(s,o);g(s,l,o)},p(s,o){if(o&111){n=B(G(s[17]));let i;for(i=0;i<n.length;i+=1){const f=R(s,n,i);e[i]?e[i].p(f,o):(e[i]=ne(f),e[i].c(),e[i].m(l.parentNode,l))}for(;i<e.length;i+=1)e[i].d(1);e.length=n.length}},d(s){s&&h(l),K(e,s)}}}function ye(t){let l;function n(o,i){return o[4]==="categories"?He:Ne}let e=n(t),s=e(t);return{c(){l=w("div"),s.c(),k(l,"class","container svelte-ju12zg")},m(o,i){g(o,l,i),s.m(l,null)},p(o,[i]){e===(e=n(o))&&s?s.p(o,i):(s.d(1),s=e(o),s&&(s.c(),s.m(l,null)))},i:W,o:W,d(o){o&&h(l),s.d()}}}function G(t){return t.split(`
`)}function Be(t,l,n){const e=typeof document<"u";let{value:s=[]}=l,{show_legend:o=!1}=l,{color_map:i={}}=l,{selectable:f=!1}=l,a,u={},_="";const m=re();let v;function z(d){n(3,_=d)}function r(){n(3,_="")}const b=d=>z(d),S=d=>z(d),N=()=>r(),T=()=>r(),D=(d,c,M)=>{m("select",{index:d,value:[c,M]})};return t.$$set=d=>{"value"in d&&n(0,s=d.value),"show_legend"in d&&n(1,o=d.show_legend),"color_map"in d&&n(9,i=d.color_map),"selectable"in d&&n(2,f=d.selectable)},t.$$.update=()=>{if(t.$$.dirty&513){if(i||n(9,i={}),s.length>0){for(let[d,c]of s)if(c!==null)if(typeof c=="string"){if(n(4,v="categories"),!(c in i)){let M=ke(Object.keys(i).length);n(9,i[c]=M,i)}}else n(4,v="scores")}ve(i,u,e,a)}},[s,o,f,_,v,u,m,z,r,i,b,S,N,T,D]}class Oe extends oe{constructor(l){super(),ae(this,l,Be,ye,fe,{value:0,show_legend:1,color_map:9,selectable:2})}}function se(t){let l,n;return l=new je({props:{Icon:ce,label:t[7],float:!1,disable:t[8]===!1}}),{c(){E(l.$$.fragment)},m(e,s){I(l,e,s),n=!0},p(e,s){const o={};s&128&&(o.label=e[7]),s&256&&(o.disable=e[8]===!1),l.$set(o)},i(e){n||(p(l.$$.fragment,e),n=!0)},o(e){j(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function Se(t){let l,n;return l=new ze({props:{$$slots:{default:[Ee]},$$scope:{ctx:t}}}),{c(){E(l.$$.fragment)},m(e,s){I(l,e,s),n=!0},p(e,s){const o={};s&262144&&(o.$$scope={dirty:s,ctx:e}),l.$set(o)},i(e){n||(p(l.$$.fragment,e),n=!0)},o(e){j(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function Te(t){let l,n;return l=new Oe({props:{selectable:t[11],value:t[0],show_legend:t[6],color_map:t[1]}}),l.$on("select",t[16]),{c(){E(l.$$.fragment)},m(e,s){I(l,e,s),n=!0},p(e,s){const o={};s&2048&&(o.selectable=e[11]),s&1&&(o.value=e[0]),s&64&&(o.show_legend=e[6]),s&2&&(o.color_map=e[1]),l.$set(o)},i(e){n||(p(l.$$.fragment,e),n=!0)},o(e){j(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function Ee(t){let l,n;return l=new ce({}),{c(){E(l.$$.fragment)},m(e,s){I(l,e,s),n=!0},i(e){n||(p(l.$$.fragment,e),n=!0)},o(e){j(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function Ie(t){let l,n,e,s,o,i,f;const a=[t[13]];let u={};for(let r=0;r<a.length;r+=1)u=de(u,a[r]);l=new ge({props:u});let _=t[7]&&se(t);const m=[Te,Se],v=[];function z(r,b){return r[0]?0:1}return s=z(t),o=v[s]=m[s](t),{c(){E(l.$$.fragment),n=O(),_&&_.c(),e=O(),o.c(),i=V()},m(r,b){I(l,r,b),g(r,n,b),_&&_.m(r,b),g(r,e,b),v[s].m(r,b),g(r,i,b),f=!0},p(r,b){const S=b&8192?he(a,[be(r[13])]):{};l.$set(S),r[7]?_?(_.p(r,b),b&128&&p(_,1)):(_=se(r),_.c(),p(_,1),_.m(e.parentNode,e)):_&&(Z(),j(_,1,1,()=>{_=null}),A());let N=s;s=z(r),s===N?v[s].p(r,b):(Z(),j(v[N],1,1,()=>{v[N]=null}),A(),o=v[s],o?o.p(r,b):(o=v[s]=m[s](r),o.c()),p(o,1),o.m(i.parentNode,i))},i(r){f||(p(l.$$.fragment,r),p(_),p(o),f=!0)},o(r){j(l.$$.fragment,r),j(_),j(o),f=!1},d(r){r&&(h(n),h(e),h(i)),q(l,r),_&&_.d(r),v[s].d(r)}}}function qe(t){let l,n;return l=new pe({props:{variant:t[5]==="interactive"?"dashed":"solid",test_id:"highlighted-text",visible:t[4],elem_id:t[2],elem_classes:t[3],padding:!1,container:t[8],scale:t[9],min_width:t[10],$$slots:{default:[Ie]},$$scope:{ctx:t}}}),{c(){E(l.$$.fragment)},m(e,s){I(l,e,s),n=!0},p(e,[s]){const o={};s&32&&(o.variant=e[5]==="interactive"?"dashed":"solid"),s&16&&(o.visible=e[4]),s&4&&(o.elem_id=e[2]),s&8&&(o.elem_classes=e[3]),s&256&&(o.container=e[8]),s&512&&(o.scale=e[9]),s&1024&&(o.min_width=e[10]),s&276931&&(o.$$scope={dirty:s,ctx:e}),l.$set(o)},i(e){n||(p(l.$$.fragment,e),n=!0)},o(e){j(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function Ce(t,l,n){let e;ue(t,me,c=>n(17,e=c));let{elem_id:s=""}=l,{elem_classes:o=[]}=l,{visible:i=!0}=l,{value:f}=l,a,{mode:u}=l,{show_legend:_}=l,{color_map:m={}}=l,{label:v=e("highlighted_text.highlighted_text")}=l,{container:z=!0}=l,{scale:r=null}=l,{min_width:b=void 0}=l,{selectable:S=!1}=l,{combine_adjacent:N=!1}=l,{gradio:T}=l,{loading_status:D}=l;f&&N&&(f=we(f,"equal"));const d=({detail:c})=>T.dispatch("select",c);return t.$$set=c=>{"elem_id"in c&&n(2,s=c.elem_id),"elem_classes"in c&&n(3,o=c.elem_classes),"visible"in c&&n(4,i=c.visible),"value"in c&&n(0,f=c.value),"mode"in c&&n(5,u=c.mode),"show_legend"in c&&n(6,_=c.show_legend),"color_map"in c&&n(1,m=c.color_map),"label"in c&&n(7,v=c.label),"container"in c&&n(8,z=c.container),"scale"in c&&n(9,r=c.scale),"min_width"in c&&n(10,b=c.min_width),"selectable"in c&&n(11,S=c.selectable),"combine_adjacent"in c&&n(14,N=c.combine_adjacent),"gradio"in c&&n(12,T=c.gradio),"loading_status"in c&&n(13,D=c.loading_status)},t.$$.update=()=>{t.$$.dirty&2&&!m&&Object.keys(m).length&&n(1,m),t.$$.dirty&36865&&f!==a&&(n(15,a=f),T.dispatch("change"))},[f,m,s,o,i,u,_,v,z,r,b,S,T,D,N,a,d]}class Le extends oe{constructor(l){super(),ae(this,l,Ce,qe,fe,{elem_id:2,elem_classes:3,visible:4,value:0,mode:5,show_legend:6,color_map:1,label:7,container:8,scale:9,min_width:10,selectable:11,combine_adjacent:14,gradio:12,loading_status:13})}}const Ve=Le;export{Ve as default};
//# sourceMappingURL=index-2de34455.js.map
