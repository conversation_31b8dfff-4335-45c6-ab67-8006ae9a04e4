import{S as u,e as m,s as r,F as d,G as b,w as c,u as f,H as g,a9 as v,ab as S,ac as h,ad as k}from"./index-7674dbb6.js";import{B as p}from"./Button-770df9ba.js";function w(n){let t;const l=n[3].default,e=v(l,n,n[4],null);return{c(){e&&e.c()},m(s,a){e&&e.m(s,a),t=!0},p(s,a){e&&e.p&&(!t||a&16)&&S(e,l,s,s[4],t?k(l,s[4],a,null):h(s[4]),null)},i(s){t||(c(e,s),t=!0)},o(s){f(e,s),t=!1},d(s){e&&e.d(s)}}}function B(n){let t,l;return t=new p({props:{elem_id:n[0],elem_classes:n[1],visible:n[2],explicit_call:!0,$$slots:{default:[w]},$$scope:{ctx:n}}}),{c(){d(t.$$.fragment)},m(e,s){b(t,e,s),l=!0},p(e,[s]){const a={};s&1&&(a.elem_id=e[0]),s&2&&(a.elem_classes=e[1]),s&4&&(a.visible=e[2]),s&16&&(a.$$scope={dirty:s,ctx:e}),t.$set(a)},i(e){l||(c(t.$$.fragment,e),l=!0)},o(e){f(t.$$.fragment,e),l=!1},d(e){g(t,e)}}}function q(n,t,l){let{$$slots:e={},$$scope:s}=t,{elem_id:a}=t,{elem_classes:o}=t,{visible:_=!0}=t;return n.$$set=i=>{"elem_id"in i&&l(0,a=i.elem_id),"elem_classes"in i&&l(1,o=i.elem_classes),"visible"in i&&l(2,_=i.visible),"$$scope"in i&&l(4,s=i.$$scope)},[a,o,_,e,s]}class C extends u{constructor(t){super(),m(this,t,q,B,r,{elem_id:0,elem_classes:1,visible:2})}}const H=C;export{H as default};
//# sourceMappingURL=index-30a87b3f.js.map
