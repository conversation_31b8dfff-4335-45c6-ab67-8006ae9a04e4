import{S as Z,e as z,s as A,F as v,G as k,w as N,u as S,H as B,a0 as D,a1 as E,Z as J,ae as K,R as q,U as C,o as L,h as M,V as O,W as P,X as F,k as Q}from"./index-7674dbb6.js";import{N as T}from"./Number-04def8a9.js";import{B as Y}from"./Button-770df9ba.js";import"./BlockTitle-2eb1c338.js";import"./Info-47344107.js";function y(a){let i,l,u,t,m,f;const b=[a[13]];let c={};for(let n=0;n<b.length;n+=1)c=J(c,b[n]);i=new K({props:c});function g(n){a[16](n)}function h(n){a[17](n)}let r={label:a[2],info:a[3],show_label:a[10],minimum:a[11],maximum:a[12],step:a[14],container:a[7]};return a[0]!==void 0&&(r.value=a[0]),a[1]!==void 0&&(r.value_is_output=a[1]),u=new T({props:r}),q.push(()=>C(u,"value",g)),q.push(()=>C(u,"value_is_output",h)),u.$on("change",a[18]),u.$on("input",a[19]),u.$on("submit",a[20]),u.$on("blur",a[21]),u.$on("focus",a[22]),{c(){v(i.$$.fragment),l=L(),v(u.$$.fragment)},m(n,s){k(i,n,s),M(n,l,s),k(u,n,s),f=!0},p(n,s){const d=s&8192?O(b,[P(n[13])]):{};i.$set(d);const _={};s&4&&(_.label=n[2]),s&8&&(_.info=n[3]),s&1024&&(_.show_label=n[10]),s&2048&&(_.minimum=n[11]),s&4096&&(_.maximum=n[12]),s&16384&&(_.step=n[14]),s&128&&(_.container=n[7]),!t&&s&1&&(t=!0,_.value=n[0],F(()=>t=!1)),!m&&s&2&&(m=!0,_.value_is_output=n[1],F(()=>m=!1)),u.$set(_)},i(n){f||(N(i.$$.fragment,n),N(u.$$.fragment,n),f=!0)},o(n){S(i.$$.fragment,n),S(u.$$.fragment,n),f=!1},d(n){n&&Q(l),B(i,n),B(u,n)}}}function p(a){let i,l;return i=new Y({props:{visible:a[6],elem_id:a[4],elem_classes:a[5],padding:a[7],allow_overflow:!1,scale:a[8],min_width:a[9],$$slots:{default:[y]},$$scope:{ctx:a}}}),{c(){v(i.$$.fragment)},m(u,t){k(i,u,t),l=!0},p(u,[t]){const m={};t&64&&(m.visible=u[6]),t&16&&(m.elem_id=u[4]),t&32&&(m.elem_classes=u[5]),t&128&&(m.padding=u[7]),t&256&&(m.scale=u[8]),t&512&&(m.min_width=u[9]),t&16841871&&(m.$$scope={dirty:t,ctx:u}),i.$set(m)},i(u){l||(N(i.$$.fragment,u),l=!0)},o(u){S(i.$$.fragment,u),l=!1},d(u){B(i,u)}}}function x(a,i,l){let u;D(a,E,e=>l(23,u=e));let{label:t=u("number.number")}=i,{info:m=void 0}=i,{elem_id:f=""}=i,{elem_classes:b=[]}=i,{visible:c=!0}=i,{container:g=!0}=i,{scale:h=null}=i,{min_width:r=void 0}=i,{value:n=0}=i,{show_label:s}=i,{minimum:d=void 0}=i,{maximum:_=void 0}=i,{loading_status:I}=i,{value_is_output:w=!1}=i,{step:j=null}=i,{gradio:o}=i;function G(e){n=e,l(0,n)}function H(e){w=e,l(1,w)}const R=()=>o.dispatch("change"),U=()=>o.dispatch("input"),V=()=>o.dispatch("submit"),W=()=>o.dispatch("blur"),X=()=>o.dispatch("focus");return a.$$set=e=>{"label"in e&&l(2,t=e.label),"info"in e&&l(3,m=e.info),"elem_id"in e&&l(4,f=e.elem_id),"elem_classes"in e&&l(5,b=e.elem_classes),"visible"in e&&l(6,c=e.visible),"container"in e&&l(7,g=e.container),"scale"in e&&l(8,h=e.scale),"min_width"in e&&l(9,r=e.min_width),"value"in e&&l(0,n=e.value),"show_label"in e&&l(10,s=e.show_label),"minimum"in e&&l(11,d=e.minimum),"maximum"in e&&l(12,_=e.maximum),"loading_status"in e&&l(13,I=e.loading_status),"value_is_output"in e&&l(1,w=e.value_is_output),"step"in e&&l(14,j=e.step),"gradio"in e&&l(15,o=e.gradio)},[n,w,t,m,f,b,c,g,h,r,s,d,_,I,j,o,G,H,R,U,V,W,X]}class $ extends Z{constructor(i){super(),z(this,i,x,p,A,{label:2,info:3,elem_id:4,elem_classes:5,visible:6,container:7,scale:8,min_width:9,value:0,show_label:10,minimum:11,maximum:12,loading_status:13,value_is_output:1,step:14,gradio:15})}}const le=$;export{le as default};
//# sourceMappingURL=index-3a436d1d.js.map
