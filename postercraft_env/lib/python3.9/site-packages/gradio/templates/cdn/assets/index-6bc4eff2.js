const VERSION_RE = new RegExp("3.50.0/", "g");function import_fix(mod, base) {const url =  new URL(mod, base); return import(`https://gradio.s3-us-west-2.amazonaws.com/3.50.0/${url.pathname?.startsWith('/') ? url.pathname.substring(1).replace(VERSION_RE, "") : url.pathname.replace(VERSION_RE, "")}`);}import{S as ve,e as ye,s as Ae,Q as be,h as S,n as ee,k as V,C as De,Z as Re,ah as Be,R as Se,m as H,t as K,K as Fl,j as L,x as j,M as Le,g as b,p as F,af as Nl,b as me,B as he,o as G,N as y,ai as we,I as Pe,O as cl,w as R,r as Ve,u as N,v as Ee,aj as Ol,F as Q,G as W,H as Z,a6 as Bl,a0 as Ll,a1 as Cl,ak as Tl,_ as Ce,E as Te,U as Ie,X as Ue,a9 as zl,ab as Kl,ac as jl,ad as ql,ag as T,ae as Xl,V as Yl,W as Jl}from"./index-7674dbb6.js";import{a as gl,B as Gl,n as Ql}from"./Button-770df9ba.js";import{U as Wl}from"./UploadText-455dd9a9.js";import{U as Zl}from"./Upload-59d47275.js";import{M as xl}from"./ModifyUpload-b9d1b06a.js";import{B as $l}from"./BlockLabel-520e742a.js";import{l as en,M as ln}from"./utils-08c16748.js";import{h as ze,r as Ke,u as nn}from"./file-url-43dd5b28.js";import"./IconButton-a4282a0e.js";import"./Undo-e443528b.js";function an(l){let e,i=l[10].message+"",n;return{c(){e=H("p"),n=K(i),Fl(e,"color","red")},m(a,t){S(a,e,t),L(e,n)},p(a,t){t&2&&i!==(i=a[10].message+"")&&j(n,i)},d(a){a&&V(e)}}}function tn(l){let e,i,n,a,t,s;return{c(){e=H("audio"),Le(e.src,i=l[9])||b(e,"src",i),e.controls=l[4],b(e,"preload",l[5]),b(e,"data-testid",n=l[7]["data-testid"]),b(e,"class","svelte-eemfgq")},m(f,r){S(f,e,r),l[8](e),t||(s=[F(e,"play",l[6].bind(null,"play")),F(e,"pause",l[6].bind(null,"pause")),F(e,"ended",l[6].bind(null,"ended")),Nl(a=en.call(null,e,{autoplay:l[2],crop_values:l[3]}))],t=!0)},p(f,r){r&2&&!Le(e.src,i=f[9])&&b(e,"src",i),r&16&&(e.controls=f[4]),r&32&&b(e,"preload",f[5]),r&128&&n!==(n=f[7]["data-testid"])&&b(e,"data-testid",n),a&&me(a.update)&&r&12&&a.update.call(null,{autoplay:f[2],crop_values:f[3]})},d(f){f&&V(e),l[8](null),t=!1,he(s)}}}function sn(l){return{c:ee,m:ee,p:ee,d:ee}}function fn(l){let e,i,n={ctx:l,current:null,token:null,hasCatch:!0,pending:sn,then:tn,catch:an,value:9,error:10};return ze(i=Ke(l[1]),n),{c(){e=be(),n.block.c()},m(a,t){S(a,e,t),n.block.m(a,n.anchor=t),n.mount=()=>e.parentNode,n.anchor=e},p(a,[t]){l=a,n.ctx=l,t&2&&i!==(i=Ke(l[1]))&&ze(i,n)||nn(n,l,t)},i:ee,o:ee,d(a){a&&V(e),n.block.d(a),n.token=null,n=null}}}function un(l,e,i){let{src:n=void 0}=e,{autoplay:a=void 0}=e,{crop_values:t=void 0}=e,{controls:s=void 0}=e,{preload:f=void 0}=e,{node:r=void 0}=e;const h=De();function d(o){Se[o?"unshift":"push"](()=>{r=o,i(0,r)})}return l.$$set=o=>{i(7,e=Re(Re({},e),Be(o))),"src"in o&&i(1,n=o.src),"autoplay"in o&&i(2,a=o.autoplay),"crop_values"in o&&i(3,t=o.crop_values),"controls"in o&&i(4,s=o.controls),"preload"in o&&i(5,f=o.preload),"node"in o&&i(0,r=o.node)},e=Be(e),[r,n,a,t,s,f,h,e,d]}class rn extends ve{constructor(e){super(),ye(this,e,un,fn,Ae,{src:1,autoplay:2,crop_values:3,controls:4,preload:5,node:0})}}function je(l,e,i){const n=l.slice();return n[27]=e[i],n[29]=i,n}function qe(l){let e,i,n,a,t=(l[6]==="label"||l[7]==="label")&&Xe(l);return{c(){e=H("span"),t&&t.c(),b(e,"class","pip first"),b(e,"style",i=l[14]+": 0%;"),y(e,"selected",l[17](l[0])),y(e,"in-range",l[16](l[0]))},m(s,f){S(s,e,f),t&&t.m(e,null),n||(a=[F(e,"click",function(){me(l[20](l[0]))&&l[20](l[0]).apply(this,arguments)}),F(e,"touchend",we(function(){me(l[20](l[0]))&&l[20](l[0]).apply(this,arguments)}))],n=!0)},p(s,f){l=s,l[6]==="label"||l[7]==="label"?t?t.p(l,f):(t=Xe(l),t.c(),t.m(e,null)):t&&(t.d(1),t=null),f&16384&&i!==(i=l[14]+": 0%;")&&b(e,"style",i),f&131073&&y(e,"selected",l[17](l[0])),f&65537&&y(e,"in-range",l[16](l[0]))},d(s){s&&V(e),t&&t.d(),n=!1,he(a)}}}function Xe(l){let e,i=l[12](l[0],0,0)+"",n,a=l[10]&&Ye(l),t=l[11]&&Je(l);return{c(){e=H("span"),a&&a.c(),n=K(i),t&&t.c(),b(e,"class","pipVal")},m(s,f){S(s,e,f),a&&a.m(e,null),L(e,n),t&&t.m(e,null)},p(s,f){s[10]?a?a.p(s,f):(a=Ye(s),a.c(),a.m(e,n)):a&&(a.d(1),a=null),f&4097&&i!==(i=s[12](s[0],0,0)+"")&&j(n,i),s[11]?t?t.p(s,f):(t=Je(s),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(s){s&&V(e),a&&a.d(),t&&t.d()}}}function Ye(l){let e,i;return{c(){e=H("span"),i=K(l[10]),b(e,"class","pipVal-prefix")},m(n,a){S(n,e,a),L(e,i)},p(n,a){a&1024&&j(i,n[10])},d(n){n&&V(e)}}}function Je(l){let e,i;return{c(){e=H("span"),i=K(l[11]),b(e,"class","pipVal-suffix")},m(n,a){S(n,e,a),L(e,i)},p(n,a){a&2048&&j(i,n[11])},d(n){n&&V(e)}}}function Ge(l){let e,i=Pe(Array(l[19]+1)),n=[];for(let a=0;a<i.length;a+=1)n[a]=$e(je(l,i,a));return{c(){for(let a=0;a<n.length;a+=1)n[a].c();e=be()},m(a,t){for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(a,t);S(a,e,t)},p(a,t){if(t&2088515){i=Pe(Array(a[19]+1));let s;for(s=0;s<i.length;s+=1){const f=je(a,i,s);n[s]?n[s].p(f,t):(n[s]=$e(f),n[s].c(),n[s].m(e.parentNode,e))}for(;s<n.length;s+=1)n[s].d(1);n.length=i.length}},d(a){a&&V(e),cl(n,a)}}}function Qe(l){let e,i,n,a,t,s=(l[6]==="label"||l[9]==="label")&&We(l);return{c(){e=H("span"),s&&s.c(),i=G(),b(e,"class","pip"),b(e,"style",n=l[14]+": "+l[15](l[18](l[29]))+"%;"),y(e,"selected",l[17](l[18](l[29]))),y(e,"in-range",l[16](l[18](l[29])))},m(f,r){S(f,e,r),s&&s.m(e,null),L(e,i),a||(t=[F(e,"click",function(){me(l[20](l[18](l[29])))&&l[20](l[18](l[29])).apply(this,arguments)}),F(e,"touchend",we(function(){me(l[20](l[18](l[29])))&&l[20](l[18](l[29])).apply(this,arguments)}))],a=!0)},p(f,r){l=f,l[6]==="label"||l[9]==="label"?s?s.p(l,r):(s=We(l),s.c(),s.m(e,i)):s&&(s.d(1),s=null),r&311296&&n!==(n=l[14]+": "+l[15](l[18](l[29]))+"%;")&&b(e,"style",n),r&393216&&y(e,"selected",l[17](l[18](l[29]))),r&327680&&y(e,"in-range",l[16](l[18](l[29])))},d(f){f&&V(e),s&&s.d(),a=!1,he(t)}}}function We(l){let e,i=l[12](l[18](l[29]),l[29],l[15](l[18](l[29])))+"",n,a=l[10]&&Ze(l),t=l[11]&&xe(l);return{c(){e=H("span"),a&&a.c(),n=K(i),t&&t.c(),b(e,"class","pipVal")},m(s,f){S(s,e,f),a&&a.m(e,null),L(e,n),t&&t.m(e,null)},p(s,f){s[10]?a?a.p(s,f):(a=Ze(s),a.c(),a.m(e,n)):a&&(a.d(1),a=null),f&299008&&i!==(i=s[12](s[18](s[29]),s[29],s[15](s[18](s[29])))+"")&&j(n,i),s[11]?t?t.p(s,f):(t=xe(s),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(s){s&&V(e),a&&a.d(),t&&t.d()}}}function Ze(l){let e,i;return{c(){e=H("span"),i=K(l[10]),b(e,"class","pipVal-prefix")},m(n,a){S(n,e,a),L(e,i)},p(n,a){a&1024&&j(i,n[10])},d(n){n&&V(e)}}}function xe(l){let e,i;return{c(){e=H("span"),i=K(l[11]),b(e,"class","pipVal-suffix")},m(n,a){S(n,e,a),L(e,i)},p(n,a){a&2048&&j(i,n[11])},d(n){n&&V(e)}}}function $e(l){let e=l[18](l[29])!==l[0]&&l[18](l[29])!==l[1],i,n=e&&Qe(l);return{c(){n&&n.c(),i=be()},m(a,t){n&&n.m(a,t),S(a,i,t)},p(a,t){t&262147&&(e=a[18](a[29])!==a[0]&&a[18](a[29])!==a[1]),e?n?n.p(a,t):(n=Qe(a),n.c(),n.m(i.parentNode,i)):n&&(n.d(1),n=null)},d(a){a&&V(i),n&&n.d(a)}}}function el(l){let e,i,n,a,t=(l[6]==="label"||l[8]==="label")&&ll(l);return{c(){e=H("span"),t&&t.c(),b(e,"class","pip last"),b(e,"style",i=l[14]+": 100%;"),y(e,"selected",l[17](l[1])),y(e,"in-range",l[16](l[1]))},m(s,f){S(s,e,f),t&&t.m(e,null),n||(a=[F(e,"click",function(){me(l[20](l[1]))&&l[20](l[1]).apply(this,arguments)}),F(e,"touchend",we(function(){me(l[20](l[1]))&&l[20](l[1]).apply(this,arguments)}))],n=!0)},p(s,f){l=s,l[6]==="label"||l[8]==="label"?t?t.p(l,f):(t=ll(l),t.c(),t.m(e,null)):t&&(t.d(1),t=null),f&16384&&i!==(i=l[14]+": 100%;")&&b(e,"style",i),f&131074&&y(e,"selected",l[17](l[1])),f&65538&&y(e,"in-range",l[16](l[1]))},d(s){s&&V(e),t&&t.d(),n=!1,he(a)}}}function ll(l){let e,i=l[12](l[1],l[19],100)+"",n,a=l[10]&&nl(l),t=l[11]&&il(l);return{c(){e=H("span"),a&&a.c(),n=K(i),t&&t.c(),b(e,"class","pipVal")},m(s,f){S(s,e,f),a&&a.m(e,null),L(e,n),t&&t.m(e,null)},p(s,f){s[10]?a?a.p(s,f):(a=nl(s),a.c(),a.m(e,n)):a&&(a.d(1),a=null),f&528386&&i!==(i=s[12](s[1],s[19],100)+"")&&j(n,i),s[11]?t?t.p(s,f):(t=il(s),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(s){s&&V(e),a&&a.d(),t&&t.d()}}}function nl(l){let e,i;return{c(){e=H("span"),i=K(l[10]),b(e,"class","pipVal-prefix")},m(n,a){S(n,e,a),L(e,i)},p(n,a){a&1024&&j(i,n[10])},d(n){n&&V(e)}}}function il(l){let e,i;return{c(){e=H("span"),i=K(l[11]),b(e,"class","pipVal-suffix")},m(n,a){S(n,e,a),L(e,i)},p(n,a){a&2048&&j(i,n[11])},d(n){n&&V(e)}}}function on(l){let e,i,n,a=(l[6]&&l[7]!==!1||l[7])&&qe(l),t=(l[6]&&l[9]!==!1||l[9])&&Ge(l),s=(l[6]&&l[8]!==!1||l[8])&&el(l);return{c(){e=H("div"),a&&a.c(),i=G(),t&&t.c(),n=G(),s&&s.c(),b(e,"class","rangePips"),y(e,"disabled",l[5]),y(e,"hoverable",l[4]),y(e,"vertical",l[2]),y(e,"reversed",l[3]),y(e,"focus",l[13])},m(f,r){S(f,e,r),a&&a.m(e,null),L(e,i),t&&t.m(e,null),L(e,n),s&&s.m(e,null)},p(f,[r]){f[6]&&f[7]!==!1||f[7]?a?a.p(f,r):(a=qe(f),a.c(),a.m(e,i)):a&&(a.d(1),a=null),f[6]&&f[9]!==!1||f[9]?t?t.p(f,r):(t=Ge(f),t.c(),t.m(e,n)):t&&(t.d(1),t=null),f[6]&&f[8]!==!1||f[8]?s?s.p(f,r):(s=el(f),s.c(),s.m(e,null)):s&&(s.d(1),s=null),r&32&&y(e,"disabled",f[5]),r&16&&y(e,"hoverable",f[4]),r&4&&y(e,"vertical",f[2]),r&8&&y(e,"reversed",f[3]),r&8192&&y(e,"focus",f[13])},i:ee,o:ee,d(f){f&&V(e),a&&a.d(),t&&t.d(),s&&s.d()}}}function dn(l,e,i){let n,a,t,s,f,{range:r=!1}=e,{min:h=0}=e,{max:d=100}=e,{step:o=1}=e,{values:_=[(d+h)/2]}=e,{vertical:c=!1}=e,{reversed:m=!1}=e,{hoverable:A=!0}=e,{disabled:P=!1}=e,{pipstep:v=void 0}=e,{all:D=!0}=e,{first:z=void 0}=e,{last:O=void 0}=e,{rest:I=void 0}=e,{prefix:B=""}=e,{suffix:J=""}=e,{formatter:q=(g,ne)=>g}=e,{focus:$=void 0}=e,{orientationStart:le=void 0}=e,{percentOf:U=void 0}=e,{moveHandle:X=void 0}=e;function te(g){X(void 0,g)}return l.$$set=g=>{"range"in g&&i(21,r=g.range),"min"in g&&i(0,h=g.min),"max"in g&&i(1,d=g.max),"step"in g&&i(22,o=g.step),"values"in g&&i(23,_=g.values),"vertical"in g&&i(2,c=g.vertical),"reversed"in g&&i(3,m=g.reversed),"hoverable"in g&&i(4,A=g.hoverable),"disabled"in g&&i(5,P=g.disabled),"pipstep"in g&&i(24,v=g.pipstep),"all"in g&&i(6,D=g.all),"first"in g&&i(7,z=g.first),"last"in g&&i(8,O=g.last),"rest"in g&&i(9,I=g.rest),"prefix"in g&&i(10,B=g.prefix),"suffix"in g&&i(11,J=g.suffix),"formatter"in g&&i(12,q=g.formatter),"focus"in g&&i(13,$=g.focus),"orientationStart"in g&&i(14,le=g.orientationStart),"percentOf"in g&&i(15,U=g.percentOf),"moveHandle"in g&&i(25,X=g.moveHandle)},l.$$.update=()=>{l.$$.dirty&20971527&&i(26,n=v||((d-h)/o>=(c?50:100)?(d-h)/(c?10:20):1)),l.$$.dirty&71303171&&i(19,a=parseInt((d-h)/(o*n),10)),l.$$.dirty&71303169&&i(18,t=function(g){return h+g*o*n}),l.$$.dirty&8388608&&i(17,s=function(g){return _.some(ne=>ne===g)}),l.$$.dirty&10485760&&i(16,f=function(g){if(r==="min")return _[0]>g;if(r==="max")return _[0]<g;if(r)return _[0]<g&&_[1]>g})},[h,d,c,m,A,P,D,z,O,I,B,J,q,$,le,U,f,s,t,a,te,r,o,_,v,X,n]}class _n extends ve{constructor(e){super(),ye(this,e,dn,on,Ae,{range:21,min:0,max:1,step:22,values:23,vertical:2,reversed:3,hoverable:4,disabled:5,pipstep:24,all:6,first:7,last:8,rest:9,prefix:10,suffix:11,formatter:12,focus:13,orientationStart:14,percentOf:15,moveHandle:25})}}function al(l,e,i){const n=l.slice();return n[63]=e[i],n[65]=i,n}function tl(l){let e,i=l[21](l[63],l[65],l[23](l[63]))+"",n,a=l[18]&&sl(l),t=l[19]&&fl(l);return{c(){e=H("span"),a&&a.c(),n=K(i),t&&t.c(),b(e,"class","rangeFloat")},m(s,f){S(s,e,f),a&&a.m(e,null),L(e,n),t&&t.m(e,null)},p(s,f){s[18]?a?a.p(s,f):(a=sl(s),a.c(),a.m(e,n)):a&&(a.d(1),a=null),f[0]&10485761&&i!==(i=s[21](s[63],s[65],s[23](s[63]))+"")&&j(n,i),s[19]?t?t.p(s,f):(t=fl(s),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(s){s&&V(e),a&&a.d(),t&&t.d()}}}function sl(l){let e,i;return{c(){e=H("span"),i=K(l[18]),b(e,"class","rangeFloat-prefix")},m(n,a){S(n,e,a),L(e,i)},p(n,a){a[0]&262144&&j(i,n[18])},d(n){n&&V(e)}}}function fl(l){let e,i;return{c(){e=H("span"),i=K(l[19]),b(e,"class","rangeFloat-suffix")},m(n,a){S(n,e,a),L(e,i)},p(n,a){a[0]&524288&&j(i,n[19])},d(n){n&&V(e)}}}function ul(l){let e,i,n,a,t,s,f,r,h,d,o,_,c=l[7]&&tl(l);return{c(){e=H("span"),i=H("span"),n=G(),c&&c.c(),b(i,"class","rangeNub"),b(e,"role","slider"),b(e,"class","rangeHandle"),b(e,"data-handle",l[65]),b(e,"style",a=l[28]+": "+l[29][l[65]]+"%; z-index: "+(l[26]===l[65]?3:2)+";"),b(e,"aria-valuemin",t=l[2]===!0&&l[65]===1?l[0][0]:l[3]),b(e,"aria-valuemax",s=l[2]===!0&&l[65]===0?l[0][1]:l[4]),b(e,"aria-valuenow",f=l[63]),b(e,"aria-valuetext",r=""+(l[18]+l[21](l[63],l[65],l[23](l[63]))+l[19])),b(e,"aria-orientation",h=l[6]?"vertical":"horizontal"),b(e,"aria-disabled",l[10]),b(e,"disabled",l[10]),b(e,"tabindex",d=l[10]?-1:0),y(e,"active",l[24]&&l[26]===l[65]),y(e,"press",l[25]&&l[26]===l[65])},m(m,A){S(m,e,A),L(e,i),L(e,n),c&&c.m(e,null),o||(_=[F(e,"blur",l[33]),F(e,"focus",l[34]),F(e,"keydown",l[35])],o=!0)},p(m,A){m[7]?c?c.p(m,A):(c=tl(m),c.c(),c.m(e,null)):c&&(c.d(1),c=null),A[0]&872415232&&a!==(a=m[28]+": "+m[29][m[65]]+"%; z-index: "+(m[26]===m[65]?3:2)+";")&&b(e,"style",a),A[0]&13&&t!==(t=m[2]===!0&&m[65]===1?m[0][0]:m[3])&&b(e,"aria-valuemin",t),A[0]&21&&s!==(s=m[2]===!0&&m[65]===0?m[0][1]:m[4])&&b(e,"aria-valuemax",s),A[0]&1&&f!==(f=m[63])&&b(e,"aria-valuenow",f),A[0]&11272193&&r!==(r=""+(m[18]+m[21](m[63],m[65],m[23](m[63]))+m[19]))&&b(e,"aria-valuetext",r),A[0]&64&&h!==(h=m[6]?"vertical":"horizontal")&&b(e,"aria-orientation",h),A[0]&1024&&b(e,"aria-disabled",m[10]),A[0]&1024&&b(e,"disabled",m[10]),A[0]&1024&&d!==(d=m[10]?-1:0)&&b(e,"tabindex",d),A[0]&83886080&&y(e,"active",m[24]&&m[26]===m[65]),A[0]&100663296&&y(e,"press",m[25]&&m[26]===m[65])},d(m){m&&V(e),c&&c.d(),o=!1,he(_)}}}function rl(l){let e,i;return{c(){e=H("span"),b(e,"class","rangeBar"),b(e,"style",i=l[28]+": "+l[31](l[29])+"%; "+l[27]+": "+l[32](l[29])+"%;")},m(n,a){S(n,e,a)},p(n,a){a[0]&939524096&&i!==(i=n[28]+": "+n[31](n[29])+"%; "+n[27]+": "+n[32](n[29])+"%;")&&b(e,"style",i)},d(n){n&&V(e)}}}function ol(l){let e,i;return e=new _n({props:{values:l[0],min:l[3],max:l[4],step:l[5],range:l[2],vertical:l[6],reversed:l[8],orientationStart:l[28],hoverable:l[9],disabled:l[10],all:l[13],first:l[14],last:l[15],rest:l[16],pipstep:l[12],prefix:l[18],suffix:l[19],formatter:l[20],focus:l[24],percentOf:l[23],moveHandle:l[30]}}),{c(){Q(e.$$.fragment)},m(n,a){W(e,n,a),i=!0},p(n,a){const t={};a[0]&1&&(t.values=n[0]),a[0]&8&&(t.min=n[3]),a[0]&16&&(t.max=n[4]),a[0]&32&&(t.step=n[5]),a[0]&4&&(t.range=n[2]),a[0]&64&&(t.vertical=n[6]),a[0]&256&&(t.reversed=n[8]),a[0]&268435456&&(t.orientationStart=n[28]),a[0]&512&&(t.hoverable=n[9]),a[0]&1024&&(t.disabled=n[10]),a[0]&8192&&(t.all=n[13]),a[0]&16384&&(t.first=n[14]),a[0]&32768&&(t.last=n[15]),a[0]&65536&&(t.rest=n[16]),a[0]&4096&&(t.pipstep=n[12]),a[0]&262144&&(t.prefix=n[18]),a[0]&524288&&(t.suffix=n[19]),a[0]&1048576&&(t.formatter=n[20]),a[0]&16777216&&(t.focus=n[24]),a[0]&8388608&&(t.percentOf=n[23]),e.$set(t)},i(n){i||(R(e.$$.fragment,n),i=!0)},o(n){N(e.$$.fragment,n),i=!1},d(n){Z(e,n)}}}function mn(l){let e,i,n,a,t,s,f=Pe(l[0]),r=[];for(let o=0;o<f.length;o+=1)r[o]=ul(al(l,f,o));let h=l[2]&&rl(l),d=l[11]&&ol(l);return{c(){e=H("div");for(let o=0;o<r.length;o+=1)r[o].c();i=G(),h&&h.c(),n=G(),d&&d.c(),b(e,"id",l[17]),b(e,"class","rangeSlider"),y(e,"range",l[2]),y(e,"disabled",l[10]),y(e,"hoverable",l[9]),y(e,"vertical",l[6]),y(e,"reversed",l[8]),y(e,"focus",l[24]),y(e,"min",l[2]==="min"),y(e,"max",l[2]==="max"),y(e,"pips",l[11]),y(e,"pip-labels",l[13]==="label"||l[14]==="label"||l[15]==="label"||l[16]==="label")},m(o,_){S(o,e,_);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(e,null);L(e,i),h&&h.m(e,null),L(e,n),d&&d.m(e,null),l[49](e),a=!0,t||(s=[F(window,"mousedown",l[38]),F(window,"touchstart",l[38]),F(window,"mousemove",l[39]),F(window,"touchmove",l[39]),F(window,"mouseup",l[40]),F(window,"touchend",l[41]),F(window,"keydown",l[42]),F(e,"mousedown",l[36]),F(e,"mouseup",l[37]),F(e,"touchstart",we(l[36])),F(e,"touchend",we(l[37]))],t=!0)},p(o,_){if(_[0]&934020317|_[1]&28){f=Pe(o[0]);let c;for(c=0;c<f.length;c+=1){const m=al(o,f,c);r[c]?r[c].p(m,_):(r[c]=ul(m),r[c].c(),r[c].m(e,i))}for(;c<r.length;c+=1)r[c].d(1);r.length=f.length}o[2]?h?h.p(o,_):(h=rl(o),h.c(),h.m(e,n)):h&&(h.d(1),h=null),o[11]?d?(d.p(o,_),_[0]&2048&&R(d,1)):(d=ol(o),d.c(),R(d,1),d.m(e,null)):d&&(Ve(),N(d,1,1,()=>{d=null}),Ee()),(!a||_[0]&131072)&&b(e,"id",o[17]),(!a||_[0]&4)&&y(e,"range",o[2]),(!a||_[0]&1024)&&y(e,"disabled",o[10]),(!a||_[0]&512)&&y(e,"hoverable",o[9]),(!a||_[0]&64)&&y(e,"vertical",o[6]),(!a||_[0]&256)&&y(e,"reversed",o[8]),(!a||_[0]&16777216)&&y(e,"focus",o[24]),(!a||_[0]&4)&&y(e,"min",o[2]==="min"),(!a||_[0]&4)&&y(e,"max",o[2]==="max"),(!a||_[0]&2048)&&y(e,"pips",o[11]),(!a||_[0]&122880)&&y(e,"pip-labels",o[13]==="label"||o[14]==="label"||o[15]==="label"||o[16]==="label")},i(o){a||(R(d),a=!0)},o(o){N(d),a=!1},d(o){o&&V(e),cl(r,o),h&&h.d(),d&&d.d(),l[49](null),t=!1,he(s)}}}function dl(l){if(!l)return-1;for(var e=0;l=l.previousElementSibling;)e++;return e}function Me(l){return l.type.includes("touch")?l.touches[0]:l}function cn(l,e,i){let n,a,t,s,f,r,h=ee,d=()=>(h(),h=Bl(C,u=>i(29,r=u)),C);l.$$.on_destroy.push(()=>h());let{slider:o}=e,{range:_=!1}=e,{pushy:c=!1}=e,{min:m=0}=e,{max:A=100}=e,{step:P=1}=e,{values:v=[(A+m)/2]}=e,{vertical:D=!1}=e,{float:z=!1}=e,{reversed:O=!1}=e,{hoverable:I=!0}=e,{disabled:B=!1}=e,{pips:J=!1}=e,{pipstep:q=void 0}=e,{all:$=void 0}=e,{first:le=void 0}=e,{last:U=void 0}=e,{rest:X=void 0}=e,{id:te=void 0}=e,{prefix:g=""}=e,{suffix:ne=""}=e,{formatter:re=(u,w,M)=>u}=e,{handleFormatter:ce=re}=e,{precision:se=2}=e,{springValues:oe={stiffness:.15,damping:.4}}=e;const de=De();let x=0,k=!1,fe=!1,ue=!1,pe=!1,Y=v.length-1,_e,p,C;function ie(u){const w=o.querySelectorAll(".handle"),M=Array.prototype.includes.call(w,u),E=Array.prototype.some.call(w,ae=>ae.contains(u));return M||E}function ke(u){return _==="min"||_==="max"?u.slice(0,1):_?u.slice(0,2):u}function Fe(){return o.getBoundingClientRect()}function bl(u){const w=Fe();let M=0,E=0,ae=0;D?(M=u.clientY-w.top,E=M/w.height*100,E=O?E:100-E):(M=u.clientX-w.left,E=M/w.width*100,E=O?100-E:E),ae=(A-m)/100*E+m;let Oe;return _===!0&&v[0]===v[1]?ae>v[1]?1:0:(Oe=v.indexOf([...v].sort((Il,Ul)=>Math.abs(ae-Il)-Math.abs(ae-Ul))[0]),Oe)}function He(u){const w=Fe();let M=0,E=0,ae=0;D?(M=u.clientY-w.top,E=M/w.height*100,E=O?E:100-E):(M=u.clientX-w.left,E=M/w.width*100,E=O?100-E:E),ae=(A-m)/100*E+m,ge(Y,ae)}function ge(u,w){return w=t(w),typeof u>"u"&&(u=Y),_&&(u===0&&w>v[1]?c?i(0,v[1]=w,v):w=v[1]:u===1&&w<v[0]&&(c?i(0,v[0]=w,v):w=v[0])),v[u]!==w&&i(0,v[u]=w,v),p!==w&&(Rl(),p=w),w}function hl(u){return _==="min"?0:u[0]}function pl(u){return _==="max"?0:_==="min"?100-u[0]:100-u[1]}function kl(u){pe&&(i(24,k=!1),fe=!1,i(25,ue=!1))}function wl(u){B||(i(26,Y=dl(u.target)),i(24,k=!0))}function vl(u){if(!B){const w=dl(u.target);let M=u.ctrlKey||u.metaKey||u.shiftKey?P*10:P,E=!1;switch(u.key){case"PageDown":M*=10;case"ArrowRight":case"ArrowUp":ge(w,v[w]+M),E=!0;break;case"PageUp":M*=10;case"ArrowLeft":case"ArrowDown":ge(w,v[w]-M),E=!0;break;case"Home":ge(w,m),E=!0;break;case"End":ge(w,A),E=!0;break}E&&(u.preventDefault(),u.stopPropagation())}}function yl(u){if(!B){const w=u.target,M=Me(u);i(24,k=!0),fe=!0,i(25,ue=!0),i(26,Y=bl(M)),_e=p=t(v[Y]),Ml(),u.type==="touchstart"&&!w.matches(".pipVal")&&He(M)}}function Al(u){u.type==="touchend"&&Ne(),i(25,ue=!1)}function Sl(u){pe=!1,k&&u.target!==o&&!o.contains(u.target)&&i(24,k=!1)}function Vl(u){B||fe&&He(Me(u))}function El(u){if(!B){const w=u.target;fe&&((w===o||o.contains(w))&&(i(24,k=!0),!ie(w)&&!w.matches(".pipVal")&&He(Me(u))),Ne())}fe=!1,i(25,ue=!1)}function Pl(u){fe=!1,i(25,ue=!1)}function Hl(u){B||(u.target===o||o.contains(u.target))&&(pe=!0)}function Ml(){!B&&de("start",{activeHandle:Y,value:_e,values:v.map(u=>t(u))})}function Ne(){!B&&de("stop",{activeHandle:Y,startValue:_e,value:v[Y],values:v.map(u=>t(u))})}function Rl(){!B&&de("change",{activeHandle:Y,startValue:_e,previousValue:typeof p>"u"?_e:p,value:v[Y],values:v.map(u=>t(u))})}function Dl(u){Se[u?"unshift":"push"](()=>{o=u,i(1,o)})}return l.$$set=u=>{"slider"in u&&i(1,o=u.slider),"range"in u&&i(2,_=u.range),"pushy"in u&&i(43,c=u.pushy),"min"in u&&i(3,m=u.min),"max"in u&&i(4,A=u.max),"step"in u&&i(5,P=u.step),"values"in u&&i(0,v=u.values),"vertical"in u&&i(6,D=u.vertical),"float"in u&&i(7,z=u.float),"reversed"in u&&i(8,O=u.reversed),"hoverable"in u&&i(9,I=u.hoverable),"disabled"in u&&i(10,B=u.disabled),"pips"in u&&i(11,J=u.pips),"pipstep"in u&&i(12,q=u.pipstep),"all"in u&&i(13,$=u.all),"first"in u&&i(14,le=u.first),"last"in u&&i(15,U=u.last),"rest"in u&&i(16,X=u.rest),"id"in u&&i(17,te=u.id),"prefix"in u&&i(18,g=u.prefix),"suffix"in u&&i(19,ne=u.suffix),"formatter"in u&&i(20,re=u.formatter),"handleFormatter"in u&&i(21,ce=u.handleFormatter),"precision"in u&&i(44,se=u.precision),"springValues"in u&&i(45,oe=u.springValues)},l.$$.update=()=>{l.$$.dirty[0]&24&&i(48,a=function(u){return u<=m?m:u>=A?A:u}),l.$$.dirty[0]&56|l.$$.dirty[1]&139264&&i(47,t=function(u){if(u<=m)return m;if(u>=A)return A;let w=(u-m)%P,M=u-w;return Math.abs(w)*2>=P&&(M+=w>0?P:-P),M=a(M),parseFloat(M.toFixed(se))}),l.$$.dirty[0]&24|l.$$.dirty[1]&8192&&i(23,n=function(u){let w=(u-m)/(A-m)*100;return isNaN(w)||w<=0?0:w>=100?100:parseFloat(w.toFixed(se))}),l.$$.dirty[0]&12582937|l.$$.dirty[1]&114688&&(Array.isArray(v)||(i(0,v=[(A+m)/2]),console.error("'values' prop should be an Array (https://github.com/simeydotme/svelte-range-slider-pips#slider-props)")),i(0,v=ke(v.map(u=>t(u)))),x!==v.length?d(i(22,C=Ol(v.map(u=>n(u)),oe))):C.set(v.map(u=>n(u))),i(46,x=v.length)),l.$$.dirty[0]&320&&i(28,s=D?O?"top":"bottom":O?"right":"left"),l.$$.dirty[0]&320&&i(27,f=D?O?"bottom":"top":O?"left":"right")},[v,o,_,m,A,P,D,z,O,I,B,J,q,$,le,U,X,te,g,ne,re,ce,C,n,k,ue,Y,f,s,r,ge,hl,pl,kl,wl,vl,yl,Al,Sl,Vl,El,Pl,Hl,c,se,oe,x,t,a,Dl]}class gn extends ve{constructor(e){super(),ye(this,e,cn,mn,Ae,{slider:1,range:2,pushy:43,min:3,max:4,step:5,values:0,vertical:6,float:7,reversed:8,hoverable:9,disabled:10,pips:11,pipstep:12,all:13,first:14,last:15,rest:16,id:17,prefix:18,suffix:19,formatter:20,handleFormatter:21,precision:44,springValues:45},null,[-1,-1,-1])}}function bn(l){let e,i,n,a,t,s,f,r;e=new xl({props:{editable:l[7],absolute:!0}}),e.$on("clear",l[15]),e.$on("edit",l[26]);function h(_){l[27](_)}let d={controls:!0,autoplay:l[6],crop_values:l[11],preload:"metadata",src:l[1]?.data,"data-testid":`${l[2]}-audio`};l[10]!==void 0&&(d.node=l[10]),a=new rn({props:d}),Se.push(()=>Ie(a,"node",h)),a.$on("play",l[28]),a.$on("pause",l[29]),a.$on("ended",l[18]);let o=l[9]==="edit"&&l[10]?.duration&&_l(l);return{c(){Q(e.$$.fragment),i=G(),n=H("div"),Q(a.$$.fragment),s=G(),o&&o.c(),f=be(),b(n,"class","container")},m(_,c){W(e,_,c),S(_,i,c),S(_,n,c),W(a,n,null),S(_,s,c),o&&o.m(_,c),S(_,f,c),r=!0},p(_,c){const m={};c[0]&128&&(m.editable=_[7]),e.$set(m);const A={};c[0]&64&&(A.autoplay=_[6]),c[0]&2048&&(A.crop_values=_[11]),c[0]&2&&(A.src=_[1]?.data),c[0]&4&&(A["data-testid"]=`${_[2]}-audio`),!t&&c[0]&1024&&(t=!0,A.node=_[10],Ue(()=>t=!1)),a.$set(A),_[9]==="edit"&&_[10]?.duration?o?(o.p(_,c),c[0]&1536&&R(o,1)):(o=_l(_),o.c(),R(o,1),o.m(f.parentNode,f)):o&&(Ve(),N(o,1,1,()=>{o=null}),Ee())},i(_){r||(R(e.$$.fragment,_),R(a.$$.fragment,_),R(o),r=!0)},o(_){N(e.$$.fragment,_),N(a.$$.fragment,_),N(o),r=!1},d(_){_&&(V(i),V(n),V(s),V(f)),Z(e,_),Z(a),o&&o.d(_)}}}function hn(l){let e,i,n,a;const t=[kn,pn],s=[];function f(r,h){return r[4]==="microphone"?0:r[4]==="upload"?1:-1}return~(e=f(l))&&(i=s[e]=t[e](l)),{c(){i&&i.c(),n=be()},m(r,h){~e&&s[e].m(r,h),S(r,n,h),a=!0},p(r,h){let d=e;e=f(r),e===d?~e&&s[e].p(r,h):(i&&(Ve(),N(s[d],1,1,()=>{s[d]=null}),Ee()),~e?(i=s[e],i?i.p(r,h):(i=s[e]=t[e](r),i.c()),R(i,1),i.m(n.parentNode,n)):i=null)},i(r){a||(R(i),a=!0)},o(r){N(i),a=!1},d(r){r&&V(n),~e&&s[e].d(r)}}}function _l(l){let e,i,n;function a(s){l[30](s)}let t={range:!0,min:0,max:100,step:1};return l[11]!==void 0&&(t.values=l[11]),e=new gn({props:t}),Se.push(()=>Ie(e,"values",a)),e.$on("change",l[16]),{c(){Q(e.$$.fragment)},m(s,f){W(e,s,f),n=!0},p(s,f){const r={};!i&&f[0]&2048&&(i=!0,r.values=s[11],Ue(()=>i=!1)),e.$set(r)},i(s){n||(R(e.$$.fragment,s),n=!0)},o(s){N(e.$$.fragment,s),n=!1},d(s){Z(e,s)}}}function pn(l){let e,i,n;function a(s){l[25](s)}let t={filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",$$slots:{default:[wn]},$$scope:{ctx:l}};return l[0]!==void 0&&(t.dragging=l[0]),e=new Zl({props:t}),Se.push(()=>Ie(e,"dragging",a)),e.$on("load",l[17]),{c(){Q(e.$$.fragment)},m(s,f){W(e,s,f),n=!0},p(s,f){const r={};f[1]&1&&(r.$$scope={dirty:f,ctx:s}),!i&&f[0]&1&&(i=!0,r.dragging=s[0],Ue(()=>i=!1)),e.$set(r)},i(s){n||(R(e.$$.fragment,s),n=!0)},o(s){N(e.$$.fragment,s),n=!1},d(s){Z(e,s)}}}function kn(l){let e,i,n,a;const t=[yn,vn],s=[];function f(r,h){return r[8]?0:1}return i=f(l),n=s[i]=t[i](l),{c(){e=H("div"),n.c(),b(e,"class","mic-wrap svelte-600m6y")},m(r,h){S(r,e,h),s[i].m(e,null),a=!0},p(r,h){let d=i;i=f(r),i===d?s[i].p(r,h):(Ve(),N(s[d],1,1,()=>{s[d]=null}),Ee(),n=s[i],n?n.p(r,h):(n=s[i]=t[i](r),n.c()),R(n,1),n.m(e,null))},i(r){a||(R(n),a=!0)},o(r){N(n),a=!1},d(r){r&&V(e),s[i].d()}}}function wn(l){let e;const i=l[24].default,n=zl(i,l,l[31],null);return{c(){n&&n.c()},m(a,t){n&&n.m(a,t),e=!0},p(a,t){n&&n.p&&(!e||t[1]&1)&&Kl(n,i,a,a[31],e?ql(i,a[31],t,null):jl(a[31]),null)},i(a){e||(R(n,a),e=!0)},o(a){N(n,a),e=!1},d(a){n&&n.d(a)}}}function vn(l){let e,i;return e=new gl({props:{size:"sm",$$slots:{default:[An]},$$scope:{ctx:l}}}),e.$on("click",l[13]),{c(){Q(e.$$.fragment)},m(n,a){W(e,n,a),i=!0},p(n,a){const t={};a[0]&4096|a[1]&1&&(t.$$scope={dirty:a,ctx:n}),e.$set(t)},i(n){i||(R(e.$$.fragment,n),i=!0)},o(n){N(e.$$.fragment,n),i=!1},d(n){Z(e,n)}}}function yn(l){let e,i;return e=new gl({props:{size:"sm",$$slots:{default:[Sn]},$$scope:{ctx:l}}}),e.$on("click",l[14]),{c(){Q(e.$$.fragment)},m(n,a){W(e,n,a),i=!0},p(n,a){const t={};a[0]&4096|a[1]&1&&(t.$$scope={dirty:a,ctx:n}),e.$set(t)},i(n){i||(R(e.$$.fragment,n),i=!0)},o(n){N(e.$$.fragment,n),i=!1},d(n){Z(e,n)}}}function An(l){let e,i,n=l[12]("audio.record_from_microphone")+"",a;return{c(){e=H("span"),e.innerHTML='<span class="dot svelte-600m6y"></span>',i=G(),a=K(n),b(e,"class","record-icon svelte-600m6y")},m(t,s){S(t,e,s),S(t,i,s),S(t,a,s)},p(t,s){s[0]&4096&&n!==(n=t[12]("audio.record_from_microphone")+"")&&j(a,n)},d(t){t&&(V(e),V(i),V(a))}}}function Sn(l){let e,i,n=l[12]("audio.stop_recording")+"",a;return{c(){e=H("span"),e.innerHTML='<span class="pinger svelte-600m6y"></span> <span class="dot svelte-600m6y"></span>',i=G(),a=K(n),b(e,"class","record-icon svelte-600m6y")},m(t,s){S(t,e,s),S(t,i,s),S(t,a,s)},p(t,s){s[0]&4096&&n!==(n=t[12]("audio.stop_recording")+"")&&j(a,n)},d(t){t&&(V(e),V(i),V(a))}}}function Vn(l){let e,i,n,a,t,s;e=new $l({props:{show_label:l[3],Icon:ln,float:l[4]==="upload"&&l[1]===null,label:l[2]||l[12]("audio.audio")}});const f=[hn,bn],r=[];function h(d,o){return d[1]===null||d[5]?0:1}return n=h(l),a=r[n]=f[n](l),{c(){Q(e.$$.fragment),i=G(),a.c(),t=be()},m(d,o){W(e,d,o),S(d,i,o),r[n].m(d,o),S(d,t,o),s=!0},p(d,o){const _={};o[0]&8&&(_.show_label=d[3]),o[0]&18&&(_.float=d[4]==="upload"&&d[1]===null),o[0]&4100&&(_.label=d[2]||d[12]("audio.audio")),e.$set(_);let c=n;n=h(d),n===c?r[n].p(d,o):(Ve(),N(r[c],1,1,()=>{r[c]=null}),Ee(),a=r[n],a?a.p(d,o):(a=r[n]=f[n](d),a.c()),R(a,1),a.m(t.parentNode,t))},i(d){s||(R(e.$$.fragment,d),R(a),s=!0)},o(d){N(e.$$.fragment,d),N(a),s=!1},d(d){d&&(V(i),V(t)),Z(e,d),r[n].d(d)}}}const En=500,ml=44;function Pn(l){return new Promise((e,i)=>{let n=new FileReader;n.onerror=i,n.onload=()=>e(n.result),n.readAsDataURL(l)})}function Hn(l,e,i){let n;Ll(l,Cl,p=>i(12,n=p));let{$$slots:a={},$$scope:t}=e,{value:s=null}=e,{label:f}=e,{show_label:r=!0}=e,{name:h=""}=e,{source:d}=e,{pending:o=!1}=e,{streaming:_=!1}=e,{autoplay:c=!1}=e,{show_edit_button:m=!0}=e,A=!1,P,v="",D,z=[],O=!1,I,B=!1,J=[0,100],q=[],$;function le(){$=[Ce(()=>import("./module-ab2763d0.js"),["assets/module-ab2763d0.js","assets/index-7674dbb6.js","assets/index-642268e4.css"]),Ce(()=>import("./module-1791af61.js"),[])]}_&&le();const U=De(),X=async(p,C)=>{let ie=new Blob(p,{type:"audio/wav"});i(1,s={data:await Pn(ie),name:"audio.wav"}),U(C,s)};async function te(){let p;try{p=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(C){if(!navigator.mediaDevices){U("error",n("audio.no_device_support"));return}if(C instanceof DOMException&&C.name=="NotAllowedError"){U("error",n("audio.allow_recording_access"));return}throw C}if(p!=null){if(_){const[{MediaRecorder:C,register:ie},{connect:ke}]=await Promise.all($);await ie(await ke()),P=new C(p,{mimeType:"audio/wav"}),P.addEventListener("dataavailable",g)}else P=new MediaRecorder(p),P.addEventListener("dataavailable",C=>{q.push(C.data)}),P.addEventListener("stop",async()=>{i(8,A=!1),await X(q,"change"),await X(q,"stop_recording"),q=[]});B=!0}}async function g(p){let C=await p.data.arrayBuffer(),ie=new Uint8Array(C);if(D||(i(21,D=new Uint8Array(C.slice(0,ml))),ie=new Uint8Array(C.slice(ml))),o)z.push(ie);else{let ke=[D].concat(z,[ie]);X(ke,"stream"),i(22,z=[])}}async function ne(){if(!navigator.mediaDevices){U("error",n("audio.no_device_support"));return}i(8,A=!0),U("start_recording"),B||await te(),i(21,D=void 0),_?P.start(En):P.start()}Tl(()=>{P&&P.state!=="inactive"&&P.stop()});function re(){P.stop(),_&&(i(8,A=!1),U("stop_recording"),o&&i(23,O=!0))}function ce(){U("change",null),U("clear"),i(9,v=""),i(1,s=null)}function se({detail:{values:p}}){s&&(U("change",{data:s.data,name:h,crop_min:p[0],crop_max:p[1]}),U("edit"))}function oe({detail:p}){i(1,s=p),U("change",{data:p.data,name:p.name}),U("upload",p)}function de(){U("stop"),U("end")}let{dragging:x=!1}=e;function k(p){x=p,i(0,x)}const fe=()=>i(9,v="edit");function ue(p){I=p,i(10,I)}function pe(p){Te.call(this,l,p)}function Y(p){Te.call(this,l,p)}function _e(p){J=p,i(11,J)}return l.$$set=p=>{"value"in p&&i(1,s=p.value),"label"in p&&i(2,f=p.label),"show_label"in p&&i(3,r=p.show_label),"name"in p&&i(19,h=p.name),"source"in p&&i(4,d=p.source),"pending"in p&&i(20,o=p.pending),"streaming"in p&&i(5,_=p.streaming),"autoplay"in p&&i(6,c=p.autoplay),"show_edit_button"in p&&i(7,m=p.show_edit_button),"dragging"in p&&i(0,x=p.dragging),"$$scope"in p&&i(31,t=p.$$scope)},l.$$.update=()=>{if(l.$$.dirty[0]&15728640&&O&&o===!1&&(i(23,O=!1),D&&z)){let p=[D].concat(z);i(22,z=[]),X(p,"stream")}l.$$.dirty[0]&1&&U("drag",x)},[x,s,f,r,d,_,c,m,A,v,I,J,n,ne,re,ce,se,oe,de,h,o,D,z,O,a,k,fe,ue,pe,Y,_e,t]}class Mn extends ve{constructor(e){super(),ye(this,e,Hn,Vn,Ae,{value:1,label:2,show_label:3,name:19,source:4,pending:20,streaming:5,autoplay:6,show_edit_button:7,dragging:0},null,[-1,-1])}}function Rn(l){let e,i;return e=new Wl({props:{type:"audio"}}),{c(){Q(e.$$.fragment)},m(n,a){W(e,n,a),i=!0},p:ee,i(n){i||(R(e.$$.fragment,n),i=!0)},o(n){N(e.$$.fragment,n),i=!1},d(n){Z(e,n)}}}function Dn(l){let e,i,n,a;const t=[l[1]];let s={};for(let f=0;f<t.length;f+=1)s=Re(s,t[f]);return e=new Xl({props:s}),n=new Mn({props:{label:l[7],show_label:l[8],value:l[17],name:l[5],source:l[6],pending:l[9],streaming:l[10],autoplay:l[14],show_edit_button:l[15],$$slots:{default:[Rn]},$$scope:{ctx:l}}}),n.$on("change",l[22]),n.$on("stream",l[23]),n.$on("drag",l[24]),n.$on("edit",l[25]),n.$on("play",l[26]),n.$on("pause",l[27]),n.$on("stop",l[28]),n.$on("end",l[29]),n.$on("start_recording",l[30]),n.$on("stop_recording",l[31]),n.$on("upload",l[32]),n.$on("clear",l[33]),n.$on("error",l[34]),{c(){Q(e.$$.fragment),i=G(),Q(n.$$.fragment)},m(f,r){W(e,f,r),S(f,i,r),W(n,f,r),a=!0},p(f,r){const h=r[0]&2?Yl(t,[Jl(f[1])]):{};e.$set(h);const d={};r[0]&128&&(d.label=f[7]),r[0]&256&&(d.show_label=f[8]),r[0]&131072&&(d.value=f[17]),r[0]&32&&(d.name=f[5]),r[0]&64&&(d.source=f[6]),r[0]&512&&(d.pending=f[9]),r[0]&1024&&(d.streaming=f[10]),r[0]&16384&&(d.autoplay=f[14]),r[0]&32768&&(d.show_edit_button=f[15]),r[1]&16&&(d.$$scope={dirty:r,ctx:f}),n.$set(d)},i(f){a||(R(e.$$.fragment,f),R(n.$$.fragment,f),a=!0)},o(f){N(e.$$.fragment,f),N(n.$$.fragment,f),a=!1},d(f){f&&V(i),Z(e,f),Z(n,f)}}}function In(l){let e,i;return e=new Gl({props:{variant:l[0]===null&&l[6]==="upload"?"dashed":"solid",border_mode:l[18]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],visible:l[4],container:l[11],scale:l[12],min_width:l[13],$$slots:{default:[Dn]},$$scope:{ctx:l}}}),{c(){Q(e.$$.fragment)},m(n,a){W(e,n,a),i=!0},p(n,a){const t={};a[0]&65&&(t.variant=n[0]===null&&n[6]==="upload"?"dashed":"solid"),a[0]&262144&&(t.border_mode=n[18]?"focus":"base"),a[0]&4&&(t.elem_id=n[2]),a[0]&8&&(t.elem_classes=n[3]),a[0]&16&&(t.visible=n[4]),a[0]&2048&&(t.container=n[11]),a[0]&4096&&(t.scale=n[12]),a[0]&8192&&(t.min_width=n[13]),a[0]&509923|a[1]&16&&(t.$$scope={dirty:a,ctx:n}),e.$set(t)},i(n){i||(R(e.$$.fragment,n),i=!0)},o(n){N(e.$$.fragment,n),i=!1},d(n){Z(e,n)}}}function Un(l,e,i){let{elem_id:n=""}=e,{elem_classes:a=[]}=e,{visible:t=!0}=e,{value:s=null}=e,{name:f}=e,{source:r}=e,{label:h}=e,{root:d}=e,{show_label:o}=e,{pending:_}=e,{streaming:c}=e,{root_url:m}=e,{container:A=!0}=e,{scale:P=null}=e,{min_width:v=void 0}=e,{loading_status:D}=e,{autoplay:z=!1}=e,{show_edit_button:O=!0}=e,{gradio:I}=e,B=null,J,q;const $=({detail:k})=>i(0,s=k),le=({detail:k})=>{i(0,s=k),I.dispatch("stream",s)},U=({detail:k})=>i(18,q=k),X=()=>I.dispatch("edit"),te=()=>I.dispatch("play"),g=()=>I.dispatch("pause"),ne=()=>I.dispatch("stop"),re=()=>I.dispatch("end"),ce=()=>I.dispatch("start_recording"),se=()=>I.dispatch("stop_recording"),oe=()=>I.dispatch("upload"),de=()=>I.dispatch("clear"),x=({detail:k})=>{i(1,D=D||{}),i(1,D.status="error",D),I.dispatch("error",k)};return l.$$set=k=>{"elem_id"in k&&i(2,n=k.elem_id),"elem_classes"in k&&i(3,a=k.elem_classes),"visible"in k&&i(4,t=k.visible),"value"in k&&i(0,s=k.value),"name"in k&&i(5,f=k.name),"source"in k&&i(6,r=k.source),"label"in k&&i(7,h=k.label),"root"in k&&i(19,d=k.root),"show_label"in k&&i(8,o=k.show_label),"pending"in k&&i(9,_=k.pending),"streaming"in k&&i(10,c=k.streaming),"root_url"in k&&i(20,m=k.root_url),"container"in k&&i(11,A=k.container),"scale"in k&&i(12,P=k.scale),"min_width"in k&&i(13,v=k.min_width),"loading_status"in k&&i(1,D=k.loading_status),"autoplay"in k&&i(14,z=k.autoplay),"show_edit_button"in k&&i(15,O=k.show_edit_button),"gradio"in k&&i(16,I=k.gradio)},l.$$.update=()=>{l.$$.dirty[0]&1572865&&i(17,J=Ql(s,d,m)),l.$$.dirty[0]&2162689&&JSON.stringify(s)!==JSON.stringify(B)&&(i(21,B=s),I.dispatch("change"))},[s,D,n,a,t,f,r,h,o,_,c,A,P,v,z,O,I,J,q,d,m,B,$,le,U,X,te,g,ne,re,ce,se,oe,de,x]}class Fn extends ve{constructor(e){super(),ye(this,e,Un,In,Ae,{elem_id:2,elem_classes:3,visible:4,value:0,name:5,source:6,label:7,root:19,show_label:8,pending:9,streaming:10,root_url:20,container:11,scale:12,min_width:13,loading_status:1,autoplay:14,show_edit_button:15,gradio:16},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),T()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),T()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),T()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),T()}get name(){return this.$$.ctx[5]}set name(e){this.$$set({name:e}),T()}get source(){return this.$$.ctx[6]}set source(e){this.$$set({source:e}),T()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),T()}get root(){return this.$$.ctx[19]}set root(e){this.$$set({root:e}),T()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),T()}get pending(){return this.$$.ctx[9]}set pending(e){this.$$set({pending:e}),T()}get streaming(){return this.$$.ctx[10]}set streaming(e){this.$$set({streaming:e}),T()}get root_url(){return this.$$.ctx[20]}set root_url(e){this.$$set({root_url:e}),T()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),T()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),T()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),T()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),T()}get autoplay(){return this.$$.ctx[14]}set autoplay(e){this.$$set({autoplay:e}),T()}get show_edit_button(){return this.$$.ctx[15]}set show_edit_button(e){this.$$set({show_edit_button:e}),T()}get gradio(){return this.$$.ctx[16]}set gradio(e){this.$$set({gradio:e}),T()}}const Xn=Fn;export{Xn as default};
//# sourceMappingURL=index-6bc4eff2.js.map
