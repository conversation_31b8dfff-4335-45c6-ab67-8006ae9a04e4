{"version": 3, "mappings": "q2BA+ByBA,EAAAC,MAAM,QAAO,4DAArCC,EAAyCC,EAAAC,EAAAC,CAAA,iBAAjBC,EAAA,GAAAN,OAAAC,MAAM,QAAO,KAAAM,EAAAC,EAAAR,CAAA,oFAX/BC,EAAY,KAAAQ,EAAAC,EAAA,MAAAC,CAAA,sCAQJF,EAAAC,EAAA,cAAAE,EAAAX,KAAQ,aAAa,wCATnCC,EAUCC,EAAAO,EAAAL,CAAA,iBANSQ,EAAAH,EAAA,OAAAT,EAAS,QAAK,KAAM,MAAM,GACzBY,EAAAH,EAAA,QAAAT,EAAS,QAAK,KAAM,OAAO,GAC3BY,EAAAH,EAAA,QAAAT,EAAS,QAAK,KAAM,OAAO,wBAEvB,SAAAA,EAAQ,GAAE,YAAAA,EAAW,sCAP9BA,EAAY,oEAQJK,EAAA,KAAAM,OAAAX,KAAQ,aAAa,oEADpB,SAAAA,EAAQ,GAAE,YAAAA,EAAW,wMAT7B,OAAAa,GAAAC,EAAAC,GAAiBf,EAAG,IAAAgB,CAAA,6HAApBX,EAAA,GAAAS,OAAAC,GAAiBf,EAAG,MAAAa,GAAAC,EAAAE,CAAA,2FAZhB,QAAAC,EAAkC,MAAS,EAAAC,EAE3C,UAAAC,EAAqC,MAAS,EAAAD,EAC9C,aAAAE,EAA2C,MAAS,EAAAF,EACpD,UAAAG,EAA4C,MAAS,EAAAH,EACrD,SAAAI,EAA0C,MAAS,EAAAJ,EAEnD,MAAAK,EAAqC,MAAS,EAAAL,EAEnD,MAAAM,EAAWC,+CAWLF,EAAIG,4eC4JNC,GAAA3B,EAAQ,cAAWA,OAAU,UAAO4B,GAAA5B,CAAA,yEAJjCA,EAAgB,aAFR6B,EAAAC,EAAA,WAAA9B,MAAWA,EAAG,KACd6B,EAAAC,EAAA,WAAA9B,MAAQA,EAAG,aAH7BC,EAaMC,EAAA4B,EAAA1B,CAAA,+CARM2B,GAAA/B,EAAW,IAAAA,EAAX,MAAAA,MAAWA,EAAG,yDACI+B,GAAA/B,EAAW,IAAAA,EAAX,MAAAA,MAAWA,EAAG,gDAErCA,EAAQ,cAAWA,OAAU,kFAJ1BA,EAAgB,uCAFR6B,EAAAC,EAAA,WAAA9B,MAAWA,EAAG,cACd6B,EAAAC,EAAA,WAAA9B,MAAQA,EAAG,8DAOsCD,EAAAC,EAAU,IAAAA,EAAI,KAAE,CAAC,SAAzEA,EAAM,KAAAgC,GAAAhC,CAAA,IAA0EA,EAAM,KAAAiC,GAAAjC,CAAA,gFAD7FC,EAEMC,EAAA4B,EAAA1B,CAAA,+CADCJ,EAAM,2DAAkDK,EAAA,MAAAN,OAAAC,EAAU,IAAAA,EAAI,KAAE,CAAC,OAAAM,EAAAC,EAAAR,CAAA,EAAOC,EAAM,kJAAlDA,EAAM,0CAAnCC,EAA2CC,EAAA4B,EAAA1B,CAAA,6BAAdJ,EAAM,uEAA0EA,EAAM,0CAAnCC,EAA2CC,EAAA4B,EAAA1B,CAAA,6BAAdJ,EAAM,+CAO9H,MAAMA,EAAQ,IAAG,CAAC,wBAAvB,OAAIkC,GAAA,sKAAC,MAAMlC,EAAQ,IAAG,CAAC,qBAAvB,OAAIkC,GAAA,6HAAJ,6DAUSP,GAAA3B,EAAQ,cAAWA,OAAS,UAAOmC,GAAAnC,CAAA,2DAJhCQ,EAAAsB,EAAA,QAAAM,EAAApC,EAAoB,SAAAA,EAAU,IAAAA,MAAOA,EAAC,2BAF9BA,EAAU,IAACA,EAAM,IAACA,EAAC,sBACnBA,EAAO,IAACA,EAAM,IAACA,EAAC,eAHlCC,EAaMC,EAAA4B,EAAA1B,CAAA,yDARMJ,EAAU,IAACA,EAAO,IAAAA,UAAlBA,EAAU,IAACA,EAAM,IAACA,EAAC,8DACDA,EAAU,IAACA,EAAO,IAAAA,UAAlBA,EAAU,IAACA,EAAM,IAACA,EAAC,kDAE1CA,EAAQ,cAAWA,OAAS,+DAJzBK,EAAA,QAAA+B,OAAApC,EAAoB,SAAAA,EAAU,IAAAA,MAAOA,EAAC,qDAF9BA,EAAU,IAACA,EAAM,IAACA,EAAC,gCACnBA,EAAO,IAACA,EAAM,IAACA,EAAC,kEAOiCA,EAAS,IAACA,EAAM,IAACA,EAAC,KAAEA,EAAC,IAACA,EAAS,IAACA,EAAM,IAACA,EAAC,cAAhGA,EAAM,KAAAqC,GAAArC,CAAA,IAAmGA,EAAM,KAAAsC,GAAAtC,CAAA,gFADtHC,EAEMC,EAAA4B,EAAA1B,CAAA,+CADCJ,EAAM,4EAAkDA,EAAS,IAACA,EAAM,IAACA,EAAC,KAAEA,EAAC,IAACA,EAAS,IAACA,EAAM,IAACA,EAAC,YAAAM,EAAAC,EAAAR,CAAA,EAASC,EAAM,kJAA3EA,EAAM,0CAAnCC,EAA2CC,EAAA4B,EAAA1B,CAAA,6BAAdJ,EAAM,uEAAmGA,EAAM,0CAAnCC,EAA2CC,EAAA4B,EAAA1B,CAAA,6BAAdJ,EAAM,0CAX3JA,EAAM,IAACA,EAAO,OAAAA,MAAOA,EAAM,IAACA,EAAC,OAAMA,EAAG,6FAAtCA,EAAM,IAACA,EAAO,OAAAA,MAAOA,EAAM,IAACA,EAAC,OAAMA,EAAG,6HA4BtC2B,GAAA3B,EAAQ,cAAWA,OAAS,UAAOuC,GAAAvC,CAAA,wEAJhCA,EAAgB,eAFR6B,EAAAC,EAAA,WAAA9B,MAAWA,EAAG,KACd6B,EAAAC,EAAA,WAAA9B,MAAQA,EAAG,aAH7BC,EAaMC,EAAA4B,EAAA1B,CAAA,+CARM2B,GAAA/B,EAAW,IAAAA,EAAX,MAAAA,MAAWA,EAAG,yDACI+B,GAAA/B,EAAW,IAAAA,EAAX,MAAAA,MAAWA,EAAG,gDAErCA,EAAQ,cAAWA,OAAS,kFAJzBA,EAAgB,yCAFR6B,EAAAC,EAAA,WAAA9B,MAAWA,EAAG,cACd6B,EAAAC,EAAA,WAAA9B,MAAQA,EAAG,8DAOsCD,EAAAC,EAAU,IAAAA,EAAI,GAAAA,MAAS,GAAG,SAAlFA,EAAM,KAAAwC,GAAAxC,CAAA,IAAmFA,EAAM,KAAAyC,GAAAzC,CAAA,gFADtGC,EAEMC,EAAA4B,EAAA1B,CAAA,+CADCJ,EAAM,2DAAkDK,EAAA,QAAAN,OAAAC,EAAU,IAAAA,EAAI,GAAAA,MAAS,GAAG,OAAAM,EAAAC,EAAAR,CAAA,EAAOC,EAAM,kJAA3DA,EAAM,0CAAnCC,EAA2CC,EAAA4B,EAAA1B,CAAA,6BAAdJ,EAAM,uEAAmFA,EAAM,0CAAnCC,EAA2CC,EAAA4B,EAAA1B,CAAA,6BAAdJ,EAAM,8CAjDzI0C,GAAA1C,EAAO,IAAAA,EAAU,SAAWA,EAAK,KAAA2C,GAAA3C,CAAA,EAiBjC4C,GAAA5C,EAAO,IAAAA,EAAS,SAAWA,EAAI,KAAA6C,GAAA7C,CAAA,EAqB/B8C,GAAA9C,EAAO,IAAAA,EAAS,SAAWA,EAAI,KAAA+C,GAAA/C,CAAA,wMA9CxCC,EA+DKC,EAAA8C,EAAA5C,CAAA,uEAvDIJ,EAAO,IAAAA,EAAU,SAAWA,EAAK,0DAiBjCA,EAAO,IAAAA,EAAS,SAAWA,EAAI,0DAqB/BA,EAAO,IAAAA,EAAS,SAAWA,EAAI,yRAhN3B,OAAAiD,EAAQ,EAAK,EAAA/B,EACb,KAAAgC,EAAM,CAAC,EAAAhC,EACP,KAAAiC,EAAM,GAAG,EAAAjC,EACT,MAAAkC,EAAO,CAAC,EAAAlC,EACR,QAAAmC,EAAW,EAAAF,EAAMD,GAAO,CAAC,GAAAhC,EACzB,UAAAoC,EAAW,EAAK,EAAApC,EAChB,UAAAqC,EAAW,EAAK,EAAArC,EAChB,WAAAsC,EAAY,EAAI,EAAAtC,EAChB,UAAAuC,EAAW,EAAK,EAAAvC,EAGhB,SAAAwC,EAAU,MAAS,EAAAxC,EACnB,KAAAyC,EAAM,EAAI,EAAAzC,EACV,OAAA0C,EAAQ,MAAS,EAAA1C,EACjB,MAAA2C,EAAO,MAAS,EAAA3C,EAChB,MAAA4C,EAAO,MAAS,EAAA5C,EAGhB,QAAA6C,EAAS,EAAE,EAAA7C,EACX,QAAA8C,EAAS,EAAE,EAAA9C,EACX,WAAA+C,EAAa,CAAAC,EAAEhC,KAAMgC,CAAC,EAAAhD,EAGtB,OAAAiD,EAAQ,MAAS,EAAAjD,EACjB,kBAAAkD,GAAmB,MAAS,EAAAlD,EAG5B,WAAAmD,EAAY,MAAS,EAAAnD,EACrB,YAAAoD,EAAa,MAAS,EAAApD,EAwBxB,SAAAqD,GAAWC,EAAG,CACrBF,EAAY,OAAWE,CAAG,stBAvBzBC,EAAA,GAAAC,EAAUhB,KAAaP,EAAMD,GAAOE,IAAUE,EAAW,GAAK,MAASH,EAAMD,IAASI,EAAW,GAAK,IAAO,EAAC,uBAE9GmB,EAAA,GAAAE,EAAW,UAAUxB,EAAMD,IAAQE,EAAOsB,GAAU,EAAE,wBAExDD,EAAA,GAAEG,EAAM,SAAYJ,EAAG,CACf,OAAAtB,EAAMsB,EAAMpB,EAAOsB,wBAG3BD,EAAA,GAAEI,EAAU,SAAYL,EAAG,CACnB,OAAAnB,EAAO,KAAKa,IAAKA,KAAMM,CAAG,yBAGlCC,EAAA,GAAEK,EAAO,SAAYN,EAAG,CACnB,GAAAvB,IAAU,aACLI,EAAO,CAAC,EAAImB,EACV,GAAAvB,IAAU,aACZI,EAAO,CAAC,EAAImB,KACVvB,SACFI,EAAO,CAAC,EAAImB,GAAOnB,EAAO,CAAC,EAAImB,2aCmwB+BzE,EAAAC,MAAgBA,EAAK,IAACA,EAAM,IAAAA,MAAUA,EAAK,aAAvGA,EAAM,KAAA6C,GAAA7C,CAAA,IAAyGA,EAAM,KAAAwC,GAAAxC,CAAA,oFAD5HC,EAEMC,EAAA4B,EAAA1B,CAAA,+CADCJ,EAAM,2DAAsDK,EAAA,aAAAN,OAAAC,MAAgBA,EAAK,IAACA,EAAM,IAAAA,MAAUA,EAAK,WAAAM,EAAAC,EAAAR,CAAA,EAAQC,EAAM,kJAA7EA,EAAM,8CAAvCC,EAA+CC,EAAA4B,EAAA1B,CAAA,kCAAdJ,EAAM,uEAAyGA,EAAM,8CAAvCC,EAA+CC,EAAA4B,EAAA1B,CAAA,kCAAdJ,EAAM,kEAFjKA,EAAK,IAAAyC,GAAAzC,CAAA,8IAfGA,EAAK,mBAIVA,EAAgB,SAAIA,EAAgB,IAACA,EAAK,qBAAeA,EAAY,MAAKA,EAAK,IAAG,EAAI,GAAC,KAChFQ,EAAAuE,EAAA,gBAAAC,EAAAhF,EAAU,SAAQA,QAAU,EAAIA,KAAO,CAAC,EAAIA,EAAG,IAC/CQ,EAAAuE,EAAA,gBAAAE,EAAAjF,EAAU,SAAQA,QAAU,EAAIA,KAAO,CAAC,EAAIA,EAAG,0BAC/CA,EAAK,gCACHA,EAAM,IAAEA,EAAe,IAACA,EAAK,IAACA,EAAK,IAACA,EAAS,IAACA,EAAK,MAAIA,EAAM,+BAC5DA,EAAQ,GAAG,WAAa,YAAY,sBACvCA,EAAQ,4CAEXA,EAAQ,OAAQ,CAAC,eAdfA,EAAK,KAAIA,EAAY,MAAKA,EAAK,iBAChCA,EAAa,KAAIA,EAAY,MAAKA,EAAK,aAJtDC,EAyBMC,EAAA6E,EAAA3E,CAAA,EANJ8E,EAAwBH,EAAAI,CAAA,0CAbfnF,EAAgB,iBACfA,EAAiB,mBACfA,EAAa,oBAYpBA,EAAK,oFAXFA,EAAgB,SAAIA,EAAgB,IAACA,EAAK,qBAAeA,EAAY,MAAKA,EAAK,IAAG,EAAI,GAAC,qBAChFK,EAAA,OAAA2E,OAAAhF,EAAU,SAAQA,QAAU,EAAIA,KAAO,CAAC,EAAIA,EAAG,4BAC/CK,EAAA,OAAA4E,OAAAjF,EAAU,SAAQA,QAAU,EAAIA,KAAO,CAAC,EAAIA,EAAG,2CAC/CA,EAAK,uDACHA,EAAM,IAAEA,EAAe,IAACA,EAAK,IAACA,EAAK,IAACA,EAAS,IAACA,EAAK,MAAIA,EAAM,+CAC5DA,EAAQ,GAAG,WAAa,wEAC3BA,EAAQ,wDAEXA,EAAQ,OAAQ,kDAddA,EAAK,KAAIA,EAAY,MAAKA,EAAK,iCAChCA,EAAa,KAAIA,EAAY,MAAKA,EAAK,6HA0B5CA,EAAgB,SAAIA,EAAW,IAAAA,aAC/BA,EAAc,SAAIA,EAAQ,IAACA,EAAgB,mBAHrDC,EAG4DC,EAAA4B,EAAA1B,CAAA,iCADlDJ,EAAgB,SAAIA,EAAW,IAAAA,aAC/BA,EAAc,SAAIA,EAAQ,IAACA,EAAgB,omCAhChDA,EAAM,yBAAX,OAAIkC,GAAA,2BA4BDlC,EAAK,IAAAuC,GAAAvC,CAAA,IAMLA,EAAI,KAAA+C,GAAA/C,CAAA,iQA3CE6B,EAAAmB,EAAA,MAAAhD,OAAU,KAAK,EACf6B,EAAAmB,EAAA,MAAAhD,OAAU,KAAK,oBAER6B,EAAAmB,EAAA,aAAAhD,EAAQ,eAAWA,EAAU,eAAWA,EAAS,eAAWA,QAAS,OAAO,UAbhGC,EA8EKC,EAAA8C,EAAA5C,CAAA,yIAGWJ,EAAiB,2BAChBA,EAAiB,0BAClBA,EAAY,0BACZA,EAAY,wBACdA,EAAW,yBACVA,EAAY,wBACbA,EAAW,qBAzETA,EAAmB,mBACrBA,EAAiB,yBACCA,EAAmB,wBACrBA,EAAiB,qDAEtCA,EAAM,sBAAX,OAAIkC,GAAA,kHAAJ,OA4BGlC,EAAK,0DAMLA,EAAI,0XA3CE6B,EAAAmB,EAAA,MAAAhD,OAAU,KAAK,gBACf6B,EAAAmB,EAAA,MAAAhD,OAAU,KAAK,wDAER6B,EAAAmB,EAAA,aAAAhD,EAAQ,eAAWA,EAAU,eAAWA,EAAS,eAAWA,QAAS,OAAO,wGA5nBrF,SAAAoF,GAAMC,EAAE,CACV,IAAAA,mBACDnD,EAAI,EACAmD,EAAKA,EAAG,wBACdnD,WAEKA,EASA,SAAAoD,GAAiBC,EAAC,CACrB,OAAAA,EAAE,KAAK,SAAS,OAAO,EAClBA,EAAE,QAAQ,CAAC,EAEXA,+GA1KA,OAAAC,CAAM,EAAAtE,EAGN,OAAA+B,EAAQ,EAAK,EAAA/B,EACb,OAAAuE,EAAQ,EAAK,EAAAvE,EACb,KAAAgC,EAAM,CAAC,EAAAhC,EACP,KAAAiC,EAAM,GAAG,EAAAjC,EACT,MAAAkC,EAAO,CAAC,EAAAlC,EACR,QAAAmC,EAAW,EAAAF,EAAMD,GAAO,CAAC,GAAAhC,EACzB,UAAAoC,EAAW,EAAK,EAAApC,EAChB,OAAAwE,EAAQ,EAAK,EAAAxE,EACb,UAAAqC,EAAW,EAAK,EAAArC,EAChB,WAAAsC,EAAY,EAAI,EAAAtC,EAChB,UAAAuC,EAAW,EAAK,EAAAvC,EAGhB,MAAAyE,EAAO,EAAK,EAAAzE,EACZ,SAAAwC,EAAU,MAAS,EAAAxC,EACnB,KAAAyC,EAAM,MAAS,EAAAzC,EACf,OAAA0C,GAAQ,MAAS,EAAA1C,EACjB,MAAA2C,EAAO,MAAS,EAAA3C,EAChB,MAAA4C,EAAO,MAAS,EAAA5C,EAGhB,IAAA0E,GAAK,MAAS,EAAA1E,EACd,QAAA6C,EAAS,EAAE,EAAA7C,EACX,QAAA8C,GAAS,EAAE,EAAA9C,EACX,WAAA+C,IAAaC,EAAEhC,EAAE/B,IAAM+D,CAAC,EAAAhD,EACxB,iBAAA2E,GAAkB5B,EAAS,EAAA/C,EAG3B,WAAA4E,GAAY,CAAC,EAAA5E,EACb,cAAA6E,IAAiB,UAAW,IAAM,QAAS,EAAG,GAAA7E,EAGnD,MAAAM,GAAWC,KAGb,IAAAuE,EAAc,EACd7B,EAAQ,GACR8B,GAAkB,GAClBC,GAAgB,GAChBC,GAAiB,GACjBC,EAAe/C,EAAO,OAAS,EAC/BgD,GACAC,EAKAC,EAiIK,SAAAC,GAAenB,EAAE,CAClB,MAAAoB,EAAUjB,EAAO,iBAAiB,SAAS,EAC3CkB,EAAW,MAAM,UAAU,SAAS,KAAKD,EAASpB,CAAE,EACpDsB,EAAU,MAAM,UAAU,KAAK,KAAKF,EAAUlB,IAAMA,GAAE,SAASF,CAAE,GAChE,OAAAqB,GAAYC,EAWZ,SAAAC,GAAUvD,EAAM,CACnB,OAAAJ,IAAU,OAASA,IAAU,MACxBI,EAAO,MAAM,EAAG,CAAC,EACfJ,EACFI,EAAO,MAAM,EAAG,CAAC,EAEjBA,WASFwD,IAAmB,CACnB,OAAArB,EAAO,wBAQP,SAAAsB,GAAiBC,EAAS,CAG3B,MAAAC,EAAOH,KAET,IAAAI,EAAY,EACZC,EAAgB,EAChBC,GAAY,EACZ7D,GACF2D,EAAYF,EAAU,QAAUC,EAAK,IACrCE,EAAiBD,EAAYD,EAAK,OAAU,IAC5CE,EAAgB3D,EAAW2D,EAAgB,IAAMA,IAEjDD,EAAYF,EAAU,QAAUC,EAAK,KACrCE,EAAiBD,EAAYD,EAAK,MAAS,IAC3CE,EAAgB3D,EAAW,IAAM2D,EAAgBA,GAEnDC,IAAchE,EAAMD,GAAO,IAAOgE,EAAgBhE,MAE9CkE,UAKAnE,IAAU,IAAQI,EAAO,CAAC,IAAMA,EAAO,CAAC,EACtC8D,GAAY9D,EAAO,CAAC,EACf,EAEA,GAMT+D,GAAU/D,EAAO,YACXA,CAAM,EAAE,KAAM,CAAAgE,GAAGC,KAAM,KAAK,IAAIH,GAAYE,EAAC,EAAI,KAAK,IAAIF,GAAYG,EAAC,GAAG,CAAC,GAG5EF,IAUA,SAAAG,GAAeR,EAAS,CAGzB,MAAAC,EAAOH,KAET,IAAAI,EAAY,EACZC,EAAgB,EAChBC,GAAY,EACZ7D,GACF2D,EAAYF,EAAU,QAAUC,EAAK,IACrCE,EAAiBD,EAAYD,EAAK,OAAU,IAC5CE,EAAgB3D,EAAW2D,EAAgB,IAAMA,IAEjDD,EAAYF,EAAU,QAAUC,EAAK,KACrCE,EAAiBD,EAAYD,EAAK,MAAS,IAC3CE,EAAgB3D,EAAW,IAAM2D,EAAgBA,GAEnDC,IAAchE,EAAMD,GAAO,IAAOgE,EAAgBhE,EAElDoB,GAAW8B,EAAce,EAAS,WAS3B7C,GAAWc,EAAOoC,EAAK,CAG9B,OAAAA,EAAQC,EAAiBD,CAAK,EAElB,OAAApC,EAAU,MACpBA,EAAQgB,GAGNnD,IAGEmC,IAAU,GAAKoC,EAAQnE,EAAO,CAAC,EAC7BoC,MACFpC,EAAO,CAAC,EAAImE,EAAKnE,CAAA,EAEjBmE,EAAQnE,EAAO,CAAC,EAET+B,IAAU,GAAKoC,EAAQnE,EAAO,CAAC,IACpCoC,MACFpC,EAAO,CAAC,EAAImE,EAAKnE,CAAA,EAEjBmE,EAAQnE,EAAO,CAAC,IAMlBA,EAAO+B,CAAK,IAAMoC,OACpBnE,EAAO+B,CAAK,EAAIoC,EAAKnE,CAAA,EAKnBiD,IAAkBkB,IACpBE,KACApB,EAAgBkB,GAEXA,EAQA,SAAAG,GAAWtE,EAAM,CACpB,OAAAJ,IAAU,MACL,EAEAI,EAAO,CAAC,EASV,SAAAuE,GAASvE,EAAM,CAClB,OAAAJ,IAAU,MACL,EACEA,IAAU,MACZ,IAAMI,EAAO,CAAC,EAEd,IAAMA,EAAO,CAAC,EAShB,SAAAwE,GAAiBtC,EAAC,CACrBY,KACF1B,EAAA,GAAAN,EAAQ,EAAK,EACb8B,GAAkB,GAClBxB,EAAA,GAAAyB,GAAgB,EAAK,GAShB,SAAA4B,GAAkBvC,EAAC,CACpB9B,IACJgB,EAAA,GAAA2B,EAAehB,GAAMG,EAAE,MAAM,GAC7Bd,EAAA,GAAAN,EAAQ,EAAI,GASP,SAAA4D,GAAcxC,EAAC,KAChB9B,EAAQ,CACN,MAAAuE,EAAS5C,GAAMG,EAAE,MAAM,EACzB,IAAA0C,EAAO1C,EAAE,SAAWA,EAAE,SAAWA,EAAE,SAAWnC,EAAO,GAAKA,EAC1D8E,EAAU,GAEN,OAAA3C,EAAE,IAAG,KACN,WACH0C,GAAQ,OACL,iBACA,UACH3D,GAAW0D,EAAQ3E,EAAO2E,CAAM,EAAIC,CAAI,EACxCC,EAAU,aAEP,SACHD,GAAQ,OACL,gBACA,YACH3D,GAAW0D,EAAQ3E,EAAO2E,CAAM,EAAIC,CAAI,EACxCC,EAAU,aAEP,OACH5D,GAAW0D,EAAQ9E,CAAG,EACtBgF,EAAU,aAEP,MACH5D,GAAW0D,EAAQ7E,CAAG,EACtB+E,EAAU,SAGVA,IACF3C,EAAE,eAAc,EAChBA,EAAE,gBAAe,IAUd,SAAA4C,GAAoB5C,EAAC,KACtB9B,EAAQ,OACN4B,EAAKE,EAAE,OACPwB,EAAYzB,GAAiBC,CAAC,EAEpCd,EAAA,GAAAN,EAAQ,EAAI,EACZ8B,GAAkB,GAClBxB,EAAA,GAAAyB,GAAgB,EAAI,OACpBE,EAAeU,GAAiBC,CAAS,GAGzCV,GAAaC,EAAgBmB,EAAiBpE,EAAO+C,CAAY,GACjEgC,KAII7C,EAAE,OAAS,eAAiBF,EAAG,QAAQ,SAAS,GAClDkC,GAAeR,CAAS,GAUrB,SAAAsB,GAAkB9C,EAAC,CAEtBA,EAAE,OAAS,YACb+C,KAEF7D,EAAA,GAAAyB,GAAgB,EAAK,EAQd,SAAAqC,GAAkBhD,EAAC,CAC1BY,GAAiB,GACbhC,GAASoB,EAAE,SAAWC,GAAW,CAAAA,EAAO,SAASD,EAAE,MAAM,GAC3Dd,EAAA,GAAAN,EAAQ,EAAK,EASR,SAAAqE,GAAajD,EAAC,CACf9B,GACAwC,IACFsB,GAAejC,GAAiBC,CAAC,GAW9B,SAAAkD,GAAYlD,EAAC,KACd9B,EAAQ,OACN4B,EAAKE,EAAE,OAITU,MACEZ,IAAOG,GAAUA,EAAO,SAASH,CAAE,KACrCZ,EAAA,GAAAN,EAAQ,EAAI,EAGP,CAAAqC,GAAenB,CAAE,IAAMA,EAAG,QAAQ,SAAS,GAC9CkC,GAAejC,GAAiBC,CAAC,IAKrC+C,MAGJrC,GAAkB,GAClBxB,EAAA,GAAAyB,GAAgB,EAAK,EAQd,SAAAwC,GAAanD,EAAC,CACrBU,GAAkB,GAClBxB,EAAA,GAAAyB,GAAgB,EAAK,EAGd,SAAAyC,GAAYpD,EAAC,CACd9B,IACA8B,EAAE,SAAWC,GAAUA,EAAO,SAASD,EAAE,MAAM,KACjDY,GAAiB,aAKdiC,IAAM,EACZ3E,GAAYjC,GAAS,QAAO,CAC3B,aAAA4E,EACA,MAAOC,GACP,OAAQhD,EAAO,IAAKa,GAAMuD,EAAiBvD,CAAC,cAIvCoE,IAAK,EACX7E,GAAYjC,GAAS,OAAM,CAC1B,aAAA4E,EACY,WAAAC,GACZ,MAAOhD,EAAO+C,CAAY,EAC1B,OAAQ/C,EAAO,IAAKa,GAAMuD,EAAiBvD,CAAC,cAIvCwD,IAAO,EACbjE,GAAYjC,GAAS,SAAQ,CAC5B,aAAA4E,EACY,WAAAC,GACZ,cAAa,OACJC,EAAkB,IAAcD,GAAaC,EACtD,MAAOjD,EAAO+C,CAAY,EAC1B,OAAQ/C,EAAO,IAAKa,GAAMuD,EAAiBvD,CAAC,gDAiNrCsB,EAAM9D,k1BAjqBhB+C,EAAA,GAAEmE,EAAU,SAAapE,EAAG,QAEpBA,GAAOtB,EAAMA,EAAMsB,GAAOrB,EAAMA,EAAMqB,2CAS9CC,EAAA,GAAEgD,EAAgB,SAAajD,EAAG,CAE7B,GAAAA,GAAOtB,SACFA,EACE,GAAAsB,GAAOrB,SACTA,EAML,IAAA0F,GAAarE,EAAMtB,GAAOE,EAC1B0F,EAAUtE,EAAMqE,EAChB,YAAK,IAAIA,CAAS,EAAI,GAAKzF,IAC7B0F,GAAWD,EAAY,EAAIzF,GAAQA,GAGrC0F,EAAUF,EAAWE,CAAO,EAKrB,WAAWA,EAAQ,QAAQhD,EAAS,0CAlD5CrB,EAAA,GAAEJ,EAAS,SAAaG,EAAG,KACtBuE,GAASvE,EAAMtB,IAAQC,EAAMD,GAAQ,IACrC,aAAM6F,CAAI,GAAKA,GAAQ,EAClB,EACEA,GAAQ,IACV,IAEA,WAAWA,EAAK,QAAQjD,EAAS,mDArCpC,MAAM,QAASzC,CAAM,IACzBoB,EAAA,EAAApB,EAAW,EAAAF,EAAMD,GAAO,CAAC,GACzB,QAAQ,MAAO,wGAAwG,OAIzHG,EAASuD,GAAUvD,EAAO,IAAKa,GAAMuD,EAAiBvD,CAAC,KAKlD8B,IAAgB3C,EAAO,OAG1B2F,EAAAvE,EAAA,GAAA8B,EAAkB0C,GAAO5F,EAAO,IAAKa,GAAMG,EAAUH,CAAC,GAAI6B,EAAY,IAItEQ,EAAgB,IAAIlD,EAAO,IAAKa,GAAMG,EAAUH,CAAC,SAGnD8B,EAAc3C,EAAO,MAAM,sBAkE5BoB,EAAA,GAAEL,EAAmBd,EAAWC,EAAW,MAAQ,SAAWA,EAAW,QAAU,MAAM,qBACzFkB,EAAA,GAAEyE,EAAiB5F,EAAWC,EAAW,SAAW,MAAQA,EAAW,OAAS,OAAO,ogBC+I9EvD,EAAgB,YAChB,oBAHAA,EAAK,sHAaT,IAAAA,MAAO,sBAIIA,EAAK,YANVA,EAAM,sBAANA,EAAM,4GAKPA,EAAY,KAKnB,IAAA2B,EAAA3B,EAAS,aAAUA,OAAQ,UAAQ6C,GAAA7C,CAAA,yIAfxCC,EAaKC,EAAA8C,EAAA5C,CAAA,0FAjBMJ,EAAgB,qFAWpBK,EAAA,OAAA8I,EAAA,IAAAnJ,MAAO,mCAIIA,EAAK,wCANVA,EAAM,4BAUdA,EAAS,aAAUA,OAAQ,gWApD3B,OAAAA,OAAW,aAAY,EAmBlBA,OAAW,SAAQ,mZAqCtB,EAAC,IACD,IAAG,KACF,UAJOA,EAAW,wBAAXA,EAAW,sEAKbA,EAAa,kGALXA,EAAW,qeA/BfA,EAAW,yTArBfA,EAAS,wGADfC,EAiBKC,EAAA8C,EAAA5C,CAAA,4kBAP6BJ,EAAM,6TARNA,EAAI,mOAYlCoJ,EAAApJ,MAAG,8BAA8B,kJAHlCC,EAEMC,EAAA6E,EAAA3E,CAAA,4BACLC,EAAA,SAAA+I,OAAApJ,MAAG,8BAA8B,OAAAM,EAAA+I,EAAAD,CAAA,qDAPjCE,EAAAtJ,MAAG,sBAAsB,6LAJ1BC,EAGMC,EAAAqJ,EAAAnJ,CAAA,4BACLC,EAAA,SAAAiJ,OAAAtJ,MAAG,sBAAsB,OAAAM,EAAAkJ,EAAAF,CAAA,mGAbxBG,GACC,MAAAzJ,EAAW,eAAYA,OAAU,WACjCA,EAAK,IAAIA,EAAE,IAAC,aAAa,iDAE5BA,EAAK,KAAK,MAAQA,EAAS,gLAHxBK,EAAA,QAAAqJ,EAAA,MAAA1J,EAAW,eAAYA,OAAU,0BACjCA,EAAK,IAAIA,EAAE,IAAC,aAAa,6QArN1B,MAAA2J,GAAmB,IACnBC,GAAmB,GAkChB,SAAAC,GAAiBC,EAAU,YACxB,QAAO,CAAEC,EAASC,IAAM,CAC9B,IAAAC,MAAa,WACjBA,EAAO,QAAUD,EACjBC,EAAO,OAAM,IAASF,EAAQE,EAAO,MAAgB,EACrDA,EAAO,cAAcH,CAAI,iFA9DhB,OAAAtC,EAA+C,IAAI,EAAAtG,GACnD,MAAAgJ,CAAa,EAAAhJ,EACb,YAAAiJ,EAAa,EAAI,EAAAjJ,EACjB,MAAAkJ,EAAO,EAAE,EAAAlJ,GACT,OAAAmJ,CAAwC,EAAAnJ,EACxC,SAAAoJ,EAAU,EAAK,EAAApJ,EACf,WAAAqJ,EAAY,EAAK,EAAArJ,EACjB,UAAAC,EAAW,EAAK,EAAAD,EAChB,kBAAAsJ,EAAmB,EAAI,EAAAtJ,EAK9BuJ,EAAY,GACZC,EACAC,EAAO,GACPC,EACAC,EAAc,GACdC,EAAuC,GACvCC,EACAC,EAAS,GACT5J,EAAW,CAAsB,EAAG,GAAG,EAGvC6J,EAAY,GACZC,WAKKC,IAAW,CACnBD,EAAe,eACP,sBAA2B,sGAC3B,sBAAuC,OAI5CX,GACHY,KAGK,MAAA3J,EAAWC,KAyBX2J,EAAa,MAClBC,EACAC,IAA6C,CAEzC,IAAAC,OAAkB,KAAKF,EAAS,MAAM,WAAW,OACrD7D,EAAK,CACJ,KAAI,MAAQqC,GAAiB0B,EAAW,EACxC,KAAM,cAEP/J,EAAS8J,EAAO9D,CAAK,kBAGPgE,IAAa,KACvBC,MAGHA,EAAM,MAAS,UAAU,aAAa,aAAY,CAAG,MAAO,EAAI,SACxDC,GACH,cAAU,aAAY,CAC1BlK,EAAS,QAASmK,EAAG,yBAAyB,UAG3C,GAAAD,aAAe,cAAgBA,EAAI,MAAQ,kBAAiB,CAC/DlK,EAAS,QAASmK,EAAG,8BAA8B,gBAG9CD,EAGH,GAAAD,GAAU,SAEVlB,EAAS,QACH,cAAAqB,EAAe,SAAAC,EAAQ,EAAM,SAAAC,WAAmB,QAAQ,IAChEZ,CAAe,EAGV,MAAAW,SAAeC,GAAO,GAE5BpB,MAAekB,EAAcH,EAAU,UAAU,WAAW,GAE5Df,EAAS,iBAAiB,gBAAiBqB,CAAY,OAEvDrB,EAAQ,IAAO,cAAce,CAAM,EAEnCf,EAAS,iBAAiB,gBAAkBY,GAAK,CAChDL,EAAa,KAAKK,EAAM,IAAI,IAG7BZ,EAAS,iBAAiB,OAAM,UAC/BjG,EAAA,EAAAgG,EAAY,EAAK,QACXW,EAAcH,EAAc,QAAQ,QACpCG,EAAcH,EAAc,gBAAgB,EAClDA,EAAY,KAIdD,EAAS,IAGK,eAAAe,EAAaT,EAAiB,CACxC,IAAAU,EAAe,MAAAV,EAAM,KAAK,YAAW,EACrCW,GAAO,IAAO,WAAWD,CAAM,KAC9BpB,SACJA,EAAM,IAAO,WAAWoB,EAAO,MAAM,EAAGpC,EAAgB,IACxDqC,OAAc,WAAWD,EAAO,MAAMpC,EAAgB,IAEnDU,EACHO,EAAe,KAAKoB,EAAO,OAEvB,IAAAC,IAAatB,CAAM,EAAE,OAAOC,GAAiBoB,EAAO,GACxDb,EAAcc,GAAW,QAAQ,OACjCrB,EAAc,oBAaDsB,IAAM,CACf,cAAU,aAAY,CAC1B3K,EAAS,QAASmK,EAAG,yBAAyB,UAG/ClH,EAAA,EAAAgG,EAAY,EAAI,EAChBjJ,EAAS,iBAAiB,EACrBwJ,SAAcQ,KACnB/G,EAAA,GAAAmG,EAAS,MAAS,EACdL,EACHG,EAAS,MAAMf,EAAgB,EAE/Be,EAAS,MAAK,EAIhB0B,GAAS,KACJ1B,GAAYA,EAAS,QAAU,YAClCA,EAAS,KAAI,aAIN2B,IAAI,CACZ3B,EAAS,KAAI,EACTH,IACH9F,EAAA,EAAAgG,EAAY,EAAK,EACjBjJ,EAAS,gBAAgB,EACrB8I,GACH7F,EAAA,GAAAqG,EAAuC,EAAI,YAKrCwB,IAAK,CACb9K,EAAS,SAAU,IAAI,EACvBA,EAAS,OAAO,EAChBiD,EAAA,EAAAkG,EAAO,EAAE,EACTlG,EAAA,EAAA+C,EAAQ,IAAI,WAGJ+E,GAAa,CACrB,OAAM,CAAI,OAAAlJ,CAAM,IAIXmE,IAELhG,EAAS,SAAQ,CAChB,KAAMgG,EAAM,KACZ,KAAA4C,EACA,SAAU/G,EAAO,CAAC,EAClB,SAAUA,EAAO,CAAC,IAGnB7B,EAAS,MAAM,GAGP,SAAAgL,IACR,OAAAC,GAAM,CASNhI,EAAA,EAAA+C,EAAQiF,CAAM,EACdjL,EAAS,SAAY,MAAMiL,EAAO,KAAM,KAAMA,EAAO,IAAI,GACzDjL,EAAS,SAAUiL,CAAM,WAGjBC,IAAY,CACpBlL,EAAS,MAAM,EACfA,EAAS,KAAK,EAGJ,aAAAmL,EAAW,EAAK,EAAAzL,2BA2CV,MAAA0L,GAAA,IAAAnI,EAAA,EAAAkG,EAAO,MAAM,iBAUjBI,EAAMvD,0FAYJpG,EAAWoG,6bAvJnBsD,GAAwCR,IAAY,KAC1D7F,EAAA,GAAAqG,EAAuC,EAAK,EACxCF,GAAUC,GAAc,CACvB,IAAAqB,EAA2B,CAAAtB,CAAM,EAAE,OAAOC,CAAc,OAC5DA,EAAc,IACdO,EAAcc,EAAW,QAAQ,mBAkFhC1K,EAAS,OAAQmL,CAAQ,gfC/KT3M,EAAc,mIAIzBA,EAAM,6hBAJKA,EAAc,2GAIzBA,EAAM,wZAdL,QAAAA,EAAU,WAAQA,OAAW,SAAW,SAAW,oBAC/CA,EAAQ,IAAG,QAAU,eACzB,oMAFAK,EAAA,QAAAwM,EAAA,QAAA7M,EAAU,WAAQA,OAAW,SAAW,SAAW,qCAC/CA,EAAQ,IAAG,QAAU,gVAlDvB,YAAA8M,EAAU,EAAE,EAAA5L,GACZ,aAAA6L,EAAY,IAAA7L,EACZ,SAAA8L,EAAU,EAAI,EAAA9L,EACd,OAAAsG,EAAkC,IAAI,EAAAtG,GACtC,KAAAkJ,CAAY,EAAAlJ,GACZ,OAAAmJ,CAA+B,EAAAnJ,GAC/B,MAAAgJ,CAAa,EAAAhJ,GACb,KAAA+L,CAAY,EAAA/L,GACZ,WAAAiJ,CAAmB,EAAAjJ,GACnB,QAAAoJ,CAAgB,EAAApJ,GAChB,UAAAqJ,CAAkB,EAAArJ,GAClB,SAAAgM,CAAuB,EAAAhM,EACvB,WAAAiM,EAAY,EAAI,EAAAjM,EAChB,OAAAkM,EAAuB,IAAI,EAAAlM,EAC3B,WAAAmM,EAAgC,MAAS,EAAAnM,GACzC,eAAAoM,CAA6B,EAAApM,EAC7B,UAAAC,EAAW,EAAK,EAAAD,EAChB,kBAAAsJ,EAAmB,EAAI,EAAAtJ,GACvB,OAAAqM,CAaT,EAAArM,EAEEsM,EAAsC,KAEtCC,EAUAd,YAmBW,OAAAF,CAAM,IAAAhI,EAAA,EAAQ+C,EAAQiF,CAAM,OAC5B,OAAAA,KAAM,CACnBhI,EAAA,EAAA+C,EAAQiF,CAAM,EACdc,EAAO,SAAS,SAAU/F,CAAK,OAEpB,OAAAiF,CAAM,IAAAhI,EAAA,GAAQkI,EAAWF,CAAM,QAO5Bc,EAAO,SAAS,MAAM,SACtBA,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,SACvBA,EAAO,SAAS,KAAK,SACTA,EAAO,SAAS,iBAAiB,SAClCA,EAAO,SAAS,gBAAgB,SACxCA,EAAO,SAAS,QAAQ,SACzBA,EAAO,SAAS,OAAO,MAC1B,OAAAd,KAAM,CAClBhI,EAAA,EAAA6I,EAAiBA,GAAc,QAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BC,EAAO,SAAS,QAASd,CAAM,4sBApDhChI,EAAA,GAAEgJ,EAASC,GAAelG,EAAOyF,EAAMC,CAAQ,0BAG3C,KAAK,UAAU1F,CAAK,IAAM,KAAK,UAAUgG,CAAS,IACrD/I,EAAA,GAAA+I,EAAYhG,CAAK,EACjB+F,EAAO,SAAS,QAAQ", "names": ["t_value", "ctx", "insert", "target", "p", "anchor", "dirty", "set_data", "t", "attr", "audio", "audio_src_value", "audio_data_testid_value", "listen", "handle_promise", "promise", "resolve_wasm_src", "info", "src", "$$props", "autoplay", "crop_values", "controls", "preload", "node", "dispatch", "createEventDispatcher", "$$value", "if_block", "create_if_block_10", "toggle_class", "span", "is_function", "create_if_block_12", "create_if_block_11", "i", "create_if_block_6", "span_style_value", "create_if_block_8", "create_if_block_7", "create_if_block_1", "create_if_block_3", "create_if_block_2", "if_block0", "create_if_block_9", "if_block1", "create_if_block_4", "if_block2", "create_if_block", "div", "range", "min", "max", "step", "values", "vertical", "reversed", "hoverable", "disabled", "pipstep", "all", "first", "last", "rest", "prefix", "suffix", "formatter", "v", "focus", "orientationStart", "percentOf", "moveHandle", "labelClick", "val", "$$invalidate", "pipStep", "pipCount", "pipVal", "isSelected", "inRange", "span1", "span1_aria_valuemin_value", "span1_aria_valuemax_value", "append", "span0", "index", "el", "normalisedClient", "e", "slider", "pushy", "float", "pips", "id", "handleFormatter", "precision", "springValues", "valueLength", "handleActivated", "handlePressed", "keyboardActive", "activeHandle", "startValue", "previousValue", "springPositions", "targetIsHandle", "handles", "is<PERSON><PERSON>le", "<PERSON><PERSON><PERSON><PERSON>", "trimRange", "getSliderDimensions", "getClosestHandle", "clientPos", "dims", "handlePos", "handlePercent", "handleVal", "closest", "a", "b", "handleInteract", "value", "alignValueToStep", "eChange", "rangeStart", "rangeEnd", "slide<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slider<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slider<PERSON><PERSON>down", "handle", "jump", "prevent", "sliderInteractStart", "eStart", "sliderInteractEnd", "eStop", "bodyInteractStart", "bodyInteract", "bodyMouseUp", "bodyTouchEnd", "bodyKeyDown", "clampValue", "remainder", "aligned", "perc", "$$subscribe_springPositions", "spring", "orientationEnd", "audio_changes", "t1_value", "t1", "t2_value", "span2", "t2", "Music", "blocklabel_changes", "STREAM_TIMESLICE", "NUM_HEADER_BYTES", "blob_to_data_url", "blob", "fulfill", "reject", "reader", "label", "show_label", "name", "source", "pending", "streaming", "show_edit_button", "recording", "recorder", "mode", "header", "pending_stream", "submit_pending_stream_on_pending_end", "player", "inited", "audio_chunks", "module_promises", "get_modules", "dispatch_blob", "blobs", "event", "_audio_blob", "prepare_audio", "stream", "err", "$_", "MediaRecorder", "register", "connect", "handle_chunk", "buffer", "payload", "blobParts", "record", "onDestroy", "stop", "clear", "handle_change", "handle_load", "detail", "handle_ended", "dragging", "edit_handler", "block_changes", "elem_id", "elem_classes", "visible", "root", "root_url", "container", "scale", "min_width", "loading_status", "gradio", "old_value", "_value", "normalise_file"], "sources": ["../../../../js/audio/shared/Audio.svelte", "../../../../node_modules/.pnpm/svelte-range-slider-pips@2.0.2/node_modules/svelte-range-slider-pips/src/RangePips.svelte", "../../../../node_modules/.pnpm/svelte-range-slider-pips@2.0.2/node_modules/svelte-range-slider-pips/src/RangeSlider.svelte", "../../../../js/audio/interactive/Audio.svelte", "../../../../js/audio/interactive/InteractiveAudio.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { HTMLAudioAttributes } from \"svelte/elements\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { loaded, type LoadedParams } from \"../shared/utils\";\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let src: HTMLAudioAttributes[\"src\"] = undefined;\n\n\texport let autoplay: LoadedParams[\"autoplay\"] = undefined;\n\texport let crop_values: LoadedParams[\"crop_values\"] = undefined;\n\texport let controls: HTMLAudioAttributes[\"controls\"] = undefined;\n\texport let preload: HTMLAudioAttributes[\"preload\"] = undefined;\n\n\texport let node: HTMLAudioElement | undefined = undefined;\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n{#await resolve_wasm_src(src) then resolved_src}\n\t<audio\n\t\tsrc={resolved_src}\n\t\t{controls}\n\t\t{preload}\n\t\ton:play={dispatch.bind(null, \"play\")}\n\t\ton:pause={dispatch.bind(null, \"pause\")}\n\t\ton:ended={dispatch.bind(null, \"ended\")}\n\t\tbind:this={node}\n\t\tuse:loaded={{ autoplay, crop_values }}\n\t\tdata-testid={$$props[\"data-testid\"]}\n\t/>\n{:catch error}\n\t<p style=\"color: red;\">{error.message}</p>\n{/await}\n\n\n<style>\n\taudio {\n\t\tpadding: var(--size-2);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-14);\n\t}\n</style>", "<script>\n\n  // range slider props\n  export let range = false;\n  export let min = 0;\n  export let max = 100;\n  export let step = 1;\n  export let values = [(max + min) / 2];\n  export let vertical = false;\n  export let reversed = false;\n  export let hoverable = true;\n  export let disabled = false;\n\n  // range pips / values props\n  export let pipstep = undefined;\n  export let all = true;\n  export let first = undefined;\n  export let last = undefined;\n  export let rest = undefined;\n\n  // formatting props\n  export let prefix = \"\";\n  export let suffix = \"\";\n  export let formatter = (v,i) => v;\n\n  // stylistic props\n  export let focus = undefined;\n  export let orientationStart = undefined;\n\n  // methods\n  export let percentOf = undefined;\n  export let moveHandle = undefined;\n\n  $: pipStep = pipstep || ((max - min) / step >= ( vertical ? 50 : 100 ) ? (max - min) / ( vertical ? 10 : 20 ) : 1);\n\n  $: pipCount = parseInt((max - min) / (step * pipStep), 10);\n\n  $: pipVal = function(val) {\n    return min + val * step * pipStep;\n  };\n\n  $: isSelected = function(val) {\n    return values.some(v => v === val);\n  };\n\n  $: inRange = function(val) {\n    if (range === \"min\") {\n      return values[0] > val;\n    } else if (range === \"max\") {\n      return values[0] < val;\n    } else if (range) {\n      return values[0] < val && values[1] > val;\n    }\n  };\n\n  function labelClick(val) {\n    moveHandle( undefined, val );\n  }\n</script>\n\n<style>\n  :global(.rangeSlider) {\n    --pip: var(--range-pip, lightslategray);\n    --pip-text: var(--range-pip-text, var(--pip));\n    --pip-active: var(--range-pip-active, darkslategrey);\n    --pip-active-text: var(--range-pip-active-text, var(--pip-active));\n    --pip-hover: var(--range-pip-hover, darkslategrey);\n    --pip-hover-text: var(--range-pip-hover-text, var(--pip-hover));\n    --pip-in-range: var(--range-pip-in-range, var(--pip-active));\n    --pip-in-range-text: var(--range-pip-in-range-text, var(--pip-active-text));\n  }\n  :global(.rangePips) {\n    position: absolute;\n    height: 1em;\n    left: 0;\n    right: 0;\n    bottom: -1em;\n  }\n  :global(.rangePips.vertical) {\n    height: auto;\n    width: 1em;\n    left: 100%;\n    right: auto;\n    top: 0;\n    bottom: 0;\n  }\n  :global(.rangePips .pip) {\n    height: 0.4em;\n    position: absolute;\n    top: 0.25em;\n    width: 1px;\n    white-space: nowrap;\n  }\n  :global(.rangePips.vertical .pip) {\n    height: 1px;\n    width: 0.4em;\n    left: 0.25em;\n    top: auto;\n    bottom: auto;\n  }\n  :global(.rangePips .pipVal) {\n    position: absolute;\n    top: 0.4em;\n    transform: translate(-50%, 25%);\n  }\n  :global(.rangePips.vertical .pipVal) {\n    position: absolute;\n    top: 0;\n    left: 0.4em;\n    transform: translate(25%, -50%);\n  }\n  :global(.rangePips .pip) {\n    transition: all 0.15s ease;\n  }\n  :global(.rangePips .pipVal) {\n    transition: all 0.15s ease, font-weight 0s linear;\n  }\n  :global(.rangePips .pip) {\n    color: lightslategray;\n    color: var(--pip-text);\n    background-color: lightslategray;\n    background-color: var(--pip);\n  }\n  :global(.rangePips .pip.selected) {\n    color: darkslategrey;\n    color: var(--pip-active-text);\n    background-color: darkslategrey;\n    background-color: var(--pip-active);\n  }\n  :global(.rangePips.hoverable:not(.disabled) .pip:hover) {\n    color: darkslategrey;\n    color: var(--pip-hover-text);\n    background-color: darkslategrey;\n    background-color: var(--pip-hover);\n  }\n  :global(.rangePips .pip.in-range) {\n    color: darkslategrey;\n    color: var(--pip-in-range-text);\n    background-color: darkslategrey;\n    background-color: var(--pip-in-range);\n  }\n  :global(.rangePips .pip.selected) {\n    height: 0.75em;\n  }\n  :global(.rangePips.vertical .pip.selected) {\n    height: 1px;\n    width: 0.75em;\n  }\n  :global(.rangePips .pip.selected .pipVal) {\n    font-weight: bold;\n    top: 0.75em;\n  }\n  :global(.rangePips.vertical .pip.selected .pipVal) {\n    top: 0;\n    left: 0.75em;\n  }\n  :global(.rangePips.hoverable:not(.disabled) .pip:not(.selected):hover) {\n    transition: none;\n  }\n  :global(.rangePips.hoverable:not(.disabled) .pip:not(.selected):hover .pipVal) {\n    transition: none;\n    font-weight: bold;\n  }\n</style>\n\n<div \n  class=\"rangePips\" \n  class:disabled\n  class:hoverable \n  class:vertical \n  class:reversed \n  class:focus \n>\n  {#if ( all && first !== false ) || first }\n    <span\n      class=\"pip first\"\n      class:selected={isSelected(min)}\n      class:in-range={inRange(min)}\n      style=\"{orientationStart}: 0%;\"\n      on:click={labelClick(min)}\n      on:touchend|preventDefault={labelClick(min)}\n    >\n      {#if all === 'label' || first === 'label'}\n        <span class=\"pipVal\">\n          {#if prefix}<span class=\"pipVal-prefix\">{prefix}</span>{/if}{formatter(min,0,0)}{#if suffix}<span class=\"pipVal-suffix\">{suffix}</span>{/if}\n        </span>\n      {/if}\n    </span>\n  {/if}\n\n  {#if ( all && rest !== false ) || rest}\n    {#each Array(pipCount + 1) as _, i}\n      {#if pipVal(i) !== min && pipVal(i) !== max}\n        <span\n          class=\"pip\"\n          class:selected={isSelected(pipVal(i))}\n          class:in-range={inRange(pipVal(i))}\n          style=\"{orientationStart}: {percentOf(pipVal(i))}%;\"\n          on:click={labelClick(pipVal(i))}\n          on:touchend|preventDefault={labelClick(pipVal(i))}\n        >\n          {#if all === 'label' || rest === 'label'}\n            <span class=\"pipVal\">\n              {#if prefix}<span class=\"pipVal-prefix\">{prefix}</span>{/if}{formatter(pipVal(i),i,percentOf(pipVal(i)))}{#if suffix}<span class=\"pipVal-suffix\">{suffix}</span>{/if}\n            </span>\n          {/if}\n        </span>\n      {/if}\n    {/each}\n  {/if}\n\n  {#if ( all && last !== false ) || last}\n    <span\n      class=\"pip last\"\n      class:selected={isSelected(max)}\n      class:in-range={inRange(max)}\n      style=\"{orientationStart}: 100%;\"\n      on:click={labelClick(max)}\n      on:touchend|preventDefault={labelClick(max)}\n    >\n      {#if all === 'label' || last === 'label'}\n        <span class=\"pipVal\">\n          {#if prefix}<span class=\"pipVal-prefix\">{prefix}</span>{/if}{formatter(max,pipCount,100)}{#if suffix}<span class=\"pipVal-suffix\">{suffix}</span>{/if}\n        </span>\n      {/if}\n    </span>\n  {/if}\n  \n</div>\n", "<script>\n  import { spring } from \"svelte/motion\";\n  import { createEventDispatcher } from \"svelte\";\n  import RangePips from \"./RangePips.svelte\";\n\n  // dom references\n  export let slider;\n\n  // range slider props\n  export let range = false;\n  export let pushy = false;\n  export let min = 0;\n  export let max = 100;\n  export let step = 1;\n  export let values = [(max + min) / 2];\n  export let vertical = false;\n  export let float = false;\n  export let reversed = false;\n  export let hoverable = true;\n  export let disabled = false;\n\n  // range pips / values props\n  export let pips = false;\n  export let pipstep = undefined;\n  export let all = undefined;\n  export let first = undefined;\n  export let last = undefined;\n  export let rest = undefined;\n\n  // formatting props\n  export let id = undefined;\n  export let prefix = \"\";\n  export let suffix = \"\";\n  export let formatter = (v,i,p) => v;\n  export let handleFormatter = formatter;\n\n  // stylistic props\n  export let precision = 2;\n  export let springValues = { stiffness: 0.15, damping: 0.4 };\n\n  // prepare dispatched events\n  const dispatch = createEventDispatcher();\n\n  // state management\n  let valueLength = 0;\n  let focus = false;\n  let handleActivated = false;\n  let handlePressed = false;\n  let keyboardActive = false;\n  let activeHandle = values.length - 1;\n  let startValue;\n  let previousValue;\n\n  // copy the initial values in to a spring function which\n  // will update every time the values array is modified\n\n  let springPositions;\n\n  $: {\n\n    // check that \"values\" is an array, or set it as array\n    // to prevent any errors in springs, or range trimming\n    if ( !Array.isArray( values ) ) {\n      values = [(max + min) / 2];\n      console.error( \"'values' prop should be an Array (https://github.com/simeydotme/svelte-range-slider-pips#slider-props)\" );\n    }\n    // trim the range so it remains as a min/max (only 2 handles)\n    // and also align the handles to the steps\n    values = trimRange(values.map((v) => alignValueToStep(v)));\n\n    // check if the valueLength (length of values[]) has changed,\n    // because if so we need to re-seed the spring function with the\n    // new values array.\n    if ( valueLength !== values.length ) {\n      // set the initial spring values when the slider initialises,\n      // or when values array length has changed\n      springPositions = spring(values.map((v) => percentOf(v)), springValues );\n    } else {\n      // update the value of the spring function for animated handles\n      // whenever the values has updated\n      springPositions.set(values.map((v) => percentOf(v)));\n    }\n    // set the valueLength for the next check\n    valueLength = values.length;\n  };\n\n  /**\n   * take in a value, and then calculate that value's percentage\n   * of the overall range (min-max);\n   * @param {number} val the value we're getting percent for\n   * @return {number} the percentage value\n   **/\n  $: percentOf = function (val) {\n    let perc = ((val - min) / (max - min)) * 100;\n    if (isNaN(perc) || perc <= 0) {\n      return 0;\n    } else if (perc >= 100) {\n      return 100;\n    } else {\n      return parseFloat(perc.toFixed(precision));\n    }\n  };\n\n  /**\n   * clamp a value from the range so that it always\n   * falls within the min/max values\n   * @param {number} val the value to clamp\n   * @return {number} the value after it's been clamped\n   **/\n  $: clampValue = function (val) {\n    // return the min/max if outside of that range\n    return val <= min ? min : val >= max ? max : val;\n  };\n\n  /**\n   * align the value with the steps so that it\n   * always sits on the closest (above/below) step\n   * @param {number} val the value to align\n   * @return {number} the value after it's been aligned\n   **/\n  $: alignValueToStep = function (val) {\n    // sanity check for performance\n    if (val <= min) {\n      return min;\n    } else if (val >= max) {\n      return max;\n    }\n\n    // find the middle-point between steps\n    // and see if the value is closer to the\n    // next step, or previous step\n    let remainder = (val - min) % step;\n    let aligned = val - remainder;\n    if (Math.abs(remainder) * 2 >= step) {\n      aligned += remainder > 0 ? step : -step;\n    }\n    // make sure the value is within acceptable limits\n    aligned = clampValue(aligned);\n    // make sure the returned value is set to the precision desired\n    // this is also because javascript often returns weird floats\n    // when dealing with odd numbers and percentages\n\n    return parseFloat(aligned.toFixed(precision));\n  };\n\n  /**\n   * the orientation of the handles/pips based on the\n   * input values of vertical and reversed\n   **/\n  $: orientationStart = vertical ? reversed ? 'top' : 'bottom' : reversed ? 'right' : 'left';\n  $: orientationEnd = vertical ? reversed ? 'bottom' : 'top' : reversed ? 'left' : 'right';\n\n  /**\n   * helper func to get the index of an element in it's DOM container\n   * @param {object} el dom object reference we want the index of\n   * @returns {number} the index of the input element\n   **/\n  function index(el) {\n    if (!el) return -1;\n    var i = 0;\n    while ((el = el.previousElementSibling)) {\n      i++;\n    }\n    return i;\n  }\n\n  /**\n   * normalise a mouse or touch event to return the\n   * client (x/y) object for that event\n   * @param {event} e a mouse/touch event to normalise\n   * @returns {object} normalised event client object (x,y)\n   **/\n  function normalisedClient(e) {\n    if (e.type.includes(\"touch\")) {\n      return e.touches[0];\n    } else {\n      return e;\n    }\n  }\n\n  /**\n   * check if an element is a handle on the slider\n   * @param {object} el dom object reference we want to check\n   * @returns {boolean}\n   **/\n  function targetIsHandle(el) {\n    const handles = slider.querySelectorAll(\".handle\");\n    const isHandle = Array.prototype.includes.call(handles, el);\n    const isChild = Array.prototype.some.call(handles, (e) => e.contains(el));\n    return isHandle || isChild;\n  }\n\n  /**\n   * trim the values array based on whether the property\n   * for 'range' is 'min', 'max', or truthy. This is because we\n   * do not want more than one handle for a min/max range, and we do\n   * not want more than two handles for a true range.\n   * @param {array} values the input values for the rangeSlider\n   * @return {array} the range array for creating a rangeSlider\n   **/\n  function trimRange(values) {\n    if (range === \"min\" || range === \"max\") {\n      return values.slice(0, 1);\n    } else if (range) {\n      return values.slice(0, 2);\n    } else {\n      return values;\n    }\n  }\n\n  /**\n   * helper to return the slider dimensions for finding\n   * the closest handle to user interaction\n   * @return {object} the range slider DOM client rect\n   **/\n  function getSliderDimensions() {\n    return slider.getBoundingClientRect();\n  }\n\n  /**\n   * helper to return closest handle to user interaction\n   * @param {object} clientPos the client{x,y} positions to check against\n   * @return {number} the index of the closest handle to clientPos\n   **/\n  function getClosestHandle(clientPos) {\n    // first make sure we have the latest dimensions\n    // of the slider, as it may have changed size\n    const dims = getSliderDimensions();\n    // calculate the interaction position, percent and value\n    let handlePos = 0;\n    let handlePercent = 0;\n    let handleVal = 0;\n    if (vertical) {\n      handlePos = clientPos.clientY - dims.top;\n      handlePercent = (handlePos / dims.height) * 100;\n      handlePercent = reversed ? handlePercent : 100 - handlePercent;\n    } else {\n      handlePos = clientPos.clientX - dims.left;\n      handlePercent = (handlePos / dims.width) * 100;\n      handlePercent = reversed ? 100 - handlePercent : handlePercent;\n    }\n    handleVal = ((max - min) / 100) * handlePercent + min;\n\n    let closest;\n\n    // if we have a range, and the handles are at the same\n    // position, we want a simple check if the interaction\n    // value is greater than return the second handle\n    if (range === true && values[0] === values[1]) {\n      if (handleVal > values[1]) {\n        return 1;\n      } else {\n        return 0;\n      }\n      // if there are multiple handles, and not a range, then\n      // we sort the handles values, and return the first one closest\n      // to the interaction value\n    } else {\n      closest = values.indexOf(\n        [...values].sort((a, b) => Math.abs(handleVal - a) - Math.abs(handleVal - b))[0]\n      );\n    }\n    return closest;\n  }\n\n  /**\n   * take the interaction position on the slider, convert\n   * it to a value on the range, and then send that value\n   * through to the moveHandle() method to set the active\n   * handle's position\n   * @param {object} clientPos the client{x,y} of the interaction\n   **/\n  function handleInteract(clientPos) {\n    // first make sure we have the latest dimensions\n    // of the slider, as it may have changed size\n    const dims = getSliderDimensions();\n    // calculate the interaction position, percent and value\n    let handlePos = 0;\n    let handlePercent = 0;\n    let handleVal = 0;\n    if (vertical) {\n      handlePos = clientPos.clientY - dims.top;\n      handlePercent = (handlePos / dims.height) * 100;\n      handlePercent = reversed ? handlePercent : 100 - handlePercent;\n    } else {\n      handlePos = clientPos.clientX - dims.left;\n      handlePercent = (handlePos / dims.width) * 100;\n      handlePercent = reversed ? 100 - handlePercent : handlePercent;\n    }\n    handleVal = ((max - min) / 100) * handlePercent + min;\n    // move handle to the value\n    moveHandle(activeHandle, handleVal);\n  }\n\n  /**\n   * move a handle to a specific value, respecting the clamp/align rules\n   * @param {number} index the index of the handle we want to move\n   * @param {number} value the value to move the handle to\n   * @return {number} the value that was moved to (after alignment/clamping)\n   **/\n  function moveHandle(index, value) {\n    // align & clamp the value so we're not doing extra\n    // calculation on an out-of-range value down below\n    value = alignValueToStep(value);\n    // use the active handle if handle index is not provided\n    if ( typeof index === 'undefined' ) {\n      index = activeHandle;\n    }\n    // if this is a range slider perform special checks\n    if (range) {\n      // restrict the handles of a range-slider from\n      // going past one-another unless \"pushy\" is true\n      if (index === 0 && value > values[1]) {\n        if (pushy) {\n          values[1] = value;\n        } else {\n          value = values[1];\n        }\n      } else if (index === 1 && value < values[0]) {\n        if (pushy) {\n          values[0] = value;\n        } else {\n          value = values[0];\n        }\n      }\n    }\n\n    // if the value has changed, update it\n    if (values[index] !== value) {\n      values[index] = value;\n    }\n\n    // fire the change event when the handle moves,\n    // and store the previous value for the next time\n    if (previousValue !== value) {\n      eChange();\n      previousValue = value;\n    }\n    return value;\n  }\n\n  /**\n   * helper to find the beginning range value for use with css style\n   * @param {array} values the input values for the rangeSlider\n   * @return {number} the beginning of the range\n   **/\n  function rangeStart(values) {\n    if (range === \"min\") {\n      return 0;\n    } else {\n      return values[0];\n    }\n  }\n\n  /**\n   * helper to find the ending range value for use with css style\n   * @param {array} values the input values for the rangeSlider\n   * @return {number} the end of the range\n   **/\n  function rangeEnd(values) {\n    if (range === \"max\") {\n      return 0;\n    } else if (range === \"min\") {\n      return 100 - values[0];\n    } else {\n      return 100 - values[1];\n    }\n  }\n\n  /**\n   * when the user has unfocussed (blurred) from the\n   * slider, deactivate all handles\n   * @param {event} e the event from browser\n   **/\n  function sliderBlurHandle(e) {\n    if (keyboardActive) {\n      focus = false;\n      handleActivated = false;\n      handlePressed = false;\n    }\n  }\n\n  /**\n   * when the user focusses the handle of a slider\n   * set it to be active\n   * @param {event} e the event from browser\n   **/\n  function sliderFocusHandle(e) {\n    if ( !disabled ) {\n      activeHandle = index(e.target);\n      focus = true;\n    }\n  }\n\n  /**\n   * handle the keyboard accessible features by checking the\n   * input type, and modfier key then moving handle by appropriate amount\n   * @param {event} e the event from browser\n   **/\n  function sliderKeydown(e) {\n    if ( !disabled ) {\n      const handle = index(e.target);\n      let jump = e.ctrlKey || e.metaKey || e.shiftKey ? step * 10 : step;\n      let prevent = false;\n\n      switch (e.key) {\n        case \"PageDown\":\n          jump *= 10;\n        case \"ArrowRight\":\n        case \"ArrowUp\":\n          moveHandle(handle, values[handle] + jump);\n          prevent = true;\n          break;\n        case \"PageUp\":\n          jump *= 10;\n        case \"ArrowLeft\":\n        case \"ArrowDown\":\n          moveHandle(handle, values[handle] - jump);\n          prevent = true;\n          break;\n        case \"Home\":\n          moveHandle(handle, min);\n          prevent = true;\n          break;\n        case \"End\":\n          moveHandle(handle, max);\n          prevent = true;\n          break;\n      }\n      if (prevent) {\n        e.preventDefault();\n        e.stopPropagation();\n      }\n    }\n  }\n\n  /**\n   * function to run when the user touches\n   * down on the slider element anywhere\n   * @param {event} e the event from browser\n   **/\n  function sliderInteractStart(e) {\n    if ( !disabled ) {\n      const el = e.target;\n      const clientPos = normalisedClient(e);\n      // set the closest handle as active\n      focus = true;\n      handleActivated = true;\n      handlePressed = true;\n      activeHandle = getClosestHandle(clientPos);\n\n      // fire the start event\n      startValue = previousValue = alignValueToStep(values[activeHandle]);\n      eStart();\n\n      // for touch devices we want the handle to instantly\n      // move to the position touched for more responsive feeling\n      if (e.type === \"touchstart\" && !el.matches(\".pipVal\")) {\n        handleInteract(clientPos);\n      }\n    }\n  }\n\n  /**\n   * function to run when the user stops touching\n   * down on the slider element anywhere\n   * @param {event} e the event from browser\n   **/\n  function sliderInteractEnd(e) {\n    // fire the stop event for touch devices\n    if (e.type === \"touchend\") {\n      eStop();\n    }\n    handlePressed = false;\n  }\n\n  /**\n   * unfocus the slider if the user clicked off of\n   * it, somewhere else on the screen\n   * @param {event} e the event from browser\n   **/\n  function bodyInteractStart(e) {\n    keyboardActive = false;\n    if (focus && e.target !== slider && !slider.contains(e.target)) {\n      focus = false;\n    }\n  }\n\n  /**\n   * send the clientX through to handle the interaction\n   * whenever the user moves acros screen while active\n   * @param {event} e the event from browser\n   **/\n  function bodyInteract(e) {\n    if ( !disabled ) {\n      if (handleActivated) {\n        handleInteract(normalisedClient(e));\n      }\n    }\n  }\n\n  /**\n   * if user triggers mouseup on the body while\n   * a handle is active (without moving) then we\n   * trigger an interact event there\n   * @param {event} e the event from browser\n   **/\n  function bodyMouseUp(e) {\n    if ( !disabled ) {\n      const el = e.target;\n      // this only works if a handle is active, which can\n      // only happen if there was sliderInteractStart triggered\n      // on the slider, already\n      if (handleActivated) {\n        if (el === slider || slider.contains(el)) {\n          focus = true;\n          // don't trigger interact if the target is a handle (no need) or\n          // if the target is a label (we want to move to that value from rangePips)\n          if (!targetIsHandle(el) && !el.matches(\".pipVal\")) {\n            handleInteract(normalisedClient(e));\n          }\n        }\n        // fire the stop event for mouse device\n        // when the body is triggered with an active handle\n        eStop();\n      }\n    }\n    handleActivated = false;\n    handlePressed = false;\n  }\n\n  /**\n   * if user triggers touchend on the body then we\n   * defocus the slider completely\n   * @param {event} e the event from browser\n   **/\n  function bodyTouchEnd(e) {\n    handleActivated = false;\n    handlePressed = false;\n  }\n\n  function bodyKeyDown(e) {\n    if ( !disabled ) {\n      if (e.target === slider || slider.contains(e.target)) {\n        keyboardActive = true;\n      }\n    }\n  }\n\n  function eStart() {\n    !disabled && dispatch(\"start\", {\n      activeHandle,\n      value: startValue,\n      values: values.map((v) => alignValueToStep(v)),\n    });\n  }\n\n  function eStop() {\n    !disabled && dispatch(\"stop\", {\n      activeHandle,\n      startValue: startValue,\n      value: values[activeHandle],\n      values: values.map((v) => alignValueToStep(v)),\n    });\n  }\n\n  function eChange() {\n    !disabled && dispatch(\"change\", {\n      activeHandle,\n      startValue: startValue,\n      previousValue:\n        typeof previousValue === \"undefined\" ? startValue : previousValue,\n      value: values[activeHandle],\n      values: values.map((v) => alignValueToStep(v)),\n    });\n  }\n</script>\n\n<style>\n  :global(.rangeSlider) {\n    --slider: var(--range-slider, #d7dada);\n    --handle-inactive: var(--range-handle-inactive, #99a2a2);\n    --handle: var(--range-handle, #838de7);\n    --handle-focus: var(--range-handle-focus, #4a40d4);\n    --handle-border: var(--range-handle-border, var(--handle));\n    --range-inactive: var(--range-range-inactive, var(--handle-inactive));\n    --range: var(--range-range, var(--handle-focus));\n    --float-inactive: var(--range-float-inactive, var(--handle-inactive));\n    --float: var(--range-float, var(--handle-focus));\n    --float-text: var(--range-float-text, white);\n  }\n  :global(.rangeSlider) {\n    position: relative;\n    border-radius: 100px;\n    height: 0.5em;\n    margin: 1em;\n    transition: opacity 0.2s ease;\n    user-select: none;\n  }\n  :global(.rangeSlider *) {\n    user-select: none;\n  }\n  :global(.rangeSlider.pips) {\n    margin-bottom: 1.8em;\n  }\n  :global(.rangeSlider.pip-labels) {\n    margin-bottom: 2.8em;\n  }\n  :global(.rangeSlider.vertical) {\n    display: inline-block;\n    border-radius: 100px;\n    width: 0.5em;\n    min-height: 200px;\n  }\n  :global(.rangeSlider.vertical.pips) {\n    margin-right: 1.8em;\n    margin-bottom: 1em;\n  }\n  :global(.rangeSlider.vertical.pip-labels) {\n    margin-right: 2.8em;\n    margin-bottom: 1em;\n  }\n  :global(.rangeSlider .rangeHandle) {\n    position: absolute;\n    display: block;\n    height: 1.4em;\n    width: 1.4em;\n    top: 0.25em;\n    bottom: auto;\n    transform: translateY(-50%) translateX(-50%);\n    z-index: 2;\n  }\n  :global(.rangeSlider.reversed .rangeHandle) {\n    transform: translateY(-50%) translateX(50%);\n  }\n  :global(.rangeSlider.vertical .rangeHandle) {\n    left: 0.25em;\n    top: auto;\n    transform: translateY(50%) translateX(-50%);\n  }\n  :global(.rangeSlider.vertical.reversed .rangeHandle) {\n    transform: translateY(-50%) translateX(-50%);\n  }\n  :global(.rangeSlider .rangeNub),\n  :global(.rangeSlider .rangeHandle:before) {\n    position: absolute;\n    left: 0;\n    top: 0;\n    display: block;\n    border-radius: 10em;\n    height: 100%;\n    width: 100%;\n    transition: box-shadow 0.2s ease;\n  }\n  :global(.rangeSlider .rangeHandle:before) {\n    content: \"\";\n    left: 1px;\n    top: 1px;\n    bottom: 1px;\n    right: 1px;\n    height: auto;\n    width: auto;\n    box-shadow: 0 0 0 0px var(--handle-border);\n    opacity: 0;\n  }\n  :global(.rangeSlider.hoverable:not(.disabled) .rangeHandle:hover:before) {\n    box-shadow: 0 0 0 8px var(--handle-border);\n    opacity: 0.2;\n  }\n  :global(.rangeSlider.hoverable:not(.disabled) .rangeHandle.press:before),\n  :global(.rangeSlider.hoverable:not(.disabled) .rangeHandle.press:hover:before) {\n    box-shadow: 0 0 0 12px var(--handle-border);\n    opacity: 0.4;\n  }\n  :global(.rangeSlider.range:not(.min):not(.max) .rangeNub) {\n    border-radius: 10em 10em 10em 1.6em;\n  }\n  :global(.rangeSlider.range .rangeHandle:nth-of-type(1) .rangeNub) {\n    transform: rotate(-135deg);\n  }\n  :global(.rangeSlider.range .rangeHandle:nth-of-type(2) .rangeNub) {\n    transform: rotate(45deg);\n  }\n  :global(.rangeSlider.range.reversed .rangeHandle:nth-of-type(1) .rangeNub) {\n    transform: rotate(45deg);\n  }\n  :global(.rangeSlider.range.reversed .rangeHandle:nth-of-type(2) .rangeNub) {\n    transform: rotate(-135deg);\n  }\n  :global(.rangeSlider.range.vertical .rangeHandle:nth-of-type(1) .rangeNub) {\n    transform: rotate(135deg);\n  }\n  :global(.rangeSlider.range.vertical .rangeHandle:nth-of-type(2) .rangeNub) {\n    transform: rotate(-45deg);\n  }\n  :global(.rangeSlider.range.vertical.reversed .rangeHandle:nth-of-type(1) .rangeNub) {\n    transform: rotate(-45deg);\n  }\n  :global(.rangeSlider.range.vertical.reversed .rangeHandle:nth-of-type(2) .rangeNub) {\n    transform: rotate(135deg);\n  }\n  :global(.rangeSlider .rangeFloat) {\n    display: block;\n    position: absolute;\n    left: 50%;\n    top: -0.5em;\n    transform: translate(-50%, -100%);\n    font-size: 1em;\n    text-align: center;\n    opacity: 0;\n    pointer-events: none;\n    white-space: nowrap;\n    transition: all 0.2s ease;\n    font-size: 0.9em;\n    padding: 0.2em 0.4em;\n    border-radius: 0.2em;\n  }\n  :global(.rangeSlider .rangeHandle.active .rangeFloat),\n  :global(.rangeSlider.hoverable .rangeHandle:hover .rangeFloat) {\n    opacity: 1;\n    top: -0.2em;\n    transform: translate(-50%, -100%);\n  }\n  :global(.rangeSlider .rangeBar) {\n    position: absolute;\n    display: block;\n    transition: background 0.2s ease;\n    border-radius: 1em;\n    height: 0.5em;\n    top: 0;\n    user-select: none;\n    z-index: 1;\n  }\n  :global(.rangeSlider.vertical .rangeBar) {\n    width: 0.5em;\n    height: auto;\n  }\n  :global(.rangeSlider) {\n    background-color: #d7dada;\n    background-color: var(--slider);\n  }\n  :global(.rangeSlider .rangeBar) {\n    background-color: #99a2a2;\n    background-color: var(--range-inactive);\n  }\n  :global(.rangeSlider.focus .rangeBar) {\n    background-color: #838de7;\n    background-color: var(--range);\n  }\n  :global(.rangeSlider .rangeNub) {\n    background-color: #99a2a2;\n    background-color: var(--handle-inactive);\n  }\n  :global(.rangeSlider.focus .rangeNub) {\n    background-color: #838de7;\n    background-color: var(--handle);\n  }\n  :global(.rangeSlider .rangeHandle.active .rangeNub) {\n    background-color: #4a40d4;\n    background-color: var(--handle-focus);\n  }\n  :global(.rangeSlider .rangeFloat) {\n    color: white;\n    color: var(--float-text);\n    background-color: #99a2a2;\n    background-color: var(--float-inactive);\n  }\n  :global(.rangeSlider.focus .rangeFloat) {\n    background-color: #4a40d4;\n    background-color: var(--float);\n  }\n  :global(.rangeSlider.disabled) {\n    opacity: 0.5;\n  }\n  :global(.rangeSlider.disabled .rangeNub) {\n    background-color: #d7dada;\n    background-color: var(--slider);\n  }\n</style>\n\n<div\n  {id}\n  bind:this={slider}\n  class=\"rangeSlider\"\n  class:range\n  class:disabled\n  class:hoverable\n  class:vertical\n  class:reversed\n  class:focus\n  class:min={range === 'min'}\n  class:max={range === 'max'}\n  class:pips\n  class:pip-labels={all === 'label' || first === 'label' || last === 'label' || rest === 'label'}\n  on:mousedown={sliderInteractStart}\n  on:mouseup={sliderInteractEnd}\n  on:touchstart|preventDefault={sliderInteractStart}\n  on:touchend|preventDefault={sliderInteractEnd}\n>\n  {#each values as value, index}\n    <span\n      role=\"slider\"\n      class=\"rangeHandle\"\n      class:active={focus && activeHandle === index}\n      class:press={handlePressed && activeHandle === index}\n      data-handle={index}\n      on:blur={sliderBlurHandle}\n      on:focus={sliderFocusHandle}\n      on:keydown={sliderKeydown}\n      style=\"{orientationStart}: {$springPositions[index]}%; z-index: {activeHandle === index ? 3 : 2};\"\n      aria-valuemin={range === true && index === 1 ? values[0] : min}\n      aria-valuemax={range === true && index === 0 ? values[1] : max}\n      aria-valuenow={value}\n      aria-valuetext=\"{prefix}{handleFormatter(value,index,percentOf(value))}{suffix}\"\n      aria-orientation={vertical ? 'vertical' : 'horizontal'}\n      aria-disabled={disabled}\n      {disabled}\n      tabindex=\"{ disabled ? -1 : 0 }\"\n    >\n      <span class=\"rangeNub\" />\n      {#if float}\n        <span class=\"rangeFloat\">\n          {#if prefix}<span class=\"rangeFloat-prefix\">{prefix}</span>{/if}{handleFormatter(value,index,percentOf(value))}{#if suffix}<span class=\"rangeFloat-suffix\">{suffix}</span>{/if}\n        </span>\n      {/if}\n    </span>\n  {/each}\n  {#if range}\n    <span\n      class=\"rangeBar\"\n      style=\"{orientationStart}: {rangeStart($springPositions)}%; \n             {orientationEnd}: {rangeEnd($springPositions)}%;\" />\n  {/if}\n  {#if pips}\n    <RangePips\n      {values}\n      {min}\n      {max}\n      {step}\n      {range}\n      {vertical}\n      {reversed}\n      {orientationStart}\n      {hoverable}\n      {disabled}\n      {all}\n      {first}\n      {last}\n      {rest}\n      {pipstep}\n      {prefix}\n      {suffix}\n      {formatter}\n      {focus}\n      {percentOf}\n      {moveHandle}\n    />\n  {/if}\n</div>\n\n<svelte:window\n  on:mousedown={bodyInteractStart}\n  on:touchstart={bodyInteractStart}\n  on:mousemove={bodyInteract}\n  on:touchmove={bodyInteract}\n  on:mouseup={bodyMouseUp}\n  on:touchend={bodyTouchEnd}\n  on:keydown={bodyKeyDown} />\n", "<script context=\"module\" lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\timport { BaseButton } from \"@gradio/button/static\";\n\n\texport interface AudioData extends FileData {\n\t\tcrop_min?: number;\n\t\tcrop_max?: number;\n\t}\n</script>\n\n<script lang=\"ts\">\n\timport { onDestroy, createEventDispatcher } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Music } from \"@gradio/icons\";\n\timport Audio from \"../shared/Audio.svelte\";\n\t// @ts-ignore\n\timport Range from \"svelte-range-slider-pips\";\n\timport { _ } from \"svelte-i18n\";\n\n\timport type { IBlobEvent, IMediaRecorder } from \"extendable-media-recorder\";\n\n\texport let value: null | { name: string; data: string } = null;\n\texport let label: string;\n\texport let show_label = true;\n\texport let name = \"\";\n\texport let source: \"microphone\" | \"upload\" | \"none\";\n\texport let pending = false;\n\texport let streaming = false;\n\texport let autoplay = false;\n\texport let show_edit_button = true;\n\n\t// TODO: make use of this\n\t// export let type: \"normal\" | \"numpy\" = \"normal\";\n\n\tlet recording = false;\n\tlet recorder: IMediaRecorder;\n\tlet mode = \"\";\n\tlet header: Uint8Array | undefined = undefined;\n\tlet pending_stream: Uint8Array[] = [];\n\tlet submit_pending_stream_on_pending_end = false;\n\tlet player: HTMLAudioElement;\n\tlet inited = false;\n\tlet crop_values: [number, number] = [0, 100];\n\tconst STREAM_TIMESLICE = 500;\n\tconst NUM_HEADER_BYTES = 44;\n\tlet audio_chunks: Blob[] = [];\n\tlet module_promises: [\n\t\tPromise<typeof import(\"extendable-media-recorder\")>,\n\t\tPromise<typeof import(\"extendable-media-recorder-wav-encoder\")>\n\t];\n\n\tfunction get_modules(): void {\n\t\tmodule_promises = [\n\t\t\timport(\"extendable-media-recorder\"),\n\t\t\timport(\"extendable-media-recorder-wav-encoder\"),\n\t\t];\n\t}\n\n\tif (streaming) {\n\t\tget_modules();\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: AudioData | null;\n\t\tstream: AudioData;\n\t\tedit: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tdrag: boolean;\n\t\terror: string;\n\t\tupload: FileData;\n\t\tclear: never;\n\t\tstart_recording: never;\n\t\tstop_recording: never;\n\t}>();\n\n\tfunction blob_to_data_url(blob: Blob): Promise<string> {\n\t\treturn new Promise((fulfill, reject) => {\n\t\t\tlet reader = new FileReader();\n\t\t\treader.onerror = reject;\n\t\t\treader.onload = () => fulfill(reader.result as string);\n\t\t\treader.readAsDataURL(blob);\n\t\t});\n\t}\n\n\tconst dispatch_blob = async (\n\t\tblobs: Uint8Array[] | Blob[],\n\t\tevent: \"stream\" | \"change\" | \"stop_recording\"\n\t): Promise<void> => {\n\t\tlet _audio_blob = new Blob(blobs, { type: \"audio/wav\" });\n\t\tvalue = {\n\t\t\tdata: await blob_to_data_url(_audio_blob),\n\t\t\tname: \"audio.wav\",\n\t\t};\n\t\tdispatch(event, value);\n\t};\n\n\tasync function prepare_audio(): Promise<void> {\n\t\tlet stream: MediaStream | null;\n\n\t\ttry {\n\t\t\tstream = await navigator.mediaDevices.getUserMedia({ audio: true });\n\t\t} catch (err) {\n\t\t\tif (!navigator.mediaDevices) {\n\t\t\t\tdispatch(\"error\", $_(\"audio.no_device_support\"));\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\t\tdispatch(\"error\", $_(\"audio.allow_recording_access\"));\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthrow err;\n\t\t}\n\n\t\tif (stream == null) return;\n\n\t\tif (streaming) {\n\t\t\tconst [{ MediaRecorder, register }, { connect }] = await Promise.all(\n\t\t\t\tmodule_promises\n\t\t\t);\n\n\t\t\tawait register(await connect());\n\n\t\t\trecorder = new MediaRecorder(stream, { mimeType: \"audio/wav\" });\n\n\t\t\trecorder.addEventListener(\"dataavailable\", handle_chunk);\n\t\t} else {\n\t\t\trecorder = new MediaRecorder(stream);\n\n\t\t\trecorder.addEventListener(\"dataavailable\", (event) => {\n\t\t\t\taudio_chunks.push(event.data);\n\t\t\t});\n\n\t\t\trecorder.addEventListener(\"stop\", async () => {\n\t\t\t\trecording = false;\n\t\t\t\tawait dispatch_blob(audio_chunks, \"change\");\n\t\t\t\tawait dispatch_blob(audio_chunks, \"stop_recording\");\n\t\t\t\taudio_chunks = [];\n\t\t\t});\n\t\t}\n\n\t\tinited = true;\n\t}\n\n\tasync function handle_chunk(event: IBlobEvent): Promise<void> {\n\t\tlet buffer = await event.data.arrayBuffer();\n\t\tlet payload = new Uint8Array(buffer);\n\t\tif (!header) {\n\t\t\theader = new Uint8Array(buffer.slice(0, NUM_HEADER_BYTES));\n\t\t\tpayload = new Uint8Array(buffer.slice(NUM_HEADER_BYTES));\n\t\t}\n\t\tif (pending) {\n\t\t\tpending_stream.push(payload);\n\t\t} else {\n\t\t\tlet blobParts = [header].concat(pending_stream, [payload]);\n\t\t\tdispatch_blob(blobParts, \"stream\");\n\t\t\tpending_stream = [];\n\t\t}\n\t}\n\n\t$: if (submit_pending_stream_on_pending_end && pending === false) {\n\t\tsubmit_pending_stream_on_pending_end = false;\n\t\tif (header && pending_stream) {\n\t\t\tlet blobParts: Uint8Array[] = [header].concat(pending_stream);\n\t\t\tpending_stream = [];\n\t\t\tdispatch_blob(blobParts, \"stream\");\n\t\t}\n\t}\n\n\tasync function record(): Promise<void> {\n\t\tif (!navigator.mediaDevices) {\n\t\t\tdispatch(\"error\", $_(\"audio.no_device_support\"));\n\t\t\treturn;\n\t\t}\n\t\trecording = true;\n\t\tdispatch(\"start_recording\");\n\t\tif (!inited) await prepare_audio();\n\t\theader = undefined;\n\t\tif (streaming) {\n\t\t\trecorder.start(STREAM_TIMESLICE);\n\t\t} else {\n\t\t\trecorder.start();\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (recorder && recorder.state !== \"inactive\") {\n\t\t\trecorder.stop();\n\t\t}\n\t});\n\n\tfunction stop(): void {\n\t\trecorder.stop();\n\t\tif (streaming) {\n\t\t\trecording = false;\n\t\t\tdispatch(\"stop_recording\");\n\t\t\tif (pending) {\n\t\t\t\tsubmit_pending_stream_on_pending_end = true;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction clear(): void {\n\t\tdispatch(\"change\", null);\n\t\tdispatch(\"clear\");\n\t\tmode = \"\";\n\t\tvalue = null;\n\t}\n\n\tfunction handle_change({\n\t\tdetail: { values },\n\t}: {\n\t\tdetail: { values: [number, number] };\n\t}): void {\n\t\tif (!value) return;\n\n\t\tdispatch(\"change\", {\n\t\t\tdata: value.data,\n\t\t\tname,\n\t\t\tcrop_min: values[0],\n\t\t\tcrop_max: values[1],\n\t\t});\n\n\t\tdispatch(\"edit\");\n\t}\n\n\tfunction handle_load({\n\t\tdetail,\n\t}: {\n\t\tdetail: {\n\t\t\tdata: string;\n\t\t\tname: string;\n\t\t\tsize: number;\n\t\t\tis_example: boolean;\n\t\t};\n\t}): void {\n\t\tvalue = detail;\n\t\tdispatch(\"change\", { data: detail.data, name: detail.name });\n\t\tdispatch(\"upload\", detail);\n\t}\n\n\tfunction handle_ended(): void {\n\t\tdispatch(\"stop\");\n\t\tdispatch(\"end\");\n\t}\n\n\texport let dragging = false;\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={Music}\n\tfloat={source === \"upload\" && value === null}\n\tlabel={label || $_(\"audio.audio\")}\n/>\n{#if value === null || streaming}\n\t{#if source === \"microphone\"}\n\t\t<div class=\"mic-wrap\">\n\t\t\t{#if recording}\n\t\t\t\t<BaseButton size=\"sm\" on:click={stop}>\n\t\t\t\t\t<span class=\"record-icon\">\n\t\t\t\t\t\t<span class=\"pinger\" />\n\t\t\t\t\t\t<span class=\"dot\" />\n\t\t\t\t\t</span>\n\t\t\t\t\t{$_(\"audio.stop_recording\")}\n\t\t\t\t</BaseButton>\n\t\t\t{:else}\n\t\t\t\t<BaseButton size=\"sm\" on:click={record}>\n\t\t\t\t\t<span class=\"record-icon\">\n\t\t\t\t\t\t<span class=\"dot\" />\n\t\t\t\t\t</span>\n\t\t\t\t\t{$_(\"audio.record_from_microphone\")}\n\t\t\t\t</BaseButton>\n\t\t\t{/if}\n\t\t</div>\n\t{:else if source === \"upload\"}\n\t\t<!-- explicitly listed out audio mimetypes due to iOS bug not recognizing audio/* -->\n\t\t<Upload\n\t\t\tfiletype=\"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*\"\n\t\t\ton:load={handle_load}\n\t\t\tbind:dragging\n\t\t>\n\t\t\t<slot />\n\t\t</Upload>\n\t{/if}\n{:else}\n\t<ModifyUpload\n\t\ton:clear={clear}\n\t\ton:edit={() => (mode = \"edit\")}\n\t\teditable={show_edit_button}\n\t\tabsolute={true}\n\t/>\n\n\t<div class=\"container\">\n\t\t<Audio\n\t\t\tcontrols\n\t\t\t{autoplay}\n\t\t\t{crop_values}\n\t\t\tbind:node={player}\n\t\t\tpreload=\"metadata\"\n\t\t\tsrc={value?.data}\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:ended={handle_ended}\n\t\t\tdata-testid={`${label}-audio`}\n\t\t/>\n\t</div>\n\n\t{#if mode === \"edit\" && player?.duration}\n\t\t<Range\n\t\t\tbind:values={crop_values}\n\t\t\trange\n\t\t\tmin={0}\n\t\t\tmax={100}\n\t\t\tstep={1}\n\t\t\ton:change={handle_change}\n\t\t/>\n\t{/if}\n{/if}\n\n<style>\n\t.mic-wrap {\n\t\tpadding: var(--size-2);\n\t}\n\n\t.record-icon {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tmargin-right: var(--size-2);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t}\n\n\t.dot {\n\t\tdisplay: inline-flex;\n\t\tposition: relative;\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-red-500);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t}\n\n\t.pinger {\n\t\tdisplay: inline-flex;\n\t\tposition: absolute;\n\t\topacity: 0.9;\n\t\tanimation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-red-500);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t@keyframes ping {\n\t\t75%,\n\t\t100% {\n\t\t\ttransform: scale(2);\n\t\t\topacity: 0;\n\t\t}\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { _ } from \"svelte-i18n\";\n\timport { UploadText } from \"@gradio/atoms\";\n\n\timport type { FileData } from \"@gradio/upload\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport Audio from \"./Audio.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { Block } from \"@gradio/atoms\";\n\n\timport { normalise_file } from \"@gradio/upload\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData | string = null;\n\texport let name: string;\n\texport let source: \"microphone\" | \"upload\";\n\texport let label: string;\n\texport let root: string;\n\texport let show_label: boolean;\n\texport let pending: boolean;\n\texport let streaming: boolean;\n\texport let root_url: null | string;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let autoplay = false;\n\texport let show_edit_button = true;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tstream: typeof value;\n\t\terror: string;\n\t\tedit: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tstart_recording: never;\n\t\tstop_recording: never;\n\t\tupload: never;\n\t\tclear: never;\n\t}>;\n\n\tlet old_value: null | FileData | string = null;\n\n\tlet _value: null | FileData;\n\t$: _value = normalise_file(value, root, root_url);\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\tlet dragging: boolean;\n</script>\n\n<Block\n\tvariant={value === null && source === \"upload\" ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\t<Audio\n\t\t{label}\n\t\t{show_label}\n\t\tvalue={_value}\n\t\ton:change={({ detail }) => (value = detail)}\n\t\ton:stream={({ detail }) => {\n\t\t\tvalue = detail;\n\t\t\tgradio.dispatch(\"stream\", value);\n\t\t}}\n\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t{name}\n\t\t{source}\n\t\t{pending}\n\t\t{streaming}\n\t\t{autoplay}\n\t\t{show_edit_button}\n\t\ton:edit={() => gradio.dispatch(\"edit\")}\n\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\ton:end={() => gradio.dispatch(\"end\")}\n\t\ton:start_recording={() => gradio.dispatch(\"start_recording\")}\n\t\ton:stop_recording={() => gradio.dispatch(\"stop_recording\")}\n\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\ton:error={({ detail }) => {\n\t\t\tloading_status = loading_status || {};\n\t\t\tloading_status.status = \"error\";\n\t\t\tgradio.dispatch(\"error\", detail);\n\t\t}}\n\t>\n\t\t<UploadText type=\"audio\" />\n\t</Audio>\n</Block>\n"], "file": "assets/index-6bc4eff2.js"}