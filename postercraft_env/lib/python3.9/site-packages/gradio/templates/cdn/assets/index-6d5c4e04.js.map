{"version": 3, "file": "index-6d5c4e04.js", "sources": ["../../../../js/timeseries/interactive/InteractiveTimeseries.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport Chart from \"../shared\";\n\timport { UploadText } from \"@gradio/atoms\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\timport { Chart as ChartIcon } from \"@gradio/icons\";\n\n\tfunction format_value(val: StaticData): any {\n\t\treturn val.data.map((r) =>\n\t\t\tr.reduce((acc, next, i) => ({ ...acc, [val.headers[i]]: next }), {})\n\t\t);\n\t}\n\n\tinterface StaticData {\n\t\tdata: number[][];\n\t\theaders: string[];\n\t}\n\tinterface Data {\n\t\tdata: number[][] | string;\n\t\theaders?: string[];\n\t}\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | Data;\n\texport let y: string[];\n\texport let x: string;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let colors: string[];\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: undefined;\n\t\tclear: undefined;\n\t}>;\n\n\tlet _value: string | null;\n\n\tfunction data_uri_to_blob(data_uri: string): Blob {\n\t\tconst byte_str = atob(data_uri.split(\",\")[1]);\n\t\tconst mime_str = data_uri.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\n\t\tconst ab = new ArrayBuffer(byte_str.length);\n\t\tconst ia = new Uint8Array(ab);\n\n\t\tfor (let i = 0; i < byte_str.length; i++) {\n\t\t\tia[i] = byte_str.charCodeAt(i);\n\t\t}\n\n\t\treturn new Blob([ab], { type: mime_str });\n\t}\n\n\tfunction blob_to_string(blob: Blob): void {\n\t\tconst reader = new FileReader();\n\n\t\treader.addEventListener(\"loadend\", (e) => {\n\t\t\t//@ts-ignore\n\t\t\t_value = e.srcElement.result;\n\t\t});\n\n\t\treader.readAsText(blob);\n\t}\n\n\tfunction dict_to_string(dict: Data): void {\n\t\tif (dict.headers) _value = dict.headers.join(\",\");\n\t\tconst data = dict.data as number[][];\n\t\tdata.forEach((_x: unknown[]) => {\n\t\t\t_value = _value + \"\\n\";\n\t\t\t_value = _value + _x.join(\",\");\n\t\t});\n\t}\n\n\t$: {\n\t\tif (value && value.data && typeof value.data === \"string\") {\n\t\t\tif (!value) _value = null;\n\t\t\telse blob_to_string(data_uri_to_blob(value.data));\n\t\t} else if (value && value.data && typeof value.data != \"string\") {\n\t\t\tif (!value) _value = null;\n\t\t\tdict_to_string(value);\n\t\t}\n\t}\n\n\tinterface XRow {\n\t\tname: string;\n\t\tvalues: number[];\n\t}\n\n\tinterface YRow {\n\t\tname: string;\n\t\tvalues: { x: number; y: number }[];\n\t}\n\n\tfunction make_dict(_x: XRow, _y: YRow[]): Data {\n\t\tconst headers = [];\n\t\tconst data = [];\n\n\t\theaders.push(_x.name);\n\t\t_y.forEach(({ name }) => headers.push(name));\n\n\t\tfor (let i = 0; i < _x.values.length; i++) {\n\t\t\tlet _data = [];\n\t\t\t_data.push(_x.values[i]);\n\t\t\t_y.forEach(({ values }) => _data.push(values[i].y));\n\n\t\t\tdata.push(_data);\n\t\t}\n\t\treturn { headers, data };\n\t}\n\n\tfunction handle_load(\n\t\tv: string | FileData | (string | FileData)[] | null\n\t): string | FileData | (string | FileData)[] | null {\n\t\tvalue = { data: v as string };\n\t\treturn v;\n\t}\n\n\tfunction handle_clear({ detail }: CustomEvent<FileData | null>): void {\n\t\tvalue = null;\n\t\tgradio.dispatch(\"change\");\n\t\tgradio.dispatch(\"clear\");\n\t}\n\n\t$: _value = value == null ? null : _value;\n\n\t$: value, gradio.dispatch(\"change\");\n</script>\n\n<Block\n\t{visible}\n\tvariant={!_value ? \"dashed\" : \"solid\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<BlockLabel {show_label} Icon={ChartIcon} label={label || \"TimeSeries\"} />\n\t<StatusTracker {...loading_status} />\n\n\t{#if _value}\n\t\t<div class=\"chart\">\n\t\t\t<ModifyUpload on:clear={handle_clear} />\n\t\t\t<Chart\n\t\t\t\tvalue={_value}\n\t\t\t\t{y}\n\t\t\t\t{x}\n\t\t\t\ton:process={({ detail: { x, y } }) => (value = make_dict(x, y))}\n\t\t\t\t{colors}\n\t\t\t/>\n\t\t</div>\n\t{:else if value === undefined || value === null}\n\t\t<Upload\n\t\t\tfiletype=\"text/csv\"\n\t\t\ton:load={({ detail }) => handle_load(detail)}\n\t\t\tinclude_file_metadata={false}\n\t\t>\n\t\t\t<UploadText type=\"csv\" />\n\t\t</Upload>\n\t{/if}\n</Block>\n\n<style>\n\t.chart {\n\t\tdisplay: flex;\n\t\tdisplay: relative;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-64);\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "ChartIcon", "dirty", "blocklabel_changes", "data_uri_to_blob", "data_uri", "byte_str", "mime_str", "ab", "ia", "i", "make_dict", "_x", "_y", "headers", "data", "name", "_data", "values", "elem_id", "$$props", "elem_classes", "visible", "value", "y", "x", "label", "show_label", "colors", "container", "scale", "min_width", "loading_status", "gradio", "_value", "blob_to_string", "blob", "reader", "e", "$$invalidate", "dict_to_string", "dict", "handle_load", "v", "handle_clear", "detail"], "mappings": "+rBAsK0B,+TAbCA,EAAY,EAAA,CAAA,yBAE5BA,EAAM,EAAA,yJAHfC,EASKC,EAAAC,EAAAC,CAAA,0EANIJ,EAAM,EAAA,4cAPeK,EAAkB,MAAAL,MAAS,yBACvCA,EAAc,EAAA,CAAA,kHAE5BA,EAAM,EAAA,EAAA,EAWDA,EAAU,CAAA,IAAA,QAAaA,OAAU,KAAI,iOAdEM,EAAA,KAAAC,EAAA,MAAAP,MAAS,8CACvCA,EAAc,EAAA,CAAA,CAAA,CAAA,2ZATvBA,EAAM,EAAA,EAAc,QAAX,iBACV,iOADCA,EAAM,EAAA,EAAc,QAAX,0QA3FV,SAAAQ,GAAiBC,EAAgB,OACnCC,EAAW,KAAKD,EAAS,MAAM,GAAG,EAAE,CAAC,CAAA,EACrCE,EAAWF,EAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAE5DG,EAAS,IAAA,YAAYF,EAAS,MAAM,EACpCG,EAAE,IAAO,WAAWD,CAAE,UAEnBE,EAAI,EAAGA,EAAIJ,EAAS,OAAQI,IACpCD,EAAGC,CAAC,EAAIJ,EAAS,WAAWI,CAAC,EAGnB,OAAA,IAAA,KAAM,CAAAF,CAAE,EAAK,CAAA,KAAMD,CAAQ,CAAA,WA2C9BI,GAAUC,EAAUC,EAAU,OAChCC,EAAO,CAAA,EACPC,EAAI,CAAA,EAEVD,EAAQ,KAAKF,EAAG,IAAI,EACpBC,EAAG,QAAW,CAAA,CAAA,KAAAG,CAAI,IAAOF,EAAQ,KAAKE,CAAI,CAAA,UAEjCN,EAAI,EAAGA,EAAIE,EAAG,OAAO,OAAQF,IAAC,KAClCO,EAAK,CAAA,EACTA,EAAM,KAAKL,EAAG,OAAOF,CAAC,CAAA,EACtBG,EAAG,QAAW,CAAA,CAAA,OAAAK,KAAaD,EAAM,KAAKC,EAAOR,CAAC,EAAE,CAAC,CAAA,EAEjDK,EAAK,KAAKE,CAAK,EAEP,MAAA,CAAA,QAAAH,EAAS,KAAAC,sBAxFR,GAAA,CAAA,QAAAI,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAkB,EAAAH,GAClB,EAAAI,CAAW,EAAAJ,GACX,EAAAK,CAAS,EAAAL,GACT,MAAAM,CAAa,EAAAN,GACb,WAAAO,CAAmB,EAAAP,GACnB,OAAAQ,CAAgB,EAAAR,EAChB,CAAA,UAAAS,EAAY,EAAI,EAAAT,EAChB,CAAA,MAAAU,EAAuB,IAAI,EAAAV,EAC3B,CAAA,UAAAW,EAAgC,MAAS,EAAAX,GACzC,eAAAY,CAA6B,EAAAZ,GAC7B,OAAAa,CAGT,EAAAb,EAEEc,EAgBK,SAAAC,EAAeC,EAAU,CAC3B,MAAAC,MAAa,WAEnBA,EAAO,iBAAiB,UAAYC,GAAC,CAEpCC,EAAA,GAAAL,EAASI,EAAE,WAAW,MAAM,IAG7BD,EAAO,WAAWD,CAAI,EAGd,SAAAI,EAAeC,EAAU,CAC7BA,EAAK,SAAOF,EAAA,GAAEL,EAASO,EAAK,QAAQ,KAAK,GAAG,CAAA,EACnCA,EAAK,KACb,QAAS7B,GAAa,MAC1BsB,EAASA,EAAS;AAAA,CAAI,EACtBK,EAAA,GAAAL,EAASA,EAAStB,EAAG,KAAK,GAAG,CAAA,IAyCtB,SAAA8B,EACRC,EAAmD,YAEnDpB,EAAK,CAAK,KAAMoB,CAAW,CAAA,EACpBA,EAGC,SAAAC,GAAe,OAAAC,GAAM,CAC7BN,EAAA,EAAAhB,EAAQ,IAAI,EACZU,EAAO,SAAS,QAAQ,EACxBA,EAAO,SAAS,OAAO,YA4BN,OAAM,CAAI,EAAAR,EAAG,EAAAD,CAAC,CAAA,IAAAe,EAAA,EAAUhB,EAAQZ,GAAUc,EAAGD,CAAC,CAAA,MAOlD,OAAAqB,CAAM,IAAOH,EAAYG,CAAM,wfAjFxCtB,GAASA,EAAM,aAAeA,EAAM,MAAS,SAC3CA,EACAY,EAAe/B,GAAiBmB,EAAM,IAAI,CAAA,EADrCgB,EAAA,GAAEL,EAAS,IAAI,EAEfX,GAASA,EAAM,aAAeA,EAAM,MAAQ,WACjDA,GAAKgB,EAAA,GAAEL,EAAS,IAAI,EACzBM,EAAejB,CAAK,qBA4CrBgB,EAAA,GAAEL,EAASX,GAAS,KAAO,KAAOW,CAAM,oBAE/BD,EAAO,SAAS,QAAQ"}