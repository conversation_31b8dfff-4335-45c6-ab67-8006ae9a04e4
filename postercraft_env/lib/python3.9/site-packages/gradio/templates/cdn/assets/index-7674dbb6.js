const VERSION_RE = new RegExp("3.50.0/", "g");function import_fix(mod, base) {const url =  new URL(mod, base); return import(`https://gradio.s3-us-west-2.amazonaws.com/3.50.0/${url.pathname?.startsWith('/') ? url.pathname.substring(1).replace(VERSION_RE, "") : url.pathname.replace(VERSION_RE, "")}`);}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&o(a)}).observe(document,{childList:!0,subtree:!0});function r(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(n){if(n.ep)return;n.ep=!0;const i=r(n);fetch(n.href,i)}})();var su=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function bo(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ot={},nt={},dr={exports:{}},q=String,yo=function(){return{isColorSupported:!1,reset:q,bold:q,dim:q,italic:q,underline:q,inverse:q,hidden:q,strikethrough:q,black:q,red:q,green:q,yellow:q,blue:q,magenta:q,cyan:q,white:q,gray:q,bgBlack:q,bgRed:q,bgGreen:q,bgYellow:q,bgBlue:q,bgMagenta:q,bgCyan:q,bgWhite:q}};dr.exports=yo();dr.exports.createColors=yo;var Vi=dr.exports;Object.defineProperty(nt,"__esModule",{value:!0});nt.dim=Xi;nt.default=void 0;var ve=Wi(Vi);function Wi(e){return e&&e.__esModule?e:{default:e}}let xr=new Set;function It(e,t,r){typeof process<"u"&&{}.JEST_WORKER_ID||r&&xr.has(r)||(r&&xr.add(r),console.warn(""),t.forEach(o=>console.warn(e,"-",o)))}function Xi(e){return ve.default.dim(e)}var Zi={info(e,t){It(ve.default.bold(ve.default.cyan("info")),...Array.isArray(e)?[e]:[t,e])},warn(e,t){It(ve.default.bold(ve.default.yellow("warn")),...Array.isArray(e)?[e]:[t,e])},risk(e,t){It(ve.default.bold(ve.default.magenta("risk")),...Array.isArray(e)?[e]:[t,e])}};nt.default=Zi;Object.defineProperty(Ot,"__esModule",{value:!0});Ot.default=void 0;var Ji=Yi(nt);function Yi(e){return e&&e.__esModule?e:{default:e}}function Ze({version:e,from:t,to:r}){Ji.default.warn(`${t}-color-renamed`,[`As of Tailwind CSS ${e}, \`${t}\` has been renamed to \`${r}\`.`,"Update your configuration file to silence this warning."])}var Qi={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337"},get lightBlue(){return Ze({version:"v2.2",from:"lightBlue",to:"sky"}),this.sky},get warmGray(){return Ze({version:"v3.0",from:"warmGray",to:"stone"}),this.stone},get trueGray(){return Ze({version:"v3.0",from:"trueGray",to:"neutral"}),this.neutral},get coolGray(){return Ze({version:"v3.0",from:"coolGray",to:"gray"}),this.gray},get blueGray(){return Ze({version:"v3.0",from:"blueGray",to:"slate"}),this.slate}};Ot.default=Qi;let Lt=Ot;var Ki=(Lt.__esModule?Lt:{default:Lt}).default;const Er=bo(Ki),lu=["red","green","blue","yellow","purple","teal","orange","cyan","lime","pink"],$i=[{color:"red",primary:600,secondary:100},{color:"green",primary:600,secondary:100},{color:"blue",primary:600,secondary:100},{color:"yellow",primary:500,secondary:100},{color:"purple",primary:600,secondary:100},{color:"teal",primary:600,secondary:100},{color:"orange",primary:600,secondary:100},{color:"cyan",primary:600,secondary:100},{color:"lime",primary:500,secondary:100},{color:"pink",primary:600,secondary:100}],cu=$i.reduce((e,{color:t,primary:r,secondary:o})=>({...e,[t]:{primary:Er[t][r],secondary:Er[t][o]}}),{}),ea="modulepreload",ta=function(e){return"https://gradio.s3-us-west-2.amazonaws.com/3.50.0/"+e},Sr={},mt=function(t,r,o){if(!r||r.length===0)return t();const n=document.getElementsByTagName("link");return Promise.all(r.map(i=>{if(i=ta(i),i in Sr)return;Sr[i]=!0;const a=i.endsWith(".css"),l=a?'[rel="stylesheet"]':"";if(!!o)for(let c=n.length-1;c>=0;c--){const f=n[c];if(f.href===i&&(!a||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${l}`))return;const s=document.createElement("link");if(s.rel=a?"stylesheet":ea,a||(s.as="script",s.crossOrigin=""),s.href=i,document.head.appendChild(s),a)return new Promise((c,f)=>{s.addEventListener("load",c),s.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t())};var Rt=new Intl.Collator(0,{numeric:1}).compare;function kr(e,t,r){return e=e.split("."),t=t.split("."),Rt(e[0],t[0])||Rt(e[1],t[1])||(t[2]=t.slice(2).join("."),r=/[.-]/.test(e[2]=e.slice(2).join(".")),r==/[.-]/.test(t[2])?Rt(e[2],t[2]):r?-1:1)}function Pe(e,t,r){return t.startsWith("http://")||t.startsWith("https://")?r?e:t:e+t}function zt(e){if(e.startsWith("http")){const{protocol:t,host:r}=new URL(e);return r.endsWith("hf.space")?{ws_protocol:"wss",host:r,http_protocol:t}:{ws_protocol:t==="https:"?"wss":"ws",http_protocol:t,host:r}}else if(e.startsWith("file:"))return{ws_protocol:"ws",http_protocol:"http:",host:"lite.local"};return{ws_protocol:"wss",http_protocol:"https:",host:e}}const vo=/^[^\/]*\/[^\/]*$/,ra=/.*hf\.space\/{0,1}$/;async function oa(e,t){const r={};t&&(r.Authorization=`Bearer ${t}`);const o=e.trim();if(vo.test(o))try{const n=await fetch(`https://huggingface.co/api/spaces/${o}/host`,{headers:r});if(n.status!==200)throw new Error("Space metadata could not be loaded.");const i=(await n.json()).host;return{space_id:e,...zt(i)}}catch(n){throw new Error("Space metadata could not be loaded."+n.message)}if(ra.test(o)){const{ws_protocol:n,http_protocol:i,host:a}=zt(o);return{space_id:a.replace(".hf.space",""),ws_protocol:n,http_protocol:i,host:a}}return{space_id:!1,...zt(o)}}function na(e){let t={};return e.forEach(({api_name:r},o)=>{r&&(t[r]=o)}),t}const ia=/^(?=[^]*\b[dD]iscussions{0,1}\b)(?=[^]*\b[dD]isabled\b)[^]*$/;async function Ar(e){try{const r=(await fetch(`https://huggingface.co/api/spaces/${e}/discussions`,{method:"HEAD"})).headers.get("x-error-message");return!(r&&ia.test(r))}catch{return!1}}const aa="This application is too busy. Keep trying!",ct="Connection errored out.";let wo;function sa(e,t){return{post_data:r,upload_files:o,client:n,handle_blob:i};async function r(a,l,u){const s={"Content-Type":"application/json"};u&&(s.Authorization=`Bearer ${u}`);try{var c=await e(a,{method:"POST",body:JSON.stringify(l),headers:s})}catch{return[{error:ct},500]}return[await c.json(),c.status]}async function o(a,l,u){const s={};u&&(s.Authorization=`Bearer ${u}`);const c=1e3,f=[];for(let p=0;p<l.length;p+=c){const h=l.slice(p,p+c),_=new FormData;h.forEach(x=>{_.append("files",x)});try{var d=await e(`${a}/upload`,{method:"POST",body:_,headers:s})}catch{return{error:ct}}const v=await d.json();f.push(...v)}return{files:f}}async function n(a,l={normalise_files:!0}){return new Promise(async u=>{const{status_callback:s,hf_token:c,normalise_files:f}=l,d={predict:X,submit:fe,view_api:de,component_server:W},p=f??!0;if((typeof window>"u"||!("WebSocket"in window))&&!global.Websocket){const P=await mt(()=>import("./wrapper-6f348d45-38be7a64.js"),["assets/wrapper-6f348d45-38be7a64.js","assets/__vite-browser-external-b25bb000.js"]);wo=(await mt(()=>import("./__vite-browser-external-b25bb000.js"),[])).Blob,global.WebSocket=P.WebSocket}const{ws_protocol:h,http_protocol:_,host:v,space_id:x}=await oa(a,c),E=Math.random().toString(36).substring(2),g={};let b,k={},C=!1;c&&x&&(C=await ca(x,c));async function A(P){if(b=P,k=na(P?.dependencies||[]),b.auth_required)return{config:b,...d};try{H=await de(b)}catch(G){console.error(`Could not get api details: ${G.message}`)}return{config:b,...d}}let H;async function U(P){if(s&&s(P),P.status==="running")try{b=await Nr(e,`${_}//${v}`,c);const G=await A(b);u(G)}catch(G){console.error(G),s&&s({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"})}}try{b=await Nr(e,`${_}//${v}`,c);const P=await A(b);u(P)}catch(P){console.error(P),x?Jt(x,vo.test(x)?"space_name":"subdomain",U):s&&s({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"})}function X(P,G,ie){let B=!1,K=!1,R;if(typeof P=="number")R=b.dependencies[P];else{const N=P.replace(/^\//,"");R=b.dependencies[k[N]]}if(R.types.continuous)throw new Error("Cannot call predict on this function as it may run forever. Use submit instead");return new Promise((N,ue)=>{const ae=fe(P,G,ie);let w;ae.on("data",se=>{K&&(ae.destroy(),N(se)),B=!0,w=se}).on("status",se=>{se.stage==="error"&&ue(se),se.stage==="complete"&&(K=!0,B&&(ae.destroy(),N(w)))})})}function fe(P,G,ie){let B,K;if(typeof P=="number")B=P,K=H.unnamed_endpoints[B];else{const Z=P.replace(/^\//,"");B=k[Z],K=H.named_endpoints[P.trim()]}if(typeof B!="number")throw new Error("There is no endpoint matching that name of fn_index matching that number.");let R;const N=typeof P=="number"?"/predict":P;let ue,ae=!1;const w={};let se="";typeof window<"u"&&(se=new URLSearchParams(window.location.search).toString()),i(`${_}//${Pe(v,b.path,!0)}`,G,K,c).then(Z=>{if(ue={data:Z||[],event_data:ie,fn_index:B},fa(B,b))le({type:"status",endpoint:N,stage:"pending",queue:!1,fn_index:B,time:new Date}),r(`${_}//${Pe(v,b.path,!0)}/run${N.startsWith("/")?N:`/${N}`}${se?"?"+se:""}`,{...ue,session_hash:E},c).then(([Y,m])=>{const D=p?Pr(Y.data,K,b.root,b.root_url):Y.data;m==200?(le({type:"data",endpoint:N,fn_index:B,data:D,time:new Date}),le({type:"status",endpoint:N,fn_index:B,stage:"complete",eta:Y.average_duration,queue:!1,time:new Date})):le({type:"status",stage:"error",endpoint:N,fn_index:B,message:Y.error,queue:!1,time:new Date})}).catch(Y=>{le({type:"status",stage:"error",message:Y.message,endpoint:N,fn_index:B,queue:!1,time:new Date})});else{le({type:"status",stage:"pending",queue:!0,endpoint:N,fn_index:B,time:new Date});let Y=new URL(`${h}://${Pe(v,b.path,!0)}
							/queue/join${se?"?"+se:""}`);C&&Y.searchParams.set("__sign",C),R=t(Y),R.onclose=m=>{m.wasClean||le({type:"status",stage:"error",broken:!0,message:ct,queue:!0,endpoint:N,fn_index:B,time:new Date})},R.onmessage=function(m){const D=JSON.parse(m.data),{type:F,status:$,data:Xe}=da(D,g[B]);if(F==="update"&&$&&!ae)le({type:"status",endpoint:N,fn_index:B,time:new Date,...$}),$.stage==="error"&&R.close();else if(F==="hash"){R.send(JSON.stringify({fn_index:B,session_hash:E}));return}else F==="data"?R.send(JSON.stringify({...ue,session_hash:E})):F==="complete"?ae=$:F==="log"?le({type:"log",log:Xe.log,level:Xe.level,endpoint:N,fn_index:B}):F==="generating"&&le({type:"status",time:new Date,...$,stage:$?.stage,queue:!0,endpoint:N,fn_index:B});Xe&&(le({type:"data",time:new Date,data:p?Pr(Xe.data,K,b.root,b.root_url):Xe.data,endpoint:N,fn_index:B}),ae&&(le({type:"status",time:new Date,...ae,stage:$?.stage,queue:!0,endpoint:N,fn_index:B}),R.close()))},kr(b.version||"2.0.0","3.6")<0&&addEventListener("open",()=>R.send(JSON.stringify({hash:E})))}});function le(Z){const m=w[Z.type]||[];m?.forEach(D=>D(Z))}function qe(Z,Y){const m=w,D=m[Z]||[];return m[Z]=D,D?.push(Y),{on:qe,off:Ae,cancel:Ve,destroy:We}}function Ae(Z,Y){const m=w;let D=m[Z]||[];return D=D?.filter(F=>F!==Y),m[Z]=D,{on:qe,off:Ae,cancel:Ve,destroy:We}}async function Ve(){const Z={stage:"complete",queue:!1,time:new Date};ae=Z,le({...Z,type:"status",endpoint:N,fn_index:B}),R&&R.readyState===0?R.addEventListener("open",()=>{R.close()}):R.close();try{await e(`${_}//${Pe(v,b.path,!0)}/reset`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify({fn_index:B,session_hash:E})})}catch{console.warn("The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.")}}function We(){for(const Z in w)w[Z].forEach(Y=>{Ae(Z,Y)})}return{on:qe,off:Ae,cancel:Ve,destroy:We}}async function W(P,G,ie){var B;const K={"Content-Type":"application/json"};c&&(K.Authorization=`Bearer ${c}`);let R,N=b.components.find(w=>w.id===P);(B=N?.props)!=null&&B.root_url?R=N.props.root_url:R=`${_}//${Pe(v,b.path,!0)}/`;const ue=await e(`${R}component_server/`,{method:"POST",body:JSON.stringify({data:ie,component_id:P,fn_name:G,session_hash:E}),headers:K});if(!ue.ok)throw new Error("Could not connect to component server: "+ue.statusText);return await ue.json()}async function de(P){if(H)return H;const G={"Content-Type":"application/json"};c&&(G.Authorization=`Bearer ${c}`);let ie;if(kr(P.version||"2.0.0","3.30")<0?ie=await e("https://gradio-space-api-fetcher-v2.hf.space/api",{method:"POST",body:JSON.stringify({serialize:!1,config:JSON.stringify(P)}),headers:G}):ie=await e(`${P.root}/info`,{headers:G}),!ie.ok)throw new Error(ct);let B=await ie.json();return"api"in B&&(B=B.api),B.named_endpoints["/predict"]&&!B.unnamed_endpoints[0]&&(B.unnamed_endpoints[0]=B.named_endpoints["/predict"]),la(B,P,k)}})}async function i(a,l,u,s){const c=await Zt(l,void 0,[],!0,u);return Promise.all(c.map(async({path:f,blob:d,data:p,type:h})=>{if(d){const _=(await o(a,[d],s)).files[0];return{path:f,file_url:_,type:h}}return{path:f,base64:p,type:h}})).then(f=>(f.forEach(({path:d,file_url:p,base64:h,type:_})=>{if(h)jt(l,h,d);else if(_==="Gallery")jt(l,p,d);else if(p){const v={is_file:!0,name:`${p}`,data:null};jt(l,v,d)}}),l))}}const{post_data:uu,upload_files:Tr,client:Or,handle_blob:fu}=sa(fetch,(...e)=>new WebSocket(...e));function Pr(e,t,r,o){return e.map((n,i)=>{var a,l,u,s;return((l=(a=t?.returns)==null?void 0:a[i])==null?void 0:l.component)==="File"?Qe(n,r,o):((s=(u=t?.returns)==null?void 0:u[i])==null?void 0:s.component)==="Gallery"?n.map(c=>Array.isArray(c)?[Qe(c[0],r,o),c[1]]:[Qe(c,r,o),null]):typeof n=="object"&&n?.is_file?Qe(n,r,o):n})}function Qe(e,t,r){if(e==null)return null;if(typeof e=="string")return{name:"file_data",data:e};if(Array.isArray(e)){const o=[];for(const n of e)n===null?o.push(null):o.push(Qe(n,t,r));return o}else e.is_file&&(r?e.data="/proxy="+r+"file="+e.name:e.data=t+"/file="+e.name);return e}function Br(e,t,r,o){switch(e.type){case"string":return"string";case"boolean":return"boolean";case"number":return"number"}if(r==="JSONSerializable"||r==="StringSerializable")return"any";if(r==="ListStringSerializable")return"string[]";if(t==="Image")return o==="parameter"?"Blob | File | Buffer":"string";if(r==="FileSerializable")return e?.type==="array"?o==="parameter"?"(Blob | File | Buffer)[]":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]":o==="parameter"?"Blob | File | Buffer":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}";if(r==="GallerySerializable")return o==="parameter"?"[(Blob | File | Buffer), (string | null)][]":"[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]"}function Hr(e,t){return t==="GallerySerializable"?"array of [file, label] tuples":t==="ListStringSerializable"?"array of strings":t==="FileSerializable"?"array of files or single file":e.description}function la(e,t,r){const o={named_endpoints:{},unnamed_endpoints:{}};for(const n in e){const i=e[n];for(const a in i){const l=t.dependencies[a]?a:r[a.replace("/","")],u=i[a];o[n][a]={},o[n][a].parameters={},o[n][a].returns={},o[n][a].type=t.dependencies[l].types,o[n][a].parameters=u.parameters.map(({label:s,component:c,type:f,serializer:d})=>({label:s,component:c,type:Br(f,c,d,"parameter"),description:Hr(f,d)})),o[n][a].returns=u.returns.map(({label:s,component:c,type:f,serializer:d})=>({label:s,component:c,type:Br(f,c,d,"return"),description:Hr(f,d)}))}}return o}async function ca(e,t){try{return(await(await fetch(`https://huggingface.co/api/spaces/${e}/jwt`,{headers:{Authorization:`Bearer ${t}`}})).json()).token||!1}catch(r){return console.error(r),!1}}function jt(e,t,r){for(;r.length>1;)e=e[r.shift()];e[r.shift()]=t}async function Zt(e,t=void 0,r=[],o=!1,n=void 0){if(Array.isArray(e)){let i=[];return await Promise.all(e.map(async(a,l)=>{var u;let s=r.slice();s.push(l);const c=await Zt(e[l],o?((u=n?.parameters[l])==null?void 0:u.component)||void 0:t,s,!1,n);i=i.concat(c)})),i}else if(globalThis.Buffer&&e instanceof globalThis.Buffer){const i=t==="Image";return[{path:r,blob:i?!1:new wo([e]),data:i?`${e.toString("base64")}`:!1,type:t}]}else if(e instanceof Blob||typeof window<"u"&&e instanceof File){if(t==="Image"){let i;if(typeof window<"u")i=await ua(e);else{const a=await e.arrayBuffer();i=Buffer.from(a).toString("base64")}return[{path:r,data:i,type:t,blob:!1}]}return[{path:r,blob:e,type:t,data:!1}]}else if(typeof e=="object"){let i=[];for(let a in e)if(e.hasOwnProperty(a)){let l=r.slice();l.push(a),i=i.concat(await Zt(e[a],void 0,l,!1,n))}return i}return[]}function ua(e){return new Promise((t,r)=>{const o=new FileReader;o.onloadend=()=>t(o.result),o.readAsDataURL(e)})}function fa(e,t){var r,o,n,i;return!(((o=(r=t?.dependencies)==null?void 0:r[e])==null?void 0:o.queue)===null?t.enable_queue:(i=(n=t?.dependencies)==null?void 0:n[e])!=null&&i.queue)||!1}async function Nr(e,t,r){const o={};if(r&&(o.Authorization=`Bearer ${r}`),typeof window<"u"&&window.gradio_config&&location.origin!=="http://localhost:9876"&&!window.gradio_config.dev_mode){const n=window.gradio_config.root,i=window.gradio_config;return i.root=Pe(t,i.root,!1),{...i,path:n}}else if(t){let n=await e(`${t}/config`,{headers:o});if(n.status===200){const i=await n.json();return i.path=i.path??"",i.root=t,i}throw new Error("Could not get config.")}throw new Error("No config or app endpoint found")}async function Jt(e,t,r){let o=t==="subdomain"?`https://huggingface.co/api/spaces/by-subdomain/${e}`:`https://huggingface.co/api/spaces/${e}`,n,i;try{if(n=await fetch(o),i=n.status,i!==200)throw new Error;n=await n.json()}catch{r({status:"error",load_status:"error",message:"Could not get space status",detail:"NOT_FOUND"});return}if(!n||i!==200)return;const{runtime:{stage:a},id:l}=n;switch(a){case"STOPPED":case"SLEEPING":r({status:"sleeping",load_status:"pending",message:"Space is asleep. Waking it up...",detail:a}),setTimeout(()=>{Jt(e,t,r)},1e3);break;case"PAUSED":r({status:"paused",load_status:"error",message:"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.",detail:a,discussions_enabled:await Ar(l)});break;case"RUNNING":case"RUNNING_BUILDING":r({status:"running",load_status:"complete",message:"",detail:a});break;case"BUILDING":r({status:"building",load_status:"pending",message:"Space is building...",detail:a}),setTimeout(()=>{Jt(e,t,r)},1e3);break;default:r({status:"space_error",load_status:"error",message:"This space is experiencing an issue.",detail:a,discussions_enabled:await Ar(l)});break}}function da(e,t){switch(e.msg){case"send_data":return{type:"data"};case"send_hash":return{type:"hash"};case"queue_full":return{type:"update",status:{queue:!0,message:aa,stage:"error",code:e.code,success:e.success}};case"estimation":return{type:"update",status:{queue:!0,stage:t||"pending",code:e.code,size:e.queue_size,position:e.rank,eta:e.rank_eta,success:e.success}};case"progress":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,progress_data:e.progress_data,success:e.success}};case"log":return{type:"log",data:e};case"process_generating":return{type:"generating",status:{queue:!0,message:e.success?null:e.output.error,stage:e.success?"generating":"error",code:e.code,progress_data:e.progress_data,eta:e.average_duration},data:e.success?e.output:null};case"process_completed":return"error"in e.output?{type:"update",status:{queue:!0,message:e.output.error,stage:"error",code:e.code,success:e.success}}:{type:"complete",status:{queue:!0,message:e.success?void 0:e.output.error,stage:e.success?"complete":"error",code:e.code,progress_data:e.progress_data,eta:e.output.average_duration},data:e.success?e.output:null};case"process_starts":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,size:e.rank,position:0,success:e.success}}}return{type:"none",status:{stage:"error",queue:!0}}}function Yt(e,t){if(document.querySelector(`link[href='${e}']`))return Promise.resolve();const o=document.createElement("link");return o.rel="stylesheet",o.href=e,new Promise((n,i)=>{o.addEventListener("load",()=>n()),o.addEventListener("error",()=>{console.error(`Unable to preload CSS for ${e}`),n()}),t.appendChild(o)})}function Q(){}const hr=e=>e;function xo(e,t){for(const r in t)e[r]=t[r];return e}function du(e){return!!e&&(typeof e=="object"||typeof e=="function")&&typeof e.then=="function"}function Eo(e){return e()}function Cr(){return Object.create(null)}function _e(e){e.forEach(Eo)}function Ee(e){return typeof e=="function"}function it(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}let ut;function ha(e,t){return ut||(ut=document.createElement("a")),ut.href=t,e===ut.href}function pa(e){return Object.keys(e).length===0}function So(e,...t){if(e==null){for(const o of t)o(void 0);return Q}const r=e.subscribe(...t);return r.unsubscribe?()=>r.unsubscribe():r}function xe(e,t,r){e.$$.on_destroy.push(So(t,r))}function ko(e,t,r,o){if(e){const n=Ao(e,t,r,o);return e[0](n)}}function Ao(e,t,r,o){return e[1]&&o?xo(r.ctx.slice(),e[1](o(t))):r.ctx}function To(e,t,r,o){if(e[2]&&o){const n=e[2](o(r));if(t.dirty===void 0)return n;if(typeof n=="object"){const i=[],a=Math.max(t.dirty.length,n.length);for(let l=0;l<a;l+=1)i[l]=t.dirty[l]|n[l];return i}return t.dirty|n}return t.dirty}function Oo(e,t,r,o,n,i){if(n){const a=Ao(t,r,o,i);e.p(a,n)}}function Po(e){if(e.ctx.length>32){const t=[],r=e.ctx.length/32;for(let o=0;o<r;o++)t[o]=-1;return t}return-1}function hu(e){const t={};for(const r in e)r[0]!=="$"&&(t[r]=e[r]);return t}function pu(e,t){const r={};t=new Set(t);for(const o in e)!t.has(o)&&o[0]!=="$"&&(r[o]=e[o]);return r}function _u(e){return e??""}function gu(e,t,r){return e.set(r),t}function mu(e){return e&&Ee(e.destroy)?e.destroy:Q}function bu(e){const t=typeof e=="string"&&e.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[e,"px"]}const Bo=typeof window<"u";let et=Bo?()=>window.performance.now():()=>Date.now(),pr=Bo?e=>requestAnimationFrame(e):Q;const Ne=new Set;function Ho(e){Ne.forEach(t=>{t.c(e)||(Ne.delete(t),t.f())}),Ne.size!==0&&pr(Ho)}function Pt(e){let t;return Ne.size===0&&pr(Ho),{promise:new Promise(r=>{Ne.add(t={c:e,f:r})}),abort(){Ne.delete(t)}}}const _a=typeof window<"u"?window:typeof globalThis<"u"?globalThis:global;"WeakMap"in _a;function O(e,t){e.appendChild(t)}function No(e){if(!e)return document;const t=e.getRootNode?e.getRootNode():e.ownerDocument;return t&&t.host?t:e.ownerDocument}function ga(e){const t=j("style");return t.textContent="/* empty */",ma(No(e),t),t.sheet}function ma(e,t){return O(e.head||e,t),t.sheet}function T(e,t,r){e.insertBefore(t,r||null)}function S(e){e.parentNode&&e.parentNode.removeChild(e)}function Co(e,t){for(let r=0;r<e.length;r+=1)e[r]&&e[r].d(t)}function j(e){return document.createElement(e)}function he(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function I(e){return document.createTextNode(e)}function te(){return I(" ")}function Se(){return I("")}function Mr(e,t,r,o){return e.addEventListener(t,r,o),()=>e.removeEventListener(t,r,o)}function yu(e){return function(t){return t.preventDefault(),e.call(this,t)}}function vu(e){return function(t){return t.stopPropagation(),e.call(this,t)}}function y(e,t,r){r==null?e.removeAttribute(t):e.getAttribute(t)!==r&&e.setAttribute(t,r)}const ba=["width","height"];function ya(e,t){const r=Object.getOwnPropertyDescriptors(e.__proto__);for(const o in t)t[o]==null?e.removeAttribute(o):o==="style"?e.style.cssText=t[o]:o==="__value"?e.value=e[o]=t[o]:r[o]&&r[o].set&&ba.indexOf(o)===-1?e[o]=t[o]:y(e,o,t[o])}function va(e,t){Object.keys(t).forEach(r=>{wa(e,r,t[r])})}function wa(e,t,r){t in e?e[t]=typeof e[t]=="boolean"&&r===""?!0:r:y(e,t,r)}function wu(e){return/-/.test(e)?va:ya}function xu(e){let t;return{p(...r){t=r,t.forEach(o=>e.push(o))},r(){t.forEach(r=>e.splice(e.indexOf(r),1))}}}function Eu(e){return e===""?null:+e}function xa(e){return Array.from(e.childNodes)}function re(e,t){t=""+t,e.data!==t&&(e.data=t)}function Su(e,t){e.value=t??""}function oe(e,t,r,o){r==null?e.style.removeProperty(t):e.style.setProperty(t,r,o?"important":"")}let ft;function Ea(){if(ft===void 0){ft=!1;try{typeof window<"u"&&window.parent&&window.parent.document}catch{ft=!0}}return ft}function ku(e,t){getComputedStyle(e).position==="static"&&(e.style.position="relative");const o=j("iframe");o.setAttribute("style","display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;"),o.setAttribute("aria-hidden","true"),o.tabIndex=-1;const n=Ea();let i;return n?(o.src="data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}<\/script>",i=Mr(window,"message",a=>{a.source===o.contentWindow&&t()})):(o.src="about:blank",o.onload=()=>{i=Mr(o.contentWindow,"resize",t),t()}),O(e,o),()=>{(n||i&&o.contentWindow)&&i(),S(o)}}function J(e,t,r){e.classList.toggle(t,!!r)}function Mo(e,t,{bubbles:r=!1,cancelable:o=!1}={}){return new CustomEvent(e,{detail:t,bubbles:r,cancelable:o})}class Au{is_svg=!1;e=void 0;n=void 0;t=void 0;a=void 0;constructor(t=!1){this.is_svg=t,this.e=this.n=null}c(t){this.h(t)}m(t,r,o=null){this.e||(this.is_svg?this.e=he(r.nodeName):this.e=j(r.nodeType===11?"TEMPLATE":r.nodeName),this.t=r.tagName!=="TEMPLATE"?r:r.content,this.c(t)),this.i(o)}h(t){this.e.innerHTML=t,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(t){for(let r=0;r<this.n.length;r+=1)T(this.t,this.n[r],t)}p(t){this.d(),this.h(t),this.i(this.a)}d(){this.n.forEach(S)}}function Tu(e,t){return new e(t)}const bt=new Map;let yt=0;function Sa(e){let t=5381,r=e.length;for(;r--;)t=(t<<5)-t^e.charCodeAt(r);return t>>>0}function ka(e,t){const r={stylesheet:ga(t),rules:{}};return bt.set(e,r),r}function vt(e,t,r,o,n,i,a,l=0){const u=16.666/o;let s=`{
`;for(let v=0;v<=1;v+=u){const x=t+(r-t)*i(v);s+=v*100+`%{${a(x,1-x)}}
`}const c=s+`100% {${a(r,1-r)}}
}`,f=`__svelte_${Sa(c)}_${l}`,d=No(e),{stylesheet:p,rules:h}=bt.get(d)||ka(d,e);h[f]||(h[f]=!0,p.insertRule(`@keyframes ${f} ${c}`,p.cssRules.length));const _=e.style.animation||"";return e.style.animation=`${_?`${_}, `:""}${f} ${o}ms linear ${n}ms 1 both`,yt+=1,f}function wt(e,t){const r=(e.style.animation||"").split(", "),o=r.filter(t?i=>i.indexOf(t)<0:i=>i.indexOf("__svelte")===-1),n=r.length-o.length;n&&(e.style.animation=o.join(", "),yt-=n,yt||Aa())}function Aa(){pr(()=>{yt||(bt.forEach(e=>{const{ownerNode:t}=e.stylesheet;t&&S(t)}),bt.clear())})}let tt;function $e(e){tt=e}function ke(){if(!tt)throw new Error("Function called outside component initialization");return tt}function Ou(e){ke().$$.before_update.push(e)}function Qt(e){ke().$$.on_mount.push(e)}function Pu(e){ke().$$.after_update.push(e)}function Ta(e){ke().$$.on_destroy.push(e)}function Bu(){const e=ke();return(t,r,{cancelable:o=!1}={})=>{const n=e.$$.callbacks[t];if(n){const i=Mo(t,r,{cancelable:o});return n.slice().forEach(a=>{a.call(e,i)}),!i.defaultPrevented}return!0}}function Io(e,t){return ke().$$.context.set(e,t),t}function Oa(e){return ke().$$.context.get(e)}function Hu(e,t){const r=e.$$.callbacks[t.type];r&&r.slice().forEach(o=>o.call(this,t))}const Be=[],me=[];let Ce=[];const Kt=[],Lo=Promise.resolve();let $t=!1;function Ro(){$t||($t=!0,Lo.then(zo))}function Pa(){return Ro(),Lo}function Me(e){Ce.push(e)}function er(e){Kt.push(e)}const Dt=new Set;let Te=0;function zo(){if(Te!==0)return;const e=tt;do{try{for(;Te<Be.length;){const t=Be[Te];Te++,$e(t),Ba(t.$$)}}catch(t){throw Be.length=0,Te=0,t}for($e(null),Be.length=0,Te=0;me.length;)me.pop()();for(let t=0;t<Ce.length;t+=1){const r=Ce[t];Dt.has(r)||(Dt.add(r),r())}Ce.length=0}while(Be.length);for(;Kt.length;)Kt.pop()();$t=!1,Dt.clear(),$e(e)}function Ba(e){if(e.fragment!==null){e.update(),_e(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(Me)}}function Ha(e){const t=[],r=[];Ce.forEach(o=>e.indexOf(o)===-1?t.push(o):r.push(o)),r.forEach(o=>o()),Ce=t}let Je;function _r(){return Je||(Je=Promise.resolve(),Je.then(()=>{Je=null})),Je}function we(e,t,r){e.dispatchEvent(Mo(`${t?"intro":"outro"}${r}`))}const pt=new Set;let pe;function xt(){pe={r:0,c:[],p:pe}}function Et(){pe.r||_e(pe.c),pe=pe.p}function ee(e,t){e&&e.i&&(pt.delete(e),e.i(t))}function ne(e,t,r,o){if(e&&e.o){if(pt.has(e))return;pt.add(e),pe.c.push(()=>{pt.delete(e),o&&(r&&e.d(1),o())}),e.o(t)}else o&&o()}const gr={duration:0};function Nu(e,t,r){const o={direction:"in"};let n=t(e,r,o),i=!1,a,l,u=0;function s(){a&&wt(e,a)}function c(){const{delay:d=0,duration:p=300,easing:h=hr,tick:_=Q,css:v}=n||gr;v&&(a=vt(e,0,1,p,d,h,v,u++)),_(0,1);const x=et()+d,E=x+p;l&&l.abort(),i=!0,Me(()=>we(e,!0,"start")),l=Pt(g=>{if(i){if(g>=E)return _(1,0),we(e,!0,"end"),s(),i=!1;if(g>=x){const b=h((g-x)/p);_(b,1-b)}}return i})}let f=!1;return{start(){f||(f=!0,wt(e),Ee(n)?(n=n(o),_r().then(c)):c())},invalidate(){f=!1},end(){i&&(s(),i=!1)}}}function Cu(e,t,r){const o={direction:"out"};let n=t(e,r,o),i=!0,a;const l=pe;l.r+=1;let u;function s(){const{delay:c=0,duration:f=300,easing:d=hr,tick:p=Q,css:h}=n||gr;h&&(a=vt(e,1,0,f,c,d,h));const _=et()+c,v=_+f;Me(()=>we(e,!1,"start")),"inert"in e&&(u=e.inert,e.inert=!0),Pt(x=>{if(i){if(x>=v)return p(0,1),we(e,!1,"end"),--l.r||_e(l.c),!1;if(x>=_){const E=d((x-_)/f);p(1-E,E)}}return i})}return Ee(n)?_r().then(()=>{n=n(o),s()}):s(),{end(c){c&&"inert"in e&&(e.inert=u),c&&n.tick&&n.tick(1,0),i&&(a&&wt(e,a),i=!1)}}}function Mu(e,t,r,o){let i=t(e,r,{direction:"both"}),a=o?0:1,l=null,u=null,s=null,c;function f(){s&&wt(e,s)}function d(h,_){const v=h.b-a;return _*=Math.abs(v),{a,b:h.b,d:v,duration:_,start:h.start,end:h.start+_,group:h.group}}function p(h){const{delay:_=0,duration:v=300,easing:x=hr,tick:E=Q,css:g}=i||gr,b={start:et()+_,b:h};h||(b.group=pe,pe.r+=1),"inert"in e&&(h?c!==void 0&&(e.inert=c):(c=e.inert,e.inert=!0)),l||u?u=b:(g&&(f(),s=vt(e,a,h,v,_,x,g)),h&&E(0,1),l=d(b,v),Me(()=>we(e,h,"start")),Pt(k=>{if(u&&k>u.start&&(l=d(u,v),u=null,we(e,l.b,"start"),g&&(f(),s=vt(e,a,l.b,l.duration,0,x,i.css))),l){if(k>=l.end)E(a=l.b,1-a),we(e,l.b,"end"),u||(l.b?f():--l.group.r||_e(l.group.c)),l=null;else if(k>=l.start){const C=k-l.start;a=l.a+l.d*x(C/l.duration),E(a,1-a)}}return!!(l||u)}))}return{run(h){Ee(i)?_r().then(()=>{i=i({direction:h?"in":"out"}),p(h)}):p(h)},end(){f(),l=u=null}}}function St(e){return e?.length!==void 0?e:Array.from(e)}function Iu(e,t){e.d(1),t.delete(e.key)}function Na(e,t){ne(e,1,1,()=>{t.delete(e.key)})}function Lu(e,t){e.f(),Na(e,t)}function Ru(e,t,r,o,n,i,a,l,u,s,c,f){let d=e.length,p=i.length,h=d;const _={};for(;h--;)_[e[h].key]=h;const v=[],x=new Map,E=new Map,g=[];for(h=p;h--;){const A=f(n,i,h),H=r(A);let U=a.get(H);U?o&&g.push(()=>U.p(A,t)):(U=s(H,A),U.c()),x.set(H,v[h]=U),H in _&&E.set(H,Math.abs(h-_[H]))}const b=new Set,k=new Set;function C(A){ee(A,1),A.m(l,c),a.set(A.key,A),c=A.first,p--}for(;d&&p;){const A=v[p-1],H=e[d-1],U=A.key,X=H.key;A===H?(c=A.first,d--,p--):x.has(X)?!a.has(U)||b.has(U)?C(A):k.has(X)?d--:E.get(U)>E.get(X)?(k.add(U),C(A)):(b.add(X),d--):(u(H,a),d--)}for(;d--;){const A=e[d];x.has(A.key)||u(A,a)}for(;p;)C(v[p-1]);return _e(g),v}function Ca(e,t){const r={},o={},n={$$scope:1};let i=e.length;for(;i--;){const a=e[i],l=t[i];if(l){for(const u in a)u in l||(o[u]=1);for(const u in l)n[u]||(r[u]=l[u],n[u]=1);e[i]=l}else for(const u in a)n[u]=1}for(const a in o)a in r||(r[a]=void 0);return r}function Ma(e){return typeof e=="object"&&e!==null?e:{}}const Ia=["allowfullscreen","allowpaymentrequest","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","hidden","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected"];[...Ia];function tr(e,t,r){const o=e.$$.props[t];o!==void 0&&(e.$$.bound[o]=r,r(e.$$.ctx[o]))}function at(e){e&&e.c()}function je(e,t,r){const{fragment:o,after_update:n}=e.$$;o&&o.m(t,r),Me(()=>{const i=e.$$.on_mount.map(Eo).filter(Ee);e.$$.on_destroy?e.$$.on_destroy.push(...i):_e(i),e.$$.on_mount=[]}),n.forEach(Me)}function De(e,t){const r=e.$$;r.fragment!==null&&(Ha(r.after_update),_e(r.on_destroy),r.fragment&&r.fragment.d(t),r.on_destroy=r.fragment=null,r.ctx=[])}function La(e,t){e.$$.dirty[0]===-1&&(Be.push(e),Ro(),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31}function Bt(e,t,r,o,n,i,a,l=[-1]){const u=tt;$e(e);const s=e.$$={fragment:null,ctx:[],props:i,update:Q,not_equal:n,bound:Cr(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(u?u.$$.context:[])),callbacks:Cr(),dirty:l,skip_bound:!1,root:t.target||u.$$.root};a&&a(s.root);let c=!1;if(s.ctx=r?r(e,t.props||{},(f,d,...p)=>{const h=p.length?p[0]:d;return s.ctx&&n(s.ctx[f],s.ctx[f]=h)&&(!s.skip_bound&&s.bound[f]&&s.bound[f](h),c&&La(e,f)),d}):[],s.update(),c=!0,_e(s.before_update),s.fragment=o?o(s.ctx):!1,t.target){if(t.hydrate){const f=xa(t.target);s.fragment&&s.fragment.l(f),f.forEach(S)}else s.fragment&&s.fragment.c();t.intro&&ee(e.$$.fragment),je(e,t.target,t.anchor),zo()}$e(u)}class Ht{$$=void 0;$$set=void 0;$destroy(){De(this,1),this.$destroy=Q}$on(t,r){if(!Ee(r))return Q;const o=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return o.push(r),()=>{const n=o.indexOf(r);n!==-1&&o.splice(n,1)}}$set(t){this.$$set&&!pa(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}const Ra="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(Ra);const Oe=[];function za(e,t){return{subscribe:be(e,t).subscribe}}function be(e,t=Q){let r;const o=new Set;function n(l){if(it(e,l)&&(e=l,r)){const u=!Oe.length;for(const s of o)s[1](),Oe.push(s,e);if(u){for(let s=0;s<Oe.length;s+=2)Oe[s][0](Oe[s+1]);Oe.length=0}}}function i(l){n(l(e))}function a(l,u=Q){const s=[l,u];return o.add(s),o.size===1&&(r=t(n,i)||Q),l(e),()=>{o.delete(s),o.size===0&&r&&(r(),r=null)}}return{set:n,update:i,subscribe:a}}function Ue(e,t,r){const o=!Array.isArray(e),n=o?[e]:e;if(!n.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const i=t.length<2;return za(r,(a,l)=>{let u=!1;const s=[];let c=0,f=Q;const d=()=>{if(c)return;f();const h=t(o?s[0]:s,a,l);i?a(h):f=Ee(h)?h:Q},p=n.map((h,_)=>So(h,v=>{s[_]=v,c&=~(1<<_),u&&d()},()=>{c|=1<<_}));return u=!0,d(),function(){_e(p),f(),u=!1}})}const ja="https://gradio.s3-us-west-2.amazonaws.com/3.50.0/assets/spaces-a79177ad.svg";var Da=function(t){return Ua(t)&&!Ga(t)};function Ua(e){return!!e&&typeof e=="object"}function Ga(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||Va(e)}var Fa=typeof Symbol=="function"&&Symbol.for,qa=Fa?Symbol.for("react.element"):60103;function Va(e){return e.$$typeof===qa}function Wa(e){return Array.isArray(e)?[]:{}}function rt(e,t){return t.clone!==!1&&t.isMergeableObject(e)?Ie(Wa(e),e,t):e}function Xa(e,t,r){return e.concat(t).map(function(o){return rt(o,r)})}function Za(e,t){if(!t.customMerge)return Ie;var r=t.customMerge(e);return typeof r=="function"?r:Ie}function Ja(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[]}function Ir(e){return Object.keys(e).concat(Ja(e))}function jo(e,t){try{return t in e}catch{return!1}}function Ya(e,t){return jo(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function Qa(e,t,r){var o={};return r.isMergeableObject(e)&&Ir(e).forEach(function(n){o[n]=rt(e[n],r)}),Ir(t).forEach(function(n){Ya(e,n)||(jo(e,n)&&r.isMergeableObject(t[n])?o[n]=Za(n,r)(e[n],t[n],r):o[n]=rt(t[n],r))}),o}function Ie(e,t,r){r=r||{},r.arrayMerge=r.arrayMerge||Xa,r.isMergeableObject=r.isMergeableObject||Da,r.cloneUnlessOtherwiseSpecified=rt;var o=Array.isArray(t),n=Array.isArray(e),i=o===n;return i?o?r.arrayMerge(e,t,r):Qa(e,t,r):rt(t,r)}Ie.all=function(t,r){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(o,n){return Ie(o,n,r)},{})};var Ka=Ie,$a=Ka;const es=bo($a);var rr=function(e,t){return rr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,o){r.__proto__=o}||function(r,o){for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(r[n]=o[n])},rr(e,t)};function Nt(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");rr(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var z=function(){return z=Object.assign||function(t){for(var r,o=1,n=arguments.length;o<n;o++){r=arguments[o];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},z.apply(this,arguments)};function ts(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r}function Ut(e,t,r){if(r||arguments.length===2)for(var o=0,n=t.length,i;o<n;o++)(i||!(o in t))&&(i||(i=Array.prototype.slice.call(t,0,o)),i[o]=t[o]);return e.concat(i||Array.prototype.slice.call(t))}var M;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(M||(M={}));var V;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(V||(V={}));var Le;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(Le||(Le={}));function Lr(e){return e.type===V.literal}function rs(e){return e.type===V.argument}function Do(e){return e.type===V.number}function Uo(e){return e.type===V.date}function Go(e){return e.type===V.time}function Fo(e){return e.type===V.select}function qo(e){return e.type===V.plural}function os(e){return e.type===V.pound}function Vo(e){return e.type===V.tag}function Wo(e){return!!(e&&typeof e=="object"&&e.type===Le.number)}function or(e){return!!(e&&typeof e=="object"&&e.type===Le.dateTime)}var Xo=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,ns=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function is(e){var t={};return e.replace(ns,function(r){var o=r.length;switch(r[0]){case"G":t.era=o===4?"long":o===5?"narrow":"short";break;case"y":t.year=o===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][o-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][o-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=o===4?"short":o===5?"narrow":"short";break;case"e":if(o<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][o-4];break;case"c":if(o<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][o-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][o-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][o-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][o-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][o-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][o-1];break;case"s":t.second=["numeric","2-digit"][o-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=o<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var as=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function ss(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(as).filter(function(d){return d.length>0}),r=[],o=0,n=t;o<n.length;o++){var i=n[o],a=i.split("/");if(a.length===0)throw new Error("Invalid number skeleton");for(var l=a[0],u=a.slice(1),s=0,c=u;s<c.length;s++){var f=c[s];if(f.length===0)throw new Error("Invalid number skeleton")}r.push({stem:l,options:u})}return r}function ls(e){return e.replace(/^(.*?)-/,"")}var Rr=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,Zo=/^(@+)?(\+|#+)?[rs]?$/g,cs=/(\*)(0+)|(#+)(0+)|(0+)/g,Jo=/^(0+)$/;function zr(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(Zo,function(r,o,n){return typeof n!="string"?(t.minimumSignificantDigits=o.length,t.maximumSignificantDigits=o.length):n==="+"?t.minimumSignificantDigits=o.length:o[0]==="#"?t.maximumSignificantDigits=o.length:(t.minimumSignificantDigits=o.length,t.maximumSignificantDigits=o.length+(typeof n=="string"?n.length:0)),""}),t}function Yo(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function us(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if(r==="+!"?(t.signDisplay="always",e=e.slice(2)):r==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!Jo.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function jr(e){var t={},r=Yo(e);return r||t}function fs(e){for(var t={},r=0,o=e;r<o.length;r++){var n=o[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=ls(n.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=z(z(z({},t),{notation:"scientific"}),n.options.reduce(function(u,s){return z(z({},u),jr(s))},{}));continue;case"engineering":t=z(z(z({},t),{notation:"engineering"}),n.options.reduce(function(u,s){return z(z({},u),jr(s))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"integer-width":if(n.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");n.options[0].replace(cs,function(u,s,c,f,d,p){if(s)t.minimumIntegerDigits=c.length;else{if(f&&d)throw new Error("We currently do not support maximum integer digits");if(p)throw new Error("We currently do not support exact integer digits")}return""});continue}if(Jo.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(Rr.test(n.stem)){if(n.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(Rr,function(u,s,c,f,d,p){return c==="*"?t.minimumFractionDigits=s.length:f&&f[0]==="#"?t.maximumFractionDigits=f.length:d&&p?(t.minimumFractionDigits=d.length,t.maximumFractionDigits=d.length+p.length):(t.minimumFractionDigits=s.length,t.maximumFractionDigits=s.length),""});var i=n.options[0];i==="w"?t=z(z({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=z(z({},t),zr(i)));continue}if(Zo.test(n.stem)){t=z(z({},t),zr(n.stem));continue}var a=Yo(n.stem);a&&(t=z(z({},t),a));var l=us(n.stem);l&&(t=z(z({},t),l))}return t}var dt={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function ds(e,t){for(var r="",o=0;o<e.length;o++){var n=e.charAt(o);if(n==="j"){for(var i=0;o+1<e.length&&e.charAt(o+1)===n;)i++,o++;var a=1+(i&1),l=i<2?1:3+(i>>1),u="a",s=hs(t);for((s=="H"||s=="k")&&(l=0);l-- >0;)r+=u;for(;a-- >0;)r=s+r}else n==="J"?r+="H":r+=n}return r}function hs(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var r=e.language,o;r!=="root"&&(o=e.maximize().region);var n=dt[o||""]||dt[r||""]||dt["".concat(r,"-001")]||dt["001"];return n[0]}var Gt,ps=new RegExp("^".concat(Xo.source,"*")),_s=new RegExp("".concat(Xo.source,"*$"));function L(e,t){return{start:e,end:t}}var gs=!!String.prototype.startsWith&&"_a".startsWith("a",1),ms=!!String.fromCodePoint,bs=!!Object.fromEntries,ys=!!String.prototype.codePointAt,vs=!!String.prototype.trimStart,ws=!!String.prototype.trimEnd,xs=!!Number.isSafeInteger,Es=xs?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},nr=!0;try{var Ss=Ko("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");nr=((Gt=Ss.exec("a"))===null||Gt===void 0?void 0:Gt[0])==="a"}catch{nr=!1}var Dr=gs?function(t,r,o){return t.startsWith(r,o)}:function(t,r,o){return t.slice(o,o+r.length)===r},ir=ms?String.fromCodePoint:function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var o="",n=t.length,i=0,a;n>i;){if(a=t[i++],a>1114111)throw RangeError(a+" is not a valid code point");o+=a<65536?String.fromCharCode(a):String.fromCharCode(((a-=65536)>>10)+55296,a%1024+56320)}return o},Ur=bs?Object.fromEntries:function(t){for(var r={},o=0,n=t;o<n.length;o++){var i=n[o],a=i[0],l=i[1];r[a]=l}return r},Qo=ys?function(t,r){return t.codePointAt(r)}:function(t,r){var o=t.length;if(!(r<0||r>=o)){var n=t.charCodeAt(r),i;return n<55296||n>56319||r+1===o||(i=t.charCodeAt(r+1))<56320||i>57343?n:(n-55296<<10)+(i-56320)+65536}},ks=vs?function(t){return t.trimStart()}:function(t){return t.replace(ps,"")},As=ws?function(t){return t.trimEnd()}:function(t){return t.replace(_s,"")};function Ko(e,t){return new RegExp(e,t)}var ar;if(nr){var Gr=Ko("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");ar=function(t,r){var o;Gr.lastIndex=r;var n=Gr.exec(t);return(o=n[1])!==null&&o!==void 0?o:""}}else ar=function(t,r){for(var o=[];;){var n=Qo(t,r);if(n===void 0||$o(n)||Bs(n))break;o.push(n),r+=n>=65536?2:1}return ir.apply(void 0,o)};var Ts=function(){function e(t,r){r===void 0&&(r={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!r.ignoreTag,this.locale=r.locale,this.requiresOtherClause=!!r.requiresOtherClause,this.shouldParseSkeletons=!!r.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,r,o){for(var n=[];!this.isEOF();){var i=this.char();if(i===123){var a=this.parseArgument(t,o);if(a.err)return a;n.push(a.val)}else{if(i===125&&t>0)break;if(i===35&&(r==="plural"||r==="selectordinal")){var l=this.clonePosition();this.bump(),n.push({type:V.pound,location:L(l,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(o)break;return this.error(M.UNMATCHED_CLOSING_TAG,L(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&sr(this.peek()||0)){var a=this.parseTag(t,r);if(a.err)return a;n.push(a.val)}else{var a=this.parseLiteral(t,r);if(a.err)return a;n.push(a.val)}}}return{val:n,err:null}},e.prototype.parseTag=function(t,r){var o=this.clonePosition();this.bump();var n=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:V.literal,value:"<".concat(n,"/>"),location:L(o,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(t+1,r,!0);if(i.err)return i;var a=i.val,l=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!sr(this.char()))return this.error(M.INVALID_TAG,L(l,this.clonePosition()));var u=this.clonePosition(),s=this.parseTagName();return n!==s?this.error(M.UNMATCHED_CLOSING_TAG,L(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:V.tag,value:n,children:a,location:L(o,this.clonePosition())},err:null}:this.error(M.INVALID_TAG,L(l,this.clonePosition())))}else return this.error(M.UNCLOSED_TAG,L(o,this.clonePosition()))}else return this.error(M.INVALID_TAG,L(o,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&Ps(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,r){for(var o=this.clonePosition(),n="";;){var i=this.tryParseQuote(r);if(i){n+=i;continue}var a=this.tryParseUnquoted(t,r);if(a){n+=a;continue}var l=this.tryParseLeftAngleBracket();if(l){n+=l;continue}break}var u=L(o,this.clonePosition());return{val:{type:V.literal,value:n,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!Os(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var r=[this.char()];for(this.bump();!this.isEOF();){var o=this.char();if(o===39)if(this.peek()===39)r.push(39),this.bump();else{this.bump();break}else r.push(o);this.bump()}return ir.apply(void 0,r)},e.prototype.tryParseUnquoted=function(t,r){if(this.isEOF())return null;var o=this.char();return o===60||o===123||o===35&&(r==="plural"||r==="selectordinal")||o===125&&t>0?null:(this.bump(),ir(o))},e.prototype.parseArgument=function(t,r){var o=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(M.EXPECT_ARGUMENT_CLOSING_BRACE,L(o,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(M.EMPTY_ARGUMENT,L(o,this.clonePosition()));var n=this.parseIdentifierIfPossible().value;if(!n)return this.error(M.MALFORMED_ARGUMENT,L(o,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(M.EXPECT_ARGUMENT_CLOSING_BRACE,L(o,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:V.argument,value:n,location:L(o,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(M.EXPECT_ARGUMENT_CLOSING_BRACE,L(o,this.clonePosition())):this.parseArgumentOptions(t,r,n,o);default:return this.error(M.MALFORMED_ARGUMENT,L(o,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),r=this.offset(),o=ar(this.message,r),n=r+o.length;this.bumpTo(n);var i=this.clonePosition(),a=L(t,i);return{value:o,location:a}},e.prototype.parseArgumentOptions=function(t,r,o,n){var i,a=this.clonePosition(),l=this.parseIdentifierIfPossible().value,u=this.clonePosition();switch(l){case"":return this.error(M.EXPECT_ARGUMENT_TYPE,L(a,u));case"number":case"date":case"time":{this.bumpSpace();var s=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),f=this.parseSimpleArgStyleIfPossible();if(f.err)return f;var d=As(f.val);if(d.length===0)return this.error(M.EXPECT_ARGUMENT_STYLE,L(this.clonePosition(),this.clonePosition()));var p=L(c,this.clonePosition());s={style:d,styleLocation:p}}var h=this.tryParseArgumentClose(n);if(h.err)return h;var _=L(n,this.clonePosition());if(s&&Dr(s?.style,"::",0)){var v=ks(s.style.slice(2));if(l==="number"){var f=this.parseNumberSkeletonFromString(v,s.styleLocation);return f.err?f:{val:{type:V.number,value:o,location:_,style:f.val},err:null}}else{if(v.length===0)return this.error(M.EXPECT_DATE_TIME_SKELETON,_);var x=v;this.locale&&(x=ds(v,this.locale));var d={type:Le.dateTime,pattern:x,location:s.styleLocation,parsedOptions:this.shouldParseSkeletons?is(x):{}},E=l==="date"?V.date:V.time;return{val:{type:E,value:o,location:_,style:d},err:null}}}return{val:{type:l==="number"?V.number:l==="date"?V.date:V.time,value:o,location:_,style:(i=s?.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var g=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(M.EXPECT_SELECT_ARGUMENT_OPTIONS,L(g,z({},g)));this.bumpSpace();var b=this.parseIdentifierIfPossible(),k=0;if(l!=="select"&&b.value==="offset"){if(!this.bumpIf(":"))return this.error(M.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,L(this.clonePosition(),this.clonePosition()));this.bumpSpace();var f=this.tryParseDecimalInteger(M.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,M.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(f.err)return f;this.bumpSpace(),b=this.parseIdentifierIfPossible(),k=f.val}var C=this.tryParsePluralOrSelectOptions(t,l,r,b);if(C.err)return C;var h=this.tryParseArgumentClose(n);if(h.err)return h;var A=L(n,this.clonePosition());return l==="select"?{val:{type:V.select,value:o,options:Ur(C.val),location:A},err:null}:{val:{type:V.plural,value:o,options:Ur(C.val),offset:k,pluralType:l==="plural"?"cardinal":"ordinal",location:A},err:null}}default:return this.error(M.INVALID_ARGUMENT_TYPE,L(a,u))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(M.EXPECT_ARGUMENT_CLOSING_BRACE,L(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,r=this.clonePosition();!this.isEOF();){var o=this.char();switch(o){case 39:{this.bump();var n=this.clonePosition();if(!this.bumpUntil("'"))return this.error(M.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,L(n,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(r.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(r.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,r){var o=[];try{o=ss(t)}catch{return this.error(M.INVALID_NUMBER_SKELETON,r)}return{val:{type:Le.number,tokens:o,location:r,parsedOptions:this.shouldParseSkeletons?fs(o):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,r,o,n){for(var i,a=!1,l=[],u=new Set,s=n.value,c=n.location;;){if(s.length===0){var f=this.clonePosition();if(r!=="select"&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(M.EXPECT_PLURAL_ARGUMENT_SELECTOR,M.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=L(f,this.clonePosition()),s=this.message.slice(f.offset,this.offset())}else break}if(u.has(s))return this.error(r==="select"?M.DUPLICATE_SELECT_ARGUMENT_SELECTOR:M.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);s==="other"&&(a=!0),this.bumpSpace();var p=this.clonePosition();if(!this.bumpIf("{"))return this.error(r==="select"?M.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:M.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,L(this.clonePosition(),this.clonePosition()));var h=this.parseMessage(t+1,r,o);if(h.err)return h;var _=this.tryParseArgumentClose(p);if(_.err)return _;l.push([s,{value:h.val,location:L(p,this.clonePosition())}]),u.add(s),this.bumpSpace(),i=this.parseIdentifierIfPossible(),s=i.value,c=i.location}return l.length===0?this.error(r==="select"?M.EXPECT_SELECT_ARGUMENT_SELECTOR:M.EXPECT_PLURAL_ARGUMENT_SELECTOR,L(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(M.MISSING_OTHER_CLAUSE,L(this.clonePosition(),this.clonePosition())):{val:l,err:null}},e.prototype.tryParseDecimalInteger=function(t,r){var o=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(o=-1);for(var i=!1,a=0;!this.isEOF();){var l=this.char();if(l>=48&&l<=57)i=!0,a=a*10+(l-48),this.bump();else break}var u=L(n,this.clonePosition());return i?(a*=o,Es(a)?{val:a,err:null}:this.error(r,u)):this.error(t,u)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var r=Qo(this.message,t);if(r===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return r},e.prototype.error=function(t,r){return{val:null,err:{kind:t,message:this.message,location:r}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(Dr(this.message,t,this.offset())){for(var r=0;r<t.length;r++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var r=this.offset(),o=this.message.indexOf(t,r);return o>=0?(this.bumpTo(o),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var r=this.offset();if(r===t)break;if(r>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&$o(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),r=this.offset(),o=this.message.charCodeAt(r+(t>=65536?2:1));return o??null},e}();function sr(e){return e>=97&&e<=122||e>=65&&e<=90}function Os(e){return sr(e)||e===47}function Ps(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function $o(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function Bs(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function lr(e){e.forEach(function(t){if(delete t.location,Fo(t)||qo(t))for(var r in t.options)delete t.options[r].location,lr(t.options[r].value);else Do(t)&&Wo(t.style)||(Uo(t)||Go(t))&&or(t.style)?delete t.style.location:Vo(t)&&lr(t.children)})}function Hs(e,t){t===void 0&&(t={}),t=z({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var r=new Ts(e,t).parse();if(r.err){var o=SyntaxError(M[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return t?.captureLocation||lr(r.val),r.val}function Ft(e,t){var r=t&&t.cache?t.cache:Rs,o=t&&t.serializer?t.serializer:Ls,n=t&&t.strategy?t.strategy:Cs;return n(e,{cache:r,serializer:o})}function Ns(e){return e==null||typeof e=="number"||typeof e=="boolean"}function en(e,t,r,o){var n=Ns(o)?o:r(o),i=t.get(n);return typeof i>"u"&&(i=e.call(this,o),t.set(n,i)),i}function tn(e,t,r){var o=Array.prototype.slice.call(arguments,3),n=r(o),i=t.get(n);return typeof i>"u"&&(i=e.apply(this,o),t.set(n,i)),i}function mr(e,t,r,o,n){return r.bind(t,e,o,n)}function Cs(e,t){var r=e.length===1?en:tn;return mr(e,this,r,t.cache.create(),t.serializer)}function Ms(e,t){return mr(e,this,tn,t.cache.create(),t.serializer)}function Is(e,t){return mr(e,this,en,t.cache.create(),t.serializer)}var Ls=function(){return JSON.stringify(arguments)};function br(){this.cache=Object.create(null)}br.prototype.get=function(e){return this.cache[e]};br.prototype.set=function(e,t){this.cache[e]=t};var Rs={create:function(){return new br}},qt={variadic:Ms,monadic:Is},Re;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(Re||(Re={}));var Ct=function(e){Nt(t,e);function t(r,o,n){var i=e.call(this,r)||this;return i.code=o,i.originalMessage=n,i}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),Fr=function(e){Nt(t,e);function t(r,o,n,i){return e.call(this,'Invalid values for "'.concat(r,'": "').concat(o,'". Options are "').concat(Object.keys(n).join('", "'),'"'),Re.INVALID_VALUE,i)||this}return t}(Ct),zs=function(e){Nt(t,e);function t(r,o,n){return e.call(this,'Value for "'.concat(r,'" must be of type ').concat(o),Re.INVALID_VALUE,n)||this}return t}(Ct),js=function(e){Nt(t,e);function t(r,o){return e.call(this,'The intl string context variable "'.concat(r,'" was not provided to the string "').concat(o,'"'),Re.MISSING_VALUE,o)||this}return t}(Ct),ce;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(ce||(ce={}));function Ds(e){return e.length<2?e:e.reduce(function(t,r){var o=t[t.length-1];return!o||o.type!==ce.literal||r.type!==ce.literal?t.push(r):o.value+=r.value,t},[])}function Us(e){return typeof e=="function"}function _t(e,t,r,o,n,i,a){if(e.length===1&&Lr(e[0]))return[{type:ce.literal,value:e[0].value}];for(var l=[],u=0,s=e;u<s.length;u++){var c=s[u];if(Lr(c)){l.push({type:ce.literal,value:c.value});continue}if(os(c)){typeof i=="number"&&l.push({type:ce.literal,value:r.getNumberFormat(t).format(i)});continue}var f=c.value;if(!(n&&f in n))throw new js(f,a);var d=n[f];if(rs(c)){(!d||typeof d=="string"||typeof d=="number")&&(d=typeof d=="string"||typeof d=="number"?String(d):""),l.push({type:typeof d=="string"?ce.literal:ce.object,value:d});continue}if(Uo(c)){var p=typeof c.style=="string"?o.date[c.style]:or(c.style)?c.style.parsedOptions:void 0;l.push({type:ce.literal,value:r.getDateTimeFormat(t,p).format(d)});continue}if(Go(c)){var p=typeof c.style=="string"?o.time[c.style]:or(c.style)?c.style.parsedOptions:o.time.medium;l.push({type:ce.literal,value:r.getDateTimeFormat(t,p).format(d)});continue}if(Do(c)){var p=typeof c.style=="string"?o.number[c.style]:Wo(c.style)?c.style.parsedOptions:void 0;p&&p.scale&&(d=d*(p.scale||1)),l.push({type:ce.literal,value:r.getNumberFormat(t,p).format(d)});continue}if(Vo(c)){var h=c.children,_=c.value,v=n[_];if(!Us(v))throw new zs(_,"function",a);var x=_t(h,t,r,o,n,i),E=v(x.map(function(k){return k.value}));Array.isArray(E)||(E=[E]),l.push.apply(l,E.map(function(k){return{type:typeof k=="string"?ce.literal:ce.object,value:k}}))}if(Fo(c)){var g=c.options[d]||c.options.other;if(!g)throw new Fr(c.value,d,Object.keys(c.options),a);l.push.apply(l,_t(g.value,t,r,o,n));continue}if(qo(c)){var g=c.options["=".concat(d)];if(!g){if(!Intl.PluralRules)throw new Ct(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,Re.MISSING_INTL_API,a);var b=r.getPluralRules(t,{type:c.pluralType}).select(d-(c.offset||0));g=c.options[b]||c.options.other}if(!g)throw new Fr(c.value,d,Object.keys(c.options),a);l.push.apply(l,_t(g.value,t,r,o,n,d-(c.offset||0)));continue}}return Ds(l)}function Gs(e,t){return t?z(z(z({},e||{}),t||{}),Object.keys(e).reduce(function(r,o){return r[o]=z(z({},e[o]),t[o]||{}),r},{})):e}function Fs(e,t){return t?Object.keys(e).reduce(function(r,o){return r[o]=Gs(e[o],t[o]),r},z({},e)):e}function Vt(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}function qs(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:Ft(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.NumberFormat).bind.apply(t,Ut([void 0],r,!1)))},{cache:Vt(e.number),strategy:qt.variadic}),getDateTimeFormat:Ft(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.DateTimeFormat).bind.apply(t,Ut([void 0],r,!1)))},{cache:Vt(e.dateTime),strategy:qt.variadic}),getPluralRules:Ft(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.PluralRules).bind.apply(t,Ut([void 0],r,!1)))},{cache:Vt(e.pluralRules),strategy:qt.variadic})}}var rn=function(){function e(t,r,o,n){r===void 0&&(r=e.defaultLocale);var i=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(u){var s=i.formatToParts(u);if(s.length===1)return s[0].value;var c=s.reduce(function(f,d){return!f.length||d.type!==ce.literal||typeof f[f.length-1]!="string"?f.push(d.value):f[f.length-1]+=d.value,f},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(u){return _t(i.ast,i.locales,i.formatters,i.formats,u,void 0,i.message)},this.resolvedOptions=function(){var u;return{locale:((u=i.resolvedLocale)===null||u===void 0?void 0:u.toString())||Intl.NumberFormat.supportedLocalesOf(i.locales)[0]}},this.getAst=function(){return i.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var a=n||{};a.formatters;var l=ts(a,["formatters"]);this.ast=e.__parse(t,z(z({},l),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=Fs(e.formats,o),this.formatters=n&&n.formatters||qs(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var r=Intl.NumberFormat.supportedLocalesOf(t);return r.length>0?new Intl.Locale(r[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=Hs,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();function Vs(e,t){if(t==null)return;if(t in e)return e[t];const r=t.split(".");let o=e;for(let n=0;n<r.length;n++)if(typeof o=="object"){if(n>0){const i=r.slice(n,r.length).join(".");if(i in o){o=o[i];break}}o=o[r[n]]}else o=void 0;return o}const ge={},Ws=(e,t,r)=>r&&(t in ge||(ge[t]={}),e in ge[t]||(ge[t][e]=r),r),on=(e,t)=>{if(t==null)return;if(t in ge&&e in ge[t])return ge[t][e];const r=lt(t);for(let o=0;o<r.length;o++){const n=r[o],i=Zs(n,e);if(i)return Ws(e,t,i)}};let yr;const st=be({});function Xs(e){return yr[e]||null}function nn(e){return e in yr}function Zs(e,t){if(!nn(e))return null;const r=Xs(e);return Vs(r,t)}function Js(e){if(e==null)return;const t=lt(e);for(let r=0;r<t.length;r++){const o=t[r];if(nn(o))return o}}function an(e,...t){delete ge[e],st.update(r=>(r[e]=es.all([r[e]||{},...t]),r))}Ue([st],([e])=>Object.keys(e));st.subscribe(e=>yr=e);const gt={};function Ys(e,t){gt[e].delete(t),gt[e].size===0&&delete gt[e]}function sn(e){return gt[e]}function Qs(e){return lt(e).map(t=>{const r=sn(t);return[t,r?[...r]:[]]}).filter(([,t])=>t.length>0)}function kt(e){return e==null?!1:lt(e).some(t=>{var r;return(r=sn(t))==null?void 0:r.size})}function Ks(e,t){return Promise.all(t.map(o=>(Ys(e,o),o().then(n=>n.default||n)))).then(o=>an(e,...o))}const Ye={};function ln(e){if(!kt(e))return e in Ye?Ye[e]:Promise.resolve();const t=Qs(e);return Ye[e]=Promise.all(t.map(([r,o])=>Ks(r,o))).then(()=>{if(kt(e))return ln(e);delete Ye[e]}),Ye[e]}var qr=Object.getOwnPropertySymbols,$s=Object.prototype.hasOwnProperty,el=Object.prototype.propertyIsEnumerable,tl=(e,t)=>{var r={};for(var o in e)$s.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&qr)for(var o of qr(e))t.indexOf(o)<0&&el.call(e,o)&&(r[o]=e[o]);return r};const rl={number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};function ol({locale:e,id:t}){console.warn(`[svelte-i18n] The message "${t}" was not found in "${lt(e).join('", "')}".${kt(ye())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const nl={fallbackLocale:null,loadingDelay:200,formats:rl,warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0},Ke=nl;function ze(){return Ke}function il(e){const t=e,{formats:r}=t,o=tl(t,["formats"]);let n=e.fallbackLocale;if(e.initialLocale)try{rn.resolveLocale(e.initialLocale)&&(n=e.initialLocale)}catch{console.warn(`[svelte-i18n] The initial locale "${e.initialLocale}" is not a valid locale.`)}return o.warnOnMissingMessages&&(delete o.warnOnMissingMessages,o.handleMissingMessage==null?o.handleMissingMessage=ol:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign(Ke,o,{initialLocale:n}),r&&("number"in r&&Object.assign(Ke.formats.number,r.number),"date"in r&&Object.assign(Ke.formats.date,r.date),"time"in r&&Object.assign(Ke.formats.time,r.time)),Ge.set(n)}const Wt=be(!1);var al=Object.defineProperty,sl=Object.defineProperties,ll=Object.getOwnPropertyDescriptors,Vr=Object.getOwnPropertySymbols,cl=Object.prototype.hasOwnProperty,ul=Object.prototype.propertyIsEnumerable,Wr=(e,t,r)=>t in e?al(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,fl=(e,t)=>{for(var r in t||(t={}))cl.call(t,r)&&Wr(e,r,t[r]);if(Vr)for(var r of Vr(t))ul.call(t,r)&&Wr(e,r,t[r]);return e},dl=(e,t)=>sl(e,ll(t));let cr;const At=be(null);function Xr(e){return e.split("-").map((t,r,o)=>o.slice(0,r+1).join("-")).reverse()}function lt(e,t=ze().fallbackLocale){const r=Xr(e);return t?[...new Set([...r,...Xr(t)])]:r}function ye(){return cr??void 0}At.subscribe(e=>{cr=e??void 0,typeof window<"u"&&e!=null&&document.documentElement.setAttribute("lang",e)});const hl=e=>{if(e&&Js(e)&&kt(e)){const{loadingDelay:t}=ze();let r;return typeof window<"u"&&ye()!=null&&t?r=window.setTimeout(()=>Wt.set(!0),t):Wt.set(!0),ln(e).then(()=>{At.set(e)}).finally(()=>{clearTimeout(r),Wt.set(!1)})}return At.set(e)},Ge=dl(fl({},At),{set:hl}),pl=()=>typeof window>"u"?null:window.navigator.language||window.navigator.languages[0],Mt=e=>{const t=Object.create(null);return o=>{const n=JSON.stringify(o);return n in t?t[n]:t[n]=e(o)}};var _l=Object.defineProperty,Tt=Object.getOwnPropertySymbols,cn=Object.prototype.hasOwnProperty,un=Object.prototype.propertyIsEnumerable,Zr=(e,t,r)=>t in e?_l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,vr=(e,t)=>{for(var r in t||(t={}))cn.call(t,r)&&Zr(e,r,t[r]);if(Tt)for(var r of Tt(t))un.call(t,r)&&Zr(e,r,t[r]);return e},Fe=(e,t)=>{var r={};for(var o in e)cn.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&Tt)for(var o of Tt(e))t.indexOf(o)<0&&un.call(e,o)&&(r[o]=e[o]);return r};const ot=(e,t)=>{const{formats:r}=ze();if(e in r&&t in r[e])return r[e][t];throw new Error(`[svelte-i18n] Unknown "${t}" ${e} format.`)},gl=Mt(e=>{var t=e,{locale:r,format:o}=t,n=Fe(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return o&&(n=ot("number",o)),new Intl.NumberFormat(r,n)}),ml=Mt(e=>{var t=e,{locale:r,format:o}=t,n=Fe(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return o?n=ot("date",o):Object.keys(n).length===0&&(n=ot("date","short")),new Intl.DateTimeFormat(r,n)}),bl=Mt(e=>{var t=e,{locale:r,format:o}=t,n=Fe(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return o?n=ot("time",o):Object.keys(n).length===0&&(n=ot("time","short")),new Intl.DateTimeFormat(r,n)}),yl=(e={})=>{var t=e,{locale:r=ye()}=t,o=Fe(t,["locale"]);return gl(vr({locale:r},o))},vl=(e={})=>{var t=e,{locale:r=ye()}=t,o=Fe(t,["locale"]);return ml(vr({locale:r},o))},wl=(e={})=>{var t=e,{locale:r=ye()}=t,o=Fe(t,["locale"]);return bl(vr({locale:r},o))},xl=Mt((e,t=ye())=>new rn(e,t,ze().formats,{ignoreTag:ze().ignoreTag})),El=(e,t={})=>{var r,o,n,i;let a=t;typeof e=="object"&&(a=e,e=a.id);const{values:l,locale:u=ye(),default:s}=a;if(u==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let c=on(e,u);if(!c)c=(i=(n=(o=(r=ze()).handleMissingMessage)==null?void 0:o.call(r,{locale:u,id:e,defaultValue:s}))!=null?n:s)!=null?i:e;else if(typeof c!="string")return console.warn(`[svelte-i18n] Message with id "${e}" must be of type "string", found: "${typeof c}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),c;if(!l)return c;let f=c;try{f=xl(c,u).format(l)}catch(d){d instanceof Error&&console.warn(`[svelte-i18n] Message "${e}" has syntax error:`,d.message)}return f},Sl=(e,t)=>wl(t).format(e),kl=(e,t)=>vl(t).format(e),Al=(e,t)=>yl(t).format(e),Tl=(e,t=ye())=>on(e,t),wr=Ue([Ge,st],()=>El);Ue([Ge],()=>Sl);Ue([Ge],()=>kl);Ue([Ge],()=>Al);Ue([Ge,st],()=>Tl);function Jr(e){let t,r,o,n,i,a,l,u=e[8]("common.built_with")+"",s,c,f,d,p,h,_=e[8]("common.hosted_on")+"",v,x,E,g,b,k,C;return{c(){t=j("div"),r=j("span"),o=j("a"),n=I(e[4]),a=te(),l=j("span"),s=I(u),c=te(),f=j("a"),f.textContent="Gradio",d=I("."),p=te(),h=j("span"),v=I(_),x=te(),E=j("a"),g=j("span"),b=j("img"),C=I(" Spaces"),y(o,"href",i="https://huggingface.co/spaces/"+e[4]),y(o,"class","title svelte-1kyws56"),y(r,"class","svelte-1kyws56"),y(f,"class","gradio svelte-1kyws56"),y(f,"href","https://gradio.app"),y(l,"class","svelte-1kyws56"),ha(b.src,k=ja)||y(b,"src",k),y(b,"alt","Hugging Face Space"),y(b,"class","svelte-1kyws56"),y(g,"class","space-logo svelte-1kyws56"),y(E,"class","hf svelte-1kyws56"),y(E,"href","https://huggingface.co/spaces"),y(h,"class","svelte-1kyws56"),y(t,"class","info svelte-1kyws56")},m(A,H){T(A,t,H),O(t,r),O(r,o),O(o,n),O(t,a),O(t,l),O(l,s),O(l,c),O(l,f),O(l,d),O(t,p),O(t,h),O(h,v),O(h,x),O(h,E),O(E,g),O(g,b),O(E,C)},p(A,H){H&16&&re(n,A[4]),H&16&&i!==(i="https://huggingface.co/spaces/"+A[4])&&y(o,"href",i),H&256&&u!==(u=A[8]("common.built_with")+"")&&re(s,u),H&256&&_!==(_=A[8]("common.hosted_on")+"")&&re(v,_)},d(A){A&&S(t)}}}function Ol(e){let t,r,o,n,i;const a=e[10].default,l=ko(a,e,e[9],null);let u=e[5]&&e[4]&&e[6]&&Jr(e);return{c(){t=j("div"),r=j("div"),l&&l.c(),o=te(),u&&u.c(),y(r,"class","main svelte-1kyws56"),y(t,"class",n="gradio-container gradio-container-"+e[1]+" svelte-1kyws56"),y(t,"data-iframe-height",""),J(t,"app",!e[5]&&!e[3]),J(t,"embed-container",e[5]),J(t,"with-info",e[6]),oe(t,"min-height",e[7]?"initial":e[2]),oe(t,"flex-grow",e[5]?"auto":"1")},m(s,c){T(s,t,c),O(t,r),l&&l.m(r,null),O(t,o),u&&u.m(t,null),e[11](t),i=!0},p(s,[c]){l&&l.p&&(!i||c&512)&&Oo(l,a,s,s[9],i?To(a,s[9],c,null):Po(s[9]),null),s[5]&&s[4]&&s[6]?u?u.p(s,c):(u=Jr(s),u.c(),u.m(t,null)):u&&(u.d(1),u=null),(!i||c&2&&n!==(n="gradio-container gradio-container-"+s[1]+" svelte-1kyws56"))&&y(t,"class",n),(!i||c&42)&&J(t,"app",!s[5]&&!s[3]),(!i||c&34)&&J(t,"embed-container",s[5]),(!i||c&66)&&J(t,"with-info",s[6]),c&132&&oe(t,"min-height",s[7]?"initial":s[2]),c&32&&oe(t,"flex-grow",s[5]?"auto":"1")},i(s){i||(ee(l,s),i=!0)},o(s){ne(l,s),i=!1},d(s){s&&S(t),l&&l.d(s),u&&u.d(),e[11](null)}}}function Pl(e,t,r){let o;xe(e,wr,_=>r(8,o=_));let{$$slots:n={},$$scope:i}=t,{wrapper:a}=t,{version:l}=t,{initial_height:u}=t,{is_embed:s}=t,{space:c}=t,{display:f}=t,{info:d}=t,{loaded:p}=t;function h(_){me[_?"unshift":"push"](()=>{a=_,r(0,a)})}return e.$$set=_=>{"wrapper"in _&&r(0,a=_.wrapper),"version"in _&&r(1,l=_.version),"initial_height"in _&&r(2,u=_.initial_height),"is_embed"in _&&r(3,s=_.is_embed),"space"in _&&r(4,c=_.space),"display"in _&&r(5,f=_.display),"info"in _&&r(6,d=_.info),"loaded"in _&&r(7,p=_.loaded),"$$scope"in _&&r(9,i=_.$$scope)},[a,l,u,s,c,f,d,p,o,i,n,h]}class Bl extends Ht{constructor(t){super(),Bt(this,t,Pl,Ol,it,{wrapper:0,version:1,initial_height:2,is_embed:3,space:4,display:5,info:6,loaded:7})}}function He(e){let t=["","k","M","G","T","P","E","Z"],r=0;for(;e>1e3&&r<t.length-1;)e/=1e3,r++;let o=t[r];return(Number.isInteger(e)?e:e.toFixed(1))+o}function zu(){const e=be({}),t=[],r=[],o=new Map,n=new Map,i=new Map,a=[];function l({fn_index:s,status:c,queue:f=!0,size:d,position:p=null,eta:h=null,message:_=null,progress:v}){const x=r[s],E=t[s],g=a[s],b=x.map(k=>{let C;const A=o.get(k)||0;if(g==="pending"&&c!=="pending"){let H=A-1;o.set(k,H<0?0:H),C=H>0?"pending":c}else g==="pending"&&c==="pending"?C="pending":g!=="pending"&&c==="pending"?(C="pending",o.set(k,A+1)):C=c;return{id:k,queue_position:p,queue_size:d,eta:h,status:C,message:_,progress:v}});E.forEach(k=>{const C=n.get(k)||0;if(g==="pending"&&c!=="pending"){let A=C-1;n.set(k,A<0?0:A),i.set(k,c)}else g!=="pending"&&c==="pending"?(n.set(k,C+1),i.set(k,c)):i.delete(k)}),e.update(k=>(b.forEach(({id:C,queue_position:A,queue_size:H,eta:U,status:X,message:fe,progress:W})=>{k[C]={queue:f,queue_size:H,queue_position:A,eta:U,message:fe,progress:W,status:X,fn_index:s}}),k)),a[s]=c}function u(s,c,f){t[s]=c,r[s]=f}return{update:l,register:u,subscribe:e.subscribe,get_status_for_fn(s){return a[s]},get_inputs_to_update(){return i}}}const Hl=be({autoscroll:!1});function Yr(e){return Object.prototype.toString.call(e)==="[object Date]"}function ur(e,t,r,o){if(typeof r=="number"||Yr(r)){const n=o-r,i=(r-t)/(e.dt||1/60),a=e.opts.stiffness*n,l=e.opts.damping*i,u=(a-l)*e.inv_mass,s=(i+u)*e.dt;return Math.abs(s)<e.opts.precision&&Math.abs(n)<e.opts.precision?o:(e.settled=!1,Yr(r)?new Date(r.getTime()+s):r+s)}else{if(Array.isArray(r))return r.map((n,i)=>ur(e,t[i],r[i],o[i]));if(typeof r=="object"){const n={};for(const i in r)n[i]=ur(e,t[i],r[i],o[i]);return n}else throw new Error(`Cannot spring ${typeof r} values`)}}function Qr(e,t={}){const r=be(e),{stiffness:o=.15,damping:n=.8,precision:i=.01}=t;let a,l,u,s=e,c=e,f=1,d=0,p=!1;function h(v,x={}){c=v;const E=u={};return e==null||x.hard||_.stiffness>=1&&_.damping>=1?(p=!0,a=et(),s=v,r.set(e=c),Promise.resolve()):(x.soft&&(d=1/((x.soft===!0?.5:+x.soft)*60),f=0),l||(a=et(),p=!1,l=Pt(g=>{if(p)return p=!1,l=null,!1;f=Math.min(f+d,1);const b={inv_mass:f,opts:_,settled:!0,dt:(g-a)*60/1e3},k=ur(b,s,e,c);return a=g,s=e,r.set(e=k),b.settled&&(l=null),!b.settled})),new Promise(g=>{l.promise.then(()=>{E===u&&g()})}))}const _={set:h,update:(v,x)=>h(v(c,e),x),subscribe:r.subscribe,stiffness:o,damping:n,precision:i};return _}function Nl(e){let t,r,o,n,i,a,l,u,s,c,f,d;return{c(){t=j("div"),r=he("svg"),o=he("g"),n=he("path"),i=he("path"),a=he("path"),l=he("path"),u=he("g"),s=he("path"),c=he("path"),f=he("path"),d=he("path"),y(n,"d","M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z"),y(n,"fill","#FF7C00"),y(n,"fill-opacity","0.4"),y(n,"class","svelte-zyxd38"),y(i,"d","M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z"),y(i,"fill","#FF7C00"),y(i,"class","svelte-zyxd38"),y(a,"d","M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z"),y(a,"fill","#FF7C00"),y(a,"fill-opacity","0.4"),y(a,"class","svelte-zyxd38"),y(l,"d","M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z"),y(l,"fill","#FF7C00"),y(l,"class","svelte-zyxd38"),oe(o,"transform","translate("+e[1][0]+"px, "+e[1][1]+"px)"),y(s,"d","M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z"),y(s,"fill","#FF7C00"),y(s,"fill-opacity","0.4"),y(s,"class","svelte-zyxd38"),y(c,"d","M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z"),y(c,"fill","#FF7C00"),y(c,"class","svelte-zyxd38"),y(f,"d","M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z"),y(f,"fill","#FF7C00"),y(f,"fill-opacity","0.4"),y(f,"class","svelte-zyxd38"),y(d,"d","M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z"),y(d,"fill","#FF7C00"),y(d,"class","svelte-zyxd38"),oe(u,"transform","translate("+e[2][0]+"px, "+e[2][1]+"px)"),y(r,"viewBox","-1200 -1200 3000 3000"),y(r,"fill","none"),y(r,"xmlns","http://www.w3.org/2000/svg"),y(r,"class","svelte-zyxd38"),y(t,"class","svelte-zyxd38"),J(t,"margin",e[0])},m(p,h){T(p,t,h),O(t,r),O(r,o),O(o,n),O(o,i),O(o,a),O(o,l),O(r,u),O(u,s),O(u,c),O(u,f),O(u,d)},p(p,[h]){h&2&&oe(o,"transform","translate("+p[1][0]+"px, "+p[1][1]+"px)"),h&4&&oe(u,"transform","translate("+p[2][0]+"px, "+p[2][1]+"px)"),h&1&&J(t,"margin",p[0])},i:Q,o:Q,d(p){p&&S(t)}}}function Cl(e,t,r){let o,n,{margin:i=!0}=t;const a=Qr([0,0]);xe(e,a,d=>r(1,o=d));const l=Qr([0,0]);xe(e,l,d=>r(2,n=d));let u;async function s(){await Promise.all([a.set([125,140]),l.set([-125,-140])]),await Promise.all([a.set([-125,140]),l.set([125,-140])]),await Promise.all([a.set([-125,0]),l.set([125,-0])]),await Promise.all([a.set([125,0]),l.set([-125,0])])}async function c(){await s(),u||c()}async function f(){await Promise.all([a.set([125,0]),l.set([-125,0])]),c()}return Qt(()=>(f(),()=>u=!0)),e.$$set=d=>{"margin"in d&&r(0,i=d.margin)},[i,o,n,a,l]}class Ml extends Ht{constructor(t){super(),Bt(this,t,Cl,Nl,it,{margin:0})}}const Il=e=>({}),Kr=e=>({});function $r(e,t,r){const o=e.slice();return o[38]=t[r],o[40]=r,o}function eo(e,t,r){const o=e.slice();return o[38]=t[r],o}function Ll(e){let t,r=e[20]("common.error")+"",o,n,i;const a=e[29].error,l=ko(a,e,e[28],Kr);return{c(){t=j("span"),o=I(r),n=te(),l&&l.c(),y(t,"class","error svelte-119qaqt")},m(u,s){T(u,t,s),O(t,o),T(u,n,s),l&&l.m(u,s),i=!0},p(u,s){(!i||s[0]&1048576)&&r!==(r=u[20]("common.error")+"")&&re(o,r),l&&l.p&&(!i||s[0]&268435456)&&Oo(l,a,u,u[28],i?To(a,u[28],s,Il):Po(u[28]),Kr)},i(u){i||(ee(l,u),i=!0)},o(u){ne(l,u),i=!1},d(u){u&&(S(t),S(n)),l&&l.d(u)}}}function Rl(e){let t,r,o,n,i,a,l,u,s,c=e[7]==="default"&&e[17]&&e[5]==="full"&&to(e);function f(g,b){if(g[6])return Dl;if(g[1]!==null&&g[2]!==void 0&&g[1]>=0)return jl;if(g[1]===0)return zl}let d=f(e),p=d&&d(e),h=e[4]&&no(e);const _=[ql,Fl],v=[];function x(g,b){return g[14]!=null?0:g[5]==="full"?1:-1}~(i=x(e))&&(a=v[i]=_[i](e));let E=!e[4]&&fo(e);return{c(){c&&c.c(),t=te(),r=j("div"),p&&p.c(),o=te(),h&&h.c(),n=te(),a&&a.c(),l=te(),E&&E.c(),u=Se(),y(r,"class","progress-text svelte-119qaqt"),J(r,"meta-text-center",e[7]==="center"),J(r,"meta-text",e[7]==="default")},m(g,b){c&&c.m(g,b),T(g,t,b),T(g,r,b),p&&p.m(r,null),O(r,o),h&&h.m(r,null),T(g,n,b),~i&&v[i].m(g,b),T(g,l,b),E&&E.m(g,b),T(g,u,b),s=!0},p(g,b){g[7]==="default"&&g[17]&&g[5]==="full"?c?c.p(g,b):(c=to(g),c.c(),c.m(t.parentNode,t)):c&&(c.d(1),c=null),d===(d=f(g))&&p?p.p(g,b):(p&&p.d(1),p=d&&d(g),p&&(p.c(),p.m(r,o))),g[4]?h?h.p(g,b):(h=no(g),h.c(),h.m(r,null)):h&&(h.d(1),h=null),(!s||b[0]&128)&&J(r,"meta-text-center",g[7]==="center"),(!s||b[0]&128)&&J(r,"meta-text",g[7]==="default");let k=i;i=x(g),i===k?~i&&v[i].p(g,b):(a&&(xt(),ne(v[k],1,1,()=>{v[k]=null}),Et()),~i?(a=v[i],a?a.p(g,b):(a=v[i]=_[i](g),a.c()),ee(a,1),a.m(l.parentNode,l)):a=null),g[4]?E&&(E.d(1),E=null):E?E.p(g,b):(E=fo(g),E.c(),E.m(u.parentNode,u))},i(g){s||(ee(a),s=!0)},o(g){ne(a),s=!1},d(g){g&&(S(t),S(r),S(n),S(l),S(u)),c&&c.d(g),p&&p.d(),h&&h.d(),~i&&v[i].d(g),E&&E.d(g)}}}function to(e){let t,r=`translateX(${(e[16]||0)*100-100}%)`;return{c(){t=j("div"),y(t,"class","eta-bar svelte-119qaqt"),oe(t,"transform",r)},m(o,n){T(o,t,n)},p(o,n){n[0]&65536&&r!==(r=`translateX(${(o[16]||0)*100-100}%)`)&&oe(t,"transform",r)},d(o){o&&S(t)}}}function zl(e){let t;return{c(){t=I("processing |")},m(r,o){T(r,t,o)},p:Q,d(r){r&&S(t)}}}function jl(e){let t,r=e[1]+1+"",o,n,i,a;return{c(){t=I("queue: "),o=I(r),n=I("/"),i=I(e[2]),a=I(" |")},m(l,u){T(l,t,u),T(l,o,u),T(l,n,u),T(l,i,u),T(l,a,u)},p(l,u){u[0]&2&&r!==(r=l[1]+1+"")&&re(o,r),u[0]&4&&re(i,l[2])},d(l){l&&(S(t),S(o),S(n),S(i),S(a))}}}function Dl(e){let t,r=St(e[6]),o=[];for(let n=0;n<r.length;n+=1)o[n]=oo(eo(e,r,n));return{c(){for(let n=0;n<o.length;n+=1)o[n].c();t=Se()},m(n,i){for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(n,i);T(n,t,i)},p(n,i){if(i[0]&64){r=St(n[6]);let a;for(a=0;a<r.length;a+=1){const l=eo(n,r,a);o[a]?o[a].p(l,i):(o[a]=oo(l),o[a].c(),o[a].m(t.parentNode,t))}for(;a<o.length;a+=1)o[a].d(1);o.length=r.length}},d(n){n&&S(t),Co(o,n)}}}function ro(e){let t,r=e[38].unit+"",o,n,i=" ",a;function l(c,f){return c[38].length!=null?Gl:Ul}let u=l(e),s=u(e);return{c(){s.c(),t=te(),o=I(r),n=I(" | "),a=I(i)},m(c,f){s.m(c,f),T(c,t,f),T(c,o,f),T(c,n,f),T(c,a,f)},p(c,f){u===(u=l(c))&&s?s.p(c,f):(s.d(1),s=u(c),s&&(s.c(),s.m(t.parentNode,t))),f[0]&64&&r!==(r=c[38].unit+"")&&re(o,r)},d(c){c&&(S(t),S(o),S(n),S(a)),s.d(c)}}}function Ul(e){let t=He(e[38].index||0)+"",r;return{c(){r=I(t)},m(o,n){T(o,r,n)},p(o,n){n[0]&64&&t!==(t=He(o[38].index||0)+"")&&re(r,t)},d(o){o&&S(r)}}}function Gl(e){let t=He(e[38].index||0)+"",r,o,n=He(e[38].length)+"",i;return{c(){r=I(t),o=I("/"),i=I(n)},m(a,l){T(a,r,l),T(a,o,l),T(a,i,l)},p(a,l){l[0]&64&&t!==(t=He(a[38].index||0)+"")&&re(r,t),l[0]&64&&n!==(n=He(a[38].length)+"")&&re(i,n)},d(a){a&&(S(r),S(o),S(i))}}}function oo(e){let t,r=e[38].index!=null&&ro(e);return{c(){r&&r.c(),t=Se()},m(o,n){r&&r.m(o,n),T(o,t,n)},p(o,n){o[38].index!=null?r?r.p(o,n):(r=ro(o),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},d(o){o&&S(t),r&&r.d(o)}}}function no(e){let t,r=e[0]?`/${e[18]}`:"",o,n;return{c(){t=I(e[19]),o=I(r),n=I("s")},m(i,a){T(i,t,a),T(i,o,a),T(i,n,a)},p(i,a){a[0]&524288&&re(t,i[19]),a[0]&262145&&r!==(r=i[0]?`/${i[18]}`:"")&&re(o,r)},d(i){i&&(S(t),S(o),S(n))}}}function Fl(e){let t,r;return t=new Ml({props:{margin:e[7]==="default"}}),{c(){at(t.$$.fragment)},m(o,n){je(t,o,n),r=!0},p(o,n){const i={};n[0]&128&&(i.margin=o[7]==="default"),t.$set(i)},i(o){r||(ee(t.$$.fragment,o),r=!0)},o(o){ne(t.$$.fragment,o),r=!1},d(o){De(t,o)}}}function ql(e){let t,r,o,n,i,a=`${e[14]*100}%`,l=e[6]!=null&&io(e);return{c(){t=j("div"),r=j("div"),l&&l.c(),o=te(),n=j("div"),i=j("div"),y(r,"class","progress-level-inner svelte-119qaqt"),y(i,"class","progress-bar svelte-119qaqt"),oe(i,"width",a),y(n,"class","progress-bar-wrap svelte-119qaqt"),y(t,"class","progress-level svelte-119qaqt")},m(u,s){T(u,t,s),O(t,r),l&&l.m(r,null),O(t,o),O(t,n),O(n,i),e[30](i)},p(u,s){u[6]!=null?l?l.p(u,s):(l=io(u),l.c(),l.m(r,null)):l&&(l.d(1),l=null),s[0]&16384&&a!==(a=`${u[14]*100}%`)&&oe(i,"width",a)},i:Q,o:Q,d(u){u&&S(t),l&&l.d(),e[30](null)}}}function io(e){let t,r=St(e[6]),o=[];for(let n=0;n<r.length;n+=1)o[n]=uo($r(e,r,n));return{c(){for(let n=0;n<o.length;n+=1)o[n].c();t=Se()},m(n,i){for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(n,i);T(n,t,i)},p(n,i){if(i[0]&8256){r=St(n[6]);let a;for(a=0;a<r.length;a+=1){const l=$r(n,r,a);o[a]?o[a].p(l,i):(o[a]=uo(l),o[a].c(),o[a].m(t.parentNode,t))}for(;a<o.length;a+=1)o[a].d(1);o.length=r.length}},d(n){n&&S(t),Co(o,n)}}}function ao(e){let t,r,o,n,i=e[40]!==0&&Vl(),a=e[38].desc!=null&&so(e),l=e[38].desc!=null&&e[13]&&e[13][e[40]]!=null&&lo(),u=e[13]!=null&&co(e);return{c(){i&&i.c(),t=te(),a&&a.c(),r=te(),l&&l.c(),o=te(),u&&u.c(),n=Se()},m(s,c){i&&i.m(s,c),T(s,t,c),a&&a.m(s,c),T(s,r,c),l&&l.m(s,c),T(s,o,c),u&&u.m(s,c),T(s,n,c)},p(s,c){s[38].desc!=null?a?a.p(s,c):(a=so(s),a.c(),a.m(r.parentNode,r)):a&&(a.d(1),a=null),s[38].desc!=null&&s[13]&&s[13][s[40]]!=null?l||(l=lo(),l.c(),l.m(o.parentNode,o)):l&&(l.d(1),l=null),s[13]!=null?u?u.p(s,c):(u=co(s),u.c(),u.m(n.parentNode,n)):u&&(u.d(1),u=null)},d(s){s&&(S(t),S(r),S(o),S(n)),i&&i.d(s),a&&a.d(s),l&&l.d(s),u&&u.d(s)}}}function Vl(e){let t;return{c(){t=I(" /")},m(r,o){T(r,t,o)},d(r){r&&S(t)}}}function so(e){let t=e[38].desc+"",r;return{c(){r=I(t)},m(o,n){T(o,r,n)},p(o,n){n[0]&64&&t!==(t=o[38].desc+"")&&re(r,t)},d(o){o&&S(r)}}}function lo(e){let t;return{c(){t=I("-")},m(r,o){T(r,t,o)},d(r){r&&S(t)}}}function co(e){let t=(100*(e[13][e[40]]||0)).toFixed(1)+"",r,o;return{c(){r=I(t),o=I("%")},m(n,i){T(n,r,i),T(n,o,i)},p(n,i){i[0]&8192&&t!==(t=(100*(n[13][n[40]]||0)).toFixed(1)+"")&&re(r,t)},d(n){n&&(S(r),S(o))}}}function uo(e){let t,r=(e[38].desc!=null||e[13]&&e[13][e[40]]!=null)&&ao(e);return{c(){r&&r.c(),t=Se()},m(o,n){r&&r.m(o,n),T(o,t,n)},p(o,n){o[38].desc!=null||o[13]&&o[13][o[40]]!=null?r?r.p(o,n):(r=ao(o),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},d(o){o&&S(t),r&&r.d(o)}}}function fo(e){let t,r;return{c(){t=j("p"),r=I(e[8]),y(t,"class","loading svelte-119qaqt")},m(o,n){T(o,t,n),O(t,r)},p(o,n){n[0]&256&&re(r,o[8])},d(o){o&&S(t)}}}function Wl(e){let t,r,o,n,i;const a=[Rl,Ll],l=[];function u(s,c){return s[3]==="pending"?0:s[3]==="error"?1:-1}return~(r=u(e))&&(o=l[r]=a[r](e)),{c(){t=j("div"),o&&o.c(),y(t,"class",n="wrap "+e[7]+" "+e[5]+" svelte-119qaqt"),J(t,"hide",!e[3]||e[3]==="complete"||e[5]==="hidden"),J(t,"translucent",e[7]==="center"&&(e[3]==="pending"||e[3]==="error")||e[10]||e[5]==="minimal"),J(t,"generating",e[3]==="generating"),J(t,"border",e[11]),oe(t,"position",e[9]?"absolute":"static"),oe(t,"padding",e[9]?"0":"var(--size-8) 0")},m(s,c){T(s,t,c),~r&&l[r].m(t,null),e[31](t),i=!0},p(s,c){let f=r;r=u(s),r===f?~r&&l[r].p(s,c):(o&&(xt(),ne(l[f],1,1,()=>{l[f]=null}),Et()),~r?(o=l[r],o?o.p(s,c):(o=l[r]=a[r](s),o.c()),ee(o,1),o.m(t,null)):o=null),(!i||c[0]&160&&n!==(n="wrap "+s[7]+" "+s[5]+" svelte-119qaqt"))&&y(t,"class",n),(!i||c[0]&168)&&J(t,"hide",!s[3]||s[3]==="complete"||s[5]==="hidden"),(!i||c[0]&1192)&&J(t,"translucent",s[7]==="center"&&(s[3]==="pending"||s[3]==="error")||s[10]||s[5]==="minimal"),(!i||c[0]&168)&&J(t,"generating",s[3]==="generating"),(!i||c[0]&2208)&&J(t,"border",s[11]),c[0]&512&&oe(t,"position",s[9]?"absolute":"static"),c[0]&512&&oe(t,"padding",s[9]?"0":"var(--size-8) 0")},i(s){i||(ee(o),i=!0)},o(s){ne(o),i=!1},d(s){s&&S(t),~r&&l[r].d(),e[31](null)}}}let ht=[],Xt=!1;async function Xl(e,t=!0){if(!(window.__gradio_mode__==="website"||window.__gradio_mode__!=="app"&&t!==!0)){if(ht.push(e),!Xt)Xt=!0;else return;await Pa(),requestAnimationFrame(()=>{let r=[0,0];for(let o=0;o<ht.length;o++){const i=ht[o].getBoundingClientRect();(o===0||i.top+window.scrollY<=r[0])&&(r[0]=i.top+window.scrollY,r[1]=o)}window.scrollTo({top:r[0]-20,behavior:"smooth"}),Xt=!1,ht=[]})}}function Zl(e,t,r){let o,n,i;xe(e,Hl,w=>r(27,n=w)),xe(e,wr,w=>r(20,i=w));let{$$slots:a={},$$scope:l}=t,{eta:u=null}=t,{queue:s=!1}=t,{queue_position:c}=t,{queue_size:f}=t,{status:d}=t,{scroll_to_output:p=!1}=t,{timer:h=!0}=t,{show_progress:_="full"}=t,{message:v=null}=t,{progress:x=null}=t,{variant:E="default"}=t,{loading_text:g="Loading..."}=t,{absolute:b=!0}=t,{translucent:k=!1}=t,{border:C=!1}=t,A,H=!1,U=0,X=0,fe=null,W=0,de=null,P,G=null,ie=!0;const B=()=>{r(24,U=performance.now()),r(25,X=0),H=!0,K()};function K(){requestAnimationFrame(()=>{r(25,X=(performance.now()-U)/1e3),H&&K()})}function R(){r(25,X=0),H&&(H=!1)}Ta(()=>{H&&R()});let N=null;function ue(w){me[w?"unshift":"push"](()=>{G=w,r(15,G),r(6,x),r(13,de),r(14,P)})}function ae(w){me[w?"unshift":"push"](()=>{A=w,r(12,A)})}return e.$$set=w=>{"eta"in w&&r(0,u=w.eta),"queue"in w&&r(21,s=w.queue),"queue_position"in w&&r(1,c=w.queue_position),"queue_size"in w&&r(2,f=w.queue_size),"status"in w&&r(3,d=w.status),"scroll_to_output"in w&&r(22,p=w.scroll_to_output),"timer"in w&&r(4,h=w.timer),"show_progress"in w&&r(5,_=w.show_progress),"message"in w&&r(23,v=w.message),"progress"in w&&r(6,x=w.progress),"variant"in w&&r(7,E=w.variant),"loading_text"in w&&r(8,g=w.loading_text),"absolute"in w&&r(9,b=w.absolute),"translucent"in w&&r(10,k=w.translucent),"border"in w&&r(11,C=w.border),"$$scope"in w&&r(28,l=w.$$scope)},e.$$.update=()=>{e.$$.dirty[0]&85983233&&(u===null?r(0,u=fe):s&&r(0,u=(performance.now()-U)/1e3+u),u!=null&&(r(18,N=u.toFixed(1)),r(26,fe=u))),e.$$.dirty[0]&33554433&&r(16,W=u===null||u<=0||!X?null:Math.min(X/u,1)),e.$$.dirty[0]&64&&x!=null&&r(17,ie=!1),e.$$.dirty[0]&57408&&(x!=null?r(13,de=x.map(w=>{if(w.index!=null&&w.length!=null)return w.index/w.length;if(w.progress!=null)return w.progress})):r(13,de=null),de?(r(14,P=de[de.length-1]),G&&(P===0?r(15,G.style.transition="0",G):r(15,G.style.transition="150ms",G))):r(14,P=void 0)),e.$$.dirty[0]&8&&(d==="pending"?B():R()),e.$$.dirty[0]&138416136&&A&&p&&(d==="pending"||d==="complete")&&Xl(A,n.autoscroll),e.$$.dirty[0]&8388616,e.$$.dirty[0]&33554432&&r(19,o=X.toFixed(1))},[u,c,f,d,h,_,x,E,g,b,k,C,A,de,P,G,W,ie,N,o,i,s,p,v,U,X,fe,n,l,a,ue,ae]}class Jl extends Ht{constructor(t){super(),Bt(this,t,Zl,Wl,it,{eta:0,queue:21,queue_position:1,queue_size:2,status:3,scroll_to_output:22,timer:4,show_progress:5,message:23,progress:6,variant:7,loading_text:8,absolute:9,translucent:10,border:11},null,[-1,-1])}}const fn={built_with_gradio:"تم الإنشاء بإستخدام Gradio",clear:"أمسح",or:"أو",submit:"أرسل"},dn={click_to_upload:"إضغط للتحميل",drop_audio:"أسقط الملف الصوتي هنا",drop_csv:"أسقط ملف البيانات هنا",drop_file:"أسقط الملف هنا",drop_image:"أسقط الصورة هنا",drop_video:"أسقط الفيديو هنا"},Yl={common:fn,upload_text:dn},Ql=Object.freeze(Object.defineProperty({__proto__:null,common:fn,default:Yl,upload_text:dn},Symbol.toStringTag,{value:"Module"})),hn={built_with_gradio:"Construït amb gradio",clear:"Neteja",empty:"Buit",error:"Error",loading:"S'està carregant",or:"o",submit:"Envia"},pn={click_to_upload:"Feu clic per pujar",drop_audio:"Deixeu anar l'àudio aquí",drop_csv:"Deixeu anar el CSV aquí",drop_file:"Deixeu anar el fitxer aquí",drop_image:"Deixeu anar la imatge aquí",drop_video:"Deixeu anar el vídeo aquí"},Kl={common:hn,upload_text:pn},$l=Object.freeze(Object.defineProperty({__proto__:null,common:hn,default:Kl,upload_text:pn},Symbol.toStringTag,{value:"Module"})),_n={annotated_image:"وێنەی نیشانە کراو"},gn={allow_recording_access:"تکایە ڕێگە بدە بە بەکارهێنانی مایکرۆفۆنەکە بۆ تۆمارکردن.",audio:"دەنگ",record_from_microphone:"تۆمارکردن لە مایکەوە",stop_recording:"تۆمارکردن بوەستێنە"},mn={connection_can_break:"لە مۆبایلدا، پەیوەندییەکە دەکرێت بپچڕێت ئەگەر ئەم تابە چالاک نەبێت یان ئامێرەکە بچێتە دۆخی پشوو، ئەمەش شوێنی خۆت لە ڕیزدا لەدەست دەدات.",long_requests_queue:"ڕیزێکی درێژی داواکاری هەیە. ئەم سپەیسە دووباد بکە بۆی چاوەڕوان نەبیت.",lost_connection:"پەیوەندی پچڕا بەهۆی جێهێشتنی پەیج. "},bn={checkbox:"بۆکسی هەڵبژاردن",checkbox_group:"گروپی بۆکسی هەڵبژاردن"},yn={code:"کۆد"},vn={color_picker:"ڕەنگ هەڵبژاردە"},wn={built_with:"دروستکراوە لەگەڵ...",built_with_gradio:"Gradio دروستکراوە بە",clear:"خاوێنکردنەوە",download:"دابەزاندن",edit:"بژارکردن",empty:"بەتاڵ",error:"هەڵە",hosted_on:"میوانداری کراوە لە",loading:"بارکردن",logo:"لۆگۆ",or:"یان",remove:"لابردن",share:"هاوبەشکردن",submit:"پێشکەشکردن",undo:"پووچکردنەوە"},xn={incorrect_format:"فۆرماتێکی هەڵە، تەنها فایلەکانی CSV و TSV پشتگیری دەکرێن",new_column:"ستوونی نوێ",new_row:"ڕیزێکی نوێ"},En={dropdown:"فڕێدانە خوار"},Sn={build_error:"هەڵەی دروستکردن هەیە",config_error:"هەڵەی ڕێکخستن هەیە",contact_page_author:"تکایە پەیوەندی بە نووسەری پەیجەوە بکەن بۆ ئەوەی ئاگاداریان بکەنەوە.",no_app_file:"هیچ فایلێکی ئەپ نییە",runtime_error:"هەڵەیەکی runtime هەیە",space_not_working:'"سپەیسەکە کارناکات چونکە" {0}',space_paused:"فەزاکە وەستاوە",use_via_api:"لە ڕێگەی API بەکاری بهێنە"},kn={uploading:"بارکردن..."},An={highlighted_text:"دەقی ڕۆشن کراو"},Tn={allow_webcam_access:"تکایە ڕێگە بدە بە بەکارهێنانی وێبکامەکە بۆ تۆمارکردن.",brush_color:"ڕەنگی فڵچە",brush_radius:"تیژڕەوی فڵچە",image:"وێنە",remove_image:"لابردنی وێنە",select_brush_color:"ڕەنگی فڵچە هەڵبژێرە",start_drawing:"دەست بکە بە وێنەکێشان",use_brush:"فڵچە بەکاربهێنە"},On={label:"لەیبڵ"},Pn={enable_cookies:"ئەگەر تۆ سەردانی HuggingFace Space دەکەیت لە دۆخی نادیاردا، پێویستە کووکی لایەنی سێیەم چالاک بکەیت.",incorrect_credentials:"بڕوانامەی هەڵە",login:"چونه‌ ژووره‌وه‌"},Bn={number:"ژمارە"},Hn={plot:"هێڵکاری"},Nn={radio:"ڕادیۆ"},Cn={slider:"خلیسکە"},Mn={click_to_upload:"کلیک بکە بۆ بارکردن",drop_audio:"دەنگ لێرە دابنێ",drop_csv:"لێرەدا CSV دابنێ",drop_file:"فایل لێرە دابنێ",drop_image:"وێنە لێرەدا دابنێ",drop_video:"ڤیدیۆ لێرە دابنێ"},ec={"3D_model":{"3d_model":"مۆدێلی سێ ڕەهەندی"},annotated_image:_n,audio:gn,blocks:mn,checkbox:bn,code:yn,color_picker:vn,common:wn,dataframe:xn,dropdown:En,errors:Sn,file:kn,highlighted_text:An,image:Tn,label:On,login:Pn,number:Bn,plot:Hn,radio:Nn,slider:Cn,upload_text:Mn},tc=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:_n,audio:gn,blocks:mn,checkbox:bn,code:yn,color_picker:vn,common:wn,dataframe:xn,default:ec,dropdown:En,errors:Sn,file:kn,highlighted_text:An,image:Tn,label:On,login:Pn,number:Bn,plot:Hn,radio:Nn,slider:Cn,upload_text:Mn},Symbol.toStringTag,{value:"Module"})),In={built_with_gradio:"Mit Gradio erstellt",clear:"Löschen",or:"oder",submit:"Absenden"},Ln={click_to_upload:"Hochladen",drop_audio:"Audio hier ablegen",drop_csv:"CSV Datei hier ablegen",drop_file:"Datei hier ablegen",drop_image:"Bild hier ablegen",drop_video:"Video hier ablegen"},rc={common:In,upload_text:Ln},oc=Object.freeze(Object.defineProperty({__proto__:null,common:In,default:rc,upload_text:Ln},Symbol.toStringTag,{value:"Module"})),Rn={annotated_image:"Annotated Image"},zn={allow_recording_access:"Please allow access to the microphone for recording.",audio:"Audio",record_from_microphone:"Record from microphone",stop_recording:"Stop recording",no_device_support:"Media devices could not be accessed. Check that you are running on a secure origin (https) or localhost (or you have passed a valid SSL certificate to ssl_verify), and you have allowed browser access to your device."},jn={connection_can_break:"On mobile, the connection can break if this tab is unfocused or the device sleeps, losing your position in queue.",long_requests_queue:"There is a long queue of requests pending. Duplicate this Space to skip.",lost_connection:"Lost connection due to leaving page. Rejoining queue..."},Dn={checkbox:"Checkbox",checkbox_group:"Checkbox Group"},Un={code:"Code"},Gn={color_picker:"Color Picker"},Fn={built_with:"built with",built_with_gradio:"Built with Gradio",clear:"Clear",download:"Download",edit:"Edit",empty:"Empty",error:"Error",hosted_on:"Hosted on",loading:"Loading",logo:"logo",or:"or",remove:"Remove",share:"Share",submit:"Submit",undo:"Undo"},qn={incorrect_format:"Incorrect format, only CSV and TSV files are supported",new_column:"New column",new_row:"New row"},Vn={dropdown:"Dropdown"},Wn={build_error:"there is a build error",config_error:"there is a config error",contact_page_author:"Please contact the author of the page to let them know.",no_app_file:"there is no app file",runtime_error:"there is a runtime error",space_not_working:`"Space isn't working because" {0}`,space_paused:"the space is paused",use_via_api:"Use via API"},Xn={uploading:"Uploading..."},Zn={highlighted_text:"Highlighted Text"},Jn={allow_webcam_access:"Please allow access to the webcam for recording.",brush_color:"Brush color",brush_radius:"Brush radius",image:"Image",remove_image:"Remove Image",select_brush_color:"Select brush color",start_drawing:"Start drawing",use_brush:"Use brush"},Yn={label:"Label"},Qn={enable_cookies:"If you are visiting a HuggingFace Space in Incognito mode, you must enable third party cookies.",incorrect_credentials:"Incorrect Credentials",login:"Login"},Kn={number:"Number"},$n={plot:"Plot"},ei={radio:"Radio"},ti={slider:"Slider"},ri={click_to_upload:"Click to Upload",drop_audio:"Drop Audio Here",drop_csv:"Drop CSV Here",drop_file:"Drop File Here",drop_image:"Drop Image Here",drop_video:"Drop Video Here"},nc={"3D_model":{"3d_model":"3D Model"},annotated_image:Rn,audio:zn,blocks:jn,checkbox:Dn,code:Un,color_picker:Gn,common:Fn,dataframe:qn,dropdown:Vn,errors:Wn,file:Xn,highlighted_text:Zn,image:Jn,label:Yn,login:Qn,number:Kn,plot:$n,radio:ei,slider:ti,upload_text:ri},ic=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:Rn,audio:zn,blocks:jn,checkbox:Dn,code:Un,color_picker:Gn,common:Fn,dataframe:qn,default:nc,dropdown:Vn,errors:Wn,file:Xn,highlighted_text:Zn,image:Jn,label:Yn,login:Qn,number:Kn,plot:$n,radio:ei,slider:ti,upload_text:ri},Symbol.toStringTag,{value:"Module"})),oi={built_with_gradio:"Construido con Gradio",clear:"Limpiar",or:"o",submit:"Enviar"},ni={click_to_upload:"Haga click para cargar",drop_audio:"Coloque el audio aquí",drop_csv:"Coloque el CSV aquí",drop_file:"Coloque el archivo aquí",drop_image:"Coloque la imagen aquí",drop_video:"Coloque el video aquí"},ac={common:oi,upload_text:ni},sc=Object.freeze(Object.defineProperty({__proto__:null,common:oi,default:ac,upload_text:ni},Symbol.toStringTag,{value:"Module"})),ii={built_with_gradio:"Gradiorekin eraikia",clear:"Garbitu",or:"edo",submit:"Bidali"},ai={click_to_upload:"Klik egin kargatzeko",drop_audio:"Jarri hemen audioa",drop_csv:"Jarri hemen CSVa",drop_file:"Jarri hemen fitxategia",drop_image:"Jarri hemen irudia",drop_video:"Jarri hemen bideoa"},lc={common:ii,upload_text:ai},cc=Object.freeze(Object.defineProperty({__proto__:null,common:ii,default:lc,upload_text:ai},Symbol.toStringTag,{value:"Module"})),si={built_with_gradio:"ساخته شده با gradio",clear:"حذف",or:"یا",submit:"ارسال"},li={click_to_upload:"برای آپلود کلیک کنید",drop_audio:"صوت را اینجا رها کنید",drop_csv:"فایل csv را  اینجا رها کنید",drop_file:"فایل را اینجا رها کنید",drop_image:"تصویر را اینجا رها کنید",drop_video:"ویدیو را اینجا رها کنید"},uc={common:si,upload_text:li},fc=Object.freeze(Object.defineProperty({__proto__:null,common:si,default:uc,upload_text:li},Symbol.toStringTag,{value:"Module"})),ci={allow_recording_access:"Veuillez autoriser l'accès à l'enregistrement",audio:"Audio",record_from_microphone:"Enregistrer avec le microphone",stop_recording:"Arrêter l'enregistrement"},ui={built_with:"Construit avec",built_with_gradio:"Construit avec Gradio",clear:"Effacer",download:"Télécharger",edit:"Éditer",error:"Erreur",loading:"Chargement",logo:"logo",or:"ou",remove:"Supprimer",share:"Partager",submit:"Soumettre"},fi={click_to_upload:"Cliquer pour Télécharger",drop_audio:"Déposer l'Audio Ici",drop_csv:"Déposer le CSV Ici",drop_file:"Déposer le Fichier Ici",drop_image:"Déposer l'Image Ici",drop_video:"Déposer la Vidéo Ici"},dc={audio:ci,common:ui,upload_text:fi},hc=Object.freeze(Object.defineProperty({__proto__:null,audio:ci,common:ui,default:dc,upload_text:fi},Symbol.toStringTag,{value:"Module"})),di={built_with_gradio:"בנוי עם גרדיו",clear:"נקה",or:"או",submit:"שלח"},hi={click_to_upload:"לחץ כדי להעלות",drop_audio:"גרור לכאן קובץ שמע",drop_csv:"גרור csv קובץ לכאן",drop_file:"גרור קובץ לכאן",drop_image:"גרור קובץ תמונה לכאן",drop_video:"גרור קובץ סרטון לכאן"},pc={common:di,upload_text:hi},_c=Object.freeze(Object.defineProperty({__proto__:null,common:di,default:pc,upload_text:hi},Symbol.toStringTag,{value:"Module"})),pi={built_with_gradio:"Gradio से बना",clear:"हटाये",or:"या",submit:"सबमिट करे"},_i={click_to_upload:"अपलोड के लिए बटन दबायें",drop_audio:"यहाँ ऑडियो ड्रॉप करें",drop_csv:"यहाँ CSV ड्रॉप करें",drop_file:"यहाँ File ड्रॉप करें",drop_image:"यहाँ इमेज ड्रॉप करें",drop_video:"यहाँ वीडियो ड्रॉप करें"},gc={common:pi,upload_text:_i},mc=Object.freeze(Object.defineProperty({__proto__:null,common:pi,default:gc,upload_text:_i},Symbol.toStringTag,{value:"Module"})),gi={built_with_gradio:"gradioで作ろう",clear:"クリア",or:"または",submit:"送信"},mi={click_to_upload:"クリックしてアップロード",drop_audio:"ここに音声をドロップ",drop_csv:"ここにCSVをドロップ",drop_file:"ここにファイルをドロップ",drop_image:"ここに画像をドロップ",drop_video:"ここに動画をドロップ"},bc={common:gi,upload_text:mi},yc=Object.freeze(Object.defineProperty({__proto__:null,common:gi,default:bc,upload_text:mi},Symbol.toStringTag,{value:"Module"})),bi={built_with_gradio:"gradio로 제작되었습니다",clear:"클리어",or:"또는",submit:"제출하기"},yi={click_to_upload:"클릭해서 업로드하기",drop_audio:"오디오를 끌어 놓으세요",drop_csv:"CSV파일을 끌어 놓으세요",drop_file:"파일을 끌어 놓으세요",drop_image:"이미지를 끌어 놓으세요",drop_video:"비디오를 끌어 놓으세요"},vc={common:bi,upload_text:yi},wc=Object.freeze(Object.defineProperty({__proto__:null,common:bi,default:vc,upload_text:yi},Symbol.toStringTag,{value:"Module"})),vi={built_with_gradio:"sukurta su gradio",clear:"Trinti",or:"arba",submit:"Pateikti"},wi={click_to_upload:"Spustelėkite norėdami įkelti",drop_audio:"Įkelkite garso įrašą čia",drop_csv:"Įkelkite CSV čia",drop_file:"Įkelkite bylą čia",drop_image:"Įkelkite paveikslėlį čia",drop_video:"Įkelkite vaizdo įrašą čia"},xc={common:vi,upload_text:wi},Ec=Object.freeze(Object.defineProperty({__proto__:null,common:vi,default:xc,upload_text:wi},Symbol.toStringTag,{value:"Module"})),xi={built_with_gradio:"gemaakt met gradio",clear:"Wis",or:"of",submit:"Zend in"},Ei={click_to_upload:"Klik om the Uploaden",drop_audio:"Sleep een Geluidsbestand hier",drop_csv:"Sleep een CSV hier",drop_file:"Sleep een Document hier",drop_image:"Sleep een Afbeelding hier",drop_video:"Sleep een Video hier"},Sc={common:xi,upload_text:Ei},kc=Object.freeze(Object.defineProperty({__proto__:null,common:xi,default:Sc,upload_text:Ei},Symbol.toStringTag,{value:"Module"})),Si={built_with_gradio:"utworzone z gradio",clear:"Wyczyść",or:"lub",submit:"Zatwierdź"},ki={click_to_upload:"Kliknij, aby przesłać",drop_audio:"Przeciągnij tutaj audio",drop_csv:"Przeciągnij tutaj CSV",drop_file:"Przeciągnij tutaj plik",drop_image:"Przeciągnij tutaj zdjęcie",drop_video:"Przeciągnij tutaj video"},Ac={common:Si,upload_text:ki},Tc=Object.freeze(Object.defineProperty({__proto__:null,common:Si,default:Ac,upload_text:ki},Symbol.toStringTag,{value:"Module"})),Ai={built_with_gradio:"Construído com gradio",clear:"Limpar",error:"Erro",flag:"Marcar",loading:"Carregando",or:"ou",submit:"Enviar"},Ti={click_to_upload:"Clique para o Upload",drop_audio:"Solte o Áudio Aqui",drop_csv:"Solte o CSV Aqui",drop_file:"Solte o Arquivo Aqui",drop_image:"Solte a Imagem Aqui",drop_video:"Solte o Vídeo Aqui"},Oc={common:Ai,upload_text:Ti},Pc=Object.freeze(Object.defineProperty({__proto__:null,common:Ai,default:Oc,upload_text:Ti},Symbol.toStringTag,{value:"Module"})),Oi={built_with_gradio:"сделано с помощью gradio",clear:"Очистить",or:"или",submit:"Исполнить"},Pi={click_to_upload:"Нажмите, чтобы загрузить",drop_audio:"Поместите Аудио Здесь",drop_csv:"Поместите CSV Здесь",drop_file:"Поместите Документ Здесь",drop_image:"Поместите Изображение Здесь",drop_video:"Поместите Видео Здесь"},Bc={common:Oi,upload_text:Pi},Hc=Object.freeze(Object.defineProperty({__proto__:null,common:Oi,default:Bc,upload_text:Pi},Symbol.toStringTag,{value:"Module"})),Bi={built_with_gradio:"கிரேடியோ வுடன் உருவாக்கப்பட்டது",clear:"அழிக்கவும்",or:"அல்லது",submit:"சமர்ப்பிக்கவும்"},Hi={click_to_upload:"பதிவேற்ற அழுத்தவும்",drop_audio:"ஆடியோவை பதிவேற்றவும்",drop_csv:"csv ஐ பதிவேற்றவும்",drop_file:"கோப்பை பதிவேற்றவும்",drop_image:"படத்தை பதிவேற்றவும்",drop_video:"காணொளியை பதிவேற்றவும்"},Nc={common:Bi,upload_text:Hi},Cc=Object.freeze(Object.defineProperty({__proto__:null,common:Bi,default:Nc,upload_text:Hi},Symbol.toStringTag,{value:"Module"})),Ni={built_with_gradio:"Gradio ile oluşturulmuştur",clear:"Temizle",or:"veya",submit:"Yükle"},Ci={click_to_upload:"Yüklemek için Tıkla",drop_audio:"Kaydı Buraya Sürükle",drop_csv:"CSV'yi Buraya Sürükle",drop_file:"Dosyayı Buraya Sürükle",drop_image:"Resmi Buraya Sürükle",drop_video:"Videoyu Buraya Sürükle"},Mc={common:Ni,upload_text:Ci},Ic=Object.freeze(Object.defineProperty({__proto__:null,common:Ni,default:Mc,upload_text:Ci},Symbol.toStringTag,{value:"Module"})),Mi={built_with_gradio:"Зроблено на основі gradio",clear:"Очистити",or:"або",submit:"Надіслати"},Ii={click_to_upload:"Натисніть щоб завантажити",drop_audio:"Перетягніть аудіо сюди",drop_csv:"Перетягніть CSV-файл сюди",drop_file:"Перетягніть файл сюди",drop_image:"Перетягніть зображення сюди",drop_video:"Перетягніть відео сюди"},Lc={common:Mi,upload_text:Ii},Rc=Object.freeze(Object.defineProperty({__proto__:null,common:Mi,default:Lc,upload_text:Ii},Symbol.toStringTag,{value:"Module"})),Li={built_with_gradio:"کے ساتھ بنایا گیا Gradio",clear:"ہٹا دیں",or:"یا",submit:"جمع کریں"},Ri={click_to_upload:"اپ لوڈ کے لیے کلک کریں",drop_audio:"یہاں آڈیو ڈراپ کریں",drop_csv:"یہاں فائل ڈراپ کریں",drop_file:"یہاں فائل ڈراپ کریں",drop_image:"یہاں تصویر ڈراپ کریں",drop_video:"یہاں ویڈیو ڈراپ کریں"},zc={common:Li,upload_text:Ri},jc=Object.freeze(Object.defineProperty({__proto__:null,common:Li,default:zc,upload_text:Ri},Symbol.toStringTag,{value:"Module"})),zi={built_with_gradio:"gradio bilan qilingan",clear:"Tozalash",submit:"Yubor"},ji={click_to_upload:"Yuklash uchun Bosing",drop_audio:"Audioni Shu Yerga Tashlang",drop_csv:"CSVni Shu Yerga Tashlang",drop_file:"Faylni Shu Yerga Tashlang",drop_image:"Rasmni Shu Yerga Tashlang",drop_video:"Videoni Shu Yerga Tashlang"},Dc={common:zi,upload_text:ji},Uc=Object.freeze(Object.defineProperty({__proto__:null,common:zi,default:Dc,upload_text:ji},Symbol.toStringTag,{value:"Module"})),Di={built_with_gradio:"使用Gradio构建",clear:"清除",or:"或",submit:"提交"},Ui={click_to_upload:"点击上传",drop_audio:"拖放音频至此处",drop_csv:"拖放CSV至此处",drop_file:"拖放文件至此处",drop_image:"拖放图片至此处",drop_video:"拖放视频至此处"},Gc={common:Di,upload_text:Ui},Fc=Object.freeze(Object.defineProperty({__proto__:null,common:Di,default:Gc,upload_text:Ui},Symbol.toStringTag,{value:"Module"})),Gi={built_with_gradio:"使用Gradio構建",clear:"清除",or:"或",submit:"提交"},Fi={click_to_upload:"點擊上傳",drop_audio:"拖放音訊至此處",drop_csv:"拖放CSV至此處",drop_file:"拖放檔案至此處",drop_image:"拖放圖片至此處",drop_video:"拖放影片至此處"},qc={common:Gi,upload_text:Fi},Vc=Object.freeze(Object.defineProperty({__proto__:null,common:Gi,default:qc,upload_text:Fi},Symbol.toStringTag,{value:"Module"})),ho=Object.assign({"./lang/ar.json":Ql,"./lang/ca.json":$l,"./lang/ckb.json":tc,"./lang/de.json":oc,"./lang/en.json":ic,"./lang/es.json":sc,"./lang/eu.json":cc,"./lang/fa.json":fc,"./lang/fr.json":hc,"./lang/he.json":_c,"./lang/hi.json":mc,"./lang/ja.json":yc,"./lang/ko.json":wc,"./lang/lt.json":Ec,"./lang/nl.json":kc,"./lang/pl.json":Tc,"./lang/pt-BR.json":Pc,"./lang/ru.json":Hc,"./lang/ta.json":Cc,"./lang/tr.json":Ic,"./lang/uk.json":Rc,"./lang/ur.json":jc,"./lang/uz.json":Uc,"./lang/zh-CN.json":Fc,"./lang/zh-TW.json":Vc});function Wc(){let e={};for(const t in ho){const r=t.split("/").pop().split(".").shift();e[r]=ho[t].default}return e}const po=Wc();for(const e in po)an(e,po[e]);function Xc(){il({fallbackLocale:"en",initialLocale:pl()})}const qi="WORKER_PROXY_CONTEXT_KEY";function Zc(e){Io(qi,e)}function ju(){return Oa(qi)}function _o(e){let t,r;return t=new Jl({props:{absolute:!e[4],status:e[14],timer:!1,queue_position:null,queue_size:null,translucent:!0,loading_text:e[15],$$slots:{error:[Qc]},$$scope:{ctx:e}}}),{c(){at(t.$$.fragment)},m(o,n){je(t,o,n),r=!0},p(o,n){const i={};n[0]&16&&(i.absolute=!o[4]),n[0]&16384&&(i.status=o[14]),n[0]&32768&&(i.loading_text=o[15]),n[0]&2105600|n[1]&32768&&(i.$$scope={dirty:n,ctx:o}),t.$set(i)},i(o){r||(ee(t.$$.fragment,o),r=!0)},o(o){ne(t.$$.fragment,o),r=!1},d(o){De(t,o)}}}function Jc(e){let t,r=e[21]("errors.contact_page_author")+"",o;return{c(){t=j("p"),o=I(r),y(t,"class","svelte-y6l4b")},m(n,i){T(n,t,i),O(t,o)},p(n,i){i[0]&2097152&&r!==(r=n[21]("errors.contact_page_author")+"")&&re(o,r)},d(n){n&&S(t)}}}function Yc(e){let t,r,o,n,i,a;return{c(){t=j("p"),r=I("Please "),o=j("a"),n=I("contact the author of the space"),a=I(" to let them know."),y(o,"href",i="https://huggingface.co/spaces/"+e[8]+"/discussions/new?title="+e[22].title(e[13]?.detail)+"&description="+e[22].description(e[13]?.detail,location.origin)),y(o,"class","svelte-y6l4b"),y(t,"class","svelte-y6l4b")},m(l,u){T(l,t,u),O(t,r),O(t,o),O(o,n),O(t,a)},p(l,u){u[0]&8448&&i!==(i="https://huggingface.co/spaces/"+l[8]+"/discussions/new?title="+l[22].title(l[13]?.detail)+"&description="+l[22].description(l[13]?.detail,location.origin))&&y(o,"href",i)},d(l){l&&S(t)}}}function Qc(e){let t,r,o,n=(e[13]?.message||"")+"",i,a;function l(c,f){return(c[13].status==="space_error"||c[13].status==="paused")&&c[13].discussions_enabled?Yc:Jc}let u=l(e),s=u(e);return{c(){t=j("div"),r=j("p"),o=j("strong"),i=I(n),a=te(),s.c(),y(r,"class","svelte-y6l4b"),y(t,"class","error svelte-y6l4b"),y(t,"slot","error")},m(c,f){T(c,t,f),O(t,r),O(r,o),O(o,i),O(t,a),s.m(t,null)},p(c,f){f[0]&8192&&n!==(n=(c[13]?.message||"")+"")&&re(i,n),u===(u=l(c))&&s?s.p(c,f):(s.d(1),s=u(c),s&&(s.c(),s.m(t,null)))},d(c){c&&S(t),s.d()}}}function Kc(e){let t,r,o,n;const i=[{app:e[17]},e[12],{theme_mode:e[16]},{control_page_title:e[5]},{target:e[9]},{autoscroll:e[0]},{show_footer:!e[4]},{app_mode:e[3]},{version:e[1]}];function a(s){e[32](s)}function l(s){e[33](s)}let u={};for(let s=0;s<i.length;s+=1)u=xo(u,i[s]);return e[10]!==void 0&&(u.ready=e[10]),e[11]!==void 0&&(u.render_complete=e[11]),t=new e[19]({props:u}),me.push(()=>tr(t,"ready",a)),me.push(()=>tr(t,"render_complete",l)),{c(){at(t.$$.fragment)},m(s,c){je(t,s,c),n=!0},p(s,c){const f=c[0]&201275?Ca(i,[c[0]&131072&&{app:s[17]},c[0]&4096&&Ma(s[12]),c[0]&65536&&{theme_mode:s[16]},c[0]&32&&{control_page_title:s[5]},c[0]&512&&{target:s[9]},c[0]&1&&{autoscroll:s[0]},c[0]&16&&{show_footer:!s[4]},c[0]&8&&{app_mode:s[3]},c[0]&2&&{version:s[1]}]):{};!r&&c[0]&1024&&(r=!0,f.ready=s[10],er(()=>r=!1)),!o&&c[0]&2048&&(o=!0,f.render_complete=s[11],er(()=>o=!1)),t.$set(f)},i(s){n||(ee(t.$$.fragment,s),n=!0)},o(s){ne(t.$$.fragment,s),n=!1},d(s){De(t,s)}}}function $c(e){let t,r;return t=new e[20]({props:{auth_message:e[12].auth_message,root:e[12].root,space_id:e[8],app_mode:e[3]}}),{c(){at(t.$$.fragment)},m(o,n){je(t,o,n),r=!0},p(o,n){const i={};n[0]&4096&&(i.auth_message=o[12].auth_message),n[0]&4096&&(i.root=o[12].root),n[0]&256&&(i.space_id=o[8]),n[0]&8&&(i.app_mode=o[3]),t.$set(i)},i(o){r||(ee(t.$$.fragment,o),r=!0)},o(o){ne(t.$$.fragment,o),r=!1},d(o){De(t,o)}}}function eu(e){let t,r,o,n,i,a=(e[14]==="pending"||e[14]==="error")&&!(e[12]&&e[12]?.auth_required)&&_o(e);const l=[$c,Kc],u=[];function s(c,f){return c[12]?.auth_required&&c[20]?0:c[12]&&c[19]&&c[18]?1:-1}return~(r=s(e))&&(o=u[r]=l[r](e)),{c(){a&&a.c(),t=te(),o&&o.c(),n=Se()},m(c,f){a&&a.m(c,f),T(c,t,f),~r&&u[r].m(c,f),T(c,n,f),i=!0},p(c,f){(c[14]==="pending"||c[14]==="error")&&!(c[12]&&c[12]?.auth_required)?a?(a.p(c,f),f[0]&20480&&ee(a,1)):(a=_o(c),a.c(),ee(a,1),a.m(t.parentNode,t)):a&&(xt(),ne(a,1,1,()=>{a=null}),Et());let d=r;r=s(c),r===d?~r&&u[r].p(c,f):(o&&(xt(),ne(u[d],1,1,()=>{u[d]=null}),Et()),~r?(o=u[r],o?o.p(c,f):(o=u[r]=l[r](c),o.c()),ee(o,1),o.m(n.parentNode,n)):o=null)},i(c){i||(ee(a),ee(o),i=!0)},o(c){ne(a),ne(o),i=!1},d(c){c&&(S(t),S(n)),a&&a.d(c),~r&&u[r].d(c)}}}function tu(e){let t,r,o;function n(a){e[34](a)}let i={display:e[6]&&e[4],is_embed:e[4],info:!!e[8]&&e[7],version:e[1],initial_height:e[2],space:e[8],loaded:e[14]==="complete",$$slots:{default:[eu]},$$scope:{ctx:e}};return e[9]!==void 0&&(i.wrapper=e[9]),t=new Bl({props:i}),me.push(()=>tr(t,"wrapper",n)),{c(){at(t.$$.fragment)},m(a,l){je(t,a,l),o=!0},p(a,l){const u={};l[0]&80&&(u.display=a[6]&&a[4]),l[0]&16&&(u.is_embed=a[4]),l[0]&384&&(u.info=!!a[8]&&a[7]),l[0]&2&&(u.version=a[1]),l[0]&4&&(u.initial_height=a[2]),l[0]&256&&(u.space=a[8]),l[0]&16384&&(u.loaded=a[14]==="complete"),l[0]&4194107|l[1]&32768&&(u.$$scope={dirty:l,ctx:a}),!r&&l[0]&512&&(r=!0,u.wrapper=a[9],er(()=>r=!1)),t.$set(u)},i(a){o||(ee(t.$$.fragment,a),o=!0)},o(a){ne(t.$$.fragment,a),o=!1},d(a){De(t,a)}}}let ru=-1;function ou(){const e=be({}),t=new Map,r=new IntersectionObserver(n=>{n.forEach(i=>{if(i.isIntersecting){let a=t.get(i.target);a!==void 0&&e.update(l=>({...l,[a]:!0}))}})});function o(n,i){t.set(i,n),r.observe(i)}return{register:o,subscribe:e.subscribe}}const go=ou();function nu(e,t,r){let o,n;xe(e,wr,m=>r(21,o=m)),xe(e,go,m=>r(31,n=m)),Xc();let{autoscroll:i}=t,{version:a}=t,{initial_height:l}=t,{app_mode:u}=t,{is_embed:s}=t,{theme_mode:c="system"}=t,{control_page_title:f}=t,{container:d}=t,{info:p}=t,{eager:h}=t,_,{mount_css:v=Yt}=t,{client:x}=t,{upload_files:E}=t,{worker_proxy:g=void 0}=t;g&&(Zc(g),g.addEventListener("progress-update",m=>{r(15,de=m.detail+"...")}),g.addEventListener("initialization-error",m=>{const D=m.detail;r(13,R={status:"space_error",message:D.message,detail:"RUNTIME_ERROR",load_status:"error",discussions_enabled:!1})}));let{space:b}=t,{host:k}=t,{src:C}=t,A=ru++,H="pending",U,X=!1,fe=!1,W,de=o("common.loading")+"...",P;async function G(m,D){if(D){let F=document.createElement("style");F.innerHTML=D,m.appendChild(F)}await v(W.root+"/theme.css",document.head),W.stylesheets&&await Promise.all(W.stylesheets.map(F=>{let $=F.startsWith("http:")||F.startsWith("https:");return v($?F:W.root+"/"+F,document.head)}))}function ie(m){let F=new URL(window.location.toString()).searchParams.get("__theme");return r(16,P=c||F||"system"),P==="dark"||P==="light"?K(m,P):r(16,P=B(m)),P}function B(m){const D=F();window?.matchMedia("(prefers-color-scheme: dark)")?.addEventListener("change",F);function F(){let $=window?.matchMedia?.("(prefers-color-scheme: dark)").matches?"dark":"light";return K(m,$),$}return D}function K(m,D){const F=s?m.parentElement:document.body,$=s?m:m.parentElement;$.style.background="var(--body-background-fill)",D==="dark"?F.classList.add("dark"):F.classList.remove("dark")}let R={message:"",load_status:"pending",status:"sleeping",detail:"SLEEPING"},N,ue=!1;function ae(m){r(13,R=m)}Qt(async()=>{window.__gradio_mode__!=="website"&&r(16,P=ie(U));const m=k||b||C||location.origin;r(17,N=await x(m,{status_callback:ae,normalise_files:!1})),r(12,W=N.config),window.__gradio_space__=W.space_id,r(13,R={message:"",load_status:"complete",status:"running",detail:"RUNNING"}),await G(U,W.css),r(18,ue=!0),window.__is_colab__=W.is_colab,W.dev_mode&&setTimeout(()=>{const{host:D}=new URL(m);let F=new URL(`ws://${D}/dev/reload`);_=new WebSocket(F),_.onmessage=async function($){$.data==="CHANGE"&&(r(17,N=await x(m,{status_callback:ae,normalise_files:!1})),r(17,N.config.root=N.config.path,N),r(12,W=N.config),window.__gradio_space__=W.space_id)}},200)}),Io("upload_files",E);let w,se;async function le(){r(19,w=(await mt(()=>import("./Blocks-e0da70dc.js").then(m=>m.B),["assets/Blocks-e0da70dc.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/Blocks-0733f3b3.css"])).default)}async function qe(){r(20,se=(await mt(()=>import("./Login-3649b4c5.js"),["assets/Login-3649b4c5.js","assets/StaticForm-0610b7e2.js","assets/StaticForm-3812b7f1.css","assets/InteractiveTextbox-8ea757fe.js","assets/Textbox-5df53a1e.js","assets/Button-770df9ba.js","assets/Button-dd3ccceb.css","assets/BlockTitle-2eb1c338.js","assets/Info-47344107.js","assets/Copy-bc542573.js","assets/Textbox-dde6f8cc.css","assets/StaticColumn-2df50ccb.js","assets/StaticColumn-2853eb31.css","assets/Login-9c3cc0eb.css"])).default)}function Ae(){W.auth_required?qe():le()}const Ve={readable_error:{NO_APP_FILE:o("errors.no_app_file"),CONFIG_ERROR:o("errors.config_error"),BUILD_ERROR:o("errors.build_error"),RUNTIME_ERROR:o("errors.runtime_error"),PAUSED:o("errors.space_paused")},title(m){return encodeURIComponent(o("errors.space_not_working"))},description(m,D){return encodeURIComponent(`Hello,

Firstly, thanks for creating this space!

I noticed that the space isn't working correctly because there is ${this.readable_error[m]||"an error"}.

It would be great if you could take a look at this because this space is being embedded on ${D}.

Thanks!`)}};Qt(async()=>{go.register(A,U)});function We(m){X=m,r(10,X)}function Z(m){fe=m,r(11,fe)}function Y(m){U=m,r(9,U)}return e.$$set=m=>{"autoscroll"in m&&r(0,i=m.autoscroll),"version"in m&&r(1,a=m.version),"initial_height"in m&&r(2,l=m.initial_height),"app_mode"in m&&r(3,u=m.app_mode),"is_embed"in m&&r(4,s=m.is_embed),"theme_mode"in m&&r(23,c=m.theme_mode),"control_page_title"in m&&r(5,f=m.control_page_title),"container"in m&&r(6,d=m.container),"info"in m&&r(7,p=m.info),"eager"in m&&r(24,h=m.eager),"mount_css"in m&&r(25,v=m.mount_css),"client"in m&&r(26,x=m.client),"upload_files"in m&&r(27,E=m.upload_files),"worker_proxy"in m&&r(28,g=m.worker_proxy),"space"in m&&r(8,b=m.space),"host"in m&&r(29,k=m.host),"src"in m&&r(30,C=m.src)},e.$$.update=()=>{e.$$.dirty[0]&4096&&W?.app_id&&W.app_id,e.$$.dirty[0]&9216&&r(14,H=!X&&R.load_status!=="error"?"pending":!X&&R.load_status==="error"?"error":R.load_status),e.$$.dirty[0]&16781312|e.$$.dirty[1]&1&&W&&(h||n[A])&&Ae(),e.$$.dirty[0]&2560&&fe&&U.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0}))},[i,a,l,u,s,f,d,p,b,U,X,fe,W,R,H,de,P,N,ue,w,se,o,Ve,c,h,v,x,E,g,k,C,n,We,Z,Y]}class mo extends Ht{constructor(t){super(),Bt(this,t,nu,tu,it,{autoscroll:0,version:1,initial_height:2,app_mode:3,is_embed:4,theme_mode:23,control_page_title:5,container:6,info:7,eager:24,mount_css:25,client:26,upload_files:27,worker_proxy:28,space:8,host:29,src:30},null,[-1,-1])}}const iu="https://gradio.s3-us-west-2.amazonaws.com/3.50.0/assets/index-642268e4.css";let fr;fr=[];function au(){class e extends HTMLElement{constructor(){super(),this.host=this.getAttribute("host"),this.space=this.getAttribute("space"),this.src=this.getAttribute("src"),this.control_page_title=this.getAttribute("control_page_title"),this.initial_height=this.getAttribute("initial_height")??"300px",this.is_embed=this.getAttribute("embed")??"true",this.container=this.getAttribute("container")??"true",this.info=this.getAttribute("info")??!0,this.autoscroll=this.getAttribute("autoscroll"),this.eager=this.getAttribute("eager"),this.theme_mode=this.getAttribute("theme_mode"),this.updating=!1,this.loading=!1}async connectedCallback(){this.loading=!0,this.app&&this.app.$destroy(),typeof fr!="string"&&fr.forEach(n=>Yt(n,document.head)),await Yt(iu,document.head);const r=new CustomEvent("domchange",{bubbles:!0,cancelable:!1,composed:!0});new MutationObserver(n=>{this.dispatchEvent(r)}).observe(this,{childList:!0}),this.app=new mo({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"3-50-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",client:Or,upload_files:Tr,app_mode:window.__gradio_mode__==="app"}}),this.updating&&this.setAttribute(this.updating.name,this.updating.value),this.loading=!1}static get observedAttributes(){return["src","space","host"]}attributeChangedCallback(r,o,n){if((r==="host"||r==="space"||r==="src")&&n!==o){if(this.updating={name:r,value:n},this.loading)return;this.app&&this.app.$destroy(),this.space=null,this.host=null,this.src=null,r==="host"?this.host=n:r==="space"?this.space=n:r==="src"&&(this.src=n),this.app=new mo({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"3-50-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",client:Or,upload_files:Tr,app_mode:window.__gradio_mode__==="app"}}),this.updating=!1}}}customElements.get("gradio-app")||customElements.define("gradio-app",e)}au();export{Na as $,Cu as A,_e as B,Bu as C,Qt as D,Hu as E,at as F,je as G,De as H,St as I,Ru as J,oe as K,Lu as L,ha as M,J as N,Co as O,Ml as P,Se as Q,me as R,Ht as S,Tu as T,tr as U,Ca as V,Ma as W,er as X,Io as Y,xo as Z,mt as _,et as a,xe as a0,wr as a1,Xc as a2,zu as a3,Hl as a4,_a as a5,So as a6,Pa as a7,bu as a8,ko as a9,su as aA,bo as aB,pr as aC,cu as aD,lu as aE,be as aF,Eu as aG,Iu as aH,xu as aI,gu as aJ,wu as aa,Oo as ab,Po as ac,To as ad,Jl as ae,mu as af,zo as ag,hu as ah,yu as ai,Qr as aj,Ta as ak,_u as al,Ou as am,Pu as an,Mu as ao,Su as ap,Au as aq,ku as ar,pu as as,ya as at,Oa as au,Tr as av,du as aw,ke as ax,$e as ay,ju as az,Ee as b,vt as c,wt as d,Bt as e,he as f,y as g,T as h,hr as i,O as j,S as k,Pt as l,j as m,Q as n,te as o,Mr as p,vu as q,xt as r,it as s,I as t,ne as u,Et as v,ee as w,re as x,Me as y,Nu as z};
//# sourceMappingURL=index-7674dbb6.js.map
