{"version": 3, "file": "index-79cdedb6.js", "sources": ["../../../../js/fileexplorer/static/StaticFileExplorer.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { File } from \"@gradio/icons\";\n\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport DirectoryExplorer from \"../shared/DirectoryExplorer.svelte\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string[][];\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let height: number | undefined = undefined;\n\texport let file_count: \"single\" | \"multiple\" = \"multiple\";\n\n\texport let loading_status: LoadingStatus;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t}>;\n\texport let server: {\n\t\tls: (path: string[]) => Promise<[string[], string[]]>;\n\t};\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null ? \"dashed\" : \"solid\"}\n\tborder_mode={\"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\t{height}\n>\n\t<StatusTracker {...loading_status} />\n\t<BlockLabel\n\t\t{show_label}\n\t\tIcon={File}\n\t\tlabel={label || \"FileExplorer\"}\n\t\tfloat={false}\n\t/>\n\t<DirectoryExplorer\n\t\tbind:value\n\t\t{file_count}\n\t\t{server}\n\t\tmode=\"static\"\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t/>\n</Block>\n"], "names": ["ctx", "File", "dirty", "blocklabel_changes", "block_changes", "elem_id", "$$props", "elem_classes", "visible", "value", "label", "show_label", "height", "file_count", "loading_status", "container", "scale", "min_width", "gradio", "server"], "mappings": "8ZAgDoBA,EAAc,CAAA,CAAA,2GAG1BC,EACC,MAAAD,MAAS,qBACT,oVALWA,EAAc,CAAA,CAAA,CAAA,CAAA,oDAIzBE,EAAA,KAAAC,EAAA,MAAAH,MAAS,+XAfR,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACxB,eACJ,4FAMO,+IARPE,EAAA,IAAAE,EAAA,QAAAJ,EAAU,CAAA,IAAA,KAAO,SAAW,+SAvB1B,GAAA,CAAA,QAAAK,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAiB,EAAAH,GACjB,MAAAI,CAAa,EAAAJ,GACb,WAAAK,CAAmB,EAAAL,EACnB,CAAA,OAAAM,EAA6B,MAAS,EAAAN,EACtC,CAAA,WAAAO,EAAoC,UAAU,EAAAP,GAE9C,eAAAQ,CAA6B,EAAAR,EAC7B,CAAA,UAAAS,EAAY,EAAI,EAAAT,EAChB,CAAA,MAAAU,EAAuB,IAAI,EAAAV,EAC3B,CAAA,UAAAW,EAAgC,MAAS,EAAAX,GACzC,OAAAY,CAET,EAAAZ,GACS,OAAAa,CAEV,EAAAb,uCA4BiBY,EAAO,SAAS,QAAQ"}