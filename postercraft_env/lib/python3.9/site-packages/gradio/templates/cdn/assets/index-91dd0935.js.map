{"version": 3, "file": "index-91dd0935.js", "sources": ["../../../../js/checkbox/static/StaticCheckbox.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport Checkbox from \"../shared\";\n\timport { Block, Info } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = false;\n\texport let value_is_output = false;\n\texport let label = \"Checkbox\";\n\texport let info: string | undefined = undefined;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t}>;\n</script>\n\n<Block {visible} {elem_id} {elem_classes} {container} {scale} {min_width}>\n\t<StatusTracker {...loading_status} />\n\n\t{#if info}\n\t\t<Info>{info}</Info>\n\t{/if}\n\t<Checkbox\n\t\t{label}\n\t\tbind:value\n\t\tbind:value_is_output\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\tdisabled\n\t/>\n</Block>\n"], "names": ["ctx", "create_if_block", "elem_id", "$$props", "elem_classes", "visible", "value", "value_is_output", "label", "info", "container", "scale", "min_width", "loading_status", "gradio", "e"], "mappings": "0mBA8BSA,EAAI,CAAA,CAAA,qCAAJA,EAAI,CAAA,CAAA,2DAHOA,EAAc,EAAA,CAAA,4EAE5BA,EAAI,CAAA,GAAAC,EAAAD,CAAA,ucAFUA,EAAc,EAAA,CAAA,CAAA,CAAA,eAE5BA,EAAI,CAAA,+5BArBE,GAAA,CAAA,QAAAE,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,EACd,CAAA,MAAAG,EAAQ,EAAK,EAAAH,EACb,CAAA,gBAAAI,EAAkB,EAAK,EAAAJ,EACvB,CAAA,MAAAK,EAAQ,UAAU,EAAAL,EAClB,CAAA,KAAAM,EAA2B,MAAS,EAAAN,EACpC,CAAA,UAAAO,EAAY,EAAI,EAAAP,EAChB,CAAA,MAAAQ,EAAuB,IAAI,EAAAR,EAC3B,CAAA,UAAAS,EAAgC,MAAS,EAAAT,GACzC,eAAAU,CAA6B,EAAAV,GAC7B,OAAAW,CAIT,EAAAX,gEAagBW,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO,IAC3BC,GAAMD,EAAO,SAAS,SAAUC,EAAE,MAAM"}