{"version": 3, "file": "index-948debff.js", "sources": ["../../../../js/slider/static/StaticSlider.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\timport Slider from \"../shared\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = 0;\n\texport let label = $_(\"slider.slider\");\n\texport let info: string | undefined = undefined;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let minimum: number;\n\texport let maximum: number;\n\texport let step: number;\n\texport let show_label: boolean;\n\n\texport let loading_status: LoadingStatus;\n\texport let value_is_output = false;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\trelease: number;\n\t}>;\n</script>\n\n<Block {visible} {elem_id} {elem_classes} {container} {scale} {min_width}>\n\t<StatusTracker {...loading_status} />\n\n\t<Slider\n\t\tbind:value\n\t\tbind:value_is_output\n\t\t{label}\n\t\t{info}\n\t\t{show_label}\n\t\t{minimum}\n\t\t{maximum}\n\t\t{step}\n\t\tdisabled\n\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\ton:release={(e) => gradio.dispatch(\"release\", e.detail)}\n\t/>\n</Block>\n"], "names": ["ctx", "elem_id", "$$props", "elem_classes", "visible", "value", "label", "$_", "info", "container", "scale", "min_width", "minimum", "maximum", "step", "show_label", "loading_status", "value_is_output", "gradio"], "mappings": "oWAgCoBA,EAAc,EAAA,CAAA,2iBAAdA,EAAc,EAAA,CAAA,CAAA,CAAA,i8BAxBtB,GAAA,CAAA,QAAAC,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,EACd,CAAA,MAAAG,EAAQ,CAAC,EAAAH,GACT,MAAAI,EAAQC,EAAG,eAAe,CAAA,EAAAL,EAC1B,CAAA,KAAAM,EAA2B,MAAS,EAAAN,EACpC,CAAA,UAAAO,EAAY,EAAI,EAAAP,EAChB,CAAA,MAAAQ,EAAuB,IAAI,EAAAR,EAC3B,CAAA,UAAAS,EAAgC,MAAS,EAAAT,GACzC,QAAAU,CAAe,EAAAV,GACf,QAAAW,CAAe,EAAAX,GACf,KAAAY,CAAY,EAAAZ,GACZ,WAAAa,CAAmB,EAAAb,GAEnB,eAAAc,CAA6B,EAAAd,EAC7B,CAAA,gBAAAe,EAAkB,EAAK,EAAAf,GACvB,OAAAgB,CAIT,EAAAhB,gEAgBegB,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,IAC5B,GAAMA,EAAO,SAAS,UAAW,EAAE,MAAM"}