import{S as T,e as V,s as q,F as k,o as L,Q as G,G as z,h as E,w as b,r as H,u as g,v as J,k as M,H as v,a0 as Q,a1 as W,D as X,m as O,g as D,j as B,R as Z,Z as A,ae as K,V as Y,W as y}from"./index-7674dbb6.js";import{B as p,n as x}from"./Button-770df9ba.js";import{B as P}from"./BlockLabel-520e742a.js";import{E as $}from"./Empty-89f2f53e.js";import{F as j}from"./File-29fa02e0.js";import{I as U}from"./IconButton-a4282a0e.js";import{D as ee}from"./Download-036e6033.js";import{U as le}from"./Undo-e443528b.js";import{b as ne,a as C,r as te,c as oe}from"./babylonjs.loaders.min-628a697c.js";function N(a){let l,t,e,o,n,s,f,u,w,m,c;return e=new U({props:{Icon:le,label:"Undo"}}),e.$on("click",a[12]),s=new U({props:{Icon:ee,label:a[4]("common.download")}}),{c(){l=O("div"),t=O("div"),k(e.$$.fragment),o=L(),n=O("a"),k(s.$$.fragment),w=L(),m=O("canvas"),D(n,"href",f=a[0].data),D(n,"target",window.__is_colab__?"_blank":null),D(n,"download",u=window.__is_colab__?null:a[0].orig_name||a[0].name),D(t,"class","buttons svelte-xz066x"),D(m,"class","svelte-xz066x"),D(l,"class","model3D svelte-xz066x")},m(i,r){E(i,l,r),B(l,t),z(e,t,null),B(t,o),B(t,n),z(s,n,null),B(l,w),B(l,m),a[13](m),c=!0},p(i,r){const d={};r&16&&(d.label=i[4]("common.download")),s.$set(d),(!c||r&1&&f!==(f=i[0].data))&&D(n,"href",f),(!c||r&1&&u!==(u=window.__is_colab__?null:i[0].orig_name||i[0].name))&&D(n,"download",u)},i(i){c||(b(e.$$.fragment,i),b(s.$$.fragment,i),c=!0)},o(i){g(e.$$.fragment,i),g(s.$$.fragment,i),c=!1},d(i){i&&M(l),v(e),v(s),a[13](null)}}}function ae(a){let l,t,e,o;l=new P({props:{show_label:a[2],Icon:j,label:a[1]||a[4]("3D_model.3d_model")}});let n=a[0]&&N(a);return{c(){k(l.$$.fragment),t=L(),n&&n.c(),e=G()},m(s,f){z(l,s,f),E(s,t,f),n&&n.m(s,f),E(s,e,f),o=!0},p(s,[f]){const u={};f&4&&(u.show_label=s[2]),f&18&&(u.label=s[1]||s[4]("3D_model.3d_model")),l.$set(u),s[0]?n?(n.p(s,f),f&1&&b(n,1)):(n=N(s),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(H(),g(n,1,1,()=>{n=null}),J())},i(s){o||(b(l.$$.fragment,s),b(n),o=!0)},o(s){g(l.$$.fragment,s),g(n),o=!1},d(s){s&&(M(t),M(e)),v(l,s),n&&n.d(s)}}}function se(a,l,t){let e,o,n;Q(a,W,_=>t(4,n=_));let{value:s}=l,{clear_color:f=[0,0,0,0]}=l,{label:u=""}=l,{show_label:w}=l,{zoom_speed:m=1}=l,{camera_position:c=[null,null,null]}=l;ne.OBJFileLoader.IMPORT_VERTEX_COLORS=!0;let i,r,d,h=!1;X(()=>{d=new C.Engine(i,!0),window.addEventListener("resize",()=>{d?.resize()}),t(9,h=!0)});function S(){r&&!r.isDisposed&&(r.dispose(),d?.stopRenderLoop(),d?.dispose(),d=null,d=new C.Engine(i,!0),window.addEventListener("resize",()=>{d?.resize()})),d!==null&&(r=oe(i,r,d,s,f,c,m))}function I(){te(r,c,m)}const R=()=>I();function F(_){Z[_?"unshift":"push"](()=>{i=_,t(3,i)})}return a.$$set=_=>{"value"in _&&t(0,s=_.value),"clear_color"in _&&t(6,f=_.clear_color),"label"in _&&t(1,u=_.label),"show_label"in _&&t(2,w=_.show_label),"zoom_speed"in _&&t(7,m=_.zoom_speed),"camera_position"in _&&t(8,c=_.camera_position)},a.$$.update=()=>{a.$$.dirty&1&&t(11,{data:e,name:o}=s||{data:void 0,name:void 0},e,(t(10,o),t(0,s))),a.$$.dirty&3592&&i&&h&&e!=null&&o&&S()},[s,u,w,i,n,I,f,m,c,h,o,e,R,F]}class ie extends T{constructor(l){super(),V(this,l,se,ae,q,{value:0,clear_color:6,label:1,show_label:2,zoom_speed:7,camera_position:8})}}function _e(a){let l,t,e,o;return l=new P({props:{show_label:a[7],Icon:j,label:a[6]||"3D Model"}}),e=new $({props:{unpadded_box:!0,size:"large",$$slots:{default:[re]},$$scope:{ctx:a}}}),{c(){k(l.$$.fragment),t=L(),k(e.$$.fragment)},m(n,s){z(l,n,s),E(n,t,s),z(e,n,s),o=!0},p(n,s){const f={};s&128&&(f.show_label=n[7]),s&64&&(f.label=n[6]||"3D Model"),l.$set(f);const u={};s&131072&&(u.$$scope={dirty:s,ctx:n}),e.$set(u)},i(n){o||(b(l.$$.fragment,n),b(e.$$.fragment,n),o=!0)},o(n){g(l.$$.fragment,n),g(e.$$.fragment,n),o=!1},d(n){n&&M(t),v(l,n),v(e,n)}}}function fe(a){let l,t;return l=new ie({props:{value:a[14],clear_color:a[4],label:a[6],show_label:a[7],camera_position:a[13],zoom_speed:a[12]}}),{c(){k(l.$$.fragment)},m(e,o){z(l,e,o),t=!0},p(e,o){const n={};o&16384&&(n.value=e[14]),o&16&&(n.clear_color=e[4]),o&64&&(n.label=e[6]),o&128&&(n.show_label=e[7]),o&8192&&(n.camera_position=e[13]),o&4096&&(n.zoom_speed=e[12]),l.$set(n)},i(e){t||(b(l.$$.fragment,e),t=!0)},o(e){g(l.$$.fragment,e),t=!1},d(e){v(l,e)}}}function re(a){let l,t;return l=new j({}),{c(){k(l.$$.fragment)},m(e,o){z(l,e,o),t=!0},i(e){t||(b(l.$$.fragment,e),t=!0)},o(e){g(l.$$.fragment,e),t=!1},d(e){v(l,e)}}}function ue(a){let l,t,e,o,n,s;const f=[a[5]];let u={};for(let i=0;i<f.length;i+=1)u=A(u,f[i]);l=new K({props:u});const w=[fe,_e],m=[];function c(i,r){return i[3]?0:1}return e=c(a),o=m[e]=w[e](a),{c(){k(l.$$.fragment),t=L(),o.c(),n=G()},m(i,r){z(l,i,r),E(i,t,r),m[e].m(i,r),E(i,n,r),s=!0},p(i,r){const d=r&32?Y(f,[y(i[5])]):{};l.$set(d);let h=e;e=c(i),e===h?m[e].p(i,r):(H(),g(m[h],1,1,()=>{m[h]=null}),J(),o=m[e],o?o.p(i,r):(o=m[e]=w[e](i),o.c()),b(o,1),o.m(n.parentNode,n))},i(i){s||(b(l.$$.fragment,i),b(o),s=!0)},o(i){g(l.$$.fragment,i),g(o),s=!1},d(i){i&&(M(t),M(n)),v(l,i),m[e].d(i)}}}function me(a){let l,t;return l=new p({props:{visible:a[2],variant:a[3]===null?"dashed":"solid",border_mode:"base",padding:!1,elem_id:a[0],elem_classes:a[1],container:a[8],scale:a[9],min_width:a[10],height:a[11],$$slots:{default:[ue]},$$scope:{ctx:a}}}),{c(){k(l.$$.fragment)},m(e,o){z(l,e,o),t=!0},p(e,[o]){const n={};o&4&&(n.visible=e[2]),o&8&&(n.variant=e[3]===null?"dashed":"solid"),o&1&&(n.elem_id=e[0]),o&2&&(n.elem_classes=e[1]),o&256&&(n.container=e[8]),o&512&&(n.scale=e[9]),o&1024&&(n.min_width=e[10]),o&2048&&(n.height=e[11]),o&159992&&(n.$$scope={dirty:o,ctx:e}),l.$set(n)},i(e){t||(b(l.$$.fragment,e),t=!0)},o(e){g(l.$$.fragment,e),t=!1},d(e){v(l,e)}}}function ce(a,l,t){let{elem_id:e=""}=l,{elem_classes:o=[]}=l,{visible:n=!0}=l,{value:s=null}=l,{root:f}=l,{root_url:u}=l,{clear_color:w}=l,{loading_status:m}=l,{label:c}=l,{show_label:i}=l,{container:r=!0}=l,{scale:d=null}=l,{min_width:h=void 0}=l,{height:S=void 0}=l,{zoom_speed:I=1}=l,{camera_position:R=[null,null,null]}=l,F;return a.$$set=_=>{"elem_id"in _&&t(0,e=_.elem_id),"elem_classes"in _&&t(1,o=_.elem_classes),"visible"in _&&t(2,n=_.visible),"value"in _&&t(3,s=_.value),"root"in _&&t(15,f=_.root),"root_url"in _&&t(16,u=_.root_url),"clear_color"in _&&t(4,w=_.clear_color),"loading_status"in _&&t(5,m=_.loading_status),"label"in _&&t(6,c=_.label),"show_label"in _&&t(7,i=_.show_label),"container"in _&&t(8,r=_.container),"scale"in _&&t(9,d=_.scale),"min_width"in _&&t(10,h=_.min_width),"height"in _&&t(11,S=_.height),"zoom_speed"in _&&t(12,I=_.zoom_speed),"camera_position"in _&&t(13,R=_.camera_position)},a.$$.update=()=>{a.$$.dirty&98312&&t(14,F=x(s,f,u))},[e,o,n,s,w,m,c,i,r,d,h,S,I,R,F,f,u]}class de extends T{constructor(l){super(),V(this,l,ce,me,q,{elem_id:0,elem_classes:1,visible:2,value:3,root:15,root_url:16,clear_color:4,loading_status:5,label:6,show_label:7,container:8,scale:9,min_width:10,height:11,zoom_speed:12,camera_position:13})}}const Me=de;export{Me as default};
//# sourceMappingURL=index-9505482f.js.map
