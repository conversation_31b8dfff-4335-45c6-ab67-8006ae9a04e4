{"version": 3, "file": "index-9505482f.js", "sources": ["../../../../js/model3D/static/Model3D.svelte", "../../../../js/model3D/static/StaticModel3d.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\timport { BlockLabel, IconButton } from \"@gradio/atoms\";\n\timport { File, Download, Undo } from \"@gradio/icons\";\n\timport { add_new_model, reset_camera_position } from \"../shared/utils\";\n\timport { _ } from \"svelte-i18n\";\n\timport { onMount } from \"svelte\";\n\timport * as BA<PERSON>Y<PERSON><PERSON> from \"babylonjs\";\n\timport * as BABYLON_LOADERS from \"babylonjs-loaders\";\n\n\texport let value: FileData | null;\n\texport let clear_color: [number, number, number, number] = [0, 0, 0, 0];\n\texport let label = \"\";\n\texport let show_label: boolean;\n\texport let zoom_speed = 1;\n\n\t// alpha, beta, radius\n\texport let camera_position: [number | null, number | null, number | null] = [\n\t\tnull,\n\t\tnull,\n\t\tnull\n\t];\n\n\tBABYLON_LOADERS.OBJFileLoader.IMPORT_VERTEX_COLORS = true;\n\n\tlet canvas: HTMLCanvasElement;\n\tlet scene: BABYLON.Scene;\n\tlet engine: BABYLON.Engine | null;\n\tlet mounted = false;\n\n\tonMount(() => {\n\t\tengine = new BABYLON.Engine(canvas, true);\n\t\twindow.addEventListener(\"resize\", () => {\n\t\t\tengine?.resize();\n\t\t});\n\t\tmounted = true;\n\t});\n\n\t$: ({ data, name } = value || {\n\t\tdata: undefined,\n\t\tname: undefined\n\t});\n\n\t$: canvas && mounted && data != null && name && dispose();\n\n\tfunction dispose(): void {\n\t\tif (scene && !scene.isDisposed) {\n\t\t\tscene.dispose();\n\t\t\tengine?.stopRenderLoop();\n\t\t\tengine?.dispose();\n\t\t\tengine = null;\n\t\t\tengine = new BABYLON.Engine(canvas, true);\n\t\t\twindow.addEventListener(\"resize\", () => {\n\t\t\t\tengine?.resize();\n\t\t\t});\n\t\t}\n\t\tif (engine !== null) {\n\t\t\tscene = add_new_model(\n\t\t\t\tcanvas,\n\t\t\t\tscene,\n\t\t\t\tengine,\n\t\t\t\tvalue,\n\t\t\t\tclear_color,\n\t\t\t\tcamera_position,\n\t\t\t\tzoom_speed\n\t\t\t);\n\t\t}\n\t}\n\n\tfunction handle_undo(): void {\n\t\treset_camera_position(scene, camera_position, zoom_speed);\n\t}\n</script>\n\n<BlockLabel {show_label} Icon={File} label={label || $_(\"3D_model.3d_model\")} />\n{#if value}\n\t<div class=\"model3D\">\n\t\t<div class=\"buttons\">\n\t\t\t<IconButton Icon={Undo} label=\"Undo\" on:click={() => handle_undo()} />\n\t\t\t<a\n\t\t\t\thref={value.data}\n\t\t\t\ttarget={window.__is_colab__ ? \"_blank\" : null}\n\t\t\t\tdownload={window.__is_colab__ ? null : value.orig_name || value.name}\n\t\t\t>\n\t\t\t\t<IconButton Icon={Download} label={$_(\"common.download\")} />\n\t\t\t</a>\n\t\t</div>\n\n\t\t<canvas bind:this={canvas} />\n\t</div>\n{/if}\n\n<style>\n\t.model3D {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\tcanvas {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\toverflow: hidden;\n\t}\n\t.buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport Model3D from \"./Model3D.svelte\";\n\timport { BlockLabel, Block, Empty } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData = null;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let clear_color: [number, number, number, number];\n\texport let loading_status: LoadingStatus;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let height: number | undefined = undefined;\n\texport let zoom_speed = 1;\n\n\t// alpha, beta, radius\n\texport let camera_position: [number | null, number | null, number | null] = [\n\t\tnull,\n\t\tnull,\n\t\tnull\n\t];\n\n\tlet _value: null | FileData;\n\t$: _value = normalise_file(value, root, root_url);\n\n\tlet dragging = false;\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\t{height}\n>\n\t<StatusTracker {...loading_status} />\n\n\t{#if value}\n\t\t<Model3D\n\t\t\tvalue={_value}\n\t\t\t{clear_color}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{camera_position}\n\t\t\t{zoom_speed}\n\t\t/>\n\t{:else}\n\t\t<!-- Not ideal but some bugs to work out before we can \n\t\t\t\t make this consistent with other components -->\n\n\t\t<BlockLabel {show_label} Icon={File} label={label || \"3D Model\"} />\n\n\t\t<Empty unpadded_box={true} size=\"large\"><File /></Empty>\n\t{/if}\n</Block>\n"], "names": ["Undo", "Download", "ctx", "attr", "a", "a_href_value", "a_download_value", "insert", "target", "div1", "anchor", "append", "div0", "canvas_1", "dirty", "iconbutton1_changes", "current", "File", "create_if_block", "value", "$$props", "clear_color", "label", "show_label", "zoom_speed", "camera_position", "BABYLON_LOADERS.OBJFileLoader", "canvas", "scene", "engine", "mounted", "onMount", "BABYLON.Engine", "$$invalidate", "dispose", "add_new_model", "handle_undo", "reset_camera_position", "$$value", "data", "name", "blocklabel_changes", "block_changes", "elem_id", "elem_classes", "visible", "root", "root_url", "loading_status", "container", "scale", "min_width", "height", "_value", "normalise_file"], "mappings": "opBA8EqBA,GAAI,MAAA,MAAA,CAAA,CAAA,4CAMHC,GAAiB,MAAAC,KAAG,iBAAiB,qGAJjDC,EAAAC,EAAA,OAAAC,EAAAH,KAAM,IAAI,EACRC,EAAAC,EAAA,SAAA,OAAO,aAAe,SAAW,IAAI,EACnCD,EAAAC,EAAA,WAAAE,EAAA,OAAO,aAAe,KAAOJ,EAAM,CAAA,EAAA,WAAaA,KAAM,IAAI,iHANvEK,EAaKC,EAAAC,EAAAC,CAAA,EAZJC,EASKF,EAAAG,CAAA,qBAPJD,EAMGC,EAAAR,CAAA,qBAGJO,EAA4BF,EAAAI,CAAA,mCAJSC,EAAA,KAAAC,EAAA,MAAAb,KAAG,iBAAiB,cAJjD,CAAAc,GAAAF,EAAA,GAAAT,KAAAA,EAAAH,KAAM,uBAEF,CAAAc,GAAAF,EAAA,GAAAR,KAAAA,EAAA,OAAO,aAAe,KAAOJ,EAAM,CAAA,EAAA,WAAaA,KAAM,uOARrCe,QAAaf,EAAK,CAAA,GAAIA,EAAE,CAAA,EAAC,mBAAmB,WACtEA,EAAK,CAAA,GAAAgB,EAAAhB,CAAA,oKADkCA,EAAK,CAAA,GAAIA,EAAE,CAAA,EAAC,mBAAmB,aACtEA,EAAK,CAAA,+QAjEE,MAAAiB,CAAsB,EAAAC,EACtB,CAAA,YAAAC,GAAiD,EAAG,EAAG,EAAG,CAAC,CAAA,EAAAD,EAC3D,CAAA,MAAAE,EAAQ,EAAE,EAAAF,GACV,WAAAG,CAAmB,EAAAH,EACnB,CAAA,WAAAI,EAAa,CAAC,EAAAJ,EAGd,CAAA,gBAAAK,EACV,CAAA,KACA,KACA,IAAA,CAAA,EAAAL,EAGDM,GAA6B,cAAC,qBAAuB,OAEjDC,EACAC,EACAC,EACAC,EAAU,GAEdC,EAAO,IAAA,CACNF,MAAaG,EAAAA,OAAeL,EAAQ,EAAI,EACxC,OAAO,iBAAiB,SAAQ,IAAA,CAC/BE,GAAQ,OAAM,IAEfI,EAAA,EAAAH,EAAU,EAAI,aAUNI,GAAO,CACXN,GAAK,CAAKA,EAAM,aACnBA,EAAM,QAAO,EACbC,GAAQ,eAAc,EACtBA,GAAQ,QAAO,EACfA,EAAS,KACTA,MAAaG,EAAAA,OAAeL,EAAQ,EAAI,EACxC,OAAO,iBAAiB,SAAQ,IAAA,CAC/BE,GAAQ,OAAM,KAGZA,IAAW,OACdD,EAAQO,GACPR,EACAC,EACAC,EACAV,EACAE,EACAI,EACAD,CAAU,YAKJY,GAAW,CACnBC,GAAsBT,EAAOH,EAAiBD,CAAU,cAQFY,6CAUnCT,EAAMW,0RAlDpBL,EAAA,GAAA,CAAA,KAAAM,EAAM,KAAAC,CAAS,EAAArB,GACpB,CAAA,KAAM,OACN,KAAM,MAAA,EAAAoB,GAAAN,EAAA,GAAAO,CAAA,EAAAP,EAAA,EAAAd,CAAA,qBAGJQ,GAAUG,GAAWS,GAAQ,MAAQC,GAAQN,EAAO,iPCwBvBjB,EAAa,MAAAf,MAAS,2CAEhC,6LAFuBY,EAAA,KAAA2B,EAAA,MAAAvC,MAAS,2QAX7CA,EAAM,EAAA,yKAANA,EAAM,EAAA,qbAJIA,EAAc,CAAA,CAAA,kHAE5BA,EAAK,CAAA,EAAA,yJAFSA,EAAc,CAAA,CAAA,CAAA,CAAA,wUAVxB,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACH,eACzB,0NAFAY,EAAA,IAAA4B,EAAA,QAAAxC,EAAU,CAAA,IAAA,KAAO,SAAW,kTA/B1B,GAAA,CAAA,QAAAyC,EAAU,EAAE,EAAAvB,GACZ,aAAAwB,EAAY,EAAA,EAAAxB,EACZ,CAAA,QAAAyB,EAAU,EAAI,EAAAzB,EACd,CAAA,MAAAD,EAAyB,IAAI,EAAAC,GAC7B,KAAA0B,CAAY,EAAA1B,GACZ,SAAA2B,CAAuB,EAAA3B,GACvB,YAAAC,CAA6C,EAAAD,GAC7C,eAAA4B,CAA6B,EAAA5B,GAC7B,MAAAE,CAAa,EAAAF,GACb,WAAAG,CAAmB,EAAAH,EACnB,CAAA,UAAA6B,EAAY,EAAI,EAAA7B,EAChB,CAAA,MAAA8B,EAAuB,IAAI,EAAA9B,EAC3B,CAAA,UAAA+B,EAAgC,MAAS,EAAA/B,EACzC,CAAA,OAAAgC,EAA6B,MAAS,EAAAhC,EACtC,CAAA,WAAAI,EAAa,CAAC,EAAAJ,EAGd,CAAA,gBAAAK,EACV,CAAA,KACA,KACA,IAAA,CAAA,EAAAL,EAGGiC,inBACHpB,EAAA,GAAEoB,EAASC,EAAenC,EAAO2B,EAAMC,CAAQ,CAAA"}