import{S as g,e as v,s as d,a9 as r,m as q,g as o,al as h,N as u,h as b,ab as S,ac as w,ad as R,w as j,u as k,k as C}from"./index-7674dbb6.js";function N(i){let e,_,a;const f=i[6].default,s=r(f,i,i[5],null);return{c(){e=q("div"),s&&s.c(),o(e,"id",i[1]),o(e,"class",_=h(i[2].join(" "))+" svelte-15lo0d8"),u(e,"compact",i[4]==="compact"),u(e,"panel",i[4]==="panel"),u(e,"unequal-height",i[0]===!1),u(e,"stretch",i[0]),u(e,"hide",!i[3])},m(l,t){b(l,e,t),s&&s.m(e,null),a=!0},p(l,[t]){s&&s.p&&(!a||t&32)&&S(s,f,l,l[5],a?R(f,l[5],t,null):w(l[5]),null),(!a||t&2)&&o(e,"id",l[1]),(!a||t&4&&_!==(_=h(l[2].join(" "))+" svelte-15lo0d8"))&&o(e,"class",_),(!a||t&20)&&u(e,"compact",l[4]==="compact"),(!a||t&20)&&u(e,"panel",l[4]==="panel"),(!a||t&5)&&u(e,"unequal-height",l[0]===!1),(!a||t&5)&&u(e,"stretch",l[0]),(!a||t&12)&&u(e,"hide",!l[3])},i(l){a||(j(s,l),a=!0)},o(l){k(s,l),a=!1},d(l){l&&C(e),s&&s.d(l)}}}function z(i,e,_){let{$$slots:a={},$$scope:f}=e,{equal_height:s=!0}=e,{elem_id:l}=e,{elem_classes:t=[]}=e,{visible:c=!0}=e,{variant:m="default"}=e;return i.$$set=n=>{"equal_height"in n&&_(0,s=n.equal_height),"elem_id"in n&&_(1,l=n.elem_id),"elem_classes"in n&&_(2,t=n.elem_classes),"visible"in n&&_(3,c=n.visible),"variant"in n&&_(4,m=n.variant),"$$scope"in n&&_(5,f=n.$$scope)},[s,l,t,c,m,f,a]}class A extends g{constructor(e){super(),v(this,e,z,N,d,{equal_height:0,elem_id:1,elem_classes:2,visible:3,variant:4})}}const D=A;export{D as default};
//# sourceMappingURL=index-973a999e.js.map
