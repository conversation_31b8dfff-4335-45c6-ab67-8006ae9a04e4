{"version": 3, "file": "index-9b5e17ab.js", "sources": ["../../../../node_modules/.pnpm/@lezer+markdown@1.0.2/node_modules/@lezer/markdown/dist/index.js", "../../../../node_modules/.pnpm/@codemirror+lang-markdown@6.1.0/node_modules/@codemirror/lang-markdown/dist/index.js"], "sourcesContent": ["import { NodeType, NodeProp, NodeSet, Tree, Parser, parseMixed } from '@lezer/common';\nimport { styleTags, tags, Tag } from '@lezer/highlight';\n\nclass CompositeBlock {\n    constructor(type, \n    // Used for indentation in list items, markup character in lists\n    value, from, hash, end, children, positions) {\n        this.type = type;\n        this.value = value;\n        this.from = from;\n        this.hash = hash;\n        this.end = end;\n        this.children = children;\n        this.positions = positions;\n        this.hashProp = [[NodeProp.contextHash, hash]];\n    }\n    static create(type, value, from, parentHash, end) {\n        let hash = (parentHash + (parentHash << 8) + type + (value << 4)) | 0;\n        return new CompositeBlock(type, value, from, hash, end, [], []);\n    }\n    addChild(child, pos) {\n        if (child.prop(NodeProp.contextHash) != this.hash)\n            child = new Tree(child.type, child.children, child.positions, child.length, this.hashProp);\n        this.children.push(child);\n        this.positions.push(pos);\n    }\n    toTree(nodeSet, end = this.end) {\n        let last = this.children.length - 1;\n        if (last >= 0)\n            end = Math.max(end, this.positions[last] + this.children[last].length + this.from);\n        let tree = new Tree(nodeSet.types[this.type], this.children, this.positions, end - this.from).balance({\n            makeTree: (children, positions, length) => new Tree(NodeType.none, children, positions, length, this.hashProp)\n        });\n        return tree;\n    }\n}\nvar Type;\n(function (Type) {\n    Type[Type[\"Document\"] = 1] = \"Document\";\n    Type[Type[\"CodeBlock\"] = 2] = \"CodeBlock\";\n    Type[Type[\"FencedCode\"] = 3] = \"FencedCode\";\n    Type[Type[\"Blockquote\"] = 4] = \"Blockquote\";\n    Type[Type[\"HorizontalRule\"] = 5] = \"HorizontalRule\";\n    Type[Type[\"BulletList\"] = 6] = \"BulletList\";\n    Type[Type[\"OrderedList\"] = 7] = \"OrderedList\";\n    Type[Type[\"ListItem\"] = 8] = \"ListItem\";\n    Type[Type[\"ATXHeading1\"] = 9] = \"ATXHeading1\";\n    Type[Type[\"ATXHeading2\"] = 10] = \"ATXHeading2\";\n    Type[Type[\"ATXHeading3\"] = 11] = \"ATXHeading3\";\n    Type[Type[\"ATXHeading4\"] = 12] = \"ATXHeading4\";\n    Type[Type[\"ATXHeading5\"] = 13] = \"ATXHeading5\";\n    Type[Type[\"ATXHeading6\"] = 14] = \"ATXHeading6\";\n    Type[Type[\"SetextHeading1\"] = 15] = \"SetextHeading1\";\n    Type[Type[\"SetextHeading2\"] = 16] = \"SetextHeading2\";\n    Type[Type[\"HTMLBlock\"] = 17] = \"HTMLBlock\";\n    Type[Type[\"LinkReference\"] = 18] = \"LinkReference\";\n    Type[Type[\"Paragraph\"] = 19] = \"Paragraph\";\n    Type[Type[\"CommentBlock\"] = 20] = \"CommentBlock\";\n    Type[Type[\"ProcessingInstructionBlock\"] = 21] = \"ProcessingInstructionBlock\";\n    // Inline\n    Type[Type[\"Escape\"] = 22] = \"Escape\";\n    Type[Type[\"Entity\"] = 23] = \"Entity\";\n    Type[Type[\"HardBreak\"] = 24] = \"HardBreak\";\n    Type[Type[\"Emphasis\"] = 25] = \"Emphasis\";\n    Type[Type[\"StrongEmphasis\"] = 26] = \"StrongEmphasis\";\n    Type[Type[\"Link\"] = 27] = \"Link\";\n    Type[Type[\"Image\"] = 28] = \"Image\";\n    Type[Type[\"InlineCode\"] = 29] = \"InlineCode\";\n    Type[Type[\"HTMLTag\"] = 30] = \"HTMLTag\";\n    Type[Type[\"Comment\"] = 31] = \"Comment\";\n    Type[Type[\"ProcessingInstruction\"] = 32] = \"ProcessingInstruction\";\n    Type[Type[\"URL\"] = 33] = \"URL\";\n    // Smaller tokens\n    Type[Type[\"HeaderMark\"] = 34] = \"HeaderMark\";\n    Type[Type[\"QuoteMark\"] = 35] = \"QuoteMark\";\n    Type[Type[\"ListMark\"] = 36] = \"ListMark\";\n    Type[Type[\"LinkMark\"] = 37] = \"LinkMark\";\n    Type[Type[\"EmphasisMark\"] = 38] = \"EmphasisMark\";\n    Type[Type[\"CodeMark\"] = 39] = \"CodeMark\";\n    Type[Type[\"CodeText\"] = 40] = \"CodeText\";\n    Type[Type[\"CodeInfo\"] = 41] = \"CodeInfo\";\n    Type[Type[\"LinkTitle\"] = 42] = \"LinkTitle\";\n    Type[Type[\"LinkLabel\"] = 43] = \"LinkLabel\";\n})(Type || (Type = {}));\n/// Data structure used to accumulate a block's content during [leaf\n/// block parsing](#BlockParser.leaf).\nclass LeafBlock {\n    /// @internal\n    constructor(\n    /// The start position of the block.\n    start, \n    /// The block's text content.\n    content) {\n        this.start = start;\n        this.content = content;\n        /// @internal\n        this.marks = [];\n        /// The block parsers active for this block.\n        this.parsers = [];\n    }\n}\n/// Data structure used during block-level per-line parsing.\nclass Line {\n    constructor() {\n        /// The line's full text.\n        this.text = \"\";\n        /// The base indent provided by the composite contexts (that have\n        /// been handled so far).\n        this.baseIndent = 0;\n        /// The string position corresponding to the base indent.\n        this.basePos = 0;\n        /// The number of contexts handled @internal\n        this.depth = 0;\n        /// Any markers (i.e. block quote markers) parsed for the contexts. @internal\n        this.markers = [];\n        /// The position of the next non-whitespace character beyond any\n        /// list, blockquote, or other composite block markers.\n        this.pos = 0;\n        /// The column of the next non-whitespace character.\n        this.indent = 0;\n        /// The character code of the character after `pos`.\n        this.next = -1;\n    }\n    /// @internal\n    forward() {\n        if (this.basePos > this.pos)\n            this.forwardInner();\n    }\n    /// @internal\n    forwardInner() {\n        let newPos = this.skipSpace(this.basePos);\n        this.indent = this.countIndent(newPos, this.pos, this.indent);\n        this.pos = newPos;\n        this.next = newPos == this.text.length ? -1 : this.text.charCodeAt(newPos);\n    }\n    /// Skip whitespace after the given position, return the position of\n    /// the next non-space character or the end of the line if there's\n    /// only space after `from`.\n    skipSpace(from) { return skipSpace(this.text, from); }\n    /// @internal\n    reset(text) {\n        this.text = text;\n        this.baseIndent = this.basePos = this.pos = this.indent = 0;\n        this.forwardInner();\n        this.depth = 1;\n        while (this.markers.length)\n            this.markers.pop();\n    }\n    /// Move the line's base position forward to the given position.\n    /// This should only be called by composite [block\n    /// parsers](#BlockParser.parse) or [markup skipping\n    /// functions](#NodeSpec.composite).\n    moveBase(to) {\n        this.basePos = to;\n        this.baseIndent = this.countIndent(to, this.pos, this.indent);\n    }\n    /// Move the line's base position forward to the given _column_.\n    moveBaseColumn(indent) {\n        this.baseIndent = indent;\n        this.basePos = this.findColumn(indent);\n    }\n    /// Store a composite-block-level marker. Should be called from\n    /// [markup skipping functions](#NodeSpec.composite) when they\n    /// consume any non-whitespace characters.\n    addMarker(elt) {\n        this.markers.push(elt);\n    }\n    /// Find the column position at `to`, optionally starting at a given\n    /// position and column.\n    countIndent(to, from = 0, indent = 0) {\n        for (let i = from; i < to; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return indent;\n    }\n    /// Find the position corresponding to the given column.\n    findColumn(goal) {\n        let i = 0;\n        for (let indent = 0; i < this.text.length && indent < goal; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return i;\n    }\n    /// @internal\n    scrub() {\n        if (!this.baseIndent)\n            return this.text;\n        let result = \"\";\n        for (let i = 0; i < this.basePos; i++)\n            result += \" \";\n        return result + this.text.slice(this.basePos);\n    }\n}\nfunction skipForList(bl, cx, line) {\n    if (line.pos == line.text.length ||\n        (bl != cx.block && line.indent >= cx.stack[line.depth + 1].value + line.baseIndent))\n        return true;\n    if (line.indent >= line.baseIndent + 4)\n        return false;\n    let size = (bl.type == Type.OrderedList ? isOrderedList : isBulletList)(line, cx, false);\n    return size > 0 &&\n        (bl.type != Type.BulletList || isHorizontalRule(line, cx, false) < 0) &&\n        line.text.charCodeAt(line.pos + size - 1) == bl.value;\n}\nconst DefaultSkipMarkup = {\n    [Type.Blockquote](bl, cx, line) {\n        if (line.next != 62 /* '>' */)\n            return false;\n        line.markers.push(elt(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1));\n        line.moveBase(line.pos + (space(line.text.charCodeAt(line.pos + 1)) ? 2 : 1));\n        bl.end = cx.lineStart + line.text.length;\n        return true;\n    },\n    [Type.ListItem](bl, _cx, line) {\n        if (line.indent < line.baseIndent + bl.value && line.next > -1)\n            return false;\n        line.moveBaseColumn(line.baseIndent + bl.value);\n        return true;\n    },\n    [Type.OrderedList]: skipForList,\n    [Type.BulletList]: skipForList,\n    [Type.Document]() { return true; }\n};\nfunction space(ch) { return ch == 32 || ch == 9 || ch == 10 || ch == 13; }\nfunction skipSpace(line, i = 0) {\n    while (i < line.length && space(line.charCodeAt(i)))\n        i++;\n    return i;\n}\nfunction skipSpaceBack(line, i, to) {\n    while (i > to && space(line.charCodeAt(i - 1)))\n        i--;\n    return i;\n}\nfunction isFencedCode(line) {\n    if (line.next != 96 && line.next != 126 /* '`~' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    if (pos < line.pos + 3)\n        return -1;\n    if (line.next == 96)\n        for (let i = pos; i < line.text.length; i++)\n            if (line.text.charCodeAt(i) == 96)\n                return -1;\n    return pos;\n}\nfunction isBlockquote(line) {\n    return line.next != 62 /* '>' */ ? -1 : line.text.charCodeAt(line.pos + 1) == 32 ? 2 : 1;\n}\nfunction isHorizontalRule(line, cx, breaking) {\n    if (line.next != 42 && line.next != 45 && line.next != 95 /* '_-*' */)\n        return -1;\n    let count = 1;\n    for (let pos = line.pos + 1; pos < line.text.length; pos++) {\n        let ch = line.text.charCodeAt(pos);\n        if (ch == line.next)\n            count++;\n        else if (!space(ch))\n            return -1;\n    }\n    // Setext headers take precedence\n    if (breaking && line.next == 45 && isSetextUnderline(line) > -1 && line.depth == cx.stack.length)\n        return -1;\n    return count < 3 ? -1 : 1;\n}\nfunction inList(cx, type) {\n    for (let i = cx.stack.length - 1; i >= 0; i--)\n        if (cx.stack[i].type == type)\n            return true;\n    return false;\n}\nfunction isBulletList(line, cx, breaking) {\n    return (line.next == 45 || line.next == 43 || line.next == 42 /* '-+*' */) &&\n        (line.pos == line.text.length - 1 || space(line.text.charCodeAt(line.pos + 1))) &&\n        (!breaking || inList(cx, Type.BulletList) || line.skipSpace(line.pos + 2) < line.text.length) ? 1 : -1;\n}\nfunction isOrderedList(line, cx, breaking) {\n    let pos = line.pos, next = line.next;\n    for (;;) {\n        if (next >= 48 && next <= 57 /* '0-9' */)\n            pos++;\n        else\n            break;\n        if (pos == line.text.length)\n            return -1;\n        next = line.text.charCodeAt(pos);\n    }\n    if (pos == line.pos || pos > line.pos + 9 ||\n        (next != 46 && next != 41 /* '.)' */) ||\n        (pos < line.text.length - 1 && !space(line.text.charCodeAt(pos + 1))) ||\n        breaking && !inList(cx, Type.OrderedList) &&\n            (line.skipSpace(pos + 1) == line.text.length || pos > line.pos + 1 || line.next != 49 /* '1' */))\n        return -1;\n    return pos + 1 - line.pos;\n}\nfunction isAtxHeading(line) {\n    if (line.next != 35 /* '#' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == 35)\n        pos++;\n    if (pos < line.text.length && line.text.charCodeAt(pos) != 32)\n        return -1;\n    let size = pos - line.pos;\n    return size > 6 ? -1 : size;\n}\nfunction isSetextUnderline(line) {\n    if (line.next != 45 && line.next != 61 /* '-=' */ || line.indent >= line.baseIndent + 4)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    let end = pos;\n    while (pos < line.text.length && space(line.text.charCodeAt(pos)))\n        pos++;\n    return pos == line.text.length ? end : -1;\n}\nconst EmptyLine = /^[ \\t]*$/, CommentEnd = /-->/, ProcessingEnd = /\\?>/;\nconst HTMLBlockStyle = [\n    [/^<(?:script|pre|style)(?:\\s|>|$)/i, /<\\/(?:script|pre|style)>/i],\n    [/^\\s*<!--/, CommentEnd],\n    [/^\\s*<\\?/, ProcessingEnd],\n    [/^\\s*<![A-Z]/, />/],\n    [/^\\s*<!\\[CDATA\\[/, /\\]\\]>/],\n    [/^\\s*<\\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\\s|\\/?>|$)/i, EmptyLine],\n    [/^\\s*(?:<\\/[a-z][\\w-]*\\s*>|<[a-z][\\w-]*(\\s+[a-z:_][\\w-.]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*>)\\s*$/i, EmptyLine]\n];\nfunction isHTMLBlock(line, _cx, breaking) {\n    if (line.next != 60 /* '<' */)\n        return -1;\n    let rest = line.text.slice(line.pos);\n    for (let i = 0, e = HTMLBlockStyle.length - (breaking ? 1 : 0); i < e; i++)\n        if (HTMLBlockStyle[i][0].test(rest))\n            return i;\n    return -1;\n}\nfunction getListIndent(line, pos) {\n    let indentAfter = line.countIndent(pos, line.pos, line.indent);\n    let indented = line.countIndent(line.skipSpace(pos), pos, indentAfter);\n    return indented >= indentAfter + 5 ? indentAfter + 1 : indented;\n}\nfunction addCodeText(marks, from, to) {\n    let last = marks.length - 1;\n    if (last >= 0 && marks[last].to == from && marks[last].type == Type.CodeText)\n        marks[last].to = to;\n    else\n        marks.push(elt(Type.CodeText, from, to));\n}\n// Rules for parsing blocks. A return value of false means the rule\n// doesn't apply here, true means it does. When true is returned and\n// `p.line` has been updated, the rule is assumed to have consumed a\n// leaf block. Otherwise, it is assumed to have opened a context.\nconst DefaultBlockParsers = {\n    LinkReference: undefined,\n    IndentedCode(cx, line) {\n        let base = line.baseIndent + 4;\n        if (line.indent < base)\n            return false;\n        let start = line.findColumn(base);\n        let from = cx.lineStart + start, to = cx.lineStart + line.text.length;\n        let marks = [], pendingMarks = [];\n        addCodeText(marks, from, to);\n        while (cx.nextLine() && line.depth >= cx.stack.length) {\n            if (line.pos == line.text.length) { // Empty\n                addCodeText(pendingMarks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    pendingMarks.push(m);\n            }\n            else if (line.indent < base) {\n                break;\n            }\n            else {\n                if (pendingMarks.length) {\n                    for (let m of pendingMarks) {\n                        if (m.type == Type.CodeText)\n                            addCodeText(marks, m.from, m.to);\n                        else\n                            marks.push(m);\n                    }\n                    pendingMarks = [];\n                }\n                addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                to = cx.lineStart + line.text.length;\n                let codeStart = cx.lineStart + line.findColumn(line.baseIndent + 4);\n                if (codeStart < to)\n                    addCodeText(marks, codeStart, to);\n            }\n        }\n        if (pendingMarks.length) {\n            pendingMarks = pendingMarks.filter(m => m.type != Type.CodeText);\n            if (pendingMarks.length)\n                line.markers = pendingMarks.concat(line.markers);\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(Type.CodeBlock, to - from), from);\n        return true;\n    },\n    FencedCode(cx, line) {\n        let fenceEnd = isFencedCode(line);\n        if (fenceEnd < 0)\n            return false;\n        let from = cx.lineStart + line.pos, ch = line.next, len = fenceEnd - line.pos;\n        let infoFrom = line.skipSpace(fenceEnd), infoTo = skipSpaceBack(line.text, line.text.length, infoFrom);\n        let marks = [elt(Type.CodeMark, from, from + len)];\n        if (infoFrom < infoTo)\n            marks.push(elt(Type.CodeInfo, cx.lineStart + infoFrom, cx.lineStart + infoTo));\n        for (let first = true; cx.nextLine() && line.depth >= cx.stack.length; first = false) {\n            let i = line.pos;\n            if (line.indent - line.baseIndent < 4)\n                while (i < line.text.length && line.text.charCodeAt(i) == ch)\n                    i++;\n            if (i - line.pos >= len && line.skipSpace(i) == line.text.length) {\n                for (let m of line.markers)\n                    marks.push(m);\n                marks.push(elt(Type.CodeMark, cx.lineStart + line.pos, cx.lineStart + i));\n                cx.nextLine();\n                break;\n            }\n            else {\n                if (!first)\n                    addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                let textStart = cx.lineStart + line.basePos, textEnd = cx.lineStart + line.text.length;\n                if (textStart < textEnd)\n                    addCodeText(marks, textStart, textEnd);\n            }\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from)\n            .finish(Type.FencedCode, cx.prevLineEnd() - from), from);\n        return true;\n    },\n    Blockquote(cx, line) {\n        let size = isBlockquote(line);\n        if (size < 0)\n            return false;\n        cx.startContext(Type.Blockquote, line.pos);\n        cx.addNode(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1);\n        line.moveBase(line.pos + size);\n        return null;\n    },\n    HorizontalRule(cx, line) {\n        if (isHorizontalRule(line, cx, false) < 0)\n            return false;\n        let from = cx.lineStart + line.pos;\n        cx.nextLine();\n        cx.addNode(Type.HorizontalRule, from);\n        return true;\n    },\n    BulletList(cx, line) {\n        let size = isBulletList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.BulletList)\n            cx.startContext(Type.BulletList, line.basePos, line.next);\n        let newBase = getListIndent(line, line.pos + 1);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    OrderedList(cx, line) {\n        let size = isOrderedList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.OrderedList)\n            cx.startContext(Type.OrderedList, line.basePos, line.text.charCodeAt(line.pos + size - 1));\n        let newBase = getListIndent(line, line.pos + size);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    ATXHeading(cx, line) {\n        let size = isAtxHeading(line);\n        if (size < 0)\n            return false;\n        let off = line.pos, from = cx.lineStart + off;\n        let endOfSpace = skipSpaceBack(line.text, line.text.length, off), after = endOfSpace;\n        while (after > off && line.text.charCodeAt(after - 1) == line.next)\n            after--;\n        if (after == endOfSpace || after == off || !space(line.text.charCodeAt(after - 1)))\n            after = line.text.length;\n        let buf = cx.buffer\n            .write(Type.HeaderMark, 0, size)\n            .writeElements(cx.parser.parseInline(line.text.slice(off + size + 1, after), from + size + 1), -from);\n        if (after < line.text.length)\n            buf.write(Type.HeaderMark, after - off, endOfSpace - off);\n        let node = buf.finish(Type.ATXHeading1 - 1 + size, line.text.length - off);\n        cx.nextLine();\n        cx.addNode(node, from);\n        return true;\n    },\n    HTMLBlock(cx, line) {\n        let type = isHTMLBlock(line, cx, false);\n        if (type < 0)\n            return false;\n        let from = cx.lineStart + line.pos, end = HTMLBlockStyle[type][1];\n        let marks = [], trailing = end != EmptyLine;\n        while (!end.test(line.text) && cx.nextLine()) {\n            if (line.depth < cx.stack.length) {\n                trailing = false;\n                break;\n            }\n            for (let m of line.markers)\n                marks.push(m);\n        }\n        if (trailing)\n            cx.nextLine();\n        let nodeType = end == CommentEnd ? Type.CommentBlock : end == ProcessingEnd ? Type.ProcessingInstructionBlock : Type.HTMLBlock;\n        let to = cx.prevLineEnd();\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(nodeType, to - from), from);\n        return true;\n    },\n    SetextHeading: undefined // Specifies relative precedence for block-continue function\n};\n// This implements a state machine that incrementally parses link references. At each\n// next line, it looks ahead to see if the line continues the reference or not. If it\n// doesn't and a valid link is available ending before that line, it finishes that.\n// Similarly, on `finish` (when the leaf is terminated by external circumstances), it\n// creates a link reference if there's a valid reference up to the current point.\nclass LinkReferenceParser {\n    constructor(leaf) {\n        this.stage = 0 /* Start */;\n        this.elts = [];\n        this.pos = 0;\n        this.start = leaf.start;\n        this.advance(leaf.content);\n    }\n    nextLine(cx, line, leaf) {\n        if (this.stage == -1 /* Failed */)\n            return false;\n        let content = leaf.content + \"\\n\" + line.scrub();\n        let finish = this.advance(content);\n        if (finish > -1 && finish < content.length)\n            return this.complete(cx, leaf, finish);\n        return false;\n    }\n    finish(cx, leaf) {\n        if ((this.stage == 2 /* Link */ || this.stage == 3 /* Title */) && skipSpace(leaf.content, this.pos) == leaf.content.length)\n            return this.complete(cx, leaf, leaf.content.length);\n        return false;\n    }\n    complete(cx, leaf, len) {\n        cx.addLeafElement(leaf, elt(Type.LinkReference, this.start, this.start + len, this.elts));\n        return true;\n    }\n    nextStage(elt) {\n        if (elt) {\n            this.pos = elt.to - this.start;\n            this.elts.push(elt);\n            this.stage++;\n            return true;\n        }\n        if (elt === false)\n            this.stage = -1 /* Failed */;\n        return false;\n    }\n    advance(content) {\n        for (;;) {\n            if (this.stage == -1 /* Failed */) {\n                return -1;\n            }\n            else if (this.stage == 0 /* Start */) {\n                if (!this.nextStage(parseLinkLabel(content, this.pos, this.start, true)))\n                    return -1;\n                if (content.charCodeAt(this.pos) != 58 /* ':' */)\n                    return this.stage = -1 /* Failed */;\n                this.elts.push(elt(Type.LinkMark, this.pos + this.start, this.pos + this.start + 1));\n                this.pos++;\n            }\n            else if (this.stage == 1 /* Label */) {\n                if (!this.nextStage(parseURL(content, skipSpace(content, this.pos), this.start)))\n                    return -1;\n            }\n            else if (this.stage == 2 /* Link */) {\n                let skip = skipSpace(content, this.pos), end = 0;\n                if (skip > this.pos) {\n                    let title = parseLinkTitle(content, skip, this.start);\n                    if (title) {\n                        let titleEnd = lineEnd(content, title.to - this.start);\n                        if (titleEnd > 0) {\n                            this.nextStage(title);\n                            end = titleEnd;\n                        }\n                    }\n                }\n                if (!end)\n                    end = lineEnd(content, this.pos);\n                return end > 0 && end < content.length ? end : -1;\n            }\n            else { // RefStage.Title\n                return lineEnd(content, this.pos);\n            }\n        }\n    }\n}\nfunction lineEnd(text, pos) {\n    for (; pos < text.length; pos++) {\n        let next = text.charCodeAt(pos);\n        if (next == 10)\n            break;\n        if (!space(next))\n            return -1;\n    }\n    return pos;\n}\nclass SetextHeadingParser {\n    nextLine(cx, line, leaf) {\n        let underline = line.depth < cx.stack.length ? -1 : isSetextUnderline(line);\n        let next = line.next;\n        if (underline < 0)\n            return false;\n        let underlineMark = elt(Type.HeaderMark, cx.lineStart + line.pos, cx.lineStart + underline);\n        cx.nextLine();\n        cx.addLeafElement(leaf, elt(next == 61 ? Type.SetextHeading1 : Type.SetextHeading2, leaf.start, cx.prevLineEnd(), [\n            ...cx.parser.parseInline(leaf.content, leaf.start),\n            underlineMark\n        ]));\n        return true;\n    }\n    finish() {\n        return false;\n    }\n}\nconst DefaultLeafBlocks = {\n    LinkReference(_, leaf) { return leaf.content.charCodeAt(0) == 91 /* '[' */ ? new LinkReferenceParser(leaf) : null; },\n    SetextHeading() { return new SetextHeadingParser; }\n};\nconst DefaultEndLeaf = [\n    (_, line) => isAtxHeading(line) >= 0,\n    (_, line) => isFencedCode(line) >= 0,\n    (_, line) => isBlockquote(line) >= 0,\n    (p, line) => isBulletList(line, p, true) >= 0,\n    (p, line) => isOrderedList(line, p, true) >= 0,\n    (p, line) => isHorizontalRule(line, p, true) >= 0,\n    (p, line) => isHTMLBlock(line, p, true) >= 0\n];\nconst scanLineResult = { text: \"\", end: 0 };\n/// Block-level parsing functions get access to this context object.\nclass BlockContext {\n    /// @internal\n    constructor(\n    /// The parser configuration used.\n    parser, \n    /// @internal\n    input, fragments, \n    /// @internal\n    ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.line = new Line();\n        this.atEnd = false;\n        /// @internal\n        this.dontInject = new Set;\n        this.stoppedAt = null;\n        /// The range index that absoluteLineStart points into @internal\n        this.rangeI = 0;\n        this.to = ranges[ranges.length - 1].to;\n        this.lineStart = this.absoluteLineStart = this.absoluteLineEnd = ranges[0].from;\n        this.block = CompositeBlock.create(Type.Document, 0, this.lineStart, 0, 0);\n        this.stack = [this.block];\n        this.fragments = fragments.length ? new FragmentCursor(fragments, input) : null;\n        this.readLine();\n    }\n    get parsedPos() {\n        return this.absoluteLineStart;\n    }\n    advance() {\n        if (this.stoppedAt != null && this.absoluteLineStart > this.stoppedAt)\n            return this.finish();\n        let { line } = this;\n        for (;;) {\n            while (line.depth < this.stack.length)\n                this.finishContext();\n            for (let mark of line.markers)\n                this.addNode(mark.type, mark.from, mark.to);\n            if (line.pos < line.text.length)\n                break;\n            // Empty line\n            if (!this.nextLine())\n                return this.finish();\n        }\n        if (this.fragments && this.reuseFragment(line.basePos))\n            return null;\n        start: for (;;) {\n            for (let type of this.parser.blockParsers)\n                if (type) {\n                    let result = type(this, line);\n                    if (result != false) {\n                        if (result == true)\n                            return null;\n                        line.forward();\n                        continue start;\n                    }\n                }\n            break;\n        }\n        let leaf = new LeafBlock(this.lineStart + line.pos, line.text.slice(line.pos));\n        for (let parse of this.parser.leafBlockParsers)\n            if (parse) {\n                let parser = parse(this, leaf);\n                if (parser)\n                    leaf.parsers.push(parser);\n            }\n        lines: while (this.nextLine()) {\n            if (line.pos == line.text.length)\n                break;\n            if (line.indent < line.baseIndent + 4) {\n                for (let stop of this.parser.endLeafBlock)\n                    if (stop(this, line, leaf))\n                        break lines;\n            }\n            for (let parser of leaf.parsers)\n                if (parser.nextLine(this, line, leaf))\n                    return null;\n            leaf.content += \"\\n\" + line.scrub();\n            for (let m of line.markers)\n                leaf.marks.push(m);\n        }\n        this.finishLeaf(leaf);\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    reuseFragment(start) {\n        if (!this.fragments.moveTo(this.absoluteLineStart + start, this.absoluteLineStart) ||\n            !this.fragments.matches(this.block.hash))\n            return false;\n        let taken = this.fragments.takeNodes(this);\n        if (!taken)\n            return false;\n        let withoutGaps = taken, end = this.absoluteLineStart + taken;\n        for (let i = 1; i < this.ranges.length; i++) {\n            let gapFrom = this.ranges[i - 1].to, gapTo = this.ranges[i].from;\n            if (gapFrom >= this.lineStart && gapTo < end)\n                withoutGaps -= gapTo - gapFrom;\n        }\n        this.lineStart += withoutGaps;\n        this.absoluteLineStart += taken;\n        this.moveRangeI();\n        if (this.absoluteLineStart < this.to) {\n            this.lineStart++;\n            this.absoluteLineStart++;\n            this.readLine();\n        }\n        else {\n            this.atEnd = true;\n            this.readLine();\n        }\n        return true;\n    }\n    /// The number of parent blocks surrounding the current block.\n    get depth() {\n        return this.stack.length;\n    }\n    /// Get the type of the parent block at the given depth. When no\n    /// depth is passed, return the type of the innermost parent.\n    parentType(depth = this.depth - 1) {\n        return this.parser.nodeSet.types[this.stack[depth].type];\n    }\n    /// Move to the next input line. This should only be called by\n    /// (non-composite) [block parsers](#BlockParser.parse) that consume\n    /// the line directly, or leaf block parser\n    /// [`nextLine`](#LeafBlockParser.nextLine) methods when they\n    /// consume the current line (and return true).\n    nextLine() {\n        this.lineStart += this.line.text.length;\n        if (this.absoluteLineEnd >= this.to) {\n            this.absoluteLineStart = this.absoluteLineEnd;\n            this.atEnd = true;\n            this.readLine();\n            return false;\n        }\n        else {\n            this.lineStart++;\n            this.absoluteLineStart = this.absoluteLineEnd + 1;\n            this.moveRangeI();\n            this.readLine();\n            return true;\n        }\n    }\n    moveRangeI() {\n        while (this.rangeI < this.ranges.length - 1 && this.absoluteLineStart >= this.ranges[this.rangeI].to) {\n            this.rangeI++;\n            this.absoluteLineStart = Math.max(this.absoluteLineStart, this.ranges[this.rangeI].from);\n        }\n    }\n    /// @internal\n    scanLine(start) {\n        let r = scanLineResult;\n        r.end = start;\n        if (start >= this.to) {\n            r.text = \"\";\n        }\n        else {\n            r.text = this.lineChunkAt(start);\n            r.end += r.text.length;\n            if (this.ranges.length > 1) {\n                let textOffset = this.absoluteLineStart, rangeI = this.rangeI;\n                while (this.ranges[rangeI].to < r.end) {\n                    rangeI++;\n                    let nextFrom = this.ranges[rangeI].from;\n                    let after = this.lineChunkAt(nextFrom);\n                    r.end = nextFrom + after.length;\n                    r.text = r.text.slice(0, this.ranges[rangeI - 1].to - textOffset) + after;\n                    textOffset = r.end - r.text.length;\n                }\n            }\n        }\n        return r;\n    }\n    /// @internal\n    readLine() {\n        let { line } = this, { text, end } = this.scanLine(this.absoluteLineStart);\n        this.absoluteLineEnd = end;\n        line.reset(text);\n        for (; line.depth < this.stack.length; line.depth++) {\n            let cx = this.stack[line.depth], handler = this.parser.skipContextMarkup[cx.type];\n            if (!handler)\n                throw new Error(\"Unhandled block context \" + Type[cx.type]);\n            if (!handler(cx, this, line))\n                break;\n            line.forward();\n        }\n    }\n    lineChunkAt(pos) {\n        let next = this.input.chunk(pos), text;\n        if (!this.input.lineChunks) {\n            let eol = next.indexOf(\"\\n\");\n            text = eol < 0 ? next : next.slice(0, eol);\n        }\n        else {\n            text = next == \"\\n\" ? \"\" : next;\n        }\n        return pos + text.length > this.to ? text.slice(0, this.to - pos) : text;\n    }\n    /// The end position of the previous line.\n    prevLineEnd() { return this.atEnd ? this.lineStart : this.lineStart - 1; }\n    /// @internal\n    startContext(type, start, value = 0) {\n        this.block = CompositeBlock.create(type, value, this.lineStart + start, this.block.hash, this.lineStart + this.line.text.length);\n        this.stack.push(this.block);\n    }\n    /// Start a composite block. Should only be called from [block\n    /// parser functions](#BlockParser.parse) that return null.\n    startComposite(type, start, value = 0) {\n        this.startContext(this.parser.getNodeType(type), start, value);\n    }\n    /// @internal\n    addNode(block, from, to) {\n        if (typeof block == \"number\")\n            block = new Tree(this.parser.nodeSet.types[block], none, none, (to !== null && to !== void 0 ? to : this.prevLineEnd()) - from);\n        this.block.addChild(block, from - this.block.from);\n    }\n    /// Add a block element. Can be called by [block\n    /// parsers](#BlockParser.parse).\n    addElement(elt) {\n        this.block.addChild(elt.toTree(this.parser.nodeSet), elt.from - this.block.from);\n    }\n    /// Add a block element from a [leaf parser](#LeafBlockParser). This\n    /// makes sure any extra composite block markup (such as blockquote\n    /// markers) inside the block are also added to the syntax tree.\n    addLeafElement(leaf, elt) {\n        this.addNode(this.buffer\n            .writeElements(injectMarks(elt.children, leaf.marks), -elt.from)\n            .finish(elt.type, elt.to - elt.from), elt.from);\n    }\n    /// @internal\n    finishContext() {\n        let cx = this.stack.pop();\n        let top = this.stack[this.stack.length - 1];\n        top.addChild(cx.toTree(this.parser.nodeSet), cx.from - top.from);\n        this.block = top;\n    }\n    finish() {\n        while (this.stack.length > 1)\n            this.finishContext();\n        return this.addGaps(this.block.toTree(this.parser.nodeSet, this.lineStart));\n    }\n    addGaps(tree) {\n        return this.ranges.length > 1 ? injectGaps(this.ranges, 0, tree.topNode, this.ranges[0].from, this.dontInject) : tree;\n    }\n    /// @internal\n    finishLeaf(leaf) {\n        for (let parser of leaf.parsers)\n            if (parser.finish(this, leaf))\n                return;\n        let inline = injectMarks(this.parser.parseInline(leaf.content, leaf.start), leaf.marks);\n        this.addNode(this.buffer\n            .writeElements(inline, -leaf.start)\n            .finish(Type.Paragraph, leaf.content.length), leaf.start);\n    }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n    /// @internal\n    get buffer() { return new Buffer(this.parser.nodeSet); }\n}\nfunction injectGaps(ranges, rangeI, tree, offset, dont) {\n    if (dont.has(tree.tree))\n        return tree.tree;\n    let rangeEnd = ranges[rangeI].to;\n    let children = [], positions = [], start = tree.from + offset;\n    function movePastNext(upto, inclusive) {\n        while (inclusive ? upto >= rangeEnd : upto > rangeEnd) {\n            let size = ranges[rangeI + 1].from - rangeEnd;\n            offset += size;\n            upto += size;\n            rangeI++;\n            rangeEnd = ranges[rangeI].to;\n        }\n    }\n    for (let ch = tree.firstChild; ch; ch = ch.nextSibling) {\n        movePastNext(ch.from + offset, true);\n        let from = ch.from + offset, node;\n        if (ch.to + offset > rangeEnd) {\n            node = injectGaps(ranges, rangeI, ch, offset, dont);\n            movePastNext(ch.to + offset, false);\n        }\n        else {\n            node = ch.toTree();\n        }\n        children.push(node);\n        positions.push(from - start);\n    }\n    movePastNext(tree.to + offset, false);\n    return new Tree(tree.type, children, positions, tree.to + offset - start, tree.tree ? tree.tree.propValues : undefined);\n}\n/// A Markdown parser configuration.\nclass MarkdownParser extends Parser {\n    /// @internal\n    constructor(\n    /// The parser's syntax [node\n    /// types](https://lezer.codemirror.net/docs/ref/#common.NodeSet).\n    nodeSet, \n    /// @internal\n    blockParsers, \n    /// @internal\n    leafBlockParsers, \n    /// @internal\n    blockNames, \n    /// @internal\n    endLeafBlock, \n    /// @internal\n    skipContextMarkup, \n    /// @internal\n    inlineParsers, \n    /// @internal\n    inlineNames, \n    /// @internal\n    wrappers) {\n        super();\n        this.nodeSet = nodeSet;\n        this.blockParsers = blockParsers;\n        this.leafBlockParsers = leafBlockParsers;\n        this.blockNames = blockNames;\n        this.endLeafBlock = endLeafBlock;\n        this.skipContextMarkup = skipContextMarkup;\n        this.inlineParsers = inlineParsers;\n        this.inlineNames = inlineNames;\n        this.wrappers = wrappers;\n        /// @internal\n        this.nodeTypes = Object.create(null);\n        for (let t of nodeSet.types)\n            this.nodeTypes[t.name] = t.id;\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new BlockContext(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /// Reconfigure the parser.\n    configure(spec) {\n        let config = resolveConfig(spec);\n        if (!config)\n            return this;\n        let { nodeSet, skipContextMarkup } = this;\n        let blockParsers = this.blockParsers.slice(), leafBlockParsers = this.leafBlockParsers.slice(), blockNames = this.blockNames.slice(), inlineParsers = this.inlineParsers.slice(), inlineNames = this.inlineNames.slice(), endLeafBlock = this.endLeafBlock.slice(), wrappers = this.wrappers;\n        if (nonEmpty(config.defineNodes)) {\n            skipContextMarkup = Object.assign({}, skipContextMarkup);\n            let nodeTypes = nodeSet.types.slice(), styles;\n            for (let s of config.defineNodes) {\n                let { name, block, composite, style } = typeof s == \"string\" ? { name: s } : s;\n                if (nodeTypes.some(t => t.name == name))\n                    continue;\n                if (composite)\n                    skipContextMarkup[nodeTypes.length] =\n                        (bl, cx, line) => composite(cx, line, bl.value);\n                let id = nodeTypes.length;\n                let group = composite ? [\"Block\", \"BlockContext\"] : !block ? undefined\n                    : id >= Type.ATXHeading1 && id <= Type.SetextHeading2 ? [\"Block\", \"LeafBlock\", \"Heading\"] : [\"Block\", \"LeafBlock\"];\n                nodeTypes.push(NodeType.define({\n                    id,\n                    name,\n                    props: group && [[NodeProp.group, group]]\n                }));\n                if (style) {\n                    if (!styles)\n                        styles = {};\n                    if (Array.isArray(style) || style instanceof Tag)\n                        styles[name] = style;\n                    else\n                        Object.assign(styles, style);\n                }\n            }\n            nodeSet = new NodeSet(nodeTypes);\n            if (styles)\n                nodeSet = nodeSet.extend(styleTags(styles));\n        }\n        if (nonEmpty(config.props))\n            nodeSet = nodeSet.extend(...config.props);\n        if (nonEmpty(config.remove)) {\n            for (let rm of config.remove) {\n                let block = this.blockNames.indexOf(rm), inline = this.inlineNames.indexOf(rm);\n                if (block > -1)\n                    blockParsers[block] = leafBlockParsers[block] = undefined;\n                if (inline > -1)\n                    inlineParsers[inline] = undefined;\n            }\n        }\n        if (nonEmpty(config.parseBlock)) {\n            for (let spec of config.parseBlock) {\n                let found = blockNames.indexOf(spec.name);\n                if (found > -1) {\n                    blockParsers[found] = spec.parse;\n                    leafBlockParsers[found] = spec.leaf;\n                }\n                else {\n                    let pos = spec.before ? findName(blockNames, spec.before)\n                        : spec.after ? findName(blockNames, spec.after) + 1 : blockNames.length - 1;\n                    blockParsers.splice(pos, 0, spec.parse);\n                    leafBlockParsers.splice(pos, 0, spec.leaf);\n                    blockNames.splice(pos, 0, spec.name);\n                }\n                if (spec.endLeaf)\n                    endLeafBlock.push(spec.endLeaf);\n            }\n        }\n        if (nonEmpty(config.parseInline)) {\n            for (let spec of config.parseInline) {\n                let found = inlineNames.indexOf(spec.name);\n                if (found > -1) {\n                    inlineParsers[found] = spec.parse;\n                }\n                else {\n                    let pos = spec.before ? findName(inlineNames, spec.before)\n                        : spec.after ? findName(inlineNames, spec.after) + 1 : inlineNames.length - 1;\n                    inlineParsers.splice(pos, 0, spec.parse);\n                    inlineNames.splice(pos, 0, spec.name);\n                }\n            }\n        }\n        if (config.wrap)\n            wrappers = wrappers.concat(config.wrap);\n        return new MarkdownParser(nodeSet, blockParsers, leafBlockParsers, blockNames, endLeafBlock, skipContextMarkup, inlineParsers, inlineNames, wrappers);\n    }\n    /// @internal\n    getNodeType(name) {\n        let found = this.nodeTypes[name];\n        if (found == null)\n            throw new RangeError(`Unknown node type '${name}'`);\n        return found;\n    }\n    /// Parse the given piece of inline text at the given offset,\n    /// returning an array of [`Element`](#Element) objects representing\n    /// the inline content.\n    parseInline(text, offset) {\n        let cx = new InlineContext(this, text, offset);\n        outer: for (let pos = offset; pos < cx.end;) {\n            let next = cx.char(pos);\n            for (let token of this.inlineParsers)\n                if (token) {\n                    let result = token(cx, next, pos);\n                    if (result >= 0) {\n                        pos = result;\n                        continue outer;\n                    }\n                }\n            pos++;\n        }\n        return cx.resolveMarkers(0);\n    }\n}\nfunction nonEmpty(a) {\n    return a != null && a.length > 0;\n}\nfunction resolveConfig(spec) {\n    if (!Array.isArray(spec))\n        return spec;\n    if (spec.length == 0)\n        return null;\n    let conf = resolveConfig(spec[0]);\n    if (spec.length == 1)\n        return conf;\n    let rest = resolveConfig(spec.slice(1));\n    if (!rest || !conf)\n        return conf || rest;\n    let conc = (a, b) => (a || none).concat(b || none);\n    let wrapA = conf.wrap, wrapB = rest.wrap;\n    return {\n        props: conc(conf.props, rest.props),\n        defineNodes: conc(conf.defineNodes, rest.defineNodes),\n        parseBlock: conc(conf.parseBlock, rest.parseBlock),\n        parseInline: conc(conf.parseInline, rest.parseInline),\n        remove: conc(conf.remove, rest.remove),\n        wrap: !wrapA ? wrapB : !wrapB ? wrapA :\n            (inner, input, fragments, ranges) => wrapA(wrapB(inner, input, fragments, ranges), input, fragments, ranges)\n    };\n}\nfunction findName(names, name) {\n    let found = names.indexOf(name);\n    if (found < 0)\n        throw new RangeError(`Position specified relative to unknown parser ${name}`);\n    return found;\n}\nlet nodeTypes = [NodeType.none];\nfor (let i = 1, name; name = Type[i]; i++) {\n    nodeTypes[i] = NodeType.define({\n        id: i,\n        name,\n        props: i >= Type.Escape ? [] : [[NodeProp.group, i in DefaultSkipMarkup ? [\"Block\", \"BlockContext\"] : [\"Block\", \"LeafBlock\"]]]\n    });\n}\nconst none = [];\nclass Buffer {\n    constructor(nodeSet) {\n        this.nodeSet = nodeSet;\n        this.content = [];\n        this.nodes = [];\n    }\n    write(type, from, to, children = 0) {\n        this.content.push(type, from, to, 4 + children * 4);\n        return this;\n    }\n    writeElements(elts, offset = 0) {\n        for (let e of elts)\n            e.writeTo(this, offset);\n        return this;\n    }\n    finish(type, length) {\n        return Tree.build({\n            buffer: this.content,\n            nodeSet: this.nodeSet,\n            reused: this.nodes,\n            topID: type,\n            length\n        });\n    }\n}\n/// Elements are used to compose syntax nodes during parsing.\nclass Element {\n    /// @internal\n    constructor(\n    /// The node's\n    /// [id](https://lezer.codemirror.net/docs/ref/#common.NodeType.id).\n    type, \n    /// The start of the node, as an offset from the start of the document.\n    from, \n    /// The end of the node.\n    to, \n    /// The node's child nodes @internal\n    children = none) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.children = children;\n    }\n    /// @internal\n    writeTo(buf, offset) {\n        let startOff = buf.content.length;\n        buf.writeElements(this.children, offset);\n        buf.content.push(this.type, this.from + offset, this.to + offset, buf.content.length + 4 - startOff);\n    }\n    /// @internal\n    toTree(nodeSet) {\n        return new Buffer(nodeSet).writeElements(this.children, -this.from).finish(this.type, this.to - this.from);\n    }\n}\nclass TreeElement {\n    constructor(tree, from) {\n        this.tree = tree;\n        this.from = from;\n    }\n    get to() { return this.from + this.tree.length; }\n    get type() { return this.tree.type.id; }\n    get children() { return none; }\n    writeTo(buf, offset) {\n        buf.nodes.push(this.tree);\n        buf.content.push(buf.nodes.length - 1, this.from + offset, this.to + offset, -1);\n    }\n    toTree() { return this.tree; }\n}\nfunction elt(type, from, to, children) {\n    return new Element(type, from, to, children);\n}\nconst EmphasisUnderscore = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst EmphasisAsterisk = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst LinkStart = {}, ImageStart = {};\nclass InlineDelimiter {\n    constructor(type, from, to, side) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.side = side;\n    }\n}\nconst Escapable = \"!\\\"#$%&'()*+,-./:;<=>?@[\\\\]^_`{|}~\";\nlet Punctuation = /[!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~\\xA1\\u2010-\\u2027]/;\ntry {\n    Punctuation = new RegExp(\"[\\\\p{Pc}|\\\\p{Pd}|\\\\p{Pe}|\\\\p{Pf}|\\\\p{Pi}|\\\\p{Po}|\\\\p{Ps}]\", \"u\");\n}\ncatch (_) { }\nconst DefaultInline = {\n    Escape(cx, next, start) {\n        if (next != 92 /* '\\\\' */ || start == cx.end - 1)\n            return -1;\n        let escaped = cx.char(start + 1);\n        for (let i = 0; i < Escapable.length; i++)\n            if (Escapable.charCodeAt(i) == escaped)\n                return cx.append(elt(Type.Escape, start, start + 2));\n        return -1;\n    },\n    Entity(cx, next, start) {\n        if (next != 38 /* '&' */)\n            return -1;\n        let m = /^(?:#\\d+|#x[a-f\\d]+|\\w+);/i.exec(cx.slice(start + 1, start + 31));\n        return m ? cx.append(elt(Type.Entity, start, start + 1 + m[0].length)) : -1;\n    },\n    InlineCode(cx, next, start) {\n        if (next != 96 /* '`' */ || start && cx.char(start - 1) == 96)\n            return -1;\n        let pos = start + 1;\n        while (pos < cx.end && cx.char(pos) == 96)\n            pos++;\n        let size = pos - start, curSize = 0;\n        for (; pos < cx.end; pos++) {\n            if (cx.char(pos) == 96) {\n                curSize++;\n                if (curSize == size && cx.char(pos + 1) != 96)\n                    return cx.append(elt(Type.InlineCode, start, pos + 1, [\n                        elt(Type.CodeMark, start, start + size),\n                        elt(Type.CodeMark, pos + 1 - size, pos + 1)\n                    ]));\n            }\n            else {\n                curSize = 0;\n            }\n        }\n        return -1;\n    },\n    HTMLTag(cx, next, start) {\n        if (next != 60 /* '<' */ || start == cx.end - 1)\n            return -1;\n        let after = cx.slice(start + 1, cx.end);\n        let url = /^(?:[a-z][-\\w+.]+:[^\\s>]+|[a-z\\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?(?:\\.[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?)*)>/i.exec(after);\n        if (url)\n            return cx.append(elt(Type.URL, start, start + 1 + url[0].length));\n        let comment = /^!--[^>](?:-[^-]|[^-])*?-->/i.exec(after);\n        if (comment)\n            return cx.append(elt(Type.Comment, start, start + 1 + comment[0].length));\n        let procInst = /^\\?[^]*?\\?>/.exec(after);\n        if (procInst)\n            return cx.append(elt(Type.ProcessingInstruction, start, start + 1 + procInst[0].length));\n        let m = /^(?:![A-Z][^]*?>|!\\[CDATA\\[[^]*?\\]\\]>|\\/\\s*[a-zA-Z][\\w-]*\\s*>|\\s*[a-zA-Z][\\w-]*(\\s+[a-zA-Z:_][\\w-.:]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*(\\/\\s*)?>)/.exec(after);\n        if (!m)\n            return -1;\n        return cx.append(elt(Type.HTMLTag, start, start + 1 + m[0].length));\n    },\n    Emphasis(cx, next, start) {\n        if (next != 95 && next != 42)\n            return -1;\n        let pos = start + 1;\n        while (cx.char(pos) == next)\n            pos++;\n        let before = cx.slice(start - 1, start), after = cx.slice(pos, pos + 1);\n        let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n        let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n        let leftFlanking = !sAfter && (!pAfter || sBefore || pBefore);\n        let rightFlanking = !sBefore && (!pBefore || sAfter || pAfter);\n        let canOpen = leftFlanking && (next == 42 || !rightFlanking || pBefore);\n        let canClose = rightFlanking && (next == 42 || !leftFlanking || pAfter);\n        return cx.append(new InlineDelimiter(next == 95 ? EmphasisUnderscore : EmphasisAsterisk, start, pos, (canOpen ? 1 /* Open */ : 0) | (canClose ? 2 /* Close */ : 0)));\n    },\n    HardBreak(cx, next, start) {\n        if (next == 92 /* '\\\\' */ && cx.char(start + 1) == 10 /* '\\n' */)\n            return cx.append(elt(Type.HardBreak, start, start + 2));\n        if (next == 32) {\n            let pos = start + 1;\n            while (cx.char(pos) == 32)\n                pos++;\n            if (cx.char(pos) == 10 && pos >= start + 2)\n                return cx.append(elt(Type.HardBreak, start, pos + 1));\n        }\n        return -1;\n    },\n    Link(cx, next, start) {\n        return next == 91 /* '[' */ ? cx.append(new InlineDelimiter(LinkStart, start, start + 1, 1 /* Open */)) : -1;\n    },\n    Image(cx, next, start) {\n        return next == 33 /* '!' */ && cx.char(start + 1) == 91 /* '[' */\n            ? cx.append(new InlineDelimiter(ImageStart, start, start + 2, 1 /* Open */)) : -1;\n    },\n    LinkEnd(cx, next, start) {\n        if (next != 93 /* ']' */)\n            return -1;\n        // Scanning back to the next link/image start marker\n        for (let i = cx.parts.length - 1; i >= 0; i--) {\n            let part = cx.parts[i];\n            if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart)) {\n                // If this one has been set invalid (because it would produce\n                // a nested link) or there's no valid link here ignore both.\n                if (!part.side || cx.skipSpace(part.to) == start && !/[(\\[]/.test(cx.slice(start + 1, start + 2))) {\n                    cx.parts[i] = null;\n                    return -1;\n                }\n                // Finish the content and replace the entire range in\n                // this.parts with the link/image node.\n                let content = cx.takeContent(i);\n                let link = cx.parts[i] = finishLink(cx, content, part.type == LinkStart ? Type.Link : Type.Image, part.from, start + 1);\n                // Set any open-link markers before this link to invalid.\n                if (part.type == LinkStart)\n                    for (let j = 0; j < i; j++) {\n                        let p = cx.parts[j];\n                        if (p instanceof InlineDelimiter && p.type == LinkStart)\n                            p.side = 0;\n                    }\n                return link.to;\n            }\n        }\n        return -1;\n    }\n};\nfunction finishLink(cx, content, type, start, startPos) {\n    let { text } = cx, next = cx.char(startPos), endPos = startPos;\n    content.unshift(elt(Type.LinkMark, start, start + (type == Type.Image ? 2 : 1)));\n    content.push(elt(Type.LinkMark, startPos - 1, startPos));\n    if (next == 40 /* '(' */) {\n        let pos = cx.skipSpace(startPos + 1);\n        let dest = parseURL(text, pos - cx.offset, cx.offset), title;\n        if (dest) {\n            pos = cx.skipSpace(dest.to);\n            title = parseLinkTitle(text, pos - cx.offset, cx.offset);\n            if (title)\n                pos = cx.skipSpace(title.to);\n        }\n        if (cx.char(pos) == 41 /* ')' */) {\n            content.push(elt(Type.LinkMark, startPos, startPos + 1));\n            endPos = pos + 1;\n            if (dest)\n                content.push(dest);\n            if (title)\n                content.push(title);\n            content.push(elt(Type.LinkMark, pos, endPos));\n        }\n    }\n    else if (next == 91 /* '[' */) {\n        let label = parseLinkLabel(text, startPos - cx.offset, cx.offset, false);\n        if (label) {\n            content.push(label);\n            endPos = label.to;\n        }\n    }\n    return elt(type, start, endPos, content);\n}\n// These return `null` when falling off the end of the input, `false`\n// when parsing fails otherwise (for use in the incremental link\n// reference parser).\nfunction parseURL(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next == 60 /* '<' */) {\n        for (let pos = start + 1; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (ch == 62 /* '>' */)\n                return elt(Type.URL, start + offset, pos + 1 + offset);\n            if (ch == 60 || ch == 10 /* '<\\n' */)\n                return false;\n        }\n        return null;\n    }\n    else {\n        let depth = 0, pos = start;\n        for (let escaped = false; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (space(ch)) {\n                break;\n            }\n            else if (escaped) {\n                escaped = false;\n            }\n            else if (ch == 40 /* '(' */) {\n                depth++;\n            }\n            else if (ch == 41 /* ')' */) {\n                if (!depth)\n                    break;\n                depth--;\n            }\n            else if (ch == 92 /* '\\\\' */) {\n                escaped = true;\n            }\n        }\n        return pos > start ? elt(Type.URL, start + offset, pos + offset) : pos == text.length ? null : false;\n    }\n}\nfunction parseLinkTitle(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next != 39 && next != 34 && next != 40 /* '\"\\'(' */)\n        return false;\n    let end = next == 40 ? 41 : next;\n    for (let pos = start + 1, escaped = false; pos < text.length; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == end)\n            return elt(Type.LinkTitle, start + offset, pos + 1 + offset);\n        else if (ch == 92 /* '\\\\' */)\n            escaped = true;\n    }\n    return null;\n}\nfunction parseLinkLabel(text, start, offset, requireNonWS) {\n    for (let escaped = false, pos = start + 1, end = Math.min(text.length, pos + 999); pos < end; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == 93 /* ']' */)\n            return requireNonWS ? false : elt(Type.LinkLabel, start + offset, pos + 1 + offset);\n        else {\n            if (requireNonWS && !space(ch))\n                requireNonWS = false;\n            if (ch == 91 /* '[' */)\n                return false;\n            else if (ch == 92 /* '\\\\' */)\n                escaped = true;\n        }\n    }\n    return null;\n}\n/// Inline parsing functions get access to this context, and use it to\n/// read the content and emit syntax nodes.\nclass InlineContext {\n    /// @internal\n    constructor(\n    /// The parser that is being used.\n    parser, \n    /// The text of this inline section.\n    text, \n    /// The starting offset of the section in the document.\n    offset) {\n        this.parser = parser;\n        this.text = text;\n        this.offset = offset;\n        /// @internal\n        this.parts = [];\n    }\n    /// Get the character code at the given (document-relative)\n    /// position.\n    char(pos) { return pos >= this.end ? -1 : this.text.charCodeAt(pos - this.offset); }\n    /// The position of the end of this inline section.\n    get end() { return this.offset + this.text.length; }\n    /// Get a substring of this inline section. Again uses\n    /// document-relative positions.\n    slice(from, to) { return this.text.slice(from - this.offset, to - this.offset); }\n    /// @internal\n    append(elt) {\n        this.parts.push(elt);\n        return elt.to;\n    }\n    /// Add a [delimiter](#DelimiterType) at this given position. `open`\n    /// and `close` indicate whether this delimiter is opening, closing,\n    /// or both. Returns the end of the delimiter, for convenient\n    /// returning from [parse functions](#InlineParser.parse).\n    addDelimiter(type, from, to, open, close) {\n        return this.append(new InlineDelimiter(type, from, to, (open ? 1 /* Open */ : 0) | (close ? 2 /* Close */ : 0)));\n    }\n    /// Add an inline element. Returns the end of the element.\n    addElement(elt) {\n        return this.append(elt);\n    }\n    /// Resolve markers between this.parts.length and from, wrapping matched markers in the\n    /// appropriate node and updating the content of this.parts. @internal\n    resolveMarkers(from) {\n        // Scan forward, looking for closing tokens\n        for (let i = from; i < this.parts.length; i++) {\n            let close = this.parts[i];\n            if (!(close instanceof InlineDelimiter && close.type.resolve && (close.side & 2 /* Close */)))\n                continue;\n            let emp = close.type == EmphasisUnderscore || close.type == EmphasisAsterisk;\n            let closeSize = close.to - close.from;\n            let open, j = i - 1;\n            // Continue scanning for a matching opening token\n            for (; j >= from; j--) {\n                let part = this.parts[j];\n                if (part instanceof InlineDelimiter && (part.side & 1 /* Open */) && part.type == close.type &&\n                    // Ignore emphasis delimiters where the character count doesn't match\n                    !(emp && ((close.side & 1 /* Open */) || (part.side & 2 /* Close */)) &&\n                        (part.to - part.from + closeSize) % 3 == 0 && ((part.to - part.from) % 3 || closeSize % 3))) {\n                    open = part;\n                    break;\n                }\n            }\n            if (!open)\n                continue;\n            let type = close.type.resolve, content = [];\n            let start = open.from, end = close.to;\n            // Emphasis marker effect depends on the character count. Size consumed is minimum of the two\n            // markers.\n            if (emp) {\n                let size = Math.min(2, open.to - open.from, closeSize);\n                start = open.to - size;\n                end = close.from + size;\n                type = size == 1 ? \"Emphasis\" : \"StrongEmphasis\";\n            }\n            // Move the covered region into content, optionally adding marker nodes\n            if (open.type.mark)\n                content.push(this.elt(open.type.mark, start, open.to));\n            for (let k = j + 1; k < i; k++) {\n                if (this.parts[k] instanceof Element)\n                    content.push(this.parts[k]);\n                this.parts[k] = null;\n            }\n            if (close.type.mark)\n                content.push(this.elt(close.type.mark, close.from, end));\n            let element = this.elt(type, start, end, content);\n            // If there are leftover emphasis marker characters, shrink the close/open markers. Otherwise, clear them.\n            this.parts[j] = emp && open.from != start ? new InlineDelimiter(open.type, open.from, start, open.side) : null;\n            let keep = this.parts[i] = emp && close.to != end ? new InlineDelimiter(close.type, end, close.to, close.side) : null;\n            // Insert the new element in this.parts\n            if (keep)\n                this.parts.splice(i, 0, element);\n            else\n                this.parts[i] = element;\n        }\n        // Collect the elements remaining in this.parts into an array.\n        let result = [];\n        for (let i = from; i < this.parts.length; i++) {\n            let part = this.parts[i];\n            if (part instanceof Element)\n                result.push(part);\n        }\n        return result;\n    }\n    /// Find an opening delimiter of the given type. Returns `null` if\n    /// no delimiter is found, or an index that can be passed to\n    /// [`takeContent`](#InlineContext.takeContent) otherwise.\n    findOpeningDelimiter(type) {\n        for (let i = this.parts.length - 1; i >= 0; i--) {\n            let part = this.parts[i];\n            if (part instanceof InlineDelimiter && part.type == type)\n                return i;\n        }\n        return null;\n    }\n    /// Remove all inline elements and delimiters starting from the\n    /// given index (which you should get from\n    /// [`findOpeningDelimiter`](#InlineContext.findOpeningDelimiter),\n    /// resolve delimiters inside of them, and return them as an array\n    /// of elements.\n    takeContent(startIndex) {\n        let content = this.resolveMarkers(startIndex);\n        this.parts.length = startIndex;\n        return content;\n    }\n    /// Skip space after the given (document) position, returning either\n    /// the position of the next non-space character or the end of the\n    /// section.\n    skipSpace(from) { return skipSpace(this.text, from - this.offset) + this.offset; }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n}\nfunction injectMarks(elements, marks) {\n    if (!marks.length)\n        return elements;\n    if (!elements.length)\n        return marks;\n    let elts = elements.slice(), eI = 0;\n    for (let mark of marks) {\n        while (eI < elts.length && elts[eI].to < mark.to)\n            eI++;\n        if (eI < elts.length && elts[eI].from < mark.from) {\n            let e = elts[eI];\n            if (e instanceof Element)\n                elts[eI] = new Element(e.type, e.from, e.to, injectMarks(e.children, [mark]));\n        }\n        else {\n            elts.splice(eI++, 0, mark);\n        }\n    }\n    return elts;\n}\n// These are blocks that can span blank lines, and should thus only be\n// reused if their next sibling is also being reused.\nconst NotLast = [Type.CodeBlock, Type.ListItem, Type.OrderedList, Type.BulletList];\nclass FragmentCursor {\n    constructor(fragments, input) {\n        this.fragments = fragments;\n        this.input = input;\n        // Index into fragment array\n        this.i = 0;\n        // Active fragment\n        this.fragment = null;\n        this.fragmentEnd = -1;\n        // Cursor into the current fragment, if any. When `moveTo` returns\n        // true, this points at the first block after `pos`.\n        this.cursor = null;\n        if (fragments.length)\n            this.fragment = fragments[this.i++];\n    }\n    nextFragment() {\n        this.fragment = this.i < this.fragments.length ? this.fragments[this.i++] : null;\n        this.cursor = null;\n        this.fragmentEnd = -1;\n    }\n    moveTo(pos, lineStart) {\n        while (this.fragment && this.fragment.to <= pos)\n            this.nextFragment();\n        if (!this.fragment || this.fragment.from > (pos ? pos - 1 : 0))\n            return false;\n        if (this.fragmentEnd < 0) {\n            let end = this.fragment.to;\n            while (end > 0 && this.input.read(end - 1, end) != \"\\n\")\n                end--;\n            this.fragmentEnd = end ? end - 1 : 0;\n        }\n        let c = this.cursor;\n        if (!c) {\n            c = this.cursor = this.fragment.tree.cursor();\n            c.firstChild();\n        }\n        let rPos = pos + this.fragment.offset;\n        while (c.to <= rPos)\n            if (!c.parent())\n                return false;\n        for (;;) {\n            if (c.from >= rPos)\n                return this.fragment.from <= lineStart;\n            if (!c.childAfter(rPos))\n                return false;\n        }\n    }\n    matches(hash) {\n        let tree = this.cursor.tree;\n        return tree && tree.prop(NodeProp.contextHash) == hash;\n    }\n    takeNodes(cx) {\n        let cur = this.cursor, off = this.fragment.offset, fragEnd = this.fragmentEnd - (this.fragment.openEnd ? 1 : 0);\n        let start = cx.absoluteLineStart, end = start, blockI = cx.block.children.length;\n        let prevEnd = end, prevI = blockI;\n        for (;;) {\n            if (cur.to - off > fragEnd) {\n                if (cur.type.isAnonymous && cur.firstChild())\n                    continue;\n                break;\n            }\n            cx.dontInject.add(cur.tree);\n            cx.addNode(cur.tree, cur.from - off);\n            // Taken content must always end in a block, because incremental\n            // parsing happens on block boundaries. Never stop directly\n            // after an indented code block, since those can continue after\n            // any number of blank lines.\n            if (cur.type.is(\"Block\")) {\n                if (NotLast.indexOf(cur.type.id) < 0) {\n                    end = cur.to - off;\n                    blockI = cx.block.children.length;\n                }\n                else {\n                    end = prevEnd;\n                    blockI = prevI;\n                    prevEnd = cur.to - off;\n                    prevI = cx.block.children.length;\n                }\n            }\n            if (!cur.nextSibling())\n                break;\n        }\n        while (cx.block.children.length > blockI) {\n            cx.block.children.pop();\n            cx.block.positions.pop();\n        }\n        return end - start;\n    }\n}\nconst markdownHighlighting = styleTags({\n    \"Blockquote/...\": tags.quote,\n    HorizontalRule: tags.contentSeparator,\n    \"ATXHeading1/... SetextHeading1/...\": tags.heading1,\n    \"ATXHeading2/... SetextHeading2/...\": tags.heading2,\n    \"ATXHeading3/...\": tags.heading3,\n    \"ATXHeading4/...\": tags.heading4,\n    \"ATXHeading5/...\": tags.heading5,\n    \"ATXHeading6/...\": tags.heading6,\n    \"Comment CommentBlock\": tags.comment,\n    Escape: tags.escape,\n    Entity: tags.character,\n    \"Emphasis/...\": tags.emphasis,\n    \"StrongEmphasis/...\": tags.strong,\n    \"Link/... Image/...\": tags.link,\n    \"OrderedList/... BulletList/...\": tags.list,\n    \"BlockQuote/...\": tags.quote,\n    \"InlineCode CodeText\": tags.monospace,\n    URL: tags.url,\n    \"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark\": tags.processingInstruction,\n    \"CodeInfo LinkLabel\": tags.labelName,\n    LinkTitle: tags.string,\n    Paragraph: tags.content\n});\n/// The default CommonMark parser.\nconst parser = new MarkdownParser(new NodeSet(nodeTypes).extend(markdownHighlighting), Object.keys(DefaultBlockParsers).map(n => DefaultBlockParsers[n]), Object.keys(DefaultBlockParsers).map(n => DefaultLeafBlocks[n]), Object.keys(DefaultBlockParsers), DefaultEndLeaf, DefaultSkipMarkup, Object.keys(DefaultInline).map(n => DefaultInline[n]), Object.keys(DefaultInline), []);\n\nfunction leftOverSpace(node, from, to) {\n    let ranges = [];\n    for (let n = node.firstChild, pos = from;; n = n.nextSibling) {\n        let nextPos = n ? n.from : to;\n        if (nextPos > pos)\n            ranges.push({ from: pos, to: nextPos });\n        if (!n)\n            break;\n        pos = n.to;\n    }\n    return ranges;\n}\n/// Create a Markdown extension to enable nested parsing on code\n/// blocks and/or embedded HTML.\nfunction parseCode(config) {\n    let { codeParser, htmlParser } = config;\n    let wrap = parseMixed((node, input) => {\n        let id = node.type.id;\n        if (codeParser && (id == Type.CodeBlock || id == Type.FencedCode)) {\n            let info = \"\";\n            if (id == Type.FencedCode) {\n                let infoNode = node.node.getChild(Type.CodeInfo);\n                if (infoNode)\n                    info = input.read(infoNode.from, infoNode.to);\n            }\n            let parser = codeParser(info);\n            if (parser)\n                return { parser, overlay: node => node.type.id == Type.CodeText };\n        }\n        else if (htmlParser && (id == Type.HTMLBlock || id == Type.HTMLTag)) {\n            return { parser: htmlParser, overlay: leftOverSpace(node.node, node.from, node.to) };\n        }\n        return null;\n    });\n    return { wrap };\n}\n\nconst StrikethroughDelim = { resolve: \"Strikethrough\", mark: \"StrikethroughMark\" };\n/// An extension that implements\n/// [GFM-style](https://github.github.com/gfm/#strikethrough-extension-)\n/// Strikethrough syntax using `~~` delimiters.\nconst Strikethrough = {\n    defineNodes: [{\n            name: \"Strikethrough\",\n            style: { \"Strikethrough/...\": tags.strikethrough }\n        }, {\n            name: \"StrikethroughMark\",\n            style: tags.processingInstruction\n        }],\n    parseInline: [{\n            name: \"Strikethrough\",\n            parse(cx, next, pos) {\n                if (next != 126 /* '~' */ || cx.char(pos + 1) != 126 || cx.char(pos + 2) == 126)\n                    return -1;\n                let before = cx.slice(pos - 1, pos), after = cx.slice(pos + 2, pos + 3);\n                let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n                let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n                return cx.addDelimiter(StrikethroughDelim, pos, pos + 2, !sAfter && (!pAfter || sBefore || pBefore), !sBefore && (!pBefore || sAfter || pAfter));\n            },\n            after: \"Emphasis\"\n        }]\n};\nfunction parseRow(cx, line, startI = 0, elts, offset = 0) {\n    let count = 0, first = true, cellStart = -1, cellEnd = -1, esc = false;\n    let parseCell = () => {\n        elts.push(cx.elt(\"TableCell\", offset + cellStart, offset + cellEnd, cx.parser.parseInline(line.slice(cellStart, cellEnd), offset + cellStart)));\n    };\n    for (let i = startI; i < line.length; i++) {\n        let next = line.charCodeAt(i);\n        if (next == 124 /* '|' */ && !esc) {\n            if (!first || cellStart > -1)\n                count++;\n            first = false;\n            if (elts) {\n                if (cellStart > -1)\n                    parseCell();\n                elts.push(cx.elt(\"TableDelimiter\", i + offset, i + offset + 1));\n            }\n            cellStart = cellEnd = -1;\n        }\n        else if (esc || next != 32 && next != 9) {\n            if (cellStart < 0)\n                cellStart = i;\n            cellEnd = i + 1;\n        }\n        esc = !esc && next == 92;\n    }\n    if (cellStart > -1) {\n        count++;\n        if (elts)\n            parseCell();\n    }\n    return count;\n}\nfunction hasPipe(str, start) {\n    for (let i = start; i < str.length; i++) {\n        let next = str.charCodeAt(i);\n        if (next == 124 /* '|' */)\n            return true;\n        if (next == 92 /* '\\\\' */)\n            i++;\n    }\n    return false;\n}\nconst delimiterLine = /^\\|?(\\s*:?-+:?\\s*\\|)+(\\s*:?-+:?\\s*)?$/;\nclass TableParser {\n    constructor() {\n        // Null means we haven't seen the second line yet, false means this\n        // isn't a table, and an array means this is a table and we've\n        // parsed the given rows so far.\n        this.rows = null;\n    }\n    nextLine(cx, line, leaf) {\n        if (this.rows == null) { // Second line\n            this.rows = false;\n            let lineText;\n            if ((line.next == 45 || line.next == 58 || line.next == 124 /* '-:|' */) &&\n                delimiterLine.test(lineText = line.text.slice(line.pos))) {\n                let firstRow = [], firstCount = parseRow(cx, leaf.content, 0, firstRow, leaf.start);\n                if (firstCount == parseRow(cx, lineText, line.pos))\n                    this.rows = [cx.elt(\"TableHeader\", leaf.start, leaf.start + leaf.content.length, firstRow),\n                        cx.elt(\"TableDelimiter\", cx.lineStart + line.pos, cx.lineStart + line.text.length)];\n            }\n        }\n        else if (this.rows) { // Line after the second\n            let content = [];\n            parseRow(cx, line.text, line.pos, content, cx.lineStart);\n            this.rows.push(cx.elt(\"TableRow\", cx.lineStart + line.pos, cx.lineStart + line.text.length, content));\n        }\n        return false;\n    }\n    finish(cx, leaf) {\n        if (!this.rows)\n            return false;\n        cx.addLeafElement(leaf, cx.elt(\"Table\", leaf.start, leaf.start + leaf.content.length, this.rows));\n        return true;\n    }\n}\n/// This extension provides\n/// [GFM-style](https://github.github.com/gfm/#tables-extension-)\n/// tables, using syntax like this:\n///\n/// ```\n/// | head 1 | head 2 |\n/// | ---    | ---    |\n/// | cell 1 | cell 2 |\n/// ```\nconst Table = {\n    defineNodes: [\n        { name: \"Table\", block: true },\n        { name: \"TableHeader\", style: { \"TableHeader/...\": tags.heading } },\n        \"TableRow\",\n        { name: \"TableCell\", style: tags.content },\n        { name: \"TableDelimiter\", style: tags.processingInstruction },\n    ],\n    parseBlock: [{\n            name: \"Table\",\n            leaf(_, leaf) { return hasPipe(leaf.content, 0) ? new TableParser : null; },\n            endLeaf(cx, line, leaf) {\n                if (leaf.parsers.some(p => p instanceof TableParser) || !hasPipe(line.text, line.basePos))\n                    return false;\n                let next = cx.scanLine(cx.absoluteLineEnd + 1).text;\n                return delimiterLine.test(next) && parseRow(cx, line.text, line.basePos) == parseRow(cx, next, line.basePos);\n            },\n            before: \"SetextHeading\"\n        }]\n};\nclass TaskParser {\n    nextLine() { return false; }\n    finish(cx, leaf) {\n        cx.addLeafElement(leaf, cx.elt(\"Task\", leaf.start, leaf.start + leaf.content.length, [\n            cx.elt(\"TaskMarker\", leaf.start, leaf.start + 3),\n            ...cx.parser.parseInline(leaf.content.slice(3), leaf.start + 3)\n        ]));\n        return true;\n    }\n}\n/// Extension providing\n/// [GFM-style](https://github.github.com/gfm/#task-list-items-extension-)\n/// task list items, where list items can be prefixed with `[ ]` or\n/// `[x]` to add a checkbox.\nconst TaskList = {\n    defineNodes: [\n        { name: \"Task\", block: true, style: tags.list },\n        { name: \"TaskMarker\", style: tags.atom }\n    ],\n    parseBlock: [{\n            name: \"TaskList\",\n            leaf(cx, leaf) {\n                return /^\\[[ xX]\\]/.test(leaf.content) && cx.parentType().name == \"ListItem\" ? new TaskParser : null;\n            },\n            after: \"SetextHeading\"\n        }]\n};\n/// Extension bundle containing [`Table`](#Table),\n/// [`TaskList`](#TaskList) and [`Strikethrough`](#Strikethrough).\nconst GFM = [Table, TaskList, Strikethrough];\nfunction parseSubSuper(ch, node, mark) {\n    return (cx, next, pos) => {\n        if (next != ch || cx.char(pos + 1) == ch)\n            return -1;\n        let elts = [cx.elt(mark, pos, pos + 1)];\n        for (let i = pos + 1; i < cx.end; i++) {\n            let next = cx.char(i);\n            if (next == ch)\n                return cx.addElement(cx.elt(node, pos, i + 1, elts.concat(cx.elt(mark, i, i + 1))));\n            if (next == 92 /* '\\\\' */)\n                elts.push(cx.elt(\"Escape\", i, i++ + 2));\n            if (space(next))\n                break;\n        }\n        return -1;\n    };\n}\n/// Extension providing\n/// [Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\n/// superscript using `^` markers.\nconst Superscript = {\n    defineNodes: [\n        { name: \"Superscript\", style: tags.special(tags.content) },\n        { name: \"SuperscriptMark\", style: tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Superscript\",\n            parse: parseSubSuper(94 /* '^' */, \"Superscript\", \"SuperscriptMark\")\n        }]\n};\n/// Extension providing\n/// [Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\n/// subscript using `~` markers.\nconst Subscript = {\n    defineNodes: [\n        { name: \"Subscript\", style: tags.special(tags.content) },\n        { name: \"SubscriptMark\", style: tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Subscript\",\n            parse: parseSubSuper(126 /* '~' */, \"Subscript\", \"SubscriptMark\")\n        }]\n};\n/// Extension that parses two colons with only letters, underscores,\n/// and numbers between them as `Emoji` nodes.\nconst Emoji = {\n    defineNodes: [{ name: \"Emoji\", style: tags.character }],\n    parseInline: [{\n            name: \"Emoji\",\n            parse(cx, next, pos) {\n                let match;\n                if (next != 58 /* ':' */ || !(match = /^[a-zA-Z_0-9]+:/.exec(cx.slice(pos + 1, cx.end))))\n                    return -1;\n                return cx.addElement(cx.elt(\"Emoji\", pos, pos + 1 + match[0].length));\n            }\n        }]\n};\n\nexport { BlockContext, Element, Emoji, GFM, InlineContext, LeafBlock, Line, MarkdownParser, Strikethrough, Subscript, Superscript, Table, TaskList, parseCode, parser };\n", "import { EditorSelection, Prec } from '@codemirror/state';\nimport { keymap } from '@codemirror/view';\nimport { defineLanguageFacet, foldNodeProp, indentNodeProp, languageDataProp, foldService, syntaxTree, Language, LanguageDescription, ParseContext, LanguageSupport } from '@codemirror/language';\nimport { parser, GFM, Subscript, Superscript, Emoji, MarkdownParser, parseCode } from '@lezer/markdown';\nimport { html } from '@codemirror/lang-html';\nimport { NodeProp } from '@lezer/common';\n\nconst data = /*@__PURE__*/defineLanguageFacet({ block: { open: \"<!--\", close: \"-->\" } });\nconst headingProp = /*@__PURE__*/new NodeProp();\nconst commonmark = /*@__PURE__*/parser.configure({\n    props: [\n        /*@__PURE__*/foldNodeProp.add(type => {\n            return !type.is(\"Block\") || type.is(\"Document\") || isHeading(type) != null ? undefined\n                : (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to });\n        }),\n        /*@__PURE__*/headingProp.add(isHeading),\n        /*@__PURE__*/indentNodeProp.add({\n            Document: () => null\n        }),\n        /*@__PURE__*/languageDataProp.add({\n            Document: data\n        })\n    ]\n});\nfunction isHeading(type) {\n    let match = /^(?:ATX|Setext)Heading(\\d)$/.exec(type.name);\n    return match ? +match[1] : undefined;\n}\nfunction findSectionEnd(headerNode, level) {\n    let last = headerNode;\n    for (;;) {\n        let next = last.nextSibling, heading;\n        if (!next || (heading = isHeading(next.type)) != null && heading <= level)\n            break;\n        last = next;\n    }\n    return last.to;\n}\nconst headerIndent = /*@__PURE__*/foldService.of((state, start, end) => {\n    for (let node = syntaxTree(state).resolveInner(end, -1); node; node = node.parent) {\n        if (node.from < start)\n            break;\n        let heading = node.type.prop(headingProp);\n        if (heading == null)\n            continue;\n        let upto = findSectionEnd(node, heading);\n        if (upto > end)\n            return { from: end, to: upto };\n    }\n    return null;\n});\nfunction mkLang(parser) {\n    return new Language(data, parser, [headerIndent], \"markdown\");\n}\n/**\nLanguage support for strict CommonMark.\n*/\nconst commonmarkLanguage = /*@__PURE__*/mkLang(commonmark);\nconst extended = /*@__PURE__*/commonmark.configure([GFM, Subscript, Superscript, Emoji]);\n/**\nLanguage support for [GFM](https://github.github.com/gfm/) plus\nsubscript, superscript, and emoji syntax.\n*/\nconst markdownLanguage = /*@__PURE__*/mkLang(extended);\nfunction getCodeParser(languages, defaultLanguage) {\n    return (info) => {\n        if (info && languages) {\n            let found = null;\n            // Strip anything after whitespace\n            info = /\\S*/.exec(info)[0];\n            if (typeof languages == \"function\")\n                found = languages(info);\n            else\n                found = LanguageDescription.matchLanguageName(languages, info, true);\n            if (found instanceof LanguageDescription)\n                return found.support ? found.support.language.parser : ParseContext.getSkippingParser(found.load());\n            else if (found)\n                return found.parser;\n        }\n        return defaultLanguage ? defaultLanguage.parser : null;\n    };\n}\n\nclass Context {\n    constructor(node, from, to, spaceBefore, spaceAfter, type, item) {\n        this.node = node;\n        this.from = from;\n        this.to = to;\n        this.spaceBefore = spaceBefore;\n        this.spaceAfter = spaceAfter;\n        this.type = type;\n        this.item = item;\n    }\n    blank(maxWidth, trailing = true) {\n        let result = this.spaceBefore + (this.node.name == \"Blockquote\" ? \">\" : \"\");\n        if (maxWidth != null) {\n            while (result.length < maxWidth)\n                result += \" \";\n            return result;\n        }\n        else {\n            for (let i = this.to - this.from - result.length - this.spaceAfter.length; i > 0; i--)\n                result += \" \";\n            return result + (trailing ? this.spaceAfter : \"\");\n        }\n    }\n    marker(doc, add) {\n        let number = this.node.name == \"OrderedList\" ? String((+itemNumber(this.item, doc)[2] + add)) : \"\";\n        return this.spaceBefore + number + this.type + this.spaceAfter;\n    }\n}\nfunction getContext(node, doc) {\n    let nodes = [];\n    for (let cur = node; cur && cur.name != \"Document\"; cur = cur.parent) {\n        if (cur.name == \"ListItem\" || cur.name == \"Blockquote\" || cur.name == \"FencedCode\")\n            nodes.push(cur);\n    }\n    let context = [];\n    for (let i = nodes.length - 1; i >= 0; i--) {\n        let node = nodes[i], match;\n        let line = doc.lineAt(node.from), startPos = node.from - line.from;\n        if (node.name == \"FencedCode\") {\n            context.push(new Context(node, startPos, startPos, \"\", \"\", \"\", null));\n        }\n        else if (node.name == \"Blockquote\" && (match = /^[ \\t]*>( ?)/.exec(line.text.slice(startPos)))) {\n            context.push(new Context(node, startPos, startPos + match[0].length, \"\", match[1], \">\", null));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"OrderedList\" &&\n            (match = /^([ \\t]*)\\d+([.)])([ \\t]*)/.exec(line.text.slice(startPos)))) {\n            let after = match[3], len = match[0].length;\n            if (after.length >= 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, match[2], node));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"BulletList\" &&\n            (match = /^([ \\t]*)([-+*])([ \\t]{1,4}\\[[ xX]\\])?([ \\t]+)/.exec(line.text.slice(startPos)))) {\n            let after = match[4], len = match[0].length;\n            if (after.length > 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            let type = match[2];\n            if (match[3])\n                type += match[3].replace(/[xX]/, ' ');\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, type, node));\n        }\n    }\n    return context;\n}\nfunction itemNumber(item, doc) {\n    return /^(\\s*)(\\d+)(?=[.)])/.exec(doc.sliceString(item.from, item.from + 10));\n}\nfunction renumberList(after, doc, changes, offset = 0) {\n    for (let prev = -1, node = after;;) {\n        if (node.name == \"ListItem\") {\n            let m = itemNumber(node, doc);\n            let number = +m[2];\n            if (prev >= 0) {\n                if (number != prev + 1)\n                    return;\n                changes.push({ from: node.from + m[1].length, to: node.from + m[0].length, insert: String(prev + 2 + offset) });\n            }\n            prev = number;\n        }\n        let next = node.nextSibling;\n        if (!next)\n            break;\n        node = next;\n    }\n}\n/**\nThis command, when invoked in Markdown context with cursor\nselection(s), will create a new line with the markup for\nblockquotes and lists that were active on the old line. If the\ncursor was directly after the end of the markup for the old line,\ntrailing whitespace and list markers are removed from that line.\n\nThe command does nothing in non-Markdown context, so it should\nnot be used as the only binding for Enter (even in a Markdown\ndocument, HTML and code regions might use a different language).\n*/\nconst insertNewlineContinueMarkup = ({ state, dispatch }) => {\n    let tree = syntaxTree(state), { doc } = state;\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty || !markdownLanguage.isActiveAt(state, range.from))\n            return dont = { range };\n        let pos = range.from, line = doc.lineAt(pos);\n        let context = getContext(tree.resolveInner(pos, -1), doc);\n        while (context.length && context[context.length - 1].from > pos - line.from)\n            context.pop();\n        if (!context.length)\n            return dont = { range };\n        let inner = context[context.length - 1];\n        if (inner.to - inner.spaceAfter.length > pos - line.from)\n            return dont = { range };\n        let emptyLine = pos >= (inner.to - inner.spaceAfter.length) && !/\\S/.test(line.text.slice(inner.to));\n        // Empty line in list\n        if (inner.item && emptyLine) {\n            // First list item or blank line before: delete a level of markup\n            if (inner.node.firstChild.to >= pos ||\n                line.from > 0 && !/[^\\s>]/.test(doc.lineAt(line.from - 1).text)) {\n                let next = context.length > 1 ? context[context.length - 2] : null;\n                let delTo, insert = \"\";\n                if (next && next.item) { // Re-add marker for the list at the next level\n                    delTo = line.from + next.from;\n                    insert = next.marker(doc, 1);\n                }\n                else {\n                    delTo = line.from + (next ? next.to : 0);\n                }\n                let changes = [{ from: delTo, to: pos, insert }];\n                if (inner.node.name == \"OrderedList\")\n                    renumberList(inner.item, doc, changes, -2);\n                if (next && next.node.name == \"OrderedList\")\n                    renumberList(next.item, doc, changes);\n                return { range: EditorSelection.cursor(delTo + insert.length), changes };\n            }\n            else { // Move this line down\n                let insert = \"\";\n                for (let i = 0, e = context.length - 2; i <= e; i++) {\n                    insert += context[i].blank(i < e ? context[i + 1].from - insert.length : null, i < e);\n                }\n                insert += state.lineBreak;\n                return { range: EditorSelection.cursor(pos + insert.length), changes: { from: line.from, insert } };\n            }\n        }\n        if (inner.node.name == \"Blockquote\" && emptyLine && line.from) {\n            let prevLine = doc.lineAt(line.from - 1), quoted = />\\s*$/.exec(prevLine.text);\n            // Two aligned empty quoted lines in a row\n            if (quoted && quoted.index == inner.from) {\n                let changes = state.changes([{ from: prevLine.from + quoted.index, to: prevLine.to },\n                    { from: line.from + inner.from, to: line.to }]);\n                return { range: range.map(changes), changes };\n            }\n        }\n        let changes = [];\n        if (inner.node.name == \"OrderedList\")\n            renumberList(inner.item, doc, changes);\n        let continued = inner.item && inner.item.from < line.from;\n        let insert = \"\";\n        // If not dedented\n        if (!continued || /^[\\s\\d.)\\-+*>]*/.exec(line.text)[0].length >= inner.to) {\n            for (let i = 0, e = context.length - 1; i <= e; i++) {\n                insert += i == e && !continued ? context[i].marker(doc, 1)\n                    : context[i].blank(i < e ? context[i + 1].from - insert.length : null);\n            }\n        }\n        let from = pos;\n        while (from > line.from && /\\s/.test(line.text.charAt(from - line.from - 1)))\n            from--;\n        insert = state.lineBreak + insert;\n        changes.push({ from, to: pos, insert });\n        return { range: EditorSelection.cursor(from + insert.length), changes };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\nfunction isMark(node) {\n    return node.name == \"QuoteMark\" || node.name == \"ListMark\";\n}\nfunction contextNodeForDelete(tree, pos) {\n    let node = tree.resolveInner(pos, -1), scan = pos;\n    if (isMark(node)) {\n        scan = node.from;\n        node = node.parent;\n    }\n    for (let prev; prev = node.childBefore(scan);) {\n        if (isMark(prev)) {\n            scan = prev.from;\n        }\n        else if (prev.name == \"OrderedList\" || prev.name == \"BulletList\") {\n            node = prev.lastChild;\n            scan = node.to;\n        }\n        else {\n            break;\n        }\n    }\n    return node;\n}\n/**\nThis command will, when invoked in a Markdown context with the\ncursor directly after list or blockquote markup, delete one level\nof markup. When the markup is for a list, it will be replaced by\nspaces on the first invocation (a further invocation will delete\nthe spaces), to make it easy to continue a list.\n\nWhen not after Markdown block markup, this command will return\nfalse, so it is intended to be bound alongside other deletion\ncommands, with a higher precedence than the more generic commands.\n*/\nconst deleteMarkupBackward = ({ state, dispatch }) => {\n    let tree = syntaxTree(state);\n    let dont = null, changes = state.changeByRange(range => {\n        let pos = range.from, { doc } = state;\n        if (range.empty && markdownLanguage.isActiveAt(state, range.from)) {\n            let line = doc.lineAt(pos);\n            let context = getContext(contextNodeForDelete(tree, pos), doc);\n            if (context.length) {\n                let inner = context[context.length - 1];\n                let spaceEnd = inner.to - inner.spaceAfter.length + (inner.spaceAfter ? 1 : 0);\n                // Delete extra trailing space after markup\n                if (pos - line.from > spaceEnd && !/\\S/.test(line.text.slice(spaceEnd, pos - line.from)))\n                    return { range: EditorSelection.cursor(line.from + spaceEnd),\n                        changes: { from: line.from + spaceEnd, to: pos } };\n                if (pos - line.from == spaceEnd) {\n                    let start = line.from + inner.from;\n                    // Replace a list item marker with blank space\n                    if (inner.item && inner.node.from < inner.item.from && /\\S/.test(line.text.slice(inner.from, inner.to)))\n                        return { range, changes: { from: start, to: line.from + inner.to, insert: inner.blank(inner.to - inner.from) } };\n                    // Delete one level of indentation\n                    if (start < pos)\n                        return { range: EditorSelection.cursor(start), changes: { from: start, to: pos } };\n                }\n            }\n        }\n        return dont = { range };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"delete\" }));\n    return true;\n};\n\n/**\nA small keymap with Markdown-specific bindings. Binds Enter to\n[`insertNewlineContinueMarkup`](https://codemirror.net/6/docs/ref/#lang-markdown.insertNewlineContinueMarkup)\nand Backspace to\n[`deleteMarkupBackward`](https://codemirror.net/6/docs/ref/#lang-markdown.deleteMarkupBackward).\n*/\nconst markdownKeymap = [\n    { key: \"Enter\", run: insertNewlineContinueMarkup },\n    { key: \"Backspace\", run: deleteMarkupBackward }\n];\nconst htmlNoMatch = /*@__PURE__*/html({ matchClosingTags: false });\n/**\nMarkdown language support.\n*/\nfunction markdown(config = {}) {\n    let { codeLanguages, defaultCodeLanguage, addKeymap = true, base: { parser } = commonmarkLanguage } = config;\n    if (!(parser instanceof MarkdownParser))\n        throw new RangeError(\"Base parser provided to `markdown` should be a Markdown parser\");\n    let extensions = config.extensions ? [config.extensions] : [];\n    let support = [htmlNoMatch.support], defaultCode;\n    if (defaultCodeLanguage instanceof LanguageSupport) {\n        support.push(defaultCodeLanguage.support);\n        defaultCode = defaultCodeLanguage.language;\n    }\n    else if (defaultCodeLanguage) {\n        defaultCode = defaultCodeLanguage;\n    }\n    let codeParser = codeLanguages || defaultCode ? getCodeParser(codeLanguages, defaultCode) : undefined;\n    extensions.push(parseCode({ codeParser, htmlParser: htmlNoMatch.language.parser }));\n    if (addKeymap)\n        support.push(Prec.high(keymap.of(markdownKeymap)));\n    return new LanguageSupport(mkLang(parser.configure(extensions)), support);\n}\n\nexport { commonmarkLanguage, deleteMarkupBackward, insertNewlineContinueMarkup, markdown, markdownKeymap, markdownLanguage };\n"], "names": ["CompositeBlock", "type", "value", "from", "hash", "end", "children", "positions", "NodeProp", "parentHash", "child", "pos", "Tree", "nodeSet", "last", "length", "NodeType", "Type", "LeafBlock", "start", "content", "Line", "newPos", "skipSpace", "text", "to", "indent", "elt", "i", "goal", "result", "skipForList", "bl", "cx", "line", "size", "isOrderedList", "isBulletList", "isHorizontalRule", "DefaultSkipMarkup", "space", "_cx", "ch", "skipSpaceBack", "isFencedCode", "isBlockquote", "breaking", "count", "isSetextUnderline", "inList", "next", "isAtxHeading", "EmptyLine", "CommentEnd", "ProcessingEnd", "HTMLBlockStyle", "isHTMLBlock", "rest", "e", "getListIndent", "indentAfter", "indented", "addCodeText", "marks", "DefaultBlockParsers", "base", "pendingMarks", "m", "codeStart", "fenceEnd", "len", "infoFrom", "infoTo", "first", "textStart", "textEnd", "newBase", "off", "endOfSpace", "after", "buf", "node", "trailing", "nodeType", "LinkReferenceParser", "leaf", "finish", "parseLinkLabel", "parseURL", "skip", "title", "parseLinkTitle", "titleEnd", "lineEnd", "SetextHeadingParser", "underline", "underlineMark", "DefaultLeafBlocks", "_", "DefaultEndLeaf", "p", "scanLineResult", "BlockContext", "parser", "input", "fragments", "ranges", "FragmentCursor", "mark", "parse", "lines", "stop", "taken", "withoutGaps", "gapFrom", "gapTo", "depth", "textOffset", "rangeI", "nextFrom", "handler", "eol", "block", "none", "injectMarks", "top", "tree", "injectGaps", "inline", "TreeElement", "<PERSON><PERSON><PERSON>", "offset", "dont", "rangeEnd", "movePastNext", "upto", "inclusive", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "blockParsers", "leafBlockParsers", "blockNames", "endLeafBlock", "skipContextMarkup", "inlineParsers", "inlineNames", "wrappers", "t", "w", "spec", "config", "resolveConfig", "nonEmpty", "nodeTypes", "styles", "s", "name", "composite", "style", "id", "group", "Tag", "NodeSet", "styleTags", "rm", "found", "<PERSON><PERSON><PERSON>", "InlineContext", "outer", "token", "a", "conf", "conc", "b", "wrapA", "wrapB", "inner", "names", "elts", "Element", "startOff", "EmphasisUnderscore", "EmphasisAsterisk", "LinkStart", "ImageStart", "InlineDelimiter", "side", "Escapable", "Punctuation", "DefaultInline", "escaped", "curSize", "url", "comment", "procInst", "before", "pBefore", "pAfter", "sBefore", "sAfter", "leftFlanking", "rightFlanking", "canOpen", "canClose", "part", "link", "finishLink", "j", "startPos", "endPos", "dest", "label", "requireNonWS", "open", "close", "emp", "closeSize", "k", "element", "startIndex", "elements", "eI", "NotLast", "lineStart", "c", "rPos", "cur", "fragEnd", "blockI", "prevEnd", "prevI", "markdownHighlighting", "tags", "n", "leftOverSpace", "nextPos", "parseCode", "codeParser", "htmlParser", "parseMixed", "info", "infoNode", "<PERSON>th<PERSON><PERSON><PERSON><PERSON>", "Strikethrough", "parseRow", "startI", "cellStart", "cellEnd", "esc", "parseCell", "hasPipe", "str", "delimiterLine", "<PERSON><PERSON><PERSON>er", "lineText", "firstRow", "Table", "TaskParser", "TaskList", "GFM", "parseSubSuper", "Superscript", "Subscript", "<PERSON><PERSON><PERSON>", "match", "data", "defineLanguageFacet", "headingProp", "commonmark", "foldNodeProp", "isHeading", "state", "indentNodeProp", "languageDataProp", "findSectionEnd", "headerNode", "level", "heading", "headerIndent", "foldService", "syntaxTree", "mkLang", "Language", "commonmarkLanguage", "extended", "markdownLanguage", "getCodeP<PERSON>er", "languages", "defaultLanguage", "LanguageDescription", "ParseContext", "Context", "spaceBefore", "spaceAfter", "item", "max<PERSON><PERSON><PERSON>", "doc", "add", "number", "itemNumber", "getContext", "nodes", "context", "renumberList", "changes", "prev", "insertNewlineContinueMarkup", "dispatch", "range", "emptyLine", "delTo", "insert", "EditorSelection", "prevLine", "quoted", "continued", "isMark", "contextNodeForDelete", "scan", "deleteMarkupBackward", "spaceEnd", "markdownKeymap", "htmlNoMatch", "html", "markdown", "codeLanguages", "defaultCodeLanguage", "addKeymap", "extensions", "support", "defaultCode", "LanguageSupport", "Prec", "keymap"], "mappings": "uYAGA,MAAMA,CAAe,CACjB,YAAYC,EAEZC,EAAOC,EAAMC,EAAMC,EAAKC,EAAUC,EAAW,CACzC,KAAK,KAAON,EACZ,KAAK,MAAQC,EACb,KAAK,KAAOC,EACZ,KAAK,KAAOC,EACZ,KAAK,IAAMC,EACX,KAAK,SAAWC,EAChB,KAAK,UAAYC,EACjB,KAAK,SAAW,CAAC,CAACC,EAAS,YAAaJ,CAAI,CAAC,CAChD,CACD,OAAO,OAAOH,EAAMC,EAAOC,EAAMM,EAAYJ,EAAK,CAC9C,IAAID,EAAQK,GAAcA,GAAc,GAAKR,GAAQC,GAAS,GAAM,EACpE,OAAO,IAAIF,EAAeC,EAAMC,EAAOC,EAAMC,EAAMC,EAAK,GAAI,CAAA,CAAE,CACjE,CACD,SAASK,EAAOC,EAAK,CACbD,EAAM,KAAKF,EAAS,WAAW,GAAK,KAAK,OACzCE,EAAQ,IAAIE,EAAKF,EAAM,KAAMA,EAAM,SAAUA,EAAM,UAAWA,EAAM,OAAQ,KAAK,QAAQ,GAC7F,KAAK,SAAS,KAAKA,CAAK,EACxB,KAAK,UAAU,KAAKC,CAAG,CAC1B,CACD,OAAOE,EAASR,EAAM,KAAK,IAAK,CAC5B,IAAIS,EAAO,KAAK,SAAS,OAAS,EAClC,OAAIA,GAAQ,IACRT,EAAM,KAAK,IAAIA,EAAK,KAAK,UAAUS,CAAI,EAAI,KAAK,SAASA,CAAI,EAAE,OAAS,KAAK,IAAI,GAC1E,IAAIF,EAAKC,EAAQ,MAAM,KAAK,IAAI,EAAG,KAAK,SAAU,KAAK,UAAWR,EAAM,KAAK,IAAI,EAAE,QAAQ,CAClG,SAAU,CAACC,EAAUC,EAAWQ,IAAW,IAAIH,EAAKI,EAAS,KAAMV,EAAUC,EAAWQ,EAAQ,KAAK,QAAQ,CACzH,CAAS,CAEJ,CACL,CACA,IAAIE,GACH,SAAUA,EAAM,CACbA,EAAKA,EAAK,SAAc,CAAC,EAAI,WAC7BA,EAAKA,EAAK,UAAe,CAAC,EAAI,YAC9BA,EAAKA,EAAK,WAAgB,CAAC,EAAI,aAC/BA,EAAKA,EAAK,WAAgB,CAAC,EAAI,aAC/BA,EAAKA,EAAK,eAAoB,CAAC,EAAI,iBACnCA,EAAKA,EAAK,WAAgB,CAAC,EAAI,aAC/BA,EAAKA,EAAK,YAAiB,CAAC,EAAI,cAChCA,EAAKA,EAAK,SAAc,CAAC,EAAI,WAC7BA,EAAKA,EAAK,YAAiB,CAAC,EAAI,cAChCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,eAAoB,EAAE,EAAI,iBACpCA,EAAKA,EAAK,eAAoB,EAAE,EAAI,iBACpCA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,cAAmB,EAAE,EAAI,gBACnCA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,aAAkB,EAAE,EAAI,eAClCA,EAAKA,EAAK,2BAAgC,EAAE,EAAI,6BAEhDA,EAAKA,EAAK,OAAY,EAAE,EAAI,SAC5BA,EAAKA,EAAK,OAAY,EAAE,EAAI,SAC5BA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,eAAoB,EAAE,EAAI,iBACpCA,EAAKA,EAAK,KAAU,EAAE,EAAI,OAC1BA,EAAKA,EAAK,MAAW,EAAE,EAAI,QAC3BA,EAAKA,EAAK,WAAgB,EAAE,EAAI,aAChCA,EAAKA,EAAK,QAAa,EAAE,EAAI,UAC7BA,EAAKA,EAAK,QAAa,EAAE,EAAI,UAC7BA,EAAKA,EAAK,sBAA2B,EAAE,EAAI,wBAC3CA,EAAKA,EAAK,IAAS,EAAE,EAAI,MAEzBA,EAAKA,EAAK,WAAgB,EAAE,EAAI,aAChCA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,aAAkB,EAAE,EAAI,eAClCA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,UAAe,EAAE,EAAI,WACnC,GAAGA,IAASA,EAAO,CAAE,EAAC,EAGtB,MAAMC,EAAU,CAEZ,YAEAC,EAEAC,EAAS,CACL,KAAK,MAAQD,EACb,KAAK,QAAUC,EAEf,KAAK,MAAQ,GAEb,KAAK,QAAU,EAClB,CACL,CAEA,MAAMC,EAAK,CACP,aAAc,CAEV,KAAK,KAAO,GAGZ,KAAK,WAAa,EAElB,KAAK,QAAU,EAEf,KAAK,MAAQ,EAEb,KAAK,QAAU,GAGf,KAAK,IAAM,EAEX,KAAK,OAAS,EAEd,KAAK,KAAO,EACf,CAED,SAAU,CACF,KAAK,QAAU,KAAK,KACpB,KAAK,aAAY,CACxB,CAED,cAAe,CACX,IAAIC,EAAS,KAAK,UAAU,KAAK,OAAO,EACxC,KAAK,OAAS,KAAK,YAAYA,EAAQ,KAAK,IAAK,KAAK,MAAM,EAC5D,KAAK,IAAMA,EACX,KAAK,KAAOA,GAAU,KAAK,KAAK,OAAS,GAAK,KAAK,KAAK,WAAWA,CAAM,CAC5E,CAID,UAAUnB,EAAM,CAAE,OAAOoB,EAAU,KAAK,KAAMpB,CAAI,CAAI,CAEtD,MAAMqB,EAAM,CAKR,IAJA,KAAK,KAAOA,EACZ,KAAK,WAAa,KAAK,QAAU,KAAK,IAAM,KAAK,OAAS,EAC1D,KAAK,aAAY,EACjB,KAAK,MAAQ,EACN,KAAK,QAAQ,QAChB,KAAK,QAAQ,KACpB,CAKD,SAASC,EAAI,CACT,KAAK,QAAUA,EACf,KAAK,WAAa,KAAK,YAAYA,EAAI,KAAK,IAAK,KAAK,MAAM,CAC/D,CAED,eAAeC,EAAQ,CACnB,KAAK,WAAaA,EAClB,KAAK,QAAU,KAAK,WAAWA,CAAM,CACxC,CAID,UAAUC,EAAK,CACX,KAAK,QAAQ,KAAKA,CAAG,CACxB,CAGD,YAAYF,EAAItB,EAAO,EAAGuB,EAAS,EAAG,CAClC,QAASE,EAAIzB,EAAMyB,EAAIH,EAAIG,IACvBF,GAAU,KAAK,KAAK,WAAWE,CAAC,GAAK,EAAI,EAAIF,EAAS,EAAI,EAC9D,OAAOA,CACV,CAED,WAAWG,EAAM,CACb,IAAID,EAAI,EACR,QAASF,EAAS,EAAGE,EAAI,KAAK,KAAK,QAAUF,EAASG,EAAMD,IACxDF,GAAU,KAAK,KAAK,WAAWE,CAAC,GAAK,EAAI,EAAIF,EAAS,EAAI,EAC9D,OAAOE,CACV,CAED,OAAQ,CACJ,GAAI,CAAC,KAAK,WACN,OAAO,KAAK,KAChB,IAAIE,EAAS,GACb,QAASF,EAAI,EAAGA,EAAI,KAAK,QAASA,IAC9BE,GAAU,IACd,OAAOA,EAAS,KAAK,KAAK,MAAM,KAAK,OAAO,CAC/C,CACL,CACA,SAASC,GAAYC,EAAIC,EAAIC,EAAM,CAC/B,GAAIA,EAAK,KAAOA,EAAK,KAAK,QACrBF,GAAMC,EAAG,OAASC,EAAK,QAAUD,EAAG,MAAMC,EAAK,MAAQ,CAAC,EAAE,MAAQA,EAAK,WACxE,MAAO,GACX,GAAIA,EAAK,QAAUA,EAAK,WAAa,EACjC,MAAO,GACX,IAAIC,GAAQH,EAAG,MAAQf,EAAK,YAAcmB,GAAgBC,GAAcH,EAAMD,EAAI,EAAK,EACvF,OAAOE,EAAO,IACTH,EAAG,MAAQf,EAAK,YAAcqB,EAAiBJ,EAAMD,EAAI,EAAK,EAAI,IACnEC,EAAK,KAAK,WAAWA,EAAK,IAAMC,EAAO,CAAC,GAAKH,EAAG,KACxD,CACA,MAAMO,GAAoB,CACtB,CAACtB,EAAK,UAAU,EAAEe,EAAIC,EAAIC,EAAM,CAC5B,OAAIA,EAAK,MAAQ,GACN,IACXA,EAAK,QAAQ,KAAKP,EAAIV,EAAK,UAAWgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,IAAM,CAAC,CAAC,EAC3FA,EAAK,SAASA,EAAK,KAAOM,EAAMN,EAAK,KAAK,WAAWA,EAAK,IAAM,CAAC,CAAC,EAAI,EAAI,EAAE,EAC5EF,EAAG,IAAMC,EAAG,UAAYC,EAAK,KAAK,OAC3B,GACV,EACD,CAACjB,EAAK,QAAQ,EAAEe,EAAIS,EAAKP,EAAM,CAC3B,OAAIA,EAAK,OAASA,EAAK,WAAaF,EAAG,OAASE,EAAK,KAAO,GACjD,IACXA,EAAK,eAAeA,EAAK,WAAaF,EAAG,KAAK,EACvC,GACV,EACD,CAACf,EAAK,WAAW,EAAGc,GACpB,CAACd,EAAK,UAAU,EAAGc,GACnB,CAACd,EAAK,QAAQ,GAAI,CAAE,MAAO,EAAO,CACtC,EACA,SAASuB,EAAME,EAAI,CAAE,OAAOA,GAAM,IAAMA,GAAM,GAAKA,GAAM,IAAMA,GAAM,EAAK,CAC1E,SAASnB,EAAUW,EAAMN,EAAI,EAAG,CAC5B,KAAOA,EAAIM,EAAK,QAAUM,EAAMN,EAAK,WAAWN,CAAC,CAAC,GAC9CA,IACJ,OAAOA,CACX,CACA,SAASe,GAAcT,EAAMN,EAAGH,EAAI,CAChC,KAAOG,EAAIH,GAAMe,EAAMN,EAAK,WAAWN,EAAI,CAAC,CAAC,GACzCA,IACJ,OAAOA,CACX,CACA,SAASgB,GAAaV,EAAM,CACxB,GAAIA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAChC,MAAO,GACX,IAAIvB,EAAMuB,EAAK,IAAM,EACrB,KAAOvB,EAAMuB,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWvB,CAAG,GAAKuB,EAAK,MAC/DvB,IACJ,GAAIA,EAAMuB,EAAK,IAAM,EACjB,MAAO,GACX,GAAIA,EAAK,MAAQ,IACb,QAASN,EAAIjB,EAAKiB,EAAIM,EAAK,KAAK,OAAQN,IACpC,GAAIM,EAAK,KAAK,WAAWN,CAAC,GAAK,GAC3B,MAAO,GACnB,OAAOjB,CACX,CACA,SAASkC,GAAaX,EAAM,CACxB,OAAOA,EAAK,MAAQ,GAAe,GAAKA,EAAK,KAAK,WAAWA,EAAK,IAAM,CAAC,GAAK,GAAK,EAAI,CAC3F,CACA,SAASI,EAAiBJ,EAAMD,EAAIa,EAAU,CAC1C,GAAIZ,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,GACnD,MAAO,GACX,IAAIa,EAAQ,EACZ,QAASpC,EAAMuB,EAAK,IAAM,EAAGvB,EAAMuB,EAAK,KAAK,OAAQvB,IAAO,CACxD,IAAI+B,EAAKR,EAAK,KAAK,WAAWvB,CAAG,EACjC,GAAI+B,GAAMR,EAAK,KACXa,YACK,CAACP,EAAME,CAAE,EACd,MAAO,GAGf,OAAII,GAAYZ,EAAK,MAAQ,IAAMc,GAAkBd,CAAI,EAAI,IAAMA,EAAK,OAASD,EAAG,MAAM,QAEnFc,EAAQ,EADJ,GACa,CAC5B,CACA,SAASE,GAAOhB,EAAIhC,EAAM,CACtB,QAAS2B,EAAIK,EAAG,MAAM,OAAS,EAAGL,GAAK,EAAGA,IACtC,GAAIK,EAAG,MAAML,CAAC,EAAE,MAAQ3B,EACpB,MAAO,GACf,MAAO,EACX,CACA,SAASoC,EAAaH,EAAMD,EAAIa,EAAU,CACtC,OAAQZ,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,MACtDA,EAAK,KAAOA,EAAK,KAAK,OAAS,GAAKM,EAAMN,EAAK,KAAK,WAAWA,EAAK,IAAM,CAAC,CAAC,KAC5E,CAACY,GAAYG,GAAOhB,EAAIhB,EAAK,UAAU,GAAKiB,EAAK,UAAUA,EAAK,IAAM,CAAC,EAAIA,EAAK,KAAK,QAAU,EAAI,EAC5G,CACA,SAASE,GAAcF,EAAMD,EAAIa,EAAU,CACvC,IAAInC,EAAMuB,EAAK,IAAKgB,EAAOhB,EAAK,KAChC,KACQgB,GAAQ,IAAMA,GAAQ,IADrB,CAEDvC,IAGJ,GAAIA,GAAOuB,EAAK,KAAK,OACjB,MAAO,GACXgB,EAAOhB,EAAK,KAAK,WAAWvB,CAAG,EAEnC,OAAIA,GAAOuB,EAAK,KAAOvB,EAAMuB,EAAK,IAAM,GACnCgB,GAAQ,IAAMA,GAAQ,IACtBvC,EAAMuB,EAAK,KAAK,OAAS,GAAK,CAACM,EAAMN,EAAK,KAAK,WAAWvB,EAAM,CAAC,CAAC,GACnEmC,GAAY,CAACG,GAAOhB,EAAIhB,EAAK,WAAW,IACnCiB,EAAK,UAAUvB,EAAM,CAAC,GAAKuB,EAAK,KAAK,QAAUvB,EAAMuB,EAAK,IAAM,GAAKA,EAAK,MAAQ,IAChF,GACJvB,EAAM,EAAIuB,EAAK,GAC1B,CACA,SAASiB,GAAajB,EAAM,CACxB,GAAIA,EAAK,MAAQ,GACb,MAAO,GACX,IAAIvB,EAAMuB,EAAK,IAAM,EACrB,KAAOvB,EAAMuB,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWvB,CAAG,GAAK,IAC1DA,IACJ,GAAIA,EAAMuB,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWvB,CAAG,GAAK,GACvD,MAAO,GACX,IAAIwB,EAAOxB,EAAMuB,EAAK,IACtB,OAAOC,EAAO,EAAI,GAAKA,CAC3B,CACA,SAASa,GAAkBd,EAAM,CAC7B,GAAIA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAAiBA,EAAK,QAAUA,EAAK,WAAa,EAClF,MAAO,GACX,IAAIvB,EAAMuB,EAAK,IAAM,EACrB,KAAOvB,EAAMuB,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWvB,CAAG,GAAKuB,EAAK,MAC/DvB,IACJ,IAAIN,EAAMM,EACV,KAAOA,EAAMuB,EAAK,KAAK,QAAUM,EAAMN,EAAK,KAAK,WAAWvB,CAAG,CAAC,GAC5DA,IACJ,OAAOA,GAAOuB,EAAK,KAAK,OAAS7B,EAAM,EAC3C,CACA,MAAM+C,EAAY,WAAYC,GAAa,MAAOC,GAAgB,MAC5DC,EAAiB,CACnB,CAAC,oCAAqC,2BAA2B,EACjE,CAAC,WAAYF,EAAU,EACvB,CAAC,UAAWC,EAAa,EACzB,CAAC,cAAe,GAAG,EACnB,CAAC,kBAAmB,OAAO,EAC3B,CAAC,gYAAiYF,CAAS,EAC3Y,CAAC,mHAAoHA,CAAS,CAClI,EACA,SAASI,GAAYtB,EAAMO,EAAKK,EAAU,CACtC,GAAIZ,EAAK,MAAQ,GACb,MAAO,GACX,IAAIuB,EAAOvB,EAAK,KAAK,MAAMA,EAAK,GAAG,EACnC,QAASN,EAAI,EAAG8B,EAAIH,EAAe,QAAUT,EAAW,EAAI,GAAIlB,EAAI8B,EAAG9B,IACnE,GAAI2B,EAAe3B,CAAC,EAAE,CAAC,EAAE,KAAK6B,CAAI,EAC9B,OAAO7B,EACf,MAAO,EACX,CACA,SAAS+B,GAAczB,EAAMvB,EAAK,CAC9B,IAAIiD,EAAc1B,EAAK,YAAYvB,EAAKuB,EAAK,IAAKA,EAAK,MAAM,EACzD2B,EAAW3B,EAAK,YAAYA,EAAK,UAAUvB,CAAG,EAAGA,EAAKiD,CAAW,EACrE,OAAOC,GAAYD,EAAc,EAAIA,EAAc,EAAIC,CAC3D,CACA,SAASC,EAAYC,EAAO5D,EAAMsB,EAAI,CAClC,IAAIX,EAAOiD,EAAM,OAAS,EACtBjD,GAAQ,GAAKiD,EAAMjD,CAAI,EAAE,IAAMX,GAAQ4D,EAAMjD,CAAI,EAAE,MAAQG,EAAK,SAChE8C,EAAMjD,CAAI,EAAE,GAAKW,EAEjBsC,EAAM,KAAKpC,EAAIV,EAAK,SAAUd,EAAMsB,CAAE,CAAC,CAC/C,CAKA,MAAMuC,EAAsB,CACxB,cAAe,OACf,aAAa/B,EAAIC,EAAM,CACnB,IAAI+B,EAAO/B,EAAK,WAAa,EAC7B,GAAIA,EAAK,OAAS+B,EACd,MAAO,GACX,IAAI9C,EAAQe,EAAK,WAAW+B,CAAI,EAC5B9D,EAAO8B,EAAG,UAAYd,EAAOM,EAAKQ,EAAG,UAAYC,EAAK,KAAK,OAC3D6B,EAAQ,CAAA,EAAIG,EAAe,GAE/B,IADAJ,EAAYC,EAAO5D,EAAMsB,CAAE,EACpBQ,EAAG,YAAcC,EAAK,OAASD,EAAG,MAAM,QAC3C,GAAIC,EAAK,KAAOA,EAAK,KAAK,OAAQ,CAC9B4B,EAAYI,EAAcjC,EAAG,UAAY,EAAGA,EAAG,SAAS,EACxD,QAASkC,KAAKjC,EAAK,QACfgC,EAAa,KAAKC,CAAC,MAEtB,IAAIjC,EAAK,OAAS+B,EACnB,MAEC,CACD,GAAIC,EAAa,OAAQ,CACrB,QAASC,KAAKD,EACNC,EAAE,MAAQlD,EAAK,SACf6C,EAAYC,EAAOI,EAAE,KAAMA,EAAE,EAAE,EAE/BJ,EAAM,KAAKI,CAAC,EAEpBD,EAAe,CAAA,EAEnBJ,EAAYC,EAAO9B,EAAG,UAAY,EAAGA,EAAG,SAAS,EACjD,QAASkC,KAAKjC,EAAK,QACf6B,EAAM,KAAKI,CAAC,EAChB1C,EAAKQ,EAAG,UAAYC,EAAK,KAAK,OAC9B,IAAIkC,EAAYnC,EAAG,UAAYC,EAAK,WAAWA,EAAK,WAAa,CAAC,EAC9DkC,EAAY3C,GACZqC,EAAYC,EAAOK,EAAW3C,CAAE,GAG5C,OAAIyC,EAAa,SACbA,EAAeA,EAAa,OAAOC,GAAKA,EAAE,MAAQlD,EAAK,QAAQ,EAC3DiD,EAAa,SACbhC,EAAK,QAAUgC,EAAa,OAAOhC,EAAK,OAAO,IAEvDD,EAAG,QAAQA,EAAG,OAAO,cAAc8B,EAAO,CAAC5D,CAAI,EAAE,OAAOc,EAAK,UAAWQ,EAAKtB,CAAI,EAAGA,CAAI,EACjF,EACV,EACD,WAAW8B,EAAIC,EAAM,CACjB,IAAImC,EAAWzB,GAAaV,CAAI,EAChC,GAAImC,EAAW,EACX,MAAO,GACX,IAAIlE,EAAO8B,EAAG,UAAYC,EAAK,IAAKQ,EAAKR,EAAK,KAAMoC,EAAMD,EAAWnC,EAAK,IACtEqC,EAAWrC,EAAK,UAAUmC,CAAQ,EAAGG,EAAS7B,GAAcT,EAAK,KAAMA,EAAK,KAAK,OAAQqC,CAAQ,EACjGR,EAAQ,CAACpC,EAAIV,EAAK,SAAUd,EAAMA,EAAOmE,CAAG,CAAC,EAC7CC,EAAWC,GACXT,EAAM,KAAKpC,EAAIV,EAAK,SAAUgB,EAAG,UAAYsC,EAAUtC,EAAG,UAAYuC,CAAM,CAAC,EACjF,QAASC,EAAQ,GAAMxC,EAAG,SAAQ,GAAMC,EAAK,OAASD,EAAG,MAAM,OAAQwC,EAAQ,GAAO,CAClF,IAAI7C,EAAIM,EAAK,IACb,GAAIA,EAAK,OAASA,EAAK,WAAa,EAChC,KAAON,EAAIM,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWN,CAAC,GAAKc,GACtDd,IACR,GAAIA,EAAIM,EAAK,KAAOoC,GAAOpC,EAAK,UAAUN,CAAC,GAAKM,EAAK,KAAK,OAAQ,CAC9D,QAASiC,KAAKjC,EAAK,QACf6B,EAAM,KAAKI,CAAC,EAChBJ,EAAM,KAAKpC,EAAIV,EAAK,SAAUgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYL,CAAC,CAAC,EACxEK,EAAG,SAAQ,EACX,UAEC,CACIwC,GACDX,EAAYC,EAAO9B,EAAG,UAAY,EAAGA,EAAG,SAAS,EACrD,QAASkC,KAAKjC,EAAK,QACf6B,EAAM,KAAKI,CAAC,EAChB,IAAIO,EAAYzC,EAAG,UAAYC,EAAK,QAASyC,EAAU1C,EAAG,UAAYC,EAAK,KAAK,OAC5EwC,EAAYC,GACZb,EAAYC,EAAOW,EAAWC,CAAO,GAGjD,OAAA1C,EAAG,QAAQA,EAAG,OAAO,cAAc8B,EAAO,CAAC5D,CAAI,EAC1C,OAAOc,EAAK,WAAYgB,EAAG,YAAW,EAAK9B,CAAI,EAAGA,CAAI,EACpD,EACV,EACD,WAAW8B,EAAIC,EAAM,CACjB,IAAIC,EAAOU,GAAaX,CAAI,EAC5B,OAAIC,EAAO,EACA,IACXF,EAAG,aAAahB,EAAK,WAAYiB,EAAK,GAAG,EACzCD,EAAG,QAAQhB,EAAK,UAAWgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,IAAM,CAAC,EAC/EA,EAAK,SAASA,EAAK,IAAMC,CAAI,EACtB,KACV,EACD,eAAeF,EAAIC,EAAM,CACrB,GAAII,EAAiBJ,EAAMD,EAAI,EAAK,EAAI,EACpC,MAAO,GACX,IAAI9B,EAAO8B,EAAG,UAAYC,EAAK,IAC/B,OAAAD,EAAG,SAAQ,EACXA,EAAG,QAAQhB,EAAK,eAAgBd,CAAI,EAC7B,EACV,EACD,WAAW8B,EAAIC,EAAM,CACjB,IAAIC,EAAOE,EAAaH,EAAMD,EAAI,EAAK,EACvC,GAAIE,EAAO,EACP,MAAO,GACPF,EAAG,MAAM,MAAQhB,EAAK,YACtBgB,EAAG,aAAahB,EAAK,WAAYiB,EAAK,QAASA,EAAK,IAAI,EAC5D,IAAI0C,EAAUjB,GAAczB,EAAMA,EAAK,IAAM,CAAC,EAC9C,OAAAD,EAAG,aAAahB,EAAK,SAAUiB,EAAK,QAAS0C,EAAU1C,EAAK,UAAU,EACtED,EAAG,QAAQhB,EAAK,SAAUgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,IAAMC,CAAI,EACjFD,EAAK,eAAe0C,CAAO,EACpB,IACV,EACD,YAAY3C,EAAIC,EAAM,CAClB,IAAIC,EAAOC,GAAcF,EAAMD,EAAI,EAAK,EACxC,GAAIE,EAAO,EACP,MAAO,GACPF,EAAG,MAAM,MAAQhB,EAAK,aACtBgB,EAAG,aAAahB,EAAK,YAAaiB,EAAK,QAASA,EAAK,KAAK,WAAWA,EAAK,IAAMC,EAAO,CAAC,CAAC,EAC7F,IAAIyC,EAAUjB,GAAczB,EAAMA,EAAK,IAAMC,CAAI,EACjD,OAAAF,EAAG,aAAahB,EAAK,SAAUiB,EAAK,QAAS0C,EAAU1C,EAAK,UAAU,EACtED,EAAG,QAAQhB,EAAK,SAAUgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,IAAMC,CAAI,EACjFD,EAAK,eAAe0C,CAAO,EACpB,IACV,EACD,WAAW3C,EAAIC,EAAM,CACjB,IAAIC,EAAOgB,GAAajB,CAAI,EAC5B,GAAIC,EAAO,EACP,MAAO,GACX,IAAI0C,EAAM3C,EAAK,IAAK/B,EAAO8B,EAAG,UAAY4C,EACtCC,EAAanC,GAAcT,EAAK,KAAMA,EAAK,KAAK,OAAQ2C,CAAG,EAAGE,EAAQD,EAC1E,KAAOC,EAAQF,GAAO3C,EAAK,KAAK,WAAW6C,EAAQ,CAAC,GAAK7C,EAAK,MAC1D6C,KACAA,GAASD,GAAcC,GAASF,GAAO,CAACrC,EAAMN,EAAK,KAAK,WAAW6C,EAAQ,CAAC,CAAC,KAC7EA,EAAQ7C,EAAK,KAAK,QACtB,IAAI8C,EAAM/C,EAAG,OACR,MAAMhB,EAAK,WAAY,EAAGkB,CAAI,EAC9B,cAAcF,EAAG,OAAO,YAAYC,EAAK,KAAK,MAAM2C,EAAM1C,EAAO,EAAG4C,CAAK,EAAG5E,EAAOgC,EAAO,CAAC,EAAG,CAAChC,CAAI,EACpG4E,EAAQ7C,EAAK,KAAK,QAClB8C,EAAI,MAAM/D,EAAK,WAAY8D,EAAQF,EAAKC,EAAaD,CAAG,EAC5D,IAAII,EAAOD,EAAI,OAAO/D,EAAK,YAAc,EAAIkB,EAAMD,EAAK,KAAK,OAAS2C,CAAG,EACzE,OAAA5C,EAAG,SAAQ,EACXA,EAAG,QAAQgD,EAAM9E,CAAI,EACd,EACV,EACD,UAAU8B,EAAIC,EAAM,CAChB,IAAIjC,EAAOuD,GAAYtB,EAAMD,EAAI,EAAK,EACtC,GAAIhC,EAAO,EACP,MAAO,GACX,IAAIE,EAAO8B,EAAG,UAAYC,EAAK,IAAK7B,EAAMkD,EAAetD,CAAI,EAAE,CAAC,EAC5D8D,EAAQ,CAAE,EAAEmB,EAAW7E,GAAO+C,EAClC,KAAO,CAAC/C,EAAI,KAAK6B,EAAK,IAAI,GAAKD,EAAG,YAAY,CAC1C,GAAIC,EAAK,MAAQD,EAAG,MAAM,OAAQ,CAC9BiD,EAAW,GACX,MAEJ,QAASf,KAAKjC,EAAK,QACf6B,EAAM,KAAKI,CAAC,EAEhBe,GACAjD,EAAG,SAAQ,EACf,IAAIkD,EAAW9E,GAAOgD,GAAapC,EAAK,aAAeZ,GAAOiD,GAAgBrC,EAAK,2BAA6BA,EAAK,UACjHQ,EAAKQ,EAAG,cACZ,OAAAA,EAAG,QAAQA,EAAG,OAAO,cAAc8B,EAAO,CAAC5D,CAAI,EAAE,OAAOgF,EAAU1D,EAAKtB,CAAI,EAAGA,CAAI,EAC3E,EACV,EACD,cAAe,MACnB,EAMA,MAAMiF,EAAoB,CACtB,YAAYC,EAAM,CACd,KAAK,MAAQ,EACb,KAAK,KAAO,GACZ,KAAK,IAAM,EACX,KAAK,MAAQA,EAAK,MAClB,KAAK,QAAQA,EAAK,OAAO,CAC5B,CACD,SAASpD,EAAIC,EAAMmD,EAAM,CACrB,GAAI,KAAK,OAAS,GACd,MAAO,GACX,IAAIjE,EAAUiE,EAAK,QAAU;AAAA,EAAOnD,EAAK,QACrCoD,EAAS,KAAK,QAAQlE,CAAO,EACjC,OAAIkE,EAAS,IAAMA,EAASlE,EAAQ,OACzB,KAAK,SAASa,EAAIoD,EAAMC,CAAM,EAClC,EACV,CACD,OAAOrD,EAAIoD,EAAM,CACb,OAAK,KAAK,OAAS,GAAgB,KAAK,OAAS,IAAkB9D,EAAU8D,EAAK,QAAS,KAAK,GAAG,GAAKA,EAAK,QAAQ,OAC1G,KAAK,SAASpD,EAAIoD,EAAMA,EAAK,QAAQ,MAAM,EAC/C,EACV,CACD,SAASpD,EAAIoD,EAAMf,EAAK,CACpB,OAAArC,EAAG,eAAeoD,EAAM1D,EAAIV,EAAK,cAAe,KAAK,MAAO,KAAK,MAAQqD,EAAK,KAAK,IAAI,CAAC,EACjF,EACV,CACD,UAAU3C,EAAK,CACX,OAAIA,GACA,KAAK,IAAMA,EAAI,GAAK,KAAK,MACzB,KAAK,KAAK,KAAKA,CAAG,EAClB,KAAK,QACE,KAEPA,IAAQ,KACR,KAAK,MAAQ,IACV,GACV,CACD,QAAQP,EAAS,CACb,OAAS,CACL,GAAI,KAAK,OAAS,GACd,MAAO,GAEN,GAAI,KAAK,OAAS,EAAe,CAClC,GAAI,CAAC,KAAK,UAAUmE,GAAenE,EAAS,KAAK,IAAK,KAAK,MAAO,EAAI,CAAC,EACnE,MAAO,GACX,GAAIA,EAAQ,WAAW,KAAK,GAAG,GAAK,GAChC,OAAO,KAAK,MAAQ,GACxB,KAAK,KAAK,KAAKO,EAAIV,EAAK,SAAU,KAAK,IAAM,KAAK,MAAO,KAAK,IAAM,KAAK,MAAQ,CAAC,CAAC,EACnF,KAAK,cAEA,KAAK,OAAS,GACnB,GAAI,CAAC,KAAK,UAAUuE,GAASpE,EAASG,EAAUH,EAAS,KAAK,GAAG,EAAG,KAAK,KAAK,CAAC,EAC3E,MAAO,WAEN,KAAK,OAAS,EAAc,CACjC,IAAIqE,EAAOlE,EAAUH,EAAS,KAAK,GAAG,EAAGf,EAAM,EAC/C,GAAIoF,EAAO,KAAK,IAAK,CACjB,IAAIC,EAAQC,GAAevE,EAASqE,EAAM,KAAK,KAAK,EACpD,GAAIC,EAAO,CACP,IAAIE,EAAWC,EAAQzE,EAASsE,EAAM,GAAK,KAAK,KAAK,EACjDE,EAAW,IACX,KAAK,UAAUF,CAAK,EACpBrF,EAAMuF,IAIlB,OAAKvF,IACDA,EAAMwF,EAAQzE,EAAS,KAAK,GAAG,GAC5Bf,EAAM,GAAKA,EAAMe,EAAQ,OAASf,EAAM,OAG/C,QAAOwF,EAAQzE,EAAS,KAAK,GAAG,EAG3C,CACL,CACA,SAASyE,EAAQrE,EAAMb,EAAK,CACxB,KAAOA,EAAMa,EAAK,OAAQb,IAAO,CAC7B,IAAIuC,EAAO1B,EAAK,WAAWb,CAAG,EAC9B,GAAIuC,GAAQ,GACR,MACJ,GAAI,CAACV,EAAMU,CAAI,EACX,MAAO,GAEf,OAAOvC,CACX,CACA,MAAMmF,EAAoB,CACtB,SAAS7D,EAAIC,EAAMmD,EAAM,CACrB,IAAIU,EAAY7D,EAAK,MAAQD,EAAG,MAAM,OAAS,GAAKe,GAAkBd,CAAI,EACtEgB,EAAOhB,EAAK,KAChB,GAAI6D,EAAY,EACZ,MAAO,GACX,IAAIC,EAAgBrE,EAAIV,EAAK,WAAYgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAY8D,CAAS,EAC1F,OAAA9D,EAAG,SAAQ,EACXA,EAAG,eAAeoD,EAAM1D,EAAIuB,GAAQ,GAAKjC,EAAK,eAAiBA,EAAK,eAAgBoE,EAAK,MAAOpD,EAAG,YAAW,EAAI,CAC9G,GAAGA,EAAG,OAAO,YAAYoD,EAAK,QAASA,EAAK,KAAK,EACjDW,CACH,CAAA,CAAC,EACK,EACV,CACD,QAAS,CACL,MAAO,EACV,CACL,CACA,MAAMC,GAAoB,CACtB,cAAcC,EAAGb,EAAM,CAAE,OAAOA,EAAK,QAAQ,WAAW,CAAC,GAAK,GAAe,IAAID,GAAoBC,CAAI,EAAI,IAAO,EACpH,eAAgB,CAAE,OAAO,IAAIS,EAAsB,CACvD,EACMK,GAAiB,CACnB,CAACD,EAAGhE,IAASiB,GAAajB,CAAI,GAAK,EACnC,CAACgE,EAAGhE,IAASU,GAAaV,CAAI,GAAK,EACnC,CAACgE,EAAGhE,IAASW,GAAaX,CAAI,GAAK,EACnC,CAACkE,EAAGlE,IAASG,EAAaH,EAAMkE,EAAG,EAAI,GAAK,EAC5C,CAACA,EAAGlE,IAASE,GAAcF,EAAMkE,EAAG,EAAI,GAAK,EAC7C,CAACA,EAAGlE,IAASI,EAAiBJ,EAAMkE,EAAG,EAAI,GAAK,EAChD,CAACA,EAAGlE,IAASsB,GAAYtB,EAAMkE,EAAG,EAAI,GAAK,CAC/C,EACMC,GAAiB,CAAE,KAAM,GAAI,IAAK,CAAC,EAEzC,MAAMC,EAAa,CAEf,YAEAC,EAEAC,EAAOC,EAEPC,EAAQ,CACJ,KAAK,OAASH,EACd,KAAK,MAAQC,EACb,KAAK,OAASE,EACd,KAAK,KAAO,IAAIrF,GAChB,KAAK,MAAQ,GAEb,KAAK,WAAa,IAAI,IACtB,KAAK,UAAY,KAEjB,KAAK,OAAS,EACd,KAAK,GAAKqF,EAAOA,EAAO,OAAS,CAAC,EAAE,GACpC,KAAK,UAAY,KAAK,kBAAoB,KAAK,gBAAkBA,EAAO,CAAC,EAAE,KAC3E,KAAK,MAAQ1G,EAAe,OAAOiB,EAAK,SAAU,EAAG,KAAK,UAAW,EAAG,CAAC,EACzE,KAAK,MAAQ,CAAC,KAAK,KAAK,EACxB,KAAK,UAAYwF,EAAU,OAAS,IAAIE,GAAeF,EAAWD,CAAK,EAAI,KAC3E,KAAK,SAAQ,CAChB,CACD,IAAI,WAAY,CACZ,OAAO,KAAK,iBACf,CACD,SAAU,CACN,GAAI,KAAK,WAAa,MAAQ,KAAK,kBAAoB,KAAK,UACxD,OAAO,KAAK,SAChB,GAAI,CAAE,KAAAtE,CAAM,EAAG,KACf,OAAS,CACL,KAAOA,EAAK,MAAQ,KAAK,MAAM,QAC3B,KAAK,cAAa,EACtB,QAAS0E,KAAQ1E,EAAK,QAClB,KAAK,QAAQ0E,EAAK,KAAMA,EAAK,KAAMA,EAAK,EAAE,EAC9C,GAAI1E,EAAK,IAAMA,EAAK,KAAK,OACrB,MAEJ,GAAI,CAAC,KAAK,SAAU,EAChB,OAAO,KAAK,SAEpB,GAAI,KAAK,WAAa,KAAK,cAAcA,EAAK,OAAO,EACjD,OAAO,KACXf,EAAO,OAAS,CACZ,QAASlB,KAAQ,KAAK,OAAO,aACzB,GAAIA,EAAM,CACN,IAAI6B,EAAS7B,EAAK,KAAMiC,CAAI,EAC5B,GAAIJ,GAAU,GAAO,CACjB,GAAIA,GAAU,GACV,OAAO,KACXI,EAAK,QAAO,EACZ,SAASf,GAGrB,MAEJ,IAAIkE,EAAO,IAAInE,GAAU,KAAK,UAAYgB,EAAK,IAAKA,EAAK,KAAK,MAAMA,EAAK,GAAG,CAAC,EAC7E,QAAS2E,KAAS,KAAK,OAAO,iBAC1B,GAAIA,EAAO,CACP,IAAIN,EAASM,EAAM,KAAMxB,CAAI,EACzBkB,GACAlB,EAAK,QAAQ,KAAKkB,CAAM,EAEpCO,EAAO,KAAO,KAAK,YACX5E,EAAK,KAAOA,EAAK,KAAK,QADC,CAG3B,GAAIA,EAAK,OAASA,EAAK,WAAa,GAChC,QAAS6E,KAAQ,KAAK,OAAO,aACzB,GAAIA,EAAK,KAAM7E,EAAMmD,CAAI,EACrB,MAAMyB,EAElB,QAASP,KAAUlB,EAAK,QACpB,GAAIkB,EAAO,SAAS,KAAMrE,EAAMmD,CAAI,EAChC,OAAO,KACfA,EAAK,SAAW;AAAA,EAAOnD,EAAK,MAAK,EACjC,QAASiC,KAAKjC,EAAK,QACfmD,EAAK,MAAM,KAAKlB,CAAC,EAEzB,YAAK,WAAWkB,CAAI,EACb,IACV,CACD,OAAO1E,EAAK,CACR,GAAI,KAAK,WAAa,MAAQ,KAAK,UAAYA,EAC3C,MAAM,IAAI,WAAW,8BAA8B,EACvD,KAAK,UAAYA,CACpB,CACD,cAAcQ,EAAO,CACjB,GAAI,CAAC,KAAK,UAAU,OAAO,KAAK,kBAAoBA,EAAO,KAAK,iBAAiB,GAC7E,CAAC,KAAK,UAAU,QAAQ,KAAK,MAAM,IAAI,EACvC,MAAO,GACX,IAAI6F,EAAQ,KAAK,UAAU,UAAU,IAAI,EACzC,GAAI,CAACA,EACD,MAAO,GACX,IAAIC,EAAcD,EAAO3G,EAAM,KAAK,kBAAoB2G,EACxD,QAAS,EAAI,EAAG,EAAI,KAAK,OAAO,OAAQ,IAAK,CACzC,IAAIE,EAAU,KAAK,OAAO,EAAI,CAAC,EAAE,GAAIC,EAAQ,KAAK,OAAO,CAAC,EAAE,KACxDD,GAAW,KAAK,WAAaC,EAAQ9G,IACrC4G,GAAeE,EAAQD,GAE/B,YAAK,WAAaD,EAClB,KAAK,mBAAqBD,EAC1B,KAAK,WAAU,EACX,KAAK,kBAAoB,KAAK,IAC9B,KAAK,YACL,KAAK,oBACL,KAAK,SAAQ,IAGb,KAAK,MAAQ,GACb,KAAK,SAAQ,GAEV,EACV,CAED,IAAI,OAAQ,CACR,OAAO,KAAK,MAAM,MACrB,CAGD,WAAWI,EAAQ,KAAK,MAAQ,EAAG,CAC/B,OAAO,KAAK,OAAO,QAAQ,MAAM,KAAK,MAAMA,CAAK,EAAE,IAAI,CAC1D,CAMD,UAAW,CAEP,OADA,KAAK,WAAa,KAAK,KAAK,KAAK,OAC7B,KAAK,iBAAmB,KAAK,IAC7B,KAAK,kBAAoB,KAAK,gBAC9B,KAAK,MAAQ,GACb,KAAK,SAAQ,EACN,KAGP,KAAK,YACL,KAAK,kBAAoB,KAAK,gBAAkB,EAChD,KAAK,WAAU,EACf,KAAK,SAAQ,EACN,GAEd,CACD,YAAa,CACT,KAAO,KAAK,OAAS,KAAK,OAAO,OAAS,GAAK,KAAK,mBAAqB,KAAK,OAAO,KAAK,MAAM,EAAE,IAC9F,KAAK,SACL,KAAK,kBAAoB,KAAK,IAAI,KAAK,kBAAmB,KAAK,OAAO,KAAK,MAAM,EAAE,IAAI,CAE9F,CAED,SAASjG,EAAO,CACZ,IAAI,EAAIkF,GAER,GADA,EAAE,IAAMlF,EACJA,GAAS,KAAK,GACd,EAAE,KAAO,WAGT,EAAE,KAAO,KAAK,YAAYA,CAAK,EAC/B,EAAE,KAAO,EAAE,KAAK,OACZ,KAAK,OAAO,OAAS,EAAG,CACxB,IAAIkG,EAAa,KAAK,kBAAmBC,EAAS,KAAK,OACvD,KAAO,KAAK,OAAOA,CAAM,EAAE,GAAK,EAAE,KAAK,CACnCA,IACA,IAAIC,EAAW,KAAK,OAAOD,CAAM,EAAE,KAC/BvC,EAAQ,KAAK,YAAYwC,CAAQ,EACrC,EAAE,IAAMA,EAAWxC,EAAM,OACzB,EAAE,KAAO,EAAE,KAAK,MAAM,EAAG,KAAK,OAAOuC,EAAS,CAAC,EAAE,GAAKD,CAAU,EAAItC,EACpEsC,EAAa,EAAE,IAAM,EAAE,KAAK,QAIxC,OAAO,CACV,CAED,UAAW,CACP,GAAI,CAAE,KAAAnF,CAAI,EAAK,KAAM,CAAE,KAAAV,EAAM,IAAAnB,CAAK,EAAG,KAAK,SAAS,KAAK,iBAAiB,EAGzE,IAFA,KAAK,gBAAkBA,EACvB6B,EAAK,MAAMV,CAAI,EACRU,EAAK,MAAQ,KAAK,MAAM,OAAQA,EAAK,QAAS,CACjD,IAAID,EAAK,KAAK,MAAMC,EAAK,KAAK,EAAGsF,EAAU,KAAK,OAAO,kBAAkBvF,EAAG,IAAI,EAChF,GAAI,CAACuF,EACD,MAAM,IAAI,MAAM,2BAA6BvG,EAAKgB,EAAG,IAAI,CAAC,EAC9D,GAAI,CAACuF,EAAQvF,EAAI,KAAMC,CAAI,EACvB,MACJA,EAAK,QAAO,EAEnB,CACD,YAAYvB,EAAK,CACb,IAAIuC,EAAO,KAAK,MAAM,MAAMvC,CAAG,EAAGa,EAClC,GAAK,KAAK,MAAM,WAKZA,EAAO0B,GAAQ;AAAA,EAAO,GAAKA,MALH,CACxB,IAAIuE,EAAMvE,EAAK,QAAQ;AAAA,CAAI,EAC3B1B,EAAOiG,EAAM,EAAIvE,EAAOA,EAAK,MAAM,EAAGuE,CAAG,EAK7C,OAAO9G,EAAMa,EAAK,OAAS,KAAK,GAAKA,EAAK,MAAM,EAAG,KAAK,GAAKb,CAAG,EAAIa,CACvE,CAED,aAAc,CAAE,OAAO,KAAK,MAAQ,KAAK,UAAY,KAAK,UAAY,CAAI,CAE1E,aAAavB,EAAMkB,EAAOjB,EAAQ,EAAG,CACjC,KAAK,MAAQF,EAAe,OAAOC,EAAMC,EAAO,KAAK,UAAYiB,EAAO,KAAK,MAAM,KAAM,KAAK,UAAY,KAAK,KAAK,KAAK,MAAM,EAC/H,KAAK,MAAM,KAAK,KAAK,KAAK,CAC7B,CAGD,eAAelB,EAAMkB,EAAOjB,EAAQ,EAAG,CACnC,KAAK,aAAa,KAAK,OAAO,YAAYD,CAAI,EAAGkB,EAAOjB,CAAK,CAChE,CAED,QAAQwH,EAAOvH,EAAMsB,EAAI,CACjB,OAAOiG,GAAS,WAChBA,EAAQ,IAAI9G,EAAK,KAAK,OAAO,QAAQ,MAAM8G,CAAK,EAAGC,EAAMA,GAAOlG,GAAoC,KAAK,eAAiBtB,CAAI,GAClI,KAAK,MAAM,SAASuH,EAAOvH,EAAO,KAAK,MAAM,IAAI,CACpD,CAGD,WAAWwB,EAAK,CACZ,KAAK,MAAM,SAASA,EAAI,OAAO,KAAK,OAAO,OAAO,EAAGA,EAAI,KAAO,KAAK,MAAM,IAAI,CAClF,CAID,eAAe0D,EAAM1D,EAAK,CACtB,KAAK,QAAQ,KAAK,OACb,cAAciG,EAAYjG,EAAI,SAAU0D,EAAK,KAAK,EAAG,CAAC1D,EAAI,IAAI,EAC9D,OAAOA,EAAI,KAAMA,EAAI,GAAKA,EAAI,IAAI,EAAGA,EAAI,IAAI,CACrD,CAED,eAAgB,CACZ,IAAIM,EAAK,KAAK,MAAM,IAAG,EACnB4F,EAAM,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,EAC1CA,EAAI,SAAS5F,EAAG,OAAO,KAAK,OAAO,OAAO,EAAGA,EAAG,KAAO4F,EAAI,IAAI,EAC/D,KAAK,MAAQA,CAChB,CACD,QAAS,CACL,KAAO,KAAK,MAAM,OAAS,GACvB,KAAK,cAAa,EACtB,OAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,KAAK,OAAO,QAAS,KAAK,SAAS,CAAC,CAC7E,CACD,QAAQC,EAAM,CACV,OAAO,KAAK,OAAO,OAAS,EAAIC,GAAW,KAAK,OAAQ,EAAGD,EAAK,QAAS,KAAK,OAAO,CAAC,EAAE,KAAM,KAAK,UAAU,EAAIA,CACpH,CAED,WAAWzC,EAAM,CACb,QAASkB,KAAUlB,EAAK,QACpB,GAAIkB,EAAO,OAAO,KAAMlB,CAAI,EACxB,OACR,IAAI2C,EAASJ,EAAY,KAAK,OAAO,YAAYvC,EAAK,QAASA,EAAK,KAAK,EAAGA,EAAK,KAAK,EACtF,KAAK,QAAQ,KAAK,OACb,cAAc2C,EAAQ,CAAC3C,EAAK,KAAK,EACjC,OAAOpE,EAAK,UAAWoE,EAAK,QAAQ,MAAM,EAAGA,EAAK,KAAK,CAC/D,CACD,IAAIpF,EAAME,EAAMsB,EAAInB,EAAU,CAC1B,OAAI,OAAOL,GAAQ,SACR0B,EAAI,KAAK,OAAO,YAAY1B,CAAI,EAAGE,EAAMsB,EAAInB,CAAQ,EACzD,IAAI2H,GAAYhI,EAAME,CAAI,CACpC,CAED,IAAI,QAAS,CAAE,OAAO,IAAI+H,GAAO,KAAK,OAAO,OAAO,CAAI,CAC5D,CACA,SAASH,GAAWrB,EAAQY,EAAQQ,EAAMK,EAAQC,EAAM,CACpD,GAAIA,EAAK,IAAIN,EAAK,IAAI,EAClB,OAAOA,EAAK,KAChB,IAAIO,EAAW3B,EAAOY,CAAM,EAAE,GAC1BhH,EAAW,CAAE,EAAEC,EAAY,CAAA,EAAIY,EAAQ2G,EAAK,KAAOK,EACvD,SAASG,EAAaC,EAAMC,EAAW,CACnC,KAAOA,EAAYD,GAAQF,EAAWE,EAAOF,GAAU,CACnD,IAAIlG,EAAOuE,EAAOY,EAAS,CAAC,EAAE,KAAOe,EACrCF,GAAUhG,EACVoG,GAAQpG,EACRmF,IACAe,EAAW3B,EAAOY,CAAM,EAAE,GAEjC,CACD,QAAS5E,EAAKoF,EAAK,WAAYpF,EAAIA,EAAKA,EAAG,YAAa,CACpD4F,EAAa5F,EAAG,KAAOyF,EAAQ,EAAI,EACnC,IAAIhI,EAAOuC,EAAG,KAAOyF,EAAQlD,EACzBvC,EAAG,GAAKyF,EAASE,GACjBpD,EAAO8C,GAAWrB,EAAQY,EAAQ5E,EAAIyF,EAAQC,CAAI,EAClDE,EAAa5F,EAAG,GAAKyF,EAAQ,EAAK,GAGlClD,EAAOvC,EAAG,SAEdpC,EAAS,KAAK2E,CAAI,EAClB1E,EAAU,KAAKJ,EAAOgB,CAAK,EAE/B,OAAAmH,EAAaR,EAAK,GAAKK,EAAQ,EAAK,EAC7B,IAAIvH,EAAKkH,EAAK,KAAMxH,EAAUC,EAAWuH,EAAK,GAAKK,EAAShH,EAAO2G,EAAK,KAAOA,EAAK,KAAK,WAAa,MAAS,CAC1H,CAEA,MAAMW,UAAuBC,EAAO,CAEhC,YAGA7H,EAEA8H,EAEAC,EAEAC,EAEAC,EAEAC,EAEAC,EAEAC,EAEAC,EAAU,CACN,QACA,KAAK,QAAUrI,EACf,KAAK,aAAe8H,EACpB,KAAK,iBAAmBC,EACxB,KAAK,WAAaC,EAClB,KAAK,aAAeC,EACpB,KAAK,kBAAoBC,EACzB,KAAK,cAAgBC,EACrB,KAAK,YAAcC,EACnB,KAAK,SAAWC,EAEhB,KAAK,UAAY,OAAO,OAAO,IAAI,EACnC,QAASC,KAAKtI,EAAQ,MAClB,KAAK,UAAUsI,EAAE,IAAI,EAAIA,EAAE,EAClC,CACD,YAAY3C,EAAOC,EAAWC,EAAQ,CAClC,IAAIG,EAAQ,IAAIP,GAAa,KAAME,EAAOC,EAAWC,CAAM,EAC3D,QAAS0C,KAAK,KAAK,SACfvC,EAAQuC,EAAEvC,EAAOL,EAAOC,EAAWC,CAAM,EAC7C,OAAOG,CACV,CAED,UAAUwC,EAAM,CACZ,IAAIC,EAASC,EAAcF,CAAI,EAC/B,GAAI,CAACC,EACD,OAAO,KACX,GAAI,CAAE,QAAAzI,EAAS,kBAAAkI,CAAmB,EAAG,KACjCJ,EAAe,KAAK,aAAa,MAAO,EAAEC,EAAmB,KAAK,iBAAiB,MAAK,EAAIC,EAAa,KAAK,WAAW,MAAO,EAAEG,EAAgB,KAAK,cAAc,MAAK,EAAIC,EAAc,KAAK,YAAY,MAAO,EAAEH,EAAe,KAAK,aAAa,MAAK,EAAII,EAAW,KAAK,SACpR,GAAIM,EAASF,EAAO,WAAW,EAAG,CAC9BP,EAAoB,OAAO,OAAO,CAAE,EAAEA,CAAiB,EACvD,IAAIU,EAAY5I,EAAQ,MAAM,MAAK,EAAI6I,EACvC,QAASC,KAAKL,EAAO,YAAa,CAC9B,GAAI,CAAE,KAAAM,EAAM,MAAAlC,EAAO,UAAAmC,EAAW,MAAAC,CAAO,EAAG,OAAOH,GAAK,SAAW,CAAE,KAAMA,CAAC,EAAKA,EAC7E,GAAIF,EAAU,KAAKN,GAAKA,EAAE,MAAQS,CAAI,EAClC,SACAC,IACAd,EAAkBU,EAAU,MAAM,EAC9B,CAACzH,EAAIC,GAAIC,KAAS2H,EAAU5H,GAAIC,GAAMF,EAAG,KAAK,GACtD,IAAI+H,EAAKN,EAAU,OACfO,GAAQH,EAAY,CAAC,QAAS,cAAc,EAAKnC,EAC/CqC,GAAM9I,EAAK,aAAe8I,GAAM9I,EAAK,eAAiB,CAAC,QAAS,YAAa,SAAS,EAAI,CAAC,QAAS,WAAW,EADxD,OAE7DwI,EAAU,KAAKzI,EAAS,OAAO,CAC3B,GAAA+I,EACA,KAAAH,EACA,MAAOI,IAAS,CAAC,CAACxJ,EAAS,MAAOwJ,EAAK,CAAC,CAC3C,CAAA,CAAC,EACEF,IACKJ,IACDA,EAAS,CAAA,GACT,MAAM,QAAQI,CAAK,GAAKA,aAAiBG,GACzCP,EAAOE,CAAI,EAAIE,EAEf,OAAO,OAAOJ,EAAQI,CAAK,GAGvCjJ,EAAU,IAAIqJ,GAAQT,CAAS,EAC3BC,IACA7I,EAAUA,EAAQ,OAAOsJ,GAAUT,CAAM,CAAC,GAIlD,GAFIF,EAASF,EAAO,KAAK,IACrBzI,EAAUA,EAAQ,OAAO,GAAGyI,EAAO,KAAK,GACxCE,EAASF,EAAO,MAAM,EACtB,QAASc,KAAMd,EAAO,OAAQ,CAC1B,IAAI5B,EAAQ,KAAK,WAAW,QAAQ0C,CAAE,EAAGpC,EAAS,KAAK,YAAY,QAAQoC,CAAE,EACzE1C,EAAQ,KACRiB,EAAajB,CAAK,EAAIkB,EAAiBlB,CAAK,EAAI,QAChDM,EAAS,KACTgB,EAAchB,CAAM,EAAI,QAGpC,GAAIwB,EAASF,EAAO,UAAU,EAC1B,QAASD,KAAQC,EAAO,WAAY,CAChC,IAAIe,EAAQxB,EAAW,QAAQQ,EAAK,IAAI,EACxC,GAAIgB,EAAQ,GACR1B,EAAa0B,CAAK,EAAIhB,EAAK,MAC3BT,EAAiByB,CAAK,EAAIhB,EAAK,SAE9B,CACD,IAAI1I,EAAM0I,EAAK,OAASiB,EAASzB,EAAYQ,EAAK,MAAM,EAClDA,EAAK,MAAQiB,EAASzB,EAAYQ,EAAK,KAAK,EAAI,EAAIR,EAAW,OAAS,EAC9EF,EAAa,OAAOhI,EAAK,EAAG0I,EAAK,KAAK,EACtCT,EAAiB,OAAOjI,EAAK,EAAG0I,EAAK,IAAI,EACzCR,EAAW,OAAOlI,EAAK,EAAG0I,EAAK,IAAI,EAEnCA,EAAK,SACLP,EAAa,KAAKO,EAAK,OAAO,EAG1C,GAAIG,EAASF,EAAO,WAAW,EAC3B,QAASD,KAAQC,EAAO,YAAa,CACjC,IAAIe,EAAQpB,EAAY,QAAQI,EAAK,IAAI,EACzC,GAAIgB,EAAQ,GACRrB,EAAcqB,CAAK,EAAIhB,EAAK,UAE3B,CACD,IAAI1I,EAAM0I,EAAK,OAASiB,EAASrB,EAAaI,EAAK,MAAM,EACnDA,EAAK,MAAQiB,EAASrB,EAAaI,EAAK,KAAK,EAAI,EAAIJ,EAAY,OAAS,EAChFD,EAAc,OAAOrI,EAAK,EAAG0I,EAAK,KAAK,EACvCJ,EAAY,OAAOtI,EAAK,EAAG0I,EAAK,IAAI,GAIhD,OAAIC,EAAO,OACPJ,EAAWA,EAAS,OAAOI,EAAO,IAAI,GACnC,IAAIb,EAAe5H,EAAS8H,EAAcC,EAAkBC,EAAYC,EAAcC,EAAmBC,EAAeC,EAAaC,CAAQ,CACvJ,CAED,YAAYU,EAAM,CACd,IAAIS,EAAQ,KAAK,UAAUT,CAAI,EAC/B,GAAIS,GAAS,KACT,MAAM,IAAI,WAAW,sBAAsBT,IAAO,EACtD,OAAOS,CACV,CAID,YAAY7I,EAAM2G,EAAQ,CACtB,IAAIlG,EAAK,IAAIsI,GAAc,KAAM/I,EAAM2G,CAAM,EAC7CqC,EAAO,QAAS7J,EAAMwH,EAAQxH,EAAMsB,EAAG,KAAM,CACzC,IAAIiB,EAAOjB,EAAG,KAAKtB,CAAG,EACtB,QAAS8J,KAAS,KAAK,cACnB,GAAIA,EAAO,CACP,IAAI3I,EAAS2I,EAAMxI,EAAIiB,EAAMvC,CAAG,EAChC,GAAImB,GAAU,EAAG,CACbnB,EAAMmB,EACN,SAAS0I,GAGrB7J,IAEJ,OAAOsB,EAAG,eAAe,CAAC,CAC7B,CACL,CACA,SAASuH,EAASkB,EAAG,CACjB,OAAOA,GAAK,MAAQA,EAAE,OAAS,CACnC,CACA,SAASnB,EAAcF,EAAM,CACzB,GAAI,CAAC,MAAM,QAAQA,CAAI,EACnB,OAAOA,EACX,GAAIA,EAAK,QAAU,EACf,OAAO,KACX,IAAIsB,EAAOpB,EAAcF,EAAK,CAAC,CAAC,EAChC,GAAIA,EAAK,QAAU,EACf,OAAOsB,EACX,IAAIlH,EAAO8F,EAAcF,EAAK,MAAM,CAAC,CAAC,EACtC,GAAI,CAAC5F,GAAQ,CAACkH,EACV,OAAOA,GAAQlH,EACnB,IAAImH,EAAO,CAACF,EAAGG,KAAOH,GAAK/C,GAAM,OAAOkD,GAAKlD,CAAI,EAC7CmD,EAAQH,EAAK,KAAMI,EAAQtH,EAAK,KACpC,MAAO,CACH,MAAOmH,EAAKD,EAAK,MAAOlH,EAAK,KAAK,EAClC,YAAamH,EAAKD,EAAK,YAAalH,EAAK,WAAW,EACpD,WAAYmH,EAAKD,EAAK,WAAYlH,EAAK,UAAU,EACjD,YAAamH,EAAKD,EAAK,YAAalH,EAAK,WAAW,EACpD,OAAQmH,EAAKD,EAAK,OAAQlH,EAAK,MAAM,EACrC,KAAOqH,EAAiBC,EACpB,CAACC,EAAOxE,EAAOC,EAAWC,IAAWoE,EAAMC,EAAMC,EAAOxE,EAAOC,EAAWC,CAAM,EAAGF,EAAOC,EAAWC,CAAM,EAD/EoE,EAAjBC,CAEvB,CACA,CACA,SAAST,EAASW,EAAOrB,EAAM,CAC3B,IAAIS,EAAQY,EAAM,QAAQrB,CAAI,EAC9B,GAAIS,EAAQ,EACR,MAAM,IAAI,WAAW,iDAAiDT,GAAM,EAChF,OAAOS,CACX,CACA,IAAIZ,GAAY,CAACzI,EAAS,IAAI,EAC9B,QAASY,EAAI,EAAGgI,EAAMA,EAAO3I,EAAKW,CAAC,EAAGA,IAClC6H,GAAU7H,CAAC,EAAIZ,EAAS,OAAO,CAC3B,GAAIY,EACJ,KAAAgI,EACA,MAAOhI,GAAKX,EAAK,OAAS,CAAA,EAAK,CAAC,CAACT,EAAS,MAAOoB,KAAKW,GAAoB,CAAC,QAAS,cAAc,EAAI,CAAC,QAAS,WAAW,CAAC,CAAC,CACrI,CAAK,EAEL,MAAMoF,EAAO,CAAA,EACb,MAAMO,EAAO,CACT,YAAYrH,EAAS,CACjB,KAAK,QAAUA,EACf,KAAK,QAAU,GACf,KAAK,MAAQ,EAChB,CACD,MAAMZ,EAAME,EAAMsB,EAAInB,EAAW,EAAG,CAChC,YAAK,QAAQ,KAAKL,EAAME,EAAMsB,EAAI,EAAInB,EAAW,CAAC,EAC3C,IACV,CACD,cAAc4K,EAAM/C,EAAS,EAAG,CAC5B,QAASzE,KAAKwH,EACVxH,EAAE,QAAQ,KAAMyE,CAAM,EAC1B,OAAO,IACV,CACD,OAAOlI,EAAMc,EAAQ,CACjB,OAAOH,EAAK,MAAM,CACd,OAAQ,KAAK,QACb,QAAS,KAAK,QACd,OAAQ,KAAK,MACb,MAAOX,EACP,OAAAc,CACZ,CAAS,CACJ,CACL,CAEA,MAAMoK,CAAQ,CAEV,YAGAlL,EAEAE,EAEAsB,EAEAnB,EAAWqH,EAAM,CACb,KAAK,KAAO1H,EACZ,KAAK,KAAOE,EACZ,KAAK,GAAKsB,EACV,KAAK,SAAWnB,CACnB,CAED,QAAQ0E,EAAKmD,EAAQ,CACjB,IAAIiD,EAAWpG,EAAI,QAAQ,OAC3BA,EAAI,cAAc,KAAK,SAAUmD,CAAM,EACvCnD,EAAI,QAAQ,KAAK,KAAK,KAAM,KAAK,KAAOmD,EAAQ,KAAK,GAAKA,EAAQnD,EAAI,QAAQ,OAAS,EAAIoG,CAAQ,CACtG,CAED,OAAOvK,EAAS,CACZ,OAAO,IAAIqH,GAAOrH,CAAO,EAAE,cAAc,KAAK,SAAU,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,KAAM,KAAK,GAAK,KAAK,IAAI,CAC5G,CACL,CACA,MAAMoH,EAAY,CACd,YAAYH,EAAM3H,EAAM,CACpB,KAAK,KAAO2H,EACZ,KAAK,KAAO3H,CACf,CACD,IAAI,IAAK,CAAE,OAAO,KAAK,KAAO,KAAK,KAAK,MAAS,CACjD,IAAI,MAAO,CAAE,OAAO,KAAK,KAAK,KAAK,EAAK,CACxC,IAAI,UAAW,CAAE,OAAOwH,CAAO,CAC/B,QAAQ3C,EAAKmD,EAAQ,CACjBnD,EAAI,MAAM,KAAK,KAAK,IAAI,EACxBA,EAAI,QAAQ,KAAKA,EAAI,MAAM,OAAS,EAAG,KAAK,KAAOmD,EAAQ,KAAK,GAAKA,EAAQ,EAAE,CAClF,CACD,QAAS,CAAE,OAAO,KAAK,IAAO,CAClC,CACA,SAASxG,EAAI1B,EAAME,EAAMsB,EAAInB,EAAU,CACnC,OAAO,IAAI6K,EAAQlL,EAAME,EAAMsB,EAAInB,CAAQ,CAC/C,CACA,MAAM+K,GAAqB,CAAE,QAAS,WAAY,KAAM,cAAc,EAChEC,GAAmB,CAAE,QAAS,WAAY,KAAM,cAAc,EAC9DC,EAAY,CAAA,EAAIC,GAAa,GACnC,MAAMC,CAAgB,CAClB,YAAYxL,EAAME,EAAMsB,EAAIiK,EAAM,CAC9B,KAAK,KAAOzL,EACZ,KAAK,KAAOE,EACZ,KAAK,GAAKsB,EACV,KAAK,KAAOiK,CACf,CACL,CACA,MAAMC,GAAY,qCAClB,IAAIC,EAAc,2DAClB,GAAI,CACAA,EAAc,IAAI,OAAO,4DAA6D,GAAG,CAC7F,MACA,CAAa,CACb,MAAMC,EAAgB,CAClB,OAAO5J,EAAIiB,EAAM/B,EAAO,CACpB,GAAI+B,GAAQ,IAAiB/B,GAASc,EAAG,IAAM,EAC3C,MAAO,GACX,IAAI6J,EAAU7J,EAAG,KAAKd,EAAQ,CAAC,EAC/B,QAASS,EAAI,EAAGA,EAAI+J,GAAU,OAAQ/J,IAClC,GAAI+J,GAAU,WAAW/J,CAAC,GAAKkK,EAC3B,OAAO7J,EAAG,OAAON,EAAIV,EAAK,OAAQE,EAAOA,EAAQ,CAAC,CAAC,EAC3D,MAAO,EACV,EACD,OAAOc,EAAIiB,EAAM/B,EAAO,CACpB,GAAI+B,GAAQ,GACR,MAAO,GACX,IAAIiB,EAAI,6BAA6B,KAAKlC,EAAG,MAAMd,EAAQ,EAAGA,EAAQ,EAAE,CAAC,EACzE,OAAOgD,EAAIlC,EAAG,OAAON,EAAIV,EAAK,OAAQE,EAAOA,EAAQ,EAAIgD,EAAE,CAAC,EAAE,MAAM,CAAC,EAAI,EAC5E,EACD,WAAWlC,EAAIiB,EAAM/B,EAAO,CACxB,GAAI+B,GAAQ,IAAgB/B,GAASc,EAAG,KAAKd,EAAQ,CAAC,GAAK,GACvD,MAAO,GACX,IAAIR,EAAMQ,EAAQ,EAClB,KAAOR,EAAMsB,EAAG,KAAOA,EAAG,KAAKtB,CAAG,GAAK,IACnCA,IACJ,IAAIwB,EAAOxB,EAAMQ,EAAO4K,EAAU,EAClC,KAAOpL,EAAMsB,EAAG,IAAKtB,IACjB,GAAIsB,EAAG,KAAKtB,CAAG,GAAK,IAEhB,GADAoL,IACIA,GAAW5J,GAAQF,EAAG,KAAKtB,EAAM,CAAC,GAAK,GACvC,OAAOsB,EAAG,OAAON,EAAIV,EAAK,WAAYE,EAAOR,EAAM,EAAG,CAClDgB,EAAIV,EAAK,SAAUE,EAAOA,EAAQgB,CAAI,EACtCR,EAAIV,EAAK,SAAUN,EAAM,EAAIwB,EAAMxB,EAAM,CAAC,CAC7C,CAAA,CAAC,OAGNoL,EAAU,EAGlB,MAAO,EACV,EACD,QAAQ9J,EAAIiB,EAAM/B,EAAO,CACrB,GAAI+B,GAAQ,IAAgB/B,GAASc,EAAG,IAAM,EAC1C,MAAO,GACX,IAAI8C,EAAQ9C,EAAG,MAAMd,EAAQ,EAAGc,EAAG,GAAG,EAClC+J,EAAM,sIAAsI,KAAKjH,CAAK,EAC1J,GAAIiH,EACA,OAAO/J,EAAG,OAAON,EAAIV,EAAK,IAAKE,EAAOA,EAAQ,EAAI6K,EAAI,CAAC,EAAE,MAAM,CAAC,EACpE,IAAIC,EAAU,+BAA+B,KAAKlH,CAAK,EACvD,GAAIkH,EACA,OAAOhK,EAAG,OAAON,EAAIV,EAAK,QAASE,EAAOA,EAAQ,EAAI8K,EAAQ,CAAC,EAAE,MAAM,CAAC,EAC5E,IAAIC,EAAW,cAAc,KAAKnH,CAAK,EACvC,GAAImH,EACA,OAAOjK,EAAG,OAAON,EAAIV,EAAK,sBAAuBE,EAAOA,EAAQ,EAAI+K,EAAS,CAAC,EAAE,MAAM,CAAC,EAC3F,IAAI/H,EAAI,mKAAmK,KAAKY,CAAK,EACrL,OAAKZ,EAEElC,EAAG,OAAON,EAAIV,EAAK,QAASE,EAAOA,EAAQ,EAAIgD,EAAE,CAAC,EAAE,MAAM,CAAC,EADvD,EAEd,EACD,SAASlC,EAAIiB,EAAM/B,EAAO,CACtB,GAAI+B,GAAQ,IAAMA,GAAQ,GACtB,MAAO,GACX,IAAIvC,EAAMQ,EAAQ,EAClB,KAAOc,EAAG,KAAKtB,CAAG,GAAKuC,GACnBvC,IACJ,IAAIwL,EAASlK,EAAG,MAAMd,EAAQ,EAAGA,CAAK,EAAG4D,EAAQ9C,EAAG,MAAMtB,EAAKA,EAAM,CAAC,EAClEyL,EAAUR,EAAY,KAAKO,CAAM,EAAGE,EAAST,EAAY,KAAK7G,CAAK,EACnEuH,EAAU,QAAQ,KAAKH,CAAM,EAAGI,EAAS,QAAQ,KAAKxH,CAAK,EAC3DyH,EAAe,CAACD,IAAW,CAACF,GAAUC,GAAWF,GACjDK,EAAgB,CAACH,IAAY,CAACF,GAAWG,GAAUF,GACnDK,EAAUF,IAAiBtJ,GAAQ,IAAM,CAACuJ,GAAiBL,GAC3DO,EAAWF,IAAkBvJ,GAAQ,IAAM,CAACsJ,GAAgBH,GAChE,OAAOpK,EAAG,OAAO,IAAIwJ,EAAgBvI,GAAQ,GAAKmI,GAAqBC,GAAkBnK,EAAOR,GAAM+L,EAAU,EAAe,IAAMC,EAAW,EAAgB,EAAE,CAAC,CACtK,EACD,UAAU1K,EAAIiB,EAAM/B,EAAO,CACvB,GAAI+B,GAAQ,IAAiBjB,EAAG,KAAKd,EAAQ,CAAC,GAAK,GAC/C,OAAOc,EAAG,OAAON,EAAIV,EAAK,UAAWE,EAAOA,EAAQ,CAAC,CAAC,EAC1D,GAAI+B,GAAQ,GAAI,CACZ,IAAIvC,EAAMQ,EAAQ,EAClB,KAAOc,EAAG,KAAKtB,CAAG,GAAK,IACnBA,IACJ,GAAIsB,EAAG,KAAKtB,CAAG,GAAK,IAAMA,GAAOQ,EAAQ,EACrC,OAAOc,EAAG,OAAON,EAAIV,EAAK,UAAWE,EAAOR,EAAM,CAAC,CAAC,EAE5D,MAAO,EACV,EACD,KAAKsB,EAAIiB,EAAM/B,EAAO,CAClB,OAAO+B,GAAQ,GAAejB,EAAG,OAAO,IAAIwJ,EAAgBF,EAAWpK,EAAOA,EAAQ,EAAG,CAAC,CAAY,EAAI,EAC7G,EACD,MAAMc,EAAIiB,EAAM/B,EAAO,CACnB,OAAO+B,GAAQ,IAAgBjB,EAAG,KAAKd,EAAQ,CAAC,GAAK,GAC/Cc,EAAG,OAAO,IAAIwJ,EAAgBD,GAAYrK,EAAOA,EAAQ,EAAG,EAAa,EAAI,EACtF,EACD,QAAQc,EAAIiB,EAAM/B,EAAO,CACrB,GAAI+B,GAAQ,GACR,MAAO,GAEX,QAAStB,EAAIK,EAAG,MAAM,OAAS,EAAGL,GAAK,EAAGA,IAAK,CAC3C,IAAIgL,EAAO3K,EAAG,MAAML,CAAC,EACrB,GAAIgL,aAAgBnB,IAAoBmB,EAAK,MAAQrB,GAAaqB,EAAK,MAAQpB,IAAa,CAGxF,GAAI,CAACoB,EAAK,MAAQ3K,EAAG,UAAU2K,EAAK,EAAE,GAAKzL,GAAS,CAAC,QAAQ,KAAKc,EAAG,MAAMd,EAAQ,EAAGA,EAAQ,CAAC,CAAC,EAC5F,OAAAc,EAAG,MAAML,CAAC,EAAI,KACP,GAIX,IAAIR,EAAUa,EAAG,YAAYL,CAAC,EAC1BiL,EAAO5K,EAAG,MAAML,CAAC,EAAIkL,GAAW7K,EAAIb,EAASwL,EAAK,MAAQrB,EAAYtK,EAAK,KAAOA,EAAK,MAAO2L,EAAK,KAAMzL,EAAQ,CAAC,EAEtH,GAAIyL,EAAK,MAAQrB,EACb,QAASwB,EAAI,EAAGA,EAAInL,EAAGmL,IAAK,CACxB,IAAI3G,EAAInE,EAAG,MAAM8K,CAAC,EACd3G,aAAaqF,GAAmBrF,EAAE,MAAQmF,IAC1CnF,EAAE,KAAO,GAErB,OAAOyG,EAAK,IAGpB,MAAO,EACV,CACL,EACA,SAASC,GAAW7K,EAAIb,EAASnB,EAAMkB,EAAO6L,EAAU,CACpD,GAAI,CAAE,KAAAxL,GAASS,EAAIiB,EAAOjB,EAAG,KAAK+K,CAAQ,EAAGC,EAASD,EAGtD,GAFA5L,EAAQ,QAAQO,EAAIV,EAAK,SAAUE,EAAOA,GAASlB,GAAQgB,EAAK,MAAQ,EAAI,EAAE,CAAC,EAC/EG,EAAQ,KAAKO,EAAIV,EAAK,SAAU+L,EAAW,EAAGA,CAAQ,CAAC,EACnD9J,GAAQ,GAAc,CACtB,IAAIvC,EAAMsB,EAAG,UAAU+K,EAAW,CAAC,EAC/BE,EAAO1H,GAAShE,EAAMb,EAAMsB,EAAG,OAAQA,EAAG,MAAM,EAAGyD,EACnDwH,IACAvM,EAAMsB,EAAG,UAAUiL,EAAK,EAAE,EAC1BxH,EAAQC,GAAenE,EAAMb,EAAMsB,EAAG,OAAQA,EAAG,MAAM,EACnDyD,IACA/E,EAAMsB,EAAG,UAAUyD,EAAM,EAAE,IAE/BzD,EAAG,KAAKtB,CAAG,GAAK,KAChBS,EAAQ,KAAKO,EAAIV,EAAK,SAAU+L,EAAUA,EAAW,CAAC,CAAC,EACvDC,EAAStM,EAAM,EACXuM,GACA9L,EAAQ,KAAK8L,CAAI,EACjBxH,GACAtE,EAAQ,KAAKsE,CAAK,EACtBtE,EAAQ,KAAKO,EAAIV,EAAK,SAAUN,EAAKsM,CAAM,CAAC,WAG3C/J,GAAQ,GAAc,CAC3B,IAAIiK,EAAQ5H,GAAe/D,EAAMwL,EAAW/K,EAAG,OAAQA,EAAG,OAAQ,EAAK,EACnEkL,IACA/L,EAAQ,KAAK+L,CAAK,EAClBF,EAASE,EAAM,IAGvB,OAAOxL,EAAI1B,EAAMkB,EAAO8L,EAAQ7L,CAAO,CAC3C,CAIA,SAASoE,GAAShE,EAAML,EAAOgH,EAAQ,CAEnC,GADW3G,EAAK,WAAWL,CAAK,GACpB,GAAc,CACtB,QAASR,EAAMQ,EAAQ,EAAGR,EAAMa,EAAK,OAAQb,IAAO,CAChD,IAAI+B,EAAKlB,EAAK,WAAWb,CAAG,EAC5B,GAAI+B,GAAM,GACN,OAAOf,EAAIV,EAAK,IAAKE,EAAQgH,EAAQxH,EAAM,EAAIwH,CAAM,EACzD,GAAIzF,GAAM,IAAMA,GAAM,GAClB,MAAO,GAEf,OAAO,SAEN,CACD,IAAI0E,EAAQ,EAAGzG,EAAMQ,EACrB,QAAS2K,EAAU,GAAOnL,EAAMa,EAAK,OAAQb,IAAO,CAChD,IAAI+B,EAAKlB,EAAK,WAAWb,CAAG,EAC5B,GAAI6B,EAAME,CAAE,EACR,MAEC,GAAIoJ,EACLA,EAAU,WAELpJ,GAAM,GACX0E,YAEK1E,GAAM,GAAc,CACzB,GAAI,CAAC0E,EACD,MACJA,SAEK1E,GAAM,KACXoJ,EAAU,IAGlB,OAAOnL,EAAMQ,EAAQQ,EAAIV,EAAK,IAAKE,EAAQgH,EAAQxH,EAAMwH,CAAM,EAAIxH,GAAOa,EAAK,OAAS,KAAO,GAEvG,CACA,SAASmE,GAAenE,EAAML,EAAOgH,EAAQ,CACzC,IAAIjF,EAAO1B,EAAK,WAAWL,CAAK,EAChC,GAAI+B,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,GACpC,MAAO,GACX,IAAI7C,EAAM6C,GAAQ,GAAK,GAAKA,EAC5B,QAASvC,EAAMQ,EAAQ,EAAG2K,EAAU,GAAOnL,EAAMa,EAAK,OAAQb,IAAO,CACjE,IAAI+B,EAAKlB,EAAK,WAAWb,CAAG,EAC5B,GAAImL,EACAA,EAAU,OACT,IAAIpJ,GAAMrC,EACX,OAAOsB,EAAIV,EAAK,UAAWE,EAAQgH,EAAQxH,EAAM,EAAIwH,CAAM,EACtDzF,GAAM,KACXoJ,EAAU,KAElB,OAAO,IACX,CACA,SAASvG,GAAe/D,EAAML,EAAOgH,EAAQiF,EAAc,CACvD,QAAStB,EAAU,GAAOnL,EAAMQ,EAAQ,EAAGd,EAAM,KAAK,IAAImB,EAAK,OAAQb,EAAM,GAAG,EAAGA,EAAMN,EAAKM,IAAO,CACjG,IAAI+B,EAAKlB,EAAK,WAAWb,CAAG,EAC5B,GAAImL,EACAA,EAAU,OACT,IAAIpJ,GAAM,GACX,OAAO0K,EAAe,GAAQzL,EAAIV,EAAK,UAAWE,EAAQgH,EAAQxH,EAAM,EAAIwH,CAAM,EAIlF,GAFIiF,GAAgB,CAAC5K,EAAME,CAAE,IACzB0K,EAAe,IACf1K,GAAM,GACN,MAAO,GACFA,GAAM,KACXoJ,EAAU,KAGtB,OAAO,IACX,CAGA,MAAMvB,EAAc,CAEhB,YAEAhE,EAEA/E,EAEA2G,EAAQ,CACJ,KAAK,OAAS5B,EACd,KAAK,KAAO/E,EACZ,KAAK,OAAS2G,EAEd,KAAK,MAAQ,EAChB,CAGD,KAAKxH,EAAK,CAAE,OAAOA,GAAO,KAAK,IAAM,GAAK,KAAK,KAAK,WAAWA,EAAM,KAAK,MAAM,CAAI,CAEpF,IAAI,KAAM,CAAE,OAAO,KAAK,OAAS,KAAK,KAAK,MAAS,CAGpD,MAAMR,EAAMsB,EAAI,CAAE,OAAO,KAAK,KAAK,MAAMtB,EAAO,KAAK,OAAQsB,EAAK,KAAK,MAAM,CAAI,CAEjF,OAAOE,EAAK,CACR,YAAK,MAAM,KAAKA,CAAG,EACZA,EAAI,EACd,CAKD,aAAa1B,EAAME,EAAMsB,EAAI4L,EAAMC,EAAO,CACtC,OAAO,KAAK,OAAO,IAAI7B,EAAgBxL,EAAME,EAAMsB,GAAK4L,EAAO,EAAe,IAAMC,EAAQ,EAAgB,EAAE,CAAC,CAClH,CAED,WAAW3L,EAAK,CACZ,OAAO,KAAK,OAAOA,CAAG,CACzB,CAGD,eAAexB,EAAM,CAEjB,QAASyB,EAAIzB,EAAMyB,EAAI,KAAK,MAAM,OAAQA,IAAK,CAC3C,IAAI0L,EAAQ,KAAK,MAAM1L,CAAC,EACxB,GAAI,EAAE0L,aAAiB7B,GAAmB6B,EAAM,KAAK,SAAYA,EAAM,KAAO,GAC1E,SACJ,IAAIC,EAAMD,EAAM,MAAQjC,IAAsBiC,EAAM,MAAQhC,GACxDkC,EAAYF,EAAM,GAAKA,EAAM,KAC7BD,EAAMN,EAAInL,EAAI,EAElB,KAAOmL,GAAK5M,EAAM4M,IAAK,CACnB,IAAIH,EAAO,KAAK,MAAMG,CAAC,EACvB,GAAIH,aAAgBnB,GAAoBmB,EAAK,KAAO,GAAiBA,EAAK,MAAQU,EAAM,MAEpF,EAAEC,IAASD,EAAM,KAAO,GAAkBV,EAAK,KAAO,KACjDA,EAAK,GAAKA,EAAK,KAAOY,GAAa,GAAK,KAAOZ,EAAK,GAAKA,EAAK,MAAQ,GAAKY,EAAY,IAAK,CACjGH,EAAOT,EACP,OAGR,GAAI,CAACS,EACD,SACJ,IAAIpN,EAAOqN,EAAM,KAAK,QAASlM,EAAU,CAAA,EACrCD,EAAQkM,EAAK,KAAMhN,EAAMiN,EAAM,GAGnC,GAAIC,EAAK,CACL,IAAIpL,EAAO,KAAK,IAAI,EAAGkL,EAAK,GAAKA,EAAK,KAAMG,CAAS,EACrDrM,EAAQkM,EAAK,GAAKlL,EAClB9B,EAAMiN,EAAM,KAAOnL,EACnBlC,EAAOkC,GAAQ,EAAI,WAAa,iBAGhCkL,EAAK,KAAK,MACVjM,EAAQ,KAAK,KAAK,IAAIiM,EAAK,KAAK,KAAMlM,EAAOkM,EAAK,EAAE,CAAC,EACzD,QAASI,EAAIV,EAAI,EAAGU,EAAI7L,EAAG6L,IACnB,KAAK,MAAMA,CAAC,YAAatC,GACzB/J,EAAQ,KAAK,KAAK,MAAMqM,CAAC,CAAC,EAC9B,KAAK,MAAMA,CAAC,EAAI,KAEhBH,EAAM,KAAK,MACXlM,EAAQ,KAAK,KAAK,IAAIkM,EAAM,KAAK,KAAMA,EAAM,KAAMjN,CAAG,CAAC,EAC3D,IAAIqN,EAAU,KAAK,IAAIzN,EAAMkB,EAAOd,EAAKe,CAAO,EAEhD,KAAK,MAAM2L,CAAC,EAAIQ,GAAOF,EAAK,MAAQlM,EAAQ,IAAIsK,EAAgB4B,EAAK,KAAMA,EAAK,KAAMlM,EAAOkM,EAAK,IAAI,EAAI,MAC/F,KAAK,MAAMzL,CAAC,EAAI2L,GAAOD,EAAM,IAAMjN,EAAM,IAAIoL,EAAgB6B,EAAM,KAAMjN,EAAKiN,EAAM,GAAIA,EAAM,IAAI,EAAI,MAG7G,KAAK,MAAM,OAAO1L,EAAG,EAAG8L,CAAO,EAE/B,KAAK,MAAM9L,CAAC,EAAI8L,EAGxB,IAAI5L,EAAS,CAAA,EACb,QAASF,EAAIzB,EAAMyB,EAAI,KAAK,MAAM,OAAQA,IAAK,CAC3C,IAAIgL,EAAO,KAAK,MAAMhL,CAAC,EACnBgL,aAAgBzB,GAChBrJ,EAAO,KAAK8K,CAAI,EAExB,OAAO9K,CACV,CAID,qBAAqB7B,EAAM,CACvB,QAAS2B,EAAI,KAAK,MAAM,OAAS,EAAGA,GAAK,EAAGA,IAAK,CAC7C,IAAIgL,EAAO,KAAK,MAAMhL,CAAC,EACvB,GAAIgL,aAAgBnB,GAAmBmB,EAAK,MAAQ3M,EAChD,OAAO2B,EAEf,OAAO,IACV,CAMD,YAAY+L,EAAY,CACpB,IAAIvM,EAAU,KAAK,eAAeuM,CAAU,EAC5C,YAAK,MAAM,OAASA,EACbvM,CACV,CAID,UAAUjB,EAAM,CAAE,OAAOoB,EAAU,KAAK,KAAMpB,EAAO,KAAK,MAAM,EAAI,KAAK,MAAS,CAClF,IAAIF,EAAME,EAAMsB,EAAInB,EAAU,CAC1B,OAAI,OAAOL,GAAQ,SACR0B,EAAI,KAAK,OAAO,YAAY1B,CAAI,EAAGE,EAAMsB,EAAInB,CAAQ,EACzD,IAAI2H,GAAYhI,EAAME,CAAI,CACpC,CACL,CACA,SAASyH,EAAYgG,EAAU7J,EAAO,CAClC,GAAI,CAACA,EAAM,OACP,OAAO6J,EACX,GAAI,CAACA,EAAS,OACV,OAAO7J,EACX,IAAImH,EAAO0C,EAAS,MAAK,EAAIC,EAAK,EAClC,QAASjH,KAAQ7C,EAAO,CACpB,KAAO8J,EAAK3C,EAAK,QAAUA,EAAK2C,CAAE,EAAE,GAAKjH,EAAK,IAC1CiH,IACJ,GAAIA,EAAK3C,EAAK,QAAUA,EAAK2C,CAAE,EAAE,KAAOjH,EAAK,KAAM,CAC/C,IAAIlD,EAAIwH,EAAK2C,CAAE,EACXnK,aAAayH,IACbD,EAAK2C,CAAE,EAAI,IAAI1C,EAAQzH,EAAE,KAAMA,EAAE,KAAMA,EAAE,GAAIkE,EAAYlE,EAAE,SAAU,CAACkD,CAAI,CAAC,CAAC,QAGhFsE,EAAK,OAAO2C,IAAM,EAAGjH,CAAI,EAGjC,OAAOsE,CACX,CAGA,MAAM4C,GAAU,CAAC7M,EAAK,UAAWA,EAAK,SAAUA,EAAK,YAAaA,EAAK,UAAU,EACjF,MAAM0F,EAAe,CACjB,YAAYF,EAAWD,EAAO,CAC1B,KAAK,UAAYC,EACjB,KAAK,MAAQD,EAEb,KAAK,EAAI,EAET,KAAK,SAAW,KAChB,KAAK,YAAc,GAGnB,KAAK,OAAS,KACVC,EAAU,SACV,KAAK,SAAWA,EAAU,KAAK,GAAG,EACzC,CACD,cAAe,CACX,KAAK,SAAW,KAAK,EAAI,KAAK,UAAU,OAAS,KAAK,UAAU,KAAK,GAAG,EAAI,KAC5E,KAAK,OAAS,KACd,KAAK,YAAc,EACtB,CACD,OAAO9F,EAAKoN,EAAW,CACnB,KAAO,KAAK,UAAY,KAAK,SAAS,IAAMpN,GACxC,KAAK,aAAY,EACrB,GAAI,CAAC,KAAK,UAAY,KAAK,SAAS,MAAQA,EAAMA,EAAM,EAAI,GACxD,MAAO,GACX,GAAI,KAAK,YAAc,EAAG,CACtB,IAAIN,EAAM,KAAK,SAAS,GACxB,KAAOA,EAAM,GAAK,KAAK,MAAM,KAAKA,EAAM,EAAGA,CAAG,GAAK;AAAA,GAC/CA,IACJ,KAAK,YAAcA,EAAMA,EAAM,EAAI,EAEvC,IAAI2N,EAAI,KAAK,OACRA,IACDA,EAAI,KAAK,OAAS,KAAK,SAAS,KAAK,SACrCA,EAAE,WAAU,GAEhB,IAAIC,EAAOtN,EAAM,KAAK,SAAS,OAC/B,KAAOqN,EAAE,IAAMC,GACX,GAAI,CAACD,EAAE,OAAQ,EACX,MAAO,GACf,OAAS,CACL,GAAIA,EAAE,MAAQC,EACV,OAAO,KAAK,SAAS,MAAQF,EACjC,GAAI,CAACC,EAAE,WAAWC,CAAI,EAClB,MAAO,GAElB,CACD,QAAQ7N,EAAM,CACV,IAAI0H,EAAO,KAAK,OAAO,KACvB,OAAOA,GAAQA,EAAK,KAAKtH,EAAS,WAAW,GAAKJ,CACrD,CACD,UAAU6B,EAAI,CACV,IAAIiM,EAAM,KAAK,OAAQrJ,EAAM,KAAK,SAAS,OAAQsJ,EAAU,KAAK,aAAe,KAAK,SAAS,QAAU,EAAI,GACzGhN,EAAQc,EAAG,kBAAmB5B,EAAMc,EAAOiN,EAASnM,EAAG,MAAM,SAAS,OACtEoM,EAAUhO,EAAKiO,EAAQF,EAC3B,OAAS,CACL,GAAIF,EAAI,GAAKrJ,EAAMsJ,EAAS,CACxB,GAAID,EAAI,KAAK,aAAeA,EAAI,WAAY,EACxC,SACJ,MAoBJ,GAlBAjM,EAAG,WAAW,IAAIiM,EAAI,IAAI,EAC1BjM,EAAG,QAAQiM,EAAI,KAAMA,EAAI,KAAOrJ,CAAG,EAK/BqJ,EAAI,KAAK,GAAG,OAAO,IACfJ,GAAQ,QAAQI,EAAI,KAAK,EAAE,EAAI,GAC/B7N,EAAM6N,EAAI,GAAKrJ,EACfuJ,EAASnM,EAAG,MAAM,SAAS,SAG3B5B,EAAMgO,EACND,EAASE,EACTD,EAAUH,EAAI,GAAKrJ,EACnByJ,EAAQrM,EAAG,MAAM,SAAS,SAG9B,CAACiM,EAAI,YAAa,EAClB,MAER,KAAOjM,EAAG,MAAM,SAAS,OAASmM,GAC9BnM,EAAG,MAAM,SAAS,MAClBA,EAAG,MAAM,UAAU,MAEvB,OAAO5B,EAAMc,CAChB,CACL,CACA,MAAMoN,GAAuBpE,GAAU,CACnC,iBAAkBqE,EAAK,MACvB,eAAgBA,EAAK,iBACrB,qCAAsCA,EAAK,SAC3C,qCAAsCA,EAAK,SAC3C,kBAAmBA,EAAK,SACxB,kBAAmBA,EAAK,SACxB,kBAAmBA,EAAK,SACxB,kBAAmBA,EAAK,SACxB,uBAAwBA,EAAK,QAC7B,OAAQA,EAAK,OACb,OAAQA,EAAK,UACb,eAAgBA,EAAK,SACrB,qBAAsBA,EAAK,OAC3B,qBAAsBA,EAAK,KAC3B,iCAAkCA,EAAK,KACvC,iBAAkBA,EAAK,MACvB,sBAAuBA,EAAK,UAC5B,IAAKA,EAAK,IACV,yEAA0EA,EAAK,sBAC/E,qBAAsBA,EAAK,UAC3B,UAAWA,EAAK,OAChB,UAAWA,EAAK,OACpB,CAAC,EAEKjI,GAAS,IAAIkC,EAAe,IAAIyB,GAAQT,EAAS,EAAE,OAAO8E,EAAoB,EAAG,OAAO,KAAKvK,CAAmB,EAAE,IAAIyK,GAAKzK,EAAoByK,CAAC,CAAC,EAAG,OAAO,KAAKzK,CAAmB,EAAE,IAAIyK,GAAKxI,GAAkBwI,CAAC,CAAC,EAAG,OAAO,KAAKzK,CAAmB,EAAGmC,GAAgB5D,GAAmB,OAAO,KAAKsJ,CAAa,EAAE,IAAI4C,GAAK5C,EAAc4C,CAAC,CAAC,EAAG,OAAO,KAAK5C,CAAa,EAAG,CAAA,CAAE,EAErX,SAAS6C,GAAczJ,EAAM9E,EAAMsB,EAAI,CACnC,IAAIiF,EAAS,CAAA,EACb,QAAS,EAAIzB,EAAK,WAAYtE,EAAMR,GAAO,EAAI,EAAE,YAAa,CAC1D,IAAIwO,EAAU,EAAI,EAAE,KAAOlN,EAG3B,GAFIkN,EAAUhO,GACV+F,EAAO,KAAK,CAAE,KAAM/F,EAAK,GAAIgO,CAAO,CAAE,EACtC,CAAC,EACD,MACJhO,EAAM,EAAE,GAEZ,OAAO+F,CACX,CAGA,SAASkI,GAAUtF,EAAQ,CACvB,GAAI,CAAE,WAAAuF,EAAY,WAAAC,CAAY,EAAGxF,EAmBjC,MAAO,CAAE,KAlBEyF,GAAW,CAAC9J,EAAMuB,IAAU,CACnC,IAAIuD,EAAK9E,EAAK,KAAK,GACnB,GAAI4J,IAAe9E,GAAM9I,EAAK,WAAa8I,GAAM9I,EAAK,YAAa,CAC/D,IAAI+N,EAAO,GACX,GAAIjF,GAAM9I,EAAK,WAAY,CACvB,IAAIgO,EAAWhK,EAAK,KAAK,SAAShE,EAAK,QAAQ,EAC3CgO,IACAD,EAAOxI,EAAM,KAAKyI,EAAS,KAAMA,EAAS,EAAE,GAEpD,IAAI1I,EAASsI,EAAWG,CAAI,EAC5B,GAAIzI,EACA,MAAO,CAAE,OAAAA,EAAQ,QAAStB,GAAQA,EAAK,KAAK,IAAMhE,EAAK,kBAEtD6N,IAAe/E,GAAM9I,EAAK,WAAa8I,GAAM9I,EAAK,SACvD,MAAO,CAAE,OAAQ6N,EAAY,QAASJ,GAAczJ,EAAK,KAAMA,EAAK,KAAMA,EAAK,EAAE,CAAC,EAEtF,OAAO,IACf,CAAK,CACY,CACjB,CAEA,MAAMiK,GAAqB,CAAE,QAAS,gBAAiB,KAAM,mBAAmB,EAI1EC,GAAgB,CAClB,YAAa,CAAC,CACN,KAAM,gBACN,MAAO,CAAE,oBAAqBX,EAAK,aAAe,CAC9D,EAAW,CACC,KAAM,oBACN,MAAOA,EAAK,qBACxB,CAAS,EACL,YAAa,CAAC,CACN,KAAM,gBACN,MAAMvM,EAAIiB,EAAMvC,EAAK,CACjB,GAAIuC,GAAQ,KAAiBjB,EAAG,KAAKtB,EAAM,CAAC,GAAK,KAAOsB,EAAG,KAAKtB,EAAM,CAAC,GAAK,IACxE,MAAO,GACX,IAAIwL,EAASlK,EAAG,MAAMtB,EAAM,EAAGA,CAAG,EAAGoE,EAAQ9C,EAAG,MAAMtB,EAAM,EAAGA,EAAM,CAAC,EAClE2L,EAAU,QAAQ,KAAKH,CAAM,EAAGI,EAAS,QAAQ,KAAKxH,CAAK,EAC3DqH,EAAUR,EAAY,KAAKO,CAAM,EAAGE,EAAST,EAAY,KAAK7G,CAAK,EACvE,OAAO9C,EAAG,aAAaiN,GAAoBvO,EAAKA,EAAM,EAAG,CAAC4L,IAAW,CAACF,GAAUC,GAAWF,GAAU,CAACE,IAAY,CAACF,GAAWG,GAAUF,EAAO,CAClJ,EACD,MAAO,UACnB,CAAS,CACT,EACA,SAAS+C,EAASnN,EAAIC,EAAMmN,EAAS,EAAGnE,EAAM/C,EAAS,EAAG,CACtD,IAAIpF,EAAQ,EAAG0B,EAAQ,GAAM6K,EAAY,GAAIC,EAAU,GAAIC,EAAM,GAC7DC,EAAY,IAAM,CAClBvE,EAAK,KAAKjJ,EAAG,IAAI,YAAakG,EAASmH,EAAWnH,EAASoH,EAAStN,EAAG,OAAO,YAAYC,EAAK,MAAMoN,EAAWC,CAAO,EAAGpH,EAASmH,CAAS,CAAC,CAAC,CACtJ,EACI,QAAS1N,EAAIyN,EAAQzN,EAAIM,EAAK,OAAQN,IAAK,CACvC,IAAIsB,EAAOhB,EAAK,WAAWN,CAAC,EACxBsB,GAAQ,KAAiB,CAACsM,IACtB,CAAC/K,GAAS6K,EAAY,KACtBvM,IACJ0B,EAAQ,GACJyG,IACIoE,EAAY,IACZG,IACJvE,EAAK,KAAKjJ,EAAG,IAAI,iBAAkBL,EAAIuG,EAAQvG,EAAIuG,EAAS,CAAC,CAAC,GAElEmH,EAAYC,EAAU,KAEjBC,GAAOtM,GAAQ,IAAMA,GAAQ,KAC9BoM,EAAY,IACZA,EAAY1N,GAChB2N,EAAU3N,EAAI,GAElB4N,EAAM,CAACA,GAAOtM,GAAQ,GAE1B,OAAIoM,EAAY,KACZvM,IACImI,GACAuE,KAED1M,CACX,CACA,SAAS2M,GAAQC,EAAKxO,EAAO,CACzB,QAASS,EAAIT,EAAOS,EAAI+N,EAAI,OAAQ/N,IAAK,CACrC,IAAIsB,EAAOyM,EAAI,WAAW/N,CAAC,EAC3B,GAAIsB,GAAQ,IACR,MAAO,GACPA,GAAQ,IACRtB,IAER,MAAO,EACX,CACA,MAAMgO,GAAgB,wCACtB,MAAMC,EAAY,CACd,aAAc,CAIV,KAAK,KAAO,IACf,CACD,SAAS5N,EAAIC,EAAMmD,EAAM,CACrB,GAAI,KAAK,MAAQ,KAAM,CACnB,KAAK,KAAO,GACZ,IAAIyK,EACJ,IAAK5N,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,MACpD0N,GAAc,KAAKE,EAAW5N,EAAK,KAAK,MAAMA,EAAK,GAAG,CAAC,EAAG,CAC1D,IAAI6N,EAAW,CAAA,EAAiBX,EAASnN,EAAIoD,EAAK,QAAS,EAAG0K,EAAU1K,EAAK,KAAK,GAChE+J,EAASnN,EAAI6N,EAAU5N,EAAK,GAAG,IAC7C,KAAK,KAAO,CAACD,EAAG,IAAI,cAAeoD,EAAK,MAAOA,EAAK,MAAQA,EAAK,QAAQ,OAAQ0K,CAAQ,EACrF9N,EAAG,IAAI,iBAAkBA,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,KAAK,MAAM,CAAC,YAGzF,KAAK,KAAM,CAChB,IAAId,EAAU,CAAA,EACdgO,EAASnN,EAAIC,EAAK,KAAMA,EAAK,IAAKd,EAASa,EAAG,SAAS,EACvD,KAAK,KAAK,KAAKA,EAAG,IAAI,WAAYA,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,KAAK,OAAQd,CAAO,CAAC,EAExG,MAAO,EACV,CACD,OAAOa,EAAIoD,EAAM,CACb,OAAK,KAAK,MAEVpD,EAAG,eAAeoD,EAAMpD,EAAG,IAAI,QAASoD,EAAK,MAAOA,EAAK,MAAQA,EAAK,QAAQ,OAAQ,KAAK,IAAI,CAAC,EACzF,IAFI,EAGd,CACL,CAUA,MAAM2K,GAAQ,CACV,YAAa,CACT,CAAE,KAAM,QAAS,MAAO,EAAM,EAC9B,CAAE,KAAM,cAAe,MAAO,CAAE,kBAAmBxB,EAAK,QAAW,EACnE,WACA,CAAE,KAAM,YAAa,MAAOA,EAAK,OAAS,EAC1C,CAAE,KAAM,iBAAkB,MAAOA,EAAK,qBAAuB,CAChE,EACD,WAAY,CAAC,CACL,KAAM,QACN,KAAKtI,EAAGb,EAAM,CAAE,OAAOqK,GAAQrK,EAAK,QAAS,CAAC,EAAI,IAAIwK,GAAc,IAAO,EAC3E,QAAQ5N,EAAIC,EAAMmD,EAAM,CACpB,GAAIA,EAAK,QAAQ,KAAKe,GAAKA,aAAayJ,EAAW,GAAK,CAACH,GAAQxN,EAAK,KAAMA,EAAK,OAAO,EACpF,MAAO,GACX,IAAIgB,EAAOjB,EAAG,SAASA,EAAG,gBAAkB,CAAC,EAAE,KAC/C,OAAO2N,GAAc,KAAK1M,CAAI,GAAKkM,EAASnN,EAAIC,EAAK,KAAMA,EAAK,OAAO,GAAKkN,EAASnN,EAAIiB,EAAMhB,EAAK,OAAO,CAC9G,EACD,OAAQ,eACpB,CAAS,CACT,EACA,MAAM+N,EAAW,CACb,UAAW,CAAE,MAAO,EAAQ,CAC5B,OAAOhO,EAAIoD,EAAM,CACb,OAAApD,EAAG,eAAeoD,EAAMpD,EAAG,IAAI,OAAQoD,EAAK,MAAOA,EAAK,MAAQA,EAAK,QAAQ,OAAQ,CACjFpD,EAAG,IAAI,aAAcoD,EAAK,MAAOA,EAAK,MAAQ,CAAC,EAC/C,GAAGpD,EAAG,OAAO,YAAYoD,EAAK,QAAQ,MAAM,CAAC,EAAGA,EAAK,MAAQ,CAAC,CACjE,CAAA,CAAC,EACK,EACV,CACL,CAKA,MAAM6K,GAAW,CACb,YAAa,CACT,CAAE,KAAM,OAAQ,MAAO,GAAM,MAAO1B,EAAK,IAAM,EAC/C,CAAE,KAAM,aAAc,MAAOA,EAAK,IAAM,CAC3C,EACD,WAAY,CAAC,CACL,KAAM,WACN,KAAKvM,EAAIoD,EAAM,CACX,MAAO,aAAa,KAAKA,EAAK,OAAO,GAAKpD,EAAG,WAAU,EAAG,MAAQ,WAAa,IAAIgO,GAAa,IACnG,EACD,MAAO,eACnB,CAAS,CACT,EAGME,GAAM,CAACH,GAAOE,GAAUf,EAAa,EAC3C,SAASiB,GAAc1N,EAAIuC,EAAM2B,EAAM,CACnC,MAAO,CAAC3E,EAAIiB,EAAMvC,IAAQ,CACtB,GAAIuC,GAAQR,GAAMT,EAAG,KAAKtB,EAAM,CAAC,GAAK+B,EAClC,MAAO,GACX,IAAIwI,EAAO,CAACjJ,EAAG,IAAI2E,EAAMjG,EAAKA,EAAM,CAAC,CAAC,EACtC,QAASiB,EAAIjB,EAAM,EAAGiB,EAAIK,EAAG,IAAKL,IAAK,CACnC,IAAIsB,EAAOjB,EAAG,KAAKL,CAAC,EACpB,GAAIsB,GAAQR,EACR,OAAOT,EAAG,WAAWA,EAAG,IAAIgD,EAAMtE,EAAKiB,EAAI,EAAGsJ,EAAK,OAAOjJ,EAAG,IAAI2E,EAAMhF,EAAGA,EAAI,CAAC,CAAC,CAAC,CAAC,EAGtF,GAFIsB,GAAQ,IACRgI,EAAK,KAAKjJ,EAAG,IAAI,SAAUL,EAAGA,IAAM,CAAC,CAAC,EACtCY,EAAMU,CAAI,EACV,MAER,MAAO,EACf,CACA,CAIA,MAAMmN,GAAc,CAChB,YAAa,CACT,CAAE,KAAM,cAAe,MAAO7B,EAAK,QAAQA,EAAK,OAAO,CAAG,EAC1D,CAAE,KAAM,kBAAmB,MAAOA,EAAK,qBAAuB,CACjE,EACD,YAAa,CAAC,CACN,KAAM,cACN,MAAO4B,GAAc,GAAc,cAAe,iBAAiB,CAC/E,CAAS,CACT,EAIME,GAAY,CACd,YAAa,CACT,CAAE,KAAM,YAAa,MAAO9B,EAAK,QAAQA,EAAK,OAAO,CAAG,EACxD,CAAE,KAAM,gBAAiB,MAAOA,EAAK,qBAAuB,CAC/D,EACD,YAAa,CAAC,CACN,KAAM,YACN,MAAO4B,GAAc,IAAe,YAAa,eAAe,CAC5E,CAAS,CACT,EAGMG,GAAQ,CACV,YAAa,CAAC,CAAE,KAAM,QAAS,MAAO/B,EAAK,UAAW,EACtD,YAAa,CAAC,CACN,KAAM,QACN,MAAMvM,EAAIiB,EAAMvC,EAAK,CACjB,IAAI6P,EACJ,OAAItN,GAAQ,IAAgB,EAAEsN,EAAQ,kBAAkB,KAAKvO,EAAG,MAAMtB,EAAM,EAAGsB,EAAG,GAAG,CAAC,GAC3E,GACJA,EAAG,WAAWA,EAAG,IAAI,QAAStB,EAAKA,EAAM,EAAI6P,EAAM,CAAC,EAAE,MAAM,CAAC,CACvE,CACb,CAAS,CACT,EC/6DMC,GAAoBC,GAAoB,CAAE,MAAO,CAAE,KAAM,OAAQ,MAAO,KAAO,CAAA,CAAE,EACjFC,GAA2B,IAAInQ,EAC/BoQ,GAA0BrK,GAAO,UAAU,CAC7C,MAAO,CACUsK,GAAa,IAAI5Q,GACnB,CAACA,EAAK,GAAG,OAAO,GAAKA,EAAK,GAAG,UAAU,GAAK6Q,EAAU7Q,CAAI,GAAK,KAAO,OACvE,CAAC6H,EAAMiJ,KAAW,CAAE,KAAMA,EAAM,IAAI,OAAOjJ,EAAK,IAAI,EAAE,GAAI,GAAIA,EAAK,EAAI,EAChF,EACY6I,GAAY,IAAIG,CAAS,EACzBE,GAAe,IAAI,CAC5B,SAAU,IAAM,IAC5B,CAAS,EACYC,GAAiB,IAAI,CAC9B,SAAUR,EACtB,CAAS,CACJ,CACL,CAAC,EACD,SAASK,EAAU7Q,EAAM,CACrB,IAAIuQ,EAAQ,8BAA8B,KAAKvQ,EAAK,IAAI,EACxD,OAAOuQ,EAAQ,CAACA,EAAM,CAAC,EAAI,MAC/B,CACA,SAASU,GAAeC,EAAYC,EAAO,CACvC,IAAItQ,EAAOqQ,EACX,OAAS,CACL,IAAIjO,EAAOpC,EAAK,YAAauQ,EAC7B,GAAI,CAACnO,IAASmO,EAAUP,EAAU5N,EAAK,IAAI,IAAM,MAAQmO,GAAWD,EAChE,MACJtQ,EAAOoC,EAEX,OAAOpC,EAAK,EAChB,CACA,MAAMwQ,GAA4BC,GAAY,GAAG,CAACR,EAAO5P,EAAOd,IAAQ,CACpE,QAAS4E,EAAOuM,EAAWT,CAAK,EAAE,aAAa1Q,EAAK,EAAE,EAAG4E,GACjD,EAAAA,EAAK,KAAO9D,GAD2C8D,EAAOA,EAAK,OAAQ,CAG/E,IAAIoM,EAAUpM,EAAK,KAAK,KAAK0L,EAAW,EACxC,GAAIU,GAAW,KACX,SACJ,IAAI9I,EAAO2I,GAAejM,EAAMoM,CAAO,EACvC,GAAI9I,EAAOlI,EACP,MAAO,CAAE,KAAMA,EAAK,GAAIkI,CAAI,EAEpC,OAAO,IACX,CAAC,EACD,SAASkJ,GAAOlL,EAAQ,CACpB,OAAO,IAAImL,GAASjB,GAAMlK,EAAQ,CAAC+K,EAAY,EAAG,UAAU,CAChE,CAIK,MAACK,GAAkCF,GAAOb,EAAU,EACnDgB,GAAwBhB,GAAW,UAAU,CAACT,GAAKG,GAAWD,GAAaE,EAAK,CAAC,EAKjFsB,GAAgCJ,GAAOG,EAAQ,EACrD,SAASE,GAAcC,EAAWC,EAAiB,CAC/C,OAAQhD,GAAS,CACb,GAAIA,GAAQ+C,EAAW,CACnB,IAAI1H,EAAQ,KAOZ,GALA2E,EAAO,MAAM,KAAKA,CAAI,EAAE,CAAC,EACrB,OAAO+C,GAAa,WACpB1H,EAAQ0H,EAAU/C,CAAI,EAEtB3E,EAAQ4H,GAAoB,kBAAkBF,EAAW/C,EAAM,EAAI,EACnE3E,aAAiB4H,GACjB,OAAO5H,EAAM,QAAUA,EAAM,QAAQ,SAAS,OAAS6H,GAAa,kBAAkB7H,EAAM,KAAM,CAAA,EACjG,GAAIA,EACL,OAAOA,EAAM,OAErB,OAAO2H,EAAkBA,EAAgB,OAAS,IAC1D,CACA,CAEA,MAAMG,CAAQ,CACV,YAAYlN,EAAM9E,EAAMsB,EAAI2Q,EAAaC,EAAYpS,EAAMqS,EAAM,CAC7D,KAAK,KAAOrN,EACZ,KAAK,KAAO9E,EACZ,KAAK,GAAKsB,EACV,KAAK,YAAc2Q,EACnB,KAAK,WAAaC,EAClB,KAAK,KAAOpS,EACZ,KAAK,KAAOqS,CACf,CACD,MAAMC,EAAUrN,EAAW,GAAM,CAC7B,IAAIpD,EAAS,KAAK,aAAe,KAAK,KAAK,MAAQ,aAAe,IAAM,IACxE,GAAIyQ,GAAY,KAAM,CAClB,KAAOzQ,EAAO,OAASyQ,GACnBzQ,GAAU,IACd,OAAOA,MAEN,CACD,QAASF,EAAI,KAAK,GAAK,KAAK,KAAOE,EAAO,OAAS,KAAK,WAAW,OAAQF,EAAI,EAAGA,IAC9EE,GAAU,IACd,OAAOA,GAAUoD,EAAW,KAAK,WAAa,IAErD,CACD,OAAOsN,EAAKC,EAAK,CACb,IAAIC,EAAS,KAAK,KAAK,MAAQ,cAAgB,OAAQ,CAACC,GAAW,KAAK,KAAMH,CAAG,EAAE,CAAC,EAAIC,CAAK,EAAG,GAChG,OAAO,KAAK,YAAcC,EAAS,KAAK,KAAO,KAAK,UACvD,CACL,CACA,SAASE,GAAW3N,EAAMuN,EAAK,CAC3B,IAAIK,EAAQ,CAAA,EACZ,QAAS3E,EAAMjJ,EAAMiJ,GAAOA,EAAI,MAAQ,WAAYA,EAAMA,EAAI,QACtDA,EAAI,MAAQ,YAAcA,EAAI,MAAQ,cAAgBA,EAAI,MAAQ,eAClE2E,EAAM,KAAK3E,CAAG,EAEtB,IAAI4E,EAAU,CAAA,EACd,QAASlR,EAAIiR,EAAM,OAAS,EAAGjR,GAAK,EAAGA,IAAK,CACxC,IAAIqD,EAAO4N,EAAMjR,CAAC,EAAG4O,EACjBtO,EAAOsQ,EAAI,OAAOvN,EAAK,IAAI,EAAG+H,EAAW/H,EAAK,KAAO/C,EAAK,KAC9D,GAAI+C,EAAK,MAAQ,aACb6N,EAAQ,KAAK,IAAIX,EAAQlN,EAAM+H,EAAUA,EAAU,GAAI,GAAI,GAAI,IAAI,CAAC,UAE/D/H,EAAK,MAAQ,eAAiBuL,EAAQ,eAAe,KAAKtO,EAAK,KAAK,MAAM8K,CAAQ,CAAC,GACxF8F,EAAQ,KAAK,IAAIX,EAAQlN,EAAM+H,EAAUA,EAAWwD,EAAM,CAAC,EAAE,OAAQ,GAAIA,EAAM,CAAC,EAAG,IAAK,IAAI,CAAC,UAExFvL,EAAK,MAAQ,YAAcA,EAAK,OAAO,MAAQ,gBACnDuL,EAAQ,6BAA6B,KAAKtO,EAAK,KAAK,MAAM8K,CAAQ,CAAC,GAAI,CACxE,IAAIjI,EAAQyL,EAAM,CAAC,EAAGlM,EAAMkM,EAAM,CAAC,EAAE,OACjCzL,EAAM,QAAU,IAChBA,EAAQA,EAAM,MAAM,EAAGA,EAAM,OAAS,CAAC,EACvCT,GAAO,GAEXwO,EAAQ,KAAK,IAAIX,EAAQlN,EAAK,OAAQ+H,EAAUA,EAAW1I,EAAKkM,EAAM,CAAC,EAAGzL,EAAOyL,EAAM,CAAC,EAAGvL,CAAI,CAAC,UAE3FA,EAAK,MAAQ,YAAcA,EAAK,OAAO,MAAQ,eACnDuL,EAAQ,iDAAiD,KAAKtO,EAAK,KAAK,MAAM8K,CAAQ,CAAC,GAAI,CAC5F,IAAIjI,EAAQyL,EAAM,CAAC,EAAGlM,EAAMkM,EAAM,CAAC,EAAE,OACjCzL,EAAM,OAAS,IACfA,EAAQA,EAAM,MAAM,EAAGA,EAAM,OAAS,CAAC,EACvCT,GAAO,GAEX,IAAIrE,EAAOuQ,EAAM,CAAC,EACdA,EAAM,CAAC,IACPvQ,GAAQuQ,EAAM,CAAC,EAAE,QAAQ,OAAQ,GAAG,GACxCsC,EAAQ,KAAK,IAAIX,EAAQlN,EAAK,OAAQ+H,EAAUA,EAAW1I,EAAKkM,EAAM,CAAC,EAAGzL,EAAO9E,EAAMgF,CAAI,CAAC,GAGpG,OAAO6N,CACX,CACA,SAASH,GAAWL,EAAME,EAAK,CAC3B,MAAO,sBAAsB,KAAKA,EAAI,YAAYF,EAAK,KAAMA,EAAK,KAAO,EAAE,CAAC,CAChF,CACA,SAASS,EAAahO,EAAOyN,EAAKQ,EAAS7K,EAAS,EAAG,CACnD,QAAS8K,EAAO,GAAIhO,EAAOF,IAAS,CAChC,GAAIE,EAAK,MAAQ,WAAY,CACzB,IAAId,EAAIwO,GAAW1N,EAAMuN,CAAG,EACxBE,EAAS,CAACvO,EAAE,CAAC,EACjB,GAAI8O,GAAQ,EAAG,CACX,GAAIP,GAAUO,EAAO,EACjB,OACJD,EAAQ,KAAK,CAAE,KAAM/N,EAAK,KAAOd,EAAE,CAAC,EAAE,OAAQ,GAAIc,EAAK,KAAOd,EAAE,CAAC,EAAE,OAAQ,OAAQ,OAAO8O,EAAO,EAAI9K,CAAM,CAAC,CAAE,EAElH8K,EAAOP,EAEX,IAAIxP,EAAO+B,EAAK,YAChB,GAAI,CAAC/B,EACD,MACJ+B,EAAO/B,EAEf,CAYK,MAACgQ,GAA8B,CAAC,CAAE,MAAAnC,EAAO,SAAAoC,KAAe,CACzD,IAAIrL,EAAO0J,EAAWT,CAAK,EAAG,CAAE,IAAAyB,CAAK,EAAGzB,EACpC3I,EAAO,KAAM4K,EAAUjC,EAAM,cAAcqC,GAAS,CACpD,GAAI,CAACA,EAAM,OAAS,CAACvB,GAAiB,WAAWd,EAAOqC,EAAM,IAAI,EAC9D,OAAOhL,EAAO,CAAE,MAAAgL,GACpB,IAAIzS,EAAMyS,EAAM,KAAMlR,EAAOsQ,EAAI,OAAO7R,CAAG,EACvCmS,EAAUF,GAAW9K,EAAK,aAAanH,EAAK,EAAE,EAAG6R,CAAG,EACxD,KAAOM,EAAQ,QAAUA,EAAQA,EAAQ,OAAS,CAAC,EAAE,KAAOnS,EAAMuB,EAAK,MACnE4Q,EAAQ,IAAG,EACf,GAAI,CAACA,EAAQ,OACT,OAAO1K,EAAO,CAAE,MAAAgL,GACpB,IAAIpI,EAAQ8H,EAAQA,EAAQ,OAAS,CAAC,EACtC,GAAI9H,EAAM,GAAKA,EAAM,WAAW,OAASrK,EAAMuB,EAAK,KAChD,OAAOkG,EAAO,CAAE,MAAAgL,GACpB,IAAIC,EAAY1S,GAAQqK,EAAM,GAAKA,EAAM,WAAW,QAAW,CAAC,KAAK,KAAK9I,EAAK,KAAK,MAAM8I,EAAM,EAAE,CAAC,EAEnG,GAAIA,EAAM,MAAQqI,EAEd,GAAIrI,EAAM,KAAK,WAAW,IAAMrK,GAC5BuB,EAAK,KAAO,GAAK,CAAC,SAAS,KAAKsQ,EAAI,OAAOtQ,EAAK,KAAO,CAAC,EAAE,IAAI,EAAG,CACjE,IAAIgB,EAAO4P,EAAQ,OAAS,EAAIA,EAAQA,EAAQ,OAAS,CAAC,EAAI,KAC1DQ,EAAOC,EAAS,GAChBrQ,GAAQA,EAAK,MACboQ,EAAQpR,EAAK,KAAOgB,EAAK,KACzBqQ,EAASrQ,EAAK,OAAOsP,EAAK,CAAC,GAG3Bc,EAAQpR,EAAK,MAAQgB,EAAOA,EAAK,GAAK,GAE1C,IAAI8P,EAAU,CAAC,CAAE,KAAMM,EAAO,GAAI3S,EAAK,OAAA4S,CAAM,CAAE,EAC/C,OAAIvI,EAAM,KAAK,MAAQ,eACnB+H,EAAa/H,EAAM,KAAMwH,EAAKQ,EAAS,EAAE,EACzC9P,GAAQA,EAAK,KAAK,MAAQ,eAC1B6P,EAAa7P,EAAK,KAAMsP,EAAKQ,CAAO,EACjC,CAAE,MAAOQ,EAAgB,OAAOF,EAAQC,EAAO,MAAM,EAAG,QAAAP,OAE9D,CACD,IAAIO,EAAS,GACb,QAAS3R,EAAI,EAAG8B,EAAIoP,EAAQ,OAAS,EAAGlR,GAAK8B,EAAG9B,IAC5C2R,GAAUT,EAAQlR,CAAC,EAAE,MAAMA,EAAI8B,EAAIoP,EAAQlR,EAAI,CAAC,EAAE,KAAO2R,EAAO,OAAS,KAAM3R,EAAI8B,CAAC,EAExF,OAAA6P,GAAUxC,EAAM,UACT,CAAE,MAAOyC,EAAgB,OAAO7S,EAAM4S,EAAO,MAAM,EAAG,QAAS,CAAE,KAAMrR,EAAK,KAAM,OAAAqR,CAAQ,CAAA,EAGzG,GAAIvI,EAAM,KAAK,MAAQ,cAAgBqI,GAAanR,EAAK,KAAM,CAC3D,IAAIuR,EAAWjB,EAAI,OAAOtQ,EAAK,KAAO,CAAC,EAAGwR,EAAS,QAAQ,KAAKD,EAAS,IAAI,EAE7E,GAAIC,GAAUA,EAAO,OAAS1I,EAAM,KAAM,CACtC,IAAIgI,EAAUjC,EAAM,QAAQ,CAAC,CAAE,KAAM0C,EAAS,KAAOC,EAAO,MAAO,GAAID,EAAS,EAAI,EAChF,CAAE,KAAMvR,EAAK,KAAO8I,EAAM,KAAM,GAAI9I,EAAK,EAAI,CAAA,CAAC,EAClD,MAAO,CAAE,MAAOkR,EAAM,IAAIJ,CAAO,EAAG,QAAAA,IAG5C,IAAIA,EAAU,CAAA,EACVhI,EAAM,KAAK,MAAQ,eACnB+H,EAAa/H,EAAM,KAAMwH,EAAKQ,CAAO,EACzC,IAAIW,EAAY3I,EAAM,MAAQA,EAAM,KAAK,KAAO9I,EAAK,KACjDqR,EAAS,GAEb,GAAI,CAACI,GAAa,kBAAkB,KAAKzR,EAAK,IAAI,EAAE,CAAC,EAAE,QAAU8I,EAAM,GACnE,QAASpJ,EAAI,EAAG8B,EAAIoP,EAAQ,OAAS,EAAGlR,GAAK8B,EAAG9B,IAC5C2R,GAAU3R,GAAK8B,GAAK,CAACiQ,EAAYb,EAAQlR,CAAC,EAAE,OAAO4Q,EAAK,CAAC,EACnDM,EAAQlR,CAAC,EAAE,MAAMA,EAAI8B,EAAIoP,EAAQlR,EAAI,CAAC,EAAE,KAAO2R,EAAO,OAAS,IAAI,EAGjF,IAAIpT,EAAOQ,EACX,KAAOR,EAAO+B,EAAK,MAAQ,KAAK,KAAKA,EAAK,KAAK,OAAO/B,EAAO+B,EAAK,KAAO,CAAC,CAAC,GACvE/B,IACJ,OAAAoT,EAASxC,EAAM,UAAYwC,EAC3BP,EAAQ,KAAK,CAAE,KAAA7S,EAAM,GAAIQ,EAAK,OAAA4S,CAAM,CAAE,EAC/B,CAAE,MAAOC,EAAgB,OAAOrT,EAAOoT,EAAO,MAAM,EAAG,QAAAP,EACtE,CAAK,EACD,OAAI5K,EACO,IACX+K,EAASpC,EAAM,OAAOiC,EAAS,CAAE,eAAgB,GAAM,UAAW,OAAS,CAAA,CAAC,EACrE,GACX,EACA,SAASY,GAAO3O,EAAM,CAClB,OAAOA,EAAK,MAAQ,aAAeA,EAAK,MAAQ,UACpD,CACA,SAAS4O,GAAqB/L,EAAMnH,EAAK,CACrC,IAAIsE,EAAO6C,EAAK,aAAanH,EAAK,EAAE,EAAGmT,EAAOnT,EAC1CiT,GAAO3O,CAAI,IACX6O,EAAO7O,EAAK,KACZA,EAAOA,EAAK,QAEhB,QAASgO,EAAMA,EAAOhO,EAAK,YAAY6O,CAAI,GACvC,GAAIF,GAAOX,CAAI,EACXa,EAAOb,EAAK,aAEPA,EAAK,MAAQ,eAAiBA,EAAK,MAAQ,aAChDhO,EAAOgO,EAAK,UACZa,EAAO7O,EAAK,OAGZ,OAGR,OAAOA,CACX,CAYK,MAAC8O,GAAuB,CAAC,CAAE,MAAAhD,EAAO,SAAAoC,KAAe,CAClD,IAAIrL,EAAO0J,EAAWT,CAAK,EACvB3I,EAAO,KAAM4K,EAAUjC,EAAM,cAAcqC,GAAS,CACpD,IAAIzS,EAAMyS,EAAM,KAAM,CAAE,IAAAZ,CAAG,EAAKzB,EAChC,GAAIqC,EAAM,OAASvB,GAAiB,WAAWd,EAAOqC,EAAM,IAAI,EAAG,CAC/D,IAAIlR,EAAOsQ,EAAI,OAAO7R,CAAG,EACrBmS,EAAUF,GAAWiB,GAAqB/L,EAAMnH,CAAG,EAAG6R,CAAG,EAC7D,GAAIM,EAAQ,OAAQ,CAChB,IAAI9H,EAAQ8H,EAAQA,EAAQ,OAAS,CAAC,EAClCkB,EAAWhJ,EAAM,GAAKA,EAAM,WAAW,QAAUA,EAAM,WAAa,EAAI,GAE5E,GAAIrK,EAAMuB,EAAK,KAAO8R,GAAY,CAAC,KAAK,KAAK9R,EAAK,KAAK,MAAM8R,EAAUrT,EAAMuB,EAAK,IAAI,CAAC,EACnF,MAAO,CAAE,MAAOsR,EAAgB,OAAOtR,EAAK,KAAO8R,CAAQ,EACvD,QAAS,CAAE,KAAM9R,EAAK,KAAO8R,EAAU,GAAIrT,CAAG,GACtD,GAAIA,EAAMuB,EAAK,MAAQ8R,EAAU,CAC7B,IAAI7S,EAAQe,EAAK,KAAO8I,EAAM,KAE9B,GAAIA,EAAM,MAAQA,EAAM,KAAK,KAAOA,EAAM,KAAK,MAAQ,KAAK,KAAK9I,EAAK,KAAK,MAAM8I,EAAM,KAAMA,EAAM,EAAE,CAAC,EAClG,MAAO,CAAE,MAAAoI,EAAO,QAAS,CAAE,KAAMjS,EAAO,GAAIe,EAAK,KAAO8I,EAAM,GAAI,OAAQA,EAAM,MAAMA,EAAM,GAAKA,EAAM,IAAI,CAAC,GAEhH,GAAI7J,EAAQR,EACR,MAAO,CAAE,MAAO6S,EAAgB,OAAOrS,CAAK,EAAG,QAAS,CAAE,KAAMA,EAAO,GAAIR,CAAK,CAAA,IAIhG,OAAOyH,EAAO,CAAE,MAAAgL,EACxB,CAAK,EACD,OAAIhL,EACO,IACX+K,EAASpC,EAAM,OAAOiC,EAAS,CAAE,eAAgB,GAAM,UAAW,QAAU,CAAA,CAAC,EACtE,GACX,EAQMiB,GAAiB,CACnB,CAAE,IAAK,QAAS,IAAKf,EAA6B,EAClD,CAAE,IAAK,YAAa,IAAKa,EAAsB,CACnD,EACMG,GAA2BC,GAAK,CAAE,iBAAkB,EAAO,CAAA,EAIjE,SAASC,GAAS9K,EAAS,GAAI,CAC3B,GAAI,CAAE,cAAA+K,EAAe,oBAAAC,EAAqB,UAAAC,EAAY,GAAM,KAAM,CAAE,OAAAhO,CAAQ,EAAGoL,EAAoB,EAAGrI,EACtG,GAAI,EAAE/C,aAAkBkC,GACpB,MAAM,IAAI,WAAW,gEAAgE,EACzF,IAAI+L,EAAalL,EAAO,WAAa,CAACA,EAAO,UAAU,EAAI,GACvDmL,EAAU,CAACP,GAAY,OAAO,EAAGQ,EACjCJ,aAA+BK,IAC/BF,EAAQ,KAAKH,EAAoB,OAAO,EACxCI,EAAcJ,EAAoB,UAE7BA,IACLI,EAAcJ,GAElB,IAAIzF,EAAawF,GAAiBK,EAAc5C,GAAcuC,EAAeK,CAAW,EAAI,OAC5F,OAAAF,EAAW,KAAK5F,GAAU,CAAE,WAAAC,EAAY,WAAYqF,GAAY,SAAS,MAAQ,CAAA,CAAC,EAC9EK,GACAE,EAAQ,KAAKG,GAAK,KAAKC,GAAO,GAAGZ,EAAc,CAAC,CAAC,EAC9C,IAAIU,GAAgBlD,GAAOlL,EAAO,UAAUiO,CAAU,CAAC,EAAGC,CAAO,CAC5E", "x_google_ignoreList": [0, 1]}