import{S as G,e as H,s as U,F as h,G as o,w as d,u as g,H as w,an as V,Z as W,ae as Z,o as v,h as z,V as E,W as I,k as K}from"./index-7674dbb6.js";import{B as L}from"./Button-770df9ba.js";import{T as M}from"./Table-575bd406.js";import"./index-2f00b72c.js";import"./utils-c3e3db58.js";import"./Upload-59d47275.js";import"./StaticMarkdown-0958b32f.js";import"./dsv-576afacd.js";function P(n){let a,l,i,_;const u=[n[17],{border:!0}];let m={};for(let t=0;t<u.length;t+=1)m=W(m,u[t]);return a=new Z({props:m}),i=new M({props:{label:n[7],row_count:n[6],col_count:n[5],value:n[0],headers:n[1],wrap:n[8],datatype:n[9],latex_delimiters:n[15],editable:!1,height:n[16],line_breaks:n[12],column_widths:n[13]}}),i.$on("select",n[20]),{c(){h(a.$$.fragment),l=v(),h(i.$$.fragment)},m(t,s){o(a,t,s),z(t,l,s),o(i,t,s),_=!0},p(t,s){const r=s&131072?E(u,[I(t[17]),u[1]]):{};a.$set(r);const f={};s&128&&(f.label=t[7]),s&64&&(f.row_count=t[6]),s&32&&(f.col_count=t[5]),s&1&&(f.value=t[0]),s&2&&(f.headers=t[1]),s&256&&(f.wrap=t[8]),s&512&&(f.datatype=t[9]),s&32768&&(f.latex_delimiters=t[15]),s&65536&&(f.height=t[16]),s&4096&&(f.line_breaks=t[12]),s&8192&&(f.column_widths=t[13]),i.$set(f)},i(t){_||(d(a.$$.fragment,t),d(i.$$.fragment,t),_=!0)},o(t){g(a.$$.fragment,t),g(i.$$.fragment,t),_=!1},d(t){t&&K(l),w(a,t),w(i,t)}}}function Q(n){let a,l;return a=new L({props:{visible:n[4],padding:!1,elem_id:n[2],elem_classes:n[3],container:!1,scale:n[10],min_width:n[11],allow_overflow:!1,$$slots:{default:[P]},$$scope:{ctx:n}}}),{c(){h(a.$$.fragment)},m(i,_){o(a,i,_),l=!0},p(i,[_]){const u={};_&16&&(u.visible=i[4]),_&4&&(u.elem_id=i[2]),_&8&&(u.elem_classes=i[3]),_&1024&&(u.scale=i[10]),_&2048&&(u.min_width=i[11]),_&4453347&&(u.$$scope={dirty:_,ctx:i}),a.$set(u)},i(i){l||(d(a.$$.fragment,i),l=!0)},o(i){g(a.$$.fragment,i),l=!1},d(i){w(a,i)}}}function R(n,a,l){let{headers:i=[]}=a,{elem_id:_=""}=a,{elem_classes:u=[]}=a,{visible:m=!0}=a,{value:t={data:[["","",""]],headers:["1","2","3"],metadata:null}}=a,s=JSON.stringify(t),{value_is_output:r=!1}=a,{col_count:f}=a,{row_count:b}=a,{label:k=null}=a,{wrap:S}=a,{datatype:A}=a,{scale:J=null}=a,{min_width:N=void 0}=a,{line_breaks:O=!0}=a,{column_widths:B=[]}=a,{gradio:c}=a,{latex_delimiters:D}=a,{height:T=void 0}=a,{loading_status:j}=a;function q(){c.dispatch("change"),r||c.dispatch("input")}V(()=>{l(18,r=!1)}),(Array.isArray(t)&&t?.[0]?.length===0||t.data?.[0]?.length===0)&&(t={data:[Array(f?.[0]||3).fill("")],headers:Array(f?.[0]||3).fill("").map((e,F)=>`${F+1}`),metadata:null});const C=e=>c.dispatch("select",e.detail);return n.$$set=e=>{"headers"in e&&l(1,i=e.headers),"elem_id"in e&&l(2,_=e.elem_id),"elem_classes"in e&&l(3,u=e.elem_classes),"visible"in e&&l(4,m=e.visible),"value"in e&&l(0,t=e.value),"value_is_output"in e&&l(18,r=e.value_is_output),"col_count"in e&&l(5,f=e.col_count),"row_count"in e&&l(6,b=e.row_count),"label"in e&&l(7,k=e.label),"wrap"in e&&l(8,S=e.wrap),"datatype"in e&&l(9,A=e.datatype),"scale"in e&&l(10,J=e.scale),"min_width"in e&&l(11,N=e.min_width),"line_breaks"in e&&l(12,O=e.line_breaks),"column_widths"in e&&l(13,B=e.column_widths),"gradio"in e&&l(14,c=e.gradio),"latex_delimiters"in e&&l(15,D=e.latex_delimiters),"height"in e&&l(16,T=e.height),"loading_status"in e&&l(17,j=e.loading_status)},n.$$.update=()=>{n.$$.dirty&524289&&JSON.stringify(t)!==s&&(l(19,s=JSON.stringify(t)),q())},[t,i,_,u,m,f,b,k,S,A,J,N,O,B,c,D,T,j,r,s,C]}class X extends G{constructor(a){super(),H(this,a,R,Q,U,{headers:1,elem_id:2,elem_classes:3,visible:4,value:0,value_is_output:18,col_count:5,row_count:6,label:7,wrap:8,datatype:9,scale:10,min_width:11,line_breaks:12,column_widths:13,gradio:14,latex_delimiters:15,height:16,loading_status:17})}}const le=X;export{le as default};
//# sourceMappingURL=index-9f71a59c.js.map
