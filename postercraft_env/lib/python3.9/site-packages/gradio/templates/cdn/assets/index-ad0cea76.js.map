{"version": 3, "file": "index-ad0cea76.js", "sources": ["../../../../js/file/interactive/FileUpload.svelte", "../../../../js/file/interactive/InteractiveFile.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, tick } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\n\timport { FilePreview } from \"../shared\";\n\n\texport let value: null | FileData | FileData[];\n\n\texport let label: string;\n\texport let show_label = true;\n\texport let file_count = \"single\";\n\texport let file_types: string[] | null = null;\n\texport let selectable = false;\n\texport let height: number | undefined = undefined;\n\n\tasync function handle_upload({\n\t\tdetail\n\t}: CustomEvent<FileData | FileData[]>): Promise<void> {\n\t\tvalue = detail;\n\t\tawait tick();\n\t\tdispatch(\"change\", value);\n\t\tdispatch(\"upload\", detail);\n\t}\n\n\tfunction handle_clear({ detail }: CustomEvent<null>): void {\n\t\tvalue = null;\n\t\tdispatch(\"change\", value);\n\t\tdispatch(\"clear\");\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData[] | FileData | null;\n\t\tclear: undefined;\n\t\tdrag: boolean;\n\t\tupload: FileData[] | FileData;\n\t\terror: string;\n\t}>();\n\n\tlet accept_file_types: string | null;\n\tif (file_types == null) {\n\t\taccept_file_types = null;\n\t} else {\n\t\tfile_types = file_types.map((x) => {\n\t\t\tif (x.startsWith(\".\")) {\n\t\t\t\treturn x;\n\t\t\t}\n\t\t\treturn x + \"/*\";\n\t\t});\n\t\taccept_file_types = file_types.join(\", \");\n\t}\n\n\tlet dragging = false;\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={File}\n\tfloat={value === null}\n\tlabel={label || \"File\"}\n/>\n\n{#if value}\n\t<ModifyUpload on:clear={handle_clear} absolute />\n\t<FilePreview on:select {selectable} {value} {height} />\n{:else}\n\t<Upload\n\t\ton:load={handle_upload}\n\t\tfiletype={accept_file_types}\n\t\tparse_to_data_url={false}\n\t\t{file_count}\n\t\tbind:dragging\n\t>\n\t\t<slot />\n\t</Upload>\n{/if}\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { getContext } from \"svelte\";\n\timport FileUpload from \"./FileUpload.svelte\";\n\timport { blobToBase64 } from \"@gradio/upload\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { UploadText } from \"@gradio/atoms\";\n\timport { upload_files as default_upload_files } from \"@gradio/client\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport { _ } from \"svelte-i18n\";\n\timport type { S } from \"@storybook/theming/dist/create-c2b2ce6d\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData | FileData[];\n\tlet old_value: null | FileData | FileData[];\n\n\texport let mode: \"static\" | \"interactive\";\n\texport let root: string;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let file_count: string;\n\texport let file_types: string[] = [\"file\"];\n\texport let root_url: null | string;\n\texport let selectable = false;\n\texport let loading_status: LoadingStatus;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let height: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\terror: string;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t}>;\n\n\tconst upload_files =\n\t\tgetContext<typeof default_upload_files>(\"upload_files\") ??\n\t\tdefault_upload_files;\n\n\t$: _value = normalise_file(value, root, root_url);\n\n\tlet dragging = false;\n\tlet pending_upload = false;\n\n\t$: {\n\t\tif (JSON.stringify(_value) !== JSON.stringify(old_value)) {\n\t\t\told_value = _value;\n\t\t\tif (_value === null) {\n\t\t\t\tgradio.dispatch(\"change\");\n\t\t\t\tpending_upload = false;\n\t\t\t} else if (\n\t\t\t\t!(Array.isArray(_value) ? _value : [_value]).every(\n\t\t\t\t\t(file_data) => file_data.blob\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\tpending_upload = false;\n\t\t\t\tgradio.dispatch(\"change\");\n\t\t\t} else if (mode === \"interactive\") {\n\t\t\t\tlet files = (Array.isArray(_value) ? _value : [_value]).map(\n\t\t\t\t\t(file_data) => file_data.blob!\n\t\t\t\t);\n\t\t\t\tlet upload_value = _value;\n\t\t\t\tpending_upload = true;\n\t\t\t\tupload_files(root, files).then((response) => {\n\t\t\t\t\tif (upload_value !== _value) {\n\t\t\t\t\t\t// value has changed since upload started\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tpending_upload = false;\n\t\t\t\t\tif (response.error) {\n\t\t\t\t\t\t(Array.isArray(_value) ? _value : [_value]).forEach(\n\t\t\t\t\t\t\tasync (file_data, i) => {\n\t\t\t\t\t\t\t\tfile_data.data = await blobToBase64(file_data.blob!);\n\t\t\t\t\t\t\t\tfile_data.blob = undefined;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t(Array.isArray(_value) ? _value : [_value]).forEach(\n\t\t\t\t\t\t\t(file_data, i) => {\n\t\t\t\t\t\t\t\tif (response.files) {\n\t\t\t\t\t\t\t\t\tfile_data.orig_name = file_data.name;\n\t\t\t\t\t\t\t\t\tfile_data.name = response.files[i];\n\t\t\t\t\t\t\t\t\tfile_data.is_file = true;\n\t\t\t\t\t\t\t\t\tfile_data.blob = undefined;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t);\n\t\t\t\t\t\told_value = _value = normalise_file(value, root, root_url);\n\t\t\t\t\t}\n\t\t\t\t\tgradio.dispatch(\"change\");\n\t\t\t\t\tgradio.dispatch(\"upload\");\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\t{height}\n\tallow_overflow={false}\n>\n\t<StatusTracker\n\t\t{...loading_status}\n\t\tstatus={pending_upload\n\t\t\t? \"generating\"\n\t\t\t: loading_status?.status || \"complete\"}\n\t/>\n\n\t<FileUpload\n\t\t{label}\n\t\t{show_label}\n\t\tvalue={_value}\n\t\t{file_count}\n\t\t{file_types}\n\t\t{selectable}\n\t\t{height}\n\t\ton:change={({ detail }) => (value = detail)}\n\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t>\n\t\t<UploadText type=\"file\" />\n\t</FileUpload>\n</Block>\n"], "names": ["ctx", "File", "dirty", "blocklabel_changes", "value", "$$props", "label", "show_label", "file_count", "file_types", "selectable", "height", "handle_upload", "detail", "$$invalidate", "tick", "dispatch", "handle_clear", "createEventDispatcher", "accept_file_types", "x", "dragging", "block_changes", "elem_id", "elem_classes", "visible", "old_value", "mode", "root", "root_url", "loading_status", "container", "scale", "min_width", "gradio", "upload_files", "getContext", "default_upload_files", "pending_upload", "select_handler", "_value", "normalise_file", "file_data", "files", "upload_value", "response", "i", "blobToBase64"], "mappings": "0tBAuEYA,EAAiB,CAAA,oBACR,oKAFVA,EAAa,CAAA,CAAA,oFACZA,EAAiB,CAAA,yRALJA,EAAY,CAAA,CAAA,8sBAN9BC,GACC,MAAAD,OAAU,KACV,MAAAA,MAAS,sDAGZA,EAAK,CAAA,EAAA,2KAJFE,EAAA,IAAAC,EAAA,MAAAH,OAAU,MACVE,EAAA,IAAAC,EAAA,MAAAH,MAAS,kUArDL,MAAAI,CAAmC,EAAAC,GAEnC,MAAAC,CAAa,EAAAD,EACb,CAAA,WAAAE,EAAa,EAAI,EAAAF,EACjB,CAAA,WAAAG,EAAa,QAAQ,EAAAH,EACrB,CAAA,WAAAI,EAA8B,IAAI,EAAAJ,EAClC,CAAA,WAAAK,EAAa,EAAK,EAAAL,EAClB,CAAA,OAAAM,EAA6B,MAAS,EAAAN,EAElC,eAAAO,GACd,OAAAC,GAAM,CAENC,EAAA,EAAAV,EAAQS,CAAM,QACRE,EAAI,EACVC,EAAS,SAAUZ,CAAK,EACxBY,EAAS,SAAUH,CAAM,EAGjB,SAAAI,GAAe,OAAAJ,GAAM,CAC7BC,EAAA,EAAAV,EAAQ,IAAI,EACZY,EAAS,SAAUZ,CAAK,EACxBY,EAAS,OAAO,EAGX,MAAAA,EAAWE,QAQbC,EACAV,GAAc,KACjBU,EAAoB,MAEpBV,EAAaA,EAAW,IAAKW,GACxBA,EAAE,WAAW,GAAG,EACZA,EAEDA,EAAI,MAEZD,EAAoBV,EAAW,KAAK,IAAI,GAGrC,IAAAY,EAAW,+XACZL,EAAS,OAAQK,CAAQ,mZCoEvBrB,EAAc,CAAA,GACV,OAAAA,EAAA,EAAA,EACL,aACAA,EAAc,CAAA,GAAE,QAAU,4IAMtBA,EAAM,EAAA,4TATTA,EAAc,CAAA,CAAA,GACV,OAAAA,EAAA,EAAA,EACL,aACAA,EAAc,CAAA,GAAE,QAAU,uGAMtBA,EAAM,EAAA,gWArBL,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACxBA,EAAQ,EAAA,EAAG,QAAU,eACzB,0GAOO,oIATPE,EAAA,IAAAoB,EAAA,QAAAtB,EAAU,CAAA,IAAA,KAAO,SAAW,iCACxBA,EAAQ,EAAA,EAAG,QAAU,8TA7FvB,CAAA,QAAAuB,EAAU,EAAE,EAAAlB,GACZ,aAAAmB,EAAY,EAAA,EAAAnB,EACZ,CAAA,QAAAoB,EAAU,EAAI,EAAApB,GACd,MAAAD,CAAmC,EAAAC,EAC1CqB,GAEO,KAAAC,CAA8B,EAAAtB,GAC9B,KAAAuB,CAAY,EAAAvB,GACZ,MAAAC,CAAa,EAAAD,GACb,WAAAE,CAAmB,EAAAF,GACnB,WAAAG,CAAkB,EAAAH,EAClB,CAAA,WAAAI,GAAwB,MAAM,CAAA,EAAAJ,GAC9B,SAAAwB,CAAuB,EAAAxB,EACvB,CAAA,WAAAK,EAAa,EAAK,EAAAL,GAClB,eAAAyB,CAA6B,EAAAzB,EAC7B,CAAA,UAAA0B,EAAY,EAAI,EAAA1B,EAChB,CAAA,MAAA2B,EAAuB,IAAI,EAAA3B,EAC3B,CAAA,UAAA4B,EAAgC,MAAS,EAAA5B,EACzC,CAAA,OAAAM,EAA6B,MAAS,EAAAN,GACtC,OAAA6B,CAMT,EAAA7B,EAEI,MAAA8B,EACLC,GAAwC,cAAc,GACtDC,GAIG,IAAAhB,EAAW,GACXiB,EAAiB,aAoFN,OAAAzB,CAAM,IAAAC,EAAA,EAAQV,EAAQS,CAAM,MAC9B,OAAAA,CAAM,IAAAC,EAAA,GAAQO,EAAWR,CAAM,QAC3BqB,EAAO,SAAS,OAAO,EACzBK,EAAA,CAAA,CAAA,OAAA1B,KAAaqB,EAAO,SAAS,SAAUrB,CAAM,oqBA1F3DC,EAAA,GAAE0B,EAASC,EAAerC,EAAOwB,EAAMC,CAAQ,CAAA,sBAM3C,KAAK,UAAUW,CAAM,IAAM,KAAK,UAAUd,CAAS,GAElD,GADJZ,EAAA,GAAAY,EAAYc,CAAM,EACdA,IAAW,KACdN,EAAO,SAAS,QAAQ,EACxBpB,EAAA,GAAAwB,EAAiB,EAAK,UAEpB,EAAA,MAAM,QAAQE,CAAM,EAAIA,EAAM,CAAIA,CAAM,GAAG,MAC3CE,GAAcA,EAAU,IAAI,EAG9B5B,EAAA,GAAAwB,EAAiB,EAAK,EACtBJ,EAAO,SAAS,QAAQ,UACdP,IAAS,cAAa,CAC5B,IAAAgB,GAAS,MAAM,QAAQH,CAAM,EAAIA,EAAU,CAAAA,CAAM,GAAG,IACtDE,GAAcA,EAAU,IAAK,EAE3BE,EAAeJ,EACnB1B,EAAA,GAAAwB,EAAiB,EAAI,EACrBH,EAAaP,EAAMe,CAAK,EAAE,KAAME,GAAQ,CACnCD,IAAiBJ,IAKrB1B,EAAA,GAAAwB,EAAiB,EAAK,EAClBO,EAAS,OACX,MAAM,QAAQL,CAAM,EAAIA,EAAU,CAAAA,CAAM,GAAG,QACpC,MAAAE,EAAWI,IAAC,CAClBJ,EAAU,KAAI,MAASK,GAAaL,EAAU,IAAK,EACnDA,EAAU,KAAO,WAIlB,MAAM,QAAQF,CAAM,EAAIA,EAAU,CAAAA,CAAM,GAAG,QAC1C,CAAAE,EAAWI,IAAC,CACRD,EAAS,QACZH,EAAU,UAAYA,EAAU,KAChCA,EAAU,KAAOG,EAAS,MAAMC,CAAC,EACjCJ,EAAU,QAAU,GACpBA,EAAU,KAAO,eAIpBhB,EAASZ,EAAA,GAAG0B,EAASC,EAAerC,EAAOwB,EAAMC,CAAQ,CAAA,CAAA,GAE1DK,EAAO,SAAS,QAAQ,EACxBA,EAAO,SAAS,QAAQ"}