{"version": 3, "file": "index-afc543b3.js", "sources": ["../../../../js/uploadbutton/interactive/InteractiveUploadButton.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { getContext, tick } from \"svelte\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport UploadButton from \"../shared\";\n\timport { upload_files as default_upload_files } from \"@gradio/client\";\n\timport { blobToBase64 } from \"@gradio/upload\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let label: string;\n\texport let value: null | FileData;\n\texport let file_count: string;\n\texport let file_types: string[] = [];\n\texport let root: string;\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let gradio: Gradio<{\n\t\tchange: FileData | null;\n\t\tupload: FileData;\n\t\tclick: never;\n\t}>;\n\n\tconst upload_files =\n\t\tgetContext<typeof default_upload_files>(\"upload_files\") ??\n\t\tdefault_upload_files;\n\n\tasync function handle_upload({\n\t\tdetail\n\t}: CustomEvent<FileData>): Promise<void> {\n\t\tvalue = detail;\n\t\tawait tick();\n\t\tlet files = (Array.isArray(detail) ? detail : [detail]).map(\n\t\t\t(file_data) => file_data.blob!\n\t\t);\n\n\t\tupload_files(root, files).then(async (response) => {\n\t\t\tif (response.error) {\n\t\t\t\t(Array.isArray(detail) ? detail : [detail]).forEach(\n\t\t\t\t\tasync (file_data, i) => {\n\t\t\t\t\t\tfile_data.data = await blobToBase64(file_data.blob!);\n\t\t\t\t\t\tfile_data.blob = undefined;\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\t(Array.isArray(detail) ? detail : [detail]).forEach((file_data, i) => {\n\t\t\t\t\tif (response.files) {\n\t\t\t\t\t\tfile_data.orig_name = file_data.name;\n\t\t\t\t\t\tfile_data.name = response.files[i];\n\t\t\t\t\t\tfile_data.is_file = true;\n\t\t\t\t\t\tfile_data.blob = undefined;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tgradio.dispatch(\"change\", value);\n\t\t\tgradio.dispatch(\"upload\", detail);\n\t\t});\n\t}\n</script>\n\n<UploadButton\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\t{file_count}\n\t{file_types}\n\t{size}\n\t{scale}\n\t{min_width}\n\t{variant}\n\t{label}\n\ton:click={() => gradio.dispatch(\"click\")}\n\ton:load={handle_upload}\n>\n\t{$_(label)}\n</UploadButton>\n"], "names": ["t_value", "ctx", "dirty", "set_data", "t", "elem_id", "$$props", "elem_classes", "visible", "label", "value", "file_count", "file_types", "root", "size", "scale", "min_width", "variant", "gradio", "upload_files", "getContext", "default_upload_files", "handle_upload", "detail", "$$invalidate", "tick", "files", "file_data", "response", "i", "blobToBase64"], "mappings": "mQA+EE,IAAAA,EAAAC,MAAGA,EAAK,CAAA,CAAA,EAAA,gDAARC,EAAA,MAAAF,KAAAA,EAAAC,MAAGA,EAAK,CAAA,CAAA,EAAA,KAAAE,EAAAC,EAAAJ,CAAA,kRAFAC,EAAa,EAAA,CAAA,0dApEX,GAAA,CAAA,QAAAI,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAa,EAAAH,GACb,MAAAI,CAAsB,EAAAJ,GACtB,WAAAK,CAAkB,EAAAL,GAClB,WAAAM,EAAU,EAAA,EAAAN,GACV,KAAAO,CAAY,EAAAP,EACZ,CAAA,KAAAQ,EAAoB,IAAI,EAAAR,EACxB,CAAA,MAAAS,EAAuB,IAAI,EAAAT,EAC3B,CAAA,UAAAU,EAAgC,MAAS,EAAAV,EACzC,CAAA,QAAAW,EAA4C,WAAW,EAAAX,GACvD,OAAAY,CAIT,EAAAZ,EAEI,MAAAa,EACLC,EAAwC,cAAc,GACtDC,EAEc,eAAAC,GACd,OAAAC,GAAM,CAENC,EAAA,GAAAd,EAAQa,CAAM,QACRE,EAAI,EACN,IAAAC,GAAS,MAAM,QAAQH,CAAM,EAAIA,EAAU,CAAAA,CAAM,GAAG,IACtDI,GAAcA,EAAU,IAAK,EAG/BR,EAAaN,EAAMa,CAAK,EAAE,WAAYE,GAAQ,CACzCA,EAAS,OACX,MAAM,QAAQL,CAAM,EAAIA,EAAU,CAAAA,CAAM,GAAG,QACpC,MAAAI,EAAWE,IAAC,CAClBF,EAAU,KAAI,MAASG,EAAaH,EAAU,IAAK,EACnDA,EAAU,KAAO,UAIlB,MAAM,QAAQJ,CAAM,EAAIA,EAAU,CAAAA,CAAM,GAAG,QAAS,CAAAI,EAAWE,IAAC,CAC5DD,EAAS,QACZD,EAAU,UAAYA,EAAU,KAChCA,EAAU,KAAOC,EAAS,MAAMC,CAAC,EACjCF,EAAU,QAAU,GACpBA,EAAU,KAAO,UAKpBT,EAAO,SAAS,SAAUR,CAAK,EAC/BQ,EAAO,SAAS,SAAUK,CAAM,gBAgBlBL,EAAO,SAAS,OAAO"}