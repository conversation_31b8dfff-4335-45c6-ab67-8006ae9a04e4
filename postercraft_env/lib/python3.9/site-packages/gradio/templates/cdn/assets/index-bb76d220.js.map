{"version": 3, "file": "index-bb76d220.js", "sources": ["../../../../node_modules/.pnpm/@lezer+lr@1.3.3/node_modules/@lezer/lr/dist/index.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, <PERSON>deT<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, IterMode } from '@lezer/common';\n\n/// A parse stack. These are used internally by the parser to track\n/// parsing progress. They also provide some properties and methods\n/// that external code such as a tokenizer can use to get information\n/// about the parse state.\nclass Stack {\n    /// @internal\n    constructor(\n    /// The parse that this stack is part of @internal\n    p, \n    /// Holds state, input pos, buffer index triplets for all but the\n    /// top state @internal\n    stack, \n    /// The current parse state @internal\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /// @internal\n    reducePos, \n    /// The input position up to which this stack has parsed.\n    pos, \n    /// The dynamic score of the stack, including dynamic precedence\n    /// and error-recovery penalties\n    /// @internal\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /// @internal\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /// @internal\n    bufferBase, \n    /// @internal\n    curContext, \n    /// @internal\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /// @internal\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /// @internal\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /// @internal\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /// The stack's current [context](#lr.ContextTracker) value, if\n    /// any. Its type will depend on the context tracker's type\n    /// parameter, or it will be `null` if there is no context\n    /// tracker.\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /// @internal\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /// @internal\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /// @internal\n    storeNode(term, start, end, size = 4, isReduce = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!isReduce || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */)\n                while (index > 0 && this.buffer[index - 2] > end) {\n                    // Move this record forward\n                    this.buffer[index] = this.buffer[index - 4];\n                    this.buffer[index + 1] = this.buffer[index - 3];\n                    this.buffer[index + 2] = this.buffer[index - 2];\n                    this.buffer[index + 3] = this.buffer[index - 1];\n                    index -= 4;\n                    if (size > 4)\n                        size -= 4;\n                }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /// @internal\n    shift(action, next, nextEnd) {\n        let start = this.pos;\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (nextEnd > this.pos || next <= parser.maxNode) {\n                this.pos = nextEnd;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = nextEnd;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(next, start);\n            if (next <= parser.maxNode)\n                this.buffer.push(next, start, nextEnd, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = nextEnd;\n            this.shiftContext(next, start);\n            if (next <= this.p.parser.maxNode)\n                this.buffer.push(next, start, nextEnd, 4);\n        }\n    }\n    // Apply an action\n    /// @internal\n    apply(action, next, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /// @internal\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /// @internal\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /// @internal\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /// Check if the given term would be able to be shifted (optionally\n    /// after some reductions) on this stack. This can be useful for\n    /// external tokenizers that want to make sure they only provide a\n    /// given token when it applies.\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /// @internal\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /// @internal\n    forceReduce() {\n        let reduce = this.p.parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        let { parser } = this.p;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0)\n                return false;\n            this.storeNode(0 /* Term.Err */, this.reducePos, this.reducePos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /// @internal\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /// Check whether this state has no further actions (assumed to be a direct descendant of the\n    /// top state, since any other states must be able to continue\n    /// somehow). @internal\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /// Restart the stack (put it back in its start state). Only safe\n    /// when this.stack.length == 3 (state is directly below the top\n    /// state). @internal\n    restart() {\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /// @internal\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /// Get the parser used by this stack.\n    get parser() { return this.p.parser; }\n    /// Test whether a given dialect (by numeric ID, as exported from\n    /// the terms file) is enabled.\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /// @internal\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.reducePos, this.reducePos, -3);\n    }\n    /// @internal\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.reducePos, this.reducePos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /// @internal\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /// @internal\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\nvar Recover;\n(function (Recover) {\n    Recover[Recover[\"Insert\"] = 200] = \"Insert\";\n    Recover[Recover[\"Delete\"] = 190] = \"Delete\";\n    Recover[Recover[\"Reduce\"] = 100] = \"Reduce\";\n    Recover[Recover[\"MaxNext\"] = 4] = \"MaxNext\";\n    Recover[Recover[\"MaxInsertStackDepth\"] = 300] = \"MaxInsertStackDepth\";\n    Recover[Recover[\"DampenInsertStackDepth\"] = 120] = \"DampenInsertStackDepth\";\n    Recover[Recover[\"MinBigReduction\"] = 2000] = \"MinBigReduction\";\n})(Recover || (Recover = {}));\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/// [Tokenizers](#lr.ExternalTokenizer) interact with the input\n/// through this interface. It presents the input as a stream of\n/// characters, tracking lookahead and hiding the complexity of\n/// [ranges](#common.Parser.parse^ranges) from tokenizer code.\nclass InputStream {\n    /// @internal\n    constructor(\n    /// @internal\n    input, \n    /// @internal\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /// @internal\n        this.chunk = \"\";\n        /// @internal\n        this.chunkOff = 0;\n        /// Backup chunk\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /// The character code of the next code unit in the input, or -1\n        /// when the stream is at the end of the input.\n        this.next = -1;\n        /// @internal\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /// @internal\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /// @internal\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /// Look at a code unit near the stream position. `.peek(0)` equals\n    /// `.next`, `.peek(-1)` gives you the previous character, and so\n    /// on.\n    ///\n    /// Note that looking around during tokenizing creates dependencies\n    /// on potentially far-away content, which may reduce the\n    /// effectiveness incremental parsing—when looking forward—or even\n    /// cause invalid reparses when looking backward more than 25 code\n    /// units, since the library does not track lookbehind.\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /// Accept a token. By default, the end of the token is set to the\n    /// current stream position, but you can pass an offset (relative to\n    /// the stream position) to change that.\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /// Move the stream forward N (defaults to 1) code units. Returns\n    /// the new value of [`next`](#lr.InputStream.next).\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /// @internal\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /// @internal\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/// @internal\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/// @hide\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, cur;\n        for (;;) {\n            cur = input.pos;\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (input.next < 0)\n                break;\n            input.advance();\n            input.reset(cur + 1, input.token);\n        }\n        if (cur > start) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, cur - start);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/// `@external tokens` declarations in the grammar should resolve to\n/// an instance of this class.\nclass ExternalTokenizer {\n    /// Create a tokenizer. The first argument is the function that,\n    /// given an input stream, scans for the types of tokens it\n    /// recognizes at the stream's position, and calls\n    /// [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    /// one.\n    constructor(\n    /// @internal\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */ && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nvar Safety;\n(function (Safety) {\n    Safety[Safety[\"Margin\"] = 25] = \"Margin\";\n})(Safety || (Safety = {}));\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Safety.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Safety.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Safety.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nvar Rec;\n(function (Rec) {\n    Rec[Rec[\"Distance\"] = 5] = \"Distance\";\n    Rec[Rec[\"MaxRemainingPerStep\"] = 3] = \"MaxRemainingPerStep\";\n    // When two stacks have been running independently long enough to\n    // add this many elements to their buffers, prune one.\n    Rec[Rec[\"MinBufferLengthPrune\"] = 500] = \"MinBufferLengthPrune\";\n    Rec[Rec[\"ForceReduceLimit\"] = 10] = \"ForceReduceLimit\";\n    // Once a stack reaches this depth (in .stack.length) force-reduce\n    // it back to CutTo to avoid creating trees that overflow the stack\n    // on recursive traversal.\n    Rec[Rec[\"CutDepth\"] = 15000] = \"CutDepth\";\n    Rec[Rec[\"CutTo\"] = 9000] = \"CutTo\";\n    Rec[Rec[\"MaxLeftAssociativeReductionCount\"] = 300] = \"MaxLeftAssociativeReductionCount\";\n    // The maximum number of non-recovering stacks to explore (to avoid\n    // getting bogged down with exponentially multiplying stacks in\n    // ambiguous content)\n    Rec[Rec[\"MaxStackCount\"] = 12] = \"MaxStackCount\";\n})(Rec || (Rec = {}));\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished)\n                return this.stackToTree(finished);\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished)\n                return this.stackToTree(finished.forceAll());\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 15000 /* Rec.CutDepth */) {\n            while (stack.stack.length > 9000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            localStack.apply(action, term, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/// Context trackers are used to track stateful context (such as\n/// indentation in the Python grammar, or parent elements in the XML\n/// grammar) needed by external tokenizers. You declare them in a\n/// grammar file as `@context exportName from \"module\"`.\n///\n/// Context values should be immutable, and can be updated (replaced)\n/// on shift or reduce actions.\n///\n/// The export used in a `@context` declaration should be of this\n/// type.\nclass ContextTracker {\n    /// Define a context tracker.\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/// Holds the parse tables for a given grammar, as generated by\n/// `lezer-generator`, and provides [methods](#common.Parser) to parse\n/// content with.\nclass LRParser extends Parser {\n    /// @internal\n    constructor(spec) {\n        super();\n        /// @internal\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /// Get a goto table entry @internal\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /// Check if this state has an action for a given terminal @internal\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /// @internal\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /// @internal\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /// @internal\n    validAction(state, action) {\n        if (action == this.stateSlot(state, 4 /* ParseState.DefaultReduce */))\n            return true;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    return false;\n            }\n            if (action == pair(this.data, i + 1))\n                return true;\n        }\n    }\n    /// Get the states that can follow this one through shift actions or\n    /// goto jumps. @internal\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /// Configure the parser. Returns a new parser instance that has the\n    /// given settings modified. Settings not provided in `config` are\n    /// kept from the original parser.\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /// Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    /// are registered for this parser.\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /// Returns the name associated with a given term. This will only\n    /// work for all terms when the parser was generated with the\n    /// `--names` option. By default, only the names of tagged terms are\n    /// stored.\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /// The eof term id is always allocated directly after the node\n    /// types. @internal\n    get eofTerm() { return this.maxNode + 1; }\n    /// The type of top node produced by the parser.\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /// @internal\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /// @internal\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /// Used by the output of the parser generator. Not available to\n    /// user code. @hide\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n"], "names": ["<PERSON><PERSON>", "p", "stack", "state", "reducePos", "pos", "score", "buffer", "bufferBase", "cur<PERSON><PERSON><PERSON><PERSON>", "lookAhead", "parent", "_", "i", "cx", "StackContext", "start", "action", "_a", "depth", "type", "parser", "dPrec", "base", "size", "count", "baseStateID", "term", "end", "isReduce", "cur", "top", "index", "next", "nextEnd", "nextState", "value", "off", "isNode", "sim", "SimulatedStack", "nextStates", "best", "s", "v", "result", "reduce", "target", "other", "dialectID", "last", "context", "newCx", "tracker", "Recover", "goto", "StackBufferCursor", "decodeArray", "input", "Type", "array", "out", "stop", "digit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nullToken", "InputStream", "ranges", "offset", "assoc", "range", "idx", "resolved", "token", "endOffset", "chunk", "chunkPos", "nextChunk", "n", "from", "to", "r", "TokenGroup", "data", "id", "readToken", "LocalTokenGroup", "precTable", "elseToken", "ExternalTokenizer", "options", "group", "precOffset", "groupMask", "dialect", "scan", "accEnd", "overrides", "low", "high", "mid", "findOffset", "prev", "tableData", "tableOffset", "iPrev", "verbose", "stackIDs", "Safety", "cutAt", "tree", "side", "cursor", "IterMode", "FragmentCursor", "fragments", "nodeSet", "fr", "Tree", "NodeProp", "TokenCache", "stream", "actionIndex", "main", "tokenizers", "mask", "tokenizer", "startIndex", "set", "pair", "Rec", "Parse", "stacks", "newStacks", "stopped", "stoppedTokens", "tok", "finished", "findFinished", "maxRemaining", "a", "b", "outer", "j", "split", "strictCx", "cxHash", "cached", "match", "inner", "defaultReduce", "actions", "localStack", "pushStackDedup", "tokens", "restarted", "tokenEnd", "force", "forceBase", "insert", "Dialect", "source", "flags", "disabled", "x", "ContextTracker", "spec", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "nodeNames", "topTerms", "nodeProps", "setProp", "nodeID", "prop", "propSpec", "NodeSet", "name", "NodeType", "De<PERSON>ult<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "tokenArray", "getSpecializer", "parse", "w", "loose", "table", "groupTag", "terminal", "slot", "flag", "config", "copy", "info", "t", "found", "prec", "values", "part"], "mappings": "kHAMA,MAAMA,CAAM,CAER,YAEAC,EAGAC,EAEAC,EAMAC,EAEAC,EAIAC,EAKAC,EAOAC,EAEAC,EAEAC,EAAY,EAMZC,EAAQ,CACJ,KAAK,EAAIV,EACT,KAAK,MAAQC,EACb,KAAK,MAAQC,EACb,KAAK,UAAYC,EACjB,KAAK,IAAMC,EACX,KAAK,MAAQC,EACb,KAAK,OAASC,EACd,KAAK,WAAaC,EAClB,KAAK,WAAaC,EAClB,KAAK,UAAYC,EACjB,KAAK,OAASC,CACjB,CAED,UAAW,CACP,MAAO,IAAI,KAAK,MAAM,OAAO,CAACC,EAAGC,IAAMA,EAAI,GAAK,CAAC,EAAE,OAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,MAAQ,IAAM,KAAK,MAAQ,IACxH,CAGD,OAAO,MAAMZ,EAAGE,EAAOE,EAAM,EAAG,CAC5B,IAAIS,EAAKb,EAAE,OAAO,QAClB,OAAO,IAAID,EAAMC,EAAG,CAAE,EAAEE,EAAOE,EAAKA,EAAK,EAAG,GAAI,EAAGS,EAAK,IAAIC,EAAaD,EAAIA,EAAG,KAAK,EAAI,KAAM,EAAG,IAAI,CACzG,CAKD,IAAI,SAAU,CAAE,OAAO,KAAK,WAAa,KAAK,WAAW,QAAU,IAAO,CAI1E,UAAUX,EAAOa,EAAO,CACpB,KAAK,MAAM,KAAK,KAAK,MAAOA,EAAO,KAAK,WAAa,KAAK,OAAO,MAAM,EACvE,KAAK,MAAQb,CAChB,CAGD,OAAOc,EAAQ,CACX,IAAIC,EACJ,IAAIC,EAAQF,GAAU,GAAkCG,EAAOH,EAAS,MACpE,CAAE,OAAAI,CAAM,EAAK,KAAK,EAClBC,EAAQD,EAAO,kBAAkBD,CAAI,EAGzC,GAFIE,IACA,KAAK,OAASA,GACdH,GAAS,EAAG,CACZ,KAAK,UAAUE,EAAO,QAAQ,KAAK,MAAOD,EAAM,EAAI,EAAG,KAAK,SAAS,EAGjEA,EAAOC,EAAO,eACd,KAAK,UAAUD,EAAM,KAAK,UAAW,KAAK,UAAW,EAAG,EAAI,EAChE,KAAK,cAAcA,EAAM,KAAK,SAAS,EACvC,OAOJ,IAAIG,EAAO,KAAK,MAAM,QAAWJ,EAAQ,GAAK,GAAMF,EAAS,OAA+B,EAAI,GAC5FD,EAAQO,EAAO,KAAK,MAAMA,EAAO,CAAC,EAAI,KAAK,EAAE,OAAO,CAAC,EAAE,KAAMC,EAAO,KAAK,UAAYR,EAIrFQ,GAAQ,KAAsC,EAAG,GAAAN,EAAK,KAAK,EAAE,OAAO,QAAQ,MAAME,CAAI,KAAO,MAAQF,IAAO,SAAkBA,EAAG,eAC7HF,GAAS,KAAK,EAAE,uBAChB,KAAK,EAAE,oBACP,KAAK,EAAE,qBAAuBQ,GAEzB,KAAK,EAAE,qBAAuBA,IACnC,KAAK,EAAE,kBAAoB,EAC3B,KAAK,EAAE,sBAAwBR,EAC/B,KAAK,EAAE,qBAAuBQ,IAGtC,IAAIhB,EAAae,EAAO,KAAK,MAAMA,EAAO,CAAC,EAAI,EAAGE,EAAQ,KAAK,WAAa,KAAK,OAAO,OAASjB,EAEjG,GAAIY,EAAOC,EAAO,eAAkBJ,EAAS,OAAiC,CAC1E,IAAIZ,EAAMgB,EAAO,UAAU,KAAK,MAAO,CAAC,EAA4B,KAAK,IAAM,KAAK,UACpF,KAAK,UAAUD,EAAMJ,EAAOX,EAAKoB,EAAQ,EAAG,EAAI,EAEpD,GAAIR,EAAS,OACT,KAAK,MAAQ,KAAK,MAAMM,CAAI,MAE3B,CACD,IAAIG,EAAc,KAAK,MAAMH,EAAO,CAAC,EACrC,KAAK,MAAQF,EAAO,QAAQK,EAAaN,EAAM,EAAI,EAEvD,KAAO,KAAK,MAAM,OAASG,GACvB,KAAK,MAAM,MACf,KAAK,cAAcH,EAAMJ,CAAK,CACjC,CAGD,UAAUW,EAAMX,EAAOY,EAAKJ,EAAO,EAAGK,EAAW,GAAO,CACpD,GAAIF,GAAQ,IACP,CAAC,KAAK,MAAM,QAAU,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,EAAI,KAAK,OAAO,OAAS,KAAK,YAAa,CAElG,IAAIG,EAAM,KAAMC,EAAM,KAAK,OAAO,OAKlC,GAJIA,GAAO,GAAKD,EAAI,SAChBC,EAAMD,EAAI,WAAaA,EAAI,OAAO,WAClCA,EAAMA,EAAI,QAEVC,EAAM,GAAKD,EAAI,OAAOC,EAAM,CAAC,GAAK,GAAoBD,EAAI,OAAOC,EAAM,CAAC,EAAI,GAAI,CAChF,GAAIf,GAASY,EACT,OACJ,GAAIE,EAAI,OAAOC,EAAM,CAAC,GAAKf,EAAO,CAC9Bc,EAAI,OAAOC,EAAM,CAAC,EAAIH,EACtB,SAIZ,GAAI,CAACC,GAAY,KAAK,KAAOD,EACzB,KAAK,OAAO,KAAKD,EAAMX,EAAOY,EAAKJ,CAAI,MAEtC,CACD,IAAIQ,EAAQ,KAAK,OAAO,OACxB,GAAIA,EAAQ,GAAK,KAAK,OAAOA,EAAQ,CAAC,GAAK,EACvC,KAAOA,EAAQ,GAAK,KAAK,OAAOA,EAAQ,CAAC,EAAIJ,GAEzC,KAAK,OAAOI,CAAK,EAAI,KAAK,OAAOA,EAAQ,CAAC,EAC1C,KAAK,OAAOA,EAAQ,CAAC,EAAI,KAAK,OAAOA,EAAQ,CAAC,EAC9C,KAAK,OAAOA,EAAQ,CAAC,EAAI,KAAK,OAAOA,EAAQ,CAAC,EAC9C,KAAK,OAAOA,EAAQ,CAAC,EAAI,KAAK,OAAOA,EAAQ,CAAC,EAC9CA,GAAS,EACLR,EAAO,IACPA,GAAQ,GAEpB,KAAK,OAAOQ,CAAK,EAAIL,EACrB,KAAK,OAAOK,EAAQ,CAAC,EAAIhB,EACzB,KAAK,OAAOgB,EAAQ,CAAC,EAAIJ,EACzB,KAAK,OAAOI,EAAQ,CAAC,EAAIR,EAEhC,CAGD,MAAMP,EAAQgB,EAAMC,EAAS,CACzB,IAAIlB,EAAQ,KAAK,IACjB,GAAIC,EAAS,OACT,KAAK,UAAUA,EAAS,MAA8B,KAAK,GAAG,UAExDA,EAAS,OAaf,KAAK,IAAMiB,EACX,KAAK,aAAaD,EAAMjB,CAAK,EACzBiB,GAAQ,KAAK,EAAE,OAAO,SACtB,KAAK,OAAO,KAAKA,EAAMjB,EAAOkB,EAAS,CAAC,MAhBO,CACnD,IAAIC,EAAYlB,EAAQ,CAAE,OAAAI,CAAM,EAAK,KAAK,GACtCa,EAAU,KAAK,KAAOD,GAAQZ,EAAO,WACrC,KAAK,IAAMa,EACNb,EAAO,UAAUc,EAAW,CAA0B,IACvD,KAAK,UAAYD,IAEzB,KAAK,UAAUC,EAAWnB,CAAK,EAC/B,KAAK,aAAaiB,EAAMjB,CAAK,EACzBiB,GAAQZ,EAAO,SACf,KAAK,OAAO,KAAKY,EAAMjB,EAAOkB,EAAS,CAAC,EAQnD,CAGD,MAAMjB,EAAQgB,EAAMC,EAAS,CACrBjB,EAAS,MACT,KAAK,OAAOA,CAAM,EAElB,KAAK,MAAMA,EAAQgB,EAAMC,CAAO,CACvC,CAGD,QAAQE,EAAOH,EAAM,CACjB,IAAID,EAAQ,KAAK,EAAE,OAAO,OAAS,GAC/BA,EAAQ,GAAK,KAAK,EAAE,OAAOA,CAAK,GAAKI,KACrC,KAAK,EAAE,OAAO,KAAKA,CAAK,EACxBJ,KAEJ,IAAIhB,EAAQ,KAAK,IACjB,KAAK,UAAY,KAAK,IAAMA,EAAQoB,EAAM,OAC1C,KAAK,UAAUH,EAAMjB,CAAK,EAC1B,KAAK,OAAO,KAAKgB,EAAOhB,EAAO,KAAK,UAAW,IAC3C,KAAK,YACL,KAAK,cAAc,KAAK,WAAW,QAAQ,MAAM,KAAK,WAAW,QAASoB,EAAO,KAAM,KAAK,EAAE,OAAO,MAAM,KAAK,IAAMA,EAAM,MAAM,CAAC,CAAC,CAC3I,CAKD,OAAQ,CACJ,IAAIzB,EAAS,KACT0B,EAAM1B,EAAO,OAAO,OAKxB,KAAO0B,EAAM,GAAK1B,EAAO,OAAO0B,EAAM,CAAC,EAAI1B,EAAO,WAC9C0B,GAAO,EACX,IAAI9B,EAASI,EAAO,OAAO,MAAM0B,CAAG,EAAGd,EAAOZ,EAAO,WAAa0B,EAElE,KAAO1B,GAAUY,GAAQZ,EAAO,YAC5BA,EAASA,EAAO,OACpB,OAAO,IAAIX,EAAM,KAAK,EAAG,KAAK,MAAM,QAAS,KAAK,MAAO,KAAK,UAAW,KAAK,IAAK,KAAK,MAAOO,EAAQgB,EAAM,KAAK,WAAY,KAAK,UAAWZ,CAAM,CACvJ,CAGD,gBAAgBsB,EAAMC,EAAS,CAC3B,IAAII,EAASL,GAAQ,KAAK,EAAE,OAAO,QAC/BK,GACA,KAAK,UAAUL,EAAM,KAAK,IAAKC,EAAS,CAAC,EAC7C,KAAK,UAAU,EAAkB,KAAK,IAAKA,EAASI,EAAS,EAAI,CAAC,EAClE,KAAK,IAAM,KAAK,UAAYJ,EAC5B,KAAK,OAAS,GACjB,CAKD,SAASP,EAAM,CACX,QAASY,EAAM,IAAIC,EAAe,IAAI,IAAK,CACvC,IAAIvB,EAAS,KAAK,EAAE,OAAO,UAAUsB,EAAI,MAAO,CAAiC,GAAI,KAAK,EAAE,OAAO,UAAUA,EAAI,MAAOZ,CAAI,EAC5H,GAAIV,GAAU,EACV,MAAO,GACX,GAAK,EAAAA,EAAS,OACV,MAAO,GACXsB,EAAI,OAAOtB,CAAM,EAExB,CAID,gBAAgBgB,EAAM,CAClB,GAAI,KAAK,MAAM,QAAU,IACrB,MAAO,GACX,IAAIQ,EAAa,KAAK,EAAE,OAAO,WAAW,KAAK,KAAK,EACpD,GAAIA,EAAW,OAAS,GAA2B,GAAK,KAAK,MAAM,QAAU,IAA0C,CACnH,IAAIC,EAAO,CAAA,EACX,QAAS7B,EAAI,EAAG8B,EAAG9B,EAAI4B,EAAW,OAAQ5B,GAAK,GACtC8B,EAAIF,EAAW5B,EAAI,CAAC,IAAM,KAAK,OAAS,KAAK,EAAE,OAAO,UAAU8B,EAAGV,CAAI,GACxES,EAAK,KAAKD,EAAW5B,CAAC,EAAG8B,CAAC,EAElC,GAAI,KAAK,MAAM,OAAS,IACpB,QAAS9B,EAAI,EAAG6B,EAAK,OAAS,GAA2B,GAAK7B,EAAI4B,EAAW,OAAQ5B,GAAK,EAAG,CACzF,IAAI8B,EAAIF,EAAW5B,EAAI,CAAC,EACnB6B,EAAK,KAAK,CAACE,EAAG/B,IAAOA,EAAI,GAAM+B,GAAKD,CAAC,GACtCD,EAAK,KAAKD,EAAW5B,CAAC,EAAG8B,CAAC,EAEtCF,EAAaC,EAEjB,IAAIG,EAAS,CAAA,EACb,QAAS,EAAI,EAAG,EAAIJ,EAAW,QAAUI,EAAO,OAAS,EAAyB,GAAK,EAAG,CACtF,IAAIF,EAAIF,EAAW,EAAI,CAAC,EACxB,GAAIE,GAAK,KAAK,MACV,SACJ,IAAIzC,EAAQ,KAAK,QACjBA,EAAM,UAAUyC,EAAG,KAAK,GAAG,EAC3BzC,EAAM,UAAU,EAAkBA,EAAM,IAAKA,EAAM,IAAK,EAAG,EAAI,EAC/DA,EAAM,aAAauC,EAAW,CAAC,EAAG,KAAK,GAAG,EAC1CvC,EAAM,OAAS,IACf2C,EAAO,KAAK3C,CAAK,EAErB,OAAO2C,CACV,CAID,aAAc,CACV,IAAIC,EAAS,KAAK,EAAE,OAAO,UAAU,KAAK,MAAO,GACjD,GAAK,EAAAA,EAAS,OACV,MAAO,GACX,GAAI,CAAE,OAAAzB,CAAM,EAAK,KAAK,EACtB,GAAI,CAACA,EAAO,YAAY,KAAK,MAAOyB,CAAM,EAAG,CACzC,IAAI3B,EAAQ2B,GAAU,GAAkCnB,EAAOmB,EAAS,MACpEC,EAAS,KAAK,MAAM,OAAS5B,EAAQ,EACzC,GAAI4B,EAAS,GAAK1B,EAAO,QAAQ,KAAK,MAAM0B,CAAM,EAAGpB,EAAM,EAAK,EAAI,EAChE,MAAO,GACX,KAAK,UAAU,EAAkB,KAAK,UAAW,KAAK,UAAW,EAAG,EAAI,EACxE,KAAK,OAAS,IAElB,YAAK,UAAY,KAAK,IACtB,KAAK,OAAOmB,CAAM,EACX,EACV,CAED,UAAW,CACP,KAAO,CAAC,KAAK,EAAE,OAAO,UAAU,KAAK,MAAO,IACxC,GAAI,CAAC,KAAK,cAAe,CACrB,KAAK,UAAU,EAAkB,KAAK,IAAK,KAAK,IAAK,EAAG,EAAI,EAC5D,MAGR,OAAO,IACV,CAID,IAAI,SAAU,CACV,GAAI,KAAK,MAAM,QAAU,EACrB,MAAO,GACX,GAAI,CAAE,OAAAzB,CAAM,EAAK,KAAK,EACtB,OAAOA,EAAO,KAAKA,EAAO,UAAU,KAAK,MAAO,CAA2B,CAAA,GAAK,OAC5E,CAACA,EAAO,UAAU,KAAK,MAAO,CAAC,CACtC,CAID,SAAU,CACN,KAAK,MAAQ,KAAK,MAAM,CAAC,EACzB,KAAK,MAAM,OAAS,CACvB,CAED,UAAU2B,EAAO,CACb,GAAI,KAAK,OAASA,EAAM,OAAS,KAAK,MAAM,QAAUA,EAAM,MAAM,OAC9D,MAAO,GACX,QAASnC,EAAI,EAAGA,EAAI,KAAK,MAAM,OAAQA,GAAK,EACxC,GAAI,KAAK,MAAMA,CAAC,GAAKmC,EAAM,MAAMnC,CAAC,EAC9B,MAAO,GACf,MAAO,EACV,CAED,IAAI,QAAS,CAAE,OAAO,KAAK,EAAE,MAAS,CAGtC,eAAeoC,EAAW,CAAE,OAAO,KAAK,EAAE,OAAO,QAAQ,MAAMA,CAAS,CAAI,CAC5E,aAAatB,EAAMX,EAAO,CAClB,KAAK,YACL,KAAK,cAAc,KAAK,WAAW,QAAQ,MAAM,KAAK,WAAW,QAASW,EAAM,KAAM,KAAK,EAAE,OAAO,MAAMX,CAAK,CAAC,CAAC,CACxH,CACD,cAAcW,EAAMX,EAAO,CACnB,KAAK,YACL,KAAK,cAAc,KAAK,WAAW,QAAQ,OAAO,KAAK,WAAW,QAASW,EAAM,KAAM,KAAK,EAAE,OAAO,MAAMX,CAAK,CAAC,CAAC,CACzH,CAED,aAAc,CACV,IAAIkC,EAAO,KAAK,OAAO,OAAS,GAC5BA,EAAO,GAAK,KAAK,OAAOA,CAAI,GAAK,KACjC,KAAK,OAAO,KAAK,KAAK,WAAW,KAAM,KAAK,UAAW,KAAK,UAAW,EAAE,CAChF,CAED,eAAgB,CACZ,IAAIA,EAAO,KAAK,OAAO,OAAS,GAC5BA,EAAO,GAAK,KAAK,OAAOA,CAAI,GAAK,KACjC,KAAK,OAAO,KAAK,KAAK,UAAW,KAAK,UAAW,KAAK,UAAW,EAAE,CAC1E,CACD,cAAcC,EAAS,CACnB,GAAIA,GAAW,KAAK,WAAW,QAAS,CACpC,IAAIC,EAAQ,IAAIrC,EAAa,KAAK,WAAW,QAASoC,CAAO,EACzDC,EAAM,MAAQ,KAAK,WAAW,MAC9B,KAAK,YAAW,EACpB,KAAK,WAAaA,EAEzB,CAED,aAAa1C,EAAW,CAChBA,EAAY,KAAK,YACjB,KAAK,cAAa,EAClB,KAAK,UAAYA,EAExB,CAED,OAAQ,CACA,KAAK,YAAc,KAAK,WAAW,QAAQ,QAC3C,KAAK,YAAW,EAChB,KAAK,UAAY,GACjB,KAAK,cAAa,CACzB,CACL,CACA,MAAMK,CAAa,CACf,YAAYsC,EAASF,EAAS,CAC1B,KAAK,QAAUE,EACf,KAAK,QAAUF,EACf,KAAK,KAAOE,EAAQ,OAASA,EAAQ,KAAKF,CAAO,EAAI,CACxD,CACL,CACA,IAAIG,GACH,SAAUA,EAAS,CAChBA,EAAQA,EAAQ,OAAY,GAAG,EAAI,SACnCA,EAAQA,EAAQ,OAAY,GAAG,EAAI,SACnCA,EAAQA,EAAQ,OAAY,GAAG,EAAI,SACnCA,EAAQA,EAAQ,QAAa,CAAC,EAAI,UAClCA,EAAQA,EAAQ,oBAAyB,GAAG,EAAI,sBAChDA,EAAQA,EAAQ,uBAA4B,GAAG,EAAI,yBACnDA,EAAQA,EAAQ,gBAAqB,GAAI,EAAI,iBACjD,GAAGA,IAAYA,EAAU,CAAE,EAAC,EAG5B,MAAMd,CAAe,CACjB,YAAYxB,EAAO,CACf,KAAK,MAAQA,EACb,KAAK,MAAQA,EAAM,MACnB,KAAK,MAAQA,EAAM,MACnB,KAAK,KAAO,KAAK,MAAM,MAC1B,CACD,OAAOC,EAAQ,CACX,IAAIU,EAAOV,EAAS,MAA8BE,EAAQF,GAAU,GAChEE,GAAS,GACL,KAAK,OAAS,KAAK,MAAM,QACzB,KAAK,MAAQ,KAAK,MAAM,MAAK,GACjC,KAAK,MAAM,KAAK,KAAK,MAAO,EAAG,CAAC,EAChC,KAAK,MAAQ,GAGb,KAAK,OAASA,EAAQ,GAAK,EAE/B,IAAIoC,EAAO,KAAK,MAAM,EAAE,OAAO,QAAQ,KAAK,MAAM,KAAK,KAAO,CAAC,EAAG5B,EAAM,EAAI,EAC5E,KAAK,MAAQ4B,CAChB,CACL,CAGA,MAAMC,CAAkB,CACpB,YAAYtD,EAAOG,EAAK2B,EAAO,CAC3B,KAAK,MAAQ9B,EACb,KAAK,IAAMG,EACX,KAAK,MAAQ2B,EACb,KAAK,OAAS9B,EAAM,OAChB,KAAK,OAAS,GACd,KAAK,UAAS,CACrB,CACD,OAAO,OAAOA,EAAOG,EAAMH,EAAM,WAAaA,EAAM,OAAO,OAAQ,CAC/D,OAAO,IAAIsD,EAAkBtD,EAAOG,EAAKA,EAAMH,EAAM,UAAU,CAClE,CACD,WAAY,CACR,IAAI+B,EAAO,KAAK,MAAM,OAClBA,GAAQ,OACR,KAAK,MAAQ,KAAK,MAAM,WAAaA,EAAK,WAC1C,KAAK,MAAQA,EACb,KAAK,OAASA,EAAK,OAE1B,CACD,IAAI,IAAK,CAAE,OAAO,KAAK,OAAO,KAAK,MAAQ,CAAC,CAAI,CAChD,IAAI,OAAQ,CAAE,OAAO,KAAK,OAAO,KAAK,MAAQ,CAAC,CAAI,CACnD,IAAI,KAAM,CAAE,OAAO,KAAK,OAAO,KAAK,MAAQ,CAAC,CAAI,CACjD,IAAI,MAAO,CAAE,OAAO,KAAK,OAAO,KAAK,MAAQ,CAAC,CAAI,CAClD,MAAO,CACH,KAAK,OAAS,EACd,KAAK,KAAO,EACR,KAAK,OAAS,GACd,KAAK,UAAS,CACrB,CACD,MAAO,CACH,OAAO,IAAIuB,EAAkB,KAAK,MAAO,KAAK,IAAK,KAAK,KAAK,CAChE,CACL,CAIA,SAASC,EAAYC,EAAOC,EAAO,YAAa,CAC5C,GAAI,OAAOD,GAAS,SAChB,OAAOA,EACX,IAAIE,EAAQ,KACZ,QAASvD,EAAM,EAAGwD,EAAM,EAAGxD,EAAMqD,EAAM,QAAS,CAC5C,IAAItB,EAAQ,EACZ,OAAS,CACL,IAAIH,EAAOyB,EAAM,WAAWrD,GAAK,EAAGyD,EAAO,GAC3C,GAAI7B,GAAQ,IAA6B,CACrCG,EAAQ,MACR,MAEAH,GAAQ,IACRA,IACAA,GAAQ,IACRA,IACJ,IAAI8B,EAAQ9B,EAAO,GAMnB,GALI8B,GAAS,KACTA,GAAS,GACTD,EAAO,IAEX1B,GAAS2B,EACLD,EACA,MACJ1B,GAAS,GAETwB,EACAA,EAAMC,GAAK,EAAIzB,EAEfwB,EAAQ,IAAID,EAAKvB,CAAK,EAE9B,OAAOwB,CACX,CAEA,MAAMI,CAAY,CACd,aAAc,CACV,KAAK,MAAQ,GACb,KAAK,MAAQ,GACb,KAAK,IAAM,GACX,KAAK,SAAW,GAChB,KAAK,UAAY,EACjB,KAAK,KAAO,EACZ,KAAK,QAAU,CAClB,CACL,CACA,MAAMC,EAAY,IAAID,EAKtB,MAAME,CAAY,CAEd,YAEAR,EAEAS,EAAQ,CACJ,KAAK,MAAQT,EACb,KAAK,OAASS,EAEd,KAAK,MAAQ,GAEb,KAAK,SAAW,EAEhB,KAAK,OAAS,GACd,KAAK,UAAY,EAGjB,KAAK,KAAO,GAEZ,KAAK,MAAQF,EACb,KAAK,WAAa,EAClB,KAAK,IAAM,KAAK,SAAWE,EAAO,CAAC,EAAE,KACrC,KAAK,MAAQA,EAAO,CAAC,EACrB,KAAK,IAAMA,EAAOA,EAAO,OAAS,CAAC,EAAE,GACrC,KAAK,SAAQ,CAChB,CAED,cAAcC,EAAQC,EAAO,CACzB,IAAIC,EAAQ,KAAK,MAAOtC,EAAQ,KAAK,WACjC3B,EAAM,KAAK,IAAM+D,EACrB,KAAO/D,EAAMiE,EAAM,MAAM,CACrB,GAAI,CAACtC,EACD,OAAO,KACX,IAAIC,EAAO,KAAK,OAAO,EAAED,CAAK,EAC9B3B,GAAOiE,EAAM,KAAOrC,EAAK,GACzBqC,EAAQrC,EAEZ,KAAOoC,EAAQ,EAAIhE,EAAMiE,EAAM,GAAKjE,GAAOiE,EAAM,IAAI,CACjD,GAAItC,GAAS,KAAK,OAAO,OAAS,EAC9B,OAAO,KACX,IAAIC,EAAO,KAAK,OAAO,EAAED,CAAK,EAC9B3B,GAAO4B,EAAK,KAAOqC,EAAM,GACzBA,EAAQrC,EAEZ,OAAO5B,CACV,CAED,QAAQA,EAAK,CACT,GAAIA,GAAO,KAAK,MAAM,MAAQA,EAAM,KAAK,MAAM,GAC3C,OAAOA,EACX,QAASiE,KAAS,KAAK,OACnB,GAAIA,EAAM,GAAKjE,EACX,OAAO,KAAK,IAAIA,EAAKiE,EAAM,IAAI,EACvC,OAAO,KAAK,GACf,CAUD,KAAKF,EAAQ,CACT,IAAIG,EAAM,KAAK,SAAWH,EAAQ/D,EAAKwC,EACvC,GAAI0B,GAAO,GAAKA,EAAM,KAAK,MAAM,OAC7BlE,EAAM,KAAK,IAAM+D,EACjBvB,EAAS,KAAK,MAAM,WAAW0B,CAAG,MAEjC,CACD,IAAIC,EAAW,KAAK,cAAcJ,EAAQ,CAAC,EAC3C,GAAII,GAAY,KACZ,MAAO,GAEX,GADAnE,EAAMmE,EACFnE,GAAO,KAAK,WAAaA,EAAM,KAAK,UAAY,KAAK,OAAO,OAC5DwC,EAAS,KAAK,OAAO,WAAWxC,EAAM,KAAK,SAAS,MAEnD,CACD,IAAIQ,EAAI,KAAK,WAAYyD,EAAQ,KAAK,MACtC,KAAOA,EAAM,IAAMjE,GACfiE,EAAQ,KAAK,OAAO,EAAEzD,CAAC,EAC3B,KAAK,OAAS,KAAK,MAAM,MAAM,KAAK,UAAYR,CAAG,EAC/CA,EAAM,KAAK,OAAO,OAASiE,EAAM,KACjC,KAAK,OAAS,KAAK,OAAO,MAAM,EAAGA,EAAM,GAAKjE,CAAG,GACrDwC,EAAS,KAAK,OAAO,WAAW,CAAC,GAGzC,OAAIxC,GAAO,KAAK,MAAM,YAClB,KAAK,MAAM,UAAYA,EAAM,GAC1BwC,CACV,CAID,YAAY4B,EAAOC,EAAY,EAAG,CAC9B,IAAI9C,EAAM8C,EAAY,KAAK,cAAcA,EAAW,EAAE,EAAI,KAAK,IAC/D,GAAI9C,GAAO,MAAQA,EAAM,KAAK,MAAM,MAChC,MAAM,IAAI,WAAW,yBAAyB,EAClD,KAAK,MAAM,MAAQ6C,EACnB,KAAK,MAAM,IAAM7C,CACpB,CACD,UAAW,CACP,GAAI,KAAK,KAAO,KAAK,WAAa,KAAK,IAAM,KAAK,UAAY,KAAK,OAAO,OAAQ,CAC9E,GAAI,CAAE,MAAA+C,EAAO,SAAAC,CAAU,EAAG,KAC1B,KAAK,MAAQ,KAAK,OAClB,KAAK,SAAW,KAAK,UACrB,KAAK,OAASD,EACd,KAAK,UAAYC,EACjB,KAAK,SAAW,KAAK,IAAM,KAAK,aAE/B,CACD,KAAK,OAAS,KAAK,MACnB,KAAK,UAAY,KAAK,SACtB,IAAIC,EAAY,KAAK,MAAM,MAAM,KAAK,GAAG,EACrCjD,EAAM,KAAK,IAAMiD,EAAU,OAC/B,KAAK,MAAQjD,EAAM,KAAK,MAAM,GAAKiD,EAAU,MAAM,EAAG,KAAK,MAAM,GAAK,KAAK,GAAG,EAAIA,EAClF,KAAK,SAAW,KAAK,IACrB,KAAK,SAAW,EAEvB,CACD,UAAW,CACP,OAAI,KAAK,UAAY,KAAK,MAAM,SAC5B,KAAK,SAAQ,EACT,KAAK,UAAY,KAAK,MAAM,QACrB,KAAK,KAAO,GAEpB,KAAK,KAAO,KAAK,MAAM,WAAW,KAAK,QAAQ,CACzD,CAGD,QAAQC,EAAI,EAAG,CAEX,IADA,KAAK,UAAYA,EACV,KAAK,IAAMA,GAAK,KAAK,MAAM,IAAI,CAClC,GAAI,KAAK,YAAc,KAAK,OAAO,OAAS,EACxC,OAAO,KAAK,UAChBA,GAAK,KAAK,MAAM,GAAK,KAAK,IAC1B,KAAK,MAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,EAC1C,KAAK,IAAM,KAAK,MAAM,KAE1B,YAAK,KAAOA,EACR,KAAK,KAAO,KAAK,MAAM,YACvB,KAAK,MAAM,UAAY,KAAK,IAAM,GAC/B,KAAK,UACf,CACD,SAAU,CACN,YAAK,IAAM,KAAK,SAAW,KAAK,IAChC,KAAK,MAAQ,KAAK,OAAO,KAAK,WAAa,KAAK,OAAO,OAAS,CAAC,EACjE,KAAK,MAAQ,GACN,KAAK,KAAO,EACtB,CAED,MAAMzE,EAAKoE,EAAO,CAUd,GATIA,GACA,KAAK,MAAQA,EACbA,EAAM,MAAQpE,EACdoE,EAAM,UAAYpE,EAAM,EACxBoE,EAAM,MAAQA,EAAM,SAAW,IAG/B,KAAK,MAAQR,EAEb,KAAK,KAAO5D,EAAK,CAEjB,GADA,KAAK,IAAMA,EACPA,GAAO,KAAK,IACZ,YAAK,QAAO,EACL,KAEX,KAAOA,EAAM,KAAK,MAAM,MACpB,KAAK,MAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,EAC9C,KAAOA,GAAO,KAAK,MAAM,IACrB,KAAK,MAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,EAC1CA,GAAO,KAAK,UAAYA,EAAM,KAAK,SAAW,KAAK,MAAM,OACzD,KAAK,SAAWA,EAAM,KAAK,UAG3B,KAAK,MAAQ,GACb,KAAK,SAAW,GAEpB,KAAK,SAAQ,EAEjB,OAAO,IACV,CAED,KAAK0E,EAAMC,EAAI,CACX,GAAID,GAAQ,KAAK,UAAYC,GAAM,KAAK,SAAW,KAAK,MAAM,OAC1D,OAAO,KAAK,MAAM,MAAMD,EAAO,KAAK,SAAUC,EAAK,KAAK,QAAQ,EACpE,GAAID,GAAQ,KAAK,WAAaC,GAAM,KAAK,UAAY,KAAK,OAAO,OAC7D,OAAO,KAAK,OAAO,MAAMD,EAAO,KAAK,UAAWC,EAAK,KAAK,SAAS,EACvE,GAAID,GAAQ,KAAK,MAAM,MAAQC,GAAM,KAAK,MAAM,GAC5C,OAAO,KAAK,MAAM,KAAKD,EAAMC,CAAE,EACnC,IAAInC,EAAS,GACb,QAASoC,KAAK,KAAK,OAAQ,CACvB,GAAIA,EAAE,MAAQD,EACV,MACAC,EAAE,GAAKF,IACPlC,GAAU,KAAK,MAAM,KAAK,KAAK,IAAIoC,EAAE,KAAMF,CAAI,EAAG,KAAK,IAAIE,EAAE,GAAID,CAAE,CAAC,GAE5E,OAAOnC,CACV,CACL,CAEA,MAAMqC,CAAW,CACb,YAAYC,EAAMC,EAAI,CAClB,KAAK,KAAOD,EACZ,KAAK,GAAKC,CACb,CACD,MAAM1B,EAAOxD,EAAO,CAChB,GAAI,CAAE,OAAAmB,CAAM,EAAKnB,EAAM,EACvBmF,EAAU,KAAK,KAAM3B,EAAOxD,EAAO,KAAK,GAAImB,EAAO,KAAMA,EAAO,cAAc,CACjF,CACL,CACA6D,EAAW,UAAU,WAAaA,EAAW,UAAU,SAAWA,EAAW,UAAU,OAAS,GAEhG,MAAMI,CAAgB,CAClB,YAAYH,EAAMI,EAAWC,EAAW,CACpC,KAAK,UAAYD,EACjB,KAAK,UAAYC,EACjB,KAAK,KAAO,OAAOL,GAAQ,SAAW1B,EAAY0B,CAAI,EAAIA,CAC7D,CACD,MAAMzB,EAAOxD,EAAO,CAChB,IAAIc,EAAQ0C,EAAM,IAAK5B,EACvB,KACIA,EAAM4B,EAAM,IACZ2B,EAAU,KAAK,KAAM3B,EAAOxD,EAAO,EAAG,KAAK,KAAM,KAAK,SAAS,EAC3D,EAAAwD,EAAM,MAAM,MAAQ,KAHnB,CAKL,GAAI,KAAK,WAAa,KAClB,OACJ,GAAIA,EAAM,KAAO,EACb,MACJA,EAAM,QAAO,EACbA,EAAM,MAAM5B,EAAM,EAAG4B,EAAM,KAAK,EAEhC5B,EAAMd,IACN0C,EAAM,MAAM1C,EAAO0C,EAAM,KAAK,EAC9BA,EAAM,YAAY,KAAK,UAAW5B,EAAMd,CAAK,EAEpD,CACL,CACAsE,EAAgB,UAAU,WAAaJ,EAAW,UAAU,SAAWA,EAAW,UAAU,OAAS,GAGrG,MAAMO,EAAkB,CAMpB,YAEAhB,EAAOiB,EAAU,GAAI,CACjB,KAAK,MAAQjB,EACb,KAAK,WAAa,CAAC,CAACiB,EAAQ,WAC5B,KAAK,SAAW,CAAC,CAACA,EAAQ,SAC1B,KAAK,OAAS,CAAC,CAACA,EAAQ,MAC3B,CACL,CAqBA,SAASL,EAAUF,EAAMzB,EAAOxD,EAAOyF,EAAOJ,EAAWK,EAAY,CACjE,IAAIzF,EAAQ,EAAG0F,EAAY,GAAKF,EAAO,CAAE,QAAAG,GAAY5F,EAAM,EAAE,OAC7D6F,EAAM,KACGF,EAAYV,EAAKhF,CAAK,GADhB,CAGX,IAAI6F,EAASb,EAAKhF,EAAQ,CAAC,EAI3B,QAASU,EAAIV,EAAQ,EAAGU,EAAImF,EAAQnF,GAAK,EACrC,IAAKsE,EAAKtE,EAAI,CAAC,EAAIgF,GAAa,EAAG,CAC/B,IAAIlE,EAAOwD,EAAKtE,CAAC,EACjB,GAAIiF,EAAQ,OAAOnE,CAAI,IAClB+B,EAAM,MAAM,OAAS,IAAMA,EAAM,MAAM,OAAS/B,GAC7CsE,EAAUtE,EAAM+B,EAAM,MAAM,MAAO6B,EAAWK,CAAU,GAAI,CAChElC,EAAM,YAAY/B,CAAI,EACtB,OAGZ,IAAIM,EAAOyB,EAAM,KAAMwC,EAAM,EAAGC,EAAOhB,EAAKhF,EAAQ,CAAC,EAErD,GAAIuD,EAAM,KAAO,GAAKyC,EAAOD,GAAOf,EAAKa,EAASG,EAAO,EAAI,CAAC,GAAK,OAAuBhB,EAAKa,EAASG,EAAO,EAAI,CAAC,GAAK,MAAqB,CAC1IhG,EAAQgF,EAAKa,EAASG,EAAO,EAAI,CAAC,EAClC,SAASJ,EAGb,KAAOG,EAAMC,GAAO,CAChB,IAAIC,EAAOF,EAAMC,GAAS,EACtBnE,EAAQgE,EAASI,GAAOA,GAAO,GAC/BrB,EAAOI,EAAKnD,CAAK,EAAGgD,EAAKG,EAAKnD,EAAQ,CAAC,GAAK,MAChD,GAAIC,EAAO8C,EACPoB,EAAOC,UACFnE,GAAQ+C,EACbkB,EAAME,EAAM,MACX,CACDjG,EAAQgF,EAAKnD,EAAQ,CAAC,EACtB0B,EAAM,QAAO,EACb,SAASqC,GAGjB,MAER,CACA,SAASM,EAAWlB,EAAMnE,EAAOW,EAAM,CACnC,QAASd,EAAIG,EAAOiB,GAAOA,EAAOkD,EAAKtE,CAAC,IAAM,MAAqBA,IAC/D,GAAIoB,GAAQN,EACR,OAAOd,EAAIG,EACnB,MAAO,EACX,CACA,SAASiF,EAAUxB,EAAO6B,EAAMC,EAAWC,EAAa,CACpD,IAAIC,EAAQJ,EAAWE,EAAWC,EAAaF,CAAI,EACnD,OAAOG,EAAQ,GAAKJ,EAAWE,EAAWC,EAAa/B,CAAK,EAAIgC,CACpE,CAGA,MAAMC,EAAU,OAAO,QAAW,KAAe,QAAQ,KAAO,YAAY,KAAK,GAAY,GAAG,EAChG,IAAIC,EAAW,KACf,IAAIC,GACH,SAAUA,EAAQ,CACfA,EAAOA,EAAO,OAAY,EAAE,EAAI,QACpC,GAAGA,IAAWA,EAAS,CAAE,EAAC,EAC1B,SAASC,EAAMC,EAAMzG,EAAK0G,EAAM,CAC5B,IAAIC,EAASF,EAAK,OAAOG,EAAS,gBAAgB,EAElD,IADAD,EAAO,OAAO3G,CAAG,IAEb,GAAI,EAAE0G,EAAO,EAAIC,EAAO,YAAY3G,CAAG,EAAI2G,EAAO,WAAW3G,CAAG,GAC5D,OAAS,CACL,IAAK0G,EAAO,EAAIC,EAAO,GAAK3G,EAAM2G,EAAO,KAAO3G,IAAQ,CAAC2G,EAAO,KAAK,QACjE,OAAOD,EAAO,EAAI,KAAK,IAAI,EAAG,KAAK,IAAIC,EAAO,GAAK,EAAG3G,EAAM,EAAE,CAAqB,EAC7E,KAAK,IAAIyG,EAAK,OAAQ,KAAK,IAAIE,EAAO,KAAO,EAAG3G,EAAM,EAAuB,CAAA,EACvF,GAAI0G,EAAO,EAAIC,EAAO,YAAa,EAAGA,EAAO,YAAa,EACtD,MACJ,GAAI,CAACA,EAAO,OAAQ,EAChB,OAAOD,EAAO,EAAI,EAAID,EAAK,OAG/C,CACA,MAAMI,CAAe,CACjB,YAAYC,EAAWC,EAAS,CAC5B,KAAK,UAAYD,EACjB,KAAK,QAAUC,EACf,KAAK,EAAI,EACT,KAAK,SAAW,KAChB,KAAK,SAAW,GAChB,KAAK,OAAS,GACd,KAAK,MAAQ,GACb,KAAK,MAAQ,GACb,KAAK,MAAQ,GACb,KAAK,aAAY,CACpB,CACD,cAAe,CACX,IAAIC,EAAK,KAAK,SAAW,KAAK,GAAK,KAAK,UAAU,OAAS,KAAO,KAAK,UAAU,KAAK,GAAG,EACzF,GAAIA,EAAI,CAGJ,IAFA,KAAK,SAAWA,EAAG,UAAYR,EAAMQ,EAAG,KAAMA,EAAG,KAAOA,EAAG,OAAQ,CAAC,EAAIA,EAAG,OAASA,EAAG,KACvF,KAAK,OAASA,EAAG,QAAUR,EAAMQ,EAAG,KAAMA,EAAG,GAAKA,EAAG,OAAQ,EAAE,EAAIA,EAAG,OAASA,EAAG,GAC3E,KAAK,MAAM,QACd,KAAK,MAAM,MACX,KAAK,MAAM,MACX,KAAK,MAAM,MAEf,KAAK,MAAM,KAAKA,EAAG,IAAI,EACvB,KAAK,MAAM,KAAK,CAACA,EAAG,MAAM,EAC1B,KAAK,MAAM,KAAK,CAAC,EACjB,KAAK,UAAY,KAAK,cAGtB,KAAK,UAAY,GAExB,CAED,OAAOhH,EAAK,CACR,GAAIA,EAAM,KAAK,UACX,OAAO,KACX,KAAO,KAAK,UAAY,KAAK,QAAUA,GACnC,KAAK,aAAY,EACrB,GAAI,CAAC,KAAK,SACN,OAAO,KACX,OAAS,CACL,IAAI6C,EAAO,KAAK,MAAM,OAAS,EAC/B,GAAIA,EAAO,EACP,YAAK,aAAY,EACV,KAEX,IAAInB,EAAM,KAAK,MAAMmB,CAAI,EAAGlB,EAAQ,KAAK,MAAMkB,CAAI,EACnD,GAAIlB,GAASD,EAAI,SAAS,OAAQ,CAC9B,KAAK,MAAM,MACX,KAAK,MAAM,MACX,KAAK,MAAM,MACX,SAEJ,IAAIE,EAAOF,EAAI,SAASC,CAAK,EACzBhB,EAAQ,KAAK,MAAMkC,CAAI,EAAInB,EAAI,UAAUC,CAAK,EAClD,GAAIhB,EAAQX,EACR,YAAK,UAAYW,EACV,KAEX,GAAIiB,aAAgBqF,EAAM,CACtB,GAAItG,GAASX,EAAK,CACd,GAAIW,EAAQ,KAAK,SACb,OAAO,KACX,IAAIY,EAAMZ,EAAQiB,EAAK,OACvB,GAAIL,GAAO,KAAK,OAAQ,CACpB,IAAIlB,EAAYuB,EAAK,KAAKsF,EAAS,SAAS,EAC5C,GAAI,CAAC7G,GAAakB,EAAMlB,EAAY,KAAK,SAAS,GAC9C,OAAOuB,GAGnB,KAAK,MAAMiB,CAAI,IACXlC,EAAQiB,EAAK,QAAU,KAAK,IAAI,KAAK,SAAU5B,CAAG,IAClD,KAAK,MAAM,KAAK4B,CAAI,EACpB,KAAK,MAAM,KAAKjB,CAAK,EACrB,KAAK,MAAM,KAAK,CAAC,QAIrB,KAAK,MAAMkC,CAAI,IACf,KAAK,UAAYlC,EAAQiB,EAAK,OAGzC,CACL,CACA,MAAMuF,CAAW,CACb,YAAYnG,EAAQoG,EAAQ,CACxB,KAAK,OAASA,EACd,KAAK,OAAS,GACd,KAAK,UAAY,KACjB,KAAK,QAAU,GACf,KAAK,OAASpG,EAAO,WAAW,IAAIT,GAAK,IAAIoD,CAAW,CAC3D,CACD,WAAW9D,EAAO,CACd,IAAIwH,EAAc,EACdC,EAAO,KACP,CAAE,OAAAtG,CAAM,EAAKnB,EAAM,EAAG,CAAE,WAAA0H,CAAY,EAAGvG,EACvCwG,EAAOxG,EAAO,UAAUnB,EAAM,MAAO,GACrCiD,EAAUjD,EAAM,WAAaA,EAAM,WAAW,KAAO,EACrDQ,EAAY,EAChB,QAASG,EAAI,EAAGA,EAAI+G,EAAW,OAAQ/G,IAAK,CACxC,GAAM,KAAKA,EAAKgH,GACZ,SACJ,IAAIC,EAAYF,EAAW/G,CAAC,EAAG4D,EAAQ,KAAK,OAAO5D,CAAC,EACpD,GAAI,EAAA8G,GAAQ,CAACG,EAAU,aAEnBA,EAAU,YAAcrD,EAAM,OAASvE,EAAM,KAAOuE,EAAM,MAAQoD,GAAQpD,EAAM,SAAWtB,KAC3F,KAAK,kBAAkBsB,EAAOqD,EAAW5H,CAAK,EAC9CuE,EAAM,KAAOoD,EACbpD,EAAM,QAAUtB,GAEhBsB,EAAM,UAAYA,EAAM,IAAM,KAC9B/D,EAAY,KAAK,IAAI+D,EAAM,UAAW/D,CAAS,GAC/C+D,EAAM,OAAS,GAAkB,CACjC,IAAIsD,EAAaL,EAIjB,GAHIjD,EAAM,SAAW,KACjBiD,EAAc,KAAK,WAAWxH,EAAOuE,EAAM,SAAUA,EAAM,IAAKiD,CAAW,GAC/EA,EAAc,KAAK,WAAWxH,EAAOuE,EAAM,MAAOA,EAAM,IAAKiD,CAAW,EACpE,CAACI,EAAU,SACXH,EAAOlD,EACHiD,EAAcK,GACd,OAIhB,KAAO,KAAK,QAAQ,OAASL,GACzB,KAAK,QAAQ,MACjB,OAAIhH,GACAR,EAAM,aAAaQ,CAAS,EAC5B,CAACiH,GAAQzH,EAAM,KAAO,KAAK,OAAO,MAClCyH,EAAO,IAAI3D,EACX2D,EAAK,MAAQzH,EAAM,EAAE,OAAO,QAC5ByH,EAAK,MAAQA,EAAK,IAAMzH,EAAM,IAC9BwH,EAAc,KAAK,WAAWxH,EAAOyH,EAAK,MAAOA,EAAK,IAAKD,CAAW,GAE1E,KAAK,UAAYC,EACV,KAAK,OACf,CACD,aAAazH,EAAO,CAChB,GAAI,KAAK,UACL,OAAO,KAAK,UAChB,IAAIyH,EAAO,IAAI3D,EAAa,CAAE,IAAA3D,EAAK,EAAAJ,CAAG,EAAGC,EACzC,OAAAyH,EAAK,MAAQtH,EACbsH,EAAK,IAAM,KAAK,IAAItH,EAAM,EAAGJ,EAAE,OAAO,GAAG,EACzC0H,EAAK,MAAQtH,GAAOJ,EAAE,OAAO,IAAMA,EAAE,OAAO,QAAU,EAC/C0H,CACV,CACD,kBAAkBlD,EAAOqD,EAAW5H,EAAO,CACvC,IAAIc,EAAQ,KAAK,OAAO,QAAQd,EAAM,GAAG,EAEzC,GADA4H,EAAU,MAAM,KAAK,OAAO,MAAM9G,EAAOyD,CAAK,EAAGvE,CAAK,EAClDuE,EAAM,MAAQ,GAAI,CAClB,GAAI,CAAE,OAAApD,CAAM,EAAKnB,EAAM,EACvB,QAASW,EAAI,EAAGA,EAAIQ,EAAO,YAAY,OAAQR,IAC3C,GAAIQ,EAAO,YAAYR,CAAC,GAAK4D,EAAM,MAAO,CACtC,IAAI5B,EAASxB,EAAO,aAAaR,CAAC,EAAE,KAAK,OAAO,KAAK4D,EAAM,MAAOA,EAAM,GAAG,EAAGvE,CAAK,EACnF,GAAI2C,GAAU,GAAK3C,EAAM,EAAE,OAAO,QAAQ,OAAO2C,GAAU,CAAC,EAAG,CACtDA,EAAS,EAGV4B,EAAM,SAAW5B,GAAU,EAF3B4B,EAAM,MAAQ5B,GAAU,EAG5B,aAKZ4B,EAAM,MAAQ,EACdA,EAAM,IAAM,KAAK,OAAO,QAAQzD,EAAQ,CAAC,CAEhD,CACD,UAAUC,EAAQwD,EAAO7C,EAAKI,EAAO,CAEjC,QAASnB,EAAI,EAAGA,EAAImB,EAAOnB,GAAK,EAC5B,GAAI,KAAK,QAAQA,CAAC,GAAKI,EACnB,OAAOe,EACf,YAAK,QAAQA,GAAO,EAAIf,EACxB,KAAK,QAAQe,GAAO,EAAIyC,EACxB,KAAK,QAAQzC,GAAO,EAAIJ,EACjBI,CACV,CACD,WAAW9B,EAAOuE,EAAO7C,EAAKI,EAAO,CACjC,GAAI,CAAE,MAAA7B,CAAK,EAAKD,EAAO,CAAE,OAAAmB,CAAM,EAAKnB,EAAM,EAAG,CAAE,KAAAiF,CAAM,EAAG9D,EACxD,QAAS2G,EAAM,EAAGA,EAAM,EAAGA,IACvB,QAASnH,EAAIQ,EAAO,UAAUlB,EAAO6H,EAAM,EAA0B,CAAC,GAA6BnH,GAAK,EAAG,CACvG,GAAIsE,EAAKtE,CAAC,GAAK,MACX,GAAIsE,EAAKtE,EAAI,CAAC,GAAK,EACfA,EAAIoH,EAAK9C,EAAMtE,EAAI,CAAC,MAEnB,CACGmB,GAAS,GAAKmD,EAAKtE,EAAI,CAAC,GAAK,IAC7BmB,EAAQ,KAAK,UAAUiG,EAAK9C,EAAMtE,EAAI,CAAC,EAAG4D,EAAO7C,EAAKI,CAAK,GAC/D,MAGJmD,EAAKtE,CAAC,GAAK4D,IACXzC,EAAQ,KAAK,UAAUiG,EAAK9C,EAAMtE,EAAI,CAAC,EAAG4D,EAAO7C,EAAKI,CAAK,GAGvE,OAAOA,CACV,CACL,CACA,IAAIkG,GACH,SAAUA,EAAK,CACZA,EAAIA,EAAI,SAAc,CAAC,EAAI,WAC3BA,EAAIA,EAAI,oBAAyB,CAAC,EAAI,sBAGtCA,EAAIA,EAAI,qBAA0B,GAAG,EAAI,uBACzCA,EAAIA,EAAI,iBAAsB,EAAE,EAAI,mBAIpCA,EAAIA,EAAI,SAAc,IAAK,EAAI,WAC/BA,EAAIA,EAAI,MAAW,GAAI,EAAI,QAC3BA,EAAIA,EAAI,iCAAsC,GAAG,EAAI,mCAIrDA,EAAIA,EAAI,cAAmB,EAAE,EAAI,eACrC,GAAGA,IAAQA,EAAM,CAAE,EAAC,EACpB,MAAMC,CAAM,CACR,YAAY9G,EAAQqC,EAAOyD,EAAWhD,EAAQ,CAC1C,KAAK,OAAS9C,EACd,KAAK,MAAQqC,EACb,KAAK,OAASS,EACd,KAAK,WAAa,EAClB,KAAK,YAAc,KACnB,KAAK,YAAc,EACnB,KAAK,OAAS,GACd,KAAK,UAAY,KACjB,KAAK,sBAAwB,GAC7B,KAAK,qBAAuB,EAC5B,KAAK,kBAAoB,EACzB,KAAK,OAAS,IAAID,EAAYR,EAAOS,CAAM,EAC3C,KAAK,OAAS,IAAIqD,EAAWnG,EAAQ,KAAK,MAAM,EAChD,KAAK,QAAUA,EAAO,IAAI,CAAC,EAC3B,GAAI,CAAE,KAAA0D,CAAI,EAAKZ,EAAO,CAAC,EACvB,KAAK,OAAS,CAACnE,EAAM,MAAM,KAAMqB,EAAO,IAAI,CAAC,EAAG0D,CAAI,CAAC,EACrD,KAAK,UAAYoC,EAAU,QAAU,KAAK,OAAO,IAAMpC,EAAO1D,EAAO,aAAe,EAC9E,IAAI6F,EAAeC,EAAW9F,EAAO,OAAO,EAAI,IACzD,CACD,IAAI,WAAY,CACZ,OAAO,KAAK,WACf,CAOD,SAAU,CACN,IAAI+G,EAAS,KAAK,OAAQ/H,EAAM,KAAK,YAEjCgI,EAAY,KAAK,OAAS,GAC1BC,EAASC,EAQb,GAAI,KAAK,kBAAoB,KAAkDH,EAAO,QAAU,EAAG,CAC/F,GAAI,CAACzF,CAAC,EAAIyF,EACV,KAAOzF,EAAE,YAAa,GAAIA,EAAE,MAAM,QAAUA,EAAE,MAAMA,EAAE,MAAM,OAAS,CAAC,GAAK,KAAK,uBAAuB,CACvG,KAAK,kBAAoB,KAAK,qBAAuB,EAKzD,QAAS9B,EAAI,EAAGA,EAAIuH,EAAO,OAAQvH,IAAK,CACpC,IAAIX,EAAQkI,EAAOvH,CAAC,EACpB,OAAS,CAEL,GADA,KAAK,OAAO,UAAY,KACpBX,EAAM,IAAMG,EACZgI,EAAU,KAAKnI,CAAK,MAEnB,IAAI,KAAK,aAAaA,EAAOmI,EAAWD,CAAM,EAC/C,SAEC,CACIE,IACDA,EAAU,CAAA,EACVC,EAAgB,CAAA,GAEpBD,EAAQ,KAAKpI,CAAK,EAClB,IAAIsI,EAAM,KAAK,OAAO,aAAatI,CAAK,EACxCqI,EAAc,KAAKC,EAAI,MAAOA,EAAI,GAAG,GAEzC,OAGR,GAAI,CAACH,EAAU,OAAQ,CACnB,IAAII,EAAWH,GAAWI,EAAaJ,CAAO,EAC9C,GAAIG,EACA,OAAO,KAAK,YAAYA,CAAQ,EACpC,GAAI,KAAK,OAAO,OACZ,MAAI/B,GAAW4B,GACX,QAAQ,IAAI,qBAAuB,KAAK,OAAO,UAAY,KAAK,OAAO,QAAQ,KAAK,OAAO,UAAU,KAAK,EAAI,OAAO,EACnH,IAAI,YAAY,eAAiBjI,CAAG,EAEzC,KAAK,aACN,KAAK,WAAa,GAE1B,GAAI,KAAK,YAAciI,EAAS,CAC5B,IAAIG,EAAW,KAAK,WAAa,MAAQH,EAAQ,CAAC,EAAE,IAAM,KAAK,UAAYA,EAAQ,CAAC,EAC9E,KAAK,YAAYA,EAASC,EAAeF,CAAS,EACxD,GAAII,EACA,OAAO,KAAK,YAAYA,EAAS,SAAU,CAAA,EAEnD,GAAI,KAAK,WAAY,CACjB,IAAIE,EAAe,KAAK,YAAc,EAAI,EAAI,KAAK,WAAa,EAChE,GAAIN,EAAU,OAASM,EAEnB,IADAN,EAAU,KAAK,CAACO,EAAGC,IAAMA,EAAE,MAAQD,EAAE,KAAK,EACnCP,EAAU,OAASM,GACtBN,EAAU,IAAG,EAEjBA,EAAU,KAAK1F,GAAKA,EAAE,UAAYtC,CAAG,GACrC,KAAK,qBAEJgI,EAAU,OAAS,EAAG,CAI3BS,EAAO,QAASjI,EAAI,EAAGA,EAAIwH,EAAU,OAAS,EAAGxH,IAAK,CAClD,IAAIX,EAAQmI,EAAUxH,CAAC,EACvB,QAASkI,EAAIlI,EAAI,EAAGkI,EAAIV,EAAU,OAAQU,IAAK,CAC3C,IAAI/F,EAAQqF,EAAUU,CAAC,EACvB,GAAI7I,EAAM,UAAU8C,CAAK,GACrB9C,EAAM,OAAO,OAAS,KAAsC8C,EAAM,OAAO,OAAS,IAClF,IAAM9C,EAAM,MAAQ8C,EAAM,OAAW9C,EAAM,OAAO,OAAS8C,EAAM,OAAO,QAAW,EAC/EqF,EAAU,OAAOU,IAAK,CAAC,MAEtB,CACDV,EAAU,OAAOxH,IAAK,CAAC,EACvB,SAASiI,IAKrBT,EAAU,OAAS,IACnBA,EAAU,OAAO,GAA4BA,EAAU,OAAS,EAAE,EAE1E,KAAK,YAAcA,EAAU,CAAC,EAAE,IAChC,QAASxH,EAAI,EAAGA,EAAIwH,EAAU,OAAQxH,IAC9BwH,EAAUxH,CAAC,EAAE,IAAM,KAAK,cACxB,KAAK,YAAcwH,EAAUxH,CAAC,EAAE,KACxC,OAAO,IACV,CACD,OAAOR,EAAK,CACR,GAAI,KAAK,WAAa,MAAQ,KAAK,UAAYA,EAC3C,MAAM,IAAI,WAAW,8BAA8B,EACvD,KAAK,UAAYA,CACpB,CAKD,aAAaH,EAAOkI,EAAQY,EAAO,CAC/B,IAAIhI,EAAQd,EAAM,IAAK,CAAE,OAAAmB,CAAM,EAAK,KAChCE,EAAOmF,EAAU,KAAK,QAAQxG,CAAK,EAAI,OAAS,GACpD,GAAI,KAAK,WAAa,MAAQc,EAAQ,KAAK,UACvC,OAAOd,EAAM,cAAgBA,EAAQ,KACzC,GAAI,KAAK,UAAW,CAChB,IAAI+I,EAAW/I,EAAM,YAAcA,EAAM,WAAW,QAAQ,OAAQgJ,EAASD,EAAW/I,EAAM,WAAW,KAAO,EAChH,QAASiJ,EAAS,KAAK,UAAU,OAAOnI,CAAK,EAAGmI,GAAS,CACrD,IAAIC,EAAQ,KAAK,OAAO,QAAQ,MAAMD,EAAO,KAAK,EAAE,GAAKA,EAAO,KAAO9H,EAAO,QAAQnB,EAAM,MAAOiJ,EAAO,KAAK,EAAE,EAAI,GACrH,GAAIC,EAAQ,IAAMD,EAAO,SAAW,CAACF,IAAaE,EAAO,KAAK5B,EAAS,WAAW,GAAK,IAAM2B,GACzF,OAAAhJ,EAAM,QAAQiJ,EAAQC,CAAK,EACvB1C,GACA,QAAQ,IAAInF,EAAO,KAAK,QAAQrB,CAAK,EAAI,kBAAkBmB,EAAO,QAAQ8H,EAAO,KAAK,EAAE,IAAI,EACzF,GAEX,GAAI,EAAEA,aAAkB7B,IAAS6B,EAAO,SAAS,QAAU,GAAKA,EAAO,UAAU,CAAC,EAAI,EAClF,MACJ,IAAIE,EAAQF,EAAO,SAAS,CAAC,EAC7B,GAAIE,aAAiB/B,GAAQ6B,EAAO,UAAU,CAAC,GAAK,EAChDA,EAASE,MAET,QAGZ,IAAIC,EAAgBjI,EAAO,UAAUnB,EAAM,MAAO,GAClD,GAAIoJ,EAAgB,EAChB,OAAApJ,EAAM,OAAOoJ,CAAa,EACtB5C,GACA,QAAQ,IAAInF,EAAO,KAAK,QAAQrB,CAAK,EAAI,uBAAuBmB,EAAO,QAAQiI,EAAgB,KAAK,IAA2B,EAC5H,GAEX,GAAIpJ,EAAM,MAAM,QAAU,KACtB,KAAOA,EAAM,MAAM,OAAS,KAAwBA,EAAM,YAAW,GAAI,CAE7E,IAAIqJ,EAAU,KAAK,OAAO,WAAWrJ,CAAK,EAC1C,QAASW,EAAI,EAAGA,EAAI0I,EAAQ,QAAS,CACjC,IAAItI,EAASsI,EAAQ1I,GAAG,EAAGc,EAAO4H,EAAQ1I,GAAG,EAAGe,EAAM2H,EAAQ1I,GAAG,EAC7DqC,EAAOrC,GAAK0I,EAAQ,QAAU,CAACP,EAC/BQ,EAAatG,EAAOhD,EAAQA,EAAM,MAAK,EAK3C,GAJAsJ,EAAW,MAAMvI,EAAQU,EAAMC,CAAG,EAC9B8E,GACA,QAAQ,IAAInF,EAAO,KAAK,QAAQiI,CAAU,EAAI,SAAUvI,EAAS,MAC3D,aAAaI,EAAO,QAAQJ,EAAS,SAD4D,eACrBI,EAAO,QAAQM,CAAI,OAAOX,IAAQwI,GAActJ,EAAQ,GAAK,YAAY,EAC3JgD,EACA,MAAO,GACFsG,EAAW,IAAMxI,EACtBoH,EAAO,KAAKoB,CAAU,EAEtBR,EAAM,KAAKQ,CAAU,EAE7B,MAAO,EACV,CAID,aAAatJ,EAAOmI,EAAW,CAC3B,IAAIhI,EAAMH,EAAM,IAChB,OAAS,CACL,GAAI,CAAC,KAAK,aAAaA,EAAO,KAAM,IAAI,EACpC,MAAO,GACX,GAAIA,EAAM,IAAMG,EACZ,OAAAoJ,EAAevJ,EAAOmI,CAAS,EACxB,GAGlB,CACD,YAAYD,EAAQsB,EAAQrB,EAAW,CACnC,IAAII,EAAW,KAAMkB,EAAY,GACjC,QAAS9I,EAAI,EAAGA,EAAIuH,EAAO,OAAQvH,IAAK,CACpC,IAAIX,EAAQkI,EAAOvH,CAAC,EAAG4D,EAAQiF,EAAO7I,GAAK,CAAC,EAAG+I,EAAWF,GAAQ7I,GAAK,GAAK,CAAC,EACzEU,EAAOmF,EAAU,KAAK,QAAQxG,CAAK,EAAI,OAAS,GACpD,GAAIA,EAAM,UACFyJ,IAEJA,EAAY,GACZzJ,EAAM,QAAO,EACTwG,GACA,QAAQ,IAAInF,EAAO,KAAK,QAAQrB,CAAK,EAAI,cAAc,EAChD,KAAK,aAAaA,EAAOmI,CAAS,IAEzC,SAER,IAAIwB,EAAQ3J,EAAM,MAAK,EAAI4J,EAAYvI,EACvC,QAASwH,EAAI,EAAGc,EAAM,YAAW,GAAMd,EAAI,KACnCrC,GACA,QAAQ,IAAIoD,EAAY,KAAK,QAAQD,CAAK,EAAI,qBAAqB,EAC5D,MAAK,aAAaA,EAAOxB,CAAS,GAHyBU,IAMlErC,IACAoD,EAAY,KAAK,QAAQD,CAAK,EAAI,QAE1C,QAASE,KAAU7J,EAAM,gBAAgBuE,CAAK,EACtCiC,GACA,QAAQ,IAAInF,EAAO,KAAK,QAAQwI,CAAM,EAAI,uBAAuB,EACrE,KAAK,aAAaA,EAAQ1B,CAAS,EAEnC,KAAK,OAAO,IAAMnI,EAAM,KACpB0J,GAAY1J,EAAM,MAClB0J,IACAnF,EAAQ,GAEZvE,EAAM,gBAAgBuE,EAAOmF,CAAQ,EACjClD,GACA,QAAQ,IAAInF,EAAO,KAAK,QAAQrB,CAAK,EAAI,wBAAwB,KAAK,OAAO,QAAQuE,CAAK,IAAI,EAClGgF,EAAevJ,EAAOmI,CAAS,IAE1B,CAACI,GAAYA,EAAS,MAAQvI,EAAM,SACzCuI,EAAWvI,GAGnB,OAAOuI,CACV,CAED,YAAYvI,EAAO,CACf,OAAAA,EAAM,MAAK,EACJoH,EAAK,MAAM,CAAE,OAAQ9D,EAAkB,OAAOtD,CAAK,EACtD,QAAS,KAAK,OAAO,QACrB,MAAO,KAAK,QACZ,gBAAiB,KAAK,OAAO,aAC7B,OAAQ,KAAK,OACb,MAAO,KAAK,OAAO,CAAC,EAAE,KACtB,OAAQA,EAAM,IAAM,KAAK,OAAO,CAAC,EAAE,KACnC,cAAe,KAAK,OAAO,aAAe,CAAA,CACjD,CACD,QAAQA,EAAO,CACX,IAAIkF,GAAMuB,IAAaA,EAAW,IAAI,UAAU,IAAIzG,CAAK,EACzD,OAAKkF,GACDuB,EAAS,IAAIzG,EAAOkF,EAAK,OAAO,cAAc,KAAK,aAAa,CAAC,EAC9DA,EAAKlF,CACf,CACL,CACA,SAASuJ,EAAevJ,EAAOmI,EAAW,CACtC,QAASxH,EAAI,EAAGA,EAAIwH,EAAU,OAAQxH,IAAK,CACvC,IAAImC,EAAQqF,EAAUxH,CAAC,EACvB,GAAImC,EAAM,KAAO9C,EAAM,KAAO8C,EAAM,UAAU9C,CAAK,EAAG,CAC9CmI,EAAUxH,CAAC,EAAE,MAAQX,EAAM,QAC3BmI,EAAUxH,CAAC,EAAIX,GACnB,QAGRmI,EAAU,KAAKnI,CAAK,CACxB,CACA,MAAM8J,CAAQ,CACV,YAAYC,EAAQC,EAAOC,EAAU,CACjC,KAAK,OAASF,EACd,KAAK,MAAQC,EACb,KAAK,SAAWC,CACnB,CACD,OAAOxI,EAAM,CAAE,MAAO,CAAC,KAAK,UAAY,KAAK,SAASA,CAAI,GAAK,CAAI,CACvE,CACA,MAAMyD,EAAKgF,GAAKA,EAWhB,MAAMC,EAAe,CAEjB,YAAYC,EAAM,CACd,KAAK,MAAQA,EAAK,MAClB,KAAK,MAAQA,EAAK,OAASlF,EAC3B,KAAK,OAASkF,EAAK,QAAUlF,EAC7B,KAAK,MAAQkF,EAAK,OAASlF,EAC3B,KAAK,KAAOkF,EAAK,OAAS,IAAM,GAChC,KAAK,OAASA,EAAK,SAAW,EACjC,CACL,CAIA,MAAMC,UAAiBC,CAAO,CAE1B,YAAYF,EAAM,CAId,GAHA,QAEA,KAAK,SAAW,GACZA,EAAK,SAAW,GAChB,MAAM,IAAI,WAAW,mBAAmBA,EAAK,6CAAmE,EACpH,IAAIG,EAAYH,EAAK,UAAU,MAAM,GAAG,EACxC,KAAK,cAAgBG,EAAU,OAC/B,QAAS5J,EAAI,EAAGA,EAAIyJ,EAAK,gBAAiBzJ,IACtC4J,EAAU,KAAK,EAAE,EACrB,IAAIC,EAAW,OAAO,KAAKJ,EAAK,QAAQ,EAAE,IAAIrF,GAAKqF,EAAK,SAASrF,CAAC,EAAE,CAAC,CAAC,EAClE0F,EAAY,CAAA,EAChB,QAAS9J,EAAI,EAAGA,EAAI4J,EAAU,OAAQ5J,IAClC8J,EAAU,KAAK,CAAA,CAAE,EACrB,SAASC,EAAQC,EAAQC,EAAM1I,EAAO,CAClCuI,EAAUE,CAAM,EAAE,KAAK,CAACC,EAAMA,EAAK,YAAY,OAAO1I,CAAK,CAAC,CAAC,CAAC,CACjE,CACD,GAAIkI,EAAK,UACL,QAASS,KAAYT,EAAK,UAAW,CACjC,IAAIQ,EAAOC,EAAS,CAAC,EACjB,OAAOD,GAAQ,WACfA,EAAOvD,EAASuD,CAAI,GACxB,QAASjK,EAAI,EAAGA,EAAIkK,EAAS,QAAS,CAClC,IAAI9I,EAAO8I,EAASlK,GAAG,EACvB,GAAIoB,GAAQ,EACR2I,EAAQ3I,EAAM6I,EAAMC,EAASlK,GAAG,CAAC,MAEhC,CACD,IAAIuB,EAAQ2I,EAASlK,EAAI,CAACoB,CAAI,EAC9B,QAAS8G,EAAI,CAAC9G,EAAM8G,EAAI,EAAGA,IACvB6B,EAAQG,EAASlK,GAAG,EAAGiK,EAAM1I,CAAK,EACtCvB,MAIhB,KAAK,QAAU,IAAImK,EAAQP,EAAU,IAAI,CAACQ,EAAMpK,IAAMqK,EAAS,OAAO,CAClE,KAAMrK,GAAK,KAAK,cAAgB,OAAYoK,EAC5C,GAAIpK,EACJ,MAAO8J,EAAU9J,CAAC,EAClB,IAAK6J,EAAS,QAAQ7J,CAAC,EAAI,GAC3B,MAAOA,GAAK,EACZ,QAASyJ,EAAK,cAAgBA,EAAK,aAAa,QAAQzJ,CAAC,EAAI,EAChE,CAAA,CAAC,CAAC,EACCyJ,EAAK,cACL,KAAK,QAAU,KAAK,QAAQ,OAAO,GAAGA,EAAK,WAAW,GAC1D,KAAK,OAAS,GACd,KAAK,aAAea,EACpB,IAAIC,EAAa3H,EAAY6G,EAAK,SAAS,EAC3C,KAAK,QAAUA,EAAK,QACpB,KAAK,iBAAmBA,EAAK,aAAe,CAAA,EAC5C,KAAK,YAAc,IAAI,YAAY,KAAK,iBAAiB,MAAM,EAC/D,QAASzJ,EAAI,EAAGA,EAAI,KAAK,iBAAiB,OAAQA,IAC9C,KAAK,YAAYA,CAAC,EAAI,KAAK,iBAAiBA,CAAC,EAAE,KACnD,KAAK,aAAe,KAAK,iBAAiB,IAAIwK,CAAc,EAC5D,KAAK,OAAS5H,EAAY6G,EAAK,OAAQ,WAAW,EAClD,KAAK,KAAO7G,EAAY6G,EAAK,SAAS,EACtC,KAAK,KAAO7G,EAAY6G,EAAK,IAAI,EACjC,KAAK,QAAUA,EAAK,QACpB,KAAK,WAAaA,EAAK,WAAW,IAAIlI,GAAS,OAAOA,GAAS,SAAW,IAAI8C,EAAWkG,EAAYhJ,CAAK,EAAIA,CAAK,EACnH,KAAK,SAAWkI,EAAK,SACrB,KAAK,SAAWA,EAAK,UAAY,CAAA,EACjC,KAAK,mBAAqBA,EAAK,oBAAsB,KACrD,KAAK,eAAiBA,EAAK,UAC3B,KAAK,UAAYA,EAAK,WAAa,KACnC,KAAK,QAAU,KAAK,QAAQ,MAAM,OAAS,EAC3C,KAAK,QAAU,KAAK,eACpB,KAAK,IAAM,KAAK,SAAS,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC,CAAC,CACzD,CACD,YAAY5G,EAAOyD,EAAWhD,EAAQ,CAClC,IAAImH,EAAQ,IAAInD,EAAM,KAAMzE,EAAOyD,EAAWhD,CAAM,EACpD,QAASoH,KAAK,KAAK,SACfD,EAAQC,EAAED,EAAO5H,EAAOyD,EAAWhD,CAAM,EAC7C,OAAOmH,CACV,CAED,QAAQnL,EAAOwB,EAAM6J,EAAQ,GAAO,CAChC,IAAIC,EAAQ,KAAK,KACjB,GAAI9J,GAAQ8J,EAAM,CAAC,EACf,MAAO,GACX,QAASpL,EAAMoL,EAAM9J,EAAO,CAAC,IAAK,CAC9B,IAAI+J,EAAWD,EAAMpL,GAAK,EAAG6C,EAAOwI,EAAW,EAC3C3I,EAAS0I,EAAMpL,GAAK,EACxB,GAAI6C,GAAQsI,EACR,OAAOzI,EACX,QAASnB,EAAMvB,GAAOqL,GAAY,GAAIrL,EAAMuB,EAAKvB,IAC7C,GAAIoL,EAAMpL,CAAG,GAAKF,EACd,OAAO4C,EACf,GAAIG,EACA,MAAO,GAElB,CAED,UAAU/C,EAAOwL,EAAU,CACvB,IAAIxG,EAAO,KAAK,KAChB,QAAS6C,EAAM,EAAGA,EAAM,EAAGA,IACvB,QAASnH,EAAI,KAAK,UAAUV,EAAO6H,EAAM,EAA0B,CAA2B,EAAE/F,GAAOpB,GAAK,EAAG,CAC3G,IAAKoB,EAAOkD,EAAKtE,CAAC,IAAM,MACpB,GAAIsE,EAAKtE,EAAI,CAAC,GAAK,EACfoB,EAAOkD,EAAKtE,EAAIoH,EAAK9C,EAAMtE,EAAI,CAAC,CAAC,MAChC,IAAIsE,EAAKtE,EAAI,CAAC,GAAK,EACpB,OAAOoH,EAAK9C,EAAMtE,EAAI,CAAC,EAEvB,MAER,GAAIoB,GAAQ0J,GAAY1J,GAAQ,EAC5B,OAAOgG,EAAK9C,EAAMtE,EAAI,CAAC,EAGnC,MAAO,EACV,CAED,UAAUV,EAAOyL,EAAM,CACnB,OAAO,KAAK,OAAQzL,EAAQ,EAA2ByL,CAAI,CAC9D,CAED,UAAUzL,EAAO0L,EAAM,CACnB,OAAQ,KAAK,UAAU1L,EAAO,CAAyB,EAAG0L,GAAQ,CACrE,CAED,YAAY1L,EAAOc,EAAQ,CACvB,GAAIA,GAAU,KAAK,UAAUd,EAAO,CAAiC,EACjE,MAAO,GACX,QAASU,EAAI,KAAK,UAAUV,EAAO,CAA2B,GAAGU,GAAK,EAAG,CACrE,GAAI,KAAK,KAAKA,CAAC,GAAK,MAChB,GAAI,KAAK,KAAKA,EAAI,CAAC,GAAK,EACpBA,EAAIoH,EAAK,KAAK,KAAMpH,EAAI,CAAC,MAEzB,OAAO,GAEf,GAAII,GAAUgH,EAAK,KAAK,KAAMpH,EAAI,CAAC,EAC/B,MAAO,GAElB,CAGD,WAAWV,EAAO,CACd,IAAI0C,EAAS,CAAA,EACb,QAAShC,EAAI,KAAK,UAAUV,EAAO,CAA2B,GAAGU,GAAK,EAAG,CACrE,GAAI,KAAK,KAAKA,CAAC,GAAK,MAChB,GAAI,KAAK,KAAKA,EAAI,CAAC,GAAK,EACpBA,EAAIoH,EAAK,KAAK,KAAMpH,EAAI,CAAC,MAEzB,OAER,GAAK,OAAK,KAAKA,EAAI,CAAC,EAAK,GAA4C,CACjE,IAAIuB,EAAQ,KAAK,KAAKvB,EAAI,CAAC,EACtBgC,EAAO,KAAK,CAACD,EAAG/B,IAAOA,EAAI,GAAM+B,GAAKR,CAAK,GAC5CS,EAAO,KAAK,KAAK,KAAKhC,CAAC,EAAGuB,CAAK,GAG3C,OAAOS,CACV,CAID,UAAUiJ,EAAQ,CAGd,IAAIC,EAAO,OAAO,OAAO,OAAO,OAAOxB,EAAS,SAAS,EAAG,IAAI,EAGhE,GAFIuB,EAAO,QACPC,EAAK,QAAU,KAAK,QAAQ,OAAO,GAAGD,EAAO,KAAK,GAClDA,EAAO,IAAK,CACZ,IAAIE,EAAO,KAAK,SAASF,EAAO,GAAG,EACnC,GAAI,CAACE,EACD,MAAM,IAAI,WAAW,yBAAyBF,EAAO,KAAK,EAC9DC,EAAK,IAAMC,EAEf,OAAIF,EAAO,aACPC,EAAK,WAAa,KAAK,WAAW,IAAIE,GAAK,CACvC,IAAIC,EAAQJ,EAAO,WAAW,KAAK7G,GAAKA,EAAE,MAAQgH,CAAC,EACnD,OAAOC,EAAQA,EAAM,GAAKD,CAC1C,CAAa,GACDH,EAAO,eACPC,EAAK,aAAe,KAAK,aAAa,MAAK,EAC3CA,EAAK,iBAAmB,KAAK,iBAAiB,IAAI,CAAC,EAAG,IAAM,CACxD,IAAIG,EAAQJ,EAAO,aAAa,KAAK7G,GAAKA,EAAE,MAAQ,EAAE,QAAQ,EAC9D,GAAI,CAACiH,EACD,OAAO,EACX,IAAI5B,EAAO,OAAO,OAAO,OAAO,OAAO,CAAA,EAAI,CAAC,EAAG,CAAE,SAAU4B,EAAM,EAAI,CAAA,EACrE,OAAAH,EAAK,aAAa,CAAC,EAAIV,EAAef,CAAI,EACnCA,CACvB,CAAa,GAEDwB,EAAO,iBACPC,EAAK,QAAUD,EAAO,gBACtBA,EAAO,UACPC,EAAK,QAAU,KAAK,aAAaD,EAAO,OAAO,GAC/CA,EAAO,QAAU,OACjBC,EAAK,OAASD,EAAO,QACrBA,EAAO,OACPC,EAAK,SAAWA,EAAK,SAAS,OAAOD,EAAO,IAAI,GAChDA,EAAO,cAAgB,OACvBC,EAAK,aAAeD,EAAO,cACxBC,CACV,CAGD,aAAc,CACV,OAAO,KAAK,SAAS,OAAS,CACjC,CAKD,QAAQpK,EAAM,CACV,OAAO,KAAK,UAAY,KAAK,UAAUA,CAAI,EAAI,OAAOA,GAAQ,KAAK,SAAW,KAAK,QAAQ,MAAMA,CAAI,EAAE,MAAQA,CAAI,CACtH,CAGD,IAAI,SAAU,CAAE,OAAO,KAAK,QAAU,CAAI,CAE1C,IAAI,SAAU,CAAE,OAAO,KAAK,QAAQ,MAAM,KAAK,IAAI,CAAC,CAAC,CAAI,CAEzD,kBAAkBA,EAAM,CACpB,IAAIwK,EAAO,KAAK,mBAChB,OAAOA,GAAQ,KAAO,EAAIA,EAAKxK,CAAI,GAAK,CAC3C,CAED,aAAamE,EAAS,CAClB,IAAIsG,EAAS,OAAO,KAAK,KAAK,QAAQ,EAAGlC,EAAQkC,EAAO,IAAI,IAAM,EAAK,EACvE,GAAItG,EACA,QAASuG,KAAQvG,EAAQ,MAAM,GAAG,EAAG,CACjC,IAAIV,EAAKgH,EAAO,QAAQC,CAAI,EACxBjH,GAAM,IACN8E,EAAM9E,CAAE,EAAI,IAExB,IAAI+E,EAAW,KACf,QAAStJ,EAAI,EAAGA,EAAIuL,EAAO,OAAQvL,IAC/B,GAAI,CAACqJ,EAAMrJ,CAAC,EACR,QAASkI,EAAI,KAAK,SAASqD,EAAOvL,CAAC,CAAC,EAAGuE,GAAKA,EAAK,KAAK,KAAK2D,GAAG,IAAM,QAC/DoB,IAAaA,EAAW,IAAI,WAAW,KAAK,QAAU,CAAC,IAAI/E,CAAE,EAAI,EAE9E,OAAO,IAAI4E,EAAQlE,EAASoE,EAAOC,CAAQ,CAC9C,CAGD,OAAO,YAAYG,EAAM,CACrB,OAAO,IAAIC,EAASD,CAAI,CAC3B,CACL,CACA,SAASrC,EAAK9C,EAAM9C,EAAK,CAAE,OAAO8C,EAAK9C,CAAG,EAAK8C,EAAK9C,EAAM,CAAC,GAAK,EAAM,CACtE,SAASqG,EAAaN,EAAQ,CAC1B,IAAI1F,EAAO,KACX,QAASxC,KAASkI,EAAQ,CACtB,IAAIE,EAAUpI,EAAM,EAAE,WACjBA,EAAM,KAAOA,EAAM,EAAE,OAAO,KAAOoI,GAAW,MAAQpI,EAAM,IAAMoI,IACnEpI,EAAM,EAAE,OAAO,UAAUA,EAAM,MAAO,CAA4B,IACjE,CAACwC,GAAQA,EAAK,MAAQxC,EAAM,SAC7BwC,EAAOxC,GAEf,OAAOwC,CACX,CACA,SAAS2I,EAAef,EAAM,CAC1B,GAAIA,EAAK,SAAU,CACf,IAAIzC,EAAOyC,EAAK,OAAS,EAA4B,EACrD,MAAO,CAAClI,EAAOlC,IAAWoK,EAAK,SAASlI,EAAOlC,CAAK,GAAK,EAAK2H,EAElE,OAAOyC,EAAK,GAChB", "x_google_ignoreList": [0]}