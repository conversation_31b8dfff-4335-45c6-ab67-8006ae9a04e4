import{S as X,e as x,s as $,f as F,g as f,h as C,j as B,n as M,k as y,m as H,o as O,p as V,w as m,r as L,u as d,v as S,ak as Pe,F as T,G as E,H as N,B as ne,K as _e,N as z,al as be,af as Ve,C as qe,am as Ue,an as Ze,I as ae,Q as ce,O as Ne,E as Y,R as Ie,M as J,t as Fe,x as Oe,Z as Re,ae as Ge,V as Ke,W as Qe}from"./index-7674dbb6.js";import{u as me,c as We}from"./utils-c3e3db58.js";import{d as Ye}from"./index-2f00b72c.js";import{g as de,B as Je,n as he}from"./Button-770df9ba.js";import{S as Xe}from"./ShareButton-c5d88eaa.js";import{M as xe}from"./StaticMarkdown-0958b32f.js";import{C as $e,a as el}from"./Copy-bc542573.js";import{B as ll}from"./BlockLabel-520e742a.js";import"./IconButton-a4282a0e.js";function tl(t){let e,n,l;return{c(){e=F("svg"),n=F("path"),l=F("path"),f(n,"fill","currentColor"),f(n,"d","M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"),f(l,"fill","currentColor"),f(l,"d","M8 10h16v2H8zm0 6h10v2H8z"),f(e,"xmlns","http://www.w3.org/2000/svg"),f(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),f(e,"aria-hidden","true"),f(e,"role","img"),f(e,"class","iconify iconify--carbon"),f(e,"width","100%"),f(e,"height","100%"),f(e,"preserveAspectRatio","xMidYMid meet"),f(e,"viewBox","0 0 32 32")},m(a,r){C(a,e,r),B(e,n),B(e,l)},p:M,i:M,o:M,d(a){a&&y(e)}}}class al extends X{constructor(e){super(),x(this,e,null,tl,$,{})}}function nl(t){let e,n,l,a;return{c(){e=F("svg"),n=F("path"),l=F("path"),f(n,"stroke","currentColor"),f(n,"stroke-width","1.5"),f(n,"stroke-linecap","round"),f(n,"d","M16.472 3.5H4.1a.6.6 0 0 0-.6.6v9.8a.6.6 0 0 0 .6.6h2.768a2 2 0 0 1 1.715.971l2.71 4.517a1.631 1.631 0 0 0 2.961-1.308l-1.022-3.408a.6.6 0 0 1 .574-.772h4.575a2 2 0 0 0 1.93-2.526l-1.91-7A2 2 0 0 0 16.473 3.5Z"),f(l,"stroke","currentColor"),f(l,"stroke-width","1.5"),f(l,"stroke-linecap","round"),f(l,"stroke-linejoin","round"),f(l,"d","M7 14.5v-11"),f(e,"xmlns","http://www.w3.org/2000/svg"),f(e,"width","15px"),f(e,"height","15px"),f(e,"viewBox","0 0 24 24"),f(e,"fill",a=t[0]?"currentColor":"none"),f(e,"stroke-width","1.5"),f(e,"color","currentColor")},m(r,s){C(r,e,s),B(e,n),B(e,l)},p(r,[s]){s&1&&a!==(a=r[0]?"currentColor":"none")&&f(e,"fill",a)},i:M,o:M,d(r){r&&y(e)}}}function il(t,e,n){let{actioned:l}=e;return t.$$set=a=>{"actioned"in a&&n(0,l=a.actioned)},[l]}class sl extends X{constructor(e){super(),x(this,e,il,nl,$,{actioned:0})}}function rl(t){let e,n,l,a;return{c(){e=F("svg"),n=F("path"),l=F("path"),f(n,"stroke","currentColor"),f(n,"stroke-width","1.5"),f(n,"stroke-linecap","round"),f(n,"d","M16.472 20H4.1a.6.6 0 0 1-.6-.6V9.6a.6.6 0 0 1 .6-.6h2.768a2 2 0 0 0 1.715-.971l2.71-4.517a1.631 1.631 0 0 1 2.961 1.308l-1.022 3.408a.6.6 0 0 0 .574.772h4.575a2 2 0 0 1 1.93 2.526l-1.91 7A2 2 0 0 1 16.473 20Z"),f(l,"stroke","currentColor"),f(l,"stroke-width","1.5"),f(l,"stroke-linecap","round"),f(l,"stroke-linejoin","round"),f(l,"d","M7 20V9"),f(e,"xmlns","http://www.w3.org/2000/svg"),f(e,"width","15px"),f(e,"height","15px"),f(e,"viewBox","0 0 24 24"),f(e,"fill",a=t[0]?"currentColor":"none"),f(e,"stroke-width","1.5"),f(e,"color","currentColor")},m(r,s){C(r,e,s),B(e,n),B(e,l)},p(r,[s]){s&1&&a!==(a=r[0]?"currentColor":"none")&&f(e,"fill",a)},i:M,o:M,d(r){r&&y(e)}}}function ol(t,e,n){let{actioned:l}=e;return t.$$set=a=>{"actioned"in a&&n(0,l=a.actioned)},[l]}class fl extends X{constructor(e){super(),x(this,e,ol,rl,$,{actioned:0})}}const ul=async t=>(await Promise.all(t.map(async n=>await Promise.all(n.map(async(l,a)=>{if(l===null)return"";let r=a===0?"😃":"🤖",s="";if(typeof l=="string"){const o={audio:/<audio.*?src="(\/file=.*?)"/g,video:/<video.*?src="(\/file=.*?)"/g,image:/<img.*?src="(\/file=.*?)".*?\/>|!\[.*?\]\((\/file=.*?)\)/g};s=l;for(let[i,u]of Object.entries(o)){let _;for(;(_=u.exec(l))!==null;){const k=_[1]||_[2],A=await me(k,"url");s=s.replace(k,A)}}}else{const o=await me(l.data,"url");l.mime_type?.includes("audio")?s=`<audio controls src="${o}"></audio>`:l.mime_type?.includes("video")?s=o:l.mime_type?.includes("image")&&(s=`<img src="${o}" />`)}return`${r}: ${s}`}))))).map(n=>n.join(n[0]!==""&&n[1]!==""?`
`:"")).join(`
`);function ge(t){let e,n;return e=new $e({}),{c(){T(e.$$.fragment)},m(l,a){E(e,l,a),n=!0},i(l){n||(m(e.$$.fragment,l),n=!0)},o(l){d(e.$$.fragment,l),n=!1},d(l){N(e,l)}}}function ke(t){let e,n;return e=new el({}),{c(){T(e.$$.fragment)},m(l,a){E(e,l,a),n=!0},i(l){n||(m(e.$$.fragment,l),n=!0)},o(l){d(e.$$.fragment,l),n=!1},d(l){N(e,l)}}}function _l(t){let e,n,l,a,r,s,o=!t[0]&&ge(),i=t[0]&&ke();return{c(){e=H("button"),o&&o.c(),n=O(),i&&i.c(),f(e,"title","copy"),f(e,"aria-label",l=t[0]?"Copied message":"Copy message"),f(e,"class","svelte-11hlfrc")},m(u,_){C(u,e,_),o&&o.m(e,null),B(e,n),i&&i.m(e,null),a=!0,r||(s=V(e,"click",t[1]),r=!0)},p(u,[_]){u[0]?o&&(L(),d(o,1,1,()=>{o=null}),S()):o?_&1&&m(o,1):(o=ge(),o.c(),m(o,1),o.m(e,n)),u[0]?i?_&1&&m(i,1):(i=ke(),i.c(),m(i,1),i.m(e,null)):i&&(L(),d(i,1,1,()=>{i=null}),S()),(!a||_&1&&l!==(l=u[0]?"Copied message":"Copy message"))&&f(e,"aria-label",l)},i(u){a||(m(o),m(i),a=!0)},o(u){d(o),d(i),a=!1},d(u){u&&y(e),o&&o.d(),i&&i.d(),r=!1,s()}}}function cl(t,e,n){let l=!1,{value:a}=e,r;function s(){n(0,l=!0),r&&clearTimeout(r),r=setTimeout(()=>{n(0,l=!1)},2e3)}async function o(){if("clipboard"in navigator)await navigator.clipboard.writeText(a),s();else{const i=document.createElement("textarea");i.value=a,i.style.position="absolute",i.style.left="-999999px",document.body.prepend(i),i.select();try{document.execCommand("copy"),s()}catch(u){console.error(u)}finally{i.remove()}}}return Pe(()=>{r&&clearTimeout(r)}),t.$$set=i=>{"value"in i&&n(2,a=i.value)},[l,o,a]}class bl extends X{constructor(e){super(),x(this,e,cl,_l,$,{value:2})}}function ml(t){let e,n,l,a,r,s,o;return n=new t[3]({props:{actioned:t[2]}}),{c(){e=H("button"),T(n.$$.fragment),f(e,"title",l=t[0]+" message"),f(e,"aria-label",a=t[2]?`clicked ${t[0]}`:t[0]),f(e,"class","svelte-3snf3m")},m(i,u){C(i,e,u),E(n,e,null),r=!0,s||(o=[V(e,"click",t[5]),V(e,"keydown",t[6])],s=!0)},p(i,[u]){const _={};u&4&&(_.actioned=i[2]),n.$set(_),(!r||u&1&&l!==(l=i[0]+" message"))&&f(e,"title",l),(!r||u&5&&a!==(a=i[2]?`clicked ${i[0]}`:i[0]))&&f(e,"aria-label",a)},i(i){r||(m(n.$$.fragment,i),r=!0)},o(i){d(n.$$.fragment,i),r=!1},d(i){i&&y(e),N(n),s=!1,ne(o)}}}function dl(t,e,n){let{action:l}=e,{handle_action:a}=e,r=!1,s=l==="like"?fl:sl;function o(){n(2,r=!0)}const i=()=>{o(),a()},u=_=>{_.key==="Enter"&&(o(),a())};return t.$$set=_=>{"action"in _&&n(0,l=_.action),"handle_action"in _&&n(1,a=_.handle_action)},[l,a,r,s,o,i,u]}class we extends X{constructor(e){super(),x(this,e,dl,ml,$,{action:0,handle_action:1})}}function hl(t){let e;return{c(){e=H("div"),e.innerHTML=`<span class="sr-only">Loading content</span> <div class="dot-flashing svelte-1ofy3w8"></div>
	 
	<div class="dot-flashing svelte-1ofy3w8"></div>
	 
	<div class="dot-flashing svelte-1ofy3w8"></div>`,f(e,"class","message pending svelte-1ofy3w8"),f(e,"role","status"),f(e,"aria-label","Loading response"),f(e,"aria-live","polite"),_e(e,"border-radius",t[0]==="bubble"?"var(--radius-xxl)":"none")},m(n,l){C(n,e,l)},p(n,[l]){l&1&&_e(e,"border-radius",n[0]==="bubble"?"var(--radius-xxl)":"none")},i:M,o:M,d(n){n&&y(e)}}}function gl(t,e,n){let{layout:l="bubble"}=e;return t.$$set=a=>{"layout"in a&&n(0,l=a.layout)},[l]}class kl extends X{constructor(e){super(),x(this,e,gl,hl,$,{layout:0})}}function ve(t,e,n){const l=t.slice();return l[36]=e[n],l[38]=n,l}function Ce(t,e,n){const l=t.slice();return l[39]=e[n],l[41]=n,l}function ye(t){let e,n,l;return n=new Xe({props:{formatter:ul,value:t[0]}}),n.$on("error",t[27]),n.$on("share",t[28]),{c(){e=H("div"),T(n.$$.fragment),f(e,"class","share-button svelte-1pjfiar")},m(a,r){C(a,e,r),E(n,e,null),l=!0},p(a,r){const s={};r[0]&1&&(s.value=a[0]),n.$set(s)},i(a){l||(m(n.$$.fragment,a),l=!0)},o(a){d(n.$$.fragment,a),l=!1},d(a){a&&y(e),N(n)}}}function je(t){let e,n,l=ae(t[0]),a=[];for(let s=0;s<l.length;s+=1)a[s]=Se(ve(t,l,s));const r=s=>d(a[s],1,1,()=>{a[s]=null});return{c(){for(let s=0;s<a.length;s+=1)a[s].c();e=ce()},m(s,o){for(let i=0;i<a.length;i+=1)a[i]&&a[i].m(s,o);C(s,e,o),n=!0},p(s,o){if(o[0]&983007){l=ae(s[0]);let i;for(i=0;i<l.length;i+=1){const u=ve(s,l,i);a[i]?(a[i].p(u,o),m(a[i],1)):(a[i]=Se(u),a[i].c(),m(a[i],1),a[i].m(e.parentNode,e))}for(L(),i=l.length;i<a.length;i+=1)r(i);S()}},i(s){if(!n){for(let o=0;o<l.length;o+=1)m(a[o]);n=!0}},o(s){a=a.filter(Boolean);for(let o=0;o<a.length;o+=1)d(a[o]);n=!1},d(s){s&&y(e),Ne(a,s)}}}function pe(t){let e,n,l,a,r,s,o,i,u,_,k,A,U,q,j,Z,R,p=t[8][t[41]]!==null&&ze(t);const G=[pl,jl,yl,Cl,vl,wl],v=[];function I(g,w){return w[0]&1&&(r=null),w[0]&1&&(s=null),w[0]&1&&(o=null),typeof g[39]=="string"?0:(r==null&&(r=!!(g[39]!==null&&g[39].mime_type?.includes("audio"))),r?1:(s==null&&(s=!!(g[39]!==null&&g[39].mime_type?.includes("video"))),s?2:(o==null&&(o=!!(g[39]!==null&&g[39].mime_type?.includes("image"))),o?3:g[39]!==null&&g[39].data!==null?4:g[2]&&g[41]===1?5:-1)))}~(i=I(t,[-1,-1]))&&(u=v[i]=G[i](t));function K(){return t[29](t[38],t[41],t[39])}function Q(...g){return t[30](t[38],t[41],t[39],...g)}let h=(t[4]&&t[41]!==0||t[7]&&t[39]&&typeof t[39]=="string")&&Be(t);return{c(){e=H("div"),p&&p.c(),n=O(),l=H("div"),a=H("button"),u&&u.c(),A=O(),h&&h.c(),U=O(),f(a,"data-testid",t[41]==0?"user":"bot"),f(a,"dir",_=t[6]?"rtl":"ltr"),f(a,"aria-label",k=(t[41]==0?"user":"bot")+"'s message:' "+t[39]),f(a,"class","svelte-1pjfiar"),z(a,"latest",t[38]===t[0].length-1),z(a,"message-markdown-disabled",!t[11]),z(a,"selectable",t[3]),_e(a,"text-align","left"),f(l,"class","message "+(t[41]==0?"user":"bot")+" svelte-1pjfiar"),z(l,"message-fit",t[15]==="bubble"&&!t[10]),z(l,"panel-full-width",t[15]==="panel"),z(l,"message-bubble-border",t[15]==="bubble"),z(l,"message-markdown-disabled",!t[11]),f(e,"class",q="message-row "+t[15]+" "+(t[41]==0?"user-row":"bot-row")+" svelte-1pjfiar")},m(g,w){C(g,e,w),p&&p.m(e,null),B(e,n),B(e,l),B(l,a),~i&&v[i].m(a,null),B(e,A),h&&h.m(e,null),B(e,U),j=!0,Z||(R=[V(a,"click",K),V(a,"keydown",Q)],Z=!0)},p(g,w){t=g,t[8][t[41]]!==null?p?p.p(t,w):(p=ze(t),p.c(),p.m(e,n)):p&&(p.d(1),p=null);let W=i;i=I(t,w),i===W?~i&&v[i].p(t,w):(u&&(L(),d(v[W],1,1,()=>{v[W]=null}),S()),~i?(u=v[i],u?u.p(t,w):(u=v[i]=G[i](t),u.c()),m(u,1),u.m(a,null)):u=null),(!j||w[0]&64&&_!==(_=t[6]?"rtl":"ltr"))&&f(a,"dir",_),(!j||w[0]&1&&k!==(k=(t[41]==0?"user":"bot")+"'s message:' "+t[39]))&&f(a,"aria-label",k),(!j||w[0]&1)&&z(a,"latest",t[38]===t[0].length-1),(!j||w[0]&2048)&&z(a,"message-markdown-disabled",!t[11]),(!j||w[0]&8)&&z(a,"selectable",t[3]),(!j||w[0]&33792)&&z(l,"message-fit",t[15]==="bubble"&&!t[10]),(!j||w[0]&32768)&&z(l,"panel-full-width",t[15]==="panel"),(!j||w[0]&32768)&&z(l,"message-bubble-border",t[15]==="bubble"),(!j||w[0]&2048)&&z(l,"message-markdown-disabled",!t[11]),t[4]&&t[41]!==0||t[7]&&t[39]&&typeof t[39]=="string"?h?(h.p(t,w),w[0]&145&&m(h,1)):(h=Be(t),h.c(),m(h,1),h.m(e,U)):h&&(L(),d(h,1,1,()=>{h=null}),S()),(!j||w[0]&32768&&q!==(q="message-row "+t[15]+" "+(t[41]==0?"user-row":"bot-row")+" svelte-1pjfiar"))&&f(e,"class",q)},i(g){j||(m(u),m(h),j=!0)},o(g){d(u),d(h),j=!1},d(g){g&&y(e),p&&p.d(),~i&&v[i].d(),h&&h.d(),Z=!1,ne(R)}}}function ze(t){let e,n,l;return{c(){e=H("div"),n=H("img"),f(n,"class","avatar-image svelte-1pjfiar"),J(n.src,l=de(t[8][t[41]],t[13],t[14]))||f(n,"src",l),f(n,"alt",(t[41]==0?"user":"bot")+" avatar"),f(e,"class","avatar-container svelte-1pjfiar")},m(a,r){C(a,e,r),B(e,n)},p(a,r){r[0]&24832&&!J(n.src,l=de(a[8][a[41]],a[13],a[14]))&&f(n,"src",l)},d(a){a&&y(e)}}}function wl(t){let e,n;return e=new kl({props:{layout:t[15]}}),{c(){T(e.$$.fragment)},m(l,a){E(e,l,a),n=!0},p(l,a){const r={};a[0]&32768&&(r.layout=l[15]),e.$set(r)},i(l){n||(m(e.$$.fragment,l),n=!0)},o(l){d(e.$$.fragment,l),n=!1},d(l){N(e,l)}}}function vl(t){let e,n=(t[39].orig_name||t[39].name)+"",l,a,r;return{c(){e=H("a"),l=Fe(n),f(e,"data-testid","chatbot-file"),f(e,"href",a=t[39].data),f(e,"target","_blank"),f(e,"download",r=window.__is_colab__?null:t[39].orig_name||t[39].name),f(e,"class","svelte-1pjfiar")},m(s,o){C(s,e,o),B(e,l)},p(s,o){o[0]&1&&n!==(n=(s[39].orig_name||s[39].name)+"")&&Oe(l,n),o[0]&1&&a!==(a=s[39].data)&&f(e,"href",a),o[0]&1&&r!==(r=window.__is_colab__?null:s[39].orig_name||s[39].name)&&f(e,"download",r)},i:M,o:M,d(s){s&&y(e)}}}function Cl(t){let e,n,l;return{c(){e=H("img"),f(e,"data-testid","chatbot-image"),J(e.src,n=t[39].data)||f(e,"src",n),f(e,"alt",l=t[39].alt_text),f(e,"class","svelte-1pjfiar")},m(a,r){C(a,e,r)},p(a,r){r[0]&1&&!J(e.src,n=a[39].data)&&f(e,"src",n),r[0]&1&&l!==(l=a[39].alt_text)&&f(e,"alt",l)},i:M,o:M,d(a){a&&y(e)}}}function yl(t){let e,n,l,a,r,s;return{c(){e=H("video"),n=H("track"),f(n,"kind","captions"),f(n,"class","svelte-1pjfiar"),f(e,"data-testid","chatbot-video"),e.controls=!0,J(e.src,l=t[39].data)||f(e,"src",l),f(e,"title",a=t[39].alt_text),f(e,"preload","auto"),f(e,"class","svelte-1pjfiar")},m(o,i){C(o,e,i),B(e,n),r||(s=[V(e,"play",t[24]),V(e,"pause",t[25]),V(e,"ended",t[26])],r=!0)},p(o,i){i[0]&1&&!J(e.src,l=o[39].data)&&f(e,"src",l),i[0]&1&&a!==(a=o[39].alt_text)&&f(e,"title",a)},i:M,o:M,d(o){o&&y(e),r=!1,ne(s)}}}function jl(t){let e,n,l,a,r;return{c(){e=H("audio"),f(e,"data-testid","chatbot-audio"),e.controls=!0,f(e,"preload","metadata"),J(e.src,n=t[39].data)||f(e,"src",n),f(e,"title",l=t[39].alt_text),f(e,"class","svelte-1pjfiar")},m(s,o){C(s,e,o),a||(r=[V(e,"play",t[21]),V(e,"pause",t[22]),V(e,"ended",t[23])],a=!0)},p(s,o){o[0]&1&&!J(e.src,n=s[39].data)&&f(e,"src",n),o[0]&1&&l!==(l=s[39].alt_text)&&f(e,"title",l)},i:M,o:M,d(s){s&&y(e),a=!1,ne(r)}}}function pl(t){let e,n;return e=new xe({props:{message:t[39],latex_delimiters:t[1],sanitize_html:t[9],render_markdown:t[11],line_breaks:t[12]}}),e.$on("load",t[17]),{c(){T(e.$$.fragment)},m(l,a){E(e,l,a),n=!0},p(l,a){const r={};a[0]&1&&(r.message=l[39]),a[0]&2&&(r.latex_delimiters=l[1]),a[0]&512&&(r.sanitize_html=l[9]),a[0]&2048&&(r.render_markdown=l[11]),a[0]&4096&&(r.line_breaks=l[12]),e.$set(r)},i(l){n||(m(e.$$.fragment,l),n=!0)},o(l){d(e.$$.fragment,l),n=!1},d(l){N(e,l)}}}function Be(t){let e,n,l,a,r=t[4]&&t[41]==1&&He(t),s=t[7]&&t[39]&&typeof t[39]=="string"&&Me(t);return{c(){e=H("div"),r&&r.c(),n=O(),s&&s.c(),f(e,"class",l="message-buttons-"+(t[41]==0?"user":"bot")+" message-buttons-"+t[15]+" "+(t[8][t[41]]!==null&&"with-avatar")+" svelte-1pjfiar"),z(e,"message-buttons-fit",t[15]==="bubble"&&!t[10]),z(e,"bubble-buttons-user",t[15]==="bubble")},m(o,i){C(o,e,i),r&&r.m(e,null),B(e,n),s&&s.m(e,null),a=!0},p(o,i){o[4]&&o[41]==1?r?(r.p(o,i),i[0]&16&&m(r,1)):(r=He(o),r.c(),m(r,1),r.m(e,n)):r&&(L(),d(r,1,1,()=>{r=null}),S()),o[7]&&o[39]&&typeof o[39]=="string"?s?(s.p(o,i),i[0]&129&&m(s,1)):(s=Me(o),s.c(),m(s,1),s.m(e,null)):s&&(L(),d(s,1,1,()=>{s=null}),S()),(!a||i[0]&33024&&l!==(l="message-buttons-"+(o[41]==0?"user":"bot")+" message-buttons-"+o[15]+" "+(o[8][o[41]]!==null&&"with-avatar")+" svelte-1pjfiar"))&&f(e,"class",l),(!a||i[0]&34048)&&z(e,"message-buttons-fit",o[15]==="bubble"&&!o[10]),(!a||i[0]&33024)&&z(e,"bubble-buttons-user",o[15]==="bubble")},i(o){a||(m(r),m(s),a=!0)},o(o){d(r),d(s),a=!1},d(o){o&&y(e),r&&r.d(),s&&s.d()}}}function He(t){let e,n,l,a;function r(){return t[31](t[38],t[41],t[39])}e=new we({props:{action:"like",handle_action:r}});function s(){return t[32](t[38],t[41],t[39])}return l=new we({props:{action:"dislike",handle_action:s}}),{c(){T(e.$$.fragment),n=O(),T(l.$$.fragment)},m(o,i){E(e,o,i),C(o,n,i),E(l,o,i),a=!0},p(o,i){t=o;const u={};i[0]&1&&(u.handle_action=r),e.$set(u);const _={};i[0]&1&&(_.handle_action=s),l.$set(_)},i(o){a||(m(e.$$.fragment,o),m(l.$$.fragment,o),a=!0)},o(o){d(e.$$.fragment,o),d(l.$$.fragment,o),a=!1},d(o){o&&y(n),N(e,o),N(l,o)}}}function Me(t){let e,n;return e=new bl({props:{value:t[39]}}),{c(){T(e.$$.fragment)},m(l,a){E(e,l,a),n=!0},p(l,a){const r={};a[0]&1&&(r.value=l[39]),e.$set(r)},i(l){n||(m(e.$$.fragment,l),n=!0)},o(l){d(e.$$.fragment,l),n=!1},d(l){N(e,l)}}}function Le(t){let e,n,l=(t[39]!==null||t[2])&&pe(t);return{c(){l&&l.c(),e=ce()},m(a,r){l&&l.m(a,r),C(a,e,r),n=!0},p(a,r){a[39]!==null||a[2]?l?(l.p(a,r),r[0]&5&&m(l,1)):(l=pe(a),l.c(),m(l,1),l.m(e.parentNode,e)):l&&(L(),d(l,1,1,()=>{l=null}),S())},i(a){n||(m(l),n=!0)},o(a){d(l),n=!1},d(a){a&&y(e),l&&l.d(a)}}}function Se(t){let e,n,l=ae(t[36]),a=[];for(let s=0;s<l.length;s+=1)a[s]=Le(Ce(t,l,s));const r=s=>d(a[s],1,1,()=>{a[s]=null});return{c(){for(let s=0;s<a.length;s+=1)a[s].c();e=ce()},m(s,o){for(let i=0;i<a.length;i+=1)a[i]&&a[i].m(s,o);C(s,e,o),n=!0},p(s,o){if(o[0]&983007){l=ae(s[36]);let i;for(i=0;i<l.length;i+=1){const u=Ce(s,l,i);a[i]?(a[i].p(u,o),m(a[i],1)):(a[i]=Le(u),a[i].c(),m(a[i],1),a[i].m(e.parentNode,e))}for(L(),i=l.length;i<a.length;i+=1)r(i);S()}},i(s){if(!n){for(let o=0;o<l.length;o+=1)m(a[o]);n=!0}},o(s){a=a.filter(Boolean);for(let o=0;o<a.length;o+=1)d(a[o]);n=!1},d(s){s&&y(e),Ne(a,s)}}}function zl(t){let e,n,l,a,r,s,o,i=t[5]&&t[0]!==null&&t[0].length>0&&ye(t),u=t[0]!==null&&je(t);return{c(){i&&i.c(),e=O(),n=H("div"),l=H("div"),u&&u.c(),f(l,"class","message-wrap svelte-1pjfiar"),z(l,"bubble-gap",t[15]==="bubble"),f(n,"class",a=be(t[15]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-1pjfiar"),f(n,"role","log"),f(n,"aria-label","chatbot conversation"),f(n,"aria-live","polite")},m(_,k){i&&i.m(_,k),C(_,e,k),C(_,n,k),B(n,l),u&&u.m(l,null),t[33](n),r=!0,s||(o=Ve(We.call(null,l)),s=!0)},p(_,k){_[5]&&_[0]!==null&&_[0].length>0?i?(i.p(_,k),k[0]&33&&m(i,1)):(i=ye(_),i.c(),m(i,1),i.m(e.parentNode,e)):i&&(L(),d(i,1,1,()=>{i=null}),S()),_[0]!==null?u?(u.p(_,k),k[0]&1&&m(u,1)):(u=je(_),u.c(),m(u,1),u.m(l,null)):u&&(L(),d(u,1,1,()=>{u=null}),S()),(!r||k[0]&32768)&&z(l,"bubble-gap",_[15]==="bubble"),(!r||k[0]&32768&&a!==(a=be(_[15]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-1pjfiar"))&&f(n,"class",a)},i(_){r||(m(i),m(u),r=!0)},o(_){d(i),d(u),r=!1},d(_){_&&(y(e),y(n)),i&&i.d(_),u&&u.d(),t[33](null),s=!1,o()}}}function Bl(t,e,n){let{value:l}=e,a=null,{latex_delimiters:r}=e,{pending_message:s=!1}=e,{selectable:o=!1}=e,{likeable:i=!1}=e,{show_share_button:u=!1}=e,{rtl:_=!1}=e,{show_copy_button:k=!1}=e,{avatar_images:A=[null,null]}=e,{sanitize_html:U=!0}=e,{bubble_full_width:q=!0}=e,{render_markdown:j=!0}=e,{line_breaks:Z=!0}=e,{root:R}=e,{root_url:p}=e,{layout:G="bubble"}=e,v,I;const K=qe();Ue(()=>{I=v&&v.offsetHeight+v.scrollTop>v.scrollHeight-100});const Q=()=>{I&&v.scrollTo(0,v.scrollHeight)};Ze(()=>{I&&(Q(),v.querySelectorAll("img").forEach(b=>{b.addEventListener("load",()=>{Q()})}))});function h(b,D,P){K("select",{index:[b,D],value:P})}function g(b,D,P,ue){K("like",{index:[b,D],value:P,liked:ue})}function w(b){Y.call(this,t,b)}function W(b){Y.call(this,t,b)}function le(b){Y.call(this,t,b)}function te(b){Y.call(this,t,b)}function ie(b){Y.call(this,t,b)}function se(b){Y.call(this,t,b)}function re(b){Y.call(this,t,b)}function oe(b){Y.call(this,t,b)}const fe=(b,D,P)=>h(b,D,P),c=(b,D,P,ue)=>{ue.key==="Enter"&&h(b,D,P)},ee=(b,D,P)=>g(b,D,P,!0),Ae=(b,D,P)=>g(b,D,P,!1);function De(b){Ie[b?"unshift":"push"](()=>{v=b,n(16,v)})}return t.$$set=b=>{"value"in b&&n(0,l=b.value),"latex_delimiters"in b&&n(1,r=b.latex_delimiters),"pending_message"in b&&n(2,s=b.pending_message),"selectable"in b&&n(3,o=b.selectable),"likeable"in b&&n(4,i=b.likeable),"show_share_button"in b&&n(5,u=b.show_share_button),"rtl"in b&&n(6,_=b.rtl),"show_copy_button"in b&&n(7,k=b.show_copy_button),"avatar_images"in b&&n(8,A=b.avatar_images),"sanitize_html"in b&&n(9,U=b.sanitize_html),"bubble_full_width"in b&&n(10,q=b.bubble_full_width),"render_markdown"in b&&n(11,j=b.render_markdown),"line_breaks"in b&&n(12,Z=b.line_breaks),"root"in b&&n(13,R=b.root),"root_url"in b&&n(14,p=b.root_url),"layout"in b&&n(15,G=b.layout)},t.$$.update=()=>{t.$$.dirty[0]&1048577&&(Ye(l,a)||(n(20,a=l),K("change")))},[l,r,s,o,i,u,_,k,A,U,q,j,Z,R,p,G,v,Q,h,g,a,w,W,le,te,ie,se,re,oe,fe,c,ee,Ae,De]}class Hl extends X{constructor(e){super(),x(this,e,Bl,zl,$,{value:0,latex_delimiters:1,pending_message:2,selectable:3,likeable:4,show_share_button:5,rtl:6,show_copy_button:7,avatar_images:8,sanitize_html:9,bubble_full_width:10,render_markdown:11,line_breaks:12,root:13,root_url:14,layout:15},null,[-1,-1])}}function Te(t){let e,n;const l=[t[23],{show_progress:t[23].show_progress==="hidden"?"hidden":"minimal"}];let a={};for(let r=0;r<l.length;r+=1)a=Re(a,l[r]);return e=new Ge({props:a}),{c(){T(e.$$.fragment)},m(r,s){E(e,r,s),n=!0},p(r,s){const o=s[0]&8388608?Ke(l,[Qe(r[23]),{show_progress:r[23].show_progress==="hidden"?"hidden":"minimal"}]):{};e.$set(o)},i(r){n||(m(e.$$.fragment,r),n=!0)},o(r){d(e.$$.fragment,r),n=!1},d(r){N(e,r)}}}function Ee(t){let e,n;return e=new ll({props:{show_label:t[7],Icon:al,float:!1,label:t[6]||"Chatbot"}}),{c(){T(e.$$.fragment)},m(l,a){E(e,l,a),n=!0},p(l,a){const r={};a[0]&128&&(r.show_label=l[7]),a[0]&64&&(r.label=l[6]||"Chatbot"),e.$set(r)},i(l){n||(m(e.$$.fragment,l),n=!0)},o(l){d(e.$$.fragment,l),n=!1},d(l){N(e,l)}}}function Ml(t){let e,n,l,a,r,s=t[23]&&Te(t),o=t[7]&&Ee(t);return a=new Hl({props:{selectable:t[10],likeable:t[11],show_share_button:t[12],value:t[25],latex_delimiters:t[20],render_markdown:t[18],pending_message:t[23]?.status==="pending",rtl:t[13],show_copy_button:t[14],avatar_images:t[22],sanitize_html:t[15],bubble_full_width:t[16],line_breaks:t[19],layout:t[17],root_url:t[9],root:t[8]}}),a.$on("change",t[26]),a.$on("select",t[27]),a.$on("like",t[28]),a.$on("share",t[29]),a.$on("error",t[30]),{c(){s&&s.c(),e=O(),n=H("div"),o&&o.c(),l=O(),T(a.$$.fragment),f(n,"class","wrapper svelte-nab2ao")},m(i,u){s&&s.m(i,u),C(i,e,u),C(i,n,u),o&&o.m(n,null),B(n,l),E(a,n,null),r=!0},p(i,u){i[23]?s?(s.p(i,u),u[0]&8388608&&m(s,1)):(s=Te(i),s.c(),m(s,1),s.m(e.parentNode,e)):s&&(L(),d(s,1,1,()=>{s=null}),S()),i[7]?o?(o.p(i,u),u[0]&128&&m(o,1)):(o=Ee(i),o.c(),m(o,1),o.m(n,l)):o&&(L(),d(o,1,1,()=>{o=null}),S());const _={};u[0]&1024&&(_.selectable=i[10]),u[0]&2048&&(_.likeable=i[11]),u[0]&4096&&(_.show_share_button=i[12]),u[0]&33554432&&(_.value=i[25]),u[0]&1048576&&(_.latex_delimiters=i[20]),u[0]&262144&&(_.render_markdown=i[18]),u[0]&8388608&&(_.pending_message=i[23]?.status==="pending"),u[0]&8192&&(_.rtl=i[13]),u[0]&16384&&(_.show_copy_button=i[14]),u[0]&4194304&&(_.avatar_images=i[22]),u[0]&32768&&(_.sanitize_html=i[15]),u[0]&65536&&(_.bubble_full_width=i[16]),u[0]&524288&&(_.line_breaks=i[19]),u[0]&131072&&(_.layout=i[17]),u[0]&512&&(_.root_url=i[9]),u[0]&256&&(_.root=i[8]),a.$set(_)},i(i){r||(m(s),m(o),m(a.$$.fragment,i),r=!0)},o(i){d(s),d(o),d(a.$$.fragment,i),r=!1},d(i){i&&(y(e),y(n)),s&&s.d(i),o&&o.d(),N(a)}}}function Ll(t){let e,n;return e=new Je({props:{elem_id:t[0],elem_classes:t[1],visible:t[2],padding:!1,scale:t[4],min_width:t[5],height:t[24],allow_overflow:!1,$$slots:{default:[Ml]},$$scope:{ctx:t}}}),{c(){T(e.$$.fragment)},m(l,a){E(e,l,a),n=!0},p(l,a){const r={};a[0]&1&&(r.elem_id=l[0]),a[0]&2&&(r.elem_classes=l[1]),a[0]&4&&(r.visible=l[2]),a[0]&16&&(r.scale=l[4]),a[0]&32&&(r.min_width=l[5]),a[0]&16777216&&(r.height=l[24]),a[0]&50331592|a[1]&2&&(r.$$scope={dirty:a,ctx:l}),e.$set(r)},i(l){n||(m(e.$$.fragment,l),n=!0)},o(l){d(e.$$.fragment,l),n=!1},d(l){N(e,l)}}}function Sl(t,e,n){let{elem_id:l=""}=e,{elem_classes:a=[]}=e,{visible:r=!0}=e,{value:s=[]}=e,{scale:o=null}=e,{min_width:i=void 0}=e,{label:u}=e,{show_label:_=!0}=e,{root:k}=e,{root_url:A}=e,{selectable:U=!1}=e,{likeable:q=!1}=e,{show_share_button:j=!1}=e,{rtl:Z=!1}=e,{show_copy_button:R=!1}=e,{sanitize_html:p=!0}=e,{bubble_full_width:G=!0}=e,{layout:v="bubble"}=e,{render_markdown:I=!0}=e,{line_breaks:K=!0}=e,{latex_delimiters:Q}=e,{gradio:h}=e,{avatar_images:g=[null,null]}=e,w;const W=c=>c.replace('src="/file',`src="${k}file`);let{loading_status:le=void 0}=e,{height:te=400}=e;const ie=()=>h.dispatch("change",s),se=c=>h.dispatch("select",c.detail),re=c=>h.dispatch("like",c.detail),oe=c=>h.dispatch("share",c.detail),fe=c=>h.dispatch("error",c.detail);return t.$$set=c=>{"elem_id"in c&&n(0,l=c.elem_id),"elem_classes"in c&&n(1,a=c.elem_classes),"visible"in c&&n(2,r=c.visible),"value"in c&&n(3,s=c.value),"scale"in c&&n(4,o=c.scale),"min_width"in c&&n(5,i=c.min_width),"label"in c&&n(6,u=c.label),"show_label"in c&&n(7,_=c.show_label),"root"in c&&n(8,k=c.root),"root_url"in c&&n(9,A=c.root_url),"selectable"in c&&n(10,U=c.selectable),"likeable"in c&&n(11,q=c.likeable),"show_share_button"in c&&n(12,j=c.show_share_button),"rtl"in c&&n(13,Z=c.rtl),"show_copy_button"in c&&n(14,R=c.show_copy_button),"sanitize_html"in c&&n(15,p=c.sanitize_html),"bubble_full_width"in c&&n(16,G=c.bubble_full_width),"layout"in c&&n(17,v=c.layout),"render_markdown"in c&&n(18,I=c.render_markdown),"line_breaks"in c&&n(19,K=c.line_breaks),"latex_delimiters"in c&&n(20,Q=c.latex_delimiters),"gradio"in c&&n(21,h=c.gradio),"avatar_images"in c&&n(22,g=c.avatar_images),"loading_status"in c&&n(23,le=c.loading_status),"height"in c&&n(24,te=c.height)},t.$$.update=()=>{t.$$.dirty[0]&776&&n(25,w=s?s.map(([c,ee])=>[typeof c=="string"?W(c):he(c,k,A),typeof ee=="string"?W(ee):he(ee,k,A)]):[])},[l,a,r,s,o,i,u,_,k,A,U,q,j,Z,R,p,G,v,I,K,Q,h,g,le,te,w,ie,se,re,oe,fe]}class Tl extends X{constructor(e){super(),x(this,e,Sl,Ll,$,{elem_id:0,elem_classes:1,visible:2,value:3,scale:4,min_width:5,label:6,show_label:7,root:8,root_url:9,selectable:10,likeable:11,show_share_button:12,rtl:13,show_copy_button:14,sanitize_html:15,bubble_full_width:16,layout:17,render_markdown:18,line_breaks:19,latex_delimiters:20,gradio:21,avatar_images:22,loading_status:23,height:24},null,[-1,-1])}}const Il=Tl;export{Il as default};
//# sourceMappingURL=index-da243c51.js.map
