{"version": 3, "file": "index-e2033f22.js", "sources": ["../../../../js/file/static/File.svelte", "../../../../js/file/static/StaticFile.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\timport { BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\timport { FilePreview } from \"../shared\";\n\n\texport let value: FileData | FileData[] | null = null;\n\texport let label: string;\n\texport let show_label = true;\n\texport let selectable = false;\n\texport let height: number | undefined = undefined;\n</script>\n\n<BlockLabel\n\t{show_label}\n\tfloat={value === null}\n\tIcon={File}\n\tlabel={label || \"File\"}\n/>\n\n{#if value}\n\t<FilePreview {selectable} on:select {value} {height} />\n{:else}\n\t<Empty unpadded_box={true} size=\"large\"><File /></Empty>\n{/if}\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { getContext } from \"svelte\";\n\timport File from \"./File.svelte\";\n\timport { blobToBase64 } from \"@gradio/upload\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport { Block } from \"@gradio/atoms\";\n\n\timport { upload_files as default_upload_files } from \"@gradio/client\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData | FileData[];\n\tlet old_value: null | FileData | FileData[];\n\n\texport let mode: \"static\" | \"interactive\";\n\texport let root: string;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let height: number | undefined = undefined;\n\n\texport let root_url: null | string;\n\texport let selectable = false;\n\texport let loading_status: LoadingStatus;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\terror: string;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t}>;\n\n\tconst upload_files =\n\t\tgetContext<typeof default_upload_files>(\"upload_files\") ??\n\t\tdefault_upload_files;\n\n\t$: _value = normalise_file(value, root, root_url);\n\n\tlet dragging = false;\n\tlet pending_upload = false;\n\n\t$: {\n\t\tif (JSON.stringify(_value) !== JSON.stringify(old_value)) {\n\t\t\told_value = _value;\n\t\t\tif (_value === null) {\n\t\t\t\tgradio.dispatch(\"change\");\n\t\t\t\tpending_upload = false;\n\t\t\t} else if (\n\t\t\t\t!(Array.isArray(_value) ? _value : [_value]).every(\n\t\t\t\t\t(file_data) => file_data.blob\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\tpending_upload = false;\n\t\t\t\tgradio.dispatch(\"change\");\n\t\t\t} else if (mode === \"interactive\") {\n\t\t\t\tlet files = (Array.isArray(_value) ? _value : [_value]).map(\n\t\t\t\t\t(file_data) => file_data.blob!\n\t\t\t\t);\n\t\t\t\tlet upload_value = _value;\n\t\t\t\tpending_upload = true;\n\t\t\t\tupload_files(root, files).then((response) => {\n\t\t\t\t\tif (upload_value !== _value) {\n\t\t\t\t\t\t// value has changed since upload started\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tpending_upload = false;\n\t\t\t\t\tif (response.error) {\n\t\t\t\t\t\t(Array.isArray(_value) ? _value : [_value]).forEach(\n\t\t\t\t\t\t\tasync (file_data, i) => {\n\t\t\t\t\t\t\t\tfile_data.data = await blobToBase64(file_data.blob!);\n\t\t\t\t\t\t\t\tfile_data.blob = undefined;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t(Array.isArray(_value) ? _value : [_value]).forEach(\n\t\t\t\t\t\t\t(file_data, i) => {\n\t\t\t\t\t\t\t\tif (response.files) {\n\t\t\t\t\t\t\t\t\tfile_data.orig_name = file_data.name;\n\t\t\t\t\t\t\t\t\tfile_data.name = response.files[i];\n\t\t\t\t\t\t\t\t\tfile_data.is_file = true;\n\t\t\t\t\t\t\t\t\tfile_data.blob = undefined;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t);\n\t\t\t\t\t\told_value = _value = normalise_file(value, root, root_url);\n\t\t\t\t\t}\n\t\t\t\t\tgradio.dispatch(\"change\");\n\t\t\t\t\tgradio.dispatch(\"upload\");\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker\n\t\t{...loading_status}\n\t\tstatus={pending_upload\n\t\t\t? \"generating\"\n\t\t\t: loading_status?.status || \"complete\"}\n\t/>\n\n\t<File\n\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t{selectable}\n\t\tvalue={_value}\n\t\t{label}\n\t\t{show_label}\n\t\t{height}\n\t/>\n</Block>\n"], "names": ["ctx", "File", "dirty", "blocklabel_changes", "value", "$$props", "label", "show_label", "selectable", "height", "block_changes", "elem_id", "elem_classes", "visible", "old_value", "mode", "root", "root_url", "loading_status", "container", "scale", "min_width", "gradio", "upload_files", "getContext", "default_upload_files", "pending_upload", "select_handler", "detail", "$$invalidate", "_value", "normalise_file", "file_data", "files", "upload_value", "response", "i", "blobToBase64"], "mappings": "geAuBsB,qyBARd,MAAAA,OAAU,UACXC,EACC,MAAAD,MAAS,sDAGZA,EAAK,CAAA,EAAA,2KALFE,EAAA,IAAAC,EAAA,MAAAH,OAAU,MAEVE,EAAA,IAAAC,EAAA,MAAAH,MAAS,mSAXL,GAAA,CAAA,MAAAI,EAAsC,IAAI,EAAAC,GAC1C,MAAAC,CAAa,EAAAD,EACb,CAAA,WAAAE,EAAa,EAAI,EAAAF,EACjB,CAAA,WAAAG,EAAa,EAAK,EAAAH,EAClB,CAAA,OAAAI,EAA6B,MAAS,EAAAJ,2XC8G5CL,EAAc,CAAA,GACV,OAAAA,EAAA,EAAA,EACL,aACAA,EAAc,CAAA,GAAE,QAAU,+HAMtBA,EAAM,EAAA,6LATTA,EAAc,CAAA,CAAA,GACV,OAAAA,EAAA,EAAA,EACL,aACAA,EAAc,CAAA,GAAE,QAAU,kFAMtBA,EAAM,EAAA,kRApBL,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACH,eACzB,4FAMO,oIARPE,EAAA,IAAAQ,EAAA,QAAAV,EAAU,CAAA,IAAA,KAAO,SAAW,kSA3F1B,CAAA,QAAAW,EAAU,EAAE,EAAAN,GACZ,aAAAO,EAAY,EAAA,EAAAP,EACZ,CAAA,QAAAQ,EAAU,EAAI,EAAAR,GACd,MAAAD,CAAmC,EAAAC,EAC1CS,GAEO,KAAAC,CAA8B,EAAAV,GAC9B,KAAAW,CAAY,EAAAX,GACZ,MAAAC,CAAa,EAAAD,GACb,WAAAE,CAAmB,EAAAF,EACnB,CAAA,OAAAI,EAA6B,MAAS,EAAAJ,GAEtC,SAAAY,CAAuB,EAAAZ,EACvB,CAAA,WAAAG,EAAa,EAAK,EAAAH,GAClB,eAAAa,CAA6B,EAAAb,EAC7B,CAAA,UAAAc,EAAY,EAAI,EAAAd,EAChB,CAAA,MAAAe,EAAuB,IAAI,EAAAf,EAC3B,CAAA,UAAAgB,EAAgC,MAAS,EAAAhB,GACzC,OAAAiB,CAMT,EAAAjB,EAEI,MAAAkB,EACLC,EAAwC,cAAc,GACtDC,EAKG,IAAAC,EAAiB,GA4EN,MAAAC,EAAA,CAAA,CAAA,OAAAC,KAAaN,EAAO,SAAS,SAAUM,CAAM,qlBA/E3DC,EAAA,GAAEC,EAASC,EAAe3B,EAAOY,EAAMC,CAAQ,CAAA,qBAM3C,KAAK,UAAUa,CAAM,IAAM,KAAK,UAAUhB,CAAS,GAElD,GADJe,EAAA,GAAAf,EAAYgB,CAAM,EACdA,IAAW,KACdR,EAAO,SAAS,QAAQ,EACxBO,EAAA,GAAAH,EAAiB,EAAK,UAEpB,EAAA,MAAM,QAAQI,CAAM,EAAIA,EAAM,CAAIA,CAAM,GAAG,MAC3CE,GAAcA,EAAU,IAAI,EAG9BH,EAAA,GAAAH,EAAiB,EAAK,EACtBJ,EAAO,SAAS,QAAQ,UACdP,IAAS,cAAa,CAC5B,IAAAkB,GAAS,MAAM,QAAQH,CAAM,EAAIA,EAAU,CAAAA,CAAM,GAAG,IACtDE,GAAcA,EAAU,IAAK,EAE3BE,EAAeJ,EACnBD,EAAA,GAAAH,EAAiB,EAAI,EACrBH,EAAaP,EAAMiB,CAAK,EAAE,KAAME,GAAQ,CACnCD,IAAiBJ,IAKrBD,EAAA,GAAAH,EAAiB,EAAK,EAClBS,EAAS,OACX,MAAM,QAAQL,CAAM,EAAIA,EAAU,CAAAA,CAAM,GAAG,QACpC,MAAAE,EAAWI,IAAC,CAClBJ,EAAU,KAAI,MAASK,GAAaL,EAAU,IAAK,EACnDA,EAAU,KAAO,WAIlB,MAAM,QAAQF,CAAM,EAAIA,EAAU,CAAAA,CAAM,GAAG,QAC1C,CAAAE,EAAWI,IAAC,CACRD,EAAS,QACZH,EAAU,UAAYA,EAAU,KAChCA,EAAU,KAAOG,EAAS,MAAMC,CAAC,EACjCJ,EAAU,QAAU,GACpBA,EAAU,KAAO,eAIpBlB,EAASe,EAAA,GAAGC,EAASC,EAAe3B,EAAOY,EAAMC,CAAQ,CAAA,CAAA,GAE1DK,EAAO,SAAS,QAAQ,EACxBA,EAAO,SAAS,QAAQ"}