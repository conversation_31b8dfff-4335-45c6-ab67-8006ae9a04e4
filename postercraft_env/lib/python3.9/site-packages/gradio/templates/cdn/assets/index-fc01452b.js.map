{"version": 3, "file": "index-fc01452b.js", "sources": ["../../../../js/html/static/HTML.svelte", "../../../../js/html/static/StaticHtml.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let value: string;\n\texport let visible = true;\n\texport let min_height = false;\n\n\tconst dispatch = createEventDispatcher<{ change: undefined }>();\n\n\t$: value, dispatch(\"change\");\n</script>\n\n<div\n\tclass=\"prose {elem_classes.join(' ')}\"\n\tclass:min={min_height}\n\tid={elem_id}\n\tclass:hide={!visible}\n>\n\t{@html value}\n</div>\n\n<style>\n\t.min {\n\t\tmin-height: var(--size-24);\n\t}\n\t.hide {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport HTML from \"./HTML.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let label: string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = \"\";\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t}>;\n\n\t$: label, gradio.dispatch(\"change\");\n</script>\n\n<Block {visible} {elem_id} {elem_classes} container={false}>\n\t<StatusTracker {...loading_status} variant=\"center\" />\n\t<div class:pending={loading_status?.status === \"pending\"}>\n\t\t<HTML\n\t\t\tmin_height={loading_status && loading_status?.status !== \"complete\"}\n\t\t\t{value}\n\t\t\t{elem_id}\n\t\t\t{elem_classes}\n\t\t\t{visible}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\tdiv {\n\t\ttransition: 150ms;\n\t}\n\n\t.pending {\n\t\topacity: 0.2;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "elem_id", "$$props", "elem_classes", "value", "visible", "min_height", "dispatch", "createEventDispatcher", "dirty", "html_changes", "label", "loading_status", "gradio"], "mappings": "gRAceA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iBAAA,WAE/BA,EAAO,CAAA,CAAA,YADAA,EAAU,CAAA,CAAA,cAERA,EAAO,CAAA,CAAA,UAJrBC,EAOKC,EAAAC,EAAAC,CAAA,cADGJ,EAAK,CAAA,8BAALA,EAAK,CAAA,wBALEA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iDAE/BA,EAAO,CAAA,CAAA,kBADAA,EAAU,CAAA,CAAA,oBAERA,EAAO,CAAA,CAAA,4CAfT,GAAA,CAAA,QAAAK,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,GACZ,MAAAE,CAAa,EAAAF,EACb,CAAA,QAAAG,EAAU,EAAI,EAAAH,EACd,CAAA,WAAAI,EAAa,EAAK,EAAAJ,EAEvB,MAAAK,EAAWC,mOAEPD,EAAS,QAAQ,wKCWRX,EAAc,CAAA,EAAA,CAAA,QAAA,QAAA,CAAA,6FAGnB,WAAAA,EAAkB,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,8LAFvCA,EAAc,CAAA,GAAE,SAAW,SAAS,4BAAxDC,EASKC,EAAAC,EAAAC,CAAA,+CAVcJ,EAAc,CAAA,CAAA,iCAGnBa,EAAA,KAAAC,EAAA,WAAAd,EAAkB,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,2IAFvCA,EAAc,CAAA,GAAE,SAAW,SAAS,+OAFJ,uUAbzC,MAAAe,CAAa,EAAAT,EACb,CAAA,QAAAD,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,EACd,CAAA,MAAAE,EAAQ,EAAE,EAAAF,GACV,eAAAU,CAA6B,EAAAV,GAC7B,OAAAW,CAET,EAAAX,cAciBW,EAAO,SAAS,QAAQ,oSAZjCA,EAAO,SAAS,QAAQ"}