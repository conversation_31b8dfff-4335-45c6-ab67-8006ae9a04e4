function Q(n,e){return n==null||e==null?NaN:n<e?-1:n>e?1:n>=e?0:NaN}function kn(n){let e=n,r=n,t=n;n.length!==2&&(e=(o,u)=>n(o)-u,r=Q,t=(o,u)=>Q(n(o),u));function i(o,u,s=0,h=o.length){if(s<h){if(r(u,u)!==0)return h;do{const c=s+h>>>1;t(o[c],u)<0?s=c+1:h=c}while(s<h)}return s}function a(o,u,s=0,h=o.length){if(s<h){if(r(u,u)!==0)return h;do{const c=s+h>>>1;t(o[c],u)<=0?s=c+1:h=c}while(s<h)}return s}function f(o,u,s=0,h=o.length){const c=i(o,u,s,h-1);return c>s&&e(o[c-1],u)>-e(o[c],u)?c-1:c}return{left:i,center:f,right:a}}function Tn(n){return n===null?NaN:+n}function*Ce(n,e){if(e===void 0)for(let r of n)r!=null&&(r=+r)>=r&&(yield r);else{let r=-1;for(let t of n)(t=e(t,++r,n))!=null&&(t=+t)>=t&&(yield t)}}const An=kn(Q),_n=An.right,Fe=An.left;kn(Tn).center;const Gn=_n;var W=Math.sqrt(50),Y=Math.sqrt(10),Z=Math.sqrt(2);function Vn(n,e,r){var t,i=-1,a,f,o;if(e=+e,n=+n,r=+r,n===e&&r>0)return[n];if((t=e<n)&&(a=n,n=e,e=a),(o=Rn(n,e,r))===0||!isFinite(o))return[];if(o>0){let u=Math.round(n/o),s=Math.round(e/o);for(u*o<n&&++u,s*o>e&&--s,f=new Array(a=s-u+1);++i<a;)f[i]=(u+i)*o}else{o=-o;let u=Math.round(n*o),s=Math.round(e*o);for(u/o<n&&++u,s/o>e&&--s,f=new Array(a=s-u+1);++i<a;)f[i]=(u+i)/o}return t&&f.reverse(),f}function Rn(n,e,r){var t=(e-n)/Math.max(0,r),i=Math.floor(Math.log(t)/Math.LN10),a=t/Math.pow(10,i);return i>=0?(a>=W?10:a>=Y?5:a>=Z?2:1)*Math.pow(10,i):-Math.pow(10,-i)/(a>=W?10:a>=Y?5:a>=Z?2:1)}function Xn(n,e,r){var t=Math.abs(e-n)/Math.max(0,r),i=Math.pow(10,Math.floor(Math.log(t)/Math.LN10)),a=t/i;return a>=W?i*=10:a>=Y?i*=5:a>=Z&&(i*=2),e<n?-i:i}function Un(n){return Math.abs(n=Math.round(n))>=1e21?n.toLocaleString("en").replace(/,/g,""):n.toString(10)}function D(n,e){if((r=(n=e?n.toExponential(e-1):n.toExponential()).indexOf("e"))<0)return null;var r,t=n.slice(0,r);return[t.length>1?t[0]+t.slice(2):t,+n.slice(r+1)]}function P(n){return n=D(Math.abs(n)),n?n[1]:NaN}function Jn(n,e){return function(r,t){for(var i=r.length,a=[],f=0,o=n[0],u=0;i>0&&o>0&&(u+o+1>t&&(o=Math.max(1,t-u)),a.push(r.substring(i-=o,i+o)),!((u+=o+1)>t));)o=n[f=(f+1)%n.length];return a.reverse().join(e)}}function Kn(n){return function(e){return e.replace(/[0-9]/g,function(r){return n[+r]})}}var Qn=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function O(n){if(!(e=Qn.exec(n)))throw new Error("invalid format: "+n);var e;return new rn({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}O.prototype=rn.prototype;function rn(n){this.fill=n.fill===void 0?" ":n.fill+"",this.align=n.align===void 0?">":n.align+"",this.sign=n.sign===void 0?"-":n.sign+"",this.symbol=n.symbol===void 0?"":n.symbol+"",this.zero=!!n.zero,this.width=n.width===void 0?void 0:+n.width,this.comma=!!n.comma,this.precision=n.precision===void 0?void 0:+n.precision,this.trim=!!n.trim,this.type=n.type===void 0?"":n.type+""}rn.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function Wn(n){n:for(var e=n.length,r=1,t=-1,i;r<e;++r)switch(n[r]){case".":t=i=r;break;case"0":t===0&&(t=r),i=r;break;default:if(!+n[r])break n;t>0&&(t=0);break}return t>0?n.slice(0,t)+n.slice(i+1):n}var Sn;function Yn(n,e){var r=D(n,e);if(!r)return n+"";var t=r[0],i=r[1],a=i-(Sn=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,f=t.length;return a===f?t:a>f?t+new Array(a-f+1).join("0"):a>0?t.slice(0,a)+"."+t.slice(a):"0."+new Array(1-a).join("0")+D(n,Math.max(0,e+a-1))[0]}function cn(n,e){var r=D(n,e);if(!r)return n+"";var t=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+t:t.length>i+1?t.slice(0,i+1)+"."+t.slice(i+1):t+new Array(i-t.length+2).join("0")}const hn={"%":(n,e)=>(n*100).toFixed(e),b:n=>Math.round(n).toString(2),c:n=>n+"",d:Un,e:(n,e)=>n.toExponential(e),f:(n,e)=>n.toFixed(e),g:(n,e)=>n.toPrecision(e),o:n=>Math.round(n).toString(8),p:(n,e)=>cn(n*100,e),r:cn,s:Yn,X:n=>Math.round(n).toString(16).toUpperCase(),x:n=>Math.round(n).toString(16)};function ln(n){return n}var dn=Array.prototype.map,gn=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Zn(n){var e=n.grouping===void 0||n.thousands===void 0?ln:Jn(dn.call(n.grouping,Number),n.thousands+""),r=n.currency===void 0?"":n.currency[0]+"",t=n.currency===void 0?"":n.currency[1]+"",i=n.decimal===void 0?".":n.decimal+"",a=n.numerals===void 0?ln:Kn(dn.call(n.numerals,String)),f=n.percent===void 0?"%":n.percent+"",o=n.minus===void 0?"−":n.minus+"",u=n.nan===void 0?"NaN":n.nan+"";function s(c){c=O(c);var d=c.fill,M=c.align,b=c.sign,$=c.symbol,k=c.zero,E=c.width,X=c.comma,N=c.precision,fn=c.trim,x=c.type;x==="n"?(X=!0,x="g"):hn[x]||(N===void 0&&(N=12),fn=!0,x="g"),(k||d==="0"&&M==="=")&&(k=!0,d="0",M="=");var Bn=$==="$"?r:$==="#"&&/[boxX]/.test(x)?"0"+x.toLowerCase():"",Dn=$==="$"?t:/[%p]/.test(x)?f:"",on=hn[x],On=/[defgprs%]/.test(x);N=N===void 0?6:/[gprs]/.test(x)?Math.max(1,Math.min(21,N)):Math.max(0,Math.min(20,N));function un(l){var v=Bn,m=Dn,A,sn,z;if(x==="c")m=on(l)+m,l="";else{l=+l;var C=l<0||1/l<0;if(l=isNaN(l)?u:on(Math.abs(l),N),fn&&(l=Wn(l)),C&&+l==0&&b!=="+"&&(C=!1),v=(C?b==="("?b:o:b==="-"||b==="("?"":b)+v,m=(x==="s"?gn[8+Sn/3]:"")+m+(C&&b==="("?")":""),On){for(A=-1,sn=l.length;++A<sn;)if(z=l.charCodeAt(A),48>z||z>57){m=(z===46?i+l.slice(A+1):l.slice(A))+m,l=l.slice(0,A);break}}}X&&!k&&(l=e(l,1/0));var F=v.length+l.length+m.length,w=F<E?new Array(E-F+1).join(d):"";switch(X&&k&&(l=e(w+l,w.length?E-m.length:1/0),w=""),M){case"<":l=v+l+m+w;break;case"=":l=v+w+l+m;break;case"^":l=w.slice(0,F=w.length>>1)+v+l+m+w.slice(F);break;default:l=w+v+l+m;break}return a(l)}return un.toString=function(){return c+""},un}function h(c,d){var M=s((c=O(c),c.type="f",c)),b=Math.max(-8,Math.min(8,Math.floor(P(d)/3)))*3,$=Math.pow(10,-b),k=gn[8+b/3];return function(E){return M($*E)+k}}return{format:s,formatPrefix:h}}var L,Pn,$n;ne({thousands:",",grouping:[3],currency:["$",""]});function ne(n){return L=Zn(n),Pn=L.format,$n=L.formatPrefix,L}function ee(n){return Math.max(0,-P(Math.abs(n)))}function re(n,e){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(P(e)/3)))*3-P(Math.abs(n)))}function te(n,e){return n=Math.abs(n),e=Math.abs(e)-n,Math.max(0,P(e)-P(n))+1}function ie(n,e){switch(arguments.length){case 0:break;case 1:this.range(n);break;default:this.range(e).domain(n);break}return this}function Le(n,e){switch(arguments.length){case 0:break;case 1:{typeof n=="function"?this.interpolator(n):this.range(n);break}default:{this.domain(n),typeof e=="function"?this.interpolator(e):this.range(e);break}}return this}function tn(n,e,r){n.prototype=e.prototype=r,r.constructor=n}function En(n,e){var r=Object.create(n.prototype);for(var t in e)r[t]=e[t];return r}function I(){}var j=.7,T=1/j,S="\\s*([+-]?\\d+)\\s*",q="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)\\s*",y="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ae=/^#([0-9a-f]{3,8})$/,fe=new RegExp("^rgb\\("+[S,S,S]+"\\)$"),oe=new RegExp("^rgb\\("+[y,y,y]+"\\)$"),ue=new RegExp("^rgba\\("+[S,S,S,q]+"\\)$"),se=new RegExp("^rgba\\("+[y,y,y,q]+"\\)$"),ce=new RegExp("^hsl\\("+[q,y,y]+"\\)$"),he=new RegExp("^hsla\\("+[q,y,y,q]+"\\)$"),xn={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};tn(I,H,{copy:function(n){return Object.assign(new this.constructor,this,n)},displayable:function(){return this.rgb().displayable()},hex:mn,formatHex:mn,formatHsl:le,formatRgb:bn,toString:bn});function mn(){return this.rgb().formatHex()}function le(){return jn(this).formatHsl()}function bn(){return this.rgb().formatRgb()}function H(n){var e,r;return n=(n+"").trim().toLowerCase(),(e=ae.exec(n))?(r=e[1].length,e=parseInt(e[1],16),r===6?pn(e):r===3?new g(e>>8&15|e>>4&240,e>>4&15|e&240,(e&15)<<4|e&15,1):r===8?B(e>>24&255,e>>16&255,e>>8&255,(e&255)/255):r===4?B(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|e&240,((e&15)<<4|e&15)/255):null):(e=fe.exec(n))?new g(e[1],e[2],e[3],1):(e=oe.exec(n))?new g(e[1]*255/100,e[2]*255/100,e[3]*255/100,1):(e=ue.exec(n))?B(e[1],e[2],e[3],e[4]):(e=se.exec(n))?B(e[1]*255/100,e[2]*255/100,e[3]*255/100,e[4]):(e=ce.exec(n))?Mn(e[1],e[2]/100,e[3]/100,1):(e=he.exec(n))?Mn(e[1],e[2]/100,e[3]/100,e[4]):xn.hasOwnProperty(n)?pn(xn[n]):n==="transparent"?new g(NaN,NaN,NaN,0):null}function pn(n){return new g(n>>16&255,n>>8&255,n&255,1)}function B(n,e,r,t){return t<=0&&(n=e=r=NaN),new g(n,e,r,t)}function de(n){return n instanceof I||(n=H(n)),n?(n=n.rgb(),new g(n.r,n.g,n.b,n.opacity)):new g}function _(n,e,r,t){return arguments.length===1?de(n):new g(n,e,r,t??1)}function g(n,e,r,t){this.r=+n,this.g=+e,this.b=+r,this.opacity=+t}tn(g,_,En(I,{brighter:function(n){return n=n==null?T:Math.pow(T,n),new g(this.r*n,this.g*n,this.b*n,this.opacity)},darker:function(n){return n=n==null?j:Math.pow(j,n),new g(this.r*n,this.g*n,this.b*n,this.opacity)},rgb:function(){return this},displayable:function(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:yn,formatHex:yn,formatRgb:wn,toString:wn}));function yn(){return"#"+U(this.r)+U(this.g)+U(this.b)}function wn(){var n=this.opacity;return n=isNaN(n)?1:Math.max(0,Math.min(1,n)),(n===1?"rgb(":"rgba(")+Math.max(0,Math.min(255,Math.round(this.r)||0))+", "+Math.max(0,Math.min(255,Math.round(this.g)||0))+", "+Math.max(0,Math.min(255,Math.round(this.b)||0))+(n===1?")":", "+n+")")}function U(n){return n=Math.max(0,Math.min(255,Math.round(n)||0)),(n<16?"0":"")+n.toString(16)}function Mn(n,e,r,t){return t<=0?n=e=r=NaN:r<=0||r>=1?n=e=NaN:e<=0&&(n=NaN),new p(n,e,r,t)}function jn(n){if(n instanceof p)return new p(n.h,n.s,n.l,n.opacity);if(n instanceof I||(n=H(n)),!n)return new p;if(n instanceof p)return n;n=n.rgb();var e=n.r/255,r=n.g/255,t=n.b/255,i=Math.min(e,r,t),a=Math.max(e,r,t),f=NaN,o=a-i,u=(a+i)/2;return o?(e===a?f=(r-t)/o+(r<t)*6:r===a?f=(t-e)/o+2:f=(e-r)/o+4,o/=u<.5?a+i:2-a-i,f*=60):o=u>0&&u<1?0:f,new p(f,o,u,n.opacity)}function ge(n,e,r,t){return arguments.length===1?jn(n):new p(n,e,r,t??1)}function p(n,e,r,t){this.h=+n,this.s=+e,this.l=+r,this.opacity=+t}tn(p,ge,En(I,{brighter:function(n){return n=n==null?T:Math.pow(T,n),new p(this.h,this.s,this.l*n,this.opacity)},darker:function(n){return n=n==null?j:Math.pow(j,n),new p(this.h,this.s,this.l*n,this.opacity)},rgb:function(){var n=this.h%360+(this.h<0)*360,e=isNaN(n)||isNaN(this.s)?0:this.s,r=this.l,t=r+(r<.5?r:1-r)*e,i=2*r-t;return new g(J(n>=240?n-240:n+120,i,t),J(n,i,t),J(n<120?n+240:n-120,i,t),this.opacity)},displayable:function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl:function(){var n=this.opacity;return n=isNaN(n)?1:Math.max(0,Math.min(1,n)),(n===1?"hsl(":"hsla(")+(this.h||0)+", "+(this.s||0)*100+"%, "+(this.l||0)*100+"%"+(n===1?")":", "+n+")")}}));function J(n,e,r){return(n<60?e+(r-e)*n/60:n<180?r:n<240?e+(r-e)*(240-n)/60:e)*255}function qn(n,e,r,t,i){var a=n*n,f=a*n;return((1-3*n+3*a-f)*e+(4-6*a+3*f)*r+(1+3*n+3*a-3*f)*t+f*i)/6}function xe(n){var e=n.length-1;return function(r){var t=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=n[t],a=n[t+1],f=t>0?n[t-1]:2*i-a,o=t<e-1?n[t+2]:2*a-i;return qn((r-t/e)*e,f,i,a,o)}}function me(n){var e=n.length;return function(r){var t=Math.floor(((r%=1)<0?++r:r)*e),i=n[(t+e-1)%e],a=n[t%e],f=n[(t+1)%e],o=n[(t+2)%e];return qn((r-t/e)*e,i,a,f,o)}}const V=n=>()=>n;function Hn(n,e){return function(r){return n+r*e}}function be(n,e,r){return n=Math.pow(n,r),e=Math.pow(e,r)-n,r=1/r,function(t){return Math.pow(n+t*e,r)}}function Be(n,e){var r=e-n;return r?Hn(n,r>180||r<-180?r-360*Math.round(r/360):r):V(isNaN(n)?e:n)}function pe(n){return(n=+n)==1?In:function(e,r){return r-e?be(e,r,n):V(isNaN(e)?r:e)}}function In(n,e){var r=e-n;return r?Hn(n,r):V(isNaN(n)?e:n)}const Nn=function n(e){var r=pe(e);function t(i,a){var f=r((i=_(i)).r,(a=_(a)).r),o=r(i.g,a.g),u=r(i.b,a.b),s=In(i.opacity,a.opacity);return function(h){return i.r=f(h),i.g=o(h),i.b=u(h),i.opacity=s(h),i+""}}return t.gamma=n,t}(1);function zn(n){return function(e){var r=e.length,t=new Array(r),i=new Array(r),a=new Array(r),f,o;for(f=0;f<r;++f)o=_(e[f]),t[f]=o.r||0,i[f]=o.g||0,a[f]=o.b||0;return t=n(t),i=n(i),a=n(a),o.opacity=1,function(u){return o.r=t(u),o.g=i(u),o.b=a(u),o+""}}}var De=zn(xe),Oe=zn(me);function Cn(n,e){e||(e=[]);var r=n?Math.min(e.length,n.length):0,t=e.slice(),i;return function(a){for(i=0;i<r;++i)t[i]=n[i]*(1-a)+e[i]*a;return t}}function Fn(n){return ArrayBuffer.isView(n)&&!(n instanceof DataView)}function Te(n,e){return(Fn(e)?Cn:Ln)(n,e)}function Ln(n,e){var r=e?e.length:0,t=n?Math.min(r,n.length):0,i=new Array(t),a=new Array(r),f;for(f=0;f<t;++f)i[f]=an(n[f],e[f]);for(;f<r;++f)a[f]=e[f];return function(o){for(f=0;f<t;++f)a[f]=i[f](o);return a}}function ye(n,e){var r=new Date;return n=+n,e=+e,function(t){return r.setTime(n*(1-t)+e*t),r}}function G(n,e){return n=+n,e=+e,function(r){return n*(1-r)+e*r}}function we(n,e){var r={},t={},i;(n===null||typeof n!="object")&&(n={}),(e===null||typeof e!="object")&&(e={});for(i in e)i in n?r[i]=an(n[i],e[i]):t[i]=e[i];return function(a){for(i in r)t[i]=r[i](a);return t}}var nn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,K=new RegExp(nn.source,"g");function Me(n){return function(){return n}}function Ne(n){return function(e){return n(e)+""}}function ve(n,e){var r=nn.lastIndex=K.lastIndex=0,t,i,a,f=-1,o=[],u=[];for(n=n+"",e=e+"";(t=nn.exec(n))&&(i=K.exec(e));)(a=i.index)>r&&(a=e.slice(r,a),o[f]?o[f]+=a:o[++f]=a),(t=t[0])===(i=i[0])?o[f]?o[f]+=i:o[++f]=i:(o[++f]=null,u.push({i:f,x:G(t,i)})),r=K.lastIndex;return r<e.length&&(a=e.slice(r),o[f]?o[f]+=a:o[++f]=a),o.length<2?u[0]?Ne(u[0].x):Me(e):(e=u.length,function(s){for(var h=0,c;h<e;++h)o[(c=u[h]).i]=c.x(s);return o.join("")})}function an(n,e){var r=typeof e,t;return e==null||r==="boolean"?V(e):(r==="number"?G:r==="string"?(t=H(e))?(e=t,Nn):ve:e instanceof H?Nn:e instanceof Date?ye:Fn(e)?Cn:Array.isArray(e)?Ln:typeof e.valueOf!="function"&&typeof e.toString!="function"||isNaN(e)?we:G)(n,e)}function ke(n,e){return n=+n,e=+e,function(r){return Math.round(n*(1-r)+e*r)}}function Ae(n){return function(){return n}}function Re(n){return+n}var vn=[0,1];function R(n){return n}function en(n,e){return(e-=n=+n)?function(r){return(r-n)/e}:Ae(isNaN(e)?NaN:.5)}function Se(n,e){var r;return n>e&&(r=n,n=e,e=r),function(t){return Math.max(n,Math.min(e,t))}}function Pe(n,e,r){var t=n[0],i=n[1],a=e[0],f=e[1];return i<t?(t=en(i,t),a=r(f,a)):(t=en(t,i),a=r(a,f)),function(o){return a(t(o))}}function $e(n,e,r){var t=Math.min(n.length,e.length)-1,i=new Array(t),a=new Array(t),f=-1;for(n[t]<n[0]&&(n=n.slice().reverse(),e=e.slice().reverse());++f<t;)i[f]=en(n[f],n[f+1]),a[f]=r(e[f],e[f+1]);return function(o){var u=Gn(n,o,1,t)-1;return a[u](i[u](o))}}function Ee(n,e){return e.domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown())}function je(){var n=vn,e=vn,r=an,t,i,a,f=R,o,u,s;function h(){var d=Math.min(n.length,e.length);return f!==R&&(f=Se(n[0],n[d-1])),o=d>2?$e:Pe,u=s=null,c}function c(d){return d==null||isNaN(d=+d)?a:(u||(u=o(n.map(t),e,r)))(t(f(d)))}return c.invert=function(d){return f(i((s||(s=o(e,n.map(t),G)))(d)))},c.domain=function(d){return arguments.length?(n=Array.from(d,Re),h()):n.slice()},c.range=function(d){return arguments.length?(e=Array.from(d),h()):e.slice()},c.rangeRound=function(d){return e=Array.from(d),r=ke,h()},c.clamp=function(d){return arguments.length?(f=d?!0:R,h()):f!==R},c.interpolate=function(d){return arguments.length?(r=d,h()):r},c.unknown=function(d){return arguments.length?(a=d,c):a},function(d,M){return t=d,i=M,h()}}function qe(){return je()(R,R)}function He(n,e,r,t){var i=Xn(n,e,r),a;switch(t=O(t??",f"),t.type){case"s":{var f=Math.max(Math.abs(n),Math.abs(e));return t.precision==null&&!isNaN(a=re(i,f))&&(t.precision=a),$n(t,f)}case"":case"e":case"g":case"p":case"r":{t.precision==null&&!isNaN(a=te(i,Math.max(Math.abs(n),Math.abs(e))))&&(t.precision=a-(t.type==="e"));break}case"f":case"%":{t.precision==null&&!isNaN(a=ee(i))&&(t.precision=a-(t.type==="%")*2);break}}return Pn(t)}function Ie(n){var e=n.domain;return n.ticks=function(r){var t=e();return Vn(t[0],t[t.length-1],r??10)},n.tickFormat=function(r,t){var i=e();return He(i[0],i[i.length-1],r??10,t)},n.nice=function(r){r==null&&(r=10);var t=e(),i=0,a=t.length-1,f=t[i],o=t[a],u,s,h=10;for(o<f&&(s=f,f=o,o=s,s=i,i=a,a=s);h-- >0;){if(s=Rn(f,o,r),s===u)return t[i]=f,t[a]=o,e(t);if(s>0)f=Math.floor(f/s)*s,o=Math.ceil(o/s)*s;else if(s<0)f=Math.ceil(f*s)/s,o=Math.floor(o*s)/s;else break;u=s}return n},n}function ze(){var n=qe();return n.copy=function(){return Ee(n,ze())},ie.apply(n,arguments),Ie(n)}export{ye as A,Cn as B,I as C,we as D,Nn as E,De as F,Oe as G,ke as H,ve as I,Re as J,Ie as K,je as L,Ee as M,Vn as N,R as O,Gn as P,qe as Q,g as R,Le as S,ze as T,_n as U,He as V,_ as W,Fe as X,Q as a,Tn as b,kn as c,te as d,re as e,O as f,Pn as g,$n as h,Zn as i,ie as j,tn as k,En as l,T as m,Ce as n,j as o,ee as p,Be as q,de as r,G as s,Xn as t,ge as u,In as v,an as w,Te as x,xe as y,me as z};
//# sourceMappingURL=linear-bcbcf466.js.map
