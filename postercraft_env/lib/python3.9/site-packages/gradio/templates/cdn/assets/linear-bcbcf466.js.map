{"version": 3, "file": "linear-bcbcf466.js", "sources": ["../../../../node_modules/.pnpm/d3-array@3.1.1/node_modules/d3-array/src/ascending.js", "../../../../node_modules/.pnpm/d3-array@3.1.1/node_modules/d3-array/src/bisector.js", "../../../../node_modules/.pnpm/d3-array@3.1.1/node_modules/d3-array/src/number.js", "../../../../node_modules/.pnpm/d3-array@3.1.1/node_modules/d3-array/src/bisect.js", "../../../../node_modules/.pnpm/d3-array@3.1.1/node_modules/d3-array/src/ticks.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatDecimal.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/exponent.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatGroup.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatNumerals.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatSpecifier.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatTrim.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatPrefixAuto.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatRounded.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatTypes.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/identity.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/locale.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/defaultLocale.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/precisionFixed.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/precisionPrefix.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/precisionRound.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/init.js", "../../../../node_modules/.pnpm/d3-color@3.0.1/node_modules/d3-color/src/define.js", "../../../../node_modules/.pnpm/d3-color@3.0.1/node_modules/d3-color/src/color.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/basis.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/basisClosed.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/constant.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/color.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/rgb.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/numberArray.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/array.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/date.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/number.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/object.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/string.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/value.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/round.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/constant.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/number.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/continuous.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/tickFormat.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/linear.js"], "sourcesContent": ["export default function ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function bisector(f) {\n  let delta = f;\n  let compare1 = f;\n  let compare2 = f;\n\n  if (f.length !== 2) {\n    delta = (d, x) => f(d) - x;\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n", "export default function number(x) {\n  return x === null ? NaN : +x;\n}\n\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n", "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\n\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;\n", "var e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nexport default function ticks(start, stop, count) {\n  var reverse,\n      i = -1,\n      n,\n      ticks,\n      step;\n\n  stop = +stop, start = +start, count = +count;\n  if (start === stop && count > 0) return [start];\n  if (reverse = stop < start) n = start, start = stop, stop = n;\n  if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];\n\n  if (step > 0) {\n    let r0 = Math.round(start / step), r1 = Math.round(stop / step);\n    if (r0 * step < start) ++r0;\n    if (r1 * step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) * step;\n  } else {\n    step = -step;\n    let r0 = Math.round(start * step), r1 = Math.round(stop * step);\n    if (r0 / step < start) ++r0;\n    if (r1 / step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) / step;\n  }\n\n  if (reverse) ticks.reverse();\n\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  var step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log(step) / Math.LN10),\n      error = step / Math.pow(10, power);\n  return power >= 0\n      ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power)\n      : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);\n}\n\nexport function tickStep(start, stop, count) {\n  var step0 = Math.abs(stop - start) / Math.max(0, count),\n      step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),\n      error = step0 / step1;\n  if (error >= e10) step1 *= 10;\n  else if (error >= e5) step1 *= 5;\n  else if (error >= e2) step1 *= 2;\n  return stop < start ? -step1 : step1;\n}\n", "export default function(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21\n      ? x.toLocaleString(\"en\").replace(/,/g, \"\")\n      : x.toString(10);\n}\n\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nexport function formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n  var i, coefficient = x.slice(0, i);\n\n  // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n  return [\n    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n    +x.slice(i + 1)\n  ];\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x) {\n  return x = formatDecimalParts(Math.abs(x)), x ? x[1] : NaN;\n}\n", "export default function(grouping, thousands) {\n  return function(value, width) {\n    var i = value.length,\n        t = [],\n        j = 0,\n        g = grouping[0],\n        length = 0;\n\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n\n    return t.reverse().join(thousands);\n  };\n}\n", "export default function(numerals) {\n  return function(value) {\n    return value.replace(/[0-9]/g, function(i) {\n      return numerals[+i];\n    });\n  };\n}\n", "// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\n\nexport default function formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\n\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nexport function FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\n\nFormatSpecifier.prototype.toString = function() {\n  return this.fill\n      + this.align\n      + this.sign\n      + this.symbol\n      + (this.zero ? \"0\" : \"\")\n      + (this.width === undefined ? \"\" : Math.max(1, this.width | 0))\n      + (this.comma ? \",\" : \"\")\n      + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0))\n      + (this.trim ? \"~\" : \"\")\n      + this.type;\n};\n", "// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nexport default function(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\": i0 = i1 = i; break;\n      case \"0\": if (i0 === 0) i0 = i; i1 = i; break;\n      default: if (!+s[i]) break out; if (i0 > 0) i0 = 0; break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport var prefixExponent;\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1],\n      i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n      n = coefficient.length;\n  return i === n ? coefficient\n      : i > n ? coefficient + new Array(i - n + 1).join(\"0\")\n      : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i)\n      : \"0.\" + new Array(1 - i).join(\"0\") + formatDecimalParts(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient\n      : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1)\n      : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n", "import formatDecimal from \"./formatDecimal.js\";\nimport formatPrefixAuto from \"./formatPrefixAuto.js\";\nimport formatRounded from \"./formatRounded.js\";\n\nexport default {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": (x) => Math.round(x).toString(2),\n  \"c\": (x) => x + \"\",\n  \"d\": formatDecimal,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": (x) => Math.round(x).toString(8),\n  \"p\": (x, p) => formatRounded(x * 100, p),\n  \"r\": formatRounded,\n  \"s\": formatPrefixAuto,\n  \"X\": (x) => Math.round(x).toString(16).toUpperCase(),\n  \"x\": (x) => Math.round(x).toString(16)\n};\n", "export default function(x) {\n  return x;\n}\n", "import exponent from \"./exponent.js\";\nimport formatGroup from \"./formatGroup.js\";\nimport formatNumerals from \"./formatNumerals.js\";\nimport formatSpecifier from \"./formatSpecifier.js\";\nimport formatTrim from \"./formatTrim.js\";\nimport formatTypes from \"./formatTypes.js\";\nimport {prefixExponent} from \"./formatPrefixAuto.js\";\nimport identity from \"./identity.js\";\n\nvar map = Array.prototype.map,\n    prefixes = [\"y\",\"z\",\"a\",\"f\",\"p\",\"n\",\"µ\",\"m\",\"\",\"k\",\"M\",\"G\",\"T\",\"P\",\"E\",\"Z\",\"Y\"];\n\nexport default function(locale) {\n  var group = locale.grouping === undefined || locale.thousands === undefined ? identity : formatGroup(map.call(locale.grouping, Number), locale.thousands + \"\"),\n      currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\",\n      currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\",\n      decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\",\n      numerals = locale.numerals === undefined ? identity : formatNumerals(map.call(locale.numerals, String)),\n      percent = locale.percent === undefined ? \"%\" : locale.percent + \"\",\n      minus = locale.minus === undefined ? \"−\" : locale.minus + \"\",\n      nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n\n  function newFormat(specifier) {\n    specifier = formatSpecifier(specifier);\n\n    var fill = specifier.fill,\n        align = specifier.align,\n        sign = specifier.sign,\n        symbol = specifier.symbol,\n        zero = specifier.zero,\n        width = specifier.width,\n        comma = specifier.comma,\n        precision = specifier.precision,\n        trim = specifier.trim,\n        type = specifier.type;\n\n    // The \"n\" type is an alias for \",g\".\n    if (type === \"n\") comma = true, type = \"g\";\n\n    // The \"\" type, and any invalid type, is an alias for \".12~g\".\n    else if (!formatTypes[type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n\n    // If zero fill is specified, padding goes after sign and before digits.\n    if (zero || (fill === \"0\" && align === \"=\")) zero = true, fill = \"0\", align = \"=\";\n\n    // Compute the prefix and suffix.\n    // For SI-prefix, the suffix is lazily computed.\n    var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\",\n        suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n\n    // What format function should we use?\n    // Is this an integer type?\n    // Can this type generate exponential notation?\n    var formatType = formatTypes[type],\n        maybeSuffix = /[defgprs%]/.test(type);\n\n    // Set the default precision if not specified,\n    // or clamp the specified precision to the supported range.\n    // For significant precision, it must be in [1, 21].\n    // For fixed precision, it must be in [0, 20].\n    precision = precision === undefined ? 6\n        : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision))\n        : Math.max(0, Math.min(20, precision));\n\n    function format(value) {\n      var valuePrefix = prefix,\n          valueSuffix = suffix,\n          i, n, c;\n\n      if (type === \"c\") {\n        valueSuffix = formatType(value) + valueSuffix;\n        value = \"\";\n      } else {\n        value = +value;\n\n        // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n        var valueNegative = value < 0 || 1 / value < 0;\n\n        // Perform the initial formatting.\n        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n\n        // Trim insignificant zeros.\n        if (trim) value = formatTrim(value);\n\n        // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n        if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n\n        // Compute the prefix and suffix.\n        valuePrefix = (valueNegative ? (sign === \"(\" ? sign : minus) : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n        valueSuffix = (type === \"s\" ? prefixes[8 + prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n\n        // Break the formatted value into the integer “value” part that can be\n        // grouped, and fractional or exponential “suffix” part that is not.\n        if (maybeSuffix) {\n          i = -1, n = value.length;\n          while (++i < n) {\n            if (c = value.charCodeAt(i), 48 > c || c > 57) {\n              valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n              value = value.slice(0, i);\n              break;\n            }\n          }\n        }\n      }\n\n      // If the fill character is not \"0\", grouping is applied before padding.\n      if (comma && !zero) value = group(value, Infinity);\n\n      // Compute the padding.\n      var length = valuePrefix.length + value.length + valueSuffix.length,\n          padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n\n      // If the fill character is \"0\", grouping is applied after padding.\n      if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n\n      // Reconstruct the final output based on the desired alignment.\n      switch (align) {\n        case \"<\": value = valuePrefix + value + valueSuffix + padding; break;\n        case \"=\": value = valuePrefix + padding + value + valueSuffix; break;\n        case \"^\": value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length); break;\n        default: value = padding + valuePrefix + value + valueSuffix; break;\n      }\n\n      return numerals(value);\n    }\n\n    format.toString = function() {\n      return specifier + \"\";\n    };\n\n    return format;\n  }\n\n  function formatPrefix(specifier, value) {\n    var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = \"f\", specifier)),\n        e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,\n        k = Math.pow(10, -e),\n        prefix = prefixes[8 + e / 3];\n    return function(value) {\n      return f(k * value) + prefix;\n    };\n  }\n\n  return {\n    format: newFormat,\n    formatPrefix: formatPrefix\n  };\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var format;\nexport var formatPrefix;\n\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  format = locale.format;\n  formatPrefix = locale.formatPrefix;\n  return locale;\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step) {\n  return Math.max(0, -exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, exponent(max) - exponent(step)) + 1;\n}\n", "export function initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nexport function initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: {\n      if (typeof domain === \"function\") this.interpolator(domain);\n      else this.range(domain);\n      break;\n    }\n    default: {\n      this.domain(domain);\n      if (typeof interpolator === \"function\") this.interpolator(interpolator);\n      else this.range(interpolator);\n      break;\n    }\n  }\n  return this;\n}\n", "export default function(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\n", "import define, {extend} from \"./define.js\";\n\nexport function Color() {}\n\nexport var darker = 0.7;\nexport var brighter = 1 / darker;\n\nvar reI = \"\\\\s*([+-]?\\\\d+)\\\\s*\",\n    reN = \"\\\\s*([+-]?\\\\d*\\\\.?\\\\d+(?:[eE][+-]?\\\\d+)?)\\\\s*\",\n    reP = \"\\\\s*([+-]?\\\\d*\\\\.?\\\\d+(?:[eE][+-]?\\\\d+)?)%\\\\s*\",\n    reHex = /^#([0-9a-f]{3,8})$/,\n    reRgbInteger = new RegExp(\"^rgb\\\\(\" + [reI, reI, reI] + \"\\\\)$\"),\n    reRgbPercent = new RegExp(\"^rgb\\\\(\" + [reP, reP, reP] + \"\\\\)$\"),\n    reRgbaInteger = new RegExp(\"^rgba\\\\(\" + [reI, reI, reI, reN] + \"\\\\)$\"),\n    reRgbaPercent = new RegExp(\"^rgba\\\\(\" + [reP, reP, reP, reN] + \"\\\\)$\"),\n    reHslPercent = new RegExp(\"^hsl\\\\(\" + [reN, reP, reP] + \"\\\\)$\"),\n    reHslaPercent = new RegExp(\"^hsla\\\\(\" + [reN, reP, reP, reN] + \"\\\\)$\");\n\nvar named = {\n  aliceblue: 0xf0f8ff,\n  antiquewhite: 0xfaebd7,\n  aqua: 0x00ffff,\n  aquamarine: 0x7fffd4,\n  azure: 0xf0ffff,\n  beige: 0xf5f5dc,\n  bisque: 0xffe4c4,\n  black: 0x000000,\n  blanchedalmond: 0xffebcd,\n  blue: 0x0000ff,\n  blueviolet: 0x8a2be2,\n  brown: 0xa52a2a,\n  burlywood: 0xdeb887,\n  cadetblue: 0x5f9ea0,\n  chartreuse: 0x7fff00,\n  chocolate: 0xd2691e,\n  coral: 0xff7f50,\n  cornflowerblue: 0x6495ed,\n  cornsilk: 0xfff8dc,\n  crimson: 0xdc143c,\n  cyan: 0x00ffff,\n  darkblue: 0x00008b,\n  darkcyan: 0x008b8b,\n  darkgoldenrod: 0xb8860b,\n  darkgray: 0xa9a9a9,\n  darkgreen: 0x006400,\n  darkgrey: 0xa9a9a9,\n  darkkhaki: 0xbdb76b,\n  darkmagenta: 0x8b008b,\n  darkolivegreen: 0x556b2f,\n  darkorange: 0xff8c00,\n  darkorchid: 0x9932cc,\n  darkred: 0x8b0000,\n  darksalmon: 0xe9967a,\n  darkseagreen: 0x8fbc8f,\n  darkslateblue: 0x483d8b,\n  darkslategray: 0x2f4f4f,\n  darkslategrey: 0x2f4f4f,\n  darkturquoise: 0x00ced1,\n  darkviolet: 0x9400d3,\n  deeppink: 0xff1493,\n  deepskyblue: 0x00bfff,\n  dimgray: 0x696969,\n  dimgrey: 0x696969,\n  dodgerblue: 0x1e90ff,\n  firebrick: 0xb22222,\n  floralwhite: 0xfffaf0,\n  forestgreen: 0x228b22,\n  fuchsia: 0xff00ff,\n  gainsboro: 0xdcdcdc,\n  ghostwhite: 0xf8f8ff,\n  gold: 0xffd700,\n  goldenrod: 0xdaa520,\n  gray: 0x808080,\n  green: 0x008000,\n  greenyellow: 0xadff2f,\n  grey: 0x808080,\n  honeydew: 0xf0fff0,\n  hotpink: 0xff69b4,\n  indianred: 0xcd5c5c,\n  indigo: 0x4b0082,\n  ivory: 0xfffff0,\n  khaki: 0xf0e68c,\n  lavender: 0xe6e6fa,\n  lavenderblush: 0xfff0f5,\n  lawngreen: 0x7cfc00,\n  lemonchiffon: 0xfffacd,\n  lightblue: 0xadd8e6,\n  lightcoral: 0xf08080,\n  lightcyan: 0xe0ffff,\n  lightgoldenrodyellow: 0xfafad2,\n  lightgray: 0xd3d3d3,\n  lightgreen: 0x90ee90,\n  lightgrey: 0xd3d3d3,\n  lightpink: 0xffb6c1,\n  lightsalmon: 0xffa07a,\n  lightseagreen: 0x20b2aa,\n  lightskyblue: 0x87cefa,\n  lightslategray: 0x778899,\n  lightslategrey: 0x778899,\n  lightsteelblue: 0xb0c4de,\n  lightyellow: 0xffffe0,\n  lime: 0x00ff00,\n  limegreen: 0x32cd32,\n  linen: 0xfaf0e6,\n  magenta: 0xff00ff,\n  maroon: 0x800000,\n  mediumaquamarine: 0x66cdaa,\n  mediumblue: 0x0000cd,\n  mediumorchid: 0xba55d3,\n  mediumpurple: 0x9370db,\n  mediumseagreen: 0x3cb371,\n  mediumslateblue: 0x7b68ee,\n  mediumspringgreen: 0x00fa9a,\n  mediumturquoise: 0x48d1cc,\n  mediumvioletred: 0xc71585,\n  midnightblue: 0x191970,\n  mintcream: 0xf5fffa,\n  mistyrose: 0xffe4e1,\n  moccasin: 0xffe4b5,\n  navajowhite: 0xffdead,\n  navy: 0x000080,\n  oldlace: 0xfdf5e6,\n  olive: 0x808000,\n  olivedrab: 0x6b8e23,\n  orange: 0xffa500,\n  orangered: 0xff4500,\n  orchid: 0xda70d6,\n  palegoldenrod: 0xeee8aa,\n  palegreen: 0x98fb98,\n  paleturquoise: 0xafeeee,\n  palevioletred: 0xdb7093,\n  papayawhip: 0xffefd5,\n  peachpuff: 0xffdab9,\n  peru: 0xcd853f,\n  pink: 0xffc0cb,\n  plum: 0xdda0dd,\n  powderblue: 0xb0e0e6,\n  purple: 0x800080,\n  rebeccapurple: 0x663399,\n  red: 0xff0000,\n  rosybrown: 0xbc8f8f,\n  royalblue: 0x4169e1,\n  saddlebrown: 0x8b4513,\n  salmon: 0xfa8072,\n  sandybrown: 0xf4a460,\n  seagreen: 0x2e8b57,\n  seashell: 0xfff5ee,\n  sienna: 0xa0522d,\n  silver: 0xc0c0c0,\n  skyblue: 0x87ceeb,\n  slateblue: 0x6a5acd,\n  slategray: 0x708090,\n  slategrey: 0x708090,\n  snow: 0xfffafa,\n  springgreen: 0x00ff7f,\n  steelblue: 0x4682b4,\n  tan: 0xd2b48c,\n  teal: 0x008080,\n  thistle: 0xd8bfd8,\n  tomato: 0xff6347,\n  turquoise: 0x40e0d0,\n  violet: 0xee82ee,\n  wheat: 0xf5deb3,\n  white: 0xffffff,\n  whitesmoke: 0xf5f5f5,\n  yellow: 0xffff00,\n  yellowgreen: 0x9acd32\n};\n\ndefine(Color, color, {\n  copy: function(channels) {\n    return Object.assign(new this.constructor, this, channels);\n  },\n  displayable: function() {\n    return this.rgb().displayable();\n  },\n  hex: color_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: color_formatHex,\n  formatHsl: color_formatHsl,\n  formatRgb: color_formatRgb,\n  toString: color_formatRgb\n});\n\nfunction color_formatHex() {\n  return this.rgb().formatHex();\n}\n\nfunction color_formatHsl() {\n  return hslConvert(this).formatHsl();\n}\n\nfunction color_formatRgb() {\n  return this.rgb().formatRgb();\n}\n\nexport default function color(format) {\n  var m, l;\n  format = (format + \"\").trim().toLowerCase();\n  return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) // #ff0000\n      : l === 3 ? new Rgb((m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1) // #f00\n      : l === 8 ? rgba(m >> 24 & 0xff, m >> 16 & 0xff, m >> 8 & 0xff, (m & 0xff) / 0xff) // #ff000000\n      : l === 4 ? rgba((m >> 12 & 0xf) | (m >> 8 & 0xf0), (m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), (((m & 0xf) << 4) | (m & 0xf)) / 0xff) // #f000\n      : null) // invalid hex\n      : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)\n      : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)\n      : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)\n      : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)\n      : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)\n      : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)\n      : named.hasOwnProperty(format) ? rgbn(named[format]) // eslint-disable-line no-prototype-builtins\n      : format === \"transparent\" ? new Rgb(NaN, NaN, NaN, 0)\n      : null;\n}\n\nfunction rgbn(n) {\n  return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);\n}\n\nfunction rgba(r, g, b, a) {\n  if (a <= 0) r = g = b = NaN;\n  return new Rgb(r, g, b, a);\n}\n\nexport function rgbConvert(o) {\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Rgb;\n  o = o.rgb();\n  return new Rgb(o.r, o.g, o.b, o.opacity);\n}\n\nexport function rgb(r, g, b, opacity) {\n  return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);\n}\n\nexport function Rgb(r, g, b, opacity) {\n  this.r = +r;\n  this.g = +g;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Rgb, rgb, extend(Color, {\n  brighter: function(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  darker: function(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  rgb: function() {\n    return this;\n  },\n  displayable: function() {\n    return (-0.5 <= this.r && this.r < 255.5)\n        && (-0.5 <= this.g && this.g < 255.5)\n        && (-0.5 <= this.b && this.b < 255.5)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  hex: rgb_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: rgb_formatHex,\n  formatRgb: rgb_formatRgb,\n  toString: rgb_formatRgb\n}));\n\nfunction rgb_formatHex() {\n  return \"#\" + hex(this.r) + hex(this.g) + hex(this.b);\n}\n\nfunction rgb_formatRgb() {\n  var a = this.opacity; a = isNaN(a) ? 1 : Math.max(0, Math.min(1, a));\n  return (a === 1 ? \"rgb(\" : \"rgba(\")\n      + Math.max(0, Math.min(255, Math.round(this.r) || 0)) + \", \"\n      + Math.max(0, Math.min(255, Math.round(this.g) || 0)) + \", \"\n      + Math.max(0, Math.min(255, Math.round(this.b) || 0))\n      + (a === 1 ? \")\" : \", \" + a + \")\");\n}\n\nfunction hex(value) {\n  value = Math.max(0, Math.min(255, Math.round(value) || 0));\n  return (value < 16 ? \"0\" : \"\") + value.toString(16);\n}\n\nfunction hsla(h, s, l, a) {\n  if (a <= 0) h = s = l = NaN;\n  else if (l <= 0 || l >= 1) h = s = NaN;\n  else if (s <= 0) h = NaN;\n  return new Hsl(h, s, l, a);\n}\n\nexport function hslConvert(o) {\n  if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Hsl;\n  if (o instanceof Hsl) return o;\n  o = o.rgb();\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      min = Math.min(r, g, b),\n      max = Math.max(r, g, b),\n      h = NaN,\n      s = max - min,\n      l = (max + min) / 2;\n  if (s) {\n    if (r === max) h = (g - b) / s + (g < b) * 6;\n    else if (g === max) h = (b - r) / s + 2;\n    else h = (r - g) / s + 4;\n    s /= l < 0.5 ? max + min : 2 - max - min;\n    h *= 60;\n  } else {\n    s = l > 0 && l < 1 ? 0 : h;\n  }\n  return new Hsl(h, s, l, o.opacity);\n}\n\nexport function hsl(h, s, l, opacity) {\n  return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);\n}\n\nfunction Hsl(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Hsl, hsl, extend(Color, {\n  brighter: function(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker: function(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb: function() {\n    var h = this.h % 360 + (this.h < 0) * 360,\n        s = isNaN(h) || isNaN(this.s) ? 0 : this.s,\n        l = this.l,\n        m2 = l + (l < 0.5 ? l : 1 - l) * s,\n        m1 = 2 * l - m2;\n    return new Rgb(\n      hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),\n      hsl2rgb(h, m1, m2),\n      hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),\n      this.opacity\n    );\n  },\n  displayable: function() {\n    return (0 <= this.s && this.s <= 1 || isNaN(this.s))\n        && (0 <= this.l && this.l <= 1)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  formatHsl: function() {\n    var a = this.opacity; a = isNaN(a) ? 1 : Math.max(0, Math.min(1, a));\n    return (a === 1 ? \"hsl(\" : \"hsla(\")\n        + (this.h || 0) + \", \"\n        + (this.s || 0) * 100 + \"%, \"\n        + (this.l || 0) * 100 + \"%\"\n        + (a === 1 ? \")\" : \", \" + a + \")\");\n  }\n}));\n\n/* From FvD 13.37, CSS Color Module Level 3 */\nfunction hsl2rgb(h, m1, m2) {\n  return (h < 60 ? m1 + (m2 - m1) * h / 60\n      : h < 180 ? m2\n      : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60\n      : m1) * 255;\n}\n", "export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1, t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0\n      + (4 - 6 * t2 + 3 * t3) * v1\n      + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2\n      + t3 * v3) / 6;\n}\n\nexport default function(values) {\n  var n = values.length - 1;\n  return function(t) {\n    var i = t <= 0 ? (t = 0) : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n        v1 = values[i],\n        v2 = values[i + 1],\n        v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n        v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "import {basis} from \"./basis.js\";\n\nexport default function(values) {\n  var n = values.length;\n  return function(t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "export default x => () => x;\n", "import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n", "import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n", "export default function(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function(t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\n\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n", "import value from \"./value.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\n\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n      na = a ? Math.min(nb, a.length) : 0,\n      x = new Array(na),\n      c = new Array(nb),\n      i;\n\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n\n  return function(t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}\n", "export default function(a, b) {\n  var d = new Date;\n  return a = +a, b = +b, function(t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n", "import value from \"./value.js\";\n\nexport default function(a, b) {\n  var i = {},\n      c = {},\n      k;\n\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function(t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}\n", "import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n", "import {color} from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport {genericArray} from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  var t = typeof b, c;\n  return b == null || t === \"boolean\" ? constant(b)\n      : (t === \"number\" ? number\n      : t === \"string\" ? ((c = color(b)) ? (b = c, rgb) : string)\n      : b instanceof color ? rgb\n      : b instanceof Date ? date\n      : isNumberArray(b) ? numberArray\n      : Array.isArray(b) ? genericArray\n      : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object\n      : number)(a, b);\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}\n", "export default function constants(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function number(x) {\n  return +x;\n}\n", "import {bisect} from \"d3-array\";\nimport {interpolate as interpolateValue, interpolateNumber, interpolateRound} from \"d3-interpolate\";\nimport constant from \"./constant.js\";\nimport number from \"./number.js\";\n\nvar unit = [0, 1];\n\nexport function identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : constant(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport function transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = interpolateValue,\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = Array.from(_), interpolate = interpolateRound, rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nexport default function continuous() {\n  return transformer()(identity, identity);\n}\n", "import {tickStep} from \"d3-array\";\nimport {format, formatPrefix, formatSpecifier, precisionFixed, precisionPrefix, precisionRound} from \"d3-format\";\n\nexport default function tickFormat(start, stop, count, specifier) {\n  var step = tickStep(start, stop, count),\n      precision;\n  specifier = formatSpecifier(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;\n      return formatPrefix(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return format(specifier);\n}\n", "import {ticks, tickIncrement} from \"d3-array\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport tickFormat from \"./tickFormat.js\";\n\nexport function linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    \n    while (maxIter-- > 0) {\n      step = tickIncrement(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start\n        d[i1] = stop\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nexport default function linear() {\n  var scale = continuous();\n\n  scale.copy = function() {\n    return copy(scale, linear());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n"], "names": ["ascending", "a", "b", "bisector", "f", "delta", "compare1", "compare2", "d", "x", "left", "lo", "hi", "mid", "right", "center", "i", "number", "numbers", "values", "valueof", "value", "index", "ascendingBisect", "bisectRight", "bisectLeft", "bisect", "e10", "e5", "e2", "ticks", "start", "stop", "count", "reverse", "n", "step", "tickIncrement", "r0", "r1", "power", "error", "tickStep", "step0", "step1", "formatDecimal", "formatDecimalParts", "p", "coefficient", "exponent", "formatGroup", "grouping", "thousands", "width", "t", "j", "g", "length", "formatNumerals", "numerals", "re", "formatSpecifier", "specifier", "match", "FormatSpecifier", "formatTrim", "s", "out", "i0", "i1", "prefixExponent", "formatPrefixAuto", "formatRounded", "formatTypes", "identity$1", "map", "prefixes", "formatLocale", "locale", "group", "identity", "currencyPrefix", "currencySuffix", "decimal", "percent", "minus", "nan", "newFormat", "fill", "align", "sign", "symbol", "zero", "comma", "precision", "trim", "type", "prefix", "suffix", "formatType", "maybeSuffix", "format", "valuePrefix", "valueSuffix", "c", "valueNegative", "padding", "formatPrefix", "e", "k", "defaultLocale", "definition", "precisionFixed", "precisionPrefix", "precisionRound", "max", "initRange", "domain", "range", "initInterpolator", "interpolator", "define", "constructor", "factory", "prototype", "extend", "parent", "key", "Color", "darker", "brighter", "reI", "reN", "reP", "reHex", "reRgbInteger", "reRgbPercent", "reRgbaInteger", "reRgbaPercent", "reHslPercent", "reHslaPercent", "named", "color", "channels", "color_formatHex", "color_formatHsl", "color_formatRgb", "hslConvert", "m", "l", "rgbn", "Rgb", "rgba", "hsla", "r", "rgbConvert", "o", "rgb", "opacity", "rgb_formatHex", "rgb_formatRgb", "hex", "h", "Hsl", "min", "hsl", "m2", "m1", "hsl2rgb", "basis", "t1", "v0", "v1", "v2", "v3", "t2", "t3", "basis$1", "basisClosed", "constant", "linear", "exponential", "y", "hue", "gamma", "nogamma", "rgbGamma", "end", "colorRgb", "rgbSpline", "spline", "colors", "rgbBasis", "rgbBasisClosed", "numberArray", "isNumberArray", "array", "genericArray", "nb", "na", "date", "interpolateNumber", "object", "reA", "reB", "one", "string", "bi", "am", "bm", "bs", "q", "interpolate$1", "interpolateRound", "constants", "unit", "normalize", "clamper", "bimap", "interpolate", "d0", "d1", "polymap", "copy", "source", "target", "transformer", "interpolateV<PERSON>ue", "transform", "untransform", "unknown", "clamp", "piecewise", "output", "input", "rescale", "scale", "_", "u", "continuous", "tickFormat", "linearish", "prestep", "maxIter"], "mappings": "AAAe,SAASA,EAAUC,EAAGC,EAAG,CACtC,OAAOD,GAAK,MAAQC,GAAK,KAAO,IAAMD,EAAIC,EAAI,GAAKD,EAAIC,EAAI,EAAID,GAAKC,EAAI,EAAI,GAC9E,CCAe,SAASC,GAASC,EAAG,CAClC,IAAIC,EAAQD,EACRE,EAAWF,EACXG,EAAWH,EAEXA,EAAE,SAAW,IACfC,EAAQ,CAACG,EAAGC,IAAML,EAAEI,CAAC,EAAIC,EACzBH,EAAWN,EACXO,EAAW,CAACC,EAAGC,IAAMT,EAAUI,EAAEI,CAAC,EAAGC,CAAC,GAGxC,SAASC,EAAKT,EAAGQ,EAAGE,EAAK,EAAGC,EAAKX,EAAE,OAAQ,CACzC,GAAIU,EAAKC,EAAI,CACX,GAAIN,EAASG,EAAGA,CAAC,IAAM,EAAG,OAAOG,EACjC,EAAG,CACD,MAAMC,EAAOF,EAAKC,IAAQ,EACtBL,EAASN,EAAEY,CAAG,EAAGJ,CAAC,EAAI,EAAGE,EAAKE,EAAM,EACnCD,EAAKC,QACHF,EAAKC,GAEhB,OAAOD,CACR,CAED,SAASG,EAAMb,EAAGQ,EAAGE,EAAK,EAAGC,EAAKX,EAAE,OAAQ,CAC1C,GAAIU,EAAKC,EAAI,CACX,GAAIN,EAASG,EAAGA,CAAC,IAAM,EAAG,OAAOG,EACjC,EAAG,CACD,MAAMC,EAAOF,EAAKC,IAAQ,EACtBL,EAASN,EAAEY,CAAG,EAAGJ,CAAC,GAAK,EAAGE,EAAKE,EAAM,EACpCD,EAAKC,QACHF,EAAKC,GAEhB,OAAOD,CACR,CAED,SAASI,EAAOd,EAAGQ,EAAGE,EAAK,EAAGC,EAAKX,EAAE,OAAQ,CAC3C,MAAMe,EAAIN,EAAKT,EAAGQ,EAAGE,EAAIC,EAAK,CAAC,EAC/B,OAAOI,EAAIL,GAAMN,EAAMJ,EAAEe,EAAI,CAAC,EAAGP,CAAC,EAAI,CAACJ,EAAMJ,EAAEe,CAAC,EAAGP,CAAC,EAAIO,EAAI,EAAIA,CACjE,CAED,MAAO,CAAC,KAAAN,EAAM,OAAAK,EAAQ,MAAAD,CAAK,CAC7B,CC3Ce,SAASG,GAAOR,EAAG,CAChC,OAAOA,IAAM,KAAO,IAAM,CAACA,CAC7B,CAEO,SAAUS,GAAQC,EAAQC,EAAS,CACxC,GAAIA,IAAY,OACd,QAASC,KAASF,EACZE,GAAS,OAASA,EAAQ,CAACA,IAAUA,IACvC,MAAMA,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OAASE,EAAQ,CAACA,IAAUA,IAC3E,MAAMA,GAId,CCfA,MAAME,GAAkBpB,GAASH,CAAS,EAC7BwB,GAAcD,GAAgB,MAC9BE,GAAaF,GAAgB,KACdpB,GAASc,EAAM,EAAE,OAC7C,MAAAS,GAAeF,GCRf,IAAIG,EAAM,KAAK,KAAK,EAAE,EAClBC,EAAK,KAAK,KAAK,EAAE,EACjBC,EAAK,KAAK,KAAK,CAAC,EAEL,SAASC,GAAMC,EAAOC,EAAMC,EAAO,CAChD,IAAIC,EACA,EAAI,GACJC,EACAL,EACAM,EAGJ,GADAJ,EAAO,CAACA,EAAMD,EAAQ,CAACA,EAAOE,EAAQ,CAACA,EACnCF,IAAUC,GAAQC,EAAQ,EAAG,MAAO,CAACF,CAAK,EAE9C,IADIG,EAAUF,EAAOD,KAAOI,EAAIJ,EAAOA,EAAQC,EAAMA,EAAOG,IACvDC,EAAOC,GAAcN,EAAOC,EAAMC,CAAK,KAAO,GAAK,CAAC,SAASG,CAAI,EAAG,MAAO,CAAA,EAEhF,GAAIA,EAAO,EAAG,CACZ,IAAIE,EAAK,KAAK,MAAMP,EAAQK,CAAI,EAAGG,EAAK,KAAK,MAAMP,EAAOI,CAAI,EAI9D,IAHIE,EAAKF,EAAOL,GAAO,EAAEO,EACrBC,EAAKH,EAAOJ,GAAM,EAAEO,EACxBT,EAAQ,IAAI,MAAMK,EAAII,EAAKD,EAAK,CAAC,EAC1B,EAAE,EAAIH,GAAGL,EAAM,CAAC,GAAKQ,EAAK,GAAKF,MACjC,CACLA,EAAO,CAACA,EACR,IAAIE,EAAK,KAAK,MAAMP,EAAQK,CAAI,EAAGG,EAAK,KAAK,MAAMP,EAAOI,CAAI,EAI9D,IAHIE,EAAKF,EAAOL,GAAO,EAAEO,EACrBC,EAAKH,EAAOJ,GAAM,EAAEO,EACxBT,EAAQ,IAAI,MAAMK,EAAII,EAAKD,EAAK,CAAC,EAC1B,EAAE,EAAIH,GAAGL,EAAM,CAAC,GAAKQ,EAAK,GAAKF,EAGxC,OAAIF,GAASJ,EAAM,UAEZA,CACT,CAEO,SAASO,GAAcN,EAAOC,EAAMC,EAAO,CAChD,IAAIG,GAAQJ,EAAOD,GAAS,KAAK,IAAI,EAAGE,CAAK,EACzCO,EAAQ,KAAK,MAAM,KAAK,IAAIJ,CAAI,EAAI,KAAK,IAAI,EAC7CK,EAAQL,EAAO,KAAK,IAAI,GAAII,CAAK,EACrC,OAAOA,GAAS,GACTC,GAASd,EAAM,GAAKc,GAASb,EAAK,EAAIa,GAASZ,EAAK,EAAI,GAAK,KAAK,IAAI,GAAIW,CAAK,EAChF,CAAC,KAAK,IAAI,GAAI,CAACA,CAAK,GAAKC,GAASd,EAAM,GAAKc,GAASb,EAAK,EAAIa,GAASZ,EAAK,EAAI,EACzF,CAEO,SAASa,GAASX,EAAOC,EAAMC,EAAO,CAC3C,IAAIU,EAAQ,KAAK,IAAIX,EAAOD,CAAK,EAAI,KAAK,IAAI,EAAGE,CAAK,EAClDW,EAAQ,KAAK,IAAI,GAAI,KAAK,MAAM,KAAK,IAAID,CAAK,EAAI,KAAK,IAAI,CAAC,EAC5DF,EAAQE,EAAQC,EACpB,OAAIH,GAASd,EAAKiB,GAAS,GAClBH,GAASb,EAAIgB,GAAS,EACtBH,GAASZ,IAAIe,GAAS,GACxBZ,EAAOD,EAAQ,CAACa,EAAQA,CACjC,CCrDe,SAAQC,GAACpC,EAAG,CACzB,OAAO,KAAK,IAAIA,EAAI,KAAK,MAAMA,CAAC,CAAC,GAAK,KAChCA,EAAE,eAAe,IAAI,EAAE,QAAQ,KAAM,EAAE,EACvCA,EAAE,SAAS,EAAE,CACrB,CAKO,SAASqC,EAAmBrC,EAAGsC,EAAG,CACvC,IAAK/B,GAAKP,EAAIsC,EAAItC,EAAE,cAAcsC,EAAI,CAAC,EAAItC,EAAE,cAAa,GAAI,QAAQ,GAAG,GAAK,EAAG,OAAO,KACxF,IAAIO,EAAGgC,EAAcvC,EAAE,MAAM,EAAGO,CAAC,EAIjC,MAAO,CACLgC,EAAY,OAAS,EAAIA,EAAY,CAAC,EAAIA,EAAY,MAAM,CAAC,EAAIA,EACjE,CAACvC,EAAE,MAAMO,EAAI,CAAC,CAClB,CACA,CCjBe,SAAQiC,EAACxC,EAAG,CACzB,OAAOA,EAAIqC,EAAmB,KAAK,IAAIrC,CAAC,CAAC,EAAGA,EAAIA,EAAE,CAAC,EAAI,GACzD,CCJe,SAAAyC,GAASC,EAAUC,EAAW,CAC3C,OAAO,SAAS/B,EAAOgC,EAAO,CAO5B,QANI,EAAIhC,EAAM,OACViC,EAAI,CAAE,EACNC,EAAI,EACJC,EAAIL,EAAS,CAAC,EACdM,EAAS,EAEN,EAAI,GAAKD,EAAI,IACdC,EAASD,EAAI,EAAIH,IAAOG,EAAI,KAAK,IAAI,EAAGH,EAAQI,CAAM,GAC1DH,EAAE,KAAKjC,EAAM,UAAU,GAAKmC,EAAG,EAAIA,CAAC,CAAC,EAChC,GAAAC,GAAUD,EAAI,GAAKH,KACxBG,EAAIL,EAASI,GAAKA,EAAI,GAAKJ,EAAS,MAAM,EAG5C,OAAOG,EAAE,QAAO,EAAG,KAAKF,CAAS,CACrC,CACA,CCjBe,SAAQM,GAACC,EAAU,CAChC,OAAO,SAAStC,EAAO,CACrB,OAAOA,EAAM,QAAQ,SAAU,SAASL,EAAG,CACzC,OAAO2C,EAAS,CAAC3C,CAAC,CACxB,CAAK,CACL,CACA,CCLA,IAAI4C,GAAK,2EAEM,SAASC,EAAgBC,EAAW,CACjD,GAAI,EAAEC,EAAQH,GAAG,KAAKE,CAAS,GAAI,MAAM,IAAI,MAAM,mBAAqBA,CAAS,EACjF,IAAIC,EACJ,OAAO,IAAIC,GAAgB,CACzB,KAAMD,EAAM,CAAC,EACb,MAAOA,EAAM,CAAC,EACd,KAAMA,EAAM,CAAC,EACb,OAAQA,EAAM,CAAC,EACf,KAAMA,EAAM,CAAC,EACb,MAAOA,EAAM,CAAC,EACd,MAAOA,EAAM,CAAC,EACd,UAAWA,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAE,MAAM,CAAC,EACvC,KAAMA,EAAM,CAAC,EACb,KAAMA,EAAM,EAAE,CAClB,CAAG,CACH,CAEAF,EAAgB,UAAYG,GAAgB,UAErC,SAASA,GAAgBF,EAAW,CACzC,KAAK,KAAOA,EAAU,OAAS,OAAY,IAAMA,EAAU,KAAO,GAClE,KAAK,MAAQA,EAAU,QAAU,OAAY,IAAMA,EAAU,MAAQ,GACrE,KAAK,KAAOA,EAAU,OAAS,OAAY,IAAMA,EAAU,KAAO,GAClE,KAAK,OAASA,EAAU,SAAW,OAAY,GAAKA,EAAU,OAAS,GACvE,KAAK,KAAO,CAAC,CAACA,EAAU,KACxB,KAAK,MAAQA,EAAU,QAAU,OAAY,OAAY,CAACA,EAAU,MACpE,KAAK,MAAQ,CAAC,CAACA,EAAU,MACzB,KAAK,UAAYA,EAAU,YAAc,OAAY,OAAY,CAACA,EAAU,UAC5E,KAAK,KAAO,CAAC,CAACA,EAAU,KACxB,KAAK,KAAOA,EAAU,OAAS,OAAY,GAAKA,EAAU,KAAO,EACnE,CAEAE,GAAgB,UAAU,SAAW,UAAW,CAC9C,OAAO,KAAK,KACN,KAAK,MACL,KAAK,KACL,KAAK,QACJ,KAAK,KAAO,IAAM,KAClB,KAAK,QAAU,OAAY,GAAK,KAAK,IAAI,EAAG,KAAK,MAAQ,CAAC,IAC1D,KAAK,MAAQ,IAAM,KACnB,KAAK,YAAc,OAAY,GAAK,IAAM,KAAK,IAAI,EAAG,KAAK,UAAY,CAAC,IACxE,KAAK,KAAO,IAAM,IACnB,KAAK,IACb,EC7Ce,SAAQC,GAACC,EAAG,CACzBC,EAAK,QAAShC,EAAI+B,EAAE,OAAQlD,EAAI,EAAGoD,EAAK,GAAIC,EAAIrD,EAAImB,EAAG,EAAEnB,EACvD,OAAQkD,EAAElD,CAAC,EAAC,CACV,IAAK,IAAKoD,EAAKC,EAAKrD,EAAG,MACvB,IAAK,IAASoD,IAAO,IAAGA,EAAKpD,GAAGqD,EAAKrD,EAAG,MACxC,QAAS,GAAI,CAAC,CAACkD,EAAElD,CAAC,EAAG,MAAMmD,EAASC,EAAK,IAAGA,EAAK,GAAG,KACrD,CAEH,OAAOA,EAAK,EAAIF,EAAE,MAAM,EAAGE,CAAE,EAAIF,EAAE,MAAMG,EAAK,CAAC,EAAIH,CACrD,CCRO,IAAII,GAEI,SAAAC,GAAS9D,EAAGsC,EAAG,CAC5B,IAAIvC,EAAIsC,EAAmBrC,EAAGsC,CAAC,EAC/B,GAAI,CAACvC,EAAG,OAAOC,EAAI,GACnB,IAAIuC,EAAcxC,EAAE,CAAC,EACjByC,EAAWzC,EAAE,CAAC,EACdQ,EAAIiC,GAAYqB,GAAiB,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG,KAAK,MAAMrB,EAAW,CAAC,CAAC,CAAC,EAAI,GAAK,EAC5Fd,EAAIa,EAAY,OACpB,OAAOhC,IAAMmB,EAAIa,EACXhC,EAAImB,EAAIa,EAAc,IAAI,MAAMhC,EAAImB,EAAI,CAAC,EAAE,KAAK,GAAG,EACnDnB,EAAI,EAAIgC,EAAY,MAAM,EAAGhC,CAAC,EAAI,IAAMgC,EAAY,MAAMhC,CAAC,EAC3D,KAAO,IAAI,MAAM,EAAIA,CAAC,EAAE,KAAK,GAAG,EAAI8B,EAAmBrC,EAAG,KAAK,IAAI,EAAGsC,EAAI/B,EAAI,CAAC,CAAC,EAAE,CAAC,CAC3F,CCbe,SAAAwD,GAAS/D,EAAGsC,EAAG,CAC5B,IAAIvC,EAAIsC,EAAmBrC,EAAGsC,CAAC,EAC/B,GAAI,CAACvC,EAAG,OAAOC,EAAI,GACnB,IAAIuC,EAAcxC,EAAE,CAAC,EACjByC,EAAWzC,EAAE,CAAC,EAClB,OAAOyC,EAAW,EAAI,KAAO,IAAI,MAAM,CAACA,CAAQ,EAAE,KAAK,GAAG,EAAID,EACxDA,EAAY,OAASC,EAAW,EAAID,EAAY,MAAM,EAAGC,EAAW,CAAC,EAAI,IAAMD,EAAY,MAAMC,EAAW,CAAC,EAC7GD,EAAc,IAAI,MAAMC,EAAWD,EAAY,OAAS,CAAC,EAAE,KAAK,GAAG,CAC3E,CCNA,MAAeyB,GAAA,CACb,IAAK,CAAChE,EAAGsC,KAAOtC,EAAI,KAAK,QAAQsC,CAAC,EAClC,EAAMtC,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,CAAC,EACpC,EAAMA,GAAMA,EAAI,GAChB,EAAKoC,GACL,EAAK,CAACpC,EAAGsC,IAAMtC,EAAE,cAAcsC,CAAC,EAChC,EAAK,CAACtC,EAAGsC,IAAMtC,EAAE,QAAQsC,CAAC,EAC1B,EAAK,CAACtC,EAAGsC,IAAMtC,EAAE,YAAYsC,CAAC,EAC9B,EAAMtC,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,CAAC,EACpC,EAAK,CAACA,EAAGsC,IAAMyB,GAAc/D,EAAI,IAAKsC,CAAC,EACvC,EAAKyB,GACL,EAAKD,GACL,EAAM9D,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,EAAE,EAAE,YAAa,EACpD,EAAMA,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,EAAE,CACvC,EClBe,SAAQiE,GAACjE,EAAG,CACzB,OAAOA,CACT,CCOA,IAAIkE,GAAM,MAAM,UAAU,IACtBC,GAAW,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,EAEnE,SAAQC,GAACC,EAAQ,CAC9B,IAAIC,EAAQD,EAAO,WAAa,QAAaA,EAAO,YAAc,OAAYE,GAAW9B,GAAYyB,GAAI,KAAKG,EAAO,SAAU,MAAM,EAAGA,EAAO,UAAY,EAAE,EACzJG,EAAiBH,EAAO,WAAa,OAAY,GAAKA,EAAO,SAAS,CAAC,EAAI,GAC3EI,EAAiBJ,EAAO,WAAa,OAAY,GAAKA,EAAO,SAAS,CAAC,EAAI,GAC3EK,EAAUL,EAAO,UAAY,OAAY,IAAMA,EAAO,QAAU,GAChEnB,EAAWmB,EAAO,WAAa,OAAYE,GAAWtB,GAAeiB,GAAI,KAAKG,EAAO,SAAU,MAAM,CAAC,EACtGM,EAAUN,EAAO,UAAY,OAAY,IAAMA,EAAO,QAAU,GAChEO,EAAQP,EAAO,QAAU,OAAY,IAAMA,EAAO,MAAQ,GAC1DQ,EAAMR,EAAO,MAAQ,OAAY,MAAQA,EAAO,IAAM,GAE1D,SAASS,EAAUzB,EAAW,CAC5BA,EAAYD,EAAgBC,CAAS,EAErC,IAAI0B,EAAO1B,EAAU,KACjB2B,EAAQ3B,EAAU,MAClB4B,EAAO5B,EAAU,KACjB6B,EAAS7B,EAAU,OACnB8B,EAAO9B,EAAU,KACjBT,EAAQS,EAAU,MAClB+B,EAAQ/B,EAAU,MAClBgC,EAAYhC,EAAU,UACtBiC,GAAOjC,EAAU,KACjBkC,EAAOlC,EAAU,KAGjBkC,IAAS,KAAKH,EAAQ,GAAMG,EAAO,KAG7BvB,GAAYuB,CAAI,IAAGF,IAAc,SAAcA,EAAY,IAAKC,GAAO,GAAMC,EAAO,MAG1FJ,GAASJ,IAAS,KAAOC,IAAU,OAAMG,EAAO,GAAMJ,EAAO,IAAKC,EAAQ,KAI9E,IAAIQ,GAASN,IAAW,IAAMV,EAAiBU,IAAW,KAAO,SAAS,KAAKK,CAAI,EAAI,IAAMA,EAAK,YAAa,EAAG,GAC9GE,GAASP,IAAW,IAAMT,EAAiB,OAAO,KAAKc,CAAI,EAAIZ,EAAU,GAKzEe,GAAa1B,GAAYuB,CAAI,EAC7BI,GAAc,aAAa,KAAKJ,CAAI,EAMxCF,EAAYA,IAAc,OAAY,EAChC,SAAS,KAAKE,CAAI,EAAI,KAAK,IAAI,EAAG,KAAK,IAAI,GAAIF,CAAS,CAAC,EACzD,KAAK,IAAI,EAAG,KAAK,IAAI,GAAIA,CAAS,CAAC,EAEzC,SAASO,GAAOhF,EAAO,CACrB,IAAIiF,EAAcL,GACdM,EAAcL,GACdlF,EAAGmB,GAAGqE,EAEV,GAAIR,IAAS,IACXO,EAAcJ,GAAW9E,CAAK,EAAIkF,EAClClF,EAAQ,OACH,CACLA,EAAQ,CAACA,EAGT,IAAIoF,EAAgBpF,EAAQ,GAAK,EAAIA,EAAQ,EAiB7C,GAdAA,EAAQ,MAAMA,CAAK,EAAIiE,EAAMa,GAAW,KAAK,IAAI9E,CAAK,EAAGyE,CAAS,EAG9DC,KAAM1E,EAAQ4C,GAAW5C,CAAK,GAG9BoF,GAAiB,CAACpF,GAAU,GAAKqE,IAAS,MAAKe,EAAgB,IAGnEH,GAAeG,EAAiBf,IAAS,IAAMA,EAAOL,EAASK,IAAS,KAAOA,IAAS,IAAM,GAAKA,GAAQY,EAC3GC,GAAeP,IAAS,IAAMpB,GAAS,EAAIN,GAAiB,CAAC,EAAI,IAAMiC,GAAeE,GAAiBf,IAAS,IAAM,IAAM,IAIxHU,IAEF,IADApF,EAAI,GAAImB,GAAId,EAAM,OACX,EAAEL,EAAImB,IACX,GAAIqE,EAAInF,EAAM,WAAWL,CAAC,EAAG,GAAKwF,GAAKA,EAAI,GAAI,CAC7CD,GAAeC,IAAM,GAAKrB,EAAU9D,EAAM,MAAML,EAAI,CAAC,EAAIK,EAAM,MAAML,CAAC,GAAKuF,EAC3ElF,EAAQA,EAAM,MAAM,EAAGL,CAAC,EACxB,QAOJ6E,GAAS,CAACD,IAAMvE,EAAQ0D,EAAM1D,EAAO,GAAQ,GAGjD,IAAIoC,EAAS6C,EAAY,OAASjF,EAAM,OAASkF,EAAY,OACzDG,EAAUjD,EAASJ,EAAQ,IAAI,MAAMA,EAAQI,EAAS,CAAC,EAAE,KAAK+B,CAAI,EAAI,GAM1E,OAHIK,GAASD,IAAMvE,EAAQ0D,EAAM2B,EAAUrF,EAAOqF,EAAQ,OAASrD,EAAQkD,EAAY,OAAS,GAAQ,EAAGG,EAAU,IAG7GjB,EAAK,CACX,IAAK,IAAKpE,EAAQiF,EAAcjF,EAAQkF,EAAcG,EAAS,MAC/D,IAAK,IAAKrF,EAAQiF,EAAcI,EAAUrF,EAAQkF,EAAa,MAC/D,IAAK,IAAKlF,EAAQqF,EAAQ,MAAM,EAAGjD,EAASiD,EAAQ,QAAU,CAAC,EAAIJ,EAAcjF,EAAQkF,EAAcG,EAAQ,MAAMjD,CAAM,EAAG,MAC9H,QAASpC,EAAQqF,EAAUJ,EAAcjF,EAAQkF,EAAa,KAC/D,CAED,OAAO5C,EAAStC,CAAK,CACtB,CAED,OAAAgF,GAAO,SAAW,UAAW,CAC3B,OAAOvC,EAAY,EACzB,EAEWuC,EACR,CAED,SAASM,EAAa7C,EAAWzC,EAAO,CACtC,IAAIjB,EAAImF,GAAWzB,EAAYD,EAAgBC,CAAS,EAAGA,EAAU,KAAO,IAAKA,EAAW,EACxF8C,EAAI,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG,KAAK,MAAM3D,EAAS5B,CAAK,EAAI,CAAC,CAAC,CAAC,EAAI,EACjEwF,EAAI,KAAK,IAAI,GAAI,CAACD,CAAC,EACnBX,EAASrB,GAAS,EAAIgC,EAAI,CAAC,EAC/B,OAAO,SAASvF,EAAO,CACrB,OAAOjB,EAAEyG,EAAIxF,CAAK,EAAI4E,CAC5B,CACG,CAED,MAAO,CACL,OAAQV,EACR,aAAcoB,CAClB,CACA,CCjJA,IAAI7B,EACOuB,GACAM,GAEXG,GAAc,CACZ,UAAW,IACX,SAAU,CAAC,CAAC,EACZ,SAAU,CAAC,IAAK,EAAE,CACpB,CAAC,EAEc,SAASA,GAAcC,EAAY,CAChD,OAAAjC,EAASD,GAAakC,CAAU,EAChCV,GAASvB,EAAO,OAChB6B,GAAe7B,EAAO,aACfA,CACT,CCfe,SAAQkC,GAAC5E,EAAM,CAC5B,OAAO,KAAK,IAAI,EAAG,CAACa,EAAS,KAAK,IAAIb,CAAI,CAAC,CAAC,CAC9C,CCFe,SAAA6E,GAAS7E,EAAMf,EAAO,CACnC,OAAO,KAAK,IAAI,EAAG,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG,KAAK,MAAM4B,EAAS5B,CAAK,EAAI,CAAC,CAAC,CAAC,EAAI,EAAI4B,EAAS,KAAK,IAAIb,CAAI,CAAC,CAAC,CAC9G,CCFe,SAAA8E,GAAS9E,EAAM+E,EAAK,CACjC,OAAA/E,EAAO,KAAK,IAAIA,CAAI,EAAG+E,EAAM,KAAK,IAAIA,CAAG,EAAI/E,EACtC,KAAK,IAAI,EAAGa,EAASkE,CAAG,EAAIlE,EAASb,CAAI,CAAC,EAAI,CACvD,CCLO,SAASgF,GAAUC,EAAQC,EAAO,CACvC,OAAQ,UAAU,OAAM,CACtB,IAAK,GAAG,MACR,IAAK,GAAG,KAAK,MAAMD,CAAM,EAAG,MAC5B,QAAS,KAAK,MAAMC,CAAK,EAAE,OAAOD,CAAM,EAAG,KAC5C,CACD,OAAO,IACT,CAEO,SAASE,GAAiBF,EAAQG,EAAc,CACrD,OAAQ,UAAU,OAAM,CACtB,IAAK,GAAG,MACR,IAAK,GAAG,CACF,OAAOH,GAAW,WAAY,KAAK,aAAaA,CAAM,EACrD,KAAK,MAAMA,CAAM,EACtB,KACD,CACD,QAAS,CACP,KAAK,OAAOA,CAAM,EACd,OAAOG,GAAiB,WAAY,KAAK,aAAaA,CAAY,EACjE,KAAK,MAAMA,CAAY,EAC5B,KACD,CACF,CACD,OAAO,IACT,CCzBe,SAAAC,GAASC,EAAaC,EAASC,EAAW,CACvDF,EAAY,UAAYC,EAAQ,UAAYC,EAC5CA,EAAU,YAAcF,CAC1B,CAEO,SAASG,GAAOC,EAAQf,EAAY,CACzC,IAAIa,EAAY,OAAO,OAAOE,EAAO,SAAS,EAC9C,QAASC,KAAOhB,EAAYa,EAAUG,CAAG,EAAIhB,EAAWgB,CAAG,EAC3D,OAAOH,CACT,CCPO,SAASI,GAAQ,CAAE,CAEhB,IAACC,EAAS,GACTC,EAAW,EAAID,EAEtBE,EAAM,sBACNC,EAAM,gDACNC,EAAM,iDACNC,GAAQ,qBACRC,GAAe,IAAI,OAAO,UAAY,CAACJ,EAAKA,EAAKA,CAAG,EAAI,MAAM,EAC9DK,GAAe,IAAI,OAAO,UAAY,CAACH,EAAKA,EAAKA,CAAG,EAAI,MAAM,EAC9DI,GAAgB,IAAI,OAAO,WAAa,CAACN,EAAKA,EAAKA,EAAKC,CAAG,EAAI,MAAM,EACrEM,GAAgB,IAAI,OAAO,WAAa,CAACL,EAAKA,EAAKA,EAAKD,CAAG,EAAI,MAAM,EACrEO,GAAe,IAAI,OAAO,UAAY,CAACP,EAAKC,EAAKA,CAAG,EAAI,MAAM,EAC9DO,GAAgB,IAAI,OAAO,WAAa,CAACR,EAAKC,EAAKA,EAAKD,CAAG,EAAI,MAAM,EAErES,GAAQ,CACV,UAAW,SACX,aAAc,SACd,KAAM,MACN,WAAY,QACZ,MAAO,SACP,MAAO,SACP,OAAQ,SACR,MAAO,EACP,eAAgB,SAChB,KAAM,IACN,WAAY,QACZ,MAAO,SACP,UAAW,SACX,UAAW,QACX,WAAY,QACZ,UAAW,SACX,MAAO,SACP,eAAgB,QAChB,SAAU,SACV,QAAS,SACT,KAAM,MACN,SAAU,IACV,SAAU,MACV,cAAe,SACf,SAAU,SACV,UAAW,MACX,SAAU,SACV,UAAW,SACX,YAAa,QACb,eAAgB,QAChB,WAAY,SACZ,WAAY,SACZ,QAAS,QACT,WAAY,SACZ,aAAc,QACd,cAAe,QACf,cAAe,QACf,cAAe,QACf,cAAe,MACf,WAAY,QACZ,SAAU,SACV,YAAa,MACb,QAAS,QACT,QAAS,QACT,WAAY,QACZ,UAAW,SACX,YAAa,SACb,YAAa,QACb,QAAS,SACT,UAAW,SACX,WAAY,SACZ,KAAM,SACN,UAAW,SACX,KAAM,QACN,MAAO,MACP,YAAa,SACb,KAAM,QACN,SAAU,SACV,QAAS,SACT,UAAW,SACX,OAAQ,QACR,MAAO,SACP,MAAO,SACP,SAAU,SACV,cAAe,SACf,UAAW,QACX,aAAc,SACd,UAAW,SACX,WAAY,SACZ,UAAW,SACX,qBAAsB,SACtB,UAAW,SACX,WAAY,QACZ,UAAW,SACX,UAAW,SACX,YAAa,SACb,cAAe,QACf,aAAc,QACd,eAAgB,QAChB,eAAgB,QAChB,eAAgB,SAChB,YAAa,SACb,KAAM,MACN,UAAW,QACX,MAAO,SACP,QAAS,SACT,OAAQ,QACR,iBAAkB,QAClB,WAAY,IACZ,aAAc,SACd,aAAc,QACd,eAAgB,QAChB,gBAAiB,QACjB,kBAAmB,MACnB,gBAAiB,QACjB,gBAAiB,SACjB,aAAc,QACd,UAAW,SACX,UAAW,SACX,SAAU,SACV,YAAa,SACb,KAAM,IACN,QAAS,SACT,MAAO,QACP,UAAW,QACX,OAAQ,SACR,UAAW,SACX,OAAQ,SACR,cAAe,SACf,UAAW,SACX,cAAe,SACf,cAAe,SACf,WAAY,SACZ,UAAW,SACX,KAAM,SACN,KAAM,SACN,KAAM,SACN,WAAY,SACZ,OAAQ,QACR,cAAe,QACf,IAAK,SACL,UAAW,SACX,UAAW,QACX,YAAa,QACb,OAAQ,SACR,WAAY,SACZ,SAAU,QACV,SAAU,SACV,OAAQ,SACR,OAAQ,SACR,QAAS,QACT,UAAW,QACX,UAAW,QACX,UAAW,QACX,KAAM,SACN,YAAa,MACb,UAAW,QACX,IAAK,SACL,KAAM,MACN,QAAS,SACT,OAAQ,SACR,UAAW,QACX,OAAQ,SACR,MAAO,SACP,MAAO,SACP,WAAY,SACZ,OAAQ,SACR,YAAa,QACf,EAEApB,GAAOO,EAAOc,EAAO,CACnB,KAAM,SAASC,EAAU,CACvB,OAAO,OAAO,OAAO,IAAI,KAAK,YAAa,KAAMA,CAAQ,CAC1D,EACD,YAAa,UAAW,CACtB,OAAO,KAAK,MAAM,aACnB,EACD,IAAKC,GACL,UAAWA,GACX,UAAWC,GACX,UAAWC,GACX,SAAUA,EACZ,CAAC,EAED,SAASF,IAAkB,CACzB,OAAO,KAAK,MAAM,WACpB,CAEA,SAASC,IAAkB,CACzB,OAAOE,GAAW,IAAI,EAAE,WAC1B,CAEA,SAASD,IAAkB,CACzB,OAAO,KAAK,MAAM,WACpB,CAEe,SAASJ,EAAMzC,EAAQ,CACpC,IAAI+C,EAAGC,EACP,OAAAhD,GAAUA,EAAS,IAAI,KAAM,EAAC,YAAW,GACjC+C,EAAId,GAAM,KAAKjC,CAAM,IAAMgD,EAAID,EAAE,CAAC,EAAE,OAAQA,EAAI,SAASA,EAAE,CAAC,EAAG,EAAE,EAAGC,IAAM,EAAIC,GAAKF,CAAC,EACtFC,IAAM,EAAI,IAAIE,EAAKH,GAAK,EAAI,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,EAAI,KAASA,EAAI,KAAQ,EAAMA,EAAI,GAAM,CAAC,EAChHC,IAAM,EAAIG,EAAKJ,GAAK,GAAK,IAAMA,GAAK,GAAK,IAAMA,GAAK,EAAI,KAAOA,EAAI,KAAQ,GAAI,EAC/EC,IAAM,EAAIG,EAAMJ,GAAK,GAAK,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,EAAI,MAAUA,EAAI,KAAQ,EAAMA,EAAI,IAAQ,GAAI,EACtJ,OACCA,EAAIb,GAAa,KAAKlC,CAAM,GAAK,IAAIkD,EAAIH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAG,CAAC,GAC5DA,EAAIZ,GAAa,KAAKnC,CAAM,GAAK,IAAIkD,EAAIH,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAK,CAAC,GAChGA,EAAIX,GAAc,KAAKpC,CAAM,GAAKmD,EAAKJ,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,GAC7DA,EAAIV,GAAc,KAAKrC,CAAM,GAAKmD,EAAKJ,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,CAAC,GACjGA,EAAIT,GAAa,KAAKtC,CAAM,GAAKoD,GAAKL,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,IAAKA,EAAE,CAAC,EAAI,IAAK,CAAC,GACrEA,EAAIR,GAAc,KAAKvC,CAAM,GAAKoD,GAAKL,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,IAAKA,EAAE,CAAC,EAAI,IAAKA,EAAE,CAAC,CAAC,EAC1EP,GAAM,eAAexC,CAAM,EAAIiD,GAAKT,GAAMxC,CAAM,CAAC,EACjDA,IAAW,cAAgB,IAAIkD,EAAI,IAAK,IAAK,IAAK,CAAC,EACnD,IACR,CAEA,SAASD,GAAK,EAAG,CACf,OAAO,IAAIC,EAAI,GAAK,GAAK,IAAM,GAAK,EAAI,IAAM,EAAI,IAAM,CAAC,CAC3D,CAEA,SAASC,EAAKE,EAAGlG,EAAGtD,EAAGD,EAAG,CACxB,OAAIA,GAAK,IAAGyJ,EAAIlG,EAAItD,EAAI,KACjB,IAAIqJ,EAAIG,EAAGlG,EAAGtD,EAAGD,CAAC,CAC3B,CAEO,SAAS0J,GAAWC,EAAG,CAE5B,OADMA,aAAa5B,IAAQ4B,EAAId,EAAMc,CAAC,GACjCA,GACLA,EAAIA,EAAE,MACC,IAAIL,EAAIK,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,GAFxB,IAAIL,CAGrB,CAEO,SAASM,EAAIH,EAAGlG,EAAGtD,EAAG4J,EAAS,CACpC,OAAO,UAAU,SAAW,EAAIH,GAAWD,CAAC,EAAI,IAAIH,EAAIG,EAAGlG,EAAGtD,EAAG4J,GAAkB,CAAW,CAChG,CAEO,SAASP,EAAIG,EAAGlG,EAAGtD,EAAG4J,EAAS,CACpC,KAAK,EAAI,CAACJ,EACV,KAAK,EAAI,CAAClG,EACV,KAAK,EAAI,CAACtD,EACV,KAAK,QAAU,CAAC4J,CAClB,CAEArC,GAAO8B,EAAKM,EAAKhC,GAAOG,EAAO,CAC7B,SAAU,SAASnB,EAAG,CACpB,OAAAA,EAAIA,GAAK,KAAOqB,EAAW,KAAK,IAAIA,EAAUrB,CAAC,EACxC,IAAI0C,EAAI,KAAK,EAAI1C,EAAG,KAAK,EAAIA,EAAG,KAAK,EAAIA,EAAG,KAAK,OAAO,CAChE,EACD,OAAQ,SAASA,EAAG,CAClB,OAAAA,EAAIA,GAAK,KAAOoB,EAAS,KAAK,IAAIA,EAAQpB,CAAC,EACpC,IAAI0C,EAAI,KAAK,EAAI1C,EAAG,KAAK,EAAIA,EAAG,KAAK,EAAIA,EAAG,KAAK,OAAO,CAChE,EACD,IAAK,UAAW,CACd,OAAO,IACR,EACD,YAAa,UAAW,CACtB,MAAQ,KAAQ,KAAK,GAAK,KAAK,EAAI,OAC3B,KAAQ,KAAK,GAAK,KAAK,EAAI,OAC3B,KAAQ,KAAK,GAAK,KAAK,EAAI,OAC3B,GAAK,KAAK,SAAW,KAAK,SAAW,CAC9C,EACD,IAAKkD,GACL,UAAWA,GACX,UAAWC,GACX,SAAUA,EACZ,CAAC,CAAC,EAEF,SAASD,IAAgB,CACvB,MAAO,IAAME,EAAI,KAAK,CAAC,EAAIA,EAAI,KAAK,CAAC,EAAIA,EAAI,KAAK,CAAC,CACrD,CAEA,SAASD,IAAgB,CACvB,IAAI/J,EAAI,KAAK,QAAS,OAAAA,EAAI,MAAMA,CAAC,EAAI,EAAI,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,CAAC,CAAC,GAC3DA,IAAM,EAAI,OAAS,SACrB,KAAK,IAAI,EAAG,KAAK,IAAI,IAAK,KAAK,MAAM,KAAK,CAAC,GAAK,CAAC,CAAC,EAAI,KACtD,KAAK,IAAI,EAAG,KAAK,IAAI,IAAK,KAAK,MAAM,KAAK,CAAC,GAAK,CAAC,CAAC,EAAI,KACtD,KAAK,IAAI,EAAG,KAAK,IAAI,IAAK,KAAK,MAAM,KAAK,CAAC,GAAK,CAAC,CAAC,GACjDA,IAAM,EAAI,IAAM,KAAOA,EAAI,IACpC,CAEA,SAASgK,EAAI5I,EAAO,CAClB,OAAAA,EAAQ,KAAK,IAAI,EAAG,KAAK,IAAI,IAAK,KAAK,MAAMA,CAAK,GAAK,CAAC,CAAC,GACjDA,EAAQ,GAAK,IAAM,IAAMA,EAAM,SAAS,EAAE,CACpD,CAEA,SAASoI,GAAKS,EAAGhG,EAAGmF,EAAGpJ,EAAG,CACxB,OAAIA,GAAK,EAAGiK,EAAIhG,EAAImF,EAAI,IACfA,GAAK,GAAKA,GAAK,EAAGa,EAAIhG,EAAI,IAC1BA,GAAK,IAAGgG,EAAI,KACd,IAAIC,EAAID,EAAGhG,EAAGmF,EAAGpJ,CAAC,CAC3B,CAEO,SAASkJ,GAAWS,EAAG,CAC5B,GAAIA,aAAaO,EAAK,OAAO,IAAIA,EAAIP,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,EAE7D,GADMA,aAAa5B,IAAQ4B,EAAId,EAAMc,CAAC,GAClC,CAACA,EAAG,OAAO,IAAIO,EACnB,GAAIP,aAAaO,EAAK,OAAOP,EAC7BA,EAAIA,EAAE,MACN,IAAIF,EAAIE,EAAE,EAAI,IACVpG,EAAIoG,EAAE,EAAI,IACV1J,EAAI0J,EAAE,EAAI,IACVQ,EAAM,KAAK,IAAIV,EAAGlG,EAAGtD,CAAC,EACtBiH,EAAM,KAAK,IAAIuC,EAAGlG,EAAGtD,CAAC,EACtBgK,EAAI,IACJhG,EAAIiD,EAAMiD,EACVf,GAAKlC,EAAMiD,GAAO,EACtB,OAAIlG,GACEwF,IAAMvC,EAAK+C,GAAK1G,EAAItD,GAAKgE,GAAKV,EAAItD,GAAK,EAClCsD,IAAM2D,EAAK+C,GAAKhK,EAAIwJ,GAAKxF,EAAI,EACjCgG,GAAKR,EAAIlG,GAAKU,EAAI,EACvBA,GAAKmF,EAAI,GAAMlC,EAAMiD,EAAM,EAAIjD,EAAMiD,EACrCF,GAAK,IAELhG,EAAImF,EAAI,GAAKA,EAAI,EAAI,EAAIa,EAEpB,IAAIC,EAAID,EAAGhG,EAAGmF,EAAGO,EAAE,OAAO,CACnC,CAEO,SAASS,GAAIH,EAAGhG,EAAGmF,EAAGS,EAAS,CACpC,OAAO,UAAU,SAAW,EAAIX,GAAWe,CAAC,EAAI,IAAIC,EAAID,EAAGhG,EAAGmF,EAAGS,GAAkB,CAAW,CAChG,CAEA,SAASK,EAAID,EAAGhG,EAAGmF,EAAGS,EAAS,CAC7B,KAAK,EAAI,CAACI,EACV,KAAK,EAAI,CAAChG,EACV,KAAK,EAAI,CAACmF,EACV,KAAK,QAAU,CAACS,CAClB,CAEArC,GAAO0C,EAAKE,GAAKxC,GAAOG,EAAO,CAC7B,SAAU,SAASnB,EAAG,CACpB,OAAAA,EAAIA,GAAK,KAAOqB,EAAW,KAAK,IAAIA,EAAUrB,CAAC,EACxC,IAAIsD,EAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAItD,EAAG,KAAK,OAAO,CACxD,EACD,OAAQ,SAASA,EAAG,CAClB,OAAAA,EAAIA,GAAK,KAAOoB,EAAS,KAAK,IAAIA,EAAQpB,CAAC,EACpC,IAAIsD,EAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAItD,EAAG,KAAK,OAAO,CACxD,EACD,IAAK,UAAW,CACd,IAAIqD,EAAI,KAAK,EAAI,KAAO,KAAK,EAAI,GAAK,IAClChG,EAAI,MAAMgG,CAAC,GAAK,MAAM,KAAK,CAAC,EAAI,EAAI,KAAK,EACzCb,EAAI,KAAK,EACTiB,EAAKjB,GAAKA,EAAI,GAAMA,EAAI,EAAIA,GAAKnF,EACjCqG,EAAK,EAAIlB,EAAIiB,EACjB,OAAO,IAAIf,EACTiB,EAAQN,GAAK,IAAMA,EAAI,IAAMA,EAAI,IAAKK,EAAID,CAAE,EAC5CE,EAAQN,EAAGK,EAAID,CAAE,EACjBE,EAAQN,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAKK,EAAID,CAAE,EAC3C,KAAK,OACX,CACG,EACD,YAAa,UAAW,CACtB,OAAQ,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,MAAM,KAAK,CAAC,IAC1C,GAAK,KAAK,GAAK,KAAK,GAAK,GACzB,GAAK,KAAK,SAAW,KAAK,SAAW,CAC9C,EACD,UAAW,UAAW,CACpB,IAAIrK,EAAI,KAAK,QAAS,OAAAA,EAAI,MAAMA,CAAC,EAAI,EAAI,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,CAAC,CAAC,GAC3DA,IAAM,EAAI,OAAS,UACpB,KAAK,GAAK,GAAK,MACf,KAAK,GAAK,GAAK,IAAM,OACrB,KAAK,GAAK,GAAK,IAAM,KACrBA,IAAM,EAAI,IAAM,KAAOA,EAAI,IACnC,CACH,CAAC,CAAC,EAGF,SAASuK,EAAQN,EAAGK,EAAID,EAAI,CAC1B,OAAQJ,EAAI,GAAKK,GAAMD,EAAKC,GAAML,EAAI,GAChCA,EAAI,IAAMI,EACVJ,EAAI,IAAMK,GAAMD,EAAKC,IAAO,IAAML,GAAK,GACvCK,GAAM,GACd,CClXO,SAASE,GAAMC,EAAIC,EAAIC,EAAIC,EAAIC,EAAI,CACxC,IAAIC,EAAKL,EAAKA,EAAIM,EAAKD,EAAKL,EAC5B,QAAS,EAAI,EAAIA,EAAK,EAAIK,EAAKC,GAAML,GAC9B,EAAI,EAAII,EAAK,EAAIC,GAAMJ,GACvB,EAAI,EAAIF,EAAK,EAAIK,EAAK,EAAIC,GAAMH,EACjCG,EAAKF,GAAM,CACnB,CAEe,SAAQG,GAAC9J,EAAQ,CAC9B,IAAIgB,EAAIhB,EAAO,OAAS,EACxB,OAAO,SAASmC,EAAG,CACjB,IAAItC,EAAIsC,GAAK,EAAKA,EAAI,EAAKA,GAAK,GAAKA,EAAI,EAAGnB,EAAI,GAAK,KAAK,MAAMmB,EAAInB,CAAC,EACjEyI,EAAKzJ,EAAOH,CAAC,EACb6J,EAAK1J,EAAOH,EAAI,CAAC,EACjB2J,EAAK3J,EAAI,EAAIG,EAAOH,EAAI,CAAC,EAAI,EAAI4J,EAAKC,EACtCC,EAAK9J,EAAImB,EAAI,EAAIhB,EAAOH,EAAI,CAAC,EAAI,EAAI6J,EAAKD,EAC9C,OAAOH,IAAOnH,EAAItC,EAAImB,GAAKA,EAAGwI,EAAIC,EAAIC,EAAIC,CAAE,CAChD,CACA,CChBe,SAAQI,GAAC/J,EAAQ,CAC9B,IAAIgB,EAAIhB,EAAO,OACf,OAAO,SAASmC,EAAG,CACjB,IAAItC,EAAI,KAAK,QAAQsC,GAAK,GAAK,EAAI,EAAEA,EAAIA,GAAKnB,CAAC,EAC3CwI,EAAKxJ,GAAQH,EAAImB,EAAI,GAAKA,CAAC,EAC3ByI,EAAKzJ,EAAOH,EAAImB,CAAC,EACjB0I,EAAK1J,GAAQH,EAAI,GAAKmB,CAAC,EACvB2I,EAAK3J,GAAQH,EAAI,GAAKmB,CAAC,EAC3B,OAAOsI,IAAOnH,EAAItC,EAAImB,GAAKA,EAAGwI,EAAIC,EAAIC,EAAIC,CAAE,CAChD,CACA,CCZA,MAAeK,EAAA1K,GAAK,IAAMA,ECE1B,SAAS2K,GAAOnL,EAAGO,EAAG,CACpB,OAAO,SAAS8C,EAAG,CACjB,OAAOrD,EAAIqD,EAAI9C,CACnB,CACA,CAEA,SAAS6K,GAAYpL,EAAGC,EAAGoL,EAAG,CAC5B,OAAOrL,EAAI,KAAK,IAAIA,EAAGqL,CAAC,EAAGpL,EAAI,KAAK,IAAIA,EAAGoL,CAAC,EAAIrL,EAAGqL,EAAI,EAAIA,EAAG,SAAS,EAAG,CACxE,OAAO,KAAK,IAAIrL,EAAI,EAAIC,EAAGoL,CAAC,CAChC,CACA,CAEO,SAASC,GAAItL,EAAGC,EAAG,CACxB,IAAIM,EAAIN,EAAID,EACZ,OAAOO,EAAI4K,GAAOnL,EAAGO,EAAI,KAAOA,EAAI,KAAOA,EAAI,IAAM,KAAK,MAAMA,EAAI,GAAG,EAAIA,CAAC,EAAI2K,EAAS,MAAMlL,CAAC,EAAIC,EAAID,CAAC,CAC3G,CAEO,SAASuL,GAAMF,EAAG,CACvB,OAAQA,EAAI,CAACA,IAAO,EAAIG,GAAU,SAASxL,EAAGC,EAAG,CAC/C,OAAOA,EAAID,EAAIoL,GAAYpL,EAAGC,EAAGoL,CAAC,EAAIH,EAAS,MAAMlL,CAAC,EAAIC,EAAID,CAAC,CACnE,CACA,CAEe,SAASwL,GAAQxL,EAAGC,EAAG,CACpC,IAAIM,EAAIN,EAAID,EACZ,OAAOO,EAAI4K,GAAOnL,EAAGO,CAAC,EAAI2K,EAAS,MAAMlL,CAAC,EAAIC,EAAID,CAAC,CACrD,CCvBA,MAAA4J,GAAgB,SAAS6B,EAASJ,EAAG,CACnC,IAAIxC,EAAQ0C,GAAMF,CAAC,EAEnB,SAASzB,EAAI9H,EAAO4J,EAAK,CACvB,IAAIjC,EAAIZ,GAAO/G,EAAQ6J,EAAS7J,CAAK,GAAG,GAAI4J,EAAMC,EAASD,CAAG,GAAG,CAAC,EAC9DnI,EAAIsF,EAAM/G,EAAM,EAAG4J,EAAI,CAAC,EACxBzL,EAAI4I,EAAM/G,EAAM,EAAG4J,EAAI,CAAC,EACxB7B,EAAU2B,GAAQ1J,EAAM,QAAS4J,EAAI,OAAO,EAChD,OAAO,SAASrI,EAAG,CACjB,OAAAvB,EAAM,EAAI2H,EAAEpG,CAAC,EACbvB,EAAM,EAAIyB,EAAEF,CAAC,EACbvB,EAAM,EAAI7B,EAAEoD,CAAC,EACbvB,EAAM,QAAU+H,EAAQxG,CAAC,EAClBvB,EAAQ,EACrB,CACG,CAED,OAAA8H,EAAI,MAAQ6B,EAEL7B,CACT,EAAG,CAAC,EAEJ,SAASgC,GAAUC,EAAQ,CACzB,OAAO,SAASC,EAAQ,CACtB,IAAI5J,EAAI4J,EAAO,OACXrC,EAAI,IAAI,MAAMvH,CAAC,EACfqB,EAAI,IAAI,MAAMrB,CAAC,EACfjC,EAAI,IAAI,MAAMiC,CAAC,EACfnB,EAAG8H,EACP,IAAK9H,EAAI,EAAGA,EAAImB,EAAG,EAAEnB,EACnB8H,EAAQ8C,EAASG,EAAO/K,CAAC,CAAC,EAC1B0I,EAAE1I,CAAC,EAAI8H,EAAM,GAAK,EAClBtF,EAAExC,CAAC,EAAI8H,EAAM,GAAK,EAClB5I,EAAEc,CAAC,EAAI8H,EAAM,GAAK,EAEpB,OAAAY,EAAIoC,EAAOpC,CAAC,EACZlG,EAAIsI,EAAOtI,CAAC,EACZtD,EAAI4L,EAAO5L,CAAC,EACZ4I,EAAM,QAAU,EACT,SAASxF,EAAG,CACjB,OAAAwF,EAAM,EAAIY,EAAEpG,CAAC,EACbwF,EAAM,EAAItF,EAAEF,CAAC,EACbwF,EAAM,EAAI5I,EAAEoD,CAAC,EACNwF,EAAQ,EACrB,CACA,CACA,CAEU,IAACkD,GAAWH,GAAUpB,EAAK,EAC1BwB,GAAiBJ,GAAUX,EAAW,ECtDlC,SAAAgB,GAASjM,EAAGC,EAAG,CACvBA,IAAGA,EAAI,IACZ,IAAIiC,EAAIlC,EAAI,KAAK,IAAIC,EAAE,OAAQD,EAAE,MAAM,EAAI,EACvCuG,EAAItG,EAAE,MAAO,EACb,EACJ,OAAO,SAASoD,EAAG,CACjB,IAAK,EAAI,EAAG,EAAInB,EAAG,EAAE,EAAGqE,EAAE,CAAC,EAAIvG,EAAE,CAAC,GAAK,EAAIqD,GAAKpD,EAAE,CAAC,EAAIoD,EACvD,OAAOkD,CACX,CACA,CAEO,SAAS2F,GAAc1L,EAAG,CAC/B,OAAO,YAAY,OAAOA,CAAC,GAAK,EAAEA,aAAa,SACjD,CCVe,SAAA2L,GAASnM,EAAGC,EAAG,CAC5B,OAAQiM,GAAcjM,CAAC,EAAIgM,GAAcG,IAAcpM,EAAGC,CAAC,CAC7D,CAEO,SAASmM,GAAapM,EAAGC,EAAG,CACjC,IAAIoM,EAAKpM,EAAIA,EAAE,OAAS,EACpBqM,EAAKtM,EAAI,KAAK,IAAIqM,EAAIrM,EAAE,MAAM,EAAI,EAClCQ,EAAI,IAAI,MAAM8L,CAAE,EAChB/F,EAAI,IAAI,MAAM8F,CAAE,EAChBtL,EAEJ,IAAKA,EAAI,EAAGA,EAAIuL,EAAI,EAAEvL,EAAGP,EAAEO,CAAC,EAAIK,GAAMpB,EAAEe,CAAC,EAAGd,EAAEc,CAAC,CAAC,EAChD,KAAOA,EAAIsL,EAAI,EAAEtL,EAAGwF,EAAExF,CAAC,EAAId,EAAEc,CAAC,EAE9B,OAAO,SAASsC,EAAG,CACjB,IAAKtC,EAAI,EAAGA,EAAIuL,EAAI,EAAEvL,EAAGwF,EAAExF,CAAC,EAAIP,EAAEO,CAAC,EAAEsC,CAAC,EACtC,OAAOkD,CACX,CACA,CCrBe,SAAAgG,GAASvM,EAAGC,EAAG,CAC5B,IAAIM,EAAI,IAAI,KACZ,OAAOP,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAG,SAAS,EAAG,CACjC,OAAOM,EAAE,QAAQP,GAAK,EAAI,GAAKC,EAAI,CAAC,EAAGM,CAC3C,CACA,CCLe,SAAAiM,EAASxM,EAAGC,EAAG,CAC5B,OAAOD,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAG,SAASoD,EAAG,CACjC,OAAOrD,GAAK,EAAIqD,GAAKpD,EAAIoD,CAC7B,CACA,CCFe,SAAAoJ,GAASzM,EAAGC,EAAG,CAC5B,IAAIc,EAAI,CAAE,EACNwF,EAAI,CAAE,EACNK,GAEA5G,IAAM,MAAQ,OAAOA,GAAM,YAAUA,EAAI,KACzCC,IAAM,MAAQ,OAAOA,GAAM,YAAUA,EAAI,IAE7C,IAAK2G,KAAK3G,EACJ2G,KAAK5G,EACPe,EAAE6F,CAAC,EAAIxF,GAAMpB,EAAE4G,CAAC,EAAG3G,EAAE2G,CAAC,CAAC,EAEvBL,EAAEK,CAAC,EAAI3G,EAAE2G,CAAC,EAId,OAAO,SAASvD,EAAG,CACjB,IAAKuD,KAAK7F,EAAGwF,EAAEK,CAAC,EAAI7F,EAAE6F,CAAC,EAAEvD,CAAC,EAC1B,OAAOkD,CACX,CACA,CCpBA,IAAImG,GAAM,8CACNC,EAAM,IAAI,OAAOD,GAAI,OAAQ,GAAG,EAEpC,SAAS/G,GAAK1F,EAAG,CACf,OAAO,UAAW,CAChB,OAAOA,CACX,CACA,CAEA,SAAS2M,GAAI3M,EAAG,CACd,OAAO,SAASoD,EAAG,CACjB,OAAOpD,EAAEoD,CAAC,EAAI,EAClB,CACA,CAEe,SAAAwJ,GAAS7M,EAAGC,EAAG,CAC5B,IAAI6M,EAAKJ,GAAI,UAAYC,EAAI,UAAY,EACrCI,EACAC,EACAC,EACAlM,EAAI,GACJkD,EAAI,CAAE,EACNiJ,EAAI,CAAA,EAMR,IAHAlN,EAAIA,EAAI,GAAIC,EAAIA,EAAI,IAGZ8M,EAAKL,GAAI,KAAK1M,CAAC,KACfgN,EAAKL,EAAI,KAAK1M,CAAC,KAChBgN,EAAKD,EAAG,OAASF,IACpBG,EAAKhN,EAAE,MAAM6M,EAAIG,CAAE,EACfhJ,EAAElD,CAAC,EAAGkD,EAAElD,CAAC,GAAKkM,EACbhJ,EAAE,EAAElD,CAAC,EAAIkM,IAEXF,EAAKA,EAAG,CAAC,MAAQC,EAAKA,EAAG,CAAC,GACzB/I,EAAElD,CAAC,EAAGkD,EAAElD,CAAC,GAAKiM,EACb/I,EAAE,EAAElD,CAAC,EAAIiM,GAEd/I,EAAE,EAAElD,CAAC,EAAI,KACTmM,EAAE,KAAK,CAAC,EAAGnM,EAAG,EAAGC,EAAO+L,EAAIC,CAAE,CAAC,CAAC,GAElCF,EAAKH,EAAI,UAIX,OAAIG,EAAK7M,EAAE,SACTgN,EAAKhN,EAAE,MAAM6M,CAAE,EACX7I,EAAElD,CAAC,EAAGkD,EAAElD,CAAC,GAAKkM,EACbhJ,EAAE,EAAElD,CAAC,EAAIkM,GAKThJ,EAAE,OAAS,EAAKiJ,EAAE,CAAC,EACpBN,GAAIM,EAAE,CAAC,EAAE,CAAC,EACVvH,GAAK1F,CAAC,GACLA,EAAIiN,EAAE,OAAQ,SAAS7J,EAAG,CACzB,QAAStC,EAAI,EAAG4I,EAAG5I,EAAId,EAAG,EAAEc,EAAGkD,GAAG0F,EAAIuD,EAAEnM,CAAC,GAAG,CAAC,EAAI4I,EAAE,EAAEtG,CAAC,EACtD,OAAOY,EAAE,KAAK,EAAE,CAC1B,EACA,CCrDe,SAAAkJ,GAASnN,EAAGC,EAAG,CAC5B,IAAIoD,EAAI,OAAOpD,EAAGsG,EAClB,OAAOtG,GAAK,MAAQoD,IAAM,UAAY6H,EAASjL,CAAC,GACzCoD,IAAM,SAAWrC,EAClBqC,IAAM,UAAakD,EAAIsC,EAAM5I,CAAC,IAAMA,EAAIsG,EAAGqD,IAAOiD,GAClD5M,aAAa4I,EAAQe,GACrB3J,aAAa,KAAOsM,GACpBL,GAAcjM,CAAC,EAAIgM,GACnB,MAAM,QAAQhM,CAAC,EAAImM,GACnB,OAAOnM,EAAE,SAAY,YAAc,OAAOA,EAAE,UAAa,YAAc,MAAMA,CAAC,EAAIwM,GAClFzL,GAAQhB,EAAGC,CAAC,CACpB,CCrBe,SAAAmN,GAASpN,EAAGC,EAAG,CAC5B,OAAOD,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAG,SAASoD,EAAG,CACjC,OAAO,KAAK,MAAMrD,GAAK,EAAIqD,GAAKpD,EAAIoD,CAAC,CACzC,CACA,CCJe,SAASgK,GAAU7M,EAAG,CACnC,OAAO,UAAW,CAChB,OAAOA,CACX,CACA,CCJe,SAASQ,GAAOR,EAAG,CAChC,MAAO,CAACA,CACV,CCGA,IAAI8M,GAAO,CAAC,EAAG,CAAC,EAET,SAASvI,EAASvE,EAAG,CAC1B,OAAOA,CACT,CAEA,SAAS+M,GAAUvN,EAAGC,EAAG,CACvB,OAAQA,GAAMD,EAAI,CAACA,GACb,SAASQ,EAAG,CAAE,OAAQA,EAAIR,GAAKC,CAAI,EACnCiL,GAAS,MAAMjL,CAAC,EAAI,IAAM,EAAG,CACrC,CAEA,SAASuN,GAAQxN,EAAGC,EAAG,CACrB,IAAIoD,EACJ,OAAIrD,EAAIC,IAAGoD,EAAIrD,EAAGA,EAAIC,EAAGA,EAAIoD,GACtB,SAAS7C,EAAG,CAAE,OAAO,KAAK,IAAIR,EAAG,KAAK,IAAIC,EAAGO,CAAC,CAAC,CAAE,CAC1D,CAIA,SAASiN,GAAMrG,EAAQC,EAAOqG,EAAa,CACzC,IAAIC,EAAKvG,EAAO,CAAC,EAAGwG,EAAKxG,EAAO,CAAC,EAAG/E,EAAKgF,EAAM,CAAC,EAAG/E,EAAK+E,EAAM,CAAC,EAC/D,OAAIuG,EAAKD,GAAIA,EAAKJ,GAAUK,EAAID,CAAE,EAAGtL,EAAKqL,EAAYpL,EAAID,CAAE,IACvDsL,EAAKJ,GAAUI,EAAIC,CAAE,EAAGvL,EAAKqL,EAAYrL,EAAIC,CAAE,GAC7C,SAAS9B,EAAG,CAAE,OAAO6B,EAAGsL,EAAGnN,CAAC,CAAC,EACtC,CAEA,SAASqN,GAAQzG,EAAQC,EAAOqG,EAAa,CAC3C,IAAIpK,EAAI,KAAK,IAAI8D,EAAO,OAAQC,EAAM,MAAM,EAAI,EAC5C9G,EAAI,IAAI,MAAM+C,CAAC,EACfmG,EAAI,IAAI,MAAMnG,CAAC,EACfvC,EAAI,GAQR,IALIqG,EAAO9D,CAAC,EAAI8D,EAAO,CAAC,IACtBA,EAASA,EAAO,MAAO,EAAC,QAAO,EAC/BC,EAAQA,EAAM,MAAO,EAAC,QAAO,GAGxB,EAAEtG,EAAIuC,GACX/C,EAAEQ,CAAC,EAAIwM,GAAUnG,EAAOrG,CAAC,EAAGqG,EAAOrG,EAAI,CAAC,CAAC,EACzC0I,EAAE1I,CAAC,EAAI2M,EAAYrG,EAAMtG,CAAC,EAAGsG,EAAMtG,EAAI,CAAC,CAAC,EAG3C,OAAO,SAASP,EAAG,CACjB,IAAIO,EAAIU,GAAO2F,EAAQ5G,EAAG,EAAG8C,CAAC,EAAI,EAClC,OAAOmG,EAAE1I,CAAC,EAAER,EAAEQ,CAAC,EAAEP,CAAC,CAAC,CACvB,CACA,CAEO,SAASsN,GAAKC,EAAQC,EAAQ,CACnC,OAAOA,EACF,OAAOD,EAAO,QAAQ,EACtB,MAAMA,EAAO,OAAO,EACpB,YAAYA,EAAO,aAAa,EAChC,MAAMA,EAAO,OAAO,EACpB,QAAQA,EAAO,QAAO,CAAE,CAC/B,CAEO,SAASE,IAAc,CAC5B,IAAI7G,EAASkG,GACTjG,EAAQiG,GACRI,EAAcQ,GACdC,EACAC,EACAC,EACAC,EAAQvJ,EACRwJ,EACAC,EACAC,EAEJ,SAASC,GAAU,CACjB,IAAIxM,EAAI,KAAK,IAAIkF,EAAO,OAAQC,EAAM,MAAM,EAC5C,OAAIiH,IAAUvJ,IAAUuJ,EAAQd,GAAQpG,EAAO,CAAC,EAAGA,EAAOlF,EAAI,CAAC,CAAC,GAChEqM,EAAYrM,EAAI,EAAI2L,GAAUJ,GAC9Be,EAASC,EAAQ,KACVE,CACR,CAED,SAASA,EAAMnO,EAAG,CAChB,OAAOA,GAAK,MAAQ,MAAMA,EAAI,CAACA,CAAC,EAAI6N,GAAWG,IAAWA,EAASD,EAAUnH,EAAO,IAAI+G,CAAS,EAAG9G,EAAOqG,CAAW,IAAIS,EAAUG,EAAM9N,CAAC,CAAC,CAAC,CAC9I,CAED,OAAAmO,EAAM,OAAS,SAAStD,EAAG,CACzB,OAAOiD,EAAMF,GAAaK,IAAUA,EAAQF,EAAUlH,EAAOD,EAAO,IAAI+G,CAAS,EAAG3B,CAAiB,IAAInB,CAAC,CAAC,CAAC,CAChH,EAEEsD,EAAM,OAAS,SAASC,EAAG,CACzB,OAAO,UAAU,QAAUxH,EAAS,MAAM,KAAKwH,EAAG5N,EAAM,EAAG0N,EAAO,GAAMtH,EAAO,MAAK,CACxF,EAEEuH,EAAM,MAAQ,SAASC,EAAG,CACxB,OAAO,UAAU,QAAUvH,EAAQ,MAAM,KAAKuH,CAAC,EAAGF,EAAO,GAAMrH,EAAM,MAAK,CAC9E,EAEEsH,EAAM,WAAa,SAASC,EAAG,CAC7B,OAAOvH,EAAQ,MAAM,KAAKuH,CAAC,EAAGlB,EAAcN,GAAkBsB,GAClE,EAEEC,EAAM,MAAQ,SAASC,EAAG,CACxB,OAAO,UAAU,QAAUN,EAAQM,EAAI,GAAO7J,EAAU2J,EAAO,GAAMJ,IAAUvJ,CACnF,EAEE4J,EAAM,YAAc,SAASC,EAAG,CAC9B,OAAO,UAAU,QAAUlB,EAAckB,EAAGF,EAAS,GAAIhB,CAC7D,EAEEiB,EAAM,QAAU,SAASC,EAAG,CAC1B,OAAO,UAAU,QAAUP,EAAUO,EAAGD,GAASN,CACrD,EAES,SAAShL,EAAGwL,EAAG,CACpB,OAAAV,EAAY9K,EAAG+K,EAAcS,EACtBH,EAAO,CAClB,CACA,CAEe,SAASI,IAAa,CACnC,OAAOb,GAAa,EAAClJ,EAAUA,CAAQ,CACzC,CCzHe,SAASgK,GAAWjN,EAAOC,EAAMC,EAAO6B,EAAW,CAChE,IAAI1B,EAAOM,GAASX,EAAOC,EAAMC,CAAK,EAClC6D,EAEJ,OADAhC,EAAYD,EAAgBC,GAAoB,IAAgB,EACxDA,EAAU,KAAI,CACpB,IAAK,IAAK,CACR,IAAIzC,EAAQ,KAAK,IAAI,KAAK,IAAIU,CAAK,EAAG,KAAK,IAAIC,CAAI,CAAC,EACpD,OAAI8B,EAAU,WAAa,MAAQ,CAAC,MAAMgC,EAAYmB,GAAgB7E,EAAMf,CAAK,CAAC,IAAGyC,EAAU,UAAYgC,GACpGa,GAAa7C,EAAWzC,CAAK,CACrC,CACD,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IAAK,CACJyC,EAAU,WAAa,MAAQ,CAAC,MAAMgC,EAAYoB,GAAe9E,EAAM,KAAK,IAAI,KAAK,IAAIL,CAAK,EAAG,KAAK,IAAIC,CAAI,CAAC,CAAC,CAAC,IAAG8B,EAAU,UAAYgC,GAAahC,EAAU,OAAS,MAC9K,KACD,CACD,IAAK,IACL,IAAK,IAAK,CACJA,EAAU,WAAa,MAAQ,CAAC,MAAMgC,EAAYkB,GAAe5E,CAAI,CAAC,IAAG0B,EAAU,UAAYgC,GAAahC,EAAU,OAAS,KAAO,GAC1I,KACD,CACF,CACD,OAAOuC,GAAOvC,CAAS,CACzB,CCvBO,SAASmL,GAAUL,EAAO,CAC/B,IAAIvH,EAASuH,EAAM,OAEnB,OAAAA,EAAM,MAAQ,SAAS3M,EAAO,CAC5B,IAAIzB,EAAI6G,IACR,OAAOvF,GAAMtB,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAGyB,GAAgB,EAAU,CAClE,EAEE2M,EAAM,WAAa,SAAS3M,EAAO6B,EAAW,CAC5C,IAAItD,EAAI6G,IACR,OAAO2H,GAAWxO,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAGyB,GAAgB,GAAY6B,CAAS,CAClF,EAEE8K,EAAM,KAAO,SAAS3M,EAAO,CACvBA,GAAS,OAAMA,EAAQ,IAE3B,IAAIzB,EAAI6G,IACJjD,EAAK,EACLC,EAAK7D,EAAE,OAAS,EAChBuB,EAAQvB,EAAE4D,CAAE,EACZpC,EAAOxB,EAAE6D,CAAE,EACX6K,EACA9M,EACA+M,EAAU,GAOd,IALInN,EAAOD,IACTK,EAAOL,EAAOA,EAAQC,EAAMA,EAAOI,EACnCA,EAAOgC,EAAIA,EAAKC,EAAIA,EAAKjC,GAGpB+M,KAAY,GAAG,CAEpB,GADA/M,EAAOC,GAAcN,EAAOC,EAAMC,CAAK,EACnCG,IAAS8M,EACX,OAAA1O,EAAE4D,CAAE,EAAIrC,EACRvB,EAAE6D,CAAE,EAAIrC,EACDqF,EAAO7G,CAAC,EACV,GAAI4B,EAAO,EAChBL,EAAQ,KAAK,MAAMA,EAAQK,CAAI,EAAIA,EACnCJ,EAAO,KAAK,KAAKA,EAAOI,CAAI,EAAIA,UACvBA,EAAO,EAChBL,EAAQ,KAAK,KAAKA,EAAQK,CAAI,EAAIA,EAClCJ,EAAO,KAAK,MAAMA,EAAOI,CAAI,EAAIA,MAEjC,OAEF8M,EAAU9M,EAGZ,OAAOwM,CACX,EAESA,CACT,CAEe,SAASxD,IAAS,CAC/B,IAAIwD,EAAQG,KAEZ,OAAAH,EAAM,KAAO,UAAW,CACtB,OAAOb,GAAKa,EAAOxD,GAAM,CAAE,CAC/B,EAEEhE,GAAU,MAAMwH,EAAO,SAAS,EAEzBK,GAAUL,CAAK,CACxB", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40]}