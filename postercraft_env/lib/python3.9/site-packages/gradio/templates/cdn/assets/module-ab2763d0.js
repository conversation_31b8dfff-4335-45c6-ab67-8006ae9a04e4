const VERSION_RE = new RegExp("3.50.0/", "g");function import_fix(mod, base) {const url =  new URL(mod, base); return import(`https://gradio.s3-us-west-2.amazonaws.com/3.50.0/${url.pathname?.startsWith('/') ? url.pathname.substring(1).replace(VERSION_RE, "") : url.pathname.replace(VERSION_RE, "")}`);}import{aB as nn}from"./index-7674dbb6.js";const fr=e=>t=>{const n=e(t);return t.add(n),n},hr=e=>(t,n)=>(e.set(t,n),n),St=Number.MAX_SAFE_INTEGER===void 0?9007199254740991:Number.MAX_SAFE_INTEGER,rn=536870912,Lt=rn*2,pr=(e,t)=>n=>{const r=t.get(n);let o=r===void 0?n.size:r<Lt?r+1:0;if(!n.has(o))return e(n,o);if(n.size<rn){for(;n.has(o);)o=Math.floor(Math.random()*Lt);return e(n,o)}if(n.size>St)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;n.has(o);)o=Math.floor(Math.random()*St);return e(n,o)},on=new WeakMap,mr=hr(on),ht=pr(mr,on),gr=fr(ht),wr=e=>typeof e.start=="function",Pt=new WeakMap,vr=e=>({...e,connect:({call:t})=>async()=>{const{port1:n,port2:r}=new MessageChannel,o=await t("connect",{port:n},[n]);return Pt.set(r,o),r},disconnect:({call:t})=>async n=>{const r=Pt.get(n);if(r===void 0)throw new Error("The given port is not connected.");await t("disconnect",{portId:r})},isSupported:({call:t})=>()=>t("isSupported")}),Qe=new WeakMap,_r=e=>{if(Qe.has(e))return Qe.get(e);const t=new Map;return Qe.set(e,t),t},Er=e=>{const t=vr(e);return n=>{const r=_r(n);n.addEventListener("message",({data:i})=>{const{id:c}=i;if(c!==null&&r.has(c)){const{reject:u,resolve:d}=r.get(c);r.delete(c),i.error===void 0?d(i.result):u(new Error(i.error.message))}}),wr(n)&&n.start();const o=(i,c=null,u=[])=>new Promise((d,l)=>{const w=ht(r);r.set(w,{reject:l,resolve:d}),c===null?n.postMessage({id:w,method:i},u):n.postMessage({id:w,method:i,params:c},u)}),s=(i,c,u=[])=>{n.postMessage({id:null,method:i,params:c},u)};let a={};for(const[i,c]of Object.entries(t))a={...a,[i]:c({call:o,notify:s})};return{...a}}},Ut=new Set,yr=Er({encode:({call:e})=>async(t,n)=>{const r=await e("encode",{encoderId:t,timeslice:n});return Ut.delete(t),r},instantiate:({call:e})=>async(t,n)=>{const r=gr(Ut),o=await e("instantiate",{encoderId:r,mimeType:t,sampleRate:n});return{encoderId:r,port:o}},register:({call:e})=>t=>e("register",{port:t},[t])}),Ar=e=>{const t=new Worker(e);return yr(t)},br=`(()=>{var e={881:e=>{"use strict";e.exports=(e,t)=>{if("string"!=typeof e)throw new TypeError("expected a string");return e.trim().replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\\W/g,(e=>/[À-ž]/.test(e)?e:"-")).replace(/^-+|-+$/g,"").replace(/-{2,}/g,(e=>t&&t.condense?"-":e)).toLowerCase()}},507:e=>{var t=function(e){var t,r,n=/\\w+/.exec(e);if(!n)return"an";var o=(r=n[0]).toLowerCase(),s=["honest","hour","hono"];for(t in s)if(0==o.indexOf(s[t]))return"an";if(1==o.length)return"aedhilmnorsx".indexOf(o)>=0?"an":"a";if(r.match(/(?!FJO|[HLMNS]Y.|RY[EO]|SQU|(F[LR]?|[HL]|MN?|N|RH?|S[CHKLMNPTVW]?|X(YL)?)[AEIOU])[FHLMNRSX][A-Z]/))return"an";var a=[/^e[uw]/,/^onc?e\\b/,/^uni([^nmd]|mo)/,/^u[bcfhjkqrst][aeiou]/];for(t=0;t<a.length;t++)if(o.match(a[t]))return"a";return r.match(/^U[NK][AIEO]/)?"a":r==r.toUpperCase()?"aedhilmnorsx".indexOf(o[0])>=0?"an":"a":"aeiou".indexOf(o[0])>=0||o.match(/^y(b[lor]|cl[ea]|fere|gg|p[ios]|rou|tt)/)?"an":"a"};void 0!==e.exports?e.exports=t:window.indefiniteArticle=t}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=r(881),t=r.n(e),n=r(507),o=r.n(n);const s=(e,r)=>void 0===r?e:r.reduce(((e,r)=>{if("capitalize"===r){const t=e.charAt(0).toUpperCase(),r=e.slice(1);return"".concat(t).concat(r)}return"dashify"===r?t()(e):"prependIndefiniteArticle"===r?"".concat(o()(e)," ").concat(e):e}),e),a=(e,t)=>{const r=/\\\${([^.}]+)((\\.[^(]+\\(\\))*)}/g,n=[];let o=r.exec(e);for(;null!==o;){const t={modifiers:[],name:o[1]};if(void 0!==o[3]){const e=/\\.[^(]+\\(\\)/g;let r=e.exec(o[2]);for(;null!==r;)t.modifiers.push(r[0].slice(1,-2)),r=e.exec(o[2])}n.push(t),o=r.exec(e)}const a=n.reduce(((e,r)=>e.map((e=>"string"==typeof e?e.split((e=>{const t=e.name+e.modifiers.map((e=>"\\\\.".concat(e,"\\\\(\\\\)"))).join("");return new RegExp("\\\\$\\\\{".concat(t,"}"),"g")})(r)).reduce(((e,n,o)=>0===o?[n]:r.name in t?[...e,s(t[r.name],r.modifiers),n]:[...e,e=>s(e[r.name],r.modifiers),n]),[]):[e])).reduce(((e,t)=>[...e,...t]),[])),[e]);return e=>a.reduce(((t,r)=>"string"==typeof r?[...t,r]:[...t,r(e)]),[]).join("")},i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=void 0===e.code?void 0:a(e.code,t),n=void 0===e.message?void 0:a(e.message,t);return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0;const s=void 0===o&&(t instanceof Error||void 0!==t.code&&"Exception"===t.code.slice(-9)),{cause:a,missingParameters:i}=s?{cause:t,missingParameters:{}}:{cause:o,missingParameters:t},c=void 0===n?new Error:new Error(n(i));return null!==a&&(c.cause=a),void 0!==r&&(c.code=r(i)),void 0!==e.status&&(c.status=e.status),c}},c=-32603,d=-32602,l=i({message:'The requested method called "\${method}" is not supported.',status:-32601}),u=i({message:'The handler of the method called "\${method}" returned no required result.',status:c}),h=i({message:'The handler of the method called "\${method}" returned an unexpected result.',status:c}),m=i({message:'The specified parameter called "portId" with the given value "\${portId}" does not identify a port connected to this worker.',status:d}),p=void 0===Number.MAX_SAFE_INTEGER?9007199254740991:Number.MAX_SAFE_INTEGER,f=536870912,g=1073741824,w=new WeakMap;var v;const y=((e,t)=>r=>{const n=t.get(r);let o=void 0===n?r.size:n<g?n+1:0;if(!r.has(o))return e(r,o);if(r.size<f){for(;r.has(o);)o=Math.floor(Math.random()*g);return e(r,o)}if(r.size>p)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;r.has(o);)o=Math.floor(Math.random()*p);return e(r,o)})((v=w,(e,t)=>(v.set(e,t),t)),w),M=((e=>{})(y),new Map),E=(e,t,r)=>({...t,connect:r=>{let{port:n}=r;n.start();const o=e(n,t),s=y(M);return M.set(s,(()=>{o(),n.close(),M.delete(s)})),{result:s}},disconnect:e=>{let{portId:t}=e;const r=M.get(t);if(void 0===r)throw m({portId:t.toString()});return r(),{result:null}},isSupported:async()=>{if(await new Promise((e=>{const t=new ArrayBuffer(0),{port1:r,port2:n}=new MessageChannel;r.onmessage=t=>{let{data:r}=t;return e(null!==r)},n.postMessage(t,[t])}))){const e=r();return{result:e instanceof Promise?await e:e}}return{result:!1}}}),x=function(e,t){const r=E(x,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>!0),n=((e,t)=>async r=>{let{data:{id:n,method:o,params:s}}=r;const a=t[o];try{if(void 0===a)throw l({method:o});const t=void 0===s?a():a(s);if(void 0===t)throw u({method:o});const r=t instanceof Promise?await t:t;if(null===n){if(void 0!==r.result)throw h({method:o})}else{if(void 0===r.result)throw h({method:o});const{result:t,transferables:s=[]}=r;e.postMessage({id:n,result:t},s)}}catch(t){const{message:r,status:o=-32603}=t;e.postMessage({error:{code:o,message:r},id:n})}})(e,r);return e.addEventListener("message",n),()=>e.removeEventListener("message",n)},b=e=>{e.onmessage=null,e.close()},A=new WeakMap,T=new WeakMap,I=(e=>{const t=(r=e,{...r,connect:e=>{let{call:t}=e;return async()=>{const{port1:e,port2:r}=new MessageChannel,n=await t("connect",{port:e},[e]);return A.set(r,n),r}},disconnect:e=>{let{call:t}=e;return async e=>{const r=A.get(e);if(void 0===r)throw new Error("The given port is not connected.");await t("disconnect",{portId:r})}},isSupported:e=>{let{call:t}=e;return()=>t("isSupported")}});var r;return e=>{const r=(e=>{if(T.has(e))return T.get(e);const t=new Map;return T.set(e,t),t})(e);e.addEventListener("message",(e=>{let{data:t}=e;const{id:n}=t;if(null!==n&&r.has(n)){const{reject:e,resolve:o}=r.get(n);r.delete(n),void 0===t.error?o(t.result):e(new Error(t.error.message))}})),(e=>"function"==typeof e.start)(e)&&e.start();const n=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return new Promise(((s,a)=>{const i=y(r);r.set(i,{reject:a,resolve:s}),null===n?e.postMessage({id:i,method:t},o):e.postMessage({id:i,method:t,params:n},o)}))},o=function(t,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e.postMessage({id:null,method:t,params:r},n)};let s={};for(const[e,r]of Object.entries(t))s={...s,[e]:r({call:n,notify:o})};return{...s}}})({characterize:e=>{let{call:t}=e;return()=>t("characterize")},encode:e=>{let{call:t}=e;return(e,r)=>t("encode",{recordingId:e,timeslice:r})},record:e=>{let{call:t}=e;return async(e,r,n)=>{await t("record",{recordingId:e,sampleRate:r,typedArrays:n},n.map((e=>{let{buffer:t}=e;return t})))}}}),O=async(e,t)=>{const r=I(t),n=await r.characterize(),o=n.toString();if(e.has(o))throw new Error("There is already an encoder stored which handles exactly the same mime types.");return e.set(o,[n,r]),n},L=new Map,P=(e=>t=>{const r=e.get(t);if(void 0===r)throw new Error("There was no instance of an encoder stored with the given id.");return r})(L),S=((e,t)=>r=>{const n=t(r);return e.delete(r),n})(L,P),N=new Map,C=((e,t)=>r=>{const[n,o,s,a]=t(r);return s?new Promise((t=>{o.onmessage=s=>{let{data:i}=s;0===i.length?(e(o),t(n.encode(r,null))):n.record(r,a,i)}})):n.encode(r,null)})(b,S),R=(e=>t=>{for(const[r,n]of Array.from(e.values()))if(r.test(t))return n;throw new Error("There is no encoder registered which could handle the given mimeType.")})(N),$=((e,t,r)=>(n,o,s)=>{if(t.has(n))throw new Error('There is already an encoder registered with an id called "'.concat(n,'".'));const a=r(o),{port1:i,port2:c}=new MessageChannel,d=[a,i,!0,s];return t.set(n,d),i.onmessage=t=>{let{data:r}=t;0===r.length?(e(i),d[2]=!1):a.record(n,s,r.map((e=>"number"==typeof e?new Float32Array(e):e)))},c})(b,L,R),j=(e=>(t,r)=>{const[n]=e(t);return n.encode(t,r)})(P);x(self,{encode:async e=>{let{encoderId:t,timeslice:r}=e;const n=null===r?await C(t):await j(t,r);return{result:n,transferables:n}},instantiate:e=>{let{encoderId:t,mimeType:r,sampleRate:n}=e;const o=$(t,r,n);return{result:o,transferables:[o]}},register:async e=>{let{port:t}=e;return{result:await O(N,t)}}})})()})();`,Cr=new Blob([br],{type:"application/javascript; charset=utf-8"}),sn=URL.createObjectURL(Cr),pt=Ar(sn),ke=pt.encode,an=pt.instantiate,Tr=pt.register;URL.revokeObjectURL(sn);const Mr=e=>(t,n)=>{if(e===null)throw new Error("A native BlobEvent could not be created.");return new e(t,n)},Nr=(e,t)=>(n,r,o)=>{const s=[];let a=r,i=0;for(;i<n.byteLength;)if(a===null){const c=t(n,i);if(c===null)break;const{length:u,type:d}=c;a=d,i+=u}else{const c=e(n,i,a,o);if(c===null)break;const{content:u,length:d}=c;a=null,i+=d,u!==null&&s.push(u)}return{contents:s,currentElementType:a,offset:i}},Or=(e,t)=>class{constructor(r=null){this._listeners=new WeakMap,this._nativeEventTarget=r===null?e():r}addEventListener(r,o,s){if(o!==null){let a=this._listeners.get(o);a===void 0&&(a=t(this,o),typeof o=="function"&&this._listeners.set(o,a)),this._nativeEventTarget.addEventListener(r,a,s)}}dispatchEvent(r){return this._nativeEventTarget.dispatchEvent(r)}removeEventListener(r,o,s){const a=o===null?void 0:this._listeners.get(o);this._nativeEventTarget.removeEventListener(r,a===void 0?null:a,s)}},Rr=e=>()=>{if(e===null)throw new Error("A native EventTarget could not be created.");return e.document.createElement("p")},Ir=(e="")=>{try{return new DOMException(e,"InvalidModificationError")}catch(t){return t.code=13,t.message=e,t.name="InvalidModificationError",t}},kr=()=>{try{return new DOMException("","InvalidStateError")}catch(e){return e.code=11,e.name="InvalidStateError",e}},Sr=e=>{if(e!==null&&e.BlobEvent!==void 0&&e.MediaStream!==void 0&&(e.MediaRecorder===void 0||e.MediaRecorder.isTypeSupported!==void 0)){if(e.MediaRecorder===void 0)return Promise.resolve(!0);const t=e.document.createElement("canvas"),n=t.getContext("2d");if(n===null||typeof t.captureStream!="function")return Promise.resolve(!1);const r=t.captureStream();return Promise.all([new Promise(o=>{const s="audio/webm";try{const a=new e.MediaRecorder(r,{mimeType:s});a.addEventListener("dataavailable",({data:i})=>o(i.type===s)),a.start(),setTimeout(()=>a.stop(),10)}catch(a){o(a.name==="NotSupportedError")}}),new Promise(o=>{const s=new e.MediaRecorder(r);let a=!1,i=!1;s.addEventListener("dataavailable",()=>a=!0),s.addEventListener("error",c=>{o(!a&&!i&&"error"in c&&c.error!==null&&typeof c.error=="object"&&"name"in c.error&&c.error.name!=="UnknownError")}),s.addEventListener("stop",()=>i=!0),s.start(),n.fillRect(0,0,1,1),r.removeTrack(r.getVideoTracks()[0])})]).then(o=>o.every(s=>s))}return Promise.resolve(!1)},Lr=(e,t,n,r,o,s,a)=>class extends s{constructor(c,u={}){const{mimeType:d}=u;if(a!==null&&(d===void 0||a.isTypeSupported!==void 0&&a.isTypeSupported(d))){const l=e(a,c,u);super(l),this._internalMediaRecorder=l}else if(d!==void 0&&o.some(l=>l.test(d)))super(),a!==null&&a.isTypeSupported!==void 0&&a.isTypeSupported("audio/webm;codecs=pcm")?this._internalMediaRecorder=r(this,a,c,d):this._internalMediaRecorder=n(this,c,d);else throw a!==null&&e(a,c,u),t();this._ondataavailable=null,this._onerror=null,this._onpause=null,this._onresume=null,this._onstart=null,this._onstop=null}get mimeType(){return this._internalMediaRecorder.mimeType}get ondataavailable(){return this._ondataavailable===null?this._ondataavailable:this._ondataavailable[0]}set ondataavailable(c){if(this._ondataavailable!==null&&this.removeEventListener("dataavailable",this._ondataavailable[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("dataavailable",u),this._ondataavailable=[c,u]}else this._ondataavailable=null}get onerror(){return this._onerror===null?this._onerror:this._onerror[0]}set onerror(c){if(this._onerror!==null&&this.removeEventListener("error",this._onerror[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("error",u),this._onerror=[c,u]}else this._onerror=null}get onpause(){return this._onpause===null?this._onpause:this._onpause[0]}set onpause(c){if(this._onpause!==null&&this.removeEventListener("pause",this._onpause[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("pause",u),this._onpause=[c,u]}else this._onpause=null}get onresume(){return this._onresume===null?this._onresume:this._onresume[0]}set onresume(c){if(this._onresume!==null&&this.removeEventListener("resume",this._onresume[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("resume",u),this._onresume=[c,u]}else this._onresume=null}get onstart(){return this._onstart===null?this._onstart:this._onstart[0]}set onstart(c){if(this._onstart!==null&&this.removeEventListener("start",this._onstart[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("start",u),this._onstart=[c,u]}else this._onstart=null}get onstop(){return this._onstop===null?this._onstop:this._onstop[0]}set onstop(c){if(this._onstop!==null&&this.removeEventListener("stop",this._onstop[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("stop",u),this._onstop=[c,u]}else this._onstop=null}get state(){return this._internalMediaRecorder.state}pause(){return this._internalMediaRecorder.pause()}resume(){return this._internalMediaRecorder.resume()}start(c){return this._internalMediaRecorder.start(c)}stop(){return this._internalMediaRecorder.stop()}static isTypeSupported(c){return a!==null&&a.isTypeSupported!==void 0&&a.isTypeSupported(c)||o.some(u=>u.test(c))}},Pr=e=>e!==null&&e.BlobEvent!==void 0?e.BlobEvent:null,Ur=(e,t,n)=>{const r=new Map,o=new WeakMap,s=new WeakMap,a=new e(t,n),i=new WeakMap;let c=!1;return a.addEventListener=(u=>(d,l,w)=>{let g=l;if(typeof l=="function")if(d==="dataavailable"){const f=[];g=h=>{c&&a.state==="inactive"?f.push(h):l.call(a,h)},r.set(l,f),o.set(l,g)}else d==="error"?(g=f=>{f instanceof ErrorEvent?l.call(a,f):l.call(a,new ErrorEvent("error",{error:f.error}))},s.set(l,g)):d==="stop"&&(g=f=>{for(const[h,m]of r.entries())if(m.length>0){const[p]=m;m.length>1&&Object.defineProperty(p,"data",{value:new Blob(m.map(({data:_})=>_),{type:p.data.type})}),m.length=0,h.call(a,p)}c=!1,l.call(a,f)},i.set(l,g));return u.call(a,d,g,w)})(a.addEventListener),a.removeEventListener=(u=>(d,l,w)=>{let g=l;if(typeof l=="function"){if(d==="dataavailable"){r.delete(l);const f=o.get(l);f!==void 0&&(g=f)}else if(d==="error"){const f=s.get(l);f!==void 0&&(g=f)}else if(d==="stop"){const f=i.get(l);f!==void 0&&(g=f)}}return u.call(a,d,g,w)})(a.removeEventListener),a.start=(u=>d=>(c=d!==void 0,d===void 0?u.call(a):u.call(a,d)))(a.start),a},xr=e=>e===null||e.MediaRecorder===void 0?null:e.MediaRecorder,cn=()=>{try{return new DOMException("","NotSupportedError")}catch(e){return e.code=9,e.name="NotSupportedError",e}},Br=e=>(t,n,r,o=2)=>{const s=e(t,n);if(s===null)return s;const{length:a,value:i}=s;if(r==="master")return{content:null,length:a};if(n+a+i>t.byteLength)return null;if(r==="binary"){const c=(i/Float32Array.BYTES_PER_ELEMENT-1)/o,u=Array.from({length:o},()=>new Float32Array(c));for(let d=0;d<c;d+=1){const l=d*o+1;for(let w=0;w<o;w+=1)u[w][d]=t.getFloat32(n+a+(l+w)*Float32Array.BYTES_PER_ELEMENT,!0)}return{content:u,length:a+i}}return{content:null,length:a+i}},Wr=e=>(t,n)=>{const r=e(t,n);if(r===null)return r;const{length:o,value:s}=r;return s===35?{length:o,type:"binary"}:s===46||s===97||s===88713574||s===106212971||s===139690087||s===172351395||s===256095861?{length:o,type:"master"}:{length:o,type:"unknown"}},Dr=e=>(t,n)=>{const r=e(t,n);if(r===null)return r;const o=n+Math.floor((r-1)/8);if(o+r>t.byteLength)return null;let a=t.getUint8(o)&(1<<8-r%8)-1;for(let i=1;i<r;i+=1)a=(a<<8)+t.getUint8(o+i);return{length:r,value:a}},xt=Symbol.observable||"@@observable";function Vr(e){return Symbol.observable||(typeof e=="function"&&e.prototype&&e.prototype[Symbol.observable]?(e.prototype[xt]=e.prototype[Symbol.observable],delete e.prototype[Symbol.observable]):(e[xt]=e[Symbol.observable],delete e[Symbol.observable])),e}const Ne=()=>{},Bt=e=>{throw e};function Fr(e){return e?e.next&&e.error&&e.complete?e:{complete:(e.complete??Ne).bind(e),error:(e.error??Bt).bind(e),next:(e.next??Ne).bind(e)}:{complete:Ne,error:Bt,next:Ne}}const jr=e=>(t,n,r)=>e(o=>{const s=a=>o.next(a);return t.addEventListener(n,s,r),()=>t.removeEventListener(n,s,r)}),$r=(e,t)=>{const n=()=>{},r=o=>typeof o[0]=="function";return o=>{const s=(...a)=>{const i=o(r(a)?t({next:a[0]}):t(...a));return i!==void 0?i:n};return s[Symbol.observable]=()=>({subscribe:(...a)=>({unsubscribe:s(...a)})}),e(s)}},Gr=$r(Vr,Fr),un=jr(Gr);/*!
 * dashify <https://github.com/jonschlinkert/dashify>
 *
 * Copyright (c) 2015-2017, Jon Schlinkert.
 * Released under the MIT License.
 */var zr=(e,t)=>{if(typeof e!="string")throw new TypeError("expected a string");return e.trim().replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\W/g,n=>/[À-ž]/.test(n)?n:"-").replace(/^-+|-+$/g,"").replace(/-{2,}/g,n=>t&&t.condense?"-":n).toLowerCase()};const qr=nn(zr);var ln={exports:{}};(function(e){var t=function(n){var r,o,s=/\w+/.exec(n);if(s)o=s[0];else return"an";var a=o.toLowerCase(),i=["honest","hour","hono"];for(r in i)if(a.indexOf(i[r])==0)return"an";if(a.length==1)return"aedhilmnorsx".indexOf(a)>=0?"an":"a";if(o.match(/(?!FJO|[HLMNS]Y.|RY[EO]|SQU|(F[LR]?|[HL]|MN?|N|RH?|S[CHKLMNPTVW]?|X(YL)?)[AEIOU])[FHLMNRSX][A-Z]/))return"an";var c=[/^e[uw]/,/^onc?e\b/,/^uni([^nmd]|mo)/,/^u[bcfhjkqrst][aeiou]/];for(r=0;r<c.length;r++)if(a.match(c[r]))return"a";return o.match(/^U[NK][AIEO]/)?"a":o==o.toUpperCase()?"aedhilmnorsx".indexOf(a[0])>=0?"an":"a":"aeiou".indexOf(a[0])>=0||a.match(/^y(b[lor]|cl[ea]|fere|gg|p[ios]|rou|tt)/)?"an":"a"};e.exports=t})(ln);var Hr=ln.exports;const Xr=nn(Hr),Wt=(e,t)=>t===void 0?e:t.reduce((n,r)=>{if(r==="capitalize"){const o=n.charAt(0).toUpperCase(),s=n.slice(1);return`${o}${s}`}return r==="dashify"?qr(n):r==="prependIndefiniteArticle"?`${Xr(n)} ${n}`:n},e),Yr=e=>{const t=e.name+e.modifiers.map(n=>`\\.${n}\\(\\)`).join("");return new RegExp(`\\$\\{${t}}`,"g")},Dt=(e,t)=>{const n=/\${([^.}]+)((\.[^(]+\(\))*)}/g,r=[];let o=n.exec(e);for(;o!==null;){const a={modifiers:[],name:o[1]};if(o[3]!==void 0){const i=/\.[^(]+\(\)/g;let c=i.exec(o[2]);for(;c!==null;)a.modifiers.push(c[0].slice(1,-2)),c=i.exec(o[2])}r.push(a),o=n.exec(e)}const s=r.reduce((a,i)=>a.map(c=>typeof c=="string"?c.split(Yr(i)).reduce((u,d,l)=>l===0?[d]:i.name in t?[...u,Wt(t[i.name],i.modifiers),d]:[...u,w=>Wt(w[i.name],i.modifiers),d],[]):[c]).reduce((c,u)=>[...c,...u],[]),[e]);return a=>s.reduce((i,c)=>typeof c=="string"?[...i,c]:[...i,c(a)],[]).join("")},De=(e,t={})=>{const n=e.code===void 0?void 0:Dt(e.code,t),r=e.message===void 0?void 0:Dt(e.message,t);function o(s={},a){const i=a===void 0&&(s instanceof Error||s.code!==void 0&&s.code.slice(-9)==="Exception"),{cause:c,missingParameters:u}=i?{cause:s,missingParameters:{}}:{cause:a,missingParameters:s},d=r===void 0?new Error:new Error(r(u));return c!==null&&(d.cause=c),n!==void 0&&(d.code=n(u)),e.status!==void 0&&(d.status=e.status),d}return o},Ve={INTERNAL_ERROR:-32603,INVALID_PARAMS:-32602,METHOD_NOT_FOUND:-32601};De({message:'The requested method called "${method}" is not supported.',status:Ve.METHOD_NOT_FOUND});De({message:'The handler of the method called "${method}" returned no required result.',status:Ve.INTERNAL_ERROR});De({message:'The handler of the method called "${method}" returned an unexpected result.',status:Ve.INTERNAL_ERROR});De({message:'The specified parameter called "portId" with the given value "${portId}" does not identify a port connected to this worker.',status:Ve.INVALID_PARAMS});const Zr=(e,t,n)=>async r=>{const o=new e([n],{type:"application/javascript; charset=utf-8"}),s=t.createObjectURL(o);try{await r(s)}finally{t.revokeObjectURL(s)}},Kr=e=>({data:t})=>{const{id:n}=t;if(n!==null){const r=e.get(n);if(r!==void 0){const{reject:o,resolve:s}=r;e.delete(n),t.error===void 0?s(t.result):o(new Error(t.error.message))}}},Qr=e=>(t,n)=>(r,o=[])=>new Promise((s,a)=>{const i=e(t);t.set(i,{reject:a,resolve:s}),n.postMessage({id:i,...r},o)}),Jr=(e,t,n,r)=>(o,s,a={})=>{const i=new o(s,"recorder-audio-worklet-processor",{...a,channelCountMode:"explicit",numberOfInputs:1,numberOfOutputs:0}),c=new Map,u=t(c,i.port),d=n(i.port,"message")(e(c));i.port.start();let l="inactive";return Object.defineProperties(i,{pause:{get(){return async()=>(r(["recording"],l),l="paused",u({method:"pause"}))}},port:{get(){throw new Error("The port of a RecorderAudioWorkletNode can't be accessed.")}},record:{get(){return async w=>(r(["inactive"],l),l="recording",u({method:"record",params:{encoderPort:w}},[w]))}},resume:{get(){return async()=>(r(["paused"],l),l="recording",u({method:"resume"}))}},stop:{get(){return async()=>{r(["paused","recording"],l),l="stopped";try{await u({method:"stop"})}finally{d()}}}}}),i},eo=(e,t)=>{if(!e.includes(t))throw new Error(`Expected the state to be ${e.map(n=>`"${n}"`).join(" or ")} but it was "${t}".`)},to='(()=>{"use strict";class e extends AudioWorkletProcessor{constructor(){super(),this._encoderPort=null,this._numberOfChannels=0,this._state="inactive",this.port.onmessage=e=>{let{data:t}=e;"pause"===t.method?"active"===this._state||"recording"===this._state?(this._state="paused",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"record"===t.method?"inactive"===this._state?(this._encoderPort=t.params.encoderPort,this._state="active",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"resume"===t.method?"paused"===this._state?(this._state="active",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"stop"===t.method?"active"!==this._state&&"paused"!==this._state&&"recording"!==this._state||null===this._encoderPort?this._sendUnexpectedStateError(t.id):(this._stop(this._encoderPort),this._sendAcknowledgement(t.id)):"number"==typeof t.id&&this.port.postMessage({error:{code:-32601,message:"The requested method is not supported."},id:t.id})}}process(e){let[t]=e;if("inactive"===this._state||"paused"===this._state)return!0;if("active"===this._state){if(void 0===t)throw new Error("No channelData was received for the first input.");if(0===t.length)return!0;this._state="recording"}if("recording"===this._state&&null!==this._encoderPort){if(void 0===t)throw new Error("No channelData was received for the first input.");return 0===t.length?this._encoderPort.postMessage(Array.from({length:this._numberOfChannels},(()=>128))):(this._encoderPort.postMessage(t,t.map((e=>{let{buffer:t}=e;return t}))),this._numberOfChannels=t.length),!0}return!1}_sendAcknowledgement(e){this.port.postMessage({id:e,result:null})}_sendUnexpectedStateError(e){this.port.postMessage({error:{code:-32603,message:"The internal state does not allow to process the given message."},id:e})}_stop(e){e.postMessage([]),e.close(),this._encoderPort=null,this._state="stopped"}}e.parameterDescriptors=[],registerProcessor("recorder-audio-worklet-processor",e)})();',no=Zr(Blob,URL,to),ro=Jr(Kr,Qr(ht),un,eo),Vt=(e,t,n)=>({endTime:t,insertTime:n,type:"exponentialRampToValue",value:e}),Ft=(e,t,n)=>({endTime:t,insertTime:n,type:"linearRampToValue",value:e}),tt=(e,t)=>({startTime:t,type:"setValue",value:e}),dn=(e,t,n)=>({duration:n,startTime:t,type:"setValueCurve",values:e}),fn=(e,t,{startTime:n,target:r,timeConstant:o})=>r+(t-r)*Math.exp((n-e)/o),ge=e=>e.type==="exponentialRampToValue",Se=e=>e.type==="linearRampToValue",oe=e=>ge(e)||Se(e),mt=e=>e.type==="setValue",ee=e=>e.type==="setValueCurve",Le=(e,t,n,r)=>{const o=e[t];return o===void 0?r:oe(o)||mt(o)?o.value:ee(o)?o.values[o.values.length-1]:fn(n,Le(e,t-1,o.startTime,r),o)},jt=(e,t,n,r,o)=>n===void 0?[r.insertTime,o]:oe(n)?[n.endTime,n.value]:mt(n)?[n.startTime,n.value]:ee(n)?[n.startTime+n.duration,n.values[n.values.length-1]]:[n.startTime,Le(e,t-1,n.startTime,o)],nt=e=>e.type==="cancelAndHold",rt=e=>e.type==="cancelScheduledValues",re=e=>nt(e)||rt(e)?e.cancelTime:ge(e)||Se(e)?e.endTime:e.startTime,$t=(e,t,n,{endTime:r,value:o})=>n===o?o:0<n&&0<o||n<0&&o<0?n*(o/n)**((e-t)/(r-t)):0,Gt=(e,t,n,{endTime:r,value:o})=>n+(e-t)/(r-t)*(o-n),oo=(e,t)=>{const n=Math.floor(t),r=Math.ceil(t);return n===r?e[n]:(1-(t-n))*e[n]+(1-(r-t))*e[r]},so=(e,{duration:t,startTime:n,values:r})=>{const o=(e-n)/t*(r.length-1);return oo(r,o)},Oe=e=>e.type==="setTarget";class ao{constructor(t){this._automationEvents=[],this._currenTime=0,this._defaultValue=t}[Symbol.iterator](){return this._automationEvents[Symbol.iterator]()}add(t){const n=re(t);if(nt(t)||rt(t)){const r=this._automationEvents.findIndex(s=>rt(t)&&ee(s)?s.startTime+s.duration>=n:re(s)>=n),o=this._automationEvents[r];if(r!==-1&&(this._automationEvents=this._automationEvents.slice(0,r)),nt(t)){const s=this._automationEvents[this._automationEvents.length-1];if(o!==void 0&&oe(o)){if(s!==void 0&&Oe(s))throw new Error("The internal list is malformed.");const a=s===void 0?o.insertTime:ee(s)?s.startTime+s.duration:re(s),i=s===void 0?this._defaultValue:ee(s)?s.values[s.values.length-1]:s.value,c=ge(o)?$t(n,a,i,o):Gt(n,a,i,o),u=ge(o)?Vt(c,n,this._currenTime):Ft(c,n,this._currenTime);this._automationEvents.push(u)}if(s!==void 0&&Oe(s)&&this._automationEvents.push(tt(this.getValue(n),n)),s!==void 0&&ee(s)&&s.startTime+s.duration>n){const a=n-s.startTime,i=(s.values.length-1)/s.duration,c=Math.max(2,1+Math.ceil(a*i)),u=a/(c-1)*i,d=s.values.slice(0,c);if(u<1)for(let l=1;l<c;l+=1){const w=u*l%1;d[l]=s.values[l-1]*(1-w)+s.values[l]*w}this._automationEvents[this._automationEvents.length-1]=dn(d,s.startTime,a)}}}else{const r=this._automationEvents.findIndex(a=>re(a)>n),o=r===-1?this._automationEvents[this._automationEvents.length-1]:this._automationEvents[r-1];if(o!==void 0&&ee(o)&&re(o)+o.duration>n)return!1;const s=ge(t)?Vt(t.value,t.endTime,this._currenTime):Se(t)?Ft(t.value,n,this._currenTime):t;if(r===-1)this._automationEvents.push(s);else{if(ee(t)&&n+t.duration>re(this._automationEvents[r]))return!1;this._automationEvents.splice(r,0,s)}}return!0}flush(t){const n=this._automationEvents.findIndex(r=>re(r)>t);if(n>1){const r=this._automationEvents.slice(n-1),o=r[0];Oe(o)&&r.unshift(tt(Le(this._automationEvents,n-2,o.startTime,this._defaultValue),o.startTime)),this._automationEvents=r}}getValue(t){if(this._automationEvents.length===0)return this._defaultValue;const n=this._automationEvents.findIndex(a=>re(a)>t),r=this._automationEvents[n],o=(n===-1?this._automationEvents.length:n)-1,s=this._automationEvents[o];if(s!==void 0&&Oe(s)&&(r===void 0||!oe(r)||r.insertTime>t))return fn(t,Le(this._automationEvents,o-1,s.startTime,this._defaultValue),s);if(s!==void 0&&mt(s)&&(r===void 0||!oe(r)))return s.value;if(s!==void 0&&ee(s)&&(r===void 0||!oe(r)||s.startTime+s.duration>t))return t<s.startTime+s.duration?so(t,s):s.values[s.values.length-1];if(s!==void 0&&oe(s)&&(r===void 0||!oe(r)))return s.value;if(r!==void 0&&ge(r)){const[a,i]=jt(this._automationEvents,o,s,r,this._defaultValue);return $t(t,a,i,r)}if(r!==void 0&&Se(r)){const[a,i]=jt(this._automationEvents,o,s,r,this._defaultValue);return Gt(t,a,i,r)}return this._defaultValue}}const io=e=>({cancelTime:e,type:"cancelAndHold"}),co=e=>({cancelTime:e,type:"cancelScheduledValues"}),uo=(e,t)=>({endTime:t,type:"exponentialRampToValue",value:e}),lo=(e,t)=>({endTime:t,type:"linearRampToValue",value:e}),fo=(e,t,n)=>({startTime:t,target:e,timeConstant:n,type:"setTarget"}),ho=()=>new DOMException("","AbortError"),po=e=>(t,n,[r,o,s],a)=>{e(t[o],[n,r,s],i=>i[0]===n&&i[1]===r,a)},mo=e=>(t,n,r)=>{const o=[];for(let s=0;s<r.numberOfInputs;s+=1)o.push(new Set);e.set(t,{activeInputs:o,outputs:new Set,passiveInputs:new WeakMap,renderer:n})},go=e=>(t,n)=>{e.set(t,{activeInputs:new Set,passiveInputs:new WeakMap,renderer:n})},we=new WeakSet,hn=new WeakMap,pn=new WeakMap,mn=new WeakMap,gn=new WeakMap,wn=new WeakMap,vn=new WeakMap,ot=new WeakMap,st=new WeakMap,at=new WeakMap,_n={construct(){return _n}},wo=e=>{try{const t=new Proxy(e,_n);new t}catch{return!1}return!0},zt=/^import(?:(?:[\s]+[\w]+|(?:[\s]+[\w]+[\s]*,)?[\s]*\{[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?(?:[\s]*,[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?)*[\s]*}|(?:[\s]+[\w]+[\s]*,)?[\s]*\*[\s]+as[\s]+[\w]+)[\s]+from)?(?:[\s]*)("([^"\\]|\\.)+"|'([^'\\]|\\.)+')(?:[\s]*);?/,qt=(e,t)=>{const n=[];let r=e.replace(/^[\s]+/,""),o=r.match(zt);for(;o!==null;){const s=o[1].slice(1,-1),a=o[0].replace(/([\s]+)?;?$/,"").replace(s,new URL(s,t).toString());n.push(a),r=r.slice(o[0].length).replace(/^[\s]+/,""),o=r.match(zt)}return[n.join(";"),r]},Ht=e=>{if(e!==void 0&&!Array.isArray(e))throw new TypeError("The parameterDescriptors property of given value for processorCtor is not an array.")},Xt=e=>{if(!wo(e))throw new TypeError("The given value for processorCtor should be a constructor.");if(e.prototype===null||typeof e.prototype!="object")throw new TypeError("The given value for processorCtor should have a prototype.")},vo=(e,t,n,r,o,s,a,i,c,u,d,l,w)=>{let g=0;return(f,h,m={credentials:"omit"})=>{const p=d.get(f);if(p!==void 0&&p.has(h))return Promise.resolve();const _=u.get(f);if(_!==void 0){const A=_.get(h);if(A!==void 0)return A}const E=s(f),T=E.audioWorklet===void 0?o(h).then(([A,b])=>{const[y,v]=qt(A,b),N=`${y};((a,b)=>{(a[b]=a[b]||[]).push((AudioWorkletProcessor,global,registerProcessor,sampleRate,self,window)=>{${v}
})})(window,'_AWGS')`;return n(N)}).then(()=>{const A=w._AWGS.pop();if(A===void 0)throw new SyntaxError;r(E.currentTime,E.sampleRate,()=>A(class{},void 0,(b,y)=>{if(b.trim()==="")throw t();const v=st.get(E);if(v!==void 0){if(v.has(b))throw t();Xt(y),Ht(y.parameterDescriptors),v.set(b,y)}else Xt(y),Ht(y.parameterDescriptors),st.set(E,new Map([[b,y]]))},E.sampleRate,void 0,void 0))}):Promise.all([o(h),Promise.resolve(e(l,l))]).then(([[A,b],y])=>{const v=g+1;g=v;const[N,I]=qt(A,b),W=`${N};((AudioWorkletProcessor,registerProcessor)=>{${I}
})(${y?"AudioWorkletProcessor":"class extends AudioWorkletProcessor {__b=new WeakSet();constructor(){super();(p=>p.postMessage=(q=>(m,t)=>q.call(p,m,t?t.filter(u=>!this.__b.has(u)):t))(p.postMessage))(this.port)}}"},(n,p)=>registerProcessor(n,class extends p{${y?"":"__c = (a) => a.forEach(e=>this.__b.add(e.buffer));"}process(i,o,p){${y?"":"i.forEach(this.__c);o.forEach(this.__c);this.__c(Object.values(p));"}return super.process(i.map(j=>j.some(k=>k.length===0)?[]:j),o,p)}}));registerProcessor('__sac${v}',class extends AudioWorkletProcessor{process(){return !1}})`,U=new Blob([W],{type:"application/javascript; charset=utf-8"}),k=URL.createObjectURL(U);return E.audioWorklet.addModule(k,m).then(()=>{if(i(E))return E;const L=a(E);return L.audioWorklet.addModule(k,m).then(()=>L)}).then(L=>{if(c===null)throw new SyntaxError;try{new c(L,`__sac${v}`)}catch{throw new SyntaxError}}).finally(()=>URL.revokeObjectURL(k))});return _===void 0?u.set(f,new Map([[h,T]])):_.set(h,T),T.then(()=>{const A=d.get(f);A===void 0?d.set(f,new Set([h])):A.add(h)}).finally(()=>{const A=u.get(f);A!==void 0&&A.delete(h)}),T}},K=(e,t)=>{const n=e.get(t);if(n===void 0)throw new Error("A value with the given key could not be found.");return n},Fe=(e,t)=>{const n=Array.from(e).filter(t);if(n.length>1)throw Error("More than one element was found.");if(n.length===0)throw Error("No element was found.");const[r]=n;return e.delete(r),r},En=(e,t,n,r)=>{const o=K(e,t),s=Fe(o,a=>a[0]===n&&a[1]===r);return o.size===0&&e.delete(t),s},Ae=e=>K(vn,e),Pe=e=>{if(we.has(e))throw new Error("The AudioNode is already stored.");we.add(e),Ae(e).forEach(t=>t(!0))},yn=e=>"port"in e,gt=e=>{if(!we.has(e))throw new Error("The AudioNode is not stored.");we.delete(e),Ae(e).forEach(t=>t(!1))},it=(e,t)=>{!yn(e)&&t.every(n=>n.size===0)&&gt(e)},_o=(e,t,n,r,o,s,a,i,c,u,d,l,w)=>{const g=new WeakMap;return(f,h,m,p,_)=>{const{activeInputs:E,passiveInputs:T}=s(h),{outputs:A}=s(f),b=i(f),y=v=>{const N=c(h),I=c(f);if(v){const M=En(T,f,m,p);e(E,f,M,!1),!_&&!l(f)&&n(I,N,m,p),w(h)&&Pe(h)}else{const M=r(E,f,m,p);t(T,p,M,!1),!_&&!l(f)&&o(I,N,m,p);const x=a(h);if(x===0)d(h)&&it(h,E);else{const S=g.get(h);S!==void 0&&clearTimeout(S),g.set(h,setTimeout(()=>{d(h)&&it(h,E)},x*1e3))}}};return u(A,[h,m,p],v=>v[0]===h&&v[1]===m&&v[2]===p,!0)?(b.add(y),d(f)?e(E,f,[m,p,y],!0):t(T,p,[f,m,y],!0),!0):!1}},Eo=e=>(t,n,[r,o,s],a)=>{const i=t.get(r);i===void 0?t.set(r,new Set([[o,n,s]])):e(i,[o,n,s],c=>c[0]===o&&c[1]===n,a)},yo=e=>(t,n)=>{const r=e(t,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});n.connect(r).connect(t.destination);const o=()=>{n.removeEventListener("ended",o),n.disconnect(r),r.disconnect()};n.addEventListener("ended",o)},Ao=e=>(t,n)=>{e(t).add(n)},An=(e,t)=>e.context===t,ct=e=>{try{e.copyToChannel(new Float32Array(1),0,-1)}catch{return!1}return!0},ae=()=>new DOMException("","IndexSizeError"),bn=e=>{e.getChannelData=(t=>n=>{try{return t.call(e,n)}catch(r){throw r.code===12?ae():r}})(e.getChannelData)},bo={numberOfChannels:1},Co=(e,t,n,r,o,s,a,i)=>{let c=null;return class Cn{constructor(d){if(o===null)throw new Error("Missing the native OfflineAudioContext constructor.");const{length:l,numberOfChannels:w,sampleRate:g}={...bo,...d};c===null&&(c=new o(1,1,44100));const f=r!==null&&t(s,s)?new r({length:l,numberOfChannels:w,sampleRate:g}):c.createBuffer(w,l,g);if(f.numberOfChannels===0)throw n();return typeof f.copyFromChannel!="function"?(a(f),bn(f)):t(ct,()=>ct(f))||i(f),e.add(f),f}static[Symbol.hasInstance](d){return d!==null&&typeof d=="object"&&Object.getPrototypeOf(d)===Cn.prototype||e.has(d)}}},je=-34028234663852886e22,wt=-je,ce=e=>we.has(e),To={buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1},Mo=(e,t,n,r,o,s,a,i)=>class extends e{constructor(u,d){const l=s(u),w={...To,...d},g=o(l,w),f=a(l),h=f?t():null;super(u,!1,g,h),this._audioBufferSourceNodeRenderer=h,this._isBufferNullified=!1,this._isBufferSet=w.buffer!==null,this._nativeAudioBufferSourceNode=g,this._onended=null,this._playbackRate=n(this,f,g.playbackRate,wt,je)}get buffer(){return this._isBufferNullified?null:this._nativeAudioBufferSourceNode.buffer}set buffer(u){if(this._nativeAudioBufferSourceNode.buffer=u,u!==null){if(this._isBufferSet)throw r();this._isBufferSet=!0}}get loop(){return this._nativeAudioBufferSourceNode.loop}set loop(u){this._nativeAudioBufferSourceNode.loop=u}get loopEnd(){return this._nativeAudioBufferSourceNode.loopEnd}set loopEnd(u){this._nativeAudioBufferSourceNode.loopEnd=u}get loopStart(){return this._nativeAudioBufferSourceNode.loopStart}set loopStart(u){this._nativeAudioBufferSourceNode.loopStart=u}get onended(){return this._onended}set onended(u){const d=typeof u=="function"?i(this,u):null;this._nativeAudioBufferSourceNode.onended=d;const l=this._nativeAudioBufferSourceNode.onended;this._onended=l!==null&&l===d?u:l}get playbackRate(){return this._playbackRate}start(u=0,d=0,l){if(this._nativeAudioBufferSourceNode.start(u,d,l),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.start=l===void 0?[u,d]:[u,d,l]),this.context.state!=="closed"){Pe(this);const w=()=>{this._nativeAudioBufferSourceNode.removeEventListener("ended",w),ce(this)&&gt(this)};this._nativeAudioBufferSourceNode.addEventListener("ended",w)}}stop(u=0){this._nativeAudioBufferSourceNode.stop(u),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.stop=u)}},No=(e,t,n,r,o)=>()=>{const s=new WeakMap;let a=null,i=null;const c=async(u,d)=>{let l=n(u);const w=An(l,d);if(!w){const g={buffer:l.buffer,channelCount:l.channelCount,channelCountMode:l.channelCountMode,channelInterpretation:l.channelInterpretation,loop:l.loop,loopEnd:l.loopEnd,loopStart:l.loopStart,playbackRate:l.playbackRate.value};l=t(d,g),a!==null&&l.start(...a),i!==null&&l.stop(i)}return s.set(d,l),w?await e(d,u.playbackRate,l.playbackRate):await r(d,u.playbackRate,l.playbackRate),await o(u,d,l),l};return{set start(u){a=u},set stop(u){i=u},render(u,d){const l=s.get(d);return l!==void 0?Promise.resolve(l):c(u,d)}}},Oo=e=>"playbackRate"in e,Ro=e=>"frequency"in e&&"gain"in e,Io=e=>"offset"in e,ko=e=>!("frequency"in e)&&"gain"in e,So=e=>"detune"in e&&"frequency"in e,Lo=e=>"pan"in e,q=e=>K(hn,e),be=e=>K(mn,e),ut=(e,t)=>{const{activeInputs:n}=q(e);n.forEach(o=>o.forEach(([s])=>{t.includes(e)||ut(s,[...t,e])}));const r=Oo(e)?[e.playbackRate]:yn(e)?Array.from(e.parameters.values()):Ro(e)?[e.Q,e.detune,e.frequency,e.gain]:Io(e)?[e.offset]:ko(e)?[e.gain]:So(e)?[e.detune,e.frequency]:Lo(e)?[e.pan]:[];for(const o of r){const s=be(o);s!==void 0&&s.activeInputs.forEach(([a])=>ut(a,t))}ce(e)&&gt(e)},Po=e=>{ut(e.destination,[])},Uo=e=>e===void 0||typeof e=="number"||typeof e=="string"&&(e==="balanced"||e==="interactive"||e==="playback"),xo=(e,t,n,r,o,s,a,i)=>class extends e{constructor(u,d){const l=s(u),w=a(l),g=o(l,d,w),f=w?t(i):null;super(u,!1,g,f),this._isNodeOfNativeOfflineAudioContext=w,this._nativeAudioDestinationNode=g}get channelCount(){return this._nativeAudioDestinationNode.channelCount}set channelCount(u){if(this._isNodeOfNativeOfflineAudioContext)throw r();if(u>this._nativeAudioDestinationNode.maxChannelCount)throw n();this._nativeAudioDestinationNode.channelCount=u}get channelCountMode(){return this._nativeAudioDestinationNode.channelCountMode}set channelCountMode(u){if(this._isNodeOfNativeOfflineAudioContext)throw r();this._nativeAudioDestinationNode.channelCountMode=u}get maxChannelCount(){return this._nativeAudioDestinationNode.maxChannelCount}},Bo=e=>{const t=new WeakMap,n=async(r,o)=>{const s=o.destination;return t.set(o,s),await e(r,o,s),s};return{render(r,o){const s=t.get(o);return s!==void 0?Promise.resolve(s):n(r,o)}}},Wo=(e,t,n,r,o,s,a,i)=>(c,u)=>{const d=u.listener,l=()=>{const A=new Float32Array(1),b=t(u,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:9}),y=a(u);let v=!1,N=[0,0,-1,0,1,0],I=[0,0,0];const M=()=>{if(v)return;v=!0;const U=r(u,256,9,0);U.onaudioprocess=({inputBuffer:k})=>{const L=[s(k,A,0),s(k,A,1),s(k,A,2),s(k,A,3),s(k,A,4),s(k,A,5)];L.some((O,P)=>O!==N[P])&&(d.setOrientation(...L),N=L);const B=[s(k,A,6),s(k,A,7),s(k,A,8)];B.some((O,P)=>O!==I[P])&&(d.setPosition(...B),I=B)},b.connect(U)},x=U=>k=>{k!==N[U]&&(N[U]=k,d.setOrientation(...N))},S=U=>k=>{k!==I[U]&&(I[U]=k,d.setPosition(...I))},W=(U,k,L)=>{const B=n(u,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:k});B.connect(b,0,U),B.start(),Object.defineProperty(B.offset,"defaultValue",{get(){return k}});const O=e({context:c},y,B.offset,wt,je);return i(O,"value",P=>()=>P.call(O),P=>V=>{try{P.call(O,V)}catch(G){if(G.code!==9)throw G}M(),y&&L(V)}),O.cancelAndHoldAtTime=(P=>y?()=>{throw o()}:(...V)=>{const G=P.apply(O,V);return M(),G})(O.cancelAndHoldAtTime),O.cancelScheduledValues=(P=>y?()=>{throw o()}:(...V)=>{const G=P.apply(O,V);return M(),G})(O.cancelScheduledValues),O.exponentialRampToValueAtTime=(P=>y?()=>{throw o()}:(...V)=>{const G=P.apply(O,V);return M(),G})(O.exponentialRampToValueAtTime),O.linearRampToValueAtTime=(P=>y?()=>{throw o()}:(...V)=>{const G=P.apply(O,V);return M(),G})(O.linearRampToValueAtTime),O.setTargetAtTime=(P=>y?()=>{throw o()}:(...V)=>{const G=P.apply(O,V);return M(),G})(O.setTargetAtTime),O.setValueAtTime=(P=>y?()=>{throw o()}:(...V)=>{const G=P.apply(O,V);return M(),G})(O.setValueAtTime),O.setValueCurveAtTime=(P=>y?()=>{throw o()}:(...V)=>{const G=P.apply(O,V);return M(),G})(O.setValueCurveAtTime),O};return{forwardX:W(0,0,x(0)),forwardY:W(1,0,x(1)),forwardZ:W(2,-1,x(2)),positionX:W(6,0,S(0)),positionY:W(7,0,S(1)),positionZ:W(8,0,S(2)),upX:W(3,0,x(3)),upY:W(4,1,x(4)),upZ:W(5,0,x(5))}},{forwardX:w,forwardY:g,forwardZ:f,positionX:h,positionY:m,positionZ:p,upX:_,upY:E,upZ:T}=d.forwardX===void 0?l():d;return{get forwardX(){return w},get forwardY(){return g},get forwardZ(){return f},get positionX(){return h},get positionY(){return m},get positionZ(){return p},get upX(){return _},get upY(){return E},get upZ(){return T}}},Ue=e=>"context"in e,Ce=e=>Ue(e[0]),le=(e,t,n,r)=>{for(const o of e)if(n(o)){if(r)return!1;throw Error("The set contains at least one similar element.")}return e.add(t),!0},Yt=(e,t,[n,r],o)=>{le(e,[t,n,r],s=>s[0]===t&&s[1]===n,o)},Zt=(e,[t,n,r],o)=>{const s=e.get(t);s===void 0?e.set(t,new Set([[n,r]])):le(s,[n,r],a=>a[0]===n,o)},Tn=e=>"inputs"in e,lt=(e,t,n,r)=>{if(Tn(t)){const o=t.inputs[r];return e.connect(o,n,0),[o,n,0]}return e.connect(t,n,r),[t,n,r]},Mn=(e,t,n)=>{for(const r of e)if(r[0]===t&&r[1]===n)return e.delete(r),r;return null},Do=(e,t,n)=>Fe(e,r=>r[0]===t&&r[1]===n),Nn=(e,t)=>{if(!Ae(e).delete(t))throw new Error("Missing the expected event listener.")},On=(e,t,n)=>{const r=K(e,t),o=Fe(r,s=>s[0]===n);return r.size===0&&e.delete(t),o},dt=(e,t,n,r)=>{Tn(t)?e.disconnect(t.inputs[r],n,0):e.disconnect(t,n,r)},Y=e=>K(pn,e),Ee=e=>K(gn,e),ue=e=>ot.has(e),Ie=e=>!we.has(e),Kt=(e,t)=>new Promise(n=>{if(t!==null)n(!0);else{const r=e.createScriptProcessor(256,1,1),o=e.createGain(),s=e.createBuffer(1,2,44100),a=s.getChannelData(0);a[0]=1,a[1]=1;const i=e.createBufferSource();i.buffer=s,i.loop=!0,i.connect(r).connect(e.destination),i.connect(o),i.disconnect(o),r.onaudioprocess=c=>{const u=c.inputBuffer.getChannelData(0);Array.prototype.some.call(u,d=>d===1)?n(!0):n(!1),i.stop(),r.onaudioprocess=null,i.disconnect(r),r.disconnect(e.destination)},i.start()}}),Je=(e,t)=>{const n=new Map;for(const r of e)for(const o of r){const s=n.get(o);n.set(o,s===void 0?1:s+1)}n.forEach((r,o)=>t(o,r))},xe=e=>"context"in e,Vo=e=>{const t=new Map;e.connect=(n=>(r,o=0,s=0)=>{const a=xe(r)?n(r,o,s):n(r,o),i=t.get(r);return i===void 0?t.set(r,[{input:s,output:o}]):i.every(c=>c.input!==s||c.output!==o)&&i.push({input:s,output:o}),a})(e.connect.bind(e)),e.disconnect=(n=>(r,o,s)=>{if(n.apply(e),r===void 0)t.clear();else if(typeof r=="number")for(const[a,i]of t){const c=i.filter(u=>u.output!==r);c.length===0?t.delete(a):t.set(a,c)}else if(t.has(r))if(o===void 0)t.delete(r);else{const a=t.get(r);if(a!==void 0){const i=a.filter(c=>c.output!==o&&(c.input!==s||s===void 0));i.length===0?t.delete(r):t.set(r,i)}}for(const[a,i]of t)i.forEach(c=>{xe(a)?e.connect(a,c.output,c.input):e.connect(a,c.output)})})(e.disconnect)},Fo=(e,t,n,r)=>{const{activeInputs:o,passiveInputs:s}=be(t),{outputs:a}=q(e),i=Ae(e),c=u=>{const d=Y(e),l=Ee(t);if(u){const w=On(s,e,n);Yt(o,e,w,!1),!r&&!ue(e)&&d.connect(l,n)}else{const w=Do(o,e,n);Zt(s,w,!1),!r&&!ue(e)&&d.disconnect(l,n)}};return le(a,[t,n],u=>u[0]===t&&u[1]===n,!0)?(i.add(c),ce(e)?Yt(o,e,[n,c],!0):Zt(s,[e,n,c],!0),!0):!1},jo=(e,t,n,r)=>{const{activeInputs:o,passiveInputs:s}=q(t),a=Mn(o[r],e,n);return a===null?[En(s,e,n,r)[2],!1]:[a[2],!0]},$o=(e,t,n)=>{const{activeInputs:r,passiveInputs:o}=be(t),s=Mn(r,e,n);return s===null?[On(o,e,n)[1],!1]:[s[2],!0]},vt=(e,t,n,r,o)=>{const[s,a]=jo(e,n,r,o);if(s!==null&&(Nn(e,s),a&&!t&&!ue(e)&&dt(Y(e),Y(n),r,o)),ce(n)){const{activeInputs:i}=q(n);it(n,i)}},_t=(e,t,n,r)=>{const[o,s]=$o(e,n,r);o!==null&&(Nn(e,o),s&&!t&&!ue(e)&&Y(e).disconnect(Ee(n),r))},Go=(e,t)=>{const n=q(e),r=[];for(const o of n.outputs)Ce(o)?vt(e,t,...o):_t(e,t,...o),r.push(o[0]);return n.outputs.clear(),r},zo=(e,t,n)=>{const r=q(e),o=[];for(const s of r.outputs)s[1]===n&&(Ce(s)?vt(e,t,...s):_t(e,t,...s),o.push(s[0]),r.outputs.delete(s));return o},qo=(e,t,n,r,o)=>{const s=q(e);return Array.from(s.outputs).filter(a=>a[0]===n&&(r===void 0||a[1]===r)&&(o===void 0||a[2]===o)).map(a=>(Ce(a)?vt(e,t,...a):_t(e,t,...a),s.outputs.delete(a),a[0]))},Ho=(e,t,n,r,o,s,a,i,c,u,d,l,w,g,f,h)=>class extends u{constructor(p,_,E,T){super(E),this._context=p,this._nativeAudioNode=E;const A=d(p);l(A)&&n(Kt,()=>Kt(A,h))!==!0&&Vo(E),pn.set(this,E),vn.set(this,new Set),p.state!=="closed"&&_&&Pe(this),e(this,T,E)}get channelCount(){return this._nativeAudioNode.channelCount}set channelCount(p){this._nativeAudioNode.channelCount=p}get channelCountMode(){return this._nativeAudioNode.channelCountMode}set channelCountMode(p){this._nativeAudioNode.channelCountMode=p}get channelInterpretation(){return this._nativeAudioNode.channelInterpretation}set channelInterpretation(p){this._nativeAudioNode.channelInterpretation=p}get context(){return this._context}get numberOfInputs(){return this._nativeAudioNode.numberOfInputs}get numberOfOutputs(){return this._nativeAudioNode.numberOfOutputs}connect(p,_=0,E=0){if(_<0||_>=this._nativeAudioNode.numberOfOutputs)throw o();const T=d(this._context),A=f(T);if(w(p)||g(p))throw s();if(Ue(p)){const v=Y(p);try{const I=lt(this._nativeAudioNode,v,_,E),M=Ie(this);(A||M)&&this._nativeAudioNode.disconnect(...I),this.context.state!=="closed"&&!M&&Ie(p)&&Pe(p)}catch(I){throw I.code===12?s():I}if(t(this,p,_,E,A)){const I=c([this],p);Je(I,r(A))}return p}const b=Ee(p);if(b.name==="playbackRate"&&b.maxValue===1024)throw a();try{this._nativeAudioNode.connect(b,_),(A||Ie(this))&&this._nativeAudioNode.disconnect(b,_)}catch(v){throw v.code===12?s():v}if(Fo(this,p,_,A)){const v=c([this],p);Je(v,r(A))}}disconnect(p,_,E){let T;const A=d(this._context),b=f(A);if(p===void 0)T=Go(this,b);else if(typeof p=="number"){if(p<0||p>=this.numberOfOutputs)throw o();T=zo(this,b,p)}else{if(_!==void 0&&(_<0||_>=this.numberOfOutputs)||Ue(p)&&E!==void 0&&(E<0||E>=p.numberOfInputs))throw o();if(T=qo(this,b,p,_,E),T.length===0)throw s()}for(const y of T){const v=c([this],y);Je(v,i)}}},Xo=(e,t,n,r,o,s,a,i,c,u,d,l,w)=>(g,f,h,m=null,p=null)=>{const _=h.value,E=new ao(_),T=f?r(E):null,A={get defaultValue(){return _},get maxValue(){return m===null?h.maxValue:m},get minValue(){return p===null?h.minValue:p},get value(){return h.value},set value(b){h.value=b,A.setValueAtTime(b,g.context.currentTime)},cancelAndHoldAtTime(b){if(typeof h.cancelAndHoldAtTime=="function")T===null&&E.flush(g.context.currentTime),E.add(o(b)),h.cancelAndHoldAtTime(b);else{const y=Array.from(E).pop();T===null&&E.flush(g.context.currentTime),E.add(o(b));const v=Array.from(E).pop();h.cancelScheduledValues(b),y!==v&&v!==void 0&&(v.type==="exponentialRampToValue"?h.exponentialRampToValueAtTime(v.value,v.endTime):v.type==="linearRampToValue"?h.linearRampToValueAtTime(v.value,v.endTime):v.type==="setValue"?h.setValueAtTime(v.value,v.startTime):v.type==="setValueCurve"&&h.setValueCurveAtTime(v.values,v.startTime,v.duration))}return A},cancelScheduledValues(b){return T===null&&E.flush(g.context.currentTime),E.add(s(b)),h.cancelScheduledValues(b),A},exponentialRampToValueAtTime(b,y){if(b===0)throw new RangeError;if(!Number.isFinite(y)||y<0)throw new RangeError;const v=g.context.currentTime;return T===null&&E.flush(v),Array.from(E).length===0&&(E.add(u(_,v)),h.setValueAtTime(_,v)),E.add(a(b,y)),h.exponentialRampToValueAtTime(b,y),A},linearRampToValueAtTime(b,y){const v=g.context.currentTime;return T===null&&E.flush(v),Array.from(E).length===0&&(E.add(u(_,v)),h.setValueAtTime(_,v)),E.add(i(b,y)),h.linearRampToValueAtTime(b,y),A},setTargetAtTime(b,y,v){return T===null&&E.flush(g.context.currentTime),E.add(c(b,y,v)),h.setTargetAtTime(b,y,v),A},setValueAtTime(b,y){return T===null&&E.flush(g.context.currentTime),E.add(u(b,y)),h.setValueAtTime(b,y),A},setValueCurveAtTime(b,y,v){const N=b instanceof Float32Array?b:new Float32Array(b);if(l!==null&&l.name==="webkitAudioContext"){const I=y+v,M=g.context.sampleRate,x=Math.ceil(y*M),S=Math.floor(I*M),W=S-x,U=new Float32Array(W);for(let L=0;L<W;L+=1){const B=(N.length-1)/v*((x+L)/M-y),O=Math.floor(B),P=Math.ceil(B);U[L]=O===P?N[O]:(1-(B-O))*N[O]+(1-(P-B))*N[P]}T===null&&E.flush(g.context.currentTime),E.add(d(U,y,v)),h.setValueCurveAtTime(U,y,v);const k=S/M;k<I&&w(A,U[U.length-1],k),w(A,N[N.length-1],I)}else T===null&&E.flush(g.context.currentTime),E.add(d(N,y,v)),h.setValueCurveAtTime(N,y,v);return A}};return n.set(A,h),t.set(A,g),e(A,T),A},Yo=e=>({replay(t){for(const n of e)if(n.type==="exponentialRampToValue"){const{endTime:r,value:o}=n;t.exponentialRampToValueAtTime(o,r)}else if(n.type==="linearRampToValue"){const{endTime:r,value:o}=n;t.linearRampToValueAtTime(o,r)}else if(n.type==="setTarget"){const{startTime:r,target:o,timeConstant:s}=n;t.setTargetAtTime(o,r,s)}else if(n.type==="setValue"){const{startTime:r,value:o}=n;t.setValueAtTime(o,r)}else if(n.type==="setValueCurve"){const{duration:r,startTime:o,values:s}=n;t.setValueCurveAtTime(s,o,r)}else throw new Error("Can't apply an unknown automation.")}});class Rn{constructor(t){this._map=new Map(t)}get size(){return this._map.size}entries(){return this._map.entries()}forEach(t,n=null){return this._map.forEach((r,o)=>t.call(n,r,o,this))}get(t){return this._map.get(t)}has(t){return this._map.has(t)}keys(){return this._map.keys()}values(){return this._map.values()}}const Zo={channelCount:2,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:1,numberOfOutputs:1,parameterData:{},processorOptions:{}},Ko=(e,t,n,r,o,s,a,i,c,u,d,l,w,g)=>class extends t{constructor(h,m,p){var _;const E=i(h),T=c(E),A=d({...Zo,...p});w(A);const b=st.get(E),y=b?.get(m),v=T||E.state!=="closed"?E:(_=a(E))!==null&&_!==void 0?_:E,N=o(v,T?null:h.baseLatency,u,m,y,A),I=T?r(m,A,y):null;super(h,!0,N,I);const M=[];N.parameters.forEach((S,W)=>{const U=n(this,T,S);M.push([W,U])}),this._nativeAudioWorkletNode=N,this._onprocessorerror=null,this._parameters=new Rn(M),T&&e(E,this);const{activeInputs:x}=s(this);l(N,x)}get onprocessorerror(){return this._onprocessorerror}set onprocessorerror(h){const m=typeof h=="function"?g(this,h):null;this._nativeAudioWorkletNode.onprocessorerror=m;const p=this._nativeAudioWorkletNode.onprocessorerror;this._onprocessorerror=p!==null&&p===m?h:p}get parameters(){return this._parameters===null?this._nativeAudioWorkletNode.parameters:this._parameters}get port(){return this._nativeAudioWorkletNode.port}};function Be(e,t,n,r,o){if(typeof e.copyFromChannel=="function")t[n].byteLength===0&&(t[n]=new Float32Array(128)),e.copyFromChannel(t[n],r,o);else{const s=e.getChannelData(r);if(t[n].byteLength===0)t[n]=s.slice(o,o+128);else{const a=new Float32Array(s.buffer,o*Float32Array.BYTES_PER_ELEMENT,128);t[n].set(a)}}}const In=(e,t,n,r,o)=>{typeof e.copyToChannel=="function"?t[n].byteLength!==0&&e.copyToChannel(t[n],r,o):t[n].byteLength!==0&&e.getChannelData(r).set(t[n],o)},We=(e,t)=>{const n=[];for(let r=0;r<e;r+=1){const o=[],s=typeof t=="number"?t:t[r];for(let a=0;a<s;a+=1)o.push(new Float32Array(128));n.push(o)}return n},Qo=(e,t)=>{const n=K(at,e),r=Y(t);return K(n,r)},Jo=async(e,t,n,r,o,s,a)=>{const i=t===null?Math.ceil(e.context.length/128)*128:t.length,c=r.channelCount*r.numberOfInputs,u=o.reduce((m,p)=>m+p,0),d=u===0?null:n.createBuffer(u,i,n.sampleRate);if(s===void 0)throw new Error("Missing the processor constructor.");const l=q(e),w=await Qo(n,e),g=We(r.numberOfInputs,r.channelCount),f=We(r.numberOfOutputs,o),h=Array.from(e.parameters.keys()).reduce((m,p)=>({...m,[p]:new Float32Array(128)}),{});for(let m=0;m<i;m+=128){if(r.numberOfInputs>0&&t!==null)for(let p=0;p<r.numberOfInputs;p+=1)for(let _=0;_<r.channelCount;_+=1)Be(t,g[p],_,_,m);s.parameterDescriptors!==void 0&&t!==null&&s.parameterDescriptors.forEach(({name:p},_)=>{Be(t,h,p,c+_,m)});for(let p=0;p<r.numberOfInputs;p+=1)for(let _=0;_<o[p];_+=1)f[p][_].byteLength===0&&(f[p][_]=new Float32Array(128));try{const p=g.map((E,T)=>l.activeInputs[T].size===0?[]:E),_=a(m/n.sampleRate,n.sampleRate,()=>w.process(p,f,h));if(d!==null)for(let E=0,T=0;E<r.numberOfOutputs;E+=1){for(let A=0;A<o[E];A+=1)In(d,f[E],A,T+A,m);T+=o[E]}if(!_)break}catch(p){e.dispatchEvent(new ErrorEvent("processorerror",{colno:p.colno,filename:p.filename,lineno:p.lineno,message:p.message}));break}}return d},es=(e,t,n,r,o,s,a,i,c,u,d,l,w,g,f,h)=>(m,p,_)=>{const E=new WeakMap;let T=null;const A=async(b,y)=>{let v=d(b),N=null;const I=An(v,y),M=Array.isArray(p.outputChannelCount)?p.outputChannelCount:Array.from(p.outputChannelCount);if(l===null){const x=M.reduce((k,L)=>k+L,0),S=o(y,{channelCount:Math.max(1,x),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,x)}),W=[];for(let k=0;k<b.numberOfOutputs;k+=1)W.push(r(y,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:M[k]}));const U=a(y,{channelCount:p.channelCount,channelCountMode:p.channelCountMode,channelInterpretation:p.channelInterpretation,gain:1});U.connect=t.bind(null,W),U.disconnect=c.bind(null,W),N=[S,W,U]}else I||(v=new l(y,m));if(E.set(y,N===null?v:N[2]),N!==null){if(T===null){if(_===void 0)throw new Error("Missing the processor constructor.");if(w===null)throw new Error("Missing the native OfflineAudioContext constructor.");const L=b.channelCount*b.numberOfInputs,B=_.parameterDescriptors===void 0?0:_.parameterDescriptors.length,O=L+B;T=Jo(b,O===0?null:await(async()=>{const V=new w(O,Math.ceil(b.context.length/128)*128,y.sampleRate),G=[],he=[];for(let j=0;j<p.numberOfInputs;j+=1)G.push(a(V,{channelCount:p.channelCount,channelCountMode:p.channelCountMode,channelInterpretation:p.channelInterpretation,gain:1})),he.push(o(V,{channelCount:p.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:p.channelCount}));const pe=await Promise.all(Array.from(b.parameters.values()).map(async j=>{const H=s(V,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:j.value});return await g(V,j,H.offset),H})),me=r(V,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,L+B)});for(let j=0;j<p.numberOfInputs;j+=1){G[j].connect(he[j]);for(let H=0;H<p.channelCount;H+=1)he[j].connect(me,H,j*p.channelCount+H)}for(const[j,H]of pe.entries())H.connect(me,0,L+j),H.start(0);return me.connect(V.destination),await Promise.all(G.map(j=>f(b,V,j))),h(V)})(),y,p,M,_,u)}const x=await T,S=n(y,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),[W,U,k]=N;x!==null&&(S.buffer=x,S.start(0)),S.connect(W);for(let L=0,B=0;L<b.numberOfOutputs;L+=1){const O=U[L];for(let P=0;P<M[L];P+=1)W.connect(O,B+P,P);B+=M[L]}return k}if(I)for(const[x,S]of b.parameters.entries())await e(y,S,v.parameters.get(x));else for(const[x,S]of b.parameters.entries())await g(y,S,v.parameters.get(x));return await f(b,y,v),v};return{render(b,y){i(y,b);const v=E.get(y);return v!==void 0?Promise.resolve(v):A(b,y)}}},ts=(e,t)=>(n,r)=>{const o=t.get(n);if(o!==void 0)return o;const s=e.get(n);if(s!==void 0)return s;try{const a=r();return a instanceof Promise?(e.set(n,a),a.catch(()=>!1).then(i=>(e.delete(n),t.set(n,i),i))):(t.set(n,a),a)}catch{return t.set(n,!1),!1}},ns=e=>(t,n,r)=>e(n,t,r),rs=e=>(t,n,r=0,o=0)=>{const s=t[r];if(s===void 0)throw e();return xe(n)?s.connect(n,0,o):s.connect(n,0)},os=e=>t=>(e[0]=t,e[0]),ss=()=>new DOMException("","DataCloneError"),Qt=e=>{const{port1:t,port2:n}=new MessageChannel;return new Promise(r=>{const o=()=>{n.onmessage=null,t.close(),n.close(),r()};n.onmessage=()=>o();try{t.postMessage(e,[e])}catch{}finally{o()}})},as=(e,t,n,r,o,s,a,i,c,u,d)=>(l,w)=>{const g=a(l)?l:s(l);if(o.has(w)){const f=n();return Promise.reject(f)}try{o.add(w)}catch{}return t(c,()=>c(g))?g.decodeAudioData(w).then(f=>(Qt(w).catch(()=>{}),t(i,()=>i(f))||d(f),e.add(f),f)):new Promise((f,h)=>{const m=async()=>{try{await Qt(w)}catch{}},p=_=>{h(_),m()};try{g.decodeAudioData(w,_=>{typeof _.copyFromChannel!="function"&&(u(_),bn(_)),e.add(_),m().then(()=>f(_))},_=>{p(_===null?r():_)})}catch(_){p(_)}})},is=(e,t,n,r,o,s,a,i)=>(c,u)=>{const d=t.get(c);if(d===void 0)throw new Error("Missing the expected cycle count.");const l=s(c.context),w=i(l);if(d===u){if(t.delete(c),!w&&a(c)){const g=r(c),{outputs:f}=n(c);for(const h of f)if(Ce(h)){const m=r(h[0]);e(g,m,h[1],h[2])}else{const m=o(h[0]);g.connect(m,h[1])}}}else t.set(c,d-u)},cs=e=>(t,n,r,o)=>e(t[o],s=>s[0]===n&&s[1]===r),us=e=>(t,n)=>{e(t).delete(n)},ls=e=>"delayTime"in e,ds=(e,t,n)=>function r(o,s){const a=Ue(s)?s:n(e,s);if(ls(a))return[];if(o[0]===a)return[o];if(o.includes(a))return[];const{outputs:i}=t(a);return Array.from(i).map(c=>r([...o,a],c[0])).reduce((c,u)=>c.concat(u),[])},Re=(e,t,n)=>{const r=t[n];if(r===void 0)throw e();return r},fs=e=>(t,n=void 0,r=void 0,o=0)=>n===void 0?t.forEach(s=>s.disconnect()):typeof n=="number"?Re(e,t,n).disconnect():xe(n)?r===void 0?t.forEach(s=>s.disconnect(n)):o===void 0?Re(e,t,r).disconnect(n,0):Re(e,t,r).disconnect(n,0,o):r===void 0?t.forEach(s=>s.disconnect(n)):Re(e,t,r).disconnect(n,0),hs=()=>new DOMException("","EncodingError"),ps=e=>t=>new Promise((n,r)=>{if(e===null){r(new SyntaxError);return}const o=e.document.head;if(o===null)r(new SyntaxError);else{const s=e.document.createElement("script"),a=new Blob([t],{type:"application/javascript"}),i=URL.createObjectURL(a),c=e.onerror,u=()=>{e.onerror=c,URL.revokeObjectURL(i)};e.onerror=(d,l,w,g,f)=>{if(l===i||l===e.location.href&&w===1&&g===1)return u(),r(f),!1;if(c!==null)return c(d,l,w,g,f)},s.onerror=()=>{u(),r(new SyntaxError)},s.onload=()=>{u(),n()},s.src=i,s.type="module",o.appendChild(s)}}),ms=e=>class{constructor(n){this._nativeEventTarget=n,this._listeners=new WeakMap}addEventListener(n,r,o){if(r!==null){let s=this._listeners.get(r);s===void 0&&(s=e(this,r),typeof r=="function"&&this._listeners.set(r,s)),this._nativeEventTarget.addEventListener(n,s,o)}}dispatchEvent(n){return this._nativeEventTarget.dispatchEvent(n)}removeEventListener(n,r,o){const s=r===null?void 0:this._listeners.get(r);this._nativeEventTarget.removeEventListener(n,s===void 0?null:s,o)}},gs=e=>(t,n,r)=>{Object.defineProperties(e,{currentFrame:{configurable:!0,get(){return Math.round(t*n)}},currentTime:{configurable:!0,get(){return t}}});try{return r()}finally{e!==null&&(delete e.currentFrame,delete e.currentTime)}},ws=e=>async t=>{try{const n=await fetch(t);if(n.ok)return[await n.text(),n.url]}catch{}throw e()},vs=(e,t)=>n=>t(e,n),_s=e=>t=>{const n=e(t);if(n.renderer===null)throw new Error("Missing the renderer of the given AudioNode in the audio graph.");return n.renderer},Es=e=>t=>{var n;return(n=e.get(t))!==null&&n!==void 0?n:0},ys=e=>t=>{const n=e(t);if(n.renderer===null)throw new Error("Missing the renderer of the given AudioParam in the audio graph.");return n.renderer},As=e=>t=>e.get(t),Z=()=>new DOMException("","InvalidStateError"),bs=e=>t=>{const n=e.get(t);if(n===void 0)throw Z();return n},Cs=(e,t)=>n=>{let r=e.get(n);if(r!==void 0)return r;if(t===null)throw new Error("Missing the native OfflineAudioContext constructor.");return r=new t(1,1,44100),e.set(n,r),r},Ts=e=>t=>{const n=e.get(t);if(n===void 0)throw new Error("The context has no set of AudioWorkletNodes.");return n},Ms=()=>new DOMException("","InvalidAccessError"),Ns=(e,t,n,r,o,s)=>a=>(i,c)=>{const u=e.get(i);if(u===void 0){if(!a&&s(i)){const d=r(i),{outputs:l}=n(i);for(const w of l)if(Ce(w)){const g=r(w[0]);t(d,g,w[1],w[2])}else{const g=o(w[0]);d.disconnect(g,w[1])}}e.set(i,c)}else e.set(i,u+c)},Os=e=>t=>e!==null&&t instanceof e,Rs=e=>t=>e!==null&&typeof e.AudioNode=="function"&&t instanceof e.AudioNode,Is=e=>t=>e!==null&&typeof e.AudioParam=="function"&&t instanceof e.AudioParam,ks=(e,t)=>n=>e(n)||t(n),Ss=e=>t=>e!==null&&t instanceof e,Ls=e=>e!==null&&e.isSecureContext,Ps=(e,t,n,r)=>class extends e{constructor(s,a){const i=n(s),c=t(i,a);if(r(i))throw new TypeError;super(s,!0,c,null),this._nativeMediaStreamAudioSourceNode=c}get mediaStream(){return this._nativeMediaStreamAudioSourceNode.mediaStream}},Us=(e,t,n,r,o)=>class extends r{constructor(a={}){if(o===null)throw new Error("Missing the native AudioContext constructor.");let i;try{i=new o(a)}catch(d){throw d.code===12&&d.message==="sampleRate is not in range"?t():d}if(i===null)throw n();if(!Uo(a.latencyHint))throw new TypeError(`The provided value '${a.latencyHint}' is not a valid enum value of type AudioContextLatencyCategory.`);if(a.sampleRate!==void 0&&i.sampleRate!==a.sampleRate)throw t();super(i,2);const{latencyHint:c}=a,{sampleRate:u}=i;if(this._baseLatency=typeof i.baseLatency=="number"?i.baseLatency:c==="balanced"?512/u:c==="interactive"||c===void 0?256/u:c==="playback"?1024/u:Math.max(2,Math.min(128,Math.round(c*u/128)))*128/u,this._nativeAudioContext=i,o.name==="webkitAudioContext"?(this._nativeGainNode=i.createGain(),this._nativeOscillatorNode=i.createOscillator(),this._nativeGainNode.gain.value=1e-37,this._nativeOscillatorNode.connect(this._nativeGainNode).connect(i.destination),this._nativeOscillatorNode.start()):(this._nativeGainNode=null,this._nativeOscillatorNode=null),this._state=null,i.state==="running"){this._state="suspended";const d=()=>{this._state==="suspended"&&(this._state=null),i.removeEventListener("statechange",d)};i.addEventListener("statechange",d)}}get baseLatency(){return this._baseLatency}get state(){return this._state!==null?this._state:this._nativeAudioContext.state}close(){return this.state==="closed"?this._nativeAudioContext.close().then(()=>{throw e()}):(this._state==="suspended"&&(this._state=null),this._nativeAudioContext.close().then(()=>{this._nativeGainNode!==null&&this._nativeOscillatorNode!==null&&(this._nativeOscillatorNode.stop(),this._nativeGainNode.disconnect(),this._nativeOscillatorNode.disconnect()),Po(this)}))}resume(){return this._state==="suspended"?new Promise((a,i)=>{const c=()=>{this._nativeAudioContext.removeEventListener("statechange",c),this._nativeAudioContext.state==="running"?a():this.resume().then(a,i)};this._nativeAudioContext.addEventListener("statechange",c)}):this._nativeAudioContext.resume().catch(a=>{throw a===void 0||a.code===15?e():a})}suspend(){return this._nativeAudioContext.suspend().catch(a=>{throw a===void 0?e():a})}},xs=(e,t,n,r,o,s)=>class extends n{constructor(i,c){super(i),this._nativeContext=i,wn.set(this,i),r(i)&&o.set(i,new Set),this._destination=new e(this,c),this._listener=t(this,i),this._onstatechange=null}get currentTime(){return this._nativeContext.currentTime}get destination(){return this._destination}get listener(){return this._listener}get onstatechange(){return this._onstatechange}set onstatechange(i){const c=typeof i=="function"?s(this,i):null;this._nativeContext.onstatechange=c;const u=this._nativeContext.onstatechange;this._onstatechange=u!==null&&u===c?i:u}get sampleRate(){return this._nativeContext.sampleRate}get state(){return this._nativeContext.state}},ft=e=>{const t=new Uint32Array([1179011410,40,1163280727,544501094,16,131073,44100,176400,1048580,1635017060,4,0]);try{const n=e.decodeAudioData(t.buffer,()=>{});return n===void 0?!1:(n.catch(()=>{}),!0)}catch{}return!1},Bs=(e,t)=>(n,r,o)=>{const s=new Set;return n.connect=(a=>(i,c=0,u=0)=>{const d=s.size===0;if(t(i))return a.call(n,i,c,u),e(s,[i,c,u],l=>l[0]===i&&l[1]===c&&l[2]===u,!0),d&&r(),i;a.call(n,i,c),e(s,[i,c],l=>l[0]===i&&l[1]===c,!0),d&&r()})(n.connect),n.disconnect=(a=>(i,c,u)=>{const d=s.size>0;if(i===void 0)a.apply(n),s.clear();else if(typeof i=="number"){a.call(n,i);for(const w of s)w[1]===i&&s.delete(w)}else{t(i)?a.call(n,i,c,u):a.call(n,i,c);for(const w of s)w[0]===i&&(c===void 0||w[1]===c)&&(u===void 0||w[2]===u)&&s.delete(w)}const l=s.size===0;d&&l&&o()})(n.disconnect),n},ie=(e,t,n)=>{const r=t[n];r!==void 0&&r!==e[n]&&(e[n]=r)},Te=(e,t)=>{ie(e,t,"channelCount"),ie(e,t,"channelCountMode"),ie(e,t,"channelInterpretation")},Ws=e=>e===null?null:e.hasOwnProperty("AudioBuffer")?e.AudioBuffer:null,Et=(e,t,n)=>{const r=t[n];r!==void 0&&r!==e[n].value&&(e[n].value=r)},Ds=e=>{e.start=(t=>{let n=!1;return(r=0,o=0,s)=>{if(n)throw Z();t.call(e,r,o,s),n=!0}})(e.start)},kn=e=>{e.start=(t=>(n=0,r=0,o)=>{if(typeof o=="number"&&o<0||r<0||n<0)throw new RangeError("The parameters can't be negative.");t.call(e,n,r,o)})(e.start)},Sn=e=>{e.stop=(t=>(n=0)=>{if(n<0)throw new RangeError("The parameter can't be negative.");t.call(e,n)})(e.stop)},Vs=(e,t,n,r,o,s,a,i,c,u,d)=>(l,w)=>{const g=l.createBufferSource();return Te(g,w),Et(g,w,"playbackRate"),ie(g,w,"buffer"),ie(g,w,"loop"),ie(g,w,"loopEnd"),ie(g,w,"loopStart"),t(n,()=>n(l))||Ds(g),t(r,()=>r(l))||c(g),t(o,()=>o(l))||u(g,l),t(s,()=>s(l))||kn(g),t(a,()=>a(l))||d(g,l),t(i,()=>i(l))||Sn(g),e(l,g),g},Fs=e=>e===null?null:e.hasOwnProperty("AudioContext")?e.AudioContext:e.hasOwnProperty("webkitAudioContext")?e.webkitAudioContext:null,js=(e,t)=>(n,r,o)=>{const s=n.destination;if(s.channelCount!==r)try{s.channelCount=r}catch{}o&&s.channelCountMode!=="explicit"&&(s.channelCountMode="explicit"),s.maxChannelCount===0&&Object.defineProperty(s,"maxChannelCount",{value:r});const a=e(n,{channelCount:r,channelCountMode:s.channelCountMode,channelInterpretation:s.channelInterpretation,gain:1});return t(a,"channelCount",i=>()=>i.call(a),i=>c=>{i.call(a,c);try{s.channelCount=c}catch(u){if(c>s.maxChannelCount)throw u}}),t(a,"channelCountMode",i=>()=>i.call(a),i=>c=>{i.call(a,c),s.channelCountMode=c}),t(a,"channelInterpretation",i=>()=>i.call(a),i=>c=>{i.call(a,c),s.channelInterpretation=c}),Object.defineProperty(a,"maxChannelCount",{get:()=>s.maxChannelCount}),a.connect(s),a},$s=e=>e===null?null:e.hasOwnProperty("AudioWorkletNode")?e.AudioWorkletNode:null,Gs=e=>{const{port1:t}=new MessageChannel;try{t.postMessage(e)}finally{t.close()}},zs=(e,t,n,r,o)=>(s,a,i,c,u,d)=>{if(i!==null)try{const l=new i(s,c,d),w=new Map;let g=null;if(Object.defineProperties(l,{channelCount:{get:()=>d.channelCount,set:()=>{throw e()}},channelCountMode:{get:()=>"explicit",set:()=>{throw e()}},onprocessorerror:{get:()=>g,set:f=>{typeof g=="function"&&l.removeEventListener("processorerror",g),g=typeof f=="function"?f:null,typeof g=="function"&&l.addEventListener("processorerror",g)}}}),l.addEventListener=(f=>(...h)=>{if(h[0]==="processorerror"){const m=typeof h[1]=="function"?h[1]:typeof h[1]=="object"&&h[1]!==null&&typeof h[1].handleEvent=="function"?h[1].handleEvent:null;if(m!==null){const p=w.get(h[1]);p!==void 0?h[1]=p:(h[1]=_=>{_.type==="error"?(Object.defineProperties(_,{type:{value:"processorerror"}}),m(_)):m(new ErrorEvent(h[0],{..._}))},w.set(m,h[1]))}}return f.call(l,"error",h[1],h[2]),f.call(l,...h)})(l.addEventListener),l.removeEventListener=(f=>(...h)=>{if(h[0]==="processorerror"){const m=w.get(h[1]);m!==void 0&&(w.delete(h[1]),h[1]=m)}return f.call(l,"error",h[1],h[2]),f.call(l,h[0],h[1],h[2])})(l.removeEventListener),d.numberOfOutputs!==0){const f=n(s,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return l.connect(f).connect(s.destination),o(l,()=>f.disconnect(),()=>f.connect(s.destination))}return l}catch(l){throw l.code===11?r():l}if(u===void 0)throw r();return Gs(d),t(s,a,u,d)},qs=(e,t)=>e===null?512:Math.max(512,Math.min(16384,Math.pow(2,Math.round(Math.log2(e*t))))),Hs=e=>new Promise((t,n)=>{const{port1:r,port2:o}=new MessageChannel;r.onmessage=({data:s})=>{r.close(),o.close(),t(s)},r.onmessageerror=({data:s})=>{r.close(),o.close(),n(s)},o.postMessage(e)}),Xs=async(e,t)=>{const n=await Hs(t);return new e(n)},Ys=(e,t,n,r)=>{let o=at.get(e);o===void 0&&(o=new WeakMap,at.set(e,o));const s=Xs(n,r);return o.set(t,s),s},Zs=(e,t,n,r,o,s,a,i,c,u,d,l,w)=>(g,f,h,m)=>{if(m.numberOfInputs===0&&m.numberOfOutputs===0)throw c();const p=Array.isArray(m.outputChannelCount)?m.outputChannelCount:Array.from(m.outputChannelCount);if(p.some(C=>C<1))throw c();if(p.length!==m.numberOfOutputs)throw t();if(m.channelCountMode!=="explicit")throw c();const _=m.channelCount*m.numberOfInputs,E=p.reduce((C,R)=>C+R,0),T=h.parameterDescriptors===void 0?0:h.parameterDescriptors.length;if(_+T>6||E>6)throw c();const A=new MessageChannel,b=[],y=[];for(let C=0;C<m.numberOfInputs;C+=1)b.push(a(g,{channelCount:m.channelCount,channelCountMode:m.channelCountMode,channelInterpretation:m.channelInterpretation,gain:1})),y.push(o(g,{channelCount:m.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:m.channelCount}));const v=[];if(h.parameterDescriptors!==void 0)for(const{defaultValue:C,maxValue:R,minValue:z,name:F}of h.parameterDescriptors){const D=s(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:m.parameterData[F]!==void 0?m.parameterData[F]:C===void 0?0:C});Object.defineProperties(D.offset,{defaultValue:{get:()=>C===void 0?0:C},maxValue:{get:()=>R===void 0?wt:R},minValue:{get:()=>z===void 0?je:z}}),v.push(D)}const N=r(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,_+T)}),I=qs(f,g.sampleRate),M=i(g,I,_+T,Math.max(1,E)),x=o(g,{channelCount:Math.max(1,E),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,E)}),S=[];for(let C=0;C<m.numberOfOutputs;C+=1)S.push(r(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:p[C]}));for(let C=0;C<m.numberOfInputs;C+=1){b[C].connect(y[C]);for(let R=0;R<m.channelCount;R+=1)y[C].connect(N,R,C*m.channelCount+R)}const W=new Rn(h.parameterDescriptors===void 0?[]:h.parameterDescriptors.map(({name:C},R)=>{const z=v[R];return z.connect(N,0,_+R),z.start(0),[C,z.offset]}));N.connect(M);let U=m.channelInterpretation,k=null;const L=m.numberOfOutputs===0?[M]:S,B={get bufferSize(){return I},get channelCount(){return m.channelCount},set channelCount(C){throw n()},get channelCountMode(){return m.channelCountMode},set channelCountMode(C){throw n()},get channelInterpretation(){return U},set channelInterpretation(C){for(const R of b)R.channelInterpretation=C;U=C},get context(){return M.context},get inputs(){return b},get numberOfInputs(){return m.numberOfInputs},get numberOfOutputs(){return m.numberOfOutputs},get onprocessorerror(){return k},set onprocessorerror(C){typeof k=="function"&&B.removeEventListener("processorerror",k),k=typeof C=="function"?C:null,typeof k=="function"&&B.addEventListener("processorerror",k)},get parameters(){return W},get port(){return A.port2},addEventListener(...C){return M.addEventListener(C[0],C[1],C[2])},connect:e.bind(null,L),disconnect:u.bind(null,L),dispatchEvent(...C){return M.dispatchEvent(C[0])},removeEventListener(...C){return M.removeEventListener(C[0],C[1],C[2])}},O=new Map;A.port1.addEventListener=(C=>(...R)=>{if(R[0]==="message"){const z=typeof R[1]=="function"?R[1]:typeof R[1]=="object"&&R[1]!==null&&typeof R[1].handleEvent=="function"?R[1].handleEvent:null;if(z!==null){const F=O.get(R[1]);F!==void 0?R[1]=F:(R[1]=D=>{d(g.currentTime,g.sampleRate,()=>z(D))},O.set(z,R[1]))}}return C.call(A.port1,R[0],R[1],R[2])})(A.port1.addEventListener),A.port1.removeEventListener=(C=>(...R)=>{if(R[0]==="message"){const z=O.get(R[1]);z!==void 0&&(O.delete(R[1]),R[1]=z)}return C.call(A.port1,R[0],R[1],R[2])})(A.port1.removeEventListener);let P=null;Object.defineProperty(A.port1,"onmessage",{get:()=>P,set:C=>{typeof P=="function"&&A.port1.removeEventListener("message",P),P=typeof C=="function"?C:null,typeof P=="function"&&(A.port1.addEventListener("message",P),A.port1.start())}}),h.prototype.port=A.port1;let V=null;Ys(g,B,h,m).then(C=>V=C);const he=We(m.numberOfInputs,m.channelCount),pe=We(m.numberOfOutputs,p),me=h.parameterDescriptors===void 0?[]:h.parameterDescriptors.reduce((C,{name:R})=>({...C,[R]:new Float32Array(128)}),{});let j=!0;const H=()=>{m.numberOfOutputs>0&&M.disconnect(x);for(let C=0,R=0;C<m.numberOfOutputs;C+=1){const z=S[C];for(let F=0;F<p[C];F+=1)x.disconnect(z,R+F,F);R+=p[C]}},Me=new Map;M.onaudioprocess=({inputBuffer:C,outputBuffer:R})=>{if(V!==null){const z=l(B);for(let F=0;F<I;F+=128){for(let D=0;D<m.numberOfInputs;D+=1)for(let $=0;$<m.channelCount;$+=1)Be(C,he[D],$,$,F);h.parameterDescriptors!==void 0&&h.parameterDescriptors.forEach(({name:D},$)=>{Be(C,me,D,_+$,F)});for(let D=0;D<m.numberOfInputs;D+=1)for(let $=0;$<p[D];$+=1)pe[D][$].byteLength===0&&(pe[D][$]=new Float32Array(128));try{const D=he.map((X,ne)=>{if(z[ne].size>0)return Me.set(ne,I/128),X;const Ke=Me.get(ne);return Ke===void 0?[]:(X.every(lr=>lr.every(dr=>dr===0))&&(Ke===1?Me.delete(ne):Me.set(ne,Ke-1)),X)});j=d(g.currentTime+F/g.sampleRate,g.sampleRate,()=>V.process(D,pe,me));for(let X=0,ne=0;X<m.numberOfOutputs;X+=1){for(let _e=0;_e<p[X];_e+=1)In(R,pe[X],_e,ne+_e,F);ne+=p[X]}}catch(D){j=!1,B.dispatchEvent(new ErrorEvent("processorerror",{colno:D.colno,filename:D.filename,lineno:D.lineno,message:D.message}))}if(!j){for(let D=0;D<m.numberOfInputs;D+=1){b[D].disconnect(y[D]);for(let $=0;$<m.channelCount;$+=1)y[F].disconnect(N,$,D*m.channelCount+$)}if(h.parameterDescriptors!==void 0){const D=h.parameterDescriptors.length;for(let $=0;$<D;$+=1){const X=v[$];X.disconnect(N,0,_+$),X.stop()}}N.disconnect(M),M.onaudioprocess=null,Ye?H():kt();break}}}};let Ye=!1;const Ze=a(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0}),It=()=>M.connect(Ze).connect(g.destination),kt=()=>{M.disconnect(Ze),Ze.disconnect()},cr=()=>{if(j){kt(),m.numberOfOutputs>0&&M.connect(x);for(let C=0,R=0;C<m.numberOfOutputs;C+=1){const z=S[C];for(let F=0;F<p[C];F+=1)x.connect(z,R+F,F);R+=p[C]}}Ye=!0},ur=()=>{j&&(It(),H()),Ye=!1};return It(),w(B,cr,ur)},Ks=(e,t)=>(n,r)=>{const o=n.createChannelMerger(r.numberOfInputs);return e!==null&&e.name==="webkitAudioContext"&&t(n,o),Te(o,r),o},Qs=e=>{const t=e.numberOfOutputs;Object.defineProperty(e,"channelCount",{get:()=>t,set:n=>{if(n!==t)throw Z()}}),Object.defineProperty(e,"channelCountMode",{get:()=>"explicit",set:n=>{if(n!=="explicit")throw Z()}}),Object.defineProperty(e,"channelInterpretation",{get:()=>"discrete",set:n=>{if(n!=="discrete")throw Z()}})},Ln=(e,t)=>{const n=e.createChannelSplitter(t.numberOfOutputs);return Te(n,t),Qs(n),n},Js=(e,t,n,r,o)=>(s,a)=>{if(s.createConstantSource===void 0)return n(s,a);const i=s.createConstantSource();return Te(i,a),Et(i,a,"offset"),t(r,()=>r(s))||kn(i),t(o,()=>o(s))||Sn(i),e(s,i),i},Pn=(e,t)=>(e.connect=t.connect.bind(t),e.disconnect=t.disconnect.bind(t),e),ea=(e,t,n,r)=>(o,{offset:s,...a})=>{const i=o.createBuffer(1,2,44100),c=t(o,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),u=n(o,{...a,gain:s}),d=i.getChannelData(0);d[0]=1,d[1]=1,c.buffer=i,c.loop=!0;const l={get bufferSize(){},get channelCount(){return u.channelCount},set channelCount(f){u.channelCount=f},get channelCountMode(){return u.channelCountMode},set channelCountMode(f){u.channelCountMode=f},get channelInterpretation(){return u.channelInterpretation},set channelInterpretation(f){u.channelInterpretation=f},get context(){return u.context},get inputs(){return[]},get numberOfInputs(){return c.numberOfInputs},get numberOfOutputs(){return u.numberOfOutputs},get offset(){return u.gain},get onended(){return c.onended},set onended(f){c.onended=f},addEventListener(...f){return c.addEventListener(f[0],f[1],f[2])},dispatchEvent(...f){return c.dispatchEvent(f[0])},removeEventListener(...f){return c.removeEventListener(f[0],f[1],f[2])},start(f=0){c.start.call(c,f)},stop(f=0){c.stop.call(c,f)}},w=()=>c.connect(u),g=()=>c.disconnect(u);return e(o,c),r(Pn(l,u),w,g)},se=(e,t)=>{const n=e.createGain();return Te(n,t),Et(n,t,"gain"),n},ta=(e,{mediaStream:t})=>{const n=t.getAudioTracks();n.sort((s,a)=>s.id<a.id?-1:s.id>a.id?1:0);const r=n.slice(0,1),o=e.createMediaStreamSource(new MediaStream(r));return Object.defineProperty(o,"mediaStream",{value:t}),o},na=e=>e===null?null:e.hasOwnProperty("OfflineAudioContext")?e.OfflineAudioContext:e.hasOwnProperty("webkitOfflineAudioContext")?e.webkitOfflineAudioContext:null,ra=e=>(t,{disableNormalization:n,imag:r,real:o})=>{const s=r instanceof Float32Array?r:new Float32Array(r),a=o instanceof Float32Array?o:new Float32Array(o),i=t.createPeriodicWave(a,s,{disableNormalization:n});if(Array.from(r).length<2)throw e();return i},yt=(e,t,n,r)=>e.createScriptProcessor(t,n,r),de=()=>new DOMException("","NotSupportedError"),oa={disableNormalization:!1},sa=(e,t,n,r)=>class Un{constructor(s,a){const i=t(s),c=r({...oa,...a}),u=e(i,c);return n.add(u),u}static[Symbol.hasInstance](s){return s!==null&&typeof s=="object"&&Object.getPrototypeOf(s)===Un.prototype||n.has(s)}},aa=(e,t)=>(n,r,o)=>(e(r).replay(o),t(r,n,o)),ia=(e,t,n)=>async(r,o,s)=>{const a=e(r);await Promise.all(a.activeInputs.map((i,c)=>Array.from(i).map(async([u,d])=>{const w=await t(u).render(u,o),g=r.context.destination;!n(u)&&(r!==g||!n(r))&&w.connect(s,d,c)})).reduce((i,c)=>[...i,...c],[]))},ca=(e,t,n)=>async(r,o,s)=>{const a=t(r);await Promise.all(Array.from(a.activeInputs).map(async([i,c])=>{const d=await e(i).render(i,o);n(i)||d.connect(s,c)}))},ua=(e,t,n,r)=>o=>e(ft,()=>ft(o))?Promise.resolve(e(r,r)).then(s=>{if(!s){const a=n(o,512,0,1);o.oncomplete=()=>{a.onaudioprocess=null,a.disconnect()},a.onaudioprocess=()=>o.currentTime,a.connect(o.destination)}return o.startRendering()}):new Promise(s=>{const a=t(o,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});o.oncomplete=i=>{a.disconnect(),s(i.renderedBuffer)},a.connect(o.destination),o.startRendering()}),la=e=>(t,n)=>{e.set(t,n)},da=e=>()=>{if(e===null)return!1;try{new e({length:1,sampleRate:44100})}catch{return!1}return!0},fa=(e,t)=>async()=>{if(e===null)return!0;if(t===null)return!1;const n=new Blob(['class A extends AudioWorkletProcessor{process(i){this.port.postMessage(i,[i[0][0].buffer])}}registerProcessor("a",A)'],{type:"application/javascript; charset=utf-8"}),r=new t(1,128,44100),o=URL.createObjectURL(n);let s=!1,a=!1;try{await r.audioWorklet.addModule(o);const i=new e(r,"a",{numberOfOutputs:0}),c=r.createOscillator();i.port.onmessage=()=>s=!0,i.onprocessorerror=()=>a=!0,c.connect(i),c.start(0),await r.startRendering(),await new Promise(u=>setTimeout(u))}catch{}finally{URL.revokeObjectURL(o)}return s&&!a},ha=(e,t)=>()=>{if(t===null)return Promise.resolve(!1);const n=new t(1,1,44100),r=e(n,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return new Promise(o=>{n.oncomplete=()=>{r.disconnect(),o(n.currentTime!==0)},n.startRendering()})},pa=()=>new DOMException("","UnknownError"),ma=()=>typeof window>"u"?null:window,ga=(e,t)=>n=>{n.copyFromChannel=(r,o,s=0)=>{const a=e(s),i=e(o);if(i>=n.numberOfChannels)throw t();const c=n.length,u=n.getChannelData(i),d=r.length;for(let l=a<0?-a:0;l+a<c&&l<d;l+=1)r[l]=u[l+a]},n.copyToChannel=(r,o,s=0)=>{const a=e(s),i=e(o);if(i>=n.numberOfChannels)throw t();const c=n.length,u=n.getChannelData(i),d=r.length;for(let l=a<0?-a:0;l+a<c&&l<d;l+=1)u[l+a]=r[l]}},wa=e=>t=>{t.copyFromChannel=(n=>(r,o,s=0)=>{const a=e(s),i=e(o);if(a<t.length)return n.call(t,r,i,a)})(t.copyFromChannel),t.copyToChannel=(n=>(r,o,s=0)=>{const a=e(s),i=e(o);if(a<t.length)return n.call(t,r,i,a)})(t.copyToChannel)},va=e=>(t,n)=>{const r=n.createBuffer(1,1,44100);t.buffer===null&&(t.buffer=r),e(t,"buffer",o=>()=>{const s=o.call(t);return s===r?null:s},o=>s=>o.call(t,s===null?r:s))},_a=(e,t)=>(n,r)=>{r.channelCount=1,r.channelCountMode="explicit",Object.defineProperty(r,"channelCount",{get:()=>1,set:()=>{throw e()}}),Object.defineProperty(r,"channelCountMode",{get:()=>"explicit",set:()=>{throw e()}});const o=n.createBufferSource();t(r,()=>{const i=r.numberOfInputs;for(let c=0;c<i;c+=1)o.connect(r,0,c)},()=>o.disconnect(r))},Ea=(e,t,n)=>e.copyFromChannel===void 0?e.getChannelData(n)[0]:(e.copyFromChannel(t,n),t[0]),At=(e,t,n,r)=>{let o=e;for(;!o.hasOwnProperty(t);)o=Object.getPrototypeOf(o);const{get:s,set:a}=Object.getOwnPropertyDescriptor(o,t);Object.defineProperty(e,t,{get:n(s),set:r(a)})},ya=e=>({...e,outputChannelCount:e.outputChannelCount!==void 0?e.outputChannelCount:e.numberOfInputs===1&&e.numberOfOutputs===1?[e.channelCount]:Array.from({length:e.numberOfOutputs},()=>1)}),Aa=e=>{const{imag:t,real:n}=e;return t===void 0?n===void 0?{...e,imag:[0,0],real:[0,0]}:{...e,imag:Array.from(n,()=>0),real:n}:n===void 0?{...e,imag:t,real:Array.from(t,()=>0)}:{...e,imag:t,real:n}},xn=(e,t,n)=>{try{e.setValueAtTime(t,n)}catch(r){if(r.code!==9)throw r;xn(e,t,n+1e-7)}},ba=e=>{const t=e.createBufferSource();t.start();try{t.start()}catch{return!0}return!1},Ca=e=>{const t=e.createBufferSource(),n=e.createBuffer(1,1,44100);t.buffer=n;try{t.start(0,1)}catch{return!1}return!0},Ta=e=>{const t=e.createBufferSource();t.start();try{t.stop()}catch{return!1}return!0},Bn=e=>{const t=e.createOscillator();try{t.start(-1)}catch(n){return n instanceof RangeError}return!1},Ma=e=>{const t=e.createBuffer(1,1,44100),n=e.createBufferSource();n.buffer=t,n.start(),n.stop();try{return n.stop(),!0}catch{return!1}},Wn=e=>{const t=e.createOscillator();try{t.stop(-1)}catch(n){return n instanceof RangeError}return!1},Na=e=>{const{port1:t,port2:n}=new MessageChannel;try{t.postMessage(e)}finally{t.close(),n.close()}},Oa=e=>{e.start=(t=>(n=0,r=0,o)=>{const s=e.buffer,a=s===null?r:Math.min(s.duration,r);s!==null&&a>s.duration-.5/e.context.sampleRate?t.call(e,n,0,0):t.call(e,n,a,o)})(e.start)},Ra=(e,t)=>{const n=t.createGain();e.connect(n);const r=(o=>()=>{o.call(e,n),e.removeEventListener("ended",r)})(e.disconnect);e.addEventListener("ended",r),Pn(e,n),e.stop=(o=>{let s=!1;return(a=0)=>{if(s)try{o.call(e,a)}catch{n.gain.setValueAtTime(0,a)}else o.call(e,a),s=!0}})(e.stop)},$e=(e,t)=>n=>{const r={value:e};return Object.defineProperties(n,{currentTarget:r,target:r}),typeof t=="function"?t.call(e,n):t.handleEvent.call(e,n)},Ia=po(le),ka=Eo(le),Sa=cs(Fe),La=new WeakMap,Pa=Es(La),fe=ts(new Map,new WeakMap),Q=ma(),Dn=_s(q),bt=ia(q,Dn,ue),te=bs(wn),ve=na(Q),J=Ss(ve),Vn=new WeakMap,Fn=ms($e),Ge=Fs(Q),jn=Os(Ge),$n=Rs(Q),Ua=Is(Q),ye=$s(Q),ze=Ho(mo(hn),_o(Ia,ka,lt,Sa,dt,q,Pa,Ae,Y,le,ce,ue,Ie),fe,Ns(ot,dt,q,Y,Ee,ce),ae,Ms,de,is(lt,ot,q,Y,Ee,te,ce,J),ds(Vn,q,K),Fn,te,jn,$n,Ua,J,ye),Gn=new WeakSet,Jt=Ws(Q),zn=os(new Uint32Array(1)),qn=ga(zn,ae),Hn=wa(zn),xa=Co(Gn,fe,de,Jt,ve,da(Jt),qn,Hn),Ct=yo(se),Xn=ca(Dn,be,ue),Yn=ns(Xn),qe=Vs(Ct,fe,ba,Ca,Ta,Bn,Ma,Wn,Oa,va(At),Ra),Zn=aa(ys(be),Xn),Ba=No(Yn,qe,Y,Zn,bt),Tt=Xo(go(mn),Vn,gn,Yo,io,co,uo,lo,fo,tt,dn,Ge,xn),Wa=Mo(ze,Ba,Tt,Z,qe,te,J,$e),Da=xo(ze,Bo,ae,Z,js(se,At),te,J,bt),He=Bs(le,$n),Va=_a(Z,He),Mt=Ks(Ge,Va),Fa=ea(Ct,qe,se,He),Nt=Js(Ct,fe,Fa,Bn,Wn),ja=ua(fe,se,yt,ha(se,ve)),$a=Wo(Tt,Mt,Nt,yt,de,Ea,J,At),Kn=new WeakMap,Ga=xs(Da,$a,Fn,J,Kn,$e),za=ra(ae);sa(za,te,new WeakSet,Aa);const Qn=Ls(Q),Ot=gs(Q),Jn=new WeakMap,qa=Cs(Jn,ve),en=Qn?vo(fe,de,ps(Q),Ot,ws(ho),te,qa,J,ye,new WeakMap,new WeakMap,fa(ye,ve),Q):void 0,Ha=ks(jn,J);as(Gn,fe,ss,hs,new WeakSet,te,Ha,ct,ft,qn,Hn);const Xa=Ps(ze,ta,te,J),er=Ts(Kn),Ya=Ao(er),tr=rs(ae),Za=us(er),nr=fs(ae),rr=new WeakMap,Ka=vs(rr,K),Qa=Zs(tr,ae,Z,Mt,Ln,Nt,se,yt,de,nr,Ot,Ka,He),Ja=zs(Z,Qa,se,de,He),ei=es(Yn,tr,qe,Mt,Ln,Nt,se,Za,nr,Ot,Y,ye,ve,Zn,bt,ja),ti=As(Jn),ni=la(rr),tn=Qn?Ko(Ya,ze,Tt,ei,Ja,q,ti,te,J,ye,ya,ni,Na,$e):void 0,ri=Us(Z,de,pa,Ga,Ge),or="Missing AudioWorklet support. Maybe this is not running in a secure context.",oi=async(e,t,n,r,o)=>{const{encoderId:s,port:a}=await an(o,t.sampleRate);if(tn===void 0)throw new Error(or);const i=new Wa(t,{buffer:e}),c=new Xa(t,{mediaStream:r}),u=ro(tn,t,{channelCount:n});return{audioBufferSourceNode:i,encoderId:s,mediaStreamAudioSourceNode:c,port:a,recorderAudioWorkletNode:u}},si=(e,t,n,r)=>(o,s,a)=>{var i;const c=(i=s.getAudioTracks()[0])===null||i===void 0?void 0:i.getSettings().sampleRate,u=new ri({latencyHint:"playback",sampleRate:c}),d=Math.max(1024,Math.ceil(u.baseLatency*u.sampleRate)),l=new xa({length:d,sampleRate:u.sampleRate}),w=[],g=no(v=>{if(en===void 0)throw new Error(or);return en(u,v)});let f=null,h=null,m=null,p=null,_=!0;const E=v=>{o.dispatchEvent(e("dataavailable",{data:new Blob(v,{type:a})}))},T=async(v,N)=>{const I=await ke(v,N);m===null?w.push(...I):(E(I),p=T(v,N))},A=()=>(_=!0,u.resume()),b=()=>{m!==null&&(f!==null&&(s.removeEventListener("addtrack",f),s.removeEventListener("removetrack",f)),h!==null&&clearTimeout(h),m.then(async({encoderId:v,mediaStreamAudioSourceNode:N,recorderAudioWorkletNode:I})=>{p!==null&&(p.catch(()=>{}),p=null),await I.stop(),N.disconnect(I);const M=await ke(v,null);m===null&&await y(),E([...w,...M]),w.length=0,o.dispatchEvent(new Event("stop"))}),m=null)},y=()=>(_=!1,u.suspend());return y(),{get mimeType(){return a},get state(){return m===null?"inactive":_?"recording":"paused"},pause(){if(m===null)throw n();_&&(y(),o.dispatchEvent(new Event("pause")))},resume(){if(m===null)throw n();_||(A(),o.dispatchEvent(new Event("resume")))},start(v){var N;if(m!==null)throw n();if(s.getVideoTracks().length>0)throw r();o.dispatchEvent(new Event("start"));const I=s.getAudioTracks(),M=I.length===0?2:(N=I[0].getSettings().channelCount)!==null&&N!==void 0?N:2;m=Promise.all([A(),g.then(()=>oi(l,u,M,s,a))]).then(async([,{audioBufferSourceNode:S,encoderId:W,mediaStreamAudioSourceNode:U,port:k,recorderAudioWorkletNode:L}])=>(U.connect(L),await new Promise(B=>{S.onended=B,S.connect(L),S.start(u.currentTime+d/u.sampleRate)}),S.disconnect(L),await L.record(k),v!==void 0&&(p=T(W,v)),{encoderId:W,mediaStreamAudioSourceNode:U,recorderAudioWorkletNode:L}));const x=s.getTracks();f=()=>{b(),o.dispatchEvent(new ErrorEvent("error",{error:t()}))},s.addEventListener("addtrack",f),s.addEventListener("removetrack",f),h=setInterval(()=>{const S=s.getTracks();(S.length!==x.length||S.some((W,U)=>W!==x[U]))&&f!==null&&f()},1e3)},stop:b}};class et{constructor(t,n=0,r){if(n<0||r!==void 0&&r<0)throw new RangeError;const o=t.reduce((d,l)=>d+l.byteLength,0);if(n>o||r!==void 0&&n+r>o)throw new RangeError;const s=[],a=r===void 0?o-n:r,i=[];let c=0,u=n;for(const d of t)if(i.length===0)if(d.byteLength>u){c=d.byteLength-u;const l=c>a?a:c;s.push(new DataView(d,u,l)),i.push(d)}else u-=d.byteLength;else if(c<a){c+=d.byteLength;const l=c>a?d.byteLength-c+a:d.byteLength;s.push(new DataView(d,0,l)),i.push(d)}this._buffers=i,this._byteLength=a,this._byteOffset=u,this._dataViews=s,this._internalBuffer=new DataView(new ArrayBuffer(8))}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get byteOffset(){return this._byteOffset}getFloat32(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.getFloat32(0,n)}getFloat64(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.setUint8(4,this.getUint8(t+4)),this._internalBuffer.setUint8(5,this.getUint8(t+5)),this._internalBuffer.setUint8(6,this.getUint8(t+6)),this._internalBuffer.setUint8(7,this.getUint8(t+7)),this._internalBuffer.getFloat64(0,n)}getInt16(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.getInt16(0,n)}getInt32(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.getInt32(0,n)}getInt8(t){const[n,r]=this._findDataViewWithOffset(t);return n.getInt8(t-r)}getUint16(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.getUint16(0,n)}getUint32(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.getUint32(0,n)}getUint8(t){const[n,r]=this._findDataViewWithOffset(t);return n.getUint8(t-r)}setFloat32(t,n,r){this._internalBuffer.setFloat32(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3))}setFloat64(t,n,r){this._internalBuffer.setFloat64(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3)),this.setUint8(t+4,this._internalBuffer.getUint8(4)),this.setUint8(t+5,this._internalBuffer.getUint8(5)),this.setUint8(t+6,this._internalBuffer.getUint8(6)),this.setUint8(t+7,this._internalBuffer.getUint8(7))}setInt16(t,n,r){this._internalBuffer.setInt16(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1))}setInt32(t,n,r){this._internalBuffer.setInt32(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3))}setInt8(t,n){const[r,o]=this._findDataViewWithOffset(t);r.setInt8(t-o,n)}setUint16(t,n,r){this._internalBuffer.setUint16(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1))}setUint32(t,n,r){this._internalBuffer.setUint32(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3))}setUint8(t,n){const[r,o]=this._findDataViewWithOffset(t);r.setUint8(t-o,n)}_findDataViewWithOffset(t){let n=0;for(const r of this._dataViews){const o=n+r.byteLength;if(t>=n&&t<o)return[r,n];n=o}throw new RangeError}}const ai=(e,t,n)=>(r,o,s,a)=>{const i=[],c=new o(s,{mimeType:"audio/webm;codecs=pcm"});let u=null,d=()=>{};const l=f=>{r.dispatchEvent(e("dataavailable",{data:new Blob(f,{type:a})}))},w=async(f,h)=>{const m=await ke(f,h);c.state==="inactive"?i.push(...m):(l(m),u=w(f,h))},g=()=>{c.state!=="inactive"&&(u!==null&&(u.catch(()=>{}),u=null),d(),d=()=>{},c.stop())};return c.addEventListener("error",f=>{g(),r.dispatchEvent(new ErrorEvent("error",{error:f.error}))}),c.addEventListener("pause",()=>r.dispatchEvent(new Event("pause"))),c.addEventListener("resume",()=>r.dispatchEvent(new Event("resume"))),c.addEventListener("start",()=>r.dispatchEvent(new Event("start"))),{get mimeType(){return a},get state(){return c.state},pause(){return c.pause()},resume(){return c.resume()},start(f){const[h]=s.getAudioTracks();if(h!==void 0&&c.state==="inactive"){const{channelCount:m,sampleRate:p}=h.getSettings();if(m===void 0)throw new Error("The channelCount is not defined.");if(p===void 0)throw new Error("The sampleRate is not defined.");let _=!1,E=!1,T=0,A=an(a,p);d=()=>{E=!0};const b=un(c,"dataavailable")(({data:y})=>{T+=1,A=A.then(async({dataView:v=null,elementType:N=null,encoderId:I,port:M})=>{const x=await y.arrayBuffer();T-=1;const S=v===null?new et([x]):new et([...v.buffers,x],v.byteOffset);if(!_&&c.state==="recording"&&!E){const B=n(S,0);if(B===null)return{dataView:S,elementType:N,encoderId:I,port:M};const{value:O}=B;if(O!==172351395)return{dataView:v,elementType:N,encoderId:I,port:M};_=!0}const{currentElementType:W,offset:U,contents:k}=t(S,N,m),L=U<S.byteLength?new et(S.buffers,S.byteOffset+U):null;return k.forEach(B=>M.postMessage(B,B.map(({buffer:O})=>O))),T===0&&(c.state==="inactive"||E)&&(ke(I,null).then(B=>{l([...i,...B]),i.length=0,r.dispatchEvent(new Event("stop"))}),M.postMessage([]),M.close(),b()),{dataView:L,elementType:W,encoderId:I,port:M}})});f!==void 0&&A.then(({encoderId:y})=>u=w(y,f))}c.start(100)},stop:g}},ii=()=>typeof window>"u"?null:window,sr=(e,t)=>{if(t>=e.byteLength)return null;const n=e.getUint8(t);if(n>127)return 1;if(n>63)return 2;if(n>31)return 3;if(n>15)return 4;if(n>7)return 5;if(n>3)return 6;if(n>1)return 7;if(n>0)return 8;const r=sr(e,t+1);return r===null?null:r+8},ci=(e,t)=>n=>{const r={value:e};return Object.defineProperties(n,{currentTarget:r,target:r}),typeof t=="function"?t.call(e,n):t.handleEvent.call(e,n)},ar=[],Xe=ii(),ui=Pr(Xe),ir=Mr(ui),li=si(ir,Ir,kr,cn),Rt=Dr(sr),di=Br(Rt),fi=Wr(Rt),hi=Nr(di,fi),pi=ai(ir,hi,Rt),mi=Rr(Xe),gi=xr(Xe),Ri=Lr(Ur,cn,li,pi,ar,Or(mi,ci),gi),Ii=()=>Sr(Xe),ki=async e=>{ar.push(await Tr(e))};export{Ri as MediaRecorder,Ii as isSupported,ki as register};
//# sourceMappingURL=module-ab2763d0.js.map
