{"version": 3, "file": "BlockLabel-CJsotHlk.js", "sources": ["../../../../js/atoms/src/BlockLabel.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let label: string | null = null;\n\texport let Icon: any;\n\texport let show_label = true;\n\texport let disable = false;\n\texport let float = true;\n</script>\n\n<label\n\tfor=\"\"\n\tclass:hide={!show_label}\n\tclass:sr-only={!show_label}\n\tclass:float\n\tclass:hide-label={disable}\n\tdata-testid=\"block-label\"\n>\n\t<span>\n\t\t<Icon />\n\t</span>\n\t{label}\n</label>\n\n<style>\n\tlabel {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\tz-index: var(--layer-2);\n\t\tbox-shadow: var(--block-label-shadow);\n\t\tborder: var(--block-label-border-width) solid var(--border-color-primary);\n\t\tborder-top: none;\n\t\tborder-left: none;\n\t\tborder-radius: var(--block-label-radius);\n\t\tbackground: var(--block-label-background-fill);\n\t\tpadding: var(--block-label-padding);\n\t\tpointer-events: none;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--block-label-text-weight);\n\t\tfont-size: var(--block-label-text-size);\n\t\tline-height: var(--line-sm);\n\t}\n\t:global(.gr-group) label {\n\t\tborder-top-left-radius: 0;\n\t}\n\n\tlabel.float {\n\t\tposition: absolute;\n\t\ttop: var(--block-label-margin);\n\t\tleft: var(--block-label-margin);\n\t}\n\tlabel:not(.float) {\n\t\tposition: static;\n\t\tmargin-top: var(--block-label-margin);\n\t\tmargin-left: var(--block-label-margin);\n\t}\n\n\t.hide {\n\t\theight: 0;\n\t}\n\n\tspan {\n\t\topacity: 0.8;\n\t\tmargin-right: var(--size-2);\n\t\twidth: calc(var(--block-label-text-size) - 1px);\n\t\theight: calc(var(--block-label-text-size) - 1px);\n\t}\n\t.hide-label {\n\t\tbox-shadow: none;\n\t\tborder-width: 0;\n\t\tbackground: transparent;\n\t\toverflow: visible;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "label_1", "anchor", "append", "span", "label", "$$props", "Icon", "show_label", "disable", "float"], "mappings": "mZAmBEA,EAAK,CAAA,CAAA,uHATOA,EAAU,CAAA,CAAA,iBACPA,EAAU,CAAA,CAAA,qCAERA,EAAO,CAAA,CAAA,UAL1BC,EAYOC,EAAAC,EAAAC,CAAA,EAJNC,EAEMF,EAAAG,CAAA,0DACLN,EAAK,CAAA,CAAA,yBATOA,EAAU,CAAA,CAAA,4BACPA,EAAU,CAAA,CAAA,4DAERA,EAAO,CAAA,CAAA,0GAZd,GAAA,CAAA,MAAAO,EAAuB,IAAI,EAAAC,GAC3B,KAAAC,CAAS,EAAAD,EACT,CAAA,WAAAE,EAAa,EAAI,EAAAF,EACjB,CAAA,QAAAG,EAAU,EAAK,EAAAH,EACf,CAAA,MAAAI,EAAQ,EAAI,EAAAJ"}