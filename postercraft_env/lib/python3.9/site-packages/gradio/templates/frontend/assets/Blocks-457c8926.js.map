{"version": 3, "mappings": "4kBAUO,SAASA,GAAiBC,EAAMC,EAAMC,EAAIC,EAAQ,CACxD,GAAI,CAACF,EAAM,OAAOG,EAClB,MAAMC,EAAKL,EAAK,wBAChB,GACCC,EAAK,OAASI,EAAG,MACjBJ,EAAK,QAAUI,EAAG,OAClBJ,EAAK,MAAQI,EAAG,KAChBJ,EAAK,SAAWI,EAAG,OAEnB,OAAOD,EACR,KAAM,CACL,MAAAE,EAAQ,EACR,SAAAC,EAAW,IACX,OAAAC,EAASC,GAET,MAAOC,EAAaC,GAAG,EAAKL,EAE5B,IAAAM,EAAMF,EAAaH,EACnB,KAAAM,EAAOT,EACP,IAAAU,CACF,EAAKZ,EAAGF,EAAM,CAAE,KAAAC,EAAM,GAAAI,CAAE,EAAIF,CAAM,EACjC,IAAIY,EAAU,GACVC,EAAU,GACVC,EAEJ,SAASC,GAAQ,CACZJ,IACHG,EAAOE,GAAYnB,EAAM,EAAG,EAAGO,EAAUD,EAAOE,EAAQM,CAAG,GAEvDR,IACJU,EAAU,GAEX,CAED,SAASI,GAAO,CACXN,GAAKO,GAAYrB,EAAMiB,CAAI,EAC/BF,EAAU,EACV,CACD,OAAAO,GAAMX,GAAQ,CAQb,GAPI,CAACK,GAAWL,GAAOD,IACtBM,EAAU,IAEPA,GAAWL,GAAOC,IACrBC,EAAK,EAAG,CAAC,EACTO,KAEG,CAACL,EACJ,MAAO,GAER,GAAIC,EAAS,CACZ,MAAMO,EAAIZ,EAAMD,EACVc,EAAI,EAAI,EAAIhB,EAAOe,EAAIhB,CAAQ,EACrCM,EAAKW,EAAG,EAAIA,CAAC,EAEd,MAAO,EACT,CAAE,EACDN,IACAL,EAAK,EAAG,CAAC,EACFO,CACR,CAMO,SAASK,GAAazB,EAAM,CAClC,MAAM0B,EAAQ,iBAAiB1B,CAAI,EACnC,GAAI0B,EAAM,WAAa,YAAcA,EAAM,WAAa,QAAS,CAChE,KAAM,CAAE,MAAAC,EAAO,OAAAC,CAAQ,EAAGF,EACpBG,EAAI7B,EAAK,wBACfA,EAAK,MAAM,SAAW,WACtBA,EAAK,MAAM,MAAQ2B,EACnB3B,EAAK,MAAM,OAAS4B,EACpBE,GAAc9B,EAAM6B,CAAC,EAEvB,CAOO,SAASC,GAAc9B,EAAM6B,EAAG,CACtC,MAAME,EAAI/B,EAAK,wBACf,GAAI6B,EAAE,OAASE,EAAE,MAAQF,EAAE,MAAQE,EAAE,IAAK,CACzC,MAAML,EAAQ,iBAAiB1B,CAAI,EAC7BgC,EAAYN,EAAM,YAAc,OAAS,GAAKA,EAAM,UAC1D1B,EAAK,MAAM,UAAY,GAAGgC,eAAuBH,EAAE,KAAOE,EAAE,WAAWF,EAAE,IAAME,EAAE,SAEnF,CCtFO,SAASE,GAAKjC,EAAM,CAAE,KAAAC,EAAM,GAAAI,CAAI,EAAEF,EAAS,GAAI,CACrD,MAAMuB,EAAQ,iBAAiB1B,CAAI,EAC7BgC,EAAYN,EAAM,YAAc,OAAS,GAAKA,EAAM,UACpD,CAACQ,EAAIC,CAAE,EAAIT,EAAM,gBAAgB,MAAM,GAAG,EAAE,IAAI,UAAU,EAC1DU,EAAKnC,EAAK,KAAQA,EAAK,MAAQiC,EAAM7B,EAAG,OAASA,EAAG,KAAO6B,GAC3DG,EAAKpC,EAAK,IAAOA,EAAK,OAASkC,EAAM9B,EAAG,QAAUA,EAAG,IAAM8B,GAC3D,CAAE,MAAA7B,EAAQ,EAAG,SAAAC,EAAY+B,GAAM,KAAK,KAAKA,CAAC,EAAI,IAAK,OAAA9B,EAAS+B,EAAQ,EAAKpC,EAC/E,MAAO,CACN,MAAAG,EACA,SAAUkC,GAAYjC,CAAQ,EAAIA,EAAS,KAAK,KAAK6B,EAAKA,EAAKC,EAAKA,CAAE,CAAC,EAAI9B,EAC3E,OAAAC,EACA,IAAK,CAACgB,EAAGiB,IAAM,CACd,MAAMC,EAAID,EAAIL,EACRO,EAAIF,EAAIJ,EACRO,EAAKpB,EAAKiB,EAAIxC,EAAK,MAASI,EAAG,MAC/BwC,EAAKrB,EAAKiB,EAAIxC,EAAK,OAAUI,EAAG,OACtC,MAAO,cAAc2B,eAAuBU,QAAQC,cAAcC,MAAOC,KACzE,CACH,CACA,keChCAC,EAiBKC,EAAAC,EAAAC,CAAA,EALJC,EAICF,EAAAG,CAAA,mpBChBFL,EAiBKC,EAAAC,EAAAC,CAAA,EALJC,EAICF,EAAAG,CAAA,4qBChBFL,EAiBKC,EAAAC,EAAAC,CAAA,EALJC,EAICF,EAAAG,CAAA,6sBCmBK,OAAAC,OAAS,UAAS,EAEbA,OAAS,OAAM,EAEfA,OAAS,QAAO,4GAMOA,EAAI,yBAEnCA,EAAO,mGAbcA,EAAI,kDAWFA,EAAI,iDACLA,EAAI,oDAFFA,EAAI,4EASVA,EAAI,4HAQNA,EAAI,iDAnCJA,EAAI,iFADxBN,EAqCKC,EAAAM,EAAAJ,CAAA,EA5BJC,EAQKG,EAAAC,CAAA,4BAELJ,EAKKG,EAAAE,CAAA,EAJJL,EAA2CK,EAAAC,CAAA,gBAC3CN,EAEKK,EAAAE,CAAA,gBAGNP,EAQQG,EAAAK,CAAA,EADPR,EAAqCQ,EAAAC,CAAA,SAGtCT,EAA2BG,EAAAO,CAAA,4BAThBR,EAAa,6OAlBAA,EAAI,qDAWMA,EAAI,oCAAZA,EAAI,qDAE3BA,EAAO,mCADeA,EAAI,wEAFFA,EAAI,sEASVA,EAAI,gEAQNA,EAAI,qEAnCJA,EAAI,gFAKZS,EAAAC,GAAAT,EAAAU,GAAA,UAAU,IAAK,MAAO,GAAG,uDACxBC,EAAAC,GAAAZ,EAAAU,GAAA,UAAU,GAAG,mFA1Bd,YAAAG,EAAU,EAAE,EAAAC,GACZ,KAAAC,CAA0B,EAAAD,GAC1B,GAAAE,CAAU,EAAAF,EAEf,MAAAG,EAAWC,cAERC,GAAa,CACrBF,EAAS,QAASD,CAAE,EAGrBI,GAAO,KACN,gBACCD,KACE,gkBCCH1B,EAEKC,EAAA2B,EAAAzB,CAAA,8KAFgB0B,EAAA5E,GAAA2E,EAAAE,EAAA3C,GAAA,UAAU,GAAG,oIAD5BmB,EAAQ,eAA2BA,EAAE,mBAA1C,OAAIyB,GAAA,2JADP/B,EAMKC,EAAA2B,EAAAzB,CAAA,+EALGG,EAAQ,0JAAb,OAAIyB,GAAA,oHAVG,SAAAC,GAAcC,EAAyB,CAC3CA,EAAU,OAAS,GAClB,iBAAkB,QACrB,OAAO,cAAc,SAAS,EAAG,CAAC,yBAP1B,SAAAC,EAAQ,IAAAb,uHAEhBW,GAAcE,CAAQ,sFCPnB,MAAMC,GAAgB,CAC5B,UAAW,CACV,OAAQ,IAAMC,EAAA,WAAO,qBAA0B,wNAChD,EACA,eAAgB,CACf,OAAQ,IAAMA,EAAA,WAAO,qBAA+B,oOACrD,EACA,MAAO,CACN,OAAQ,UAAM,OAAO,qBAAsB,2UAC3C,YAAa,IAAMA,EAAA,WAAO,qBAA2B,sWACtD,EACA,IAAK,CACJ,OAAQ,UAAM,OAAO,qBAAoB,sIAC1C,EACA,OAAQ,CACP,OAAQ,UAAM,OAAO,qBAAuB,sIAC7C,EACA,QAAS,CACR,OAAQ,IAAMA,EAAA,WAAO,qBAAwB,+WAC9C,EACA,SAAU,CACT,OAAQ,IAAMA,EAAA,WAAO,qBAAyB,+MAC9C,YAAa,IAAMA,EAAA,WAAO,qBAA8B,8MACzD,EACA,cAAe,CACd,OAAQ,IAAMA,EAAA,WAAO,qBAA8B,oPACnD,YAAa,IAAMA,EAAA,WAAO,qBAAmC,mPAC9D,EACA,KAAM,CACL,OAAQ,UAAM,OAAO,qBAAqB,qTAC1C,YAAa,IAAMA,EAAA,WAAO,qBAA0B,gPACrD,EACA,YAAa,CACZ,OAAQ,IAAMA,EAAA,WAAO,qBAA4B,gPACjD,YAAa,IAAMA,EAAA,WAAO,qBAAiC,+OAC5D,EACA,OAAQ,CACP,OAAQ,UAAM,OAAO,qBAAuB,kJAC7C,EACA,UAAW,CACV,OAAQ,IAAMA,EAAA,WAAO,qBAA0B,0UAC/C,YAAa,IAAMA,EAAA,WAAO,qBAA+B,yUAC1D,EACA,QAAS,CACR,OAAQ,IAAMA,EAAA,WAAO,qBAAW,2SACjC,EACA,SAAU,CACT,OAAQ,IAAMA,EAAA,WAAO,qBAAyB,gPAC9C,YAAa,IAAMA,EAAA,WAAO,qBAA8B,+OACzD,EACA,KAAM,CACL,OAAQ,UAAM,OAAO,qBAAqB,sQAC1C,YAAa,IAAMA,EAAA,WAAO,qBAA0B,8WACrD,EACA,KAAM,CACL,OAAQ,UAAM,OAAO,qBAAqB,8IAC3C,EACA,QAAS,CACR,OAAQ,IAAMA,EAAA,WAAO,qBAAwB,4XAC9C,EACA,MAAO,CACN,OAAQ,UAAM,OAAO,qBAAsB,8GAC5C,EACA,gBAAiB,CAChB,OAAQ,IAAMA,EAAA,WAAO,qBAAgC,2PACrD,YAAa,UAAM,OAAO,qBAAqC,0PAChE,EACA,aAAc,CACb,OAAQ,IAAMA,EAAA,WAAO,qBAA6B,kRAClD,YAAa,IAAMA,EAAA,WAAO,qBAAkC,iRAC7D,EACA,KAAM,CACL,OAAQ,UAAM,OAAO,qBAAqB,6JAC3C,EACA,MAAO,CACN,OAAQ,UAAM,OAAO,qBAAsB,iWAC3C,YAAa,IAAMA,EAAA,WAAO,qBAA2B,+YACtD,EACA,eAAgB,CACf,OAAQ,IAAMA,EAAA,WAAO,qBAAkB,8MACvC,YAAa,IAAMA,EAAA,WAAO,qBAAkB,6MAC7C,EACA,KAAM,CACL,OAAQ,UAAM,OAAO,qBAAqB,mOAC3C,EACA,MAAO,CACN,OAAQ,UAAM,OAAO,qBAAsB,8MAC5C,EACA,SAAU,CACT,OAAQ,IAAMA,EAAA,WAAO,qBAAyB,2NAC/C,EACA,QAAS,CACR,OAAQ,IAAMA,EAAA,WAAO,qBAAwB,mVAC7C,YAAa,IAAMA,EAAA,WAAO,qBAA6B,kXACxD,EACA,OAAQ,CACP,OAAQ,IAAMA,EAAA,WAAO,qBAAuB,sOAC5C,YAAa,IAAMA,EAAA,WAAO,qBAA4B,qOACvD,EACA,KAAM,CACL,OAAQ,UAAM,OAAO,qBAAqB,+QAC3C,EACA,MAAO,CACN,OAAQ,UAAM,OAAO,qBAAsB,oOAC3C,YAAa,IAAMA,EAAA,WAAO,qBAA2B,mOACtD,EACA,IAAK,CACJ,OAAQ,UAAM,OAAO,qBAAoB,8GAC1C,EACA,OAAQ,CACP,OAAQ,IAAMA,EAAA,WAAO,qBAAuB,oOAC5C,YAAa,IAAMA,EAAA,WAAO,qBAA4B,mOACvD,EACA,MAAO,CACN,OAAQ,IAAMA,EAAA,WAAO,qBAAS,uFAC/B,EACA,cAAe,CACd,OAAQ,IAAMA,EAAA,WAAO,qBAA8B,sIACpD,EACA,KAAM,CACL,OAAQ,UAAM,OAAO,qBAAqB,8IAC3C,EACA,QAAS,CACR,OAAQ,IAAMA,EAAA,WAAO,qBAAwB,gOAC9C,EACA,QAAS,CACR,OAAQ,IAAMA,EAAA,WAAO,qBAAwB,6PAC7C,YAAa,IAAMA,EAAA,WAAO,qBAA6B,+RACxD,EACA,WAAY,CACX,OAAQ,IAAMA,EAAA,WAAO,qBAA2B,0TAChD,YAAa,IAAMA,EAAA,WAAO,qBAAgC,ybAC3D,EACA,aAAc,CACb,OAAQ,IAAMA,EAAA,WAAO,qBAA6B,kMAClD,YAAa,IAAMA,EAAA,WAAO,qBAAkC,iMAC7D,EACA,MAAO,CACN,OAAQ,UAAM,OAAO,qBAAsB,ucAC3C,YAAa,IAAMA,EAAA,WAAO,qBAA2B,miBACtD,CACD,o3BC7IApC,EAkBKC,EAAAC,EAAAC,CAAA,EARJC,EAOGF,EAAAmC,CAAA,EANFjC,EAECiC,EAAAC,CAAA,EACDlC,EAECiC,EAAAE,CAAA,8OCLkB;AAAA,GAEnB,kBACEjC,EAAI;;;;;;;;;iMALRN,EAwBKC,EAAA2B,EAAAzB,CAAA,EAvBJC,EAAgBwB,EAAAY,CAAA,SAChBpC,EAKGwB,EAAAa,CAAA,SAHFrC,EAEMqC,EAAAC,CAAA,gBAEPtC,EAeGwB,EAAAe,CAAA,WAGJ3C,EAEQC,EAAAW,EAAAT,CAAA,2EAvBJG,EAAI,kIAVD,MAAAkB,EAAWC,SAEN,KAAAmB,CAAY,EAAAvB,EA6BA,MAAAwB,EAAA,IAAArB,EAAS,OAAO,iICnCjC,SAASsB,GACfC,EACAzB,EACA0B,EAA2B,KACkC,CAC7D,OAAI1B,IAAS,OACL0B,IAAS,KAAO,OAAS,KAE7B1B,IAAS,UAAYA,IAAS,MAC1B0B,IAAS,KAAOD,EAAQ,IAAMA,EAAQ,IACnCzB,IAAS,SACZ0B,IAAS,KAAO,WAAWD,CAAK,EAAIA,EACjCzB,IAAS,WAAaA,GAAQ,OACpC0B,IAAS,MACZD,EAAQ,OAAOA,CAAK,EACbA,IAAU,OAAS,OAAS,SACzBC,IAAS,KACZD,EAEDA,IAAU,OACPzB,IAAS,aACXyB,EAAA,KAAK,UAAUA,CAAK,EACrBA,GAGJC,IAAS,KACLD,IAAU,GAAK,KAAO,KAAK,MAAMA,CAAK,EACnC,OAAOA,GAAU,SACvBA,IAAU,GACNC,IAAS,KAAO,OAAS,OAE1BD,EAED,KAAK,UAAUA,CAAK,CAC5B,CClCA,MAAeE,GAAA,6FCoBuD,GAAC,4FAAfC,EAAA5C,KAAY,GAAC6C,GAAA,qEAPhE;AAAA,GAEH,iBACE7C,EAAI,sCAIaA,EAAS,QAAQ,eAAa,2DARxC2C,EAAQ,GAAAG,EAAAC,EAAA,MAAAC,CAAA,qNADnBtD,EAWIC,EAAAsD,EAAApD,CAAA,EAVHC,EAA4BmD,EAAAF,CAAA,SAC5BjD,EAKKmD,EAAA7C,CAAA,SAHJN,EAEKM,EAAAF,CAAA,gBAENJ,EAEMmD,EAAAC,CAAA,EADLpD,EAAoCoD,EAAAC,CAAA,wCAItCzD,EAEQC,EAAAW,EAAAT,CAAA,2EAVJG,EAAI,mBAIaA,EAAS,IAA0BA,KAAY,8LAfxD,KAAAsC,CAAY,EAAAvB,GACZ,UAAAqC,CAAiB,EAAArC,EAEtB,MAAAG,EAAWC,KAgBMoB,EAAA,IAAArB,EAAS,OAAO,mLCtBjC,MAAMmC,EAAsC,CAClDC,GAIAC,GAGA,YACCtC,EACAuC,EACAC,EACAC,EACApB,EACC,CACD,KAAKgB,GAAMrC,EACX,KAAK,MAAQwC,EACb,KAAK,QAAUC,EACf,KAAKH,GAAMC,EAEX,KAAK,KAAOlB,CACb,CAEA,SAA4BqB,EAAeC,EAAmB,CACvD,MAAAC,EAAI,IAAI,YAAY,SAAU,CACnC,QAAS,GACT,OAAQ,CAAE,KAAAD,EAAM,GAAI,KAAKN,GAAK,MAAOK,CAAW,EAChD,EACI,KAAAJ,GAAI,cAAcM,CAAC,CACzB,CACD,yLCboC,GAAC,wDAKuG7D,EAAU,GAC9IA,OACC,KAAI,SAAMA,EAAU,GACrBA,OACC,aAAW8D,GAAA9D,CAAA,8FAJwHA,EAAU,GAC9IA,OACC,KAAI,KAAA+D,EAAA,EAAAC,CAAA,EAAMhE,EAAU,GACrBA,OACC,8HAJsC,IAAAgE,EAAAhE,KAAY,KAAI,OAAM4C,EAAA5C,MAAa,aAAWiE,GAAAjE,CAAA,kFAA9CkE,EAAA,GAAAF,OAAAhE,KAAY,KAAI,KAAA+D,EAAA,EAAAC,CAAA,EAAMhE,MAAa,sIAIrDA,EAAU,GAC/BA,OAEI,YAAW,sBAHF,IAAO,aAGJ,GAAC,yDAHIA,EAAU,GAC/BA,OAEI,YAAW,KAAA+D,EAAAI,EAAAC,CAAA,mDAP+EA,EAAApE,KAAY,YAAW,sBAA/B,IAAO,aAAyB,GAAC,6CAAzBkE,EAAA,GAAAE,OAAApE,KAAY,YAAW,KAAA+D,EAAAI,EAAAC,CAAA,kEAatF,GAAC,gEAJPpE,EAAK,YAC7BA,EAAS,0BAVF,OAAAA,OAAqB,SAAQqE,0BAalCrE,EAAgB,GAAC,OAAS,GAACsE,GAAA,wCAb9B,IAAG,YASH;AAAA,+BAAwB,aAAO,IAAE,aACvB;AAAA,gBACH,gGAdctE,EAAgB,GAAC,OAAS,CAAC,UAApDN,EAiBKC,EAAA2B,EAAAzB,CAAA,EAhBJC,EAeCwB,EAAAf,CAAA,wJAJ2BP,EAAK,QAAA+D,EAAAQ,EAAAC,CAAA,cAC7BxE,EAAS,QAAA+D,EAAAU,EAAAC,CAAA,EAGP1E,EAAgB,GAAC,OAAS,4EAhBRA,EAAgB,GAAC,OAAS,CAAC,qEAmBpB,GAAC,2FAIjB,EAAK,qFADtBN,EAEKC,EAAA2B,EAAAzB,CAAA,0IA1BAG,EAAgB,GAAC,OAAS,GAAC2E,GAAA,OACzB3E,EAAgB,yBAArB,OAAIyB,GAAA,2BAoBDzB,EAAgB,GAAC,OAAS,GAAC4E,GAAA,IAE5B5E,EAAU,IAAA6C,GAAA,+JAxBE7C,EAAU,wDAD5BN,EA8BKC,EAAAS,EAAAP,CAAA,EA7BJC,EAuBKM,EAAAF,CAAA,qIAtBCF,EAAgB,GAAC,OAAS,2DACxBA,EAAgB,sBAArB,OAAIyB,GAAA,kHAAJ,OAoBGzB,EAAgB,GAAC,OAAS,0EAtBfA,EAAU,IAwBtBA,EAAU;8EAhCjBN,EAKIC,EAAAkF,EAAAhF,CAAA,kNAXQ,WAAAiF,CAAmB,EAAA/D,GACnB,iBAAAgE,CAAqB,EAAAhE,GACrB,WAAAiE,CAAe,EAAAjE,GACf,iBAAAkE,CAAyC,EAAAlE,sXCQnDf,EAAS,sCAATA,EAAS,qIADqBA,EAAI,uNAZxB,KAAAkF,CAAY,EAAAnE,EACnBoE,EAAY,gBAEPC,GAAI,CACZ,UAAU,UAAU,UAAUF,CAAI,EAClCG,EAAA,EAAAF,EAAY,SAAS,EACrB,gBACCE,EAAA,EAAAF,EAAY,MAAM,GAChB,iMCWiBG,EAAU,oFAGpBA,4EAJT5F,EAEKC,EAAAO,EAAAL,CAAA,uBACLH,EAEKC,EAAAS,EAAAP,CAAA,EADJC,EAAwBM,EAAAmF,CAAA,yKAVNC,EAAU,oFAGpBA,4EAJT9F,EAEKC,EAAAO,EAAAL,CAAA,uBACLH,EAEKC,EAAAS,EAAAP,CAAA,EADJC,EAAwBM,EAAAmF,CAAA,6KALrB,OAAAvF,OAAqB,SAAQ,EAOxBA,OAAqB,aAAY,sGAR5CN,EAgBMC,EAAAuF,EAAArF,CAAA,mhBArBF,IAAA2F,GAAa,4BACbF,GAAa,iDAHN,iBAAAL,CAAyC,EAAAlE,qNCQjD;AAAA,GAEF,kBAAoBf,EAAQ,2EAF7BN,EAGIC,EAAA8F,EAAA5F,CAAA,SADHC,EAAmC2F,EAAAlF,CAAA,0BAAfP,EAAQ,6CALRoE,EAAA,IAAMpE,EAAQ,8BAFhC;AAAA,GAEF,4FAFDN,EAGIC,EAAA8F,EAAA5F,CAAA,SADHC,EAAyC2F,EAAAlF,CAAA,iBAArB2D,EAAA,GAAAE,OAAA,IAAMpE,EAAQ,KAAA+D,EAAAI,EAAAC,CAAA,8DAH/BpE,EAAK,GAAA6C,8MALE,aAAA6C,EAA0B,IAAI,EAAA3E,EAC9B,UAAA4E,EAA0B,IAAI,EAAA5E,GAC9B,MAAA6E,CAAc,EAAA7E,uzBCqCUf,EAAgB,2GAAhBA,EAAgB,wJAFhB,SAAAA,KAAW,gGAAXkE,EAAA,IAAA2B,EAAA,SAAA7F,KAAW,+JA2CxB,YAAAA,MAAS,SAAS,aAIlCA,EAAa,0BAAlB,OAAIyB,GAAA,4CAOgCzB,EAAK,GAAA8F,6BACjC9F,EAAmB,yBAAxB,OAAIyB,GAAA,4FATC;AAAA,CACV,2CAKE;AAAA,0BACuB,kBAA2B,GAAC,MAACzB,EAAI,QAAC,GAAC,MAAO;AAAA,kCAClC,YAAiE,KAAG,2CAiCzF;AAAA;AAAA;AAAA;AAAA,CAIZ,sHAjDIN,EAEKC,EAAAO,EAAAL,CAAA,uBACLH,EA+CKC,EAAAS,EAAAP,CAAA,EA9CJC,EA6CAM,EAAAmF,CAAA,iEAtCoBzF,EAA0CyF,EAAAhF,CAAA,6IAV5C2D,EAAA,MAAA6B,EAAA,KAAA/F,MAAS,kCAIzBA,EAAa,uBAAlB,OAAIyB,GAAA,qHAAJ,yBAMoDzB,EAAI,8EAEhDA,EAAmB,sBAAxB,OAAIyB,GAAA,qHAAJ,qMA/CkB,YAAAzB,MAAa,SAAS,aAOnCA,EAAmB,yBAAxB,OAAIyB,GAAA,4CAkBCzB,EAAK,GAAA4E,+FAtBL;AAAA;AAAA,iBAEM,kBAA2B,GAAC,MAAC5E,EAAI,QAAC,GAAC,MAAO;AAAA,yBAClC,wDAuBlB;AAAA;AAAA,cAEO,sHAhCTN,EAEKC,EAAAO,EAAAL,CAAA,uBACLH,EA8BKC,EAAAS,EAAAP,CAAA,EA7BJC,EA4BaM,EAAAmF,CAAA,SA1BFzF,EAA0CyF,EAAAhF,CAAA,6IALnC2D,EAAA,MAAA6B,EAAA,KAAA/F,MAAa,oCAKSA,EAAI,eAEvCA,EAAmB,sBAAxB,OAAIyB,GAAA,kHAAJ,2NAkC8BzB,EAAa,aAClCA,EAAS,8BAFrB;AAAA,gBACa,MAACA,EAAC,SAAC,kBAAgB,aAAe;AAAA,cACpC,aAAW,oBAAkB,MAACA,EAAC,SAAC;AAAA,OACvC,6LAGsEA,EAAgB,sCAAhBA,EAAgB,yCAA7CoE,EAAApE,KAAW,SAAQ,sBAAtB,IAAE,aAAqB,GAAC,6CAArBkE,EAAA,GAAAE,OAAApE,KAAW,SAAQ,KAAA+D,EAAAI,EAAAC,CAAA,qDAiBvDA,EAAA5B,GACDxC,EAAa,IACbA,EAAY,SACZ,IAAG,eAKNA,EAAa,GAACA,EAAC,KACX,KAAI,WAENA,EAAK,aACVA,EAAS,aAHSA,EAAa,GAACA,EAAC,KAAE,aAAW2E,GAAA3E,CAAA,iBAX9C;AAAA,KACA,yBAMK,IAAE,kBAGR,KAAG,aACU,GAAC,eAEd,OAAK,aAAO,IAAE,aACH,YAAU,mGAbpBN,EAMKC,EAAAwD,EAAAtD,CAAA,kBACNH,EAQMC,EAAAuD,EAAArD,CAAA,iFAdEqE,EAAA,IAAAE,OAAA5B,GACDxC,EAAa,IACbA,EAAY,SACZ,IAAG,OAAA+D,EAAAI,EAAAC,CAAA,eAKNpE,EAAa,GAACA,EAAC,KACX,KAAI,KAAA+D,EAAAU,EAAAC,CAAA,EAAO1E,EAAa,GAACA,EAAC,KAAE,gFAE9BA,EAAK,SAAA+D,EAAAiC,EAAAC,CAAA,eACVjG,EAAS,SAAA+D,EAAAmC,EAAAC,CAAA,uEAxB2BnG,EAAS,iBAKvCA,EAAK,aACTA,EAAS,0BARZ;AAAA,KACC,kBAC4B,SAAO,aAC9B,IAAE,kBAIN,eAAI,aAAO,IAAE,aACF,YAAU,mGAPtBN,EAEKC,EAAAwD,EAAAtD,CAAA,yBACJH,EAMIC,EAAAuD,EAAArD,CAAA,0DAR+BG,EAAS,SAAA+D,EAAAQ,EAAAC,CAAA,eAKvCxE,EAAK,SAAA+D,EAAAqC,EAAAC,CAAA,eACTrG,EAAS,SAAA+D,EAAAiC,EAAAC,CAAA,0DAesCjG,EAAa,GAACA,EAAC,KACxD,YAAW,sBAD4B,GAAC,aAC5B,GAAC,0DAD4BA,EAAa,GAACA,EAAC,KACxD,YAAW,KAAA+D,EAAAI,EAAAC,CAAA,yGAzBbpE,EAAe,IAAC,SAASA,EAAS,4SAlCtCN,EAEGC,EAAAY,EAAAV,CAAA,uCAIkDuE,EAAApE,MAAY,YAAW,sBAAzB,GAAC,aAAyB,GAAC,6CAAzBkE,EAAA,IAAAE,OAAApE,MAAY,YAAW,KAAA+D,EAAAI,EAAAC,CAAA,qDATxEA,EAAA5B,GAAgBxC,EAAa,IAAEA,EAAY,SAAM,IAAI,aASrD0E,EAAA1E,MAAY,KAAI,WAA4EA,EAAK,aACpGA,EAAS,aARLA,EAAmB,GAACA,EAAgB,IAAEA,EAAC,MAAAqE,GAAA,EAOjBiC,EAAAtG,MAAY,aAAWsE,GAAAtE,CAAA,iBAZ9C;AAAA,GACR,yBAGM,GAAC,2BAQH,KAAG,aAAkB,GAAC,eAAoE,OAAK,aAAO,IAAE,aAC7F,YAAU,mGAZzBN,EAGMC,EAAAwD,EAAAtD,CAAA,8BAMFH,EAIOC,EAAAuD,EAAArD,CAAA,iFAXHqE,EAAA,IAAAE,OAAA5B,GAAgBxC,EAAa,IAAEA,EAAY,SAAM,IAAI,OAAA+D,EAAAI,EAAAC,CAAA,EAEpDpE,EAAmB,GAACA,EAAgB,IAAEA,EAAC,8DAOxCkE,EAAA,IAAAQ,OAAA1E,MAAY,KAAI,KAAA+D,EAAAU,EAAAC,CAAA,EAAO1E,MAAY,gFAAyDA,EAAK,SAAA+D,EAAAiC,EAAAC,CAAA,eACpGjG,EAAS,SAAA+D,EAAAmC,EAAAC,CAAA,4FAML;AAAA,YACA,MAACnG,EAAgB,+CAAhBA,EAAgB,gDAFdoE,EAAApE,KAAW,SAAQ,sBADlB;AAAA,cACF,aAAqB,GAAC,6CAArBkE,EAAA,GAAAE,OAAApE,KAAW,SAAQ,KAAA+D,EAAAI,EAAAC,CAAA,8FA5BzB,OAAApE,OAAqB,SAAQ,EAmCxBA,OAAqB,aAAY,uGApC5CN,EAyFMC,EAAAuF,EAAArF,CAAA,yUA/FFG,EAAK,4LADXN,EAkGKC,EAAA2B,EAAAzB,CAAA,kXA/HO,WAAA0G,CAAsB,EAAAxF,GACtB,iBAAAyF,CAAwB,EAAAzF,GACxB,KAAAuB,CAAY,EAAAvB,GACZ,oBAAA0F,CAAgC,EAAA1F,GAChC,oBAAA2F,CAAwB,EAAA3F,GACxB,cAAA4F,CAAkB,EAAA5F,GAClB,MAAA6E,CAAc,EAAA7E,GAEd,iBAAAkE,CAAyC,EAAAlE,EAEhD6F,EACAC,EAEAC,GAAmB,QAAS,OAAQ,QAAS,OAAO,EACpDC,EAAuBL,EAAoB,OAC7CM,GAUKF,EAAgB,SAASE,EAAM,SAAS,6CAgB5BJ,EAAWK,qDAmCXJ,EAAOI,ylBCnF3B,MAAeC,GAAA,uDCAAC,GAAA,+VCmJTC,GAAA,OAAAA,EAAA,UAAO,KAAKpH,EAAI,GAAC,eAAe,EAAE,OAAS,OAAO,KAAKA,EAAK,oBAAiB,EAAE,+lBAqC5EqH,EAAA,OAAO,KAAKrH,EAAK,kBAAe,EAAE,WAyClCoH,EAAA,OAAO,KAAKpH,EAAK,oBAAiB,EAAE,sCAzE9B,iBAAO,KAAKA,EAAI,GAAC,eAAe,EAAE,OAC5C,OAAO,KAAKA,EAAK,oBAAiB,EAAE,wCAkB7BA,EAAK,yBAAV,OAAIyB,GAAA,kFAiBAzB,EAAY,yBAAjB,OAAIyB,GAAA,+EAyCCzB,EAAY,yBAAjB,OAAIyB,GAAA;;mlBAjFR/B,EAOKC,EAAAO,EAAAL,CAAA,uBACLH,EAoGKC,EAAAa,EAAAX,CAAA,EAnGJC,EAWKU,EAAAJ,CAAA,SACLN,EAsFKU,EAAAL,CAAA,EArFJL,EAWKK,EAAAE,CAAA,gRA7BM6D,EAAA,KAAAoD,EAAA,iBAAO,KAAKtH,EAAI,GAAC,eAAe,EAAE,OAC5C,OAAO,KAAKA,EAAK,oBAAiB,EAAE,8BAkB7BA,EAAK,sBAAV,OAAIyB,GAAA,qHAAJ,iEAaEyC,EAAA,KAAAmD,EAAA,OAAO,KAAKrH,EAAK,kBAAe,EAAE,oEAIhCA,EAAY,sBAAjB,OAAIyB,GAAA,2GAAJ,OAAIA,EAAA8F,EAAA,OAAA9F,GAAA,eAqCDyC,EAAA,KAAAkD,EAAA,OAAO,KAAKpH,EAAK,oBAAiB,EAAE,oEAIlCA,EAAY,sBAAjB,OAAIyB,GAAA,8GAAJ,OAAIA,EAAA+F,EAAA,OAAA/F,GAAA,iFAzCJ,OAAIA,GAAA,0BAyCJ,OAAIA,GAAA,8RAnDFzB,EAAQ,gHADCA,EAAG,MAAA8C,EAAAC,EAAA,MAAAC,CAAA,6CAHZF,EAAA2E,EAAA,QAAAC,EAAA,YAAA1H,OAAqBA,EAAQ,IAAG,eAAiB,iBAAe,0BAFlEN,EAOIC,EAAA8H,EAAA5H,CAAA,EAFHC,EAAuB2H,EAAA1E,CAAA,+DAHtBmB,EAAA,GAAAwD,OAAA,YAAA1H,OAAqBA,EAAQ,IAAG,eAAiB,iBAAe,iLAWnEN,EAAsCC,EAAAsD,EAAApD,CAAA,6EAO5B,uBACcG,EAAI,GAAC,gBACzB,IAAMA,EAAU,IAAC,UAChB,yBACaA,EAAO,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,iJAegBA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,mBACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,iKA3BJN,EA+BKC,EAAA2B,EAAAzB,CAAA,sFA5BkBG,EAAI,GAAC,gBACzB,IAAMA,EAAU,IAAC,UAChB,mCACaA,EAAO,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,0KAegBA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,6BACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,mOA5BA4C,EAAA5C,MAAW,UAAQiE,GAAAjE,CAAA,uEAAnBA,MAAW,ySAqChBN,EAAwCC,EAAAsD,EAAApD,CAAA,+EAO9B,GACc,oBAAAG,EAAK,qBAAkBA,OAC1C,WACa,cAAAA,EAAQ,qBAAkBA,OACvC,gIASgB,iBAAAA,EAAK,qBAAkBA,OACvC,QACU,WAAAA,EAAQ,qBAAkBA,OAAkB,uKAjB1DN,EAqBKC,EAAA2B,EAAAzB,CAAA,gEAlBkBqE,EAAA,KAAAyD,EAAA,oBAAA3H,EAAK,qBAAkBA,OAC1C,YACakE,EAAA,KAAAyD,EAAA,cAAA3H,EAAQ,qBAAkBA,OACvC,gJASgBkE,EAAA,KAAA0D,EAAA,iBAAA5H,EAAK,qBAAkBA,OACvC,SACUkE,EAAA,KAAA0D,EAAA,WAAA5H,EAAQ,qBAAkBA,OAAkB,qOAlBtDA,EAAI,GAAC,kBAAkBA,EAAgB,MAAAsE,GAAAtE,CAAA,uEAAvCA,EAAI,GAAC,kBAAkBA,EAAgB,uMApF5CA,EAAI,IAAA6C,GAAA7C,CAAA,yEAAJA,EAAI,mMA/HG,aAAA6H,CAEV,EAAA9G,GACU,aAAA+G,CAA0B,EAAA/G,GAC1B,KAAAuB,CAAY,EAAAvB,GACZ,IAAAgH,CAAuC,EAAAhH,EAE9CuB,IAAS,KACZA,EAAO,SAAS,SAAW,KAAO,SAAS,KAAO,SAAS,UAEvDA,EAAK,SAAS,GAAG,IACrBA,GAAQ,KAGL,IAAA2C,EAA4C,SAE1C,MAAA+C,IACJ,SAAUd,EAAM,EAChB,cAAcC,EAAU,GAGtB,IAAArC,EAAa,GAEOgD,EAAa,IAAKvB,GACzCA,EAAW,OAAO,IAAK0B,GAAG,CACrB,IAAAC,EAAeL,EAAaI,CAAG,EAAE,eAAe,aAChD,OAAAC,IAAiB,OACpBA,EAAe,GACE,OAAAA,GAAiB,WAClCA,EAAe,KAAK,UAAUA,CAAY,GAEpCA,KAIyBJ,EAAa,IAC7CvB,GAAmB,UAAMA,EAAW,QAAQ,MAAM,GAGhD,IAAAE,EAAmCqB,EAAa,IAAKvB,GACpD,UAAMA,EAAW,OAAO,MAAM,EAAE,KAAK,EAAK,kBAGhC4B,GAAQ,QAKd,MADa,YAAM7F,EAAO,MAAM,GACd,sBAGZ8F,GAAW,QACV,MAASL,EAAI,eAIzBM,EAKAC,EAEJH,IAAW,KAAMvE,GAAUyB,EAAA,EAAAgD,EAAOzE,CAAI,GAEtCwE,IAAc,KAAMG,GAAiBlD,EAAA,EAAAiD,EAAUC,CAAW,GAmD1DlH,GAAO,KACN,SAAS,KAAK,MAAM,SAAW,SAC3B,iBAAkB,QACrB,OAAO,cAAc,SAAS,EAAG,CAAC,OAGlC,SAAS,KAAK,MAAM,SAAW,0CAkCV,MAAAkB,EAAAiG,GAAAnD,EAAA,EAAAJ,EAAmBuD,CAAQ,yfC7F1CxI,EAAQ,eAAuEA,EAAO,oBAA3F,OAAIyB,GAAA,gMAACzB,EAAQ,wFAAb,OAAIyB,GAAA,sMAIAzB,EAAO,oDAIDA,EAAS,8QAJfA,EAAO,wFAIDA,EAAS,+MATjBA,EAAQ,IAAIA,EAAQ,GAAC,QAAM6C,GAAA7C,CAAA,uEAA3BA,EAAQ,IAAIA,EAAQ,GAAC,oNAThB,oBAAaA,EAAK,IAAIA,EAAM,yBAAyBA,EAAE,OAClD,8BAAkBA,EAAS,IAAAA,KAAM,cAAY,kBAGxDA,EAAK,6CAGGqD,GAAOrD,EAAI,GAAAA,KAAQA,EAAU,GAAEA,EAAO,GAAEA,EAAI,mCAVlDA,EAAS,yGAEH,OAAAA,EAAa,GAAAA,EAAI,UAAM,QAAK,SAA5ByI,EAAA,MAAAzI,EAAa,GAAAA,EAAI,UAAM,mGAGnBA,EAAkB,6GAFxB,oBAAaA,EAAK,IAAIA,EAAM,yBAAyBA,EAAE,YAClD,8BAAkBA,EAAS,IAAAA,KAAM,cAAY,gCAGxDA,EAAK,iEAGGqD,GAAOrD,EAAI,GAAAA,KAAQA,EAAU,GAAEA,EAAO,GAAEA,EAAI,oEAR5C0I,EAAA,MAAA1I,EAAa,GAAAA,EAAI,UAAM,kCAF7BA,EAAS,2IAKCA,EAAkB,2NApEvB,KAAAsC,CAAY,EAAAvB,GACZ,UAAA4H,CAAqC,EAAA5H,GACrC,aAAA8G,CAA2C,EAAA9G,GAE3C,GAAAE,CAAU,EAAAF,GACV,MAAA6H,CAA6B,EAAA7H,GAE7B,SAAA8H,CAAmC,EAAA9H,GACnC,YAAA+H,CAAwB,EAAA/H,EACxB,QAAAgI,EAAwB,IAAI,EAAAhI,GAC5B,OAAApB,CAAmB,EAAAoB,GACnB,WAAAiI,CAAqB,EAAAjI,GACrB,QAAA2C,CAAe,EAAA3C,EAEpB,MAAAG,EAAWC,SACb8H,EAAiB,GAErB5H,GAAO,KACNH,EAAS,QAASD,CAAE,EAET,UAAAiI,KAASD,EACnB/H,EAAS,QAASgI,EAAM,EAAE,aAI1BhI,EAAS,UAAWD,CAAE,EAEX,UAAAiI,KAASD,EACnB/H,EAAS,QAASgI,EAAM,EAAE,KAe7BC,GAAW,YAAaJ,CAAM,EAErB,SAAAK,EAAmBvF,EAAkC,WAClDwF,KAAKxF,EAAE,WACjBgE,EAAa5G,CAAE,EAAE,MAAMoI,CAAC,EAAIxF,EAAE,OAAOwF,CAAC,EAAAxB,CAAA,4GAiB7BA,EAAa5G,CAAE,EAAE,SAAQgG,yBACxBqC,EAAA,aAAAzB,EAAa5G,CAAE,EAAE,MAAM,MAAKwB,CAAA,IAA5BoF,EAAa5G,CAAE,EAAE,MAAM,MAAKwB,oaAhCvC4C,EAAA,EAAEwD,EACFA,GACAA,EAAS,OAAQU,GAAC,OACXC,EAAa3B,EAAa0B,EAAE,EAAE,EAAE,OAAS,uBAC1CC,GACJP,EAAkB,KAAKM,CAAC,EAElBC,oBAYJ3B,EAAa5G,CAAE,EAAE,OAAS,SACzB4H,GAAU,MAAOY,IAAOA,EAAE,MAAM,OAAO,MAC1Cb,EAAM,QAAU,GAAKA,CAAA,MAErBA,EAAM,QAAU,GAAIA,CAAA,yQC3CZ,UAAA5I,KAAS,UAChB,GAAAA,KAAS,GACN,MAAAA,KAAS,MACN,SAAAA,KAAS,mKAHRkE,EAAA,IAAAwF,EAAA,UAAA1J,KAAS,WAChBkE,EAAA,IAAAwF,EAAA,GAAA1J,KAAS,IACNkE,EAAA,IAAAwF,EAAA,MAAA1J,KAAS,OACNkE,EAAA,IAAAwF,EAAA,SAAA1J,KAAS,0QAlBR,SAAA2J,CAAa,EAAA5I,GACb,YAAA+H,CAAgB,EAAA/H,GAChB,aAAA8G,CAAiB,EAAA9G,GACjB,KAAAuB,CAAS,EAAAvB,GACT,OAAApB,CAAW,EAAAoB,GACX,WAAAiI,CAAe,EAAAjI,GACf,QAAA2C,CAAY,EAAA3C,EAEjB,MAAAG,EAAWC,KACjB,OAAAE,GAAO,KACNH,EAAS,OAAO,oaCdlB,MAAe0I,GAAA,0GCwoBL5J,EAAK;;;;;2JAGbN,EAISC,EAAAkK,EAAAhK,CAAA,WACTH,EAOQC,EAAAmK,EAAAjK,CAAA,uMAcIG,EAAY,+ZA2BrBoE,EAAApE,MAAG,0BAA0B,iBAlB1BA,EAAQ,IAAAqE,GAAArE,CAAA,sFAmBF4J,EAAI,GAAA9G,EAAAC,EAAA,MAAAC,CAAA,EAAOF,EAAAC,EAAA,MAAAgH,EAAA/J,MAAG,aAAa,gMApBvCN,EAsBQC,EAAAqK,EAAAnK,CAAA,wBATPC,EAQGkK,EAAAvL,CAAA,gBADFqB,EAAyCrB,EAAAsE,CAAA,UAnBrC/C,EAAQ,0DAkBXkE,EAAA,WAAAE,OAAApE,MAAG,0BAA0B,OAAA+D,EAAAI,EAAAC,CAAA,EACTF,EAAA,WAAA6F,OAAA/J,MAAG,aAAa,+DAZnCiK,EAAAjK,MAAG,oBAAoB,sHACd2C,EAAQ,GAAAG,EAAAC,EAAA,MAAAC,CAAA,EAAOF,EAAAC,EAAA,MAAAgH,EAAA/J,MAAG,aAAa,8GAP1CN,EAQQC,EAAAW,EAAAT,CAAA,gBADPC,EAA6CQ,EAAAyC,CAAA,WAE9CrD,EAAWC,EAAA2B,EAAAzB,CAAA,0CAHTqE,EAAA,WAAA+F,OAAAjK,MAAG,oBAAoB,OAAA+D,EAAAmG,EAAAD,CAAA,EACC/F,EAAA,WAAA6F,OAAA/J,MAAG,aAAa,iYAkB7CN,EAqBKC,EAAAU,EAAAR,CAAA,EAjBJC,EAKCO,EAAAH,CAAA,SACDJ,EAUKO,EAAAD,CAAA,+WAKsBJ,EAAkB,uOA1FzCA,EAAkB,IAAA2E,GAAA3E,CAAA,IAGlBA,EAAiB,IAAA8D,GAAA,IAmBhB9D,EAAK,IAAAiE,GAAAjE,CAAA,IAeNA,EAAW,IAAAsE,GAAAtE,CAAA,EA2BZmK,EAAAnK,OAAoBA,EAAK,IAAA4E,GAAA5E,CAAA,IAyBzBA,EAAQ,KAAA6C,GAAA7C,CAAA,8KApE0BA,EAAQ,GAAG,IAAM,MAAM,uDAD1BA,EAAQ,GAAG,OAAS,MAAM,uFAA9DN,EA0CKC,EAAAS,EAAAP,CAAA,EAzCJC,EAcKM,EAAAF,CAAA,uGAnCAF,EAAkB,6DAGlBA,EAAiB,4DAmBhBA,EAAK,kIAD2BA,EAAQ,GAAG,IAAM,MAAM,EAgBxDA,EAAW,yFAjBmBA,EAAQ,GAAG,OAAS,MAAM,EA4CzDA,OAAoBA,EAAK,mHAyBzBA,EAAQ,wRAlWN,MAAAoK,GAAmB,aAKnBC,GAAgC,GAChCC,GAAmC,GA3ShC,SAAAC,GACRtJ,EACAD,EACAwJ,EAAkB,CAEP,UAAAC,KAAOD,YACNE,KAAYD,EAAIzJ,CAAI,KAC1B0J,IAAazJ,EAAE,MAAS,SAGvB,GAKC,SAAA0J,GAAqBlI,EAAU,CAErC,aAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,GAC1CA,IAAU,IACVA,IAAU,IACTA,EAohBM,SAAAmI,GAAcC,EAAY,CAC3B,iBAAYA,0HAzmBpBC,SAEW,KAAAxI,CAAY,EAAAvB,GACZ,WAAAgK,CAA2B,EAAAhK,GAC3B,OAAAiK,CAAkB,EAAAjK,GAClB,aAAA+G,CAA0B,EAAA/G,EAC1B,OAAAkK,EAAQ,QAAQ,EAAAlK,EAChB,mBAAAmK,EAAoB,EAAK,EAAAnK,GACzB,OAAApB,CAAmB,EAAAoB,GACnB,WAAAoK,CAAmB,EAAApK,EACnB,UAAAqK,EAAW,EAAI,EAAArK,EACf,aAAAsK,EAAc,EAAI,EAAAtK,EAClB,oBAAAuK,EAAqB,EAAK,EAAAvK,GAC1B,SAAAwK,CAAiB,EAAAxK,GACjB,WAAAiI,CAAqB,EAAAjI,GACrB,IAAAgH,CAAuC,EAAAhH,GACvC,SAAAyK,CAAuB,EAAAzK,GACvB,QAAA2C,CAAe,EAAA3C,EAEtB0K,EAAiBC,aAIjB/B,EAAQ,CACX,GAAIqB,EAAO,GACX,KAAM,SACN,MAAK,CAAI,KAAM,QAAQ,EACvB,UAAW,GACX,SAAU,KACV,UAAW,YAGNW,EAAgB,OAAO,eAAc,oBAAuB,YAClE7D,EAAa,QAAS5I,GAAC,CAClB,GAAAA,EAAE,GAAE,OACD0M,EAAO1M,EAAE,WACZA,EAAE,OAAO,SAAW,EACpBA,EAAE,QAAQ,SAAW,MAEvBA,EAAE,YAAkB,IAAAyM,EACnB,YACuB,uBAAAzM,EAAE;AAAA,eACf0M,mDAAI,QAEP/H,GACR,QAAQ,MAAM,mCAAmC,EACjD,QAAQ,MAAMA,CAAC,UAMdgI,MADa,gBAAgB,OAAO,SAAS,MAAM,EACzB,IAAI,MAAM,IAAM,OAAST,EAC9C,SAAAU,EAAqBC,EAAgB,CAC7C1G,EAAA,GAAAwG,EAAmBE,CAAO,EACtB,IAAAhP,MAAa,gBAAgB,OAAO,SAAS,MAAM,EACnDgP,EACHhP,EAAO,IAAI,OAAQ,KAAK,EAExBA,EAAO,OAAO,MAAM,EAErB,QAAQ,aAAa,KAAM,GAAI,IAAMA,EAAO,SAAQ,GAgBjD,IAAA+L,MAA+B,IAW/BjB,kBAMWmE,EACdnO,EACAoO,EAAoC,KAO7B,MAAAxC,EAAU,MAAA5H,GAAchE,CAAI,EAAEoO,CAAI,WAEvC,KAAApO,EACA,UAAW4L,SAEJ5F,GACJ,GAAAoI,IAAS,kBAEL,MAAAxC,EAAU,MAAA5H,GAAchE,CAAI,EAAE,OAAQ,SAE3C,KAAAA,EACA,UAAW4L,SAEJ5F,GACR,cAAQ,MAAK,mBAAoBhG,GAAI,EACrC,QAAQ,MAAMgG,CAAC,EACTA,MAGP,eAAQ,MAAK,mBAAoBhG,GAAI,EACrC,QAAQ,MAAMgG,CAAC,EACTA,GAKL,IAAAqI,MAAoB,IAIpBC,OAAqB,IAKV,eAAAC,GACdxP,EACAyP,EACAxE,EACAhG,EAGC,CAEDwD,EAAA,EAAAiH,GAAQ,EAAK,EACT,IAAAC,EAAW1E,EAAajL,EAAK,EAAE,EAE7B,MAAA4P,UAAoB3K,EAAc,IACpC,GAAA0K,EAAS,QAAQF,EAAS,IAAIzP,EAAK,EAAE,GAAK,aAC1C,UACJ2P,EAAS,UAAYC,GAAW,QAE5B5P,EAAK,WACR2P,EAAS,SAAW3P,EAAK,SAAS,IAAK2M,GAAM1B,EAAa0B,EAAE,EAAE,GACxD,cAAQ,IACb3M,EAAK,SAAS,IAAK2M,GAClB6C,GAAY7C,EAAG8C,EAAUxE,EAAchG,CAAa,KAM7C,UAAAyK,GAAQ,EAAK,EAAAvL,EACb,iBAAA0L,GAAkB,EAAK,EAAA1L,WAIzB2L,IAAkB,CAC1BC,EAAAtH,EAAA,GAAAoG,EAAiBC,GAA2B,IAE5C5D,EAAa,QAAS,CAAAyB,EAAG9H,IAAC,CACzBgK,EAAe,SAAShK,EAAG8H,EAAE,OAAQA,EAAE,OAAO,IAGzC,MAAAqD,MAAmB,IACd,UAAAC,KAAQ9B,EAAU,OACpB,GAAA9J,EAAI,MAAA2H,EAAK,EAAKiE,GACLtC,GAAOtJ,EAAI,SAAU6G,CAAY,GAG/C,CAAAyC,GAAOtJ,EAAI,UAAW6G,CAAY,GACnC6C,GAAqB/B,IAAO,KAAK,IAElCgE,EAAa,IAAI3L,CAAE,EAIrBoE,EAAA,GAAAyD,EAAc8D,CAAY,QAEpBE,EAAS,CACd,GAAI9B,EAAO,GACX,KAAM,SACN,MAAK,CAAI,KAAM,QAAQ,EACvB,UAAW,GACX,SAAU,KACV,UAAW,MAEZD,EAAW,KAAK+B,CAAS,EACnB,MAAAC,MAAqB,IAGrBC,MAAsB,IAItBC,MAAoB,IACpBC,GAAgBnC,EAAW,OAC/B,CAAAoC,EAAKC,KACLD,EAAIC,EAAK,EAAE,EAAIA,EACRD,OAITpC,EAAW,QAAStB,GAAC,IACfA,EAAE,MAAc,cAAgB,GACnCA,EAAE,MAAc,KAAO,SACbA,EAAE,MAAc,cAAgB,IAEjCX,EAAY,IAAIW,EAAE,EAAE,EAD7BA,EAAE,MAAc,KAAO,cAIvBA,EAAE,MAAc,KAAO,SAGpBA,EAAE,MAAc,WAAU,KAC1B4D,GAAM,GACT5D,EAAE,MAAc,WAAW,QAAS3M,IAAU,CAC9CuQ,GAAOvQ,EAAE,WAAcwQ,MAClBA,GAAK,SAAW,IACnBA,GAAOA,GAAK,CAAC,GAEF,MAASvF,EAAI,iBAAiB0B,EAAE,GAAI3M,GAAIwQ,EAAI,KAIzD7D,EAAE,MAAc,OAAS4D,GAE3BJ,EAAc,IAAIxD,EAAE,GAAIA,EAAE,MAAM,IAAI,QAE9B8D,EAAKvB,EAAevC,EAAE,KAAMA,EAAE,MAAM,IAAI,EAC9CsD,EAAe,IAAIQ,CAAE,EACrBP,EAAgB,IAAO,GAAAvD,EAAE,QAAQA,EAAE,MAAM,OAAQ8D,CAAE,IAGpD,QAAQ,IAAI,MAAM,KAAKR,CAAc,GAAG,KAAI,KAC3CX,GAAYpB,EAAQiC,EAAeC,GAAeF,CAAe,EAC/D,KAAI,UACJ3H,EAAA,EAAAiH,GAAQ,EAAI,EACZJ,EAAgBa,EAChBZ,GAAiBa,EACjB3H,EAAA,GAAAwC,GAAeqF,EAAa,EAC5B7H,EAAA,GAAAsE,EAAWmD,CAAS,CAEpB,SAAOjJ,GAAC,CACR,QAAQ,MAAMA,CAAC,qBAKJ2J,GACdjB,EACAN,EAA0C,CAEtC,IAAAwB,EACHxB,IAAS,UAAY,cAAgBA,EAElC,GAAAM,EAAS,MAAM,OAASkB,EAAQ,OAEpClB,EAAS,MAAM,KAAOkB,QAChBF,EAAKvB,EAAeO,EAAS,KAAMA,EAAS,MAAM,IAAI,EAC5DL,EAAc,IAAIqB,CAAE,EACpBpB,GAAe,IACX,GAAAI,EAAS,QAAQA,EAAS,MAAM,OACnCgB,CAGE,EAGHA,EAAG,KAAM9D,GAAC,CACT8C,EAAS,UAAY9C,EAAE,UAAU,2BAK1BiE,GAAc9J,EAAW+B,EAAgB,CAC3C,MAAAgI,EAAU7F,EAAanC,CAAQ,EAAE,QACvC/B,GAAM,QAAS,CAAAnB,EAAYhB,IAAS,CAC7B,MAAAmM,GAAS/F,GAAa8F,EAAQlM,CAAC,MACrCmM,GAAO,MAAM,gBAAkB,UAEvBnL,GAAU,UACjBA,IAAU,MACVA,EAAM,WAAa,SAEP,UAAAoL,EAAYC,CAAY,IAAK,OAAO,QAAQrL,CAAK,EACxDoL,IAAe,aAGdA,IAAe,QAClBL,GACCI,GACAE,CAAoC,EAGtCF,GAAO,MAAMC,CAAU,EAAIC,QAI7BF,GAAO,MAAM,MAAQnL,YAMpB,IAAAsL,OAA6D,IAExD,SAAAC,GACRC,EACAC,EACAC,EAAQ,CAEHF,GAAK,QAETA,EAAI,MAAK,IAEVA,EAAI,MAAMC,CAAI,EAAIC,cAGfC,GAAoB,GAEpBxM,GAAQ,GACH,SAAAyM,GACRvN,EACA6E,EACA3E,EAA0B,CAGzB,eAAAF,EACA,SAAA6E,EACA,KAAA3E,EACA,GAAM,EAAAsN,IAIJ,IAAAA,MAEAC,GAAiB,GACrB,SAAS,iBAAiB,mBAAkB,WACvC,SAAS,kBAAoB,WAChCA,GAAiB,YAMbC,GAAoBC,EAAG,4BAA4B,EACnDC,GAAuBD,EAAG,6BAA6B,EACvDE,GAA2BF,EAAG,wBAAwB,EAGtDG,GACL,iEAAiE,KAChE,UAAU,SAAS,EAEjB,IAAAC,GAA2B,GAC3BC,GAAwB,GAEb,eAAAC,GACdC,EACAC,EAAsB,KAAI,KAEtBxE,EAAM3C,EAAakH,CAAS,EAC1B,MAAAE,EAAiBzD,EAAe,kBAAkBuD,CAAS,EAY7D,QAXJpN,GAAWA,GAAS,SAAU,SAAA+D,KAAeA,IAAaqJ,CAAS,GAC/DvE,EAAI,eACD,QAAQ,IACbA,EAAI,QAAQ,IAAG,MAAQ9E,GAAQ,CACxB,MAAAwJ,EAAapB,GAAW,IAAIpI,CAAQ,EAC1C,OAAAwJ,GAAY,OAAM,EACXA,KAKND,IAAmB,WAAaA,IAAmB,wBAInDE,EAAO,CACV,SAAUJ,EACV,KAAMvE,EAAI,OAAO,IAAKxJ,GAAO4G,GAAa5G,CAAE,EAAE,MAAM,KAAK,EACzD,WAAYwJ,EAAI,oBAAsBwE,EAAa,MAGhDxE,EAAI,YACPA,EACE,YACA2E,EAAQ,KAAK,OACZ3E,EAAI,QAAQ,IAAKxJ,GAAO4G,GAAa5G,CAAE,EAAE,MAAM,KAAK,IAGrD,KAAMsI,GAAY,CACdkB,EAAI,YACP2E,EAAQ,KAAO7F,EACf8F,MAEA3B,GAAcnE,EAAGyF,CAAS,IAIzBvE,EAAI,YACP4E,cAIOA,IAAe,OACjBF,EAAapH,EACjB,OAAOqH,EAAQ,SAAUA,EAAQ,KAAmBA,EAAQ,UAAU,EACtE,GAAG,OAAM,EAAK,KAAAxL,EAAM,SAAA+B,MAAQ,CAC5B+H,GAAc9J,EAAM+B,EAAQ,CAE5B,MAAG,SAAa,WAAAA,KAAa2J,MAAM,CACnC7R,GAAI,EAAG,KAAI,KA4CN,GA1CJgO,EAAe,OAAM,IACjB6D,GACH,OAAQA,GAAO,MACf,SAAUA,GAAO,cACjB,SAAA3J,KAGCkJ,IACDrD,IAAa,MACb8D,GAAO,WAAa,QACpBA,GAAO,UAAY,GACnBA,GAAO,MAAQ,QACfA,GAAO,IAAMjF,KAEbwE,GAA2B,QAC3BjN,GAAQ,CACPyM,GAAYG,GAAmB7I,EAAU,SAAS,EAC/C,GAAA/D,EAAA,IAIH,CAAAkN,IACDF,IACAU,GAAO,MAAQ,QACfA,GAAO,IAAMhF,KAEbwE,GAAwB,QACxBlN,GAAQ,CACPyM,GAAYK,GAAsB/I,EAAU,SAAS,EAClD,GAAA/D,EAAA,IAID0N,GAAO,QAAU,aACpBxH,EAAa,IAAW,MAAA2C,GAAKhJ,KAAC,CACzBgJ,GAAI,gBAAkB9E,GACzBoJ,GAAiBtN,EAAC,IAIpB0N,EAAW,QAAO,GAEfG,GAAO,QAAUV,IAAoBL,GACxC,OAAO,qBACN3M,GAAQ,CACPyM,GAAYM,GAA0BhJ,EAAU,OAAO,EACpD,GAAA/D,MAEF,GACHmN,GAAiBC,EAAWC,CAAU,EACtCV,GAAiB,WACPe,GAAO,QAAU,QAAO,CAC9B,GAAAA,GAAO,QAAO,CACX,MAAAC,GAAWD,GAAO,QAAQ,QAC/BlF,GACC,CAAAoF,GAAG7Q,KAAMA,EAAC,OAEZiD,GAAQ,CACPyM,GAAYkB,GAAU5J,EAAU,OAAO,EACpC,GAAA/D,EAAA,GAGLkG,EAAa,IAAW,MAAA2C,GAAKhJ,KAAC,CAE5BgJ,GAAI,gBAAkB9E,GACrB,CAAA8E,GAAI,yBAELsE,GAAiBtN,EAAC,IAIpB0N,EAAW,QAAO,IAIpB,MAAG,MAAK,EAAK,IAAAM,EAAK,SAAA9J,GAAU,MAAA+J,MAAK,MACjC9N,GAAQ,CAAIyM,GAAYoB,EAAK9J,GAAU+J,EAAK,KAAM9N,EAAQ,KAG5DmM,GAAW,IAAIiB,EAAWG,CAAU,YAI7BQ,GAAc1E,EAA2B2E,EAAmB,CAChE,GAAApE,IAAa,kBAGXqE,EAAc,IAAO,IAAG,iCACIrE,mBAAQ,EAEtCP,IAAU,QAAaA,EAAM,OAAS,GACzC4E,EAAe,aAAa,IAAI,QAAS5E,CAAK,EAE/C4E,EAAe,aAAa,IAAI,cAAeD,CAAW,EAC1D,OAAO,KAAKC,EAAe,WAAY,QAAQ,EAGvC,SAAAC,GAAmBjM,EAA6B,OAClDoE,EAAMpE,EAAE,YACdjC,GAAWA,GAAS,OAAQmO,GAAMA,EAAE,KAAO9H,CAAG,GAGzC,MAAA+H,GAAmBC,GAAmB,GACxCA,GAAI,IAAQ,IAAIA,EAAM,SAAS,IAAI,EAAE,SAAW,SAAS,uBAoB9CC,IAAY,OACpBzS,GAAI,UAENgB,EAAIkB,EAAO,qBAAqB,GAAG,EAE9B8B,EAAI,EAAGA,EAAIhD,EAAE,OAAQgD,IAAC,CACxB,MAAA0O,EAAU1R,EAAEgD,CAAC,EAAE,aAAa,QAAQ,EACpC2O,EAAQ3R,EAAEgD,CAAC,EAAE,aAAa,MAAM,EAGlCuO,GAAgBI,CAAK,GAAKD,IAAY,UACzC1R,EAAEgD,CAAC,EAAE,aAAa,SAAU,QAAQ,EAItCqG,EAAa,QAAS,CAAA2C,EAAKhJ,IAAC,CACvBgJ,EAAI,QAAQ,SAAW,GAAKA,EAAI,QAAQ,CAAC,EAAE,CAAC,IAAM,QACrDsE,GAAiBtN,CAAC,IAIpB9B,EAAO,iBAAiB,SAAWkE,GAAQ,CACrC,IAAA+G,GAAc/G,CAAC,EAAa,gBAAM,oBAAoB,EAEnD,SAAA5C,EAAI,MAAA4J,EAAO,KAAAjH,EAAI,EAAKC,EAAE,OAE1B,GAAAgH,IAAU,QAAO,OACZ,MAAAI,EAAO,YAAA2E,CAAW,EAAKhM,GAC/B+L,GAAc1E,EAAO2E,CAAW,OACtB/E,IAAU,aACpBjJ,GAAQ,CAAIyM,GAAYzK,MAAU,OAAO,KAAMhC,EAAQ,GAE1CyO,EAAWpP,CAAE,IAAI4J,CAAK,GAC7B,QAASyF,GAAM,CACpBvB,GAAiBuB,EAAQ1M,EAAI,MAKhCyB,EAAA,GAAAoH,GAAkB,EAAI,EAGd,SAAA8D,GAAetP,EAAU,CACjCmN,GAAuBA,GAAqB,IAAK3D,GACzCA,EAAI,OAAQxC,GAAQA,IAAQhH,CAAE,GAM9B,SAAAuP,GAAWC,EAAiC,CACzC,UAAAxP,KAAMwP,EAAQ,KACpBhF,EAAiBgF,EAASxP,CAAE,EAC5BsF,EAAauB,EAAa2D,EAAe,QAAQ,EACrDA,EAAe,iBAAmBlF,EAAW,iBAC7CkF,EAAe,cAAgBlF,EAAW,cAE1CyH,GAASnG,GAAa5G,CAAE,EAAG,iBAAkBwK,CAAc,QAEtDiF,EAAmBjF,EAAe,iCAC5BxK,EAAI0P,CAAc,IAAKD,EAClC1C,GAASnG,GAAa5G,CAAE,EAAG,UAAW0P,IAAmB,SAAS,aAyClD,OAAAC,CAAM,IAAOL,GAAeK,CAAM,UAW/C9E,GAAsBD,CAAgB,WA8BxCC,EAAqB,EAAK,WAMzBA,EAAqB,EAAK,qtBAvqB3B+E,GAAU,OAAQC,IAAC,IAAWA,EAAG,WAAA3F,CAAU,6BAmJvBuB,GAAkB,oBA6WtC2D,EAAavI,EAAa,QAC3BqF,EAAK1C,EAAKhJ,KACVgJ,EAAI,QAAQ,QAAU,EAAAxJ,EAAI8P,CAAO,KAC3B5D,EAAIlM,CAAE,IACVkM,EAAIlM,CAAE,MAEHkM,EAAIlM,CAAE,IAAI8P,CAAO,EACpB5D,EAAIlM,CAAE,EAAE8P,CAAO,EAAE,KAAKtP,CAAC,EAEvB0L,EAAIlM,CAAE,EAAE8P,CAAO,GAAKtP,CAAC,IAIhB0L,iCAqDNqD,GAAWQ,CAAe", "names": ["create_animation", "node", "from", "fn", "params", "noop", "to", "delay", "duration", "easing", "linear", "start_time", "now", "end", "tick", "css", "running", "started", "name", "start", "create_rule", "stop", "delete_rule", "loop", "p", "t", "fix_position", "style", "width", "height", "a", "add_transform", "b", "transform", "flip", "ox", "oy", "dx", "dy", "d", "cubicOut", "is_function", "u", "x", "y", "sx", "sy", "insert", "target", "svg", "anchor", "append", "path", "ctx", "div5", "div0", "div3", "div1", "div2", "button", "span", "div4", "div5_intro", "create_in_transition", "fade", "div5_outro", "create_out_transition", "message", "$$props", "type", "id", "dispatch", "createEventDispatcher", "close_message", "onMount", "div", "stop_animation", "rect", "i", "scroll_to_top", "_messages", "messages", "component_map", "__vitePreload", "g", "path0", "path1", "h1", "p0", "code0", "p1", "root", "click_handler", "represent_value", "value", "lang", "api_logo", "if_block", "create_if_block", "attr", "img", "img_src_value", "h2", "span1", "span0", "api_count", "Gradio", "#id", "#el", "el", "theme", "version", "event_name", "data", "e", "create_if_block_5", "set_data", "t_value", "create_if_block_4", "dirty", "t1", "t1_value", "create_if_block_3", "create_if_block_2", "t2", "t2_value", "t4", "t4_value", "create_if_block_6", "create_if_block_1", "h4", "is_running", "endpoint_returns", "js_returns", "current_language", "code", "copy_text", "copy", "$$invalidate", "js_install", "pre", "py_install", "h3", "api_name", "fn_index", "named", "endpointdetail_changes", "create_if_block_7", "copybutton_changes", "t7", "t7_value", "t9", "t9_value", "t5", "t5_value", "if_block1", "dependency", "dependency_index", "dependency_failures", "endpoint_parameters", "js_parameters", "python_code", "js_code", "blob_components", "blob_examples", "param", "$$value", "python", "javascript", "show_if", "show_if_1", "apibanner_changes", "each_blocks_1", "each_blocks", "li", "li_class_value", "codesnippets_changes", "responseobject_changes", "instance_map", "dependencies", "app", "langs", "_id", "default_data", "get_info", "get_js_info", "info", "js_info", "js_api_info", "language", "switch_instance_props", "switch_instance_changes", "component", "props", "children", "dynamic_ids", "parent", "theme_mode", "filtered_children", "child", "setContext", "handle_prop_change", "k", "$$self", "v", "valid_node", "c", "render_changes", "rootNode", "logo", "script0", "script1", "img_alt_value", "footer", "t0_value", "t0", "if_block4", "MESSAGE_QUOTE_RE", "SHOW_DUPLICATE_MESSAGE_ON_ETA", "SHOW_MOBILE_QUEUE_WARNING_ON_ETA", "is_dep", "deps", "dep", "dep_item", "has_no_default_value", "isCustomEvent", "event", "setupi18n", "components", "layout", "title", "analytics_enabled", "autoscroll", "show_api", "show_footer", "control_page_title", "app_mode", "space_id", "loading_status", "create_loading_status_store", "AsyncFunction", "wrap", "api_docs_visible", "set_api_docs_visible", "visible", "load_component", "mode", "component_set", "_component_map", "walk_layout", "type_map", "ready", "instance", "_component", "render_complete", "prepare_components", "$$subscribe_loading_status", "_dynamic_ids", "comp", "_rootNode", "_component_set", "__component_map", "__type_for_id", "_instance_map", "acc", "next", "server", "args", "_c", "update_interactive_mode", "new_mode", "handle_update", "outputs", "output", "update_key", "update_value", "submit_map", "set_prop", "obj", "prop", "val", "handled_dependencies", "new_message", "_error_id", "user_left_page", "DUPLICATE_MESSAGE", "$_", "MOBILE_QUEUE_WARNING", "MOBILE_RECONNECT_MESSAGE", "is_mobile_device", "showed_duplicate_message", "showed_mobile_warning", "trigger_api_call", "dep_index", "event_data", "current_status", "submission", "payload", "make_prediction", "status", "_message", "_", "log", "level", "trigger_share", "description", "discussion_url", "handle_error_close", "m", "is_external_url", "link", "handle_mount", "_target", "_link", "target_map", "dep_id", "handle_destroy", "set_status", "statuses", "inputs_to_update", "pending_status", "detail", "app_state", "s", "trigger", "$loading_status"], "sources": ["../../../../node_modules/.pnpm/svelte@4.0.0/node_modules/svelte/src/runtime/internal/animations.js", "../../../../node_modules/.pnpm/svelte@4.0.0/node_modules/svelte/src/runtime/animate/index.js", "../../../../js/icons/src/Error.svelte", "../../../../js/icons/src/Info.svelte", "../../../../js/icons/src/Warning.svelte", "../../../../js/statustracker/static/ToastContent.svelte", "../../../../js/statustracker/static/Toast.svelte", "../../../../js/app/src/components/directory.ts", "../../../../js/app/src/api_docs/img/clear.svelte", "../../../../js/app/src/api_docs/NoApi.svelte", "../../../../js/app/src/api_docs/utils.ts", "../../../../js/app/src/api_docs/img/api-logo.svg", "../../../../js/app/src/api_docs/ApiBanner.svelte", "../../../../js/app/src/gradio_helper.ts", "../../../../js/app/src/api_docs/ResponseObject.svelte", "../../../../js/app/src/api_docs/CopyButton.svelte", "../../../../js/app/src/api_docs/InstallSnippet.svelte", "../../../../js/app/src/api_docs/EndpointDetail.svelte", "../../../../js/app/src/api_docs/CodeSnippets.svelte", "../../../../js/app/src/api_docs/img/python.svg", "../../../../js/app/src/api_docs/img/javascript.svg", "../../../../js/app/src/api_docs/ApiDocs.svelte", "../../../../js/app/src/Render.svelte", "../../../../js/app/src/MountComponents.svelte", "../../../../js/app/src/images/logo.svg", "../../../../js/app/src/Blocks.svelte"], "sourcesContent": ["import { identity as linear, noop } from './utils.js';\nimport { now } from './environment.js';\nimport { loop } from './loop.js';\nimport { create_rule, delete_rule } from './style_manager.js';\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {import('./private.js').PositionRect} from\n * @param {import('./private.js').AnimationFn} fn\n */\nexport function create_animation(node, from, fn, params) {\n\tif (!from) return noop;\n\tconst to = node.getBoundingClientRect();\n\tif (\n\t\tfrom.left === to.left &&\n\t\tfrom.right === to.right &&\n\t\tfrom.top === to.top &&\n\t\tfrom.bottom === to.bottom\n\t)\n\t\treturn noop;\n\tconst {\n\t\tdelay = 0,\n\t\tduration = 300,\n\t\teasing = linear,\n\t\t// @ts-ignore todo: should this be separated from destructuring? Or start/end added to public api and documentation?\n\t\tstart: start_time = now() + delay,\n\t\t// @ts-ignore todo:\n\t\tend = start_time + duration,\n\t\ttick = noop,\n\t\tcss\n\t} = fn(node, { from, to }, params);\n\tlet running = true;\n\tlet started = false;\n\tlet name;\n\t/** @returns {void} */\n\tfunction start() {\n\t\tif (css) {\n\t\t\tname = create_rule(node, 0, 1, duration, delay, easing, css);\n\t\t}\n\t\tif (!delay) {\n\t\t\tstarted = true;\n\t\t}\n\t}\n\t/** @returns {void} */\n\tfunction stop() {\n\t\tif (css) delete_rule(node, name);\n\t\trunning = false;\n\t}\n\tloop((now) => {\n\t\tif (!started && now >= start_time) {\n\t\t\tstarted = true;\n\t\t}\n\t\tif (started && now >= end) {\n\t\t\ttick(1, 0);\n\t\t\tstop();\n\t\t}\n\t\tif (!running) {\n\t\t\treturn false;\n\t\t}\n\t\tif (started) {\n\t\t\tconst p = now - start_time;\n\t\t\tconst t = 0 + 1 * easing(p / duration);\n\t\t\ttick(t, 1 - t);\n\t\t}\n\t\treturn true;\n\t});\n\tstart();\n\ttick(0, 1);\n\treturn stop;\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @returns {void}\n */\nexport function fix_position(node) {\n\tconst style = getComputedStyle(node);\n\tif (style.position !== 'absolute' && style.position !== 'fixed') {\n\t\tconst { width, height } = style;\n\t\tconst a = node.getBoundingClientRect();\n\t\tnode.style.position = 'absolute';\n\t\tnode.style.width = width;\n\t\tnode.style.height = height;\n\t\tadd_transform(node, a);\n\t}\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {import('./private.js').PositionRect} a\n * @returns {void}\n */\nexport function add_transform(node, a) {\n\tconst b = node.getBoundingClientRect();\n\tif (a.left !== b.left || a.top !== b.top) {\n\t\tconst style = getComputedStyle(node);\n\t\tconst transform = style.transform === 'none' ? '' : style.transform;\n\t\tnode.style.transform = `${transform} translate(${a.left - b.left}px, ${a.top - b.top}px)`;\n\t}\n}\n", "import { cubicOut } from '../easing/index.js';\nimport { is_function } from '../internal/index.js';\n\n/**\n * The flip function calculates the start and end position of an element and animates between them, translating the x and y values.\n * `flip` stands for [First, Last, Invert, Play](https://aerotwist.com/blog/flip-your-animations/).\n *\n * https://svelte.dev/docs/svelte-animate#flip\n * @param {Element} node\n * @param {{ from: DOMRect; to: DOMRect }} fromTo\n * @param {import('./public.js').FlipParams} params\n * @returns {import('./public.js').AnimationConfig}\n */\nexport function flip(node, { from, to }, params = {}) {\n\tconst style = getComputedStyle(node);\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst [ox, oy] = style.transformOrigin.split(' ').map(parseFloat);\n\tconst dx = from.left + (from.width * ox) / to.width - (to.left + ox);\n\tconst dy = from.top + (from.height * oy) / to.height - (to.top + oy);\n\tconst { delay = 0, duration = (d) => Math.sqrt(d) * 120, easing = cubicOut } = params;\n\treturn {\n\t\tdelay,\n\t\tduration: is_function(duration) ? duration(Math.sqrt(dx * dx + dy * dy)) : duration,\n\t\teasing,\n\t\tcss: (t, u) => {\n\t\t\tconst x = u * dx;\n\t\t\tconst y = u * dy;\n\t\t\tconst sx = t + (u * from.width) / to.width;\n\t\t\tconst sy = t + (u * from.height) / to.height;\n\t\t\treturn `transform: ${transform} translate(${x}px, ${y}px) scale(${sx}, ${sy});`;\n\t\t}\n\t};\n}\n", "<svg\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\taria-hidden=\"true\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z\"\n\t/>\n</svg>\n", "<svg\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\taria-hidden=\"true\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z\"\n\t/>\n</svg>\n", "<svg\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\taria-hidden=\"true\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { Error, Info, Warning } from \"@gradio/icons\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport type { ToastMessage } from \"./types\";\n\n\texport let message = \"\";\n\texport let type: ToastMessage[\"type\"];\n\texport let id: number;\n\n\tconst dispatch = createEventDispatcher();\n\n\tfunction close_message(): void {\n\t\tdispatch(\"close\", id);\n\t}\n\n\tonMount(() => {\n\t\tsetTimeout(() => {\n\t\t\tclose_message();\n\t\t}, 10000);\n\t});\n</script>\n\n<!-- TODO: fix-->\n<!-- svelte-ignore a11y-no-noninteractive-element-interactions-->\n<div\n\tclass=\"toast-body {type}\"\n\trole=\"alert\"\n\tdata-testid=\"toast-body\"\n\ton:click|stopPropagation\n\ton:keydown|stopPropagation\n\tin:fade={{ duration: 200, delay: 100 }}\n\tout:fade={{ duration: 200 }}\n>\n\t<div class=\"toast-icon {type}\">\n\t\t{#if type === \"warning\"}\n\t\t\t<Warning />\n\t\t{:else if type === \"info\"}\n\t\t\t<Info />\n\t\t{:else if type === \"error\"}\n\t\t\t<Error />\n\t\t{/if}\n\t</div>\n\n\t<div class=\"toast-details {type}\">\n\t\t<div class=\"toast-title {type}\">{type}</div>\n\t\t<div class=\"toast-text {type}\">\n\t\t\t{message}\n\t\t</div>\n\t</div>\n\n\t<button\n\t\ton:click={close_message}\n\t\tclass=\"toast-close {type}\"\n\t\ttype=\"button\"\n\t\taria-label=\"Close\"\n\t\tdata-testid=\"toast-close\"\n\t>\n\t\t<span aria-hidden=\"true\">&#215;</span>\n\t</button>\n\n\t<div class=\"timer {type}\" />\n</div>\n\n<style>\n\t.toast-body {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tright: 0;\n\t\tleft: 0;\n\t\talign-items: center;\n\t\tmargin: var(--size-6) var(--size-4);\n\t\tmargin: auto;\n\t\tborder-radius: var(--container-radius);\n\t\toverflow: hidden;\n\t\tpointer-events: auto;\n\t}\n\n\t.toast-body.error {\n\t\tborder: 1px solid var(--color-red-700);\n\t\tbackground: var(--color-red-50);\n\t}\n\t:global(.dark) .toast-body.error {\n\t\tborder: 1px solid var(--color-red-500);\n\t\tbackground-color: var(--color-grey-950);\n\t}\n\n\t.toast-body.warning {\n\t\tborder: 1px solid var(--color-yellow-700);\n\t\tbackground: var(--color-yellow-50);\n\t}\n\t:global(.dark) .toast-body.warning {\n\t\tborder: 1px solid var(--color-yellow-500);\n\t\tbackground-color: var(--color-grey-950);\n\t}\n\n\t.toast-body.info {\n\t\tborder: 1px solid var(--color-grey-700);\n\t\tbackground: var(--color-grey-50);\n\t}\n\t:global(.dark) .toast-body.info {\n\t\tborder: 1px solid var(--color-grey-500);\n\t\tbackground-color: var(--color-grey-950);\n\t}\n\n\t.toast-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-lg);\n\t\tline-height: var(--line-sm);\n\t\ttext-transform: capitalize;\n\t}\n\n\t.toast-title.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\t:global(.dark) .toast-title.error {\n\t\tcolor: var(--color-red-50);\n\t}\n\n\t.toast-title.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\t:global(.dark) .toast-title.warning {\n\t\tcolor: var(--color-yellow-50);\n\t}\n\n\t.toast-title.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\t:global(.dark) .toast-title.info {\n\t\tcolor: var(--color-grey-50);\n\t}\n\n\t.toast-close {\n\t\tmargin: 0 var(--size-3);\n\t\tborder-radius: var(--size-3);\n\t\tpadding: 0px var(--size-1-5);\n\t\tfont-size: var(--size-5);\n\t\tline-height: var(--size-5);\n\t}\n\n\t.toast-close.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\t:global(.dark) .toast-close.error {\n\t\tcolor: var(--color-red-500);\n\t}\n\n\t.toast-close.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\t:global(.dark) .toast-close.warning {\n\t\tcolor: var(--color-yellow-500);\n\t}\n\n\t.toast-close.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\t:global(.dark) .toast-close.info {\n\t\tcolor: var(--color-grey-500);\n\t}\n\n\t.toast-text {\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.toast-text.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\t:global(.dark) .toast-text.error {\n\t\tcolor: var(--color-red-50);\n\t}\n\n\t.toast-text.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\n\t:global(.dark) .toast-text.warning {\n\t\tcolor: var(--color-yellow-50);\n\t}\n\n\t.toast-text.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\n\t:global(.dark) .toast-text.info {\n\t\tcolor: var(--color-grey-50);\n\t}\n\n\t.toast-details {\n\t\tmargin: var(--size-3) var(--size-3) var(--size-3) 0;\n\t\twidth: 100%;\n\t}\n\n\t.toast-icon {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tposition: relative;\n\t\tflex-shrink: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tpadding: var(--size-1);\n\t\tpadding-left: calc(var(--size-1) - 1px);\n\t\twidth: 35px;\n\t\theight: 35px;\n\t}\n\n\t.toast-icon.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\n\t:global(.dark) .toast-icon.error {\n\t\tcolor: var(--color-red-500);\n\t}\n\n\t.toast-icon.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\n\t:global(.dark) .toast-icon.warning {\n\t\tcolor: var(--color-yellow-500);\n\t}\n\n\t.toast-icon.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\n\t:global(.dark) .toast-icon.info {\n\t\tcolor: var(--color-grey-500);\n\t}\n\n\t@keyframes countdown {\n\t\tfrom {\n\t\t\ttransform: scaleX(1);\n\t\t}\n\t\tto {\n\t\t\ttransform: scaleX(0);\n\t\t}\n\t}\n\n\t.timer {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\ttransform-origin: 0 0;\n\t\tanimation: countdown 10s linear forwards;\n\t\twidth: 100%;\n\t\theight: var(--size-1);\n\t}\n\n\t.timer.error {\n\t\tbackground: var(--color-red-700);\n\t}\n\n\t:global(.dark) .timer.error {\n\t\tbackground: var(--color-red-500);\n\t}\n\n\t.timer.warning {\n\t\tbackground: var(--color-yellow-700);\n\t}\n\n\t:global(.dark) .timer.warning {\n\t\tbackground: var(--color-yellow-500);\n\t}\n\n\t.timer.info {\n\t\tbackground: var(--color-grey-700);\n\t}\n\n\t:global(.dark) .timer.info {\n\t\tbackground: var(--color-grey-500);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { flip } from \"svelte/animate\";\n\timport type { ToastMessage } from \"./types\";\n\timport ToastContent from \"./ToastContent.svelte\";\n\n\texport let messages: ToastMessage[] = [];\n\n\t$: scroll_to_top(messages);\n\n\tfunction scroll_to_top(_messages: ToastMessage[]): void {\n\t\tif (_messages.length > 0) {\n\t\t\tif (\"parentIFrame\" in window) {\n\t\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<div class=\"toast-wrap\">\n\t{#each messages as { type, message, id } (id)}\n\t\t<div animate:flip={{ duration: 300 }} style:width=\"100%\">\n\t\t\t<ToastContent {type} {message} on:close {id} />\n\t\t</div>\n\t{/each}\n</div>\n\n<style>\n\t.toast-wrap {\n\t\tdisplay: flex;\n\t\tposition: fixed;\n\t\ttop: var(--size-4);\n\t\tright: var(--size-4);\n\n\t\tflex-direction: column;\n\t\talign-items: end;\n\t\tgap: var(--size-2);\n\t\tz-index: var(--layer-top);\n\t\twidth: calc(100% - var(--size-8));\n\t}\n\n\t@media (--screen-sm) {\n\t\t.toast-wrap {\n\t\t\twidth: calc(var(--size-96) + var(--size-10));\n\t\t}\n\t}\n</style>\n", "export const component_map = {\n\taccordion: {\n\t\tstatic: () => import(\"@gradio/accordion/static\")\n\t},\n\tannotatedimage: {\n\t\tstatic: () => import(\"@gradio/annotatedimage/static\")\n\t},\n\taudio: {\n\t\tstatic: () => import(\"@gradio/audio/static\"),\n\t\tinteractive: () => import(\"@gradio/audio/interactive\")\n\t},\n\tbox: {\n\t\tstatic: () => import(\"@gradio/box/static\")\n\t},\n\tbutton: {\n\t\tstatic: () => import(\"@gradio/button/static\")\n\t},\n\tchatbot: {\n\t\tstatic: () => import(\"@gradio/chatbot/static\")\n\t},\n\tcheckbox: {\n\t\tstatic: () => import(\"@gradio/checkbox/static\"),\n\t\tinteractive: () => import(\"@gradio/checkbox/interactive\")\n\t},\n\tcheckboxgroup: {\n\t\tstatic: () => import(\"@gradio/checkboxgroup/static\"),\n\t\tinteractive: () => import(\"@gradio/checkboxgroup/interactive\")\n\t},\n\tcode: {\n\t\tstatic: () => import(\"@gradio/code/static\"),\n\t\tinteractive: () => import(\"@gradio/code/interactive\")\n\t},\n\tcolorpicker: {\n\t\tstatic: () => import(\"@gradio/colorpicker/static\"),\n\t\tinteractive: () => import(\"@gradio/colorpicker/interactive\")\n\t},\n\tcolumn: {\n\t\tstatic: () => import(\"@gradio/column/static\")\n\t},\n\tdataframe: {\n\t\tstatic: () => import(\"@gradio/dataframe/static\"),\n\t\tinteractive: () => import(\"@gradio/dataframe/interactive\")\n\t},\n\tdataset: {\n\t\tstatic: () => import(\"./Dataset\")\n\t},\n\tdropdown: {\n\t\tstatic: () => import(\"@gradio/dropdown/static\"),\n\t\tinteractive: () => import(\"@gradio/dropdown/interactive\")\n\t},\n\tfile: {\n\t\tstatic: () => import(\"@gradio/file/static\"),\n\t\tinteractive: () => import(\"@gradio/file/interactive\")\n\t},\n\tform: {\n\t\tstatic: () => import(\"@gradio/form/static\")\n\t},\n\tgallery: {\n\t\tstatic: () => import(\"@gradio/gallery/static\")\n\t},\n\tgroup: {\n\t\tstatic: () => import(\"@gradio/group/static\")\n\t},\n\thighlightedtext: {\n\t\tstatic: () => import(\"@gradio/highlightedtext/static\"),\n\t\tinteractive: () => import(\"@gradio/highlightedtext/interactive\")\n\t},\n\tfileexplorer: {\n\t\tstatic: () => import(\"@gradio/fileexplorer/static\"),\n\t\tinteractive: () => import(\"@gradio/fileexplorer/interactive\")\n\t},\n\thtml: {\n\t\tstatic: () => import(\"@gradio/html/static\")\n\t},\n\timage: {\n\t\tstatic: () => import(\"@gradio/image/static\"),\n\t\tinteractive: () => import(\"@gradio/image/interactive\")\n\t},\n\tinterpretation: {\n\t\tstatic: () => import(\"./Interpretation\"),\n\t\tinteractive: () => import(\"./Interpretation\")\n\t},\n\tjson: {\n\t\tstatic: () => import(\"@gradio/json/static\")\n\t},\n\tlabel: {\n\t\tstatic: () => import(\"@gradio/label/static\")\n\t},\n\tmarkdown: {\n\t\tstatic: () => import(\"@gradio/markdown/static\")\n\t},\n\tmodel3d: {\n\t\tstatic: () => import(\"@gradio/model3d/static\"),\n\t\tinteractive: () => import(\"@gradio/model3d/interactive\")\n\t},\n\tnumber: {\n\t\tstatic: () => import(\"@gradio/number/static\"),\n\t\tinteractive: () => import(\"@gradio/number/interactive\")\n\t},\n\tplot: {\n\t\tstatic: () => import(\"@gradio/plot/static\")\n\t},\n\tradio: {\n\t\tstatic: () => import(\"@gradio/radio/static\"),\n\t\tinteractive: () => import(\"@gradio/radio/interactive\")\n\t},\n\trow: {\n\t\tstatic: () => import(\"@gradio/row/static\")\n\t},\n\tslider: {\n\t\tstatic: () => import(\"@gradio/slider/static\"),\n\t\tinteractive: () => import(\"@gradio/slider/interactive\")\n\t},\n\tstate: {\n\t\tstatic: () => import(\"./State\")\n\t},\n\tstatustracker: {\n\t\tstatic: () => import(\"@gradio/statustracker/static\")\n\t},\n\ttabs: {\n\t\tstatic: () => import(\"@gradio/tabs/static\")\n\t},\n\ttabitem: {\n\t\tstatic: () => import(\"@gradio/tabitem/static\")\n\t},\n\ttextbox: {\n\t\tstatic: () => import(\"@gradio/textbox/static\"),\n\t\tinteractive: () => import(\"@gradio/textbox/interactive\")\n\t},\n\ttimeseries: {\n\t\tstatic: () => import(\"@gradio/timeseries/static\"),\n\t\tinteractive: () => import(\"@gradio/timeseries/interactive\")\n\t},\n\tuploadbutton: {\n\t\tstatic: () => import(\"@gradio/uploadbutton/static\"),\n\t\tinteractive: () => import(\"@gradio/uploadbutton/interactive\")\n\t},\n\tvideo: {\n\t\tstatic: () => import(\"@gradio/video/static\"),\n\t\tinteractive: () => import(\"@gradio/video/interactive\")\n\t}\n};\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 5 5\"\n\tversion=\"1.1\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\txml:space=\"preserve\"\n\tstyle=\"fill:currentColor;fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;\"\n>\n\t<g>\n\t\t<path\n\t\t\td=\"M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z\"\n\t\t/>\n\t</g>\n</svg>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport Clear from \"./img/clear.svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let root: string;\n</script>\n\n<div class=\"wrap prose\">\n\t<h1>API Docs</h1>\n\t<p class=\"attention\">\n\t\tNo API Routes found for\n\t\t<code>\n\t\t\t{root}\n\t\t</code>\n\t</p>\n\t<p>\n\t\tTo expose an API endpoint of your app in this page, set the <code>\n\t\t\tapi_name\n\t\t</code>\n\t\tparameter of the event listener.\n\t\t<br />\n\t\tFor more information, visit the\n\t\t<a href=\"https://gradio.app/sharing_your_app/#api-page\" target=\"_blank\">\n\t\t\tAPI Page guide\n\t\t</a>\n\t\t. To hide the API documentation button and this page, set\n\t\t<code>show_api=False</code>\n\t\tin the\n\t\t<code>Blocks.launch()</code>\n\t\tmethod.\n\t</p>\n</div>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\t.wrap {\n\t\tpadding: var(--size-6);\n\t}\n\n\t.attention {\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.attention code {\n\t\tborder: none;\n\t\tbackground: none;\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\t}\n</style>\n", "export function represent_value(\n\tvalue: string,\n\ttype: string | undefined,\n\tlang: \"js\" | \"py\" | null = null\n): string | null | number | boolean | Record<string, unknown> {\n\tif (type === undefined) {\n\t\treturn lang === \"py\" ? \"None\" : null;\n\t}\n\tif (type === \"string\" || type === \"str\") {\n\t\treturn lang === null ? value : '\"' + value + '\"';\n\t} else if (type === \"number\") {\n\t\treturn lang === null ? parseFloat(value) : value;\n\t} else if (type === \"boolean\" || type == \"bool\") {\n\t\tif (lang === \"py\") {\n\t\t\tvalue = String(value);\n\t\t\treturn value === \"true\" ? \"True\" : \"False\";\n\t\t} else if (lang === \"js\") {\n\t\t\treturn value;\n\t\t}\n\t\treturn value === \"true\";\n\t} else if (type === \"List[str]\") {\n\t\tvalue = JSON.stringify(value);\n\t\treturn value;\n\t}\n\t// assume object type\n\tif (lang === null) {\n\t\treturn value === \"\" ? null : JSON.parse(value);\n\t} else if (typeof value === \"string\") {\n\t\tif (value === \"\") {\n\t\t\treturn lang === \"py\" ? \"None\" : \"null\";\n\t\t}\n\t\treturn value;\n\t}\n\treturn JSON.stringify(value);\n}\n", "export default \"__VITE_ASSET__ffd29fb0__\"", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport api_logo from \"./img/api-logo.svg\";\n\timport Clear from \"./img/clear.svelte\";\n\n\texport let root: string;\n\texport let api_count: number;\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<h2>\n\t<img src={api_logo} alt=\"\" />\n\t<div>\n\t\tAPI documentation\n\t\t<div class=\"url\">\n\t\t\t{root}\n\t\t</div>\n\t</div>\n\t<span class=\"counts\">\n\t\t<span class=\"url\">{api_count}</span> API endpoint{#if api_count > 1}s{/if}\n\t</span>\n</h2>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\th2 {\n\t\tdisplay: flex;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tgap: var(--size-4);\n\t}\n\n\th2 img {\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-4);\n\t\tdisplay: inline-block;\n\t}\n\n\t.url {\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: normal;\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\n\t\th2 img {\n\t\t\twidth: var(--size-5);\n\t\t}\n\t}\n\n\t.counts {\n\t\tmargin-top: auto;\n\t\tmargin-right: var(--size-8);\n\t\tmargin-bottom: auto;\n\t\tmargin-left: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-light);\n\t}\n</style>\n", "// import { _ } from \"svelte-i18n\";\n\nexport class Gradio<T extends Record<string, any>> {\n\t#id: number;\n\ttheme: string;\n\tversion: string;\n\t// i18n: typeof _;\n\t#el: HTMLElement;\n\troot: string;\n\n\tconstructor(\n\t\tid: number,\n\t\tel: HTMLElement,\n\t\ttheme: string,\n\t\tversion: string,\n\t\troot: string\n\t) {\n\t\tthis.#id = id;\n\t\tthis.theme = theme;\n\t\tthis.version = version;\n\t\tthis.#el = el;\n\t\t// this.i18n = _;\n\t\tthis.root = root;\n\t}\n\n\tdispatch<E extends keyof T>(event_name: E, data?: T[E]): void {\n\t\tconst e = new CustomEvent(\"gradio\", {\n\t\t\tbubbles: true,\n\t\t\tdetail: { data, id: this.#id, event: event_name }\n\t\t});\n\t\tthis.#el.dispatchEvent(e);\n\t}\n}\n", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot\" />\n\t</div>\n\tReturn Type(s)\n</h4>\n<Block>\n\t<div class=\"response-wrap\">\n\t\t<div class:hide={is_running}>\n\t\t\t{#if endpoint_returns.length > 1}({/if}\n\t\t\t{#each endpoint_returns as { label, type, python_type, component, serializer }, i}\n\t\t\t\t<div class:second-level={endpoint_returns.length > 1}>\n\t\t\t\t\t<span class=\"desc\"\n\t\t\t\t\t\t><!--\n\t\t\t\t\t--> # {#if current_language === \"python\"}{python_type.type}{#if python_type?.description}&nbsp;({python_type.description}){/if}{:else}{js_returns[\n\t\t\t\t\t\t\t\ti\n\t\t\t\t\t\t\t].type}{#if js_returns[\n\t\t\t\t\t\t\t\ti\n\t\t\t\t\t\t\t].description}&nbsp;({js_returns[\n\t\t\t\t\t\t\t\ti\n\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t\t\t\t\t.description}){/if}{/if}\n\t\t\t\t\t\t<!--\n\t\t\t\t\t-->representing output in '{label}' <!--\n\t\t\t\t\t-->{component}\n\t\t\t\t\t\tcomponent<!--\n\t\t\t\t\t--></span\n\t\t\t\t\t>{#if endpoint_returns.length > 1},{/if}\n\t\t\t\t</div>\n\t\t\t{/each}\n\t\t\t{#if endpoint_returns.length > 1}){/if}\n\t\t</div>\n\t\t{#if is_running}\n\t\t\t<div class=\"load-wrap\">\n\t\t\t\t<Loader margin={false} />\n\t\t\t</div>\n\t\t{/if}\n\t</div>\n</Block>\n\n<style>\n\t.load-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tmargin-left: auto;\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t}\n\n\t.response-wrap {\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.second-level {\n\t\tmargin-left: var(--size-4);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { BaseButton } from \"@gradio/button/static\";\n\texport let code: string;\n\tlet copy_text = \"copy\";\n\n\tfunction copy(): void {\n\t\tnavigator.clipboard.writeText(code);\n\t\tcopy_text = \"copied!\";\n\t\tsetTimeout(() => {\n\t\t\tcopy_text = \"copy\";\n\t\t}, 1500);\n\t}\n</script>\n\n<BaseButton size=\"sm\" on:click={copy}>\n\t{copy_text}\n</BaseButton>\n", "<script lang=\"ts\">\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let current_language: \"python\" | \"javascript\";\n\n\tlet py_install = \"pip install gradio_client\";\n\tlet js_install = \"npm i -D @gradio/client\";\n</script>\n\n<Block>\n\t<code>\n\t\t{#if current_language === \"python\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={py_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {py_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={js_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {js_install}</pre>\n\t\t\t</div>\n\t\t{/if}\n\t</code>\n</Block>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let api_name: string | null = null;\n\texport let fn_index: number | null = null;\n\texport let named: boolean;\n</script>\n\n{#if named}\n\t<h3>\n\t\tapi_name:\n\t\t<span class=\"post\">{\"/\" + api_name}</span>\n\t</h3>\n{:else}\n\t<h3>\n\t\tfn_index:\n\t\t<span class=\"post\">{fn_index}</span>\n\t</h3>\n{/if}\n\n<style>\n\th3 {\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.post {\n\t\tmargin-right: var(--size-2);\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: var(--size-1);\n\t\tpadding-left: var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { ComponentMeta, Dependency } from \"../components/types\";\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { represent_value } from \"./utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport EndpointDetail from \"./EndpointDetail.svelte\";\n\n\texport let dependency: Dependency;\n\texport let dependency_index: number;\n\texport let root: string;\n\texport let dependency_failures: boolean[][];\n\texport let endpoint_parameters: any;\n\texport let js_parameters: any;\n\texport let named: boolean;\n\n\texport let current_language: \"python\" | \"javascript\";\n\n\tlet python_code: HTMLElement;\n\tlet js_code: HTMLElement;\n\n\tlet blob_components = [\"Audio\", \"File\", \"Image\", \"Video\"];\n\tlet blob_examples: any[] = endpoint_parameters.filter(\n\t\t(param: {\n\t\t\tlabel: string;\n\t\t\ttype: string;\n\t\t\tpython_type: {\n\t\t\t\ttype: string;\n\t\t\t\tdescription: string;\n\t\t\t};\n\t\t\tcomponent: string;\n\t\t\texample_input: string;\n\t\t\tserializer: string;\n\t\t}) => blob_components.includes(param.component)\n\t);\n</script>\n\n<div class=\"container\">\n\t{#if named}\n\t\t<EndpointDetail {named} api_name={dependency.api_name} />\n\t{:else}\n\t\t<EndpointDetail {named} fn_index={dependency_index} />\n\t{/if}\n\t<Block>\n\t\t<code>\n\t\t\t{#if current_language === \"python\"}\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre>from gradio_client import Client\n\nclient = Client(<span class=\"token string\">\"{root}\"</span>)\nresult = client.predict(<!--\n-->{#each endpoint_parameters as { label, type, python_type, component, example_input, serializer }, i}<!--\n        -->\n\t\t<span\n\t\t\t\t\t\t\t\tclass=\"example-inputs\"\n\t\t\t\t\t\t\t\t>{represent_value(example_input, python_type.type, \"py\")}</span\n\t\t\t\t\t\t\t>,<!--\n\t\t\t-->{#if dependency_failures[dependency_index][i]}<!--\n\t\t\t--><span\n\t\t\t\t\t\t\t\t\tclass=\"error\">ERROR</span\n\t\t\t\t\t\t\t\t><!--\n\t\t\t\t-->{/if}<!--\n\t\t\t--><span class=\"desc\"\n\t\t\t\t\t\t\t\t><!--\n\t\t\t-->\t# {python_type.type} {#if python_type.description}({python_type.description}){/if}<!----> in '{label}' <!--\n\t\t\t-->{component} component<!--\n\t\t\t--></span><!--\n\t\t-->{/each}<!--\n\n\t\t-->{#if named}\n\t\tapi_name=\"/{dependency.api_name}\"<!--\n\t\t-->{:else}\n\t\tfn_index={dependency_index}\n\t\t{/if}\n)\nprint(result)</pre>\n\t\t\t\t</div>\n\t\t\t{:else if current_language === \"javascript\"}\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; client &rbrace; from \"@gradio/client\";\n{#each blob_examples as { label, type, python_type, component, example_input, serializer }, i}<!--\n-->\nconst response_{i} = await fetch(\"{example_input}\");\nconst example{component} = await response_{i}.blob();\n\t\t\t\t\t\t{/each}<!--\n-->\nconst app = await client(<span class=\"token string\">\"{root}\"</span>);\nconst result = await app.predict({#if named}\"/{dependency.api_name}\"{:else}{dependency_index}{/if}, [<!--\n-->{#each endpoint_parameters as { label, type, python_type, component, example_input, serializer }, i}<!--\n\t\t-->{#if blob_components.includes(component)}<!--\n\t-->\n\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"example-inputs\">example{component}</span\n\t\t\t\t\t\t\t\t>, <!--\n\t\t--><span class=\"desc\"\n\t\t\t\t\t\t\t\t\t><!--\n\t\t-->\t// blob <!--\n\t\t-->in '{label}' <!--\n\t\t-->{component} component<!--\n\t\t--></span\n\t\t\t\t\t\t\t\t><!--\n\t\t-->{:else}<!--\n\t-->\t\t\n\t\t\t\t<span class=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{represent_value(\n\t\t\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t\t>, <!--\n--><span class=\"desc\"\n\t\t\t\t\t\t\t\t\t><!--\n-->// {js_parameters[i]\n\t\t\t\t\t\t\t\t\t\t.type} {#if js_parameters[i].description}({js_parameters[i]\n\t\t\t\t\t\t\t\t\t\t\t.description}){/if}<!--\n--> in '{label}' <!--\n-->{component} component<!--\n--></span\n\t\t\t\t\t\t\t\t><!--\n-->{/if}\n\t\t\t\t\t\t{/each}\n\t]);\n\nconsole.log(result.data);\n</pre>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</code>\n\t</Block>\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.error {\n\t\tcolor: var(--error-text-color);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.example-inputs {\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding-right: var(--size-1);\n\t\tpadding-left: var(--size-1);\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "export default \"__VITE_ASSET__dc7a038c__\"", "export default \"__VITE_ASSET__90c1298d__\"", "<script lang=\"ts\">\n\t/* eslint-disable */\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport type { ComponentMeta, Dependency } from \"../components/types\";\n\timport { post_data } from \"@gradio/client\";\n\timport NoApi from \"./NoApi.svelte\";\n\timport type { client } from \"@gradio/client\";\n\n\timport { represent_value } from \"./utils\";\n\n\timport ApiBanner from \"./ApiBanner.svelte\";\n\timport ResponseObject from \"./ResponseObject.svelte\";\n\timport InstallSnippet from \"./InstallSnippet.svelte\";\n\timport CodeSnippets from \"./CodeSnippets.svelte\";\n\n\timport TryButton from \"./TryButton.svelte\";\n\timport python from \"./img/python.svg\";\n\timport javascript from \"./img/javascript.svg\";\n\n\texport let instance_map: {\n\t\t[id: number]: ComponentMeta;\n\t};\n\texport let dependencies: Dependency[];\n\texport let root: string;\n\texport let app: Awaited<ReturnType<typeof client>>;\n\n\tif (root === \"\") {\n\t\troot = location.protocol + \"//\" + location.host + location.pathname;\n\t}\n\tif (!root.endsWith(\"/\")) {\n\t\troot += \"/\";\n\t}\n\n\tlet current_language: \"python\" | \"javascript\" = \"python\";\n\n\tconst langs = [\n\t\t[\"python\", python],\n\t\t[\"javascript\", javascript]\n\t] as const;\n\n\tlet is_running = false;\n\n\tlet dependency_inputs = dependencies.map((dependency) =>\n\t\tdependency.inputs.map((_id) => {\n\t\t\tlet default_data = instance_map[_id].documentation?.example_data;\n\t\t\tif (default_data === undefined) {\n\t\t\t\tdefault_data = \"\";\n\t\t\t} else if (typeof default_data === \"object\") {\n\t\t\t\tdefault_data = JSON.stringify(default_data);\n\t\t\t}\n\t\t\treturn default_data;\n\t\t})\n\t);\n\n\tlet dependency_outputs: any[][] = dependencies.map(\n\t\t(dependency) => new Array(dependency.outputs.length)\n\t);\n\n\tlet dependency_failures: boolean[][] = dependencies.map((dependency) =>\n\t\tnew Array(dependency.inputs.length).fill(false)\n\t);\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(root + \"info\");\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\tasync function get_js_info(): Promise<Record<string, any>> {\n\t\tlet js_api_info = await app.view_api();\n\t\treturn js_api_info;\n\t}\n\n\tlet info: {\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t};\n\n\tlet js_info: Record<string, any>;\n\n\tget_info().then((data) => (info = data));\n\n\tget_js_info().then((js_api_info) => (js_info = js_api_info));\n\n\tasync function run(index: number): Promise<void> {\n\t\tis_running = true;\n\t\tlet dependency = dependencies[index];\n\t\tlet attempted_component_index = 0;\n\t\ttry {\n\t\t\tvar inputs = dependency_inputs[index].map((input_val, i) => {\n\t\t\t\tattempted_component_index = i;\n\t\t\t\tlet component = instance_map[dependency.inputs[i]];\n\t\t\t\t// @ts-ignore\n\t\t\t\tinput_val = represent_value(\n\t\t\t\t\tinput_val,\n\t\t\t\t\tcomponent.documentation?.type?.input_payload ||\n\t\t\t\t\t\tcomponent.documentation?.type?.payload\n\t\t\t\t);\n\t\t\t\tdependency_failures[index][attempted_component_index] = false;\n\t\t\t\treturn input_val;\n\t\t\t});\n\t\t} catch (err) {\n\t\t\tdependency_failures[index][attempted_component_index] = true;\n\t\t\tis_running = false;\n\t\t\treturn;\n\t\t}\n\t\tlet [response, status_code] = await post_data(\n\t\t\t`${root}run/${dependency.api_name}`,\n\t\t\t{\n\t\t\t\tdata: inputs\n\t\t\t}\n\t\t);\n\t\tis_running = false;\n\t\tif (status_code == 200) {\n\t\t\tdependency_outputs[index] = response.data.map(\n\t\t\t\t(output_val: any, i: number) => {\n\t\t\t\t\tlet component = instance_map[dependency.outputs[i]];\n\n\t\t\t\t\treturn represent_value(\n\t\t\t\t\t\toutput_val,\n\t\t\t\t\t\tcomponent.documentation?.type?.response_object ||\n\t\t\t\t\t\t\tcomponent.documentation?.type?.payload,\n\t\t\t\t\t\t\"js\"\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t);\n\t\t} else {\n\t\t\tdependency_failures[index] = new Array(\n\t\t\t\tdependency_failures[index].length\n\t\t\t).fill(true);\n\t\t}\n\t}\n\n\tonMount(() => {\n\t\tdocument.body.style.overflow = \"hidden\";\n\t\tif (\"parentIFrame\" in window) {\n\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t}\n\t\treturn () => {\n\t\t\tdocument.body.style.overflow = \"auto\";\n\t\t};\n\t});\n</script>\n\n{#if info}\n\t{#if Object.keys(info.named_endpoints).length + Object.keys(info.unnamed_endpoints).length}\n\t\t<div class=\"banner-wrap\">\n\t\t\t<ApiBanner\n\t\t\t\ton:close\n\t\t\t\t{root}\n\t\t\t\tapi_count={Object.keys(info.named_endpoints).length +\n\t\t\t\t\tObject.keys(info.unnamed_endpoints).length}\n\t\t\t/>\n\t\t</div>\n\t\t<div class=\"docs-wrap\">\n\t\t\t<div class=\"client-doc\">\n\t\t\t\t<p>\n\t\t\t\t\tUse the <a\n\t\t\t\t\t\thref=\"https://gradio.app/docs/#python-client\"\n\t\t\t\t\t\ttarget=\"_blank\"><code class=\"library\">gradio_client</code></a\n\t\t\t\t\t>\n\t\t\t\t\tPython library or the\n\t\t\t\t\t<a href=\"https://gradio.app/docs/#javascript-client\" target=\"_blank\"\n\t\t\t\t\t\t><code class=\"library\">@gradio/client</code></a\n\t\t\t\t\t> Javascript package to query the demo via API.\n\t\t\t\t</p>\n\t\t\t</div>\n\t\t\t<div class=\"endpoint\">\n\t\t\t\t<div class=\"snippets\">\n\t\t\t\t\t{#each langs as [language, img]}\n\t\t\t\t\t\t<li\n\t\t\t\t\t\t\tclass=\"snippet\n\t\t\t\t\t\t\t{current_language === language ? 'current-lang' : 'inactive-lang'}\"\n\t\t\t\t\t\t\ton:click={() => (current_language = language)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<img src={img} alt=\"\" />\n\t\t\t\t\t\t\t{language}\n\t\t\t\t\t\t</li>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t\t<InstallSnippet {current_language} />\n\n\t\t\t\t{#if Object.keys(info.named_endpoints).length}\n\t\t\t\t\t<h2 class=\"header\">Named Endpoints</h2>\n\t\t\t\t{/if}\n\n\t\t\t\t{#each dependencies as dependency, dependency_index}\n\t\t\t\t\t{#if dependency.api_name}\n\t\t\t\t\t\t<div class=\"endpoint-container\">\n\t\t\t\t\t\t\t<CodeSnippets\n\t\t\t\t\t\t\t\tnamed={true}\n\t\t\t\t\t\t\t\tendpoint_parameters={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\tjs_parameters={js_info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\t{dependency}\n\t\t\t\t\t\t\t\t{dependency_index}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t{dependency_failures}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<!-- <TryButton\n\t\t\t\t\t\t\tnamed={true}\n\t\t\t\t\t\t\t{dependency_index}\n\t\t\t\t\t\t\t{run}\n\t\t\t\t\t\t/> -->\n\n\t\t\t\t\t\t\t<ResponseObject\n\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].returns}\n\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t.returns}\n\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\n\t\t\t\t{#if Object.keys(info.unnamed_endpoints).length}\n\t\t\t\t\t<h2 class=\"header\">Unnamed Endpoints</h2>\n\t\t\t\t{/if}\n\n\t\t\t\t{#each dependencies as dependency, dependency_index}\n\t\t\t\t\t{#if info.unnamed_endpoints[dependency_index]}\n\t\t\t\t\t\t<div class=\"endpoint-container\">\n\t\t\t\t\t\t\t<CodeSnippets\n\t\t\t\t\t\t\t\tnamed={false}\n\t\t\t\t\t\t\t\tendpoint_parameters={info.unnamed_endpoints[dependency_index]\n\t\t\t\t\t\t\t\t\t.parameters}\n\t\t\t\t\t\t\t\tjs_parameters={js_info.unnamed_endpoints[dependency_index]\n\t\t\t\t\t\t\t\t\t.parameters}\n\t\t\t\t\t\t\t\t{dependency}\n\t\t\t\t\t\t\t\t{dependency_index}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t{dependency_failures}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<ResponseObject\n\t\t\t\t\t\t\t\tendpoint_returns={info.unnamed_endpoints[dependency_index]\n\t\t\t\t\t\t\t\t\t.returns}\n\t\t\t\t\t\t\t\tjs_returns={js_info.unnamed_endpoints[dependency_index].returns}\n\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</div>\n\t{:else}\n\t\t<NoApi {root} on:close />\n\t{/if}\n{/if}\n\n<style>\n\t.banner-wrap {\n\t\tposition: relative;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tpadding: var(--size-4) var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t@media (--screen-md) {\n\t\t.banner-wrap {\n\t\t\tfont-size: var(--text-xl);\n\t\t}\n\t}\n\n\t.docs-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t}\n\n\t.endpoint {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-6);\n\t\tpadding-top: var(--size-1);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.client-doc {\n\t\tpadding-top: var(--size-6);\n\t\tpadding-right: var(--size-6);\n\t\tpadding-left: var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.library {\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: var(--size-1);\n\t\tpadding-left: var(--size-1);\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.snippets {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-4);\n\t}\n\n\t.snippets > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.snippet {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-1) var(--size-1-5);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: 1;\n\t\tuser-select: none;\n\t\ttext-transform: capitalize;\n\t}\n\n\t.current-lang {\n\t\tborder: 1px solid var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.inactive-lang {\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.inactive-lang:hover,\n\t.inactive-lang:focus {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.snippet img {\n\t\tmargin-right: var(--size-1-5);\n\t\twidth: var(--size-3);\n\t}\n\n\t.header {\n\t\tmargin-top: var(--size-6);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.endpoint-container {\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-xl);\n\t\tpadding: var(--size-3);\n\t\tpadding-top: 0;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Gradio } from \"./gradio_helper\";\n\timport { onMount, createEventDispatcher, setContext } from \"svelte\";\n\timport type { ComponentMeta } from \"./components/types\";\n\timport type { ThemeMode } from \"./components/types\";\n\n\texport let root: string;\n\texport let component: ComponentMeta[\"component\"];\n\texport let instance_map: Record<number, ComponentMeta>;\n\n\texport let id: number;\n\texport let props: ComponentMeta[\"props\"];\n\n\texport let children: ComponentMeta[\"children\"];\n\texport let dynamic_ids: Set<number>;\n\texport let parent: string | null = null;\n\texport let target: HTMLElement;\n\texport let theme_mode: ThemeMode;\n\texport let version: string;\n\n\tconst dispatch = createEventDispatcher<{ mount: number; destroy: number }>();\n\tlet filtered_children: ComponentMeta[] = [];\n\n\tonMount(() => {\n\t\tdispatch(\"mount\", id);\n\n\t\tfor (const child of filtered_children) {\n\t\t\tdispatch(\"mount\", child.id);\n\t\t}\n\n\t\treturn () => {\n\t\t\tdispatch(\"destroy\", id);\n\n\t\t\tfor (const child of filtered_children) {\n\t\t\t\tdispatch(\"mount\", child.id);\n\t\t\t}\n\t\t};\n\t});\n\n\t$: children =\n\t\tchildren &&\n\t\tchildren.filter((v) => {\n\t\t\tconst valid_node = instance_map[v.id].type !== \"statustracker\";\n\t\t\tif (!valid_node) {\n\t\t\t\tfiltered_children.push(v);\n\t\t\t}\n\t\t\treturn valid_node;\n\t\t});\n\n\tsetContext(\"BLOCK_KEY\", parent);\n\n\tfunction handle_prop_change(e: { detail: Record<string, any> }): void {\n\t\tfor (const k in e.detail) {\n\t\t\tinstance_map[id].props[k] = e.detail[k];\n\t\t}\n\t}\n\n\t$: {\n\t\tif (instance_map[id].type === \"form\") {\n\t\t\tif (children?.every((c) => !c.props.visible)) {\n\t\t\t\tprops.visible = false;\n\t\t\t} else {\n\t\t\t\tprops.visible = true;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<svelte:component\n\tthis={component}\n\tbind:this={instance_map[id].instance}\n\tbind:value={instance_map[id].props.value}\n\telem_id={(\"elem_id\" in props && props.elem_id) || `component-${id}`}\n\telem_classes={(\"elem_classes\" in props && props.elem_classes) || []}\n\ton:prop_change={handle_prop_change}\n\t{target}\n\t{...props}\n\t{theme_mode}\n\t{root}\n\tgradio={new Gradio(id, target, theme_mode, version, root)}\n>\n\t{#if children && children.length}\n\t\t{#each children as { component, id: each_id, props, children: _children, has_modes } (each_id)}\n\t\t\t<svelte:self\n\t\t\t\t{component}\n\t\t\t\t{target}\n\t\t\t\tid={each_id}\n\t\t\t\t{props}\n\t\t\t\t{root}\n\t\t\t\t{instance_map}\n\t\t\t\tchildren={_children}\n\t\t\t\t{dynamic_ids}\n\t\t\t\t{has_modes}\n\t\t\t\t{theme_mode}\n\t\t\t\ton:destroy\n\t\t\t\ton:mount\n\t\t\t/>\n\t\t{/each}\n\t{/if}\n</svelte:component>\n", "<script lang=\"ts\">\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport Render from \"./Render.svelte\";\n\n\texport let rootNode: any;\n\texport let dynamic_ids: any;\n\texport let instance_map: any;\n\texport let root: any;\n\texport let target: any;\n\texport let theme_mode: any;\n\texport let version: any;\n\n\tconst dispatch = createEventDispatcher<{ mount: never }>();\n\tonMount(() => {\n\t\tdispatch(\"mount\");\n\t});\n</script>\n\n<Render\n\tcomponent={rootNode.component}\n\tid={rootNode.id}\n\tprops={rootNode.props}\n\tchildren={rootNode.children}\n\t{dynamic_ids}\n\t{instance_map}\n\t{root}\n\t{target}\n\t{theme_mode}\n\t{version}\n/>\n", "export default \"__VITE_ASSET__c2b3a5f0__\"", "<script lang=\"ts\">\n\timport { onMount, tick } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\timport type { client } from \"@gradio/client\";\n\n\timport { component_map } from \"./components/directory\";\n\timport { create_loading_status_store, app_state } from \"./stores\";\n\timport type { LoadingStatusCollection } from \"./stores\";\n\n\timport type {\n\t\tComponentMeta,\n\t\tDependency,\n\t\tLayoutNode\n\t} from \"./components/types\";\n\timport { setupi18n } from \"./i18n\";\n\timport { ApiDocs } from \"./api_docs/\";\n\timport type { ThemeMode } from \"./components/types\";\n\timport { Toast } from \"@gradio/statustracker\";\n\timport type { ToastMessage } from \"@gradio/statustracker\";\n\timport type { ShareData } from \"@gradio/utils\";\n\timport MountComponents from \"./MountComponents.svelte\";\n\n\timport logo from \"./images/logo.svg\";\n\timport api_logo from \"./api_docs/img/api-logo.svg\";\n\n\tsetupi18n();\n\n\texport let root: string;\n\texport let components: ComponentMeta[];\n\texport let layout: LayoutNode;\n\texport let dependencies: Dependency[];\n\texport let title = \"Gradio\";\n\texport let analytics_enabled = false;\n\texport let target: HTMLElement;\n\texport let autoscroll: boolean;\n\texport let show_api = true;\n\texport let show_footer = true;\n\texport let control_page_title = false;\n\texport let app_mode: boolean;\n\texport let theme_mode: ThemeMode;\n\texport let app: Awaited<ReturnType<typeof client>>;\n\texport let space_id: string | null;\n\texport let version: string;\n\n\tlet loading_status = create_loading_status_store();\n\n\t$: app_state.update((s) => ({ ...s, autoscroll }));\n\n\tlet rootNode: ComponentMeta = {\n\t\tid: layout.id,\n\t\ttype: \"column\",\n\t\tprops: { mode: \"static\" },\n\t\thas_modes: false,\n\t\tinstance: null as unknown as ComponentMeta[\"instance\"],\n\t\tcomponent: null as unknown as ComponentMeta[\"component\"]\n\t};\n\n\tconst AsyncFunction = Object.getPrototypeOf(async function () {}).constructor;\n\tdependencies.forEach((d) => {\n\t\tif (d.js) {\n\t\t\tconst wrap = d.backend_fn\n\t\t\t\t? d.inputs.length === 1\n\t\t\t\t: d.outputs.length === 1;\n\t\t\ttry {\n\t\t\t\td.frontend_fn = new AsyncFunction(\n\t\t\t\t\t\"__fn_args\",\n\t\t\t\t\t`let result = await (${d.js})(...__fn_args);\n\t\t\t\t\treturn (${wrap} && !Array.isArray(result)) ? [result] : result;`\n\t\t\t\t);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error(\"Could not parse custom js method.\");\n\t\t\t\tconsole.error(e);\n\t\t\t}\n\t\t}\n\t});\n\n\tlet params = new URLSearchParams(window.location.search);\n\tlet api_docs_visible = params.get(\"view\") === \"api\" && show_api;\n\tfunction set_api_docs_visible(visible: boolean): void {\n\t\tapi_docs_visible = visible;\n\t\tlet params = new URLSearchParams(window.location.search);\n\t\tif (visible) {\n\t\t\tparams.set(\"view\", \"api\");\n\t\t} else {\n\t\t\tparams.delete(\"view\");\n\t\t}\n\t\thistory.replaceState(null, \"\", \"?\" + params.toString());\n\t}\n\n\tfunction is_dep(\n\t\tid: number,\n\t\ttype: \"inputs\" | \"outputs\",\n\t\tdeps: Dependency[]\n\t): boolean {\n\t\tfor (const dep of deps) {\n\t\t\tfor (const dep_item of dep[type]) {\n\t\t\t\tif (dep_item === id) return true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}\n\n\tlet dynamic_ids: Set<number> = new Set();\n\n\tfunction has_no_default_value(value: any): boolean {\n\t\treturn (\n\t\t\t(Array.isArray(value) && value.length === 0) ||\n\t\t\tvalue === \"\" ||\n\t\t\tvalue === 0 ||\n\t\t\t!value\n\t\t);\n\t}\n\n\tlet instance_map: { [id: number]: ComponentMeta };\n\n\ttype LoadedComponent = {\n\t\tdefault: ComponentMeta[\"component\"];\n\t};\n\n\tasync function load_component<T extends ComponentMeta[\"type\"]>(\n\t\tname: T,\n\t\tmode: ComponentMeta[\"props\"][\"mode\"]\n\t): Promise<{\n\t\tname: T;\n\t\tcomponent: LoadedComponent;\n\t}> {\n\t\ttry {\n\t\t\t//@ts-ignore\n\t\t\tconst c = await component_map[name][mode]();\n\t\t\treturn {\n\t\t\t\tname,\n\t\t\t\tcomponent: c as LoadedComponent\n\t\t\t};\n\t\t} catch (e) {\n\t\t\tif (mode === \"interactive\") {\n\t\t\t\ttry {\n\t\t\t\t\tconst c = await component_map[name][\"static\"]();\n\t\t\t\t\treturn {\n\t\t\t\t\t\tname,\n\t\t\t\t\t\tcomponent: c as LoadedComponent\n\t\t\t\t\t};\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(`failed to load: ${name}`);\n\t\t\t\t\tconsole.error(e);\n\t\t\t\t\tthrow e;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconsole.error(`failed to load: ${name}`);\n\t\t\t\tconsole.error(e);\n\t\t\t\tthrow e;\n\t\t\t}\n\t\t}\n\t}\n\n\tlet component_set = new Set<\n\t\tPromise<{ name: ComponentMeta[\"type\"]; component: LoadedComponent }>\n\t>();\n\n\tlet _component_map = new Map<\n\t\t`${ComponentMeta[\"type\"]}_${ComponentMeta[\"props\"][\"mode\"]}`,\n\t\tPromise<{ name: ComponentMeta[\"type\"]; component: LoadedComponent }>\n\t>();\n\n\tasync function walk_layout(\n\t\tnode: LayoutNode,\n\t\ttype_map: Map<number, ComponentMeta[\"props\"][\"mode\"]>,\n\t\tinstance_map: { [id: number]: ComponentMeta },\n\t\tcomponent_map: Map<\n\t\t\t`${ComponentMeta[\"type\"]}_${ComponentMeta[\"props\"][\"mode\"]}`,\n\t\t\tPromise<{ name: ComponentMeta[\"type\"]; component: LoadedComponent }>\n\t\t>\n\t): Promise<void> {\n\t\tready = false;\n\t\tlet instance = instance_map[node.id];\n\n\t\tconst _component = (await component_map.get(\n\t\t\t`${instance.type}_${type_map.get(node.id) || \"static\"}`\n\t\t))!.component;\n\t\tinstance.component = _component.default;\n\n\t\tif (node.children) {\n\t\t\tinstance.children = node.children.map((v) => instance_map[v.id]);\n\t\t\tawait Promise.all(\n\t\t\t\tnode.children.map((v) =>\n\t\t\t\t\twalk_layout(v, type_map, instance_map, component_map)\n\t\t\t\t)\n\t\t\t);\n\t\t}\n\t}\n\n\texport let ready = false;\n\texport let render_complete = false;\n\n\t$: components, layout, prepare_components();\n\n\tfunction prepare_components(): void {\n\t\tloading_status = create_loading_status_store();\n\n\t\tdependencies.forEach((v, i) => {\n\t\t\tloading_status.register(i, v.inputs, v.outputs);\n\t\t});\n\n\t\tconst _dynamic_ids = new Set<number>();\n\t\tfor (const comp of components) {\n\t\t\tconst { id, props } = comp;\n\t\t\tconst is_input = is_dep(id, \"inputs\", dependencies);\n\t\t\tif (\n\t\t\t\tis_input ||\n\t\t\t\t(!is_dep(id, \"outputs\", dependencies) &&\n\t\t\t\t\thas_no_default_value(props?.value))\n\t\t\t) {\n\t\t\t\t_dynamic_ids.add(id);\n\t\t\t}\n\t\t}\n\n\t\tdynamic_ids = _dynamic_ids;\n\n\t\tconst _rootNode: typeof rootNode = {\n\t\t\tid: layout.id,\n\t\t\ttype: \"column\",\n\t\t\tprops: { mode: \"static\" },\n\t\t\thas_modes: false,\n\t\t\tinstance: null as unknown as ComponentMeta[\"instance\"],\n\t\t\tcomponent: null as unknown as ComponentMeta[\"component\"]\n\t\t};\n\t\tcomponents.push(_rootNode);\n\t\tconst _component_set = new Set<\n\t\t\tPromise<{ name: ComponentMeta[\"type\"]; component: LoadedComponent }>\n\t\t>();\n\t\tconst __component_map = new Map<\n\t\t\t`${ComponentMeta[\"type\"]}_${ComponentMeta[\"props\"][\"mode\"]}`,\n\t\t\tPromise<{ name: ComponentMeta[\"type\"]; component: LoadedComponent }>\n\t\t>();\n\t\tconst __type_for_id = new Map<number, ComponentMeta[\"props\"][\"mode\"]>();\n\t\tconst _instance_map = components.reduce(\n\t\t\t(acc, next) => {\n\t\t\t\tacc[next.id] = next;\n\t\t\t\treturn acc;\n\t\t\t},\n\t\t\t{} as { [id: number]: ComponentMeta }\n\t\t);\n\t\tcomponents.forEach((c) => {\n\t\t\tif ((c.props as any).interactive === false) {\n\t\t\t\t(c.props as any).mode = \"static\";\n\t\t\t} else if ((c.props as any).interactive === true) {\n\t\t\t\t(c.props as any).mode = \"interactive\";\n\t\t\t} else if (dynamic_ids.has(c.id)) {\n\t\t\t\t(c.props as any).mode = \"interactive\";\n\t\t\t} else {\n\t\t\t\t(c.props as any).mode = \"static\";\n\t\t\t}\n\n\t\t\tif ((c.props as any).server_fns) {\n\t\t\t\tlet server: Record<string, (...args: any[]) => Promise<any>> = {};\n\t\t\t\t(c.props as any).server_fns.forEach((fn: string) => {\n\t\t\t\t\tserver[fn] = async (...args: any[]) => {\n\t\t\t\t\t\tif (args.length === 1) {\n\t\t\t\t\t\t\targs = args[0];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst result = await app.component_server(c.id, fn, args);\n\t\t\t\t\t\treturn result;\n\t\t\t\t\t};\n\t\t\t\t});\n\t\t\t\t(c.props as any).server = server;\n\t\t\t}\n\t\t\t__type_for_id.set(c.id, c.props.mode);\n\n\t\t\tconst _c = load_component(c.type, c.props.mode);\n\t\t\t_component_set.add(_c);\n\t\t\t__component_map.set(`${c.type}_${c.props.mode}`, _c);\n\t\t});\n\n\t\tPromise.all(Array.from(_component_set)).then(() => {\n\t\t\twalk_layout(layout, __type_for_id, _instance_map, __component_map)\n\t\t\t\t.then(async () => {\n\t\t\t\t\tready = true;\n\t\t\t\t\tcomponent_set = _component_set;\n\t\t\t\t\t_component_map = __component_map;\n\t\t\t\t\tinstance_map = _instance_map;\n\t\t\t\t\trootNode = _rootNode;\n\t\t\t\t})\n\t\t\t\t.catch((e) => {\n\t\t\t\t\tconsole.error(e);\n\t\t\t\t});\n\t\t});\n\t}\n\n\tasync function update_interactive_mode(\n\t\tinstance: ComponentMeta,\n\t\tmode: \"dynamic\" | \"interactive\" | \"static\"\n\t): Promise<void> {\n\t\tlet new_mode: \"interactive\" | \"static\" =\n\t\t\tmode === \"dynamic\" ? \"interactive\" : mode;\n\n\t\tif (instance.props.mode === new_mode) return;\n\n\t\tinstance.props.mode = new_mode;\n\t\tconst _c = load_component(instance.type, instance.props.mode);\n\t\tcomponent_set.add(_c);\n\t\t_component_map.set(\n\t\t\t`${instance.type}_${instance.props.mode}`,\n\t\t\t_c as Promise<{\n\t\t\t\tname: ComponentMeta[\"type\"];\n\t\t\t\tcomponent: LoadedComponent;\n\t\t\t}>\n\t\t);\n\n\t\t_c.then((c) => {\n\t\t\tinstance.component = c.component.default;\n\t\t\trootNode = rootNode;\n\t\t});\n\t}\n\n\tfunction handle_update(data: any, fn_index: number): void {\n\t\tconst outputs = dependencies[fn_index].outputs;\n\t\tdata?.forEach((value: any, i: number) => {\n\t\t\tconst output = instance_map[outputs[i]];\n\t\t\toutput.props.value_is_output = true;\n\t\t\tif (\n\t\t\t\ttypeof value === \"object\" &&\n\t\t\t\tvalue !== null &&\n\t\t\t\tvalue.__type__ === \"update\"\n\t\t\t) {\n\t\t\t\tfor (const [update_key, update_value] of Object.entries(value)) {\n\t\t\t\t\tif (update_key === \"__type__\") {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (update_key === \"mode\") {\n\t\t\t\t\t\t\tupdate_interactive_mode(\n\t\t\t\t\t\t\t\toutput,\n\t\t\t\t\t\t\t\tupdate_value as \"dynamic\" | \"static\"\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t}\n\t\t\t\t\t\toutput.props[update_key] = update_value;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toutput.props.value = value;\n\t\t\t}\n\t\t});\n\t\trootNode = rootNode;\n\t}\n\n\tlet submit_map: Map<number, ReturnType<typeof app.submit>> = new Map();\n\n\tfunction set_prop<T extends ComponentMeta>(\n\t\tobj: T,\n\t\tprop: string,\n\t\tval: any\n\t): void {\n\t\tif (!obj?.props) {\n\t\t\t// @ts-ignore\n\t\t\tobj.props = {};\n\t\t}\n\t\tobj.props[prop] = val;\n\t\trootNode = rootNode;\n\t}\n\tlet handled_dependencies: number[][] = [];\n\n\tlet messages: (ToastMessage & { fn_index: number })[] = [];\n\tfunction new_message(\n\t\tmessage: string,\n\t\tfn_index: number,\n\t\ttype: ToastMessage[\"type\"]\n\t): ToastMessage & { fn_index: number } {\n\t\treturn {\n\t\t\tmessage,\n\t\t\tfn_index,\n\t\t\ttype,\n\t\t\tid: ++_error_id\n\t\t};\n\t}\n\n\tlet _error_id = -1;\n\n\tlet user_left_page = false;\n\tdocument.addEventListener(\"visibilitychange\", function () {\n\t\tif (document.visibilityState === \"hidden\") {\n\t\t\tuser_left_page = true;\n\t\t}\n\t});\n\n\tconst MESSAGE_QUOTE_RE = /^'([^]+)'$/;\n\n\tconst DUPLICATE_MESSAGE = $_(\"blocks.long_requests_queue\");\n\tconst MOBILE_QUEUE_WARNING = $_(\"blocks.connection_can_break\");\n\tconst MOBILE_RECONNECT_MESSAGE = $_(\"blocks.lost_connection\");\n\tconst SHOW_DUPLICATE_MESSAGE_ON_ETA = 15;\n\tconst SHOW_MOBILE_QUEUE_WARNING_ON_ETA = 10;\n\tconst is_mobile_device =\n\t\t/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n\t\t\tnavigator.userAgent\n\t\t);\n\tlet showed_duplicate_message = false;\n\tlet showed_mobile_warning = false;\n\n\tasync function trigger_api_call(\n\t\tdep_index: number,\n\t\tevent_data: unknown = null\n\t): Promise<void> {\n\t\tlet dep = dependencies[dep_index];\n\t\tconst current_status = loading_status.get_status_for_fn(dep_index);\n\t\tmessages = messages.filter(({ fn_index }) => fn_index !== dep_index);\n\t\tif (dep.cancels) {\n\t\t\tawait Promise.all(\n\t\t\t\tdep.cancels.map(async (fn_index) => {\n\t\t\t\t\tconst submission = submit_map.get(fn_index);\n\t\t\t\t\tsubmission?.cancel();\n\t\t\t\t\treturn submission;\n\t\t\t\t})\n\t\t\t);\n\t\t}\n\n\t\tif (current_status === \"pending\" || current_status === \"generating\") {\n\t\t\treturn;\n\t\t}\n\n\t\tlet payload = {\n\t\t\tfn_index: dep_index,\n\t\t\tdata: dep.inputs.map((id) => instance_map[id].props.value),\n\t\t\tevent_data: dep.collects_event_data ? event_data : null\n\t\t};\n\n\t\tif (dep.frontend_fn) {\n\t\t\tdep\n\t\t\t\t.frontend_fn(\n\t\t\t\t\tpayload.data.concat(\n\t\t\t\t\t\tdep.outputs.map((id) => instance_map[id].props.value)\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t\t.then((v: unknown[]) => {\n\t\t\t\t\tif (dep.backend_fn) {\n\t\t\t\t\t\tpayload.data = v;\n\t\t\t\t\t\tmake_prediction();\n\t\t\t\t\t} else {\n\t\t\t\t\t\thandle_update(v, dep_index);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t} else {\n\t\t\tif (dep.backend_fn) {\n\t\t\t\tmake_prediction();\n\t\t\t}\n\t\t}\n\n\t\tfunction make_prediction(): void {\n\t\t\tconst submission = app\n\t\t\t\t.submit(payload.fn_index, payload.data as unknown[], payload.event_data)\n\t\t\t\t.on(\"data\", ({ data, fn_index }) => {\n\t\t\t\t\thandle_update(data, fn_index);\n\t\t\t\t})\n\t\t\t\t.on(\"status\", ({ fn_index, ...status }) => {\n\t\t\t\t\ttick().then(() => {\n\t\t\t\t\t\t//@ts-ignore\n\t\t\t\t\t\tloading_status.update({\n\t\t\t\t\t\t\t...status,\n\t\t\t\t\t\t\tstatus: status.stage,\n\t\t\t\t\t\t\tprogress: status.progress_data,\n\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t!showed_duplicate_message &&\n\t\t\t\t\t\t\tspace_id !== null &&\n\t\t\t\t\t\t\tstatus.position !== undefined &&\n\t\t\t\t\t\t\tstatus.position >= 2 &&\n\t\t\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\t\t\tstatus.eta > SHOW_DUPLICATE_MESSAGE_ON_ETA\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tshowed_duplicate_message = true;\n\t\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\t\tnew_message(DUPLICATE_MESSAGE, fn_index, \"warning\"),\n\t\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t\t];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t!showed_mobile_warning &&\n\t\t\t\t\t\t\tis_mobile_device &&\n\t\t\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\t\t\tstatus.eta > SHOW_MOBILE_QUEUE_WARNING_ON_ETA\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tshowed_mobile_warning = true;\n\t\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\t\tnew_message(MOBILE_QUEUE_WARNING, fn_index, \"warning\"),\n\t\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t\t];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (status.stage === \"complete\") {\n\t\t\t\t\t\t\tdependencies.map(async (dep, i) => {\n\t\t\t\t\t\t\t\tif (dep.trigger_after === fn_index) {\n\t\t\t\t\t\t\t\t\ttrigger_api_call(i);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tsubmission.destroy();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (status.broken && is_mobile_device && user_left_page) {\n\t\t\t\t\t\t\twindow.setTimeout(() => {\n\t\t\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\t\t\tnew_message(MOBILE_RECONNECT_MESSAGE, fn_index, \"error\"),\n\t\t\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t\t\t];\n\t\t\t\t\t\t\t}, 0);\n\t\t\t\t\t\t\ttrigger_api_call(dep_index, event_data);\n\t\t\t\t\t\t\tuser_left_page = false;\n\t\t\t\t\t\t} else if (status.stage === \"error\") {\n\t\t\t\t\t\t\tif (status.message) {\n\t\t\t\t\t\t\t\tconst _message = status.message.replace(\n\t\t\t\t\t\t\t\t\tMESSAGE_QUOTE_RE,\n\t\t\t\t\t\t\t\t\t(_, b) => b\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\t\t\tnew_message(_message, fn_index, \"error\"),\n\t\t\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t\t\t];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tdependencies.map(async (dep, i) => {\n\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\tdep.trigger_after === fn_index &&\n\t\t\t\t\t\t\t\t\t!dep.trigger_only_on_success\n\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\ttrigger_api_call(i);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tsubmission.destroy();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t.on(\"log\", ({ log, fn_index, level }) => {\n\t\t\t\t\tmessages = [new_message(log, fn_index, level), ...messages];\n\t\t\t\t});\n\n\t\t\tsubmit_map.set(dep_index, submission);\n\t\t}\n\t}\n\n\tfunction trigger_share(title: string | undefined, description: string): void {\n\t\tif (space_id === null) {\n\t\t\treturn;\n\t\t}\n\t\tconst discussion_url = new URL(\n\t\t\t`https://huggingface.co/spaces/${space_id}/discussions/new`\n\t\t);\n\t\tif (title !== undefined && title.length > 0) {\n\t\t\tdiscussion_url.searchParams.set(\"title\", title);\n\t\t}\n\t\tdiscussion_url.searchParams.set(\"description\", description);\n\t\twindow.open(discussion_url.toString(), \"_blank\");\n\t}\n\n\tfunction handle_error_close(e: Event & { detail: number }): void {\n\t\tconst _id = e.detail;\n\t\tmessages = messages.filter((m) => m.id !== _id);\n\t}\n\n\tconst is_external_url = (link: string | null): boolean =>\n\t\t!!(link && new URL(link, location.href).origin !== location.origin);\n\n\t$: target_map = dependencies.reduce(\n\t\t(acc, dep, i) => {\n\t\t\tdep.targets.forEach(([id, trigger]) => {\n\t\t\t\tif (!acc[id]) {\n\t\t\t\t\tacc[id] = {};\n\t\t\t\t}\n\t\t\t\tif (acc[id]?.[trigger]) {\n\t\t\t\t\tacc[id][trigger].push(i);\n\t\t\t\t} else {\n\t\t\t\t\tacc[id][trigger] = [i];\n\t\t\t\t}\n\t\t\t});\n\n\t\t\treturn acc;\n\t\t},\n\t\t{} as Record<number, Record<string, number[]>>\n\t);\n\n\tasync function handle_mount(): Promise<void> {\n\t\tawait tick();\n\n\t\tvar a = target.getElementsByTagName(\"a\");\n\n\t\tfor (var i = 0; i < a.length; i++) {\n\t\t\tconst _target = a[i].getAttribute(\"target\");\n\t\t\tconst _link = a[i].getAttribute(\"href\");\n\n\t\t\t// only target anchor tags with external links\n\t\t\tif (is_external_url(_link) && _target !== \"_blank\")\n\t\t\t\ta[i].setAttribute(\"target\", \"_blank\");\n\t\t}\n\n\t\t// handle load triggers\n\t\tdependencies.forEach((dep, i) => {\n\t\t\tif (dep.targets.length === 1 && dep.targets[0][1] === \"load\") {\n\t\t\t\ttrigger_api_call(i);\n\t\t\t}\n\t\t});\n\n\t\ttarget.addEventListener(\"gradio\", (e: Event) => {\n\t\t\tif (!isCustomEvent(e)) throw new Error(\"not a custom event\");\n\n\t\t\tconst { id, event, data } = e.detail;\n\n\t\t\tif (event === \"share\") {\n\t\t\t\tconst { title, description } = data as ShareData;\n\t\t\t\ttrigger_share(title, description);\n\t\t\t} else if (event === \"error\") {\n\t\t\t\tmessages = [new_message(data, -1, \"error\"), ...messages];\n\t\t\t} else {\n\t\t\t\tconst deps = target_map[id]?.[event];\n\t\t\t\tdeps?.forEach((dep_id) => {\n\t\t\t\t\ttrigger_api_call(dep_id, data);\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t\trender_complete = true;\n\t}\n\n\tfunction handle_destroy(id: number): void {\n\t\thandled_dependencies = handled_dependencies.map((dep) => {\n\t\t\treturn dep.filter((_id) => _id !== id);\n\t\t});\n\t}\n\n\t$: set_status($loading_status);\n\n\tfunction set_status(statuses: LoadingStatusCollection): void {\n\t\tfor (const id in statuses) {\n\t\t\tlet loading_status = statuses[id];\n\t\t\tlet dependency = dependencies[loading_status.fn_index];\n\t\t\tloading_status.scroll_to_output = dependency.scroll_to_output;\n\t\t\tloading_status.show_progress = dependency.show_progress;\n\n\t\t\tset_prop(instance_map[id], \"loading_status\", loading_status);\n\t\t}\n\t\tconst inputs_to_update = loading_status.get_inputs_to_update();\n\t\tfor (const [id, pending_status] of inputs_to_update) {\n\t\t\tset_prop(instance_map[id], \"pending\", pending_status === \"pending\");\n\t\t}\n\t}\n\n\tfunction isCustomEvent(event: Event): event is CustomEvent {\n\t\treturn \"detail\" in event;\n\t}\n</script>\n\n<svelte:head>\n\t{#if control_page_title}\n\t\t<title>{title}</title>\n\t{/if}\n\t{#if analytics_enabled}\n\t\t<script\n\t\t\tasync\n\t\t\tdefer\n\t\t\tsrc=\"https://www.googletagmanager.com/gtag/js?id=UA-156449732-1\"\n\t\t></script>\n\t\t<script>\n\t\t\twindow.dataLayer = window.dataLayer || [];\n\t\t\tfunction gtag() {\n\t\t\t\tdataLayer.push(arguments);\n\t\t\t}\n\t\t\tgtag(\"js\", new Date());\n\t\t\tgtag(\"config\", \"UA-156449732-1\");\n\t\t</script>\n\t{/if}\n</svelte:head>\n\n<div class=\"wrap\" style:min-height={app_mode ? \"100%\" : \"auto\"}>\n\t<div class=\"contain\" style:flex-grow={app_mode ? \"1\" : \"auto\"}>\n\t\t{#if ready}\n\t\t\t<MountComponents\n\t\t\t\t{rootNode}\n\t\t\t\t{dynamic_ids}\n\t\t\t\t{instance_map}\n\t\t\t\t{root}\n\t\t\t\t{target}\n\t\t\t\t{theme_mode}\n\t\t\t\ton:mount={handle_mount}\n\t\t\t\ton:destroy={({ detail }) => handle_destroy(detail)}\n\t\t\t\t{version}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\n\t{#if show_footer}\n\t\t<footer>\n\t\t\t{#if show_api}\n\t\t\t\t<button\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tset_api_docs_visible(!api_docs_visible);\n\t\t\t\t\t}}\n\t\t\t\t\tclass=\"show-api\"\n\t\t\t\t>\n\t\t\t\t\t{$_(\"errors.use_via_api\")}\n\t\t\t\t\t<img src={api_logo} alt={$_(\"common.logo\")} />\n\t\t\t\t</button>\n\t\t\t\t<div>·</div>\n\t\t\t{/if}\n\t\t\t<a\n\t\t\t\thref=\"https://gradio.app\"\n\t\t\t\tclass=\"built-with\"\n\t\t\t\ttarget=\"_blank\"\n\t\t\t\trel=\"noreferrer\"\n\t\t\t>\n\t\t\t\t{$_(\"common.built_with_gradio\")}\n\t\t\t\t<img src={logo} alt={$_(\"common.logo\")} />\n\t\t\t</a>\n\t\t</footer>\n\t{/if}\n</div>\n\n{#if api_docs_visible && ready}\n\t<div class=\"api-docs\">\n\t\t<!-- TODO: fix -->\n\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t<div\n\t\t\tclass=\"backdrop\"\n\t\t\ton:click={() => {\n\t\t\t\tset_api_docs_visible(false);\n\t\t\t}}\n\t\t/>\n\t\t<div class=\"api-docs-wrap\">\n\t\t\t<ApiDocs\n\t\t\t\ton:close={() => {\n\t\t\t\t\tset_api_docs_visible(false);\n\t\t\t\t}}\n\t\t\t\t{instance_map}\n\t\t\t\t{dependencies}\n\t\t\t\t{root}\n\t\t\t\t{app}\n\t\t\t/>\n\t\t</div>\n\t</div>\n{/if}\n\n{#if messages}\n\t<Toast {messages} on:close={handle_error_close} />\n{/if}\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-grow: 1;\n\t\tflex-direction: column;\n\t\twidth: var(--size-full);\n\t\tfont-weight: var(--body-text-weight);\n\t\tfont-size: var(--body-text-size);\n\t}\n\n\tfooter {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tmargin-top: var(--size-4);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tfooter > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.show-api {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t.show-api:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.show-api img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-2);\n\t\twidth: var(--size-3);\n\t}\n\n\t.built-with {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.built-with:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.built-with img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-2);\n\t\twidth: var(--size-3);\n\t}\n\n\t.api-docs {\n\t\tdisplay: flex;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-5);\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\twidth: var(--size-screen);\n\t\theight: var(--size-screen-h);\n\t}\n\n\t.backdrop {\n\t\tflex: 1 1 0%;\n\t\tbackdrop-filter: blur(4px);\n\t}\n\n\t.api-docs-wrap {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tbackground: var(--background-fill-primary);\n\t\toverflow-x: hidden;\n\t\toverflow-y: auto;\n\t}\n\n\t@media (--screen-md) {\n\t\t.api-docs-wrap {\n\t\t\tborder-top-left-radius: var(--radius-lg);\n\t\t\tborder-bottom-left-radius: var(--radius-lg);\n\t\t\twidth: 950px;\n\t\t}\n\t}\n\n\t@media (--screen-xxl) {\n\t\t.api-docs-wrap {\n\t\t\twidth: 1150px;\n\t\t}\n\t}\n</style>\n"], "file": "assets/Blocks-457c8926.js"}