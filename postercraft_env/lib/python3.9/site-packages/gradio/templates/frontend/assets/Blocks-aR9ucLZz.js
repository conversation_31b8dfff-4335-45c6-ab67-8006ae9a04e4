const __vite__fileDeps=["./Index-RkeFq165.js","./Button-8nmImwVJ.js","./Index-WGC0_FkS.js","./index-COY1HN2y.js","./index-luc1OtuK.css","./Index-hBVU0Tzp.css","./Button-CTZL5Nos.css","./Index-ChKBj1l7.js","./Index-CptIZeFZ.css","./Index-C5NYahSl.css","./Index-i_GKsICI.js","./BlockLabel-CJsotHlk.js","./Empty-Vuj7-ssy.js","./Image-Bsh8Umrh.js","./file-url-Bf0nK4ai.js","./Index-Cr62iXTg.css","./Example-BQyGztrG.js","./Example-D7K5RtQ2.css","./index-BpRV2GAs.js","./ShareButton-Ds9bG3Tz.js","./DownloadLink-DYBmO3sz.js","./Music-CDm0RGMk.js","./Trim-UKwaW4UI.js","./Undo-CpmTQw3B.js","./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js","./ModifyUpload-RL_SHQmd.css","./ModifyUpload-DZAlpNPL.js","./SelectSource-ghC4bkgc.js","./Upload-Cp8Go_XF.js","./UploadText-DlCTYTPP.js","./index-9HmesOWo.css","./Index-dzW3en6q.js","./Index-Bm9S7Zvm.js","./Index-CUtJsgll.js","./index-CnqicUFC.js","./Image-BZaARumT.js","./Image-B8dFOee4.css","./Video-CZt5m8l5.js","./Video-DJw86Ppo.css","./Index.svelte_svelte_type_style_lang-BgUoKvlh.js","./Example.svelte_svelte_type_style_lang-BBpfzd83.js","./prism-python-DQB1-hGx.js","./Example-BoFrNGG_.css","./Index-B2S_zKCm.css","./Check-Ck0iADAu.js","./Copy-ZPOKSMtK.js","./Index-C5rapRTu.css","./Example-CZ-iEz1g.js","./Index-C5yUArxr.js","./Info-COHEyv9U.js","./Index-BTCbxfal.css","./Example-CRm1Pmw_.js","./Index-BSDO0hSy.js","./BlockTitle-Bkh4EzYf.js","./Index-D8o7u_T6.css","./Example-Wp-_4AVX.js","./Example-oomIF0ca.css","./Index-C9nqmP2b.js","./Index-BrtrebwU.css","./Example-BaLyJYAe.js","./Example-Bw8Q_3wB.css","./Index-Bj_rbmDg.js","./Index-B6tpubC5.css","./Example-CMXuI9oj.js","./Example-CX34aPix.css","./Index-DqFs2tUO.js","./dsv-DB8NKgIY.js","./Index-CR9lHGQe.css","./Index-CKFR6bY7.js","./Index-D3f6Hf9S.css","./Index-B66Yrs9e.js","./Index-tcNSQSor.css","./Example-CUwox43B.js","./Index-D67FOkHI.js","./DropdownArrow-AhwBZaFV.js","./Index-BAQumg2K.css","./Example-DrmWnoSo.js","./Example-DpWs9cEC.css","./Index-DoR9meBn.js","./FileUpload-fvQx1gQG.js","./File-BQ_9P3Ye.js","./FileUpload-2TE7T7kD.css","./Example-CIFMxn5c.js","./Example-DfhEULNF.css","./Index-mQjq5qpN.js","./Index-YPTlsVjp.css","./Index-DDCF2BFd.js","./Index-B0JJ6p9c.css","./Index-rf9QTX8y.js","./Index-CnEIM1a1.css","./Index-CS2xVf8J.js","./Index-C-7D3Y3j.css","./Index-BgErhRC7.js","./color-D8LrHkKw.js","./Index-Dwy3Ni24.css","./Example-C2a4WxRl.js","./Example-CSw4pLi5.css","./Index-HrY4d4EM.js","./Index-BGmqBTg_.css","./Example-BI9uF_3D.js","./Example-DikqVAPo.css","./Index-C5jlwfFm.js","./ImageUploader-sLREcIL3.js","./ImageUploader-B7bPUstM.css","./Example-CO76L8ig.js","./Example-6rv12T44.css","./Index-0s73R3Rk.js","./__vite-browser-external-DFe-p4yY.js","./Index-DAGKYUjg.css","./Index-DFxZakaS.js","./Index-729KAuZc.css","./Index-Cqx-FJgX.js","./Index-BM4_tjMk.css","./Example-mBWp17nR.js","./Index-CWKLGFvw.js","./Example-uQ8MuYg6.js","./Index-BarCNVoD.js","./Index-Pyhmzacd.css","./Example-aHy38Zn4.js","./Example-sa1k2iUl.css","./Index-mWZjdNKk.js","./Video-fsmLZWjA.js","./Index-CD8YGlt2.css","./Example-CqL1e7EB.js","./Index-DWYbznWI.js","./Index-BUublr_b.css","./Example-C9__vDgN.js","./Index-a7Cqrz60.js","./Index-ClyD5tCI.css","./Index-34zXpzH2.js","./Index-D-1bCrLQ.css","./Example-BoMLuz1A.js","./Index-CI5B4P9h.js","./Index-BSqct-uf.css","./Index-B0dA5A3V.js","./Index-C43SQM_O.css","./Example-BrizabXh.js","./Index-BHQtWxGm.js","./Index-CJeiS5ET.css","./index-CzuPDZ9-.js","./Index-D-WFSnDJ.js","./Tabs-BuGX0ApY.js","./Tabs-DX5TEub2.css","./Index-thdqX2vf.css","./Index-DWOMC-fE.js","./Example-C7XUkkid.js","./Example-Cj3ii62O.css","./Index-XZHENFUj.js","./Textbox-BCZjJ4Bh.js","./Textbox-D8IAzrZj.css","./Index-DbSX1Tgn.js","./Index-DYDmCduo.css","./Example-CCJuO3xS.js","./Example-B5CSTz0f.css","./index-DN6G7dvw.js","./index-CgvweMVN.css","./Example-DxdiEFS_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{d as ws,L as Pi,e as ys,$ as Oi,w as vn,f as Es}from"./Index-WGC0_FkS.js";import{_ as O}from"./index-COY1HN2y.js";import{c as js,f as zl,a as Tn,B as on}from"./Button-8nmImwVJ.js";const{SvelteComponent:Cs,append:qs,attr:Oe,detach:As,init:Ls,insert:Ts,noop:Hn,safe_not_equal:Ps,svg_element:Ml}=window.__gradio__svelte__internal;function Os(l){let e,t;return{c(){e=Ml("svg"),t=Ml("path"),Oe(t,"stroke-linecap","round"),Oe(t,"stroke-linejoin","round"),Oe(t,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),Oe(e,"fill","none"),Oe(e,"stroke","currentColor"),Oe(e,"viewBox","0 0 24 24"),Oe(e,"width","100%"),Oe(e,"height","100%"),Oe(e,"xmlns","http://www.w3.org/2000/svg"),Oe(e,"aria-hidden","true"),Oe(e,"stroke-width","2"),Oe(e,"stroke-linecap","round"),Oe(e,"stroke-linejoin","round")},m(n,o){Ts(n,e,o),qs(e,t)},p:Hn,i:Hn,o:Hn,d(n){n&&As(e)}}}let Is=class extends Cs{constructor(e){super(),Ls(this,e,null,Os,Ps,{})}};const{SvelteComponent:Ds,append:Rs,attr:Ie,detach:Ss,init:Vs,insert:zs,noop:Gn,safe_not_equal:Ms,svg_element:Nl}=window.__gradio__svelte__internal;function Ns(l){let e,t;return{c(){e=Nl("svg"),t=Nl("path"),Ie(t,"stroke-linecap","round"),Ie(t,"stroke-linejoin","round"),Ie(t,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),Ie(e,"fill","none"),Ie(e,"stroke","currentColor"),Ie(e,"viewBox","0 0 24 24"),Ie(e,"width","100%"),Ie(e,"height","100%"),Ie(e,"xmlns","http://www.w3.org/2000/svg"),Ie(e,"aria-hidden","true"),Ie(e,"stroke-width","2"),Ie(e,"stroke-linecap","round"),Ie(e,"stroke-linejoin","round")},m(n,o){zs(n,e,o),Rs(e,t)},p:Gn,i:Gn,o:Gn,d(n){n&&Ss(e)}}}class Fs extends Ds{constructor(e){super(),Vs(this,e,null,Ns,Ms,{})}}const{SvelteComponent:Us,append:Bs,attr:De,detach:Hs,init:Gs,insert:Zs,noop:Zn,safe_not_equal:Ws,svg_element:Fl}=window.__gradio__svelte__internal;function Qs(l){let e,t;return{c(){e=Fl("svg"),t=Fl("path"),De(t,"stroke-linecap","round"),De(t,"stroke-linejoin","round"),De(t,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),De(e,"fill","none"),De(e,"stroke","currentColor"),De(e,"stroke-width","2"),De(e,"viewBox","0 0 24 24"),De(e,"width","100%"),De(e,"height","100%"),De(e,"xmlns","http://www.w3.org/2000/svg"),De(e,"aria-hidden","true"),De(e,"stroke-linecap","round"),De(e,"stroke-linejoin","round")},m(n,o){Zs(n,e,o),Bs(e,t)},p:Zn,i:Zn,o:Zn,d(n){n&&Hs(e)}}}class Js extends Us{constructor(e){super(),Gs(this,e,null,Qs,Ws,{})}}class Wn extends Error{constructor(e){super(e),this.name="ShareError"}}async function hf(l,e){if(window.__gradio_space__==null)throw new Wn("Must be on Spaces to share.");let t,n,o;if(e==="url"){const _=await fetch(l);t=await _.blob(),n=_.headers.get("content-type")||"",o=_.headers.get("content-disposition")||""}else t=Xs(l),n=l.split(";")[0].split(":")[1],o="file"+n.split("/")[1];const i=new File([t],o,{type:n}),s=await fetch("https://huggingface.co/uploads",{method:"POST",body:i,headers:{"Content-Type":i.type,"X-Requested-With":"XMLHttpRequest"}});if(!s.ok){if(s.headers.get("content-type")?.includes("application/json")){const _=await s.json();throw new Wn(`Upload failed: ${_.error}`)}throw new Wn("Upload failed.")}return await s.text()}function Xs(l){for(var e=l.split(","),t=e[0].match(/:(.*?);/)[1],n=atob(e[1]),o=n.length,i=new Uint8Array(o);o--;)i[o]=n.charCodeAt(o);return new Blob([i],{type:t})}function gf(l){l.addEventListener("click",e);async function e(t){const n=t.composedPath(),[o]=n.filter(i=>i?.tagName==="BUTTON"&&i.classList.contains("copy_code_button"));if(o){let i=function(r){r.style.opacity="1",setTimeout(()=>{r.style.opacity="0"},2e3)};t.stopImmediatePropagation();const s=o.parentElement.innerText.trim(),a=Array.from(o.children)[1];await Ys(s)&&i(a)}}return{destroy(){l.removeEventListener("click",e)}}}async function Ys(l){let e=!1;if("clipboard"in navigator)await navigator.clipboard.writeText(l),e=!0;else{const t=document.createElement("textarea");t.value=l,t.style.position="absolute",t.style.left="-999999px",document.body.prepend(t),t.select();try{document.execCommand("copy"),e=!0}catch(n){console.error(n),e=!1}finally{t.remove()}}return e}const vf=l=>{const e=Math.floor(l/3600),t=Math.floor(l%3600/60),n=Math.round(l)%60,o=`${t<10?"0":""}${t}`,i=`${n<10?"0":""}${n}`;return e>0?`${e}:${o}:${i}`:`${t}:${i}`};class Ul{#e;#t;constructor(e,t,n,o,i,s,a,_=c=>c,r){this.#e=e,this.theme=n,this.version=o,this.#t=t,this.max_file_size=a,this.i18n=_,this.root=i,this.autoscroll=s,this.client=r}dispatch(e,t){const n=new CustomEvent("gradio",{bubbles:!0,detail:{data:t,id:this.#e,event:e}});this.#t.dispatchEvent(n)}}function Ks(l,{from:e,to:t},n={}){const o=getComputedStyle(l),i=o.transform==="none"?"":o.transform,[s,a]=o.transformOrigin.split(" ").map(parseFloat),_=e.left+e.width*s/t.width-(t.left+s),r=e.top+e.height*a/t.height-(t.top+a),{delay:c=0,duration:u=d=>Math.sqrt(d)*120,easing:f=js}=n;return{delay:c,duration:ws(u)?u(Math.sqrt(_*_+r*r)):u,easing:f,css:(d,g)=>{const m=g*_,h=g*r,p=d+g*e.width/t.width,b=d+g*e.height/t.height;return`transform: ${i} translate(${m}px, ${h}px) scale(${p}, ${b});`}}}const{SvelteComponent:xs,add_render_callback:er,append:Re,attr:ce,bubble:Bl,check_outros:tr,create_component:gl,create_in_transition:nr,create_out_transition:lr,destroy_component:vl,detach:or,element:st,flush:Qn,group_outros:ir,init:sr,insert:rr,listen:Jn,mount_component:$l,run_all:ar,safe_not_equal:_r,set_data:Hl,space:an,stop_propagation:Gl,text:Zl,transition_in:tn,transition_out:nn}=window.__gradio__svelte__internal,{createEventDispatcher:cr,onMount:ur}=window.__gradio__svelte__internal;function fr(l){let e,t;return e=new Is({}),{c(){gl(e.$$.fragment)},m(n,o){$l(e,n,o),t=!0},i(n){t||(tn(e.$$.fragment,n),t=!0)},o(n){nn(e.$$.fragment,n),t=!1},d(n){vl(e,n)}}}function pr(l){let e,t;return e=new Fs({}),{c(){gl(e.$$.fragment)},m(n,o){$l(e,n,o),t=!0},i(n){t||(tn(e.$$.fragment,n),t=!0)},o(n){nn(e.$$.fragment,n),t=!1},d(n){vl(e,n)}}}function dr(l){let e,t;return e=new Js({}),{c(){gl(e.$$.fragment)},m(n,o){$l(e,n,o),t=!0},i(n){t||(tn(e.$$.fragment,n),t=!0)},o(n){nn(e.$$.fragment,n),t=!1},d(n){vl(e,n)}}}function mr(l){let e,t,n,o,i,s,a,_,r,c,u,f,d,g,m,h,p,b,$,C,w,k,y,j,v,E,q,A;const B=[dr,pr,fr],M=[];function I(R,D){return R[1]==="warning"?0:R[1]==="info"?1:R[1]==="error"?2:-1}return~(n=I(l))&&(o=M[n]=B[n](l)),{c(){e=st("div"),t=st("div"),o&&o.c(),s=an(),a=st("div"),_=st("div"),r=Zl(l[1]),u=an(),f=st("div"),d=Zl(l[0]),h=an(),p=st("button"),b=st("span"),b.textContent="×",C=an(),w=st("div"),ce(t,"class",i="toast-icon "+l[1]+" svelte-z3l7qj"),ce(_,"class",c="toast-title "+l[1]+" svelte-z3l7qj"),ce(f,"class",g="toast-text "+l[1]+" svelte-z3l7qj"),ce(a,"class",m="toast-details "+l[1]+" svelte-z3l7qj"),ce(b,"aria-hidden","true"),ce(p,"class",$="toast-close "+l[1]+" svelte-z3l7qj"),ce(p,"type","button"),ce(p,"aria-label","Close"),ce(p,"data-testid","toast-close"),ce(w,"class",k="timer "+l[1]+" svelte-z3l7qj"),ce(e,"class",y="toast-body "+l[1]+" svelte-z3l7qj"),ce(e,"role","alert"),ce(e,"data-testid","toast-body")},m(R,D){rr(R,e,D),Re(e,t),~n&&M[n].m(t,null),Re(e,s),Re(e,a),Re(a,_),Re(_,r),Re(a,u),Re(a,f),Re(f,d),Re(e,h),Re(e,p),Re(p,b),Re(e,C),Re(e,w),E=!0,q||(A=[Jn(p,"click",l[2]),Jn(e,"click",Gl(l[4])),Jn(e,"keydown",Gl(l[5]))],q=!0)},p(R,[D]){let F=n;n=I(R),n!==F&&(o&&(ir(),nn(M[F],1,1,()=>{M[F]=null}),tr()),~n?(o=M[n],o||(o=M[n]=B[n](R),o.c()),tn(o,1),o.m(t,null)):o=null),(!E||D&2&&i!==(i="toast-icon "+R[1]+" svelte-z3l7qj"))&&ce(t,"class",i),(!E||D&2)&&Hl(r,R[1]),(!E||D&2&&c!==(c="toast-title "+R[1]+" svelte-z3l7qj"))&&ce(_,"class",c),(!E||D&1)&&Hl(d,R[0]),(!E||D&2&&g!==(g="toast-text "+R[1]+" svelte-z3l7qj"))&&ce(f,"class",g),(!E||D&2&&m!==(m="toast-details "+R[1]+" svelte-z3l7qj"))&&ce(a,"class",m),(!E||D&2&&$!==($="toast-close "+R[1]+" svelte-z3l7qj"))&&ce(p,"class",$),(!E||D&2&&k!==(k="timer "+R[1]+" svelte-z3l7qj"))&&ce(w,"class",k),(!E||D&2&&y!==(y="toast-body "+R[1]+" svelte-z3l7qj"))&&ce(e,"class",y)},i(R){E||(tn(o),R&&er(()=>{E&&(v&&v.end(1),j=nr(e,zl,{duration:200,delay:100}),j.start())}),E=!0)},o(R){nn(o),j&&j.invalidate(),R&&(v=lr(e,zl,{duration:200})),E=!1},d(R){R&&or(e),~n&&M[n].d(),R&&v&&v.end(),q=!1,ar(A)}}}function hr(l,e,t){let{message:n=""}=e,{type:o}=e,{id:i}=e;const s=cr();function a(){s("close",i)}ur(()=>{setTimeout(()=>{a()},1e4)});function _(c){Bl.call(this,l,c)}function r(c){Bl.call(this,l,c)}return l.$$set=c=>{"message"in c&&t(0,n=c.message),"type"in c&&t(1,o=c.type),"id"in c&&t(3,i=c.id)},[n,o,a,i,_,r]}class gr extends xs{constructor(e){super(),sr(this,e,hr,mr,_r,{message:0,type:1,id:3})}get message(){return this.$$.ctx[0]}set message(e){this.$$set({message:e}),Qn()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),Qn()}get id(){return this.$$.ctx[3]}set id(e){this.$$set({id:e}),Qn()}}const{SvelteComponent:vr,append:$r,attr:br,bubble:kr,check_outros:wr,create_animation:yr,create_component:Er,destroy_component:jr,detach:Ii,element:Di,ensure_array_like:Wl,fix_and_outro_and_destroy_block:Cr,fix_position:qr,flush:Ar,group_outros:Lr,init:Tr,insert:Ri,mount_component:Pr,noop:Or,safe_not_equal:Ir,set_style:Dr,space:Rr,transition_in:Si,transition_out:Vi,update_keyed_each:Sr}=window.__gradio__svelte__internal;function Ql(l,e,t){const n=l.slice();return n[2]=e[t].type,n[3]=e[t].message,n[4]=e[t].id,n}function Jl(l,e){let t,n,o,i,s=Or,a;return n=new gr({props:{type:e[2],message:e[3],id:e[4]}}),n.$on("close",e[1]),{key:l,first:null,c(){t=Di("div"),Er(n.$$.fragment),o=Rr(),Dr(t,"width","100%"),this.first=t},m(_,r){Ri(_,t,r),Pr(n,t,null),$r(t,o),a=!0},p(_,r){e=_;const c={};r&1&&(c.type=e[2]),r&1&&(c.message=e[3]),r&1&&(c.id=e[4]),n.$set(c)},r(){i=t.getBoundingClientRect()},f(){qr(t),s()},a(){s(),s=yr(t,i,Ks,{duration:300})},i(_){a||(Si(n.$$.fragment,_),a=!0)},o(_){Vi(n.$$.fragment,_),a=!1},d(_){_&&Ii(t),jr(n)}}}function Vr(l){let e,t=[],n=new Map,o,i=Wl(l[0]);const s=a=>a[4];for(let a=0;a<i.length;a+=1){let _=Ql(l,i,a),r=s(_);n.set(r,t[a]=Jl(r,_))}return{c(){e=Di("div");for(let a=0;a<t.length;a+=1)t[a].c();br(e,"class","toast-wrap svelte-pu0yf1")},m(a,_){Ri(a,e,_);for(let r=0;r<t.length;r+=1)t[r]&&t[r].m(e,null);o=!0},p(a,[_]){if(_&1){i=Wl(a[0]),Lr();for(let r=0;r<t.length;r+=1)t[r].r();t=Sr(t,_,s,1,a,i,n,e,Cr,Jl,null,Ql);for(let r=0;r<t.length;r+=1)t[r].a();wr()}},i(a){if(!o){for(let _=0;_<i.length;_+=1)Si(t[_]);o=!0}},o(a){for(let _=0;_<t.length;_+=1)Vi(t[_]);o=!1},d(a){a&&Ii(e);for(let _=0;_<t.length;_+=1)t[_].d()}}}function zr(l){l.length>0&&"parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0)}function Mr(l,e,t){let{messages:n=[]}=e;function o(i){kr.call(this,l,i)}return l.$$set=i=>{"messages"in i&&t(0,n=i.messages)},l.$$.update=()=>{l.$$.dirty&1&&zr(n)},[n,o]}class Nr extends vr{constructor(e){super(),Tr(this,e,Mr,Vr,Ir,{messages:0})}get messages(){return this.$$.ctx[0]}set messages(e){this.$$set({messages:e}),Ar()}}const{SvelteComponent:Fr,append:Xn,attr:tt,detach:Ur,init:Br,insert:Hr,noop:Yn,safe_not_equal:Gr,set_style:Jt,svg_element:_n}=window.__gradio__svelte__internal;function Zr(l){let e,t,n,o;return{c(){e=_n("svg"),t=_n("g"),n=_n("path"),o=_n("path"),tt(n,"d","M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z"),tt(o,"d","M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z"),tt(e,"width","100%"),tt(e,"height","100%"),tt(e,"viewBox","0 0 5 5"),tt(e,"version","1.1"),tt(e,"xmlns","http://www.w3.org/2000/svg"),tt(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),tt(e,"xml:space","preserve"),Jt(e,"fill","currentColor"),Jt(e,"fill-rule","evenodd"),Jt(e,"clip-rule","evenodd"),Jt(e,"stroke-linejoin","round"),Jt(e,"stroke-miterlimit","2")},m(i,s){Hr(i,e,s),Xn(e,t),Xn(t,n),Xn(t,o)},p:Yn,i:Yn,o:Yn,d(i){i&&Ur(e)}}}class zi extends Fr{constructor(e){super(),Br(this,e,null,Zr,Gr,{})}}const{SvelteComponent:Wr,append:rt,attr:cn,create_component:Qr,destroy_component:Jr,detach:Kn,element:Dt,flush:Xr,init:Yr,insert:xn,listen:Kr,mount_component:xr,safe_not_equal:ea,set_data:ta,space:el,text:Xl,transition_in:na,transition_out:la}=window.__gradio__svelte__internal,{createEventDispatcher:oa}=window.__gradio__svelte__internal;function ia(l){let e,t,n,o,i,s,a,_,r,c,u,f,d,g,m;return f=new zi({}),{c(){e=Dt("div"),t=Dt("h1"),t.textContent="API Docs",n=el(),o=Dt("p"),i=Xl(`No API Routes found for
		`),s=Dt("code"),a=Xl(l[0]),_=el(),r=Dt("p"),r.innerHTML=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,c=el(),u=Dt("button"),Qr(f.$$.fragment),cn(s,"class","svelte-e1ha0f"),cn(o,"class","attention svelte-e1ha0f"),cn(e,"class","wrap prose svelte-e1ha0f"),cn(u,"class","svelte-e1ha0f")},m(h,p){xn(h,e,p),rt(e,t),rt(e,n),rt(e,o),rt(o,i),rt(o,s),rt(s,a),rt(e,_),rt(e,r),xn(h,c,p),xn(h,u,p),xr(f,u,null),d=!0,g||(m=Kr(u,"click",l[2]),g=!0)},p(h,[p]){(!d||p&1)&&ta(a,h[0])},i(h){d||(na(f.$$.fragment,h),d=!0)},o(h){la(f.$$.fragment,h),d=!1},d(h){h&&(Kn(e),Kn(c),Kn(u)),Jr(f),g=!1,m()}}}function sa(l,e,t){const n=oa();let{root:o}=e;const i=()=>n("close");return l.$$set=s=>{"root"in s&&t(0,o=s.root)},[o,n,i]}class ra extends Wr{constructor(e){super(),Yr(this,e,sa,ia,ea,{root:0})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),Xr()}}const Mi="data:image/svg+xml,%3csvg%20width='28'%20height='28'%20viewBox='0%200%2028%2028'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M26.9425%202.94265C27.4632%202.42195%2027.4632%201.57773%2026.9425%201.05703C26.4218%200.536329%2025.5776%200.536329%2025.0569%201.05703L22.5713%203.54256C21.1213%202.59333%2019.5367%202.43378%2018.1753%202.64006C16.5495%202.88638%2015.1127%203.66838%2014.3905%204.39053L12.3905%206.39053C12.1405%206.64058%2012%206.97972%2012%207.33334C12%207.68697%2012.1405%208.0261%2012.3905%208.27615L19.7239%2015.6095C20.2446%2016.1302%2021.0888%2016.1302%2021.6095%2015.6095L23.6095%2013.6095C24.3316%2012.8873%2025.1136%2011.4505%2025.36%209.82475C25.5663%208.46312%2025.4066%206.87827%2024.4571%205.42807L26.9425%202.94265Z'%20fill='%233c4555'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M12.276%2012.9426C12.7967%2012.4219%2012.7967%2011.5777%2012.276%2011.057C11.7553%2010.5363%2010.9111%2010.5363%2010.3904%2011.057L8.66651%2012.7809L8.27615%2012.3905C8.0261%2012.1405%207.68697%2012%207.33334%2012C6.97972%2012%206.64058%2012.1405%206.39053%2012.3905L4.39053%2014.3905C3.66838%2015.1127%202.88638%2016.5495%202.64006%2018.1753C2.43377%2019.5367%202.59333%2021.1214%203.54262%2022.5714L1.05703%2025.057C0.536329%2025.5777%200.536329%2026.4219%201.05703%2026.9426C1.57773%2027.4633%202.42195%2027.4633%202.94265%2026.9426L5.42817%2024.4571C6.87835%2025.4066%208.46315%2025.5663%209.82475%2025.36C11.4505%2025.1136%2012.8873%2024.3316%2013.6095%2023.6095L15.6095%2021.6095C16.1302%2021.0888%2016.1302%2020.2446%2015.6095%2019.7239L15.2188%2019.3332L16.9426%2017.6093C17.4633%2017.0886%2017.4633%2016.2444%2016.9426%2015.7237C16.4219%2015.203%2015.5777%2015.203%2015.057%2015.7237L13.3332%2017.4475L10.5521%2014.6665L12.276%2012.9426Z'%20fill='%23FF7C00'/%3e%3c/svg%3e",{SvelteComponent:aa,append:Te,attr:Ze,create_component:Yl,destroy_component:Kl,detach:Ct,element:We,flush:xl,init:_a,insert:qt,listen:ca,mount_component:eo,noop:ua,safe_not_equal:fa,set_data:to,space:Kt,src_url_equal:pa,text:xt,transition_in:no,transition_out:lo}=window.__gradio__svelte__internal,{createEventDispatcher:da}=window.__gradio__svelte__internal;function ma(l){let e,t,n;return{c(){e=We("div"),t=Kt(),n=We("p"),n.textContent="API Recorder",Ze(e,"class","loading-dot self-baseline svelte-1i1gjw2"),Ze(n,"class","self-baseline btn-text svelte-1i1gjw2")},m(o,i){qt(o,e,i),qt(o,t,i),qt(o,n,i)},p:ua,d(o){o&&(Ct(e),Ct(t),Ct(n))}}}function oo(l){let e;return{c(){e=xt("s")},m(t,n){qt(t,e,n)},d(t){t&&Ct(e)}}}function ha(l){let e,t,n,o,i,s,a,_,r,c,u,f,d,g,m,h,p,b,$,C,w,k,y;u=new Tn({props:{size:"sm",variant:"secondary",elem_id:"start-api-recorder",$$slots:{default:[ma]},$$scope:{ctx:l}}}),u.$on("click",l[3]);let j=l[1]>1&&oo();return C=new zi({}),{c(){e=We("h2"),t=We("img"),o=Kt(),i=We("div"),s=xt(`API documentation
		`),a=We("div"),_=xt(l[0]),r=Kt(),c=We("span"),Yl(u.$$.fragment),f=Kt(),d=We("p"),g=We("span"),m=xt(l[1]),h=xt(" API endpoint"),j&&j.c(),p=We("br"),b=Kt(),$=We("button"),Yl(C.$$.fragment),pa(t.src,n=Mi)||Ze(t,"src",n),Ze(t,"alt",""),Ze(t,"class","svelte-1i1gjw2"),Ze(a,"class","url svelte-1i1gjw2"),Ze(i,"class","title svelte-1i1gjw2"),Ze(g,"class","url svelte-1i1gjw2"),Ze(c,"class","counts svelte-1i1gjw2"),Ze(e,"class","svelte-1i1gjw2"),Ze($,"class","svelte-1i1gjw2")},m(v,E){qt(v,e,E),Te(e,t),Te(e,o),Te(e,i),Te(i,s),Te(i,a),Te(a,_),Te(e,r),Te(e,c),eo(u,c,null),Te(c,f),Te(c,d),Te(d,g),Te(g,m),Te(d,h),j&&j.m(d,null),Te(d,p),qt(v,b,E),qt(v,$,E),eo(C,$,null),w=!0,k||(y=ca($,"click",l[4]),k=!0)},p(v,[E]){(!w||E&1)&&to(_,v[0]);const q={};E&32&&(q.$$scope={dirty:E,ctx:v}),u.$set(q),(!w||E&2)&&to(m,v[1]),v[1]>1?j||(j=oo(),j.c(),j.m(d,p)):j&&(j.d(1),j=null)},i(v){w||(no(u.$$.fragment,v),no(C.$$.fragment,v),w=!0)},o(v){lo(u.$$.fragment,v),lo(C.$$.fragment,v),w=!1},d(v){v&&(Ct(e),Ct(b),Ct($)),Kl(u),j&&j.d(),Kl(C),k=!1,y()}}}function ga(l,e,t){let{root:n}=e,{api_count:o}=e;const i=da(),s=()=>i("close",{api_recorder_visible:!0}),a=()=>i("close");return l.$$set=_=>{"root"in _&&t(0,n=_.root),"api_count"in _&&t(1,o=_.api_count)},[n,o,i,s,a]}class va extends aa{constructor(e){super(),_a(this,e,ga,ha,fa,{root:0,api_count:1})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),xl()}get api_count(){return this.$$.ctx[1]}set api_count(e){this.$$set({api_count:e}),xl()}}function Ue(l,e,t=null){return e===void 0?t==="py"?"None":null:l===null&&t==="py"?"None":e==="string"||e==="str"?t===null?l:'"'+l+'"':e==="number"?t===null?parseFloat(l):l:e==="boolean"||e=="bool"?t==="py"?(l=String(l),l==="true"?"True":"False"):t==="js"||t==="bash"?l:l==="true":e==="List[str]"?(l=JSON.stringify(l),l):e.startsWith("Literal['")?'"'+l+'"':t===null?l===""?null:JSON.parse(l):typeof l=="string"?l===""?t==="py"?"None":"null":l:(t==="bash"&&(l=rl(l)),t==="py"&&(l=al(l)),$a(l))}function Ni(l){if(typeof l=="object"&&l!==null&&l.hasOwnProperty("url")&&l.hasOwnProperty("meta")&&typeof l.meta=="object"&&l.meta!==null&&l.meta._type==="gradio.FileData")return!0;if(typeof l=="object"&&l!==null){for(let e in l)if(typeof l[e]=="object"&&Ni(l[e]))return!0}return!1}function rl(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?{path:l.url}:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=rl(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=rl(l[e])}),l)}function al(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?`handle_file('${l.url}')`:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=al(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=al(l[e])}),l)}function $a(l){let e=JSON.stringify(l,(o,i)=>i===null?"UNQUOTEDNone":typeof i=="string"&&i.startsWith("handle_file(")&&i.endsWith(")")?`UNQUOTED${i}`:i);const t=/"UNQUOTEDhandle_file\(([^)]*)\)"/g;e=e.replace(t,(o,i)=>`handle_file(${i})`);const n=/"UNQUOTEDNone"/g;return e.replace(n,"None")}const{SvelteComponent:ba,append:ue,attr:ct,check_outros:ka,create_component:wa,destroy_component:ya,destroy_each:Ea,detach:ve,element:ze,empty:Fi,ensure_array_like:io,flush:un,group_outros:ja,init:Ca,insert:$e,mount_component:qa,noop:Aa,safe_not_equal:La,set_data:At,set_style:kt,space:wt,text:Ce,toggle_class:so,transition_in:$n,transition_out:_l}=window.__gradio__svelte__internal;function ro(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].python_type,n[6]=e[t].component,n[7]=e[t].parameter_name,n[8]=e[t].parameter_has_default,n[9]=e[t].parameter_default,n[11]=t,n}function ao(l){let e;return{c(){e=Ce("s")},m(t,n){$e(t,e,n)},d(t){t&&ve(e)}}}function Ta(l){let e=(l[2][l[11]].type||"any")+"",t;return{c(){t=Ce(e)},m(n,o){$e(n,t,o)},p(n,o){o&4&&e!==(e=(n[2][n[11]].type||"any")+"")&&At(t,e)},d(n){n&&ve(t)}}}function Pa(l){let e=l[5].type+"",t,n,o=l[8]&&l[9]===null&&_o();return{c(){t=Ce(e),o&&o.c(),n=Fi()},m(i,s){$e(i,t,s),o&&o.m(i,s),$e(i,n,s)},p(i,s){s&2&&e!==(e=i[5].type+"")&&At(t,e),i[8]&&i[9]===null?o||(o=_o(),o.c(),o.m(n.parentNode,n)):o&&(o.d(1),o=null)},d(i){i&&(ve(t),ve(n)),o&&o.d(i)}}}function _o(l){let e;return{c(){e=Ce(` |
							None`)},m(t,n){$e(t,e,n)},d(t){t&&ve(e)}}}function Oa(l){let e,t,n=Ue(l[9],l[5].type,"py")+"",o;return{c(){e=ze("span"),e.textContent="Default: ",t=ze("span"),o=Ce(n),ct(t,"class","code svelte-1yt946s"),kt(t,"font-size","var(--text-sm)")},m(i,s){$e(i,e,s),$e(i,t,s),ue(t,o)},p(i,s){s&2&&n!==(n=Ue(i[9],i[5].type,"py")+"")&&At(o,n)},d(i){i&&(ve(e),ve(t))}}}function Ia(l){let e;return{c(){e=ze("span"),e.textContent="Required",kt(e,"font-weight","bold")},m(t,n){$e(t,e,n)},p:Aa,d(t){t&&ve(e)}}}function co(l){let e,t,n,o,i,s=(l[3]!=="bash"&&l[7]?l[7]:"["+l[11]+"]")+"",a,_,r,c,u,f,d,g=l[4]+"",m,h,p=l[6]+"",b,$,C;function w(q,A){return q[3]==="python"?Pa:Ta}let k=w(l),y=k(l);function j(q,A){return!q[8]||q[3]=="bash"?Ia:Oa}let v=j(l),E=v(l);return{c(){e=ze("hr"),t=wt(),n=ze("div"),o=ze("p"),i=ze("span"),a=Ce(s),_=wt(),r=ze("span"),y.c(),c=wt(),E.c(),u=wt(),f=ze("p"),d=Ce('The input value that is provided in the "'),m=Ce(g),h=Ce('" '),b=Ce(p),$=Ce(`
				component.`),C=wt(),ct(e,"class","hr svelte-1yt946s"),ct(i,"class","code svelte-1yt946s"),kt(i,"margin-right","10px"),ct(r,"class","code highlight svelte-1yt946s"),kt(r,"margin-right","10px"),kt(o,"white-space","nowrap"),kt(o,"overflow-x","auto"),ct(f,"class","desc svelte-1yt946s"),kt(n,"margin","10px")},m(q,A){$e(q,e,A),$e(q,t,A),$e(q,n,A),ue(n,o),ue(o,i),ue(i,a),ue(o,_),ue(o,r),y.m(r,null),ue(o,c),E.m(o,null),ue(n,u),ue(n,f),ue(f,d),ue(f,m),ue(f,h),ue(f,b),ue(f,$),ue(n,C)},p(q,A){A&10&&s!==(s=(q[3]!=="bash"&&q[7]?q[7]:"["+q[11]+"]")+"")&&At(a,s),k===(k=w(q))&&y?y.p(q,A):(y.d(1),y=k(q),y&&(y.c(),y.m(r,null))),v===(v=j(q))&&E?E.p(q,A):(E.d(1),E=v(q),E&&(E.c(),E.m(o,null))),A&2&&g!==(g=q[4]+"")&&At(m,g),A&2&&p!==(p=q[6]+"")&&At(b,p)},d(q){q&&(ve(e),ve(t),ve(n)),y.d(),E.d()}}}function uo(l){let e,t,n;return t=new Pi({props:{margin:!1}}),{c(){e=ze("div"),wa(t.$$.fragment),ct(e,"class","load-wrap")},m(o,i){$e(o,e,i),qa(t,e,null),n=!0},i(o){n||($n(t.$$.fragment,o),n=!0)},o(o){_l(t.$$.fragment,o),n=!1},d(o){o&&ve(e),ya(t)}}}function Da(l){let e,t,n,o=l[1].length+"",i,s,a,_,r,c,u,f,d=l[1].length!=1&&ao(),g=io(l[1]),m=[];for(let p=0;p<g.length;p+=1)m[p]=co(ro(l,g,p));let h=l[0]&&uo();return{c(){e=ze("h4"),t=ze("div"),t.innerHTML='<div class="toggle-dot svelte-1yt946s"></div>',n=Ce(`
	Accepts `),i=Ce(o),s=Ce(" parameter"),d&&d.c(),a=Ce(":"),_=wt(),r=ze("div");for(let p=0;p<m.length;p+=1)m[p].c();c=wt(),h&&h.c(),u=Fi(),ct(t,"class","toggle-icon svelte-1yt946s"),ct(e,"class","svelte-1yt946s"),so(r,"hide",l[0])},m(p,b){$e(p,e,b),ue(e,t),ue(e,n),ue(e,i),ue(e,s),d&&d.m(e,null),ue(e,a),$e(p,_,b),$e(p,r,b);for(let $=0;$<m.length;$+=1)m[$]&&m[$].m(r,null);$e(p,c,b),h&&h.m(p,b),$e(p,u,b),f=!0},p(p,[b]){if((!f||b&2)&&o!==(o=p[1].length+"")&&At(i,o),p[1].length!=1?d||(d=ao(),d.c(),d.m(e,a)):d&&(d.d(1),d=null),b&14){g=io(p[1]);let $;for($=0;$<g.length;$+=1){const C=ro(p,g,$);m[$]?m[$].p(C,b):(m[$]=co(C),m[$].c(),m[$].m(r,null))}for(;$<m.length;$+=1)m[$].d(1);m.length=g.length}(!f||b&1)&&so(r,"hide",p[0]),p[0]?h?b&1&&$n(h,1):(h=uo(),h.c(),$n(h,1),h.m(u.parentNode,u)):h&&(ja(),_l(h,1,1,()=>{h=null}),ka())},i(p){f||($n(h),f=!0)},o(p){_l(h),f=!1},d(p){p&&(ve(e),ve(_),ve(r),ve(c),ve(u)),d&&d.d(),Ea(m,p),h&&h.d(p)}}}function Ra(l,e,t){let{is_running:n}=e,{endpoint_returns:o}=e,{js_returns:i}=e,{current_language:s}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,o=a.endpoint_returns),"js_returns"in a&&t(2,i=a.js_returns),"current_language"in a&&t(3,s=a.current_language)},[n,o,i,s]}class Sa extends ba{constructor(e){super(),Ca(this,e,Ra,Da,La,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),un()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),un()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),un()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),un()}}const{SvelteComponent:Va,create_component:za,destroy_component:Ma,detach:Na,flush:Fa,init:Ua,insert:Ba,mount_component:Ha,safe_not_equal:Ga,set_data:Za,text:Wa,transition_in:Qa,transition_out:Ja}=window.__gradio__svelte__internal;function Xa(l){let e;return{c(){e=Wa(l[0])},m(t,n){Ba(t,e,n)},p(t,n){n&1&&Za(e,t[0])},d(t){t&&Na(e)}}}function Ya(l){let e,t;return e=new Tn({props:{size:"sm",$$slots:{default:[Xa]},$$scope:{ctx:l}}}),e.$on("click",l[1]),{c(){za(e.$$.fragment)},m(n,o){Ha(e,n,o),t=!0},p(n,[o]){const i={};o&9&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(Qa(e.$$.fragment,n),t=!0)},o(n){Ja(e.$$.fragment,n),t=!1},d(n){Ma(e,n)}}}function Ka(l,e,t){let{code:n}=e,o="copy";function i(){navigator.clipboard.writeText(n),t(0,o="copied!"),setTimeout(()=>{t(0,o="copy")},1500)}return l.$$set=s=>{"code"in s&&t(2,n=s.code)},[o,i,n]}class lt extends Va{constructor(e){super(),Ua(this,e,Ka,Ya,Ga,{code:2})}get code(){return this.$$.ctx[2]}set code(e){this.$$set({code:e}),Fa()}}const{SvelteComponent:xa,append:bl,attr:Tt,check_outros:e_,create_component:Pn,destroy_component:On,detach:Ke,element:xe,flush:t_,group_outros:n_,init:l_,insert:et,mount_component:In,noop:kl,safe_not_equal:o_,space:wl,transition_in:Ut,transition_out:Bt}=window.__gradio__svelte__internal;function i_(l){let e,t,n,o,i,s;return t=new lt({props:{code:mo}}),{c(){e=xe("div"),Pn(t.$$.fragment),n=wl(),o=xe("div"),i=xe("pre"),i.textContent=`$ ${mo}`,Tt(e,"class","copy svelte-hq8ezf"),Tt(i,"class","svelte-hq8ezf")},m(a,_){et(a,e,_),In(t,e,null),et(a,n,_),et(a,o,_),bl(o,i),s=!0},p:kl,i(a){s||(Ut(t.$$.fragment,a),s=!0)},o(a){Bt(t.$$.fragment,a),s=!1},d(a){a&&(Ke(e),Ke(n),Ke(o)),On(t)}}}function s_(l){let e,t,n,o,i,s;return t=new lt({props:{code:po}}),{c(){e=xe("div"),Pn(t.$$.fragment),n=wl(),o=xe("div"),i=xe("pre"),i.textContent=`$ ${po}`,Tt(e,"class","copy svelte-hq8ezf"),Tt(i,"class","svelte-hq8ezf")},m(a,_){et(a,e,_),In(t,e,null),et(a,n,_),et(a,o,_),bl(o,i),s=!0},p:kl,i(a){s||(Ut(t.$$.fragment,a),s=!0)},o(a){Bt(t.$$.fragment,a),s=!1},d(a){a&&(Ke(e),Ke(n),Ke(o)),On(t)}}}function r_(l){let e,t,n,o,i,s;return t=new lt({props:{code:fo}}),{c(){e=xe("div"),Pn(t.$$.fragment),n=wl(),o=xe("div"),i=xe("pre"),i.textContent=`$ ${fo}`,Tt(e,"class","copy svelte-hq8ezf"),Tt(i,"class","svelte-hq8ezf")},m(a,_){et(a,e,_),In(t,e,null),et(a,n,_),et(a,o,_),bl(o,i),s=!0},p:kl,i(a){s||(Ut(t.$$.fragment,a),s=!0)},o(a){Bt(t.$$.fragment,a),s=!1},d(a){a&&(Ke(e),Ke(n),Ke(o)),On(t)}}}function a_(l){let e,t,n,o;const i=[r_,s_,i_],s=[];function a(_,r){return _[0]==="python"?0:_[0]==="javascript"?1:_[0]==="bash"?2:-1}return~(t=a(l))&&(n=s[t]=i[t](l)),{c(){e=xe("code"),n&&n.c(),Tt(e,"class","svelte-hq8ezf")},m(_,r){et(_,e,r),~t&&s[t].m(e,null),o=!0},p(_,r){let c=t;t=a(_),t===c?~t&&s[t].p(_,r):(n&&(n_(),Bt(s[c],1,1,()=>{s[c]=null}),e_()),~t?(n=s[t],n?n.p(_,r):(n=s[t]=i[t](_),n.c()),Ut(n,1),n.m(e,null)):n=null)},i(_){o||(Ut(n),o=!0)},o(_){Bt(n),o=!1},d(_){_&&Ke(e),~t&&s[t].d()}}}function __(l){let e,t;return e=new on({props:{border_mode:"contrast",$$slots:{default:[a_]},$$scope:{ctx:l}}}),{c(){Pn(e.$$.fragment)},m(n,o){In(e,n,o),t=!0},p(n,[o]){const i={};o&3&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(Ut(e.$$.fragment,n),t=!0)},o(n){Bt(e.$$.fragment,n),t=!1},d(n){On(e,n)}}}let fo="pip install gradio_client",po="npm i -D @gradio/client",mo="curl --version";function c_(l,e,t){let{current_language:n}=e;return l.$$set=o=>{"current_language"in o&&t(0,n=o.current_language)},[n]}class u_ extends xa{constructor(e){super(),l_(this,e,c_,__,o_,{current_language:0})}get current_language(){return this.$$.ctx[0]}set current_language(e){this.$$set({current_language:e}),t_()}}const{SvelteComponent:f_,append:Nt,attr:kn,detach:yl,element:wn,empty:p_,flush:tl,init:d_,insert:El,noop:ho,safe_not_equal:m_,set_data:Ui,text:yn}=window.__gradio__svelte__internal;function h_(l){let e,t,n,o;return{c(){e=wn("h3"),t=yn(`fn_index:
		`),n=wn("span"),o=yn(l[1]),kn(n,"class","post svelte-41kcm6"),kn(e,"class","svelte-41kcm6")},m(i,s){El(i,e,s),Nt(e,t),Nt(e,n),Nt(n,o)},p(i,s){s&2&&Ui(o,i[1])},d(i){i&&yl(e)}}}function g_(l){let e,t,n,o="/"+l[0],i;return{c(){e=wn("h3"),t=yn(`api_name:
		`),n=wn("span"),i=yn(o),kn(n,"class","post svelte-41kcm6"),kn(e,"class","svelte-41kcm6")},m(s,a){El(s,e,a),Nt(e,t),Nt(e,n),Nt(n,i)},p(s,a){a&1&&o!==(o="/"+s[0])&&Ui(i,o)},d(s){s&&yl(e)}}}function v_(l){let e;function t(i,s){return i[2]?g_:h_}let n=t(l),o=n(l);return{c(){o.c(),e=p_()},m(i,s){o.m(i,s),El(i,e,s)},p(i,[s]){n===(n=t(i))&&o?o.p(i,s):(o.d(1),o=n(i),o&&(o.c(),o.m(e.parentNode,e)))},i:ho,o:ho,d(i){i&&yl(e),o.d(i)}}}function $_(l,e,t){let{api_name:n=null}=e,{fn_index:o=null}=e,{named:i}=e;return l.$$set=s=>{"api_name"in s&&t(0,n=s.api_name),"fn_index"in s&&t(1,o=s.fn_index),"named"in s&&t(2,i=s.named)},[n,o,i]}class Bi extends f_{constructor(e){super(),d_(this,e,$_,v_,m_,{api_name:0,fn_index:1,named:2})}get api_name(){return this.$$.ctx[0]}set api_name(e){this.$$set({api_name:e}),tl()}get fn_index(){return this.$$.ctx[1]}set fn_index(e){this.$$set({fn_index:e}),tl()}get named(){return this.$$.ctx[2]}set named(e){this.$$set({named:e}),tl()}}const{SvelteComponent:b_,append:P,attr:ae,binding_callbacks:nl,check_outros:go,create_component:mt,destroy_component:ht,destroy_each:En,detach:X,element:te,empty:Hi,ensure_array_like:ut,flush:$t,group_outros:vo,init:k_,insert:Y,mount_component:gt,noop:w_,safe_not_equal:y_,set_data:Ee,space:Dn,text:T,transition_in:Be,transition_out:He}=window.__gradio__svelte__internal;function $o(l,e,t){const n=l.slice();return n[24]=e[t].label,n[19]=e[t].parameter_name,n[25]=e[t].type,n[17]=e[t].python_type,n[26]=e[t].component,n[18]=e[t].example_input,n[27]=e[t].serializer,n[23]=t,n}function bo(l,e,t){const n=l.slice();return n[24]=e[t].label,n[19]=e[t].parameter_name,n[25]=e[t].type,n[17]=e[t].python_type,n[26]=e[t].component,n[18]=e[t].example_input,n[27]=e[t].serializer,n[23]=t,n}function ko(l,e,t){const n=l.slice();return n[26]=e[t].component,n[18]=e[t].example_input,n[23]=t,n}function wo(l,e,t){const n=l.slice();return n[17]=e[t].python_type,n[18]=e[t].example_input,n[19]=e[t].parameter_name,n[20]=e[t].parameter_has_default,n[21]=e[t].parameter_default,n[23]=t,n}function E_(l){let e,t;return e=new Bi({props:{named:l[5],fn_index:l[1]}}),{c(){mt(e.$$.fragment)},m(n,o){gt(e,n,o),t=!0},p(n,o){const i={};o[0]&32&&(i.named=n[5]),o[0]&2&&(i.fn_index=n[1]),e.$set(i)},i(n){t||(Be(e.$$.fragment,n),t=!0)},o(n){He(e.$$.fragment,n),t=!1},d(n){ht(e,n)}}}function j_(l){let e,t;return e=new Bi({props:{named:l[5],api_name:l[0].api_name}}),{c(){mt(e.$$.fragment)},m(n,o){gt(e,n,o),t=!0},p(n,o){const i={};o[0]&32&&(i.named=n[5]),o[0]&1&&(i.api_name=n[0].api_name),e.$set(i)},i(n){t||(Be(e.$$.fragment,n),t=!0)},o(n){He(e.$$.fragment,n),t=!1},d(n){ht(e,n)}}}function C_(l){let e,t;return e=new on({props:{$$slots:{default:[L_]},$$scope:{ctx:l}}}),{c(){mt(e.$$.fragment)},m(n,o){gt(e,n,o),t=!0},p(n,o){const i={};o[0]&533|o[1]&1&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(Be(e.$$.fragment,n),t=!0)},o(n){He(e.$$.fragment,n),t=!1},d(n){ht(e,n)}}}function q_(l){let e,t;return e=new on({props:{$$slots:{default:[D_]},$$scope:{ctx:l}}}),{c(){mt(e.$$.fragment)},m(n,o){gt(e,n,o),t=!0},p(n,o){const i={};o[0]&319|o[1]&1&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(Be(e.$$.fragment,n),t=!0)},o(n){He(e.$$.fragment,n),t=!1},d(n){ht(e,n)}}}function A_(l){let e,t;return e=new on({props:{$$slots:{default:[S_]},$$scope:{ctx:l}}}),{c(){mt(e.$$.fragment)},m(n,o){gt(e,n,o),t=!0},p(n,o){const i={};o[0]&157|o[1]&1&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(Be(e.$$.fragment,n),t=!0)},o(n){He(e.$$.fragment,n),t=!1},d(n){ht(e,n)}}}function yo(l){let e;return{c(){e=T(",")},m(t,n){Y(t,e,n)},d(t){t&&X(e)}}}function Eo(l){let e,t=Ue(l[18],l[17].type,"bash")+"",n,o,i=l[23]<l[4].length-1&&yo();return{c(){e=T(`
    `),n=T(t),i&&i.c(),o=Hi()},m(s,a){Y(s,e,a),Y(s,n,a),i&&i.m(s,a),Y(s,o,a)},p(s,a){a[0]&16&&t!==(t=Ue(s[18],s[17].type,"bash")+"")&&Ee(n,t),s[23]<s[4].length-1?i||(i=yo(),i.c(),i.m(o.parentNode,o)):i&&(i.d(1),i=null)},d(s){s&&(X(e),X(n),X(o)),i&&i.d(s)}}}function L_(l){let e,t,n,o,i,s,a,_,r,c=l[0].api_name+"",u,f,d="{",g,m,h,p="}",b,$,C="{",w,k,y="}",j,v,E,q,A=l[0].api_name+"",B,M,I;n=new lt({props:{code:l[9]?.innerText}});let R=ut(l[4]),D=[];for(let F=0;F<R.length;F+=1)D[F]=Eo($o(l,R,F));return{c(){e=te("code"),t=te("div"),mt(n.$$.fragment),o=Dn(),i=te("div"),s=te("pre"),a=T("curl -X POST "),_=T(l[2]),r=T("call/"),u=T(c),f=T(` -s -H "Content-Type: application/json" -d '`),g=T(d),m=T(`
  "data": [`);for(let F=0;F<D.length;F+=1)D[F].c();h=T(`
]`),b=T(p),$=T(`' \\
  | awk -F'"' '`),w=T(C),k=T(" print $4"),j=T(y),v=T(`'  \\
  | read EVENT_ID; curl -N `),E=T(l[2]),q=T("call/"),B=T(A),M=T("/$EVENT_ID"),ae(t,"class","copy svelte-114qcyq"),ae(s,"class","svelte-114qcyq"),ae(e,"class","svelte-114qcyq")},m(F,le){Y(F,e,le),P(e,t),gt(n,t,null),P(e,o),P(e,i),P(i,s),P(s,a),P(s,_),P(s,r),P(s,u),P(s,f),P(s,g),P(s,m);for(let ee=0;ee<D.length;ee+=1)D[ee]&&D[ee].m(s,null);P(s,h),P(s,b),P(s,$),P(s,w),P(s,k),P(s,j),P(s,v),P(s,E),P(s,q),P(s,B),P(s,M),l[15](i),I=!0},p(F,le){const ee={};if(le[0]&512&&(ee.code=F[9]?.innerText),n.$set(ee),(!I||le[0]&4)&&Ee(_,F[2]),(!I||le[0]&1)&&c!==(c=F[0].api_name+"")&&Ee(u,c),le[0]&16){R=ut(F[4]);let oe;for(oe=0;oe<R.length;oe+=1){const vt=$o(F,R,oe);D[oe]?D[oe].p(vt,le):(D[oe]=Eo(vt),D[oe].c(),D[oe].m(s,h))}for(;oe<D.length;oe+=1)D[oe].d(1);D.length=R.length}(!I||le[0]&4)&&Ee(E,F[2]),(!I||le[0]&1)&&A!==(A=F[0].api_name+"")&&Ee(B,A)},i(F){I||(Be(n.$$.fragment,F),I=!0)},o(F){He(n.$$.fragment,F),I=!1},d(F){F&&X(e),ht(n),En(D,F),l[15](null)}}}function jo(l){let e,t,n,o=l[18].url+"",i,s,a=l[26]+"",_,r,c,u;return{c(){e=T(`
const response_`),t=T(l[23]),n=T(' = await fetch("'),i=T(o),s=T(`");
const example`),_=T(a),r=T(" = await response_"),c=T(l[23]),u=T(`.blob();
						`)},m(f,d){Y(f,e,d),Y(f,t,d),Y(f,n,d),Y(f,i,d),Y(f,s,d),Y(f,_,d),Y(f,r,d),Y(f,c,d),Y(f,u,d)},p:w_,d(f){f&&(X(e),X(t),X(n),X(i),X(s),X(_),X(r),X(c),X(u))}}}function T_(l){let e;return{c(){e=T(l[1])},m(t,n){Y(t,e,n)},p(t,n){n[0]&2&&Ee(e,t[1])},d(t){t&&X(e)}}}function P_(l){let e,t,n=l[0].api_name+"",o,i;return{c(){e=te("span"),t=T('"/'),o=T(n),i=T('"'),ae(e,"class","api-name svelte-114qcyq")},m(s,a){Y(s,e,a),P(e,t),P(e,o),P(e,i)},p(s,a){a[0]&1&&n!==(n=s[0].api_name+"")&&Ee(o,n)},d(s){s&&X(e)}}}function O_(l){let e,t,n=l[19]+"",o,i,s=Ue(l[18],l[17].type,"js")+"",a,_;return{c(){e=T(`		
		`),t=te("span"),o=T(n),i=T(": "),a=T(s),_=T(", "),ae(t,"class","example-inputs")},m(r,c){Y(r,e,c),Y(r,t,c),P(t,o),P(t,i),P(t,a),Y(r,_,c)},p(r,c){c[0]&16&&n!==(n=r[19]+"")&&Ee(o,n),c[0]&16&&s!==(s=Ue(r[18],r[17].type,"js")+"")&&Ee(a,s)},d(r){r&&(X(e),X(t),X(_))}}}function I_(l){let e,t,n=l[19]+"",o,i,s=l[26]+"",a,_,r;return{c(){e=T(`
				`),t=te("span"),o=T(n),i=T(": example"),a=T(s),_=T(", "),r=te("span"),r.innerHTML="",ae(t,"class","example-inputs"),ae(r,"class","desc svelte-114qcyq")},m(c,u){Y(c,e,u),Y(c,t,u),P(t,o),P(t,i),P(t,a),Y(c,_,u),Y(c,r,u)},p(c,u){u[0]&16&&n!==(n=c[19]+"")&&Ee(o,n),u[0]&16&&s!==(s=c[26]+"")&&Ee(a,s)},d(c){c&&(X(e),X(t),X(_),X(r))}}}function Co(l){let e,t;function n(s,a){return a[0]&16&&(e=null),e==null&&(e=!!s[11].includes(s[26])),e?I_:O_}let o=n(l,[-1,-1]),i=o(l);return{c(){i.c(),t=Hi()},m(s,a){i.m(s,a),Y(s,t,a)},p(s,a){o===(o=n(s,a))&&i?i.p(s,a):(i.d(1),i=o(s),i&&(i.c(),i.m(t.parentNode,t)))},d(s){s&&X(t),i.d(s)}}}function D_(l){let e,t,n,o,i,s,a,_,r,c,u=(l[3]||l[2])+"",f,d,g,m,h,p;n=new lt({props:{code:l[8]?.innerText}});let b=ut(l[12]),$=[];for(let v=0;v<b.length;v+=1)$[v]=jo(ko(l,b,v));function C(v,E){return v[5]?P_:T_}let w=C(l),k=w(l),y=ut(l[4]),j=[];for(let v=0;v<y.length;v+=1)j[v]=Co(bo(l,y,v));return{c(){e=te("code"),t=te("div"),mt(n.$$.fragment),o=Dn(),i=te("div"),s=te("pre"),a=T(`import { Client } from "@gradio/client";
`);for(let v=0;v<$.length;v+=1)$[v].c();_=T(`
const client = await Client.connect(`),r=te("span"),c=T('"'),f=T(u),d=T('"'),g=T(`);
const result = await client.predict(`),k.c(),m=T(", { ");for(let v=0;v<j.length;v+=1)j[v].c();h=T(`
});

console.log(result.data);
`),ae(t,"class","copy svelte-114qcyq"),ae(r,"class","token string svelte-114qcyq"),ae(s,"class","svelte-114qcyq"),ae(e,"class","svelte-114qcyq")},m(v,E){Y(v,e,E),P(e,t),gt(n,t,null),P(e,o),P(e,i),P(i,s),P(s,a);for(let q=0;q<$.length;q+=1)$[q]&&$[q].m(s,null);P(s,_),P(s,r),P(r,c),P(r,f),P(r,d),P(s,g),k.m(s,null),P(s,m);for(let q=0;q<j.length;q+=1)j[q]&&j[q].m(s,null);P(s,h),l[14](i),p=!0},p(v,E){const q={};if(E[0]&256&&(q.code=v[8]?.innerText),n.$set(q),E[0]&4096){b=ut(v[12]);let A;for(A=0;A<b.length;A+=1){const B=ko(v,b,A);$[A]?$[A].p(B,E):($[A]=jo(B),$[A].c(),$[A].m(s,_))}for(;A<$.length;A+=1)$[A].d(1);$.length=b.length}if((!p||E[0]&12)&&u!==(u=(v[3]||v[2])+"")&&Ee(f,u),w===(w=C(v))&&k?k.p(v,E):(k.d(1),k=w(v),k&&(k.c(),k.m(s,m))),E[0]&2064){y=ut(v[4]);let A;for(A=0;A<y.length;A+=1){const B=bo(v,y,A);j[A]?j[A].p(B,E):(j[A]=Co(B),j[A].c(),j[A].m(s,h))}for(;A<j.length;A+=1)j[A].d(1);j.length=y.length}},i(v){p||(Be(n.$$.fragment,v),p=!0)},o(v){He(n.$$.fragment,v),p=!1},d(v){v&&X(e),ht(n),En($,v),k.d(),En(j,v),l[14](null)}}}function R_(l){let e;return{c(){e=T(", handle_file")},m(t,n){Y(t,e,n)},d(t){t&&X(e)}}}function qo(l){let e,t=l[19]?l[19]+"=":"",n,o,i=Ue(l[20]?l[21]:l[18],l[17].type,"py")+"",s,a;return{c(){e=T(`
		`),n=T(t),o=te("span"),s=T(i),a=T(",")},m(_,r){Y(_,e,r),Y(_,n,r),Y(_,o,r),P(o,s),Y(_,a,r)},p(_,r){r[0]&16&&t!==(t=_[19]?_[19]+"=":"")&&Ee(n,t),r[0]&16&&i!==(i=Ue(_[20]?_[21]:_[18],_[17].type,"py")+"")&&Ee(s,i)},d(_){_&&(X(e),X(n),X(o),X(a))}}}function S_(l){let e,t,n,o,i,s,a,_,r,c,u,f,d,g=(l[3]||l[2])+"",m,h,p,b,$,C,w,k,y=l[0].api_name+"",j,v,E,q,A,B;n=new lt({props:{code:l[7]?.innerText}});let M=l[10]&&R_(),I=ut(l[4]),R=[];for(let D=0;D<I.length;D+=1)R[D]=qo(wo(l,I,D));return{c(){e=te("code"),t=te("div"),mt(n.$$.fragment),o=Dn(),i=te("div"),s=te("pre"),a=te("span"),a.textContent="from",_=T(" gradio_client "),r=te("span"),r.textContent="import",c=T(" Client"),M&&M.c(),u=T(`

client = Client(`),f=te("span"),d=T('"'),m=T(g),h=T('"'),p=T(`)
result = client.`),b=te("span"),b.textContent="predict",$=T("(");for(let D=0;D<R.length;D+=1)R[D].c();C=T(`
		api_name=`),w=te("span"),k=T('"/'),j=T(y),v=T('"'),E=T(`
)
`),q=te("span"),q.textContent="print",A=T("(result)"),ae(t,"class","copy svelte-114qcyq"),ae(a,"class","highlight"),ae(r,"class","highlight"),ae(f,"class","token string svelte-114qcyq"),ae(b,"class","highlight"),ae(w,"class","api-name svelte-114qcyq"),ae(q,"class","highlight"),ae(s,"class","svelte-114qcyq"),ae(e,"class","svelte-114qcyq")},m(D,F){Y(D,e,F),P(e,t),gt(n,t,null),P(e,o),P(e,i),P(i,s),P(s,a),P(s,_),P(s,r),P(s,c),M&&M.m(s,null),P(s,u),P(s,f),P(f,d),P(f,m),P(f,h),P(s,p),P(s,b),P(s,$);for(let le=0;le<R.length;le+=1)R[le]&&R[le].m(s,null);P(s,C),P(s,w),P(w,k),P(w,j),P(w,v),P(s,E),P(s,q),P(s,A),l[13](i),B=!0},p(D,F){const le={};if(F[0]&128&&(le.code=D[7]?.innerText),n.$set(le),(!B||F[0]&12)&&g!==(g=(D[3]||D[2])+"")&&Ee(m,g),F[0]&16){I=ut(D[4]);let ee;for(ee=0;ee<I.length;ee+=1){const oe=wo(D,I,ee);R[ee]?R[ee].p(oe,F):(R[ee]=qo(oe),R[ee].c(),R[ee].m(s,C))}for(;ee<R.length;ee+=1)R[ee].d(1);R.length=I.length}(!B||F[0]&1)&&y!==(y=D[0].api_name+"")&&Ee(j,y)},i(D){B||(Be(n.$$.fragment,D),B=!0)},o(D){He(n.$$.fragment,D),B=!1},d(D){D&&X(e),ht(n),M&&M.d(),En(R,D),l[13](null)}}}function V_(l){let e,t,n,o,i,s,a;const _=[j_,E_],r=[];function c(g,m){return g[5]?0:1}t=c(l),n=r[t]=_[t](l);const u=[A_,q_,C_],f=[];function d(g,m){return g[6]==="python"?0:g[6]==="javascript"?1:g[6]==="bash"?2:-1}return~(i=d(l))&&(s=f[i]=u[i](l)),{c(){e=te("div"),n.c(),o=Dn(),s&&s.c(),ae(e,"class","container svelte-114qcyq")},m(g,m){Y(g,e,m),r[t].m(e,null),P(e,o),~i&&f[i].m(e,null),a=!0},p(g,m){let h=t;t=c(g),t===h?r[t].p(g,m):(vo(),He(r[h],1,1,()=>{r[h]=null}),go(),n=r[t],n?n.p(g,m):(n=r[t]=_[t](g),n.c()),Be(n,1),n.m(e,o));let p=i;i=d(g),i===p?~i&&f[i].p(g,m):(s&&(vo(),He(f[p],1,1,()=>{f[p]=null}),go()),~i?(s=f[i],s?s.p(g,m):(s=f[i]=u[i](g),s.c()),Be(s,1),s.m(e,null)):s=null)},i(g){a||(Be(n),Be(s),a=!0)},o(g){He(n),He(s),a=!1},d(g){g&&X(e),r[t].d(),~i&&f[i].d()}}}function z_(l,e,t){let{dependency:n}=e,{dependency_index:o}=e,{root:i}=e,{space_id:s}=e,{endpoint_parameters:a}=e,{named:_}=e,{current_language:r}=e,c,u,f,d=a.some($=>Ni($.example_input)),g=["Audio","File","Image","Video"],m=a.filter($=>g.includes($.component));function h($){nl[$?"unshift":"push"](()=>{c=$,t(7,c)})}function p($){nl[$?"unshift":"push"](()=>{u=$,t(8,u)})}function b($){nl[$?"unshift":"push"](()=>{f=$,t(9,f)})}return l.$$set=$=>{"dependency"in $&&t(0,n=$.dependency),"dependency_index"in $&&t(1,o=$.dependency_index),"root"in $&&t(2,i=$.root),"space_id"in $&&t(3,s=$.space_id),"endpoint_parameters"in $&&t(4,a=$.endpoint_parameters),"named"in $&&t(5,_=$.named),"current_language"in $&&t(6,r=$.current_language)},[n,o,i,s,a,_,r,c,u,f,d,g,m,h,p,b]}class M_ extends b_{constructor(e){super(),k_(this,e,z_,V_,y_,{dependency:0,dependency_index:1,root:2,space_id:3,endpoint_parameters:4,named:5,current_language:6},null,[-1,-1])}get dependency(){return this.$$.ctx[0]}set dependency(e){this.$$set({dependency:e}),$t()}get dependency_index(){return this.$$.ctx[1]}set dependency_index(e){this.$$set({dependency_index:e}),$t()}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),$t()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),$t()}get endpoint_parameters(){return this.$$.ctx[4]}set endpoint_parameters(e){this.$$set({endpoint_parameters:e}),$t()}get named(){return this.$$.ctx[5]}set named(e){this.$$set({named:e}),$t()}get current_language(){return this.$$.ctx[6]}set current_language(e){this.$$set({current_language:e}),$t()}}const{SvelteComponent:N_,append:S,attr:ge,binding_callbacks:ll,check_outros:F_,create_component:Rn,destroy_component:Sn,destroy_each:jl,detach:qe,element:_e,empty:U_,ensure_array_like:Ht,flush:Xt,group_outros:B_,init:H_,insert:Ae,mount_component:Vn,safe_not_equal:G_,set_data:Qe,space:zn,text:N,transition_in:Gt,transition_out:Zt}=window.__gradio__svelte__internal,{onMount:Z_,tick:W_}=window.__gradio__svelte__internal;function Ao(l,e,t){const n=l.slice();return n[18]=e[t].call,n[19]=e[t].api_name,n}function Lo(l,e,t){const n=l.slice();return n[18]=e[t].call,n[19]=e[t].api_name,n}function To(l,e,t){const n=l.slice();return n[18]=e[t].call,n[19]=e[t].api_name,n}function Q_(l){let e,t,n,o,i,s;n=new lt({props:{code:l[5]?.innerText}});let a=Ht(l[8]),_=[];for(let r=0;r<a.length;r+=1)_[r]=Po(Ao(l,a,r));return{c(){e=_e("code"),t=_e("div"),Rn(n.$$.fragment),o=zn(),i=_e("div");for(let r=0;r<_.length;r+=1)_[r].c();ge(t,"class","copy svelte-j71ub0"),ge(e,"class","svelte-j71ub0")},m(r,c){Ae(r,e,c),S(e,t),Vn(n,t,null),S(e,o),S(e,i);for(let u=0;u<_.length;u+=1)_[u]&&_[u].m(i,null);l[14](i),s=!0},p(r,c){const u={};if(c&32&&(u.code=r[5]?.innerText),n.$set(u),c&257){a=Ht(r[8]);let f;for(f=0;f<a.length;f+=1){const d=Ao(r,a,f);_[f]?_[f].p(d,c):(_[f]=Po(d),_[f].c(),_[f].m(i,null))}for(;f<_.length;f+=1)_[f].d(1);_.length=a.length}},i(r){s||(Gt(n.$$.fragment,r),s=!0)},o(r){Zt(n.$$.fragment,r),s=!1},d(r){r&&qe(e),Sn(n),jl(_,r),l[14](null)}}}function J_(l){let e,t,n,o,i,s,a,_,r,c,u,f,d;n=new lt({props:{code:l[4]?.innerText}});let g=Ht(l[7]),m=[];for(let h=0;h<g.length;h+=1)m[h]=Io(Lo(l,g,h));return{c(){e=_e("code"),t=_e("div"),Rn(n.$$.fragment),o=zn(),i=_e("div"),s=_e("pre"),a=N(`import { Client } from "@gradio/client";

const app = await Client.connect(`),_=_e("span"),r=N('"'),c=N(l[0]),u=N('"'),f=N(`);
					`);for(let h=0;h<m.length;h+=1)m[h].c();ge(t,"class","copy svelte-j71ub0"),ge(_,"class","token string svelte-j71ub0"),ge(s,"class","svelte-j71ub0"),ge(e,"class","svelte-j71ub0")},m(h,p){Ae(h,e,p),S(e,t),Vn(n,t,null),S(e,o),S(e,i),S(i,s),S(s,a),S(s,_),S(_,r),S(_,c),S(_,u),S(s,f);for(let b=0;b<m.length;b+=1)m[b]&&m[b].m(s,null);l[13](i),d=!0},p(h,p){const b={};if(p&16&&(b.code=h[4]?.innerText),n.$set(b),(!d||p&1)&&Qe(c,h[0]),p&128){g=Ht(h[7]);let $;for($=0;$<g.length;$+=1){const C=Lo(h,g,$);m[$]?m[$].p(C,p):(m[$]=Io(C),m[$].c(),m[$].m(s,null))}for(;$<m.length;$+=1)m[$].d(1);m.length=g.length}},i(h){d||(Gt(n.$$.fragment,h),d=!0)},o(h){Zt(n.$$.fragment,h),d=!1},d(h){h&&qe(e),Sn(n),jl(m,h),l[13](null)}}}function X_(l){let e,t,n,o,i,s,a,_,r,c,u,f,d,g,m,h;n=new lt({props:{code:l[3]}});let p=Ht(l[6]),b=[];for(let $=0;$<p.length;$+=1)b[$]=Do(To(l,p,$));return{c(){e=_e("code"),t=_e("div"),Rn(n.$$.fragment),o=zn(),i=_e("div"),s=_e("pre"),a=_e("span"),a.textContent="from",_=N(" gradio_client "),r=_e("span"),r.textContent="import",c=N(` Client, file

client = Client(`),u=_e("span"),f=N('"'),d=N(l[0]),g=N('"'),m=N(`)
`);for(let $=0;$<b.length;$+=1)b[$].c();ge(t,"class","copy svelte-j71ub0"),ge(a,"class","highlight"),ge(r,"class","highlight"),ge(u,"class","token string svelte-j71ub0"),ge(s,"class","svelte-j71ub0"),ge(e,"class","svelte-j71ub0")},m($,C){Ae($,e,C),S(e,t),Vn(n,t,null),S(e,o),S(e,i),S(i,s),S(s,a),S(s,_),S(s,r),S(s,c),S(s,u),S(u,f),S(u,d),S(u,g),S(s,m);for(let w=0;w<b.length;w+=1)b[w]&&b[w].m(s,null);l[12](i),h=!0},p($,C){const w={};if(C&8&&(w.code=$[3]),n.$set(w),(!h||C&1)&&Qe(d,$[0]),C&64){p=Ht($[6]);let k;for(k=0;k<p.length;k+=1){const y=To($,p,k);b[k]?b[k].p(y,C):(b[k]=Do(y),b[k].c(),b[k].m(s,null))}for(;k<b.length;k+=1)b[k].d(1);b.length=p.length}},i($){h||(Gt(n.$$.fragment,$),h=!0)},o($){Zt(n.$$.fragment,$),h=!1},d($){$&&qe(e),Sn(n),jl(b,$),l[12](null)}}}function Po(l){let e,t,n,o,i=l[19]+"",s,a,_="{",r,c,u=l[18]+"",f,d,g="}",m,h,p="{",b,$,C="}",w,k,y,j,v=l[19]+"",E,q,A,B;return{c(){e=_e("pre"),t=N("curl -X POST "),n=N(l[0]),o=N("call/"),s=N(i),a=N(` -s -H "Content-Type: application/json" -d '`),r=N(_),c=N(` 
	"data": [`),f=N(u),d=N("]"),m=N(g),h=N(`' \\
  | awk -F'"' '`),b=N(p),$=N(" print $4"),w=N(C),k=N(`' \\
  | read EVENT_ID; curl -N `),y=N(l[0]),j=N("call/"),E=N(v),q=N("/$EVENT_ID"),A=zn(),B=_e("br"),ge(e,"class","svelte-j71ub0")},m(M,I){Ae(M,e,I),S(e,t),S(e,n),S(e,o),S(e,s),S(e,a),S(e,r),S(e,c),S(e,f),S(e,d),S(e,m),S(e,h),S(e,b),S(e,$),S(e,w),S(e,k),S(e,y),S(e,j),S(e,E),S(e,q),Ae(M,A,I),Ae(M,B,I)},p(M,I){I&1&&Qe(n,M[0]),I&256&&i!==(i=M[19]+"")&&Qe(s,i),I&256&&u!==(u=M[18]+"")&&Qe(f,u),I&1&&Qe(y,M[0]),I&256&&v!==(v=M[19]+"")&&Qe(E,v)},d(M){M&&(qe(e),qe(A),qe(B))}}}function Oo(l){let e;return{c(){e=N(",")},m(t,n){Ae(t,e,n)},d(t){t&&qe(e)}}}function Io(l){let e,t,n,o=l[19]+"",i,s,a=l[18]+"",_,r,c=l[18]&&Oo();return{c(){e=N(`
await client.predict(`),t=_e("span"),n=N(`
  "/`),i=N(o),s=N('"'),c&&c.c(),_=N(a),r=N(`);
						`),ge(t,"class","api-name svelte-j71ub0")},m(u,f){Ae(u,e,f),Ae(u,t,f),S(t,n),S(t,i),S(t,s),c&&c.m(u,f),Ae(u,_,f),Ae(u,r,f)},p(u,f){f&128&&o!==(o=u[19]+"")&&Qe(i,o),u[18]?c||(c=Oo(),c.c(),c.m(_.parentNode,_)):c&&(c.d(1),c=null),f&128&&a!==(a=u[18]+"")&&Qe(_,a)},d(u){u&&(qe(e),qe(t),qe(_),qe(r)),c&&c.d(u)}}}function Do(l){let e,t,n,o=l[18]+"",i,s,a,_,r=l[19]+"",c,u,f;return{c(){e=N(`
client.`),t=_e("span"),n=N(`predict(
`),i=N(o),s=N("  api_name="),a=_e("span"),_=N('"/'),c=N(r),u=N('"'),f=N(`
)
`),ge(a,"class","api-name svelte-j71ub0"),ge(t,"class","highlight")},m(d,g){Ae(d,e,g),Ae(d,t,g),S(t,n),S(t,i),S(t,s),S(t,a),S(a,_),S(a,c),S(a,u),S(t,f)},p(d,g){g&64&&o!==(o=d[18]+"")&&Qe(i,o),g&64&&r!==(r=d[19]+"")&&Qe(c,r)},d(d){d&&(qe(e),qe(t))}}}function Y_(l){let e,t,n,o;const i=[X_,J_,Q_],s=[];function a(_,r){return _[1]==="python"?0:_[1]==="javascript"?1:_[1]==="bash"?2:-1}return~(e=a(l))&&(t=s[e]=i[e](l)),{c(){t&&t.c(),n=U_()},m(_,r){~e&&s[e].m(_,r),Ae(_,n,r),o=!0},p(_,r){let c=e;e=a(_),e===c?~e&&s[e].p(_,r):(t&&(B_(),Zt(s[c],1,1,()=>{s[c]=null}),F_()),~e?(t=s[e],t?t.p(_,r):(t=s[e]=i[e](_),t.c()),Gt(t,1),t.m(n.parentNode,n)):t=null)},i(_){o||(Gt(t),o=!0)},o(_){Zt(t),o=!1},d(_){_&&qe(n),~e&&s[e].d(_)}}}function K_(l){let e,t,n;return t=new on({props:{border_mode:"focus",$$slots:{default:[Y_]},$$scope:{ctx:l}}}),{c(){e=_e("div"),Rn(t.$$.fragment),ge(e,"class","container svelte-j71ub0")},m(o,i){Ae(o,e,i),Vn(t,e,null),n=!0},p(o,[i]){const s={};i&67109375&&(s.$$scope={dirty:i,ctx:o}),t.$set(s)},i(o){n||(Gt(t.$$.fragment,o),n=!0)},o(o){Zt(t.$$.fragment,o),n=!1},d(o){o&&qe(e),Sn(t)}}}function x_(l,e,t){let{dependencies:n}=e,{short_root:o}=e,{root:i}=e,{current_language:s}=e,a,_,r,c,{api_calls:u=[]}=e;async function f(){return await(await fetch(i+"info/?all_endpoints=true")).json()}let d,g=[],m=[],h=[];function p(w,k){const y=`/${n[w.fn_index].api_name}`,v=w.data.filter(E=>typeof E<"u").map((E,q)=>{if(d[y]){const A=d[y].parameters[q];if(!A)return;const B=A.parameter_name,M=A.python_type.type;if(k==="py")return`  ${B}=${Ue(E,M,"py")}`;if(k==="js")return`    ${B}: ${Ue(E,M,"js")}`;if(k==="bash")return`    ${Ue(E,M,"bash")}`}return`  ${Ue(E,void 0,k)}`}).filter(E=>typeof E<"u").join(`,
`);if(v){if(k==="py")return`${v},
`;if(k==="js")return`{
${v},
}`;if(k==="bash")return`
${v}
`}return k==="py"?"":`
`}Z_(async()=>{d=(await f()).named_endpoints;let k=u.map(E=>p(E,"py")),y=u.map(E=>p(E,"js")),j=u.map(E=>p(E,"bash")),v=u.map(E=>n[E.fn_index].api_name||"");t(6,g=k.map((E,q)=>({call:E,api_name:v[q]}))),t(7,m=y.map((E,q)=>({call:E,api_name:v[q]}))),t(8,h=j.map((E,q)=>({call:E,api_name:v[q]}))),await W_(),t(3,_=a.innerText)});function b(w){ll[w?"unshift":"push"](()=>{a=w,t(2,a)})}function $(w){ll[w?"unshift":"push"](()=>{r=w,t(4,r)})}function C(w){ll[w?"unshift":"push"](()=>{c=w,t(5,c)})}return l.$$set=w=>{"dependencies"in w&&t(9,n=w.dependencies),"short_root"in w&&t(0,o=w.short_root),"root"in w&&t(10,i=w.root),"current_language"in w&&t(1,s=w.current_language),"api_calls"in w&&t(11,u=w.api_calls)},[o,s,a,_,r,c,g,m,h,n,i,u,b,$,C]}class ec extends N_{constructor(e){super(),H_(this,e,x_,K_,G_,{dependencies:9,short_root:0,root:10,current_language:1,api_calls:11})}get dependencies(){return this.$$.ctx[9]}set dependencies(e){this.$$set({dependencies:e}),Xt()}get short_root(){return this.$$.ctx[0]}set short_root(e){this.$$set({short_root:e}),Xt()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),Xt()}get current_language(){return this.$$.ctx[1]}set current_language(e){this.$$set({current_language:e}),Xt()}get api_calls(){return this.$$.ctx[11]}set api_calls(e){this.$$set({api_calls:e}),Xt()}}const tc="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e",nc="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e",lc="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e",{SvelteComponent:oc,append:Se,attr:Lt,check_outros:ic,create_component:sc,destroy_component:rc,destroy_each:ac,detach:be,element:Xe,empty:_c,ensure_array_like:Ro,flush:fn,group_outros:cc,init:uc,insert:ke,mount_component:fc,noop:pc,safe_not_equal:dc,set_data:Wt,set_style:mc,space:Vt,text:Me,toggle_class:So,transition_in:bn,transition_out:cl}=window.__gradio__svelte__internal;function Vo(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].type,n[6]=e[t].python_type,n[7]=e[t].component,n[8]=e[t].serializer,n[10]=t,n}function hc(l){let e;return{c(){e=Me("1 element")},m(t,n){ke(t,e,n)},p:pc,d(t){t&&be(e)}}}function gc(l){let e=l[3]=="python"?"tuple":"list",t,n,o=l[1].length+"",i,s;return{c(){t=Me(e),n=Me(" of "),i=Me(o),s=Me(`
		elements`)},m(a,_){ke(a,t,_),ke(a,n,_),ke(a,i,_),ke(a,s,_)},p(a,_){_&8&&e!==(e=a[3]=="python"?"tuple":"list")&&Wt(t,e),_&2&&o!==(o=a[1].length+"")&&Wt(i,o)},d(a){a&&(be(t),be(n),be(i),be(s))}}}function zo(l){let e;return{c(){e=Xe("span"),e.textContent=`[${l[10]}]`,Lt(e,"class","code svelte-16h224k")},m(t,n){ke(t,e,n)},d(t){t&&be(e)}}}function vc(l){let e=l[2][l[10]].type+"",t;return{c(){t=Me(e)},m(n,o){ke(n,t,o)},p(n,o){o&4&&e!==(e=n[2][n[10]].type+"")&&Wt(t,e)},d(n){n&&be(t)}}}function $c(l){let e=l[6].type+"",t;return{c(){t=Me(e)},m(n,o){ke(n,t,o)},p(n,o){o&2&&e!==(e=n[6].type+"")&&Wt(t,e)},d(n){n&&be(t)}}}function Mo(l){let e,t,n,o,i,s,a,_,r,c=l[4]+"",u,f,d=l[7]+"",g,m,h,p=l[1].length>1&&zo(l);function b(w,k){return w[3]==="python"?$c:vc}let $=b(l),C=$(l);return{c(){e=Xe("hr"),t=Vt(),n=Xe("div"),o=Xe("p"),p&&p.c(),i=Vt(),s=Xe("span"),C.c(),a=Vt(),_=Xe("p"),r=Me('The output value that appears in the "'),u=Me(c),f=Me('" '),g=Me(d),m=Me(`
				component.`),h=Vt(),Lt(e,"class","hr svelte-16h224k"),Lt(s,"class","code highlight svelte-16h224k"),Lt(_,"class","desc svelte-16h224k"),mc(n,"margin","10px")},m(w,k){ke(w,e,k),ke(w,t,k),ke(w,n,k),Se(n,o),p&&p.m(o,null),Se(o,i),Se(o,s),C.m(s,null),Se(n,a),Se(n,_),Se(_,r),Se(_,u),Se(_,f),Se(_,g),Se(_,m),Se(n,h)},p(w,k){w[1].length>1?p||(p=zo(w),p.c(),p.m(o,i)):p&&(p.d(1),p=null),$===($=b(w))&&C?C.p(w,k):(C.d(1),C=$(w),C&&(C.c(),C.m(s,null))),k&2&&c!==(c=w[4]+"")&&Wt(u,c),k&2&&d!==(d=w[7]+"")&&Wt(g,d)},d(w){w&&(be(e),be(t),be(n)),p&&p.d(),C.d()}}}function No(l){let e,t,n;return t=new Pi({props:{margin:!1}}),{c(){e=Xe("div"),sc(t.$$.fragment),Lt(e,"class","load-wrap")},m(o,i){ke(o,e,i),fc(t,e,null),n=!0},i(o){n||(bn(t.$$.fragment,o),n=!0)},o(o){cl(t.$$.fragment,o),n=!1},d(o){o&&be(e),rc(t)}}}function bc(l){let e,t,n,o,i,s,a,_;function r(m,h){return m[1].length>1?gc:hc}let c=r(l),u=c(l),f=Ro(l[1]),d=[];for(let m=0;m<f.length;m+=1)d[m]=Mo(Vo(l,f,m));let g=l[0]&&No();return{c(){e=Xe("h4"),t=Xe("div"),t.innerHTML='<div class="toggle-dot toggle-right svelte-16h224k"></div>',n=Me(`
	Returns `),u.c(),o=Vt(),i=Xe("div");for(let m=0;m<d.length;m+=1)d[m].c();s=Vt(),g&&g.c(),a=_c(),Lt(t,"class","toggle-icon svelte-16h224k"),Lt(e,"class","svelte-16h224k"),So(i,"hide",l[0])},m(m,h){ke(m,e,h),Se(e,t),Se(e,n),u.m(e,null),ke(m,o,h),ke(m,i,h);for(let p=0;p<d.length;p+=1)d[p]&&d[p].m(i,null);ke(m,s,h),g&&g.m(m,h),ke(m,a,h),_=!0},p(m,[h]){if(c===(c=r(m))&&u?u.p(m,h):(u.d(1),u=c(m),u&&(u.c(),u.m(e,null))),h&14){f=Ro(m[1]);let p;for(p=0;p<f.length;p+=1){const b=Vo(m,f,p);d[p]?d[p].p(b,h):(d[p]=Mo(b),d[p].c(),d[p].m(i,null))}for(;p<d.length;p+=1)d[p].d(1);d.length=f.length}(!_||h&1)&&So(i,"hide",m[0]),m[0]?g?h&1&&bn(g,1):(g=No(),g.c(),bn(g,1),g.m(a.parentNode,a)):g&&(cc(),cl(g,1,1,()=>{g=null}),ic())},i(m){_||(bn(g),_=!0)},o(m){cl(g),_=!1},d(m){m&&(be(e),be(o),be(i),be(s),be(a)),u.d(),ac(d,m),g&&g.d(m)}}}function kc(l,e,t){let{is_running:n}=e,{endpoint_returns:o}=e,{js_returns:i}=e,{current_language:s}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,o=a.endpoint_returns),"js_returns"in a&&t(2,i=a.js_returns),"current_language"in a&&t(3,s=a.current_language)},[n,o,i,s]}class wc extends oc{constructor(e){super(),uc(this,e,kc,bc,dc,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),fn()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),fn()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),fn()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),fn()}}const{SvelteComponent:yc,append:Z,attr:H,bubble:Fo,check_outros:jn,create_component:ft,destroy_component:pt,destroy_each:Uo,detach:V,element:J,empty:Cl,ensure_array_like:pn,flush:Rt,group_outros:Cn,init:Ec,insert:z,listen:jc,mount_component:dt,noop:ql,safe_not_equal:Cc,set_data:ul,set_style:bt,space:je,src_url_equal:qc,text:Q,transition_in:fe,transition_out:we}=window.__gradio__svelte__internal,{onMount:Ac,createEventDispatcher:Lc}=window.__gradio__svelte__internal;function Bo(l,e,t){const n=l.slice();return n[18]=e[t],n[20]=t,n}function Ho(l,e,t){const n=l.slice();return n[21]=e[t][0],n[22]=e[t][1],n}function Go(l){let e,t,n,o;const i=[Pc,Tc],s=[];function a(_,r){return _[7]?0:1}return e=a(l),t=s[e]=i[e](l),{c(){t.c(),n=Cl()},m(_,r){s[e].m(_,r),z(_,n,r),o=!0},p(_,r){t.p(_,r)},i(_){o||(fe(t),o=!0)},o(_){we(t),o=!1},d(_){_&&V(n),s[e].d(_)}}}function Tc(l){let e,t;return e=new ra({props:{root:l[0]}}),e.$on("close",l[15]),{c(){ft(e.$$.fragment)},m(n,o){dt(e,n,o),t=!0},p(n,o){const i={};o&1&&(i.root=n[0]),e.$set(i)},i(n){t||(fe(e.$$.fragment,n),t=!0)},o(n){we(e.$$.fragment,n),t=!1},d(n){pt(e,n)}}}function Pc(l){let e,t,n,o,i,s,a,_,r,c,u,f,d;t=new va({props:{root:l[2]||l[0],api_count:l[7]}}),t.$on("close",l[12]);let g=pn(l[8]),m=[];for(let k=0;k<g.length;k+=1)m[k]=Zo(Ho(l,g,k));const h=[Ic,Oc],p=[];function b(k,y){return k[3].length?0:1}c=b(l),u=p[c]=h[c](l);let $=pn(l[1]),C=[];for(let k=0;k<$.length;k+=1)C[k]=Xo(Bo(l,$,k));const w=k=>we(C[k],1,1,()=>{C[k]=null});return{c(){e=J("div"),ft(t.$$.fragment),n=je(),o=J("div"),i=J("div"),i.innerHTML=`<p style="font-size: var(--text-lg);">Choose a language to see the code snippets for interacting with the
					API.</p>`,s=je(),a=J("div"),_=J("div");for(let k=0;k<m.length;k+=1)m[k].c();r=je(),u.c(),f=je();for(let k=0;k<C.length;k+=1)C[k].c();H(e,"class","banner-wrap svelte-v3jjme"),H(i,"class","client-doc svelte-v3jjme"),H(_,"class","snippets svelte-v3jjme"),H(a,"class","endpoint svelte-v3jjme"),H(o,"class","docs-wrap svelte-v3jjme")},m(k,y){z(k,e,y),dt(t,e,null),z(k,n,y),z(k,o,y),Z(o,i),Z(o,s),Z(o,a),Z(a,_);for(let j=0;j<m.length;j+=1)m[j]&&m[j].m(_,null);Z(a,r),p[c].m(a,null),Z(a,f);for(let j=0;j<C.length;j+=1)C[j]&&C[j].m(a,null);d=!0},p(k,y){const j={};if(y&5&&(j.root=k[2]||k[0]),t.$set(j),y&272){g=pn(k[8]);let E;for(E=0;E<g.length;E+=1){const q=Ho(k,g,E);m[E]?m[E].p(q,y):(m[E]=Zo(q),m[E].c(),m[E].m(_,null))}for(;E<m.length;E+=1)m[E].d(1);m.length=g.length}let v=c;if(c=b(k),c===v?p[c].p(k,y):(Cn(),we(p[v],1,1,()=>{p[v]=null}),jn(),u=p[c],u?u.p(k,y):(u=p[c]=h[c](k),u.c()),fe(u,1),u.m(a,f)),y&119){$=pn(k[1]);let E;for(E=0;E<$.length;E+=1){const q=Bo(k,$,E);C[E]?(C[E].p(q,y),fe(C[E],1)):(C[E]=Xo(q),C[E].c(),fe(C[E],1),C[E].m(a,null))}for(Cn(),E=$.length;E<C.length;E+=1)w(E);jn()}},i(k){if(!d){fe(t.$$.fragment,k),fe(u);for(let y=0;y<$.length;y+=1)fe(C[y]);d=!0}},o(k){we(t.$$.fragment,k),we(u),C=C.filter(Boolean);for(let y=0;y<C.length;y+=1)we(C[y]);d=!1},d(k){k&&(V(e),V(n),V(o)),pt(t),Uo(m,k),p[c].d(),Uo(C,k)}}}function Zo(l){let e,t,n,o,i=l[21]+"",s,a,_,r,c;function u(){return l[13](l[21])}return{c(){e=J("li"),t=J("img"),o=je(),s=Q(i),a=je(),qc(t.src,n=l[22])||H(t,"src",n),H(t,"alt",""),H(t,"class","svelte-v3jjme"),H(e,"class",_="snippet "+(l[4]===l[21]?"current-lang":"inactive-lang")+" svelte-v3jjme")},m(f,d){z(f,e,d),Z(e,t),Z(e,o),Z(e,s),Z(e,a),r||(c=jc(e,"click",u),r=!0)},p(f,d){l=f,d&16&&_!==(_="snippet "+(l[4]===l[21]?"current-lang":"inactive-lang")+" svelte-v3jjme")&&H(e,"class",_)},d(f){f&&V(e),r=!1,c()}}}function Oc(l){let e,t,n,o,i,s,a,_,r,c;function u(h,p){return h[4]=="python"||h[4]=="javascript"?Rc:Dc}let f=u(l),d=f(l);n=new u_({props:{current_language:l[4]}});let g=l[2]&&Wo(l);_=new Tn({props:{size:"sm",variant:"secondary",$$slots:{default:[Sc]},$$scope:{ctx:l}}}),_.$on("click",l[14]);let m=l[4]=="bash"&&Qo();return{c(){e=J("p"),d.c(),t=je(),ft(n.$$.fragment),o=je(),i=J("p"),s=Q(`2. Find the API endpoint below corresponding to your desired
						function in the app. Copy the code snippet, replacing the
						placeholder values with your own input data.
						`),g&&g.c(),a=Q(`

						Or use the
						`),ft(_.$$.fragment),r=Q(`
						to automatically generate your API requests.
						`),m&&m.c(),H(e,"class","padded svelte-v3jjme"),H(i,"class","padded svelte-v3jjme")},m(h,p){z(h,e,p),d.m(e,null),z(h,t,p),dt(n,h,p),z(h,o,p),z(h,i,p),Z(i,s),g&&g.m(i,null),Z(i,a),dt(_,i,null),Z(i,r),m&&m.m(i,null),c=!0},p(h,p){f===(f=u(h))&&d?d.p(h,p):(d.d(1),d=f(h),d&&(d.c(),d.m(e,null)));const b={};p&16&&(b.current_language=h[4]),n.$set(b),h[2]?g?g.p(h,p):(g=Wo(h),g.c(),g.m(i,a)):g&&(g.d(1),g=null);const $={};p&33554432&&($.$$scope={dirty:p,ctx:h}),_.$set($),h[4]=="bash"?m?m.p(h,p):(m=Qo(),m.c(),m.m(i,null)):m&&(m.d(1),m=null)},i(h){c||(fe(n.$$.fragment,h),fe(_.$$.fragment,h),c=!0)},o(h){we(n.$$.fragment,h),we(_.$$.fragment,h),c=!1},d(h){h&&(V(e),V(t),V(o),V(i)),d.d(),pt(n,h),g&&g.d(),pt(_),m&&m.d()}}}function Ic(l){let e,t,n,o,i,s=l[3].length+"",a,_,r,c,u,f,d,g,m,h,p,b,$,C;return m=new ec({props:{current_language:l[4],api_calls:l[3],dependencies:l[1],root:l[0],short_root:l[2]||l[0]}}),{c(){e=J("div"),t=J("p"),n=Q("🪄 Recorded API Calls "),o=J("span"),i=Q("["),a=Q(s),_=Q("]"),r=je(),c=J("p"),u=Q(`Here is the code snippet to replay the most recently recorded API
							calls using the `),f=Q(l[4]),d=Q(`
							client.`),g=je(),ft(m.$$.fragment),h=je(),p=J("p"),p.textContent=`Note: Some API calls only affect the UI, so when using the
							clients, the desired result may be achieved with only a subset of
							the recorded calls.`,b=je(),$=J("p"),$.textContent="API Documentation",H(o,"class","api-count svelte-v3jjme"),H(t,"id","num-recorded-api-calls"),bt(t,"font-size","var(--text-lg)"),bt(t,"font-weight","bold"),bt(t,"margin","10px 0px"),bt($,"font-size","var(--text-lg)"),bt($,"font-weight","bold"),bt($,"margin","30px 0px 10px")},m(w,k){z(w,e,k),Z(e,t),Z(t,n),Z(t,o),Z(o,i),Z(o,a),Z(o,_),Z(e,r),Z(e,c),Z(c,u),Z(c,f),Z(c,d),Z(e,g),dt(m,e,null),Z(e,h),Z(e,p),z(w,b,k),z(w,$,k),C=!0},p(w,k){(!C||k&8)&&s!==(s=w[3].length+"")&&ul(a,s),(!C||k&16)&&ul(f,w[4]);const y={};k&16&&(y.current_language=w[4]),k&8&&(y.api_calls=w[3]),k&2&&(y.dependencies=w[1]),k&1&&(y.root=w[0]),k&5&&(y.short_root=w[2]||w[0]),m.$set(y)},i(w){C||(fe(m.$$.fragment,w),C=!0)},o(w){we(m.$$.fragment,w),C=!1},d(w){w&&(V(e),V(b),V($)),pt(m)}}}function Dc(l){let e;return{c(){e=Q("1. Confirm that you have cURL installed on your system.")},m(t,n){z(t,e,n)},p:ql,d(t){t&&V(e)}}}function Rc(l){let e,t,n,o,i,s,a,_;return{c(){e=Q(`1. Install the
							`),t=J("span"),n=Q(l[4]),o=Q(`
							client (`),i=J("a"),s=Q("docs"),_=Q(") if you don't already have it installed."),bt(t,"text-transform","capitalize"),H(i,"href",a=l[4]=="python"?An:qn),H(i,"target","_blank"),H(i,"class","svelte-v3jjme")},m(r,c){z(r,e,c),z(r,t,c),Z(t,n),z(r,o,c),z(r,i,c),Z(i,s),z(r,_,c)},p(r,c){c&16&&ul(n,r[4]),c&16&&a!==(a=r[4]=="python"?An:qn)&&H(i,"href",a)},d(r){r&&(V(e),V(t),V(o),V(i),V(_))}}}function Wo(l){let e,t,n,o,i;return{c(){e=Q(`If this is a private Space, you may need to pass your
							Hugging Face token as well (`),t=J("a"),n=Q("read more"),i=Q(")."),H(t,"href",o=l[4]=="python"?An+dn:l[4]=="javascript"?qn+dn:fl),H(t,"class","underline svelte-v3jjme"),H(t,"target","_blank")},m(s,a){z(s,e,a),z(s,t,a),Z(t,n),z(s,i,a)},p(s,a){a&16&&o!==(o=s[4]=="python"?An+dn:s[4]=="javascript"?qn+dn:fl)&&H(t,"href",o)},d(s){s&&(V(e),V(t),V(i))}}}function Sc(l){let e,t,n;return{c(){e=J("div"),t=je(),n=J("p"),n.textContent="API Recorder",H(e,"class","loading-dot svelte-v3jjme"),H(n,"class","self-baseline svelte-v3jjme")},m(o,i){z(o,e,i),z(o,t,i),z(o,n,i)},p:ql,d(o){o&&(V(e),V(t),V(n))}}}function Qo(l){let e,t,n,o,i,s,a,_,r,c,u,f,d,g,m,h,p,b,$,C,w,k,y;return{c(){e=J("br"),t=Q(" "),n=J("br"),o=Q(`Note: making a
							prediction and getting a result requires
							`),i=J("strong"),i.textContent="2 requests",s=Q(`: a
							`),a=J("code"),a.textContent="POST",_=Q(`
							and a `),r=J("code"),r.textContent="GET",c=Q(" request. The "),u=J("code"),u.textContent="POST",f=Q(` request
							returns an `),d=J("code"),d.textContent="EVENT_ID",g=Q(`, which is used in the second
							`),m=J("code"),m.textContent="GET",h=Q(` request to fetch the results. In these snippets,
							we've used `),p=J("code"),p.textContent="awk",b=Q(" and "),$=J("code"),$.textContent="read",C=Q(` to parse the
							results, combining these two requests into one command for ease of
							use. See `),w=J("a"),k=Q("curl docs"),y=Q("."),H(a,"class","svelte-v3jjme"),H(r,"class","svelte-v3jjme"),H(u,"class","svelte-v3jjme"),H(d,"class","svelte-v3jjme"),H(m,"class","svelte-v3jjme"),H(p,"class","svelte-v3jjme"),H($,"class","svelte-v3jjme"),H(w,"href",fl),H(w,"target","_blank"),H(w,"class","svelte-v3jjme")},m(j,v){z(j,e,v),z(j,t,v),z(j,n,v),z(j,o,v),z(j,i,v),z(j,s,v),z(j,a,v),z(j,_,v),z(j,r,v),z(j,c,v),z(j,u,v),z(j,f,v),z(j,d,v),z(j,g,v),z(j,m,v),z(j,h,v),z(j,p,v),z(j,b,v),z(j,$,v),z(j,C,v),z(j,w,v),Z(w,k),z(j,y,v)},p:ql,d(j){j&&(V(e),V(t),V(n),V(o),V(i),V(s),V(a),V(_),V(r),V(c),V(u),V(f),V(d),V(g),V(m),V(h),V(p),V(b),V($),V(C),V(w),V(y))}}}function Jo(l){let e,t,n,o,i,s,a,_;return t=new M_({props:{named:!0,endpoint_parameters:l[5].named_endpoints["/"+l[18].api_name].parameters,dependency:l[18],dependency_index:l[20],current_language:l[4],root:l[0],space_id:l[2]}}),o=new Sa({props:{endpoint_returns:l[5].named_endpoints["/"+l[18].api_name].parameters,js_returns:l[6].named_endpoints["/"+l[18].api_name].parameters,is_running:Yo,current_language:l[4]}}),s=new wc({props:{endpoint_returns:l[5].named_endpoints["/"+l[18].api_name].returns,js_returns:l[6].named_endpoints["/"+l[18].api_name].returns,is_running:Yo,current_language:l[4]}}),{c(){e=J("div"),ft(t.$$.fragment),n=je(),ft(o.$$.fragment),i=je(),ft(s.$$.fragment),a=je(),H(e,"class","endpoint-container svelte-v3jjme")},m(r,c){z(r,e,c),dt(t,e,null),Z(e,n),dt(o,e,null),Z(e,i),dt(s,e,null),Z(e,a),_=!0},p(r,c){const u={};c&34&&(u.endpoint_parameters=r[5].named_endpoints["/"+r[18].api_name].parameters),c&2&&(u.dependency=r[18]),c&16&&(u.current_language=r[4]),c&1&&(u.root=r[0]),c&4&&(u.space_id=r[2]),t.$set(u);const f={};c&34&&(f.endpoint_returns=r[5].named_endpoints["/"+r[18].api_name].parameters),c&66&&(f.js_returns=r[6].named_endpoints["/"+r[18].api_name].parameters),c&16&&(f.current_language=r[4]),o.$set(f);const d={};c&34&&(d.endpoint_returns=r[5].named_endpoints["/"+r[18].api_name].returns),c&66&&(d.js_returns=r[6].named_endpoints["/"+r[18].api_name].returns),c&16&&(d.current_language=r[4]),s.$set(d)},i(r){_||(fe(t.$$.fragment,r),fe(o.$$.fragment,r),fe(s.$$.fragment,r),_=!0)},o(r){we(t.$$.fragment,r),we(o.$$.fragment,r),we(s.$$.fragment,r),_=!1},d(r){r&&V(e),pt(t),pt(o),pt(s)}}}function Xo(l){let e,t,n=l[18].show_api&&l[5].named_endpoints["/"+l[18].api_name]&&Jo(l);return{c(){n&&n.c(),e=Cl()},m(o,i){n&&n.m(o,i),z(o,e,i),t=!0},p(o,i){o[18].show_api&&o[5].named_endpoints["/"+o[18].api_name]?n?(n.p(o,i),i&34&&fe(n,1)):(n=Jo(o),n.c(),fe(n,1),n.m(e.parentNode,e)):n&&(Cn(),we(n,1,1,()=>{n=null}),jn())},i(o){t||(fe(n),t=!0)},o(o){we(n),t=!1},d(o){o&&V(e),n&&n.d(o)}}}function Vc(l){let e,t,n=l[5]&&Go(l);return{c(){n&&n.c(),e=Cl()},m(o,i){n&&n.m(o,i),z(o,e,i),t=!0},p(o,[i]){o[5]?n?(n.p(o,i),i&32&&fe(n,1)):(n=Go(o),n.c(),fe(n,1),n.m(e.parentNode,e)):n&&(Cn(),we(n,1,1,()=>{n=null}),jn())},i(o){t||(fe(n),t=!0)},o(o){we(n),t=!1},d(o){o&&V(e),n&&n.d(o)}}}const qn="https://www.gradio.app/guides/getting-started-with-the-js-client",An="https://www.gradio.app/guides/getting-started-with-the-python-client",fl="https://www.gradio.app/guides/querying-gradio-apps-with-curl",dn="#connecting-to-a-hugging-face-space";let Yo=!1;function zc(l,e,t){let{dependencies:n}=e,{root:o}=e,{app:i}=e,{space_id:s}=e,{root_node:a}=e,_=n.filter(w=>w.show_api).length;o===""&&(o=location.protocol+"//"+location.host+location.pathname),o.endsWith("/")||(o+="/");let{api_calls:r=[]}=e,c="python";const u=[["python",tc],["javascript",nc],["bash",lc]];async function f(){return await(await fetch(o+"info")).json()}async function d(){return await i.view_api()}let g,m;f().then(w=>{t(5,g=w)}),d().then(w=>{t(6,m=w)});const h=Lc();Ac(()=>(document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0),()=>{document.body.style.overflow="auto"}));function p(w){Fo.call(this,l,w)}const b=w=>t(4,c=w),$=()=>h("close",{api_recorder_visible:!0});function C(w){Fo.call(this,l,w)}return l.$$set=w=>{"dependencies"in w&&t(1,n=w.dependencies),"root"in w&&t(0,o=w.root),"app"in w&&t(10,i=w.app),"space_id"in w&&t(2,s=w.space_id),"root_node"in w&&t(11,a=w.root_node),"api_calls"in w&&t(3,r=w.api_calls)},[o,n,s,r,c,g,m,_,u,h,i,a,p,b,$,C]}class Mc extends yc{constructor(e){super(),Ec(this,e,zc,Vc,Cc,{dependencies:1,root:0,app:10,space_id:2,root_node:11,api_calls:3})}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),Rt()}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),Rt()}get app(){return this.$$.ctx[10]}set app(e){this.$$set({app:e}),Rt()}get space_id(){return this.$$.ctx[2]}set space_id(e){this.$$set({space_id:e}),Rt()}get root_node(){return this.$$.ctx[11]}set root_node(e){this.$$set({root_node:e}),Rt()}get api_calls(){return this.$$.ctx[3]}set api_calls(e){this.$$set({api_calls:e}),Rt()}}const{SvelteComponent:Nc,append:yt,attr:zt,create_component:Fc,destroy_component:Uc,detach:Et,element:Mt,flush:Ko,init:Bc,insert:jt,mount_component:Hc,safe_not_equal:Gc,set_data:Gi,space:ol,text:en,transition_in:Zc,transition_out:Wc}=window.__gradio__svelte__internal;function xo(l){let e,t,n=l[1][l[0][l[0].length-1].fn_index].api_name+"",o;return{c(){e=Mt("span"),t=en("/"),o=en(n),zt(e,"class","api-name svelte-sy28j6")},m(i,s){jt(i,e,s),yt(e,t),yt(e,o)},p(i,s){s&3&&n!==(n=i[1][i[0][i[0].length-1].fn_index].api_name+"")&&Gi(o,n)},d(i){i&&Et(e)}}}function Qc(l){let e,t,n,o,i,s,a,_=l[0].length+"",r,c,u,f=l[0].length>0&&xo(l);return{c(){e=Mt("div"),t=ol(),n=Mt("p"),n.textContent="Recording API Calls:",o=ol(),i=Mt("p"),s=Mt("span"),a=en("["),r=en(_),c=en("]"),u=ol(),f&&f.c(),zt(e,"class","loading-dot self-baseline svelte-sy28j6"),zt(n,"class","self-baseline svelte-sy28j6"),zt(s,"class","api-count svelte-sy28j6"),zt(i,"class","self-baseline api-section svelte-sy28j6")},m(d,g){jt(d,e,g),jt(d,t,g),jt(d,n,g),jt(d,o,g),jt(d,i,g),yt(i,s),yt(s,a),yt(s,r),yt(s,c),yt(i,u),f&&f.m(i,null)},p(d,g){g&1&&_!==(_=d[0].length+"")&&Gi(r,_),d[0].length>0?f?f.p(d,g):(f=xo(d),f.c(),f.m(i,null)):f&&(f.d(1),f=null)},d(d){d&&(Et(e),Et(t),Et(n),Et(o),Et(i)),f&&f.d()}}}function Jc(l){let e,t,n;return t=new Tn({props:{size:"sm",variant:"secondary",$$slots:{default:[Qc]},$$scope:{ctx:l}}}),{c(){e=Mt("div"),Fc(t.$$.fragment),zt(e,"id","api-recorder")},m(o,i){jt(o,e,i),Hc(t,e,null),n=!0},p(o,[i]){const s={};i&7&&(s.$$scope={dirty:i,ctx:o}),t.$set(s)},i(o){n||(Zc(t.$$.fragment,o),n=!0)},o(o){Wc(t.$$.fragment,o),n=!1},d(o){o&&Et(e),Uc(t)}}}function Xc(l,e,t){let{api_calls:n=[]}=e,{dependencies:o}=e;return l.$$set=i=>{"api_calls"in i&&t(0,n=i.api_calls),"dependencies"in i&&t(1,o=i.dependencies)},[n,o]}class Yc extends Nc{constructor(e){super(),Bc(this,e,Xc,Jc,Gc,{api_calls:0,dependencies:1})}get api_calls(){return this.$$.ctx[0]}set api_calls(e){this.$$set({api_calls:e}),Ko()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),Ko()}}const ei=ys(Oi),{SvelteComponent:Kc,add_flush_callback:xc,assign:Ln,bind:ti,binding_callbacks:pl,bubble:eu,check_outros:tu,compute_rest_props:ni,construct_svelte_component:li,create_component:oi,create_slot:nu,destroy_component:ii,detach:lu,empty:ou,exclude_internal_props:iu,flush:Je,get_all_dirty_from_scope:su,get_slot_changes:ru,get_spread_object:si,get_spread_update:ri,group_outros:au,init:_u,insert:cu,mount_component:ai,not_equal:uu,transition_in:dl,transition_out:ml,update_slot_base:fu}=window.__gradio__svelte__internal,{bind:pu,binding_callbacks:du}=window.__gradio__svelte__internal;function mu(l){let e;const t=l[12].default,n=nu(t,l,l[16],null);return{c(){n&&n.c()},m(o,i){n&&n.m(o,i),e=!0},p(o,i){n&&n.p&&(!e||i&65536)&&fu(n,t,o,o[16],e?ru(t,o[16],i,null):su(o[16]),null)},i(o){e||(dl(n,o),e=!0)},o(o){ml(n,o),e=!1},d(o){n&&n.d(o)}}}function hu(l){let e,t,n,o;const i=[{elem_id:l[6]},{elem_classes:l[7]},{target:l[3]},l[9],{theme_mode:l[4]},{root:l[2]},{gradio:l[5]}];function s(r){l[14](r)}var a=l[8];function _(r,c){let u={$$slots:{default:[mu]},$$scope:{ctx:r}};for(let f=0;f<i.length;f+=1)u=Ln(u,i[f]);return c!==void 0&&c&764&&(u=Ln(u,ri(i,[c&64&&{elem_id:r[6]},c&128&&{elem_classes:r[7]},c&8&&{target:r[3]},c&512&&si(r[9]),c&16&&{theme_mode:r[4]},c&4&&{root:r[2]},c&32&&{gradio:r[5]}]))),r[1]!==void 0&&(u.value=r[1]),{props:u}}return a&&(e=li(a,_(l)),l[13](e),pl.push(()=>ti(e,"value",s)),e.$on("prop_change",l[15])),{c(){e&&oi(e.$$.fragment),n=ou()},m(r,c){e&&ai(e,r,c),cu(r,n,c),o=!0},p(r,[c]){if(a!==(a=r[8])){if(e){au();const u=e;ml(u.$$.fragment,1,0,()=>{ii(u,1)}),tu()}a?(e=li(a,_(r,c)),r[13](e),pl.push(()=>ti(e,"value",s)),e.$on("prop_change",r[15]),oi(e.$$.fragment),dl(e.$$.fragment,1),ai(e,n.parentNode,n)):e=null}else if(a){const u=c&764?ri(i,[c&64&&{elem_id:r[6]},c&128&&{elem_classes:r[7]},c&8&&{target:r[3]},c&512&&si(r[9]),c&16&&{theme_mode:r[4]},c&4&&{root:r[2]},c&32&&{gradio:r[5]}]):{};c&65536&&(u.$$scope={dirty:c,ctx:r}),!t&&c&2&&(t=!0,u.value=r[1],xc(()=>t=!1)),e.$set(u)}},i(r){o||(e&&dl(e.$$.fragment,r),o=!0)},o(r){e&&ml(e.$$.fragment,r),o=!1},d(r){r&&lu(n),l[13](null),e&&ii(e,r)}}}function gu(l,e,t){const n=["root","component","target","theme_mode","instance","value","gradio","elem_id","elem_classes","_id"];let o=ni(e,n),{$$slots:i={},$$scope:s}=e,{root:a}=e,{component:_}=e,{target:r}=e,{theme_mode:c}=e,{instance:u}=e,{value:f}=e,{gradio:d}=e,{elem_id:g}=e,{elem_classes:m}=e,{_id:h}=e;const p=(y,j,v)=>new CustomEvent("prop_change",{detail:{id:y,prop:j,value:v}});function b(y){return new Proxy(y,{construct(v,E){const q=new v(...E),A=Object.keys(q.$$.props);function B(M){return function(I){const R=p(h,M,I);r.dispatchEvent(R)}}return A.forEach(M=>{du.push(()=>pu(q,M,B(M)))}),q}})}const $=b(_);function C(y){pl[y?"unshift":"push"](()=>{u=y,t(0,u)})}function w(y){f=y,t(1,f)}function k(y){eu.call(this,l,y)}return l.$$set=y=>{e=Ln(Ln({},e),iu(y)),t(9,o=ni(e,n)),"root"in y&&t(2,a=y.root),"component"in y&&t(10,_=y.component),"target"in y&&t(3,r=y.target),"theme_mode"in y&&t(4,c=y.theme_mode),"instance"in y&&t(0,u=y.instance),"value"in y&&t(1,f=y.value),"gradio"in y&&t(5,d=y.gradio),"elem_id"in y&&t(6,g=y.elem_id),"elem_classes"in y&&t(7,m=y.elem_classes),"_id"in y&&t(11,h=y._id),"$$scope"in y&&t(16,s=y.$$scope)},[u,f,a,r,c,d,g,m,$,o,_,h,i,C,w,k,s]}class vu extends Kc{constructor(e){super(),_u(this,e,gu,hu,uu,{root:2,component:10,target:3,theme_mode:4,instance:0,value:1,gradio:5,elem_id:6,elem_classes:7,_id:11})}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),Je()}get component(){return this.$$.ctx[10]}set component(e){this.$$set({component:e}),Je()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),Je()}get theme_mode(){return this.$$.ctx[4]}set theme_mode(e){this.$$set({theme_mode:e}),Je()}get instance(){return this.$$.ctx[0]}set instance(e){this.$$set({instance:e}),Je()}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),Je()}get gradio(){return this.$$.ctx[5]}set gradio(e){this.$$set({gradio:e}),Je()}get elem_id(){return this.$$.ctx[6]}set elem_id(e){this.$$set({elem_id:e}),Je()}get elem_classes(){return this.$$.ctx[7]}set elem_classes(e){this.$$set({elem_classes:e}),Je()}get _id(){return this.$$.ctx[11]}set _id(e){this.$$set({_id:e}),Je()}}const{SvelteComponent:$u,add_flush_callback:_i,assign:bu,bind:ci,binding_callbacks:ui,bubble:fi,check_outros:Zi,create_component:Wi,destroy_component:Qi,detach:Al,empty:Ll,ensure_array_like:pi,flush:nt,get_spread_object:ku,get_spread_update:wu,group_outros:Ji,init:yu,insert:Tl,mount_component:Xi,outro_and_destroy_block:Eu,safe_not_equal:ju,transition_in:Ft,transition_out:ln,update_keyed_each:Cu}=window.__gradio__svelte__internal,{onMount:qu,createEventDispatcher:Au,setContext:Lu}=window.__gradio__svelte__internal;function di(l,e,t){const n=l.slice();return n[15]=e[t],n}function mi(l){let e=[],t=new Map,n,o,i=pi(l[0].children);const s=a=>a[15].id;for(let a=0;a<i.length;a+=1){let _=di(l,i,a),r=s(_);t.set(r,e[a]=hi(r,_))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=Ll()},m(a,_){for(let r=0;r<e.length;r+=1)e[r]&&e[r].m(a,_);Tl(a,n,_),o=!0},p(a,_){_&207&&(i=pi(a[0].children),Ji(),e=Cu(e,_,s,1,a,i,t,n.parentNode,Eu,hi,n,di),Zi())},i(a){if(!o){for(let _=0;_<i.length;_+=1)Ft(e[_]);o=!0}},o(a){for(let _=0;_<e.length;_+=1)ln(e[_]);o=!1},d(a){a&&Al(n);for(let _=0;_<e.length;_+=1)e[_].d(a)}}}function hi(l,e){let t,n,o;return n=new Yi({props:{node:e[15],component:e[15].component,target:e[2],id:e[15].id,root:e[1],theme_mode:e[3],max_file_size:e[6],client:e[7]}}),n.$on("destroy",e[9]),n.$on("mount",e[10]),{key:l,first:null,c(){t=Ll(),Wi(n.$$.fragment),this.first=t},m(i,s){Tl(i,t,s),Xi(n,i,s),o=!0},p(i,s){e=i;const a={};s&1&&(a.node=e[15]),s&1&&(a.component=e[15].component),s&4&&(a.target=e[2]),s&1&&(a.id=e[15].id),s&2&&(a.root=e[1]),s&8&&(a.theme_mode=e[3]),s&64&&(a.max_file_size=e[6]),s&128&&(a.client=e[7]),n.$set(a)},i(i){o||(Ft(n.$$.fragment,i),o=!0)},o(i){ln(n.$$.fragment,i),o=!1},d(i){i&&Al(t),Qi(n,i)}}}function Tu(l){let e,t,n=l[0].children&&l[0].children.length&&mi(l);return{c(){n&&n.c(),e=Ll()},m(o,i){n&&n.m(o,i),Tl(o,e,i),t=!0},p(o,i){o[0].children&&o[0].children.length?n?(n.p(o,i),i&1&&Ft(n,1)):(n=mi(o),n.c(),Ft(n,1),n.m(e.parentNode,e)):n&&(Ji(),ln(n,1,1,()=>{n=null}),Zi())},i(o){t||(Ft(n),t=!0)},o(o){ln(n),t=!1},d(o){o&&Al(e),n&&n.d(o)}}}function Pu(l){let e,t,n,o;const i=[{_id:l[0].id},{component:l[0].component},{elem_id:"elem_id"in l[0].props&&l[0].props.elem_id||`component-${l[0].id}`},{elem_classes:"elem_classes"in l[0].props&&l[0].props.elem_classes||[]},{target:l[2]},l[0].props,{theme_mode:l[3]},{root:l[1]},{gradio:new Ul(l[0].id,l[2],l[3],l[4],l[1],l[5],l[6],ei,l[7])}];function s(r){l[11](r)}function a(r){l[12](r)}let _={$$slots:{default:[Tu]},$$scope:{ctx:l}};for(let r=0;r<i.length;r+=1)_=bu(_,i[r]);return l[0].instance!==void 0&&(_.instance=l[0].instance),l[0].props.value!==void 0&&(_.value=l[0].props.value),e=new vu({props:_}),ui.push(()=>ci(e,"instance",s)),ui.push(()=>ci(e,"value",a)),{c(){Wi(e.$$.fragment)},m(r,c){Xi(e,r,c),o=!0},p(r,[c]){const u=c&255?wu(i,[c&1&&{_id:r[0].id},c&1&&{component:r[0].component},c&1&&{elem_id:"elem_id"in r[0].props&&r[0].props.elem_id||`component-${r[0].id}`},c&1&&{elem_classes:"elem_classes"in r[0].props&&r[0].props.elem_classes||[]},c&4&&{target:r[2]},c&1&&ku(r[0].props),c&8&&{theme_mode:r[3]},c&2&&{root:r[1]},{gradio:new Ul(r[0].id,r[2],r[3],r[4],r[1],r[5],r[6],ei,r[7])}]):{};c&262351&&(u.$$scope={dirty:c,ctx:r}),!t&&c&1&&(t=!0,u.instance=r[0].instance,_i(()=>t=!1)),!n&&c&1&&(n=!0,u.value=r[0].props.value,_i(()=>n=!1)),e.$set(u)},i(r){o||(Ft(e.$$.fragment,r),o=!0)},o(r){ln(e.$$.fragment,r),o=!1},d(r){Qi(e,r)}}}function Ou(l,e,t){let{root:n}=e,{node:o}=e,{parent:i=null}=e,{target:s}=e,{theme_mode:a}=e,{version:_}=e,{autoscroll:r}=e,{max_file_size:c}=e,{client:u}=e;const f=Au();let d=[];qu(()=>{f("mount",o.id);for(const b of d)f("mount",b.id);return()=>{f("destroy",o.id);for(const b of d)f("mount",b.id)}}),Lu("BLOCK_KEY",i);function g(b){fi.call(this,l,b)}function m(b){fi.call(this,l,b)}function h(b){l.$$.not_equal(o.instance,b)&&(o.instance=b,t(0,o),t(14,d))}function p(b){l.$$.not_equal(o.props.value,b)&&(o.props.value=b,t(0,o),t(14,d))}return l.$$set=b=>{"root"in b&&t(1,n=b.root),"node"in b&&t(0,o=b.node),"parent"in b&&t(8,i=b.parent),"target"in b&&t(2,s=b.target),"theme_mode"in b&&t(3,a=b.theme_mode),"version"in b&&t(4,_=b.version),"autoscroll"in b&&t(5,r=b.autoscroll),"max_file_size"in b&&t(6,c=b.max_file_size),"client"in b&&t(7,u=b.client)},l.$$.update=()=>{l.$$.dirty&1&&t(0,o.children=o.children&&o.children.filter(b=>{const $=o.type!=="statustracker";return $||d.push(b),$}),o),l.$$.dirty&1&&o.type==="form"&&(o.children?.every(b=>!b.props.visible)?t(0,o.props.visible=!1,o):t(0,o.props.visible=!0,o))},[o,n,s,a,_,r,c,u,i,g,m,h,p]}class Yi extends $u{constructor(e){super(),yu(this,e,Ou,Pu,ju,{root:1,node:0,parent:8,target:2,theme_mode:3,version:4,autoscroll:5,max_file_size:6,client:7})}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),nt()}get node(){return this.$$.ctx[0]}set node(e){this.$$set({node:e}),nt()}get parent(){return this.$$.ctx[8]}set parent(e){this.$$set({parent:e}),nt()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),nt()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),nt()}get version(){return this.$$.ctx[4]}set version(e){this.$$set({version:e}),nt()}get autoscroll(){return this.$$.ctx[5]}set autoscroll(e){this.$$set({autoscroll:e}),nt()}get max_file_size(){return this.$$.ctx[6]}set max_file_size(e){this.$$set({max_file_size:e}),nt()}get client(){return this.$$.ctx[7]}set client(e){this.$$set({client:e}),nt()}}const{SvelteComponent:Iu,create_component:Du,destroy_component:Ru,flush:at,init:Su,mount_component:Vu,safe_not_equal:zu,transition_in:Mu,transition_out:Nu}=window.__gradio__svelte__internal,{onMount:Fu,createEventDispatcher:Uu}=window.__gradio__svelte__internal;function Bu(l){let e,t;return e=new Yi({props:{node:l[0],root:l[1],target:l[2],theme_mode:l[3],version:l[4],autoscroll:l[5],max_file_size:l[6],client:l[7]}}),{c(){Du(e.$$.fragment)},m(n,o){Vu(e,n,o),t=!0},p(n,[o]){const i={};o&1&&(i.node=n[0]),o&2&&(i.root=n[1]),o&4&&(i.target=n[2]),o&8&&(i.theme_mode=n[3]),o&16&&(i.version=n[4]),o&32&&(i.autoscroll=n[5]),o&64&&(i.max_file_size=n[6]),o&128&&(i.client=n[7]),e.$set(i)},i(n){t||(Mu(e.$$.fragment,n),t=!0)},o(n){Nu(e.$$.fragment,n),t=!1},d(n){Ru(e,n)}}}function Hu(l,e,t){let{rootNode:n}=e,{root:o}=e,{target:i}=e,{theme_mode:s}=e,{version:a}=e,{autoscroll:_}=e,{max_file_size:r=null}=e,{client:c}=e;const u=Uu();return Fu(()=>{u("mount")}),l.$$set=f=>{"rootNode"in f&&t(0,n=f.rootNode),"root"in f&&t(1,o=f.root),"target"in f&&t(2,i=f.target),"theme_mode"in f&&t(3,s=f.theme_mode),"version"in f&&t(4,a=f.version),"autoscroll"in f&&t(5,_=f.autoscroll),"max_file_size"in f&&t(6,r=f.max_file_size),"client"in f&&t(7,c=f.client)},[n,o,i,s,a,_,r,c]}class Gu extends Iu{constructor(e){super(),Su(this,e,Hu,Bu,zu,{rootNode:0,root:1,target:2,theme_mode:3,version:4,autoscroll:5,max_file_size:6,client:7})}get rootNode(){return this.$$.ctx[0]}set rootNode(e){this.$$set({rootNode:e}),at()}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),at()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),at()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),at()}get version(){return this.$$.ctx[4]}set version(e){this.$$set({version:e}),at()}get autoscroll(){return this.$$.ctx[5]}set autoscroll(e){this.$$set({autoscroll:e}),at()}get max_file_size(){return this.$$.ctx[6]}set max_file_size(e){this.$$set({max_file_size:e}),at()}get client(){return this.$$.ctx[7]}set client(e){this.$$set({client:e}),at()}}const Zu="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='576'%20height='576'%20viewBox='0%200%20576%20576'%20fill='none'%3e%3cpath%20d='M287.5%20229L86%20344.5L287.5%20460L489%20344.5L287.5%20229Z'%20stroke='url(%23paint0_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M287.5%20116L86%20231.5L287.5%20347L489%20231.5L287.5%20116Z'%20stroke='url(%23paint1_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M86%20344L288%20229'%20stroke='url(%23paint2_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='bevel'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_102_7'%20x1='60'%20y1='341'%20x2='429.5'%20y2='344'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_102_7'%20x1='513.5'%20y1='231'%20x2='143.5'%20y2='231'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_102_7'%20x1='60'%20y1='344'%20x2='428.987'%20y2='341.811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",Wu={accordion:{component:()=>O(()=>import("./Index-RkeFq165.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]),import.meta.url)},annotatedimage:{component:()=>O(()=>import("./Index-i_GKsICI.js"),__vite__mapDeps([10,1,2,3,4,5,6,11,12,13,14,15]),import.meta.url)},audio:{example:()=>O(()=>import("./Example-BQyGztrG.js"),__vite__mapDeps([16,17]),import.meta.url),component:()=>O(()=>import("./index-BpRV2GAs.js"),__vite__mapDeps([18,2,3,4,5,11,12,19,20,14,21,22,23,24,25,26,27,28,1,6,29,16,17,30]),import.meta.url)},box:{component:()=>O(()=>import("./Index-dzW3en6q.js"),__vite__mapDeps([31,1,2,3,4,5,6]),import.meta.url)},button:{component:()=>O(()=>import("./Index-Bm9S7Zvm.js"),__vite__mapDeps([32,3,4,1,2,5,6]),import.meta.url)},chatbot:{component:()=>O(()=>import("./Index-CUtJsgll.js"),__vite__mapDeps([33,2,3,4,5,34,19,14,35,36,37,38,39,40,41,42,43,44,45,1,6,11,46]),import.meta.url)},checkbox:{example:()=>O(()=>import("./Example-CZ-iEz1g.js"),__vite__mapDeps([47,17]),import.meta.url),component:()=>O(()=>import("./Index-C5yUArxr.js"),__vite__mapDeps([48,1,2,3,4,5,6,49,50]),import.meta.url)},checkboxgroup:{example:()=>O(()=>import("./Example-CRm1Pmw_.js"),__vite__mapDeps([51,17]),import.meta.url),component:()=>O(()=>import("./Index-BSDO0hSy.js"),__vite__mapDeps([52,1,2,3,4,5,6,53,49,54]),import.meta.url)},code:{example:()=>O(()=>import("./Example-Wp-_4AVX.js"),__vite__mapDeps([55,56]),import.meta.url),component:()=>O(()=>import("./Index-C9nqmP2b.js").then(l=>l.F),__vite__mapDeps([57,3,4,1,2,5,6,44,45,20,14,11,12,55,56,58]),import.meta.url)},colorpicker:{example:()=>O(()=>import("./Example-BaLyJYAe.js"),__vite__mapDeps([59,60]),import.meta.url),component:()=>O(()=>import("./Index-Bj_rbmDg.js"),__vite__mapDeps([61,2,3,4,5,53,49,1,6,59,60,62]),import.meta.url)},column:{component:()=>O(()=>import("./Index-ChKBj1l7.js"),__vite__mapDeps([7,2,3,4,5,8]),import.meta.url)},dataframe:{example:()=>O(()=>import("./Example-CMXuI9oj.js"),__vite__mapDeps([63,64]),import.meta.url),component:()=>O(()=>import("./Index-DqFs2tUO.js"),__vite__mapDeps([65,1,2,3,4,5,6,34,24,25,39,40,41,42,43,66,63,64,67]),import.meta.url)},dataset:{component:()=>O(()=>import("./Index-CKFR6bY7.js"),__vite__mapDeps([68,1,2,3,4,5,6,69]),import.meta.url)},downloadbutton:{component:()=>O(()=>import("./Index-B66Yrs9e.js"),__vite__mapDeps([70,3,4,1,2,5,6,71]),import.meta.url)},dropdown:{example:()=>O(()=>import("./Example-CUwox43B.js"),__vite__mapDeps([72,17]),import.meta.url),component:()=>O(()=>import("./Index-D67FOkHI.js"),__vite__mapDeps([73,2,3,4,5,53,49,74,1,6,72,17,75]),import.meta.url)},file:{example:()=>O(()=>import("./Example-DrmWnoSo.js"),__vite__mapDeps([76,77]),import.meta.url),component:()=>O(()=>import("./Index-DoR9meBn.js"),__vite__mapDeps([78,79,2,3,4,5,11,12,80,24,25,26,20,14,23,81,1,6,29,28,76,77]),import.meta.url)},fileexplorer:{example:()=>O(()=>import("./Example-CIFMxn5c.js"),__vite__mapDeps([82,83]),import.meta.url),component:()=>O(()=>import("./Index-mQjq5qpN.js"),__vite__mapDeps([84,2,3,4,5,80,1,6,11,85]),import.meta.url)},form:{component:()=>O(()=>import("./Index-DDCF2BFd.js"),__vite__mapDeps([86,87]),import.meta.url)},gallery:{component:()=>O(()=>import("./Index-rf9QTX8y.js"),__vite__mapDeps([88,1,2,3,4,5,6,29,28,11,12,19,20,14,13,24,25,26,23,35,36,79,80,81,89,77]),import.meta.url)},group:{component:()=>O(()=>import("./Index-CS2xVf8J.js"),__vite__mapDeps([90,91]),import.meta.url)},highlightedtext:{component:()=>O(()=>import("./Index-BgErhRC7.js"),__vite__mapDeps([92,93,2,3,4,5,1,6,11,12,94]),import.meta.url)},html:{example:()=>O(()=>import("./Example-C2a4WxRl.js"),__vite__mapDeps([95,96]),import.meta.url),component:()=>O(()=>import("./Index-HrY4d4EM.js"),__vite__mapDeps([97,2,3,4,5,1,6,98]),import.meta.url)},image:{example:()=>O(()=>import("./Example-BI9uF_3D.js"),__vite__mapDeps([99,35,14,2,3,4,5,36,100]),import.meta.url),component:()=>O(()=>import("./Index-C5jlwfFm.js"),__vite__mapDeps([101,102,2,3,4,5,11,12,19,20,14,13,35,36,27,28,74,1,6,24,25,103,29,99,100]),import.meta.url)},imageeditor:{example:()=>O(()=>import("./Example-CO76L8ig.js"),__vite__mapDeps([104,102,2,3,4,5,11,12,19,20,14,13,35,36,27,28,74,1,6,24,25,103,105,100]),import.meta.url),component:()=>O(()=>import("./Index-0s73R3Rk.js"),__vite__mapDeps([106,102,2,3,4,5,11,12,19,20,14,13,35,36,27,28,74,1,6,24,25,103,107,44,23,108,100]),import.meta.url)},json:{component:()=>O(()=>import("./Index-DFxZakaS.js"),__vite__mapDeps([109,1,2,3,4,5,6,44,45,12,11,110]),import.meta.url)},label:{component:()=>O(()=>import("./Index-Cqx-FJgX.js"),__vite__mapDeps([111,2,3,4,5,1,6,11,12,112]),import.meta.url)},markdown:{example:()=>O(()=>import("./Example-mBWp17nR.js"),__vite__mapDeps([113,40,41,2,3,4,5,42]),import.meta.url),component:()=>O(()=>import("./Index-CWKLGFvw.js"),__vite__mapDeps([114,39,2,3,4,5,40,41,42,43,1,6,113]),import.meta.url)},model3d:{example:()=>O(()=>import("./Example-uQ8MuYg6.js"),__vite__mapDeps([115,17]),import.meta.url),component:()=>O(()=>import("./Index-BarCNVoD.js"),__vite__mapDeps([116,3,4,2,5,11,20,14,80,23,24,25,26,1,6,12,29,28,115,17,117]),import.meta.url)},multimodaltextbox:{example:()=>O(()=>import("./Example-aHy38Zn4.js"),__vite__mapDeps([118,35,14,2,3,4,5,36,119]),import.meta.url),component:()=>O(()=>import("./Index-mWZjdNKk.js"),__vite__mapDeps([120,2,3,4,5,53,49,80,21,121,24,25,35,14,36,1,6,118,119,122]),import.meta.url)},number:{example:()=>O(()=>import("./Example-CqL1e7EB.js"),__vite__mapDeps([123,17]),import.meta.url),component:()=>O(()=>import("./Index-DWYbznWI.js"),__vite__mapDeps([124,1,2,3,4,5,6,53,49,125]),import.meta.url)},paramviewer:{example:()=>O(()=>import("./Example-C9__vDgN.js"),__vite__mapDeps([126,17]),import.meta.url),component:()=>O(()=>import("./Index-a7Cqrz60.js"),__vite__mapDeps([127,41,2,3,4,5,128]),import.meta.url)},plot:{component:()=>O(()=>import("./Index-34zXpzH2.js"),__vite__mapDeps([129,2,3,4,5,93,66,12,1,6,11,130]),import.meta.url)},radio:{example:()=>O(()=>import("./Example-BoMLuz1A.js"),__vite__mapDeps([131,17]),import.meta.url),component:()=>O(()=>import("./Index-CI5B4P9h.js"),__vite__mapDeps([132,1,2,3,4,5,6,53,49,131,17,133]),import.meta.url)},row:{component:()=>O(()=>import("./Index-B0dA5A3V.js"),__vite__mapDeps([134,2,3,4,5,135]),import.meta.url)},slider:{example:()=>O(()=>import("./Example-BrizabXh.js"),__vite__mapDeps([136,17]),import.meta.url),component:()=>O(()=>import("./Index-BHQtWxGm.js"),__vite__mapDeps([137,1,2,3,4,5,6,53,49,138]),import.meta.url)},state:{component:()=>O(()=>import("./Index-uRgjJb4U.js"),[],import.meta.url)},statustracker:{component:()=>O(()=>import("./index-CzuPDZ9-.js"),__vite__mapDeps([139,2,3,4,5,1,6]),import.meta.url)},tabitem:{component:()=>O(()=>import("./Index-D-WFSnDJ.js"),__vite__mapDeps([140,141,2,3,4,5,142,7,8,143]),import.meta.url)},tabs:{component:()=>O(()=>import("./Index-DWOMC-fE.js"),__vite__mapDeps([144,141,2,3,4,5,142]),import.meta.url)},textbox:{example:()=>O(()=>import("./Example-C7XUkkid.js"),__vite__mapDeps([145,146]),import.meta.url),component:()=>O(()=>import("./Index-XZHENFUj.js"),__vite__mapDeps([147,148,2,3,4,5,53,49,44,45,1,6,149,145,146]),import.meta.url)},uploadbutton:{component:()=>O(()=>import("./Index-DbSX1Tgn.js"),__vite__mapDeps([150,3,4,1,2,5,6,151]),import.meta.url)},video:{example:()=>O(()=>import("./Example-CCJuO3xS.js"),__vite__mapDeps([152,37,14,2,3,4,5,38,153]),import.meta.url),component:()=>O(()=>import("./index-DN6G7dvw.js"),__vite__mapDeps([154,24,3,4,2,5,25,26,20,14,23,11,121,27,28,102,12,19,13,35,36,74,1,6,103,37,38,22,152,153,29,155,100]),import.meta.url)}},_t={};function gi({api_url:l,name:e,id:t,variant:n}){const o=window.__GRADIO__CC__,i={...Wu,...o||{}};if(_t[`${t}-${n}`])return{component:_t[`${t}-${n}`],name:e};try{if(!i?.[t]?.[n]&&!i?.[e]?.[n])throw new Error;return _t[`${t}-${n}`]=(i?.[t]?.[n]||i?.[e]?.[n])(),{name:e,component:_t[`${t}-${n}`]}}catch{try{return _t[`${t}-${n}`]=Ju(l,t,n),{name:e,component:_t[`${t}-${n}`]}}catch(a){if(n==="example")return _t[`${t}-${n}`]=O(()=>import("./Example-DxdiEFS_.js"),__vite__mapDeps([156,17]),import.meta.url),{name:e,component:_t[`${t}-${n}`]};throw console.error(`failed to load: ${e}`),console.error(a),a}}}function Qu(l){return new Promise((e,t)=>{const n=document.createElement("link");n.rel="stylesheet",n.href=l,document.head.appendChild(n),n.onload=()=>e(),n.onerror=()=>t()})}function Ju(l,e,t){return Promise.all([Qu(`${l}/custom_component/${e}/${t}/style.css`),O(()=>import(`${l}/custom_component/${e}/${t}/index.js`),[],import.meta.url)]).then(([n,o])=>o)}function Xu(){const l=vn({}),e={},t={},n=new Map,o=new Map,i=new Map,s={};function a({fn_index:r,status:c,queue:u=!0,size:f,position:d=null,eta:g=null,message:m=null,progress:h}){const p=t[r],b=e[r],$=s[r],C=p.map(w=>{let k;const y=n.get(w)||0;if($==="pending"&&c!=="pending"){let j=y-1;n.set(w,j<0?0:j),k=j>0?"pending":c}else $==="pending"&&c==="pending"?k="pending":$!=="pending"&&c==="pending"?(k="pending",n.set(w,y+1)):k=c;return{id:w,queue_position:d,queue_size:f,eta:g,status:k,message:m,progress:h}});b.forEach(w=>{const k=o.get(w)||0;if($==="pending"&&c!=="pending"){let y=k-1;o.set(w,y<0?0:y),i.set(w,c)}else $!=="pending"&&c==="pending"?(o.set(w,k+1),i.set(w,c)):i.delete(w)}),l.update(w=>(C.forEach(({id:k,queue_position:y,queue_size:j,eta:v,status:E,message:q,progress:A})=>{w[k]={queue:u,queue_size:j,queue_position:y,eta:v,message:q,progress:A,status:E,fn_index:r}}),w)),s[r]=c}function _(r,c,u){e[r]=c,t[r]=u}return{update:a,register:_,subscribe:l.subscribe,get_status_for_fn(r){return s[r]},get_inputs_to_update(){return i}}}let St=[];function Yu(){let l,e=vn({}),t={},n,o,i,s,a=Xu();const _=vn();let r=[],c,u={},f;function d({app:k,components:y,layout:j,dependencies:v,root:E,options:q}){c=k,b(r),r=y,n=new Set,o=new Set,St=[],i=new Map,l=new Map,s={},f={id:j.id,type:"column",props:{interactive:!1,scale:q.fill_height?1:null},has_modes:!1,instance:null,component:null,component_class_id:"",key:null},y.push(f),v.forEach(A=>{a.register(A.id,A.inputs,A.outputs),A.frontend_fn=vi(A.js,!!A.backend_fn,A.inputs.length,A.outputs.length),$i(A.targets,A.id,t),bi(A,n,o)}),e.set(t),i=ki(y,E),s=y.reduce((A,B)=>(A[B.id]=B,A),{}),m(j,E).then(()=>{_.set(f)})}function g({render_id:k,components:y,layout:j,root:v,dependencies:E}){ki(y,v).forEach((I,R)=>{i.set(R,I)}),t={},E.forEach(I=>{a.register(I.id,I.inputs,I.outputs),I.frontend_fn=vi(I.js,!!I.backend_fn,I.inputs.length,I.outputs.length),$i(I.targets,I.id,t),bi(I,n,o)}),e.set(t);let A=s[j.id],B=[];const M=I=>{B.push(I),I.children&&I.children.forEach(R=>{M(R)})};M(A),b(B),Object.entries(s).forEach(([I,R])=>{let D=Number(I);R.rendered_in===k&&(delete s[D],l.has(D)&&l.delete(D))}),y.forEach(I=>{s[I.id]=I,l.set(I.id,I)}),A.parent&&(A.parent.children[A.parent.children.indexOf(A)]=s[j.id]),m(j,v,A.parent).then(()=>{_.set(f)})}async function m(k,y,j){const v=s[k.id];return v.component=(await i.get(v.component_class_id))?.default,v.parent=j,v.type==="dataset"&&(v.props.component_map=xi(v.type,v.component_class_id,y,r,v.props.components).example_components),t[v.id]&&(v.props.attached_events=Object.keys(t[v.id])),v.props.interactive=xu(v.id,v.props.interactive,v.props.value,n,o),v.props.server=ef(v.id,v.props.server_fns,c),v.key!=null&&u[v.key]!==void 0&&(v.props.value=u[v.key]),l.set(v.id,v),k.children&&(v.children=await Promise.all(k.children.map(E=>m(E,y,v)))),v}let h=!1,p=vn(!1);function b(k){k.forEach(y=>{y.key!=null&&(u[y.key]=y.props.value)})}function $(){_.update(k=>{for(let y=0;y<St.length;y++)for(let j=0;j<St[y].length;j++){const v=St[y][j];if(!v)continue;const E=s[v.id];if(!E)continue;let q;v.value instanceof Map?q=new Map(v.value):v.value instanceof Set?q=new Set(v.value):Array.isArray(v.value)?q=[...v.value]:v.value===null?q=null:typeof v.value=="object"?q={...v.value}:q=v.value,E.props[v.prop]=q}return k}),St=[],h=!1,p.set(!1)}function C(k){k&&(St.push(k),h||(h=!0,p.set(!0),requestAnimationFrame($)))}function w(k){const y=l.get(k);return y?y.instance.get_value?y.instance.get_value():y.props.value:null}return{layout:_,targets:e,update_value:C,get_data:w,loading_status:a,scheduled_updates:p,create_layout:(...k)=>requestAnimationFrame(()=>d(...k)),rerender_layout:g}}const Ki=Object.getPrototypeOf(async function(){}).constructor;function vi(l,e,t,n){if(!l)return null;const o=e?t===1:n===1;try{return new Ki("__fn_args",`  let result = await (${l})(...__fn_args);
  return (${o} && !Array.isArray(result)) ? [result] : result;`)}catch(i){return console.error("Could not parse custom js method."),console.error(i),null}}function $i(l,e,t){return l.forEach(([n,o])=>{t[n]||(t[n]={}),t[n]?.[o]&&!t[n]?.[o].includes(e)?t[n][o].push(e):t[n][o]=[e]}),t}function bi(l,e,t){return l.inputs.forEach(n=>e.add(n)),l.outputs.forEach(n=>t.add(n)),[e,t]}function Ku(l){return Array.isArray(l)&&l.length===0||l===""||l===0||!l}function xu(l,e,t,n,o){return e===!1?!1:e===!0?!0:!!(n.has(l)||!o.has(l)&&Ku(t))}function ef(l,e,t){return e?e.reduce((n,o)=>(n[o]=async(...i)=>(i.length===1&&(i=i[0]),await t.component_server(l,o,i)),n),{}):{}}function xi(l,e,t,n,o){let i=new Map;l==="dataset"&&o&&o.forEach(a=>{if(i.has(a))return;let _;const r=n.find(c=>c.type===a);r&&(_=gi({api_url:t,name:a,id:r.component_class_id,variant:"example"}),i.set(a,_.component))});const s=gi({api_url:t,name:l,id:e,variant:"component"});return{component:s.component,name:s.name,example_components:i.size>0?i:void 0}}function ki(l,e){let t=new Map;return l.forEach(n=>{const{component:o,example_components:i}=xi(n.type,n.component_class_id,e,l);if(t.set(n.component_class_id,o),i)for(const[s,a]of i)t.set(s,a)}),t}const{SvelteComponent:tf,append:Pe,attr:ne,check_outros:mn,component_subscribe:Yt,create_component:Mn,destroy_component:Nn,detach:Ve,element:Ge,empty:wi,flush:pe,globals:nf,group_outros:hn,init:lf,insert:Fe,listen:Pl,mount_component:Fn,noop:il,safe_not_equal:of,set_data:es,set_style:gn,space:Ye,src_url_equal:ts,text:ns,transition_in:ye,transition_out:Ne}=window.__gradio__svelte__internal,{document:hl}=nf,{tick:sl}=window.__gradio__svelte__internal;function yi(l){return hl.title=l[2],{c:il,m:il,d:il}}function Ei(l){let e,t;return e=new Gu({props:{rootNode:l[13],root:l[0],target:l[3],theme_mode:l[9],version:l[12],autoscroll:l[4],max_file_size:l[10].config.max_file_size,client:l[10]}}),e.$on("mount",l[25]),{c(){Mn(e.$$.fragment)},m(n,o){Fn(e,n,o),t=!0},p(n,o){const i={};o[0]&8192&&(i.rootNode=n[13]),o[0]&1&&(i.root=n[0]),o[0]&8&&(i.target=n[3]),o[0]&512&&(i.theme_mode=n[9]),o[0]&4096&&(i.version=n[12]),o[0]&16&&(i.autoscroll=n[4]),o[0]&1024&&(i.max_file_size=n[10].config.max_file_size),o[0]&1024&&(i.client=n[10]),e.$set(i)},i(n){t||(ye(e.$$.fragment,n),t=!0)},o(n){Ne(e.$$.fragment,n),t=!1},d(n){Nn(e,n)}}}function ji(l){let e,t,n,o=l[18]("common.built_with_gradio")+"",i,s,a,_,r,c=l[5]&&Ci(l);return{c(){e=Ge("footer"),c&&c.c(),t=Ye(),n=Ge("a"),i=ns(o),s=Ye(),a=Ge("img"),ts(a.src,_=Zu)||ne(a,"src",_),ne(a,"alt",r=l[18]("common.logo")),ne(a,"class","svelte-1rjryqp"),ne(n,"href","https://gradio.app"),ne(n,"class","built-with svelte-1rjryqp"),ne(n,"target","_blank"),ne(n,"rel","noreferrer"),ne(e,"class","svelte-1rjryqp")},m(u,f){Fe(u,e,f),c&&c.m(e,null),Pe(e,t),Pe(e,n),Pe(n,i),Pe(n,s),Pe(n,a)},p(u,f){u[5]?c?c.p(u,f):(c=Ci(u),c.c(),c.m(e,t)):c&&(c.d(1),c=null),f[0]&262144&&o!==(o=u[18]("common.built_with_gradio")+"")&&es(i,o),f[0]&262144&&r!==(r=u[18]("common.logo"))&&ne(a,"alt",r)},d(u){u&&Ve(e),c&&c.d()}}}function Ci(l){let e,t=l[18]("errors.use_via_api")+"",n,o,i,s,a,_,r,c,u;return{c(){e=Ge("button"),n=ns(t),o=Ye(),i=Ge("img"),_=Ye(),r=Ge("div"),r.textContent="·",ts(i.src,s=Mi)||ne(i,"src",s),ne(i,"alt",a=l[18]("common.logo")),ne(i,"class","svelte-1rjryqp"),ne(e,"class","show-api svelte-1rjryqp"),ne(r,"class","svelte-1rjryqp")},m(f,d){Fe(f,e,d),Pe(e,n),Pe(e,o),Pe(e,i),Fe(f,_,d),Fe(f,r,d),c||(u=Pl(e,"click",l[34]),c=!0)},p(f,d){d[0]&262144&&t!==(t=f[18]("errors.use_via_api")+"")&&es(n,t),d[0]&262144&&a!==(a=f[18]("common.logo"))&&ne(i,"alt",a)},d(f){f&&(Ve(e),Ve(_),Ve(r)),c=!1,u()}}}function qi(l){let e,t,n,o,i;return t=new Yc({props:{api_calls:l[16],dependencies:l[1]}}),{c(){e=Ge("div"),Mn(t.$$.fragment),ne(e,"id","api-recorder-container"),ne(e,"class","svelte-1rjryqp")},m(s,a){Fe(s,e,a),Fn(t,e,null),n=!0,o||(i=Pl(e,"click",l[35]),o=!0)},p(s,a){const _={};a[0]&65536&&(_.api_calls=s[16]),a[0]&2&&(_.dependencies=s[1]),t.$set(_)},i(s){n||(ye(t.$$.fragment,s),n=!0)},o(s){Ne(t.$$.fragment,s),n=!1},d(s){s&&Ve(e),Nn(t),o=!1,i()}}}function Ai(l){let e,t,n,o,i,s,a,_;return i=new Mc({props:{root_node:l[13],dependencies:l[1],root:l[0],app:l[10],space_id:l[11],api_calls:l[16]}}),i.$on("close",l[37]),{c(){e=Ge("div"),t=Ge("div"),n=Ye(),o=Ge("div"),Mn(i.$$.fragment),ne(t,"class","backdrop svelte-1rjryqp"),ne(o,"class","api-docs-wrap svelte-1rjryqp"),ne(e,"class","api-docs svelte-1rjryqp")},m(r,c){Fe(r,e,c),Pe(e,t),Pe(e,n),Pe(e,o),Fn(i,o,null),s=!0,a||(_=Pl(t,"click",l[36]),a=!0)},p(r,c){const u={};c[0]&8192&&(u.root_node=r[13]),c[0]&2&&(u.dependencies=r[1]),c[0]&1&&(u.root=r[0]),c[0]&1024&&(u.app=r[10]),c[0]&2048&&(u.space_id=r[11]),c[0]&65536&&(u.api_calls=r[16]),i.$set(u)},i(r){s||(ye(i.$$.fragment,r),s=!0)},o(r){Ne(i.$$.fragment,r),s=!1},d(r){r&&Ve(e),Nn(i),a=!1,_()}}}function Li(l){let e,t;return e=new Nr({props:{messages:l[17]}}),e.$on("close",l[24]),{c(){Mn(e.$$.fragment)},m(n,o){Fn(e,n,o),t=!0},p(n,o){const i={};o[0]&131072&&(i.messages=n[17]),e.$set(i)},i(n){t||(ye(e.$$.fragment,n),t=!0)},o(n){Ne(e.$$.fragment,n),t=!1},d(n){Nn(e,n)}}}function sf(l){let e,t,n,o,i,s,a,_,r,c,u=l[7]&&yi(l),f=l[13]&&l[10].config&&Ei(l),d=l[6]&&ji(l),g=l[15]&&qi(l),m=l[14]&&l[13]&&Ai(l),h=l[17]&&Li(l);return{c(){u&&u.c(),e=wi(),t=Ye(),n=Ge("div"),o=Ge("div"),f&&f.c(),i=Ye(),d&&d.c(),s=Ye(),g&&g.c(),a=Ye(),m&&m.c(),_=Ye(),h&&h.c(),r=wi(),ne(o,"class","contain svelte-1rjryqp"),gn(o,"flex-grow",l[8]?"1":"auto"),ne(n,"class","wrap svelte-1rjryqp"),gn(n,"min-height",l[8]?"100%":"auto")},m(p,b){u&&u.m(hl.head,null),Pe(hl.head,e),Fe(p,t,b),Fe(p,n,b),Pe(n,o),f&&f.m(o,null),Pe(n,i),d&&d.m(n,null),Fe(p,s,b),g&&g.m(p,b),Fe(p,a,b),m&&m.m(p,b),Fe(p,_,b),h&&h.m(p,b),Fe(p,r,b),c=!0},p(p,b){p[7]?u||(u=yi(p),u.c(),u.m(e.parentNode,e)):u&&(u.d(1),u=null),p[13]&&p[10].config?f?(f.p(p,b),b[0]&9216&&ye(f,1)):(f=Ei(p),f.c(),ye(f,1),f.m(o,null)):f&&(hn(),Ne(f,1,1,()=>{f=null}),mn()),b[0]&256&&gn(o,"flex-grow",p[8]?"1":"auto"),p[6]?d?d.p(p,b):(d=ji(p),d.c(),d.m(n,null)):d&&(d.d(1),d=null),b[0]&256&&gn(n,"min-height",p[8]?"100%":"auto"),p[15]?g?(g.p(p,b),b[0]&32768&&ye(g,1)):(g=qi(p),g.c(),ye(g,1),g.m(a.parentNode,a)):g&&(hn(),Ne(g,1,1,()=>{g=null}),mn()),p[14]&&p[13]?m?(m.p(p,b),b[0]&24576&&ye(m,1)):(m=Ai(p),m.c(),ye(m,1),m.m(_.parentNode,_)):m&&(hn(),Ne(m,1,1,()=>{m=null}),mn()),p[17]?h?(h.p(p,b),b[0]&131072&&ye(h,1)):(h=Li(p),h.c(),ye(h,1),h.m(r.parentNode,r)):h&&(hn(),Ne(h,1,1,()=>{h=null}),mn())},i(p){c||(ye(f),ye(g),ye(m),ye(h),c=!0)},o(p){Ne(f),Ne(g),Ne(m),Ne(h),c=!1},d(p){p&&(Ve(t),Ve(n),Ve(s),Ve(a),Ve(_),Ve(r)),u&&u.d(p),Ve(e),f&&f.d(),d&&d.d(),g&&g.d(p),m&&m.d(p),h&&h.d(p)}}}const rf=/^'([^]+)'$/,af=15,_f=10;function Ti(l){return"detail"in l}function cf(l,e,t){let n,o,i,s,a;Yt(l,Oi,L=>t(18,s=L)),Es();let{root:_}=e,{components:r}=e,{layout:c}=e,{dependencies:u}=e,{title:f="Gradio"}=e,{target:d}=e,{autoscroll:g}=e,{show_api:m=!0}=e,{show_footer:h=!0}=e,{control_page_title:p=!1}=e,{app_mode:b}=e,{theme_mode:$}=e,{app:C}=e,{space_id:w}=e,{version:k}=e,{js:y}=e,{fill_height:j=!1}=e,{ready:v}=e;const{layout:E,targets:q,update_value:A,get_data:B,loading_status:M,scheduled_updates:I,create_layout:R,rerender_layout:D}=Yu();Yt(l,E,L=>t(13,a=L)),Yt(l,q,L=>t(42,o=L)),Yt(l,M,L=>t(33,n=L)),Yt(l,I,L=>t(43,i=L));let F=new URLSearchParams(window.location.search),le=F.get("view")==="api"&&m,ee=F.get("view")==="api-recorder"&&m;function oe(L){t(15,ee=!1),t(14,le=L);let x=new URLSearchParams(window.location.search);L?x.set("view","api"):x.delete("view"),history.replaceState(null,"","?"+x.toString())}let vt=[],{render_complete:sn=!1}=e;async function Ol(L,x){const G=u.find(W=>W.id==x).outputs,U=L?.map((W,me)=>({id:G[me],prop:"value_is_output",value:!0}));A(U),await sl();const se=[];L?.forEach((W,me)=>{if(typeof W=="object"&&W!==null&&W.__type__==="update")for(const[K,it]of Object.entries(W))K!=="__type__"&&se.push({id:G[me],prop:K,value:it});else se.push({id:G[me],prop:"value",value:W})}),A(se),await sl()}let Il=new Map,ie=[];function ot(L,x,G){return{message:L,fn_index:x,type:G,id:++os}}function ls(L,x){t(17,ie=[ot(L,-1,x),...ie])}let os=-1,Un=!1;document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&(Un=!0)});const is=s("blocks.long_requests_queue"),ss=s("blocks.connection_can_break"),rs=s("blocks.lost_connection"),Dl=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);let Rl=!1,Sl=!1;function Pt(L,x=null,G=null){let U=()=>{};function se(){U()}i?U=I.subscribe(W=>{W||(Vl(L,x,G),se())}):Vl(L,x,G)}async function Vl(L,x=null,G=null){let U=u.find(K=>K.id===L);const se=M.get_status_for_fn(L);t(17,ie=ie.filter(({fn_index:K})=>K!==L)),(se==="pending"||se==="generating")&&(U.pending_request=!0);let W={fn_index:L,data:await Promise.all(U.inputs.map(K=>B(K))),event_data:U.collects_event_data?G:null,trigger_id:x};U.frontend_fn?U.frontend_fn(W.data.concat(await Promise.all(U.outputs.map(K=>B(K))))).then(K=>{U.backend_fn?(W.data=K,me(W)):Ol(K,L)}):U.types.cancel&&U.cancels?await Promise.all(U.cancels.map(async K=>{const it=Il.get(K);return it?.cancel(),it})):U.backend_fn&&(U.trigger_mode==="once"?U.pending_request||me(W):U.trigger_mode==="multiple"?me(W):U.trigger_mode==="always_last"&&(U.pending_request?U.final_event=W:me(W)));async function me(K){ee&&t(16,vt=[...vt,JSON.parse(JSON.stringify(K))]);let it;try{it=C.submit(K.fn_index,K.data,K.event_data,K.trigger_id)}catch(de){t(17,ie=[ot(String(de),0,"error"),...ie]),M.update({status:"error",fn_index:0,eta:0,queue:!1,queue_position:null}),rn(n);return}Il.set(L,it);for await(const de of it)de.type==="data"?gs(de):de.type==="render"?vs(de):de.type==="status"?bs(de):de.type==="log"&&$s(de);function gs(de){const{data:he,fn_index:re}=de;U.pending_request&&U.final_event&&(U.pending_request=!1,me(U.final_event)),U.pending_request=!1,Ol(he,re),rn(n)}function vs(de){const{data:he}=de;let re=he.components,Le=he.layout,Ot=he.dependencies,It=he.render_id,Bn=[];u.forEach((Qt,ks)=>{Qt.rendered_in===It&&Bn.push(ks)}),Bn.reverse().forEach(Qt=>{u.splice(Qt,1)}),Ot.forEach(Qt=>{u.push(Qt)}),D({components:re,layout:Le,root:_,dependencies:u,render_id:It})}function $s(de){const{log:he,fn_index:re,level:Le}=de;t(17,ie=[ot(he,re,Le),...ie])}function bs(de){const{fn_index:he,...re}=de;if(M.update({...re,status:re.stage,progress:re.progress_data,fn_index:he}),rn(n),!Rl&&w!==null&&re.position!==void 0&&re.position>=2&&re.eta!==void 0&&re.eta>af&&(Rl=!0,t(17,ie=[ot(is,he,"warning"),...ie])),!Sl&&Dl&&re.eta!==void 0&&re.eta>_f&&(Sl=!0,t(17,ie=[ot(ss,he,"warning"),...ie])),re.stage==="complete"&&(re.changed_state_ids?.forEach(Le=>{u.filter(Ot=>Ot.targets.some(([It,Bn])=>It===Le)).forEach(Ot=>{Pt(Ot.id,K.trigger_id)})}),u.forEach(async Le=>{Le.trigger_after===he&&Pt(Le.id,K.trigger_id)})),re.broken&&Dl&&Un)window.setTimeout(()=>{t(17,ie=[ot(rs,he,"error"),...ie])},0),Pt(U.id,K.trigger_id,G),Un=!1;else if(re.stage==="error"){if(re.message){const Le=re.message.replace(rf,(Ot,It)=>It);t(17,ie=[ot(Le,he,"error"),...ie])}u.map(async Le=>{Le.trigger_after===he&&!Le.trigger_only_on_success&&Pt(Le.id,K.trigger_id)})}}}}function as(L,x){if(w===null)return;const G=new URL(`https://huggingface.co/spaces/${w}/discussions/new`);L!==void 0&&L.length>0&&G.searchParams.set("title",L),G.searchParams.set("description",x),window.open(G.toString(),"_blank")}function _s(L){const x=L.detail;t(17,ie=ie.filter(G=>G.id!==x))}const cs=L=>!!(L&&new URL(L,location.href).origin!==location.origin);async function us(){y&&await new Ki(`let result = await (${y})();
					return (!Array.isArray(result)) ? [result] : result;`)(),await sl();for(var L=d.getElementsByTagName("a"),x=0;x<L.length;x++){const G=L[x].getAttribute("target"),U=L[x].getAttribute("href");cs(U)&&G!=="_blank"&&L[x].setAttribute("target","_blank")}u.forEach(G=>{G.targets[0][1]==="load"&&Pt(G.id)}),!sn&&(d.addEventListener("prop_change",G=>{if(!Ti(G))throw new Error("not a custom event");const{id:U,prop:se,value:W}=G.detail;A([{id:U,prop:se,value:W}])}),d.addEventListener("gradio",G=>{if(!Ti(G))throw new Error("not a custom event");const{id:U,event:se,data:W}=G.detail;if(se==="share"){const{title:me,description:K}=W;as(me,K)}else se==="error"||se==="warning"?t(17,ie=[ot(W,-1,se),...ie]):se=="clear_status"?fs(U,"complete",W):o[U]?.[se]?.forEach(K=>{requestAnimationFrame(()=>{Pt(K,U,W)})})}),t(27,sn=!0))}function fs(L,x,G){G.status=x,A([{id:L,prop:"loading_status",value:G}])}function rn(L){let x=[];Object.entries(L).forEach(([se,W])=>{let me=u.find(K=>K.id==W.fn_index);me!==void 0&&(W.scroll_to_output=me.scroll_to_output,W.show_progress=me.show_progress,x.push({id:parseInt(se),prop:"loading_status",value:W}))});const G=M.get_inputs_to_update(),U=Array.from(G).map(([se,W])=>({id:se,prop:"pending",value:W==="pending"}));A([...x,...U])}const ps=()=>{oe(!le)},ds=()=>{oe(!0),t(15,ee=!1)},ms=()=>{oe(!1)},hs=L=>{oe(!1),t(16,vt=[]),t(15,ee=L.detail.api_recorder_visible)};return l.$$set=L=>{"root"in L&&t(0,_=L.root),"components"in L&&t(28,r=L.components),"layout"in L&&t(29,c=L.layout),"dependencies"in L&&t(1,u=L.dependencies),"title"in L&&t(2,f=L.title),"target"in L&&t(3,d=L.target),"autoscroll"in L&&t(4,g=L.autoscroll),"show_api"in L&&t(5,m=L.show_api),"show_footer"in L&&t(6,h=L.show_footer),"control_page_title"in L&&t(7,p=L.control_page_title),"app_mode"in L&&t(8,b=L.app_mode),"theme_mode"in L&&t(9,$=L.theme_mode),"app"in L&&t(10,C=L.app),"space_id"in L&&t(11,w=L.space_id),"version"in L&&t(12,k=L.version),"js"in L&&t(30,y=L.js),"fill_height"in L&&t(31,j=L.fill_height),"ready"in L&&t(26,v=L.ready),"render_complete"in L&&t(27,sn=L.render_complete)},l.$$.update=()=>{l.$$.dirty[0]&805307395|l.$$.dirty[1]&1&&R({components:r,layout:c,dependencies:u,root:_,app:C,options:{fill_height:j}}),l.$$.dirty[0]&8192&&t(26,v=!!a),l.$$.dirty[1]&4&&rn(n)},[_,u,f,d,g,m,h,p,b,$,C,w,k,a,le,ee,vt,ie,s,E,q,M,I,oe,_s,us,v,sn,r,c,y,j,ls,n,ps,ds,ms,hs]}class uf extends tf{constructor(e){super(),lf(this,e,cf,sf,of,{root:0,components:28,layout:29,dependencies:1,title:2,target:3,autoscroll:4,show_api:5,show_footer:6,control_page_title:7,app_mode:8,theme_mode:9,app:10,space_id:11,version:12,js:30,fill_height:31,ready:26,render_complete:27,add_new_message:32},null,[-1,-1])}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),pe()}get components(){return this.$$.ctx[28]}set components(e){this.$$set({components:e}),pe()}get layout(){return this.$$.ctx[29]}set layout(e){this.$$set({layout:e}),pe()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),pe()}get title(){return this.$$.ctx[2]}set title(e){this.$$set({title:e}),pe()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),pe()}get autoscroll(){return this.$$.ctx[4]}set autoscroll(e){this.$$set({autoscroll:e}),pe()}get show_api(){return this.$$.ctx[5]}set show_api(e){this.$$set({show_api:e}),pe()}get show_footer(){return this.$$.ctx[6]}set show_footer(e){this.$$set({show_footer:e}),pe()}get control_page_title(){return this.$$.ctx[7]}set control_page_title(e){this.$$set({control_page_title:e}),pe()}get app_mode(){return this.$$.ctx[8]}set app_mode(e){this.$$set({app_mode:e}),pe()}get theme_mode(){return this.$$.ctx[9]}set theme_mode(e){this.$$set({theme_mode:e}),pe()}get app(){return this.$$.ctx[10]}set app(e){this.$$set({app:e}),pe()}get space_id(){return this.$$.ctx[11]}set space_id(e){this.$$set({space_id:e}),pe()}get version(){return this.$$.ctx[12]}set version(e){this.$$set({version:e}),pe()}get js(){return this.$$.ctx[30]}set js(e){this.$$set({js:e}),pe()}get fill_height(){return this.$$.ctx[31]}set fill_height(e){this.$$set({fill_height:e}),pe()}get ready(){return this.$$.ctx[26]}set ready(e){this.$$set({ready:e}),pe()}get render_complete(){return this.$$.ctx[27]}set render_complete(e){this.$$set({render_complete:e}),pe()}get add_new_message(){return this.$$.ctx[32]}}const $f=Object.freeze(Object.defineProperty({__proto__:null,default:uf},Symbol.toStringTag,{value:"Module"}));export{$f as B,Wn as S,Nr as T,gf as c,vf as f,hf as u};
//# sourceMappingURL=Blocks-aR9ucLZz.js.map
