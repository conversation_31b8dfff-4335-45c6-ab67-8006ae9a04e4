{"version": 3, "file": "Checkbox-1d448291.js", "sources": ["../../../../js/checkbox/shared/Checkbox.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, afterUpdate } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let value: boolean;\n\texport let value_is_output = false;\n\texport let disabled = false;\n\texport let label: string;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: boolean;\n\t\tselect: SelectData;\n\t\tinput: undefined;\n\t}>();\n\n\tfunction handle_change(): void {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: value, handle_change();\n</script>\n\n<label class:disabled>\n\t<input\n\t\tbind:checked={value}\n\t\ton:keydown={(event) => {\n\t\t\tif (event.key === \"Enter\") {\n\t\t\t\tvalue = !value;\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: 0,\n\t\t\t\t\tvalue: label,\n\t\t\t\t\tselected: value,\n\t\t\t\t});\n\t\t\t}\n\t\t}}\n\t\ton:input={(evt) => {\n\t\t\tvalue = evt.currentTarget.checked;\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: 0,\n\t\t\t\tvalue: label,\n\t\t\t\tselected: evt.currentTarget.checked,\n\t\t\t});\n\t\t}}\n\t\t{disabled}\n\t\ttype=\"checkbox\"\n\t\tname=\"test\"\n\t\tdata-testid=\"checkbox\"\n\t/>\n\t<span class=\"ml-2\">{label}</span>\n</label>\n\n<style>\n\tlabel {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\tlabel > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\tinput {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: 1px solid var(--checkbox-border-color);\n\t\tborder-radius: var(--checkbox-border-radius);\n\t\tbackground-color: var(--checkbox-background-color);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tinput:checked,\n\tinput:checked:hover,\n\tinput:checked:focus {\n\t\tborder-color: var(--checkbox-border-color-selected);\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t}\n\n\tinput:checked:focus {\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t}\n\n\tinput:hover {\n\t\tborder-color: var(--checkbox-border-color-hover);\n\t\tbackground-color: var(--checkbox-background-color-hover);\n\t}\n\n\tinput:focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t\tbackground-color: var(--checkbox-background-color-focus);\n\t}\n\n\tinput[disabled],\n\t.disabled {\n\t\tcursor: not-allowed;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "label_1", "anchor", "append", "input", "span", "value", "$$props", "value_is_output", "disabled", "label", "dispatch", "createEventDispatcher", "handle_change", "afterUpdate", "$$invalidate", "event", "evt"], "mappings": "oPAqDqBA,EAAK,CAAA,CAAA,mNA1B1BC,EA2BOC,EAAAC,EAAAC,CAAA,EA1BNC,EAwBCF,EAAAG,CAAA,YAvBcN,EAAK,CAAA,SAwBpBK,EAAgCF,EAAAI,CAAA,iIAxBjBP,EAAK,CAAA,YAwBAA,EAAK,CAAA,CAAA,oFAjDd,MAAAQ,CAAc,EAAAC,EACd,CAAA,gBAAAC,EAAkB,EAAK,EAAAD,EACvB,CAAA,SAAAE,EAAW,EAAK,EAAAF,GAChB,MAAAG,CAAa,EAAAH,EAElB,MAAAI,EAAWC,aAMRC,GAAa,CACrBF,EAAS,SAAUL,CAAK,EACnBE,GACJG,EAAS,OAAO,EAGlBG,EAAW,IAAA,CACVC,EAAA,EAAAP,EAAkB,EAAK,iBAOTF,EAAK,KAAA,uBACNU,GAAK,CACbA,EAAM,MAAQ,UACjBD,EAAA,EAAAT,GAASA,CAAK,EACdK,EAAS,SACR,CAAA,MAAO,EACP,MAAOD,EACP,SAAUJ,CAAK,CAAA,MAIPW,GAAG,CACbF,EAAA,EAAAT,EAAQW,EAAI,cAAc,OAAO,EACjCN,EAAS,SAAQ,CAChB,MAAO,EACP,MAAOD,EACP,SAAUO,EAAI,cAAc,wMArBrBJ,EAAa"}