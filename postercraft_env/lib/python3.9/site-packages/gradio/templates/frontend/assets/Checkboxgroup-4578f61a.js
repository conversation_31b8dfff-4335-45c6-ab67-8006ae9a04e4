import{S as D,e as F,s as G,I as y,F as H,o as N,m as k,g as h,G as I,h as v,w as U,u as z,k as w,H as A,O as K,C as L,an as M,t as T,x as q,N as m,j as b,p as B,B as P}from"./index-c99b2410.js";import"./Button-9c502b18.js";import{B as Q}from"./BlockTitle-ebc4bfe4.js";function E(e,t,l){const a=e.slice();return a[14]=t[l],a[16]=l,a}function R(e){let t;return{c(){t=T(e[3])},m(l,a){v(l,t,a)},p(l,a){a&8&&q(t,l[3])},d(l){l&&w(t)}}}function J(e){let t,l,a,_,f,i,s,u=e[14][0]+"",c,o,d,g;function p(){return e[10](e[14])}function S(...r){return e[11](e[16],e[14],...r)}function C(...r){return e[12](e[14],e[16],...r)}return{c(){t=k("label"),l=k("input"),i=N(),s=k("span"),c=T(u),o=N(),l.disabled=e[2],l.checked=a=e[0].includes(e[14][1]),h(l,"type","checkbox"),h(l,"name",_=e[14][1]?.toString()),h(l,"title",f=e[14][1]?.toString()),h(l,"class","svelte-1k4wjf2"),h(s,"class","ml-2 svelte-1k4wjf2"),h(t,"class","svelte-1k4wjf2"),m(t,"disabled",e[2]),m(t,"selected",e[0].includes(e[14][1]))},m(r,n){v(r,t,n),b(t,l),b(t,i),b(t,s),b(s,c),b(t,o),d||(g=[B(l,"change",p),B(l,"input",S),B(l,"keydown",C)],d=!0)},p(r,n){e=r,n&4&&(l.disabled=e[2]),n&3&&a!==(a=e[0].includes(e[14][1]))&&(l.checked=a),n&2&&_!==(_=e[14][1]?.toString())&&h(l,"name",_),n&2&&f!==(f=e[14][1]?.toString())&&h(l,"title",f),n&2&&u!==(u=e[14][0]+"")&&q(c,u),n&4&&m(t,"disabled",e[2]),n&3&&m(t,"selected",e[0].includes(e[14][1]))},d(r){r&&w(t),d=!1,P(g)}}}function V(e){let t,l,a,_;t=new Q({props:{show_label:e[5],info:e[4],$$slots:{default:[R]},$$scope:{ctx:e}}});let f=y(e[1]),i=[];for(let s=0;s<f.length;s+=1)i[s]=J(E(e,f,s));return{c(){H(t.$$.fragment),l=N(),a=k("div");for(let s=0;s<i.length;s+=1)i[s].c();h(a,"class","wrap svelte-1k4wjf2"),h(a,"data-testid","checkbox-group")},m(s,u){I(t,s,u),v(s,l,u),v(s,a,u);for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(a,null);_=!0},p(s,[u]){const c={};if(u&32&&(c.show_label=s[5]),u&16&&(c.info=s[4]),u&131080&&(c.$$scope={dirty:u,ctx:s}),t.$set(c),u&199){f=y(s[1]);let o;for(o=0;o<f.length;o+=1){const d=E(s,f,o);i[o]?i[o].p(d,u):(i[o]=J(d),i[o].c(),i[o].m(a,null))}for(;o<i.length;o+=1)i[o].d(1);i.length=f.length}},i(s){_||(U(t.$$.fragment,s),_=!0)},o(s){z(t.$$.fragment,s),_=!1},d(s){s&&(w(l),w(a)),A(t,s),K(i,s)}}}function W(e,t,l){let{value:a=[]}=t,_=a.slice(),{value_is_output:f=!1}=t,{choices:i}=t,{disabled:s=!1}=t,{label:u}=t,{info:c=void 0}=t,{show_label:o}=t;const d=L();function g(n){a.includes(n)?a.splice(a.indexOf(n),1):a.push(n),l(0,a)}function p(){d("change",a),f||d("input")}M(()=>{l(8,f=!1)});const S=n=>g(n[1]),C=(n,j,O)=>d("select",{index:n,value:j[1],selected:O.currentTarget.checked}),r=(n,j,O)=>{O.key==="Enter"&&(g(n[1]),d("select",{index:j,value:n[1],selected:!a.includes(n[1])}))};return e.$$set=n=>{"value"in n&&l(0,a=n.value),"value_is_output"in n&&l(8,f=n.value_is_output),"choices"in n&&l(1,i=n.choices),"disabled"in n&&l(2,s=n.disabled),"label"in n&&l(3,u=n.label),"info"in n&&l(4,c=n.info),"show_label"in n&&l(5,o=n.show_label)},e.$$.update=()=>{e.$$.dirty&513&&JSON.stringify(a)!==JSON.stringify(_)&&(l(9,_=a.slice()),p())},[a,i,s,u,c,o,d,g,f,_,S,C,r]}class x extends D{constructor(t){super(),F(this,t,W,V,G,{value:0,value_is_output:8,choices:1,disabled:2,label:3,info:4,show_label:5})}}export{x as C};
//# sourceMappingURL=Checkboxgroup-4578f61a.js.map
