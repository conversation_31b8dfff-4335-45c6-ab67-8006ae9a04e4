import{S as p,e as c,s as h,f as a,g as e,h as u,j as i,n as s,k as g}from"./index-c99b2410.js";function v(l){let t,o;return{c(){t=a("svg"),o=a("polyline"),e(o,"points","20 6 9 17 4 12"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","15px"),e(t,"height","14px"),e(t,"viewBox","2 0 20 20"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","3"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round")},m(r,n){u(r,t,n),i(t,o)},p:s,i:s,o:s,d(r){r&&g(t)}}}class f extends p{constructor(t){super(),c(this,t,null,v,h,{})}}function w(l){let t,o,r;return{c(){t=a("svg"),o=a("path"),r=a("path"),e(o,"fill","currentColor"),e(o,"d","M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z"),e(r,"fill","currentColor"),e(r,"d","M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","15px"),e(t,"height","14px"),e(t,"viewBox","0 0 33 33"),e(t,"color","currentColor")},m(n,d){u(n,t,d),i(t,o),i(t,r)},p:s,i:s,o:s,d(n){n&&g(t)}}}class m extends p{constructor(t){super(),c(this,t,null,w,h,{})}}export{m as C,f as a};
//# sourceMappingURL=Copy-5e5bdeb5.js.map
