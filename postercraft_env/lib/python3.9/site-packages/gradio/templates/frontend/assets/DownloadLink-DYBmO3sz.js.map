{"version": 3, "file": "DownloadLink-DYBmO3sz.js", "sources": ["../../../../js/icons/src/Download.svelte", "../../../../js/wasm/svelte/DownloadLink.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 32 32\"\n\t><path\n\t\tfill=\"currentColor\"\n\t\td=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"\n\t/></svg\n>\n", "<script lang=\"ts\">\n\timport type { HTMLAnchorAttributes } from \"svelte/elements\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\tinterface DownloadLinkAttributes\n\t\textends Omit<HTMLAnchorAttributes, \"target\"> {\n\t\tdownload: NonNullable<HTMLAnchorAttributes[\"download\"]>;\n\t}\n\ttype $$Props = DownloadLinkAttributes;\n\n\timport { getWorkerProxyContext } from \"./context\";\n\timport { should_proxy_wasm_src } from \"./file-url\";\n\timport { getHeaderValue } from \"../src/http\";\n\n\texport let href: DownloadLinkAttributes[\"href\"] = undefined;\n\texport let download: DownloadLinkAttributes[\"download\"];\n\n\tconst dispatch = createEventDispatcher();\n\n\tlet is_downloading = false;\n\tconst worker_proxy = getWorkerProxyContext();\n\tasync function wasm_click_handler(): Promise<void> {\n\t\tif (is_downloading) {\n\t\t\treturn;\n\t\t}\n\n\t\tdispatch(\"click\");\n\n\t\tif (href == null) {\n\t\t\tthrow new Error(\"href is not defined.\");\n\t\t}\n\t\tif (worker_proxy == null) {\n\t\t\tthrow new Error(\"Wasm worker proxy is not available.\");\n\t\t}\n\n\t\tconst url = new URL(href, window.location.href);\n\t\tconst path = url.pathname;\n\n\t\tis_downloading = true;\n\t\tworker_proxy\n\t\t\t.httpRequest({\n\t\t\t\tmethod: \"GET\",\n\t\t\t\tpath,\n\t\t\t\theaders: {},\n\t\t\t\tquery_string: \"\"\n\t\t\t})\n\t\t\t.then((response) => {\n\t\t\t\tif (response.status !== 200) {\n\t\t\t\t\tthrow new Error(`Failed to get file ${path} from the Wasm worker.`);\n\t\t\t\t}\n\t\t\t\tconst blob = new Blob([response.body], {\n\t\t\t\t\ttype: getHeaderValue(response.headers, \"content-type\")\n\t\t\t\t});\n\t\t\t\tconst blobUrl = URL.createObjectURL(blob);\n\n\t\t\t\tconst link = document.createElement(\"a\");\n\t\t\t\tlink.href = blobUrl;\n\t\t\t\tlink.download = download;\n\t\t\t\tlink.click();\n\n\t\t\t\tURL.revokeObjectURL(blobUrl);\n\t\t\t})\n\t\t\t.finally(() => {\n\t\t\t\tis_downloading = false;\n\t\t\t});\n\t}\n</script>\n\n{#if worker_proxy && should_proxy_wasm_src(href)}\n\t{#if is_downloading}\n\t\t<slot />\n\t{:else}\n\t\t<a {...$$restProps} {href} on:click|preventDefault={wasm_click_handler}>\n\t\t\t<slot />\n\t\t</a>\n\t{/if}\n{:else}\n\t<a\n\t\t{href}\n\t\ttarget={typeof window !== \"undefined\" && window.__is_colab__\n\t\t\t? \"_blank\"\n\t\t\t: null}\n\t\trel=\"noopener noreferrer\"\n\t\t{download}\n\t\t{...$$restProps}\n\t\ton:click={dispatch.bind(null, \"click\")}\n\t>\n\t\t<slot />\n\t</a>\n{/if}\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "a_target_value", "ctx", "a", "dispose", "listen", "should_proxy_wasm_src", "__awaiter", "thisArg", "_arguments", "P", "generator"], "mappings": "ijBAAAA,GASAC,EAAAC,EAAAC,CAAA,EAJEC,EAGCF,EAAAG,CAAA,goBCuEc,OAAAC,EAAA,OAAA,OAAW,KAAe,OAAO,aAC7C,SACA,kDAGCC,EAAW,CAAA,4FAPhBP,EAWGC,EAAAO,EAAAL,CAAA,0BAHQM,EAAAC,EAAAF,EAAA,QAAAD,EAAS,CAAA,EAAA,KAAK,KAAM,OAAO,CAAA,6LADjCA,EAAW,CAAA,wJAfXA,EAAc,CAAA,EAAA,gXAGXA,EAAW,CAAA,EAAA,CAAA,KAAAA,EAAA,CAAA,CAAA,CAAA,2FAAlBP,EAEGC,EAAAO,EAAAL,CAAA,2CAFiDI,EAAkB,CAAA,CAAA,CAAA,oGAA/DA,EAAW,CAAA,8bAJfA,EAAY,CAAA,GAAII,EAAsBJ,EAAI,CAAA,CAAA,0YApE9B,IAAAK,EAAA,MAAA,KAAA,WAAA,SAAAC,EAAAC,EAAAC,EAAAC,EAAA"}