import{S as h,e as b,s as v,a9 as z,m,g as r,N as u,h as E,j as C,ab as R,ac as k,ad as B,w as S,u as j,k as q,R as w}from"./index-c99b2410.js";import"./Button-9c502b18.js";function N(a){let e,o,s;const _=a[5].default,n=z(_,a,a[4],null);return{c(){e=m("div"),o=m("div"),n&&n.c(),r(o,"class","icon svelte-1oiin9d"),r(e,"class","empty svelte-1oiin9d"),r(e,"aria-label","Empty value"),u(e,"small",a[0]==="small"),u(e,"large",a[0]==="large"),u(e,"unpadded_box",a[1]),u(e,"small_parent",a[3])},m(t,i){E(t,e,i),C(e,o),n&&n.m(o,null),a[6](e),s=!0},p(t,[i]){n&&n.p&&(!s||i&16)&&R(n,_,t,t[4],s?B(_,t[4],i,null):k(t[4]),null),(!s||i&1)&&u(e,"small",t[0]==="small"),(!s||i&1)&&u(e,"large",t[0]==="large"),(!s||i&2)&&u(e,"unpadded_box",t[1]),(!s||i&8)&&u(e,"small_parent",t[3])},i(t){s||(S(n,t),s=!0)},o(t){j(n,t),s=!1},d(t){t&&q(e),n&&n.d(t),a[6](null)}}}function y(a,e,o){let s,{$$slots:_={},$$scope:n}=e,{size:t="small"}=e,{unpadded_box:i=!1}=e,d;function g(l){if(!l)return!1;const{height:f}=l.getBoundingClientRect(),{height:c}=l.parentElement?.getBoundingClientRect()||{height:f};return f>c+2}function p(l){w[l?"unshift":"push"](()=>{d=l,o(2,d)})}return a.$$set=l=>{"size"in l&&o(0,t=l.size),"unpadded_box"in l&&o(1,i=l.unpadded_box),"$$scope"in l&&o(4,n=l.$$scope)},a.$$.update=()=>{a.$$.dirty&4&&o(3,s=g(d))},[t,i,d,s,n,_,p]}class F extends h{constructor(e){super(),b(this,e,y,N,v,{size:0,unpadded_box:1})}}export{F as E};
//# sourceMappingURL=Empty-16e1c9d8.js.map
