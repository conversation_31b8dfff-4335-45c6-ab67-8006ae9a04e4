import{V as w}from"./Video-ClgvEI2i.js";import"./index-D5ROCp7B.js";import"./file-url-BIHPd7vS.js";import"./Index-D21IHG0c.js";import"./svelte/svelte.js";const{SvelteComponent:V,add_flush_callback:q,append:z,attr:A,bind:C,binding_callbacks:E,check_outros:I,create_component:N,destroy_component:S,detach:m,element:h,empty:k,flush:d,group_outros:j,init:B,insert:b,is_function:g,mount_component:D,noop:y,safe_not_equal:F,set_data:G,text:H,toggle_class:c,transition_in:_,transition_out:p}=window.__gradio__svelte__internal;function v(n){let e,t,l,o;const r=[K,J],a=[];function i(s,u){return 0}return e=i(),t=a[e]=r[e](n),{c(){t.c(),l=k()},m(s,u){a[e].m(s,u),b(s,l,u),o=!0},p(s,u){t.p(s,u)},i(s){o||(_(t),o=!0)},o(s){p(t),o=!1},d(s){s&&m(l),a[e].d(s)}}}function J(n){let e,t;return{c(){e=h("div"),t=H(n[2])},m(l,o){b(l,e,o),z(e,t)},p(l,o){o&4&&G(t,l[2])},i:y,o:y,d(l){l&&m(e)}}}function K(n){let e,t,l,o;function r(i){n[6](i)}let a={muted:!0,playsinline:!0,src:n[2]?.video.url,loop:n[3]};return n[4]!==void 0&&(a.node=n[4]),t=new w({props:a}),E.push(()=>C(t,"node",r)),t.$on("loadeddata",n[5]),t.$on("mouseover",function(){g(n[4].play.bind(n[4]))&&n[4].play.bind(n[4]).apply(this,arguments)}),t.$on("mouseout",function(){g(n[4].pause.bind(n[4]))&&n[4].pause.bind(n[4]).apply(this,arguments)}),{c(){e=h("div"),N(t.$$.fragment),A(e,"class","container svelte-1de9zxs"),c(e,"table",n[0]==="table"),c(e,"gallery",n[0]==="gallery"),c(e,"selected",n[1])},m(i,s){b(i,e,s),D(t,e,null),o=!0},p(i,s){n=i;const u={};s&4&&(u.src=n[2]?.video.url),s&8&&(u.loop=n[3]),!l&&s&16&&(l=!0,u.node=n[4],q(()=>l=!1)),t.$set(u),(!o||s&1)&&c(e,"table",n[0]==="table"),(!o||s&1)&&c(e,"gallery",n[0]==="gallery"),(!o||s&2)&&c(e,"selected",n[1])},i(i){o||(_(t.$$.fragment,i),o=!0)},o(i){p(t.$$.fragment,i),o=!1},d(i){i&&m(e),S(t)}}}function L(n){let e,t,l=n[2]&&v(n);return{c(){l&&l.c(),e=k()},m(o,r){l&&l.m(o,r),b(o,e,r),t=!0},p(o,[r]){o[2]?l?(l.p(o,r),r&4&&_(l,1)):(l=v(o),l.c(),_(l,1),l.m(e.parentNode,e)):l&&(j(),p(l,1,1,()=>{l=null}),I())},i(o){t||(_(l),t=!0)},o(o){p(l),t=!1},d(o){o&&m(e),l&&l.d(o)}}}function M(n,e,t){let{type:l}=e,{selected:o=!1}=e,{value:r}=e,{loop:a}=e,i;async function s(){t(4,i.muted=!0,i),t(4,i.playsInline=!0,i),t(4,i.controls=!1,i),i.setAttribute("muted",""),await i.play(),i.pause()}function u(f){i=f,t(4,i)}return n.$$set=f=>{"type"in f&&t(0,l=f.type),"selected"in f&&t(1,o=f.selected),"value"in f&&t(2,r=f.value),"loop"in f&&t(3,a=f.loop)},[l,o,r,a,i,s,u]}class U extends V{constructor(e){super(),B(this,e,M,L,F,{type:0,selected:1,value:2,loop:3})}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),d()}get selected(){return this.$$.ctx[1]}set selected(e){this.$$set({selected:e}),d()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),d()}get loop(){return this.$$.ctx[3]}set loop(e){this.$$set({loop:e}),d()}}export{U as default};
//# sourceMappingURL=Example-0pSwPtAl.js.map
