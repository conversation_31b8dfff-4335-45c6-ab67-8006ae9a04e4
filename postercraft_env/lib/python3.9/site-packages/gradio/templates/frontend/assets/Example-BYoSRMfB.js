import"./Index-D21IHG0c.js";import{I as m}from"./Image-DG8jX6JY.js";import"./index-D5ROCp7B.js";/* empty css                                                   */import"./ImageUploader-B6gmy4oE.js";/* empty css                                              */import"./file-url-BIHPd7vS.js";import"./svelte/svelte.js";import"./BlockLabel-BXXlQleC.js";import"./Image-Bsh8Umrh.js";import"./SelectSource-lBfDLlH8.js";import"./Upload-Cp8Go_XF.js";import"./utils-Gtzs_Zla.js";import"./DropdownArrow-BJ6rp2o2.js";import"./Button-uOcat6Z0.js";import"./Upload-46YxStuW.js";/* empty css                                                   */const{SvelteComponent:u,attr:c,create_component:f,destroy_component:p,detach:g,element:_,flush:a,init:d,insert:h,mount_component:v,safe_not_equal:y,toggle_class:o,transition_in:b,transition_out:w}=window.__gradio__svelte__internal;function $(s){let e,r,l;return r=new m({props:{src:s[0].composite?.url||s[0].background?.url,alt:""}}),{c(){e=_("div"),f(r.$$.fragment),c(e,"class","container svelte-jhlhb0"),o(e,"table",s[1]==="table"),o(e,"gallery",s[1]==="gallery"),o(e,"selected",s[2])},m(t,i){h(t,e,i),v(r,e,null),l=!0},p(t,[i]){const n={};i&1&&(n.src=t[0].composite?.url||t[0].background?.url),r.$set(n),(!l||i&2)&&o(e,"table",t[1]==="table"),(!l||i&2)&&o(e,"gallery",t[1]==="gallery"),(!l||i&4)&&o(e,"selected",t[2])},i(t){l||(b(r.$$.fragment,t),l=!0)},o(t){w(r.$$.fragment,t),l=!1},d(t){t&&g(e),p(r)}}}function k(s,e,r){let{value:l}=e,{type:t}=e,{selected:i=!1}=e;return s.$$set=n=>{"value"in n&&r(0,l=n.value),"type"in n&&r(1,t=n.type),"selected"in n&&r(2,i=n.selected)},[l,t,i]}class N extends u{constructor(e){super(),d(this,e,k,$,y,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),a()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),a()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),a()}}export{N as default};
//# sourceMappingURL=Example-BYoSRMfB.js.map
