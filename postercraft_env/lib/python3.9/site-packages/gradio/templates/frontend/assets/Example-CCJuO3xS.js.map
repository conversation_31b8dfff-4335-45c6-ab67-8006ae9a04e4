{"version": 3, "file": "Example-CCJuO3xS.js", "sources": ["../../../../js/video/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport Video from \"./shared/Video.svelte\";\n\timport { playable } from \"./shared/utils\";\n\timport { type FileData } from \"@gradio/client\";\n\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let value: { video: FileData; subtitles: FileData | null } | null;\n\tlet video: HTMLVideoElement;\n\n\tasync function init(): Promise<void> {\n\t\tvideo.muted = true;\n\t\tvideo.playsInline = true;\n\t\tvideo.controls = false;\n\t\tvideo.setAttribute(\"muted\", \"\");\n\n\t\tawait video.play();\n\t\tvideo.pause();\n\t}\n</script>\n\n{#if value}\n\t{#if playable()}\n\t\t<div\n\t\t\tclass=\"container\"\n\t\t\tclass:table={type === \"table\"}\n\t\t\tclass:gallery={type === \"gallery\"}\n\t\t\tclass:selected\n\t\t>\n\t\t\t<Video\n\t\t\t\tmuted\n\t\t\t\tplaysinline\n\t\t\t\tbind:node={video}\n\t\t\t\ton:loadeddata={init}\n\t\t\t\ton:mouseover={video.play.bind(video)}\n\t\t\t\ton:mouseout={video.pause.bind(video)}\n\t\t\t\tsrc={value?.video.url}\n\t\t\t/>\n\t\t</div>\n\t{:else}\n\t\t<div>{value}</div>\n\t{/if}\n{/if}\n\n<style>\n\t.container {\n\t\tflex: none;\n\t\tmax-width: none;\n\t}\n\t.container :global(video) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: cover;\n\t}\n\n\t.container:hover,\n\t.container.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\t.container.table {\n\t\tmargin: 0 auto;\n\t\tborder: 2px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-lg);\n\t\toverflow: hidden;\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n\n\t.container.gallery {\n\t\theight: var(--size-20);\n\t\tmax-height: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "is_function", "toggle_class", "create_if_block", "type", "$$props", "selected", "value", "video", "init"], "mappings": "okBAsBc,MAAA,kNAkBNA,EAAK,CAAA,CAAA,UAAXC,EAAiBC,EAAAC,EAAAC,CAAA,0BAAXJ,EAAK,CAAA,CAAA,6GAJJA,EAAK,CAAA,GAAE,MAAM,YAJPA,EAAK,CAAA,IAAA,gBAALA,EAAK,CAAA,mEACDA,EAAI,CAAA,CAAA,+BACLK,EAAAL,EAAM,CAAA,EAAA,KAAK,KAAKA,EAAhB,CAAA,CAAA,CAAA,GAAAA,EAAM,CAAA,EAAA,KAAK,KAAKA,EAAK,CAAA,CAAA,EAAA,MAAA,KAAA,SAAA,gCACtBK,EAAAL,EAAM,CAAA,EAAA,MAAM,KAAKA,EAAjB,CAAA,CAAA,CAAA,GAAAA,EAAM,CAAA,EAAA,MAAM,KAAKA,EAAK,CAAA,CAAA,EAAA,MAAA,KAAA,SAAA,6EAVvBM,EAAAH,EAAA,QAAAH,OAAS,OAAO,EACdM,EAAAH,EAAA,UAAAH,OAAS,SAAS,+BAHlCC,EAeKC,EAAAC,EAAAC,CAAA,sDAFEJ,EAAK,CAAA,GAAE,MAAM,2BAJPA,EAAK,CAAA,oCAPJM,EAAAH,EAAA,QAAAH,OAAS,OAAO,aACdM,EAAAH,EAAA,UAAAH,OAAS,SAAS,gJAL/BA,EAAK,CAAA,GAAAO,EAAAP,CAAA,wEAALA,EAAK,CAAA,6LAhBE,KAAAQ,CAAyB,EAAAC,EACzB,CAAA,SAAAC,EAAW,EAAK,EAAAD,GAChB,MAAAE,CAA6D,EAAAF,EACpEG,iBAEWC,GAAI,KAClBD,EAAM,MAAQ,GAAIA,CAAA,MAClBA,EAAM,YAAc,GAAIA,CAAA,MACxBA,EAAM,SAAW,GAAKA,CAAA,EACtBA,EAAM,aAAa,QAAS,EAAE,EAExB,MAAAA,EAAM,OACZA,EAAM,MAAK,gBAeEA,EAAKD"}