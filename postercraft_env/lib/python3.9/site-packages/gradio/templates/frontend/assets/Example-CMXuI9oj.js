const{SvelteComponent:z,append:A,attr:b,destroy_each:q,detach:_,element:h,empty:B,ensure_array_like:m,flush:g,init:D,insert:a,listen:k,noop:v,run_all:F,safe_not_equal:G,space:E,text:H,toggle_class:o}=window.__gradio__svelte__internal;function y(s,e,t){const l=s.slice();return l[9]=e[t],l[11]=t,l}function p(s,e,t){const l=s.slice();return l[12]=e[t],l[14]=t,l}function I(s){let e,t,l;function c(r,n){return typeof r[5]=="string"?K:J}let i=c(s)(s);return{c(){e=h("div"),i.c(),b(e,"class","svelte-1cib1xd"),o(e,"table",s[1]==="table"),o(e,"gallery",s[1]==="gallery"),o(e,"selected",s[2])},m(r,n){a(r,e,n),i.m(e,null),t||(l=[k(e,"mouseenter",s[7]),k(e,"mouseleave",s[8])],t=!0)},p(r,n){i.p(r,n),n&2&&o(e,"table",r[1]==="table"),n&2&&o(e,"gallery",r[1]==="gallery"),n&4&&o(e,"selected",r[2])},d(r){r&&_(e),i.d(),t=!1,F(l)}}}function J(s){let e,t,l=m(s[5].slice(0,3)),c=[];for(let i=0;i<l.length;i+=1)c[i]=C(y(s,l,i));let f=s[0].length>3&&w(s);return{c(){e=h("table");for(let i=0;i<c.length;i+=1)c[i].c();t=E(),f&&f.c(),b(e,"class"," svelte-1cib1xd")},m(i,r){a(i,e,r);for(let n=0;n<c.length;n+=1)c[n]&&c[n].m(e,null);A(e,t),f&&f.m(e,null)},p(i,r){if(r&32){l=m(i[5].slice(0,3));let n;for(n=0;n<l.length;n+=1){const d=y(i,l,n);c[n]?c[n].p(d,r):(c[n]=C(d),c[n].c(),c[n].m(e,t))}for(;n<c.length;n+=1)c[n].d(1);c.length=l.length}i[0].length>3?f?f.p(i,r):(f=w(i),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(i){i&&_(e),q(c,i),f&&f.d()}}}function K(s){let e;return{c(){e=H(s[5])},m(t,l){a(t,e,l)},p:v,d(t){t&&_(e)}}}function x(s){let e;return{c(){e=h("td"),e.textContent=`${s[12]}`,b(e,"class","svelte-1cib1xd")},m(t,l){a(t,e,l)},p:v,d(t){t&&_(e)}}}function L(s){let e;return{c(){e=h("td"),e.textContent="…",b(e,"class","svelte-1cib1xd")},m(t,l){a(t,e,l)},d(t){t&&_(e)}}}function C(s){let e,t,l=m(s[9].slice(0,3)),c=[];for(let i=0;i<l.length;i+=1)c[i]=x(p(s,l,i));let f=s[9].length>3&&L();return{c(){e=h("tr");for(let i=0;i<c.length;i+=1)c[i].c();t=E(),f&&f.c()},m(i,r){a(i,e,r);for(let n=0;n<c.length;n+=1)c[n]&&c[n].m(e,null);A(e,t),f&&f.m(e,null)},p(i,r){if(r&32){l=m(i[9].slice(0,3));let n;for(n=0;n<l.length;n+=1){const d=p(i,l,n);c[n]?c[n].p(d,r):(c[n]=x(d),c[n].c(),c[n].m(e,t))}for(;n<c.length;n+=1)c[n].d(1);c.length=l.length}},d(i){i&&_(e),q(c,i),f&&f.d()}}}function w(s){let e;return{c(){e=h("div"),b(e,"class","overlay svelte-1cib1xd"),o(e,"odd",s[3]%2!=0),o(e,"even",s[3]%2==0),o(e,"button",s[1]==="gallery")},m(t,l){a(t,e,l)},p(t,l){l&8&&o(e,"odd",t[3]%2!=0),l&8&&o(e,"even",t[3]%2==0),l&2&&o(e,"button",t[1]==="gallery")},d(t){t&&_(e)}}}function M(s){let e,t=s[6]&&I(s);return{c(){t&&t.c(),e=B()},m(l,c){t&&t.m(l,c),a(l,e,c)},p(l,[c]){l[6]&&t.p(l,c)},i:v,o:v,d(l){l&&_(e),t&&t.d(l)}}}function N(s,e,t){let{value:l}=e,{type:c}=e,{selected:f=!1}=e,{index:i}=e,r=!1,n=l,d=Array.isArray(n);const S=()=>t(4,r=!0),j=()=>t(4,r=!1);return s.$$set=u=>{"value"in u&&t(0,l=u.value),"type"in u&&t(1,c=u.type),"selected"in u&&t(2,f=u.selected),"index"in u&&t(3,i=u.index)},[l,c,f,i,r,n,d,S,j]}class O extends z{constructor(e){super(),D(this,e,N,M,G,{value:0,type:1,selected:2,index:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),g()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),g()}get index(){return this.$$.ctx[3]}set index(e){this.$$set({index:e}),g()}}export{O as default};
//# sourceMappingURL=Example-CMXuI9oj.js.map
