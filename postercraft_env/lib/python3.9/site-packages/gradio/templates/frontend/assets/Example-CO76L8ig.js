import"./ImageUploader-sLREcIL3.js";import"./Index-WGC0_FkS.js";import{I as m}from"./Image-BZaARumT.js";/* empty css                                              */import"./Blocks-aR9ucLZz.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";import"./Button-8nmImwVJ.js";import"./BlockLabel-CJsotHlk.js";import"./Empty-Vuj7-ssy.js";import"./ShareButton-Ds9bG3Tz.js";import"./DownloadLink-DYBmO3sz.js";import"./file-url-Bf0nK4ai.js";import"./Image-Bsh8Umrh.js";import"./SelectSource-ghC4bkgc.js";import"./Upload-Cp8Go_XF.js";import"./DropdownArrow-AhwBZaFV.js";import"./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js";const{SvelteComponent:u,attr:c,create_component:p,destroy_component:f,detach:g,element:_,flush:a,init:d,insert:h,mount_component:v,safe_not_equal:y,toggle_class:o,transition_in:b,transition_out:w}=window.__gradio__svelte__internal;function $(s){let e,r,l;return r=new m({props:{src:s[0].composite?.url||s[0].background?.url,alt:""}}),{c(){e=_("div"),p(r.$$.fragment),c(e,"class","container svelte-jhlhb0"),o(e,"table",s[1]==="table"),o(e,"gallery",s[1]==="gallery"),o(e,"selected",s[2])},m(t,i){h(t,e,i),v(r,e,null),l=!0},p(t,[i]){const n={};i&1&&(n.src=t[0].composite?.url||t[0].background?.url),r.$set(n),(!l||i&2)&&o(e,"table",t[1]==="table"),(!l||i&2)&&o(e,"gallery",t[1]==="gallery"),(!l||i&4)&&o(e,"selected",t[2])},i(t){l||(b(r.$$.fragment,t),l=!0)},o(t){w(r.$$.fragment,t),l=!1},d(t){t&&g(e),f(r)}}}function k(s,e,r){let{value:l}=e,{type:t}=e,{selected:i=!1}=e;return s.$$set=n=>{"value"in n&&r(0,l=n.value),"type"in n&&r(1,t=n.type),"selected"in n&&r(2,i=n.selected)},[l,t,i]}class O extends u{constructor(e){super(),d(this,e,k,$,y,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),a()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),a()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),a()}}export{O as default};
//# sourceMappingURL=Example-CO76L8ig.js.map
