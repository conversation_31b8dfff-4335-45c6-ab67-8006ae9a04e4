{"version": 3, "file": "Example-CO76L8ig.js", "sources": ["../../../../js/imageeditor/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { BaseImage as Image } from \"@gradio/image\";\n\timport type { EditorData } from \"./shared/InteractiveImageEditor.svelte\";\n\n\texport let value: EditorData;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass=\"container\"\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t<Image src={value.composite?.url || value.background?.url} alt=\"\" />\n</div>\n\n<style>\n\t.container :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.container.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\t.container.table {\n\t\tmargin: 0 auto;\n\t\tborder: 2px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-lg);\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n\n\t.container.gallery {\n\t\tborder: 2px solid var(--border-color-primary);\n\t\theight: var(--size-20);\n\t\tmax-height: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert", "target", "anchor", "value", "$$props", "type", "selected"], "mappings": "i5BAeaA,EAAK,CAAA,EAAC,WAAW,KAAOA,EAAK,CAAA,EAAC,YAAY,sFAJzCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAHlCG,EAOKC,EAAAF,EAAAG,CAAA,oDADQL,EAAK,CAAA,EAAC,WAAW,KAAOA,EAAK,CAAA,EAAC,YAAY,0BAJzCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,aACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,8IARtB,MAAAM,CAAiB,EAAAC,GACjB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF"}