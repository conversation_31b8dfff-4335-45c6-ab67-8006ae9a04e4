import{M as h}from"./Example.svelte_svelte_type_style_lang-qahm4PWk.js";import"./index-D5ROCp7B.js";import"./svelte/svelte.js";import"./prism-python-Bw3EYPE-.js";const{SvelteComponent:c,attr:g,create_component:d,destroy_component:b,detach:k,element:z,flush:_,init:w,insert:v,mount_component:y,safe_not_equal:x,toggle_class:m,transition_in:q,transition_out:C}=window.__gradio__svelte__internal;function M(n){let e,i,a;return i=new h({props:{message:n[0]?n[0]:"",latex_delimiters:n[5],sanitize_html:n[3],line_breaks:n[4],chatbot:!1,root:n[6]}}),{c(){e=z("div"),d(i.$$.fragment),g(e,"class","prose svelte-1ayixqk"),m(e,"table",n[1]==="table"),m(e,"gallery",n[1]==="gallery"),m(e,"selected",n[2])},m(t,s){v(t,e,s),y(i,e,null),a=!0},p(t,[s]){const r={};s&1&&(r.message=t[0]?t[0]:""),s&32&&(r.latex_delimiters=t[5]),s&8&&(r.sanitize_html=t[3]),s&16&&(r.line_breaks=t[4]),s&64&&(r.root=t[6]),i.$set(r),(!a||s&2)&&m(e,"table",t[1]==="table"),(!a||s&2)&&m(e,"gallery",t[1]==="gallery"),(!a||s&4)&&m(e,"selected",t[2])},i(t){a||(q(i.$$.fragment,t),a=!0)},o(t){C(i.$$.fragment,t),a=!1},d(t){t&&k(e),b(i)}}}function E(n,e,i){let{value:a}=e,{type:t}=e,{selected:s=!1}=e,{sanitize_html:r}=e,{line_breaks:o}=e,{latex_delimiters:f}=e,{root:u}=e;return n.$$set=l=>{"value"in l&&i(0,a=l.value),"type"in l&&i(1,t=l.type),"selected"in l&&i(2,s=l.selected),"sanitize_html"in l&&i(3,r=l.sanitize_html),"line_breaks"in l&&i(4,o=l.line_breaks),"latex_delimiters"in l&&i(5,f=l.latex_delimiters),"root"in l&&i(6,u=l.root)},[a,t,s,r,o,f,u]}class D extends c{constructor(e){super(),w(this,e,E,M,x,{value:0,type:1,selected:2,sanitize_html:3,line_breaks:4,latex_delimiters:5,root:6})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),_()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),_()}get sanitize_html(){return this.$$.ctx[3]}set sanitize_html(e){this.$$set({sanitize_html:e}),_()}get line_breaks(){return this.$$.ctx[4]}set line_breaks(e){this.$$set({line_breaks:e}),_()}get latex_delimiters(){return this.$$.ctx[5]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),_()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),_()}}export{D as default};
//# sourceMappingURL=Example-CoyKMtl3.js.map
