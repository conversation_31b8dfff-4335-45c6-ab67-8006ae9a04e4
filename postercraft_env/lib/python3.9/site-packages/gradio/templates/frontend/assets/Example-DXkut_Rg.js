import{I as m}from"./Image-DG8jX6JY.js";/* empty css                                              */import"./file-url-BIHPd7vS.js";import"./Index-D21IHG0c.js";import"./index-D5ROCp7B.js";import"./svelte/svelte.js";const{SvelteComponent:_,attr:g,check_outros:d,create_component:h,destroy_component:p,detach:v,element:b,flush:u,group_outros:y,init:k,insert:w,mount_component:$,safe_not_equal:I,toggle_class:s,transition_in:o,transition_out:c}=window.__gradio__svelte__internal;function f(i){let e,n;return e=new m({props:{src:i[0].url,alt:""}}),{c(){h(e.$$.fragment)},m(t,l){$(e,t,l),n=!0},p(t,l){const r={};l&1&&(r.src=t[0].url),e.$set(r)},i(t){n||(o(e.$$.fragment,t),n=!0)},o(t){c(e.$$.fragment,t),n=!1},d(t){p(e,t)}}}function q(i){let e,n,t=i[0]&&f(i);return{c(){e=b("div"),t&&t.c(),g(e,"class","container svelte-a9zvka"),s(e,"table",i[1]==="table"),s(e,"gallery",i[1]==="gallery"),s(e,"selected",i[2]),s(e,"border",i[0])},m(l,r){w(l,e,r),t&&t.m(e,null),n=!0},p(l,[r]){l[0]?t?(t.p(l,r),r&1&&o(t,1)):(t=f(l),t.c(),o(t,1),t.m(e,null)):t&&(y(),c(t,1,1,()=>{t=null}),d()),(!n||r&2)&&s(e,"table",l[1]==="table"),(!n||r&2)&&s(e,"gallery",l[1]==="gallery"),(!n||r&4)&&s(e,"selected",l[2]),(!n||r&1)&&s(e,"border",l[0])},i(l){n||(o(t),n=!0)},o(l){c(t),n=!1},d(l){l&&v(e),t&&t.d()}}}function z(i,e,n){let{value:t}=e,{type:l}=e,{selected:r=!1}=e;return i.$$set=a=>{"value"in a&&n(0,t=a.value),"type"in a&&n(1,l=a.type),"selected"in a&&n(2,r=a.selected)},[t,l,r]}class D extends _{constructor(e){super(),k(this,e,z,q,I,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),u()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),u()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),u()}}export{D as default};
//# sourceMappingURL=Example-DXkut_Rg.js.map
