import{I as N}from"./Image-BZaARumT.js";import"./file-url-Bf0nK4ai.js";import"./Index-WGC0_FkS.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";const{SvelteComponent:P,add_iframe_resize_listener:W,add_render_callback:j,append:g,attr:A,binding_callbacks:D,check_outros:B,create_component:F,destroy_component:G,destroy_each:H,detach:k,element:w,empty:J,ensure_array_like:z,flush:b,group_outros:C,init:K,insert:y,mount_component:L,noop:I,safe_not_equal:O,set_data:E,space:Q,text:M,toggle_class:p,transition_in:h,transition_out:d}=window.__gradio__svelte__internal,{onMount:R}=window.__gradio__svelte__internal;function S(s,e,i){const n=s.slice();return n[8]=e[i],n}function T(s){let e=s[8].path+"",i;return{c(){i=M(e)},m(n,o){y(n,i,o)},p(n,o){o&1&&e!==(e=n[8].path+"")&&E(i,e)},i:I,o:I,d(n){n&&k(i)}}}function U(s){let e,i;return e=new N({props:{src:s[8].url,alt:""}}),{c(){F(e.$$.fragment)},m(n,o){L(e,n,o),i=!0},p(n,o){const u={};o&1&&(u.src=n[8].url),e.$set(u)},i(n){i||(h(e.$$.fragment,n),i=!0)},o(n){d(e.$$.fragment,n),i=!1},d(n){G(e,n)}}}function q(s){let e,i,n,o,u;const m=[U,T],r=[];function _(l,f){return f&1&&(e=null),e==null&&(e=!!(l[8].mime_type&&l[8].mime_type.includes("image"))),e?0:1}return i=_(s,-1),n=r[i]=m[i](s),{c(){n.c(),o=J()},m(l,f){r[i].m(l,f),y(l,o,f),u=!0},p(l,f){let t=i;i=_(l,f),i===t?r[i].p(l,f):(C(),d(r[t],1,1,()=>{r[t]=null}),B(),n=r[i],n?n.p(l,f):(n=r[i]=m[i](l),n.c()),h(n,1),n.m(o.parentNode,o))},i(l){u||(h(n),u=!0)},o(l){d(n),u=!1},d(l){l&&k(o),r[i].d(l)}}}function V(s){let e,i,n=(s[0].text?s[0].text:"")+"",o,u,m,r,_=z(s[0].files),l=[];for(let t=0;t<_.length;t+=1)l[t]=q(S(s,_,t));const f=t=>d(l[t],1,1,()=>{l[t]=null});return{c(){e=w("div"),i=w("p"),o=M(n),u=Q();for(let t=0;t<l.length;t+=1)l[t].c();A(e,"class","svelte-ou7fr2"),j(()=>s[5].call(e)),p(e,"table",s[1]==="table"),p(e,"gallery",s[1]==="gallery"),p(e,"selected",s[2])},m(t,c){y(t,e,c),g(e,i),g(i,o),g(e,u);for(let a=0;a<l.length;a+=1)l[a]&&l[a].m(e,null);m=W(e,s[5].bind(e)),s[6](e),r=!0},p(t,[c]){if((!r||c&1)&&n!==(n=(t[0].text?t[0].text:"")+"")&&E(o,n),c&1){_=z(t[0].files);let a;for(a=0;a<_.length;a+=1){const v=S(t,_,a);l[a]?(l[a].p(v,c),h(l[a],1)):(l[a]=q(v),l[a].c(),h(l[a],1),l[a].m(e,null))}for(C(),a=_.length;a<l.length;a+=1)f(a);B()}(!r||c&2)&&p(e,"table",t[1]==="table"),(!r||c&2)&&p(e,"gallery",t[1]==="gallery"),(!r||c&4)&&p(e,"selected",t[2])},i(t){if(!r){for(let c=0;c<_.length;c+=1)h(l[c]);r=!0}},o(t){l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)d(l[c]);r=!1},d(t){t&&k(e),H(l,t),m(),s[6](null)}}}function X(s,e,i){let{value:n={text:"",files:[]}}=e,{type:o}=e,{selected:u=!1}=e,m,r;function _(t,c){!t||!c||(r.style.setProperty("--local-text-width",`${c<150?c:200}px`),i(4,r.style.whiteSpace="unset",r))}R(()=>{_(r,m)});function l(){m=this.clientWidth,i(3,m)}function f(t){D[t?"unshift":"push"](()=>{r=t,i(4,r)})}return s.$$set=t=>{"value"in t&&i(0,n=t.value),"type"in t&&i(1,o=t.type),"selected"in t&&i(2,u=t.selected)},[n,o,u,m,r,l,f]}class te extends P{constructor(e){super(),K(this,e,X,V,O,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),b()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),b()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),b()}}export{te as default};
//# sourceMappingURL=Example-aHy38Zn4.js.map
