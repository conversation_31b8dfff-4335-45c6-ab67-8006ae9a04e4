import{M as h}from"./Example.svelte_svelte_type_style_lang-BBpfzd83.js";import"./prism-python-DQB1-hGx.js";import"./Index-WGC0_FkS.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";const{SvelteComponent:o,attr:c,create_component:d,destroy_component:g,detach:b,element:k,flush:_,init:z,insert:v,mount_component:w,safe_not_equal:y,toggle_class:m,transition_in:x,transition_out:q}=window.__gradio__svelte__internal;function C(n){let e,i,a;return i=new h({props:{message:n[0]?n[0]:"",latex_delimiters:n[5],sanitize_html:n[3],line_breaks:n[4],chatbot:!1}}),{c(){e=k("div"),d(i.$$.fragment),c(e,"class","prose svelte-1ayixqk"),m(e,"table",n[1]==="table"),m(e,"gallery",n[1]==="gallery"),m(e,"selected",n[2])},m(t,l){v(t,e,l),w(i,e,null),a=!0},p(t,[l]){const r={};l&1&&(r.message=t[0]?t[0]:""),l&32&&(r.latex_delimiters=t[5]),l&8&&(r.sanitize_html=t[3]),l&16&&(r.line_breaks=t[4]),i.$set(r),(!a||l&2)&&m(e,"table",t[1]==="table"),(!a||l&2)&&m(e,"gallery",t[1]==="gallery"),(!a||l&4)&&m(e,"selected",t[2])},i(t){a||(x(i.$$.fragment,t),a=!0)},o(t){q(i.$$.fragment,t),a=!1},d(t){t&&b(e),g(i)}}}function M(n,e,i){let{value:a}=e,{type:t}=e,{selected:l=!1}=e,{sanitize_html:r}=e,{line_breaks:f}=e,{latex_delimiters:u}=e;return n.$$set=s=>{"value"in s&&i(0,a=s.value),"type"in s&&i(1,t=s.type),"selected"in s&&i(2,l=s.selected),"sanitize_html"in s&&i(3,r=s.sanitize_html),"line_breaks"in s&&i(4,f=s.line_breaks),"latex_delimiters"in s&&i(5,u=s.latex_delimiters)},[a,t,l,r,f,u]}class D extends o{constructor(e){super(),z(this,e,M,C,y,{value:0,type:1,selected:2,sanitize_html:3,line_breaks:4,latex_delimiters:5})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),_()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),_()}get sanitize_html(){return this.$$.ctx[3]}set sanitize_html(e){this.$$set({sanitize_html:e}),_()}get line_breaks(){return this.$$.ctx[4]}set line_breaks(e){this.$$set({line_breaks:e}),_()}get latex_delimiters(){return this.$$.ctx[5]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),_()}}export{D as default};
//# sourceMappingURL=Example-mBWp17nR.js.map
