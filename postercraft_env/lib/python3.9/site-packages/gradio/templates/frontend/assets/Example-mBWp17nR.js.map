{"version": 3, "file": "Example-mBWp17nR.js", "sources": ["../../../../js/markdown/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport MarkdownCode from \"./shared/MarkdownCode.svelte\";\n\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let sanitize_html: boolean;\n\texport let line_breaks: boolean;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass=\"prose\"\n>\n\t<MarkdownCode\n\t\tmessage={value ? value : \"\"}\n\t\t{latex_delimiters}\n\t\t{sanitize_html}\n\t\t{line_breaks}\n\t\tchatbot={false}\n\t/>\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert", "target", "anchor", "value", "$$props", "type", "selected", "sanitize_html", "line_breaks", "latex_delimiters"], "mappings": "4dAsBWA,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,qEAIhB,2EAVGC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAaKC,EAAAF,EAAAG,CAAA,wDANML,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,gHANbC,EAAAC,EAAA,QAAAF,OAAS,OAAO,aACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,8IAdtB,MAAAM,CAAoB,EAAAC,GACpB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,GAChB,cAAAG,CAAsB,EAAAH,GACtB,YAAAI,CAAoB,EAAAJ,GACpB,iBAAAK,CAIR,EAAAL"}