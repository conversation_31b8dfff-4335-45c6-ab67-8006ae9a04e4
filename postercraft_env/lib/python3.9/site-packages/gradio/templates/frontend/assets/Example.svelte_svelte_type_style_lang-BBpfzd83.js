import{p as nr}from"./prism-python-DQB1-hGx.js";/*! @license DOMPurify 3.0.3 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE */const{entries:Cn,setPrototypeOf:Fa,isFrozen:Qi,getPrototypeOf:Ji,getOwnPropertyDescriptor:es}=Object;let{freeze:De,seal:Ue,create:ts}=Object,{apply:Sr,construct:Fr}=typeof Reflect<"u"&&Reflect;Sr||(Sr=function(e,t,r){return e.apply(t,r)});De||(De=function(e){return e});Ue||(Ue=function(e){return e});Fr||(Fr=function(e,t){return new e(...t)});const rs=Ie(Array.prototype.forEach),Ea=Ie(Array.prototype.pop),Q0=Ie(Array.prototype.push),Mt=Ie(String.prototype.toLowerCase),ir=Ie(String.prototype.toString),as=Ie(String.prototype.match),He=Ie(String.prototype.replace),ns=Ie(String.prototype.indexOf),is=Ie(String.prototype.trim),Ee=Ie(RegExp.prototype.test),J0=ss(TypeError);function Ie(a){return function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return Sr(a,e,r)}}function ss(a){return function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Fr(a,t)}}function V(a,e,t){var r;t=(r=t)!==null&&r!==void 0?r:Mt,Fa&&Fa(a,null);let n=e.length;for(;n--;){let i=e[n];if(typeof i=="string"){const s=t(i);s!==i&&(Qi(e)||(e[n]=s),i=s)}a[i]=!0}return a}function $0(a){const e=ts(null);for(const[t,r]of Cn(a))e[t]=r;return e}function vt(a,e){for(;a!==null;){const r=es(a,e);if(r){if(r.get)return Ie(r.get);if(typeof r.value=="function")return Ie(r.value)}a=Ji(a)}function t(r){return console.warn("fallback value for",r),null}return t}const Ta=De(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),sr=De(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),lr=De(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),ls=De(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),ur=De(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),us=De(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Ca=De(["#text"]),Ma=De(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),or=De(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),za=De(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),gt=De(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),os=Ue(/\{\{[\w\W]*|[\w\W]*\}\}/gm),hs=Ue(/<%[\w\W]*|[\w\W]*%>/gm),cs=Ue(/\${[\w\W]*}/gm),ms=Ue(/^data-[\-\w.\u00B7-\uFFFF]/),ds=Ue(/^aria-[\-\w]+$/),Mn=Ue(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),ps=Ue(/^(?:\w+script|data):/i),fs=Ue(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),zn=Ue(/^html$/i);var Ba=Object.freeze({__proto__:null,MUSTACHE_EXPR:os,ERB_EXPR:hs,TMPLIT_EXPR:cs,DATA_ATTR:ms,ARIA_ATTR:ds,IS_ALLOWED_URI:Mn,IS_SCRIPT_OR_DATA:ps,ATTR_WHITESPACE:fs,DOCTYPE_NAME:zn});const vs=()=>typeof window>"u"?null:window,gs=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let r=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(r=t.getAttribute(n));const i="dompurify"+(r?"#"+r:"");try{return e.createPolicy(i,{createHTML(s){return s},createScriptURL(s){return s}})}catch{return console.warn("TrustedTypes policy "+i+" could not be created."),null}};function Bn(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:vs();const e=O=>Bn(O);if(e.version="3.0.3",e.removed=[],!a||!a.document||a.document.nodeType!==9)return e.isSupported=!1,e;const t=a.document,r=t.currentScript;let{document:n}=a;const{DocumentFragment:i,HTMLTemplateElement:s,Node:o,Element:h,NodeFilter:d,NamedNodeMap:p=a.NamedNodeMap||a.MozNamedAttrMap,HTMLFormElement:g,DOMParser:x,trustedTypes:w}=a,S=h.prototype,E=vt(S,"cloneNode"),z=vt(S,"nextSibling"),k=vt(S,"childNodes"),L=vt(S,"parentNode");if(typeof s=="function"){const O=n.createElement("template");O.content&&O.content.ownerDocument&&(n=O.content.ownerDocument)}let I,G="";const{implementation:$,createNodeIterator:X,createDocumentFragment:W,getElementsByTagName:ae}=n,{importNode:Z}=t;let me={};e.isSupported=typeof Cn=="function"&&typeof L=="function"&&$&&$.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:pe,ERB_EXPR:ie,TMPLIT_EXPR:_e,DATA_ATTR:be,ARIA_ATTR:Fe,IS_SCRIPT_OR_DATA:Ge,ATTR_WHITESPACE:r0}=Ba;let{IS_ALLOWED_URI:a0}=Ba,oe=null;const qe=V({},[...Ta,...sr,...lr,...ur,...Ca]);let J=null;const ye=V({},[...Ma,...or,...za,...gt]);let ee=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),n0=null,Ve=null,X0=!0,We=!0,i0=!1,A0=!0,Pe=!1,s0=!1,Z0=!1,j0=!1,Ye=!1,S0=!1,F0=!1,O0=!0,E0=!1;const L0="user-content-";let jt=!0,K0=!1,_0={},q0=null;const ma=V({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let da=null;const pa=V({},["audio","video","img","source","image","track"]);let Kt=null;const fa=V({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),mt="http://www.w3.org/1998/Math/MathML",dt="http://www.w3.org/2000/svg",l0="http://www.w3.org/1999/xhtml";let P0=l0,Qt=!1,Jt=null;const Gi=V({},[mt,dt,l0],ir);let T0;const Vi=["application/xhtml+xml","text/html"],Wi="text/html";let ve,H0=null;const Yi=n.createElement("form"),va=function(y){return y instanceof RegExp||y instanceof Function},er=function(y){if(!(H0&&H0===y)){if((!y||typeof y!="object")&&(y={}),y=$0(y),T0=Vi.indexOf(y.PARSER_MEDIA_TYPE)===-1?T0=Wi:T0=y.PARSER_MEDIA_TYPE,ve=T0==="application/xhtml+xml"?ir:Mt,oe="ALLOWED_TAGS"in y?V({},y.ALLOWED_TAGS,ve):qe,J="ALLOWED_ATTR"in y?V({},y.ALLOWED_ATTR,ve):ye,Jt="ALLOWED_NAMESPACES"in y?V({},y.ALLOWED_NAMESPACES,ir):Gi,Kt="ADD_URI_SAFE_ATTR"in y?V($0(fa),y.ADD_URI_SAFE_ATTR,ve):fa,da="ADD_DATA_URI_TAGS"in y?V($0(pa),y.ADD_DATA_URI_TAGS,ve):pa,q0="FORBID_CONTENTS"in y?V({},y.FORBID_CONTENTS,ve):ma,n0="FORBID_TAGS"in y?V({},y.FORBID_TAGS,ve):{},Ve="FORBID_ATTR"in y?V({},y.FORBID_ATTR,ve):{},_0="USE_PROFILES"in y?y.USE_PROFILES:!1,X0=y.ALLOW_ARIA_ATTR!==!1,We=y.ALLOW_DATA_ATTR!==!1,i0=y.ALLOW_UNKNOWN_PROTOCOLS||!1,A0=y.ALLOW_SELF_CLOSE_IN_ATTR!==!1,Pe=y.SAFE_FOR_TEMPLATES||!1,s0=y.WHOLE_DOCUMENT||!1,Ye=y.RETURN_DOM||!1,S0=y.RETURN_DOM_FRAGMENT||!1,F0=y.RETURN_TRUSTED_TYPE||!1,j0=y.FORCE_BODY||!1,O0=y.SANITIZE_DOM!==!1,E0=y.SANITIZE_NAMED_PROPS||!1,jt=y.KEEP_CONTENT!==!1,K0=y.IN_PLACE||!1,a0=y.ALLOWED_URI_REGEXP||Mn,P0=y.NAMESPACE||l0,ee=y.CUSTOM_ELEMENT_HANDLING||{},y.CUSTOM_ELEMENT_HANDLING&&va(y.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(ee.tagNameCheck=y.CUSTOM_ELEMENT_HANDLING.tagNameCheck),y.CUSTOM_ELEMENT_HANDLING&&va(y.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(ee.attributeNameCheck=y.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),y.CUSTOM_ELEMENT_HANDLING&&typeof y.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(ee.allowCustomizedBuiltInElements=y.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Pe&&(We=!1),S0&&(Ye=!0),_0&&(oe=V({},[...Ca]),J=[],_0.html===!0&&(V(oe,Ta),V(J,Ma)),_0.svg===!0&&(V(oe,sr),V(J,or),V(J,gt)),_0.svgFilters===!0&&(V(oe,lr),V(J,or),V(J,gt)),_0.mathMl===!0&&(V(oe,ur),V(J,za),V(J,gt))),y.ADD_TAGS&&(oe===qe&&(oe=$0(oe)),V(oe,y.ADD_TAGS,ve)),y.ADD_ATTR&&(J===ye&&(J=$0(J)),V(J,y.ADD_ATTR,ve)),y.ADD_URI_SAFE_ATTR&&V(Kt,y.ADD_URI_SAFE_ATTR,ve),y.FORBID_CONTENTS&&(q0===ma&&(q0=$0(q0)),V(q0,y.FORBID_CONTENTS,ve)),jt&&(oe["#text"]=!0),s0&&V(oe,["html","head","body"]),oe.table&&(V(oe,["tbody"]),delete n0.tbody),y.TRUSTED_TYPES_POLICY){if(typeof y.TRUSTED_TYPES_POLICY.createHTML!="function")throw J0('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof y.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw J0('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');I=y.TRUSTED_TYPES_POLICY,G=I.createHTML("")}else I===void 0&&(I=gs(w,r)),I!==null&&typeof G=="string"&&(G=I.createHTML(""));De&&De(y),H0=y}},ga=V({},["mi","mo","mn","ms","mtext"]),ba=V({},["foreignobject","desc","title","annotation-xml"]),Xi=V({},["title","style","font","a","script"]),pt=V({},sr);V(pt,lr),V(pt,ls);const tr=V({},ur);V(tr,us);const Zi=function(y){let C=L(y);(!C||!C.tagName)&&(C={namespaceURI:P0,tagName:"template"});const R=Mt(y.tagName),j=Mt(C.tagName);return Jt[y.namespaceURI]?y.namespaceURI===dt?C.namespaceURI===l0?R==="svg":C.namespaceURI===mt?R==="svg"&&(j==="annotation-xml"||ga[j]):!!pt[R]:y.namespaceURI===mt?C.namespaceURI===l0?R==="math":C.namespaceURI===dt?R==="math"&&ba[j]:!!tr[R]:y.namespaceURI===l0?C.namespaceURI===dt&&!ba[j]||C.namespaceURI===mt&&!ga[j]?!1:!tr[R]&&(Xi[R]||!pt[R]):!!(T0==="application/xhtml+xml"&&Jt[y.namespaceURI]):!1},C0=function(y){Q0(e.removed,{element:y});try{y.parentNode.removeChild(y)}catch{y.remove()}},rr=function(y,C){try{Q0(e.removed,{attribute:C.getAttributeNode(y),from:C})}catch{Q0(e.removed,{attribute:null,from:C})}if(C.removeAttribute(y),y==="is"&&!J[y])if(Ye||S0)try{C0(C)}catch{}else try{C.setAttribute(y,"")}catch{}},ya=function(y){let C,R;if(j0)y="<remove></remove>"+y;else{const ze=as(y,/^[\r\n\t ]+/);R=ze&&ze[0]}T0==="application/xhtml+xml"&&P0===l0&&(y='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+y+"</body></html>");const j=I?I.createHTML(y):y;if(P0===l0)try{C=new x().parseFromString(j,T0)}catch{}if(!C||!C.documentElement){C=$.createDocument(P0,"template",null);try{C.documentElement.innerHTML=Qt?G:j}catch{}}const ge=C.body||C.documentElement;return y&&R&&ge.insertBefore(n.createTextNode(R),ge.childNodes[0]||null),P0===l0?ae.call(C,s0?"html":"body")[0]:s0?C.documentElement:ge},xa=function(y){return X.call(y.ownerDocument||y,y,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT,null,!1)},ji=function(y){return y instanceof g&&(typeof y.nodeName!="string"||typeof y.textContent!="string"||typeof y.removeChild!="function"||!(y.attributes instanceof p)||typeof y.removeAttribute!="function"||typeof y.setAttribute!="function"||typeof y.namespaceURI!="string"||typeof y.insertBefore!="function"||typeof y.hasChildNodes!="function")},ft=function(y){return typeof o=="object"?y instanceof o:y&&typeof y=="object"&&typeof y.nodeType=="number"&&typeof y.nodeName=="string"},u0=function(y,C,R){me[y]&&rs(me[y],j=>{j.call(e,C,R,H0)})},wa=function(y){let C;if(u0("beforeSanitizeElements",y,null),ji(y))return C0(y),!0;const R=ve(y.nodeName);if(u0("uponSanitizeElement",y,{tagName:R,allowedTags:oe}),y.hasChildNodes()&&!ft(y.firstElementChild)&&(!ft(y.content)||!ft(y.content.firstElementChild))&&Ee(/<[/\w]/g,y.innerHTML)&&Ee(/<[/\w]/g,y.textContent))return C0(y),!0;if(!oe[R]||n0[R]){if(!n0[R]&&ka(R)&&(ee.tagNameCheck instanceof RegExp&&Ee(ee.tagNameCheck,R)||ee.tagNameCheck instanceof Function&&ee.tagNameCheck(R)))return!1;if(jt&&!q0[R]){const j=L(y)||y.parentNode,ge=k(y)||y.childNodes;if(ge&&j){const ze=ge.length;for(let se=ze-1;se>=0;--se)j.insertBefore(E(ge[se],!0),z(y))}}return C0(y),!0}return y instanceof h&&!Zi(y)||(R==="noscript"||R==="noembed")&&Ee(/<\/no(script|embed)/i,y.innerHTML)?(C0(y),!0):(Pe&&y.nodeType===3&&(C=y.textContent,C=He(C,pe," "),C=He(C,ie," "),C=He(C,_e," "),y.textContent!==C&&(Q0(e.removed,{element:y.cloneNode()}),y.textContent=C)),u0("afterSanitizeElements",y,null),!1)},Da=function(y,C,R){if(O0&&(C==="id"||C==="name")&&(R in n||R in Yi))return!1;if(!(We&&!Ve[C]&&Ee(be,C))){if(!(X0&&Ee(Fe,C))){if(!J[C]||Ve[C]){if(!(ka(y)&&(ee.tagNameCheck instanceof RegExp&&Ee(ee.tagNameCheck,y)||ee.tagNameCheck instanceof Function&&ee.tagNameCheck(y))&&(ee.attributeNameCheck instanceof RegExp&&Ee(ee.attributeNameCheck,C)||ee.attributeNameCheck instanceof Function&&ee.attributeNameCheck(C))||C==="is"&&ee.allowCustomizedBuiltInElements&&(ee.tagNameCheck instanceof RegExp&&Ee(ee.tagNameCheck,R)||ee.tagNameCheck instanceof Function&&ee.tagNameCheck(R))))return!1}else if(!Kt[C]){if(!Ee(a0,He(R,r0,""))){if(!((C==="src"||C==="xlink:href"||C==="href")&&y!=="script"&&ns(R,"data:")===0&&da[y])){if(!(i0&&!Ee(Ge,He(R,r0,"")))){if(R)return!1}}}}}}return!0},ka=function(y){return y.indexOf("-")>0},Aa=function(y){let C,R,j,ge;u0("beforeSanitizeAttributes",y,null);const{attributes:ze}=y;if(!ze)return;const se={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:J};for(ge=ze.length;ge--;){C=ze[ge];const{name:Xe,namespaceURI:ar}=C;if(R=Xe==="value"?C.value:is(C.value),j=ve(Xe),se.attrName=j,se.attrValue=R,se.keepAttr=!0,se.forceKeepAttr=void 0,u0("uponSanitizeAttribute",y,se),R=se.attrValue,se.forceKeepAttr||(rr(Xe,y),!se.keepAttr))continue;if(!A0&&Ee(/\/>/i,R)){rr(Xe,y);continue}Pe&&(R=He(R,pe," "),R=He(R,ie," "),R=He(R,_e," "));const Sa=ve(y.nodeName);if(Da(Sa,j,R)){if(E0&&(j==="id"||j==="name")&&(rr(Xe,y),R=L0+R),I&&typeof w=="object"&&typeof w.getAttributeType=="function"&&!ar)switch(w.getAttributeType(Sa,j)){case"TrustedHTML":{R=I.createHTML(R);break}case"TrustedScriptURL":{R=I.createScriptURL(R);break}}try{ar?y.setAttributeNS(ar,Xe,R):y.setAttribute(Xe,R),Ea(e.removed)}catch{}}}u0("afterSanitizeAttributes",y,null)},Ki=function O(y){let C;const R=xa(y);for(u0("beforeSanitizeShadowDOM",y,null);C=R.nextNode();)u0("uponSanitizeShadowNode",C,null),!wa(C)&&(C.content instanceof i&&O(C.content),Aa(C));u0("afterSanitizeShadowDOM",y,null)};return e.sanitize=function(O){let y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C,R,j,ge;if(Qt=!O,Qt&&(O="<!-->"),typeof O!="string"&&!ft(O))if(typeof O.toString=="function"){if(O=O.toString(),typeof O!="string")throw J0("dirty is not a string, aborting")}else throw J0("toString is not a function");if(!e.isSupported)return O;if(Z0||er(y),e.removed=[],typeof O=="string"&&(K0=!1),K0){if(O.nodeName){const Xe=ve(O.nodeName);if(!oe[Xe]||n0[Xe])throw J0("root node is forbidden and cannot be sanitized in-place")}}else if(O instanceof o)C=ya("<!---->"),R=C.ownerDocument.importNode(O,!0),R.nodeType===1&&R.nodeName==="BODY"||R.nodeName==="HTML"?C=R:C.appendChild(R);else{if(!Ye&&!Pe&&!s0&&O.indexOf("<")===-1)return I&&F0?I.createHTML(O):O;if(C=ya(O),!C)return Ye?null:F0?G:""}C&&j0&&C0(C.firstChild);const ze=xa(K0?O:C);for(;j=ze.nextNode();)wa(j)||(j.content instanceof i&&Ki(j.content),Aa(j));if(K0)return O;if(Ye){if(S0)for(ge=W.call(C.ownerDocument);C.firstChild;)ge.appendChild(C.firstChild);else ge=C;return(J.shadowroot||J.shadowrootmod)&&(ge=Z.call(t,ge,!0)),ge}let se=s0?C.outerHTML:C.innerHTML;return s0&&oe["!doctype"]&&C.ownerDocument&&C.ownerDocument.doctype&&C.ownerDocument.doctype.name&&Ee(zn,C.ownerDocument.doctype.name)&&(se="<!DOCTYPE "+C.ownerDocument.doctype.name+`>
`+se),Pe&&(se=He(se,pe," "),se=He(se,ie," "),se=He(se,_e," ")),I&&F0?I.createHTML(se):se},e.setConfig=function(O){er(O),Z0=!0},e.clearConfig=function(){H0=null,Z0=!1},e.isValidAttribute=function(O,y,C){H0||er({});const R=ve(O),j=ve(y);return Da(R,j,C)},e.addHook=function(O,y){typeof y=="function"&&(me[O]=me[O]||[],Q0(me[O],y))},e.removeHook=function(O){if(me[O])return Ea(me[O])},e.removeHooks=function(O){me[O]&&(me[O]=[])},e.removeAllHooks=function(){me={}},e}var Ra=Bn();class Te{constructor(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}static range(e,t){return t?!e||!e.loc||!t.loc||e.loc.lexer!==t.loc.lexer?null:new Te(e.loc.lexer,e.loc.start,t.loc.end):e&&e.loc}}class Ne{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new Ne(t,Te.range(this,e))}}class T{constructor(e,t){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;var r="KaTeX parse error: "+e,n,i,s=t&&t.loc;if(s&&s.start<=s.end){var o=s.lexer.input;n=s.start,i=s.end,n===o.length?r+=" at end of input: ":r+=" at position "+(n+1)+": ";var h=o.slice(n,i).replace(/[^]/g,"$&̲"),d;n>15?d="…"+o.slice(n-15,n):d=o.slice(0,n);var p;i+15<o.length?p=o.slice(i,i+15)+"…":p=o.slice(i),r+=d+h+p}var g=new Error(r);return g.name="ParseError",g.__proto__=T.prototype,g.position=n,n!=null&&i!=null&&(g.length=i-n),g.rawMessage=e,g}}T.prototype.__proto__=Error.prototype;var bs=function(e,t){return e.indexOf(t)!==-1},ys=function(e,t){return e===void 0?t:e},xs=/([A-Z])/g,ws=function(e){return e.replace(xs,"-$1").toLowerCase()},Ds={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},ks=/[&><"']/g;function As(a){return String(a).replace(ks,e=>Ds[e])}var Rn=function a(e){return e.type==="ordgroup"||e.type==="color"?e.body.length===1?a(e.body[0]):e:e.type==="font"?a(e.body):e},Ss=function(e){var t=Rn(e);return t.type==="mathord"||t.type==="textord"||t.type==="atom"},Fs=function(e){if(!e)throw new Error("Expected non-null, but got "+String(e));return e},Es=function(e){var t=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(e);return t?t[2]!==":"||!/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(t[1])?null:t[1].toLowerCase():"_relative"},_={contains:bs,deflt:ys,escape:As,hyphenate:ws,getBaseElem:Rn,isCharacterBox:Ss,protocolFromUrl:Es},zt={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:a=>"#"+a},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(a,e)=>(e.push(a),e)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:a=>Math.max(0,a),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:a=>Math.max(0,a),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:a=>Math.max(0,a),cli:"-e, --max-expand <n>",cliProcessor:a=>a==="Infinity"?1/0:parseInt(a)},globalGroup:{type:"boolean",cli:!1}};function Ts(a){if(a.default)return a.default;var e=a.type,t=Array.isArray(e)?e[0]:e;if(typeof t!="string")return t.enum[0];switch(t){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class Lr{constructor(e){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{};for(var t in zt)if(zt.hasOwnProperty(t)){var r=zt[t];this[t]=e[t]!==void 0?r.processor?r.processor(e[t]):e[t]:Ts(r)}}reportNonstrict(e,t,r){var n=this.strict;if(typeof n=="function"&&(n=n(e,t,r)),!(!n||n==="ignore")){if(n===!0||n==="error")throw new T("LaTeX-incompatible input and strict mode is set to 'error': "+(t+" ["+e+"]"),r);n==="warn"?typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")):typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+n+"': "+t+" ["+e+"]"))}}useStrictBehavior(e,t,r){var n=this.strict;if(typeof n=="function")try{n=n(e,t,r)}catch{n="error"}return!n||n==="ignore"?!1:n===!0||n==="error"?!0:n==="warn"?(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")),!1):(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+n+"': "+t+" ["+e+"]")),!1)}isTrusted(e){if(e.url&&!e.protocol){var t=_.protocolFromUrl(e.url);if(t==null)return!1;e.protocol=t}var r=typeof this.trust=="function"?this.trust(e):this.trust;return!!r}}class g0{constructor(e,t,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=r}sup(){return Ze[Cs[this.id]]}sub(){return Ze[Ms[this.id]]}fracNum(){return Ze[zs[this.id]]}fracDen(){return Ze[Bs[this.id]]}cramp(){return Ze[Rs[this.id]]}text(){return Ze[Ns[this.id]]}isTight(){return this.size>=2}}var _r=0,Nt=1,G0=2,c0=3,it=4,Re=5,V0=6,we=7,Ze=[new g0(_r,0,!1),new g0(Nt,0,!0),new g0(G0,1,!1),new g0(c0,1,!0),new g0(it,2,!1),new g0(Re,2,!0),new g0(V0,3,!1),new g0(we,3,!0)],Cs=[it,Re,it,Re,V0,we,V0,we],Ms=[Re,Re,Re,Re,we,we,we,we],zs=[G0,c0,it,Re,V0,we,V0,we],Bs=[c0,c0,Re,Re,we,we,we,we],Rs=[Nt,Nt,c0,c0,Re,Re,we,we],Ns=[_r,Nt,G0,c0,G0,c0,G0,c0],P={DISPLAY:Ze[_r],TEXT:Ze[G0],SCRIPT:Ze[it],SCRIPTSCRIPT:Ze[V0]},Er=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function Is(a){for(var e=0;e<Er.length;e++)for(var t=Er[e],r=0;r<t.blocks.length;r++){var n=t.blocks[r];if(a>=n[0]&&a<=n[1])return t.name}return null}var Bt=[];Er.forEach(a=>a.blocks.forEach(e=>Bt.push(...e)));function Nn(a){for(var e=0;e<Bt.length;e+=2)if(a>=Bt[e]&&a<=Bt[e+1])return!0;return!1}var U0=80,Os=function(e,t){return"M95,"+(622+e+t)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+e/2.075+" -"+e+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+e)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},Ls=function(e,t){return"M263,"+(601+e+t)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+e/2.084+" -"+e+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+e)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},_s=function(e,t){return"M983 "+(10+e+t)+`
l`+e/3.13+" -"+e+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+e)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},qs=function(e,t){return"M424,"+(2398+e+t)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+e/4.223+" -"+e+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+e)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+e)+" "+t+`
h400000v`+(40+e)+"h-400000z"},Ps=function(e,t){return"M473,"+(2713+e+t)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+e/5.298+" -"+e+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+e)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+e)+" "+t+"h400000v"+(40+e)+"H1017.7z"},Hs=function(e){var t=e/2;return"M400000 "+e+" H0 L"+t+" 0 l65 45 L145 "+(e-80)+" H400000z"},$s=function(e,t,r){var n=r-54-t-e;return"M702 "+(e+t)+"H400000"+(40+e)+`
H742v`+n+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+t+"H400000v"+(40+e)+"H742z"},Us=function(e,t,r){t=1e3*t;var n="";switch(e){case"sqrtMain":n=Os(t,U0);break;case"sqrtSize1":n=Ls(t,U0);break;case"sqrtSize2":n=_s(t,U0);break;case"sqrtSize3":n=qs(t,U0);break;case"sqrtSize4":n=Ps(t,U0);break;case"sqrtTall":n=$s(t,U0,r)}return n},Gs=function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t+" H367z M367 0 H410 V"+t+" H367z");case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t+" H478z M478 0 H521 V"+t+" H478z");default:return""}},Na={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`},Vs=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+` v585 h43z
M367 15 v585 v`+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v602 h84z
M403 1759 V0 H319 V1759 v`+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v602 h84z
M347 1759 V0 h-84 V1759 v`+t+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(t+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(t+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(t+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(t+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}};class lt{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return _.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){var e=t=>t.toText();return this.children.map(e).join("")}}var je={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},bt={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},Ia={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function Ws(a,e){je[a]=e}function qr(a,e,t){if(!je[e])throw new Error("Font metrics not found for font: "+e+".");var r=a.charCodeAt(0),n=je[e][r];if(!n&&a[0]in Ia&&(r=Ia[a[0]].charCodeAt(0),n=je[e][r]),!n&&t==="text"&&Nn(r)&&(n=je[e][77]),n)return{depth:n[0],height:n[1],italic:n[2],skew:n[3],width:n[4]}}var hr={};function Ys(a){var e;if(a>=5?e=0:a>=3?e=1:e=2,!hr[e]){var t=hr[e]={cssEmPerMu:bt.quad[e]/18};for(var r in bt)bt.hasOwnProperty(r)&&(t[r]=bt[r][e])}return hr[e]}var Xs=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],Oa=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],La=function(e,t){return t.size<2?e:Xs[e-1][t.size-1]};class h0{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||h0.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=Oa[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return new h0(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:La(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:Oa[e-1]})}havingBaseStyle(e){e=e||this.style.text();var t=La(h0.BASESIZE,e);return this.size===t&&this.textSize===h0.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==h0.BASESIZE?["sizing","reset-size"+this.size,"size"+h0.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=Ys(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}h0.BASESIZE=6;var Tr={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},Zs={ex:!0,em:!0,mu:!0},In=function(e){return typeof e!="string"&&(e=e.unit),e in Tr||e in Zs||e==="ex"},ue=function(e,t){var r;if(e.unit in Tr)r=Tr[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if(e.unit==="mu")r=t.fontMetrics().cssEmPerMu;else{var n;if(t.style.isTight()?n=t.havingStyle(t.style.text()):n=t,e.unit==="ex")r=n.fontMetrics().xHeight;else if(e.unit==="em")r=n.fontMetrics().quad;else throw new T("Invalid unit: '"+e.unit+"'");n!==t&&(r*=n.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*r,t.maxSize)},M=function(e){return+e.toFixed(4)+"em"},x0=function(e){return e.filter(t=>t).join(" ")},On=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");var n=t.getColor();n&&(this.style.color=n)}},Ln=function(e){var t=document.createElement(e);t.className=x0(this.classes);for(var r in this.style)this.style.hasOwnProperty(r)&&(t.style[r]=this.style[r]);for(var n in this.attributes)this.attributes.hasOwnProperty(n)&&t.setAttribute(n,this.attributes[n]);for(var i=0;i<this.children.length;i++)t.appendChild(this.children[i].toNode());return t},_n=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+_.escape(x0(this.classes))+'"');var r="";for(var n in this.style)this.style.hasOwnProperty(n)&&(r+=_.hyphenate(n)+":"+this.style[n]+";");r&&(t+=' style="'+_.escape(r)+'"');for(var i in this.attributes)this.attributes.hasOwnProperty(i)&&(t+=" "+i+'="'+_.escape(this.attributes[i])+'"');t+=">";for(var s=0;s<this.children.length;s++)t+=this.children[s].toMarkup();return t+="</"+e+">",t};class ut{constructor(e,t,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,On.call(this,e,r,n),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return _.contains(this.classes,e)}toNode(){return Ln.call(this,"span")}toMarkup(){return _n.call(this,"span")}}class Pr{constructor(e,t,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,On.call(this,t,n),this.children=r||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return _.contains(this.classes,e)}toNode(){return Ln.call(this,"a")}toMarkup(){return _n.call(this,"a")}}class js{constructor(e,t,r){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=r}hasClass(e){return _.contains(this.classes,e)}toNode(){var e=document.createElement("img");e.src=this.src,e.alt=this.alt,e.className="mord";for(var t in this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){var e='<img src="'+_.escape(this.src)+'"'+(' alt="'+_.escape(this.alt)+'"'),t="";for(var r in this.style)this.style.hasOwnProperty(r)&&(t+=_.hyphenate(r)+":"+this.style[r]+";");return t&&(e+=' style="'+_.escape(t)+'"'),e+="'/>",e}}var Ks={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"};class Oe{constructor(e,t,r,n,i,s,o,h){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=r||0,this.italic=n||0,this.skew=i||0,this.width=s||0,this.classes=o||[],this.style=h||{},this.maxFontSize=0;var d=Is(this.text.charCodeAt(0));d&&this.classes.push(d+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=Ks[this.text])}hasClass(e){return _.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),t=null;this.italic>0&&(t=document.createElement("span"),t.style.marginRight=M(this.italic)),this.classes.length>0&&(t=t||document.createElement("span"),t.className=x0(this.classes));for(var r in this.style)this.style.hasOwnProperty(r)&&(t=t||document.createElement("span"),t.style[r]=this.style[r]);return t?(t.appendChild(e),t):e}toMarkup(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=_.escape(x0(this.classes)),t+='"');var r="";this.italic>0&&(r+="margin-right:"+this.italic+"em;");for(var n in this.style)this.style.hasOwnProperty(n)&&(r+=_.hyphenate(n)+":"+this.style[n]+";");r&&(e=!0,t+=' style="'+_.escape(r)+'"');var i=_.escape(this.text);return e?(t+=">",t+=i,t+="</span>",t):i}}class d0{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"svg");for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&t.setAttribute(r,this.attributes[r]);for(var n=0;n<this.children.length;n++)t.appendChild(this.children[n].toNode());return t}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+_.escape(this.attributes[t])+'"');e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</svg>",e}}class w0{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"path");return this.alternate?t.setAttribute("d",this.alternate):t.setAttribute("d",Na[this.pathName]),t}toMarkup(){return this.alternate?'<path d="'+_.escape(this.alternate)+'"/>':'<path d="'+_.escape(Na[this.pathName])+'"/>'}}class Cr{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"line");for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&t.setAttribute(r,this.attributes[r]);return t}toMarkup(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+_.escape(this.attributes[t])+'"');return e+="/>",e}}function _a(a){if(a instanceof Oe)return a;throw new Error("Expected symbolNode but got "+String(a)+".")}function Qs(a){if(a instanceof ut)return a;throw new Error("Expected span<HtmlDomNode> but got "+String(a)+".")}var Js={bin:1,close:1,inner:1,open:1,punct:1,rel:1},e1={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},re={math:{},text:{}};function l(a,e,t,r,n,i){re[a][n]={font:e,group:t,replace:r},i&&r&&(re[a][r]=re[a][n])}var u="math",A="text",c="main",f="ams",ne="accent-token",N="bin",ke="close",W0="inner",q="mathord",de="op-token",Me="open",$t="punct",v="rel",v0="spacing",b="textord";l(u,c,v,"≡","\\equiv",!0);l(u,c,v,"≺","\\prec",!0);l(u,c,v,"≻","\\succ",!0);l(u,c,v,"∼","\\sim",!0);l(u,c,v,"⊥","\\perp");l(u,c,v,"⪯","\\preceq",!0);l(u,c,v,"⪰","\\succeq",!0);l(u,c,v,"≃","\\simeq",!0);l(u,c,v,"∣","\\mid",!0);l(u,c,v,"≪","\\ll",!0);l(u,c,v,"≫","\\gg",!0);l(u,c,v,"≍","\\asymp",!0);l(u,c,v,"∥","\\parallel");l(u,c,v,"⋈","\\bowtie",!0);l(u,c,v,"⌣","\\smile",!0);l(u,c,v,"⊑","\\sqsubseteq",!0);l(u,c,v,"⊒","\\sqsupseteq",!0);l(u,c,v,"≐","\\doteq",!0);l(u,c,v,"⌢","\\frown",!0);l(u,c,v,"∋","\\ni",!0);l(u,c,v,"∝","\\propto",!0);l(u,c,v,"⊢","\\vdash",!0);l(u,c,v,"⊣","\\dashv",!0);l(u,c,v,"∋","\\owns");l(u,c,$t,".","\\ldotp");l(u,c,$t,"⋅","\\cdotp");l(u,c,b,"#","\\#");l(A,c,b,"#","\\#");l(u,c,b,"&","\\&");l(A,c,b,"&","\\&");l(u,c,b,"ℵ","\\aleph",!0);l(u,c,b,"∀","\\forall",!0);l(u,c,b,"ℏ","\\hbar",!0);l(u,c,b,"∃","\\exists",!0);l(u,c,b,"∇","\\nabla",!0);l(u,c,b,"♭","\\flat",!0);l(u,c,b,"ℓ","\\ell",!0);l(u,c,b,"♮","\\natural",!0);l(u,c,b,"♣","\\clubsuit",!0);l(u,c,b,"℘","\\wp",!0);l(u,c,b,"♯","\\sharp",!0);l(u,c,b,"♢","\\diamondsuit",!0);l(u,c,b,"ℜ","\\Re",!0);l(u,c,b,"♡","\\heartsuit",!0);l(u,c,b,"ℑ","\\Im",!0);l(u,c,b,"♠","\\spadesuit",!0);l(u,c,b,"§","\\S",!0);l(A,c,b,"§","\\S");l(u,c,b,"¶","\\P",!0);l(A,c,b,"¶","\\P");l(u,c,b,"†","\\dag");l(A,c,b,"†","\\dag");l(A,c,b,"†","\\textdagger");l(u,c,b,"‡","\\ddag");l(A,c,b,"‡","\\ddag");l(A,c,b,"‡","\\textdaggerdbl");l(u,c,ke,"⎱","\\rmoustache",!0);l(u,c,Me,"⎰","\\lmoustache",!0);l(u,c,ke,"⟯","\\rgroup",!0);l(u,c,Me,"⟮","\\lgroup",!0);l(u,c,N,"∓","\\mp",!0);l(u,c,N,"⊖","\\ominus",!0);l(u,c,N,"⊎","\\uplus",!0);l(u,c,N,"⊓","\\sqcap",!0);l(u,c,N,"∗","\\ast");l(u,c,N,"⊔","\\sqcup",!0);l(u,c,N,"◯","\\bigcirc",!0);l(u,c,N,"∙","\\bullet",!0);l(u,c,N,"‡","\\ddagger");l(u,c,N,"≀","\\wr",!0);l(u,c,N,"⨿","\\amalg");l(u,c,N,"&","\\And");l(u,c,v,"⟵","\\longleftarrow",!0);l(u,c,v,"⇐","\\Leftarrow",!0);l(u,c,v,"⟸","\\Longleftarrow",!0);l(u,c,v,"⟶","\\longrightarrow",!0);l(u,c,v,"⇒","\\Rightarrow",!0);l(u,c,v,"⟹","\\Longrightarrow",!0);l(u,c,v,"↔","\\leftrightarrow",!0);l(u,c,v,"⟷","\\longleftrightarrow",!0);l(u,c,v,"⇔","\\Leftrightarrow",!0);l(u,c,v,"⟺","\\Longleftrightarrow",!0);l(u,c,v,"↦","\\mapsto",!0);l(u,c,v,"⟼","\\longmapsto",!0);l(u,c,v,"↗","\\nearrow",!0);l(u,c,v,"↩","\\hookleftarrow",!0);l(u,c,v,"↪","\\hookrightarrow",!0);l(u,c,v,"↘","\\searrow",!0);l(u,c,v,"↼","\\leftharpoonup",!0);l(u,c,v,"⇀","\\rightharpoonup",!0);l(u,c,v,"↙","\\swarrow",!0);l(u,c,v,"↽","\\leftharpoondown",!0);l(u,c,v,"⇁","\\rightharpoondown",!0);l(u,c,v,"↖","\\nwarrow",!0);l(u,c,v,"⇌","\\rightleftharpoons",!0);l(u,f,v,"≮","\\nless",!0);l(u,f,v,"","\\@nleqslant");l(u,f,v,"","\\@nleqq");l(u,f,v,"⪇","\\lneq",!0);l(u,f,v,"≨","\\lneqq",!0);l(u,f,v,"","\\@lvertneqq");l(u,f,v,"⋦","\\lnsim",!0);l(u,f,v,"⪉","\\lnapprox",!0);l(u,f,v,"⊀","\\nprec",!0);l(u,f,v,"⋠","\\npreceq",!0);l(u,f,v,"⋨","\\precnsim",!0);l(u,f,v,"⪹","\\precnapprox",!0);l(u,f,v,"≁","\\nsim",!0);l(u,f,v,"","\\@nshortmid");l(u,f,v,"∤","\\nmid",!0);l(u,f,v,"⊬","\\nvdash",!0);l(u,f,v,"⊭","\\nvDash",!0);l(u,f,v,"⋪","\\ntriangleleft");l(u,f,v,"⋬","\\ntrianglelefteq",!0);l(u,f,v,"⊊","\\subsetneq",!0);l(u,f,v,"","\\@varsubsetneq");l(u,f,v,"⫋","\\subsetneqq",!0);l(u,f,v,"","\\@varsubsetneqq");l(u,f,v,"≯","\\ngtr",!0);l(u,f,v,"","\\@ngeqslant");l(u,f,v,"","\\@ngeqq");l(u,f,v,"⪈","\\gneq",!0);l(u,f,v,"≩","\\gneqq",!0);l(u,f,v,"","\\@gvertneqq");l(u,f,v,"⋧","\\gnsim",!0);l(u,f,v,"⪊","\\gnapprox",!0);l(u,f,v,"⊁","\\nsucc",!0);l(u,f,v,"⋡","\\nsucceq",!0);l(u,f,v,"⋩","\\succnsim",!0);l(u,f,v,"⪺","\\succnapprox",!0);l(u,f,v,"≆","\\ncong",!0);l(u,f,v,"","\\@nshortparallel");l(u,f,v,"∦","\\nparallel",!0);l(u,f,v,"⊯","\\nVDash",!0);l(u,f,v,"⋫","\\ntriangleright");l(u,f,v,"⋭","\\ntrianglerighteq",!0);l(u,f,v,"","\\@nsupseteqq");l(u,f,v,"⊋","\\supsetneq",!0);l(u,f,v,"","\\@varsupsetneq");l(u,f,v,"⫌","\\supsetneqq",!0);l(u,f,v,"","\\@varsupsetneqq");l(u,f,v,"⊮","\\nVdash",!0);l(u,f,v,"⪵","\\precneqq",!0);l(u,f,v,"⪶","\\succneqq",!0);l(u,f,v,"","\\@nsubseteqq");l(u,f,N,"⊴","\\unlhd");l(u,f,N,"⊵","\\unrhd");l(u,f,v,"↚","\\nleftarrow",!0);l(u,f,v,"↛","\\nrightarrow",!0);l(u,f,v,"⇍","\\nLeftarrow",!0);l(u,f,v,"⇏","\\nRightarrow",!0);l(u,f,v,"↮","\\nleftrightarrow",!0);l(u,f,v,"⇎","\\nLeftrightarrow",!0);l(u,f,v,"△","\\vartriangle");l(u,f,b,"ℏ","\\hslash");l(u,f,b,"▽","\\triangledown");l(u,f,b,"◊","\\lozenge");l(u,f,b,"Ⓢ","\\circledS");l(u,f,b,"®","\\circledR");l(A,f,b,"®","\\circledR");l(u,f,b,"∡","\\measuredangle",!0);l(u,f,b,"∄","\\nexists");l(u,f,b,"℧","\\mho");l(u,f,b,"Ⅎ","\\Finv",!0);l(u,f,b,"⅁","\\Game",!0);l(u,f,b,"‵","\\backprime");l(u,f,b,"▲","\\blacktriangle");l(u,f,b,"▼","\\blacktriangledown");l(u,f,b,"■","\\blacksquare");l(u,f,b,"⧫","\\blacklozenge");l(u,f,b,"★","\\bigstar");l(u,f,b,"∢","\\sphericalangle",!0);l(u,f,b,"∁","\\complement",!0);l(u,f,b,"ð","\\eth",!0);l(A,c,b,"ð","ð");l(u,f,b,"╱","\\diagup");l(u,f,b,"╲","\\diagdown");l(u,f,b,"□","\\square");l(u,f,b,"□","\\Box");l(u,f,b,"◊","\\Diamond");l(u,f,b,"¥","\\yen",!0);l(A,f,b,"¥","\\yen",!0);l(u,f,b,"✓","\\checkmark",!0);l(A,f,b,"✓","\\checkmark");l(u,f,b,"ℶ","\\beth",!0);l(u,f,b,"ℸ","\\daleth",!0);l(u,f,b,"ℷ","\\gimel",!0);l(u,f,b,"ϝ","\\digamma",!0);l(u,f,b,"ϰ","\\varkappa");l(u,f,Me,"┌","\\@ulcorner",!0);l(u,f,ke,"┐","\\@urcorner",!0);l(u,f,Me,"└","\\@llcorner",!0);l(u,f,ke,"┘","\\@lrcorner",!0);l(u,f,v,"≦","\\leqq",!0);l(u,f,v,"⩽","\\leqslant",!0);l(u,f,v,"⪕","\\eqslantless",!0);l(u,f,v,"≲","\\lesssim",!0);l(u,f,v,"⪅","\\lessapprox",!0);l(u,f,v,"≊","\\approxeq",!0);l(u,f,N,"⋖","\\lessdot");l(u,f,v,"⋘","\\lll",!0);l(u,f,v,"≶","\\lessgtr",!0);l(u,f,v,"⋚","\\lesseqgtr",!0);l(u,f,v,"⪋","\\lesseqqgtr",!0);l(u,f,v,"≑","\\doteqdot");l(u,f,v,"≓","\\risingdotseq",!0);l(u,f,v,"≒","\\fallingdotseq",!0);l(u,f,v,"∽","\\backsim",!0);l(u,f,v,"⋍","\\backsimeq",!0);l(u,f,v,"⫅","\\subseteqq",!0);l(u,f,v,"⋐","\\Subset",!0);l(u,f,v,"⊏","\\sqsubset",!0);l(u,f,v,"≼","\\preccurlyeq",!0);l(u,f,v,"⋞","\\curlyeqprec",!0);l(u,f,v,"≾","\\precsim",!0);l(u,f,v,"⪷","\\precapprox",!0);l(u,f,v,"⊲","\\vartriangleleft");l(u,f,v,"⊴","\\trianglelefteq");l(u,f,v,"⊨","\\vDash",!0);l(u,f,v,"⊪","\\Vvdash",!0);l(u,f,v,"⌣","\\smallsmile");l(u,f,v,"⌢","\\smallfrown");l(u,f,v,"≏","\\bumpeq",!0);l(u,f,v,"≎","\\Bumpeq",!0);l(u,f,v,"≧","\\geqq",!0);l(u,f,v,"⩾","\\geqslant",!0);l(u,f,v,"⪖","\\eqslantgtr",!0);l(u,f,v,"≳","\\gtrsim",!0);l(u,f,v,"⪆","\\gtrapprox",!0);l(u,f,N,"⋗","\\gtrdot");l(u,f,v,"⋙","\\ggg",!0);l(u,f,v,"≷","\\gtrless",!0);l(u,f,v,"⋛","\\gtreqless",!0);l(u,f,v,"⪌","\\gtreqqless",!0);l(u,f,v,"≖","\\eqcirc",!0);l(u,f,v,"≗","\\circeq",!0);l(u,f,v,"≜","\\triangleq",!0);l(u,f,v,"∼","\\thicksim");l(u,f,v,"≈","\\thickapprox");l(u,f,v,"⫆","\\supseteqq",!0);l(u,f,v,"⋑","\\Supset",!0);l(u,f,v,"⊐","\\sqsupset",!0);l(u,f,v,"≽","\\succcurlyeq",!0);l(u,f,v,"⋟","\\curlyeqsucc",!0);l(u,f,v,"≿","\\succsim",!0);l(u,f,v,"⪸","\\succapprox",!0);l(u,f,v,"⊳","\\vartriangleright");l(u,f,v,"⊵","\\trianglerighteq");l(u,f,v,"⊩","\\Vdash",!0);l(u,f,v,"∣","\\shortmid");l(u,f,v,"∥","\\shortparallel");l(u,f,v,"≬","\\between",!0);l(u,f,v,"⋔","\\pitchfork",!0);l(u,f,v,"∝","\\varpropto");l(u,f,v,"◀","\\blacktriangleleft");l(u,f,v,"∴","\\therefore",!0);l(u,f,v,"∍","\\backepsilon");l(u,f,v,"▶","\\blacktriangleright");l(u,f,v,"∵","\\because",!0);l(u,f,v,"⋘","\\llless");l(u,f,v,"⋙","\\gggtr");l(u,f,N,"⊲","\\lhd");l(u,f,N,"⊳","\\rhd");l(u,f,v,"≂","\\eqsim",!0);l(u,c,v,"⋈","\\Join");l(u,f,v,"≑","\\Doteq",!0);l(u,f,N,"∔","\\dotplus",!0);l(u,f,N,"∖","\\smallsetminus");l(u,f,N,"⋒","\\Cap",!0);l(u,f,N,"⋓","\\Cup",!0);l(u,f,N,"⩞","\\doublebarwedge",!0);l(u,f,N,"⊟","\\boxminus",!0);l(u,f,N,"⊞","\\boxplus",!0);l(u,f,N,"⋇","\\divideontimes",!0);l(u,f,N,"⋉","\\ltimes",!0);l(u,f,N,"⋊","\\rtimes",!0);l(u,f,N,"⋋","\\leftthreetimes",!0);l(u,f,N,"⋌","\\rightthreetimes",!0);l(u,f,N,"⋏","\\curlywedge",!0);l(u,f,N,"⋎","\\curlyvee",!0);l(u,f,N,"⊝","\\circleddash",!0);l(u,f,N,"⊛","\\circledast",!0);l(u,f,N,"⋅","\\centerdot");l(u,f,N,"⊺","\\intercal",!0);l(u,f,N,"⋒","\\doublecap");l(u,f,N,"⋓","\\doublecup");l(u,f,N,"⊠","\\boxtimes",!0);l(u,f,v,"⇢","\\dashrightarrow",!0);l(u,f,v,"⇠","\\dashleftarrow",!0);l(u,f,v,"⇇","\\leftleftarrows",!0);l(u,f,v,"⇆","\\leftrightarrows",!0);l(u,f,v,"⇚","\\Lleftarrow",!0);l(u,f,v,"↞","\\twoheadleftarrow",!0);l(u,f,v,"↢","\\leftarrowtail",!0);l(u,f,v,"↫","\\looparrowleft",!0);l(u,f,v,"⇋","\\leftrightharpoons",!0);l(u,f,v,"↶","\\curvearrowleft",!0);l(u,f,v,"↺","\\circlearrowleft",!0);l(u,f,v,"↰","\\Lsh",!0);l(u,f,v,"⇈","\\upuparrows",!0);l(u,f,v,"↿","\\upharpoonleft",!0);l(u,f,v,"⇃","\\downharpoonleft",!0);l(u,c,v,"⊶","\\origof",!0);l(u,c,v,"⊷","\\imageof",!0);l(u,f,v,"⊸","\\multimap",!0);l(u,f,v,"↭","\\leftrightsquigarrow",!0);l(u,f,v,"⇉","\\rightrightarrows",!0);l(u,f,v,"⇄","\\rightleftarrows",!0);l(u,f,v,"↠","\\twoheadrightarrow",!0);l(u,f,v,"↣","\\rightarrowtail",!0);l(u,f,v,"↬","\\looparrowright",!0);l(u,f,v,"↷","\\curvearrowright",!0);l(u,f,v,"↻","\\circlearrowright",!0);l(u,f,v,"↱","\\Rsh",!0);l(u,f,v,"⇊","\\downdownarrows",!0);l(u,f,v,"↾","\\upharpoonright",!0);l(u,f,v,"⇂","\\downharpoonright",!0);l(u,f,v,"⇝","\\rightsquigarrow",!0);l(u,f,v,"⇝","\\leadsto");l(u,f,v,"⇛","\\Rrightarrow",!0);l(u,f,v,"↾","\\restriction");l(u,c,b,"‘","`");l(u,c,b,"$","\\$");l(A,c,b,"$","\\$");l(A,c,b,"$","\\textdollar");l(u,c,b,"%","\\%");l(A,c,b,"%","\\%");l(u,c,b,"_","\\_");l(A,c,b,"_","\\_");l(A,c,b,"_","\\textunderscore");l(u,c,b,"∠","\\angle",!0);l(u,c,b,"∞","\\infty",!0);l(u,c,b,"′","\\prime");l(u,c,b,"△","\\triangle");l(u,c,b,"Γ","\\Gamma",!0);l(u,c,b,"Δ","\\Delta",!0);l(u,c,b,"Θ","\\Theta",!0);l(u,c,b,"Λ","\\Lambda",!0);l(u,c,b,"Ξ","\\Xi",!0);l(u,c,b,"Π","\\Pi",!0);l(u,c,b,"Σ","\\Sigma",!0);l(u,c,b,"Υ","\\Upsilon",!0);l(u,c,b,"Φ","\\Phi",!0);l(u,c,b,"Ψ","\\Psi",!0);l(u,c,b,"Ω","\\Omega",!0);l(u,c,b,"A","Α");l(u,c,b,"B","Β");l(u,c,b,"E","Ε");l(u,c,b,"Z","Ζ");l(u,c,b,"H","Η");l(u,c,b,"I","Ι");l(u,c,b,"K","Κ");l(u,c,b,"M","Μ");l(u,c,b,"N","Ν");l(u,c,b,"O","Ο");l(u,c,b,"P","Ρ");l(u,c,b,"T","Τ");l(u,c,b,"X","Χ");l(u,c,b,"¬","\\neg",!0);l(u,c,b,"¬","\\lnot");l(u,c,b,"⊤","\\top");l(u,c,b,"⊥","\\bot");l(u,c,b,"∅","\\emptyset");l(u,f,b,"∅","\\varnothing");l(u,c,q,"α","\\alpha",!0);l(u,c,q,"β","\\beta",!0);l(u,c,q,"γ","\\gamma",!0);l(u,c,q,"δ","\\delta",!0);l(u,c,q,"ϵ","\\epsilon",!0);l(u,c,q,"ζ","\\zeta",!0);l(u,c,q,"η","\\eta",!0);l(u,c,q,"θ","\\theta",!0);l(u,c,q,"ι","\\iota",!0);l(u,c,q,"κ","\\kappa",!0);l(u,c,q,"λ","\\lambda",!0);l(u,c,q,"μ","\\mu",!0);l(u,c,q,"ν","\\nu",!0);l(u,c,q,"ξ","\\xi",!0);l(u,c,q,"ο","\\omicron",!0);l(u,c,q,"π","\\pi",!0);l(u,c,q,"ρ","\\rho",!0);l(u,c,q,"σ","\\sigma",!0);l(u,c,q,"τ","\\tau",!0);l(u,c,q,"υ","\\upsilon",!0);l(u,c,q,"ϕ","\\phi",!0);l(u,c,q,"χ","\\chi",!0);l(u,c,q,"ψ","\\psi",!0);l(u,c,q,"ω","\\omega",!0);l(u,c,q,"ε","\\varepsilon",!0);l(u,c,q,"ϑ","\\vartheta",!0);l(u,c,q,"ϖ","\\varpi",!0);l(u,c,q,"ϱ","\\varrho",!0);l(u,c,q,"ς","\\varsigma",!0);l(u,c,q,"φ","\\varphi",!0);l(u,c,N,"∗","*",!0);l(u,c,N,"+","+");l(u,c,N,"−","-",!0);l(u,c,N,"⋅","\\cdot",!0);l(u,c,N,"∘","\\circ",!0);l(u,c,N,"÷","\\div",!0);l(u,c,N,"±","\\pm",!0);l(u,c,N,"×","\\times",!0);l(u,c,N,"∩","\\cap",!0);l(u,c,N,"∪","\\cup",!0);l(u,c,N,"∖","\\setminus",!0);l(u,c,N,"∧","\\land");l(u,c,N,"∨","\\lor");l(u,c,N,"∧","\\wedge",!0);l(u,c,N,"∨","\\vee",!0);l(u,c,b,"√","\\surd");l(u,c,Me,"⟨","\\langle",!0);l(u,c,Me,"∣","\\lvert");l(u,c,Me,"∥","\\lVert");l(u,c,ke,"?","?");l(u,c,ke,"!","!");l(u,c,ke,"⟩","\\rangle",!0);l(u,c,ke,"∣","\\rvert");l(u,c,ke,"∥","\\rVert");l(u,c,v,"=","=");l(u,c,v,":",":");l(u,c,v,"≈","\\approx",!0);l(u,c,v,"≅","\\cong",!0);l(u,c,v,"≥","\\ge");l(u,c,v,"≥","\\geq",!0);l(u,c,v,"←","\\gets");l(u,c,v,">","\\gt",!0);l(u,c,v,"∈","\\in",!0);l(u,c,v,"","\\@not");l(u,c,v,"⊂","\\subset",!0);l(u,c,v,"⊃","\\supset",!0);l(u,c,v,"⊆","\\subseteq",!0);l(u,c,v,"⊇","\\supseteq",!0);l(u,f,v,"⊈","\\nsubseteq",!0);l(u,f,v,"⊉","\\nsupseteq",!0);l(u,c,v,"⊨","\\models");l(u,c,v,"←","\\leftarrow",!0);l(u,c,v,"≤","\\le");l(u,c,v,"≤","\\leq",!0);l(u,c,v,"<","\\lt",!0);l(u,c,v,"→","\\rightarrow",!0);l(u,c,v,"→","\\to");l(u,f,v,"≱","\\ngeq",!0);l(u,f,v,"≰","\\nleq",!0);l(u,c,v0," ","\\ ");l(u,c,v0," ","\\space");l(u,c,v0," ","\\nobreakspace");l(A,c,v0," ","\\ ");l(A,c,v0," "," ");l(A,c,v0," ","\\space");l(A,c,v0," ","\\nobreakspace");l(u,c,v0,null,"\\nobreak");l(u,c,v0,null,"\\allowbreak");l(u,c,$t,",",",");l(u,c,$t,";",";");l(u,f,N,"⊼","\\barwedge",!0);l(u,f,N,"⊻","\\veebar",!0);l(u,c,N,"⊙","\\odot",!0);l(u,c,N,"⊕","\\oplus",!0);l(u,c,N,"⊗","\\otimes",!0);l(u,c,b,"∂","\\partial",!0);l(u,c,N,"⊘","\\oslash",!0);l(u,f,N,"⊚","\\circledcirc",!0);l(u,f,N,"⊡","\\boxdot",!0);l(u,c,N,"△","\\bigtriangleup");l(u,c,N,"▽","\\bigtriangledown");l(u,c,N,"†","\\dagger");l(u,c,N,"⋄","\\diamond");l(u,c,N,"⋆","\\star");l(u,c,N,"◃","\\triangleleft");l(u,c,N,"▹","\\triangleright");l(u,c,Me,"{","\\{");l(A,c,b,"{","\\{");l(A,c,b,"{","\\textbraceleft");l(u,c,ke,"}","\\}");l(A,c,b,"}","\\}");l(A,c,b,"}","\\textbraceright");l(u,c,Me,"{","\\lbrace");l(u,c,ke,"}","\\rbrace");l(u,c,Me,"[","\\lbrack",!0);l(A,c,b,"[","\\lbrack",!0);l(u,c,ke,"]","\\rbrack",!0);l(A,c,b,"]","\\rbrack",!0);l(u,c,Me,"(","\\lparen",!0);l(u,c,ke,")","\\rparen",!0);l(A,c,b,"<","\\textless",!0);l(A,c,b,">","\\textgreater",!0);l(u,c,Me,"⌊","\\lfloor",!0);l(u,c,ke,"⌋","\\rfloor",!0);l(u,c,Me,"⌈","\\lceil",!0);l(u,c,ke,"⌉","\\rceil",!0);l(u,c,b,"\\","\\backslash");l(u,c,b,"∣","|");l(u,c,b,"∣","\\vert");l(A,c,b,"|","\\textbar",!0);l(u,c,b,"∥","\\|");l(u,c,b,"∥","\\Vert");l(A,c,b,"∥","\\textbardbl");l(A,c,b,"~","\\textasciitilde");l(A,c,b,"\\","\\textbackslash");l(A,c,b,"^","\\textasciicircum");l(u,c,v,"↑","\\uparrow",!0);l(u,c,v,"⇑","\\Uparrow",!0);l(u,c,v,"↓","\\downarrow",!0);l(u,c,v,"⇓","\\Downarrow",!0);l(u,c,v,"↕","\\updownarrow",!0);l(u,c,v,"⇕","\\Updownarrow",!0);l(u,c,de,"∐","\\coprod");l(u,c,de,"⋁","\\bigvee");l(u,c,de,"⋀","\\bigwedge");l(u,c,de,"⨄","\\biguplus");l(u,c,de,"⋂","\\bigcap");l(u,c,de,"⋃","\\bigcup");l(u,c,de,"∫","\\int");l(u,c,de,"∫","\\intop");l(u,c,de,"∬","\\iint");l(u,c,de,"∭","\\iiint");l(u,c,de,"∏","\\prod");l(u,c,de,"∑","\\sum");l(u,c,de,"⨂","\\bigotimes");l(u,c,de,"⨁","\\bigoplus");l(u,c,de,"⨀","\\bigodot");l(u,c,de,"∮","\\oint");l(u,c,de,"∯","\\oiint");l(u,c,de,"∰","\\oiiint");l(u,c,de,"⨆","\\bigsqcup");l(u,c,de,"∫","\\smallint");l(A,c,W0,"…","\\textellipsis");l(u,c,W0,"…","\\mathellipsis");l(A,c,W0,"…","\\ldots",!0);l(u,c,W0,"…","\\ldots",!0);l(u,c,W0,"⋯","\\@cdots",!0);l(u,c,W0,"⋱","\\ddots",!0);l(u,c,b,"⋮","\\varvdots");l(u,c,ne,"ˊ","\\acute");l(u,c,ne,"ˋ","\\grave");l(u,c,ne,"¨","\\ddot");l(u,c,ne,"~","\\tilde");l(u,c,ne,"ˉ","\\bar");l(u,c,ne,"˘","\\breve");l(u,c,ne,"ˇ","\\check");l(u,c,ne,"^","\\hat");l(u,c,ne,"⃗","\\vec");l(u,c,ne,"˙","\\dot");l(u,c,ne,"˚","\\mathring");l(u,c,q,"","\\@imath");l(u,c,q,"","\\@jmath");l(u,c,b,"ı","ı");l(u,c,b,"ȷ","ȷ");l(A,c,b,"ı","\\i",!0);l(A,c,b,"ȷ","\\j",!0);l(A,c,b,"ß","\\ss",!0);l(A,c,b,"æ","\\ae",!0);l(A,c,b,"œ","\\oe",!0);l(A,c,b,"ø","\\o",!0);l(A,c,b,"Æ","\\AE",!0);l(A,c,b,"Œ","\\OE",!0);l(A,c,b,"Ø","\\O",!0);l(A,c,ne,"ˊ","\\'");l(A,c,ne,"ˋ","\\`");l(A,c,ne,"ˆ","\\^");l(A,c,ne,"˜","\\~");l(A,c,ne,"ˉ","\\=");l(A,c,ne,"˘","\\u");l(A,c,ne,"˙","\\.");l(A,c,ne,"¸","\\c");l(A,c,ne,"˚","\\r");l(A,c,ne,"ˇ","\\v");l(A,c,ne,"¨",'\\"');l(A,c,ne,"˝","\\H");l(A,c,ne,"◯","\\textcircled");var qn={"--":!0,"---":!0,"``":!0,"''":!0};l(A,c,b,"–","--",!0);l(A,c,b,"–","\\textendash");l(A,c,b,"—","---",!0);l(A,c,b,"—","\\textemdash");l(A,c,b,"‘","`",!0);l(A,c,b,"‘","\\textquoteleft");l(A,c,b,"’","'",!0);l(A,c,b,"’","\\textquoteright");l(A,c,b,"“","``",!0);l(A,c,b,"“","\\textquotedblleft");l(A,c,b,"”","''",!0);l(A,c,b,"”","\\textquotedblright");l(u,c,b,"°","\\degree",!0);l(A,c,b,"°","\\degree");l(A,c,b,"°","\\textdegree",!0);l(u,c,b,"£","\\pounds");l(u,c,b,"£","\\mathsterling",!0);l(A,c,b,"£","\\pounds");l(A,c,b,"£","\\textsterling",!0);l(u,f,b,"✠","\\maltese");l(A,f,b,"✠","\\maltese");var qa='0123456789/@."';for(var cr=0;cr<qa.length;cr++){var Pa=qa.charAt(cr);l(u,c,b,Pa,Pa)}var Ha='0123456789!@*()-=+";:?/.,';for(var mr=0;mr<Ha.length;mr++){var $a=Ha.charAt(mr);l(A,c,b,$a,$a)}var It="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(var dr=0;dr<It.length;dr++){var yt=It.charAt(dr);l(u,c,q,yt,yt),l(A,c,b,yt,yt)}l(u,f,b,"C","ℂ");l(A,f,b,"C","ℂ");l(u,f,b,"H","ℍ");l(A,f,b,"H","ℍ");l(u,f,b,"N","ℕ");l(A,f,b,"N","ℕ");l(u,f,b,"P","ℙ");l(A,f,b,"P","ℙ");l(u,f,b,"Q","ℚ");l(A,f,b,"Q","ℚ");l(u,f,b,"R","ℝ");l(A,f,b,"R","ℝ");l(u,f,b,"Z","ℤ");l(A,f,b,"Z","ℤ");l(u,c,q,"h","ℎ");l(A,c,q,"h","ℎ");var H="";for(var xe=0;xe<It.length;xe++){var he=It.charAt(xe);H=String.fromCharCode(55349,56320+xe),l(u,c,q,he,H),l(A,c,b,he,H),H=String.fromCharCode(55349,56372+xe),l(u,c,q,he,H),l(A,c,b,he,H),H=String.fromCharCode(55349,56424+xe),l(u,c,q,he,H),l(A,c,b,he,H),H=String.fromCharCode(55349,56580+xe),l(u,c,q,he,H),l(A,c,b,he,H),H=String.fromCharCode(55349,56684+xe),l(u,c,q,he,H),l(A,c,b,he,H),H=String.fromCharCode(55349,56736+xe),l(u,c,q,he,H),l(A,c,b,he,H),H=String.fromCharCode(55349,56788+xe),l(u,c,q,he,H),l(A,c,b,he,H),H=String.fromCharCode(55349,56840+xe),l(u,c,q,he,H),l(A,c,b,he,H),H=String.fromCharCode(55349,56944+xe),l(u,c,q,he,H),l(A,c,b,he,H),xe<26&&(H=String.fromCharCode(55349,56632+xe),l(u,c,q,he,H),l(A,c,b,he,H),H=String.fromCharCode(55349,56476+xe),l(u,c,q,he,H),l(A,c,b,he,H))}H="𝕜";l(u,c,q,"k",H);l(A,c,b,"k",H);for(var M0=0;M0<10;M0++){var b0=M0.toString();H=String.fromCharCode(55349,57294+M0),l(u,c,q,b0,H),l(A,c,b,b0,H),H=String.fromCharCode(55349,57314+M0),l(u,c,q,b0,H),l(A,c,b,b0,H),H=String.fromCharCode(55349,57324+M0),l(u,c,q,b0,H),l(A,c,b,b0,H),H=String.fromCharCode(55349,57334+M0),l(u,c,q,b0,H),l(A,c,b,b0,H)}var Mr="ÐÞþ";for(var pr=0;pr<Mr.length;pr++){var xt=Mr.charAt(pr);l(u,c,q,xt,xt),l(A,c,b,xt,xt)}var wt=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Ua=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],t1=function(e,t){var r=e.charCodeAt(0),n=e.charCodeAt(1),i=(r-55296)*1024+(n-56320)+65536,s=t==="math"?0:1;if(119808<=i&&i<120484){var o=Math.floor((i-119808)/26);return[wt[o][2],wt[o][s]]}else if(120782<=i&&i<=120831){var h=Math.floor((i-120782)/10);return[Ua[h][2],Ua[h][s]]}else{if(i===120485||i===120486)return[wt[0][2],wt[0][s]];if(120486<i&&i<120782)return["",""];throw new T("Unsupported character: "+e)}},Ut=function(e,t,r){return re[r][e]&&re[r][e].replace&&(e=re[r][e].replace),{value:e,metrics:qr(e,t,r)}},$e=function(e,t,r,n,i){var s=Ut(e,t,r),o=s.metrics;e=s.value;var h;if(o){var d=o.italic;(r==="text"||n&&n.font==="mathit")&&(d=0),h=new Oe(e,o.height,o.depth,d,o.skew,o.width,i)}else typeof console<"u"&&console.warn("No character metrics "+("for '"+e+"' in style '"+t+"' and mode '"+r+"'")),h=new Oe(e,0,0,0,0,0,i);if(n){h.maxFontSize=n.sizeMultiplier,n.style.isTight()&&h.classes.push("mtight");var p=n.getColor();p&&(h.style.color=p)}return h},r1=function(e,t,r,n){return n===void 0&&(n=[]),r.font==="boldsymbol"&&Ut(e,"Main-Bold",t).metrics?$e(e,"Main-Bold",t,r,n.concat(["mathbf"])):e==="\\"||re[t][e].font==="main"?$e(e,"Main-Regular",t,r,n):$e(e,"AMS-Regular",t,r,n.concat(["amsrm"]))},a1=function(e,t,r,n,i){return i!=="textord"&&Ut(e,"Math-BoldItalic",t).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},n1=function(e,t,r){var n=e.mode,i=e.text,s=["mord"],o=n==="math"||n==="text"&&t.font,h=o?t.font:t.fontFamily,d="",p="";if(i.charCodeAt(0)===55349&&([d,p]=t1(i,n)),d.length>0)return $e(i,d,n,t,s.concat(p));if(h){var g,x;if(h==="boldsymbol"){var w=a1(i,n,t,s,r);g=w.fontName,x=[w.fontClass]}else o?(g=$n[h].fontName,x=[h]):(g=Dt(h,t.fontWeight,t.fontShape),x=[h,t.fontWeight,t.fontShape]);if(Ut(i,g,n).metrics)return $e(i,g,n,t,s.concat(x));if(qn.hasOwnProperty(i)&&g.slice(0,10)==="Typewriter"){for(var S=[],E=0;E<i.length;E++)S.push($e(i[E],g,n,t,s.concat(x)));return Hn(S)}}if(r==="mathord")return $e(i,"Math-Italic",n,t,s.concat(["mathnormal"]));if(r==="textord"){var z=re[n][i]&&re[n][i].font;if(z==="ams"){var k=Dt("amsrm",t.fontWeight,t.fontShape);return $e(i,k,n,t,s.concat("amsrm",t.fontWeight,t.fontShape))}else if(z==="main"||!z){var L=Dt("textrm",t.fontWeight,t.fontShape);return $e(i,L,n,t,s.concat(t.fontWeight,t.fontShape))}else{var I=Dt(z,t.fontWeight,t.fontShape);return $e(i,I,n,t,s.concat(I,t.fontWeight,t.fontShape))}}else throw new Error("unexpected type: "+r+" in makeOrd")},i1=(a,e)=>{if(x0(a.classes)!==x0(e.classes)||a.skew!==e.skew||a.maxFontSize!==e.maxFontSize)return!1;if(a.classes.length===1){var t=a.classes[0];if(t==="mbin"||t==="mord")return!1}for(var r in a.style)if(a.style.hasOwnProperty(r)&&a.style[r]!==e.style[r])return!1;for(var n in e.style)if(e.style.hasOwnProperty(n)&&a.style[n]!==e.style[n])return!1;return!0},s1=a=>{for(var e=0;e<a.length-1;e++){var t=a[e],r=a[e+1];t instanceof Oe&&r instanceof Oe&&i1(t,r)&&(t.text+=r.text,t.height=Math.max(t.height,r.height),t.depth=Math.max(t.depth,r.depth),t.italic=r.italic,a.splice(e+1,1),e--)}return a},Hr=function(e){for(var t=0,r=0,n=0,i=0;i<e.children.length;i++){var s=e.children[i];s.height>t&&(t=s.height),s.depth>r&&(r=s.depth),s.maxFontSize>n&&(n=s.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=n},Ae=function(e,t,r,n){var i=new ut(e,t,r,n);return Hr(i),i},Pn=(a,e,t,r)=>new ut(a,e,t,r),l1=function(e,t,r){var n=Ae([e],[],t);return n.height=Math.max(r||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),n.style.borderBottomWidth=M(n.height),n.maxFontSize=1,n},u1=function(e,t,r,n){var i=new Pr(e,t,r,n);return Hr(i),i},Hn=function(e){var t=new lt(e);return Hr(t),t},o1=function(e,t){return e instanceof lt?Ae([],[e],t):e},h1=function(e){if(e.positionType==="individualShift"){for(var t=e.children,r=[t[0]],n=-t[0].shift-t[0].elem.depth,i=n,s=1;s<t.length;s++){var o=-t[s].shift-i-t[s].elem.depth,h=o-(t[s-1].elem.height+t[s-1].elem.depth);i=i+o,r.push({type:"kern",size:h}),r.push(t[s])}return{children:r,depth:n}}var d;if(e.positionType==="top"){for(var p=e.positionData,g=0;g<e.children.length;g++){var x=e.children[g];p-=x.type==="kern"?x.size:x.elem.height+x.elem.depth}d=p}else if(e.positionType==="bottom")d=-e.positionData;else{var w=e.children[0];if(w.type!=="elem")throw new Error('First child must have type "elem".');if(e.positionType==="shift")d=-w.elem.depth-e.positionData;else if(e.positionType==="firstBaseline")d=-w.elem.depth;else throw new Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:d}},c1=function(e,t){for(var{children:r,depth:n}=h1(e),i=0,s=0;s<r.length;s++){var o=r[s];if(o.type==="elem"){var h=o.elem;i=Math.max(i,h.maxFontSize,h.height)}}i+=2;var d=Ae(["pstrut"],[]);d.style.height=M(i);for(var p=[],g=n,x=n,w=n,S=0;S<r.length;S++){var E=r[S];if(E.type==="kern")w+=E.size;else{var z=E.elem,k=E.wrapperClasses||[],L=E.wrapperStyle||{},I=Ae(k,[d,z],void 0,L);I.style.top=M(-i-w-z.depth),E.marginLeft&&(I.style.marginLeft=E.marginLeft),E.marginRight&&(I.style.marginRight=E.marginRight),p.push(I),w+=z.height+z.depth}g=Math.min(g,w),x=Math.max(x,w)}var G=Ae(["vlist"],p);G.style.height=M(x);var $;if(g<0){var X=Ae([],[]),W=Ae(["vlist"],[X]);W.style.height=M(-g);var ae=Ae(["vlist-s"],[new Oe("​")]);$=[Ae(["vlist-r"],[G,ae]),Ae(["vlist-r"],[W])]}else $=[Ae(["vlist-r"],[G])];var Z=Ae(["vlist-t"],$);return $.length===2&&Z.classes.push("vlist-t2"),Z.height=x,Z.depth=-g,Z},m1=(a,e)=>{var t=Ae(["mspace"],[],e),r=ue(a,e);return t.style.marginRight=M(r),t},Dt=function(e,t,r){var n="";switch(e){case"amsrm":n="AMS";break;case"textrm":n="Main";break;case"textsf":n="SansSerif";break;case"texttt":n="Typewriter";break;default:n=e}var i;return t==="textbf"&&r==="textit"?i="BoldItalic":t==="textbf"?i="Bold":t==="textit"?i="Italic":i="Regular",n+"-"+i},$n={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},Un={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},d1=function(e,t){var[r,n,i]=Un[e],s=new w0(r),o=new d0([s],{width:M(n),height:M(i),style:"width:"+M(n),viewBox:"0 0 "+1e3*n+" "+1e3*i,preserveAspectRatio:"xMinYMin"}),h=Pn(["overlay"],[o],t);return h.height=i,h.style.height=M(i),h.style.width=M(n),h},D={fontMap:$n,makeSymbol:$e,mathsym:r1,makeSpan:Ae,makeSvgSpan:Pn,makeLineSpan:l1,makeAnchor:u1,makeFragment:Hn,wrapFragment:o1,makeVList:c1,makeOrd:n1,makeGlue:m1,staticSvg:d1,svgData:Un,tryCombineChars:s1},le={number:3,unit:"mu"},z0={number:4,unit:"mu"},o0={number:5,unit:"mu"},p1={mord:{mop:le,mbin:z0,mrel:o0,minner:le},mop:{mord:le,mop:le,mrel:o0,minner:le},mbin:{mord:z0,mop:z0,mopen:z0,minner:z0},mrel:{mord:o0,mop:o0,mopen:o0,minner:o0},mopen:{},mclose:{mop:le,mbin:z0,mrel:o0,minner:le},mpunct:{mord:le,mop:le,mrel:o0,mopen:le,mclose:le,mpunct:le,minner:le},minner:{mord:le,mop:le,mbin:z0,mrel:o0,mopen:le,mpunct:le,minner:le}},f1={mord:{mop:le},mop:{mord:le,mop:le},mbin:{},mrel:{},mopen:{},mclose:{mop:le},mpunct:{},minner:{mop:le}},Gn={},Ot={},Lt={};function B(a){for(var{type:e,names:t,props:r,handler:n,htmlBuilder:i,mathmlBuilder:s}=a,o={type:e,numArgs:r.numArgs,argTypes:r.argTypes,allowedInArgument:!!r.allowedInArgument,allowedInText:!!r.allowedInText,allowedInMath:r.allowedInMath===void 0?!0:r.allowedInMath,numOptionalArgs:r.numOptionalArgs||0,infix:!!r.infix,primitive:!!r.primitive,handler:n},h=0;h<t.length;++h)Gn[t[h]]=o;e&&(i&&(Ot[e]=i),s&&(Lt[e]=s))}function N0(a){var{type:e,htmlBuilder:t,mathmlBuilder:r}=a;B({type:e,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:t,mathmlBuilder:r})}var _t=function(e){return e.type==="ordgroup"&&e.body.length===1?e.body[0]:e},ce=function(e){return e.type==="ordgroup"?e.body:[e]},p0=D.makeSpan,v1=["leftmost","mbin","mopen","mrel","mop","mpunct"],g1=["rightmost","mrel","mclose","mpunct"],b1={display:P.DISPLAY,text:P.TEXT,script:P.SCRIPT,scriptscript:P.SCRIPTSCRIPT},y1={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},fe=function(e,t,r,n){n===void 0&&(n=[null,null]);for(var i=[],s=0;s<e.length;s++){var o=Y(e[s],t);if(o instanceof lt){var h=o.children;i.push(...h)}else i.push(o)}if(D.tryCombineChars(i),!r)return i;var d=t;if(e.length===1){var p=e[0];p.type==="sizing"?d=t.havingSize(p.size):p.type==="styling"&&(d=t.havingStyle(b1[p.style]))}var g=p0([n[0]||"leftmost"],[],t),x=p0([n[1]||"rightmost"],[],t),w=r==="root";return Ga(i,(S,E)=>{var z=E.classes[0],k=S.classes[0];z==="mbin"&&_.contains(g1,k)?E.classes[0]="mord":k==="mbin"&&_.contains(v1,z)&&(S.classes[0]="mord")},{node:g},x,w),Ga(i,(S,E)=>{var z=zr(E),k=zr(S),L=z&&k?S.hasClass("mtight")?f1[z][k]:p1[z][k]:null;if(L)return D.makeGlue(L,d)},{node:g},x,w),i},Ga=function a(e,t,r,n,i){n&&e.push(n);for(var s=0;s<e.length;s++){var o=e[s],h=Vn(o);if(h){a(h.children,t,r,null,i);continue}var d=!o.hasClass("mspace");if(d){var p=t(o,r.node);p&&(r.insertAfter?r.insertAfter(p):(e.unshift(p),s++))}d?r.node=o:i&&o.hasClass("newline")&&(r.node=p0(["leftmost"])),r.insertAfter=(g=>x=>{e.splice(g+1,0,x),s++})(s)}n&&e.pop()},Vn=function(e){return e instanceof lt||e instanceof Pr||e instanceof ut&&e.hasClass("enclosing")?e:null},x1=function a(e,t){var r=Vn(e);if(r){var n=r.children;if(n.length){if(t==="right")return a(n[n.length-1],"right");if(t==="left")return a(n[0],"left")}}return e},zr=function(e,t){return e?(t&&(e=x1(e,t)),y1[e.classes[0]]||null):null},st=function(e,t){var r=["nulldelimiter"].concat(e.baseSizingClasses());return p0(t.concat(r))},Y=function(e,t,r){if(!e)return p0();if(Ot[e.type]){var n=Ot[e.type](e,t);if(r&&t.size!==r.size){n=p0(t.sizingClasses(r),[n],t);var i=t.sizeMultiplier/r.sizeMultiplier;n.height*=i,n.depth*=i}return n}else throw new T("Got group of unknown type: '"+e.type+"'")};function kt(a,e){var t=p0(["base"],a,e),r=p0(["strut"]);return r.style.height=M(t.height+t.depth),t.depth&&(r.style.verticalAlign=M(-t.depth)),t.children.unshift(r),t}function Br(a,e){var t=null;a.length===1&&a[0].type==="tag"&&(t=a[0].tag,a=a[0].body);var r=fe(a,e,"root"),n;r.length===2&&r[1].hasClass("tag")&&(n=r.pop());for(var i=[],s=[],o=0;o<r.length;o++)if(s.push(r[o]),r[o].hasClass("mbin")||r[o].hasClass("mrel")||r[o].hasClass("allowbreak")){for(var h=!1;o<r.length-1&&r[o+1].hasClass("mspace")&&!r[o+1].hasClass("newline");)o++,s.push(r[o]),r[o].hasClass("nobreak")&&(h=!0);h||(i.push(kt(s,e)),s=[])}else r[o].hasClass("newline")&&(s.pop(),s.length>0&&(i.push(kt(s,e)),s=[]),i.push(r[o]));s.length>0&&i.push(kt(s,e));var d;t?(d=kt(fe(t,e,!0)),d.classes=["tag"],i.push(d)):n&&i.push(n);var p=p0(["katex-html"],i);if(p.setAttribute("aria-hidden","true"),d){var g=d.children[0];g.style.height=M(p.height+p.depth),p.depth&&(g.style.verticalAlign=M(-p.depth))}return p}function Wn(a){return new lt(a)}class Be{constructor(e,t,r){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=r||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=x0(this.classes));for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=_.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+_.escape(x0(this.classes))+'"'),e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</"+this.type+">",e}toText(){return this.children.map(e=>e.toText()).join("")}}class rt{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return _.escape(this.toText())}toText(){return this.text}}class w1{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character=" ":e>=.1666&&e<=.1667?this.character=" ":e>=.2222&&e<=.2223?this.character=" ":e>=.2777&&e<=.2778?this.character="  ":e>=-.05556&&e<=-.05555?this.character=" ⁣":e>=-.1667&&e<=-.1666?this.character=" ⁣":e>=-.2223&&e<=-.2222?this.character=" ⁣":e>=-.2778&&e<=-.2777?this.character=" ⁣":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",M(this.width)),e}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+M(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var F={MathNode:Be,TextNode:rt,SpaceNode:w1,newDocumentFragment:Wn},Le=function(e,t,r){return re[t][e]&&re[t][e].replace&&e.charCodeAt(0)!==55349&&!(qn.hasOwnProperty(e)&&r&&(r.fontFamily&&r.fontFamily.slice(4,6)==="tt"||r.font&&r.font.slice(4,6)==="tt"))&&(e=re[t][e].replace),new F.TextNode(e)},$r=function(e){return e.length===1?e[0]:new F.MathNode("mrow",e)},Ur=function(e,t){if(t.fontFamily==="texttt")return"monospace";if(t.fontFamily==="textsf")return t.fontShape==="textit"&&t.fontWeight==="textbf"?"sans-serif-bold-italic":t.fontShape==="textit"?"sans-serif-italic":t.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(t.fontShape==="textit"&&t.fontWeight==="textbf")return"bold-italic";if(t.fontShape==="textit")return"italic";if(t.fontWeight==="textbf")return"bold";var r=t.font;if(!r||r==="mathnormal")return null;var n=e.mode;if(r==="mathit")return"italic";if(r==="boldsymbol")return e.type==="textord"?"bold":"bold-italic";if(r==="mathbf")return"bold";if(r==="mathbb")return"double-struck";if(r==="mathfrak")return"fraktur";if(r==="mathscr"||r==="mathcal")return"script";if(r==="mathsf")return"sans-serif";if(r==="mathtt")return"monospace";var i=e.text;if(_.contains(["\\imath","\\jmath"],i))return null;re[n][i]&&re[n][i].replace&&(i=re[n][i].replace);var s=D.fontMap[r].fontName;return qr(i,s,n)?D.fontMap[r].variant:null},Se=function(e,t,r){if(e.length===1){var n=te(e[0],t);return r&&n instanceof Be&&n.type==="mo"&&(n.setAttribute("lspace","0em"),n.setAttribute("rspace","0em")),[n]}for(var i=[],s,o=0;o<e.length;o++){var h=te(e[o],t);if(h instanceof Be&&s instanceof Be){if(h.type==="mtext"&&s.type==="mtext"&&h.getAttribute("mathvariant")===s.getAttribute("mathvariant")){s.children.push(...h.children);continue}else if(h.type==="mn"&&s.type==="mn"){s.children.push(...h.children);continue}else if(h.type==="mi"&&h.children.length===1&&s.type==="mn"){var d=h.children[0];if(d instanceof rt&&d.text==="."){s.children.push(...h.children);continue}}else if(s.type==="mi"&&s.children.length===1){var p=s.children[0];if(p instanceof rt&&p.text==="̸"&&(h.type==="mo"||h.type==="mi"||h.type==="mn")){var g=h.children[0];g instanceof rt&&g.text.length>0&&(g.text=g.text.slice(0,1)+"̸"+g.text.slice(1),i.pop())}}}i.push(h),s=h}return i},D0=function(e,t,r){return $r(Se(e,t,r))},te=function(e,t){if(!e)return new F.MathNode("mrow");if(Lt[e.type]){var r=Lt[e.type](e,t);return r}else throw new T("Got group of unknown type: '"+e.type+"'")};function Va(a,e,t,r,n){var i=Se(a,t),s;i.length===1&&i[0]instanceof Be&&_.contains(["mrow","mtable"],i[0].type)?s=i[0]:s=new F.MathNode("mrow",i);var o=new F.MathNode("annotation",[new F.TextNode(e)]);o.setAttribute("encoding","application/x-tex");var h=new F.MathNode("semantics",[s,o]),d=new F.MathNode("math",[h]);d.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),r&&d.setAttribute("display","block");var p=n?"katex":"katex-mathml";return D.makeSpan([p],[d])}var Yn=function(e){return new h0({style:e.displayMode?P.DISPLAY:P.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},Xn=function(e,t){if(t.displayMode){var r=["katex-display"];t.leqno&&r.push("leqno"),t.fleqn&&r.push("fleqn"),e=D.makeSpan(r,[e])}return e},D1=function(e,t,r){var n=Yn(r),i;if(r.output==="mathml")return Va(e,t,n,r.displayMode,!0);if(r.output==="html"){var s=Br(e,n);i=D.makeSpan(["katex"],[s])}else{var o=Va(e,t,n,r.displayMode,!1),h=Br(e,n);i=D.makeSpan(["katex"],[o,h])}return Xn(i,r)},k1=function(e,t,r){var n=Yn(r),i=Br(e,n),s=D.makeSpan(["katex"],[i]);return Xn(s,r)},A1={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},S1=function(e){var t=new F.MathNode("mo",[new F.TextNode(A1[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},F1={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},E1=function(e){return e.type==="ordgroup"?e.body.length:1},T1=function(e,t){function r(){var o=4e5,h=e.label.slice(1);if(_.contains(["widehat","widecheck","widetilde","utilde"],h)){var d=e,p=E1(d.base),g,x,w;if(p>5)h==="widehat"||h==="widecheck"?(g=420,o=2364,w=.42,x=h+"4"):(g=312,o=2340,w=.34,x="tilde4");else{var S=[1,1,2,2,3,3][p];h==="widehat"||h==="widecheck"?(o=[0,1062,2364,2364,2364][S],g=[0,239,300,360,420][S],w=[0,.24,.3,.3,.36,.42][S],x=h+S):(o=[0,600,1033,2339,2340][S],g=[0,260,286,306,312][S],w=[0,.26,.286,.3,.306,.34][S],x="tilde"+S)}var E=new w0(x),z=new d0([E],{width:"100%",height:M(w),viewBox:"0 0 "+o+" "+g,preserveAspectRatio:"none"});return{span:D.makeSvgSpan([],[z],t),minWidth:0,height:w}}else{var k=[],L=F1[h],[I,G,$]=L,X=$/1e3,W=I.length,ae,Z;if(W===1){var me=L[3];ae=["hide-tail"],Z=[me]}else if(W===2)ae=["halfarrow-left","halfarrow-right"],Z=["xMinYMin","xMaxYMin"];else if(W===3)ae=["brace-left","brace-center","brace-right"],Z=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+W+" children.");for(var pe=0;pe<W;pe++){var ie=new w0(I[pe]),_e=new d0([ie],{width:"400em",height:M(X),viewBox:"0 0 "+o+" "+$,preserveAspectRatio:Z[pe]+" slice"}),be=D.makeSvgSpan([ae[pe]],[_e],t);if(W===1)return{span:be,minWidth:G,height:X};be.style.height=M(X),k.push(be)}return{span:D.makeSpan(["stretchy"],k,t),minWidth:G,height:X}}}var{span:n,minWidth:i,height:s}=r();return n.height=s,n.style.height=M(s),i>0&&(n.style.minWidth=M(i)),n},C1=function(e,t,r,n,i){var s,o=e.height+e.depth+r+n;if(/fbox|color|angl/.test(t)){if(s=D.makeSpan(["stretchy",t],[],i),t==="fbox"){var h=i.color&&i.getColor();h&&(s.style.borderColor=h)}}else{var d=[];/^[bx]cancel$/.test(t)&&d.push(new Cr({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&d.push(new Cr({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var p=new d0(d,{width:"100%",height:M(o)});s=D.makeSvgSpan([],[p],i)}return s.height=o,s.style.height=M(o),s},f0={encloseSpan:C1,mathMLnode:S1,svgSpan:T1};function U(a,e){if(!a||a.type!==e)throw new Error("Expected node of type "+e+", but got "+(a?"node of type "+a.type:String(a)));return a}function Gr(a){var e=Gt(a);if(!e)throw new Error("Expected node of symbol group type, but got "+(a?"node of type "+a.type:String(a)));return e}function Gt(a){return a&&(a.type==="atom"||e1.hasOwnProperty(a.type))?a:null}var Vr=(a,e)=>{var t,r,n;a&&a.type==="supsub"?(r=U(a.base,"accent"),t=r.base,a.base=t,n=Qs(Y(a,e)),a.base=r):(r=U(a,"accent"),t=r.base);var i=Y(t,e.havingCrampedStyle()),s=r.isShifty&&_.isCharacterBox(t),o=0;if(s){var h=_.getBaseElem(t),d=Y(h,e.havingCrampedStyle());o=_a(d).skew}var p=r.label==="\\c",g=p?i.height+i.depth:Math.min(i.height,e.fontMetrics().xHeight),x;if(r.isStretchy)x=f0.svgSpan(r,e),x=D.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:i},{type:"elem",elem:x,wrapperClasses:["svg-align"],wrapperStyle:o>0?{width:"calc(100% - "+M(2*o)+")",marginLeft:M(2*o)}:void 0}]},e);else{var w,S;r.label==="\\vec"?(w=D.staticSvg("vec",e),S=D.svgData.vec[1]):(w=D.makeOrd({mode:r.mode,text:r.label},e,"textord"),w=_a(w),w.italic=0,S=w.width,p&&(g+=w.depth)),x=D.makeSpan(["accent-body"],[w]);var E=r.label==="\\textcircled";E&&(x.classes.push("accent-full"),g=i.height);var z=o;E||(z-=S/2),x.style.left=M(z),r.label==="\\textcircled"&&(x.style.top=".2em"),x=D.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:i},{type:"kern",size:-g},{type:"elem",elem:x}]},e)}var k=D.makeSpan(["mord","accent"],[x],e);return n?(n.children[0]=k,n.height=Math.max(k.height,n.height),n.classes[0]="mord",n):k},Zn=(a,e)=>{var t=a.isStretchy?f0.mathMLnode(a.label):new F.MathNode("mo",[Le(a.label,a.mode)]),r=new F.MathNode("mover",[te(a.base,e),t]);return r.setAttribute("accent","true"),r},M1=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(a=>"\\"+a).join("|"));B({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(a,e)=>{var t=_t(e[0]),r=!M1.test(a.funcName),n=!r||a.funcName==="\\widehat"||a.funcName==="\\widetilde"||a.funcName==="\\widecheck";return{type:"accent",mode:a.parser.mode,label:a.funcName,isStretchy:r,isShifty:n,base:t}},htmlBuilder:Vr,mathmlBuilder:Zn});B({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(a,e)=>{var t=e[0],r=a.parser.mode;return r==="math"&&(a.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+a.funcName+" works only in text mode"),r="text"),{type:"accent",mode:r,label:a.funcName,isStretchy:!1,isShifty:!0,base:t}},htmlBuilder:Vr,mathmlBuilder:Zn});B({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(a,e)=>{var{parser:t,funcName:r}=a,n=e[0];return{type:"accentUnder",mode:t.mode,label:r,base:n}},htmlBuilder:(a,e)=>{var t=Y(a.base,e),r=f0.svgSpan(a,e),n=a.label==="\\utilde"?.12:0,i=D.makeVList({positionType:"top",positionData:t.height,children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:n},{type:"elem",elem:t}]},e);return D.makeSpan(["mord","accentunder"],[i],e)},mathmlBuilder:(a,e)=>{var t=f0.mathMLnode(a.label),r=new F.MathNode("munder",[te(a.base,e),t]);return r.setAttribute("accentunder","true"),r}});var At=a=>{var e=new F.MathNode("mpadded",a?[a]:[]);return e.setAttribute("width","+0.6em"),e.setAttribute("lspace","0.3em"),e};B({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(a,e,t){var{parser:r,funcName:n}=a;return{type:"xArrow",mode:r.mode,label:n,body:e[0],below:t[0]}},htmlBuilder(a,e){var t=e.style,r=e.havingStyle(t.sup()),n=D.wrapFragment(Y(a.body,r,e),e),i=a.label.slice(0,2)==="\\x"?"x":"cd";n.classes.push(i+"-arrow-pad");var s;a.below&&(r=e.havingStyle(t.sub()),s=D.wrapFragment(Y(a.below,r,e),e),s.classes.push(i+"-arrow-pad"));var o=f0.svgSpan(a,e),h=-e.fontMetrics().axisHeight+.5*o.height,d=-e.fontMetrics().axisHeight-.5*o.height-.111;(n.depth>.25||a.label==="\\xleftequilibrium")&&(d-=n.depth);var p;if(s){var g=-e.fontMetrics().axisHeight+s.height+.5*o.height+.111;p=D.makeVList({positionType:"individualShift",children:[{type:"elem",elem:n,shift:d},{type:"elem",elem:o,shift:h},{type:"elem",elem:s,shift:g}]},e)}else p=D.makeVList({positionType:"individualShift",children:[{type:"elem",elem:n,shift:d},{type:"elem",elem:o,shift:h}]},e);return p.children[0].children[0].children[1].classes.push("svg-align"),D.makeSpan(["mrel","x-arrow"],[p],e)},mathmlBuilder(a,e){var t=f0.mathMLnode(a.label);t.setAttribute("minsize",a.label.charAt(0)==="x"?"1.75em":"3.0em");var r;if(a.body){var n=At(te(a.body,e));if(a.below){var i=At(te(a.below,e));r=new F.MathNode("munderover",[t,i,n])}else r=new F.MathNode("mover",[t,n])}else if(a.below){var s=At(te(a.below,e));r=new F.MathNode("munder",[t,s])}else r=At(),r=new F.MathNode("mover",[t,r]);return r}});var z1=D.makeSpan;function jn(a,e){var t=fe(a.body,e,!0);return z1([a.mclass],t,e)}function Kn(a,e){var t,r=Se(a.body,e);return a.mclass==="minner"?t=new F.MathNode("mpadded",r):a.mclass==="mord"?a.isCharacterBox?(t=r[0],t.type="mi"):t=new F.MathNode("mi",r):(a.isCharacterBox?(t=r[0],t.type="mo"):t=new F.MathNode("mo",r),a.mclass==="mbin"?(t.attributes.lspace="0.22em",t.attributes.rspace="0.22em"):a.mclass==="mpunct"?(t.attributes.lspace="0em",t.attributes.rspace="0.17em"):a.mclass==="mopen"||a.mclass==="mclose"?(t.attributes.lspace="0em",t.attributes.rspace="0em"):a.mclass==="minner"&&(t.attributes.lspace="0.0556em",t.attributes.width="+0.1111em")),t}B({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(a,e){var{parser:t,funcName:r}=a,n=e[0];return{type:"mclass",mode:t.mode,mclass:"m"+r.slice(5),body:ce(n),isCharacterBox:_.isCharacterBox(n)}},htmlBuilder:jn,mathmlBuilder:Kn});var Vt=a=>{var e=a.type==="ordgroup"&&a.body.length?a.body[0]:a;return e.type==="atom"&&(e.family==="bin"||e.family==="rel")?"m"+e.family:"mord"};B({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(a,e){var{parser:t}=a;return{type:"mclass",mode:t.mode,mclass:Vt(e[0]),body:ce(e[1]),isCharacterBox:_.isCharacterBox(e[1])}}});B({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(a,e){var{parser:t,funcName:r}=a,n=e[1],i=e[0],s;r!=="\\stackrel"?s=Vt(n):s="mrel";var o={type:"op",mode:n.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:r!=="\\stackrel",body:ce(n)},h={type:"supsub",mode:i.mode,base:o,sup:r==="\\underset"?null:i,sub:r==="\\underset"?i:null};return{type:"mclass",mode:t.mode,mclass:s,body:[h],isCharacterBox:_.isCharacterBox(h)}},htmlBuilder:jn,mathmlBuilder:Kn});B({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(a,e){var{parser:t}=a;return{type:"pmb",mode:t.mode,mclass:Vt(e[0]),body:ce(e[0])}},htmlBuilder(a,e){var t=fe(a.body,e,!0),r=D.makeSpan([a.mclass],t,e);return r.style.textShadow="0.02em 0.01em 0.04px",r},mathmlBuilder(a,e){var t=Se(a.body,e),r=new F.MathNode("mstyle",t);return r.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),r}});var B1={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},Wa=()=>({type:"styling",body:[],mode:"math",style:"display"}),Ya=a=>a.type==="textord"&&a.text==="@",R1=(a,e)=>(a.type==="mathord"||a.type==="atom")&&a.text===e;function N1(a,e,t){var r=B1[a];switch(r){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return t.callFunction(r,[e[0]],[e[1]]);case"\\uparrow":case"\\downarrow":{var n=t.callFunction("\\\\cdleft",[e[0]],[]),i={type:"atom",text:r,mode:"math",family:"rel"},s=t.callFunction("\\Big",[i],[]),o=t.callFunction("\\\\cdright",[e[1]],[]),h={type:"ordgroup",mode:"math",body:[n,s,o]};return t.callFunction("\\\\cdparent",[h],[])}case"\\\\cdlongequal":return t.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{var d={type:"textord",text:"\\Vert",mode:"math"};return t.callFunction("\\Big",[d],[])}default:return{type:"textord",text:" ",mode:"math"}}}function I1(a){var e=[];for(a.gullet.beginGroup(),a.gullet.macros.set("\\cr","\\\\\\relax"),a.gullet.beginGroup();;){e.push(a.parseExpression(!1,"\\\\")),a.gullet.endGroup(),a.gullet.beginGroup();var t=a.fetch().text;if(t==="&"||t==="\\\\")a.consume();else if(t==="\\end"){e[e.length-1].length===0&&e.pop();break}else throw new T("Expected \\\\ or \\cr or \\end",a.nextToken)}for(var r=[],n=[r],i=0;i<e.length;i++){for(var s=e[i],o=Wa(),h=0;h<s.length;h++)if(!Ya(s[h]))o.body.push(s[h]);else{r.push(o),h+=1;var d=Gr(s[h]).text,p=new Array(2);if(p[0]={type:"ordgroup",mode:"math",body:[]},p[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(d)>-1))if("<>AV".indexOf(d)>-1)for(var g=0;g<2;g++){for(var x=!0,w=h+1;w<s.length;w++){if(R1(s[w],d)){x=!1,h=w;break}if(Ya(s[w]))throw new T("Missing a "+d+" character to complete a CD arrow.",s[w]);p[g].body.push(s[w])}if(x)throw new T("Missing a "+d+" character to complete a CD arrow.",s[h])}else throw new T('Expected one of "<>AV=|." after @',s[h]);var S=N1(d,p,a),E={type:"styling",body:[S],mode:"math",style:"display"};r.push(E),o=Wa()}i%2===0?r.push(o):r.shift(),r=[],n.push(r)}a.gullet.endGroup(),a.gullet.endGroup();var z=new Array(n[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:n,arraystretch:1,addJot:!0,rowGaps:[null],cols:z,colSeparationType:"CD",hLinesBeforeRow:new Array(n.length+1).fill([])}}B({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(a,e){var{parser:t,funcName:r}=a;return{type:"cdlabel",mode:t.mode,side:r.slice(4),label:e[0]}},htmlBuilder(a,e){var t=e.havingStyle(e.style.sup()),r=D.wrapFragment(Y(a.label,t,e),e);return r.classes.push("cd-label-"+a.side),r.style.bottom=M(.8-r.depth),r.height=0,r.depth=0,r},mathmlBuilder(a,e){var t=new F.MathNode("mrow",[te(a.label,e)]);return t=new F.MathNode("mpadded",[t]),t.setAttribute("width","0"),a.side==="left"&&t.setAttribute("lspace","-1width"),t.setAttribute("voffset","0.7em"),t=new F.MathNode("mstyle",[t]),t.setAttribute("displaystyle","false"),t.setAttribute("scriptlevel","1"),t}});B({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(a,e){var{parser:t}=a;return{type:"cdlabelparent",mode:t.mode,fragment:e[0]}},htmlBuilder(a,e){var t=D.wrapFragment(Y(a.fragment,e),e);return t.classes.push("cd-vert-arrow"),t},mathmlBuilder(a,e){return new F.MathNode("mrow",[te(a.fragment,e)])}});B({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(a,e){for(var{parser:t}=a,r=U(e[0],"ordgroup"),n=r.body,i="",s=0;s<n.length;s++){var o=U(n[s],"textord");i+=o.text}var h=parseInt(i),d;if(isNaN(h))throw new T("\\@char has non-numeric argument "+i);if(h<0||h>=1114111)throw new T("\\@char with invalid code point "+i);return h<=65535?d=String.fromCharCode(h):(h-=65536,d=String.fromCharCode((h>>10)+55296,(h&1023)+56320)),{type:"textord",mode:t.mode,text:d}}});var Qn=(a,e)=>{var t=fe(a.body,e.withColor(a.color),!1);return D.makeFragment(t)},Jn=(a,e)=>{var t=Se(a.body,e.withColor(a.color)),r=new F.MathNode("mstyle",t);return r.setAttribute("mathcolor",a.color),r};B({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(a,e){var{parser:t}=a,r=U(e[0],"color-token").color,n=e[1];return{type:"color",mode:t.mode,color:r,body:ce(n)}},htmlBuilder:Qn,mathmlBuilder:Jn});B({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(a,e){var{parser:t,breakOnTokenText:r}=a,n=U(e[0],"color-token").color;t.gullet.macros.set("\\current@color",n);var i=t.parseExpression(!0,r);return{type:"color",mode:t.mode,color:n,body:i}},htmlBuilder:Qn,mathmlBuilder:Jn});B({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(a,e,t){var{parser:r}=a,n=r.gullet.future().text==="["?r.parseSizeGroup(!0):null,i=!r.settings.displayMode||!r.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:r.mode,newLine:i,size:n&&U(n,"size").value}},htmlBuilder(a,e){var t=D.makeSpan(["mspace"],[],e);return a.newLine&&(t.classes.push("newline"),a.size&&(t.style.marginTop=M(ue(a.size,e)))),t},mathmlBuilder(a,e){var t=new F.MathNode("mspace");return a.newLine&&(t.setAttribute("linebreak","newline"),a.size&&t.setAttribute("height",M(ue(a.size,e)))),t}});var Rr={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},ei=a=>{var e=a.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(e))throw new T("Expected a control sequence",a);return e},O1=a=>{var e=a.gullet.popToken();return e.text==="="&&(e=a.gullet.popToken(),e.text===" "&&(e=a.gullet.popToken())),e},ti=(a,e,t,r)=>{var n=a.gullet.macros.get(t.text);n==null&&(t.noexpand=!0,n={tokens:[t],numArgs:0,unexpandable:!a.gullet.isExpandable(t.text)}),a.gullet.macros.set(e,n,r)};B({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(a){var{parser:e,funcName:t}=a;e.consumeSpaces();var r=e.fetch();if(Rr[r.text])return(t==="\\global"||t==="\\\\globallong")&&(r.text=Rr[r.text]),U(e.parseFunction(),"internal");throw new T("Invalid token after macro prefix",r)}});B({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(a){var{parser:e,funcName:t}=a,r=e.gullet.popToken(),n=r.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(n))throw new T("Expected a control sequence",r);for(var i=0,s,o=[[]];e.gullet.future().text!=="{";)if(r=e.gullet.popToken(),r.text==="#"){if(e.gullet.future().text==="{"){s=e.gullet.future(),o[i].push("{");break}if(r=e.gullet.popToken(),!/^[1-9]$/.test(r.text))throw new T('Invalid argument number "'+r.text+'"');if(parseInt(r.text)!==i+1)throw new T('Argument number "'+r.text+'" out of order');i++,o.push([])}else{if(r.text==="EOF")throw new T("Expected a macro definition");o[i].push(r.text)}var{tokens:h}=e.gullet.consumeArg();return s&&h.unshift(s),(t==="\\edef"||t==="\\xdef")&&(h=e.gullet.expandTokens(h),h.reverse()),e.gullet.macros.set(n,{tokens:h,numArgs:i,delimiters:o},t===Rr[t]),{type:"internal",mode:e.mode}}});B({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(a){var{parser:e,funcName:t}=a,r=ei(e.gullet.popToken());e.gullet.consumeSpaces();var n=O1(e);return ti(e,r,n,t==="\\\\globallet"),{type:"internal",mode:e.mode}}});B({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(a){var{parser:e,funcName:t}=a,r=ei(e.gullet.popToken()),n=e.gullet.popToken(),i=e.gullet.popToken();return ti(e,r,i,t==="\\\\globalfuture"),e.gullet.pushToken(i),e.gullet.pushToken(n),{type:"internal",mode:e.mode}}});var tt=function(e,t,r){var n=re.math[e]&&re.math[e].replace,i=qr(n||e,t,r);if(!i)throw new Error("Unsupported symbol "+e+" and font size "+t+".");return i},Wr=function(e,t,r,n){var i=r.havingBaseStyle(t),s=D.makeSpan(n.concat(i.sizingClasses(r)),[e],r),o=i.sizeMultiplier/r.sizeMultiplier;return s.height*=o,s.depth*=o,s.maxFontSize=i.sizeMultiplier,s},ri=function(e,t,r){var n=t.havingBaseStyle(r),i=(1-t.sizeMultiplier/n.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=M(i),e.height-=i,e.depth+=i},L1=function(e,t,r,n,i,s){var o=D.makeSymbol(e,"Main-Regular",i,n),h=Wr(o,t,n,s);return r&&ri(h,n,t),h},_1=function(e,t,r,n){return D.makeSymbol(e,"Size"+t+"-Regular",r,n)},ai=function(e,t,r,n,i,s){var o=_1(e,t,i,n),h=Wr(D.makeSpan(["delimsizing","size"+t],[o],n),P.TEXT,n,s);return r&&ri(h,n,P.TEXT),h},fr=function(e,t,r){var n;t==="Size1-Regular"?n="delim-size1":n="delim-size4";var i=D.makeSpan(["delimsizinginner",n],[D.makeSpan([],[D.makeSymbol(e,t,r)])]);return{type:"elem",elem:i}},vr=function(e,t,r){var n=je["Size4-Regular"][e.charCodeAt(0)]?je["Size4-Regular"][e.charCodeAt(0)][4]:je["Size1-Regular"][e.charCodeAt(0)][4],i=new w0("inner",Gs(e,Math.round(1e3*t))),s=new d0([i],{width:M(n),height:M(t),style:"width:"+M(n),viewBox:"0 0 "+1e3*n+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),o=D.makeSvgSpan([],[s],r);return o.height=t,o.style.height=M(t),o.style.width=M(n),{type:"elem",elem:o}},Nr=.008,St={type:"kern",size:-1*Nr},q1=["|","\\lvert","\\rvert","\\vert"],P1=["\\|","\\lVert","\\rVert","\\Vert"],ni=function(e,t,r,n,i,s){var o,h,d,p,g="",x=0;o=d=p=e,h=null;var w="Size1-Regular";e==="\\uparrow"?d=p="⏐":e==="\\Uparrow"?d=p="‖":e==="\\downarrow"?o=d="⏐":e==="\\Downarrow"?o=d="‖":e==="\\updownarrow"?(o="\\uparrow",d="⏐",p="\\downarrow"):e==="\\Updownarrow"?(o="\\Uparrow",d="‖",p="\\Downarrow"):_.contains(q1,e)?(d="∣",g="vert",x=333):_.contains(P1,e)?(d="∥",g="doublevert",x=556):e==="["||e==="\\lbrack"?(o="⎡",d="⎢",p="⎣",w="Size4-Regular",g="lbrack",x=667):e==="]"||e==="\\rbrack"?(o="⎤",d="⎥",p="⎦",w="Size4-Regular",g="rbrack",x=667):e==="\\lfloor"||e==="⌊"?(d=o="⎢",p="⎣",w="Size4-Regular",g="lfloor",x=667):e==="\\lceil"||e==="⌈"?(o="⎡",d=p="⎢",w="Size4-Regular",g="lceil",x=667):e==="\\rfloor"||e==="⌋"?(d=o="⎥",p="⎦",w="Size4-Regular",g="rfloor",x=667):e==="\\rceil"||e==="⌉"?(o="⎤",d=p="⎥",w="Size4-Regular",g="rceil",x=667):e==="("||e==="\\lparen"?(o="⎛",d="⎜",p="⎝",w="Size4-Regular",g="lparen",x=875):e===")"||e==="\\rparen"?(o="⎞",d="⎟",p="⎠",w="Size4-Regular",g="rparen",x=875):e==="\\{"||e==="\\lbrace"?(o="⎧",h="⎨",p="⎩",d="⎪",w="Size4-Regular"):e==="\\}"||e==="\\rbrace"?(o="⎫",h="⎬",p="⎭",d="⎪",w="Size4-Regular"):e==="\\lgroup"||e==="⟮"?(o="⎧",p="⎩",d="⎪",w="Size4-Regular"):e==="\\rgroup"||e==="⟯"?(o="⎫",p="⎭",d="⎪",w="Size4-Regular"):e==="\\lmoustache"||e==="⎰"?(o="⎧",p="⎭",d="⎪",w="Size4-Regular"):(e==="\\rmoustache"||e==="⎱")&&(o="⎫",p="⎩",d="⎪",w="Size4-Regular");var S=tt(o,w,i),E=S.height+S.depth,z=tt(d,w,i),k=z.height+z.depth,L=tt(p,w,i),I=L.height+L.depth,G=0,$=1;if(h!==null){var X=tt(h,w,i);G=X.height+X.depth,$=2}var W=E+I+G,ae=Math.max(0,Math.ceil((t-W)/($*k))),Z=W+ae*$*k,me=n.fontMetrics().axisHeight;r&&(me*=n.sizeMultiplier);var pe=Z/2-me,ie=[];if(g.length>0){var _e=Z-E-I,be=Math.round(Z*1e3),Fe=Vs(g,Math.round(_e*1e3)),Ge=new w0(g,Fe),r0=(x/1e3).toFixed(3)+"em",a0=(be/1e3).toFixed(3)+"em",oe=new d0([Ge],{width:r0,height:a0,viewBox:"0 0 "+x+" "+be}),qe=D.makeSvgSpan([],[oe],n);qe.height=be/1e3,qe.style.width=r0,qe.style.height=a0,ie.push({type:"elem",elem:qe})}else{if(ie.push(fr(p,w,i)),ie.push(St),h===null){var J=Z-E-I+2*Nr;ie.push(vr(d,J,n))}else{var ye=(Z-E-I-G)/2+2*Nr;ie.push(vr(d,ye,n)),ie.push(St),ie.push(fr(h,w,i)),ie.push(St),ie.push(vr(d,ye,n))}ie.push(St),ie.push(fr(o,w,i))}var ee=n.havingBaseStyle(P.TEXT),n0=D.makeVList({positionType:"bottom",positionData:pe,children:ie},ee);return Wr(D.makeSpan(["delimsizing","mult"],[n0],ee),P.TEXT,n,s)},gr=80,br=.08,yr=function(e,t,r,n,i){var s=Us(e,n,r),o=new w0(e,s),h=new d0([o],{width:"400em",height:M(t),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return D.makeSvgSpan(["hide-tail"],[h],i)},H1=function(e,t){var r=t.havingBaseSizing(),n=ui("\\surd",e*r.sizeMultiplier,li,r),i=r.sizeMultiplier,s=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),o,h=0,d=0,p=0,g;return n.type==="small"?(p=1e3+1e3*s+gr,e<1?i=1:e<1.4&&(i=.7),h=(1+s+br)/i,d=(1+s)/i,o=yr("sqrtMain",h,p,s,t),o.style.minWidth="0.853em",g=.833/i):n.type==="large"?(p=(1e3+gr)*at[n.size],d=(at[n.size]+s)/i,h=(at[n.size]+s+br)/i,o=yr("sqrtSize"+n.size,h,p,s,t),o.style.minWidth="1.02em",g=1/i):(h=e+s+br,d=e+s,p=Math.floor(1e3*e+s)+gr,o=yr("sqrtTall",h,p,s,t),o.style.minWidth="0.742em",g=1.056),o.height=d,o.style.height=M(h),{span:o,advanceWidth:g,ruleWidth:(t.fontMetrics().sqrtRuleThickness+s)*i}},ii=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],$1=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],si=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],at=[0,1.2,1.8,2.4,3],U1=function(e,t,r,n,i){if(e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle"),_.contains(ii,e)||_.contains(si,e))return ai(e,t,!1,r,n,i);if(_.contains($1,e))return ni(e,at[t],!1,r,n,i);throw new T("Illegal delimiter: '"+e+"'")},G1=[{type:"small",style:P.SCRIPTSCRIPT},{type:"small",style:P.SCRIPT},{type:"small",style:P.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],V1=[{type:"small",style:P.SCRIPTSCRIPT},{type:"small",style:P.SCRIPT},{type:"small",style:P.TEXT},{type:"stack"}],li=[{type:"small",style:P.SCRIPTSCRIPT},{type:"small",style:P.SCRIPT},{type:"small",style:P.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],W1=function(e){if(e.type==="small")return"Main-Regular";if(e.type==="large")return"Size"+e.size+"-Regular";if(e.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+e.type+"' here.")},ui=function(e,t,r,n){for(var i=Math.min(2,3-n.style.size),s=i;s<r.length&&r[s].type!=="stack";s++){var o=tt(e,W1(r[s]),"math"),h=o.height+o.depth;if(r[s].type==="small"){var d=n.havingBaseStyle(r[s].style);h*=d.sizeMultiplier}if(h>t)return r[s]}return r[r.length-1]},oi=function(e,t,r,n,i,s){e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle");var o;_.contains(si,e)?o=G1:_.contains(ii,e)?o=li:o=V1;var h=ui(e,t,o,n);return h.type==="small"?L1(e,h.style,r,n,i,s):h.type==="large"?ai(e,h.size,r,n,i,s):ni(e,t,r,n,i,s)},Y1=function(e,t,r,n,i,s){var o=n.fontMetrics().axisHeight*n.sizeMultiplier,h=901,d=5/n.fontMetrics().ptPerEm,p=Math.max(t-o,r+o),g=Math.max(p/500*h,2*p-d);return oi(e,g,!0,n,i,s)},m0={sqrtImage:H1,sizedDelim:U1,sizeToMaxHeight:at,customSizedDelim:oi,leftRightDelim:Y1},Xa={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},X1=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function Wt(a,e){var t=Gt(a);if(t&&_.contains(X1,t.text))return t;throw t?new T("Invalid delimiter '"+t.text+"' after '"+e.funcName+"'",a):new T("Invalid delimiter type '"+a.type+"'",a)}B({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(a,e)=>{var t=Wt(e[0],a);return{type:"delimsizing",mode:a.parser.mode,size:Xa[a.funcName].size,mclass:Xa[a.funcName].mclass,delim:t.text}},htmlBuilder:(a,e)=>a.delim==="."?D.makeSpan([a.mclass]):m0.sizedDelim(a.delim,a.size,e,a.mode,[a.mclass]),mathmlBuilder:a=>{var e=[];a.delim!=="."&&e.push(Le(a.delim,a.mode));var t=new F.MathNode("mo",e);a.mclass==="mopen"||a.mclass==="mclose"?t.setAttribute("fence","true"):t.setAttribute("fence","false"),t.setAttribute("stretchy","true");var r=M(m0.sizeToMaxHeight[a.size]);return t.setAttribute("minsize",r),t.setAttribute("maxsize",r),t}});function Za(a){if(!a.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}B({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(a,e)=>{var t=a.parser.gullet.macros.get("\\current@color");if(t&&typeof t!="string")throw new T("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:a.parser.mode,delim:Wt(e[0],a).text,color:t}}});B({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(a,e)=>{var t=Wt(e[0],a),r=a.parser;++r.leftrightDepth;var n=r.parseExpression(!1);--r.leftrightDepth,r.expect("\\right",!1);var i=U(r.parseFunction(),"leftright-right");return{type:"leftright",mode:r.mode,body:n,left:t.text,right:i.delim,rightColor:i.color}},htmlBuilder:(a,e)=>{Za(a);for(var t=fe(a.body,e,!0,["mopen","mclose"]),r=0,n=0,i=!1,s=0;s<t.length;s++)t[s].isMiddle?i=!0:(r=Math.max(t[s].height,r),n=Math.max(t[s].depth,n));r*=e.sizeMultiplier,n*=e.sizeMultiplier;var o;if(a.left==="."?o=st(e,["mopen"]):o=m0.leftRightDelim(a.left,r,n,e,a.mode,["mopen"]),t.unshift(o),i)for(var h=1;h<t.length;h++){var d=t[h],p=d.isMiddle;p&&(t[h]=m0.leftRightDelim(p.delim,r,n,p.options,a.mode,[]))}var g;if(a.right===".")g=st(e,["mclose"]);else{var x=a.rightColor?e.withColor(a.rightColor):e;g=m0.leftRightDelim(a.right,r,n,x,a.mode,["mclose"])}return t.push(g),D.makeSpan(["minner"],t,e)},mathmlBuilder:(a,e)=>{Za(a);var t=Se(a.body,e);if(a.left!=="."){var r=new F.MathNode("mo",[Le(a.left,a.mode)]);r.setAttribute("fence","true"),t.unshift(r)}if(a.right!=="."){var n=new F.MathNode("mo",[Le(a.right,a.mode)]);n.setAttribute("fence","true"),a.rightColor&&n.setAttribute("mathcolor",a.rightColor),t.push(n)}return $r(t)}});B({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(a,e)=>{var t=Wt(e[0],a);if(!a.parser.leftrightDepth)throw new T("\\middle without preceding \\left",t);return{type:"middle",mode:a.parser.mode,delim:t.text}},htmlBuilder:(a,e)=>{var t;if(a.delim===".")t=st(e,[]);else{t=m0.sizedDelim(a.delim,1,e,a.mode,[]);var r={delim:a.delim,options:e};t.isMiddle=r}return t},mathmlBuilder:(a,e)=>{var t=a.delim==="\\vert"||a.delim==="|"?Le("|","text"):Le(a.delim,a.mode),r=new F.MathNode("mo",[t]);return r.setAttribute("fence","true"),r.setAttribute("lspace","0.05em"),r.setAttribute("rspace","0.05em"),r}});var Yr=(a,e)=>{var t=D.wrapFragment(Y(a.body,e),e),r=a.label.slice(1),n=e.sizeMultiplier,i,s=0,o=_.isCharacterBox(a.body);if(r==="sout")i=D.makeSpan(["stretchy","sout"]),i.height=e.fontMetrics().defaultRuleThickness/n,s=-.5*e.fontMetrics().xHeight;else if(r==="phase"){var h=ue({number:.6,unit:"pt"},e),d=ue({number:.35,unit:"ex"},e),p=e.havingBaseSizing();n=n/p.sizeMultiplier;var g=t.height+t.depth+h+d;t.style.paddingLeft=M(g/2+h);var x=Math.floor(1e3*g*n),w=Hs(x),S=new d0([new w0("phase",w)],{width:"400em",height:M(x/1e3),viewBox:"0 0 400000 "+x,preserveAspectRatio:"xMinYMin slice"});i=D.makeSvgSpan(["hide-tail"],[S],e),i.style.height=M(g),s=t.depth+h+d}else{/cancel/.test(r)?o||t.classes.push("cancel-pad"):r==="angl"?t.classes.push("anglpad"):t.classes.push("boxpad");var E=0,z=0,k=0;/box/.test(r)?(k=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness),E=e.fontMetrics().fboxsep+(r==="colorbox"?0:k),z=E):r==="angl"?(k=Math.max(e.fontMetrics().defaultRuleThickness,e.minRuleThickness),E=4*k,z=Math.max(0,.25-t.depth)):(E=o?.2:0,z=E),i=f0.encloseSpan(t,r,E,z,e),/fbox|boxed|fcolorbox/.test(r)?(i.style.borderStyle="solid",i.style.borderWidth=M(k)):r==="angl"&&k!==.049&&(i.style.borderTopWidth=M(k),i.style.borderRightWidth=M(k)),s=t.depth+z,a.backgroundColor&&(i.style.backgroundColor=a.backgroundColor,a.borderColor&&(i.style.borderColor=a.borderColor))}var L;if(a.backgroundColor)L=D.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:s},{type:"elem",elem:t,shift:0}]},e);else{var I=/cancel|phase/.test(r)?["svg-align"]:[];L=D.makeVList({positionType:"individualShift",children:[{type:"elem",elem:t,shift:0},{type:"elem",elem:i,shift:s,wrapperClasses:I}]},e)}return/cancel/.test(r)&&(L.height=t.height,L.depth=t.depth),/cancel/.test(r)&&!o?D.makeSpan(["mord","cancel-lap"],[L],e):D.makeSpan(["mord"],[L],e)},Xr=(a,e)=>{var t=0,r=new F.MathNode(a.label.indexOf("colorbox")>-1?"mpadded":"menclose",[te(a.body,e)]);switch(a.label){case"\\cancel":r.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":r.setAttribute("notation","downdiagonalstrike");break;case"\\phase":r.setAttribute("notation","phasorangle");break;case"\\sout":r.setAttribute("notation","horizontalstrike");break;case"\\fbox":r.setAttribute("notation","box");break;case"\\angl":r.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(t=e.fontMetrics().fboxsep*e.fontMetrics().ptPerEm,r.setAttribute("width","+"+2*t+"pt"),r.setAttribute("height","+"+2*t+"pt"),r.setAttribute("lspace",t+"pt"),r.setAttribute("voffset",t+"pt"),a.label==="\\fcolorbox"){var n=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness);r.setAttribute("style","border: "+n+"em solid "+String(a.borderColor))}break;case"\\xcancel":r.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return a.backgroundColor&&r.setAttribute("mathbackground",a.backgroundColor),r};B({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(a,e,t){var{parser:r,funcName:n}=a,i=U(e[0],"color-token").color,s=e[1];return{type:"enclose",mode:r.mode,label:n,backgroundColor:i,body:s}},htmlBuilder:Yr,mathmlBuilder:Xr});B({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(a,e,t){var{parser:r,funcName:n}=a,i=U(e[0],"color-token").color,s=U(e[1],"color-token").color,o=e[2];return{type:"enclose",mode:r.mode,label:n,backgroundColor:s,borderColor:i,body:o}},htmlBuilder:Yr,mathmlBuilder:Xr});B({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(a,e){var{parser:t}=a;return{type:"enclose",mode:t.mode,label:"\\fbox",body:e[0]}}});B({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(a,e){var{parser:t,funcName:r}=a,n=e[0];return{type:"enclose",mode:t.mode,label:r,body:n}},htmlBuilder:Yr,mathmlBuilder:Xr});B({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(a,e){var{parser:t}=a;return{type:"enclose",mode:t.mode,label:"\\angl",body:e[0]}}});var hi={};function Je(a){for(var{type:e,names:t,props:r,handler:n,htmlBuilder:i,mathmlBuilder:s}=a,o={type:e,numArgs:r.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:n},h=0;h<t.length;++h)hi[t[h]]=o;i&&(Ot[e]=i),s&&(Lt[e]=s)}var ci={};function m(a,e){ci[a]=e}function ja(a){var e=[];a.consumeSpaces();var t=a.fetch().text;for(t==="\\relax"&&(a.consume(),a.consumeSpaces(),t=a.fetch().text);t==="\\hline"||t==="\\hdashline";)a.consume(),e.push(t==="\\hdashline"),a.consumeSpaces(),t=a.fetch().text;return e}var Yt=a=>{var e=a.parser.settings;if(!e.displayMode)throw new T("{"+a.envName+"} can be used only in display mode.")};function Zr(a){if(a.indexOf("ed")===-1)return a.indexOf("*")===-1}function k0(a,e,t){var{hskipBeforeAndAfter:r,addJot:n,cols:i,arraystretch:s,colSeparationType:o,autoTag:h,singleRow:d,emptySingleRow:p,maxNumCols:g,leqno:x}=e;if(a.gullet.beginGroup(),d||a.gullet.macros.set("\\cr","\\\\\\relax"),!s){var w=a.gullet.expandMacroAsText("\\arraystretch");if(w==null)s=1;else if(s=parseFloat(w),!s||s<0)throw new T("Invalid \\arraystretch: "+w)}a.gullet.beginGroup();var S=[],E=[S],z=[],k=[],L=h!=null?[]:void 0;function I(){h&&a.gullet.macros.set("\\@eqnsw","1",!0)}function G(){L&&(a.gullet.macros.get("\\df@tag")?(L.push(a.subparse([new Ne("\\df@tag")])),a.gullet.macros.set("\\df@tag",void 0,!0)):L.push(!!h&&a.gullet.macros.get("\\@eqnsw")==="1"))}for(I(),k.push(ja(a));;){var $=a.parseExpression(!1,d?"\\end":"\\\\");a.gullet.endGroup(),a.gullet.beginGroup(),$={type:"ordgroup",mode:a.mode,body:$},t&&($={type:"styling",mode:a.mode,style:t,body:[$]}),S.push($);var X=a.fetch().text;if(X==="&"){if(g&&S.length===g){if(d||o)throw new T("Too many tab characters: &",a.nextToken);a.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}a.consume()}else if(X==="\\end"){G(),S.length===1&&$.type==="styling"&&$.body[0].body.length===0&&(E.length>1||!p)&&E.pop(),k.length<E.length+1&&k.push([]);break}else if(X==="\\\\"){a.consume();var W=void 0;a.gullet.future().text!==" "&&(W=a.parseSizeGroup(!0)),z.push(W?W.value:null),G(),k.push(ja(a)),S=[],E.push(S),I()}else throw new T("Expected & or \\\\ or \\cr or \\end",a.nextToken)}return a.gullet.endGroup(),a.gullet.endGroup(),{type:"array",mode:a.mode,addJot:n,arraystretch:s,body:E,cols:i,rowGaps:z,hskipBeforeAndAfter:r,hLinesBeforeRow:k,colSeparationType:o,tags:L,leqno:x}}function jr(a){return a.slice(0,1)==="d"?"display":"text"}var e0=function(e,t){var r,n,i=e.body.length,s=e.hLinesBeforeRow,o=0,h=new Array(i),d=[],p=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),g=1/t.fontMetrics().ptPerEm,x=5*g;if(e.colSeparationType&&e.colSeparationType==="small"){var w=t.havingStyle(P.SCRIPT).sizeMultiplier;x=.2778*(w/t.sizeMultiplier)}var S=e.colSeparationType==="CD"?ue({number:3,unit:"ex"},t):12*g,E=3*g,z=e.arraystretch*S,k=.7*z,L=.3*z,I=0;function G(E0){for(var L0=0;L0<E0.length;++L0)L0>0&&(I+=.25),d.push({pos:I,isDashed:E0[L0]})}for(G(s[0]),r=0;r<e.body.length;++r){var $=e.body[r],X=k,W=L;o<$.length&&(o=$.length);var ae=new Array($.length);for(n=0;n<$.length;++n){var Z=Y($[n],t);W<Z.depth&&(W=Z.depth),X<Z.height&&(X=Z.height),ae[n]=Z}var me=e.rowGaps[r],pe=0;me&&(pe=ue(me,t),pe>0&&(pe+=L,W<pe&&(W=pe),pe=0)),e.addJot&&(W+=E),ae.height=X,ae.depth=W,I+=X,ae.pos=I,I+=W+pe,h[r]=ae,G(s[r+1])}var ie=I/2+t.fontMetrics().axisHeight,_e=e.cols||[],be=[],Fe,Ge,r0=[];if(e.tags&&e.tags.some(E0=>E0))for(r=0;r<i;++r){var a0=h[r],oe=a0.pos-ie,qe=e.tags[r],J=void 0;qe===!0?J=D.makeSpan(["eqn-num"],[],t):qe===!1?J=D.makeSpan([],[],t):J=D.makeSpan([],fe(qe,t,!0),t),J.depth=a0.depth,J.height=a0.height,r0.push({type:"elem",elem:J,shift:oe})}for(n=0,Ge=0;n<o||Ge<_e.length;++n,++Ge){for(var ye=_e[Ge]||{},ee=!0;ye.type==="separator";){if(ee||(Fe=D.makeSpan(["arraycolsep"],[]),Fe.style.width=M(t.fontMetrics().doubleRuleSep),be.push(Fe)),ye.separator==="|"||ye.separator===":"){var n0=ye.separator==="|"?"solid":"dashed",Ve=D.makeSpan(["vertical-separator"],[],t);Ve.style.height=M(I),Ve.style.borderRightWidth=M(p),Ve.style.borderRightStyle=n0,Ve.style.margin="0 "+M(-p/2);var X0=I-ie;X0&&(Ve.style.verticalAlign=M(-X0)),be.push(Ve)}else throw new T("Invalid separator type: "+ye.separator);Ge++,ye=_e[Ge]||{},ee=!1}if(!(n>=o)){var We=void 0;(n>0||e.hskipBeforeAndAfter)&&(We=_.deflt(ye.pregap,x),We!==0&&(Fe=D.makeSpan(["arraycolsep"],[]),Fe.style.width=M(We),be.push(Fe)));var i0=[];for(r=0;r<i;++r){var A0=h[r],Pe=A0[n];if(Pe){var s0=A0.pos-ie;Pe.depth=A0.depth,Pe.height=A0.height,i0.push({type:"elem",elem:Pe,shift:s0})}}i0=D.makeVList({positionType:"individualShift",children:i0},t),i0=D.makeSpan(["col-align-"+(ye.align||"c")],[i0]),be.push(i0),(n<o-1||e.hskipBeforeAndAfter)&&(We=_.deflt(ye.postgap,x),We!==0&&(Fe=D.makeSpan(["arraycolsep"],[]),Fe.style.width=M(We),be.push(Fe)))}}if(h=D.makeSpan(["mtable"],be),d.length>0){for(var Z0=D.makeLineSpan("hline",t,p),j0=D.makeLineSpan("hdashline",t,p),Ye=[{type:"elem",elem:h,shift:0}];d.length>0;){var S0=d.pop(),F0=S0.pos-ie;S0.isDashed?Ye.push({type:"elem",elem:j0,shift:F0}):Ye.push({type:"elem",elem:Z0,shift:F0})}h=D.makeVList({positionType:"individualShift",children:Ye},t)}if(r0.length===0)return D.makeSpan(["mord"],[h],t);var O0=D.makeVList({positionType:"individualShift",children:r0},t);return O0=D.makeSpan(["tag"],[O0],t),D.makeFragment([h,O0])},Z1={c:"center ",l:"left ",r:"right "},t0=function(e,t){for(var r=[],n=new F.MathNode("mtd",[],["mtr-glue"]),i=new F.MathNode("mtd",[],["mml-eqn-num"]),s=0;s<e.body.length;s++){for(var o=e.body[s],h=[],d=0;d<o.length;d++)h.push(new F.MathNode("mtd",[te(o[d],t)]));e.tags&&e.tags[s]&&(h.unshift(n),h.push(n),e.leqno?h.unshift(i):h.push(i)),r.push(new F.MathNode("mtr",h))}var p=new F.MathNode("mtable",r),g=e.arraystretch===.5?.1:.16+e.arraystretch-1+(e.addJot?.09:0);p.setAttribute("rowspacing",M(g));var x="",w="";if(e.cols&&e.cols.length>0){var S=e.cols,E="",z=!1,k=0,L=S.length;S[0].type==="separator"&&(x+="top ",k=1),S[S.length-1].type==="separator"&&(x+="bottom ",L-=1);for(var I=k;I<L;I++)S[I].type==="align"?(w+=Z1[S[I].align],z&&(E+="none "),z=!0):S[I].type==="separator"&&z&&(E+=S[I].separator==="|"?"solid ":"dashed ",z=!1);p.setAttribute("columnalign",w.trim()),/[sd]/.test(E)&&p.setAttribute("columnlines",E.trim())}if(e.colSeparationType==="align"){for(var G=e.cols||[],$="",X=1;X<G.length;X++)$+=X%2?"0em ":"1em ";p.setAttribute("columnspacing",$.trim())}else e.colSeparationType==="alignat"||e.colSeparationType==="gather"?p.setAttribute("columnspacing","0em"):e.colSeparationType==="small"?p.setAttribute("columnspacing","0.2778em"):e.colSeparationType==="CD"?p.setAttribute("columnspacing","0.5em"):p.setAttribute("columnspacing","1em");var W="",ae=e.hLinesBeforeRow;x+=ae[0].length>0?"left ":"",x+=ae[ae.length-1].length>0?"right ":"";for(var Z=1;Z<ae.length-1;Z++)W+=ae[Z].length===0?"none ":ae[Z][0]?"dashed ":"solid ";return/[sd]/.test(W)&&p.setAttribute("rowlines",W.trim()),x!==""&&(p=new F.MathNode("menclose",[p]),p.setAttribute("notation",x.trim())),e.arraystretch&&e.arraystretch<1&&(p=new F.MathNode("mstyle",[p]),p.setAttribute("scriptlevel","1")),p},mi=function(e,t){e.envName.indexOf("ed")===-1&&Yt(e);var r=[],n=e.envName.indexOf("at")>-1?"alignat":"align",i=e.envName==="split",s=k0(e.parser,{cols:r,addJot:!0,autoTag:i?void 0:Zr(e.envName),emptySingleRow:!0,colSeparationType:n,maxNumCols:i?2:void 0,leqno:e.parser.settings.leqno},"display"),o,h=0,d={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&t[0].type==="ordgroup"){for(var p="",g=0;g<t[0].body.length;g++){var x=U(t[0].body[g],"textord");p+=x.text}o=Number(p),h=o*2}var w=!h;s.body.forEach(function(k){for(var L=1;L<k.length;L+=2){var I=U(k[L],"styling"),G=U(I.body[0],"ordgroup");G.body.unshift(d)}if(w)h<k.length&&(h=k.length);else{var $=k.length/2;if(o<$)throw new T("Too many math in a row: "+("expected "+o+", but got "+$),k[0])}});for(var S=0;S<h;++S){var E="r",z=0;S%2===1?E="l":S>0&&w&&(z=1),r[S]={type:"align",align:E,pregap:z,postgap:0}}return s.colSeparationType=w?"align":"alignat",s};Je({type:"array",names:["array","darray"],props:{numArgs:1},handler(a,e){var t=Gt(e[0]),r=t?[e[0]]:U(e[0],"ordgroup").body,n=r.map(function(s){var o=Gr(s),h=o.text;if("lcr".indexOf(h)!==-1)return{type:"align",align:h};if(h==="|")return{type:"separator",separator:"|"};if(h===":")return{type:"separator",separator:":"};throw new T("Unknown column alignment: "+h,s)}),i={cols:n,hskipBeforeAndAfter:!0,maxNumCols:n.length};return k0(a.parser,i,jr(a.envName))},htmlBuilder:e0,mathmlBuilder:t0});Je({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(a){var e={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[a.envName.replace("*","")],t="c",r={hskipBeforeAndAfter:!1,cols:[{type:"align",align:t}]};if(a.envName.charAt(a.envName.length-1)==="*"){var n=a.parser;if(n.consumeSpaces(),n.fetch().text==="["){if(n.consume(),n.consumeSpaces(),t=n.fetch().text,"lcr".indexOf(t)===-1)throw new T("Expected l or c or r",n.nextToken);n.consume(),n.consumeSpaces(),n.expect("]"),n.consume(),r.cols=[{type:"align",align:t}]}}var i=k0(a.parser,r,jr(a.envName)),s=Math.max(0,...i.body.map(o=>o.length));return i.cols=new Array(s).fill({type:"align",align:t}),e?{type:"leftright",mode:a.mode,body:[i],left:e[0],right:e[1],rightColor:void 0}:i},htmlBuilder:e0,mathmlBuilder:t0});Je({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(a){var e={arraystretch:.5},t=k0(a.parser,e,"script");return t.colSeparationType="small",t},htmlBuilder:e0,mathmlBuilder:t0});Je({type:"array",names:["subarray"],props:{numArgs:1},handler(a,e){var t=Gt(e[0]),r=t?[e[0]]:U(e[0],"ordgroup").body,n=r.map(function(s){var o=Gr(s),h=o.text;if("lc".indexOf(h)!==-1)return{type:"align",align:h};throw new T("Unknown column alignment: "+h,s)});if(n.length>1)throw new T("{subarray} can contain only one column");var i={cols:n,hskipBeforeAndAfter:!1,arraystretch:.5};if(i=k0(a.parser,i,"script"),i.body.length>0&&i.body[0].length>1)throw new T("{subarray} can contain only one column");return i},htmlBuilder:e0,mathmlBuilder:t0});Je({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(a){var e={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},t=k0(a.parser,e,jr(a.envName));return{type:"leftright",mode:a.mode,body:[t],left:a.envName.indexOf("r")>-1?".":"\\{",right:a.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:e0,mathmlBuilder:t0});Je({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:mi,htmlBuilder:e0,mathmlBuilder:t0});Je({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(a){_.contains(["gather","gather*"],a.envName)&&Yt(a);var e={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:Zr(a.envName),emptySingleRow:!0,leqno:a.parser.settings.leqno};return k0(a.parser,e,"display")},htmlBuilder:e0,mathmlBuilder:t0});Je({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:mi,htmlBuilder:e0,mathmlBuilder:t0});Je({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(a){Yt(a);var e={autoTag:Zr(a.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:a.parser.settings.leqno};return k0(a.parser,e,"display")},htmlBuilder:e0,mathmlBuilder:t0});Je({type:"array",names:["CD"],props:{numArgs:0},handler(a){return Yt(a),I1(a.parser)},htmlBuilder:e0,mathmlBuilder:t0});m("\\nonumber","\\gdef\\@eqnsw{0}");m("\\notag","\\nonumber");B({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(a,e){throw new T(a.funcName+" valid only within array environment")}});var Ka=hi;B({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(a,e){var{parser:t,funcName:r}=a,n=e[0];if(n.type!=="ordgroup")throw new T("Invalid environment name",n);for(var i="",s=0;s<n.body.length;++s)i+=U(n.body[s],"textord").text;if(r==="\\begin"){if(!Ka.hasOwnProperty(i))throw new T("No such environment: "+i,n);var o=Ka[i],{args:h,optArgs:d}=t.parseArguments("\\begin{"+i+"}",o),p={mode:t.mode,envName:i,parser:t},g=o.handler(p,h,d);t.expect("\\end",!1);var x=t.nextToken,w=U(t.parseFunction(),"environment");if(w.name!==i)throw new T("Mismatch: \\begin{"+i+"} matched by \\end{"+w.name+"}",x);return g}return{type:"environment",mode:t.mode,name:i,nameGroup:n}}});var di=(a,e)=>{var t=a.font,r=e.withFont(t);return Y(a.body,r)},pi=(a,e)=>{var t=a.font,r=e.withFont(t);return te(a.body,r)},Qa={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};B({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(a,e)=>{var{parser:t,funcName:r}=a,n=_t(e[0]),i=r;return i in Qa&&(i=Qa[i]),{type:"font",mode:t.mode,font:i.slice(1),body:n}},htmlBuilder:di,mathmlBuilder:pi});B({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(a,e)=>{var{parser:t}=a,r=e[0],n=_.isCharacterBox(r);return{type:"mclass",mode:t.mode,mclass:Vt(r),body:[{type:"font",mode:t.mode,font:"boldsymbol",body:r}],isCharacterBox:n}}});B({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(a,e)=>{var{parser:t,funcName:r,breakOnTokenText:n}=a,{mode:i}=t,s=t.parseExpression(!0,n),o="math"+r.slice(1);return{type:"font",mode:i,font:o,body:{type:"ordgroup",mode:t.mode,body:s}}},htmlBuilder:di,mathmlBuilder:pi});var fi=(a,e)=>{var t=e;return a==="display"?t=t.id>=P.SCRIPT.id?t.text():P.DISPLAY:a==="text"&&t.size===P.DISPLAY.size?t=P.TEXT:a==="script"?t=P.SCRIPT:a==="scriptscript"&&(t=P.SCRIPTSCRIPT),t},Kr=(a,e)=>{var t=fi(a.size,e.style),r=t.fracNum(),n=t.fracDen(),i;i=e.havingStyle(r);var s=Y(a.numer,i,e);if(a.continued){var o=8.5/e.fontMetrics().ptPerEm,h=3.5/e.fontMetrics().ptPerEm;s.height=s.height<o?o:s.height,s.depth=s.depth<h?h:s.depth}i=e.havingStyle(n);var d=Y(a.denom,i,e),p,g,x;a.hasBarLine?(a.barSize?(g=ue(a.barSize,e),p=D.makeLineSpan("frac-line",e,g)):p=D.makeLineSpan("frac-line",e),g=p.height,x=p.height):(p=null,g=0,x=e.fontMetrics().defaultRuleThickness);var w,S,E;t.size===P.DISPLAY.size||a.size==="display"?(w=e.fontMetrics().num1,g>0?S=3*x:S=7*x,E=e.fontMetrics().denom1):(g>0?(w=e.fontMetrics().num2,S=x):(w=e.fontMetrics().num3,S=3*x),E=e.fontMetrics().denom2);var z;if(p){var L=e.fontMetrics().axisHeight;w-s.depth-(L+.5*g)<S&&(w+=S-(w-s.depth-(L+.5*g))),L-.5*g-(d.height-E)<S&&(E+=S-(L-.5*g-(d.height-E)));var I=-(L-.5*g);z=D.makeVList({positionType:"individualShift",children:[{type:"elem",elem:d,shift:E},{type:"elem",elem:p,shift:I},{type:"elem",elem:s,shift:-w}]},e)}else{var k=w-s.depth-(d.height-E);k<S&&(w+=.5*(S-k),E+=.5*(S-k)),z=D.makeVList({positionType:"individualShift",children:[{type:"elem",elem:d,shift:E},{type:"elem",elem:s,shift:-w}]},e)}i=e.havingStyle(t),z.height*=i.sizeMultiplier/e.sizeMultiplier,z.depth*=i.sizeMultiplier/e.sizeMultiplier;var G;t.size===P.DISPLAY.size?G=e.fontMetrics().delim1:t.size===P.SCRIPTSCRIPT.size?G=e.havingStyle(P.SCRIPT).fontMetrics().delim2:G=e.fontMetrics().delim2;var $,X;return a.leftDelim==null?$=st(e,["mopen"]):$=m0.customSizedDelim(a.leftDelim,G,!0,e.havingStyle(t),a.mode,["mopen"]),a.continued?X=D.makeSpan([]):a.rightDelim==null?X=st(e,["mclose"]):X=m0.customSizedDelim(a.rightDelim,G,!0,e.havingStyle(t),a.mode,["mclose"]),D.makeSpan(["mord"].concat(i.sizingClasses(e)),[$,D.makeSpan(["mfrac"],[z]),X],e)},Qr=(a,e)=>{var t=new F.MathNode("mfrac",[te(a.numer,e),te(a.denom,e)]);if(!a.hasBarLine)t.setAttribute("linethickness","0px");else if(a.barSize){var r=ue(a.barSize,e);t.setAttribute("linethickness",M(r))}var n=fi(a.size,e.style);if(n.size!==e.style.size){t=new F.MathNode("mstyle",[t]);var i=n.size===P.DISPLAY.size?"true":"false";t.setAttribute("displaystyle",i),t.setAttribute("scriptlevel","0")}if(a.leftDelim!=null||a.rightDelim!=null){var s=[];if(a.leftDelim!=null){var o=new F.MathNode("mo",[new F.TextNode(a.leftDelim.replace("\\",""))]);o.setAttribute("fence","true"),s.push(o)}if(s.push(t),a.rightDelim!=null){var h=new F.MathNode("mo",[new F.TextNode(a.rightDelim.replace("\\",""))]);h.setAttribute("fence","true"),s.push(h)}return $r(s)}return t};B({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(a,e)=>{var{parser:t,funcName:r}=a,n=e[0],i=e[1],s,o=null,h=null,d="auto";switch(r){case"\\dfrac":case"\\frac":case"\\tfrac":s=!0;break;case"\\\\atopfrac":s=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":s=!1,o="(",h=")";break;case"\\\\bracefrac":s=!1,o="\\{",h="\\}";break;case"\\\\brackfrac":s=!1,o="[",h="]";break;default:throw new Error("Unrecognized genfrac command")}switch(r){case"\\dfrac":case"\\dbinom":d="display";break;case"\\tfrac":case"\\tbinom":d="text";break}return{type:"genfrac",mode:t.mode,continued:!1,numer:n,denom:i,hasBarLine:s,leftDelim:o,rightDelim:h,size:d,barSize:null}},htmlBuilder:Kr,mathmlBuilder:Qr});B({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(a,e)=>{var{parser:t,funcName:r}=a,n=e[0],i=e[1];return{type:"genfrac",mode:t.mode,continued:!0,numer:n,denom:i,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}});B({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(a){var{parser:e,funcName:t,token:r}=a,n;switch(t){case"\\over":n="\\frac";break;case"\\choose":n="\\binom";break;case"\\atop":n="\\\\atopfrac";break;case"\\brace":n="\\\\bracefrac";break;case"\\brack":n="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:e.mode,replaceWith:n,token:r}}});var Ja=["display","text","script","scriptscript"],en=function(e){var t=null;return e.length>0&&(t=e,t=t==="."?null:t),t};B({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(a,e){var{parser:t}=a,r=e[4],n=e[5],i=_t(e[0]),s=i.type==="atom"&&i.family==="open"?en(i.text):null,o=_t(e[1]),h=o.type==="atom"&&o.family==="close"?en(o.text):null,d=U(e[2],"size"),p,g=null;d.isBlank?p=!0:(g=d.value,p=g.number>0);var x="auto",w=e[3];if(w.type==="ordgroup"){if(w.body.length>0){var S=U(w.body[0],"textord");x=Ja[Number(S.text)]}}else w=U(w,"textord"),x=Ja[Number(w.text)];return{type:"genfrac",mode:t.mode,numer:r,denom:n,continued:!1,hasBarLine:p,barSize:g,leftDelim:s,rightDelim:h,size:x}},htmlBuilder:Kr,mathmlBuilder:Qr});B({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(a,e){var{parser:t,funcName:r,token:n}=a;return{type:"infix",mode:t.mode,replaceWith:"\\\\abovefrac",size:U(e[0],"size").value,token:n}}});B({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(a,e)=>{var{parser:t,funcName:r}=a,n=e[0],i=Fs(U(e[1],"infix").size),s=e[2],o=i.number>0;return{type:"genfrac",mode:t.mode,numer:n,denom:s,continued:!1,hasBarLine:o,barSize:i,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:Kr,mathmlBuilder:Qr});var vi=(a,e)=>{var t=e.style,r,n;a.type==="supsub"?(r=a.sup?Y(a.sup,e.havingStyle(t.sup()),e):Y(a.sub,e.havingStyle(t.sub()),e),n=U(a.base,"horizBrace")):n=U(a,"horizBrace");var i=Y(n.base,e.havingBaseStyle(P.DISPLAY)),s=f0.svgSpan(n,e),o;if(n.isOver?(o=D.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:i},{type:"kern",size:.1},{type:"elem",elem:s}]},e),o.children[0].children[0].children[1].classes.push("svg-align")):(o=D.makeVList({positionType:"bottom",positionData:i.depth+.1+s.height,children:[{type:"elem",elem:s},{type:"kern",size:.1},{type:"elem",elem:i}]},e),o.children[0].children[0].children[0].classes.push("svg-align")),r){var h=D.makeSpan(["mord",n.isOver?"mover":"munder"],[o],e);n.isOver?o=D.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:h},{type:"kern",size:.2},{type:"elem",elem:r}]},e):o=D.makeVList({positionType:"bottom",positionData:h.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:h}]},e)}return D.makeSpan(["mord",n.isOver?"mover":"munder"],[o],e)},j1=(a,e)=>{var t=f0.mathMLnode(a.label);return new F.MathNode(a.isOver?"mover":"munder",[te(a.base,e),t])};B({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(a,e){var{parser:t,funcName:r}=a;return{type:"horizBrace",mode:t.mode,label:r,isOver:/^\\over/.test(r),base:e[0]}},htmlBuilder:vi,mathmlBuilder:j1});B({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=e[1],n=U(e[0],"url").url;return t.settings.isTrusted({command:"\\href",url:n})?{type:"href",mode:t.mode,href:n,body:ce(r)}:t.formatUnsupportedCmd("\\href")},htmlBuilder:(a,e)=>{var t=fe(a.body,e,!1);return D.makeAnchor(a.href,[],t,e)},mathmlBuilder:(a,e)=>{var t=D0(a.body,e);return t instanceof Be||(t=new Be("mrow",[t])),t.setAttribute("href",a.href),t}});B({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=U(e[0],"url").url;if(!t.settings.isTrusted({command:"\\url",url:r}))return t.formatUnsupportedCmd("\\url");for(var n=[],i=0;i<r.length;i++){var s=r[i];s==="~"&&(s="\\textasciitilde"),n.push({type:"textord",mode:"text",text:s})}var o={type:"text",mode:t.mode,font:"\\texttt",body:n};return{type:"href",mode:t.mode,href:r,body:ce(o)}}});B({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(a,e){var{parser:t}=a;return{type:"hbox",mode:t.mode,body:ce(e[0])}},htmlBuilder(a,e){var t=fe(a.body,e,!1);return D.makeFragment(t)},mathmlBuilder(a,e){return new F.MathNode("mrow",Se(a.body,e))}});B({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(a,e)=>{var{parser:t,funcName:r,token:n}=a,i=U(e[0],"raw").string,s=e[1];t.settings.strict&&t.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var o,h={};switch(r){case"\\htmlClass":h.class=i,o={command:"\\htmlClass",class:i};break;case"\\htmlId":h.id=i,o={command:"\\htmlId",id:i};break;case"\\htmlStyle":h.style=i,o={command:"\\htmlStyle",style:i};break;case"\\htmlData":{for(var d=i.split(","),p=0;p<d.length;p++){var g=d[p].split("=");if(g.length!==2)throw new T("Error parsing key-value for \\htmlData");h["data-"+g[0].trim()]=g[1].trim()}o={command:"\\htmlData",attributes:h};break}default:throw new Error("Unrecognized html command")}return t.settings.isTrusted(o)?{type:"html",mode:t.mode,attributes:h,body:ce(s)}:t.formatUnsupportedCmd(r)},htmlBuilder:(a,e)=>{var t=fe(a.body,e,!1),r=["enclosing"];a.attributes.class&&r.push(...a.attributes.class.trim().split(/\s+/));var n=D.makeSpan(r,t,e);for(var i in a.attributes)i!=="class"&&a.attributes.hasOwnProperty(i)&&n.setAttribute(i,a.attributes[i]);return n},mathmlBuilder:(a,e)=>D0(a.body,e)});B({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(a,e)=>{var{parser:t}=a;return{type:"htmlmathml",mode:t.mode,html:ce(e[0]),mathml:ce(e[1])}},htmlBuilder:(a,e)=>{var t=fe(a.html,e,!1);return D.makeFragment(t)},mathmlBuilder:(a,e)=>D0(a.mathml,e)});var xr=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new T("Invalid size: '"+e+"' in \\includegraphics");var r={number:+(t[1]+t[2]),unit:t[3]};if(!In(r))throw new T("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r};B({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(a,e,t)=>{var{parser:r}=a,n={number:0,unit:"em"},i={number:.9,unit:"em"},s={number:0,unit:"em"},o="";if(t[0])for(var h=U(t[0],"raw").string,d=h.split(","),p=0;p<d.length;p++){var g=d[p].split("=");if(g.length===2){var x=g[1].trim();switch(g[0].trim()){case"alt":o=x;break;case"width":n=xr(x);break;case"height":i=xr(x);break;case"totalheight":s=xr(x);break;default:throw new T("Invalid key: '"+g[0]+"' in \\includegraphics.")}}}var w=U(e[0],"url").url;return o===""&&(o=w,o=o.replace(/^.*[\\/]/,""),o=o.substring(0,o.lastIndexOf("."))),r.settings.isTrusted({command:"\\includegraphics",url:w})?{type:"includegraphics",mode:r.mode,alt:o,width:n,height:i,totalheight:s,src:w}:r.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(a,e)=>{var t=ue(a.height,e),r=0;a.totalheight.number>0&&(r=ue(a.totalheight,e)-t);var n=0;a.width.number>0&&(n=ue(a.width,e));var i={height:M(t+r)};n>0&&(i.width=M(n)),r>0&&(i.verticalAlign=M(-r));var s=new js(a.src,a.alt,i);return s.height=t,s.depth=r,s},mathmlBuilder:(a,e)=>{var t=new F.MathNode("mglyph",[]);t.setAttribute("alt",a.alt);var r=ue(a.height,e),n=0;if(a.totalheight.number>0&&(n=ue(a.totalheight,e)-r,t.setAttribute("valign",M(-n))),t.setAttribute("height",M(r+n)),a.width.number>0){var i=ue(a.width,e);t.setAttribute("width",M(i))}return t.setAttribute("src",a.src),t}});B({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(a,e){var{parser:t,funcName:r}=a,n=U(e[0],"size");if(t.settings.strict){var i=r[1]==="m",s=n.value.unit==="mu";i?(s||t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+r+" supports only mu units, "+("not "+n.value.unit+" units")),t.mode!=="math"&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+r+" works only in math mode")):s&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+r+" doesn't support mu units")}return{type:"kern",mode:t.mode,dimension:n.value}},htmlBuilder(a,e){return D.makeGlue(a.dimension,e)},mathmlBuilder(a,e){var t=ue(a.dimension,e);return new F.SpaceNode(t)}});B({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(a,e)=>{var{parser:t,funcName:r}=a,n=e[0];return{type:"lap",mode:t.mode,alignment:r.slice(5),body:n}},htmlBuilder:(a,e)=>{var t;a.alignment==="clap"?(t=D.makeSpan([],[Y(a.body,e)]),t=D.makeSpan(["inner"],[t],e)):t=D.makeSpan(["inner"],[Y(a.body,e)]);var r=D.makeSpan(["fix"],[]),n=D.makeSpan([a.alignment],[t,r],e),i=D.makeSpan(["strut"]);return i.style.height=M(n.height+n.depth),n.depth&&(i.style.verticalAlign=M(-n.depth)),n.children.unshift(i),n=D.makeSpan(["thinbox"],[n],e),D.makeSpan(["mord","vbox"],[n],e)},mathmlBuilder:(a,e)=>{var t=new F.MathNode("mpadded",[te(a.body,e)]);if(a.alignment!=="rlap"){var r=a.alignment==="llap"?"-1":"-0.5";t.setAttribute("lspace",r+"width")}return t.setAttribute("width","0px"),t}});B({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(a,e){var{funcName:t,parser:r}=a,n=r.mode;r.switchMode("math");var i=t==="\\("?"\\)":"$",s=r.parseExpression(!1,i);return r.expect(i),r.switchMode(n),{type:"styling",mode:r.mode,style:"text",body:s}}});B({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(a,e){throw new T("Mismatched "+a.funcName)}});var tn=(a,e)=>{switch(e.style.size){case P.DISPLAY.size:return a.display;case P.TEXT.size:return a.text;case P.SCRIPT.size:return a.script;case P.SCRIPTSCRIPT.size:return a.scriptscript;default:return a.text}};B({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(a,e)=>{var{parser:t}=a;return{type:"mathchoice",mode:t.mode,display:ce(e[0]),text:ce(e[1]),script:ce(e[2]),scriptscript:ce(e[3])}},htmlBuilder:(a,e)=>{var t=tn(a,e),r=fe(t,e,!1);return D.makeFragment(r)},mathmlBuilder:(a,e)=>{var t=tn(a,e);return D0(t,e)}});var gi=(a,e,t,r,n,i,s)=>{a=D.makeSpan([],[a]);var o=t&&_.isCharacterBox(t),h,d;if(e){var p=Y(e,r.havingStyle(n.sup()),r);d={elem:p,kern:Math.max(r.fontMetrics().bigOpSpacing1,r.fontMetrics().bigOpSpacing3-p.depth)}}if(t){var g=Y(t,r.havingStyle(n.sub()),r);h={elem:g,kern:Math.max(r.fontMetrics().bigOpSpacing2,r.fontMetrics().bigOpSpacing4-g.height)}}var x;if(d&&h){var w=r.fontMetrics().bigOpSpacing5+h.elem.height+h.elem.depth+h.kern+a.depth+s;x=D.makeVList({positionType:"bottom",positionData:w,children:[{type:"kern",size:r.fontMetrics().bigOpSpacing5},{type:"elem",elem:h.elem,marginLeft:M(-i)},{type:"kern",size:h.kern},{type:"elem",elem:a},{type:"kern",size:d.kern},{type:"elem",elem:d.elem,marginLeft:M(i)},{type:"kern",size:r.fontMetrics().bigOpSpacing5}]},r)}else if(h){var S=a.height-s;x=D.makeVList({positionType:"top",positionData:S,children:[{type:"kern",size:r.fontMetrics().bigOpSpacing5},{type:"elem",elem:h.elem,marginLeft:M(-i)},{type:"kern",size:h.kern},{type:"elem",elem:a}]},r)}else if(d){var E=a.depth+s;x=D.makeVList({positionType:"bottom",positionData:E,children:[{type:"elem",elem:a},{type:"kern",size:d.kern},{type:"elem",elem:d.elem,marginLeft:M(i)},{type:"kern",size:r.fontMetrics().bigOpSpacing5}]},r)}else return a;var z=[x];if(h&&i!==0&&!o){var k=D.makeSpan(["mspace"],[],r);k.style.marginRight=M(i),z.unshift(k)}return D.makeSpan(["mop","op-limits"],z,r)},bi=["\\smallint"],Y0=(a,e)=>{var t,r,n=!1,i;a.type==="supsub"?(t=a.sup,r=a.sub,i=U(a.base,"op"),n=!0):i=U(a,"op");var s=e.style,o=!1;s.size===P.DISPLAY.size&&i.symbol&&!_.contains(bi,i.name)&&(o=!0);var h;if(i.symbol){var d=o?"Size2-Regular":"Size1-Regular",p="";if((i.name==="\\oiint"||i.name==="\\oiiint")&&(p=i.name.slice(1),i.name=p==="oiint"?"\\iint":"\\iiint"),h=D.makeSymbol(i.name,d,"math",e,["mop","op-symbol",o?"large-op":"small-op"]),p.length>0){var g=h.italic,x=D.staticSvg(p+"Size"+(o?"2":"1"),e);h=D.makeVList({positionType:"individualShift",children:[{type:"elem",elem:h,shift:0},{type:"elem",elem:x,shift:o?.08:0}]},e),i.name="\\"+p,h.classes.unshift("mop"),h.italic=g}}else if(i.body){var w=fe(i.body,e,!0);w.length===1&&w[0]instanceof Oe?(h=w[0],h.classes[0]="mop"):h=D.makeSpan(["mop"],w,e)}else{for(var S=[],E=1;E<i.name.length;E++)S.push(D.mathsym(i.name[E],i.mode,e));h=D.makeSpan(["mop"],S,e)}var z=0,k=0;return(h instanceof Oe||i.name==="\\oiint"||i.name==="\\oiiint")&&!i.suppressBaseShift&&(z=(h.height-h.depth)/2-e.fontMetrics().axisHeight,k=h.italic),n?gi(h,t,r,e,s,k,z):(z&&(h.style.position="relative",h.style.top=M(z)),h)},ot=(a,e)=>{var t;if(a.symbol)t=new Be("mo",[Le(a.name,a.mode)]),_.contains(bi,a.name)&&t.setAttribute("largeop","false");else if(a.body)t=new Be("mo",Se(a.body,e));else{t=new Be("mi",[new rt(a.name.slice(1))]);var r=new Be("mo",[Le("⁡","text")]);a.parentIsSupSub?t=new Be("mrow",[t,r]):t=Wn([t,r])}return t},K1={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};B({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(a,e)=>{var{parser:t,funcName:r}=a,n=r;return n.length===1&&(n=K1[n]),{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:Y0,mathmlBuilder:ot});B({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(a,e)=>{var{parser:t}=a,r=e[0];return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:ce(r)}},htmlBuilder:Y0,mathmlBuilder:ot});var Q1={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};B({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(a){var{parser:e,funcName:t}=a;return{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:Y0,mathmlBuilder:ot});B({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(a){var{parser:e,funcName:t}=a;return{type:"op",mode:e.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:Y0,mathmlBuilder:ot});B({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(a){var{parser:e,funcName:t}=a,r=t;return r.length===1&&(r=Q1[r]),{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:r}},htmlBuilder:Y0,mathmlBuilder:ot});var yi=(a,e)=>{var t,r,n=!1,i;a.type==="supsub"?(t=a.sup,r=a.sub,i=U(a.base,"operatorname"),n=!0):i=U(a,"operatorname");var s;if(i.body.length>0){for(var o=i.body.map(g=>{var x=g.text;return typeof x=="string"?{type:"textord",mode:g.mode,text:x}:g}),h=fe(o,e.withFont("mathrm"),!0),d=0;d<h.length;d++){var p=h[d];p instanceof Oe&&(p.text=p.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}s=D.makeSpan(["mop"],h,e)}else s=D.makeSpan(["mop"],[],e);return n?gi(s,t,r,e,e.style,0,0):s},J1=(a,e)=>{for(var t=Se(a.body,e.withFont("mathrm")),r=!0,n=0;n<t.length;n++){var i=t[n];if(!(i instanceof F.SpaceNode))if(i instanceof F.MathNode)switch(i.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{var s=i.children[0];i.children.length===1&&s instanceof F.TextNode?s.text=s.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):r=!1;break}default:r=!1}else r=!1}if(r){var o=t.map(p=>p.toText()).join("");t=[new F.TextNode(o)]}var h=new F.MathNode("mi",t);h.setAttribute("mathvariant","normal");var d=new F.MathNode("mo",[Le("⁡","text")]);return a.parentIsSupSub?new F.MathNode("mrow",[h,d]):F.newDocumentFragment([h,d])};B({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(a,e)=>{var{parser:t,funcName:r}=a,n=e[0];return{type:"operatorname",mode:t.mode,body:ce(n),alwaysHandleSupSub:r==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:yi,mathmlBuilder:J1});m("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@");N0({type:"ordgroup",htmlBuilder(a,e){return a.semisimple?D.makeFragment(fe(a.body,e,!1)):D.makeSpan(["mord"],fe(a.body,e,!0),e)},mathmlBuilder(a,e){return D0(a.body,e,!0)}});B({type:"overline",names:["\\overline"],props:{numArgs:1},handler(a,e){var{parser:t}=a,r=e[0];return{type:"overline",mode:t.mode,body:r}},htmlBuilder(a,e){var t=Y(a.body,e.havingCrampedStyle()),r=D.makeLineSpan("overline-line",e),n=e.fontMetrics().defaultRuleThickness,i=D.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t},{type:"kern",size:3*n},{type:"elem",elem:r},{type:"kern",size:n}]},e);return D.makeSpan(["mord","overline"],[i],e)},mathmlBuilder(a,e){var t=new F.MathNode("mo",[new F.TextNode("‾")]);t.setAttribute("stretchy","true");var r=new F.MathNode("mover",[te(a.body,e),t]);return r.setAttribute("accent","true"),r}});B({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=e[0];return{type:"phantom",mode:t.mode,body:ce(r)}},htmlBuilder:(a,e)=>{var t=fe(a.body,e.withPhantom(),!1);return D.makeFragment(t)},mathmlBuilder:(a,e)=>{var t=Se(a.body,e);return new F.MathNode("mphantom",t)}});B({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=e[0];return{type:"hphantom",mode:t.mode,body:r}},htmlBuilder:(a,e)=>{var t=D.makeSpan([],[Y(a.body,e.withPhantom())]);if(t.height=0,t.depth=0,t.children)for(var r=0;r<t.children.length;r++)t.children[r].height=0,t.children[r].depth=0;return t=D.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e),D.makeSpan(["mord"],[t],e)},mathmlBuilder:(a,e)=>{var t=Se(ce(a.body),e),r=new F.MathNode("mphantom",t),n=new F.MathNode("mpadded",[r]);return n.setAttribute("height","0px"),n.setAttribute("depth","0px"),n}});B({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=e[0];return{type:"vphantom",mode:t.mode,body:r}},htmlBuilder:(a,e)=>{var t=D.makeSpan(["inner"],[Y(a.body,e.withPhantom())]),r=D.makeSpan(["fix"],[]);return D.makeSpan(["mord","rlap"],[t,r],e)},mathmlBuilder:(a,e)=>{var t=Se(ce(a.body),e),r=new F.MathNode("mphantom",t),n=new F.MathNode("mpadded",[r]);return n.setAttribute("width","0px"),n}});B({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(a,e){var{parser:t}=a,r=U(e[0],"size").value,n=e[1];return{type:"raisebox",mode:t.mode,dy:r,body:n}},htmlBuilder(a,e){var t=Y(a.body,e),r=ue(a.dy,e);return D.makeVList({positionType:"shift",positionData:-r,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(a,e){var t=new F.MathNode("mpadded",[te(a.body,e)]),r=a.dy.number+a.dy.unit;return t.setAttribute("voffset",r),t}});B({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0},handler(a){var{parser:e}=a;return{type:"internal",mode:e.mode}}});B({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},handler(a,e,t){var{parser:r}=a,n=t[0],i=U(e[0],"size"),s=U(e[1],"size");return{type:"rule",mode:r.mode,shift:n&&U(n,"size").value,width:i.value,height:s.value}},htmlBuilder(a,e){var t=D.makeSpan(["mord","rule"],[],e),r=ue(a.width,e),n=ue(a.height,e),i=a.shift?ue(a.shift,e):0;return t.style.borderRightWidth=M(r),t.style.borderTopWidth=M(n),t.style.bottom=M(i),t.width=r,t.height=n+i,t.depth=-i,t.maxFontSize=n*1.125*e.sizeMultiplier,t},mathmlBuilder(a,e){var t=ue(a.width,e),r=ue(a.height,e),n=a.shift?ue(a.shift,e):0,i=e.color&&e.getColor()||"black",s=new F.MathNode("mspace");s.setAttribute("mathbackground",i),s.setAttribute("width",M(t)),s.setAttribute("height",M(r));var o=new F.MathNode("mpadded",[s]);return n>=0?o.setAttribute("height",M(n)):(o.setAttribute("height",M(n)),o.setAttribute("depth",M(-n))),o.setAttribute("voffset",M(n)),o}});function xi(a,e,t){for(var r=fe(a,e,!1),n=e.sizeMultiplier/t.sizeMultiplier,i=0;i<r.length;i++){var s=r[i].classes.indexOf("sizing");s<0?Array.prototype.push.apply(r[i].classes,e.sizingClasses(t)):r[i].classes[s+1]==="reset-size"+e.size&&(r[i].classes[s+1]="reset-size"+t.size),r[i].height*=n,r[i].depth*=n}return D.makeFragment(r)}var rn=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],el=(a,e)=>{var t=e.havingSize(a.size);return xi(a.body,t,e)};B({type:"sizing",names:rn,props:{numArgs:0,allowedInText:!0},handler:(a,e)=>{var{breakOnTokenText:t,funcName:r,parser:n}=a,i=n.parseExpression(!1,t);return{type:"sizing",mode:n.mode,size:rn.indexOf(r)+1,body:i}},htmlBuilder:el,mathmlBuilder:(a,e)=>{var t=e.havingSize(a.size),r=Se(a.body,t),n=new F.MathNode("mstyle",r);return n.setAttribute("mathsize",M(t.sizeMultiplier)),n}});B({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(a,e,t)=>{var{parser:r}=a,n=!1,i=!1,s=t[0]&&U(t[0],"ordgroup");if(s)for(var o="",h=0;h<s.body.length;++h){var d=s.body[h];if(o=d.text,o==="t")n=!0;else if(o==="b")i=!0;else{n=!1,i=!1;break}}else n=!0,i=!0;var p=e[0];return{type:"smash",mode:r.mode,body:p,smashHeight:n,smashDepth:i}},htmlBuilder:(a,e)=>{var t=D.makeSpan([],[Y(a.body,e)]);if(!a.smashHeight&&!a.smashDepth)return t;if(a.smashHeight&&(t.height=0,t.children))for(var r=0;r<t.children.length;r++)t.children[r].height=0;if(a.smashDepth&&(t.depth=0,t.children))for(var n=0;n<t.children.length;n++)t.children[n].depth=0;var i=D.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e);return D.makeSpan(["mord"],[i],e)},mathmlBuilder:(a,e)=>{var t=new F.MathNode("mpadded",[te(a.body,e)]);return a.smashHeight&&t.setAttribute("height","0px"),a.smashDepth&&t.setAttribute("depth","0px"),t}});B({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(a,e,t){var{parser:r}=a,n=t[0],i=e[0];return{type:"sqrt",mode:r.mode,body:i,index:n}},htmlBuilder(a,e){var t=Y(a.body,e.havingCrampedStyle());t.height===0&&(t.height=e.fontMetrics().xHeight),t=D.wrapFragment(t,e);var r=e.fontMetrics(),n=r.defaultRuleThickness,i=n;e.style.id<P.TEXT.id&&(i=e.fontMetrics().xHeight);var s=n+i/4,o=t.height+t.depth+s+n,{span:h,ruleWidth:d,advanceWidth:p}=m0.sqrtImage(o,e),g=h.height-d;g>t.height+t.depth+s&&(s=(s+g-t.height-t.depth)/2);var x=h.height-t.height-s-d;t.style.paddingLeft=M(p);var w=D.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t,wrapperClasses:["svg-align"]},{type:"kern",size:-(t.height+x)},{type:"elem",elem:h},{type:"kern",size:d}]},e);if(a.index){var S=e.havingStyle(P.SCRIPTSCRIPT),E=Y(a.index,S,e),z=.6*(w.height-w.depth),k=D.makeVList({positionType:"shift",positionData:-z,children:[{type:"elem",elem:E}]},e),L=D.makeSpan(["root"],[k]);return D.makeSpan(["mord","sqrt"],[L,w],e)}else return D.makeSpan(["mord","sqrt"],[w],e)},mathmlBuilder(a,e){var{body:t,index:r}=a;return r?new F.MathNode("mroot",[te(t,e),te(r,e)]):new F.MathNode("msqrt",[te(t,e)])}});var an={display:P.DISPLAY,text:P.TEXT,script:P.SCRIPT,scriptscript:P.SCRIPTSCRIPT};B({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(a,e){var{breakOnTokenText:t,funcName:r,parser:n}=a,i=n.parseExpression(!0,t),s=r.slice(1,r.length-5);return{type:"styling",mode:n.mode,style:s,body:i}},htmlBuilder(a,e){var t=an[a.style],r=e.havingStyle(t).withFont("");return xi(a.body,r,e)},mathmlBuilder(a,e){var t=an[a.style],r=e.havingStyle(t),n=Se(a.body,r),i=new F.MathNode("mstyle",n),s={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]},o=s[a.style];return i.setAttribute("scriptlevel",o[0]),i.setAttribute("displaystyle",o[1]),i}});var tl=function(e,t){var r=e.base;if(r)if(r.type==="op"){var n=r.limits&&(t.style.size===P.DISPLAY.size||r.alwaysHandleSupSub);return n?Y0:null}else if(r.type==="operatorname"){var i=r.alwaysHandleSupSub&&(t.style.size===P.DISPLAY.size||r.limits);return i?yi:null}else{if(r.type==="accent")return _.isCharacterBox(r.base)?Vr:null;if(r.type==="horizBrace"){var s=!e.sub;return s===r.isOver?vi:null}else return null}else return null};N0({type:"supsub",htmlBuilder(a,e){var t=tl(a,e);if(t)return t(a,e);var{base:r,sup:n,sub:i}=a,s=Y(r,e),o,h,d=e.fontMetrics(),p=0,g=0,x=r&&_.isCharacterBox(r);if(n){var w=e.havingStyle(e.style.sup());o=Y(n,w,e),x||(p=s.height-w.fontMetrics().supDrop*w.sizeMultiplier/e.sizeMultiplier)}if(i){var S=e.havingStyle(e.style.sub());h=Y(i,S,e),x||(g=s.depth+S.fontMetrics().subDrop*S.sizeMultiplier/e.sizeMultiplier)}var E;e.style===P.DISPLAY?E=d.sup1:e.style.cramped?E=d.sup3:E=d.sup2;var z=e.sizeMultiplier,k=M(.5/d.ptPerEm/z),L=null;if(h){var I=a.base&&a.base.type==="op"&&a.base.name&&(a.base.name==="\\oiint"||a.base.name==="\\oiiint");(s instanceof Oe||I)&&(L=M(-s.italic))}var G;if(o&&h){p=Math.max(p,E,o.depth+.25*d.xHeight),g=Math.max(g,d.sub2);var $=d.defaultRuleThickness,X=4*$;if(p-o.depth-(h.height-g)<X){g=X-(p-o.depth)+h.height;var W=.8*d.xHeight-(p-o.depth);W>0&&(p+=W,g-=W)}var ae=[{type:"elem",elem:h,shift:g,marginRight:k,marginLeft:L},{type:"elem",elem:o,shift:-p,marginRight:k}];G=D.makeVList({positionType:"individualShift",children:ae},e)}else if(h){g=Math.max(g,d.sub1,h.height-.8*d.xHeight);var Z=[{type:"elem",elem:h,marginLeft:L,marginRight:k}];G=D.makeVList({positionType:"shift",positionData:g,children:Z},e)}else if(o)p=Math.max(p,E,o.depth+.25*d.xHeight),G=D.makeVList({positionType:"shift",positionData:-p,children:[{type:"elem",elem:o,marginRight:k}]},e);else throw new Error("supsub must have either sup or sub.");var me=zr(s,"right")||"mord";return D.makeSpan([me],[s,D.makeSpan(["msupsub"],[G])],e)},mathmlBuilder(a,e){var t=!1,r,n;a.base&&a.base.type==="horizBrace"&&(n=!!a.sup,n===a.base.isOver&&(t=!0,r=a.base.isOver)),a.base&&(a.base.type==="op"||a.base.type==="operatorname")&&(a.base.parentIsSupSub=!0);var i=[te(a.base,e)];a.sub&&i.push(te(a.sub,e)),a.sup&&i.push(te(a.sup,e));var s;if(t)s=r?"mover":"munder";else if(a.sub)if(a.sup){var d=a.base;d&&d.type==="op"&&d.limits&&e.style===P.DISPLAY||d&&d.type==="operatorname"&&d.alwaysHandleSupSub&&(e.style===P.DISPLAY||d.limits)?s="munderover":s="msubsup"}else{var h=a.base;h&&h.type==="op"&&h.limits&&(e.style===P.DISPLAY||h.alwaysHandleSupSub)||h&&h.type==="operatorname"&&h.alwaysHandleSupSub&&(h.limits||e.style===P.DISPLAY)?s="munder":s="msub"}else{var o=a.base;o&&o.type==="op"&&o.limits&&(e.style===P.DISPLAY||o.alwaysHandleSupSub)||o&&o.type==="operatorname"&&o.alwaysHandleSupSub&&(o.limits||e.style===P.DISPLAY)?s="mover":s="msup"}return new F.MathNode(s,i)}});N0({type:"atom",htmlBuilder(a,e){return D.mathsym(a.text,a.mode,e,["m"+a.family])},mathmlBuilder(a,e){var t=new F.MathNode("mo",[Le(a.text,a.mode)]);if(a.family==="bin"){var r=Ur(a,e);r==="bold-italic"&&t.setAttribute("mathvariant",r)}else a.family==="punct"?t.setAttribute("separator","true"):(a.family==="open"||a.family==="close")&&t.setAttribute("stretchy","false");return t}});var wi={mi:"italic",mn:"normal",mtext:"normal"};N0({type:"mathord",htmlBuilder(a,e){return D.makeOrd(a,e,"mathord")},mathmlBuilder(a,e){var t=new F.MathNode("mi",[Le(a.text,a.mode,e)]),r=Ur(a,e)||"italic";return r!==wi[t.type]&&t.setAttribute("mathvariant",r),t}});N0({type:"textord",htmlBuilder(a,e){return D.makeOrd(a,e,"textord")},mathmlBuilder(a,e){var t=Le(a.text,a.mode,e),r=Ur(a,e)||"normal",n;return a.mode==="text"?n=new F.MathNode("mtext",[t]):/[0-9]/.test(a.text)?n=new F.MathNode("mn",[t]):a.text==="\\prime"?n=new F.MathNode("mo",[t]):n=new F.MathNode("mi",[t]),r!==wi[n.type]&&n.setAttribute("mathvariant",r),n}});var wr={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},Dr={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};N0({type:"spacing",htmlBuilder(a,e){if(Dr.hasOwnProperty(a.text)){var t=Dr[a.text].className||"";if(a.mode==="text"){var r=D.makeOrd(a,e,"textord");return r.classes.push(t),r}else return D.makeSpan(["mspace",t],[D.mathsym(a.text,a.mode,e)],e)}else{if(wr.hasOwnProperty(a.text))return D.makeSpan(["mspace",wr[a.text]],[],e);throw new T('Unknown type of space "'+a.text+'"')}},mathmlBuilder(a,e){var t;if(Dr.hasOwnProperty(a.text))t=new F.MathNode("mtext",[new F.TextNode(" ")]);else{if(wr.hasOwnProperty(a.text))return new F.MathNode("mspace");throw new T('Unknown type of space "'+a.text+'"')}return t}});var nn=()=>{var a=new F.MathNode("mtd",[]);return a.setAttribute("width","50%"),a};N0({type:"tag",mathmlBuilder(a,e){var t=new F.MathNode("mtable",[new F.MathNode("mtr",[nn(),new F.MathNode("mtd",[D0(a.body,e)]),nn(),new F.MathNode("mtd",[D0(a.tag,e)])])]);return t.setAttribute("width","100%"),t}});var sn={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},ln={"\\textbf":"textbf","\\textmd":"textmd"},rl={"\\textit":"textit","\\textup":"textup"},un=(a,e)=>{var t=a.font;return t?sn[t]?e.withTextFontFamily(sn[t]):ln[t]?e.withTextFontWeight(ln[t]):e.withTextFontShape(rl[t]):e};B({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(a,e){var{parser:t,funcName:r}=a,n=e[0];return{type:"text",mode:t.mode,body:ce(n),font:r}},htmlBuilder(a,e){var t=un(a,e),r=fe(a.body,t,!0);return D.makeSpan(["mord","text"],r,t)},mathmlBuilder(a,e){var t=un(a,e);return D0(a.body,t)}});B({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(a,e){var{parser:t}=a;return{type:"underline",mode:t.mode,body:e[0]}},htmlBuilder(a,e){var t=Y(a.body,e),r=D.makeLineSpan("underline-line",e),n=e.fontMetrics().defaultRuleThickness,i=D.makeVList({positionType:"top",positionData:t.height,children:[{type:"kern",size:n},{type:"elem",elem:r},{type:"kern",size:3*n},{type:"elem",elem:t}]},e);return D.makeSpan(["mord","underline"],[i],e)},mathmlBuilder(a,e){var t=new F.MathNode("mo",[new F.TextNode("‾")]);t.setAttribute("stretchy","true");var r=new F.MathNode("munder",[te(a.body,e),t]);return r.setAttribute("accentunder","true"),r}});B({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(a,e){var{parser:t}=a;return{type:"vcenter",mode:t.mode,body:e[0]}},htmlBuilder(a,e){var t=Y(a.body,e),r=e.fontMetrics().axisHeight,n=.5*(t.height-r-(t.depth+r));return D.makeVList({positionType:"shift",positionData:n,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(a,e){return new F.MathNode("mpadded",[te(a.body,e)],["vcenter"])}});B({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(a,e,t){throw new T("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(a,e){for(var t=on(a),r=[],n=e.havingStyle(e.style.text()),i=0;i<t.length;i++){var s=t[i];s==="~"&&(s="\\textasciitilde"),r.push(D.makeSymbol(s,"Typewriter-Regular",a.mode,n,["mord","texttt"]))}return D.makeSpan(["mord","text"].concat(n.sizingClasses(e)),D.tryCombineChars(r),n)},mathmlBuilder(a,e){var t=new F.TextNode(on(a)),r=new F.MathNode("mtext",[t]);return r.setAttribute("mathvariant","monospace"),r}});var on=a=>a.body.replace(/ /g,a.star?"␣":" "),y0=Gn,Di=`[ \r
	]`,al="\\\\[a-zA-Z@]+",nl="\\\\[^\uD800-\uDFFF]",il="("+al+")"+Di+"*",sl=`\\\\(
|[ \r	]+
?)[ \r	]*`,Ir="[̀-ͯ]",ll=new RegExp(Ir+"+$"),ul="("+Di+"+)|"+(sl+"|")+"([!-\\[\\]-‧‪-퟿豈-￿]"+(Ir+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(Ir+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+il)+("|"+nl+")");class hn{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=new RegExp(ul,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){var e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new Ne("EOF",new Te(this,t,t));var r=this.tokenRegex.exec(e);if(r===null||r.index!==t)throw new T("Unexpected character: '"+e[t]+"'",new Ne(e[t],new Te(this,t,t+1)));var n=r[6]||r[3]||(r[2]?"\\ ":" ");if(this.catcodes[n]===14){var i=e.indexOf(`
`,this.tokenRegex.lastIndex);return i===-1?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=i+1,this.lex()}return new Ne(n,new Te(this,t,this.tokenRegex.lastIndex))}}class ol{constructor(e,t){e===void 0&&(e={}),t===void 0&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new T("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(e[t]==null?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,r){if(r===void 0&&(r=!1),r){for(var n=0;n<this.undefStack.length;n++)delete this.undefStack[n][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var i=this.undefStack[this.undefStack.length-1];i&&!i.hasOwnProperty(e)&&(i[e]=this.current[e])}t==null?delete this.current[e]:this.current[e]=t}}var hl=ci;m("\\noexpand",function(a){var e=a.popToken();return a.isExpandable(e.text)&&(e.noexpand=!0,e.treatAsRelax=!0),{tokens:[e],numArgs:0}});m("\\expandafter",function(a){var e=a.popToken();return a.expandOnce(!0),{tokens:[e],numArgs:0}});m("\\@firstoftwo",function(a){var e=a.consumeArgs(2);return{tokens:e[0],numArgs:0}});m("\\@secondoftwo",function(a){var e=a.consumeArgs(2);return{tokens:e[1],numArgs:0}});m("\\@ifnextchar",function(a){var e=a.consumeArgs(3);a.consumeSpaces();var t=a.future();return e[0].length===1&&e[0][0].text===t.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}});m("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}");m("\\TextOrMath",function(a){var e=a.consumeArgs(2);return a.mode==="text"?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});var cn={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};m("\\char",function(a){var e=a.popToken(),t,r="";if(e.text==="'")t=8,e=a.popToken();else if(e.text==='"')t=16,e=a.popToken();else if(e.text==="`")if(e=a.popToken(),e.text[0]==="\\")r=e.text.charCodeAt(1);else{if(e.text==="EOF")throw new T("\\char` missing argument");r=e.text.charCodeAt(0)}else t=10;if(t){if(r=cn[e.text],r==null||r>=t)throw new T("Invalid base-"+t+" digit "+e.text);for(var n;(n=cn[a.future().text])!=null&&n<t;)r*=t,r+=n,a.popToken()}return"\\@char{"+r+"}"});var Jr=(a,e,t)=>{var r=a.consumeArg().tokens;if(r.length!==1)throw new T("\\newcommand's first argument must be a macro name");var n=r[0].text,i=a.isDefined(n);if(i&&!e)throw new T("\\newcommand{"+n+"} attempting to redefine "+(n+"; use \\renewcommand"));if(!i&&!t)throw new T("\\renewcommand{"+n+"} when command "+n+" does not yet exist; use \\newcommand");var s=0;if(r=a.consumeArg().tokens,r.length===1&&r[0].text==="["){for(var o="",h=a.expandNextToken();h.text!=="]"&&h.text!=="EOF";)o+=h.text,h=a.expandNextToken();if(!o.match(/^\s*[0-9]+\s*$/))throw new T("Invalid number of arguments: "+o);s=parseInt(o),r=a.consumeArg().tokens}return a.macros.set(n,{tokens:r,numArgs:s}),""};m("\\newcommand",a=>Jr(a,!1,!0));m("\\renewcommand",a=>Jr(a,!0,!1));m("\\providecommand",a=>Jr(a,!0,!0));m("\\message",a=>{var e=a.consumeArgs(1)[0];return console.log(e.reverse().map(t=>t.text).join("")),""});m("\\errmessage",a=>{var e=a.consumeArgs(1)[0];return console.error(e.reverse().map(t=>t.text).join("")),""});m("\\show",a=>{var e=a.popToken(),t=e.text;return console.log(e,a.macros.get(t),y0[t],re.math[t],re.text[t]),""});m("\\bgroup","{");m("\\egroup","}");m("~","\\nobreakspace");m("\\lq","`");m("\\rq","'");m("\\aa","\\r a");m("\\AA","\\r A");m("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`©}");m("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}");m("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`®}");m("ℬ","\\mathscr{B}");m("ℰ","\\mathscr{E}");m("ℱ","\\mathscr{F}");m("ℋ","\\mathscr{H}");m("ℐ","\\mathscr{I}");m("ℒ","\\mathscr{L}");m("ℳ","\\mathscr{M}");m("ℛ","\\mathscr{R}");m("ℭ","\\mathfrak{C}");m("ℌ","\\mathfrak{H}");m("ℨ","\\mathfrak{Z}");m("\\Bbbk","\\Bbb{k}");m("·","\\cdotp");m("\\llap","\\mathllap{\\textrm{#1}}");m("\\rlap","\\mathrlap{\\textrm{#1}}");m("\\clap","\\mathclap{\\textrm{#1}}");m("\\mathstrut","\\vphantom{(}");m("\\underbar","\\underline{\\text{#1}}");m("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}');m("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}");m("\\ne","\\neq");m("≠","\\neq");m("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}");m("∉","\\notin");m("≘","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}");m("≙","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}");m("≚","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}");m("≛","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}");m("≝","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}");m("≞","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}");m("≟","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}");m("⟂","\\perp");m("‼","\\mathclose{!\\mkern-0.8mu!}");m("∌","\\notni");m("⌜","\\ulcorner");m("⌝","\\urcorner");m("⌞","\\llcorner");m("⌟","\\lrcorner");m("©","\\copyright");m("®","\\textregistered");m("️","\\textregistered");m("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}');m("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}');m("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}');m("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}');m("\\vdots","\\mathord{\\varvdots\\rule{0pt}{15pt}}");m("⋮","\\vdots");m("\\varGamma","\\mathit{\\Gamma}");m("\\varDelta","\\mathit{\\Delta}");m("\\varTheta","\\mathit{\\Theta}");m("\\varLambda","\\mathit{\\Lambda}");m("\\varXi","\\mathit{\\Xi}");m("\\varPi","\\mathit{\\Pi}");m("\\varSigma","\\mathit{\\Sigma}");m("\\varUpsilon","\\mathit{\\Upsilon}");m("\\varPhi","\\mathit{\\Phi}");m("\\varPsi","\\mathit{\\Psi}");m("\\varOmega","\\mathit{\\Omega}");m("\\substack","\\begin{subarray}{c}#1\\end{subarray}");m("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax");m("\\boxed","\\fbox{$\\displaystyle{#1}$}");m("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;");m("\\implies","\\DOTSB\\;\\Longrightarrow\\;");m("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");var mn={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};m("\\dots",function(a){var e="\\dotso",t=a.expandAfterFuture().text;return t in mn?e=mn[t]:(t.slice(0,4)==="\\not"||t in re.math&&_.contains(["bin","rel"],re.math[t].group))&&(e="\\dotsb"),e});var ea={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};m("\\dotso",function(a){var e=a.future().text;return e in ea?"\\ldots\\,":"\\ldots"});m("\\dotsc",function(a){var e=a.future().text;return e in ea&&e!==","?"\\ldots\\,":"\\ldots"});m("\\cdots",function(a){var e=a.future().text;return e in ea?"\\@cdots\\,":"\\@cdots"});m("\\dotsb","\\cdots");m("\\dotsm","\\cdots");m("\\dotsi","\\!\\cdots");m("\\dotsx","\\ldots\\,");m("\\DOTSI","\\relax");m("\\DOTSB","\\relax");m("\\DOTSX","\\relax");m("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax");m("\\,","\\tmspace+{3mu}{.1667em}");m("\\thinspace","\\,");m("\\>","\\mskip{4mu}");m("\\:","\\tmspace+{4mu}{.2222em}");m("\\medspace","\\:");m("\\;","\\tmspace+{5mu}{.2777em}");m("\\thickspace","\\;");m("\\!","\\tmspace-{3mu}{.1667em}");m("\\negthinspace","\\!");m("\\negmedspace","\\tmspace-{4mu}{.2222em}");m("\\negthickspace","\\tmspace-{5mu}{.277em}");m("\\enspace","\\kern.5em ");m("\\enskip","\\hskip.5em\\relax");m("\\quad","\\hskip1em\\relax");m("\\qquad","\\hskip2em\\relax");m("\\tag","\\@ifstar\\tag@literal\\tag@paren");m("\\tag@paren","\\tag@literal{({#1})}");m("\\tag@literal",a=>{if(a.macros.get("\\df@tag"))throw new T("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"});m("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}");m("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)");m("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}");m("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1");m("\\newline","\\\\\\relax");m("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var ki=M(je["Main-Regular"][84][1]-.7*je["Main-Regular"][65][1]);m("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+ki+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}");m("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+ki+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}");m("\\hspace","\\@ifstar\\@hspacer\\@hspace");m("\\@hspace","\\hskip #1\\relax");m("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax");m("\\ordinarycolon",":");m("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}");m("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}');m("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}');m("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}');m("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}');m("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}');m("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}');m("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}');m("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}');m("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}');m("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}');m("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}');m("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}');m("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}');m("∷","\\dblcolon");m("∹","\\eqcolon");m("≔","\\coloneqq");m("≕","\\eqqcolon");m("⩴","\\Coloneqq");m("\\ratio","\\vcentcolon");m("\\coloncolon","\\dblcolon");m("\\colonequals","\\coloneqq");m("\\coloncolonequals","\\Coloneqq");m("\\equalscolon","\\eqqcolon");m("\\equalscoloncolon","\\Eqqcolon");m("\\colonminus","\\coloneq");m("\\coloncolonminus","\\Coloneq");m("\\minuscolon","\\eqcolon");m("\\minuscoloncolon","\\Eqcolon");m("\\coloncolonapprox","\\Colonapprox");m("\\coloncolonsim","\\Colonsim");m("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}");m("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}");m("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}");m("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}");m("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}");m("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}");m("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}");m("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}");m("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}");m("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}");m("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}");m("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}");m("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}");m("\\gvertneqq","\\html@mathml{\\@gvertneqq}{≩}");m("\\lvertneqq","\\html@mathml{\\@lvertneqq}{≨}");m("\\ngeqq","\\html@mathml{\\@ngeqq}{≱}");m("\\ngeqslant","\\html@mathml{\\@ngeqslant}{≱}");m("\\nleqq","\\html@mathml{\\@nleqq}{≰}");m("\\nleqslant","\\html@mathml{\\@nleqslant}{≰}");m("\\nshortmid","\\html@mathml{\\@nshortmid}{∤}");m("\\nshortparallel","\\html@mathml{\\@nshortparallel}{∦}");m("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{⊈}");m("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{⊉}");m("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{⊊}");m("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{⫋}");m("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{⊋}");m("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{⫌}");m("\\imath","\\html@mathml{\\@imath}{ı}");m("\\jmath","\\html@mathml{\\@jmath}{ȷ}");m("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}");m("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}");m("⟦","\\llbracket");m("⟧","\\rrbracket");m("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}");m("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}");m("⦃","\\lBrace");m("⦄","\\rBrace");m("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}");m("⦵","\\minuso");m("\\darr","\\downarrow");m("\\dArr","\\Downarrow");m("\\Darr","\\Downarrow");m("\\lang","\\langle");m("\\rang","\\rangle");m("\\uarr","\\uparrow");m("\\uArr","\\Uparrow");m("\\Uarr","\\Uparrow");m("\\N","\\mathbb{N}");m("\\R","\\mathbb{R}");m("\\Z","\\mathbb{Z}");m("\\alef","\\aleph");m("\\alefsym","\\aleph");m("\\Alpha","\\mathrm{A}");m("\\Beta","\\mathrm{B}");m("\\bull","\\bullet");m("\\Chi","\\mathrm{X}");m("\\clubs","\\clubsuit");m("\\cnums","\\mathbb{C}");m("\\Complex","\\mathbb{C}");m("\\Dagger","\\ddagger");m("\\diamonds","\\diamondsuit");m("\\empty","\\emptyset");m("\\Epsilon","\\mathrm{E}");m("\\Eta","\\mathrm{H}");m("\\exist","\\exists");m("\\harr","\\leftrightarrow");m("\\hArr","\\Leftrightarrow");m("\\Harr","\\Leftrightarrow");m("\\hearts","\\heartsuit");m("\\image","\\Im");m("\\infin","\\infty");m("\\Iota","\\mathrm{I}");m("\\isin","\\in");m("\\Kappa","\\mathrm{K}");m("\\larr","\\leftarrow");m("\\lArr","\\Leftarrow");m("\\Larr","\\Leftarrow");m("\\lrarr","\\leftrightarrow");m("\\lrArr","\\Leftrightarrow");m("\\Lrarr","\\Leftrightarrow");m("\\Mu","\\mathrm{M}");m("\\natnums","\\mathbb{N}");m("\\Nu","\\mathrm{N}");m("\\Omicron","\\mathrm{O}");m("\\plusmn","\\pm");m("\\rarr","\\rightarrow");m("\\rArr","\\Rightarrow");m("\\Rarr","\\Rightarrow");m("\\real","\\Re");m("\\reals","\\mathbb{R}");m("\\Reals","\\mathbb{R}");m("\\Rho","\\mathrm{P}");m("\\sdot","\\cdot");m("\\sect","\\S");m("\\spades","\\spadesuit");m("\\sub","\\subset");m("\\sube","\\subseteq");m("\\supe","\\supseteq");m("\\Tau","\\mathrm{T}");m("\\thetasym","\\vartheta");m("\\weierp","\\wp");m("\\Zeta","\\mathrm{Z}");m("\\argmin","\\DOTSB\\operatorname*{arg\\,min}");m("\\argmax","\\DOTSB\\operatorname*{arg\\,max}");m("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits");m("\\bra","\\mathinner{\\langle{#1}|}");m("\\ket","\\mathinner{|{#1}\\rangle}");m("\\braket","\\mathinner{\\langle{#1}\\rangle}");m("\\Bra","\\left\\langle#1\\right|");m("\\Ket","\\left|#1\\right\\rangle");var Ai=a=>e=>{var t=e.consumeArg().tokens,r=e.consumeArg().tokens,n=e.consumeArg().tokens,i=e.consumeArg().tokens,s=e.macros.get("|"),o=e.macros.get("\\|");e.macros.beginGroup();var h=g=>x=>{a&&(x.macros.set("|",s),n.length&&x.macros.set("\\|",o));var w=g;if(!g&&n.length){var S=x.future();S.text==="|"&&(x.popToken(),w=!0)}return{tokens:w?n:r,numArgs:0}};e.macros.set("|",h(!1)),n.length&&e.macros.set("\\|",h(!0));var d=e.consumeArg().tokens,p=e.expandTokens([...i,...d,...t]);return e.macros.endGroup(),{tokens:p.reverse(),numArgs:0}};m("\\bra@ket",Ai(!1));m("\\bra@set",Ai(!0));m("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}");m("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}");m("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}");m("\\angln","{\\angl n}");m("\\blue","\\textcolor{##6495ed}{#1}");m("\\orange","\\textcolor{##ffa500}{#1}");m("\\pink","\\textcolor{##ff00af}{#1}");m("\\red","\\textcolor{##df0030}{#1}");m("\\green","\\textcolor{##28ae7b}{#1}");m("\\gray","\\textcolor{gray}{#1}");m("\\purple","\\textcolor{##9d38bd}{#1}");m("\\blueA","\\textcolor{##ccfaff}{#1}");m("\\blueB","\\textcolor{##80f6ff}{#1}");m("\\blueC","\\textcolor{##63d9ea}{#1}");m("\\blueD","\\textcolor{##11accd}{#1}");m("\\blueE","\\textcolor{##0c7f99}{#1}");m("\\tealA","\\textcolor{##94fff5}{#1}");m("\\tealB","\\textcolor{##26edd5}{#1}");m("\\tealC","\\textcolor{##01d1c1}{#1}");m("\\tealD","\\textcolor{##01a995}{#1}");m("\\tealE","\\textcolor{##208170}{#1}");m("\\greenA","\\textcolor{##b6ffb0}{#1}");m("\\greenB","\\textcolor{##8af281}{#1}");m("\\greenC","\\textcolor{##74cf70}{#1}");m("\\greenD","\\textcolor{##1fab54}{#1}");m("\\greenE","\\textcolor{##0d923f}{#1}");m("\\goldA","\\textcolor{##ffd0a9}{#1}");m("\\goldB","\\textcolor{##ffbb71}{#1}");m("\\goldC","\\textcolor{##ff9c39}{#1}");m("\\goldD","\\textcolor{##e07d10}{#1}");m("\\goldE","\\textcolor{##a75a05}{#1}");m("\\redA","\\textcolor{##fca9a9}{#1}");m("\\redB","\\textcolor{##ff8482}{#1}");m("\\redC","\\textcolor{##f9685d}{#1}");m("\\redD","\\textcolor{##e84d39}{#1}");m("\\redE","\\textcolor{##bc2612}{#1}");m("\\maroonA","\\textcolor{##ffbde0}{#1}");m("\\maroonB","\\textcolor{##ff92c6}{#1}");m("\\maroonC","\\textcolor{##ed5fa6}{#1}");m("\\maroonD","\\textcolor{##ca337c}{#1}");m("\\maroonE","\\textcolor{##9e034e}{#1}");m("\\purpleA","\\textcolor{##ddd7ff}{#1}");m("\\purpleB","\\textcolor{##c6b9fc}{#1}");m("\\purpleC","\\textcolor{##aa87ff}{#1}");m("\\purpleD","\\textcolor{##7854ab}{#1}");m("\\purpleE","\\textcolor{##543b78}{#1}");m("\\mintA","\\textcolor{##f5f9e8}{#1}");m("\\mintB","\\textcolor{##edf2df}{#1}");m("\\mintC","\\textcolor{##e0e5cc}{#1}");m("\\grayA","\\textcolor{##f6f7f7}{#1}");m("\\grayB","\\textcolor{##f0f1f2}{#1}");m("\\grayC","\\textcolor{##e3e5e6}{#1}");m("\\grayD","\\textcolor{##d6d8da}{#1}");m("\\grayE","\\textcolor{##babec2}{#1}");m("\\grayF","\\textcolor{##888d93}{#1}");m("\\grayG","\\textcolor{##626569}{#1}");m("\\grayH","\\textcolor{##3b3e40}{#1}");m("\\grayI","\\textcolor{##21242c}{#1}");m("\\kaBlue","\\textcolor{##314453}{#1}");m("\\kaGreen","\\textcolor{##71B307}{#1}");var Si={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class cl{constructor(e,t,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new ol(hl,t.macros),this.mode=r,this.stack=[]}feed(e){this.lexer=new hn(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var t,r,n;if(e){if(this.consumeSpaces(),this.future().text!=="[")return null;t=this.popToken(),{tokens:n,end:r}=this.consumeArg(["]"])}else({tokens:n,start:t,end:r}=this.consumeArg());return this.pushToken(new Ne("EOF",r.loc)),this.pushTokens(n),t.range(r,"")}consumeSpaces(){for(;;){var e=this.future();if(e.text===" ")this.stack.pop();else break}}consumeArg(e){var t=[],r=e&&e.length>0;r||this.consumeSpaces();var n=this.future(),i,s=0,o=0;do{if(i=this.popToken(),t.push(i),i.text==="{")++s;else if(i.text==="}"){if(--s,s===-1)throw new T("Extra }",i)}else if(i.text==="EOF")throw new T("Unexpected end of input in a macro argument, expected '"+(e&&r?e[o]:"}")+"'",i);if(e&&r)if((s===0||s===1&&e[o]==="{")&&i.text===e[o]){if(++o,o===e.length){t.splice(-o,o);break}}else o=0}while(s!==0||r);return n.text==="{"&&t[t.length-1].text==="}"&&(t.pop(),t.shift()),t.reverse(),{tokens:t,start:n,end:i}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new T("The length of delimiters doesn't match the number of args!");for(var r=t[0],n=0;n<r.length;n++){var i=this.popToken();if(r[n]!==i.text)throw new T("Use of the macro doesn't match its definition",i)}}for(var s=[],o=0;o<e;o++)s.push(this.consumeArg(t&&t[o+1]).tokens);return s}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new T("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){var t=this.popToken(),r=t.text,n=t.noexpand?null:this._getExpansion(r);if(n==null||e&&n.unexpandable){if(e&&n==null&&r[0]==="\\"&&!this.isDefined(r))throw new T("Undefined control sequence: "+r);return this.pushToken(t),!1}this.countExpansion(1);var i=n.tokens,s=this.consumeArgs(n.numArgs,n.delimiters);if(n.numArgs){i=i.slice();for(var o=i.length-1;o>=0;--o){var h=i[o];if(h.text==="#"){if(o===0)throw new T("Incomplete placeholder at end of macro body",h);if(h=i[--o],h.text==="#")i.splice(o+1,1);else if(/^[1-9]$/.test(h.text))i.splice(o,2,...s[+h.text-1]);else throw new T("Not a valid argument number",h)}}}return this.pushTokens(i),i.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(this.expandOnce()===!1){var e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new Ne(e)]):void 0}expandTokens(e){var t=[],r=this.stack.length;for(this.pushTokens(e);this.stack.length>r;)if(this.expandOnce(!0)===!1){var n=this.stack.pop();n.treatAsRelax&&(n.noexpand=!1,n.treatAsRelax=!1),t.push(n)}return this.countExpansion(t.length),t}expandMacroAsText(e){var t=this.expandMacro(e);return t&&t.map(r=>r.text).join("")}_getExpansion(e){var t=this.macros.get(e);if(t==null)return t;if(e.length===1){var r=this.lexer.catcodes[e];if(r!=null&&r!==13)return}var n=typeof t=="function"?t(this):t;if(typeof n=="string"){var i=0;if(n.indexOf("#")!==-1)for(var s=n.replace(/##/g,"");s.indexOf("#"+(i+1))!==-1;)++i;for(var o=new hn(n,this.settings),h=[],d=o.lex();d.text!=="EOF";)h.push(d),d=o.lex();h.reverse();var p={tokens:h,numArgs:i};return p}return n}isDefined(e){return this.macros.has(e)||y0.hasOwnProperty(e)||re.math.hasOwnProperty(e)||re.text.hasOwnProperty(e)||Si.hasOwnProperty(e)}isExpandable(e){var t=this.macros.get(e);return t!=null?typeof t=="string"||typeof t=="function"||!t.unexpandable:y0.hasOwnProperty(e)&&!y0[e].primitive}}var dn=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,Ft=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9","ₐ":"a","ₑ":"e","ₕ":"h","ᵢ":"i","ⱼ":"j","ₖ":"k","ₗ":"l","ₘ":"m","ₙ":"n","ₒ":"o","ₚ":"p","ᵣ":"r","ₛ":"s","ₜ":"t","ᵤ":"u","ᵥ":"v","ₓ":"x","ᵦ":"β","ᵧ":"γ","ᵨ":"ρ","ᵩ":"ϕ","ᵪ":"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","¹":"1","²":"2","³":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9","ᴬ":"A","ᴮ":"B","ᴰ":"D","ᴱ":"E","ᴳ":"G","ᴴ":"H","ᴵ":"I","ᴶ":"J","ᴷ":"K","ᴸ":"L","ᴹ":"M","ᴺ":"N","ᴼ":"O","ᴾ":"P","ᴿ":"R","ᵀ":"T","ᵁ":"U","ⱽ":"V","ᵂ":"W","ᵃ":"a","ᵇ":"b","ᶜ":"c","ᵈ":"d","ᵉ":"e","ᶠ":"f","ᵍ":"g",ʰ:"h","ⁱ":"i",ʲ:"j","ᵏ":"k",ˡ:"l","ᵐ":"m",ⁿ:"n","ᵒ":"o","ᵖ":"p",ʳ:"r",ˢ:"s","ᵗ":"t","ᵘ":"u","ᵛ":"v",ʷ:"w",ˣ:"x",ʸ:"y","ᶻ":"z","ᵝ":"β","ᵞ":"γ","ᵟ":"δ","ᵠ":"ϕ","ᵡ":"χ","ᶿ":"θ"}),kr={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},pn={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"};class Xt{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new cl(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(t===void 0&&(t=!0),this.fetch().text!==e)throw new T("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var t=this.nextToken;this.consume(),this.gullet.pushToken(new Ne("}")),this.gullet.pushTokens(e);var r=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,r}parseExpression(e,t){for(var r=[];;){this.mode==="math"&&this.consumeSpaces();var n=this.fetch();if(Xt.endOfExpression.indexOf(n.text)!==-1||t&&n.text===t||e&&y0[n.text]&&y0[n.text].infix)break;var i=this.parseAtom(t);if(i){if(i.type==="internal")continue}else break;r.push(i)}return this.mode==="text"&&this.formLigatures(r),this.handleInfixNodes(r)}handleInfixNodes(e){for(var t=-1,r,n=0;n<e.length;n++)if(e[n].type==="infix"){if(t!==-1)throw new T("only one infix operator per group",e[n].token);t=n,r=e[n].replaceWith}if(t!==-1&&r){var i,s,o=e.slice(0,t),h=e.slice(t+1);o.length===1&&o[0].type==="ordgroup"?i=o[0]:i={type:"ordgroup",mode:this.mode,body:o},h.length===1&&h[0].type==="ordgroup"?s=h[0]:s={type:"ordgroup",mode:this.mode,body:h};var d;return r==="\\\\abovefrac"?d=this.callFunction(r,[i,e[t],s],[]):d=this.callFunction(r,[i,s],[]),[d]}else return e}handleSupSubscript(e){var t=this.fetch(),r=t.text;this.consume(),this.consumeSpaces();var n=this.parseGroup(e);if(!n)throw new T("Expected group after '"+r+"'",t);return n}formatUnsupportedCmd(e){for(var t=[],r=0;r<e.length;r++)t.push({type:"textord",mode:"text",text:e[r]});var n={type:"text",mode:this.mode,body:t},i={type:"color",mode:this.mode,color:this.settings.errorColor,body:[n]};return i}parseAtom(e){var t=this.parseGroup("atom",e);if(this.mode==="text")return t;for(var r,n;;){this.consumeSpaces();var i=this.fetch();if(i.text==="\\limits"||i.text==="\\nolimits"){if(t&&t.type==="op"){var s=i.text==="\\limits";t.limits=s,t.alwaysHandleSupSub=!0}else if(t&&t.type==="operatorname")t.alwaysHandleSupSub&&(t.limits=i.text==="\\limits");else throw new T("Limit controls must follow a math operator",i);this.consume()}else if(i.text==="^"){if(r)throw new T("Double superscript",i);r=this.handleSupSubscript("superscript")}else if(i.text==="_"){if(n)throw new T("Double subscript",i);n=this.handleSupSubscript("subscript")}else if(i.text==="'"){if(r)throw new T("Double superscript",i);var o={type:"textord",mode:this.mode,text:"\\prime"},h=[o];for(this.consume();this.fetch().text==="'";)h.push(o),this.consume();this.fetch().text==="^"&&h.push(this.handleSupSubscript("superscript")),r={type:"ordgroup",mode:this.mode,body:h}}else if(Ft[i.text]){var d=dn.test(i.text),p=[];for(p.push(new Ne(Ft[i.text])),this.consume();;){var g=this.fetch().text;if(!Ft[g]||dn.test(g)!==d)break;p.unshift(new Ne(Ft[g])),this.consume()}var x=this.subparse(p);d?n={type:"ordgroup",mode:"math",body:x}:r={type:"ordgroup",mode:"math",body:x}}else break}return r||n?{type:"supsub",mode:this.mode,base:t,sup:r,sub:n}:t}parseFunction(e,t){var r=this.fetch(),n=r.text,i=y0[n];if(!i)return null;if(this.consume(),t&&t!=="atom"&&!i.allowedInArgument)throw new T("Got function '"+n+"' with no arguments"+(t?" as "+t:""),r);if(this.mode==="text"&&!i.allowedInText)throw new T("Can't use function '"+n+"' in text mode",r);if(this.mode==="math"&&i.allowedInMath===!1)throw new T("Can't use function '"+n+"' in math mode",r);var{args:s,optArgs:o}=this.parseArguments(n,i);return this.callFunction(n,s,o,r,e)}callFunction(e,t,r,n,i){var s={funcName:e,parser:this,token:n,breakOnTokenText:i},o=y0[e];if(o&&o.handler)return o.handler(s,t,r);throw new T("No function handler for "+e)}parseArguments(e,t){var r=t.numArgs+t.numOptionalArgs;if(r===0)return{args:[],optArgs:[]};for(var n=[],i=[],s=0;s<r;s++){var o=t.argTypes&&t.argTypes[s],h=s<t.numOptionalArgs;(t.primitive&&o==null||t.type==="sqrt"&&s===1&&i[0]==null)&&(o="primitive");var d=this.parseGroupOfType("argument to '"+e+"'",o,h);if(h)i.push(d);else if(d!=null)n.push(d);else throw new T("Null argument, please report this as a bug")}return{args:n,optArgs:i}}parseGroupOfType(e,t,r){switch(t){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseArgumentGroup(r,t);case"hbox":{var n=this.parseArgumentGroup(r,"text");return n!=null?{type:"styling",mode:n.mode,body:[n],style:"text"}:null}case"raw":{var i=this.parseStringGroup("raw",r);return i!=null?{type:"raw",mode:"text",string:i.text}:null}case"primitive":{if(r)throw new T("A primitive argument cannot be optional");var s=this.parseGroup(e);if(s==null)throw new T("Expected group as "+e,this.fetch());return s}case"original":case null:case void 0:return this.parseArgumentGroup(r);default:throw new T("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(e,t){var r=this.gullet.scanArgument(t);if(r==null)return null;for(var n="",i;(i=this.fetch()).text!=="EOF";)n+=i.text,this.consume();return this.consume(),r.text=n,r}parseRegexGroup(e,t){for(var r=this.fetch(),n=r,i="",s;(s=this.fetch()).text!=="EOF"&&e.test(i+s.text);)n=s,i+=n.text,this.consume();if(i==="")throw new T("Invalid "+t+": '"+r.text+"'",r);return r.range(n,i)}parseColorGroup(e){var t=this.parseStringGroup("color",e);if(t==null)return null;var r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!r)throw new T("Invalid color: '"+t.text+"'",t);var n=r[0];return/^[0-9a-f]{6}$/i.test(n)&&(n="#"+n),{type:"color-token",mode:this.mode,color:n}}parseSizeGroup(e){var t,r=!1;if(this.gullet.consumeSpaces(),!e&&this.gullet.future().text!=="{"?t=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):t=this.parseStringGroup("size",e),!t)return null;!e&&t.text.length===0&&(t.text="0pt",r=!0);var n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!n)throw new T("Invalid size: '"+t.text+"'",t);var i={number:+(n[1]+n[2]),unit:n[3]};if(!In(i))throw new T("Invalid unit: '"+i.unit+"'",t);return{type:"size",mode:this.mode,value:i,isBlank:r}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),t==null)return null;var r=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:r}}parseArgumentGroup(e,t){var r=this.gullet.scanArgument(e);if(r==null)return null;var n=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();var i=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var s={type:"ordgroup",mode:this.mode,loc:r.loc,body:i};return t&&this.switchMode(n),s}parseGroup(e,t){var r=this.fetch(),n=r.text,i;if(n==="{"||n==="\\begingroup"){this.consume();var s=n==="{"?"}":"\\endgroup";this.gullet.beginGroup();var o=this.parseExpression(!1,s),h=this.fetch();this.expect(s),this.gullet.endGroup(),i={type:"ordgroup",mode:this.mode,loc:Te.range(r,h),body:o,semisimple:n==="\\begingroup"||void 0}}else if(i=this.parseFunction(t,e)||this.parseSymbol(),i==null&&n[0]==="\\"&&!Si.hasOwnProperty(n)){if(this.settings.throwOnError)throw new T("Undefined control sequence: "+n,r);i=this.formatUnsupportedCmd(n),this.consume()}return i}formLigatures(e){for(var t=e.length-1,r=0;r<t;++r){var n=e[r],i=n.text;i==="-"&&e[r+1].text==="-"&&(r+1<t&&e[r+2].text==="-"?(e.splice(r,3,{type:"textord",mode:"text",loc:Te.range(n,e[r+2]),text:"---"}),t-=2):(e.splice(r,2,{type:"textord",mode:"text",loc:Te.range(n,e[r+1]),text:"--"}),t-=1)),(i==="'"||i==="`")&&e[r+1].text===i&&(e.splice(r,2,{type:"textord",mode:"text",loc:Te.range(n,e[r+1]),text:i+i}),t-=1)}}parseSymbol(){var e=this.fetch(),t=e.text;if(/^\\verb[^a-zA-Z]/.test(t)){this.consume();var r=t.slice(5),n=r.charAt(0)==="*";if(n&&(r=r.slice(1)),r.length<2||r.charAt(0)!==r.slice(-1))throw new T(`\\verb assertion failed --
                    please report what input caused this bug`);return r=r.slice(1,-1),{type:"verb",mode:"text",body:r,star:n}}pn.hasOwnProperty(t[0])&&!re[this.mode][t[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+t[0]+'" used in math mode',e),t=pn[t[0]]+t.slice(1));var i=ll.exec(t);i&&(t=t.substring(0,i.index),t==="i"?t="ı":t==="j"&&(t="ȷ"));var s;if(re[this.mode][t]){this.settings.strict&&this.mode==="math"&&Mr.indexOf(t)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+t[0]+'" used in math mode',e);var o=re[this.mode][t].group,h=Te.range(e),d;if(Js.hasOwnProperty(o)){var p=o;d={type:"atom",mode:this.mode,family:p,loc:h,text:t}}else d={type:o,mode:this.mode,loc:h,text:t};s=d}else if(t.charCodeAt(0)>=128)this.settings.strict&&(Nn(t.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+t[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+t[0]+'"'+(" ("+t.charCodeAt(0)+")"),e)),s={type:"textord",mode:"text",loc:Te.range(e),text:t};else return null;if(this.consume(),i)for(var g=0;g<i[0].length;g++){var x=i[0][g];if(!kr[x])throw new T("Unknown accent ' "+x+"'",e);var w=kr[x][this.mode]||kr[x].text;if(!w)throw new T("Accent "+x+" unsupported in "+this.mode+" mode",e);s={type:"accent",mode:this.mode,loc:Te.range(e),label:w,isStretchy:!1,isShifty:!0,base:s}}return s}}Xt.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var ta=function(e,t){if(!(typeof e=="string"||e instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var r=new Xt(e,t);delete r.gullet.macros.current["\\df@tag"];var n=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new T("\\tag works only in display equations");n=[{type:"tag",mode:"text",body:n,tag:r.subparse([new Ne("\\df@tag")])}]}return n},Fi=function(e,t,r){t.textContent="";var n=ra(e,r).toNode();t.appendChild(n)};typeof document<"u"&&document.compatMode!=="CSS1Compat"&&(typeof console<"u"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),Fi=function(){throw new T("KaTeX doesn't work in quirks mode.")});var ml=function(e,t){var r=ra(e,t).toMarkup();return r},dl=function(e,t){var r=new Lr(t);return ta(e,r)},Ei=function(e,t,r){if(r.throwOnError||!(e instanceof T))throw e;var n=D.makeSpan(["katex-error"],[new Oe(t)]);return n.setAttribute("title",e.toString()),n.setAttribute("style","color:"+r.errorColor),n},ra=function(e,t){var r=new Lr(t);try{var n=ta(e,r);return D1(n,e,r)}catch(i){return Ei(i,e,r)}},pl=function(e,t){var r=new Lr(t);try{var n=ta(e,r);return k1(n,e,r)}catch(i){return Ei(i,e,r)}},fn={version:"0.16.10",render:Fi,renderToString:ml,ParseError:T,SETTINGS_SCHEMA:zt,__parse:dl,__renderToDomTree:ra,__renderToHTMLTree:pl,__setFontMetrics:Ws,__defineSymbol:l,__defineFunction:B,__defineMacro:m,__domTree:{Span:ut,Anchor:Pr,SymbolNode:Oe,SvgNode:d0,PathNode:w0,LineNode:Cr}},fl=function(e,t,r){for(var n=r,i=0,s=e.length;n<t.length;){var o=t[n];if(i<=0&&t.slice(n,n+s)===e)return n;o==="\\"?n++:o==="{"?i++:o==="}"&&i--,n++}return-1},vl=function(e){return e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},gl=/^\\begin{/,bl=function(e,t){for(var r,n=[],i=new RegExp("("+t.map(d=>vl(d.left)).join("|")+")");r=e.search(i),r!==-1;){r>0&&(n.push({type:"text",data:e.slice(0,r)}),e=e.slice(r));var s=t.findIndex(d=>e.startsWith(d.left));if(r=fl(t[s].right,e,t[s].left.length),r===-1)break;var o=e.slice(0,r+t[s].right.length),h=gl.test(o)?o:e.slice(t[s].left.length,r);n.push({type:"math",data:h,rawData:o,display:t[s].display}),e=e.slice(r+t[s].right.length)}return e!==""&&n.push({type:"text",data:e}),n},yl=function(e,t){var r=bl(e,t.delimiters);if(r.length===1&&r[0].type==="text")return null;for(var n=document.createDocumentFragment(),i=0;i<r.length;i++)if(r[i].type==="text")n.appendChild(document.createTextNode(r[i].data));else{var s=document.createElement("span"),o=r[i].data;t.displayMode=r[i].display;try{t.preProcess&&(o=t.preProcess(o)),fn.render(o,s,t)}catch(h){if(!(h instanceof fn.ParseError))throw h;t.errorCallback("KaTeX auto-render: Failed to parse `"+r[i].data+"` with ",h),n.appendChild(document.createTextNode(r[i].rawData));continue}n.appendChild(s)}return n},xl=function a(e,t){for(var r=0;r<e.childNodes.length;r++){var n=e.childNodes[r];if(n.nodeType===3){for(var i=n.textContent,s=n.nextSibling,o=0;s&&s.nodeType===Node.TEXT_NODE;)i+=s.textContent,s=s.nextSibling,o++;var h=yl(i,t);if(h){for(var d=0;d<o;d++)n.nextSibling.remove();r+=h.childNodes.length-1,e.replaceChild(h,n)}else r+=o}else n.nodeType===1&&function(){var p=" "+n.className+" ",g=t.ignoredTags.indexOf(n.nodeName.toLowerCase())===-1&&t.ignoredClasses.every(x=>p.indexOf(" "+x+" ")===-1);g&&a(n,t)}()}},wl=function(e,t){if(!e)throw new Error("No element provided to render");var r={};for(var n in t)t.hasOwnProperty(n)&&(r[n]=t[n]);r.delimiters=r.delimiters||[{left:"$$",right:"$$",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"\\begin{equation}",right:"\\end{equation}",display:!0},{left:"\\begin{align}",right:"\\end{align}",display:!0},{left:"\\begin{alignat}",right:"\\end{alignat}",display:!0},{left:"\\begin{gather}",right:"\\end{gather}",display:!0},{left:"\\begin{CD}",right:"\\end{CD}",display:!0},{left:"\\[",right:"\\]",display:!0}],r.ignoredTags=r.ignoredTags||["script","noscript","style","textarea","pre","code","option"],r.ignoredClasses=r.ignoredClasses||[],r.errorCallback=r.errorCallback||console.error,r.macros=r.macros||{},xl(e,r)};function aa(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let I0=aa();function Ti(a){I0=a}const Ci=/[&<>"']/,Dl=new RegExp(Ci.source,"g"),Mi=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,kl=new RegExp(Mi.source,"g"),Al={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},vn=a=>Al[a];function Ce(a,e){if(e){if(Ci.test(a))return a.replace(Dl,vn)}else if(Mi.test(a))return a.replace(kl,vn);return a}const Sl=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function Fl(a){return a.replace(Sl,(e,t)=>(t=t.toLowerCase(),t==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""))}const El=/(^|[^\[])\^/g;function Q(a,e){let t=typeof a=="string"?a:a.source;e=e||"";const r={replace:(n,i)=>{let s=typeof i=="string"?i:i.source;return s=s.replace(El,"$1"),t=t.replace(n,s),r},getRegex:()=>new RegExp(t,e)};return r}function gn(a){try{a=encodeURI(a).replace(/%25/g,"%")}catch{return null}return a}const nt={exec:()=>null};function bn(a,e){const t=a.replace(/\|/g,(i,s,o)=>{let h=!1,d=s;for(;--d>=0&&o[d]==="\\";)h=!h;return h?"|":" |"}),r=t.split(/ \|/);let n=0;if(r[0].trim()||r.shift(),r.length>0&&!r[r.length-1].trim()&&r.pop(),e)if(r.length>e)r.splice(e);else for(;r.length<e;)r.push("");for(;n<r.length;n++)r[n]=r[n].trim().replace(/\\\|/g,"|");return r}function Et(a,e,t){const r=a.length;if(r===0)return"";let n=0;for(;n<r;){const i=a.charAt(r-n-1);if(i===e&&!t)n++;else if(i!==e&&t)n++;else break}return a.slice(0,r-n)}function Tl(a,e){if(a.indexOf(e[1])===-1)return-1;let t=0;for(let r=0;r<a.length;r++)if(a[r]==="\\")r++;else if(a[r]===e[0])t++;else if(a[r]===e[1]&&(t--,t<0))return r;return-1}function yn(a,e,t,r){const n=e.href,i=e.title?Ce(e.title):null,s=a[1].replace(/\\([\[\]])/g,"$1");if(a[0].charAt(0)!=="!"){r.state.inLink=!0;const o={type:"link",raw:t,href:n,title:i,text:s,tokens:r.inlineTokens(s)};return r.state.inLink=!1,o}return{type:"image",raw:t,href:n,title:i,text:Ce(s)}}function Cl(a,e){const t=a.match(/^(\s+)(?:```)/);if(t===null)return e;const r=t[1];return e.split(`
`).map(n=>{const i=n.match(/^\s+/);if(i===null)return n;const[s]=i;return s.length>=r.length?n.slice(r.length):n}).join(`
`)}class qt{options;rules;lexer;constructor(e){this.options=e||I0}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const r=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?r:Et(r,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const r=t[0],n=Cl(r,t[3]||"");return{type:"code",raw:r,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let r=t[2].trim();if(/#$/.test(r)){const n=Et(r,"#");(this.options.pedantic||!n||/ $/.test(n))&&(r=n.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const r=Et(t[0].replace(/^ *>[ \t]?/gm,""),`
`),n=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(r);return this.lexer.state.top=n,{type:"blockquote",raw:t[0],tokens:i,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let r=t[1].trim();const n=r.length>1,i={type:"list",raw:"",ordered:n,start:n?+r.slice(0,-1):"",loose:!1,items:[]};r=n?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=n?r:"[*+-]");const s=new RegExp(`^( {0,3}${r})((?:[	 ][^\\n]*)?(?:\\n|$))`);let o="",h="",d=!1;for(;e;){let p=!1;if(!(t=s.exec(e))||this.rules.block.hr.test(e))break;o=t[0],e=e.substring(o.length);let g=t[2].split(`
`,1)[0].replace(/^\t+/,k=>" ".repeat(3*k.length)),x=e.split(`
`,1)[0],w=0;this.options.pedantic?(w=2,h=g.trimStart()):(w=t[2].search(/[^ ]/),w=w>4?1:w,h=g.slice(w),w+=t[1].length);let S=!1;if(!g&&/^ *$/.test(x)&&(o+=x+`
`,e=e.substring(x.length+1),p=!0),!p){const k=new RegExp(`^ {0,${Math.min(3,w-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),L=new RegExp(`^ {0,${Math.min(3,w-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),I=new RegExp(`^ {0,${Math.min(3,w-1)}}(?:\`\`\`|~~~)`),G=new RegExp(`^ {0,${Math.min(3,w-1)}}#`);for(;e;){const $=e.split(`
`,1)[0];if(x=$,this.options.pedantic&&(x=x.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),I.test(x)||G.test(x)||k.test(x)||L.test(e))break;if(x.search(/[^ ]/)>=w||!x.trim())h+=`
`+x.slice(w);else{if(S||g.search(/[^ ]/)>=4||I.test(g)||G.test(g)||L.test(g))break;h+=`
`+x}!S&&!x.trim()&&(S=!0),o+=$+`
`,e=e.substring($.length+1),g=x.slice(w)}}i.loose||(d?i.loose=!0:/\n *\n *$/.test(o)&&(d=!0));let E=null,z;this.options.gfm&&(E=/^\[[ xX]\] /.exec(h),E&&(z=E[0]!=="[ ] ",h=h.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:o,task:!!E,checked:z,loose:!1,text:h,tokens:[]}),i.raw+=o}i.items[i.items.length-1].raw=o.trimEnd(),i.items[i.items.length-1].text=h.trimEnd(),i.raw=i.raw.trimEnd();for(let p=0;p<i.items.length;p++)if(this.lexer.state.top=!1,i.items[p].tokens=this.lexer.blockTokens(i.items[p].text,[]),!i.loose){const g=i.items[p].tokens.filter(w=>w.type==="space"),x=g.length>0&&g.some(w=>/\n.*\n/.test(w.raw));i.loose=x}if(i.loose)for(let p=0;p<i.items.length;p++)i.items[p].loose=!0;return i}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const r=t[1].toLowerCase().replace(/\s+/g," "),n=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:r,raw:t[0],href:n,title:i}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const r=bn(t[1]),n=t[2].replace(/^\||\| *$/g,"").split("|"),i=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],s={type:"table",raw:t[0],header:[],align:[],rows:[]};if(r.length===n.length){for(const o of n)/^ *-+: *$/.test(o)?s.align.push("right"):/^ *:-+: *$/.test(o)?s.align.push("center"):/^ *:-+ *$/.test(o)?s.align.push("left"):s.align.push(null);for(const o of r)s.header.push({text:o,tokens:this.lexer.inline(o)});for(const o of i)s.rows.push(bn(o,s.header.length).map(h=>({text:h,tokens:this.lexer.inline(h)})));return s}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const r=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:r,tokens:this.lexer.inline(r)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:Ce(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const r=t[2].trim();if(!this.options.pedantic&&/^</.test(r)){if(!/>$/.test(r))return;const s=Et(r.slice(0,-1),"\\");if((r.length-s.length)%2===0)return}else{const s=Tl(t[2],"()");if(s>-1){const h=(t[0].indexOf("!")===0?5:4)+t[1].length+s;t[2]=t[2].substring(0,s),t[0]=t[0].substring(0,h).trim(),t[3]=""}}let n=t[2],i="";if(this.options.pedantic){const s=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);s&&(n=s[1],i=s[3])}else i=t[3]?t[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(this.options.pedantic&&!/>$/.test(r)?n=n.slice(1):n=n.slice(1,-1)),yn(t,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let r;if((r=this.rules.inline.reflink.exec(e))||(r=this.rules.inline.nolink.exec(e))){const n=(r[2]||r[1]).replace(/\s+/g," "),i=t[n.toLowerCase()];if(!i){const s=r[0].charAt(0);return{type:"text",raw:s,text:s}}return yn(r,i,r[0],this.lexer)}}emStrong(e,t,r=""){let n=this.rules.inline.emStrongLDelim.exec(e);if(!n||n[3]&&r.match(/[\p{L}\p{N}]/u))return;if(!(n[1]||n[2]||"")||!r||this.rules.inline.punctuation.exec(r)){const s=[...n[0]].length-1;let o,h,d=s,p=0;const g=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(g.lastIndex=0,t=t.slice(-1*e.length+s);(n=g.exec(t))!=null;){if(o=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!o)continue;if(h=[...o].length,n[3]||n[4]){d+=h;continue}else if((n[5]||n[6])&&s%3&&!((s+h)%3)){p+=h;continue}if(d-=h,d>0)continue;h=Math.min(h,h+d+p);const x=[...n[0]][0].length,w=e.slice(0,s+n.index+x+h);if(Math.min(s,h)%2){const E=w.slice(1,-1);return{type:"em",raw:w,text:E,tokens:this.lexer.inlineTokens(E)}}const S=w.slice(2,-2);return{type:"strong",raw:w,text:S,tokens:this.lexer.inlineTokens(S)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let r=t[2].replace(/\n/g," ");const n=/[^ ]/.test(r),i=/^ /.test(r)&&/ $/.test(r);return n&&i&&(r=r.substring(1,r.length-1)),r=Ce(r,!0),{type:"codespan",raw:t[0],text:r}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let r,n;return t[2]==="@"?(r=Ce(t[1]),n="mailto:"+r):(r=Ce(t[1]),n=r),{type:"link",raw:t[0],text:r,href:n,tokens:[{type:"text",raw:r,text:r}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let r,n;if(t[2]==="@")r=Ce(t[0]),n="mailto:"+r;else{let i;do i=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??"";while(i!==t[0]);r=Ce(t[0]),t[1]==="www."?n="http://"+t[0]:n=t[0]}return{type:"link",raw:t[0],text:r,href:n,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let r;return this.lexer.state.inRawBlock?r=t[0]:r=Ce(t[0]),{type:"text",raw:t[0],text:r}}}}const Ml=/^(?: *(?:\n|$))+/,zl=/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,Bl=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,ht=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Rl=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,zi=/(?:[*+-]|\d{1,9}[.)])/,Bi=Q(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,zi).getRegex(),na=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Nl=/^[^\n]+/,ia=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Il=Q(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",ia).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Ol=Q(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,zi).getRegex(),Zt="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",sa=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Ll=Q("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",sa).replace("tag",Zt).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Ri=Q(na).replace("hr",ht).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Zt).getRegex(),_l=Q(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Ri).getRegex(),la={blockquote:_l,code:zl,def:Il,fences:Bl,heading:Rl,hr:ht,html:Ll,lheading:Bi,list:Ol,newline:Ml,paragraph:Ri,table:nt,text:Nl},xn=Q("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",ht).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Zt).getRegex(),ql={...la,table:xn,paragraph:Q(na).replace("hr",ht).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",xn).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Zt).getRegex()},Pl={...la,html:Q(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",sa).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:nt,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Q(na).replace("hr",ht).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Bi).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Ni=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Hl=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Ii=/^( {2,}|\\)\n(?!\s*$)/,$l=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,ct="\\p{P}\\p{S}",Ul=Q(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,ct).getRegex(),Gl=/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,Vl=Q(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,ct).getRegex(),Wl=Q("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,ct).getRegex(),Yl=Q("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,ct).getRegex(),Xl=Q(/\\([punct])/,"gu").replace(/punct/g,ct).getRegex(),Zl=Q(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),jl=Q(sa).replace("(?:-->|$)","-->").getRegex(),Kl=Q("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",jl).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Pt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ql=Q(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Pt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Oi=Q(/^!?\[(label)\]\[(ref)\]/).replace("label",Pt).replace("ref",ia).getRegex(),Li=Q(/^!?\[(ref)\](?:\[\])?/).replace("ref",ia).getRegex(),Jl=Q("reflink|nolink(?!\\()","g").replace("reflink",Oi).replace("nolink",Li).getRegex(),ua={_backpedal:nt,anyPunctuation:Xl,autolink:Zl,blockSkip:Gl,br:Ii,code:Hl,del:nt,emStrongLDelim:Vl,emStrongRDelimAst:Wl,emStrongRDelimUnd:Yl,escape:Ni,link:Ql,nolink:Li,punctuation:Ul,reflink:Oi,reflinkSearch:Jl,tag:Kl,text:$l,url:nt},eu={...ua,link:Q(/^!?\[(label)\]\((.*?)\)/).replace("label",Pt).getRegex(),reflink:Q(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Pt).getRegex()},Or={...ua,escape:Q(Ni).replace("])","~|])").getRegex(),url:Q(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},tu={...Or,br:Q(Ii).replace("{2,}","*").getRegex(),text:Q(Or.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Tt={normal:la,gfm:ql,pedantic:Pl},et={normal:ua,gfm:Or,breaks:tu,pedantic:eu};class Ke{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||I0,this.options.tokenizer=this.options.tokenizer||new qt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:Tt.normal,inline:et.normal};this.options.pedantic?(t.block=Tt.pedantic,t.inline=et.pedantic):this.options.gfm&&(t.block=Tt.gfm,this.options.breaks?t.inline=et.breaks:t.inline=et.gfm),this.tokenizer.rules=t}static get rules(){return{block:Tt,inline:et}}static lex(e,t){return new Ke(t).lex(e)}static lexInline(e,t){return new Ke(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const r=this.inlineQueue[t];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){this.options.pedantic?e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e=e.replace(/^( *)(\t+)/gm,(o,h,d)=>h+"    ".repeat(d.length));let r,n,i,s;for(;e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(o=>(r=o.call({lexer:this},e,t))?(e=e.substring(r.raw.length),t.push(r),!0):!1))){if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length),r.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length),n=t[t.length-1],n&&(n.type==="paragraph"||n.type==="text")?(n.raw+=`
`+r.raw,n.text+=`
`+r.text,this.inlineQueue[this.inlineQueue.length-1].src=n.text):t.push(r);continue}if(r=this.tokenizer.fences(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.heading(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.hr(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.blockquote(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.list(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.html(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length),n=t[t.length-1],n&&(n.type==="paragraph"||n.type==="text")?(n.raw+=`
`+r.raw,n.text+=`
`+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=n.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.lheading(e)){e=e.substring(r.raw.length),t.push(r);continue}if(i=e,this.options.extensions&&this.options.extensions.startBlock){let o=1/0;const h=e.slice(1);let d;this.options.extensions.startBlock.forEach(p=>{d=p.call({lexer:this},h),typeof d=="number"&&d>=0&&(o=Math.min(o,d))}),o<1/0&&o>=0&&(i=e.substring(0,o+1))}if(this.state.top&&(r=this.tokenizer.paragraph(i))){n=t[t.length-1],s&&n.type==="paragraph"?(n.raw+=`
`+r.raw,n.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):t.push(r),s=i.length!==e.length,e=e.substring(r.raw.length);continue}if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length),n=t[t.length-1],n&&n.type==="text"?(n.raw+=`
`+r.raw,n.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):t.push(r);continue}if(e){const o="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(o);break}else throw new Error(o)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let r,n,i,s=e,o,h,d;if(this.tokens.links){const p=Object.keys(this.tokens.links);if(p.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(s))!=null;)p.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(s))!=null;)s=s.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(s))!=null;)s=s.slice(0,o.index)+"++"+s.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(h||(d=""),h=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(p=>(r=p.call({lexer:this},e,t))?(e=e.substring(r.raw.length),t.push(r),!0):!1))){if(r=this.tokenizer.escape(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.tag(e)){e=e.substring(r.raw.length),n=t[t.length-1],n&&r.type==="text"&&n.type==="text"?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(r=this.tokenizer.link(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(r.raw.length),n=t[t.length-1],n&&r.type==="text"&&n.type==="text"?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(r=this.tokenizer.emStrong(e,s,d)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.codespan(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.br(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.del(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.autolink(e)){e=e.substring(r.raw.length),t.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(e))){e=e.substring(r.raw.length),t.push(r);continue}if(i=e,this.options.extensions&&this.options.extensions.startInline){let p=1/0;const g=e.slice(1);let x;this.options.extensions.startInline.forEach(w=>{x=w.call({lexer:this},g),typeof x=="number"&&x>=0&&(p=Math.min(p,x))}),p<1/0&&p>=0&&(i=e.substring(0,p+1))}if(r=this.tokenizer.inlineText(i)){e=e.substring(r.raw.length),r.raw.slice(-1)!=="_"&&(d=r.raw.slice(-1)),h=!0,n=t[t.length-1],n&&n.type==="text"?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(e){const p="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(p);break}else throw new Error(p)}}return t}}class Ht{options;constructor(e){this.options=e||I0}code(e,t,r){const n=(t||"").match(/^\S*/)?.[0];return e=e.replace(/\n$/,"")+`
`,n?'<pre><code class="language-'+Ce(n)+'">'+(r?e:Ce(e,!0))+`</code></pre>
`:"<pre><code>"+(r?e:Ce(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,r){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,r){const n=t?"ol":"ul",i=t&&r!==1?' start="'+r+'"':"";return"<"+n+i+`>
`+e+"</"+n+`>
`}listitem(e,t,r){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const r=t.header?"th":"td";return(t.align?`<${r} align="${t.align}">`:`<${r}>`)+e+`</${r}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,r){const n=gn(e);if(n===null)return r;e=n;let i='<a href="'+e+'"';return t&&(i+=' title="'+t+'"'),i+=">"+r+"</a>",i}image(e,t,r){const n=gn(e);if(n===null)return r;e=n;let i=`<img src="${e}" alt="${r}"`;return t&&(i+=` title="${t}"`),i+=">",i}text(e){return e}}class oa{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,r){return""+r}image(e,t,r){return""+r}br(){return""}}class Qe{options;renderer;textRenderer;constructor(e){this.options=e||I0,this.options.renderer=this.options.renderer||new Ht,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new oa}static parse(e,t){return new Qe(t).parse(e)}static parseInline(e,t){return new Qe(t).parseInline(e)}parse(e,t=!0){let r="";for(let n=0;n<e.length;n++){const i=e[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const s=i,o=this.options.extensions.renderers[s.type].call({parser:this},s);if(o!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(s.type)){r+=o||"";continue}}switch(i.type){case"space":continue;case"hr":{r+=this.renderer.hr();continue}case"heading":{const s=i;r+=this.renderer.heading(this.parseInline(s.tokens),s.depth,Fl(this.parseInline(s.tokens,this.textRenderer)));continue}case"code":{const s=i;r+=this.renderer.code(s.text,s.lang,!!s.escaped);continue}case"table":{const s=i;let o="",h="";for(let p=0;p<s.header.length;p++)h+=this.renderer.tablecell(this.parseInline(s.header[p].tokens),{header:!0,align:s.align[p]});o+=this.renderer.tablerow(h);let d="";for(let p=0;p<s.rows.length;p++){const g=s.rows[p];h="";for(let x=0;x<g.length;x++)h+=this.renderer.tablecell(this.parseInline(g[x].tokens),{header:!1,align:s.align[x]});d+=this.renderer.tablerow(h)}r+=this.renderer.table(o,d);continue}case"blockquote":{const s=i,o=this.parse(s.tokens);r+=this.renderer.blockquote(o);continue}case"list":{const s=i,o=s.ordered,h=s.start,d=s.loose;let p="";for(let g=0;g<s.items.length;g++){const x=s.items[g],w=x.checked,S=x.task;let E="";if(x.task){const z=this.renderer.checkbox(!!w);d?x.tokens.length>0&&x.tokens[0].type==="paragraph"?(x.tokens[0].text=z+" "+x.tokens[0].text,x.tokens[0].tokens&&x.tokens[0].tokens.length>0&&x.tokens[0].tokens[0].type==="text"&&(x.tokens[0].tokens[0].text=z+" "+x.tokens[0].tokens[0].text)):x.tokens.unshift({type:"text",text:z+" "}):E+=z+" "}E+=this.parse(x.tokens,d),p+=this.renderer.listitem(E,S,!!w)}r+=this.renderer.list(p,o,h);continue}case"html":{const s=i;r+=this.renderer.html(s.text,s.block);continue}case"paragraph":{const s=i;r+=this.renderer.paragraph(this.parseInline(s.tokens));continue}case"text":{let s=i,o=s.tokens?this.parseInline(s.tokens):s.text;for(;n+1<e.length&&e[n+1].type==="text";)s=e[++n],o+=`
`+(s.tokens?this.parseInline(s.tokens):s.text);r+=t?this.renderer.paragraph(o):o;continue}default:{const s='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(s),"";throw new Error(s)}}}return r}parseInline(e,t){t=t||this.renderer;let r="";for(let n=0;n<e.length;n++){const i=e[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const s=this.options.extensions.renderers[i.type].call({parser:this},i);if(s!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){r+=s||"";continue}}switch(i.type){case"escape":{const s=i;r+=t.text(s.text);break}case"html":{const s=i;r+=t.html(s.text);break}case"link":{const s=i;r+=t.link(s.href,s.title,this.parseInline(s.tokens,t));break}case"image":{const s=i;r+=t.image(s.href,s.title,s.text);break}case"strong":{const s=i;r+=t.strong(this.parseInline(s.tokens,t));break}case"em":{const s=i;r+=t.em(this.parseInline(s.tokens,t));break}case"codespan":{const s=i;r+=t.codespan(s.text);break}case"br":{r+=t.br();break}case"del":{const s=i;r+=t.del(this.parseInline(s.tokens,t));break}case"text":{const s=i;r+=t.text(s.text);break}default:{const s='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(s),"";throw new Error(s)}}}return r}}class Rt{options;constructor(e){this.options=e||I0}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}class _i{defaults=aa();options=this.setOptions;parse=this.#e(Ke.lex,Qe.parse);parseInline=this.#e(Ke.lexInline,Qe.parseInline);Parser=Qe;Renderer=Ht;TextRenderer=oa;Lexer=Ke;Tokenizer=qt;Hooks=Rt;constructor(...e){this.use(...e)}walkTokens(e,t){let r=[];for(const n of e)switch(r=r.concat(t.call(this,n)),n.type){case"table":{const i=n;for(const s of i.header)r=r.concat(this.walkTokens(s.tokens,t));for(const s of i.rows)for(const o of s)r=r.concat(this.walkTokens(o.tokens,t));break}case"list":{const i=n;r=r.concat(this.walkTokens(i.items,t));break}default:{const i=n;this.defaults.extensions?.childTokens?.[i.type]?this.defaults.extensions.childTokens[i.type].forEach(s=>{const o=i[s].flat(1/0);r=r.concat(this.walkTokens(o,t))}):i.tokens&&(r=r.concat(this.walkTokens(i.tokens,t)))}}return r}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(r=>{const n={...r};if(n.async=this.defaults.async||n.async||!1,r.extensions&&(r.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if("renderer"in i){const s=t.renderers[i.name];s?t.renderers[i.name]=function(...o){let h=i.renderer.apply(this,o);return h===!1&&(h=s.apply(this,o)),h}:t.renderers[i.name]=i.renderer}if("tokenizer"in i){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const s=t[i.level];s?s.unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}"childTokens"in i&&i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),n.extensions=t),r.renderer){const i=this.defaults.renderer||new Ht(this.defaults);for(const s in r.renderer){if(!(s in i))throw new Error(`renderer '${s}' does not exist`);if(s==="options")continue;const o=s,h=r.renderer[o],d=i[o];i[o]=(...p)=>{let g=h.apply(i,p);return g===!1&&(g=d.apply(i,p)),g||""}}n.renderer=i}if(r.tokenizer){const i=this.defaults.tokenizer||new qt(this.defaults);for(const s in r.tokenizer){if(!(s in i))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;const o=s,h=r.tokenizer[o],d=i[o];i[o]=(...p)=>{let g=h.apply(i,p);return g===!1&&(g=d.apply(i,p)),g}}n.tokenizer=i}if(r.hooks){const i=this.defaults.hooks||new Rt;for(const s in r.hooks){if(!(s in i))throw new Error(`hook '${s}' does not exist`);if(s==="options")continue;const o=s,h=r.hooks[o],d=i[o];Rt.passThroughHooks.has(s)?i[o]=p=>{if(this.defaults.async)return Promise.resolve(h.call(i,p)).then(x=>d.call(i,x));const g=h.call(i,p);return d.call(i,g)}:i[o]=(...p)=>{let g=h.apply(i,p);return g===!1&&(g=d.apply(i,p)),g}}n.hooks=i}if(r.walkTokens){const i=this.defaults.walkTokens,s=r.walkTokens;n.walkTokens=function(o){let h=[];return h.push(s.call(this,o)),i&&(h=h.concat(i.call(this,o))),h}}this.defaults={...this.defaults,...n}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return Ke.lex(e,t??this.defaults)}parser(e,t){return Qe.parse(e,t??this.defaults)}#e(e,t){return(r,n)=>{const i={...n},s={...this.defaults,...i};this.defaults.async===!0&&i.async===!1&&(s.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),s.async=!0);const o=this.#t(!!s.silent,!!s.async);if(typeof r>"u"||r===null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof r!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected"));if(s.hooks&&(s.hooks.options=s),s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(r):r).then(h=>e(h,s)).then(h=>s.hooks?s.hooks.processAllTokens(h):h).then(h=>s.walkTokens?Promise.all(this.walkTokens(h,s.walkTokens)).then(()=>h):h).then(h=>t(h,s)).then(h=>s.hooks?s.hooks.postprocess(h):h).catch(o);try{s.hooks&&(r=s.hooks.preprocess(r));let h=e(r,s);s.hooks&&(h=s.hooks.processAllTokens(h)),s.walkTokens&&this.walkTokens(h,s.walkTokens);let d=t(h,s);return s.hooks&&(d=s.hooks.postprocess(d)),d}catch(h){return o(h)}}}#t(e,t){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const n="<p>An error occurred:</p><pre>"+Ce(r.message+"",!0)+"</pre>";return t?Promise.resolve(n):n}if(t)return Promise.reject(r);throw r}}}const R0=new _i;function K(a,e){return R0.parse(a,e)}K.options=K.setOptions=function(a){return R0.setOptions(a),K.defaults=R0.defaults,Ti(K.defaults),K};K.getDefaults=aa;K.defaults=I0;K.use=function(...a){return R0.use(...a),K.defaults=R0.defaults,Ti(K.defaults),K};K.walkTokens=function(a,e){return R0.walkTokens(a,e)};K.parseInline=R0.parseInline;K.Parser=Qe;K.parser=Qe.parse;K.Renderer=Ht;K.TextRenderer=oa;K.Lexer=Ke;K.lexer=Ke.lex;K.Tokenizer=qt;K.Hooks=Rt;K.parse=K;K.options;K.setOptions;K.use;K.walkTokens;K.parseInline;Qe.parse;Ke.lex;function ru(a){if(typeof a=="function"&&(a={highlight:a}),!a||typeof a.highlight!="function")throw new Error("Must provide highlight function");return typeof a.langPrefix!="string"&&(a.langPrefix="language-"),{async:!!a.async,walkTokens(e){if(e.type!=="code")return;const t=au(e);if(a.async)return Promise.resolve(a.highlight(e.text,t)).then(wn(e));const r=a.highlight(e.text,t);wn(e)(r)},renderer:{code(e,t,r){const n=(t||"").match(/\S*/)[0],i=n?` class="${a.langPrefix}${kn(n)}"`:"";return e=e.replace(/\n$/,""),`<pre><code${i}>${r?e:kn(e,!0)}
</code></pre>`}}}}function au(a){return(a.lang||"").match(/\S*/)[0]}function wn(a){return e=>{typeof e=="string"&&e!==a.text&&(a.escaped=!0,a.text=e)}}const qi=/[&<>"']/,nu=new RegExp(qi.source,"g"),Pi=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,iu=new RegExp(Pi.source,"g"),su={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Dn=a=>su[a];function kn(a,e){if(e){if(qi.test(a))return a.replace(nu,Dn)}else if(Pi.test(a))return a.replace(iu,Dn);return a}const lu=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,uu=Object.hasOwnProperty;class Hi{constructor(){this.occurrences,this.reset()}slug(e,t){const r=this;let n=ou(e,t===!0);const i=n;for(;uu.call(r.occurrences,n);)r.occurrences[i]++,n=i+"-"+r.occurrences[i];return r.occurrences[n]=0,n}reset(){this.occurrences=Object.create(null)}}function ou(a,e){return typeof a!="string"?"":(e||(a=a.toLowerCase()),a.replace(lu,"").replace(/ /g,"-"))}let An,Sn=[];function hu({prefix:a=""}={}){return{headerIds:!1,hooks:{preprocess(e){return Sn=[],An=new Hi,e}},renderer:{heading(e,t,r){r=r.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"");const n=`${a}${An.slug(r)}`,i={level:t,text:e,id:n};return Sn.push(i),`<h${t} id="${n}">${e}</h${t}>
`}}}}(function(a){var e=/\\(?:[^a-z()[\]]|[a-z*]+)/i,t={"equation-command":{pattern:e,alias:"regex"}};a.languages.latex={comment:/%.*/,cdata:{pattern:/(\\begin\{((?:lstlisting|verbatim)\*?)\})[\s\S]*?(?=\\end\{\2\})/,lookbehind:!0},equation:[{pattern:/\$\$(?:\\[\s\S]|[^\\$])+\$\$|\$(?:\\[\s\S]|[^\\$])+\$|\\\([\s\S]*?\\\)|\\\[[\s\S]*?\\\]/,inside:t,alias:"string"},{pattern:/(\\begin\{((?:align|eqnarray|equation|gather|math|multline)\*?)\})[\s\S]*?(?=\\end\{\2\})/,lookbehind:!0,inside:t,alias:"string"}],keyword:{pattern:/(\\(?:begin|cite|documentclass|end|label|ref|usepackage)(?:\[[^\]]+\])?\{)[^}]+(?=\})/,lookbehind:!0},url:{pattern:/(\\url\{)[^}]+(?=\})/,lookbehind:!0},headline:{pattern:/(\\(?:chapter|frametitle|paragraph|part|section|subparagraph|subsection|subsubparagraph|subsubsection|subsubsubparagraph)\*?(?:\[[^\]]+\])?\{)[^}]+(?=\})/,lookbehind:!0,alias:"class-name"},function:{pattern:e,alias:"selector"},punctuation:/[[\]{}&]/},a.languages.tex=a.languages.latex,a.languages.context=a.languages.latex})(Prism);(function(a){var e="\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\b",t={pattern:/(^(["']?)\w+\2)[ \t]+\S.*/,lookbehind:!0,alias:"punctuation",inside:null},r={bash:t,environment:{pattern:RegExp("\\$"+e),alias:"constant"},variable:[{pattern:/\$?\(\([\s\S]+?\)\)/,greedy:!0,inside:{variable:[{pattern:/(^\$\(\([\s\S]+)\)\)/,lookbehind:!0},/^\$\(\(/],number:/\b0x[\dA-Fa-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[Ee]-?\d+)?/,operator:/--|\+\+|\*\*=?|<<=?|>>=?|&&|\|\||[=!+\-*/%<>^&|]=?|[?~:]/,punctuation:/\(\(?|\)\)?|,|;/}},{pattern:/\$\((?:\([^)]+\)|[^()])+\)|`[^`]+`/,greedy:!0,inside:{variable:/^\$\(|^`|\)$|`$/}},{pattern:/\$\{[^}]+\}/,greedy:!0,inside:{operator:/:[-=?+]?|[!\/]|##?|%%?|\^\^?|,,?/,punctuation:/[\[\]]/,environment:{pattern:RegExp("(\\{)"+e),lookbehind:!0,alias:"constant"}}},/\$(?:\w+|[#?*!@$])/],entity:/\\(?:[abceEfnrtv\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/};a.languages.bash={shebang:{pattern:/^#!\s*\/.*/,alias:"important"},comment:{pattern:/(^|[^"{\\$])#.*/,lookbehind:!0},"function-name":[{pattern:/(\bfunction\s+)[\w-]+(?=(?:\s*\(?:\s*\))?\s*\{)/,lookbehind:!0,alias:"function"},{pattern:/\b[\w-]+(?=\s*\(\s*\)\s*\{)/,alias:"function"}],"for-or-select":{pattern:/(\b(?:for|select)\s+)\w+(?=\s+in\s)/,alias:"variable",lookbehind:!0},"assign-left":{pattern:/(^|[\s;|&]|[<>]\()\w+(?:\.\w+)*(?=\+?=)/,inside:{environment:{pattern:RegExp("(^|[\\s;|&]|[<>]\\()"+e),lookbehind:!0,alias:"constant"}},alias:"variable",lookbehind:!0},parameter:{pattern:/(^|\s)-{1,2}(?:\w+:[+-]?)?\w+(?:\.\w+)*(?=[=\s]|$)/,alias:"variable",lookbehind:!0},string:[{pattern:/((?:^|[^<])<<-?\s*)(\w+)\s[\s\S]*?(?:\r?\n|\r)\2/,lookbehind:!0,greedy:!0,inside:r},{pattern:/((?:^|[^<])<<-?\s*)(["'])(\w+)\2\s[\s\S]*?(?:\r?\n|\r)\3/,lookbehind:!0,greedy:!0,inside:{bash:t}},{pattern:/(^|[^\\](?:\\\\)*)"(?:\\[\s\S]|\$\([^)]+\)|\$(?!\()|`[^`]+`|[^"\\`$])*"/,lookbehind:!0,greedy:!0,inside:r},{pattern:/(^|[^$\\])'[^']*'/,lookbehind:!0,greedy:!0},{pattern:/\$'(?:[^'\\]|\\[\s\S])*'/,greedy:!0,inside:{entity:r.entity}}],environment:{pattern:RegExp("\\$?"+e),alias:"constant"},variable:r.variable,function:{pattern:/(^|[\s;|&]|[<>]\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cargo|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|java|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|sysctl|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\s;|&])/,lookbehind:!0},keyword:{pattern:/(^|[\s;|&]|[<>]\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\s;|&])/,lookbehind:!0},builtin:{pattern:/(^|[\s;|&]|[<>]\()(?:\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\s;|&])/,lookbehind:!0,alias:"class-name"},boolean:{pattern:/(^|[\s;|&]|[<>]\()(?:false|true)(?=$|[)\s;|&])/,lookbehind:!0},"file-descriptor":{pattern:/\B&\d\b/,alias:"important"},operator:{pattern:/\d?<>|>\||\+=|=[=~]?|!=?|<<[<-]?|[&\d]?>>|\d[<>]&?|[<>][&=]?|&[>&]?|\|[&|]?/,inside:{"file-descriptor":{pattern:/^\d/,alias:"important"}}},punctuation:/\$?\(\(?|\)\)?|\.\.|[{}[\];\\]/,number:{pattern:/(^|\s)(?:[1-9]\d*|0)(?:[.,]\d+)?\b/,lookbehind:!0}},t.inside=a.languages.bash;for(var n=["comment","function-name","for-or-select","assign-left","parameter","string","environment","function","keyword","builtin","boolean","file-descriptor","operator","punctuation","number"],i=r.variable[1].inside,s=0;s<n.length;s++)i[n[s]]=a.languages.bash[n[s]];a.languages.sh=a.languages.bash,a.languages.shell=a.languages.bash})(Prism);const cu='<svg class="md-link-icon" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true" fill="currentColor"><path d="m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z"></path></svg>',mu=`<svg
xmlns="http://www.w3.org/2000/svg"
width="100%"
height="100%"
viewBox="0 0 32 32"
><path
  fill="currentColor"
  d="M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z"
/><path fill="currentColor" d="M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z" /></svg>`,du=`<svg
xmlns="http://www.w3.org/2000/svg"
width="100%"
height="100%"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="3"
stroke-linecap="round"
stroke-linejoin="round"><polyline points="20 6 9 17 4 12" /></svg>`,Fn=`<button title="copy" class="copy_code_button">
<span class="copy-text">${mu}</span>
<span class="check">${du}</span>
</button>`,$i=/[&<>"']/,pu=new RegExp($i.source,"g"),Ui=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,fu=new RegExp(Ui.source,"g"),vu={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},En=a=>vu[a]||"";function Ar(a,e){if(e){if($i.test(a))return a.replace(pu,En)}else if(Ui.test(a))return a.replace(fu,En);return a}const gu={code(a,e,t){const r=(e??"").match(/\S*/)?.[0]??"";return a=a.replace(/\n$/,"")+`
`,r?'<div class="code_wrap">'+Fn+'<pre><code class="language-'+Ar(r)+'">'+(t?a:Ar(a,!0))+`</code></pre></div>
`:'<div class="code_wrap">'+Fn+"<pre><code>"+(t?a:Ar(a,!0))+`</code></pre></div>
`}},bu=new Hi;function yu({header_links:a,line_breaks:e}){const t=new _i;return t.use({gfm:!0,pedantic:!1,breaks:e},ru({highlight:(r,n)=>nr.languages[n]?nr.highlight(r,nr.languages[n],n):r}),{renderer:gu}),a&&(t.use(hu()),t.use({extensions:[{name:"heading",level:"block",renderer(r){const n=r.raw.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,""),i="h"+bu.slug(n),s=r.depth,o=this.parser.parseInline(r.tokens);return`<h${s} id="${i}"><a class="md-header-anchor" href="#${i}">${cu}</a>${o}</h${s}>
`}}]})),t}const{HtmlTag:xu,SvelteComponent:wu,attr:Du,binding_callbacks:ku,detach:ha,element:Au,empty:Su,flush:B0,init:Fu,insert:ca,noop:Tn,safe_not_equal:Eu,set_data:Tu,text:Cu,toggle_class:Ct}=window.__gradio__svelte__internal,{afterUpdate:Mu,createEventDispatcher:_u}=window.__gradio__svelte__internal;function zu(a){let e;return{c(){e=Cu(a[3])},m(t,r){ca(t,e,r)},p(t,r){r&8&&Tu(e,t[3])},d(t){t&&ha(e)}}}function Bu(a){let e,t;return{c(){e=new xu(!1),t=Su(),e.a=t},m(r,n){e.m(a[3],r,n),ca(r,t,n)},p(r,n){n&8&&e.p(r[3])},d(r){r&&(ha(t),e.d())}}}function Ru(a){let e;function t(i,s){return i[1]?Bu:zu}let r=t(a),n=r(a);return{c(){e=Au("span"),n.c(),Du(e,"class","md svelte-8tpqd2"),Ct(e,"chatbot",a[0]),Ct(e,"prose",a[1])},m(i,s){ca(i,e,s),n.m(e,null),a[9](e)},p(i,[s]){r===(r=t(i))&&n?n.p(i,s):(n.d(1),n=r(i),n&&(n.c(),n.m(e,null))),s&1&&Ct(e,"chatbot",i[0]),s&2&&Ct(e,"prose",i[1])},i:Tn,o:Tn,d(i){i&&ha(e),n.d(),a[9](null)}}}function Nu(a,e,t){let{chatbot:r=!0}=e,{message:n}=e,{sanitize_html:i=!0}=e,{latex_delimiters:s=[]}=e,{render_markdown:o=!0}=e,{line_breaks:h=!0}=e,{header_links:d=!1}=e,p,g;const x=yu({header_links:d,line_breaks:h}),w=k=>{try{return!!k&&new URL(k,location.href).origin!==location.origin}catch{return!1}};Ra.addHook("afterSanitizeAttributes",function(k){"target"in k&&w(k.getAttribute("href"))&&(k.setAttribute("target","_blank"),k.setAttribute("rel","noopener noreferrer"))});function S(k){return o&&(k=x.parse(k)),i&&(k=Ra.sanitize(k)),k}async function E(k){s.length>0&&k&&s.some(I=>k.includes(I.left)&&k.includes(I.right))&&wl(p,{delimiters:s,throwOnError:!1})}Mu(()=>E(n));function z(k){ku[k?"unshift":"push"](()=>{p=k,t(2,p)})}return a.$$set=k=>{"chatbot"in k&&t(0,r=k.chatbot),"message"in k&&t(4,n=k.message),"sanitize_html"in k&&t(5,i=k.sanitize_html),"latex_delimiters"in k&&t(6,s=k.latex_delimiters),"render_markdown"in k&&t(1,o=k.render_markdown),"line_breaks"in k&&t(7,h=k.line_breaks),"header_links"in k&&t(8,d=k.header_links)},a.$$.update=()=>{a.$$.dirty&16&&(n&&n.trim()?t(3,g=S(n)):t(3,g=""))},[r,o,p,g,n,i,s,h,d,z]}class Iu extends wu{constructor(e){super(),Fu(this,e,Nu,Ru,Eu,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7,header_links:8})}get chatbot(){return this.$$.ctx[0]}set chatbot(e){this.$$set({chatbot:e}),B0()}get message(){return this.$$.ctx[4]}set message(e){this.$$set({message:e}),B0()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),B0()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),B0()}get render_markdown(){return this.$$.ctx[1]}set render_markdown(e){this.$$set({render_markdown:e}),B0()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),B0()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),B0()}}const qu=Iu;export{qu as M};
//# sourceMappingURL=Example.svelte_svelte_type_style_lang-BBpfzd83.js.map
