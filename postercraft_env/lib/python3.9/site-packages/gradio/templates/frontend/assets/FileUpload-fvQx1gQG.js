import"./Index-WGC0_FkS.js";import{B as he}from"./BlockLabel-CJsotHlk.js";import{E as we}from"./Empty-Vuj7-ssy.js";import{F as le}from"./File-BQ_9P3Ye.js";import{U as ke}from"./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js";import{M as pe}from"./ModifyUpload-DZAlpNPL.js";import{D as ve}from"./DownloadLink-DYBmO3sz.js";const ne=i=>{let e=["B","KB","MB","GB","PB"],l=0;for(;i>1024;)i/=1024,l++;let t=e[l];return i.toFixed(1)+"&nbsp;"+t},{HtmlTag:$e,SvelteComponent:ye,append:w,attr:k,check_outros:me,create_component:ze,destroy_component:qe,detach:M,element:q,ensure_array_like:ie,flush:N,group_outros:ge,init:Fe,insert:P,listen:Z,mount_component:Be,noop:se,outro_and_destroy_block:Ce,run_all:Ee,safe_not_equal:Ae,set_data:x,set_style:oe,space:T,text:H,toggle_class:ae,transition_in:K,transition_out:O,update_keyed_each:De}=window.__gradio__svelte__internal,{createEventDispatcher:Se}=window.__gradio__svelte__internal;function re(i,e,l){const t=i.slice();return t[11]=e[l],t[13]=l,t}function Ue(i){let e=i[2]("file.uploading")+"",l;return{c(){l=H(e)},m(t,n){P(t,l,n)},p(t,n){n&4&&e!==(e=t[2]("file.uploading")+"")&&x(l,e)},i:se,o:se,d(t){t&&M(l)}}}function Me(i){let e,l;return e=new ve({props:{href:i[11].url,download:window.__is_colab__?null:i[11].orig_name,$$slots:{default:[Pe]},$$scope:{ctx:i}}}),{c(){ze(e.$$.fragment)},m(t,n){Be(e,t,n),l=!0},p(t,n){const s={};n&8&&(s.href=t[11].url),n&8&&(s.download=window.__is_colab__?null:t[11].orig_name),n&16392&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){l||(K(e.$$.fragment,t),l=!0)},o(t){O(e.$$.fragment,t),l=!1},d(t){qe(e,t)}}}function Pe(i){let e,l=(i[11].size!=null?ne(i[11].size):"(size unknown)")+"",t;return{c(){e=new $e(!1),t=H(" ⇣"),e.a=t},m(n,s){e.m(l,n,s),P(n,t,s)},p(n,s){s&8&&l!==(l=(n[11].size!=null?ne(n[11].size):"(size unknown)")+"")&&e.p(l)},d(n){n&&(e.d(),M(t))}}}function ue(i){let e,l,t,n;function s(){return i[7](i[13])}function a(...f){return i[8](i[13],...f)}return{c(){e=q("td"),l=q("button"),l.textContent="×",k(l,"class","label-clear-button svelte-18wv37q"),k(l,"aria-label","Remove this file"),k(e,"class","svelte-18wv37q")},m(f,h){P(f,e,h),w(e,l),t||(n=[Z(l,"click",s),Z(l,"keydown",a)],t=!0)},p(f,h){i=f},d(f){f&&M(e),t=!1,Ee(n)}}}function _e(i,e){let l,t,n,s=e[11].filename_stem+"",a,f,h,r=e[11].filename_ext+"",u,o,m,d,c,g,p,A,$,F,B;const I=[Me,Ue],y=[];function L(_,z){return _[11].url?0:1}c=L(e),g=y[c]=I[c](e);let b=e[3].length>1&&ue(e);function X(..._){return e[9](e[13],..._)}return{key:i,first:null,c(){l=q("tr"),t=q("td"),n=q("span"),a=H(s),f=T(),h=q("span"),u=H(r),m=T(),d=q("td"),g.c(),p=T(),b&&b.c(),A=T(),k(n,"class","stem svelte-18wv37q"),k(h,"class","ext svelte-18wv37q"),k(t,"class","filename svelte-18wv37q"),k(t,"aria-label",o=e[11].orig_name),k(d,"class","download svelte-18wv37q"),k(l,"class","file svelte-18wv37q"),ae(l,"selectable",e[0]),this.first=l},m(_,z){P(_,l,z),w(l,t),w(t,n),w(n,a),w(t,f),w(t,h),w(h,u),w(l,m),w(l,d),y[c].m(d,null),w(l,p),b&&b.m(l,null),w(l,A),$=!0,F||(B=Z(l,"click",X),F=!0)},p(_,z){e=_,(!$||z&8)&&s!==(s=e[11].filename_stem+"")&&x(a,s),(!$||z&8)&&r!==(r=e[11].filename_ext+"")&&x(u,r),(!$||z&8&&o!==(o=e[11].orig_name))&&k(t,"aria-label",o);let Y=c;c=L(e),c===Y?y[c].p(e,z):(ge(),O(y[Y],1,1,()=>{y[Y]=null}),me(),g=y[c],g?g.p(e,z):(g=y[c]=I[c](e),g.c()),K(g,1),g.m(d,null)),e[3].length>1?b?b.p(e,z):(b=ue(e),b.c(),b.m(l,A)):b&&(b.d(1),b=null),(!$||z&1)&&ae(l,"selectable",e[0])},i(_){$||(K(g),$=!0)},o(_){O(g),$=!1},d(_){_&&M(l),y[c].d(),b&&b.d(),F=!1,B()}}}function Ie(i){let e,l,t,n=[],s=new Map,a,f=ie(i[3]);const h=r=>r[11];for(let r=0;r<f.length;r+=1){let u=re(i,f,r),o=h(u);s.set(o,n[r]=_e(o,u))}return{c(){e=q("div"),l=q("table"),t=q("tbody");for(let r=0;r<n.length;r+=1)n[r].c();k(t,"class","svelte-18wv37q"),k(l,"class","file-preview svelte-18wv37q"),k(e,"class","file-preview-holder svelte-18wv37q"),oe(e,"max-height",typeof i[1]===void 0?"auto":i[1]+"px")},m(r,u){P(r,e,u),w(e,l),w(l,t);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(t,null);a=!0},p(r,[u]){u&61&&(f=ie(r[3]),ge(),n=De(n,u,h,1,r,f,s,t,Ce,_e,null,re),me()),(!a||u&2)&&oe(e,"max-height",typeof r[1]===void 0?"auto":r[1]+"px")},i(r){if(!a){for(let u=0;u<f.length;u+=1)K(n[u]);a=!0}},o(r){for(let u=0;u<n.length;u+=1)O(n[u]);a=!1},d(r){r&&M(e);for(let u=0;u<n.length;u+=1)n[u].d()}}}function Le(i){const e=i.lastIndexOf(".");return e===-1?[i,""]:[i.slice(0,e),i.slice(e)]}function Ne(i,e,l){let t;const n=Se();let{value:s}=e,{selectable:a=!1}=e,{height:f=void 0}=e,{i18n:h}=e;function r(c,g){const p=c.currentTarget;(c.target===p||p&&p.firstElementChild&&c.composedPath().includes(p.firstElementChild))&&n("select",{value:t[g].orig_name,index:g})}function u(c){const g=t.splice(c,1);l(3,t=[...t]),l(6,s=t),n("delete",g[0]),n("change",t)}const o=c=>{u(c)},m=(c,g)=>{g.key==="Enter"&&u(c)},d=(c,g)=>{r(g,c)};return i.$$set=c=>{"value"in c&&l(6,s=c.value),"selectable"in c&&l(0,a=c.selectable),"height"in c&&l(1,f=c.height),"i18n"in c&&l(2,h=c.i18n)},i.$$.update=()=>{i.$$.dirty&64&&l(3,t=(Array.isArray(s)?s:[s]).map(c=>{const[g,p]=Le(c.orig_name??"");return{...c,filename_stem:g,filename_ext:p}}))},[a,f,h,t,r,u,s,o,m,d]}class Te extends ye{constructor(e){super(),Fe(this,e,Ne,Ie,Ae,{value:6,selectable:0,height:1,i18n:2})}get value(){return this.$$.ctx[6]}set value(e){this.$$set({value:e}),N()}get selectable(){return this.$$.ctx[0]}set selectable(e){this.$$set({selectable:e}),N()}get height(){return this.$$.ctx[1]}set height(e){this.$$set({height:e}),N()}get i18n(){return this.$$.ctx[2]}set i18n(e){this.$$set({i18n:e}),N()}}const de=Te,{SvelteComponent:Ge,bubble:He,check_outros:Ke,create_component:Q,destroy_component:V,detach:fe,empty:Oe,flush:D,group_outros:Re,init:je,insert:ce,mount_component:W,safe_not_equal:Je,space:Qe,transition_in:S,transition_out:U}=window.__gradio__svelte__internal;function Ve(i){let e,l;return e=new we({props:{unpadded_box:!0,size:"large",$$slots:{default:[Xe]},$$scope:{ctx:i}}}),{c(){Q(e.$$.fragment)},m(t,n){W(e,t,n),l=!0},p(t,n){const s={};n&128&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){l||(S(e.$$.fragment,t),l=!0)},o(t){U(e.$$.fragment,t),l=!1},d(t){V(e,t)}}}function We(i){let e,l;return e=new de({props:{i18n:i[5],selectable:i[3],value:i[0],height:i[4]}}),e.$on("select",i[6]),{c(){Q(e.$$.fragment)},m(t,n){W(e,t,n),l=!0},p(t,n){const s={};n&32&&(s.i18n=t[5]),n&8&&(s.selectable=t[3]),n&1&&(s.value=t[0]),n&16&&(s.height=t[4]),e.$set(s)},i(t){l||(S(e.$$.fragment,t),l=!0)},o(t){U(e.$$.fragment,t),l=!1},d(t){V(e,t)}}}function Xe(i){let e,l;return e=new le({}),{c(){Q(e.$$.fragment)},m(t,n){W(e,t,n),l=!0},i(t){l||(S(e.$$.fragment,t),l=!0)},o(t){U(e.$$.fragment,t),l=!1},d(t){V(e,t)}}}function Ye(i){let e,l,t,n,s,a,f;e=new he({props:{show_label:i[2],float:i[0]===null,Icon:le,label:i[1]||"File"}});const h=[We,Ve],r=[];function u(o,m){return m&1&&(t=null),t==null&&(t=!!(o[0]&&(!Array.isArray(o[0])||o[0].length>0))),t?0:1}return n=u(i,-1),s=r[n]=h[n](i),{c(){Q(e.$$.fragment),l=Qe(),s.c(),a=Oe()},m(o,m){W(e,o,m),ce(o,l,m),r[n].m(o,m),ce(o,a,m),f=!0},p(o,[m]){const d={};m&4&&(d.show_label=o[2]),m&1&&(d.float=o[0]===null),m&2&&(d.label=o[1]||"File"),e.$set(d);let c=n;n=u(o,m),n===c?r[n].p(o,m):(Re(),U(r[c],1,1,()=>{r[c]=null}),Ke(),s=r[n],s?s.p(o,m):(s=r[n]=h[n](o),s.c()),S(s,1),s.m(a.parentNode,a))},i(o){f||(S(e.$$.fragment,o),S(s),f=!0)},o(o){U(e.$$.fragment,o),U(s),f=!1},d(o){o&&(fe(l),fe(a)),V(e,o),r[n].d(o)}}}function Ze(i,e,l){let{value:t=null}=e,{label:n}=e,{show_label:s=!0}=e,{selectable:a=!1}=e,{height:f=void 0}=e,{i18n:h}=e;function r(u){He.call(this,i,u)}return i.$$set=u=>{"value"in u&&l(0,t=u.value),"label"in u&&l(1,n=u.label),"show_label"in u&&l(2,s=u.show_label),"selectable"in u&&l(3,a=u.selectable),"height"in u&&l(4,f=u.height),"i18n"in u&&l(5,h=u.i18n)},[t,n,s,a,f,h,r]}class xe extends Ge{constructor(e){super(),je(this,e,Ze,Ye,Je,{value:0,label:1,show_label:2,selectable:3,height:4,i18n:5})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),D()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),D()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),D()}get selectable(){return this.$$.ctx[3]}set selectable(e){this.$$set({selectable:e}),D()}get height(){return this.$$.ctx[4]}set height(e){this.$$set({height:e}),D()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),D()}}const Ct=xe,{SvelteComponent:et,add_flush_callback:tt,bind:lt,binding_callbacks:nt,bubble:G,check_outros:it,create_component:R,create_slot:st,destroy_component:j,detach:ee,empty:ot,flush:v,get_all_dirty_from_scope:at,get_slot_changes:rt,group_outros:ut,init:_t,insert:te,mount_component:J,safe_not_equal:ft,space:be,transition_in:C,transition_out:E,update_slot_base:ct}=window.__gradio__svelte__internal,{createEventDispatcher:ht,tick:mt}=window.__gradio__svelte__internal;function gt(i){let e,l,t;function n(a){i[19](a)}let s={filetype:i[4],file_count:i[3],max_file_size:i[9],root:i[6],stream_handler:i[11],upload:i[10],$$slots:{default:[bt]},$$scope:{ctx:i}};return i[12]!==void 0&&(s.dragging=i[12]),e=new ke({props:s}),nt.push(()=>lt(e,"dragging",n)),e.$on("load",i[13]),e.$on("error",i[20]),{c(){R(e.$$.fragment)},m(a,f){J(e,a,f),t=!0},p(a,f){const h={};f&16&&(h.filetype=a[4]),f&8&&(h.file_count=a[3]),f&512&&(h.max_file_size=a[9]),f&64&&(h.root=a[6]),f&2048&&(h.stream_handler=a[11]),f&1024&&(h.upload=a[10]),f&2097152&&(h.$$scope={dirty:f,ctx:a}),!l&&f&4096&&(l=!0,h.dragging=a[12],tt(()=>l=!1)),e.$set(h)},i(a){t||(C(e.$$.fragment,a),t=!0)},o(a){E(e.$$.fragment,a),t=!1},d(a){j(e,a)}}}function dt(i){let e,l,t,n;return e=new pe({props:{i18n:i[8],absolute:!0}}),e.$on("clear",i[14]),t=new de({props:{i18n:i[8],selectable:i[5],value:i[0],height:i[7]}}),t.$on("select",i[16]),t.$on("change",i[17]),t.$on("delete",i[18]),{c(){R(e.$$.fragment),l=be(),R(t.$$.fragment)},m(s,a){J(e,s,a),te(s,l,a),J(t,s,a),n=!0},p(s,a){const f={};a&256&&(f.i18n=s[8]),e.$set(f);const h={};a&256&&(h.i18n=s[8]),a&32&&(h.selectable=s[5]),a&1&&(h.value=s[0]),a&128&&(h.height=s[7]),t.$set(h)},i(s){n||(C(e.$$.fragment,s),C(t.$$.fragment,s),n=!0)},o(s){E(e.$$.fragment,s),E(t.$$.fragment,s),n=!1},d(s){s&&ee(l),j(e,s),j(t,s)}}}function bt(i){let e;const l=i[15].default,t=st(l,i,i[21],null);return{c(){t&&t.c()},m(n,s){t&&t.m(n,s),e=!0},p(n,s){t&&t.p&&(!e||s&2097152)&&ct(t,l,n,n[21],e?rt(l,n[21],s,null):at(n[21]),null)},i(n){e||(C(t,n),e=!0)},o(n){E(t,n),e=!1},d(n){t&&t.d(n)}}}function wt(i){let e,l,t,n,s,a,f;e=new he({props:{show_label:i[2],Icon:le,float:!i[0],label:i[1]||"File"}});const h=[dt,gt],r=[];function u(o,m){return m&1&&(t=null),t==null&&(t=!!(o[0]&&(!Array.isArray(o[0])||o[0].length>0))),t?0:1}return n=u(i,-1),s=r[n]=h[n](i),{c(){R(e.$$.fragment),l=be(),s.c(),a=ot()},m(o,m){J(e,o,m),te(o,l,m),r[n].m(o,m),te(o,a,m),f=!0},p(o,[m]){const d={};m&4&&(d.show_label=o[2]),m&1&&(d.float=!o[0]),m&2&&(d.label=o[1]||"File"),e.$set(d);let c=n;n=u(o,m),n===c?r[n].p(o,m):(ut(),E(r[c],1,1,()=>{r[c]=null}),it(),s=r[n],s?s.p(o,m):(s=r[n]=h[n](o),s.c()),C(s,1),s.m(a.parentNode,a))},i(o){f||(C(e.$$.fragment,o),C(s),f=!0)},o(o){E(e.$$.fragment,o),E(s),f=!1},d(o){o&&(ee(l),ee(a)),j(e,o),r[n].d(o)}}}function kt(i,e,l){let{$$slots:t={},$$scope:n}=e,{value:s}=e,{label:a}=e,{show_label:f=!0}=e,{file_count:h="single"}=e,{file_types:r=null}=e,{selectable:u=!1}=e,{root:o}=e,{height:m=void 0}=e,{i18n:d}=e,{max_file_size:c=null}=e,{upload:g}=e,{stream_handler:p}=e;async function A({detail:_}){l(0,s=_),await mt(),F("change",s),F("upload",_)}function $(){l(0,s=null),F("change",null),F("clear")}const F=ht();let B=!1;function I(_){G.call(this,i,_)}function y(_){G.call(this,i,_)}function L(_){G.call(this,i,_)}function b(_){B=_,l(12,B)}function X(_){G.call(this,i,_)}return i.$$set=_=>{"value"in _&&l(0,s=_.value),"label"in _&&l(1,a=_.label),"show_label"in _&&l(2,f=_.show_label),"file_count"in _&&l(3,h=_.file_count),"file_types"in _&&l(4,r=_.file_types),"selectable"in _&&l(5,u=_.selectable),"root"in _&&l(6,o=_.root),"height"in _&&l(7,m=_.height),"i18n"in _&&l(8,d=_.i18n),"max_file_size"in _&&l(9,c=_.max_file_size),"upload"in _&&l(10,g=_.upload),"stream_handler"in _&&l(11,p=_.stream_handler),"$$scope"in _&&l(21,n=_.$$scope)},i.$$.update=()=>{i.$$.dirty&4096&&F("drag",B)},[s,a,f,h,r,u,o,m,d,c,g,p,B,A,$,t,I,y,L,b,X,n]}class pt extends et{constructor(e){super(),_t(this,e,kt,wt,ft,{value:0,label:1,show_label:2,file_count:3,file_types:4,selectable:5,root:6,height:7,i18n:8,max_file_size:9,upload:10,stream_handler:11})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),v()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),v()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),v()}get file_count(){return this.$$.ctx[3]}set file_count(e){this.$$set({file_count:e}),v()}get file_types(){return this.$$.ctx[4]}set file_types(e){this.$$set({file_types:e}),v()}get selectable(){return this.$$.ctx[5]}set selectable(e){this.$$set({selectable:e}),v()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),v()}get height(){return this.$$.ctx[7]}set height(e){this.$$set({height:e}),v()}get i18n(){return this.$$.ctx[8]}set i18n(e){this.$$set({i18n:e}),v()}get max_file_size(){return this.$$.ctx[9]}set max_file_size(e){this.$$set({max_file_size:e}),v()}get upload(){return this.$$.ctx[10]}set upload(e){this.$$set({upload:e}),v()}get stream_handler(){return this.$$.ctx[11]}set stream_handler(e){this.$$set({stream_handler:e}),v()}}const Et=pt;export{Et as B,Ct as F,de as a};
//# sourceMappingURL=FileUpload-fvQx1gQG.js.map
