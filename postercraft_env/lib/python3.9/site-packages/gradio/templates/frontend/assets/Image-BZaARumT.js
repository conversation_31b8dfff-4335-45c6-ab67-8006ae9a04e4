import{r as d}from"./file-url-Bf0nK4ai.js";const{SvelteComponent:h,assign:o,compute_rest_props:u,detach:p,element:v,exclude_internal_props:I,flush:j,get_spread_update:q,init:x,insert:y,noop:a,safe_not_equal:b,set_attributes:m,src_url_equal:w,toggle_class:g}=window.__gradio__svelte__internal;function C(c){let e,s,_=[{src:s=c[0]},c[1]],n={};for(let t=0;t<_.length;t+=1)n=o(n,_[t]);return{c(){e=v("img"),m(e,n),g(e,"svelte-1pijsyv",!0)},m(t,r){y(t,e,r)},p(t,[r]){m(e,n=q(_,[r&1&&!w(e.src,s=t[0])&&{src:s},r&2&&t[1]])),g(e,"svelte-1pijsyv",!0)},i:a,o:a,d(t){t&&p(e)}}}function P(c,e,s){const _=["src"];let n=u(e,_),{src:t=void 0}=e,r,i;return c.$$set=l=>{e=o(o({},e),I(l)),s(1,n=u(e,_)),"src"in l&&s(2,t=l.src)},c.$$.update=()=>{if(c.$$.dirty&12){s(0,r=t),s(3,i=t);const l=t;d(l).then(f=>{i===l&&s(0,r=f)})}},[r,n,t,i]}class S extends h{constructor(e){super(),x(this,e,P,C,b,{src:2})}get src(){return this.$$.ctx[2]}set src(e){this.$$set({src:e}),j()}}const z=S;export{z as I};
//# sourceMappingURL=Image-BZaARumT.js.map
