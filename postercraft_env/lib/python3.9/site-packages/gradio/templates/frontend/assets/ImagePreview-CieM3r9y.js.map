{"version": 3, "file": "ImagePreview-CieM3r9y.js", "sources": ["../../../../js/image/shared/ImagePreview.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport { BlockLabel, Empty, IconButton, ShareButton } from \"@gradio/atoms\";\n\timport { Download } from \"@gradio/icons\";\n\timport { get_coordinates_of_clicked_image } from \"./utils\";\n\timport Image from \"./Image.svelte\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport { Maximize, Minimize } from \"@gradio/icons\";\n\n\timport { Image as ImageIcon } from \"@gradio/icons\";\n\timport { type FileData } from \"@gradio/client\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\texport let value: null | FileData;\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\texport let show_download_button = true;\n\texport let selectable = false;\n\texport let show_share_button = false;\n\texport let i18n: I18nFormatter;\n\texport let show_fullscreen_button = true;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string;\n\t\tselect: SelectData;\n\t}>();\n\n\tconst handle_click = (evt: MouseEvent): void => {\n\t\tlet coordinates = get_coordinates_of_clicked_image(evt);\n\t\tif (coordinates) {\n\t\t\tdispatch(\"select\", { index: coordinates, value: null });\n\t\t}\n\t};\n\n\tlet is_full_screen = false;\n\tlet image_container: HTMLElement;\n\n\tonMount(() => {\n\t\tdocument.addEventListener(\"fullscreenchange\", () => {\n\t\t\tis_full_screen = !!document.fullscreenElement;\n\t\t});\n\t});\n\n\tconst toggle_full_screen = async (): Promise<void> => {\n\t\tif (!is_full_screen) {\n\t\t\tawait image_container.requestFullscreen();\n\t\t} else {\n\t\t\tawait document.exitFullscreen();\n\t\t\tis_full_screen = !is_full_screen;\n\t\t}\n\t};\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={ImageIcon}\n\tlabel={!show_label ? \"\" : label || i18n(\"image.image\")}\n/>\n{#if value === null || !value.url}\n\t<Empty unpadded_box={true} size=\"large\"><ImageIcon /></Empty>\n{:else}\n\t<div class=\"image-container\" bind:this={image_container}>\n\t\t<div class=\"icon-buttons\">\n\t\t\t{#if !is_full_screen && show_fullscreen_button}\n\t\t\t\t<IconButton\n\t\t\t\t\tIcon={Maximize}\n\t\t\t\t\tlabel={is_full_screen ? \"Exit full screen\" : \"View in full screen\"}\n\t\t\t\t\ton:click={toggle_full_screen}\n\t\t\t\t/>\n\t\t\t{/if}\n\n\t\t\t{#if is_full_screen && show_fullscreen_button}\n\t\t\t\t<IconButton\n\t\t\t\t\tIcon={Minimize}\n\t\t\t\t\tlabel={is_full_screen ? \"Exit full screen\" : \"View in full screen\"}\n\t\t\t\t\ton:click={toggle_full_screen}\n\t\t\t\t/>\n\t\t\t{/if}\n\n\t\t\t{#if show_download_button}\n\t\t\t\t<DownloadLink href={value.url} download={value.orig_name || \"image\"}>\n\t\t\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t\t\t</DownloadLink>\n\t\t\t{/if}\n\t\t\t{#if show_share_button}\n\t\t\t\t<ShareButton\n\t\t\t\t\t{i18n}\n\t\t\t\t\ton:share\n\t\t\t\t\ton:error\n\t\t\t\t\tformatter={async (value) => {\n\t\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\t\tlet url = await uploadToHuggingFace(value, \"url\");\n\t\t\t\t\t\treturn `<img src=\"${url}\" />`;\n\t\t\t\t\t}}\n\t\t\t\t\t{value}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</div>\n\t\t<button on:click={handle_click}>\n\t\t\t<div class:selectable class=\"image-frame\">\n\t\t\t\t<Image src={value.url} alt=\"\" loading=\"lazy\" on:load />\n\t\t\t</div>\n\t\t</button>\n\t</div>\n{/if}\n\n<style>\n\t.image-container {\n\t\theight: 100%;\n\t\tposition: relative;\n\t}\n\n\t.image-container button {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tborder-radius: var(--radius-lg);\n\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.image-frame {\n\t\twidth: auto;\n\t\theight: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t.image-frame :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: scale-down;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t\tgap: var(--size-1);\n\t\tz-index: 1;\n\t}\n\n\t:global(.fullscreen-controls svg) {\n\t\tposition: relative;\n\t\ttop: 0px;\n\t}\n\n\t:global(.image-container:fullscreen) {\n\t\tbackground-color: black;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t:global(.image-container:fullscreen img) {\n\t\tmax-width: 90vw;\n\t\tmax-height: 90vh;\n\t\tobject-fit: scale-down;\n\t}\n</style>\n"], "names": ["onMount", "if_block0", "ctx", "create_if_block_4", "if_block1", "create_if_block_3", "create_if_block_2", "create_if_block_1", "insert", "target", "div2", "anchor", "append", "div0", "button", "div1", "dirty", "image_changes", "Maximize", "Minimize", "downloadlink_changes", "Download", "iconbutton_changes", "ImageIcon", "value", "$$props", "label", "show_label", "show_download_button", "selectable", "show_share_button", "i18n", "show_fullscreen_button", "dispatch", "createEventDispatcher", "handle_click", "evt", "coordinates", "get_coordinates_of_clicked_image", "is_full_screen", "image_container", "toggle_full_screen", "$$invalidate", "uploadToHuggingFace", "$$value"], "mappings": "i/BAC+B,QAAAA,IAAS,OAAgB,sEAgE/CC,EAAA,CAAAC,MAAkBA,EAAsB,CAAA,GAAAC,EAAAD,CAAA,EAQzCE,EAAAF,MAAkBA,EAAsB,CAAA,GAAAG,EAAAH,CAAA,IAQxCA,EAAoB,CAAA,GAAAI,EAAAJ,CAAA,IAKpBA,EAAiB,CAAA,GAAAK,EAAAL,CAAA,yBAgBT,IAAAA,KAAM,kXAvCrBM,EA0CKC,EAAAC,EAAAC,CAAA,EAzCJC,EAmCKF,EAAAG,CAAA,0FACLD,EAIQF,EAAAI,CAAA,EAHPF,EAEKE,EAAAC,CAAA,+CAHYb,EAAY,EAAA,CAAA,gBAnCvB,CAAAA,MAAkBA,EAAsB,CAAA,+FAQzCA,MAAkBA,EAAsB,CAAA,+FAQxCA,EAAoB,CAAA,6FAKpBA,EAAiB,CAAA,4GAgBTc,EAAA,IAAAC,EAAA,IAAAf,KAAM,oSAzCA,2SAMXgB,QACChB,EAAc,CAAA,EAAG,mBAAqB,uCACnCA,EAAkB,EAAA,CAAA,iFADrBA,EAAc,CAAA,EAAG,mBAAqB,mKAOvCiB,QACCjB,EAAc,CAAA,EAAG,mBAAqB,uCACnCA,EAAkB,EAAA,CAAA,iFADrBA,EAAc,CAAA,EAAG,mBAAqB,+JAM1B,KAAAA,KAAM,aAAeA,EAAK,CAAA,EAAC,WAAa,iHAAxCc,EAAA,IAAAI,EAAA,KAAAlB,KAAM,sBAAeA,EAAK,CAAA,EAAC,WAAa,4LACzCmB,EAAiB,MAAAnB,KAAK,iBAAiB,oEAAtBc,EAAA,KAAAM,EAAA,MAAApB,KAAK,iBAAiB,uoBA1BvDqB,QACErB,EAAU,CAAA,EAAQA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,EAAhC,2CAEjB,OAAAA,EAAU,CAAA,IAAA,MAAS,CAAAA,KAAM,IAAG,2LAFxBA,EAAU,CAAA,EAAQA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,EAAhC,mSA3CV,MAAAsB,CAAsB,EAAAC,EACtB,CAAA,MAAAC,EAA4B,MAAS,EAAAD,GACrC,WAAAE,CAAmB,EAAAF,EACnB,CAAA,qBAAAG,EAAuB,EAAI,EAAAH,EAC3B,CAAA,WAAAI,EAAa,EAAK,EAAAJ,EAClB,CAAA,kBAAAK,EAAoB,EAAK,EAAAL,GACzB,KAAAM,CAAmB,EAAAN,EACnB,CAAA,uBAAAO,EAAyB,EAAI,EAAAP,EAElC,MAAAQ,EAAWC,KAKXC,EAAgBC,GAAe,KAChCC,EAAcC,EAAiCF,CAAG,EAClDC,GACHJ,EAAS,SAAY,CAAA,MAAOI,EAAa,MAAO,IAAI,CAAA,GAIlD,IAAAE,EAAiB,GACjBC,EAEJxC,GAAO,IAAA,CACN,SAAS,iBAAiB,mBAAkB,IAAA,KAC3CuC,EAAc,CAAA,CAAK,SAAS,iBAAiB,YAIzCE,EAAkB,SAAA,CAClBF,GAGE,MAAA,SAAS,iBACfG,EAAA,EAAAH,GAAkBA,CAAc,GAH1B,MAAAC,EAAgB,6BA4CFhB,GACZA,eACW,MAAAmB,EAAoBnB,CAAY,CACzB,OAFJ,0IA7BgBgB,EAAeI"}