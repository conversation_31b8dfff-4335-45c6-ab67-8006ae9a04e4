import{I as dt,C as $t}from"./Index-WGC0_FkS.js";import{u as kt}from"./Blocks-aR9ucLZz.js";import{B as mt}from"./BlockLabel-CJsotHlk.js";import{E as It}from"./Empty-Vuj7-ssy.js";import{S as St}from"./ShareButton-Ds9bG3Tz.js";import{D as yt,a as qt}from"./DownloadLink-DYBmO3sz.js";import{I as Xe}from"./Image-Bsh8Umrh.js";import{I as gt}from"./Image-BZaARumT.js";import{p as Ct}from"./index-COY1HN2y.js";import{W as Dt,S as Et}from"./SelectSource-ghC4bkgc.js";import{D as ht}from"./DropdownArrow-AhwBZaFV.js";import{f as Mt}from"./Button-8nmImwVJ.js";import{U as Wt}from"./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js";const{SvelteComponent:zt,append:Je,attr:R,detach:Bt,init:Nt,insert:Tt,noop:Ee,safe_not_equal:Rt,svg_element:Me}=window.__gradio__svelte__internal;function Ht(i){let e,n,t;return{c(){e=Me("svg"),n=Me("path"),t=Me("circle"),R(n,"d","M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"),R(t,"cx","12"),R(t,"cy","13"),R(t,"r","4"),R(e,"xmlns","http://www.w3.org/2000/svg"),R(e,"width","100%"),R(e,"height","100%"),R(e,"viewBox","0 0 24 24"),R(e,"fill","none"),R(e,"stroke","currentColor"),R(e,"stroke-width","1.5"),R(e,"stroke-linecap","round"),R(e,"stroke-linejoin","round"),R(e,"class","feather feather-camera")},m(l,r){Tt(l,e,r),Je(e,n),Je(e,t)},p:Ee,i:Ee,o:Ee,d(l){l&&Bt(e)}}}class Ut extends zt{constructor(e){super(),Nt(this,e,null,Ht,Rt,{})}}const{SvelteComponent:jt,append:Lt,attr:O,detach:Pt,init:At,insert:Ft,noop:We,safe_not_equal:Ot,svg_element:Ke}=window.__gradio__svelte__internal;function Xt(i){let e,n;return{c(){e=Ke("svg"),n=Ke("circle"),O(n,"cx","12"),O(n,"cy","12"),O(n,"r","10"),O(e,"xmlns","http://www.w3.org/2000/svg"),O(e,"width","100%"),O(e,"height","100%"),O(e,"viewBox","0 0 24 24"),O(e,"stroke-width","1.5"),O(e,"stroke-linecap","round"),O(e,"stroke-linejoin","round"),O(e,"class","feather feather-circle")},m(t,l){Ft(t,e,l),Lt(e,n)},p:We,i:We,o:We,d(t){t&&Pt(e)}}}class Yt extends jt{constructor(e){super(),At(this,e,null,Xt,Ot,{})}}const{SvelteComponent:Vt,append:Gt,attr:H,detach:Jt,init:Kt,insert:Qt,noop:ze,safe_not_equal:Zt,svg_element:Qe}=window.__gradio__svelte__internal;function xt(i){let e,n;return{c(){e=Qe("svg"),n=Qe("rect"),H(n,"x","3"),H(n,"y","3"),H(n,"width","18"),H(n,"height","18"),H(n,"rx","2"),H(n,"ry","2"),H(e,"xmlns","http://www.w3.org/2000/svg"),H(e,"width","100%"),H(e,"height","100%"),H(e,"viewBox","0 0 24 24"),H(e,"stroke-width","1.5"),H(e,"stroke-linecap","round"),H(e,"stroke-linejoin","round"),H(e,"class","feather feather-square")},m(t,l){Qt(t,e,l),Gt(e,n)},p:ze,i:ze,o:ze,d(t){t&&Jt(e)}}}class en extends Vt{constructor(e){super(),Kt(this,e,null,xt,Zt,{})}}const bt=i=>{let e;if(i.currentTarget instanceof Element)e=i.currentTarget.querySelector("img");else return[NaN,NaN];const n=e.getBoundingClientRect(),t=e.naturalWidth/n.width,l=e.naturalHeight/n.height;if(t>l){const u=e.naturalHeight/t,c=(n.height-u)/2;var r=Math.round((i.clientX-n.left)*t),o=Math.round((i.clientY-n.top-c)*t)}else{const u=e.naturalWidth/l,c=(n.width-u)/2;var r=Math.round((i.clientX-n.left-c)*l),o=Math.round((i.clientY-n.top)*l)}return r<0||r>=e.naturalWidth||o<0||o>=e.naturalHeight?null:[r,o]},{SvelteComponent:tn,append:Ze,attr:Be,bubble:xe,check_outros:Ue,create_component:J,destroy_component:K,detach:oe,element:Ne,empty:nn,flush:G,group_outros:je,init:ln,insert:ae,listen:rn,mount_component:Q,safe_not_equal:on,space:Le,toggle_class:et,transition_in:T,transition_out:P}=window.__gradio__svelte__internal,{createEventDispatcher:an}=window.__gradio__svelte__internal;function sn(i){let e,n,t,l,r,o,u,c,h,a=i[3]&&tt(i),s=i[5]&&nt(i);return o=new gt({props:{src:i[0].url,alt:"",loading:"lazy"}}),{c(){e=Ne("div"),a&&a.c(),n=Le(),s&&s.c(),t=Le(),l=Ne("button"),r=Ne("div"),J(o.$$.fragment),Be(e,"class","icon-buttons svelte-1l6wqyv"),Be(r,"class","image-container svelte-1l6wqyv"),et(r,"selectable",i[4]),Be(l,"class","svelte-1l6wqyv")},m(m,b){ae(m,e,b),a&&a.m(e,null),Ze(e,n),s&&s.m(e,null),ae(m,t,b),ae(m,l,b),Ze(l,r),Q(o,r,null),u=!0,c||(h=rn(l,"click",i[7]),c=!0)},p(m,b){m[3]?a?(a.p(m,b),b&8&&T(a,1)):(a=tt(m),a.c(),T(a,1),a.m(e,n)):a&&(je(),P(a,1,1,()=>{a=null}),Ue()),m[5]?s?(s.p(m,b),b&32&&T(s,1)):(s=nt(m),s.c(),T(s,1),s.m(e,null)):s&&(je(),P(s,1,1,()=>{s=null}),Ue());const y={};b&1&&(y.src=m[0].url),o.$set(y),(!u||b&16)&&et(r,"selectable",m[4])},i(m){u||(T(a),T(s),T(o.$$.fragment,m),u=!0)},o(m){P(a),P(s),P(o.$$.fragment,m),u=!1},d(m){m&&(oe(e),oe(t),oe(l)),a&&a.d(),s&&s.d(),K(o),c=!1,h()}}}function cn(i){let e,n;return e=new It({props:{unpadded_box:!0,size:"large",$$slots:{default:[_n]},$$scope:{ctx:i}}}),{c(){J(e.$$.fragment)},m(t,l){Q(e,t,l),n=!0},p(t,l){const r={};l&4096&&(r.$$scope={dirty:l,ctx:t}),e.$set(r)},i(t){n||(T(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function tt(i){let e,n;return e=new yt({props:{href:i[0].url,download:i[0].orig_name||"image",$$slots:{default:[un]},$$scope:{ctx:i}}}),{c(){J(e.$$.fragment)},m(t,l){Q(e,t,l),n=!0},p(t,l){const r={};l&1&&(r.href=t[0].url),l&1&&(r.download=t[0].orig_name||"image"),l&4160&&(r.$$scope={dirty:l,ctx:t}),e.$set(r)},i(t){n||(T(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function un(i){let e,n;return e=new dt({props:{Icon:qt,label:i[6]("common.download")}}),{c(){J(e.$$.fragment)},m(t,l){Q(e,t,l),n=!0},p(t,l){const r={};l&64&&(r.label=t[6]("common.download")),e.$set(r)},i(t){n||(T(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function nt(i){let e,n;return e=new St({props:{i18n:i[6],formatter:i[8],value:i[0]}}),e.$on("share",i[9]),e.$on("error",i[10]),{c(){J(e.$$.fragment)},m(t,l){Q(e,t,l),n=!0},p(t,l){const r={};l&64&&(r.i18n=t[6]),l&1&&(r.value=t[0]),e.$set(r)},i(t){n||(T(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function _n(i){let e,n;return e=new Xe({}),{c(){J(e.$$.fragment)},m(t,l){Q(e,t,l),n=!0},i(t){n||(T(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function fn(i){let e,n,t,l,r,o;e=new mt({props:{show_label:i[2],Icon:Xe,label:i[1]||i[6]("image.image")}});const u=[cn,sn],c=[];function h(a,s){return a[0]===null||!a[0].url?0:1}return t=h(i),l=c[t]=u[t](i),{c(){J(e.$$.fragment),n=Le(),l.c(),r=nn()},m(a,s){Q(e,a,s),ae(a,n,s),c[t].m(a,s),ae(a,r,s),o=!0},p(a,[s]){const m={};s&4&&(m.show_label=a[2]),s&66&&(m.label=a[1]||a[6]("image.image")),e.$set(m);let b=t;t=h(a),t===b?c[t].p(a,s):(je(),P(c[b],1,1,()=>{c[b]=null}),Ue(),l=c[t],l?l.p(a,s):(l=c[t]=u[t](a),l.c()),T(l,1),l.m(r.parentNode,r))},i(a){o||(T(e.$$.fragment,a),T(l),o=!0)},o(a){P(e.$$.fragment,a),P(l),o=!1},d(a){a&&(oe(n),oe(r)),K(e,a),c[t].d(a)}}}function dn(i,e,n){let{value:t}=e,{label:l=void 0}=e,{show_label:r}=e,{show_download_button:o=!0}=e,{selectable:u=!1}=e,{show_share_button:c=!1}=e,{i18n:h}=e;const a=an(),s=_=>{let v=bt(_);v&&a("select",{index:v,value:null})},m=async _=>_?`<img src="${await kt(_,"base64")}" />`:"";function b(_){xe.call(this,i,_)}function y(_){xe.call(this,i,_)}return i.$$set=_=>{"value"in _&&n(0,t=_.value),"label"in _&&n(1,l=_.label),"show_label"in _&&n(2,r=_.show_label),"show_download_button"in _&&n(3,o=_.show_download_button),"selectable"in _&&n(4,u=_.selectable),"show_share_button"in _&&n(5,c=_.show_share_button),"i18n"in _&&n(6,h=_.i18n)},[t,l,r,o,u,c,h,s,m,b,y]}class mn extends tn{constructor(e){super(),ln(this,e,dn,fn,on,{value:0,label:1,show_label:2,show_download_button:3,selectable:4,show_share_button:5,i18n:6})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),G()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),G()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),G()}get show_download_button(){return this.$$.ctx[3]}set show_download_button(e){this.$$set({show_download_button:e}),G()}get selectable(){return this.$$.ctx[4]}set selectable(e){this.$$set({selectable:e}),G()}get show_share_button(){return this.$$.ctx[5]}set show_share_button(e){this.$$set({show_share_button:e}),G()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),G()}}const ti=mn,{SvelteComponent:gn,append:fe,attr:Te,create_component:hn,destroy_component:bn,detach:pn,element:Re,init:wn,insert:vn,listen:$n,mount_component:kn,noop:In,safe_not_equal:Sn,set_style:yn,space:qn,text:Cn,transition_in:Dn,transition_out:En}=window.__gradio__svelte__internal,{createEventDispatcher:Mn}=window.__gradio__svelte__internal;function Wn(i){let e,n,t,l,r,o="Click to Access Webcam",u,c,h,a;return l=new Dt({}),{c(){e=Re("button"),n=Re("div"),t=Re("span"),hn(l.$$.fragment),r=qn(),u=Cn(o),Te(t,"class","icon-wrap svelte-qbrfs"),Te(n,"class","wrap svelte-qbrfs"),Te(e,"class","svelte-qbrfs"),yn(e,"height","100%")},m(s,m){vn(s,e,m),fe(e,n),fe(n,t),kn(l,t,null),fe(n,r),fe(n,u),c=!0,h||(a=$n(e,"click",i[1]),h=!0)},p:In,i(s){c||(Dn(l.$$.fragment,s),c=!0)},o(s){En(l.$$.fragment,s),c=!1},d(s){s&&pn(e),bn(l),h=!1,a()}}}function zn(i){const e=Mn();return[e,()=>e("click")]}class Bn extends gn{constructor(e){super(),wn(this,e,zn,Wn,Sn,{})}}function Nn(){return navigator.mediaDevices.enumerateDevices()}function Tn(i,e){e.srcObject=i,e.muted=!0,e.play()}async function lt(i,e,n){const t={width:{ideal:1920},height:{ideal:1440}},l={video:n?{deviceId:{exact:n},...t}:t,audio:i};return navigator.mediaDevices.getUserMedia(l).then(r=>(Tn(r,e),r))}function Rn(i){return i.filter(n=>n.kind==="videoinput")}const{SvelteComponent:Hn,action_destroyer:Un,add_render_callback:jn,append:Y,attr:S,binding_callbacks:Ln,check_outros:se,create_component:te,create_in_transition:Pn,destroy_component:ne,destroy_each:An,detach:j,element:A,empty:Ye,ensure_array_like:it,flush:V,group_outros:ce,init:Fn,insert:L,listen:we,mount_component:le,noop:Ve,run_all:On,safe_not_equal:Xn,set_data:pt,set_input_value:Pe,space:ue,stop_propagation:Yn,text:wt,toggle_class:de,transition_in:D,transition_out:z}=window.__gradio__svelte__internal,{createEventDispatcher:Vn,onMount:Gn}=window.__gradio__svelte__internal;function rt(i,e,n){const t=i.slice();return t[31]=e[n],t}function Jn(i){let e,n,t,l,r,o,u,c,h,a,s;const m=[Zn,Qn],b=[];function y(p,k){return p[1]==="video"||p[0]?0:1}t=y(i),l=b[t]=m[t](i);let _=!i[8]&&ot(i),v=i[10]&&i[7]&&at(i);return{c(){e=A("div"),n=A("button"),l.c(),o=ue(),_&&_.c(),u=ue(),v&&v.c(),c=Ye(),S(n,"aria-label",r=i[1]==="image"?"capture photo":"start recording"),S(n,"class","svelte-1aa1mud"),S(e,"class","button-wrap svelte-1aa1mud")},m(p,k){L(p,e,k),Y(e,n),b[t].m(n,null),Y(e,o),_&&_.m(e,null),L(p,u,k),v&&v.m(p,k),L(p,c,k),h=!0,a||(s=we(n,"click",i[13]),a=!0)},p(p,k){let I=t;t=y(p),t===I?b[t].p(p,k):(ce(),z(b[I],1,1,()=>{b[I]=null}),se(),l=b[t],l?l.p(p,k):(l=b[t]=m[t](p),l.c()),D(l,1),l.m(n,null)),(!h||k[0]&2&&r!==(r=p[1]==="image"?"capture photo":"start recording"))&&S(n,"aria-label",r),p[8]?_&&(ce(),z(_,1,1,()=>{_=null}),se()):_?(_.p(p,k),k[0]&256&&D(_,1)):(_=ot(p),_.c(),D(_,1),_.m(e,null)),p[10]&&p[7]?v?(v.p(p,k),k[0]&1152&&D(v,1)):(v=at(p),v.c(),D(v,1),v.m(c.parentNode,c)):v&&(ce(),z(v,1,1,()=>{v=null}),se())},i(p){h||(D(l),D(_),D(v),h=!0)},o(p){z(l),z(_),z(v),h=!1},d(p){p&&(j(e),j(u),j(c)),b[t].d(),_&&_.d(),v&&v.d(p),a=!1,s()}}}function Kn(i){let e,n,t,l;return n=new Bn({}),n.$on("click",i[20]),{c(){e=A("div"),te(n.$$.fragment),S(e,"title","grant webcam access")},m(r,o){L(r,e,o),le(n,e,null),l=!0},p:Ve,i(r){l||(D(n.$$.fragment,r),r&&(t||jn(()=>{t=Pn(e,Mt,{delay:100,duration:200}),t.start()})),l=!0)},o(r){z(n.$$.fragment,r),l=!1},d(r){r&&j(e),ne(n)}}}function Qn(i){let e,n,t;return n=new Ut({}),{c(){e=A("div"),te(n.$$.fragment),S(e,"class","icon svelte-1aa1mud"),S(e,"title","capture photo")},m(l,r){L(l,e,r),le(n,e,null),t=!0},p:Ve,i(l){t||(D(n.$$.fragment,l),t=!0)},o(l){z(n.$$.fragment,l),t=!1},d(l){l&&j(e),ne(n)}}}function Zn(i){let e,n,t,l;const r=[el,xn],o=[];function u(c,h){return c[8]?0:1}return e=u(i),n=o[e]=r[e](i),{c(){n.c(),t=Ye()},m(c,h){o[e].m(c,h),L(c,t,h),l=!0},p(c,h){let a=e;e=u(c),e!==a&&(ce(),z(o[a],1,1,()=>{o[a]=null}),se(),n=o[e],n||(n=o[e]=r[e](c),n.c()),D(n,1),n.m(t.parentNode,t))},i(c){l||(D(n),l=!0)},o(c){z(n),l=!1},d(c){c&&j(t),o[e].d(c)}}}function xn(i){let e,n,t;return n=new Yt({}),{c(){e=A("div"),te(n.$$.fragment),S(e,"class","icon red svelte-1aa1mud"),S(e,"title","start recording")},m(l,r){L(l,e,r),le(n,e,null),t=!0},i(l){t||(D(n.$$.fragment,l),t=!0)},o(l){z(n.$$.fragment,l),t=!1},d(l){l&&j(e),ne(n)}}}function el(i){let e,n,t;return n=new en({}),{c(){e=A("div"),te(n.$$.fragment),S(e,"class","icon red svelte-1aa1mud"),S(e,"title","stop recording")},m(l,r){L(l,e,r),le(n,e,null),t=!0},i(l){t||(D(n.$$.fragment,l),t=!0)},o(l){z(n.$$.fragment,l),t=!1},d(l){l&&j(e),ne(n)}}}function ot(i){let e,n,t,l,r;return n=new ht({}),{c(){e=A("button"),te(n.$$.fragment),S(e,"class","icon svelte-1aa1mud"),S(e,"aria-label","select input source")},m(o,u){L(o,e,u),le(n,e,null),t=!0,l||(r=we(e,"click",i[21]),l=!0)},p:Ve,i(o){t||(D(n.$$.fragment,o),t=!0)},o(o){z(n.$$.fragment,o),t=!1},d(o){o&&j(e),ne(n),l=!1,r()}}}function at(i){let e,n,t,l,r,o,u;t=new ht({});function c(s,m){return s[6].length===0?nl:tl}let h=c(i),a=h(i);return{c(){e=A("select"),n=A("button"),te(t.$$.fragment),l=ue(),a.c(),S(n,"class","inset-icon svelte-1aa1mud"),S(e,"class","select-wrap svelte-1aa1mud"),S(e,"aria-label","select source")},m(s,m){L(s,e,m),Y(e,n),le(t,n,null),Y(n,l),a.m(e,null),r=!0,o||(u=[we(n,"click",Yn(i[22])),Un(Ge.call(null,e,i[14])),we(e,"change",i[11])],o=!0)},p(s,m){h===(h=c(s))&&a?a.p(s,m):(a.d(1),a=h(s),a&&(a.c(),a.m(e,null)))},i(s){r||(D(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&j(e),ne(t),a.d(),o=!1,On(u)}}}function tl(i){let e,n=it(i[6]),t=[];for(let l=0;l<n.length;l+=1)t[l]=st(rt(i,n,l));return{c(){for(let l=0;l<t.length;l+=1)t[l].c();e=Ye()},m(l,r){for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(l,r);L(l,e,r)},p(l,r){if(r[0]&192){n=it(l[6]);let o;for(o=0;o<n.length;o+=1){const u=rt(l,n,o);t[o]?t[o].p(u,r):(t[o]=st(u),t[o].c(),t[o].m(e.parentNode,e))}for(;o<t.length;o+=1)t[o].d(1);t.length=n.length}},d(l){l&&j(e),An(t,l)}}}function nl(i){let e,n=i[3]("common.no_devices")+"",t;return{c(){e=A("option"),t=wt(n),e.__value="",Pe(e,e.__value),S(e,"class","svelte-1aa1mud")},m(l,r){L(l,e,r),Y(e,t)},p(l,r){r[0]&8&&n!==(n=l[3]("common.no_devices")+"")&&pt(t,n)},d(l){l&&j(e)}}}function st(i){let e,n=i[31].label+"",t,l,r,o;return{c(){e=A("option"),t=wt(n),l=ue(),e.__value=r=i[31].deviceId,Pe(e,e.__value),e.selected=o=i[7].deviceId===i[31].deviceId,S(e,"class","svelte-1aa1mud")},m(u,c){L(u,e,c),Y(e,t),Y(e,l)},p(u,c){c[0]&64&&n!==(n=u[31].label+"")&&pt(t,n),c[0]&64&&r!==(r=u[31].deviceId)&&(e.__value=r,Pe(e,e.__value)),c[0]&192&&o!==(o=u[7].deviceId===u[31].deviceId)&&(e.selected=o)},d(u){u&&j(e)}}}function ll(i){let e,n,t,l,r,o;const u=[Kn,Jn],c=[];function h(a,s){return a[9]?1:0}return l=h(i),r=c[l]=u[l](i),{c(){e=A("div"),n=A("video"),t=ue(),r.c(),S(n,"class","svelte-1aa1mud"),de(n,"flip",i[2]),de(n,"hide",!i[9]),S(e,"class","wrap svelte-1aa1mud")},m(a,s){L(a,e,s),Y(e,n),i[19](n),Y(e,t),c[l].m(e,null),o=!0},p(a,s){(!o||s[0]&4)&&de(n,"flip",a[2]),(!o||s[0]&512)&&de(n,"hide",!a[9]);let m=l;l=h(a),l===m?c[l].p(a,s):(ce(),z(c[m],1,1,()=>{c[m]=null}),se(),r=c[l],r?r.p(a,s):(r=c[l]=u[l](a),r.c()),D(r,1),r.m(e,null))},i(a){o||(D(r),o=!0)},o(a){z(r),o=!1},d(a){a&&j(e),i[19](null),c[l].d()}}}function Ge(i,e){const n=t=>{i&&!i.contains(t.target)&&!t.defaultPrevented&&e(t)};return document.addEventListener("click",n,!0),{destroy(){document.removeEventListener("click",n,!0)}}}function il(i,e,n){let t,l=[],r=null,o,{streaming:u=!1}=e,{pending:c=!1}=e,{root:h=""}=e,{mode:a="image"}=e,{mirror_webcam:s}=e,{include_audio:m}=e,{i18n:b}=e,{upload:y}=e;const _=Vn();Gn(()=>o=document.createElement("canvas"));const v=async g=>{const X=g.target.value;await lt(m,t,X).then(async ie=>{M=ie,n(7,r=l.find(re=>re.deviceId===X)||null),n(10,F=!1)})};async function p(){try{lt(m,t).then(async g=>{n(9,W=!0),n(6,l=await Nn()),M=g}).then(()=>Rn(l)).then(g=>{n(6,l=g);const B=M.getTracks().map(X=>X.getSettings()?.deviceId)[0];n(7,r=B&&g.find(X=>X.deviceId===B)||l[0])}),(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)&&_("error",b("image.no_webcam_support"))}catch(g){if(g instanceof DOMException&&g.name=="NotAllowedError")_("error",b("image.allow_webcam_access"));else throw g}}function k(){var g=o.getContext("2d");(!u||u&&I)&&t.videoWidth&&t.videoHeight&&(o.width=t.videoWidth,o.height=t.videoHeight,g.drawImage(t,0,0,t.videoWidth,t.videoHeight),s&&(g.scale(-1,1),g.drawImage(t,-t.videoWidth,0)),o.toBlob(B=>{_(u?"stream":"capture",B)},"image/png",.8))}let I=!1,E=[],M,$,d;function w(){if(I){d.stop();let g=new Blob(E,{type:$}),B=new FileReader;B.onload=async function(X){if(X.target){let ie=new File([g],"sample."+$.substring(6));const re=await Ct([ie]);let De=(await y(re,h))?.filter(Boolean)[0];_("capture",De),_("stop_recording")}},B.readAsDataURL(g)}else{_("start_recording"),E=[];let g=["video/webm","video/mp4"];for(let B of g)if(MediaRecorder.isTypeSupported(B)){$=B;break}if($===null){console.error("No supported MediaRecorder mimeType");return}d=new MediaRecorder(M,{mimeType:$}),d.addEventListener("dataavailable",function(B){E.push(B.data)}),d.start(200)}n(8,I=!I)}let W=!1;function q(){a==="image"&&u&&n(8,I=!I),a==="image"?k():w(),!I&&M&&(M.getTracks().forEach(g=>g.stop()),n(5,t.srcObject=null,t),n(9,W=!1))}u&&a==="image"&&window.setInterval(()=>{t&&!c&&k()},500);let F=!1;function Ie(g){g.preventDefault(),g.stopPropagation(),n(10,F=!1)}function Se(g){Ln[g?"unshift":"push"](()=>{t=g,n(5,t)})}const ye=async()=>p(),qe=()=>n(10,F=!0),Ce=()=>n(10,F=!1);return i.$$set=g=>{"streaming"in g&&n(0,u=g.streaming),"pending"in g&&n(15,c=g.pending),"root"in g&&n(16,h=g.root),"mode"in g&&n(1,a=g.mode),"mirror_webcam"in g&&n(2,s=g.mirror_webcam),"include_audio"in g&&n(17,m=g.include_audio),"i18n"in g&&n(3,b=g.i18n),"upload"in g&&n(18,y=g.upload)},[u,a,s,b,Ge,t,l,r,I,W,F,v,p,q,Ie,c,h,m,y,Se,ye,qe,Ce]}class rl extends Hn{constructor(e){super(),Fn(this,e,il,ll,Xn,{streaming:0,pending:15,root:16,mode:1,mirror_webcam:2,include_audio:17,i18n:3,upload:18,click_outside:4},null,[-1,-1])}get streaming(){return this.$$.ctx[0]}set streaming(e){this.$$set({streaming:e}),V()}get pending(){return this.$$.ctx[15]}set pending(e){this.$$set({pending:e}),V()}get root(){return this.$$.ctx[16]}set root(e){this.$$set({root:e}),V()}get mode(){return this.$$.ctx[1]}set mode(e){this.$$set({mode:e}),V()}get mirror_webcam(){return this.$$.ctx[2]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),V()}get include_audio(){return this.$$.ctx[17]}set include_audio(e){this.$$set({include_audio:e}),V()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),V()}get upload(){return this.$$.ctx[18]}set upload(e){this.$$set({upload:e}),V()}get click_outside(){return Ge}}const ol=rl,{SvelteComponent:al,attr:sl,create_component:cl,destroy_component:ul,detach:_l,element:fl,init:dl,insert:ml,mount_component:gl,noop:hl,safe_not_equal:bl,transition_in:pl,transition_out:wl}=window.__gradio__svelte__internal,{createEventDispatcher:vl}=window.__gradio__svelte__internal;function $l(i){let e,n,t;return n=new dt({props:{Icon:$t,label:"Remove Image"}}),n.$on("click",i[1]),{c(){e=fl("div"),cl(n.$$.fragment),sl(e,"class","svelte-s6ybro")},m(l,r){ml(l,e,r),gl(n,e,null),t=!0},p:hl,i(l){t||(pl(n.$$.fragment,l),t=!0)},o(l){wl(n.$$.fragment,l),t=!1},d(l){l&&_l(e),ul(n)}}}function kl(i){const e=vl();return[e,t=>{e("remove_image"),t.stopPropagation()}]}class Il extends al{constructor(e){super(),dl(this,e,kl,$l,bl,{})}}const{SvelteComponent:Sl,add_flush_callback:Ae,append:me,attr:he,bind:Fe,binding_callbacks:ve,bubble:He,check_outros:be,create_component:Z,create_slot:yl,destroy_component:x,detach:$e,element:Oe,empty:ql,flush:U,get_all_dirty_from_scope:Cl,get_slot_changes:Dl,group_outros:pe,init:El,insert:ke,listen:Ml,mount_component:ee,noop:Wl,safe_not_equal:zl,space:ge,toggle_class:ct,transition_in:C,transition_out:N,update_slot_base:Bl}=window.__gradio__svelte__internal,{createEventDispatcher:Nl,tick:Tl}=window.__gradio__svelte__internal;function ut(i){let e,n;return e=new Il({}),e.$on("remove_image",i[25]),{c(){Z(e.$$.fragment)},m(t,l){ee(e,t,l),n=!0},p:Wl,i(t){n||(C(e.$$.fragment,t),n=!0)},o(t){N(e.$$.fragment,t),n=!1},d(t){x(e,t)}}}function _t(i){let e;const n=i[24].default,t=yl(n,i,i[36],null);return{c(){t&&t.c()},m(l,r){t&&t.m(l,r),e=!0},p(l,r){t&&t.p&&(!e||r[1]&32)&&Bl(t,n,l,l[36],e?Dl(n,l[36],r,null):Cl(l[36]),null)},i(l){e||(C(t,l),e=!0)},o(l){N(t,l),e=!1},d(l){t&&t.d(l)}}}function Rl(i){let e,n,t=i[1]===null&&_t(i);return{c(){t&&t.c(),e=ql()},m(l,r){t&&t.m(l,r),ke(l,e,r),n=!0},p(l,r){l[1]===null?t?(t.p(l,r),r[0]&2&&C(t,1)):(t=_t(l),t.c(),C(t,1),t.m(e.parentNode,e)):t&&(pe(),N(t,1,1,()=>{t=null}),be())},i(l){n||(C(t),n=!0)},o(l){N(t),n=!1},d(l){l&&$e(e),t&&t.d(l)}}}function Hl(i){let e,n,t,l,r;return n=new gt({props:{src:i[1].url,alt:i[1].alt_text}}),{c(){e=Oe("div"),Z(n.$$.fragment),he(e,"class","image-frame svelte-rrgd5g"),ct(e,"selectable",i[7])},m(o,u){ke(o,e,u),ee(n,e,null),t=!0,l||(r=Ml(e,"click",i[21]),l=!0)},p(o,u){const c={};u[0]&2&&(c.src=o[1].url),u[0]&2&&(c.alt=o[1].alt_text),n.$set(c),(!t||u[0]&128)&&ct(e,"selectable",o[7])},i(o){t||(C(n.$$.fragment,o),t=!0)},o(o){N(n.$$.fragment,o),t=!1},d(o){o&&$e(e),x(n),l=!1,r()}}}function Ul(i){let e,n;return e=new ol({props:{root:i[8],mirror_webcam:i[6],streaming:i[5],mode:"image",include_audio:!1,i18n:i[9],upload:i[11]}}),e.$on("capture",i[30]),e.$on("stream",i[31]),e.$on("error",i[32]),e.$on("drag",i[33]),e.$on("upload",i[34]),{c(){Z(e.$$.fragment)},m(t,l){ee(e,t,l),n=!0},p(t,l){const r={};l[0]&256&&(r.root=t[8]),l[0]&64&&(r.mirror_webcam=t[6]),l[0]&32&&(r.streaming=t[5]),l[0]&512&&(r.i18n=t[9]),l[0]&2048&&(r.upload=t[11]),e.$set(r)},i(t){n||(C(e.$$.fragment,t),n=!0)},o(t){N(e.$$.fragment,t),n=!1},d(t){x(e,t)}}}function ft(i){let e,n,t;function l(o){i[35](o)}let r={sources:i[4],handle_clear:i[18],handle_select:i[22]};return i[0]!==void 0&&(r.active_source=i[0]),e=new Et({props:r}),ve.push(()=>Fe(e,"active_source",l)),{c(){Z(e.$$.fragment)},m(o,u){ee(e,o,u),t=!0},p(o,u){const c={};u[0]&16&&(c.sources=o[4]),!n&&u[0]&1&&(n=!0,c.active_source=o[0],Ae(()=>n=!1)),e.$set(c)},i(o){t||(C(e.$$.fragment,o),t=!0)},o(o){N(e.$$.fragment,o),t=!1},d(o){x(e,o)}}}function jl(i){let e,n,t,l,r,o,u,c,h,a,s,m,b=i[4].length>1||i[4].includes("clipboard"),y;e=new mt({props:{show_label:i[3],Icon:Xe,label:i[2]||"Image"}});let _=i[1]?.url&&!i[15]&&ut(i);function v(d){i[27](d)}function p(d){i[28](d)}let k={hidden:i[1]!==null||i[0]==="webcam",filetype:i[0]==="clipboard"?"clipboard":"image/*",root:i[8],max_file_size:i[10],disable_click:!i[4].includes("upload"),upload:i[11],stream_handler:i[12],$$slots:{default:[Rl]},$$scope:{ctx:i}};i[13]!==void 0&&(k.uploading=i[13]),i[14]!==void 0&&(k.dragging=i[14]),o=new Wt({props:k}),i[26](o),ve.push(()=>Fe(o,"uploading",v)),ve.push(()=>Fe(o,"dragging",p)),o.$on("load",i[17]),o.$on("error",i[29]);const I=[Ul,Hl],E=[];function M(d,w){return d[0]==="webcam"&&(d[5]||!d[5]&&!d[1])?0:d[1]!==null&&!d[5]?1:-1}~(a=M(i))&&(s=E[a]=I[a](i));let $=b&&ft(i);return{c(){Z(e.$$.fragment),n=ge(),t=Oe("div"),_&&_.c(),l=ge(),r=Oe("div"),Z(o.$$.fragment),h=ge(),s&&s.c(),m=ge(),$&&$.c(),he(r,"class","upload-container svelte-rrgd5g"),he(t,"data-testid","image"),he(t,"class","image-container svelte-rrgd5g")},m(d,w){ee(e,d,w),ke(d,n,w),ke(d,t,w),_&&_.m(t,null),me(t,l),me(t,r),ee(o,r,null),me(r,h),~a&&E[a].m(r,null),me(t,m),$&&$.m(t,null),y=!0},p(d,w){const W={};w[0]&8&&(W.show_label=d[3]),w[0]&4&&(W.label=d[2]||"Image"),e.$set(W),d[1]?.url&&!d[15]?_?(_.p(d,w),w[0]&32770&&C(_,1)):(_=ut(d),_.c(),C(_,1),_.m(t,l)):_&&(pe(),N(_,1,1,()=>{_=null}),be());const q={};w[0]&3&&(q.hidden=d[1]!==null||d[0]==="webcam"),w[0]&1&&(q.filetype=d[0]==="clipboard"?"clipboard":"image/*"),w[0]&256&&(q.root=d[8]),w[0]&1024&&(q.max_file_size=d[10]),w[0]&16&&(q.disable_click=!d[4].includes("upload")),w[0]&2048&&(q.upload=d[11]),w[0]&4096&&(q.stream_handler=d[12]),w[0]&2|w[1]&32&&(q.$$scope={dirty:w,ctx:d}),!u&&w[0]&8192&&(u=!0,q.uploading=d[13],Ae(()=>u=!1)),!c&&w[0]&16384&&(c=!0,q.dragging=d[14],Ae(()=>c=!1)),o.$set(q);let F=a;a=M(d),a===F?~a&&E[a].p(d,w):(s&&(pe(),N(E[F],1,1,()=>{E[F]=null}),be()),~a?(s=E[a],s?s.p(d,w):(s=E[a]=I[a](d),s.c()),C(s,1),s.m(r,null)):s=null),w[0]&16&&(b=d[4].length>1||d[4].includes("clipboard")),b?$?($.p(d,w),w[0]&16&&C($,1)):($=ft(d),$.c(),C($,1),$.m(t,null)):$&&(pe(),N($,1,1,()=>{$=null}),be())},i(d){y||(C(e.$$.fragment,d),C(_),C(o.$$.fragment,d),C(s),C($),y=!0)},o(d){N(e.$$.fragment,d),N(_),N(o.$$.fragment,d),N(s),N($),y=!1},d(d){d&&($e(n),$e(t)),x(e,d),_&&_.d(),i[26](null),x(o),~a&&E[a].d(),$&&$.d()}}}function Ll(i,e,n){let t,{$$slots:l={},$$scope:r}=e,{value:o}=e,{label:u=void 0}=e,{show_label:c}=e,{sources:h=["upload","clipboard","webcam"]}=e,{streaming:a=!1}=e,{pending:s=!1}=e,{mirror_webcam:m}=e,{selectable:b=!1}=e,{root:y}=e,{i18n:_}=e,{max_file_size:v=null}=e,{upload:p}=e,{stream_handler:k}=e,I,E=!1,{active_source:M=null}=e;function $({detail:f}){n(1,o=f),W("upload")}function d(){n(1,o=null),W("clear"),W("change",null)}async function w(f){n(23,s=!0);const _e=await I.load_files([new File([f],"webcam.png")]);n(1,o=_e?.[0]||null),await Tl(),W(a?"stream":"change"),n(23,s=!1)}const W=Nl();let q=!1;function F(f){let _e=bt(f);_e&&W("select",{index:_e,value:null})}async function Ie(f){switch(f){case"clipboard":I.paste_clipboard();break}}const Se=()=>{n(1,o=null),W("clear")};function ye(f){ve[f?"unshift":"push"](()=>{I=f,n(16,I)})}function qe(f){E=f,n(13,E)}function Ce(f){q=f,n(14,q)}function g(f){He.call(this,i,f)}const B=f=>w(f.detail),X=f=>w(f.detail);function ie(f){He.call(this,i,f)}function re(f){He.call(this,i,f)}const De=f=>w(f.detail);function vt(f){M=f,n(0,M),n(4,h)}return i.$$set=f=>{"value"in f&&n(1,o=f.value),"label"in f&&n(2,u=f.label),"show_label"in f&&n(3,c=f.show_label),"sources"in f&&n(4,h=f.sources),"streaming"in f&&n(5,a=f.streaming),"pending"in f&&n(23,s=f.pending),"mirror_webcam"in f&&n(6,m=f.mirror_webcam),"selectable"in f&&n(7,b=f.selectable),"root"in f&&n(8,y=f.root),"i18n"in f&&n(9,_=f.i18n),"max_file_size"in f&&n(10,v=f.max_file_size),"upload"in f&&n(11,p=f.upload),"stream_handler"in f&&n(12,k=f.stream_handler),"active_source"in f&&n(0,M=f.active_source),"$$scope"in f&&n(36,r=f.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&17&&!M&&h&&n(0,M=h[0]),i.$$.dirty[0]&33&&n(15,t=a&&M==="webcam"),i.$$.dirty[0]&40960&&E&&!t&&n(1,o=null),i.$$.dirty[0]&16384&&W("drag",q)},[M,o,u,c,h,a,m,b,y,_,v,p,k,E,q,t,I,$,d,w,W,F,Ie,s,l,Se,ye,qe,Ce,g,B,X,ie,re,De,vt,r]}class Pl extends Sl{constructor(e){super(),El(this,e,Ll,jl,zl,{value:1,label:2,show_label:3,sources:4,streaming:5,pending:23,mirror_webcam:6,selectable:7,root:8,i18n:9,max_file_size:10,upload:11,stream_handler:12,active_source:0},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),U()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),U()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),U()}get sources(){return this.$$.ctx[4]}set sources(e){this.$$set({sources:e}),U()}get streaming(){return this.$$.ctx[5]}set streaming(e){this.$$set({streaming:e}),U()}get pending(){return this.$$.ctx[23]}set pending(e){this.$$set({pending:e}),U()}get mirror_webcam(){return this.$$.ctx[6]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),U()}get selectable(){return this.$$.ctx[7]}set selectable(e){this.$$set({selectable:e}),U()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),U()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),U()}get max_file_size(){return this.$$.ctx[10]}set max_file_size(e){this.$$set({max_file_size:e}),U()}get upload(){return this.$$.ctx[11]}set upload(e){this.$$set({upload:e}),U()}get stream_handler(){return this.$$.ctx[12]}set stream_handler(e){this.$$set({stream_handler:e}),U()}get active_source(){return this.$$.ctx[0]}set active_source(e){this.$$set({active_source:e}),U()}}const ni=Pl;export{ni as I,ti as S,ol as W};
//# sourceMappingURL=ImageUploader-sLREcIL3.js.map
