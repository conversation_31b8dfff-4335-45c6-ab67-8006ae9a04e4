{"version": 3, "file": "ImageUploader-sLREcIL3.js", "sources": ["../../../../js/icons/src/Camera.svelte", "../../../../js/icons/src/Circle.svelte", "../../../../js/icons/src/Square.svelte", "../../../../js/image/shared/utils.ts", "../../../../js/image/shared/ImagePreview.svelte", "../../../../js/image/shared/WebcamPermissions.svelte", "../../../../js/image/shared/stream_utils.ts", "../../../../js/image/shared/Webcam.svelte", "../../../../js/image/shared/ClearImage.svelte", "../../../../js/image/shared/ImageUploader.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-camera\"\n>\n\t<path\n\t\td=\"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z\"\n\t/>\n\t<circle cx=\"12\" cy=\"13\" r=\"4\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-circle\"\n>\n\t<circle cx=\"12\" cy=\"12\" r=\"10\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-square\"\n>\n\t<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n</svg>\n", "export const get_coordinates_of_clicked_image = (\n\tevt: MouseEvent\n): [number, number] | null => {\n\tlet image;\n\tif (evt.currentTarget instanceof Element) {\n\t\timage = evt.currentTarget.querySelector(\"img\") as HTMLImageElement;\n\t} else {\n\t\treturn [NaN, NaN];\n\t}\n\n\tconst imageRect = image.getBoundingClientRect();\n\tconst xScale = image.naturalWidth / imageRect.width;\n\tconst yScale = image.naturalHeight / imageRect.height;\n\tif (xScale > yScale) {\n\t\tconst displayed_height = image.naturalHeight / xScale;\n\t\tconst y_offset = (imageRect.height - displayed_height) / 2;\n\t\tvar x = Math.round((evt.clientX - imageRect.left) * xScale);\n\t\tvar y = Math.round((evt.clientY - imageRect.top - y_offset) * xScale);\n\t} else {\n\t\tconst displayed_width = image.naturalWidth / yScale;\n\t\tconst x_offset = (imageRect.width - displayed_width) / 2;\n\t\tvar x = Math.round((evt.clientX - imageRect.left - x_offset) * yScale);\n\t\tvar y = Math.round((evt.clientY - imageRect.top) * yScale);\n\t}\n\tif (x < 0 || x >= image.naturalWidth || y < 0 || y >= image.naturalHeight) {\n\t\treturn null;\n\t}\n\treturn [x, y];\n};\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport { BlockLabel, Empty, IconButton, ShareButton } from \"@gradio/atoms\";\n\timport { Download } from \"@gradio/icons\";\n\timport { get_coordinates_of_clicked_image } from \"./utils\";\n\timport Image from \"./Image.svelte\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\n\timport { Image as ImageIcon } from \"@gradio/icons\";\n\timport { type FileData } from \"@gradio/client\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\texport let value: null | FileData;\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\texport let show_download_button = true;\n\texport let selectable = false;\n\texport let show_share_button = false;\n\texport let i18n: I18nFormatter;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string;\n\t\tselect: SelectData;\n\t}>();\n\n\tconst handle_click = (evt: MouseEvent): void => {\n\t\tlet coordinates = get_coordinates_of_clicked_image(evt);\n\t\tif (coordinates) {\n\t\t\tdispatch(\"select\", { index: coordinates, value: null });\n\t\t}\n\t};\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={ImageIcon}\n\tlabel={label || i18n(\"image.image\")}\n/>\n{#if value === null || !value.url}\n\t<Empty unpadded_box={true} size=\"large\"><ImageIcon /></Empty>\n{:else}\n\t<div class=\"icon-buttons\">\n\t\t{#if show_download_button}\n\t\t\t<DownloadLink href={value.url} download={value.orig_name || \"image\"}>\n\t\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t\t</DownloadLink>\n\t\t{/if}\n\t\t{#if show_share_button}\n\t\t\t<ShareButton\n\t\t\t\t{i18n}\n\t\t\t\ton:share\n\t\t\t\ton:error\n\t\t\t\tformatter={async (value) => {\n\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\tlet url = await uploadToHuggingFace(value, \"base64\");\n\t\t\t\t\treturn `<img src=\"${url}\" />`;\n\t\t\t\t}}\n\t\t\t\t{value}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\t<button on:click={handle_click}>\n\t\t<div class:selectable class=\"image-container\">\n\t\t\t<Image src={value.url} alt=\"\" loading=\"lazy\" />\n\t\t</div>\n\t</button>\n{/if}\n\n<style>\n\t.image-container :global(img),\n\tbutton {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\tdisplay: block;\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t\tgap: var(--size-1);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Webcam } from \"@gradio/icons\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tclick: undefined;\n\t}>();\n</script>\n\n<button style:height=\"100%\" on:click={() => dispatch(\"click\")}>\n\t<div class=\"wrap\">\n\t\t<span class=\"icon-wrap\">\n\t\t\t<Webcam />\n\t\t</span>\n\t\t{\"Click to Access Webcam\"}\n\t</div>\n</button>\n\n<style>\n\tbutton {\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t}\n\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmin-height: var(--size-60);\n\t\tcolor: var(--block-label-text-color);\n\t\theight: 100%;\n\t\tpadding-top: var(--size-3);\n\t}\n\n\t.icon-wrap {\n\t\twidth: 30px;\n\t\tmargin-bottom: var(--spacing-lg);\n\t}\n\n\t@media (--screen-md) {\n\t\t.wrap {\n\t\t\tfont-size: var(--text-lg);\n\t\t}\n\t}\n</style>\n", "export function get_devices(): Promise<MediaDeviceInfo[]> {\n\treturn navigator.mediaDevices.enumerateDevices();\n}\n\nexport function handle_error(error: string): void {\n\tthrow new Error(error);\n}\n\nexport function set_local_stream(\n\tlocal_stream: MediaStream | null,\n\tvideo_source: HTMLVideoElement\n): void {\n\tvideo_source.srcObject = local_stream;\n\tvideo_source.muted = true;\n\tvideo_source.play();\n}\n\nexport async function get_video_stream(\n\tinclude_audio: boolean,\n\tvideo_source: HTMLVideoElement,\n\tdevice_id?: string\n): Promise<MediaStream> {\n\tconst size = {\n\t\twidth: { ideal: 1920 },\n\t\theight: { ideal: 1440 }\n\t};\n\n\tconst constraints = {\n\t\tvideo: device_id ? { deviceId: { exact: device_id }, ...size } : size,\n\t\taudio: include_audio\n\t};\n\n\treturn navigator.mediaDevices\n\t\t.getUserMedia(constraints)\n\t\t.then((local_stream: MediaStream) => {\n\t\t\tset_local_stream(local_stream, video_source);\n\t\t\treturn local_stream;\n\t\t});\n}\n\nexport function set_available_devices(\n\tdevices: MediaDeviceInfo[]\n): MediaDeviceInfo[] {\n\tconst cameras = devices.filter(\n\t\t(device: MediaDeviceInfo) => device.kind === \"videoinput\"\n\t);\n\n\treturn cameras;\n}\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { Camera, Circle, Square, DropdownArrow } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { type FileData, type Client, prepare_files } from \"@gradio/client\";\n\timport WebcamPermissions from \"./WebcamPermissions.svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport {\n\t\tget_devices,\n\t\tget_video_stream,\n\t\tset_available_devices\n\t} from \"./stream_utils\";\n\n\tlet video_source: HTMLVideoElement;\n\tlet available_video_devices: MediaDeviceInfo[] = [];\n\tlet selected_device: MediaDeviceInfo | null = null;\n\n\tlet canvas: HTMLCanvasElement;\n\texport let streaming = false;\n\texport let pending = false;\n\texport let root = \"\";\n\n\texport let mode: \"image\" | \"video\" = \"image\";\n\texport let mirror_webcam: boolean;\n\texport let include_audio: boolean;\n\texport let i18n: I18nFormatter;\n\texport let upload: Client[\"upload\"];\n\n\tconst dispatch = createEventDispatcher<{\n\t\tstream: undefined;\n\t\tcapture: FileData | Blob | null;\n\t\terror: string;\n\t\tstart_recording: undefined;\n\t\tstop_recording: undefined;\n\t}>();\n\n\tonMount(() => (canvas = document.createElement(\"canvas\")));\n\n\tconst handle_device_change = async (event: InputEvent): Promise<void> => {\n\t\tconst target = event.target as HTMLInputElement;\n\t\tconst device_id = target.value;\n\n\t\tawait get_video_stream(include_audio, video_source, device_id).then(\n\t\t\tasync (local_stream) => {\n\t\t\t\tstream = local_stream;\n\t\t\t\tselected_device =\n\t\t\t\t\tavailable_video_devices.find(\n\t\t\t\t\t\t(device) => device.deviceId === device_id\n\t\t\t\t\t) || null;\n\t\t\t\toptions_open = false;\n\t\t\t}\n\t\t);\n\t};\n\n\tasync function access_webcam(): Promise<void> {\n\t\ttry {\n\t\t\tget_video_stream(include_audio, video_source)\n\t\t\t\t.then(async (local_stream) => {\n\t\t\t\t\twebcam_accessed = true;\n\t\t\t\t\tavailable_video_devices = await get_devices();\n\t\t\t\t\tstream = local_stream;\n\t\t\t\t})\n\t\t\t\t.then(() => set_available_devices(available_video_devices))\n\t\t\t\t.then((devices) => {\n\t\t\t\t\tavailable_video_devices = devices;\n\n\t\t\t\t\tconst used_devices = stream\n\t\t\t\t\t\t.getTracks()\n\t\t\t\t\t\t.map((track) => track.getSettings()?.deviceId)[0];\n\n\t\t\t\t\tselected_device = used_devices\n\t\t\t\t\t\t? devices.find((device) => device.deviceId === used_devices) ||\n\t\t\t\t\t\t\tavailable_video_devices[0]\n\t\t\t\t\t\t: available_video_devices[0];\n\t\t\t\t});\n\n\t\t\tif (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n\t\t\t\tdispatch(\"error\", i18n(\"image.no_webcam_support\"));\n\t\t\t}\n\t\t} catch (err) {\n\t\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\t\tdispatch(\"error\", i18n(\"image.allow_webcam_access\"));\n\t\t\t} else {\n\t\t\t\tthrow err;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction take_picture(): void {\n\t\tvar context = canvas.getContext(\"2d\")!;\n\t\tif (\n\t\t\t(!streaming || (streaming && recording)) &&\n\t\t\tvideo_source.videoWidth &&\n\t\t\tvideo_source.videoHeight\n\t\t) {\n\t\t\tcanvas.width = video_source.videoWidth;\n\t\t\tcanvas.height = video_source.videoHeight;\n\t\t\tcontext.drawImage(\n\t\t\t\tvideo_source,\n\t\t\t\t0,\n\t\t\t\t0,\n\t\t\t\tvideo_source.videoWidth,\n\t\t\t\tvideo_source.videoHeight\n\t\t\t);\n\n\t\t\tif (mirror_webcam) {\n\t\t\t\tcontext.scale(-1, 1);\n\t\t\t\tcontext.drawImage(video_source, -video_source.videoWidth, 0);\n\t\t\t}\n\n\t\t\tcanvas.toBlob(\n\t\t\t\t(blob) => {\n\t\t\t\t\tdispatch(streaming ? \"stream\" : \"capture\", blob);\n\t\t\t\t},\n\t\t\t\t\"image/png\",\n\t\t\t\t0.8\n\t\t\t);\n\t\t}\n\t}\n\n\tlet recording = false;\n\tlet recorded_blobs: BlobPart[] = [];\n\tlet stream: MediaStream;\n\tlet mimeType: string;\n\tlet media_recorder: MediaRecorder;\n\n\tfunction take_recording(): void {\n\t\tif (recording) {\n\t\t\tmedia_recorder.stop();\n\t\t\tlet video_blob = new Blob(recorded_blobs, { type: mimeType });\n\t\t\tlet ReaderObj = new FileReader();\n\t\t\tReaderObj.onload = async function (e): Promise<void> {\n\t\t\t\tif (e.target) {\n\t\t\t\t\tlet _video_blob = new File(\n\t\t\t\t\t\t[video_blob],\n\t\t\t\t\t\t\"sample.\" + mimeType.substring(6)\n\t\t\t\t\t);\n\t\t\t\t\tconst val = await prepare_files([_video_blob]);\n\t\t\t\t\tlet value = (\n\t\t\t\t\t\t(await upload(val, root))?.filter(Boolean) as FileData[]\n\t\t\t\t\t)[0];\n\t\t\t\t\tdispatch(\"capture\", value);\n\t\t\t\t\tdispatch(\"stop_recording\");\n\t\t\t\t}\n\t\t\t};\n\t\t\tReaderObj.readAsDataURL(video_blob);\n\t\t} else {\n\t\t\tdispatch(\"start_recording\");\n\t\t\trecorded_blobs = [];\n\t\t\tlet validMimeTypes = [\"video/webm\", \"video/mp4\"];\n\t\t\tfor (let validMimeType of validMimeTypes) {\n\t\t\t\tif (MediaRecorder.isTypeSupported(validMimeType)) {\n\t\t\t\t\tmimeType = validMimeType;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (mimeType === null) {\n\t\t\t\tconsole.error(\"No supported MediaRecorder mimeType\");\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tmedia_recorder = new MediaRecorder(stream, {\n\t\t\t\tmimeType: mimeType\n\t\t\t});\n\t\t\tmedia_recorder.addEventListener(\"dataavailable\", function (e) {\n\t\t\t\trecorded_blobs.push(e.data);\n\t\t\t});\n\t\t\tmedia_recorder.start(200);\n\t\t}\n\t\trecording = !recording;\n\t}\n\n\tlet webcam_accessed = false;\n\n\tfunction record_video_or_photo(): void {\n\t\tif (mode === \"image\" && streaming) {\n\t\t\trecording = !recording;\n\t\t}\n\t\tif (mode === \"image\") {\n\t\t\ttake_picture();\n\t\t} else {\n\t\t\ttake_recording();\n\t\t}\n\t\tif (!recording && stream) {\n\t\t\tstream.getTracks().forEach((track) => track.stop());\n\t\t\tvideo_source.srcObject = null;\n\t\t\twebcam_accessed = false;\n\t\t}\n\t}\n\n\tif (streaming && mode === \"image\") {\n\t\twindow.setInterval(() => {\n\t\t\tif (video_source && !pending) {\n\t\t\t\ttake_picture();\n\t\t\t}\n\t\t}, 500);\n\t}\n\n\tlet options_open = false;\n\n\texport function click_outside(node: Node, cb: any): any {\n\t\tconst handle_click = (event: MouseEvent): void => {\n\t\t\tif (\n\t\t\t\tnode &&\n\t\t\t\t!node.contains(event.target as Node) &&\n\t\t\t\t!event.defaultPrevented\n\t\t\t) {\n\t\t\t\tcb(event);\n\t\t\t}\n\t\t};\n\n\t\tdocument.addEventListener(\"click\", handle_click, true);\n\n\t\treturn {\n\t\t\tdestroy() {\n\t\t\t\tdocument.removeEventListener(\"click\", handle_click, true);\n\t\t\t}\n\t\t};\n\t}\n\n\tfunction handle_click_outside(event: MouseEvent): void {\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\t\toptions_open = false;\n\t}\n</script>\n\n<div class=\"wrap\">\n\t<!-- svelte-ignore a11y-media-has-caption -->\n\t<!-- need to suppress for video streaming https://github.com/sveltejs/svelte/issues/5967 -->\n\t<video\n\t\tbind:this={video_source}\n\t\tclass:flip={mirror_webcam}\n\t\tclass:hide={!webcam_accessed}\n\t/>\n\t{#if !webcam_accessed}\n\t\t<div in:fade={{ delay: 100, duration: 200 }} title=\"grant webcam access\">\n\t\t\t<WebcamPermissions on:click={async () => access_webcam()} />\n\t\t</div>\n\t{:else}\n\t\t<div class=\"button-wrap\">\n\t\t\t<button\n\t\t\t\ton:click={record_video_or_photo}\n\t\t\t\taria-label={mode === \"image\" ? \"capture photo\" : \"start recording\"}\n\t\t\t>\n\t\t\t\t{#if mode === \"video\" || streaming}\n\t\t\t\t\t{#if recording}\n\t\t\t\t\t\t<div class=\"icon red\" title=\"stop recording\">\n\t\t\t\t\t\t\t<Square />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{:else}\n\t\t\t\t\t\t<div class=\"icon red\" title=\"start recording\">\n\t\t\t\t\t\t\t<Circle />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{:else}\n\t\t\t\t\t<div class=\"icon\" title=\"capture photo\">\n\t\t\t\t\t\t<Camera />\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t\t{#if !recording}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"icon\"\n\t\t\t\t\ton:click={() => (options_open = true)}\n\t\t\t\t\taria-label=\"select input source\"\n\t\t\t\t>\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t</button>\n\t\t\t{/if}\n\t\t</div>\n\t\t{#if options_open && selected_device}\n\t\t\t<select\n\t\t\t\tclass=\"select-wrap\"\n\t\t\t\taria-label=\"select source\"\n\t\t\t\tuse:click_outside={handle_click_outside}\n\t\t\t\ton:change={handle_device_change}\n\t\t\t>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"inset-icon\"\n\t\t\t\t\ton:click|stopPropagation={() => (options_open = false)}\n\t\t\t\t>\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t</button>\n\t\t\t\t{#if available_video_devices.length === 0}\n\t\t\t\t\t<option value=\"\">{i18n(\"common.no_devices\")}</option>\n\t\t\t\t{:else}\n\t\t\t\t\t{#each available_video_devices as device}\n\t\t\t\t\t\t<option\n\t\t\t\t\t\t\tvalue={device.deviceId}\n\t\t\t\t\t\t\tselected={selected_device.deviceId === device.deviceId}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{device.label}\n\t\t\t\t\t\t</option>\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</select>\n\t\t{/if}\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\tposition: relative;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\tvideo {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: cover;\n\t}\n\n\t.button-wrap {\n\t\tposition: absolute;\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-xl);\n\t\tpadding: var(--size-1-5);\n\t\tdisplay: flex;\n\t\tbottom: var(--size-2);\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 0);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--radius-xl);\n\t\tline-height: var(--size-3);\n\t\tcolor: var(--button-secondary-text-color);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\tbottom: var(--size-4);\n\t\t}\n\t}\n\n\t@media (--screen-xl) {\n\t\tbutton {\n\t\t\tbottom: var(--size-8);\n\t\t}\n\t}\n\n\t.icon {\n\t\topacity: 0.8;\n\t\twidth: 18px;\n\t\theight: 18px;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.red {\n\t\tfill: red;\n\t\tstroke: red;\n\t}\n\n\t.flip {\n\t\ttransform: scaleX(-1);\n\t}\n\n\t.select-wrap {\n\t\t-webkit-appearance: none;\n\t\t-moz-appearance: none;\n\t\tappearance: none;\n\t\tcolor: var(--button-secondary-text-color);\n\t\tbackground-color: transparent;\n\t\twidth: 95%;\n\t\tfont-size: var(--text-md);\n\t\tposition: absolute;\n\t\tbottom: var(--size-2);\n\t\tbackground-color: var(--block-background-fill);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--radius-xl);\n\t\tz-index: var(--layer-top);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t\tline-height: var(--size-4);\n\t\twhite-space: nowrap;\n\t\ttext-overflow: ellipsis;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 0);\n\t\tmax-width: var(--size-52);\n\t}\n\n\t.select-wrap > option {\n\t\tpadding: 0.25rem 0.5rem;\n\t\tborder-bottom: 1px solid var(--border-color-accent);\n\t\tpadding-right: var(--size-8);\n\t\ttext-overflow: ellipsis;\n\t\toverflow: hidden;\n\t}\n\n\t.select-wrap > option:hover {\n\t\tbackground-color: var(--color-accent);\n\t}\n\n\t.select-wrap > option:last-child {\n\t\tborder: none;\n\t}\n\n\t.inset-icon {\n\t\tposition: absolute;\n\t\ttop: 5px;\n\t\tright: -6.5px;\n\t\twidth: var(--size-10);\n\t\theight: var(--size-5);\n\t\topacity: 0.8;\n\t}\n\n\t@media (--screen-md) {\n\t\t.wrap {\n\t\t\tfont-size: var(--text-lg);\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Clear } from \"@gradio/icons\";\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<div>\n\t<IconButton\n\t\tIcon={Clear}\n\t\tlabel=\"Remove Image\"\n\t\ton:click={(event) => {\n\t\t\tdispatch(\"remove_image\");\n\t\t\tevent.stopPropagation();\n\t\t}}\n\t/>\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick } from \"svelte\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Image as ImageIcon } from \"@gradio/icons\";\n\timport type { SelectData, I18nFormatter } from \"@gradio/utils\";\n\timport { get_coordinates_of_clicked_image } from \"./utils\";\n\timport Webcam from \"./Webcam.svelte\";\n\n\timport { Upload } from \"@gradio/upload\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport ClearImage from \"./ClearImage.svelte\";\n\timport { SelectSource } from \"@gradio/atoms\";\n\timport Image from \"./Image.svelte\";\n\n\texport let value: null | FileData;\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\n\ttype source_type = \"upload\" | \"webcam\" | \"clipboard\" | \"microphone\" | null;\n\n\texport let sources: source_type[] = [\"upload\", \"clipboard\", \"webcam\"];\n\texport let streaming = false;\n\texport let pending = false;\n\texport let mirror_webcam: boolean;\n\texport let selectable = false;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\n\tlet upload_input: Upload;\n\tlet uploading = false;\n\texport let active_source: source_type = null;\n\n\tfunction handle_upload({ detail }: CustomEvent<FileData>): void {\n\t\tvalue = detail;\n\t\tdispatch(\"upload\");\n\t}\n\n\tfunction handle_clear(): void {\n\t\tvalue = null;\n\t\tdispatch(\"clear\");\n\t\tdispatch(\"change\", null);\n\t}\n\n\tasync function handle_save(img_blob: Blob | any): Promise<void> {\n\t\tpending = true;\n\t\tconst f = await upload_input.load_files([\n\t\t\tnew File([img_blob], `webcam.png`)\n\t\t]);\n\n\t\tvalue = f?.[0] || null;\n\n\t\tawait tick();\n\n\t\tdispatch(streaming ? \"stream\" : \"change\");\n\t\tpending = false;\n\t}\n\n\t$: active_streaming = streaming && active_source === \"webcam\";\n\t$: if (uploading && !active_streaming) value = null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange?: never;\n\t\tstream?: never;\n\t\tclear?: never;\n\t\tdrag: boolean;\n\t\tupload?: never;\n\t\tselect: SelectData;\n\t}>();\n\n\tlet dragging = false;\n\n\t$: dispatch(\"drag\", dragging);\n\n\tfunction handle_click(evt: MouseEvent): void {\n\t\tlet coordinates = get_coordinates_of_clicked_image(evt);\n\t\tif (coordinates) {\n\t\t\tdispatch(\"select\", { index: coordinates, value: null });\n\t\t}\n\t}\n\n\t$: if (!active_source && sources) {\n\t\tactive_source = sources[0];\n\t}\n\n\tasync function handle_select_source(\n\t\tsource: (typeof sources)[number]\n\t): Promise<void> {\n\t\tswitch (source) {\n\t\t\tcase \"clipboard\":\n\t\t\t\tupload_input.paste_clipboard();\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n</script>\n\n<BlockLabel {show_label} Icon={ImageIcon} label={label || \"Image\"} />\n\n<div data-testid=\"image\" class=\"image-container\">\n\t{#if value?.url && !active_streaming}\n\t\t<ClearImage\n\t\t\ton:remove_image={() => {\n\t\t\t\tvalue = null;\n\t\t\t\tdispatch(\"clear\");\n\t\t\t}}\n\t\t/>\n\t{/if}\n\t<div class=\"upload-container\">\n\t\t<Upload\n\t\t\thidden={value !== null || active_source === \"webcam\"}\n\t\t\tbind:this={upload_input}\n\t\t\tbind:uploading\n\t\t\tbind:dragging\n\t\t\tfiletype={active_source === \"clipboard\" ? \"clipboard\" : \"image/*\"}\n\t\t\ton:load={handle_upload}\n\t\t\ton:error\n\t\t\t{root}\n\t\t\t{max_file_size}\n\t\t\tdisable_click={!sources.includes(\"upload\")}\n\t\t\t{upload}\n\t\t\t{stream_handler}\n\t\t>\n\t\t\t{#if value === null}\n\t\t\t\t<slot />\n\t\t\t{/if}\n\t\t</Upload>\n\t\t{#if active_source === \"webcam\" && (streaming || (!streaming && !value))}\n\t\t\t<Webcam\n\t\t\t\t{root}\n\t\t\t\ton:capture={(e) => handle_save(e.detail)}\n\t\t\t\ton:stream={(e) => handle_save(e.detail)}\n\t\t\t\ton:error\n\t\t\t\ton:drag\n\t\t\t\ton:upload={(e) => handle_save(e.detail)}\n\t\t\t\t{mirror_webcam}\n\t\t\t\t{streaming}\n\t\t\t\tmode=\"image\"\n\t\t\t\tinclude_audio={false}\n\t\t\t\t{i18n}\n\t\t\t\t{upload}\n\t\t\t/>\n\t\t{:else if value !== null && !streaming}\n\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t<div class:selectable class=\"image-frame\" on:click={handle_click}>\n\t\t\t\t<Image src={value.url} alt={value.alt_text} />\n\t\t\t</div>\n\t\t{/if}\n\t</div>\n\t{#if sources.length > 1 || sources.includes(\"clipboard\")}\n\t\t<SelectSource\n\t\t\t{sources}\n\t\t\tbind:active_source\n\t\t\t{handle_clear}\n\t\t\thandle_select={handle_select_source}\n\t\t/>\n\t{/if}\n</div>\n\n<style>\n\t.image-frame :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: cover;\n\t}\n\n\t.image-frame {\n\t\tobject-fit: cover;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.upload-container {\n\t\theight: 100%;\n\t\tflex-shrink: 1;\n\t\tmax-height: 100%;\n\t}\n\n\t.image-container {\n\t\tdisplay: flex;\n\t\theight: 100%;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmax-height: 100%;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "circle", "rect", "get_coordinates_of_clicked_image", "evt", "image", "imageRect", "xScale", "yScale", "displayed_height", "y_offset", "x", "y", "displayed_width", "x_offset", "createEventDispatcher", "ctx", "create_if_block_2", "create_if_block_1", "div0", "button", "div1", "dirty", "image_changes", "downloadlink_changes", "Download", "iconbutton_changes", "ImageIcon", "value", "$$props", "label", "show_label", "show_download_button", "selectable", "show_share_button", "i18n", "dispatch", "handle_click", "coordinates", "uploadToHuggingFace", "div", "span", "get_devices", "set_local_stream", "local_stream", "video_source", "get_video_stream", "include_audio", "device_id", "size", "constraints", "set_available_devices", "devices", "device", "onMount", "create_if_block_3", "if_block2", "attr", "button_aria_label_value", "current", "div_intro", "create_in_transition", "fade", "select", "i", "t_value", "option", "set_data", "t0_value", "option_value_value", "option_selected_value", "t0", "video", "click_outside", "node", "cb", "event", "available_video_devices", "selected_device", "canvas", "streaming", "pending", "root", "mode", "mirror_webcam", "upload", "handle_device_change", "stream", "$$invalidate", "options_open", "access_webcam", "webcam_accessed", "used_devices", "track", "err", "take_picture", "context", "recording", "blob", "recorded_blobs", "mimeType", "media_recorder", "take_recording", "video_blob", "ReaderObj", "e", "_video_blob", "val", "prepare_files", "validMimeTypes", "validMimeType", "record_video_or_photo", "handle_click_outside", "$$value", "click_handler_1", "click_handler_2", "Clear", "tick", "if_block", "create_if_block_4", "blocklabel_changes", "upload_1_changes", "sources", "max_file_size", "stream_handler", "upload_input", "uploading", "active_source", "handle_upload", "detail", "handle_clear", "handle_save", "img_blob", "f", "dragging", "handle_select_source", "source", "capture_handler", "stream_handler_1", "upload_handler", "active_streaming"], "mappings": "8vCAAAA,GAgBKC,EAAAC,EAAAC,CAAA,EAJJC,GAECF,EAAAG,CAAA,EACDD,GAA+BF,EAAAI,CAAA,qlBCfhCN,GAWKC,EAAAC,EAAAC,CAAA,EADJC,GAAgCF,EAAAI,CAAA,koBCVjCN,GAWKC,EAAAC,EAAAC,CAAA,EADJC,GAAwDF,EAAAK,CAAA,uGCV5C,MAAAC,GACZC,GAC6B,CACzB,IAAAC,EACA,GAAAD,EAAI,yBAAyB,QACxBC,EAAAD,EAAI,cAAc,cAAc,KAAK,MAEtC,OAAA,CAAC,IAAK,GAAG,EAGX,MAAAE,EAAYD,EAAM,wBAClBE,EAASF,EAAM,aAAeC,EAAU,MACxCE,EAASH,EAAM,cAAgBC,EAAU,OAC/C,GAAIC,EAASC,EAAQ,CACd,MAAAC,EAAmBJ,EAAM,cAAgBE,EACzCG,GAAYJ,EAAU,OAASG,GAAoB,EACzD,IAAIE,EAAI,KAAK,OAAOP,EAAI,QAAUE,EAAU,MAAQC,CAAM,EACtDK,EAAI,KAAK,OAAOR,EAAI,QAAUE,EAAU,IAAMI,GAAYH,CAAM,CAAA,KAC9D,CACA,MAAAM,EAAkBR,EAAM,aAAeG,EACvCM,GAAYR,EAAU,MAAQO,GAAmB,EACnD,IAAAF,EAAI,KAAK,OAAOP,EAAI,QAAUE,EAAU,KAAOQ,GAAYN,CAAM,EACjEI,EAAI,KAAK,OAAOR,EAAI,QAAUE,EAAU,KAAOE,CAAM,CAC1D,CACI,OAAAG,EAAI,GAAKA,GAAKN,EAAM,cAAgBO,EAAI,GAAKA,GAAKP,EAAM,cACpD,KAED,CAACM,EAAGC,CAAC,CACb,4TC3BU,uBAAAG,EAAA,SAAqC,kEA2CxCC,EAAoB,CAAA,GAAAC,GAAAD,CAAA,IAKpBA,EAAiB,CAAA,GAAAE,GAAAF,CAAA,0BAgBT,IAAAA,KAAM,oRAtBpBrB,GAmBKC,EAAAuB,EAAArB,CAAA,kDACLH,GAIQC,EAAAwB,EAAAtB,CAAA,EAHPC,GAEKqB,EAAAC,CAAA,sCAHYL,EAAY,CAAA,CAAA,gBAnBxBA,EAAoB,CAAA,gGAKpBA,EAAiB,CAAA,+GAgBTM,EAAA,IAAAC,EAAA,IAAAP,KAAM,mQAxBC,sSAIC,KAAAA,KAAM,aAAeA,EAAK,CAAA,EAAC,WAAa,iHAAxCM,EAAA,IAAAE,EAAA,KAAAR,KAAM,sBAAeA,EAAK,CAAA,EAAC,WAAa,2LACzCS,GAAiB,MAAAT,KAAK,iBAAiB,oEAAtBM,EAAA,KAAAI,EAAA,MAAAV,KAAK,iBAAiB,yoBATtDW,SACCX,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,0CAE9B,OAAAA,EAAU,CAAA,IAAA,MAAS,CAAAA,KAAM,IAAG,8LAFzBA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,sSAxBvB,MAAAY,CAAsB,EAAAC,EACtB,CAAA,MAAAC,EAA4B,MAAS,EAAAD,GACrC,WAAAE,CAAmB,EAAAF,EACnB,CAAA,qBAAAG,EAAuB,EAAI,EAAAH,EAC3B,CAAA,WAAAI,EAAa,EAAK,EAAAJ,EAClB,CAAA,kBAAAK,EAAoB,EAAK,EAAAL,GACzB,KAAAM,CAAmB,EAAAN,EAExB,MAAAO,EAAWrB,KAKXsB,EAAgBjC,GAAe,KAChCkC,EAAcnC,GAAiCC,CAAG,EAClDkC,GACHF,EAAS,SAAY,CAAA,MAAOE,EAAa,MAAO,IAAI,CAAA,WAwBjCV,GACZA,eACW,MAAAW,GAAoBX,EAAO,QAAQ,CAC5B,OAFJ,i6CCrDd,CAAAb,sBAAAA,WAAqC,0DAY5C,uQALHpB,GAOQC,EAAAwB,EAAAtB,CAAA,EANPC,GAKKqB,EAAAoB,CAAA,EAJJzC,GAEMyC,EAAAC,CAAA,0LATD,MAAAL,EAAWrB,cAK0B,IAAAqB,EAAS,OAAO,sECTrD,SAASM,IAA0C,CAClD,OAAA,UAAU,aAAa,kBAC/B,CAMgB,SAAAC,GACfC,EACAC,EACO,CACPA,EAAa,UAAYD,EACzBC,EAAa,MAAQ,GACrBA,EAAa,KAAK,CACnB,CAEsB,eAAAC,GACrBC,EACAF,EACAG,EACuB,CACvB,MAAMC,EAAO,CACZ,MAAO,CAAE,MAAO,IAAK,EACrB,OAAQ,CAAE,MAAO,IAAK,CAAA,EAGjBC,EAAc,CACnB,MAAOF,EAAY,CAAE,SAAU,CAAE,MAAOA,CAAU,EAAG,GAAGC,CAAA,EAASA,EACjE,MAAOF,CAAA,EAGR,OAAO,UAAU,aACf,aAAaG,CAAW,EACxB,KAAMN,IACND,GAAiBC,EAAcC,CAAY,EACpCD,EACP,CACH,CAEO,SAASO,GACfC,EACoB,CAKb,OAJSA,EAAQ,OACtBC,GAA4BA,EAAO,OAAS,YAAA,CAI/C,yhBC/C+B,QAAAC,IAAS,OAAgB,yKAmP/CtC,EAAI,CAAA,IAAK,SAAWA,EAAS,CAAA,EAAA,iCAgB7BA,EAAS,CAAA,GAAAuC,GAAAvC,CAAA,EAUXwC,EAAAxC,OAAgBA,EAAe,CAAA,GAAAE,GAAAF,CAAA,mFA5BtByC,EAAArC,EAAA,aAAAsC,EAAA1C,OAAS,QAAU,gBAAkB,iBAAiB,kFAHpErB,EA8BKC,EAAA4C,EAAA1C,CAAA,EA7BJC,EAmBQyC,EAAApB,CAAA,6FAlBGJ,EAAqB,EAAA,CAAA,sJACnB,CAAA2C,GAAArC,EAAA,CAAA,EAAA,GAAAoC,KAAAA,EAAA1C,OAAS,QAAU,gBAAkB,yCAkB5CA,EAAS,CAAA,wGAUXA,OAAgBA,EAAe,CAAA,4XAnCpCrB,EAEKC,EAAA4C,EAAA1C,CAAA,qEAFW8D,EAAAC,GAAArB,EAAAsB,GAAA,CAAA,MAAO,IAAK,SAAU,GAAG,CAAA,gOAoBtCnE,EAEKC,EAAA4C,EAAA1C,CAAA,uLAZAkB,EAAS,CAAA,EAAA,mbAKbrB,EAEKC,EAAA4C,EAAA1C,CAAA,uQANLH,EAEKC,EAAA4C,EAAA1C,CAAA,oRAaPH,EAMQC,EAAAwB,EAAAtB,CAAA,8NAgBHkB,EAAuB,CAAA,EAAC,SAAW,EAACC,2NAZ1CtB,EAwBQC,EAAAmE,EAAAjE,CAAA,EAlBPC,EAKQgE,EAAA3C,CAAA,wFARWJ,EAAoB,EAAA,CAAA,CAAA,gBAC5BA,EAAoB,EAAA,CAAA,mNAWvBA,EAAuB,CAAA,CAAA,uBAA5B,OAAIgD,GAAA,qKAAChD,EAAuB,CAAA,CAAA,oBAA5B,OAAIgD,GAAA,EAAA,2HAAJ,qDAFgBC,EAAAjD,KAAK,mBAAmB,EAAA,yGAA1CrB,EAAoDC,EAAAsE,EAAApE,CAAA,iBAAlCwB,EAAA,CAAA,EAAA,GAAA2C,KAAAA,EAAAjD,KAAK,mBAAmB,EAAA,KAAAmD,GAAA,EAAAF,CAAA,uCAOvCG,EAAApD,MAAO,MAAK,mDAHNkD,EAAA,QAAAG,EAAArD,MAAO,yBACJkD,EAAA,SAAAI,EAAAtD,EAAgB,CAAA,EAAA,WAAaA,MAAO,+CAF/CrB,EAKQC,EAAAsE,EAAApE,CAAA,wBADNwB,EAAA,CAAA,EAAA,IAAA8C,KAAAA,EAAApD,MAAO,MAAK,KAAAmD,GAAAI,EAAAH,CAAA,EAHN9C,EAAA,CAAA,EAAA,IAAA+C,KAAAA,EAAArD,MAAO,yCACJM,EAAA,CAAA,EAAA,KAAAgD,KAAAA,EAAAtD,EAAgB,CAAA,EAAA,WAAaA,MAAO,qHAvD9CA,EAAe,CAAA,IAAA,mHAHRA,EAAa,CAAA,CAAA,eACZA,EAAe,CAAA,CAAA,6CAN9BrB,EAwEKC,EAAA4C,EAAA1C,CAAA,EArEJC,EAICyC,EAAAgC,CAAA,wEAFYxD,EAAa,CAAA,CAAA,+BACZA,EAAe,CAAA,CAAA,0NAjCbyD,GAAcC,EAAYC,EAAO,CAC1C,MAAAtC,EAAgBuC,GAAiB,CAErCF,GAAI,CACHA,EAAK,SAASE,EAAM,MAAc,GAAA,CAClCA,EAAM,kBAEPD,EAAGC,CAAK,GAIV,gBAAS,iBAAiB,QAASvC,EAAc,EAAI,GAGpD,SAAO,CACN,SAAS,oBAAoB,QAASA,EAAc,EAAI,2BAzMvDQ,EACAgC,EAAuB,CAAA,EACvBC,EAA0C,KAE1CC,EACO,CAAA,UAAAC,EAAY,EAAK,EAAAnD,EACjB,CAAA,QAAAoD,EAAU,EAAK,EAAApD,EACf,CAAA,KAAAqD,EAAO,EAAE,EAAArD,EAET,CAAA,KAAAsD,EAA0B,OAAO,EAAAtD,GACjC,cAAAuD,CAAsB,EAAAvD,GACtB,cAAAkB,CAAsB,EAAAlB,GACtB,KAAAM,CAAmB,EAAAN,GACnB,OAAAwD,CAAwB,EAAAxD,EAE7B,MAAAO,EAAWrB,KAQjBuC,OAAeyB,EAAS,SAAS,cAAc,QAAQ,CAAA,EAEjD,MAAAO,QAA8BV,GAAiB,OAE9C5B,EADS4B,EAAM,OACI,YAEnB9B,GAAiBC,EAAeF,EAAcG,CAAS,EAAE,KAAI,MAC3DJ,IAAY,CAClB2C,EAAS3C,GACT4C,EAAA,EAAAV,EACCD,EAAwB,KACtBxB,IAAWA,GAAO,WAAaL,CAAS,GACrC,IAAI,EACVwC,EAAA,GAAAC,EAAe,EAAK,oBAKRC,GAAa,KAE1B5C,GAAiBC,EAAeF,CAAY,EAC1C,WAAYD,GAAY,CACxB4C,EAAA,EAAAG,EAAkB,EAAI,EACtBH,EAAA,EAAAX,QAAgCnC,GAAW,CAAA,EAC3C6C,EAAS3C,CAET,CAAA,EAAA,SAAWO,GAAsB0B,CAAuB,CACxD,EAAA,KAAMzB,GAAO,CACboC,EAAA,EAAAX,EAA0BzB,CAAO,EAE3B,MAAAwC,EAAeL,EACnB,UAAS,EACT,IAAKM,GAAUA,EAAM,YAAe,GAAA,QAAQ,EAAE,CAAC,EAEjDL,EAAA,EAAAV,EAAkBc,GACfxC,EAAQ,KAAMC,GAAWA,EAAO,WAAauC,CAAY,GAC1Df,EAAwB,CAAC,CACC,KAGzB,CAAA,UAAU,cAAY,CAAK,UAAU,aAAa,eACtDzC,EAAS,QAASD,EAAK,yBAAyB,CAAA,QAEzC2D,EAAG,CACP,GAAAA,aAAe,cAAgBA,EAAI,MAAQ,kBAC9C1D,EAAS,QAASD,EAAK,2BAA2B,CAAA,aAE5C2D,YAKAC,GAAY,CAChB,IAAAC,EAAUjB,EAAO,WAAW,IAAI,IAEjCC,GAAcA,GAAaiB,IAC7BpD,EAAa,YACbA,EAAa,cAEbkC,EAAO,MAAQlC,EAAa,WAC5BkC,EAAO,OAASlC,EAAa,YAC7BmD,EAAQ,UACPnD,EACA,EACA,EACAA,EAAa,WACbA,EAAa,WAAW,EAGrBuC,IACHY,EAAQ,MAAO,GAAG,CAAC,EACnBA,EAAQ,UAAUnD,GAAeA,EAAa,WAAY,CAAC,GAG5DkC,EAAO,OACLmB,GAAI,CACJ9D,EAAS4C,EAAY,SAAW,UAAWkB,CAAI,GAEhD,YACA,KAKC,IAAAD,EAAY,GACZE,EAAc,CAAA,EACdZ,EACAa,EACAC,WAEKC,GAAc,IAClBL,EAAS,CACZI,EAAe,KAAI,EACf,IAAAE,MAAiB,KAAKJ,EAAkB,CAAA,KAAMC,CAAQ,CAAA,EACtDI,MAAgB,WACpBA,EAAU,OAAM,eAAmBC,EAAC,CAC/B,GAAAA,EAAE,OAAM,KACPC,GAAW,IAAO,KAAI,CACxBH,CAAU,EACX,UAAYH,EAAS,UAAU,CAAC,CAAA,QAE3BO,GAAG,MAASC,GAAa,CAAEF,EAAW,CAAA,MACxC9E,IAAK,MACDyD,EAAOsB,GAAKzB,CAAI,IAAI,OAAO,OAAO,EACxC,CAAC,EACH9C,EAAS,UAAWR,EAAK,EACzBQ,EAAS,gBAAgB,IAG3BoE,EAAU,cAAcD,CAAU,OAElCnE,EAAS,iBAAiB,EAC1B+D,EAAc,CAAA,MACVU,EAAc,CAAI,aAAc,WAAW,EACtC,QAAAC,KAAiBD,KACrB,cAAc,gBAAgBC,CAAa,EAAA,CAC9CV,EAAWU,QAIT,GAAAV,IAAa,KAAI,CACpB,QAAQ,MAAM,qCAAqC,SAGpDC,EAAqB,IAAA,cAAcd,EACxB,CAAA,SAAAa,CAAA,CAAA,EAEXC,EAAe,iBAAiB,yBAA2BI,EAAC,CAC3DN,EAAe,KAAKM,EAAE,IAAI,IAE3BJ,EAAe,MAAM,GAAG,EAEzBb,EAAA,EAAAS,GAAaA,CAAS,EAGnB,IAAAN,EAAkB,YAEboB,GAAqB,CACzB5B,IAAS,SAAWH,GACvBQ,EAAA,EAAAS,GAAaA,CAAS,EAEnBd,IAAS,QACZY,IAEAO,IAEI,CAAAL,GAAaV,IACjBA,EAAO,UAAS,EAAG,QAASM,GAAUA,EAAM,KAAI,CAAA,MAChDhD,EAAa,UAAY,KAAIA,CAAA,EAC7B2C,EAAA,EAAAG,EAAkB,EAAK,GAIrBX,GAAaG,IAAS,SACzB,OAAO,iBACFtC,IAAiBoC,GACpBc,KAEC,KAGA,IAAAN,EAAe,GAsBV,SAAAuB,GAAqBpC,EAAiB,CAC9CA,EAAM,eAAc,EACpBA,EAAM,gBAAe,EACrBY,EAAA,GAAAC,EAAe,EAAK,6CAQT5C,EAAYoE,6BAMmBvB,IA2BtBwB,GAAA,IAAA1B,EAAA,GAAAC,EAAe,EAAI,EAgBH0B,GAAA,IAAA3B,EAAA,GAAAC,EAAe,EAAK,g6CCtRhD,uBAAA1E,EAAA,SAAqC,iFASvCqG,GAAK,MAAA,cAAA,iGAFbzH,GASKC,EAAA4C,EAAA1C,CAAA,kIAZE,MAAAsC,EAAWrB,cAOL6D,GAAK,CACfxC,EAAS,cAAc,EACvBwC,EAAM,gBAAe,+iBCbO,KAAAyC,IAAM,OAAgB,sfA6H7CC,EAAAtG,OAAU,MAAIuC,GAAAvC,CAAA,wEAAdA,OAAU,sOAuBF,IAAAA,KAAM,IAAU,IAAAA,KAAM,wHADnCrB,GAEKC,EAAA4C,EAAA1C,CAAA,uCAF+CkB,EAAY,EAAA,CAAA,2BACnDM,EAAA,CAAA,EAAA,IAAAC,EAAA,IAAAP,KAAM,KAAUM,EAAA,CAAA,EAAA,IAAAC,EAAA,IAAAP,KAAM,kRARnB,ghBAiBDA,EAAoB,EAAA,+XALhCA,EAAO,CAAA,EAAC,OAAS,GAAKA,EAAO,CAAA,EAAC,SAAS,WAAW,0CArDzBW,GAAkB,MAAAX,MAAS,iBAGpDA,EAAK,CAAA,GAAE,KAAG,CAAKA,EAAgB,EAAA,GAAAuG,GAAAvG,CAAA,uDAU1B,OAAAA,EAAU,CAAA,IAAA,MAAQA,OAAkB,SAIlC,SAAAA,OAAkB,YAAc,YAAc,uDAKxCA,EAAO,CAAA,EAAC,SAAS,QAAQ,8PAJhCA,EAAa,EAAA,CAAA,4DAYlB,OAAAA,OAAkB,WAAaA,EAAe,CAAA,GAAA,CAAAA,OAAcA,EAAK,CAAA,GAAA,EAe5DA,EAAK,CAAA,IAAK,MAAI,CAAKA,EAAS,CAAA,EAAA,iUA3CxCrB,GA2DKC,EAAAyB,EAAAvB,CAAA,yBAlDJC,GAyCKsB,EAAAF,CAAA,qHApD2CG,EAAA,CAAA,EAAA,IAAAkG,EAAA,MAAAxG,MAAS,mBAGpDA,EAAK,CAAA,GAAE,KAAG,CAAKA,EAAgB,EAAA,kHAU1BM,EAAA,CAAA,EAAA,IAAAmG,EAAA,OAAAzG,EAAU,CAAA,IAAA,MAAQA,OAAkB,UAIlCM,EAAA,CAAA,EAAA,IAAAmG,EAAA,SAAAzG,OAAkB,YAAc,YAAc,iGAKxCA,EAAO,CAAA,EAAC,SAAS,QAAQ,yYA+BtCA,EAAO,CAAA,EAAC,OAAS,GAAKA,EAAO,CAAA,EAAC,SAAS,WAAW,oXA3I5C,MAAAY,CAAsB,EAAAC,EACtB,CAAA,MAAAC,EAA4B,MAAS,EAAAD,GACrC,WAAAE,CAAmB,EAAAF,EAInB,CAAA,QAAA6F,EAA0B,CAAA,SAAU,YAAa,QAAQ,CAAA,EAAA7F,EACzD,CAAA,UAAAmD,EAAY,EAAK,EAAAnD,EACjB,CAAA,QAAAoD,EAAU,EAAK,EAAApD,GACf,cAAAuD,CAAsB,EAAAvD,EACtB,CAAA,WAAAI,EAAa,EAAK,EAAAJ,GAClB,KAAAqD,CAAY,EAAArD,GACZ,KAAAM,CAAmB,EAAAN,EACnB,CAAA,cAAA8F,EAA+B,IAAI,EAAA9F,GACnC,OAAAwD,CAAwB,EAAAxD,GACxB,eAAA+F,CAAgC,EAAA/F,EAEvCgG,EACAC,EAAY,GACL,CAAA,cAAAC,EAA6B,IAAI,EAAAlG,EAEnC,SAAAmG,GAAgB,OAAAC,GAAM,CAC9BzC,EAAA,EAAA5D,EAAQqG,CAAM,EACd7F,EAAS,QAAQ,WAGT8F,GAAY,CACpB1C,EAAA,EAAA5D,EAAQ,IAAI,EACZQ,EAAS,OAAO,EAChBA,EAAS,SAAU,IAAI,EAGT,eAAA+F,EAAYC,EAAoB,CAC9C5C,EAAA,GAAAP,EAAU,EAAI,EACR,MAAAoD,SAAUR,EAAa,WACxB,CAAA,IAAA,MAAMO,CAAQ,EAAA,YAAA,CAAA,CAAA,EAGnB5C,EAAA,EAAA5D,EAAQyG,KAAI,CAAC,GAAK,IAAI,QAEhBhB,GAAI,EAEVjF,EAAS4C,EAAY,SAAW,QAAQ,EACxCQ,EAAA,GAAAP,EAAU,EAAK,EAMV,MAAA7C,EAAWrB,KASb,IAAAuH,EAAW,GAIN,SAAAjG,EAAajC,EAAe,KAChCkC,GAAcnC,GAAiCC,CAAG,EAClDkC,IACHF,EAAS,SAAY,CAAA,MAAOE,GAAa,MAAO,IAAI,CAAA,EAQvC,eAAAiG,GACdC,EAAgC,QAExBA,EAAM,KACR,YACJX,EAAa,gBAAe,uBAc5BrC,EAAA,EAAA5D,EAAQ,IAAI,EACZQ,EAAS,OAAO,8CAONyF,EAAYZ,kGAmBT,MAAAwB,EAAAhC,GAAM0B,EAAY1B,EAAE,MAAM,EAC3BiC,EAAAjC,GAAM0B,EAAY1B,EAAE,MAAM,oEAG1B,MAAAkC,GAAAlC,GAAM0B,EAAY1B,EAAE,MAAM,2mBAtDxC,CAAOsB,GAAiBL,OACxBK,EAAgBL,EAAQ,CAAC,CAAA,yBAxBvBkB,EAAmB5D,GAAa+C,IAAkB,QAAQ,uBACtDD,GAAS,CAAKc,GAAkBpD,EAAA,EAAA5D,EAAQ,IAAI,uBAahDQ,EAAS,OAAQkG,CAAQ"}