{"version": 3, "file": "Index-8jBbArya.js", "sources": ["../../../../js/tabitem/shared/TabItem.svelte", "../../../../js/tabitem/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { getContext, onMount, createEventDispatcher, tick } from \"svelte\";\n\timport { TABS } from \"@gradio/tabs\";\n\timport Column from \"@gradio/column\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let name: string;\n\texport let id: string | number | object = {};\n\texport let visible: boolean;\n\texport let interactive: boolean;\n\n\tconst dispatch = createEventDispatcher<{ select: SelectData }>();\n\n\tconst { register_tab, unregister_tab, selected_tab, selected_tab_index } =\n\t\tgetContext(TABS) as any;\n\n\tlet tab_index: number;\n\n\t$: tab_index = register_tab({ name, id, elem_id, visible, interactive });\n\n\tonMount(() => {\n\t\treturn (): void => unregister_tab({ name, id, elem_id });\n\t});\n\n\t$: $selected_tab_index === tab_index &&\n\t\ttick().then(() => dispatch(\"select\", { value: name, index: tab_index }));\n</script>\n\n<div\n\tid={elem_id}\n\tclass=\"tabitem {elem_classes.join(' ')}\"\n\tstyle:display={$selected_tab === id && visible ? \"block\" : \"none\"}\n\trole=\"tabpanel\"\n>\n\t<Column>\n\t\t<slot />\n\t</Column>\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-top: none;\n\t\tborder-bottom-right-radius: var(--container-radius);\n\t\tborder-bottom-left-radius: var(--container-radius);\n\t\tpadding: var(--block-padding);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport TabItem from \"./shared/TabItem.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let label: string;\n\texport let id: string | number;\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t}>;\n\texport let visible = true;\n\texport let interactive = true;\n</script>\n\n<TabItem\n\t{elem_id}\n\t{elem_classes}\n\tname={label}\n\t{visible}\n\t{interactive}\n\t{id}\n\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n>\n\t<slot />\n</TabItem>\n"], "names": ["getContext", "createEventDispatcher", "tick", "ctx", "insert", "target", "div", "anchor", "elem_id", "$$props", "elem_classes", "name", "id", "visible", "interactive", "dispatch", "register_tab", "unregister_tab", "selected_tab", "selected_tab_index", "TABS", "tab_index", "onMount", "$$invalidate", "$selected_tab_index", "label", "gradio", "select_handler", "detail"], "mappings": "seACU,CAAA,WAAAA,YAAmB,sBAAAC,EAAyB,KAAAC,UAAoB,oZA8BrEC,EAAO,CAAA,CAAA,2BACKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iBAAA,uCACtBA,EAAa,CAAA,IAAKA,EAAE,CAAA,GAAIA,EAAO,CAAA,EAAG,QAAU,MAAM,UAHlEC,EASKC,EAAAC,EAAAC,CAAA,0GARAJ,EAAO,CAAA,CAAA,8BACKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,wDACtBA,EAAa,CAAA,IAAKA,EAAE,CAAA,GAAIA,EAAO,CAAA,EAAG,QAAU,MAAM,6IA3BtD,CAAA,QAAAK,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,GACZ,KAAAE,CAAY,EAAAF,GACZ,GAAAG,EAAE,EAAA,EAAAH,GACF,QAAAI,CAAgB,EAAAJ,GAChB,YAAAK,CAAoB,EAAAL,EAEzB,MAAAM,EAAWd,KAET,aAAAe,EAAc,eAAAC,EAAgB,aAAAC,EAAc,mBAAAC,GACnDnB,EAAWoB,CAAI,6CAEZC,EAIJ,OAAAC,EAAO,IACa,IAAAL,EAAiB,CAAA,KAAAN,EAAM,GAAAC,EAAI,QAAAJ,CAAO,CAAA,oRAHnDe,EAAA,EAAAF,EAAYL,EAAe,CAAA,KAAAL,EAAM,GAAAC,EAAI,QAAAJ,EAAS,QAAAK,EAAS,YAAAC,CAAW,CAAA,CAAA,mBAMlEU,IAAwBH,GAC1BnB,IAAO,KAAI,IAAOa,EAAS,UAAY,MAAOJ,EAAM,MAAOU,CAAS,CAAA,CAAA,usCCT/DlB,EAAK,CAAA,uOAALA,EAAK,CAAA,qPAdA,CAAA,QAAAK,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,GACZ,MAAAgB,CAAa,EAAAhB,GACb,GAAAG,CAAmB,EAAAH,GACnB,OAAAiB,CAET,EAAAjB,EACS,CAAA,QAAAI,EAAU,EAAI,EAAAJ,EACd,CAAA,YAAAK,EAAc,EAAI,EAAAL,EAUf,MAAAkB,EAAA,CAAA,CAAA,OAAAC,KAAaF,EAAO,SAAS,SAAUE,CAAM"}