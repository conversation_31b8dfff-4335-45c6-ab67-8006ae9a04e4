import{S as k}from"./Index-WGC0_FkS.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";const{SvelteComponent:j,append:S,assign:C,attr:m,check_outros:I,create_component:p,create_slot:z,destroy_component:A,detach:B,element:D,flush:g,get_all_dirty_from_scope:E,get_slot_changes:F,get_spread_object:G,get_spread_update:H,group_outros:J,init:K,insert:L,mount_component:M,null_to_empty:q,safe_not_equal:N,space:O,toggle_class:f,transition_in:h,transition_out:c,update_slot_base:P}=window.__gradio__svelte__internal;function w(l){let e,n;const r=[{autoscroll:l[6].autoscroll},{i18n:l[6].i18n},l[5],{status:l[5]?l[5].status=="pending"?"generating":l[5].status:null}];let i={};for(let t=0;t<r.length;t+=1)i=C(i,r[t]);return e=new k({props:i}),{c(){p(e.$$.fragment)},m(t,o){M(e,t,o),n=!0},p(t,o){const u=o&96?H(r,[o&64&&{autoscroll:t[6].autoscroll},o&64&&{i18n:t[6].i18n},o&32&&G(t[5]),o&32&&{status:t[5]?t[5].status=="pending"?"generating":t[5].status:null}]):{};e.$set(u)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){c(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Q(l){let e,n,r,i,t=l[5]&&l[7]&&l[6]&&w(l);const o=l[9].default,u=z(o,l,l[8],null);return{c(){e=D("div"),t&&t.c(),n=O(),u&&u.c(),m(e,"id",l[1]),m(e,"class",r=q(l[2].join(" "))+" svelte-sa48pu"),f(e,"compact",l[4]==="compact"),f(e,"panel",l[4]==="panel"),f(e,"unequal-height",l[0]===!1),f(e,"stretch",l[0]),f(e,"hide",!l[3])},m(s,_){L(s,e,_),t&&t.m(e,null),S(e,n),u&&u.m(e,null),i=!0},p(s,[_]){s[5]&&s[7]&&s[6]?t?(t.p(s,_),_&224&&h(t,1)):(t=w(s),t.c(),h(t,1),t.m(e,n)):t&&(J(),c(t,1,1,()=>{t=null}),I()),u&&u.p&&(!i||_&256)&&P(u,o,s,s[8],i?F(o,s[8],_,null):E(s[8]),null),(!i||_&2)&&m(e,"id",s[1]),(!i||_&4&&r!==(r=q(s[2].join(" "))+" svelte-sa48pu"))&&m(e,"class",r),(!i||_&20)&&f(e,"compact",s[4]==="compact"),(!i||_&20)&&f(e,"panel",s[4]==="panel"),(!i||_&5)&&f(e,"unequal-height",s[0]===!1),(!i||_&5)&&f(e,"stretch",s[0]),(!i||_&12)&&f(e,"hide",!s[3])},i(s){i||(h(t),h(u,s),i=!0)},o(s){c(t),c(u,s),i=!1},d(s){s&&B(e),t&&t.d(),u&&u.d(s)}}}function R(l,e,n){let{$$slots:r={},$$scope:i}=e,{equal_height:t=!0}=e,{elem_id:o}=e,{elem_classes:u=[]}=e,{visible:s=!0}=e,{variant:_="default"}=e,{loading_status:d=void 0}=e,{gradio:v=void 0}=e,{show_progress:b=!1}=e;return l.$$set=a=>{"equal_height"in a&&n(0,t=a.equal_height),"elem_id"in a&&n(1,o=a.elem_id),"elem_classes"in a&&n(2,u=a.elem_classes),"visible"in a&&n(3,s=a.visible),"variant"in a&&n(4,_=a.variant),"loading_status"in a&&n(5,d=a.loading_status),"gradio"in a&&n(6,v=a.gradio),"show_progress"in a&&n(7,b=a.show_progress),"$$scope"in a&&n(8,i=a.$$scope)},[t,o,u,s,_,d,v,b,i,r]}class W extends j{constructor(e){super(),K(this,e,R,Q,N,{equal_height:0,elem_id:1,elem_classes:2,visible:3,variant:4,loading_status:5,gradio:6,show_progress:7})}get equal_height(){return this.$$.ctx[0]}set equal_height(e){this.$$set({equal_height:e}),g()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),g()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),g()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),g()}get variant(){return this.$$.ctx[4]}set variant(e){this.$$set({variant:e}),g()}get loading_status(){return this.$$.ctx[5]}set loading_status(e){this.$$set({loading_status:e}),g()}get gradio(){return this.$$.ctx[6]}set gradio(e){this.$$set({gradio:e}),g()}get show_progress(){return this.$$.ctx[7]}set show_progress(e){this.$$set({show_progress:e}),g()}}export{W as default};
//# sourceMappingURL=Index-B0dA5A3V.js.map
