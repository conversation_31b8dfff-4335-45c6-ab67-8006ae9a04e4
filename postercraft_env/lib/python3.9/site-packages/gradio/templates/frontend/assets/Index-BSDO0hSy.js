import{B as W}from"./Button-8nmImwVJ.js";import{B as X}from"./BlockTitle-Bkh4EzYf.js";import{S as Y}from"./Index-WGC0_FkS.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";import"./Info-COHEyv9U.js";const{SvelteComponent:Z,append:j,assign:y,attr:w,create_component:z,destroy_component:A,destroy_each:x,detach:B,element:T,ensure_array_like:H,flush:h,get_spread_object:$,get_spread_update:p,init:ee,insert:J,listen:I,mount_component:D,not_equal:te,run_all:le,set_data:M,space:q,text:P,toggle_class:O,transition_in:F,transition_out:G}=window.__gradio__svelte__internal;function K(t,e,l){const n=t.slice();return n[21]=e[l][0],n[22]=e[l][1],n[24]=l,n}function ie(t){let e;return{c(){e=P(t[9])},m(l,n){J(l,e,n)},p(l,n){n&512&&M(e,l[9])},d(l){l&&B(e)}}}function L(t){let e,l,n,a,u,k,m,_=t[21]+"",d,o,i,c;function b(){return t[18](t[22])}function v(...f){return t[19](t[24],t[22],...f)}function r(...f){return t[20](t[22],t[24],...f)}return{c(){e=T("label"),l=T("input"),k=q(),m=T("span"),d=P(_),o=q(),l.disabled=t[13],l.checked=n=t[0].includes(t[22]),w(l,"type","checkbox"),w(l,"name",a=t[22]?.toString()),w(l,"title",u=t[22]?.toString()),w(l,"class","svelte-1k4wjf2"),w(m,"class","ml-2 svelte-1k4wjf2"),w(e,"class","svelte-1k4wjf2"),O(e,"disabled",t[13]),O(e,"selected",t[0].includes(t[22]))},m(f,g){J(f,e,g),j(e,l),j(e,k),j(e,m),j(m,d),j(e,o),i||(c=[I(l,"change",b),I(l,"input",v),I(l,"keydown",r)],i=!0)},p(f,g){t=f,g&8192&&(l.disabled=t[13]),g&33&&n!==(n=t[0].includes(t[22]))&&(l.checked=n),g&32&&a!==(a=t[22]?.toString())&&w(l,"name",a),g&32&&u!==(u=t[22]?.toString())&&w(l,"title",u),g&32&&_!==(_=t[21]+"")&&M(d,_),g&8192&&O(e,"disabled",t[13]),g&33&&O(e,"selected",t[0].includes(t[22]))},d(f){f&&B(e),i=!1,le(c)}}}function se(t){let e,l,n,a,u,k;const m=[{autoscroll:t[1].autoscroll},{i18n:t[1].i18n},t[12]];let _={};for(let i=0;i<m.length;i+=1)_=y(_,m[i]);e=new Y({props:_}),e.$on("clear_status",t[17]),n=new X({props:{show_label:t[11],info:t[10],$$slots:{default:[ie]},$$scope:{ctx:t}}});let d=H(t[5]),o=[];for(let i=0;i<d.length;i+=1)o[i]=L(K(t,d,i));return{c(){z(e.$$.fragment),l=q(),z(n.$$.fragment),a=q(),u=T("div");for(let i=0;i<o.length;i+=1)o[i].c();w(u,"class","wrap svelte-1k4wjf2"),w(u,"data-testid","checkbox-group")},m(i,c){D(e,i,c),J(i,l,c),D(n,i,c),J(i,a,c),J(i,u,c);for(let b=0;b<o.length;b+=1)o[b]&&o[b].m(u,null);k=!0},p(i,c){const b=c&4098?p(m,[c&2&&{autoscroll:i[1].autoscroll},c&2&&{i18n:i[1].i18n},c&4096&&$(i[12])]):{};e.$set(b);const v={};if(c&2048&&(v.show_label=i[11]),c&1024&&(v.info=i[10]),c&33554944&&(v.$$scope={dirty:c,ctx:i}),n.$set(v),c&24611){d=H(i[5]);let r;for(r=0;r<d.length;r+=1){const f=K(i,d,r);o[r]?o[r].p(f,c):(o[r]=L(f),o[r].c(),o[r].m(u,null))}for(;r<o.length;r+=1)o[r].d(1);o.length=d.length}},i(i){k||(F(e.$$.fragment,i),F(n.$$.fragment,i),k=!0)},o(i){G(e.$$.fragment,i),G(n.$$.fragment,i),k=!1},d(i){i&&(B(l),B(a),B(u)),A(e,i),A(n,i),x(o,i)}}}function ne(t){let e,l;return e=new W({props:{visible:t[4],elem_id:t[2],elem_classes:t[3],type:"fieldset",container:t[6],scale:t[7],min_width:t[8],$$slots:{default:[se]},$$scope:{ctx:t}}}),{c(){z(e.$$.fragment)},m(n,a){D(e,n,a),l=!0},p(n,[a]){const u={};a&16&&(u.visible=n[4]),a&4&&(u.elem_id=n[2]),a&8&&(u.elem_classes=n[3]),a&64&&(u.container=n[6]),a&128&&(u.scale=n[7]),a&256&&(u.min_width=n[8]),a&33570339&&(u.$$scope={dirty:a,ctx:n}),e.$set(u)},i(n){l||(F(e.$$.fragment,n),l=!0)},o(n){G(e.$$.fragment,n),l=!1},d(n){A(e,n)}}}function ae(t,e,l){let n,{gradio:a}=e,{elem_id:u=""}=e,{elem_classes:k=[]}=e,{visible:m=!0}=e,{value:_=[]}=e,{choices:d}=e,{container:o=!0}=e,{scale:i=null}=e,{min_width:c=void 0}=e,{label:b=a.i18n("checkbox.checkbox_group")}=e,{info:v=void 0}=e,{show_label:r=!0}=e,{loading_status:f}=e,{interactive:g=!0}=e,{old_value:N=_.slice()}=e;function C(s){_.includes(s)?l(0,_=_.filter(S=>S!==s)):l(0,_=[..._,s]),a.dispatch("input")}const Q=()=>a.dispatch("clear_status",f),R=s=>C(s),U=(s,S,E)=>a.dispatch("select",{index:s,value:S,selected:E.currentTarget.checked}),V=(s,S,E)=>{E.key==="Enter"&&(C(s),a.dispatch("select",{index:S,value:s,selected:!_.includes(s)}))};return t.$$set=s=>{"gradio"in s&&l(1,a=s.gradio),"elem_id"in s&&l(2,u=s.elem_id),"elem_classes"in s&&l(3,k=s.elem_classes),"visible"in s&&l(4,m=s.visible),"value"in s&&l(0,_=s.value),"choices"in s&&l(5,d=s.choices),"container"in s&&l(6,o=s.container),"scale"in s&&l(7,i=s.scale),"min_width"in s&&l(8,c=s.min_width),"label"in s&&l(9,b=s.label),"info"in s&&l(10,v=s.info),"show_label"in s&&l(11,r=s.show_label),"loading_status"in s&&l(12,f=s.loading_status),"interactive"in s&&l(16,g=s.interactive),"old_value"in s&&l(15,N=s.old_value)},t.$$.update=()=>{t.$$.dirty&65536&&l(13,n=!g),t.$$.dirty&32771&&JSON.stringify(N)!==JSON.stringify(_)&&(l(15,N=_),a.dispatch("change"))},[_,a,u,k,m,d,o,i,c,b,v,r,f,n,C,N,g,Q,R,U,V]}class he extends Z{constructor(e){super(),ee(this,e,ae,ne,te,{gradio:1,elem_id:2,elem_classes:3,visible:4,value:0,choices:5,container:6,scale:7,min_width:8,label:9,info:10,show_label:11,loading_status:12,interactive:16,old_value:15})}get gradio(){return this.$$.ctx[1]}set gradio(e){this.$$set({gradio:e}),h()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),h()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get choices(){return this.$$.ctx[5]}set choices(e){this.$$set({choices:e}),h()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[7]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[8]}set min_width(e){this.$$set({min_width:e}),h()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),h()}get info(){return this.$$.ctx[10]}set info(e){this.$$set({info:e}),h()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),h()}get loading_status(){return this.$$.ctx[12]}set loading_status(e){this.$$set({loading_status:e}),h()}get interactive(){return this.$$.ctx[16]}set interactive(e){this.$$set({interactive:e}),h()}get old_value(){return this.$$.ctx[15]}set old_value(e){this.$$set({old_value:e}),h()}}export{he as default};
//# sourceMappingURL=Index-BSDO0hSy.js.map
