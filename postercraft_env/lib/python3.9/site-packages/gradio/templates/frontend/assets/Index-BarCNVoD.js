const __vite__fileDeps=["./Canvas3D-D6zfEcue.js","./Index-WGC0_FkS.js","./index-COY1HN2y.js","./index-luc1OtuK.css","./Index-hBVU0Tzp.css","./file-url-Bf0nK4ai.js","./Canvas3DGS-DvAfoeI-.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as ie}from"./index-COY1HN2y.js";import{I as De,S as Ce}from"./Index-WGC0_FkS.js";import{B as he}from"./BlockLabel-CJsotHlk.js";import{a as Oe}from"./DownloadLink-DYBmO3sz.js";import{F as oe}from"./File-BQ_9P3Ye.js";import{U as Te}from"./Undo-CpmTQw3B.js";import{U as qe}from"./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js";import{M as Pe}from"./ModifyUpload-DZAlpNPL.js";import{B as Ie}from"./Button-8nmImwVJ.js";import{E as Ve}from"./Empty-Vuj7-ssy.js";import{U as Re}from"./UploadText-DlCTYTPP.js";import{default as tn}from"./Example-uQ8MuYg6.js";import"./svelte/svelte.js";import"./file-url-Bf0nK4ai.js";import"./Upload-Cp8Go_XF.js";var we=Object.prototype.hasOwnProperty;function $e(l,e,n){for(n of l.keys())if(H(n,e))return n}function H(l,e){var n,t,s;if(l===e)return!0;if(l&&e&&(n=l.constructor)===e.constructor){if(n===Date)return l.getTime()===e.getTime();if(n===RegExp)return l.toString()===e.toString();if(n===Array){if((t=l.length)===e.length)for(;t--&&H(l[t],e[t]););return t===-1}if(n===Set){if(l.size!==e.size)return!1;for(t of l)if(s=t,s&&typeof s=="object"&&(s=$e(e,s),!s)||!e.has(s))return!1;return!0}if(n===Map){if(l.size!==e.size)return!1;for(t of l)if(s=t[0],s&&typeof s=="object"&&(s=$e(e,s),!s)||!H(t[1],e.get(s)))return!1;return!0}if(n===ArrayBuffer)l=new Uint8Array(l),e=new Uint8Array(e);else if(n===DataView){if((t=l.byteLength)===e.byteLength)for(;t--&&l.getInt8(t)===e.getInt8(t););return t===-1}if(ArrayBuffer.isView(l)){if((t=l.byteLength)===e.byteLength)for(;t--&&l[t]===e[t];);return t===-1}if(!n||typeof l=="object"){t=0;for(n in l)if(we.call(l,n)&&++t&&!we.call(e,n)||!(n in e)||!H(l[n],e[n]))return!1;return Object.keys(e).length===t}}return l!==l&&e!==e}const{SvelteComponent:Ge,add_flush_callback:Se,append:te,attr:O,bind:ne,binding_callbacks:J,check_outros:K,construct_svelte_component:le,create_component:T,destroy_component:q,detach:Q,element:me,empty:ge,flush:L,group_outros:X,init:We,insert:Y,mount_component:P,noop:Fe,safe_not_equal:He,space:de,transition_in:w,transition_out:D}=window.__gradio__svelte__internal;function ke(l){let e,n,t,s,a,o,_,i,f,r,c=!l[8]&&ve(l);a=new De({props:{Icon:Oe,label:l[4]("common.download")}});const m=[Ke,Je],h=[];function $(d,p){return d[8]?0:1}return i=$(l),f=h[i]=m[i](l),{c(){e=me("div"),n=me("div"),c&&c.c(),t=de(),s=me("a"),T(a.$$.fragment),_=de(),f.c(),O(s,"href",l[12]),O(s,"target",window.__is_colab__?"_blank":null),O(s,"download",o=window.__is_colab__?null:l[0].orig_name||l[0].path),O(n,"class","buttons svelte-8r2c23"),O(e,"class","model3D svelte-8r2c23")},m(d,p){Y(d,e,p),te(e,n),c&&c.m(n,null),te(n,t),te(n,s),P(a,s,null),te(e,_),h[i].m(e,null),r=!0},p(d,p){d[8]?c&&(X(),D(c,1,1,()=>{c=null}),K()):c?(c.p(d,p),p&256&&w(c,1)):(c=ve(d),c.c(),w(c,1),c.m(n,t));const z={};p&16&&(z.label=d[4]("common.download")),a.$set(z),(!r||p&4096)&&O(s,"href",d[12]),(!r||p&1&&o!==(o=window.__is_colab__?null:d[0].orig_name||d[0].path))&&O(s,"download",o);let E=i;i=$(d),i===E?h[i].p(d,p):(X(),D(h[E],1,1,()=>{h[E]=null}),K(),f=h[i],f?f.p(d,p):(f=h[i]=m[i](d),f.c()),w(f,1),f.m(e,null))},i(d){r||(w(c),w(a.$$.fragment,d),w(f),r=!0)},o(d){D(c),D(a.$$.fragment,d),D(f),r=!1},d(d){d&&Q(e),c&&c.d(),q(a),h[i].d()}}}function ve(l){let e,n;return e=new De({props:{Icon:Te,label:"Undo"}}),e.$on("click",l[15]),{c(){T(e.$$.fragment)},m(t,s){P(e,t,s),n=!0},p:Fe,i(t){n||(w(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){q(e,t)}}}function Je(l){let e,n,t,s;function a(i){l[18](i)}var o=l[11];function _(i,f){let r={value:i[0],clear_color:i[1],camera_position:i[7],zoom_speed:i[5],pan_speed:i[6]};return i[12]!==void 0&&(r.resolved_url=i[12]),{props:r}}return o&&(e=le(o,_(l)),l[17](e),J.push(()=>ne(e,"resolved_url",a))),{c(){e&&T(e.$$.fragment),t=ge()},m(i,f){e&&P(e,i,f),Y(i,t,f),s=!0},p(i,f){if(f&2048&&o!==(o=i[11])){if(e){X();const r=e;D(r.$$.fragment,1,0,()=>{q(r,1)}),K()}o?(e=le(o,_(i)),i[17](e),J.push(()=>ne(e,"resolved_url",a)),T(e.$$.fragment),w(e.$$.fragment,1),P(e,t.parentNode,t)):e=null}else if(o){const r={};f&1&&(r.value=i[0]),f&2&&(r.clear_color=i[1]),f&128&&(r.camera_position=i[7]),f&32&&(r.zoom_speed=i[5]),f&64&&(r.pan_speed=i[6]),!n&&f&4096&&(n=!0,r.resolved_url=i[12],Se(()=>n=!1)),e.$set(r)}},i(i){s||(e&&w(e.$$.fragment,i),s=!0)},o(i){e&&D(e.$$.fragment,i),s=!1},d(i){i&&Q(t),l[17](null),e&&q(e,i)}}}function Ke(l){let e,n,t,s;function a(i){l[16](i)}var o=l[10];function _(i,f){let r={value:i[0],zoom_speed:i[5],pan_speed:i[6]};return i[12]!==void 0&&(r.resolved_url=i[12]),{props:r}}return o&&(e=le(o,_(l)),J.push(()=>ne(e,"resolved_url",a))),{c(){e&&T(e.$$.fragment),t=ge()},m(i,f){e&&P(e,i,f),Y(i,t,f),s=!0},p(i,f){if(f&1024&&o!==(o=i[10])){if(e){X();const r=e;D(r.$$.fragment,1,0,()=>{q(r,1)}),K()}o?(e=le(o,_(i)),J.push(()=>ne(e,"resolved_url",a)),T(e.$$.fragment),w(e.$$.fragment,1),P(e,t.parentNode,t)):e=null}else if(o){const r={};f&1&&(r.value=i[0]),f&32&&(r.zoom_speed=i[5]),f&64&&(r.pan_speed=i[6]),!n&&f&4096&&(n=!0,r.resolved_url=i[12],Se(()=>n=!1)),e.$set(r)}},i(i){s||(e&&w(e.$$.fragment,i),s=!0)},o(i){e&&D(e.$$.fragment,i),s=!1},d(i){i&&Q(t),e&&q(e,i)}}}function Qe(l){let e,n,t,s;e=new he({props:{show_label:l[3],Icon:oe,label:l[2]||l[4]("3D_model.3d_model")}});let a=l[0]&&ke(l);return{c(){T(e.$$.fragment),n=de(),a&&a.c(),t=ge()},m(o,_){P(e,o,_),Y(o,n,_),a&&a.m(o,_),Y(o,t,_),s=!0},p(o,[_]){const i={};_&8&&(i.show_label=o[3]),_&20&&(i.label=o[2]||o[4]("3D_model.3d_model")),e.$set(i),o[0]?a?(a.p(o,_),_&1&&w(a,1)):(a=ke(o),a.c(),w(a,1),a.m(t.parentNode,t)):a&&(X(),D(a,1,1,()=>{a=null}),K())},i(o){s||(w(e.$$.fragment,o),w(a),s=!0)},o(o){D(e.$$.fragment,o),D(a),s=!1},d(o){o&&(Q(n),Q(t)),q(e,o),a&&a.d(o)}}}async function Xe(){return(await ie(()=>import("./Canvas3D-D6zfEcue.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function Ye(){return(await ie(()=>import("./Canvas3DGS-DvAfoeI-.js"),__vite__mapDeps([6,5,1,2,3,4]),import.meta.url)).default}function Ze(l,e,n){let{value:t}=e,{clear_color:s=[0,0,0,0]}=e,{label:a=""}=e,{show_label:o}=e,{i18n:_}=e,{zoom_speed:i=1}=e,{pan_speed:f=1}=e,{camera_position:r=[null,null,null]}=e,c={camera_position:r,zoom_speed:i,pan_speed:f},m=!1,h,$,d;function p(){d?.reset_camera_position(r,i,f)}let z;const E=()=>p();function A(g){z=g,n(12,z)}function j(g){J[g?"unshift":"push"](()=>{d=g,n(9,d)})}function W(g){z=g,n(12,z)}return l.$$set=g=>{"value"in g&&n(0,t=g.value),"clear_color"in g&&n(1,s=g.clear_color),"label"in g&&n(2,a=g.label),"show_label"in g&&n(3,o=g.show_label),"i18n"in g&&n(4,_=g.i18n),"zoom_speed"in g&&n(5,i=g.zoom_speed),"pan_speed"in g&&n(6,f=g.pan_speed),"camera_position"in g&&n(7,r=g.camera_position)},l.$$.update=()=>{l.$$.dirty&257&&t&&(n(8,m=t.path.endsWith(".splat")||t.path.endsWith(".ply")),m?Ye().then(g=>{n(10,h=g)}):Xe().then(g=>{n(11,$=g)})),l.$$.dirty&17120&&(!H(c.camera_position,r)||c.zoom_speed!==i||c.pan_speed!==f)&&(d?.reset_camera_position(r,i,f),n(14,c={camera_position:r,zoom_speed:i,pan_speed:f}))},[t,s,a,o,_,i,f,r,m,d,h,$,z,p,c,E,A,j,W]}class ye extends Ge{constructor(e){super(),We(this,e,Ze,Qe,He,{value:0,clear_color:1,label:2,show_label:3,i18n:4,zoom_speed:5,pan_speed:6,camera_position:7})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),L()}get clear_color(){return this.$$.ctx[1]}set clear_color(e){this.$$set({clear_color:e}),L()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),L()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),L()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),L()}get zoom_speed(){return this.$$.ctx[5]}set zoom_speed(e){this.$$set({zoom_speed:e}),L()}get pan_speed(){return this.$$.ctx[6]}set pan_speed(e){this.$$set({pan_speed:e}),L()}get camera_position(){return this.$$.ctx[7]}set camera_position(e){this.$$set({camera_position:e}),L()}}const xe=ye,{SvelteComponent:et,add_flush_callback:tt,append:nt,attr:lt,bind:st,binding_callbacks:Ee,bubble:it,check_outros:ae,construct_svelte_component:se,create_component:V,create_slot:ot,destroy_component:R,detach:Z,element:at,empty:pe,flush:C,get_all_dirty_from_scope:rt,get_slot_changes:_t,group_outros:re,init:ut,insert:y,mount_component:G,safe_not_equal:ft,space:Me,transition_in:I,transition_out:S,update_slot_base:ct}=window.__gradio__svelte__internal,{createEventDispatcher:mt,tick:ze}=window.__gradio__svelte__internal;function dt(l){let e,n,t,s,a,o;n=new Pe({props:{undoable:!l[12],i18n:l[5],absolute:!0}}),n.$on("clear",l[18]),n.$on("undo",l[19]);const _=[pt,gt],i=[];function f(r,c){return r[12]?0:1}return s=f(l),a=i[s]=_[s](l),{c(){e=at("div"),V(n.$$.fragment),t=Me(),a.c(),lt(e,"class","input-model svelte-hvduv8")},m(r,c){y(r,e,c),G(n,e,null),nt(e,t),i[s].m(e,null),o=!0},p(r,c){const m={};c&4096&&(m.undoable=!r[12]),c&32&&(m.i18n=r[5]),n.$set(m);let h=s;s=f(r),s===h?i[s].p(r,c):(re(),S(i[h],1,1,()=>{i[h]=null}),ae(),a=i[s],a?a.p(r,c):(a=i[s]=_[s](r),a.c()),I(a,1),a.m(e,null))},i(r){o||(I(n.$$.fragment,r),I(a),o=!0)},o(r){S(n.$$.fragment,r),S(a),o=!1},d(r){r&&Z(e),R(n),i[s].d()}}}function ht(l){let e,n,t;function s(o){l[21](o)}let a={upload:l[10],stream_handler:l[11],root:l[4],max_file_size:l[8],filetype:[".stl",".obj",".gltf",".glb","model/obj",".splat",".ply"],$$slots:{default:[bt]},$$scope:{ctx:l}};return l[13]!==void 0&&(a.dragging=l[13]),e=new qe({props:a}),Ee.push(()=>st(e,"dragging",s)),e.$on("load",l[17]),e.$on("error",l[22]),{c(){V(e.$$.fragment)},m(o,_){G(e,o,_),t=!0},p(o,_){const i={};_&1024&&(i.upload=o[10]),_&2048&&(i.stream_handler=o[11]),_&16&&(i.root=o[4]),_&256&&(i.max_file_size=o[8]),_&16777216&&(i.$$scope={dirty:_,ctx:o}),!n&&_&8192&&(n=!0,i.dragging=o[13],tt(()=>n=!1)),e.$set(i)},i(o){t||(I(e.$$.fragment,o),t=!0)},o(o){S(e.$$.fragment,o),t=!1},d(o){R(e,o)}}}function gt(l){let e,n,t;var s=l[15];function a(o,_){return{props:{value:o[0],clear_color:o[1],camera_position:o[9],zoom_speed:o[6],pan_speed:o[7]}}}return s&&(e=se(s,a(l)),l[23](e)),{c(){e&&V(e.$$.fragment),n=pe()},m(o,_){e&&G(e,o,_),y(o,n,_),t=!0},p(o,_){if(_&32768&&s!==(s=o[15])){if(e){re();const i=e;S(i.$$.fragment,1,0,()=>{R(i,1)}),ae()}s?(e=se(s,a(o)),o[23](e),V(e.$$.fragment),I(e.$$.fragment,1),G(e,n.parentNode,n)):e=null}else if(s){const i={};_&1&&(i.value=o[0]),_&2&&(i.clear_color=o[1]),_&512&&(i.camera_position=o[9]),_&64&&(i.zoom_speed=o[6]),_&128&&(i.pan_speed=o[7]),e.$set(i)}},i(o){t||(e&&I(e.$$.fragment,o),t=!0)},o(o){e&&S(e.$$.fragment,o),t=!1},d(o){o&&Z(n),l[23](null),e&&R(e,o)}}}function pt(l){let e,n,t;var s=l[14];function a(o,_){return{props:{value:o[0],zoom_speed:o[6],pan_speed:o[7]}}}return s&&(e=se(s,a(l))),{c(){e&&V(e.$$.fragment),n=pe()},m(o,_){e&&G(e,o,_),y(o,n,_),t=!0},p(o,_){if(_&16384&&s!==(s=o[14])){if(e){re();const i=e;S(i.$$.fragment,1,0,()=>{R(i,1)}),ae()}s?(e=se(s,a(o)),V(e.$$.fragment),I(e.$$.fragment,1),G(e,n.parentNode,n)):e=null}else if(s){const i={};_&1&&(i.value=o[0]),_&64&&(i.zoom_speed=o[6]),_&128&&(i.pan_speed=o[7]),e.$set(i)}},i(o){t||(e&&I(e.$$.fragment,o),t=!0)},o(o){e&&S(e.$$.fragment,o),t=!1},d(o){o&&Z(n),e&&R(e,o)}}}function bt(l){let e;const n=l[20].default,t=ot(n,l,l[24],null);return{c(){t&&t.c()},m(s,a){t&&t.m(s,a),e=!0},p(s,a){t&&t.p&&(!e||a&16777216)&&ct(t,n,s,s[24],e?_t(n,s[24],a,null):rt(s[24]),null)},i(s){e||(I(t,s),e=!0)},o(s){S(t,s),e=!1},d(s){t&&t.d(s)}}}function wt(l){let e,n,t,s,a,o;e=new he({props:{show_label:l[3],Icon:oe,label:l[2]||"3D Model"}});const _=[ht,dt],i=[];function f(r,c){return r[0]===null?0:1}return t=f(l),s=i[t]=_[t](l),{c(){V(e.$$.fragment),n=Me(),s.c(),a=pe()},m(r,c){G(e,r,c),y(r,n,c),i[t].m(r,c),y(r,a,c),o=!0},p(r,[c]){const m={};c&8&&(m.show_label=r[3]),c&4&&(m.label=r[2]||"3D Model"),e.$set(m);let h=t;t=f(r),t===h?i[t].p(r,c):(re(),S(i[h],1,1,()=>{i[h]=null}),ae(),s=i[t],s?s.p(r,c):(s=i[t]=_[t](r),s.c()),I(s,1),s.m(a.parentNode,a))},i(r){o||(I(e.$$.fragment,r),I(s),o=!0)},o(r){S(e.$$.fragment,r),S(s),o=!1},d(r){r&&(Z(n),Z(a)),R(e,r),i[t].d(r)}}}async function $t(){return(await ie(()=>import("./Canvas3D-D6zfEcue.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function kt(){return(await ie(()=>import("./Canvas3DGS-DvAfoeI-.js"),__vite__mapDeps([6,5,1,2,3,4]),import.meta.url)).default}function vt(l,e,n){let{$$slots:t={},$$scope:s}=e,{value:a}=e,{clear_color:o=[0,0,0,0]}=e,{label:_=""}=e,{show_label:i}=e,{root:f}=e,{i18n:r}=e,{zoom_speed:c=1}=e,{pan_speed:m=1}=e,{max_file_size:h=null}=e,{camera_position:$=[null,null,null]}=e,{upload:d}=e,{stream_handler:p}=e;async function z({detail:u}){n(0,a=u),await ze(),N("change",a),N("load",a)}async function E(){n(0,a=null),await ze(),N("clear"),N("change")}let A=!1,j,W,g;async function _e(){g?.reset_camera_position($,c,m)}const N=mt();let F=!1;function ue(u){F=u,n(13,F)}function fe(u){it.call(this,l,u)}function ce(u){Ee[u?"unshift":"push"](()=>{g=u,n(16,g)})}return l.$$set=u=>{"value"in u&&n(0,a=u.value),"clear_color"in u&&n(1,o=u.clear_color),"label"in u&&n(2,_=u.label),"show_label"in u&&n(3,i=u.show_label),"root"in u&&n(4,f=u.root),"i18n"in u&&n(5,r=u.i18n),"zoom_speed"in u&&n(6,c=u.zoom_speed),"pan_speed"in u&&n(7,m=u.pan_speed),"max_file_size"in u&&n(8,h=u.max_file_size),"camera_position"in u&&n(9,$=u.camera_position),"upload"in u&&n(10,d=u.upload),"stream_handler"in u&&n(11,p=u.stream_handler),"$$scope"in u&&n(24,s=u.$$scope)},l.$$.update=()=>{l.$$.dirty&4097&&a&&(n(12,A=a.path.endsWith(".splat")||a.path.endsWith(".ply")),A?kt().then(u=>{n(14,j=u)}):$t().then(u=>{n(15,W=u)})),l.$$.dirty&8192&&N("drag",F)},[a,o,_,i,f,r,c,m,h,$,d,p,A,F,j,W,g,z,E,_e,t,ue,fe,ce,s]}class zt extends et{constructor(e){super(),ut(this,e,vt,wt,ft,{value:0,clear_color:1,label:2,show_label:3,root:4,i18n:5,zoom_speed:6,pan_speed:7,max_file_size:8,camera_position:9,upload:10,stream_handler:11})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),C()}get clear_color(){return this.$$.ctx[1]}set clear_color(e){this.$$set({clear_color:e}),C()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),C()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),C()}get root(){return this.$$.ctx[4]}set root(e){this.$$set({root:e}),C()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),C()}get zoom_speed(){return this.$$.ctx[6]}set zoom_speed(e){this.$$set({zoom_speed:e}),C()}get pan_speed(){return this.$$.ctx[7]}set pan_speed(e){this.$$set({pan_speed:e}),C()}get max_file_size(){return this.$$.ctx[8]}set max_file_size(e){this.$$set({max_file_size:e}),C()}get camera_position(){return this.$$.ctx[9]}set camera_position(e){this.$$set({camera_position:e}),C()}get upload(){return this.$$.ctx[10]}set upload(e){this.$$set({upload:e}),C()}get stream_handler(){return this.$$.ctx[11]}set stream_handler(e){this.$$set({stream_handler:e}),C()}}const Dt=zt,{SvelteComponent:Ct,assign:Ue,check_outros:Be,create_component:M,destroy_component:U,detach:x,empty:Ae,flush:b,get_spread_object:Le,get_spread_update:je,group_outros:Ne,init:It,insert:ee,mount_component:B,safe_not_equal:St,space:be,transition_in:k,transition_out:v}=window.__gradio__svelte__internal;function Et(l){let e,n;return e=new Ie({props:{visible:l[4],variant:l[0]===null?"dashed":"solid",border_mode:l[17]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],container:l[9],scale:l[10],min_width:l[11],height:l[13],$$slots:{default:[Bt]},$$scope:{ctx:l}}}),{c(){M(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},p(t,s){const a={};s&16&&(a.visible=t[4]),s&1&&(a.variant=t[0]===null?"dashed":"solid"),s&131072&&(a.border_mode=t[17]?"focus":"base"),s&4&&(a.elem_id=t[2]),s&8&&(a.elem_classes=t[3]),s&512&&(a.container=t[9]),s&1024&&(a.scale=t[10]),s&2048&&(a.min_width=t[11]),s&8192&&(a.height=t[13]),s&67293667&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){n||(k(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){U(e,t)}}}function Mt(l){let e,n;return e=new Ie({props:{visible:l[4],variant:l[0]===null?"dashed":"solid",border_mode:l[17]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],container:l[9],scale:l[10],min_width:l[11],height:l[13],$$slots:{default:[Nt]},$$scope:{ctx:l}}}),{c(){M(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},p(t,s){const a={};s&16&&(a.visible=t[4]),s&1&&(a.variant=t[0]===null?"dashed":"solid"),s&131072&&(a.border_mode=t[17]?"focus":"base"),s&4&&(a.elem_id=t[2]),s&8&&(a.elem_classes=t[3]),s&512&&(a.container=t[9]),s&1024&&(a.scale=t[10]),s&2048&&(a.min_width=t[11]),s&8192&&(a.height=t[13]),s&67162563&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){n||(k(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){U(e,t)}}}function Ut(l){let e,n;return e=new Re({props:{i18n:l[12].i18n,type:"file"}}),{c(){M(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},p(t,s){const a={};s&4096&&(a.i18n=t[12].i18n),e.$set(a)},i(t){n||(k(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){U(e,t)}}}function Bt(l){let e,n,t,s;const a=[{autoscroll:l[12].autoscroll},{i18n:l[12].i18n},l[1]];let o={};for(let _=0;_<a.length;_+=1)o=Ue(o,a[_]);return e=new Ce({props:o}),e.$on("clear_status",l[19]),t=new Dt({props:{label:l[7],show_label:l[8],root:l[5],clear_color:l[6],value:l[0],camera_position:l[15],zoom_speed:l[14],i18n:l[12].i18n,max_file_size:l[12].max_file_size,upload:l[12].client.upload,stream_handler:l[12].client.stream,$$slots:{default:[Ut]},$$scope:{ctx:l}}}),t.$on("change",l[20]),t.$on("drag",l[21]),t.$on("change",l[22]),t.$on("clear",l[23]),t.$on("load",l[24]),t.$on("error",l[25]),{c(){M(e.$$.fragment),n=be(),M(t.$$.fragment)},m(_,i){B(e,_,i),ee(_,n,i),B(t,_,i),s=!0},p(_,i){const f=i&4098?je(a,[i&4096&&{autoscroll:_[12].autoscroll},i&4096&&{i18n:_[12].i18n},i&2&&Le(_[1])]):{};e.$set(f);const r={};i&128&&(r.label=_[7]),i&256&&(r.show_label=_[8]),i&32&&(r.root=_[5]),i&64&&(r.clear_color=_[6]),i&1&&(r.value=_[0]),i&32768&&(r.camera_position=_[15]),i&16384&&(r.zoom_speed=_[14]),i&4096&&(r.i18n=_[12].i18n),i&4096&&(r.max_file_size=_[12].max_file_size),i&4096&&(r.upload=_[12].client.upload),i&4096&&(r.stream_handler=_[12].client.stream),i&67112960&&(r.$$scope={dirty:i,ctx:_}),t.$set(r)},i(_){s||(k(e.$$.fragment,_),k(t.$$.fragment,_),s=!0)},o(_){v(e.$$.fragment,_),v(t.$$.fragment,_),s=!1},d(_){_&&x(n),U(e,_),U(t,_)}}}function At(l){let e,n,t,s;return e=new he({props:{show_label:l[8],Icon:oe,label:l[7]||"3D Model"}}),t=new Ve({props:{unpadded_box:!0,size:"large",$$slots:{default:[jt]},$$scope:{ctx:l}}}),{c(){M(e.$$.fragment),n=be(),M(t.$$.fragment)},m(a,o){B(e,a,o),ee(a,n,o),B(t,a,o),s=!0},p(a,o){const _={};o&256&&(_.show_label=a[8]),o&128&&(_.label=a[7]||"3D Model"),e.$set(_);const i={};o&67108864&&(i.$$scope={dirty:o,ctx:a}),t.$set(i)},i(a){s||(k(e.$$.fragment,a),k(t.$$.fragment,a),s=!0)},o(a){v(e.$$.fragment,a),v(t.$$.fragment,a),s=!1},d(a){a&&x(n),U(e,a),U(t,a)}}}function Lt(l){let e,n;return e=new xe({props:{value:l[0],i18n:l[12].i18n,clear_color:l[6],label:l[7],show_label:l[8],camera_position:l[15],zoom_speed:l[14]}}),{c(){M(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},p(t,s){const a={};s&1&&(a.value=t[0]),s&4096&&(a.i18n=t[12].i18n),s&64&&(a.clear_color=t[6]),s&128&&(a.label=t[7]),s&256&&(a.show_label=t[8]),s&32768&&(a.camera_position=t[15]),s&16384&&(a.zoom_speed=t[14]),e.$set(a)},i(t){n||(k(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){U(e,t)}}}function jt(l){let e,n;return e=new oe({}),{c(){M(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},i(t){n||(k(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){U(e,t)}}}function Nt(l){let e,n,t,s,a,o;const _=[{autoscroll:l[12].autoscroll},{i18n:l[12].i18n},l[1]];let i={};for(let m=0;m<_.length;m+=1)i=Ue(i,_[m]);e=new Ce({props:i}),e.$on("clear_status",l[18]);const f=[Lt,At],r=[];function c(m,h){return m[0]?0:1}return t=c(l),s=r[t]=f[t](l),{c(){M(e.$$.fragment),n=be(),s.c(),a=Ae()},m(m,h){B(e,m,h),ee(m,n,h),r[t].m(m,h),ee(m,a,h),o=!0},p(m,h){const $=h&4098?je(_,[h&4096&&{autoscroll:m[12].autoscroll},h&4096&&{i18n:m[12].i18n},h&2&&Le(m[1])]):{};e.$set($);let d=t;t=c(m),t===d?r[t].p(m,h):(Ne(),v(r[d],1,1,()=>{r[d]=null}),Be(),s=r[t],s?s.p(m,h):(s=r[t]=f[t](m),s.c()),k(s,1),s.m(a.parentNode,a))},i(m){o||(k(e.$$.fragment,m),k(s),o=!0)},o(m){v(e.$$.fragment,m),v(s),o=!1},d(m){m&&(x(n),x(a)),U(e,m),r[t].d(m)}}}function Ot(l){let e,n,t,s;const a=[Mt,Et],o=[];function _(i,f){return i[16]?1:0}return e=_(l),n=o[e]=a[e](l),{c(){n.c(),t=Ae()},m(i,f){o[e].m(i,f),ee(i,t,f),s=!0},p(i,[f]){let r=e;e=_(i),e===r?o[e].p(i,f):(Ne(),v(o[r],1,1,()=>{o[r]=null}),Be(),n=o[e],n?n.p(i,f):(n=o[e]=a[e](i),n.c()),k(n,1),n.m(t.parentNode,t))},i(i){s||(k(n),s=!0)},o(i){v(n),s=!1},d(i){i&&x(t),o[e].d(i)}}}function Tt(l,e,n){let{elem_id:t=""}=e,{elem_classes:s=[]}=e,{visible:a=!0}=e,{value:o=null}=e,{root:_}=e,{clear_color:i}=e,{loading_status:f}=e,{label:r}=e,{show_label:c}=e,{container:m=!0}=e,{scale:h=null}=e,{min_width:$=void 0}=e,{gradio:d}=e,{height:p=void 0}=e,{zoom_speed:z=1}=e,{camera_position:E=[null,null,null]}=e,{interactive:A}=e,j=!1;const W=()=>d.dispatch("clear_status",f),g=()=>d.dispatch("clear_status",f),_e=({detail:u})=>n(0,o=u),N=({detail:u})=>n(17,j=u),F=({detail:u})=>d.dispatch("change",u),ue=()=>{n(0,o=null),d.dispatch("clear")},fe=({detail:u})=>{n(0,o=u),d.dispatch("upload")},ce=({detail:u})=>{n(1,f=f||{}),n(1,f.status="error",f),d.dispatch("error",u)};return l.$$set=u=>{"elem_id"in u&&n(2,t=u.elem_id),"elem_classes"in u&&n(3,s=u.elem_classes),"visible"in u&&n(4,a=u.visible),"value"in u&&n(0,o=u.value),"root"in u&&n(5,_=u.root),"clear_color"in u&&n(6,i=u.clear_color),"loading_status"in u&&n(1,f=u.loading_status),"label"in u&&n(7,r=u.label),"show_label"in u&&n(8,c=u.show_label),"container"in u&&n(9,m=u.container),"scale"in u&&n(10,h=u.scale),"min_width"in u&&n(11,$=u.min_width),"gradio"in u&&n(12,d=u.gradio),"height"in u&&n(13,p=u.height),"zoom_speed"in u&&n(14,z=u.zoom_speed),"camera_position"in u&&n(15,E=u.camera_position),"interactive"in u&&n(16,A=u.interactive)},[o,f,t,s,a,_,i,r,c,m,h,$,d,p,z,E,A,j,W,g,_e,N,F,ue,fe,ce]}class yt extends Ct{constructor(e){super(),It(this,e,Tt,Ot,St,{elem_id:2,elem_classes:3,visible:4,value:0,root:5,clear_color:6,loading_status:1,label:7,show_label:8,container:9,scale:10,min_width:11,gradio:12,height:13,zoom_speed:14,camera_position:15,interactive:16})}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),b()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),b()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),b()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),b()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),b()}get clear_color(){return this.$$.ctx[6]}set clear_color(e){this.$$set({clear_color:e}),b()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),b()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),b()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),b()}get container(){return this.$$.ctx[9]}set container(e){this.$$set({container:e}),b()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),b()}get min_width(){return this.$$.ctx[11]}set min_width(e){this.$$set({min_width:e}),b()}get gradio(){return this.$$.ctx[12]}set gradio(e){this.$$set({gradio:e}),b()}get height(){return this.$$.ctx[13]}set height(e){this.$$set({height:e}),b()}get zoom_speed(){return this.$$.ctx[14]}set zoom_speed(e){this.$$set({zoom_speed:e}),b()}get camera_position(){return this.$$.ctx[15]}set camera_position(e){this.$$set({camera_position:e}),b()}get interactive(){return this.$$.ctx[16]}set interactive(e){this.$$set({interactive:e}),b()}}export{tn as BaseExample,xe as BaseModel3D,Dt as BaseModel3DUpload,yt as default};
//# sourceMappingURL=Index-BarCNVoD.js.map
