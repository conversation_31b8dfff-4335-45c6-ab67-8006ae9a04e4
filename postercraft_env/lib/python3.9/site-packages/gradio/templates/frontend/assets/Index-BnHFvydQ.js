import{B as p}from"./Button-uOcat6Z0.js";import{S as x}from"./Index-D21IHG0c.js";import{U as $}from"./UploadText-Dnj0K08n.js";import ee from"./Gallery-CFGnKqpc.js";import{B as te}from"./FileUpload-BWzg-N5i.js";/* empty css                                              */import"./index-D5ROCp7B.js";import"./svelte/svelte.js";import"./Upload-Cp8Go_XF.js";import"./BlockLabel-BXXlQleC.js";import"./Empty-CLiqUlWX.js";import"./ShareButton-OlciWAJu.js";import"./Blocks-Dw_9NR1K.js";import"./Download-DVtk-Jv3.js";import"./Minimize-X5PPawdt.js";import"./Image-Bsh8Umrh.js";/* empty css                                                   */import"./ModifyUpload-By7WzcPJ.js";import"./Undo-CpmTQw3B.js";import"./DownloadLink-BgAM71ly.js";import"./file-url-BIHPd7vS.js";import"./Image-DG8jX6JY.js";/* empty css                                                   */import"./File-BQ_9P3Ye.js";import"./Upload-46YxStuW.js";const{SvelteComponent:le,add_flush_callback:N,assign:ie,bind:T,binding_callbacks:H,check_outros:ne,create_component:j,destroy_component:B,detach:J,empty:se,flush:f,get_spread_object:oe,get_spread_update:re,group_outros:ae,init:_e,insert:K,mount_component:z,safe_not_equal:ue,space:fe,transition_in:d,transition_out:b}=window.__gradio__svelte__internal,{createEventDispatcher:he}=window.__gradio__svelte__internal;function ce(i){let e,s,t,n;function r(o){i[27](o)}function g(o){i[28](o)}let m={label:i[4],show_label:i[3],columns:i[12],rows:i[13],height:i[14],preview:i[15],object_fit:i[17],interactive:i[19],allow_preview:i[16],show_share_button:i[18],show_download_button:i[20],i18n:i[21].i18n,_fetch:i[21].client.fetch,show_fullscreen_button:i[22]};return i[1]!==void 0&&(m.selected_index=i[1]),i[0]!==void 0&&(m.value=i[0]),e=new ee({props:m}),H.push(()=>T(e,"selected_index",r)),H.push(()=>T(e,"value",g)),e.$on("change",i[29]),e.$on("select",i[30]),e.$on("share",i[31]),e.$on("error",i[32]),{c(){j(e.$$.fragment)},m(o,u){z(e,o,u),n=!0},p(o,u){const _={};u[0]&16&&(_.label=o[4]),u[0]&8&&(_.show_label=o[3]),u[0]&4096&&(_.columns=o[12]),u[0]&8192&&(_.rows=o[13]),u[0]&16384&&(_.height=o[14]),u[0]&32768&&(_.preview=o[15]),u[0]&131072&&(_.object_fit=o[17]),u[0]&524288&&(_.interactive=o[19]),u[0]&65536&&(_.allow_preview=o[16]),u[0]&262144&&(_.show_share_button=o[18]),u[0]&1048576&&(_.show_download_button=o[20]),u[0]&2097152&&(_.i18n=o[21].i18n),u[0]&2097152&&(_._fetch=o[21].client.fetch),u[0]&4194304&&(_.show_fullscreen_button=o[22]),!s&&u[0]&2&&(s=!0,_.selected_index=o[1],N(()=>s=!1)),!t&&u[0]&1&&(t=!0,_.value=o[0],N(()=>t=!1)),e.$set(_)},i(o){n||(d(e.$$.fragment,o),n=!0)},o(o){b(e.$$.fragment,o),n=!1},d(o){B(e,o)}}}function me(i){let e,s;return e=new te({props:{value:null,root:i[5],label:i[4],max_file_size:i[21].max_file_size,file_count:"multiple",file_types:["image"],i18n:i[21].i18n,upload:i[21].client.upload,stream_handler:i[21].client.stream,$$slots:{default:[ge]},$$scope:{ctx:i}}}),e.$on("upload",i[25]),e.$on("error",i[26]),{c(){j(e.$$.fragment)},m(t,n){z(e,t,n),s=!0},p(t,n){const r={};n[0]&32&&(r.root=t[5]),n[0]&16&&(r.label=t[4]),n[0]&2097152&&(r.max_file_size=t[21].max_file_size),n[0]&2097152&&(r.i18n=t[21].i18n),n[0]&2097152&&(r.upload=t[21].client.upload),n[0]&2097152&&(r.stream_handler=t[21].client.stream),n[0]&2097152|n[1]&8&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){b(e.$$.fragment,t),s=!1},d(t){B(e,t)}}}function ge(i){let e,s;return e=new $({props:{i18n:i[21].i18n,type:"gallery"}}),{c(){j(e.$$.fragment)},m(t,n){z(e,t,n),s=!0},p(t,n){const r={};n[0]&2097152&&(r.i18n=t[21].i18n),e.$set(r)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){b(e.$$.fragment,t),s=!1},d(t){B(e,t)}}}function we(i){let e,s,t,n,r,g;const m=[{autoscroll:i[21].autoscroll},{i18n:i[21].i18n},i[2]];let o={};for(let a=0;a<m.length;a+=1)o=ie(o,m[a]);e=new x({props:o}),e.$on("clear_status",i[24]);const u=[me,ce],_=[];function c(a,h){return a[19]&&a[23]?0:1}return t=c(i),n=_[t]=u[t](i),{c(){j(e.$$.fragment),s=fe(),n.c(),r=se()},m(a,h){z(e,a,h),K(a,s,h),_[t].m(a,h),K(a,r,h),g=!0},p(a,h){const A=h[0]&2097156?re(m,[h[0]&2097152&&{autoscroll:a[21].autoscroll},h[0]&2097152&&{i18n:a[21].i18n},h[0]&4&&oe(a[2])]):{};e.$set(A);let v=t;t=c(a),t===v?_[t].p(a,h):(ae(),b(_[v],1,1,()=>{_[v]=null}),ne(),n=_[t],n?n.p(a,h):(n=_[t]=u[t](a),n.c()),d(n,1),n.m(r.parentNode,r))},i(a){g||(d(e.$$.fragment,a),d(n),g=!0)},o(a){b(e.$$.fragment,a),b(n),g=!1},d(a){a&&(J(s),J(r)),B(e,a),_[t].d(a)}}}function de(i){let e,s;return e=new p({props:{visible:i[8],variant:"solid",padding:!1,elem_id:i[6],elem_classes:i[7],container:i[9],scale:i[10],min_width:i[11],allow_overflow:!1,height:typeof i[14]=="number"?i[14]:void 0,$$slots:{default:[we]},$$scope:{ctx:i}}}),{c(){j(e.$$.fragment)},m(t,n){z(e,t,n),s=!0},p(t,n){const r={};n[0]&256&&(r.visible=t[8]),n[0]&64&&(r.elem_id=t[6]),n[0]&128&&(r.elem_classes=t[7]),n[0]&512&&(r.container=t[9]),n[0]&1024&&(r.scale=t[10]),n[0]&2048&&(r.min_width=t[11]),n[0]&16384&&(r.height=typeof t[14]=="number"?t[14]:void 0),n[0]&16773183|n[1]&8&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){b(e.$$.fragment,t),s=!1},d(t){B(e,t)}}}function be(i,e,s){let t,{loading_status:n}=e,{show_label:r}=e,{label:g}=e,{root:m}=e,{elem_id:o=""}=e,{elem_classes:u=[]}=e,{visible:_=!0}=e,{value:c=null}=e,{container:a=!0}=e,{scale:h=null}=e,{min_width:A=void 0}=e,{columns:v=[2]}=e,{rows:S=void 0}=e,{height:U="auto"}=e,{preview:G}=e,{allow_preview:q=!0}=e,{selected_index:k=null}=e,{object_fit:C="cover"}=e,{show_share_button:D=!1}=e,{interactive:E}=e,{show_download_button:F=!1}=e,{gradio:w}=e,{show_fullscreen_button:I=!0}=e;const L=he(),M=()=>w.dispatch("clear_status",n),O=l=>{const Z=Array.isArray(l.detail)?l.detail:[l.detail];s(0,c=Z.map(y=>({image:y,caption:null}))),w.dispatch("upload",c)},P=({detail:l})=>{s(2,n=n||{}),s(2,n.status="error",n),w.dispatch("error",l)};function Q(l){k=l,s(1,k)}function R(l){c=l,s(0,c)}const V=()=>w.dispatch("change",c),W=l=>w.dispatch("select",l.detail),X=l=>w.dispatch("share",l.detail),Y=l=>w.dispatch("error",l.detail);return i.$$set=l=>{"loading_status"in l&&s(2,n=l.loading_status),"show_label"in l&&s(3,r=l.show_label),"label"in l&&s(4,g=l.label),"root"in l&&s(5,m=l.root),"elem_id"in l&&s(6,o=l.elem_id),"elem_classes"in l&&s(7,u=l.elem_classes),"visible"in l&&s(8,_=l.visible),"value"in l&&s(0,c=l.value),"container"in l&&s(9,a=l.container),"scale"in l&&s(10,h=l.scale),"min_width"in l&&s(11,A=l.min_width),"columns"in l&&s(12,v=l.columns),"rows"in l&&s(13,S=l.rows),"height"in l&&s(14,U=l.height),"preview"in l&&s(15,G=l.preview),"allow_preview"in l&&s(16,q=l.allow_preview),"selected_index"in l&&s(1,k=l.selected_index),"object_fit"in l&&s(17,C=l.object_fit),"show_share_button"in l&&s(18,D=l.show_share_button),"interactive"in l&&s(19,E=l.interactive),"show_download_button"in l&&s(20,F=l.show_download_button),"gradio"in l&&s(21,w=l.gradio),"show_fullscreen_button"in l&&s(22,I=l.show_fullscreen_button)},i.$$.update=()=>{i.$$.dirty[0]&1&&s(23,t=Array.isArray(c)?c.length===0:!c),i.$$.dirty[0]&2&&L("prop_change",{selected_index:k})},[c,k,n,r,g,m,o,u,_,a,h,A,v,S,U,G,q,C,D,E,F,w,I,t,M,O,P,Q,R,V,W,X,Y]}class Re extends le{constructor(e){super(),_e(this,e,be,de,ue,{loading_status:2,show_label:3,label:4,root:5,elem_id:6,elem_classes:7,visible:8,value:0,container:9,scale:10,min_width:11,columns:12,rows:13,height:14,preview:15,allow_preview:16,selected_index:1,object_fit:17,show_share_button:18,interactive:19,show_download_button:20,gradio:21,show_fullscreen_button:22},null,[-1,-1])}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),f()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),f()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),f()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),f()}get elem_id(){return this.$$.ctx[6]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[7]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[8]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get container(){return this.$$.ctx[9]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[11]}set min_width(e){this.$$set({min_width:e}),f()}get columns(){return this.$$.ctx[12]}set columns(e){this.$$set({columns:e}),f()}get rows(){return this.$$.ctx[13]}set rows(e){this.$$set({rows:e}),f()}get height(){return this.$$.ctx[14]}set height(e){this.$$set({height:e}),f()}get preview(){return this.$$.ctx[15]}set preview(e){this.$$set({preview:e}),f()}get allow_preview(){return this.$$.ctx[16]}set allow_preview(e){this.$$set({allow_preview:e}),f()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),f()}get object_fit(){return this.$$.ctx[17]}set object_fit(e){this.$$set({object_fit:e}),f()}get show_share_button(){return this.$$.ctx[18]}set show_share_button(e){this.$$set({show_share_button:e}),f()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),f()}get show_download_button(){return this.$$.ctx[20]}set show_download_button(e){this.$$set({show_download_button:e}),f()}get gradio(){return this.$$.ctx[21]}set gradio(e){this.$$set({gradio:e}),f()}get show_fullscreen_button(){return this.$$.ctx[22]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),f()}}export{ee as BaseGallery,Re as default};
//# sourceMappingURL=Index-BnHFvydQ.js.map
