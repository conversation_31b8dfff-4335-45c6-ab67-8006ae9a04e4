import{I as _e,S as fe}from"./ImageUploader-sLREcIL3.js";import{W as Xe}from"./ImageUploader-sLREcIL3.js";import{B as G}from"./Button-8nmImwVJ.js";import{S as H}from"./Index-WGC0_FkS.js";import{E as ce}from"./Empty-Vuj7-ssy.js";import{I as me}from"./Image-Bsh8Umrh.js";import{U as K}from"./UploadText-DlCTYTPP.js";import{I as Ze}from"./Image-BZaARumT.js";import{default as xe}from"./Example-BI9uF_3D.js";import"./Blocks-aR9ucLZz.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";import"./BlockLabel-CJsotHlk.js";import"./ShareButton-Ds9bG3Tz.js";import"./DownloadLink-DYBmO3sz.js";import"./file-url-Bf0nK4ai.js";import"./SelectSource-ghC4bkgc.js";import"./Upload-Cp8Go_XF.js";import"./DropdownArrow-AhwBZaFV.js";import"./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js";/* empty css                                              */const{SvelteComponent:he,add_flush_callback:A,assign:L,bind:D,binding_callbacks:F,check_outros:M,create_component:k,destroy_component:v,detach:B,empty:P,flush:c,get_spread_object:Q,get_spread_update:R,group_outros:V,init:ge,insert:z,mount_component:p,safe_not_equal:de,space:X,transition_in:b,transition_out:w}=window.__gradio__svelte__internal;function be(n){let e,s;return e=new G({props:{visible:n[4],variant:n[0]===null?"dashed":"solid",border_mode:n[22]?"focus":"base",padding:!1,elem_id:n[2],elem_classes:n[3],height:n[9]||void 0,width:n[10],allow_overflow:!1,container:n[12],scale:n[13],min_width:n[14],$$slots:{default:[Se]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[0]&16&&(r.visible=t[4]),i[0]&1&&(r.variant=t[0]===null?"dashed":"solid"),i[0]&4194304&&(r.border_mode=t[22]?"focus":"base"),i[0]&4&&(r.elem_id=t[2]),i[0]&8&&(r.elem_classes=t[3]),i[0]&512&&(r.height=t[9]||void 0),i[0]&1024&&(r.width=t[10]),i[0]&4096&&(r.container=t[12]),i[0]&8192&&(r.scale=t[13]),i[0]&16384&&(r.min_width=t[14]),i[0]&16583011|i[1]&256&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function we(n){let e,s;return e=new G({props:{visible:n[4],variant:"solid",border_mode:n[22]?"focus":"base",padding:!1,elem_id:n[2],elem_classes:n[3],height:n[9]||void 0,width:n[10],allow_overflow:!1,container:n[12],scale:n[13],min_width:n[14],$$slots:{default:[Be]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[0]&16&&(r.visible=t[4]),i[0]&4194304&&(r.border_mode=t[22]?"focus":"base"),i[0]&4&&(r.elem_id=t[2]),i[0]&8&&(r.elem_classes=t[3]),i[0]&512&&(r.height=t[9]||void 0),i[0]&1024&&(r.width=t[10]),i[0]&4096&&(r.container=t[12]),i[0]&8192&&(r.scale=t[13]),i[0]&16384&&(r.min_width=t[14]),i[0]&2132195|i[1]&256&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function ke(n){let e,s;return e=new ce({props:{unpadded_box:!0,size:"large",$$slots:{default:[$e]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[1]&256&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function ve(n){let e,s;return e=new K({props:{i18n:n[21].i18n,type:"clipboard",mode:"short"}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[0]&2097152&&(r.i18n=t[21].i18n),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function pe(n){let e,s;return e=new K({props:{i18n:n[21].i18n,type:"image"}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[0]&2097152&&(r.i18n=t[21].i18n),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function $e(n){let e,s;return e=new me({}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function Ie(n){let e,s,t,i;const r=[pe,ve,ke],_=[];function u(a,h){return a[23]==="upload"||!a[23]?0:a[23]==="clipboard"?1:2}return e=u(n),s=_[e]=r[e](n),{c(){s.c(),t=P()},m(a,h){_[e].m(a,h),z(a,t,h),i=!0},p(a,h){let m=e;e=u(a),e===m?_[e].p(a,h):(V(),w(_[m],1,1,()=>{_[m]=null}),M(),s=_[e],s?s.p(a,h):(s=_[e]=r[e](a),s.c()),b(s,1),s.m(t.parentNode,t))},i(a){i||(b(s),i=!0)},o(a){w(s),i=!1},d(a){a&&B(t),_[e].d(a)}}}function Se(n){let e,s,t,i,r,_;const u=[{autoscroll:n[21].autoscroll},{i18n:n[21].i18n},n[1]];let a={};for(let o=0;o<u.length;o+=1)a=L(a,u[o]);e=new H({props:a}),e.$on("clear_status",n[28]);function h(o){n[29](o)}function m(o){n[30](o)}let I={selectable:n[11],root:n[8],sources:n[16],label:n[5],show_label:n[6],pending:n[19],streaming:n[18],mirror_webcam:n[20],max_file_size:n[21].max_file_size,i18n:n[21].i18n,upload:n[21].client.upload,stream_handler:n[21].client.stream,$$slots:{default:[Ie]},$$scope:{ctx:n}};return n[23]!==void 0&&(I.active_source=n[23]),n[0]!==void 0&&(I.value=n[0]),t=new _e({props:I}),F.push(()=>D(t,"active_source",h)),F.push(()=>D(t,"value",m)),t.$on("edit",n[31]),t.$on("clear",n[32]),t.$on("stream",n[33]),t.$on("drag",n[34]),t.$on("upload",n[35]),t.$on("select",n[36]),t.$on("share",n[37]),t.$on("error",n[38]),{c(){k(e.$$.fragment),s=X(),k(t.$$.fragment)},m(o,f){p(e,o,f),z(o,s,f),p(t,o,f),_=!0},p(o,f){const S=f[0]&2097154?R(u,[f[0]&2097152&&{autoscroll:o[21].autoscroll},f[0]&2097152&&{i18n:o[21].i18n},f[0]&2&&Q(o[1])]):{};e.$set(S);const g={};f[0]&2048&&(g.selectable=o[11]),f[0]&256&&(g.root=o[8]),f[0]&65536&&(g.sources=o[16]),f[0]&32&&(g.label=o[5]),f[0]&64&&(g.show_label=o[6]),f[0]&524288&&(g.pending=o[19]),f[0]&262144&&(g.streaming=o[18]),f[0]&1048576&&(g.mirror_webcam=o[20]),f[0]&2097152&&(g.max_file_size=o[21].max_file_size),f[0]&2097152&&(g.i18n=o[21].i18n),f[0]&2097152&&(g.upload=o[21].client.upload),f[0]&2097152&&(g.stream_handler=o[21].client.stream),f[0]&10485760|f[1]&256&&(g.$$scope={dirty:f,ctx:o}),!i&&f[0]&8388608&&(i=!0,g.active_source=o[23],A(()=>i=!1)),!r&&f[0]&1&&(r=!0,g.value=o[0],A(()=>r=!1)),t.$set(g)},i(o){_||(b(e.$$.fragment,o),b(t.$$.fragment,o),_=!0)},o(o){w(e.$$.fragment,o),w(t.$$.fragment,o),_=!1},d(o){o&&B(s),v(e,o),v(t,o)}}}function Be(n){let e,s,t,i;const r=[{autoscroll:n[21].autoscroll},{i18n:n[21].i18n},n[1]];let _={};for(let u=0;u<r.length;u+=1)_=L(_,r[u]);return e=new H({props:_}),t=new fe({props:{value:n[0],label:n[5],show_label:n[6],show_download_button:n[7],selectable:n[11],show_share_button:n[15],i18n:n[21].i18n}}),t.$on("select",n[25]),t.$on("share",n[26]),t.$on("error",n[27]),{c(){k(e.$$.fragment),s=X(),k(t.$$.fragment)},m(u,a){p(e,u,a),z(u,s,a),p(t,u,a),i=!0},p(u,a){const h=a[0]&2097154?R(r,[a[0]&2097152&&{autoscroll:u[21].autoscroll},a[0]&2097152&&{i18n:u[21].i18n},a[0]&2&&Q(u[1])]):{};e.$set(h);const m={};a[0]&1&&(m.value=u[0]),a[0]&32&&(m.label=u[5]),a[0]&64&&(m.show_label=u[6]),a[0]&128&&(m.show_download_button=u[7]),a[0]&2048&&(m.selectable=u[11]),a[0]&32768&&(m.show_share_button=u[15]),a[0]&2097152&&(m.i18n=u[21].i18n),t.$set(m)},i(u){i||(b(e.$$.fragment,u),b(t.$$.fragment,u),i=!0)},o(u){w(e.$$.fragment,u),w(t.$$.fragment,u),i=!1},d(u){u&&B(s),v(e,u),v(t,u)}}}function ze(n){let e,s,t,i;const r=[we,be],_=[];function u(a,h){return a[17]?1:0}return e=u(n),s=_[e]=r[e](n),{c(){s.c(),t=P()},m(a,h){_[e].m(a,h),z(a,t,h),i=!0},p(a,h){let m=e;e=u(a),e===m?_[e].p(a,h):(V(),w(_[m],1,1,()=>{_[m]=null}),M(),s=_[e],s?s.p(a,h):(s=_[e]=r[e](a),s.c()),b(s,1),s.m(t.parentNode,t))},i(a){i||(b(s),i=!0)},o(a){w(s),i=!1},d(a){a&&B(t),_[e].d(a)}}}function Ne(n,e,s){let{elem_id:t=""}=e,{elem_classes:i=[]}=e,{visible:r=!0}=e,{value:_=null}=e,u=null,{label:a}=e,{show_label:h}=e,{show_download_button:m}=e,{root:I}=e,{height:o}=e,{width:f}=e,{_selectable:S=!1}=e,{container:g=!0}=e,{scale:U=null}=e,{min_width:E=void 0}=e,{loading_status:$}=e,{show_share_button:J=!1}=e,{sources:O=["upload","clipboard","webcam"]}=e,{interactive:W}=e,{streaming:j}=e,{pending:q}=e,{mirror_webcam:C}=e,{gradio:d}=e,T,N=null;const Y=({detail:l})=>d.dispatch("select",l),Z=({detail:l})=>d.dispatch("share",l),y=({detail:l})=>d.dispatch("error",l),x=()=>d.dispatch("clear_status",$);function ee(l){N=l,s(23,N)}function te(l){_=l,s(0,_)}const se=()=>d.dispatch("edit"),ne=()=>{d.dispatch("clear")},ie=()=>d.dispatch("stream"),le=({detail:l})=>s(22,T=l),ae=()=>d.dispatch("upload"),re=({detail:l})=>d.dispatch("select",l),oe=({detail:l})=>d.dispatch("share",l),ue=({detail:l})=>{s(1,$=$||{}),s(1,$.status="error",$),d.dispatch("error",l)};return n.$$set=l=>{"elem_id"in l&&s(2,t=l.elem_id),"elem_classes"in l&&s(3,i=l.elem_classes),"visible"in l&&s(4,r=l.visible),"value"in l&&s(0,_=l.value),"label"in l&&s(5,a=l.label),"show_label"in l&&s(6,h=l.show_label),"show_download_button"in l&&s(7,m=l.show_download_button),"root"in l&&s(8,I=l.root),"height"in l&&s(9,o=l.height),"width"in l&&s(10,f=l.width),"_selectable"in l&&s(11,S=l._selectable),"container"in l&&s(12,g=l.container),"scale"in l&&s(13,U=l.scale),"min_width"in l&&s(14,E=l.min_width),"loading_status"in l&&s(1,$=l.loading_status),"show_share_button"in l&&s(15,J=l.show_share_button),"sources"in l&&s(16,O=l.sources),"interactive"in l&&s(17,W=l.interactive),"streaming"in l&&s(18,j=l.streaming),"pending"in l&&s(19,q=l.pending),"mirror_webcam"in l&&s(20,C=l.mirror_webcam),"gradio"in l&&s(21,d=l.gradio)},n.$$.update=()=>{n.$$.dirty[0]&18874369&&JSON.stringify(_)!==JSON.stringify(u)&&(s(24,u=_),d.dispatch("change"))},[_,$,t,i,r,a,h,m,I,o,f,S,g,U,E,J,O,W,j,q,C,d,T,N,u,Y,Z,y,x,ee,te,se,ne,ie,le,ae,re,oe,ue]}class Qe extends he{constructor(e){super(),ge(this,e,Ne,ze,de,{elem_id:2,elem_classes:3,visible:4,value:0,label:5,show_label:6,show_download_button:7,root:8,height:9,width:10,_selectable:11,container:12,scale:13,min_width:14,loading_status:1,show_share_button:15,sources:16,interactive:17,streaming:18,pending:19,mirror_webcam:20,gradio:21},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),c()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),c()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),c()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),c()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),c()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),c()}get show_download_button(){return this.$$.ctx[7]}set show_download_button(e){this.$$set({show_download_button:e}),c()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),c()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),c()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),c()}get _selectable(){return this.$$.ctx[11]}set _selectable(e){this.$$set({_selectable:e}),c()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),c()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),c()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),c()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),c()}get show_share_button(){return this.$$.ctx[15]}set show_share_button(e){this.$$set({show_share_button:e}),c()}get sources(){return this.$$.ctx[16]}set sources(e){this.$$set({sources:e}),c()}get interactive(){return this.$$.ctx[17]}set interactive(e){this.$$set({interactive:e}),c()}get streaming(){return this.$$.ctx[18]}set streaming(e){this.$$set({streaming:e}),c()}get pending(){return this.$$.ctx[19]}set pending(e){this.$$set({pending:e}),c()}get mirror_webcam(){return this.$$.ctx[20]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),c()}get gradio(){return this.$$.ctx[21]}set gradio(e){this.$$set({gradio:e}),c()}}export{xe as BaseExample,Ze as BaseImage,_e as BaseImageUploader,fe as BaseStaticImage,Xe as Webcam,Qe as default};
//# sourceMappingURL=Index-C5jlwfFm.js.map
