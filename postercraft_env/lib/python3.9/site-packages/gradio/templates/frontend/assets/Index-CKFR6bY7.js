import{B as je}from"./Button-8nmImwVJ.js";import"./Index-WGC0_FkS.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";const{SvelteComponent:Ae,append:B,assign:Q,attr:p,check_outros:P,construct_svelte_component:T,create_component:D,destroy_component:F,destroy_each:G,detach:w,element:N,empty:K,ensure_array_like:C,flush:z,get_spread_object:U,get_spread_update:V,group_outros:E,init:Ie,insert:q,listen:R,mount_component:J,noop:Re,null_to_empty:le,run_all:ve,safe_not_equal:Se,set_data:x,set_style:te,space:A,svg_element:ne,text:W,toggle_class:se,transition_in:d,transition_out:k}=window.__gradio__svelte__internal;function ie(i,e,l){const s=i.slice();return s[35]=e[l],s}function oe(i,e,l){const s=i.slice();return s[38]=e[l],s[40]=l,s}function re(i,e,l){const s=i.slice();s[0]=e[l].value,s[42]=e[l].component,s[45]=l;const t=s[1][s[45]];return s[43]=t,s}function fe(i,e,l){const s=i.slice();return s[46]=e[l],s}function ce(i,e,l){const s=i.slice();return s[38]=e[l],s[40]=l,s}function Ye(i){let e,l,s,t,o,f,n,r=C(i[5]),c=[];for(let _=0;_<r.length;_+=1)c[_]=_e(fe(i,r,_));let h=C(i[18]),u=[];for(let _=0;_<h.length;_+=1)u[_]=me(oe(i,h,_));const v=_=>k(u[_],1,1,()=>{u[_]=null});return{c(){e=N("div"),l=N("table"),s=N("thead"),t=N("tr");for(let _=0;_<c.length;_+=1)c[_].c();o=A(),f=N("tbody");for(let _=0;_<u.length;_+=1)u[_].c();p(t,"class","tr-head svelte-p5q82i"),p(l,"tabindex","0"),p(l,"role","grid"),p(l,"class","svelte-p5q82i"),p(e,"class","table-wrap svelte-p5q82i")},m(_,b){q(_,e,b),B(e,l),B(l,s),B(s,t);for(let a=0;a<c.length;a+=1)c[a]&&c[a].m(t,null);B(l,o),B(l,f);for(let a=0;a<u.length;a+=1)u[a]&&u[a].m(f,null);n=!0},p(_,b){if(b[0]&32){r=C(_[5]);let a;for(a=0;a<r.length;a+=1){const g=fe(_,r,a);c[a]?c[a].p(g,b):(c[a]=_e(g),c[a].c(),c[a].m(t,null))}for(;a<c.length;a+=1)c[a].d(1);c.length=r.length}if(b[0]&7746063){h=C(_[18]);let a;for(a=0;a<h.length;a+=1){const g=oe(_,h,a);u[a]?(u[a].p(g,b),d(u[a],1)):(u[a]=me(g),u[a].c(),d(u[a],1),u[a].m(f,null))}for(E(),a=h.length;a<u.length;a+=1)v(a);P()}},i(_){if(!n){for(let b=0;b<h.length;b+=1)d(u[b]);n=!0}},o(_){u=u.filter(Boolean);for(let b=0;b<u.length;b+=1)k(u[b]);n=!1},d(_){_&&w(e),G(c,_),G(u,_)}}}function De(i){let e,l,s=C(i[15]),t=[];for(let f=0;f<s.length;f+=1)t[f]=pe(ce(i,s,f));const o=f=>k(t[f],1,1,()=>{t[f]=null});return{c(){e=N("div");for(let f=0;f<t.length;f+=1)t[f].c();p(e,"class","gallery svelte-p5q82i")},m(f,n){q(f,e,n);for(let r=0;r<t.length;r+=1)t[r]&&t[r].m(e,null);l=!0},p(f,n){if(n[0]&7778831){s=C(f[15]);let r;for(r=0;r<s.length;r+=1){const c=ce(f,s,r);t[r]?(t[r].p(c,n),d(t[r],1)):(t[r]=pe(c),t[r].c(),d(t[r],1),t[r].m(e,null))}for(E(),r=s.length;r<t.length;r+=1)o(r);P()}},i(f){if(!l){for(let n=0;n<s.length;n+=1)d(t[n]);l=!0}},o(f){t=t.filter(Boolean);for(let n=0;n<t.length;n+=1)k(t[n]);l=!1},d(f){f&&w(e),G(t,f)}}}function _e(i){let e,l=i[46]+"",s,t;return{c(){e=N("th"),s=W(l),t=A(),p(e,"class","svelte-p5q82i")},m(o,f){q(o,e,f),B(e,s),B(e,t)},p(o,f){f[0]&32&&l!==(l=o[46]+"")&&x(s,l)},d(o){o&&w(e)}}}function ue(i){let e,l,s,t;const o=[i[2][i[45]],{value:i[0]},{samples_dir:i[20]},{type:"table"},{selected:i[17]===i[40]},{index:i[40]}];var f=i[42];function n(r,c){let h={};for(let u=0;u<o.length;u+=1)h=Q(h,o[u]);return c!==void 0&&c[0]&1441796&&(h=Q(h,V(o,[c[0]&4&&U(r[2][r[45]]),c[0]&262144&&{value:r[0]},c[0]&1048576&&{samples_dir:r[20]},o[3],c[0]&131072&&{selected:r[17]===r[40]},o[5]]))),{props:h}}return f&&(l=T(f,n(i))),{c(){e=N("td"),l&&D(l.$$.fragment),te(e,"max-width",i[43]==="textbox"?"35ch":"auto"),p(e,"class",s=le(i[43])+" svelte-p5q82i")},m(r,c){q(r,e,c),l&&J(l,e,null),t=!0},p(r,c){if(c[0]&262144&&f!==(f=r[42])){if(l){E();const h=l;k(h.$$.fragment,1,0,()=>{F(h,1)}),P()}f?(l=T(f,n(r,c)),D(l.$$.fragment),d(l.$$.fragment,1),J(l,e,null)):l=null}else if(f){const h=c[0]&1441796?V(o,[c[0]&4&&U(r[2][r[45]]),c[0]&262144&&{value:r[0]},c[0]&1048576&&{samples_dir:r[20]},o[3],c[0]&131072&&{selected:r[17]===r[40]},o[5]]):{};l.$set(h)}(!t||c[0]&2)&&te(e,"max-width",r[43]==="textbox"?"35ch":"auto"),(!t||c[0]&2&&s!==(s=le(r[43])+" svelte-p5q82i"))&&p(e,"class",s)},i(r){t||(l&&d(l.$$.fragment,r),t=!0)},o(r){l&&k(l.$$.fragment,r),t=!1},d(r){r&&w(e),l&&F(l)}}}function ae(i){let e=i[43]!==void 0&&i[3].get(i[43])!==void 0,l,s,t=e&&ue(i);return{c(){t&&t.c(),l=K()},m(o,f){t&&t.m(o,f),q(o,l,f),s=!0},p(o,f){f[0]&10&&(e=o[43]!==void 0&&o[3].get(o[43])!==void 0),e?t?(t.p(o,f),f[0]&10&&d(t,1)):(t=ue(o),t.c(),d(t,1),t.m(l.parentNode,l)):t&&(E(),k(t,1,1,()=>{t=null}),P())},i(o){s||(d(t),s=!0)},o(o){k(t),s=!1},d(o){o&&w(l),t&&t.d(o)}}}function me(i){let e,l,s,t,o,f=C(i[38]),n=[];for(let u=0;u<f.length;u+=1)n[u]=ae(re(i,f,u));const r=u=>k(n[u],1,1,()=>{n[u]=null});function c(){return i[30](i[40])}function h(){return i[31](i[40])}return{c(){e=N("tr");for(let u=0;u<n.length;u+=1)n[u].c();l=A(),p(e,"class","tr-body svelte-p5q82i")},m(u,v){q(u,e,v);for(let _=0;_<n.length;_+=1)n[_]&&n[_].m(e,null);B(e,l),s=!0,t||(o=[R(e,"click",c),R(e,"mouseenter",h),R(e,"mouseleave",i[32])],t=!0)},p(u,v){if(i=u,v[0]&1441806){f=C(i[38]);let _;for(_=0;_<f.length;_+=1){const b=re(i,f,_);n[_]?(n[_].p(b,v),d(n[_],1)):(n[_]=ae(b),n[_].c(),d(n[_],1),n[_].m(e,l))}for(E(),_=f.length;_<n.length;_+=1)r(_);P()}},i(u){if(!s){for(let v=0;v<f.length;v+=1)d(n[v]);s=!0}},o(u){n=n.filter(Boolean);for(let v=0;v<n.length;v+=1)k(n[v]);s=!1},d(u){u&&w(e),G(n,u),t=!1,ve(o)}}}function he(i){let e,l=i[18].length&&i[3].get(i[1][0]),s,t,o,f,n=l&&ge(i);function r(){return i[27](i[40],i[38])}function c(){return i[28](i[40])}return{c(){e=N("button"),n&&n.c(),s=A(),p(e,"class","gallery-item svelte-p5q82i")},m(h,u){q(h,e,u),n&&n.m(e,null),B(e,s),t=!0,o||(f=[R(e,"click",r),R(e,"mouseenter",c),R(e,"mouseleave",i[29])],o=!0)},p(h,u){i=h,u[0]&262154&&(l=i[18].length&&i[3].get(i[1][0])),l?n?(n.p(i,u),u[0]&262154&&d(n,1)):(n=ge(i),n.c(),d(n,1),n.m(e,s)):n&&(E(),k(n,1,1,()=>{n=null}),P())},i(h){t||(d(n),t=!0)},o(h){k(n),t=!1},d(h){h&&w(e),n&&n.d(),o=!1,ve(f)}}}function ge(i){let e,l,s;const t=[i[2][0],{value:i[38][0]},{samples_dir:i[20]},{type:"gallery"},{selected:i[17]===i[40]},{index:i[40]}];var o=i[18][0][0].component;function f(n,r){let c={};for(let h=0;h<t.length;h+=1)c=Q(c,t[h]);return r!==void 0&&r[0]&1212420&&(c=Q(c,V(t,[r[0]&4&&U(n[2][0]),r[0]&32768&&{value:n[38][0]},r[0]&1048576&&{samples_dir:n[20]},t[3],r[0]&131072&&{selected:n[17]===n[40]},t[5]]))),{props:c}}return o&&(e=T(o,f(i))),{c(){e&&D(e.$$.fragment),l=K()},m(n,r){e&&J(e,n,r),q(n,l,r),s=!0},p(n,r){if(r[0]&262144&&o!==(o=n[18][0][0].component)){if(e){E();const c=e;k(c.$$.fragment,1,0,()=>{F(c,1)}),P()}o?(e=T(o,f(n,r)),D(e.$$.fragment),d(e.$$.fragment,1),J(e,l.parentNode,l)):e=null}else if(o){const c=r[0]&1212420?V(t,[r[0]&4&&U(n[2][0]),r[0]&32768&&{value:n[38][0]},r[0]&1048576&&{samples_dir:n[20]},t[3],r[0]&131072&&{selected:n[17]===n[40]},t[5]]):{};e.$set(c)}},i(n){s||(e&&d(e.$$.fragment,n),s=!0)},o(n){e&&k(e.$$.fragment,n),s=!1},d(n){n&&w(l),e&&F(e,n)}}}function pe(i){let e,l,s=i[38][0]&&he(i);return{c(){s&&s.c(),e=K()},m(t,o){s&&s.m(t,o),q(t,e,o),l=!0},p(t,o){t[38][0]?s?(s.p(t,o),o[0]&32768&&d(s,1)):(s=he(t),s.c(),d(s,1),s.m(e.parentNode,e)):s&&(E(),k(s,1,1,()=>{s=null}),P())},i(t){l||(d(s),l=!0)},o(t){k(s),l=!1},d(t){t&&w(e),s&&s.d(t)}}}function de(i){let e,l,s=C(i[16]),t=[];for(let o=0;o<s.length;o+=1)t[o]=be(ie(i,s,o));return{c(){e=N("div"),l=W(`Pages:
			`);for(let o=0;o<t.length;o+=1)t[o].c();p(e,"class","paginate svelte-p5q82i")},m(o,f){q(o,e,f),B(e,l);for(let n=0;n<t.length;n+=1)t[n]&&t[n].m(e,null)},p(o,f){if(f[0]&73728){s=C(o[16]);let n;for(n=0;n<s.length;n+=1){const r=ie(o,s,n);t[n]?t[n].p(r,f):(t[n]=be(r),t[n].c(),t[n].m(e,null))}for(;n<t.length;n+=1)t[n].d(1);t.length=s.length}},d(o){o&&w(e),G(t,o)}}}function Fe(i){let e,l=i[35]+1+"",s,t,o,f;function n(){return i[33](i[35])}return{c(){e=N("button"),s=W(l),t=A(),p(e,"class","svelte-p5q82i"),se(e,"current-page",i[13]===i[35])},m(r,c){q(r,e,c),B(e,s),B(e,t),o||(f=R(e,"click",n),o=!0)},p(r,c){i=r,c[0]&65536&&l!==(l=i[35]+1+"")&&x(s,l),c[0]&73728&&se(e,"current-page",i[13]===i[35])},d(r){r&&w(e),o=!1,f()}}}function Ge(i){let e;return{c(){e=N("div"),e.textContent="..."},m(l,s){q(l,e,s)},p:Re,d(l){l&&w(e)}}}function be(i){let e;function l(o,f){return o[35]===-1?Ge:Fe}let s=l(i),t=s(i);return{c(){t.c(),e=K()},m(o,f){t.m(o,f),q(o,e,f)},p(o,f){s===(s=l(o))&&t?t.p(o,f):(t.d(1),t=s(o),t&&(t.c(),t.m(e.parentNode,e)))},d(o){o&&w(e),t.d(o)}}}function Je(i){let e,l,s,t,o,f,n,r,c,h,u;const v=[De,Ye],_=[];function b(g,H){return g[19]?0:1}n=b(i),r=_[n]=v[n](i);let a=i[14]&&de(i);return{c(){e=N("div"),l=ne("svg"),s=ne("path"),t=A(),o=W(i[4]),f=A(),r.c(),c=A(),a&&a.c(),h=K(),p(s,"fill","currentColor"),p(s,"d","M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z"),p(l,"xmlns","http://www.w3.org/2000/svg"),p(l,"xmlns:xlink","http://www.w3.org/1999/xlink"),p(l,"aria-hidden","true"),p(l,"role","img"),p(l,"width","1em"),p(l,"height","1em"),p(l,"preserveAspectRatio","xMidYMid meet"),p(l,"viewBox","0 0 32 32"),p(l,"class","svelte-p5q82i"),p(e,"class","label svelte-p5q82i")},m(g,H){q(g,e,H),B(e,l),B(l,s),B(e,t),B(e,o),q(g,f,H),_[n].m(g,H),q(g,c,H),a&&a.m(g,H),q(g,h,H),u=!0},p(g,H){(!u||H[0]&16)&&x(o,g[4]);let S=n;n=b(g),n===S?_[n].p(g,H):(E(),k(_[S],1,1,()=>{_[S]=null}),P(),r=_[n],r?r.p(g,H):(r=_[n]=v[n](g),r.c()),d(r,1),r.m(c.parentNode,c)),g[14]?a?a.p(g,H):(a=de(g),a.c(),a.m(h.parentNode,h)):a&&(a.d(1),a=null)},i(g){u||(d(r),u=!0)},o(g){k(r),u=!1},d(g){g&&(w(e),w(f),w(c),w(h)),_[n].d(g),a&&a.d(g)}}}function Ke(i){let e,l;return e=new je({props:{visible:i[8],padding:!1,elem_id:i[6],elem_classes:i[7],scale:i[10],min_width:i[11],allow_overflow:!1,container:!1,$$slots:{default:[Je]},$$scope:{ctx:i}}}),{c(){D(e.$$.fragment)},m(s,t){J(e,s,t),l=!0},p(s,t){const o={};t[0]&256&&(o.visible=s[8]),t[0]&64&&(o.elem_id=s[6]),t[0]&128&&(o.elem_classes=s[7]),t[0]&1024&&(o.scale=s[10]),t[0]&2048&&(o.min_width=s[11]),t[0]&1045055|t[1]&262144&&(o.$$scope={dirty:t,ctx:s}),e.$set(o)},i(s){l||(d(e.$$.fragment,s),l=!0)},o(s){k(e.$$.fragment,s),l=!1},d(s){F(e,s)}}}function Le(i,e,l){let s,{components:t}=e,{component_props:o}=e,{component_map:f}=e,{label:n="Examples"}=e,{headers:r}=e,{samples:c}=e,{elem_id:h=""}=e,{elem_classes:u=[]}=e,{visible:v=!0}=e,{value:_=null}=e,{root:b}=e,{proxy_url:a}=e,{samples_per_page:g=10}=e,{scale:H=null}=e,{min_width:S=void 0}=e,{gradio:Y}=e,ke=a?`/proxy=${a}file=`:`${b}/file=`,I=0,X=c.length>g,L,O,j=[],Z=-1;function y(m){l(17,Z=m)}function $(){l(17,Z=-1)}let ee=[];async function we(m){l(18,ee=await Promise.all(m&&m.map(async M=>await Promise.all(M.map(async(Pe,Ee)=>({value:Pe,component:(await f.get(t[Ee]))?.default}))))))}const qe=(m,M)=>{l(0,_=m+I*g),Y.dispatch("click",_),Y.dispatch("select",{index:_,value:M})},ze=m=>y(m),Be=()=>$(),He=m=>{l(0,_=m+I*g),Y.dispatch("click",_)},Ne=m=>y(m),Me=()=>$(),Ce=m=>l(13,I=m);return i.$$set=m=>{"components"in m&&l(1,t=m.components),"component_props"in m&&l(2,o=m.component_props),"component_map"in m&&l(3,f=m.component_map),"label"in m&&l(4,n=m.label),"headers"in m&&l(5,r=m.headers),"samples"in m&&l(23,c=m.samples),"elem_id"in m&&l(6,h=m.elem_id),"elem_classes"in m&&l(7,u=m.elem_classes),"visible"in m&&l(8,v=m.visible),"value"in m&&l(0,_=m.value),"root"in m&&l(24,b=m.root),"proxy_url"in m&&l(25,a=m.proxy_url),"samples_per_page"in m&&l(9,g=m.samples_per_page),"scale"in m&&l(10,H=m.scale),"min_width"in m&&l(11,S=m.min_width),"gradio"in m&&l(12,Y=m.gradio)},i.$$.update=()=>{i.$$.dirty[0]&2&&l(19,s=t.length<2),i.$$.dirty[0]&75588096&&(l(14,X=c.length>g),X?(l(16,j=[]),l(15,L=c.slice(I*g,(I+1)*g)),l(26,O=Math.ceil(c.length/g)),[0,I,O-1].forEach(m=>{for(let M=m-2;M<=m+2;M++)M>=0&&M<O&&!j.includes(M)&&(j.length>0&&M-j[j.length-1]>1&&j.push(-1),j.push(M))})):l(15,L=c.slice())),i.$$.dirty[0]&32776&&we(L)},[_,t,o,f,n,r,h,u,v,g,H,S,Y,I,X,L,j,Z,ee,s,ke,y,$,c,b,a,O,qe,ze,Be,He,Ne,Me,Ce]}class Ve extends Ae{constructor(e){super(),Ie(this,e,Le,Ke,Se,{components:1,component_props:2,component_map:3,label:4,headers:5,samples:23,elem_id:6,elem_classes:7,visible:8,value:0,root:24,proxy_url:25,samples_per_page:9,scale:10,min_width:11,gradio:12},null,[-1,-1])}get components(){return this.$$.ctx[1]}set components(e){this.$$set({components:e}),z()}get component_props(){return this.$$.ctx[2]}set component_props(e){this.$$set({component_props:e}),z()}get component_map(){return this.$$.ctx[3]}set component_map(e){this.$$set({component_map:e}),z()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),z()}get headers(){return this.$$.ctx[5]}set headers(e){this.$$set({headers:e}),z()}get samples(){return this.$$.ctx[23]}set samples(e){this.$$set({samples:e}),z()}get elem_id(){return this.$$.ctx[6]}set elem_id(e){this.$$set({elem_id:e}),z()}get elem_classes(){return this.$$.ctx[7]}set elem_classes(e){this.$$set({elem_classes:e}),z()}get visible(){return this.$$.ctx[8]}set visible(e){this.$$set({visible:e}),z()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),z()}get root(){return this.$$.ctx[24]}set root(e){this.$$set({root:e}),z()}get proxy_url(){return this.$$.ctx[25]}set proxy_url(e){this.$$set({proxy_url:e}),z()}get samples_per_page(){return this.$$.ctx[9]}set samples_per_page(e){this.$$set({samples_per_page:e}),z()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),z()}get min_width(){return this.$$.ctx[11]}set min_width(e){this.$$set({min_width:e}),z()}get gradio(){return this.$$.ctx[12]}set gradio(e){this.$$set({gradio:e}),z()}}export{Ve as default};
//# sourceMappingURL=Index-CKFR6bY7.js.map
