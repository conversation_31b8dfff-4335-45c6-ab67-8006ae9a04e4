import{C as At,S as Nt}from"./Index-WGC0_FkS.js";import{u as Ee,c as Pt}from"./Blocks-aR9ucLZz.js";import{d as Vt}from"./index-CnqicUFC.js";import{S as Dt}from"./ShareButton-Ds9bG3Tz.js";import{r as It}from"./file-url-Bf0nK4ai.js";import{I as $t}from"./Image-BZaARumT.js";import{V as Ut}from"./Video-CZt5m8l5.js";import"./Index.svelte_svelte_type_style_lang-BgUoKvlh.js";import{M as Ct}from"./Example.svelte_svelte_type_style_lang-BBpfzd83.js";import"./index-COY1HN2y.js";import{C as Zt}from"./Check-Ck0iADAu.js";import{C as Ft}from"./Copy-ZPOKSMtK.js";import{B as Ot}from"./Button-8nmImwVJ.js";import{B as Rt}from"./BlockLabel-CJsotHlk.js";import"./prism-python-DQB1-hGx.js";import"./svelte/svelte.js";const{SvelteComponent:Yt,append:Le,attr:D,detach:Gt,init:Jt,insert:Kt,noop:pe,safe_not_equal:Qt,svg_element:ve}=window.__gradio__svelte__internal;function Wt(t){let e,l,n;return{c(){e=ve("svg"),l=ve("path"),n=ve("path"),D(l,"fill","currentColor"),D(l,"d","M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"),D(n,"fill","currentColor"),D(n,"d","M8 10h16v2H8zm0 6h10v2H8z"),D(e,"xmlns","http://www.w3.org/2000/svg"),D(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),D(e,"aria-hidden","true"),D(e,"role","img"),D(e,"class","iconify iconify--carbon"),D(e,"width","100%"),D(e,"height","100%"),D(e,"preserveAspectRatio","xMidYMid meet"),D(e,"viewBox","0 0 32 32")},m(i,s){Kt(i,e,s),Le(e,l),Le(e,n)},p:pe,i:pe,o:pe,d(i){i&&Gt(e)}}}class Xt extends Yt{constructor(e){super(),Jt(this,e,null,Wt,Qt,{})}}const{SvelteComponent:xt,append:je,attr:M,detach:el,flush:tl,init:ll,insert:nl,noop:Te,safe_not_equal:il,svg_element:$e}=window.__gradio__svelte__internal;function sl(t){let e,l,n,i;return{c(){e=$e("svg"),l=$e("path"),n=$e("path"),M(l,"stroke","currentColor"),M(l,"stroke-width","1.5"),M(l,"stroke-linecap","round"),M(l,"d","M16.472 3.5H4.1a.6.6 0 0 0-.6.6v9.8a.6.6 0 0 0 .6.6h2.768a2 2 0 0 1 1.715.971l2.71 4.517a1.631 1.631 0 0 0 2.961-1.308l-1.022-3.408a.6.6 0 0 1 .574-.772h4.575a2 2 0 0 0 1.93-2.526l-1.91-7A2 2 0 0 0 16.473 3.5Z"),M(n,"stroke","currentColor"),M(n,"stroke-width","1.5"),M(n,"stroke-linecap","round"),M(n,"stroke-linejoin","round"),M(n,"d","M7 14.5v-11"),M(e,"xmlns","http://www.w3.org/2000/svg"),M(e,"viewBox","0 0 24 24"),M(e,"fill",i=t[0]?"currentColor":"none"),M(e,"stroke-width","1.5"),M(e,"color","currentColor")},m(s,a){nl(s,e,a),je(e,l),je(e,n)},p(s,[a]){a&1&&i!==(i=s[0]?"currentColor":"none")&&M(e,"fill",i)},i:Te,o:Te,d(s){s&&el(e)}}}function al(t,e,l){let{selected:n}=e;return t.$$set=i=>{"selected"in i&&l(0,n=i.selected)},[n]}class rl extends xt{constructor(e){super(),ll(this,e,al,sl,il,{selected:0})}get selected(){return this.$$.ctx[0]}set selected(e){this.$$set({selected:e}),tl()}}const{SvelteComponent:ol,append:Ae,attr:E,detach:ul,flush:fl,init:_l,insert:cl,noop:Ne,safe_not_equal:hl,svg_element:Ce}=window.__gradio__svelte__internal;function ml(t){let e,l,n,i;return{c(){e=Ce("svg"),l=Ce("path"),n=Ce("path"),E(l,"stroke","currentColor"),E(l,"stroke-width","1.5"),E(l,"stroke-linecap","round"),E(l,"d","M16.472 20H4.1a.6.6 0 0 1-.6-.6V9.6a.6.6 0 0 1 .6-.6h2.768a2 2 0 0 0 1.715-.971l2.71-4.517a1.631 1.631 0 0 1 2.961 1.308l-1.022 3.408a.6.6 0 0 0 .574.772h4.575a2 2 0 0 1 1.93 2.526l-1.91 7A2 2 0 0 1 16.473 20Z"),E(n,"stroke","currentColor"),E(n,"stroke-width","1.5"),E(n,"stroke-linecap","round"),E(n,"stroke-linejoin","round"),E(n,"d","M7 20V9"),E(e,"xmlns","http://www.w3.org/2000/svg"),E(e,"viewBox","0 0 24 24"),E(e,"fill",i=t[0]?"currentColor":"none"),E(e,"stroke-width","1.5"),E(e,"color","currentColor")},m(s,a){cl(s,e,a),Ae(e,l),Ae(e,n)},p(s,[a]){a&1&&i!==(i=s[0]?"currentColor":"none")&&E(e,"fill",i)},i:Ne,o:Ne,d(s){s&&ul(e)}}}function dl(t,e,l){let{selected:n}=e;return t.$$set=i=>{"selected"in i&&l(0,n=i.selected)},[n]}class gl extends ol{constructor(e){super(),_l(this,e,dl,ml,hl,{selected:0})}get selected(){return this.$$.ctx[0]}set selected(e){this.$$set({selected:e}),fl()}}const bl=async t=>(await Promise.all(t.map(async l=>await Promise.all(l.map(async(n,i)=>{if(n===null)return"";let s=i===0?"😃":"🤖",a="";if(typeof n=="string"){const r={audio:/<audio.*?src="(\/file=.*?)"/g,video:/<video.*?src="(\/file=.*?)"/g,image:/<img.*?src="(\/file=.*?)".*?\/>|!\[.*?\]\((\/file=.*?)\)/g};a=n;for(let[o,f]of Object.entries(r)){let u;for(;(u=f.exec(n))!==null;){const m=u[1]||u[2],p=await Ee(m,"url");a=a.replace(m,p)}}}else{if(!n?.url)return"";const r=await Ee(n.url,"url");n.mime_type?.includes("audio")?a=`<audio controls src="${r}"></audio>`:n.mime_type?.includes("video")?a=r:n.mime_type?.includes("image")&&(a=`<img src="${r}" />`)}return`${s}: ${a}`}))))).map(l=>l.join(l[0]!==""&&l[1]!==""?`
`:"")).join(`
`),{SvelteComponent:kl,assign:qe,compute_rest_props:Pe,detach:wl,element:pl,exclude_internal_props:vl,flush:$l,get_spread_update:Cl,init:yl,insert:zl,listen:ye,noop:Ve,run_all:Sl,safe_not_equal:ql,set_attributes:De,src_url_equal:Bl}=window.__gradio__svelte__internal,{createEventDispatcher:Hl}=window.__gradio__svelte__internal;function Ml(t){let e,l,n,i,s=[{src:l=t[0]},t[2]],a={};for(let r=0;r<s.length;r+=1)a=qe(a,s[r]);return{c(){e=pl("audio"),De(e,a)},m(r,o){zl(r,e,o),n||(i=[ye(e,"play",t[1].bind(null,"play")),ye(e,"pause",t[1].bind(null,"pause")),ye(e,"ended",t[1].bind(null,"ended"))],n=!0)},p(r,[o]){De(e,a=Cl(s,[o&1&&!Bl(e.src,l=r[0])&&{src:l},o&4&&r[2]]))},i:Ve,o:Ve,d(r){r&&wl(e),n=!1,Sl(i)}}}function El(t,e,l){const n=["src"];let i=Pe(e,n),{src:s=void 0}=e,a,r;const o=Hl();return t.$$set=f=>{e=qe(qe({},e),vl(f)),l(2,i=Pe(e,n)),"src"in f&&l(3,s=f.src)},t.$$.update=()=>{if(t.$$.dirty&24){l(0,a=s),l(4,r=s);const f=s;It(f).then(u=>{r===f&&l(0,a=u)})}},[a,o,i,s,r]}class Ll extends kl{constructor(e){super(),yl(this,e,El,Ml,ql,{src:3})}get src(){return this.$$.ctx[3]}set src(e){this.$$set({src:e}),$l()}}const{SvelteComponent:jl,append:Tl,attr:ue,check_outros:Ie,create_component:yt,destroy_component:zt,detach:Al,element:Nl,flush:Pl,group_outros:Ue,init:Vl,insert:Dl,listen:Il,mount_component:St,safe_not_equal:Ul,space:Zl,transition_in:x,transition_out:ie}=window.__gradio__svelte__internal,{onDestroy:Fl}=window.__gradio__svelte__internal;function Ze(t){let e,l;return e=new Ft({}),{c(){yt(e.$$.fragment)},m(n,i){St(e,n,i),l=!0},i(n){l||(x(e.$$.fragment,n),l=!0)},o(n){ie(e.$$.fragment,n),l=!1},d(n){zt(e,n)}}}function Fe(t){let e,l;return e=new Zt({}),{c(){yt(e.$$.fragment)},m(n,i){St(e,n,i),l=!0},i(n){l||(x(e.$$.fragment,n),l=!0)},o(n){ie(e.$$.fragment,n),l=!1},d(n){zt(e,n)}}}function Ol(t){let e,l,n,i,s,a,r=!t[0]&&Ze(),o=t[0]&&Fe();return{c(){e=Nl("button"),r&&r.c(),l=Zl(),o&&o.c(),ue(e,"class","action svelte-rvlubk"),ue(e,"title","copy"),ue(e,"aria-label",n=t[0]?"Copied message":"Copy message")},m(f,u){Dl(f,e,u),r&&r.m(e,null),Tl(e,l),o&&o.m(e,null),i=!0,s||(a=Il(e,"click",t[1]),s=!0)},p(f,[u]){f[0]?r&&(Ue(),ie(r,1,1,()=>{r=null}),Ie()):r?u&1&&x(r,1):(r=Ze(),r.c(),x(r,1),r.m(e,l)),f[0]?o?u&1&&x(o,1):(o=Fe(),o.c(),x(o,1),o.m(e,null)):o&&(Ue(),ie(o,1,1,()=>{o=null}),Ie()),(!i||u&1&&n!==(n=f[0]?"Copied message":"Copy message"))&&ue(e,"aria-label",n)},i(f){i||(x(r),x(o),i=!0)},o(f){ie(r),ie(o),i=!1},d(f){f&&Al(e),r&&r.d(),o&&o.d(),s=!1,a()}}}function Rl(t,e,l){let n=!1,{value:i}=e,s;function a(){l(0,n=!0),s&&clearTimeout(s),s=setTimeout(()=>{l(0,n=!1)},2e3)}async function r(){if("clipboard"in navigator)await navigator.clipboard.writeText(i),a();else{const o=document.createElement("textarea");o.value=i,o.style.position="absolute",o.style.left="-999999px",document.body.prepend(o),o.select();try{document.execCommand("copy"),a()}catch(f){console.error(f)}finally{o.remove()}}}return Fl(()=>{s&&clearTimeout(s)}),t.$$set=o=>{"value"in o&&l(2,i=o.value)},[n,r,i]}class Yl extends jl{constructor(e){super(),Vl(this,e,Rl,Ol,Ul,{value:2})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),Pl()}}const{SvelteComponent:Gl,attr:ne,create_component:Oe,destroy_component:Re,detach:ze,element:Ye,flush:Jl,init:Kl,insert:Se,listen:Ge,mount_component:Je,run_all:Ql,safe_not_equal:Wl,space:Xl,transition_in:Ke,transition_out:Qe}=window.__gradio__svelte__internal;function xl(t){let e,l,n,i,s,a,r,o,f,u;return l=new gl({props:{selected:t[1]==="like"}}),a=new rl({props:{selected:t[1]==="dislike"}}),{c(){e=Ye("button"),Oe(l.$$.fragment),i=Xl(),s=Ye("button"),Oe(a.$$.fragment),ne(e,"aria-label",n=t[1]==="like"?"clicked like":"like"),ne(e,"class","svelte-3snf3m"),ne(s,"aria-label",r=t[1]==="dislike"?"clicked dislike":"dislike"),ne(s,"class","svelte-3snf3m")},m(m,p){Se(m,e,p),Je(l,e,null),Se(m,i,p),Se(m,s,p),Je(a,s,null),o=!0,f||(u=[Ge(e,"click",t[2]),Ge(s,"click",t[3])],f=!0)},p(m,[p]){const T={};p&2&&(T.selected=m[1]==="like"),l.$set(T),(!o||p&2&&n!==(n=m[1]==="like"?"clicked like":"like"))&&ne(e,"aria-label",n);const d={};p&2&&(d.selected=m[1]==="dislike"),a.$set(d),(!o||p&2&&r!==(r=m[1]==="dislike"?"clicked dislike":"dislike"))&&ne(s,"aria-label",r)},i(m){o||(Ke(l.$$.fragment,m),Ke(a.$$.fragment,m),o=!0)},o(m){Qe(l.$$.fragment,m),Qe(a.$$.fragment,m),o=!1},d(m){m&&(ze(e),ze(i),ze(s)),Re(l),Re(a),f=!1,Ql(u)}}}function en(t,e,l){let{handle_action:n}=e,i=null;const s=()=>{l(1,i="like"),n(i)},a=()=>{l(1,i="dislike"),n(i)};return t.$$set=r=>{"handle_action"in r&&l(0,n=r.handle_action)},[n,i,s,a]}class tn extends Gl{constructor(e){super(),Kl(this,e,en,xl,Wl,{handle_action:0})}get handle_action(){return this.$$.ctx[0]}set handle_action(e){this.$$set({handle_action:e}),Jl()}}const{SvelteComponent:ln,attr:fe,detach:nn,element:sn,flush:an,init:rn,insert:on,noop:We,safe_not_equal:un,set_style:Xe}=window.__gradio__svelte__internal;function fn(t){let e;return{c(){e=sn("div"),e.innerHTML=`<span class="sr-only">Loading content</span> <div class="dot-flashing svelte-1gpwetz"></div>
	 
	<div class="dot-flashing svelte-1gpwetz"></div>
	 
	<div class="dot-flashing svelte-1gpwetz"></div>`,fe(e,"class","message pending svelte-1gpwetz"),fe(e,"role","status"),fe(e,"aria-label","Loading response"),fe(e,"aria-live","polite"),Xe(e,"border-radius",t[0]==="bubble"?"var(--radius-xxl)":"none")},m(l,n){on(l,e,n)},p(l,[n]){n&1&&Xe(e,"border-radius",l[0]==="bubble"?"var(--radius-xxl)":"none")},i:We,o:We,d(l){l&&nn(e)}}}function _n(t,e,l){let{layout:n="bubble"}=e;return t.$$set=i=>{"layout"in i&&l(0,n=i.layout)},[n]}class cn extends ln{constructor(e){super(),rn(this,e,_n,fn,un,{layout:0})}get layout(){return this.$$.ctx[0]}set layout(e){this.$$set({layout:e}),an()}}const{SvelteComponent:hn,action_destroyer:mn,append:G,attr:b,binding_callbacks:xe,bubble:X,check_outros:I,create_component:Z,destroy_component:F,destroy_each:qt,detach:L,element:N,empty:Me,ensure_array_like:_e,flush:H,group_outros:U,init:dn,insert:j,listen:Be,mount_component:O,noop:He,null_to_empty:et,run_all:gn,safe_not_equal:bn,set_data:kn,set_style:re,space:te,src_url_equal:tt,text:wn,toggle_class:q,transition_in:h,transition_out:k}=window.__gradio__svelte__internal,{beforeUpdate:pn,afterUpdate:vn,createEventDispatcher:$n}=window.__gradio__svelte__internal;function lt(t,e,l){const n=t.slice();return n[42]=e[l],n[44]=l,n}function nt(t,e,l){const n=t.slice();return n[45]=e[l],n[47]=l,n}function it(t){let e,l,n;return l=new Dt({props:{i18n:t[13],formatter:bl,value:t[0]}}),l.$on("error",t[26]),l.$on("share",t[27]),{c(){e=N("div"),Z(l.$$.fragment),b(e,"class","share-button svelte-1s78gfg")},m(i,s){j(i,e,s),O(l,e,null),n=!0},p(i,s){const a={};s[0]&8192&&(a.i18n=i[13]),s[0]&1&&(a.value=i[0]),l.$set(a)},i(i){n||(h(l.$$.fragment,i),n=!0)},o(i){k(l.$$.fragment,i),n=!1},d(i){i&&L(e),F(l)}}}function Cn(t){let e,l,n;return l=new Ct({props:{message:t[15],latex_delimiters:t[1]}}),{c(){e=N("center"),Z(l.$$.fragment),b(e,"class","svelte-1s78gfg")},m(i,s){j(i,e,s),O(l,e,null),n=!0},p(i,s){const a={};s[0]&32768&&(a.message=i[15]),s[0]&2&&(a.latex_delimiters=i[1]),l.$set(a)},i(i){n||(h(l.$$.fragment,i),n=!0)},o(i){k(l.$$.fragment,i),n=!1},d(i){i&&L(e),F(l)}}}function yn(t){let e,l,n,i=_e(t[0]),s=[];for(let o=0;o<i.length;o+=1)s[o]=ct(lt(t,i,o));const a=o=>k(s[o],1,1,()=>{s[o]=null});let r=t[2]&&ht(t);return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=te(),r&&r.c(),l=Me()},m(o,f){for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(o,f);j(o,e,f),r&&r.m(o,f),j(o,l,f),n=!0},p(o,f){if(f[0]&16670683){i=_e(o[0]);let u;for(u=0;u<i.length;u+=1){const m=lt(o,i,u);s[u]?(s[u].p(m,f),h(s[u],1)):(s[u]=ct(m),s[u].c(),h(s[u],1),s[u].m(e.parentNode,e))}for(U(),u=i.length;u<s.length;u+=1)a(u);I()}o[2]?r?(r.p(o,f),f[0]&4&&h(r,1)):(r=ht(o),r.c(),h(r,1),r.m(l.parentNode,l)):r&&(U(),k(r,1,1,()=>{r=null}),I())},i(o){if(!n){for(let f=0;f<i.length;f+=1)h(s[f]);h(r),n=!0}},o(o){s=s.filter(Boolean);for(let f=0;f<s.length;f+=1)k(s[f]);k(r),n=!1},d(o){o&&(L(e),L(l)),qt(s,o),r&&r.d(o)}}}function st(t){let e,l,n,i,s,a,r,o,f,u,m,p,T,d,w,P,K,C=t[19]&&at(t),y=t[8][t[47]]!==null&&rt(t);const Q=[Hn,Bn,qn,Sn,zn],S=[];function J(v,g){return g[0]&1&&(a=null),g[0]&1&&(r=null),g[0]&1&&(o=null),typeof v[45]=="string"?0:(a==null&&(a=!!(v[45]!==null&&v[45].file?.mime_type?.includes("audio"))),a?1:(r==null&&(r=!!(v[45]!==null&&v[45].file?.mime_type?.includes("video"))),r?2:(o==null&&(o=!!(v[45]!==null&&v[45].file?.mime_type?.includes("image"))),o?3:v[45]!==null&&v[45].file?.url!==null?4:-1)))}~(f=J(t,[-1,-1]))&&(u=S[f]=Q[f](t));function A(){return t[36](t[44],t[47],t[45])}function W(...v){return t[37](t[44],t[47],t[45],...v)}let z=(t[4]&&t[47]!==0||t[7]&&t[45]&&typeof t[45]=="string")&&ot(t);return{c(){C&&C.c(),e=te(),l=N("div"),y&&y.c(),n=te(),i=N("div"),s=N("button"),u&&u.c(),T=te(),z&&z.c(),b(s,"data-testid",t[47]==0?"user":"bot"),b(s,"dir",m=t[6]?"rtl":"ltr"),b(s,"aria-label",p=(t[47]==0?"user":"bot")+"'s message: "+(typeof t[45]=="string"?t[45]:`a file of type ${t[45].file?.mime_type}, ${t[45].file?.alt_text??t[45].file?.orig_name??""}`)),b(s,"class","svelte-1s78gfg"),q(s,"latest",t[44]===t[0].length-1),q(s,"message-markdown-disabled",!t[11]),q(s,"selectable",t[3]),re(s,"user-select","text"),re(s,"text-align",t[6]?"right":"left"),b(i,"class","message "+(t[47]==0?"user":"bot")+" svelte-1s78gfg"),q(i,"message-fit",t[14]==="bubble"&&!t[10]),q(i,"panel-full-width",t[14]==="panel"),q(i,"message-bubble-border",t[14]==="bubble"),q(i,"message-markdown-disabled",!t[11]),re(i,"text-align",t[6]&&t[47]==0?"left":"right"),b(l,"class",d="message-row "+t[14]+" "+(t[47]==0?"user-row":"bot-row")+" svelte-1s78gfg")},m(v,g){C&&C.m(v,g),j(v,e,g),j(v,l,g),y&&y.m(l,null),G(l,n),G(l,i),G(i,s),~f&&S[f].m(s,null),G(l,T),z&&z.m(l,null),w=!0,P||(K=[Be(s,"click",A),Be(s,"keydown",W)],P=!0)},p(v,g){t=v,t[19]?C?(C.p(t,g),g[0]&524288&&h(C,1)):(C=at(t),C.c(),h(C,1),C.m(e.parentNode,e)):C&&(U(),k(C,1,1,()=>{C=null}),I()),t[8][t[47]]!==null?y?(y.p(t,g),g[0]&256&&h(y,1)):(y=rt(t),y.c(),h(y,1),y.m(l,n)):y&&(U(),k(y,1,1,()=>{y=null}),I());let R=f;f=J(t,g),f===R?~f&&S[f].p(t,g):(u&&(U(),k(S[R],1,1,()=>{S[R]=null}),I()),~f?(u=S[f],u?u.p(t,g):(u=S[f]=Q[f](t),u.c()),h(u,1),u.m(s,null)):u=null),(!w||g[0]&64&&m!==(m=t[6]?"rtl":"ltr"))&&b(s,"dir",m),(!w||g[0]&1&&p!==(p=(t[47]==0?"user":"bot")+"'s message: "+(typeof t[45]=="string"?t[45]:`a file of type ${t[45].file?.mime_type}, ${t[45].file?.alt_text??t[45].file?.orig_name??""}`)))&&b(s,"aria-label",p),(!w||g[0]&1)&&q(s,"latest",t[44]===t[0].length-1),(!w||g[0]&2048)&&q(s,"message-markdown-disabled",!t[11]),(!w||g[0]&8)&&q(s,"selectable",t[3]),g[0]&64&&re(s,"text-align",t[6]?"right":"left"),(!w||g[0]&17408)&&q(i,"message-fit",t[14]==="bubble"&&!t[10]),(!w||g[0]&16384)&&q(i,"panel-full-width",t[14]==="panel"),(!w||g[0]&16384)&&q(i,"message-bubble-border",t[14]==="bubble"),(!w||g[0]&2048)&&q(i,"message-markdown-disabled",!t[11]),g[0]&64&&re(i,"text-align",t[6]&&t[47]==0?"left":"right"),t[4]&&t[47]!==0||t[7]&&t[45]&&typeof t[45]=="string"?z?(z.p(t,g),g[0]&145&&h(z,1)):(z=ot(t),z.c(),h(z,1),z.m(l,null)):z&&(U(),k(z,1,1,()=>{z=null}),I()),(!w||g[0]&16384&&d!==(d="message-row "+t[14]+" "+(t[47]==0?"user-row":"bot-row")+" svelte-1s78gfg"))&&b(l,"class",d)},i(v){w||(h(C),h(y),h(u),h(z),w=!0)},o(v){k(C),k(y),k(u),k(z),w=!1},d(v){v&&(L(e),L(l)),C&&C.d(v),y&&y.d(),~f&&S[f].d(),z&&z.d(),P=!1,gn(K)}}}function at(t){let e,l,n,i,s,a,r,o,f;return a=new At({}),{c(){e=N("div"),l=N("img"),i=te(),s=N("button"),Z(a.$$.fragment),tt(l.src,n=t[17])||b(l,"src",n),b(l,"alt",t[18]),b(l,"class","svelte-1s78gfg"),b(s,"class","image-preview-close-button svelte-1s78gfg"),b(e,"class","image-preview svelte-1s78gfg")},m(u,m){j(u,e,m),G(e,l),G(e,i),G(e,s),O(a,s,null),t[28](s),r=!0,o||(f=Be(s,"click",t[29]),o=!0)},p(u,m){(!r||m[0]&131072&&!tt(l.src,n=u[17]))&&b(l,"src",n),(!r||m[0]&262144)&&b(l,"alt",u[18])},i(u){r||(h(a.$$.fragment,u),r=!0)},o(u){k(a.$$.fragment,u),r=!1},d(u){u&&L(e),F(a),t[28](null),o=!1,f()}}}function rt(t){let e,l,n;return l=new $t({props:{class:"avatar-image",src:t[8][t[47]]?.url,alt:(t[47]==0?"user":"bot")+" avatar"}}),{c(){e=N("div"),Z(l.$$.fragment),b(e,"class","avatar-container svelte-1s78gfg")},m(i,s){j(i,e,s),O(l,e,null),n=!0},p(i,s){const a={};s[0]&256&&(a.src=i[8][i[47]]?.url),l.$set(a)},i(i){n||(h(l.$$.fragment,i),n=!0)},o(i){k(l.$$.fragment,i),n=!1},d(i){i&&L(e),F(l)}}}function zn(t){let e,l=(t[45].file?.orig_name||t[45].file?.path)+"",n,i,s;return{c(){e=N("a"),n=wn(l),b(e,"data-testid","chatbot-file"),b(e,"href",i=t[45].file?.url),b(e,"target","_blank"),b(e,"download",s=window.__is_colab__?null:t[45].file?.orig_name||t[45].file?.path),b(e,"class","svelte-1s78gfg")},m(a,r){j(a,e,r),G(e,n)},p(a,r){r[0]&1&&l!==(l=(a[45].file?.orig_name||a[45].file?.path)+"")&&kn(n,l),r[0]&1&&i!==(i=a[45].file?.url)&&b(e,"href",i),r[0]&1&&s!==(s=window.__is_colab__?null:a[45].file?.orig_name||a[45].file?.path)&&b(e,"download",s)},i:He,o:He,d(a){a&&L(e)}}}function Sn(t){let e,l;return e=new $t({props:{"data-testid":"chatbot-image",src:t[45].file?.url,alt:t[45].alt_text}}),{c(){Z(e.$$.fragment)},m(n,i){O(e,n,i),l=!0},p(n,i){const s={};i[0]&1&&(s.src=n[45].file?.url),i[0]&1&&(s.alt=n[45].alt_text),e.$set(s)},i(n){l||(h(e.$$.fragment,n),l=!0)},o(n){k(e.$$.fragment,n),l=!1},d(n){F(e,n)}}}function qn(t){let e,l;return e=new Ut({props:{"data-testid":"chatbot-video",controls:!0,src:t[45].file?.url,title:t[45].alt_text,preload:"auto",$$slots:{default:[Mn]},$$scope:{ctx:t}}}),e.$on("play",t[33]),e.$on("pause",t[34]),e.$on("ended",t[35]),{c(){Z(e.$$.fragment)},m(n,i){O(e,n,i),l=!0},p(n,i){const s={};i[0]&1&&(s.src=n[45].file?.url),i[0]&1&&(s.title=n[45].alt_text),i[1]&131072&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){l||(h(e.$$.fragment,n),l=!0)},o(n){k(e.$$.fragment,n),l=!1},d(n){F(e,n)}}}function Bn(t){let e,l;return e=new Ll({props:{"data-testid":"chatbot-audio",controls:!0,preload:"metadata",src:t[45].file?.url,title:t[45].alt_text}}),e.$on("play",t[30]),e.$on("pause",t[31]),e.$on("ended",t[32]),{c(){Z(e.$$.fragment)},m(n,i){O(e,n,i),l=!0},p(n,i){const s={};i[0]&1&&(s.src=n[45].file?.url),i[0]&1&&(s.title=n[45].alt_text),e.$set(s)},i(n){l||(h(e.$$.fragment,n),l=!0)},o(n){k(e.$$.fragment,n),l=!1},d(n){F(e,n)}}}function Hn(t){let e,l;return e=new Ct({props:{message:t[45],latex_delimiters:t[1],sanitize_html:t[9],render_markdown:t[11],line_breaks:t[12]}}),e.$on("load",t[21]),{c(){Z(e.$$.fragment)},m(n,i){O(e,n,i),l=!0},p(n,i){const s={};i[0]&1&&(s.message=n[45]),i[0]&2&&(s.latex_delimiters=n[1]),i[0]&512&&(s.sanitize_html=n[9]),i[0]&2048&&(s.render_markdown=n[11]),i[0]&4096&&(s.line_breaks=n[12]),e.$set(s)},i(n){l||(h(e.$$.fragment,n),l=!0)},o(n){k(e.$$.fragment,n),l=!1},d(n){F(e,n)}}}function Mn(t){let e;return{c(){e=N("track"),b(e,"kind","captions"),b(e,"class","svelte-1s78gfg")},m(l,n){j(l,e,n)},p:He,d(l){l&&L(e)}}}function ot(t){let e,l,n,i,s=t[4]&&t[47]==1&&ut(t),a=t[7]&&t[45]&&typeof t[45]=="string"&&ft(t);return{c(){e=N("div"),s&&s.c(),l=te(),a&&a.c(),b(e,"class",n="message-buttons-"+(t[47]==0?"user":"bot")+" message-buttons-"+t[14]+" "+(t[8][t[47]]!==null&&"with-avatar")+" svelte-1s78gfg"),q(e,"message-buttons-fit",t[14]==="bubble"&&!t[10]),q(e,"bubble-buttons-user",t[14]==="bubble")},m(r,o){j(r,e,o),s&&s.m(e,null),G(e,l),a&&a.m(e,null),i=!0},p(r,o){r[4]&&r[47]==1?s?(s.p(r,o),o[0]&16&&h(s,1)):(s=ut(r),s.c(),h(s,1),s.m(e,l)):s&&(U(),k(s,1,1,()=>{s=null}),I()),r[7]&&r[45]&&typeof r[45]=="string"?a?(a.p(r,o),o[0]&129&&h(a,1)):(a=ft(r),a.c(),h(a,1),a.m(e,null)):a&&(U(),k(a,1,1,()=>{a=null}),I()),(!i||o[0]&16640&&n!==(n="message-buttons-"+(r[47]==0?"user":"bot")+" message-buttons-"+r[14]+" "+(r[8][r[47]]!==null&&"with-avatar")+" svelte-1s78gfg"))&&b(e,"class",n),(!i||o[0]&17664)&&q(e,"message-buttons-fit",r[14]==="bubble"&&!r[10]),(!i||o[0]&16640)&&q(e,"bubble-buttons-user",r[14]==="bubble")},i(r){i||(h(s),h(a),i=!0)},o(r){k(s),k(a),i=!1},d(r){r&&L(e),s&&s.d(),a&&a.d()}}}function ut(t){let e,l;function n(...i){return t[38](t[44],t[47],t[45],...i)}return e=new tn({props:{handle_action:n}}),{c(){Z(e.$$.fragment)},m(i,s){O(e,i,s),l=!0},p(i,s){t=i;const a={};s[0]&1&&(a.handle_action=n),e.$set(a)},i(i){l||(h(e.$$.fragment,i),l=!0)},o(i){k(e.$$.fragment,i),l=!1},d(i){F(e,i)}}}function ft(t){let e,l;return e=new Yl({props:{value:t[45]}}),{c(){Z(e.$$.fragment)},m(n,i){O(e,n,i),l=!0},p(n,i){const s={};i[0]&1&&(s.value=n[45]),e.$set(s)},i(n){l||(h(e.$$.fragment,n),l=!0)},o(n){k(e.$$.fragment,n),l=!1},d(n){F(e,n)}}}function _t(t){let e,l,n=t[45]!==null&&st(t);return{c(){n&&n.c(),e=Me()},m(i,s){n&&n.m(i,s),j(i,e,s),l=!0},p(i,s){i[45]!==null?n?(n.p(i,s),s[0]&1&&h(n,1)):(n=st(i),n.c(),h(n,1),n.m(e.parentNode,e)):n&&(U(),k(n,1,1,()=>{n=null}),I())},i(i){l||(h(n),l=!0)},o(i){k(n),l=!1},d(i){i&&L(e),n&&n.d(i)}}}function ct(t){let e,l,n=_e(t[42]),i=[];for(let a=0;a<n.length;a+=1)i[a]=_t(nt(t,n,a));const s=a=>k(i[a],1,1,()=>{i[a]=null});return{c(){for(let a=0;a<i.length;a+=1)i[a].c();e=Me()},m(a,r){for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(a,r);j(a,e,r),l=!0},p(a,r){if(r[0]&16670683){n=_e(a[42]);let o;for(o=0;o<n.length;o+=1){const f=nt(a,n,o);i[o]?(i[o].p(f,r),h(i[o],1)):(i[o]=_t(f),i[o].c(),h(i[o],1),i[o].m(e.parentNode,e))}for(U(),o=n.length;o<i.length;o+=1)s(o);I()}},i(a){if(!l){for(let r=0;r<n.length;r+=1)h(i[r]);l=!0}},o(a){i=i.filter(Boolean);for(let r=0;r<i.length;r+=1)k(i[r]);l=!1},d(a){a&&L(e),qt(i,a)}}}function ht(t){let e,l;return e=new cn({props:{layout:t[14]}}),{c(){Z(e.$$.fragment)},m(n,i){O(e,n,i),l=!0},p(n,i){const s={};i[0]&16384&&(s.layout=n[14]),e.$set(s)},i(n){l||(h(e.$$.fragment,n),l=!0)},o(n){k(e.$$.fragment,n),l=!1},d(n){F(e,n)}}}function En(t){let e,l,n,i,s,a,r,o,f,u=t[5]&&t[0]!==null&&t[0].length>0&&it(t);const m=[yn,Cn],p=[];function T(d,w){return d[0]!==null&&d[0].length>0?0:d[15]!==null?1:-1}return~(i=T(t))&&(s=p[i]=m[i](t)),{c(){u&&u.c(),e=te(),l=N("div"),n=N("div"),s&&s.c(),b(n,"class","message-wrap svelte-1s78gfg"),q(n,"bubble-gap",t[14]==="bubble"),b(l,"class",a=et(t[14]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-1s78gfg"),b(l,"role","log"),b(l,"aria-label","chatbot conversation"),b(l,"aria-live","polite"),q(l,"placeholder-container",t[0]===null||t[0].length===0)},m(d,w){u&&u.m(d,w),j(d,e,w),j(d,l,w),G(l,n),~i&&p[i].m(n,null),t[39](l),r=!0,o||(f=mn(Pt.call(null,n)),o=!0)},p(d,w){d[5]&&d[0]!==null&&d[0].length>0?u?(u.p(d,w),w[0]&33&&h(u,1)):(u=it(d),u.c(),h(u,1),u.m(e.parentNode,e)):u&&(U(),k(u,1,1,()=>{u=null}),I());let P=i;i=T(d),i===P?~i&&p[i].p(d,w):(s&&(U(),k(p[P],1,1,()=>{p[P]=null}),I()),~i?(s=p[i],s?s.p(d,w):(s=p[i]=m[i](d),s.c()),h(s,1),s.m(n,null)):s=null),(!r||w[0]&16384)&&q(n,"bubble-gap",d[14]==="bubble"),(!r||w[0]&16384&&a!==(a=et(d[14]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-1s78gfg"))&&b(l,"class",a),(!r||w[0]&16385)&&q(l,"placeholder-container",d[0]===null||d[0].length===0)},i(d){r||(h(u),h(s),r=!0)},o(d){k(u),k(s),r=!1},d(d){d&&(L(e),L(l)),u&&u.d(d),~i&&p[i].d(),t[39](null),o=!1,f()}}}function Ln(t,e,l){let n,{value:i}=e,s=null,{latex_delimiters:a}=e,{pending_message:r=!1}=e,{selectable:o=!1}=e,{likeable:f=!1}=e,{show_share_button:u=!1}=e,{rtl:m=!1}=e,{show_copy_button:p=!1}=e,{avatar_images:T=[null,null]}=e,{sanitize_html:d=!0}=e,{bubble_full_width:w=!0}=e,{render_markdown:P=!0}=e,{line_breaks:K=!0}=e,{i18n:C}=e,{layout:y="bubble"}=e,{placeholder:Q=null}=e,S,J;const A=$n();pn(()=>{J=S&&S.offsetHeight+S.scrollTop>S.scrollHeight-100});const W=()=>{J&&S.scrollTo(0,S.scrollHeight)};let z,v,g=!1,R;vn(()=>{J&&(W(),S.querySelectorAll("img").forEach(_=>{_.addEventListener("load",()=>{W()})})),S.querySelectorAll("img").forEach(_=>{_.addEventListener("click",V=>{const B=V.target;B&&(l(17,z=B.src),l(18,v=B.alt),l(19,g=!0))})})});function le(_,V,B){A("select",{index:[_,V],value:B})}function oe(_,V,B,ae){A("like",{index:[_,V],value:B,liked:ae==="like"})}function de(_){X.call(this,t,_)}function ge(_){X.call(this,t,_)}function be(_){xe[_?"unshift":"push"](()=>{R=_,l(20,R)})}const ke=()=>{l(19,g=!1)};function we(_){X.call(this,t,_)}function c(_){X.call(this,t,_)}function se(_){X.call(this,t,_)}function Bt(_){X.call(this,t,_)}function Ht(_){X.call(this,t,_)}function Mt(_){X.call(this,t,_)}const Et=(_,V,B)=>le(_,V,B),Lt=(_,V,B,ae)=>{ae.key==="Enter"&&le(_,V,B)},jt=(_,V,B,ae)=>oe(_,V,B,ae);function Tt(_){xe[_?"unshift":"push"](()=>{S=_,l(16,S)})}return t.$$set=_=>{"value"in _&&l(0,i=_.value),"latex_delimiters"in _&&l(1,a=_.latex_delimiters),"pending_message"in _&&l(2,r=_.pending_message),"selectable"in _&&l(3,o=_.selectable),"likeable"in _&&l(4,f=_.likeable),"show_share_button"in _&&l(5,u=_.show_share_button),"rtl"in _&&l(6,m=_.rtl),"show_copy_button"in _&&l(7,p=_.show_copy_button),"avatar_images"in _&&l(8,T=_.avatar_images),"sanitize_html"in _&&l(9,d=_.sanitize_html),"bubble_full_width"in _&&l(10,w=_.bubble_full_width),"render_markdown"in _&&l(11,P=_.render_markdown),"line_breaks"in _&&l(12,K=_.line_breaks),"i18n"in _&&l(13,C=_.i18n),"layout"in _&&l(14,y=_.layout),"placeholder"in _&&l(15,Q=_.placeholder)},t.$$.update=()=>{t.$$.dirty[0]&33554432&&n(),t.$$.dirty[0]&16777217&&(Vt(i,s)||(l(24,s=i),A("change")))},l(25,n=()=>{let V=getComputedStyle(document.body).getPropertyValue("--body-text-size"),B;switch(V){case"13px":B=14;break;case"14px":B=16;break;case"16px":B=20;break;default:B=14;break}document.body.style.setProperty("--chatbot-body-text-size",B+"px")}),[i,a,r,o,f,u,m,p,T,d,w,P,K,C,y,Q,S,z,v,g,R,W,le,oe,s,n,de,ge,be,ke,we,c,se,Bt,Ht,Mt,Et,Lt,jt,Tt]}class jn extends hn{constructor(e){super(),dn(this,e,Ln,En,bn,{value:0,latex_delimiters:1,pending_message:2,selectable:3,likeable:4,show_share_button:5,rtl:6,show_copy_button:7,avatar_images:8,sanitize_html:9,bubble_full_width:10,render_markdown:11,line_breaks:12,i18n:13,layout:14,placeholder:15},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),H()}get latex_delimiters(){return this.$$.ctx[1]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),H()}get pending_message(){return this.$$.ctx[2]}set pending_message(e){this.$$set({pending_message:e}),H()}get selectable(){return this.$$.ctx[3]}set selectable(e){this.$$set({selectable:e}),H()}get likeable(){return this.$$.ctx[4]}set likeable(e){this.$$set({likeable:e}),H()}get show_share_button(){return this.$$.ctx[5]}set show_share_button(e){this.$$set({show_share_button:e}),H()}get rtl(){return this.$$.ctx[6]}set rtl(e){this.$$set({rtl:e}),H()}get show_copy_button(){return this.$$.ctx[7]}set show_copy_button(e){this.$$set({show_copy_button:e}),H()}get avatar_images(){return this.$$.ctx[8]}set avatar_images(e){this.$$set({avatar_images:e}),H()}get sanitize_html(){return this.$$.ctx[9]}set sanitize_html(e){this.$$set({sanitize_html:e}),H()}get bubble_full_width(){return this.$$.ctx[10]}set bubble_full_width(e){this.$$set({bubble_full_width:e}),H()}get render_markdown(){return this.$$.ctx[11]}set render_markdown(e){this.$$set({render_markdown:e}),H()}get line_breaks(){return this.$$.ctx[12]}set line_breaks(e){this.$$set({line_breaks:e}),H()}get i18n(){return this.$$.ctx[13]}set i18n(e){this.$$set({i18n:e}),H()}get layout(){return this.$$.ctx[14]}set layout(e){this.$$set({layout:e}),H()}get placeholder(){return this.$$.ctx[15]}set placeholder(e){this.$$set({placeholder:e}),H()}}const Tn=jn,{SvelteComponent:An,append:Nn,assign:Pn,attr:Vn,check_outros:mt,create_component:ce,destroy_component:he,detach:dt,element:Dn,flush:$,get_spread_object:In,get_spread_update:Un,group_outros:gt,init:Zn,insert:bt,mount_component:me,safe_not_equal:Fn,space:kt,transition_in:Y,transition_out:ee}=window.__gradio__svelte__internal;function wt(t){let e,l;const n=[{autoscroll:t[19].autoscroll},{i18n:t[19].i18n},t[21],{show_progress:t[21].show_progress==="hidden"?"hidden":"minimal"}];let i={};for(let s=0;s<n.length;s+=1)i=Pn(i,n[s]);return e=new Nt({props:i}),e.$on("clear_status",t[26]),{c(){ce(e.$$.fragment)},m(s,a){me(e,s,a),l=!0},p(s,a){const r=a[0]&2621440?Un(n,[a[0]&524288&&{autoscroll:s[19].autoscroll},a[0]&524288&&{i18n:s[19].i18n},a[0]&2097152&&In(s[21]),a[0]&2097152&&{show_progress:s[21].show_progress==="hidden"?"hidden":"minimal"}]):{};e.$set(r)},i(s){l||(Y(e.$$.fragment,s),l=!0)},o(s){ee(e.$$.fragment,s),l=!1},d(s){he(e,s)}}}function pt(t){let e,l;return e=new Rt({props:{show_label:t[7],Icon:Xt,float:!1,label:t[6]||"Chatbot"}}),{c(){ce(e.$$.fragment)},m(n,i){me(e,n,i),l=!0},p(n,i){const s={};i[0]&128&&(s.show_label=n[7]),i[0]&64&&(s.label=n[6]||"Chatbot"),e.$set(s)},i(n){l||(Y(e.$$.fragment,n),l=!0)},o(n){ee(e.$$.fragment,n),l=!1},d(n){he(e,n)}}}function On(t){let e,l,n,i,s,a=t[21]&&wt(t),r=t[7]&&pt(t);return i=new Tn({props:{i18n:t[19].i18n,selectable:t[8],likeable:t[9],show_share_button:t[10],value:t[24],latex_delimiters:t[18],render_markdown:t[16],pending_message:t[21]?.status==="pending",rtl:t[11],show_copy_button:t[12],avatar_images:t[20],sanitize_html:t[13],bubble_full_width:t[14],line_breaks:t[17],layout:t[15],placeholder:t[23]}}),i.$on("change",t[27]),i.$on("select",t[28]),i.$on("like",t[29]),i.$on("share",t[30]),i.$on("error",t[31]),{c(){a&&a.c(),e=kt(),l=Dn("div"),r&&r.c(),n=kt(),ce(i.$$.fragment),Vn(l,"class","wrapper svelte-nab2ao")},m(o,f){a&&a.m(o,f),bt(o,e,f),bt(o,l,f),r&&r.m(l,null),Nn(l,n),me(i,l,null),s=!0},p(o,f){o[21]?a?(a.p(o,f),f[0]&2097152&&Y(a,1)):(a=wt(o),a.c(),Y(a,1),a.m(e.parentNode,e)):a&&(gt(),ee(a,1,1,()=>{a=null}),mt()),o[7]?r?(r.p(o,f),f[0]&128&&Y(r,1)):(r=pt(o),r.c(),Y(r,1),r.m(l,n)):r&&(gt(),ee(r,1,1,()=>{r=null}),mt());const u={};f[0]&524288&&(u.i18n=o[19].i18n),f[0]&256&&(u.selectable=o[8]),f[0]&512&&(u.likeable=o[9]),f[0]&1024&&(u.show_share_button=o[10]),f[0]&16777216&&(u.value=o[24]),f[0]&262144&&(u.latex_delimiters=o[18]),f[0]&65536&&(u.render_markdown=o[16]),f[0]&2097152&&(u.pending_message=o[21]?.status==="pending"),f[0]&2048&&(u.rtl=o[11]),f[0]&4096&&(u.show_copy_button=o[12]),f[0]&1048576&&(u.avatar_images=o[20]),f[0]&8192&&(u.sanitize_html=o[13]),f[0]&16384&&(u.bubble_full_width=o[14]),f[0]&131072&&(u.line_breaks=o[17]),f[0]&32768&&(u.layout=o[15]),f[0]&8388608&&(u.placeholder=o[23]),i.$set(u)},i(o){s||(Y(a),Y(r),Y(i.$$.fragment,o),s=!0)},o(o){ee(a),ee(r),ee(i.$$.fragment,o),s=!1},d(o){o&&(dt(e),dt(l)),a&&a.d(o),r&&r.d(),he(i)}}}function Rn(t){let e,l;return e=new Ot({props:{elem_id:t[0],elem_classes:t[1],visible:t[2],padding:!1,scale:t[4],min_width:t[5],height:t[22],allow_overflow:!1,$$slots:{default:[On]},$$scope:{ctx:t}}}),{c(){ce(e.$$.fragment)},m(n,i){me(e,n,i),l=!0},p(n,i){const s={};i[0]&1&&(s.elem_id=n[0]),i[0]&2&&(s.elem_classes=n[1]),i[0]&4&&(s.visible=n[2]),i[0]&16&&(s.scale=n[4]),i[0]&32&&(s.min_width=n[5]),i[0]&4194304&&(s.height=n[22]),i[0]&29360072|i[1]&4&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){l||(Y(e.$$.fragment,n),l=!0)},o(n){ee(e.$$.fragment,n),l=!1},d(n){he(e,n)}}}function vt(t){return t===null?t:{file:t?.file,alt_text:t?.alt_text}}function Yn(t,e,l){let{elem_id:n=""}=e,{elem_classes:i=[]}=e,{visible:s=!0}=e,{value:a=[]}=e,{scale:r=null}=e,{min_width:o=void 0}=e,{label:f}=e,{show_label:u=!0}=e,{root:m}=e,{_selectable:p=!1}=e,{likeable:T=!1}=e,{show_share_button:d=!1}=e,{rtl:w=!1}=e,{show_copy_button:P=!1}=e,{sanitize_html:K=!0}=e,{bubble_full_width:C=!0}=e,{layout:y="bubble"}=e,{render_markdown:Q=!0}=e,{line_breaks:S=!0}=e,{latex_delimiters:J}=e,{gradio:A}=e,{avatar_images:W=[null,null]}=e,z;const v=c=>c.replace('src="/file',`src="${m}file`);let{loading_status:g=void 0}=e,{height:R=400}=e,{placeholder:le=null}=e;const oe=()=>A.dispatch("clear_status",g),de=()=>A.dispatch("change",a),ge=c=>A.dispatch("select",c.detail),be=c=>A.dispatch("like",c.detail),ke=c=>A.dispatch("share",c.detail),we=c=>A.dispatch("error",c.detail);return t.$$set=c=>{"elem_id"in c&&l(0,n=c.elem_id),"elem_classes"in c&&l(1,i=c.elem_classes),"visible"in c&&l(2,s=c.visible),"value"in c&&l(3,a=c.value),"scale"in c&&l(4,r=c.scale),"min_width"in c&&l(5,o=c.min_width),"label"in c&&l(6,f=c.label),"show_label"in c&&l(7,u=c.show_label),"root"in c&&l(25,m=c.root),"_selectable"in c&&l(8,p=c._selectable),"likeable"in c&&l(9,T=c.likeable),"show_share_button"in c&&l(10,d=c.show_share_button),"rtl"in c&&l(11,w=c.rtl),"show_copy_button"in c&&l(12,P=c.show_copy_button),"sanitize_html"in c&&l(13,K=c.sanitize_html),"bubble_full_width"in c&&l(14,C=c.bubble_full_width),"layout"in c&&l(15,y=c.layout),"render_markdown"in c&&l(16,Q=c.render_markdown),"line_breaks"in c&&l(17,S=c.line_breaks),"latex_delimiters"in c&&l(18,J=c.latex_delimiters),"gradio"in c&&l(19,A=c.gradio),"avatar_images"in c&&l(20,W=c.avatar_images),"loading_status"in c&&l(21,g=c.loading_status),"height"in c&&l(22,R=c.height),"placeholder"in c&&l(23,le=c.placeholder)},t.$$.update=()=>{t.$$.dirty[0]&8&&l(24,z=a?a.map(([c,se])=>[typeof c=="string"?v(c):vt(c),typeof se=="string"?v(se):vt(se)]):[])},[n,i,s,a,r,o,f,u,p,T,d,w,P,K,C,y,Q,S,J,A,W,g,R,le,z,m,oe,de,ge,be,ke,we]}class ui extends An{constructor(e){super(),Zn(this,e,Yn,Rn,Fn,{elem_id:0,elem_classes:1,visible:2,value:3,scale:4,min_width:5,label:6,show_label:7,root:25,_selectable:8,likeable:9,show_share_button:10,rtl:11,show_copy_button:12,sanitize_html:13,bubble_full_width:14,layout:15,render_markdown:16,line_breaks:17,latex_delimiters:18,gradio:19,avatar_images:20,loading_status:21,height:22,placeholder:23},null,[-1,-1])}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),$()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),$()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),$()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),$()}get scale(){return this.$$.ctx[4]}set scale(e){this.$$set({scale:e}),$()}get min_width(){return this.$$.ctx[5]}set min_width(e){this.$$set({min_width:e}),$()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),$()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),$()}get root(){return this.$$.ctx[25]}set root(e){this.$$set({root:e}),$()}get _selectable(){return this.$$.ctx[8]}set _selectable(e){this.$$set({_selectable:e}),$()}get likeable(){return this.$$.ctx[9]}set likeable(e){this.$$set({likeable:e}),$()}get show_share_button(){return this.$$.ctx[10]}set show_share_button(e){this.$$set({show_share_button:e}),$()}get rtl(){return this.$$.ctx[11]}set rtl(e){this.$$set({rtl:e}),$()}get show_copy_button(){return this.$$.ctx[12]}set show_copy_button(e){this.$$set({show_copy_button:e}),$()}get sanitize_html(){return this.$$.ctx[13]}set sanitize_html(e){this.$$set({sanitize_html:e}),$()}get bubble_full_width(){return this.$$.ctx[14]}set bubble_full_width(e){this.$$set({bubble_full_width:e}),$()}get layout(){return this.$$.ctx[15]}set layout(e){this.$$set({layout:e}),$()}get render_markdown(){return this.$$.ctx[16]}set render_markdown(e){this.$$set({render_markdown:e}),$()}get line_breaks(){return this.$$.ctx[17]}set line_breaks(e){this.$$set({line_breaks:e}),$()}get latex_delimiters(){return this.$$.ctx[18]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),$()}get gradio(){return this.$$.ctx[19]}set gradio(e){this.$$set({gradio:e}),$()}get avatar_images(){return this.$$.ctx[20]}set avatar_images(e){this.$$set({avatar_images:e}),$()}get loading_status(){return this.$$.ctx[21]}set loading_status(e){this.$$set({loading_status:e}),$()}get height(){return this.$$.ctx[22]}set height(e){this.$$set({height:e}),$()}get placeholder(){return this.$$.ctx[23]}set placeholder(e){this.$$set({placeholder:e}),$()}}export{Tn as BaseChatBot,ui as default};
//# sourceMappingURL=Index-CUtJsgll.js.map
