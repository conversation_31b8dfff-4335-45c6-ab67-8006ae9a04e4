{"version": 3, "file": "Index-CUtJsgll.js", "sources": ["../../../../js/icons/src/Chat.svelte", "../../../../js/icons/src/Dislike.svelte", "../../../../js/icons/src/Like.svelte", "../../../../js/chatbot/shared/utils.ts", "../../../../js/audio/shared/Audio.svelte", "../../../../js/chatbot/shared/Copy.svelte", "../../../../js/chatbot/shared/LikeDislike.svelte", "../../../../js/chatbot/shared/Pending.svelte", "../../../../js/chatbot/shared/ChatBot.svelte", "../../../../js/chatbot/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z\"\n\t/>\n\t<path fill=\"currentColor\" d=\"M8 10h16v2H8zm0 6h10v2H8z\" />\n</svg>\n", "<script lang=\"ts\">\n\texport let selected: boolean;\n</script>\n\n<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 24 24\"\n\tfill={selected ? \"currentColor\" : \"none\"}\n\tstroke-width=\"1.5\"\n\tcolor=\"currentColor\"\n\t><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\td=\"M16.472 3.5H4.1a.6.6 0 0 0-.6.6v9.8a.6.6 0 0 0 .6.6h2.768a2 2 0 0 1 1.715.971l2.71 4.517a1.631 1.631 0 0 0 2.961-1.308l-1.022-3.408a.6.6 0 0 1 .574-.772h4.575a2 2 0 0 0 1.93-2.526l-1.91-7A2 2 0 0 0 16.473 3.5Z\"\n\t/><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M7 14.5v-11\"\n\t/></svg\n>\n", "<script lang=\"ts\">\n\texport let selected: boolean;\n</script>\n\n<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 24 24\"\n\tfill={selected ? \"currentColor\" : \"none\"}\n\tstroke-width=\"1.5\"\n\tcolor=\"currentColor\"\n\t><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\td=\"M16.472 20H4.1a.6.6 0 0 1-.6-.6V9.6a.6.6 0 0 1 .6-.6h2.768a2 2 0 0 0 1.715-.971l2.71-4.517a1.631 1.631 0 0 1 2.961 1.308l-1.022 3.408a.6.6 0 0 0 .574.772h4.575a2 2 0 0 1 1.93 2.526l-1.91 7A2 2 0 0 1 16.473 20Z\"\n\t/><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M7 20V9\"\n\t/></svg\n>\n", "import type { FileData } from \"@gradio/client\";\nimport { uploadToHuggingFace } from \"@gradio/utils\";\n\nexport const format_chat_for_sharing = async (\n\tchat: [string | FileData | null, string | FileData | null][]\n): Promise<string> => {\n\tlet messages = await Promise.all(\n\t\tchat.map(async (message_pair) => {\n\t\t\treturn await Promise.all(\n\t\t\t\tmessage_pair.map(async (message, i) => {\n\t\t\t\t\tif (message === null) return \"\";\n\t\t\t\t\tlet speaker_emoji = i === 0 ? \"😃\" : \"🤖\";\n\t\t\t\t\tlet html_content = \"\";\n\n\t\t\t\t\tif (typeof message === \"string\") {\n\t\t\t\t\t\tconst regexPatterns = {\n\t\t\t\t\t\t\taudio: /<audio.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\tvideo: /<video.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\timage: /<img.*?src=\"(\\/file=.*?)\".*?\\/>|!\\[.*?\\]\\((\\/file=.*?)\\)/g\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\thtml_content = message;\n\n\t\t\t\t\t\tfor (let [_, regex] of Object.entries(regexPatterns)) {\n\t\t\t\t\t\t\tlet match;\n\n\t\t\t\t\t\t\twhile ((match = regex.exec(message)) !== null) {\n\t\t\t\t\t\t\t\tconst fileUrl = match[1] || match[2];\n\t\t\t\t\t\t\t\tconst newUrl = await uploadToHuggingFace(fileUrl, \"url\");\n\t\t\t\t\t\t\t\thtml_content = html_content.replace(fileUrl, newUrl);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (!message?.url) return \"\";\n\t\t\t\t\t\tconst file_url = await uploadToHuggingFace(message.url, \"url\");\n\t\t\t\t\t\tif (message.mime_type?.includes(\"audio\")) {\n\t\t\t\t\t\t\thtml_content = `<audio controls src=\"${file_url}\"></audio>`;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"video\")) {\n\t\t\t\t\t\t\thtml_content = file_url;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"image\")) {\n\t\t\t\t\t\t\thtml_content = `<img src=\"${file_url}\" />`;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn `${speaker_emoji}: ${html_content}`;\n\t\t\t\t})\n\t\t\t);\n\t\t})\n\t);\n\treturn messages\n\t\t.map((message_pair) =>\n\t\t\tmessage_pair.join(\n\t\t\t\tmessage_pair[0] !== \"\" && message_pair[1] !== \"\" ? \"\\n\" : \"\"\n\t\t\t)\n\t\t)\n\t\t.join(\"\\n\");\n};\n", "<script lang=\"ts\">\n\timport type { HTMLAudioAttributes } from \"svelte/elements\";\n\timport { createEventDispatcher } from \"svelte\";\n\tinterface Props extends HTMLAudioAttributes {\n\t\t\"data-testid\"?: string;\n\t}\n\ttype $$Props = Props;\n\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let src: HTMLAudioAttributes[\"src\"] = undefined;\n\n\tlet resolved_src: typeof src;\n\n\t// The `src` prop can be updated before the Promise from `resolve_wasm_src` is resolved.\n\t// In such a case, the resolved value for the old `src` has to be discarded,\n\t// This variable `latest_src` is used to pick up only the value resolved for the latest `src` prop.\n\tlet latest_src: typeof src;\n\t$: {\n\t\t// In normal (non-Wasm) Gradio, the `<audio>` element should be rendered with the passed `src` props immediately\n\t\t// without waiting for `resolve_wasm_src()` to resolve.\n\t\t// If it waits, a black image is displayed until the async task finishes\n\t\t// and it leads to undesirable flickering.\n\t\t// So set `src` to `resolved_src` here.\n\t\tresolved_src = src;\n\n\t\tlatest_src = src;\n\t\tconst resolving_src = src;\n\t\tresolve_wasm_src(resolving_src).then((s) => {\n\t\t\tif (latest_src === resolving_src) {\n\t\t\t\tresolved_src = s;\n\t\t\t}\n\t\t});\n\t}\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<audio\n\tsrc={resolved_src}\n\t{...$$restProps}\n\ton:play={dispatch.bind(null, \"play\")}\n\ton:pause={dispatch.bind(null, \"pause\")}\n\ton:ended={dispatch.bind(null, \"ended\")}\n/>\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\n\tlet copied = false;\n\texport let value: string;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t} else {\n\t\t\tconst textArea = document.createElement(\"textarea\");\n\t\t\ttextArea.value = value;\n\n\t\t\ttextArea.style.position = \"absolute\";\n\t\t\ttextArea.style.left = \"-999999px\";\n\n\t\t\tdocument.body.prepend(textArea);\n\t\t\ttextArea.select();\n\n\t\t\ttry {\n\t\t\t\tdocument.execCommand(\"copy\");\n\t\t\t\tcopy_feedback();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(error);\n\t\t\t} finally {\n\t\t\t\ttextArea.remove();\n\t\t\t}\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<button\n\ton:click={handle_copy}\n\tclass=\"action\"\n\ttitle=\"copy\"\n\taria-label={copied ? \"Copied message\" : \"Copy message\"}\n>\n\t{#if !copied}\n\t\t<Copy />\n\t{/if}\n\t{#if copied}\n\t\t<Check />\n\t{/if}\n</button>\n\n<style>\n\tbutton {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tmargin-right: 5px;\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.action {\n\t\twidth: 15px;\n\t\theight: 14px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Like } from \"@gradio/icons\";\n\timport { Dislike } from \"@gradio/icons\";\n\n\texport let handle_action: (selected: string | null) => void;\n\n\tlet selected: \"like\" | \"dislike\" | null = null;\n</script>\n\n<button\n\ton:click={() => {\n\t\tselected = \"like\";\n\t\thandle_action(selected);\n\t}}\n\taria-label={selected === \"like\" ? \"clicked like\" : \"like\"}\n>\n\t<Like selected={selected === \"like\"} />\n</button>\n\n<button\n\ton:click={() => {\n\t\tselected = \"dislike\";\n\t\thandle_action(selected);\n\t}}\n\taria-label={selected === \"dislike\" ? \"clicked dislike\" : \"dislike\"}\n>\n\t<Dislike selected={selected === \"dislike\"} />\n</button>\n\n<style>\n\tbutton {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t\twidth: 17px;\n\t\theight: 17px;\n\t\tmargin-right: 5px;\n\t}\n\n\tbutton:hover,\n\tbutton:focus {\n\t\tcolor: var(--body-text-color);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let layout = \"bubble\";\n</script>\n\n<div\n\tclass=\"message pending\"\n\trole=\"status\"\n\taria-label=\"Loading response\"\n\taria-live=\"polite\"\n\tstyle:border-radius={layout === \"bubble\" ? \"var(--radius-xxl)\" : \"none\"}\n>\n\t<span class=\"sr-only\">Loading content</span>\n\t<div class=\"dot-flashing\" />\n\t&nbsp;\n\t<div class=\"dot-flashing\" />\n\t&nbsp;\n\t<div class=\"dot-flashing\" />\n</div>\n\n<style>\n\t.pending {\n\t\tbackground: var(--color-accent-soft);\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\talign-self: center;\n\t\tgap: 2px;\n\t\twidth: 100%;\n\t\theight: var(--size-16);\n\t}\n\t.dot-flashing {\n\t\tanimation: flash 1s infinite ease-in-out;\n\t\tborder-radius: 5px;\n\t\tbackground-color: var(--body-text-color);\n\t\twidth: 7px;\n\t\theight: 7px;\n\t\tcolor: var(--body-text-color);\n\t}\n\t@keyframes flash {\n\t\t0%,\n\t\t100% {\n\t\t\topacity: 0;\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.dot-flashing:nth-child(1) {\n\t\tanimation-delay: 0s;\n\t}\n\n\t.dot-flashing:nth-child(2) {\n\t\tanimation-delay: 0.33s;\n\t}\n\t.dot-flashing:nth-child(3) {\n\t\tanimation-delay: 0.66s;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { format_chat_for_sharing } from \"./utils\";\n\timport { copy } from \"@gradio/utils\";\n\n\timport { dequal } from \"dequal/lite\";\n\timport { beforeUpdate, afterUpdate, createEventDispatcher } from \"svelte\";\n\timport { ShareButton } from \"@gradio/atoms\";\n\timport { Audio } from \"@gradio/audio/shared\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport { Video } from \"@gradio/video/shared\";\n\timport { Clear } from \"@gradio/icons\";\n\timport type { SelectData, LikeData } from \"@gradio/utils\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown\";\n\timport { type FileData } from \"@gradio/client\";\n\timport Copy from \"./Copy.svelte\";\n\timport type { I18nFormatter } from \"js/app/src/gradio_helper\";\n\timport LikeDislike from \"./LikeDislike.svelte\";\n\timport Pending from \"./Pending.svelte\";\n\n\texport let value:\n\t\t| [\n\t\t\t\tstring | { file: FileData; alt_text: string | null } | null,\n\t\t\t\tstring | { file: FileData; alt_text: string | null } | null\n\t\t  ][]\n\t\t| null;\n\tlet old_value:\n\t\t| [\n\t\t\t\tstring | { file: FileData; alt_text: string | null } | null,\n\t\t\t\tstring | { file: FileData; alt_text: string | null } | null\n\t\t  ][]\n\t\t| null = null;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let pending_message = false;\n\texport let selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let sanitize_html = true;\n\texport let bubble_full_width = true;\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let i18n: I18nFormatter;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let placeholder: string | null = null;\n\n\tlet div: HTMLDivElement;\n\tlet autoscroll: boolean;\n\n\t$: adjust_text_size = () => {\n\t\tlet style = getComputedStyle(document.body);\n\t\tlet body_text_size = style.getPropertyValue(\"--body-text-size\");\n\t\tlet updated_text_size;\n\n\t\tswitch (body_text_size) {\n\t\t\tcase \"13px\":\n\t\t\t\tupdated_text_size = 14;\n\t\t\t\tbreak;\n\t\t\tcase \"14px\":\n\t\t\t\tupdated_text_size = 16;\n\t\t\t\tbreak;\n\t\t\tcase \"16px\":\n\t\t\t\tupdated_text_size = 20;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tupdated_text_size = 14;\n\t\t\t\tbreak;\n\t\t}\n\n\t\tdocument.body.style.setProperty(\n\t\t\t\"--chatbot-body-text-size\",\n\t\t\tupdated_text_size + \"px\"\n\t\t);\n\t};\n\n\t$: adjust_text_size();\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t\tlike: LikeData;\n\t}>();\n\n\tbeforeUpdate(() => {\n\t\tautoscroll =\n\t\t\tdiv && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\n\t});\n\n\tconst scroll = (): void => {\n\t\tif (autoscroll) {\n\t\t\tdiv.scrollTo(0, div.scrollHeight);\n\t\t}\n\t};\n\n\tlet image_preview_source: string;\n\tlet image_preview_source_alt: string;\n\tlet is_image_preview_open = false;\n\tlet image_preview_close_button: HTMLButtonElement;\n\n\tafterUpdate(() => {\n\t\tif (autoscroll) {\n\t\t\tscroll();\n\t\t\tdiv.querySelectorAll(\"img\").forEach((n) => {\n\t\t\t\tn.addEventListener(\"load\", () => {\n\t\t\t\t\tscroll();\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t\tdiv.querySelectorAll(\"img\").forEach((n) => {\n\t\t\tn.addEventListener(\"click\", (e) => {\n\t\t\t\tconst target = e.target as HTMLImageElement;\n\t\t\t\tif (target) {\n\t\t\t\t\timage_preview_source = target.src;\n\t\t\t\t\timage_preview_source_alt = target.alt;\n\t\t\t\t\tis_image_preview_open = true;\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t});\n\n\t$: {\n\t\tif (!dequal(value, old_value)) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\");\n\t\t}\n\t}\n\n\tfunction handle_select(\n\t\ti: number,\n\t\tj: number,\n\t\tmessage: string | { file: FileData; alt_text: string | null } | null\n\t): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: [i, j],\n\t\t\tvalue: message\n\t\t});\n\t}\n\n\tfunction handle_like(\n\t\ti: number,\n\t\tj: number,\n\t\tmessage: string | { file: FileData; alt_text: string | null } | null,\n\t\tselected: string | null\n\t): void {\n\t\tdispatch(\"like\", {\n\t\t\tindex: [i, j],\n\t\t\tvalue: message,\n\t\t\tliked: selected === \"like\"\n\t\t});\n\t}\n</script>\n\n{#if show_share_button && value !== null && value.length > 0}\n\t<div class=\"share-button\">\n\t\t<ShareButton\n\t\t\t{i18n}\n\t\t\ton:error\n\t\t\ton:share\n\t\t\tformatter={format_chat_for_sharing}\n\t\t\t{value}\n\t\t/>\n\t</div>\n{/if}\n\n<div\n\tclass={layout === \"bubble\" ? \"bubble-wrap\" : \"panel-wrap\"}\n\tclass:placeholder-container={value === null || value.length === 0}\n\tbind:this={div}\n\trole=\"log\"\n\taria-label=\"chatbot conversation\"\n\taria-live=\"polite\"\n>\n\t<div class=\"message-wrap\" class:bubble-gap={layout === \"bubble\"} use:copy>\n\t\t{#if value !== null && value.length > 0}\n\t\t\t{#each value as message_pair, i}\n\t\t\t\t{#each message_pair as message, j}\n\t\t\t\t\t{#if message !== null}\n\t\t\t\t\t\t{#if is_image_preview_open}\n\t\t\t\t\t\t\t<div class=\"image-preview\">\n\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\tsrc={image_preview_source}\n\t\t\t\t\t\t\t\t\talt={image_preview_source_alt}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\tbind:this={image_preview_close_button}\n\t\t\t\t\t\t\t\t\tclass=\"image-preview-close-button\"\n\t\t\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\t\t\tis_image_preview_open = false;\n\t\t\t\t\t\t\t\t\t}}><Clear /></button\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t<div class=\"message-row {layout} {j == 0 ? 'user-row' : 'bot-row'}\">\n\t\t\t\t\t\t\t{#if avatar_images[j] !== null}\n\t\t\t\t\t\t\t\t<div class=\"avatar-container\">\n\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\tclass=\"avatar-image\"\n\t\t\t\t\t\t\t\t\t\tsrc={avatar_images[j]?.url}\n\t\t\t\t\t\t\t\t\t\talt=\"{j == 0 ? 'user' : 'bot'} avatar\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclass=\"message {j == 0 ? 'user' : 'bot'}\"\n\t\t\t\t\t\t\t\tclass:message-fit={layout === \"bubble\" && !bubble_full_width}\n\t\t\t\t\t\t\t\tclass:panel-full-width={layout === \"panel\"}\n\t\t\t\t\t\t\t\tclass:message-bubble-border={layout === \"bubble\"}\n\t\t\t\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\t\t\t\tstyle:text-align={rtl && j == 0 ? \"left\" : \"right\"}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\tdata-testid={j == 0 ? \"user\" : \"bot\"}\n\t\t\t\t\t\t\t\t\tclass:latest={i === value.length - 1}\n\t\t\t\t\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\t\t\t\t\tstyle:user-select=\"text\"\n\t\t\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\t\t\tstyle:text-align={rtl ? \"right\" : \"left\"}\n\t\t\t\t\t\t\t\t\ton:click={() => handle_select(i, j, message)}\n\t\t\t\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\t\t\t\tif (e.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\t\t\thandle_select(i, j, message);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\t\t\t\t\t\taria-label={(j == 0 ? \"user\" : \"bot\") +\n\t\t\t\t\t\t\t\t\t\t\"'s message: \" +\n\t\t\t\t\t\t\t\t\t\t(typeof message === \"string\"\n\t\t\t\t\t\t\t\t\t\t\t? message\n\t\t\t\t\t\t\t\t\t\t\t: `a file of type ${message.file?.mime_type}, ${\n\t\t\t\t\t\t\t\t\t\t\t\t\tmessage.file?.alt_text ??\n\t\t\t\t\t\t\t\t\t\t\t\t\tmessage.file?.orig_name ??\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\n\t\t\t\t\t\t\t\t\t\t\t\t}`)}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{#if typeof message === \"string\"}\n\t\t\t\t\t\t\t\t\t\t<Markdown\n\t\t\t\t\t\t\t\t\t\t\t{message}\n\t\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\t\ton:load={scroll}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{:else if message !== null && message.file?.mime_type?.includes(\"audio\")}\n\t\t\t\t\t\t\t\t\t\t<Audio\n\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-audio\"\n\t\t\t\t\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\t\t\t\t\tpreload=\"metadata\"\n\t\t\t\t\t\t\t\t\t\t\tsrc={message.file?.url}\n\t\t\t\t\t\t\t\t\t\t\ttitle={message.alt_text}\n\t\t\t\t\t\t\t\t\t\t\ton:play\n\t\t\t\t\t\t\t\t\t\t\ton:pause\n\t\t\t\t\t\t\t\t\t\t\ton:ended\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{:else if message !== null && message.file?.mime_type?.includes(\"video\")}\n\t\t\t\t\t\t\t\t\t\t<Video\n\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-video\"\n\t\t\t\t\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\t\t\t\t\tsrc={message.file?.url}\n\t\t\t\t\t\t\t\t\t\t\ttitle={message.alt_text}\n\t\t\t\t\t\t\t\t\t\t\tpreload=\"auto\"\n\t\t\t\t\t\t\t\t\t\t\ton:play\n\t\t\t\t\t\t\t\t\t\t\ton:pause\n\t\t\t\t\t\t\t\t\t\t\ton:ended\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<track kind=\"captions\" />\n\t\t\t\t\t\t\t\t\t\t</Video>\n\t\t\t\t\t\t\t\t\t{:else if message !== null && message.file?.mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-image\"\n\t\t\t\t\t\t\t\t\t\t\tsrc={message.file?.url}\n\t\t\t\t\t\t\t\t\t\t\talt={message.alt_text}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{:else if message !== null && message.file?.url !== null}\n\t\t\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-file\"\n\t\t\t\t\t\t\t\t\t\t\thref={message.file?.url}\n\t\t\t\t\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\t\t\t\t\tdownload={window.__is_colab__\n\t\t\t\t\t\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t\t\t\t\t\t: message.file?.orig_name || message.file?.path}\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t{message.file?.orig_name || message.file?.path}\n\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{#if (likeable && j !== 0) || (show_copy_button && message && typeof message === \"string\")}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"message-buttons-{j == 0\n\t\t\t\t\t\t\t\t\t\t? 'user'\n\t\t\t\t\t\t\t\t\t\t: 'bot'} message-buttons-{layout} {avatar_images[j] !==\n\t\t\t\t\t\t\t\t\t\tnull && 'with-avatar'}\"\n\t\t\t\t\t\t\t\t\tclass:message-buttons-fit={layout === \"bubble\" &&\n\t\t\t\t\t\t\t\t\t\t!bubble_full_width}\n\t\t\t\t\t\t\t\t\tclass:bubble-buttons-user={layout === \"bubble\"}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{#if likeable && j == 1}\n\t\t\t\t\t\t\t\t\t\t<LikeDislike\n\t\t\t\t\t\t\t\t\t\t\thandle_action={(selected) =>\n\t\t\t\t\t\t\t\t\t\t\t\thandle_like(i, j, message, selected)}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t{#if show_copy_button && message && typeof message === \"string\"}\n\t\t\t\t\t\t\t\t\t\t<Copy value={message} />\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{/each}\n\t\t\t{#if pending_message}\n\t\t\t\t<Pending {layout} />\n\t\t\t{/if}\n\t\t{:else if placeholder !== null}\n\t\t\t<center>\n\t\t\t\t<Markdown message={placeholder} {latex_delimiters} />\n\t\t\t</center>\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.placeholder-container {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 100%;\n\t}\n\t.bubble-wrap {\n\t\tpadding: var(--block-padding);\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.panel-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t}\n\n\t.bubble-gap {\n\t\tgap: calc(var(--spacing-xxl) + var(--spacing-lg));\n\t}\n\n\t.message-wrap > div :not(.avatar-container) :global(img) {\n\t\tborder-radius: 13px;\n\t\tmargin: var(--size-2);\n\t\twidth: 400px;\n\t\tmax-width: 30vw;\n\t\tmax-height: auto;\n\t}\n\n\t.message-wrap > div :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.message {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-self: flex-end;\n\t\tbackground: var(--background-fill-secondary);\n\t\twidth: calc(100% - var(--spacing-xxl));\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-body-text-size);\n\t\toverflow-wrap: break-word;\n\t\toverflow-x: hidden;\n\t\tpadding-right: calc(var(--spacing-xxl) + var(--spacing-md));\n\t\tpadding: calc(var(--spacing-xxl) + var(--spacing-sm));\n\t}\n\t.message :global(.prose) {\n\t\tfont-size: var(--chatbot-body-text-size);\n\t}\n\n\t.message-bubble-border {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-xxl);\n\t}\n\n\t.message-fit {\n\t\twidth: fit-content !important;\n\t}\n\n\t.panel-full-width {\n\t\tpadding: calc(var(--spacing-xxl) * 2);\n\t\twidth: 100%;\n\t}\n\t.message-markdown-disabled {\n\t\twhite-space: pre-line;\n\t}\n\n\t@media (max-width: 480px) {\n\t\t.panel-full-width {\n\t\t\tpadding: calc(var(--spacing-xxl) * 2);\n\t\t}\n\t}\n\n\t.user {\n\t\talign-self: flex-start;\n\t\tborder-bottom-right-radius: 0;\n\t\ttext-align: right;\n\t}\n\t.bot {\n\t\tborder-bottom-left-radius: 0;\n\t\ttext-align: left;\n\t}\n\n\t/* Colors */\n\t.bot {\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.user {\n\t\tborder-color: var(--border-color-accent-subdued);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\t.message-row {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tposition: relative;\n\t}\n\n\t.message-row.panel.user-row {\n\t\tbackground: var(--color-accent-soft);\n\t}\n\n\t.message-row.panel.bot-row {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.message-row:last-of-type {\n\t\tmargin-bottom: var(--spacing-xxl);\n\t}\n\n\t.user-row.bubble {\n\t\tflex-direction: row;\n\t\tjustify-content: flex-end;\n\t}\n\t@media (max-width: 480px) {\n\t\t.user-row.bubble {\n\t\t\talign-self: flex-end;\n\t\t}\n\n\t\t.bot-row.bubble {\n\t\t\talign-self: flex-start;\n\t\t}\n\t\t.message {\n\t\t\twidth: auto;\n\t\t}\n\t}\n\t.avatar-container {\n\t\talign-self: flex-end;\n\t\tposition: relative;\n\t\tjustify-content: center;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t}\n\t.user-row.bubble > .avatar-container {\n\t\torder: 2;\n\t\tmargin-left: 10px;\n\t}\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-right: 10px;\n\t}\n\n\t.panel > .avatar-container {\n\t\tmargin-left: 25px;\n\t\talign-self: center;\n\t}\n\n\t.avatar-container :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t}\n\n\t.message-buttons-user,\n\t.message-buttons-bot {\n\t\tborder-radius: var(--radius-md);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbottom: 0;\n\t\theight: var(--size-7);\n\t\talign-self: self-end;\n\t\tposition: absolute;\n\t\tbottom: -15px;\n\t\tmargin: 2px;\n\t\tpadding-left: 5px;\n\t\tz-index: 1;\n\t}\n\t.message-buttons-bot {\n\t\tleft: 10px;\n\t}\n\t.message-buttons-user {\n\t\tright: 5px;\n\t}\n\n\t.message-buttons-bot.message-buttons-bubble.with-avatar {\n\t\tleft: 50px;\n\t}\n\t.message-buttons-user.message-buttons-bubble.with-avatar {\n\t\tright: 50px;\n\t}\n\n\t.message-buttons-bubble {\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.message-buttons-panel {\n\t\tleft: unset;\n\t\tright: 0px;\n\t\ttop: 0px;\n\t}\n\n\t.share-button {\n\t\tposition: absolute;\n\t\ttop: 4px;\n\t\tright: 6px;\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\t@keyframes dot-flashing {\n\t\t0% {\n\t\t\topacity: 0.8;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\n\t/* Copy button */\n\t.message-wrap :global(div[class*=\"code_wrap\"] > button) {\n\t\tposition: absolute;\n\t\ttop: var(--spacing-md);\n\t\tright: var(--spacing-md);\n\t\tz-index: 1;\n\t\tcursor: pointer;\n\t\tborder-bottom-left-radius: var(--radius-sm);\n\t\tpadding: 5px;\n\t\tpadding: var(--spacing-md);\n\t\twidth: 25px;\n\t\theight: 25px;\n\t}\n\n\t.message-wrap :global(code > button > span) {\n\t\tposition: absolute;\n\t\ttop: var(--spacing-md);\n\t\tright: var(--spacing-md);\n\t\twidth: 12px;\n\t\theight: 12px;\n\t}\n\t.message-wrap :global(.check) {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\topacity: 0;\n\t\tz-index: var(--layer-top);\n\t\ttransition: opacity 0.2s;\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-1);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t/* Image preview */\n\t.message :global(.preview) {\n\t\tobject-fit: contain;\n\t\twidth: 95%;\n\t\tmax-height: 93%;\n\t}\n\t.image-preview {\n\t\tposition: absolute;\n\t\tz-index: 999;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: auto;\n\t\tbackground-color: rgba(0, 0, 0, 0.9);\n\t}\n\t.image-preview :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: contain;\n\t}\n\t.image-preview :global(svg) {\n\t\tstroke: white;\n\t}\n\t.image-preview-close-button {\n\t\tposition: absolute;\n\t\ttop: 10px;\n\t\tright: 10px;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tfont-size: 1.5em;\n\t\tcursor: pointer;\n\t\theight: 30px;\n\t\twidth: 30px;\n\t\tpadding: 3px;\n\t\tbackground: var(--bg-color);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--radius-lg);\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseChatBot } from \"./shared/ChatBot.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, LikeData } from \"@gradio/utils\";\n\n\timport ChatBot from \"./shared/ChatBot.svelte\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Chat } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [\n\t\tstring | { file: FileData; alt_text: string | null } | null,\n\t\tstring | { file: FileData; alt_text: string | null } | null\n\t][] = [];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string;\n\texport let show_label = true;\n\texport let root: string;\n\texport let _selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let sanitize_html = true;\n\texport let bubble_full_width = true;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tlike: LikeData;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\n\tlet _value: [\n\t\tstring | { file: FileData; alt_text: string | null } | null,\n\t\tstring | { file: FileData; alt_text: string | null } | null\n\t][];\n\n\tconst redirect_src_url = (src: string): string =>\n\t\tsrc.replace('src=\"/file', `src=\"${root}file`);\n\n\tfunction normalize_messages(\n\t\tmessage: { file: FileData; alt_text: string | null } | null\n\t): { file: FileData; alt_text: string | null } | null {\n\t\tif (message === null) {\n\t\t\treturn message;\n\t\t}\n\t\treturn {\n\t\t\tfile: message?.file as FileData,\n\t\t\talt_text: message?.alt_text\n\t\t};\n\t}\n\n\t$: _value = value\n\t\t? value.map(([user_msg, bot_msg]) => [\n\t\t\t\ttypeof user_msg === \"string\"\n\t\t\t\t\t? redirect_src_url(user_msg)\n\t\t\t\t\t: normalize_messages(user_msg),\n\t\t\t\ttypeof bot_msg === \"string\"\n\t\t\t\t\t? redirect_src_url(bot_msg)\n\t\t\t\t\t: normalize_messages(bot_msg)\n\t\t\t])\n\t\t: [];\n\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height = 400;\n\texport let placeholder: string | null = null;\n</script>\n\n<Block\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\tpadding={false}\n\t{scale}\n\t{min_width}\n\t{height}\n\tallow_overflow={false}\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\tshow_progress={loading_status.show_progress === \"hidden\"\n\t\t\t\t? \"hidden\"\n\t\t\t\t: \"minimal\"}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\t<div class=\"wrapper\">\n\t\t{#if show_label}\n\t\t\t<BlockLabel\n\t\t\t\t{show_label}\n\t\t\t\tIcon={Chat}\n\t\t\t\tfloat={false}\n\t\t\t\tlabel={label || \"Chatbot\"}\n\t\t\t/>\n\t\t{/if}\n\t\t<ChatBot\n\t\t\ti18n={gradio.i18n}\n\t\t\tselectable={_selectable}\n\t\t\t{likeable}\n\t\t\t{show_share_button}\n\t\t\tvalue={_value}\n\t\t\t{latex_delimiters}\n\t\t\t{render_markdown}\n\t\t\tpending_message={loading_status?.status === \"pending\"}\n\t\t\t{rtl}\n\t\t\t{show_copy_button}\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:like={(e) => gradio.dispatch(\"like\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\t{avatar_images}\n\t\t\t{sanitize_html}\n\t\t\t{bubble_full_width}\n\t\t\t{line_breaks}\n\t\t\t{layout}\n\t\t\t{placeholder}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\talign-items: start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "ctx", "selected", "$$props", "format_chat_for_sharing", "chat", "message_pair", "message", "speaker_emoji", "html_content", "regexPatterns", "_", "regex", "match", "fileUrl", "newUrl", "uploadToHuggingFace", "file_url", "createEventDispatcher", "audio", "listen", "audio_src_value", "src", "resolved_src", "latest_src", "dispatch", "$$invalidate", "resolving_src", "resolve_wasm_src", "s", "onDestroy", "create_if_block_1", "create_if_block", "button", "copied", "value", "timer", "copy_feedback", "handle_copy", "textArea", "error", "attr", "button0", "button0_aria_label_value", "button1", "button1_aria_label_value", "dirty", "like_changes", "current", "dislike_changes", "handle_action", "set_style", "div3", "layout", "beforeUpdate", "afterUpdate", "div_1", "center", "i", "each_blocks", "create_if_block_12", "create_if_block_11", "create_if_block_3", "toggle_class", "div0", "div1", "div1_class_value", "img", "img_src_value", "a", "a_download_value", "set_data", "t", "t_value", "image_changes", "video_changes", "audio_changes", "track", "create_if_block_5", "if_block1", "create_if_block_4", "div_1_class_value", "if_block", "create_if_block_2", "create_if_block_14", "null_to_empty", "old_value", "latex_delimiters", "pending_message", "selectable", "likeable", "show_share_button", "rtl", "show_copy_button", "avatar_images", "sanitize_html", "bubble_full_width", "render_markdown", "line_breaks", "i18n", "placeholder", "div", "autoscroll", "scroll", "image_preview_source", "image_preview_source_alt", "is_image_preview_open", "image_preview_close_button", "n", "e", "handle_select", "j", "handle_like", "$$value", "click_handler_1", "adjust_text_size", "dequal", "body_text_size", "updated_text_size", "Cha<PERSON>", "blocklabel_changes", "chatbot_changes", "normalize_messages", "elem_id", "elem_classes", "visible", "scale", "min_width", "label", "show_label", "root", "_selectable", "gradio", "_value", "redirect_src_url", "loading_status", "height", "clear_status_handler", "change_handler", "user_msg", "bot_msg"], "mappings": "y8CAAAA,GAgBKC,EAAAC,EAAAC,CAAA,EALJC,GAGCF,EAAAG,CAAA,EACDD,GAAyDF,EAAAI,CAAA,61BCRnDC,EAAQ,CAAA,EAAG,eAAiB,MAAM,gEAHzCP,GAkBAC,EAAAC,EAAAC,CAAA,EAZEC,GAKCF,EAAAG,CAAA,EAAAD,GAMAF,EAAAI,CAAA,wBAdIC,EAAQ,CAAA,EAAG,eAAiB,yEANvB,SAAAC,CAAiB,EAAAC,88BCMtBF,EAAQ,CAAA,EAAG,eAAiB,MAAM,gEAHzCP,GAkBAC,EAAAC,EAAAC,CAAA,EAZEC,GAKCF,EAAAG,CAAA,EAAAD,GAMAF,EAAAI,CAAA,wBAdIC,EAAQ,CAAA,EAAG,eAAiB,yEANvB,SAAAC,CAAiB,EAAAC,4NCEhB,MAAAC,GAA0B,MACtCC,IAEe,MAAM,QAAQ,IAC5BA,EAAK,IAAI,MAAOC,GACR,MAAM,QAAQ,IACpBA,EAAa,IAAI,MAAOC,EAAS,IAAM,CACtC,GAAIA,IAAY,KAAa,MAAA,GACzB,IAAAC,EAAgB,IAAM,EAAI,KAAO,KACjCC,EAAe,GAEf,GAAA,OAAOF,GAAY,SAAU,CAChC,MAAMG,EAAgB,CACrB,MAAO,+BACP,MAAO,+BACP,MAAO,2DAAA,EAGOD,EAAAF,EAEf,OAAS,CAACI,EAAGC,CAAK,IAAK,OAAO,QAAQF,CAAa,EAAG,CACjD,IAAAG,EAEJ,MAAQA,EAAQD,EAAM,KAAKL,CAAO,KAAO,MAAM,CAC9C,MAAMO,EAAUD,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC7BE,EAAS,MAAMC,GAAoBF,EAAS,KAAK,EACxCL,EAAAA,EAAa,QAAQK,EAASC,CAAM,CACpD,CACD,CAAA,KACM,CACN,GAAI,CAACR,GAAS,IAAY,MAAA,GAC1B,MAAMU,EAAW,MAAMD,GAAoBT,EAAQ,IAAK,KAAK,EACzDA,EAAQ,WAAW,SAAS,OAAO,EACtCE,EAAe,wBAAwBQ,CAAQ,aACrCV,EAAQ,WAAW,SAAS,OAAO,EAC9BE,EAAAQ,EACLV,EAAQ,WAAW,SAAS,OAAO,IAC7CE,EAAe,aAAaQ,CAAQ,OAEtC,CAEO,MAAA,GAAGT,CAAa,KAAKC,CAAY,EAAA,CACxC,CAAA,CAEF,CAAA,GAGA,IAAKH,GACLA,EAAa,KACZA,EAAa,CAAC,IAAM,IAAMA,EAAa,CAAC,IAAM,GAAK;AAAA,EAAO,EAC3D,CAAA,EAEA,KAAK;AAAA,CAAI,0QCrDF,uBAAAY,EAAA,SAAqC,gEAqCzCjB,EAAY,CAAA,GACbA,EAAW,CAAA,0FAFhBP,GAMCC,EAAAwB,EAAAtB,CAAA,SAHSuB,GAAAD,EAAA,OAAAlB,EAAS,CAAA,EAAA,KAAK,KAAM,MAAM,CAAA,EACzBmB,GAAAD,EAAA,QAAAlB,EAAS,CAAA,EAAA,KAAK,KAAM,OAAO,CAAA,EAC3BmB,GAAAD,EAAA,QAAAlB,EAAS,CAAA,EAAA,KAAK,KAAM,OAAO,CAAA,iDAJhCA,EAAY,CAAA,CAAA,GAAA,CAAA,IAAAoB,CAAA,OACbpB,EAAW,CAAA,4FA9BJ,CAAA,IAAAqB,EAAkC,MAAS,EAAAnB,EAElDoB,EAKAC,EAkBE,MAAAC,EAAWP,uHAjBhB,CAMAQ,EAAA,EAAAH,EAAeD,CAAG,EAElBI,EAAA,EAAAF,EAAaF,CAAG,EACV,MAAAK,EAAgBL,EACtBM,GAAiBD,CAAa,EAAE,KAAME,GAAC,CAClCL,IAAeG,GAClBD,EAAA,EAAAH,EAAeM,CAAC,+bC7BV,CAAA,UAAAC,EAAA,SAAyB,mZAmD5B7B,EAAM,CAAA,GAAA8B,GAAA,IAGP9B,EAAM,CAAA,GAAA+B,GAAA,mIALC/B,EAAM,CAAA,EAAG,iBAAmB,cAAc,UAJvDP,GAYQC,EAAAsC,EAAApC,CAAA,gEAXGI,EAAW,CAAA,CAAA,kBAKfA,EAAM,CAAA,qFAGPA,EAAM,CAAA,yGALCA,EAAM,CAAA,EAAG,iBAAmB,uJA9CpC,IAAAiC,EAAS,IACF,MAAAC,CAAa,EAAAhC,EACpBiC,WAEKC,GAAa,CACrBX,EAAA,EAAAQ,EAAS,EAAI,EACTE,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACPV,EAAA,EAAAQ,EAAS,EAAK,GACZ,oBAGWI,GAAW,CACrB,GAAA,cAAe,UACZ,MAAA,UAAU,UAAU,UAAUH,CAAK,EACzCE,SAEM,MAAAE,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQJ,EAEjBI,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEtB,SAAS,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAM,MAGd,SAAS,YAAY,MAAM,EAC3BF,UACQG,EAAK,CACb,QAAQ,MAAMA,CAAK,UAEnBD,EAAS,OAAM,IAKlB,OAAAT,GAAS,IAAA,CACJM,GAAO,aAAaA,CAAK,ogBC1Bd,MAAA,CAAA,SAAAnC,OAAa,MAAM,qBAUhB,SAAAA,OAAa,2FAZpBwC,GAAAC,EAAA,aAAAC,EAAA1C,EAAa,CAAA,IAAA,OAAS,eAAiB,MAAM,gCAU7CwC,GAAAG,EAAA,aAAAC,EAAA5C,OAAa,UAAY,kBAAoB,SAAS,wCAfnEP,GAQQC,EAAA+C,EAAA7C,CAAA,yBAERH,GAQQC,EAAAiD,EAAA/C,CAAA,6FAXSiD,EAAA,IAAAC,EAAA,SAAA9C,OAAa,mBAFjB,CAAA+C,GAAAF,EAAA,GAAAH,KAAAA,EAAA1C,EAAa,CAAA,IAAA,OAAS,eAAiB,0CAYhC6C,EAAA,IAAAG,EAAA,SAAAhD,OAAa,sBAFpB,CAAA+C,GAAAF,EAAA,GAAAD,KAAAA,EAAA5C,OAAa,UAAY,kBAAoB,0NApB9C,cAAAiD,CAAgD,EAAA/C,EAEvDD,EAAsC,kBAKzCwB,EAAA,EAAAxB,EAAW,MAAM,EACjBgD,EAAchD,CAAQ,UAStBwB,EAAA,EAAAxB,EAAW,SAAS,EACpBgD,EAAchD,CAAQ;;;;wLCbFiD,GAAAC,EAAA,gBAAAnD,OAAW,SAAW,oBAAsB,MAAM,UALxEP,GAaKC,EAAAyD,EAAAvD,CAAA,iBARiBsD,GAAAC,EAAA,gBAAAnD,OAAW,SAAW,oBAAsB,MAAM,gDAR5D,GAAA,CAAA,OAAAoD,EAAS,QAAQ,EAAAlD,0qBCInB,CAAA,aAAAmD,GAAc,YAAAC,GAAa,sBAAArC,EAAA,SAAqC,mOA8J5Dd,+IALbV,EAQKC,EAAA6D,EAAA3D,CAAA,gPA6JiBI,EAAW,EAAA,qGAD/BP,EAEQC,EAAA8D,EAAA5D,CAAA,6DADYI,EAAW,EAAA,mKAhJxBA,EAAK,CAAA,CAAA,uBAAV,OAAIyD,GAAA,kEA2IDzD,EAAe,CAAA,GAAA8B,GAAA9B,CAAA,iMA3IbA,EAAK,CAAA,CAAA,oBAAV,OAAIyD,GAAA,EAAA,mHAAJ,OAAIA,EAAAC,EAAA,OAAAD,GAAA,WA2IDzD,EAAe,CAAA,yIA3IlB,OAAIyD,GAAA,6LAGEzD,EAAqB,EAAA,GAAA2D,GAAA3D,CAAA,IAgBpBA,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,IAAM,MAAI4D,GAAA5D,CAAA,gDA0ChB,0DAAA,OAAAA,OAAY,SAAQ,kBAStBA,EAAO,EAAA,IAAK,MAAQA,EAAO,EAAA,EAAC,MAAM,WAAW,SAAS,OAAO,wBAW7DA,EAAO,EAAA,IAAK,MAAQA,EAAO,EAAA,EAAC,MAAM,WAAW,SAAS,OAAO,wBAa7DA,EAAO,EAAA,IAAK,MAAQA,EAAO,EAAA,EAAC,MAAM,WAAW,SAAS,OAAO,QAM7DA,EAAO,EAAA,IAAK,MAAQA,MAAQ,MAAM,MAAQ,KAAI,sJAcpDA,EAAQ,CAAA,GAAIA,EAAC,EAAA,IAAK,GAAOA,EAAgB,CAAA,GAAIA,EAAO,EAAA,GAAA,OAAWA,EAAO,EAAA,GAAK,WAAQ6D,GAAA7D,CAAA,0GA5E1EwC,EAAAR,EAAA,cAAAhC,EAAK,EAAA,GAAA,EAAI,OAAS,KAAK,cAY/BA,EAAG,CAAA,EAAG,MAAQ,KAAK,sBACXA,EAAC,EAAA,GAAI,EAAI,OAAS,OAC9B,gBAAc,OACNA,EAAO,EAAA,GAAK,SACjBA,EAAA,EAAA,EACkB,kBAAAA,MAAQ,MAAM,SAAS,KACzCA,EAAQ,EAAA,EAAA,MAAM,UACdA,EAAO,EAAA,EAAC,MAAM,WACd,EAAC,GAAA,gCAnBS8D,EAAA9B,EAAA,SAAAhC,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mCACFA,EAAe,EAAA,CAAA,sEAG/BA,EAAG,CAAA,EAAG,QAAU,MAAM,EAbzBwC,EAAAuB,EAAA,QAAA,YAAA/D,EAAK,EAAA,GAAA,EAAI,OAAS,OAAK,iBAAA,oBACpBA,EAAM,EAAA,IAAK,UAAQ,CAAKA,EAAiB,EAAA,CAAA,EACpC8D,EAAAC,EAAA,mBAAA/D,QAAW,OAAO,EACb8D,EAAAC,EAAA,wBAAA/D,QAAW,QAAQ,mCACdA,EAAe,EAAA,CAAA,EAC/BkD,GAAAa,EAAA,aAAA/D,MAAOA,EAAC,EAAA,GAAI,EAAI,OAAS,OAAO,EAjB3BwC,EAAAwB,EAAA,QAAAC,EAAA,eAAAjE,WAASA,EAAC,EAAA,GAAI,EAAI,WAAa,WAAS,iBAAA,+BAAjEP,EAqHKC,EAAAsE,EAAApE,CAAA,wBA1GJC,EAoFKmE,EAAAD,CAAA,EA5EJlE,EA2EQkE,EAAA/B,CAAA,4GA7GLhC,EAAqB,EAAA,iHAgBpBA,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,IAAM,qRA+BnBA,EAAG,CAAA,EAAG,MAAQ,2CACNA,EAAC,EAAA,GAAI,EAAI,OAAS,OAC9B,gBAAc,OACNA,EAAO,EAAA,GAAK,SACjBA,EAAA,EAAA,EACkB,kBAAAA,MAAQ,MAAM,SAAS,KACzCA,EAAQ,EAAA,EAAA,MAAM,UACdA,EAAO,EAAA,EAAC,MAAM,WACd,EAAC,yCAnBS8D,EAAA9B,EAAA,SAAAhC,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,oDACFA,EAAe,EAAA,CAAA,kEAG/BA,EAAG,CAAA,EAAG,QAAU,MAAM,sCAZtBA,EAAM,EAAA,IAAK,UAAQ,CAAKA,EAAiB,EAAA,CAAA,oBACpC8D,EAAAC,EAAA,mBAAA/D,QAAW,OAAO,oBACb8D,EAAAC,EAAA,wBAAA/D,QAAW,QAAQ,oDACdA,EAAe,EAAA,CAAA,WAC/BkD,GAAAa,EAAA,aAAA/D,MAAOA,EAAC,EAAA,GAAI,EAAI,OAAS,OAAO,EA+E7CA,EAAQ,CAAA,GAAIA,EAAC,EAAA,IAAK,GAAOA,EAAgB,CAAA,GAAIA,EAAO,EAAA,GAAA,OAAWA,EAAO,EAAA,GAAK,8GAhGzD,CAAA+C,GAAAF,EAAA,CAAA,EAAA,OAAAoB,KAAAA,EAAA,eAAAjE,WAASA,EAAC,EAAA,GAAI,EAAI,WAAa,WAAS,uTAZzDA,EAAoB,EAAA,CAAA,GAAAwC,EAAA0B,EAAA,MAAAC,CAAA,YACpBnE,EAAwB,EAAA,CAAA,6IAH/BP,EAYKC,EAAA6D,EAAA3D,CAAA,EAXJC,EAGC0D,EAAAW,CAAA,SACDrE,EAMA0D,EAAAvB,CAAA,kGATMhC,EAAoB,EAAA,CAAA,8CACpBA,EAAwB,EAAA,CAAA,uLAgBvBA,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,GAAG,IACjB,KAAAA,EAAK,EAAA,GAAA,EAAI,OAAS,OAAK,qGAJ/BP,EAMKC,EAAA6D,EAAA3D,CAAA,uDAHEI,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,GAAG,6HAsFrBA,EAAO,EAAA,EAAC,MAAM,WAAaA,EAAO,EAAA,EAAC,MAAM,MAAI,oFANxCA,EAAO,EAAA,EAAC,MAAM,GAAG,yBAEbwC,EAAA4B,EAAA,WAAAC,EAAA,OAAO,aACd,KACArE,EAAO,EAAA,EAAC,MAAM,WAAaA,EAAO,EAAA,EAAC,MAAM,IAAI,wCANjDP,EASGC,EAAA0E,EAAAxE,CAAA,iCADDI,EAAO,EAAA,EAAC,MAAM,WAAaA,EAAO,EAAA,EAAC,MAAM,MAAI,KAAAsE,GAAAC,EAAAC,CAAA,iBANxCxE,EAAO,EAAA,EAAC,MAAM,oBAEV6C,EAAA,CAAA,EAAA,GAAAwB,KAAAA,EAAA,OAAO,aACd,KACArE,EAAO,EAAA,EAAC,MAAM,WAAaA,EAAO,EAAA,EAAC,MAAM,oIAVvCA,EAAO,EAAA,EAAC,MAAM,IACd,IAAAA,MAAQ,0FADRA,EAAO,EAAA,EAAC,MAAM,KACd6C,EAAA,CAAA,EAAA,IAAA4B,EAAA,IAAAzE,MAAQ,iMAbRA,EAAO,EAAA,EAAC,MAAM,IACZ,MAAAA,MAAQ,8MADVA,EAAO,EAAA,EAAC,MAAM,KACZ6C,EAAA,CAAA,EAAA,IAAA6B,EAAA,MAAA1E,MAAQ,6PAXVA,EAAO,EAAA,EAAC,MAAM,IACZ,MAAAA,MAAQ,wJADVA,EAAO,EAAA,EAAC,MAAM,KACZ6C,EAAA,CAAA,EAAA,IAAA8B,EAAA,MAAA3E,MAAQ,kQARNA,EAAM,EAAA,CAAA,waAwBfP,EAAwBC,EAAAkF,EAAAhF,CAAA,oDAgCrBI,EAAQ,CAAA,GAAIA,EAAC,EAAA,GAAI,GAAC6E,GAAA7E,CAAA,EAMlB8E,EAAA9E,EAAoB,CAAA,GAAAA,EAAkB,EAAA,GAAA,OAAAA,OAAY,UAAQ+E,GAAA/E,CAAA,iDAdvCwC,EAAAe,EAAA,QAAAyB,EAAA,oBAAAhF,EAAK,EAAA,GAAA,EAC1B,OACA,OAAwB,oBAAAA,EAAS,EAAA,EAAA,KAAAA,EAAc,CAAA,EAAAA,EACjD,EAAA,CAAA,IAAA,MAAQ,eAAa,iBAAA,4BACKA,EAAM,EAAA,IAAK,UAAQ,CAC5CA,EAAiB,EAAA,CAAA,EACQ8D,EAAAP,EAAA,sBAAAvD,QAAW,QAAQ,UAP/CP,EAkBKC,EAAA6D,EAAA3D,CAAA,oDATCI,EAAQ,CAAA,GAAIA,EAAC,EAAA,GAAI,kGAMjBA,EAAoB,CAAA,GAAAA,EAAkB,EAAA,GAAA,OAAAA,OAAY,8GAd/B,CAAA+C,GAAAF,EAAA,CAAA,EAAA,OAAAmC,KAAAA,EAAA,oBAAAhF,EAAK,EAAA,GAAA,EAC1B,OACA,OAAwB,oBAAAA,EAAS,EAAA,EAAA,KAAAA,EAAc,CAAA,EAAAA,EACjD,EAAA,CAAA,IAAA,MAAQ,eAAa,gFACKA,EAAM,EAAA,IAAK,UAAQ,CAC5CA,EAAiB,EAAA,CAAA,oBACQ8D,EAAAP,EAAA,sBAAAvD,QAAW,QAAQ,ybAShCA,EAAO,EAAA,CAAA,CAAA,CAAA,kFAAPA,EAAO,EAAA,oHAjIpBiF,EAAAjF,QAAY,MAAIkF,GAAAlF,CAAA,uEAAhBA,QAAY,0MADXA,EAAY,EAAA,CAAA,uBAAjB,OAAIyD,GAAA,sNAACzD,EAAY,EAAA,CAAA,oBAAjB,OAAIyD,GAAA,EAAA,mHAAJ,OAAIA,EAAAC,EAAA,OAAAD,GAAA,yCAAJ,OAAIA,GAAA,uYAvBLzD,EAAiB,CAAA,GAAIA,EAAU,CAAA,IAAA,MAAQA,EAAK,CAAA,EAAC,OAAS,GAACmF,GAAAnF,CAAA,uCAqBrD,OAAAA,OAAU,MAAQA,EAAM,CAAA,EAAA,OAAS,EAAC,EA+I7BA,QAAgB,KAAI,sIAhJa8D,EAAAC,EAAA,aAAA/D,QAAW,QAAQ,EAPxDwC,EAAAwB,EAAA,QAAAC,EAAAmB,GAAApF,QAAW,SAAW,cAAgB,YAAY,EAAA,iBAAA,uFAC5B8D,EAAAE,EAAA,wBAAAhE,OAAU,MAAQA,EAAM,CAAA,EAAA,SAAW,CAAC,+BAFlEP,EA8JKC,EAAAsE,EAAApE,CAAA,EAtJJC,EAqJKmE,EAAAD,CAAA,2EAzKD/D,EAAiB,CAAA,GAAIA,EAAU,CAAA,IAAA,MAAQA,EAAK,CAAA,EAAC,OAAS,uRAoBd8D,EAAAC,EAAA,aAAA/D,QAAW,QAAQ,GAPxD,CAAA+C,GAAAF,EAAA,CAAA,EAAA,OAAAoB,KAAAA,EAAAmB,GAAApF,QAAW,SAAW,cAAgB,YAAY,EAAA,sDAC5B8D,EAAAE,EAAA,wBAAAhE,OAAU,MAAQA,EAAM,CAAA,EAAA,SAAW,CAAC,8IAxJtD,MAAAkC,CAKJ,EAAAhC,EACHmF,EAKM,MACC,iBAAAC,CAIR,EAAApF,EACQ,CAAA,gBAAAqF,EAAkB,EAAK,EAAArF,EACvB,CAAA,WAAAsF,EAAa,EAAK,EAAAtF,EAClB,CAAA,SAAAuF,EAAW,EAAK,EAAAvF,EAChB,CAAA,kBAAAwF,EAAoB,EAAK,EAAAxF,EACzB,CAAA,IAAAyF,EAAM,EAAK,EAAAzF,EACX,CAAA,iBAAA0F,EAAmB,EAAK,EAAA1F,GACxB,cAAA2F,EAAa,CAAwC,KAAM,IAAI,CAAA,EAAA3F,EAC/D,CAAA,cAAA4F,EAAgB,EAAI,EAAA5F,EACpB,CAAA,kBAAA6F,EAAoB,EAAI,EAAA7F,EACxB,CAAA,gBAAA8F,EAAkB,EAAI,EAAA9F,EACtB,CAAA,YAAA+F,EAAc,EAAI,EAAA/F,GAClB,KAAAgG,CAAmB,EAAAhG,EACnB,CAAA,OAAAkD,EAA6B,QAAQ,EAAAlD,EACrC,CAAA,YAAAiG,EAA6B,IAAI,EAAAjG,EAExCkG,EACAC,EA8BE,MAAA7E,EAAWP,KAMjBoC,GAAY,IAAA,CACXgD,EACCD,GAAOA,EAAI,aAAeA,EAAI,UAAYA,EAAI,aAAe,YAGzDE,EAAM,IAAA,CACPD,GACHD,EAAI,SAAS,EAAGA,EAAI,YAAY,OAI9BG,EACAC,EACAC,EAAwB,GACxBC,EAEJpD,GAAW,IAAA,CACN+C,IACHC,IACAF,EAAI,iBAAiB,KAAK,EAAE,QAASO,GAAC,CACrCA,EAAE,iBAAiB,OAAM,IAAA,CACxBL,SAIHF,EAAI,iBAAiB,KAAK,EAAE,QAASO,GAAC,CACrCA,EAAE,iBAAiB,QAAUC,GAAC,OACvBlH,EAASkH,EAAE,OACblH,SACH6G,EAAuB7G,EAAO,GAAG,OACjC8G,EAA2B9G,EAAO,GAAG,EACrC+B,EAAA,GAAAgF,EAAwB,EAAI,SAavB,SAAAI,GACRpD,EACAqD,EACAxG,EAAoE,CAEpEkB,EAAS,SAAQ,CAChB,MAAK,CAAGiC,EAAGqD,CAAC,EACZ,MAAOxG,CAAA,CAAA,EAIA,SAAAyG,GACRtD,EACAqD,EACAxG,EACAL,GAAuB,CAEvBuB,EAAS,OAAM,CACd,MAAK,CAAGiC,EAAGqD,CAAC,EACZ,MAAOxG,EACP,MAAOL,KAAa,oHAqCHyG,EAA0BM,0BAGpCvF,EAAA,GAAAgF,EAAwB,EAAK,kMA+Bd,MAAAQ,GAAA,CAAAxD,EAAAqD,EAAAxG,IAAAuG,GAAcpD,EAAGqD,EAAGxG,CAAO,YAC9BsG,KAAC,CACTA,GAAE,MAAQ,SACbC,GAAcpD,EAAGqD,EAAGxG,CAAO,aA+EXL,KACf8G,GAAYtD,EAAGqD,EAAGxG,EAASL,EAAQ,6CAtInCmG,EAAGY,utBA5FXE,6BA8CGC,GAAOjF,EAAOmD,CAAS,IAC3B5D,EAAA,GAAA4D,EAAYnD,CAAK,EACjBV,EAAS,QAAQ,UA1EhB0F,EAAgB,IAAA,CAEd,IAAAE,EADQ,iBAAiB,SAAS,IAAI,EACf,iBAAiB,kBAAkB,EAC1DC,SAEID,EAAc,KAChB,OACJC,EAAoB,aAEhB,OACJA,EAAoB,aAEhB,OACJA,EAAoB,iBAGpBA,EAAoB,SAItB,SAAS,KAAK,MAAM,YACnB,2BACAA,EAAoB,IAAI,ktECuBZ,WAAArH,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,oNALS,WAAAA,MAAO,YACb6C,EAAA,CAAA,EAAA,QAAA,CAAA,KAAA7C,MAAO,IAAI,mBACbA,EAAc,EAAA,CAAA,+BACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,gLAQIsH,SACC,GACA,MAAAtH,MAAS,4GAAT6C,EAAA,CAAA,EAAA,KAAA0E,EAAA,MAAAvH,MAAS,sIAjBdA,EAAc,EAAA,GAAA8B,GAAA9B,CAAA,IAYbA,EAAU,CAAA,GAAA+B,GAAA/B,CAAA,0BASR,KAAAA,MAAO,gBACDA,EAAW,CAAA,8CAGhBA,EAAM,EAAA,+DAGIA,EAAc,EAAA,GAAE,SAAW,gZAjB9CP,GAgCKC,EAAA0G,EAAAxG,CAAA,mDA3CAI,EAAc,EAAA,qHAYbA,EAAU,CAAA,iHASR6C,EAAA,CAAA,EAAA,SAAA2E,EAAA,KAAAxH,MAAO,8BACDA,EAAW,CAAA,8FAGhBA,EAAM,EAAA,kHAGIA,EAAc,EAAA,GAAE,SAAW,shBAlCrC,yDAIO,+ZApCP,SAAAyH,GACRnH,EAA2D,CAEvD,OAAAA,IAAY,KACRA,GAGP,KAAMA,GAAS,KACf,SAAUA,GAAS,6BArDV,GAAA,CAAA,QAAAoH,EAAU,EAAE,EAAAxH,GACZ,aAAAyH,EAAY,EAAA,EAAAzH,EACZ,CAAA,QAAA0H,EAAU,EAAI,EAAA1H,GACd,MAAAgC,EAAK,EAAA,EAAAhC,EAIL,CAAA,MAAA2H,EAAuB,IAAI,EAAA3H,EAC3B,CAAA,UAAA4H,EAAgC,MAAS,EAAA5H,GACzC,MAAA6H,CAAa,EAAA7H,EACb,CAAA,WAAA8H,EAAa,EAAI,EAAA9H,GACjB,KAAA+H,CAAY,EAAA/H,EACZ,CAAA,YAAAgI,EAAc,EAAK,EAAAhI,EACnB,CAAA,SAAAuF,EAAW,EAAK,EAAAvF,EAChB,CAAA,kBAAAwF,EAAoB,EAAK,EAAAxF,EACzB,CAAA,IAAAyF,EAAM,EAAK,EAAAzF,EACX,CAAA,iBAAA0F,EAAmB,EAAK,EAAA1F,EACxB,CAAA,cAAA4F,EAAgB,EAAI,EAAA5F,EACpB,CAAA,kBAAA6F,EAAoB,EAAI,EAAA7F,EACxB,CAAA,OAAAkD,EAA6B,QAAQ,EAAAlD,EACrC,CAAA,gBAAA8F,EAAkB,EAAI,EAAA9F,EACtB,CAAA,YAAA+F,EAAc,EAAI,EAAA/F,GAClB,iBAAAoF,CAIR,EAAApF,GACQ,OAAAiI,CAOT,EAAAjI,GACS,cAAA2F,EAAa,CAAwC,KAAM,IAAI,CAAA,EAAA3F,EAEtEkI,QAKEC,EAAoBhH,GACzBA,EAAI,QAAQ,aAAY,QAAU4G,CAAI,MAAA,EAyB5B,GAAA,CAAA,eAAAK,EAA4C,MAAS,EAAApI,EACrD,CAAA,OAAAqI,EAAS,GAAG,EAAArI,EACZ,CAAA,YAAAiG,GAA6B,IAAI,EAAAjG,EAqBnB,MAAAsI,GAAA,IAAAL,EAAO,SAAS,eAAgBG,CAAc,EAuBpDG,GAAA,IAAAN,EAAO,SAAS,SAAUjG,CAAK,KACpC0E,GAAMuB,EAAO,SAAS,SAAUvB,EAAE,MAAM,KAC1CA,GAAMuB,EAAO,SAAS,OAAQvB,EAAE,MAAM,KACrCA,GAAMuB,EAAO,SAAS,QAASvB,EAAE,MAAM,KACvCA,GAAMuB,EAAO,SAAS,QAASvB,EAAE,MAAM,w/BA7DnDnF,EAAA,GAAE2G,EAASlG,EACTA,EAAM,IAAM,CAAA,CAAAwG,EAAUC,EAAO,IAAA,CACtB,OAAAD,GAAa,SACjBL,EAAiBK,CAAQ,EACzBjB,GAAmBiB,CAAQ,EACvB,OAAAC,IAAY,SAChBN,EAAiBM,EAAO,EACxBlB,GAAmBkB,EAAO"}