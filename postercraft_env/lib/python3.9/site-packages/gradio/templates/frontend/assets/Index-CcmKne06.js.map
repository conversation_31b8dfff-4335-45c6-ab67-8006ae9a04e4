{"version": 3, "file": "Index-CcmKne06.js", "sources": ["../../../../js/box/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { Block } from \"@gradio/atoms\";\n\texport let elem_id: string;\n\texport let elem_classes: string[];\n\texport let visible = true;\n</script>\n\n<Block {elem_id} {elem_classes} {visible} explicit_call>\n\t<slot />\n</Block>\n"], "names": ["elem_id", "$$props", "elem_classes", "visible"], "mappings": "sjCAEY,QAAAA,CAAe,EAAAC,GACf,aAAAC,CAAsB,EAAAD,EACtB,CAAA,QAAAE,EAAU,EAAI,EAAAF"}