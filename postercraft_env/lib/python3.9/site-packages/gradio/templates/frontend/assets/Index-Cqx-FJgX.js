import{S as ie}from"./Index-WGC0_FkS.js";import{B as re}from"./Button-8nmImwVJ.js";import{B as oe}from"./BlockLabel-CJsotHlk.js";import{E as ce}from"./Empty-Vuj7-ssy.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";const{SvelteComponent:ue,append:fe,attr:k,detach:_e,init:de,insert:be,noop:R,safe_not_equal:me,svg_element:T}=window.__gradio__svelte__internal;function he(a){let e,l;return{c(){e=T("svg"),l=T("path"),k(l,"fill","currentColor"),k(l,"d","M4 2H2v26a2 2 0 0 0 2 2h26v-2H4v-3h22v-8H4v-4h14V5H4Zm20 17v4H4v-4ZM16 7v4H4V7Z"),k(e,"xmlns","http://www.w3.org/2000/svg"),k(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),k(e,"aria-hidden","true"),k(e,"role","img"),k(e,"class","iconify iconify--carbon"),k(e,"width","100%"),k(e,"height","100%"),k(e,"preserveAspectRatio","xMidYMid meet"),k(e,"viewBox","0 0 32 32")},m(t,n){be(t,e,n),fe(e,l)},p:R,i:R,o:R,d(t){t&&_e(e)}}}class ae extends ue{constructor(e){super(),de(this,e,null,he,me,{})}}const{SvelteComponent:ge,append:h,attr:d,destroy_each:ve,detach:K,element:C,empty:we,ensure_array_like:U,flush:Y,init:ke,insert:P,listen:$e,noop:W,safe_not_equal:pe,set_data:G,set_style:E,space:O,text:V,toggle_class:A}=window.__gradio__svelte__internal,{createEventDispatcher:Se}=window.__gradio__svelte__internal;function X(a,e,l){const t=a.slice();return t[5]=e[l],t[7]=l,t}function y(a){let e,l=U(a[0].confidences),t=[];for(let n=0;n<l.length;n+=1)t[n]=x(X(a,l,n));return{c(){for(let n=0;n<t.length;n+=1)t[n].c();e=we()},m(n,s){for(let i=0;i<t.length;i+=1)t[i]&&t[i].m(n,s);P(n,e,s)},p(n,s){if(s&13){l=U(n[0].confidences);let i;for(i=0;i<l.length;i+=1){const u=X(n,l,i);t[i]?t[i].p(u,s):(t[i]=x(u),t[i].c(),t[i].m(e.parentNode,e))}for(;i<t.length;i+=1)t[i].d(1);t.length=l.length}},d(n){n&&K(e),ve(t,n)}}}function x(a){let e,l,t,n,s,i,u,f,b,c,p=a[5].label+"",m,B,r,_,S,w=Math.round(a[5].confidence*100)+"",N,Z,o,I,D,Q;function se(){return a[4](a[7],a[5])}return{c(){e=C("button"),l=C("div"),t=C("meter"),f=O(),b=C("dl"),c=C("dt"),m=V(p),B=O(),_=C("div"),S=C("dd"),N=V(w),Z=V("%"),o=O(),d(t,"aria-labelledby",n=J(`meter-text-${a[5].label}`)),d(t,"aria-label",s=a[5].label),d(t,"aria-valuenow",i=Math.round(a[5].confidence*100)),d(t,"aria-valuemin","0"),d(t,"aria-valuemax","100"),d(t,"class","bar svelte-1l15rn0"),d(t,"min","0"),d(t,"max","1"),t.value=u=a[5].confidence,E(t,"width",a[5].confidence*100+"%"),E(t,"background","var(--stat-background-fill)"),d(c,"id",r=J(`meter-text-${a[5].label}`)),d(c,"class","text svelte-1l15rn0"),d(_,"class","line svelte-1l15rn0"),d(S,"class","confidence svelte-1l15rn0"),d(b,"class","label svelte-1l15rn0"),d(l,"class","inner-wrap svelte-1l15rn0"),d(e,"class","confidence-set group svelte-1l15rn0"),d(e,"data-testid",I=`${a[5].label}-confidence-set`),A(e,"selectable",a[2])},m(j,v){P(j,e,v),h(e,l),h(l,t),h(l,f),h(l,b),h(b,c),h(c,m),h(c,B),h(b,_),h(b,S),h(S,N),h(S,Z),h(e,o),D||(Q=$e(e,"click",se),D=!0)},p(j,v){a=j,v&1&&n!==(n=J(`meter-text-${a[5].label}`))&&d(t,"aria-labelledby",n),v&1&&s!==(s=a[5].label)&&d(t,"aria-label",s),v&1&&i!==(i=Math.round(a[5].confidence*100))&&d(t,"aria-valuenow",i),v&1&&u!==(u=a[5].confidence)&&(t.value=u),v&1&&E(t,"width",a[5].confidence*100+"%"),v&1&&p!==(p=a[5].label+"")&&G(m,p),v&1&&r!==(r=J(`meter-text-${a[5].label}`))&&d(c,"id",r),v&1&&w!==(w=Math.round(a[5].confidence*100)+"")&&G(N,w),v&1&&I!==(I=`${a[5].label}-confidence-set`)&&d(e,"data-testid",I),v&4&&A(e,"selectable",a[2])},d(j){j&&K(e),D=!1,Q()}}}function Me(a){let e,l,t=a[0].label+"",n,s,i=typeof a[0]=="object"&&a[0].confidences&&y(a);return{c(){e=C("div"),l=C("h2"),n=V(t),s=O(),i&&i.c(),d(l,"class","output-class svelte-1l15rn0"),d(l,"data-testid","label-output-value"),A(l,"no-confidence",!("confidences"in a[0])),E(l,"background-color",a[1]||"transparent"),d(e,"class","container svelte-1l15rn0")},m(u,f){P(u,e,f),h(e,l),h(l,n),h(e,s),i&&i.m(e,null)},p(u,[f]){f&1&&t!==(t=u[0].label+"")&&G(n,t),f&1&&A(l,"no-confidence",!("confidences"in u[0])),f&2&&E(l,"background-color",u[1]||"transparent"),typeof u[0]=="object"&&u[0].confidences?i?i.p(u,f):(i=y(u),i.c(),i.m(e,null)):i&&(i.d(1),i=null)},i:W,o:W,d(u){u&&K(e),i&&i.d()}}}function J(a){return a.replace(/\s/g,"-")}function Ce(a,e,l){let{value:t}=e;const n=Se();let{color:s=void 0}=e,{selectable:i=!1}=e;const u=(f,b)=>{n("select",{index:f,value:b.label})};return a.$$set=f=>{"value"in f&&l(0,t=f.value),"color"in f&&l(1,s=f.color),"selectable"in f&&l(2,i=f.selectable)},[t,s,i,n,u]}class Be extends ge{constructor(e){super(),ke(this,e,Ce,Me,pe,{value:0,color:1,selectable:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),Y()}get color(){return this.$$.ctx[1]}set color(e){this.$$set({color:e}),Y()}get selectable(){return this.$$.ctx[2]}set selectable(e){this.$$set({selectable:e}),Y()}}const He=Be,{SvelteComponent:qe,assign:Le,check_outros:ee,create_component:H,destroy_component:q,detach:z,empty:Ne,flush:g,get_spread_object:je,get_spread_update:Ee,group_outros:te,init:Ze,insert:F,mount_component:L,safe_not_equal:Ie,space:le,transition_in:$,transition_out:M}=window.__gradio__svelte__internal;function ne(a){let e,l;return e=new oe({props:{Icon:ae,label:a[6],disable:a[7]===!1}}),{c(){H(e.$$.fragment)},m(t,n){L(e,t,n),l=!0},p(t,n){const s={};n&64&&(s.label=t[6]),n&128&&(s.disable=t[7]===!1),e.$set(s)},i(t){l||($(e.$$.fragment,t),l=!0)},o(t){M(e.$$.fragment,t),l=!1},d(t){q(e,t)}}}function Je(a){let e,l;return e=new ce({props:{unpadded_box:!0,$$slots:{default:[Ve]},$$scope:{ctx:a}}}),{c(){H(e.$$.fragment)},m(t,n){L(e,t,n),l=!0},p(t,n){const s={};n&131072&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){l||($(e.$$.fragment,t),l=!0)},o(t){M(e.$$.fragment,t),l=!1},d(t){q(e,t)}}}function Oe(a){let e,l;return e=new He({props:{selectable:a[12],value:a[5],color:a[4]}}),e.$on("select",a[16]),{c(){H(e.$$.fragment)},m(t,n){L(e,t,n),l=!0},p(t,n){const s={};n&4096&&(s.selectable=t[12]),n&32&&(s.value=t[5]),n&16&&(s.color=t[4]),e.$set(s)},i(t){l||($(e.$$.fragment,t),l=!0)},o(t){M(e.$$.fragment,t),l=!1},d(t){q(e,t)}}}function Ve(a){let e,l;return e=new ae({}),{c(){H(e.$$.fragment)},m(t,n){L(e,t,n),l=!0},i(t){l||($(e.$$.fragment,t),l=!0)},o(t){M(e.$$.fragment,t),l=!1},d(t){q(e,t)}}}function Ae(a){let e,l,t,n,s,i,u;const f=[{autoscroll:a[0].autoscroll},{i18n:a[0].i18n},a[10]];let b={};for(let r=0;r<f.length;r+=1)b=Le(b,f[r]);e=new ie({props:b}),e.$on("clear_status",a[15]);let c=a[11]&&ne(a);const p=[Oe,Je],m=[];function B(r,_){return r[13]!==void 0&&r[13]!==null?0:1}return n=B(a),s=m[n]=p[n](a),{c(){H(e.$$.fragment),l=le(),c&&c.c(),t=le(),s.c(),i=Ne()},m(r,_){L(e,r,_),F(r,l,_),c&&c.m(r,_),F(r,t,_),m[n].m(r,_),F(r,i,_),u=!0},p(r,_){const S=_&1025?Ee(f,[_&1&&{autoscroll:r[0].autoscroll},_&1&&{i18n:r[0].i18n},_&1024&&je(r[10])]):{};e.$set(S),r[11]?c?(c.p(r,_),_&2048&&$(c,1)):(c=ne(r),c.c(),$(c,1),c.m(t.parentNode,t)):c&&(te(),M(c,1,1,()=>{c=null}),ee());let w=n;n=B(r),n===w?m[n].p(r,_):(te(),M(m[w],1,1,()=>{m[w]=null}),ee(),s=m[n],s?s.p(r,_):(s=m[n]=p[n](r),s.c()),$(s,1),s.m(i.parentNode,i))},i(r){u||($(e.$$.fragment,r),$(c),$(s),u=!0)},o(r){M(e.$$.fragment,r),M(c),M(s),u=!1},d(r){r&&(z(l),z(t),z(i)),q(e,r),c&&c.d(r),m[n].d(r)}}}function De(a){let e,l;return e=new re({props:{test_id:"label",visible:a[3],elem_id:a[1],elem_classes:a[2],container:a[7],scale:a[8],min_width:a[9],padding:!1,$$slots:{default:[Ae]},$$scope:{ctx:a}}}),{c(){H(e.$$.fragment)},m(t,n){L(e,t,n),l=!0},p(t,[n]){const s={};n&8&&(s.visible=t[3]),n&2&&(s.elem_id=t[1]),n&4&&(s.elem_classes=t[2]),n&128&&(s.container=t[7]),n&256&&(s.scale=t[8]),n&512&&(s.min_width=t[9]),n&146673&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){l||($(e.$$.fragment,t),l=!0)},o(t){M(e.$$.fragment,t),l=!1},d(t){q(e,t)}}}function Re(a,e,l){let t,{gradio:n}=e,{elem_id:s=""}=e,{elem_classes:i=[]}=e,{visible:u=!0}=e,{color:f=void 0}=e,{value:b={}}=e,c=null,{label:p=n.i18n("label.label")}=e,{container:m=!0}=e,{scale:B=null}=e,{min_width:r=void 0}=e,{loading_status:_}=e,{show_label:S=!0}=e,{_selectable:w=!1}=e;const N=()=>n.dispatch("clear_status",_),Z=({detail:o})=>n.dispatch("select",o);return a.$$set=o=>{"gradio"in o&&l(0,n=o.gradio),"elem_id"in o&&l(1,s=o.elem_id),"elem_classes"in o&&l(2,i=o.elem_classes),"visible"in o&&l(3,u=o.visible),"color"in o&&l(4,f=o.color),"value"in o&&l(5,b=o.value),"label"in o&&l(6,p=o.label),"container"in o&&l(7,m=o.container),"scale"in o&&l(8,B=o.scale),"min_width"in o&&l(9,r=o.min_width),"loading_status"in o&&l(10,_=o.loading_status),"show_label"in o&&l(11,S=o.show_label),"_selectable"in o&&l(12,w=o._selectable)},a.$$.update=()=>{a.$$.dirty&16417&&JSON.stringify(b)!==JSON.stringify(c)&&(l(14,c=b),n.dispatch("change")),a.$$.dirty&32&&l(13,t=b.label)},[n,s,i,u,f,b,p,m,B,r,_,S,w,t,c,N,Z]}class Qe extends qe{constructor(e){super(),Ze(this,e,Re,De,Ie,{gradio:0,elem_id:1,elem_classes:2,visible:3,color:4,value:5,label:6,container:7,scale:8,min_width:9,loading_status:10,show_label:11,_selectable:12})}get gradio(){return this.$$.ctx[0]}set gradio(e){this.$$set({gradio:e}),g()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),g()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),g()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),g()}get color(){return this.$$.ctx[4]}set color(e){this.$$set({color:e}),g()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),g()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),g()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),g()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),g()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),g()}get loading_status(){return this.$$.ctx[10]}set loading_status(e){this.$$set({loading_status:e}),g()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),g()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),g()}}export{He as BaseLabel,Qe as default};
//# sourceMappingURL=Index-Cqx-FJgX.js.map
