{"version": 3, "file": "Index-Cqx-FJgX.js", "sources": ["../../../../js/icons/src/LineChart.svelte", "../../../../js/label/shared/Label.svelte", "../../../../js/label/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M4 2H2v26a2 2 0 0 0 2 2h26v-2H4v-3h22v-8H4v-4h14V5H4Zm20 17v4H4v-4ZM16 7v4H4V7Z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value: {\n\t\tlabel?: string;\n\t\tconfidences?: { label: string; confidence: number }[];\n\t};\n\n\tconst dispatch = createEventDispatcher<{ select: SelectData }>();\n\n\texport let color: string | undefined = undefined;\n\texport let selectable = false;\n\n\tfunction get_aria_referenceable_id(elem_id: string): string {\n\t\t// `aria-labelledby` interprets the value as a space-separated id reference list,\n\t\t// so each single id should not contain any spaces.\n\t\t// Ref: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-labelledby#benefits_and_drawbacks\n\t\treturn elem_id.replace(/\\s/g, \"-\");\n\t}\n</script>\n\n<div class=\"container\">\n\t<h2\n\t\tclass=\"output-class\"\n\t\tdata-testid=\"label-output-value\"\n\t\tclass:no-confidence={!(\"confidences\" in value)}\n\t\tstyle:background-color={color || \"transparent\"}\n\t>\n\t\t{value.label}\n\t</h2>\n\n\t{#if typeof value === \"object\" && value.confidences}\n\t\t{#each value.confidences as confidence_set, i}\n\t\t\t<button\n\t\t\t\tclass=\"confidence-set group\"\n\t\t\t\tdata-testid={`${confidence_set.label}-confidence-set`}\n\t\t\t\tclass:selectable\n\t\t\t\ton:click={() => {\n\t\t\t\t\tdispatch(\"select\", { index: i, value: confidence_set.label });\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<div class=\"inner-wrap\">\n\t\t\t\t\t<meter\n\t\t\t\t\t\taria-labelledby={get_aria_referenceable_id(\n\t\t\t\t\t\t\t`meter-text-${confidence_set.label}`\n\t\t\t\t\t\t)}\n\t\t\t\t\t\taria-label={confidence_set.label}\n\t\t\t\t\t\taria-valuenow={Math.round(confidence_set.confidence * 100)}\n\t\t\t\t\t\taria-valuemin=\"0\"\n\t\t\t\t\t\taria-valuemax=\"100\"\n\t\t\t\t\t\tclass=\"bar\"\n\t\t\t\t\t\tmin=\"0\"\n\t\t\t\t\t\tmax=\"1\"\n\t\t\t\t\t\tvalue={confidence_set.confidence}\n\t\t\t\t\t\tstyle=\"width: {confidence_set.confidence *\n\t\t\t\t\t\t\t100}%; background: var(--stat-background-fill);\n\t\t\t\t\t\t\"\n\t\t\t\t\t/>\n\n\t\t\t\t\t<dl class=\"label\">\n\t\t\t\t\t\t<dt\n\t\t\t\t\t\t\tid={get_aria_referenceable_id(\n\t\t\t\t\t\t\t\t`meter-text-${confidence_set.label}`\n\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\tclass=\"text\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{confidence_set.label}\n\t\t\t\t\t\t</dt>\n\t\t\t\t\t\t<div class=\"line\" />\n\t\t\t\t\t\t<dd class=\"confidence\">\n\t\t\t\t\t\t\t{Math.round(confidence_set.confidence * 100)}%\n\t\t\t\t\t\t</dd>\n\t\t\t\t\t</dl>\n\t\t\t\t</div>\n\t\t\t</button>\n\t\t{/each}\n\t{/if}\n</div>\n\n<style>\n\t.container {\n\t\tpadding: var(--block-padding);\n\t}\n\t.output-class {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: var(--size-6) var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-xxl);\n\t}\n\n\t.confidence-set {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: flex-start;\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: var(--line-none);\n\t\tfont-family: var(--font-mono);\n\t\twidth: 100%;\n\t}\n\n\t.confidence-set:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.inner-wrap {\n\t\tflex: 1 1 0%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.bar {\n\t\tappearance: none;\n\t\talign-self: flex-start;\n\t\tmargin-bottom: var(--size-1);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--stat-background-fill);\n\t\theight: var(--size-1);\n\t}\n\n\t.label {\n\t\tdisplay: flex;\n\t\talign-items: baseline;\n\t}\n\n\t.label > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.confidence-set:hover .label {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.confidence-set:focus .label {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.text {\n\t\tline-height: var(--line-md);\n\t\ttext-align: left;\n\t}\n\n\t.line {\n\t\tflex: 1 1 0%;\n\t\tborder: 1px dashed var(--border-color-primary);\n\t\tpadding-right: var(--size-4);\n\t\tpadding-left: var(--size-4);\n\t}\n\n\t.confidence {\n\t\tmargin-left: auto;\n\t\ttext-align: right;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseLabel } from \"./shared/Label.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport Label from \"./shared/Label.svelte\";\n\timport { LineChart as LabelIcon } from \"@gradio/icons\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let color: undefined | string = undefined;\n\texport let value: {\n\t\tlabel?: string;\n\t\tconfidences?: { label: string; confidence: number }[];\n\t} = {};\n\tlet old_value: typeof value | null = null;\n\texport let label = gradio.i18n(\"label.label\");\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let show_label = true;\n\texport let _selectable = false;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\t$: _label = value.label;\n</script>\n\n<Block\n\ttest_id=\"label\"\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tpadding={false}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t{#if show_label}\n\t\t<BlockLabel Icon={LabelIcon} {label} disable={container === false} />\n\t{/if}\n\t{#if _label !== undefined && _label !== null}\n\t\t<Label\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\tselectable={_selectable}\n\t\t\t{value}\n\t\t\t{color}\n\t\t/>\n\t{:else}\n\t\t<Empty unpadded_box={true}><LabelIcon /></Empty>\n\t{/if}\n</Block>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "createEventDispatcher", "each_value", "ensure_array_like", "ctx", "i", "t1_value", "t3_value", "get_aria_referenceable_id", "attr", "meter", "meter_aria_label_value", "meter_aria_valuenow_value", "meter_value_value", "button", "button_data_testid_value", "div1", "dl", "dt", "div0", "dd", "dirty", "set_data", "t1", "t3", "t0_value", "if_block", "create_if_block", "toggle_class", "h2", "set_style", "div", "t0", "elem_id", "value", "$$props", "dispatch", "color", "selectable", "confidence_set", "LabelIcon", "blocklabel_changes", "create_if_block_1", "gradio", "elem_classes", "visible", "old_value", "label", "container", "scale", "min_width", "loading_status", "show_label", "_selectable", "clear_status_handler", "select_handler", "detail", "$$invalidate", "_label"], "mappings": "s0BAAAA,GAeKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAG,CAAA,iWCZQ,CAAA,sBAAAC,EAAA,SAAqC,6GA+BtCC,EAAAC,EAAAC,KAAM,WAAW,uBAAtB,OAAIC,GAAA,0JAACH,EAAAC,EAAAC,KAAM,WAAW,oBAAtB,OAAI,GAAA,EAAA,yHAAJ,sEAkCIE,EAAAF,KAAe,MAAK,aAIpBG,EAAA,KAAK,MAAMH,EAAe,CAAA,EAAA,WAAa,GAAG,EAAA,4KAAE,GAC9C,gCA5BiBI,EAAyB,cAC3BJ,EAAc,CAAA,EAAC,KAAK,EAAA,CAAA,EAEvBK,EAAAC,EAAA,aAAAC,EAAAP,KAAe,KAAK,EACjBK,EAAAC,EAAA,gBAAAE,EAAA,KAAK,MAAMR,EAAe,CAAA,EAAA,WAAa,GAAG,CAAA,sHAMlDM,EAAA,MAAAG,EAAAT,KAAe,uBACPA,EAAc,CAAA,EAAC,WAC7B,IAAG,GAAA,6DAMCI,EAAyB,cACdJ,EAAc,CAAA,EAAC,KAAK,EAAA,CAAA,iPA3BtBK,EAAAK,EAAA,cAAAC,EAAA,GAAAX,KAAe,KAAK,iBAAA,iCAFrCT,EAyCQC,EAAAkB,EAAAhB,CAAA,EAjCPC,EAgCKe,EAAAE,CAAA,EA/BJjB,EAeCiB,EAAAN,CAAA,SAEDX,EAaIiB,EAAAC,CAAA,EAZHlB,EAOIkB,EAAAC,CAAA,gBACJnB,EAAmBkB,EAAAE,CAAA,EACnBpB,EAEIkB,EAAAG,CAAA,4EA5BaZ,EAAyB,cAC3BJ,EAAc,CAAA,EAAC,KAAK,EAAA,6BAEvBiB,EAAA,GAAAV,KAAAA,EAAAP,KAAe,4BACZiB,EAAA,GAAAT,KAAAA,EAAA,KAAK,MAAMR,EAAe,CAAA,EAAA,WAAa,GAAG,2BAMlDiB,EAAA,GAAAR,KAAAA,EAAAT,KAAe,0CACPA,EAAc,CAAA,EAAC,WAC7B,IAAG,GAAA,EAWFiB,EAAA,GAAAf,KAAAA,EAAAF,KAAe,MAAK,KAAAkB,EAAAC,EAAAjB,CAAA,cALjBE,EAAyB,cACdJ,EAAc,CAAA,EAAC,KAAK,EAAA,gBAQlCiB,EAAA,GAAAd,KAAAA,EAAA,KAAK,MAAMH,EAAe,CAAA,EAAA,WAAa,GAAG,EAAA,KAAAkB,EAAAE,EAAAjB,CAAA,EAnC9Bc,EAAA,GAAAN,KAAAA,EAAA,GAAAX,KAAe,KAAK,qHAPrCqB,EAAArB,KAAM,MAAK,OAGDsB,EAAA,OAAAtB,EAAU,CAAA,GAAA,UAAYA,KAAM,aAAWuB,EAAAvB,CAAA,2IAN3BwB,EAAAC,EAAA,gBAAA,EAAA,gBAAiBzB,EAAK,CAAA,EAAA,EACrB0B,EAAAD,EAAA,mBAAAzB,MAAS,aAAa,kDALhDT,EAwDKC,EAAAmC,EAAAjC,CAAA,EAvDJC,EAOIgC,EAAAF,CAAA,yCADFR,EAAA,GAAAI,KAAAA,EAAArB,KAAM,MAAK,KAAAkB,EAAAU,EAAAP,CAAA,OAHWG,EAAAC,EAAA,gBAAA,EAAA,gBAAiBzB,EAAK,CAAA,EAAA,OACrB0B,EAAAD,EAAA,mBAAAzB,MAAS,aAAa,EAKnC,OAAAA,EAAU,CAAA,GAAA,UAAYA,KAAM,sGAlB/B,SAAAI,EAA0ByB,EAAe,CAI1C,OAAAA,EAAQ,QAAQ,MAAO,GAAG,yBAdvB,MAAAC,CAGV,EAAAC,EAEK,MAAAC,EAAWnC,KAEN,GAAA,CAAA,MAAAoC,EAA4B,MAAS,EAAAF,EACrC,CAAA,WAAAG,EAAa,EAAK,EAAAH,kBA2BzBC,EAAS,SAAQ,CAAI,MAAO/B,EAAG,MAAOkC,EAAe,KAAK,CAAA,qzBCsB3CC,cAA4B,QAAApC,OAAc,0FAAdiB,EAAA,MAAAoB,EAAA,QAAArC,OAAc,0JAUvC,sSALRA,EAAW,EAAA,sIAAXA,EAAW,EAAA,wVAXZ,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,2GAGdA,EAAU,EAAA,GAAAsC,GAAAtC,CAAA,uCAGV,OAAAA,EAAW,EAAA,IAAA,QAAaA,QAAW,KAAI,iMAR/BiB,EAAA,GAAA,CAAA,WAAAjB,KAAO,UAAU,EACvBiB,EAAA,GAAA,CAAA,KAAAjB,KAAO,IAAI,aACbA,EAAc,EAAA,CAAA,iBAGdA,EAAU,EAAA,miBARN,yZAxCE,OAAAuC,CAIT,EAAAR,EACS,CAAA,QAAAF,EAAU,EAAE,EAAAE,GACZ,aAAAS,EAAY,EAAA,EAAAT,EACZ,CAAA,QAAAU,EAAU,EAAI,EAAAV,EACd,CAAA,MAAAE,EAA4B,MAAS,EAAAF,GACrC,MAAAD,EAAK,EAAA,EAAAC,EAIZW,EAAiC,KAC1B,CAAA,MAAAC,EAAQJ,EAAO,KAAK,aAAa,CAAA,EAAAR,EACjC,CAAA,UAAAa,EAAY,EAAI,EAAAb,EAChB,CAAA,MAAAc,EAAuB,IAAI,EAAAd,EAC3B,CAAA,UAAAe,EAAgC,MAAS,EAAAf,GACzC,eAAAgB,CAA6B,EAAAhB,EAC7B,CAAA,WAAAiB,EAAa,EAAI,EAAAjB,EACjB,CAAA,YAAAkB,EAAc,EAAK,EAAAlB,EA0BN,MAAAmB,EAAA,IAAAX,EAAO,SAAS,eAAgBQ,CAAc,EAOtDI,EAAA,CAAA,CAAA,OAAAC,KAAab,EAAO,SAAS,SAAUa,CAAM,wfA9BxD,KAAK,UAAUtB,CAAK,IAAM,KAAK,UAAUY,CAAS,IACrDW,EAAA,GAAAX,EAAYZ,CAAK,EACjBS,EAAO,SAAS,QAAQ,kBAIvBc,EAAA,GAAAC,EAASxB,EAAM,KAAK"}