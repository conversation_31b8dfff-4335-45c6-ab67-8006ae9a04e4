{"version": 3, "mappings": ";2sBAAA,IAAIA,GAAM,OAAO,UAAU,eAE3B,SAASC,GAAKC,EAAMC,EAAKC,EAAK,CAC7B,IAAKA,KAAOF,EAAK,OAChB,GAAIG,EAAOD,EAAKD,CAAG,EAAG,OAAOC,CAE/B,CAEO,SAASC,EAAOC,EAAKC,EAAK,CAChC,IAAIC,EAAMC,EAAKC,EACf,GAAIJ,IAAQC,EAAK,MAAO,GAExB,GAAID,GAAOC,IAAQC,EAAKF,EAAI,eAAiBC,EAAI,YAAa,CAC7D,GAAIC,IAAS,KAAM,OAAOF,EAAI,YAAcC,EAAI,UAChD,GAAIC,IAAS,OAAQ,OAAOF,EAAI,aAAeC,EAAI,WAEnD,GAAIC,IAAS,MAAO,CACnB,IAAKC,EAAIH,EAAI,UAAYC,EAAI,OAC5B,KAAOE,KAASJ,EAAOC,EAAIG,CAAG,EAAGF,EAAIE,CAAG,CAAC,GAAE,CAE5C,OAAOA,IAAQ,EACf,CAED,GAAID,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EACFC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACH,EAAI,IAAIG,CAAG,EAAG,MAAO,GAE3B,MAAO,EACP,CAED,GAAIF,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EAAI,CAAC,EACPC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACL,EAAOI,EAAI,CAAC,EAAGF,EAAI,IAAIG,CAAG,CAAC,EAC/B,MAAO,GAGT,MAAO,EACP,CAED,GAAIF,IAAS,YACZF,EAAM,IAAI,WAAWA,CAAG,EACxBC,EAAM,IAAI,WAAWA,CAAG,UACdC,IAAS,SAAU,CAC7B,IAAKC,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAI,QAAQG,CAAG,IAAMF,EAAI,QAAQE,CAAG,GAAE,CAEvD,OAAOA,IAAQ,EACf,CAED,GAAI,YAAY,OAAOH,CAAG,EAAG,CAC5B,IAAKG,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAIG,CAAG,IAAMF,EAAIE,CAAG,GAAE,CAEvC,OAAOA,IAAQ,EACf,CAED,GAAI,CAACD,GAAQ,OAAOF,GAAQ,SAAU,CACrCG,EAAM,EACN,IAAKD,KAAQF,EAEZ,GADIN,GAAI,KAAKM,EAAKE,CAAI,GAAK,EAAEC,GAAO,CAACT,GAAI,KAAKO,EAAKC,CAAI,GACnD,EAAEA,KAAQD,IAAQ,CAACF,EAAOC,EAAIE,CAAI,EAAGD,EAAIC,CAAI,CAAC,EAAG,MAAO,GAE7D,OAAO,OAAO,KAAKD,CAAG,EAAE,SAAWE,CACnC,CACD,CAED,OAAOH,IAAQA,GAAOC,IAAQA,CAC/B,yZCNSI,EAAQ,IAAAC,GAAAD,CAAA,wBASKE,GAAiB,MAAAF,KAAK,iBAAiB,iDAItDA,EAAQ,oIARLA,EAAY,KACVG,EAAAC,EAAA,gBAAO,aAAe,SAAW,IAAI,EACnCD,EAAAC,EAAA,WAAAC,EAAA,OAAO,aAAe,KAAOL,EAAM,cAAaA,KAAM,IAAI,sFATvEM,EAoCKC,EAAAC,EAAAC,CAAA,EAnCJC,GAYKF,EAAAG,CAAA,yBAPJD,GAMGC,EAAAP,CAAA,kDAVGJ,EAAQ,4GASsBY,EAAA,KAAAC,EAAA,MAAAb,KAAK,iBAAiB,sCAJnDA,EAAY,MAER,CAAAc,GAAAF,EAAA,GAAAP,OAAA,OAAO,aAAe,KAAOL,EAAM,cAAaA,KAAM,6UAL9Ce,GAAI,8NAqBhBf,EAAiB,uVAAjBA,EAAiB,olBARjBA,EAAmB,sRAAnBA,EAAmB,mfArBtBgB,SACChB,EAAK,IAAIA,EAAI,GAAC,mBAAmB,WAEpCA,EAAK,IAAAiB,GAAAjB,CAAA,uKAFFA,EAAK,IAAIA,EAAI,GAAC,mBAAmB,aAEpCA,EAAK,4OA7CMkB,IAAY,CAEnB,2BADqB,wBAAmB,mDACjC,uBAEAC,IAAc,CAErB,2BADqB,0BAAqB,mDACnC,+BA1BJ,MAAAC,CAAsB,EAAAC,EACtB,cAAAC,EAAsD,OAAO,EAAAD,EAC7D,aAAAE,GAAiD,EAAG,EAAG,EAAG,CAAC,GAAAF,EAC3D,OAAAG,EAAQ,EAAE,EAAAH,GACV,WAAAI,CAAmB,EAAAJ,GACnB,KAAAK,CAAmB,EAAAL,EACnB,YAAAM,EAAa,CAAC,EAAAN,EACd,WAAAO,EAAY,CAAC,EAAAP,EAEb,iBAAAQ,EACV,MACA,KACA,OAAAR,EAGGS,EAAqB,iBAAAD,EAAiB,WAAAF,EAAY,UAAAC,CAAS,EAE3DG,EAAW,GACXC,EACAC,EAsBAC,WACKC,GAAW,CACnBD,GAAU,sBAAsBL,EAAiBF,EAAYC,CAAS,MAcnEQ,cAaoDD,uEAsB1CD,EAAQG,+ZAhEfjB,IACNkB,EAAA,EAAAP,EAAWX,EAAM,KAAK,SAAS,QAAQ,GAAKA,EAAM,KAAK,SAAS,MAAM,GAClEW,EACHZ,GAAc,EAAG,KAAMoB,GAAS,CAC/BD,EAAA,GAAAN,EAAsBO,CAAS,IAGhCrB,GAAY,EAAG,KAAMqB,GAAS,CAC7BD,EAAA,GAAAL,EAAoBM,CAAS,wBAY7B,CAAA7C,EAAOoC,EAAiB,gBAAiBD,CAAe,GACzDC,EAAiB,aAAeH,GAChCG,EAAiB,YAAcF,KAE/BM,GAAU,sBAAsBL,EAAiBF,EAAYC,CAAS,EACtEU,EAAA,GAAAR,EAAqB,iBAAAD,EAAiB,WAAAF,EAAY,UAAAC,CAAS,m8CC7D/B,KAAAY,IAAM,OAAgB,qFAwGvCxC,EAAQ,2CACTA,EAAY,kBAEbA,EAAW,iDAIhBA,EAAQ,sIATdM,GA4BKC,EAAAkC,EAAAhC,CAAA,kFA1BQT,EAAQ,iZATT,OAAQ,OAAQ,QAAS,OAAQ,YAAa,SAAU,MAAM,qJAHhEA,EAAa,wZA4BdA,EAAiB,uQAAjBA,EAAiB,weAPjBA,EAAmB,qMAAnBA,EAAmB,gqBA3BEgB,GAAa,MAAAhB,MAAS,mDAEhD,OAAAA,OAAU,KAAI,gLAFyBY,EAAA,IAAA8B,EAAA,MAAA1C,MAAS,qSAtCrCkB,IAAY,CAEnB,2BADqB,wBAAmB,mDACjC,uBAEAC,IAAc,CAErB,2BADqB,0BAAqB,mDACnC,0DA7CJ,MAAAC,CAAsB,EAAAC,EACtB,cAAAC,EAAsD,OAAO,EAAAD,EAC7D,aAAAE,GAAiD,EAAG,EAAG,EAAG,CAAC,GAAAF,EAC3D,OAAAG,EAAQ,EAAE,EAAAH,GACV,WAAAI,CAAmB,EAAAJ,GACnB,KAAAsB,CAAY,EAAAtB,GACZ,KAAAK,CAAmB,EAAAL,EACnB,YAAAM,EAAa,CAAC,EAAAN,EACd,WAAAO,EAAY,CAAC,EAAAP,EACb,eAAAuB,EAA+B,IAAI,EAAAvB,EAGnC,iBAAAQ,EACV,MACA,KACA,OAAAR,GAEU,OAAAwB,CAAwB,EAAAxB,GACxB,eAAAyB,CAAgC,EAAAzB,EAE5B,eAAA0B,GACd,OAAAC,GAAM,CAENV,EAAA,EAAAlB,EAAQ4B,CAAM,QACRR,GAAI,EACVS,EAAS,SAAU7B,CAAK,EACxB6B,EAAS,OAAQ7B,CAAK,iBAGR8B,GAAY,CAC1BZ,EAAA,EAAAlB,EAAQ,IAAI,QACNoB,GAAI,EACVS,EAAS,OAAO,EAChBA,EAAS,QAAQ,EAGd,IAAAlB,EAAW,GACXC,EACAC,EAsBAC,iBACWC,IAAW,CACzBD,GAAU,sBAAsBL,EAAiBF,EAAYC,CAAS,EAGjE,MAAAqB,EAAWE,KAOb,IAAAC,EAAW,0GAwCDlB,EAAQG,4jBAjEfjB,IACNkB,EAAA,GAAAP,EAAWX,EAAM,KAAK,SAAS,QAAQ,GAAKA,EAAM,KAAK,SAAS,MAAM,GAClEW,EACHZ,GAAc,EAAG,KAAMoB,GAAS,CAC/BD,EAAA,GAAAN,EAAsBO,CAAS,IAGhCrB,GAAY,EAAG,KAAMqB,GAAS,CAC7BD,EAAA,GAAAL,EAAoBM,CAAS,uBAmB7BU,EAAS,OAAQG,CAAQ,2uDCKlB,QAAApD,EAAU,UAAO,SAAW,oBACxBA,EAAQ,IAAG,QAAU,eACzB,2NAFAY,EAAA,IAAAyC,EAAA,QAAArD,EAAU,UAAO,SAAW,kCACxBA,EAAQ,IAAG,QAAU,kWAzCzB,QAAAA,EAAU,UAAO,SAAW,oBACxBA,EAAQ,IAAG,QAAU,eACzB,2NAFAY,EAAA,IAAAyC,EAAA,QAAArD,EAAU,UAAO,SAAW,kCACxBA,EAAQ,IAAG,QAAU,qVAsFf,KAAAA,MAAO,mFAAPY,EAAA,OAAA0C,EAAA,KAAAtD,MAAO,qIApCb,WAAAA,MAAO,YACb,MAAAA,MAAO,IAAI,EACbA,EAAc,wPA6BZ,KAAAA,MAAO,KACE,cAAAA,MAAO,qBACdA,EAAM,IAAC,OAAO,sBACNA,EAAM,IAAC,OAAO,4SAlClB,WAAAA,MAAO,YACbY,EAAA,YAAAZ,MAAO,IAAI,UACbA,EAAc,gPA6BZY,EAAA,OAAA2C,EAAA,KAAAvD,MAAO,MACEY,EAAA,OAAA2C,EAAA,cAAAvD,MAAO,iCACdA,EAAM,IAAC,OAAO,kCACNA,EAAM,IAAC,OAAO,yQArDCgB,GAAa,MAAAhB,MAAS,4CAEhC,+LAFuBY,EAAA,MAAA8B,EAAA,MAAA1C,MAAS,oRAZ9C,KAAAA,MAAO,gMAAPY,EAAA,OAAA4C,EAAA,KAAAxD,MAAO,0dATF,WAAAA,MAAO,YACb,MAAAA,MAAO,IAAI,EACbA,EAAc,2IAId,OAAAA,MAASA,EAAU,2KANX,WAAAA,MAAO,YACbY,EAAA,YAAAZ,MAAO,IAAI,UACbA,EAAc,4VAhBfA,EAAW,oUA7BL,YAAAyD,EAAU,EAAE,EAAApC,GACZ,aAAAqC,EAAY,IAAArC,EACZ,SAAAsC,EAAU,EAAI,EAAAtC,EACd,OAAAD,EAAyB,IAAI,EAAAC,GAC7B,KAAAsB,CAAY,EAAAtB,EACZ,cAAAC,EAAsD,OAAO,EAAAD,GAC7D,YAAAE,CAA6C,EAAAF,GAC7C,eAAAuC,CAA6B,EAAAvC,GAC7B,MAAAG,CAAa,EAAAH,GACb,WAAAI,CAAmB,EAAAJ,EACnB,WAAAwC,EAAY,EAAI,EAAAxC,EAChB,OAAAyC,EAAuB,IAAI,EAAAzC,EAC3B,WAAA0C,EAAgC,MAAS,EAAA1C,GACzC,OAAA2C,CAAc,EAAA3C,EACd,QAAA4C,EAA6B,MAAS,EAAA5C,EACtC,YAAAM,EAAa,CAAC,EAAAN,EAGd,iBAAAQ,EACV,MACA,KACA,OAAAR,GAEU,YAAA6C,CAAoB,EAAA7C,EAE3B+B,EAAW,SACTe,EAAU,OAAU,OAAW,IAoBZC,EAAA,IAAAJ,EAAO,SAAS,eAAgBJ,CAAc,EAwC9CS,GAAA,IAAAL,EAAO,SAAS,eAAgBJ,CAAc,MAYvD,OAAAZ,CAAM,IAAAV,EAAA,EAAQlB,EAAQ4B,CAAM,MAC9B,OAAAA,CAAM,IAAAV,EAAA,GAAQc,EAAWJ,CAAM,EAC7BsB,GAAA,SAAAtB,KAAagB,EAAO,SAAS,SAAUhB,CAAM,UAE1DV,EAAA,EAAAlB,EAAQ,IAAI,EACZ4C,EAAO,SAAS,OAAO,QAEZ,OAAAhB,KAAM,CACjBV,EAAA,EAAAlB,EAAQ4B,CAAM,EACdgB,EAAO,SAAS,QAAQ,OAEZ,OAAAhB,KAAM,CAClBV,EAAA,EAAAsB,EAAiBA,GAAc,QAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BI,EAAO,SAAS,QAAShB,CAAM", "names": ["has", "find", "iter", "tar", "key", "dequal", "foo", "bar", "ctor", "len", "tmp", "ctx", "create_if_block_2", "Download", "attr", "a", "a_download_value", "insert", "target", "div1", "anchor", "append", "div0", "dirty", "iconbutton_changes", "current", "Undo", "File", "create_if_block", "loadCanvas3D", "loadCanvas3DGS", "value", "$$props", "display_mode", "clear_color", "label", "show_label", "i18n", "zoom_speed", "pan_speed", "camera_position", "current_settings", "use_3dgs", "Canvas3DGSComponent", "Canvas3DComponent", "canvas3d", "handle_undo", "resolved_url", "$$value", "$$invalidate", "component", "tick", "div", "blocklabel_changes", "root", "max_file_size", "upload", "stream_handler", "handle_upload", "detail", "dispatch", "handle_clear", "createEventDispatcher", "dragging", "block_changes", "uploadtext_changes", "model3dupload_changes", "model3d_changes", "elem_id", "elem_classes", "visible", "loading_status", "container", "scale", "min_width", "gradio", "height", "interactive", "is_browser", "clear_status_handler", "clear_status_handler_1", "change_handler_1"], "ignoreList": [0], "sources": ["../../../../node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/dist/index.mjs", "../../../../js/model3D/shared/Model3D.svelte", "../../../../js/model3D/shared/Model3DUpload.svelte", "../../../../js/model3D/Index.svelte"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/client\";\n\timport { BlockLabel, IconButton } from \"@gradio/atoms\";\n\timport { File, Download, Undo } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { dequal } from \"dequal\";\n\timport type Canvas3DGS from \"./Canvas3DGS.svelte\";\n\timport type Canvas3D from \"./Canvas3D.svelte\";\n\n\texport let value: FileData | null;\n\texport let display_mode: \"solid\" | \"point_cloud\" | \"wireframe\" = \"solid\";\n\texport let clear_color: [number, number, number, number] = [0, 0, 0, 0];\n\texport let label = \"\";\n\texport let show_label: boolean;\n\texport let i18n: I18nFormatter;\n\texport let zoom_speed = 1;\n\texport let pan_speed = 1;\n\t// alpha, beta, radius\n\texport let camera_position: [number | null, number | null, number | null] = [\n\t\tnull,\n\t\tnull,\n\t\tnull\n\t];\n\n\tlet current_settings = { camera_position, zoom_speed, pan_speed };\n\n\tlet use_3dgs = false;\n\tlet Canvas3DGSComponent: typeof Canvas3DGS;\n\tlet Canvas3DComponent: typeof Canvas3D;\n\tasync function loadCanvas3D(): Promise<typeof Canvas3D> {\n\t\tconst module = await import(\"./Canvas3D.svelte\");\n\t\treturn module.default;\n\t}\n\tasync function loadCanvas3DGS(): Promise<typeof Canvas3DGS> {\n\t\tconst module = await import(\"./Canvas3DGS.svelte\");\n\t\treturn module.default;\n\t}\n\t$: if (value) {\n\t\tuse_3dgs = value.path.endsWith(\".splat\") || value.path.endsWith(\".ply\");\n\t\tif (use_3dgs) {\n\t\t\tloadCanvas3DGS().then((component) => {\n\t\t\t\tCanvas3DGSComponent = component;\n\t\t\t});\n\t\t} else {\n\t\t\tloadCanvas3D().then((component) => {\n\t\t\t\tCanvas3DComponent = component;\n\t\t\t});\n\t\t}\n\t}\n\n\tlet canvas3d: Canvas3D | undefined;\n\tfunction handle_undo(): void {\n\t\tcanvas3d?.reset_camera_position(camera_position, zoom_speed, pan_speed);\n\t}\n\n\t$: {\n\t\tif (\n\t\t\t!dequal(current_settings.camera_position, camera_position) ||\n\t\t\tcurrent_settings.zoom_speed !== zoom_speed ||\n\t\t\tcurrent_settings.pan_speed !== pan_speed\n\t\t) {\n\t\t\tcanvas3d?.reset_camera_position(camera_position, zoom_speed, pan_speed);\n\t\t\tcurrent_settings = { camera_position, zoom_speed, pan_speed };\n\t\t}\n\t}\n\n\tlet resolved_url: string | undefined;\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={File}\n\tlabel={label || i18n(\"3D_model.3d_model\")}\n/>\n{#if value}\n\t<div class=\"model3D\">\n\t\t<div class=\"buttons\">\n\t\t\t{#if !use_3dgs}\n\t\t\t\t<!-- Canvas3DGS doesn't implement the undo method (reset_camera_position) -->\n\t\t\t\t<IconButton Icon={Undo} label=\"Undo\" on:click={() => handle_undo()} />\n\t\t\t{/if}\n\t\t\t<a\n\t\t\t\thref={resolved_url}\n\t\t\t\ttarget={window.__is_colab__ ? \"_blank\" : null}\n\t\t\t\tdownload={window.__is_colab__ ? null : value.orig_name || value.path}\n\t\t\t>\n\t\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t\t</a>\n\t\t</div>\n\n\t\t{#if use_3dgs}\n\t\t\t<svelte:component\n\t\t\t\tthis={Canvas3DGSComponent}\n\t\t\t\tbind:resolved_url\n\t\t\t\t{value}\n\t\t\t\t{zoom_speed}\n\t\t\t\t{pan_speed}\n\t\t\t/>\n\t\t{:else}\n\t\t\t<svelte:component\n\t\t\t\tthis={Canvas3DComponent}\n\t\t\t\tbind:this={canvas3d}\n\t\t\t\tbind:resolved_url\n\t\t\t\t{value}\n\t\t\t\t{display_mode}\n\t\t\t\t{clear_color}\n\t\t\t\t{camera_position}\n\t\t\t\t{zoom_speed}\n\t\t\t\t{pan_speed}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\t.model3D {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tborder-radius: var(--block-radius);\n\t\toverflow: hidden;\n\t}\n\t.model3D :global(canvas) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\toverflow: hidden;\n\t}\n\t.buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport type Canvas3DGS from \"./Canvas3DGS.svelte\";\n\timport type Canvas3D from \"./Canvas3D.svelte\";\n\n\texport let value: null | FileData;\n\texport let display_mode: \"solid\" | \"point_cloud\" | \"wireframe\" = \"solid\";\n\texport let clear_color: [number, number, number, number] = [0, 0, 0, 0];\n\texport let label = \"\";\n\texport let show_label: boolean;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\texport let zoom_speed = 1;\n\texport let pan_speed = 1;\n\texport let max_file_size: number | null = null;\n\n\t// alpha, beta, radius\n\texport let camera_position: [number | null, number | null, number | null] = [\n\t\tnull,\n\t\tnull,\n\t\tnull\n\t];\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\n\tasync function handle_upload({\n\t\tdetail\n\t}: CustomEvent<FileData>): Promise<void> {\n\t\tvalue = detail;\n\t\tawait tick();\n\t\tdispatch(\"change\", value);\n\t\tdispatch(\"load\", value);\n\t}\n\n\tasync function handle_clear(): Promise<void> {\n\t\tvalue = null;\n\t\tawait tick();\n\t\tdispatch(\"clear\");\n\t\tdispatch(\"change\");\n\t}\n\n\tlet use_3dgs = false;\n\tlet Canvas3DGSComponent: typeof Canvas3DGS;\n\tlet Canvas3DComponent: typeof Canvas3D;\n\tasync function loadCanvas3D(): Promise<typeof Canvas3D> {\n\t\tconst module = await import(\"./Canvas3D.svelte\");\n\t\treturn module.default;\n\t}\n\tasync function loadCanvas3DGS(): Promise<typeof Canvas3DGS> {\n\t\tconst module = await import(\"./Canvas3DGS.svelte\");\n\t\treturn module.default;\n\t}\n\t$: if (value) {\n\t\tuse_3dgs = value.path.endsWith(\".splat\") || value.path.endsWith(\".ply\");\n\t\tif (use_3dgs) {\n\t\t\tloadCanvas3DGS().then((component) => {\n\t\t\t\tCanvas3DGSComponent = component;\n\t\t\t});\n\t\t} else {\n\t\t\tloadCanvas3D().then((component) => {\n\t\t\t\tCanvas3DComponent = component;\n\t\t\t});\n\t\t}\n\t}\n\n\tlet canvas3d: Canvas3D | undefined;\n\tasync function handle_undo(): Promise<void> {\n\t\tcanvas3d?.reset_camera_position(camera_position, zoom_speed, pan_speed);\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData | null;\n\t\tclear: undefined;\n\t\tdrag: boolean;\n\t\tload: FileData;\n\t}>();\n\n\tlet dragging = false;\n\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel {show_label} Icon={File} label={label || \"3D Model\"} />\n\n{#if value === null}\n\t<Upload\n\t\t{upload}\n\t\t{stream_handler}\n\t\ton:load={handle_upload}\n\t\t{root}\n\t\t{max_file_size}\n\t\tfiletype={[\".stl\", \".obj\", \".gltf\", \".glb\", \"model/obj\", \".splat\", \".ply\"]}\n\t\tbind:dragging\n\t\ton:error\n\t>\n\t\t<slot />\n\t</Upload>\n{:else}\n\t<div class=\"input-model\">\n\t\t<ModifyUpload\n\t\t\tundoable={!use_3dgs}\n\t\t\ton:clear={handle_clear}\n\t\t\t{i18n}\n\t\t\ton:undo={handle_undo}\n\t\t\tabsolute\n\t\t/>\n\n\t\t{#if use_3dgs}\n\t\t\t<svelte:component\n\t\t\t\tthis={Canvas3DGSComponent}\n\t\t\t\t{value}\n\t\t\t\t{zoom_speed}\n\t\t\t\t{pan_speed}\n\t\t\t/>\n\t\t{:else}\n\t\t\t<svelte:component\n\t\t\t\tthis={Canvas3DComponent}\n\t\t\t\tbind:this={canvas3d}\n\t\t\t\t{value}\n\t\t\t\t{display_mode}\n\t\t\t\t{clear_color}\n\t\t\t\t{camera_position}\n\t\t\t\t{zoom_speed}\n\t\t\t\t{pan_speed}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\t.input-model {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tborder-radius: var(--block-radius);\n\t\toverflow: hidden;\n\t}\n\n\t.input-model :global(canvas) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\toverflow: hidden;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseModel3D } from \"./shared/Model3D.svelte\";\n\texport { default as BaseModel3DUpload } from \"./shared/Model3DUpload.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/client\";\n\timport Model3D from \"./shared/Model3D.svelte\";\n\timport Model3DUpload from \"./shared/Model3DUpload.svelte\";\n\timport { BlockLabel, Block, Empty, UploadText } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Gradio } from \"@gradio/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData = null;\n\texport let root: string;\n\texport let display_mode: \"solid\" | \"point_cloud\" | \"wireframe\" = \"solid\";\n\texport let clear_color: [number, number, number, number];\n\texport let loading_status: LoadingStatus;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio;\n\texport let height: number | undefined = undefined;\n\texport let zoom_speed = 1;\n\n\t// alpha, beta, radius\n\texport let camera_position: [number | null, number | null, number | null] = [\n\t\tnull,\n\t\tnull,\n\t\tnull\n\t];\n\texport let interactive: boolean;\n\n\tlet dragging = false;\n\tconst is_browser = typeof window !== \"undefined\";\n</script>\n\n{#if !interactive}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\t{height}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t{#if value && is_browser}\n\t\t\t<Model3D\n\t\t\t\t{value}\n\t\t\t\ti18n={gradio.i18n}\n\t\t\t\t{display_mode}\n\t\t\t\t{clear_color}\n\t\t\t\t{label}\n\t\t\t\t{show_label}\n\t\t\t\t{camera_position}\n\t\t\t\t{zoom_speed}\n\t\t\t/>\n\t\t{:else}\n\t\t\t<!-- Not ideal but some bugs to work out before we can \n\t\t\t\t make this consistent with other components -->\n\n\t\t\t<BlockLabel {show_label} Icon={File} label={label || \"3D Model\"} />\n\n\t\t\t<Empty unpadded_box={true} size=\"large\"><File /></Empty>\n\t\t{/if}\n\t</Block>\n{:else}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\t{height}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<Model3DUpload\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{root}\n\t\t\t{display_mode}\n\t\t\t{clear_color}\n\t\t\t{value}\n\t\t\t{camera_position}\n\t\t\t{zoom_speed}\n\t\t\ton:change={({ detail }) => (value = detail)}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:change={({ detail }) => gradio.dispatch(\"change\", detail)}\n\t\t\ton:clear={() => {\n\t\t\t\tvalue = null;\n\t\t\t\tgradio.dispatch(\"clear\");\n\t\t\t}}\n\t\t\ton:load={({ detail }) => {\n\t\t\t\tvalue = detail;\n\t\t\t\tgradio.dispatch(\"upload\");\n\t\t\t}}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t\ti18n={gradio.i18n}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\tupload={gradio.client.upload}\n\t\t\tstream_handler={gradio.client.stream}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"file\" />\n\t\t</Model3DUpload>\n\t</Block>\n{/if}\n"], "file": "assets/Index-D3d6dpgr.js"}