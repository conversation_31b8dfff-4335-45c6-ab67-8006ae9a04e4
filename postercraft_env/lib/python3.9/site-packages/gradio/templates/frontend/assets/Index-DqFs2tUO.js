import{a as el,B as Hl}from"./Button-8nmImwVJ.js";import{S as Nl}from"./Index-WGC0_FkS.js";import{d as ge}from"./index-CnqicUFC.js";import{c as Tl}from"./Blocks-aR9ucLZz.js";import{U as Ol}from"./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js";import"./index-COY1HN2y.js";import"./Index.svelte_svelte_type_style_lang-BgUoKvlh.js";import{M as Fl}from"./Example.svelte_svelte_type_style_lang-BBpfzd83.js";import{d as jl}from"./dsv-DB8NKgIY.js";import{default as As}from"./Example-CMXuI9oj.js";import"./svelte/svelte.js";import"./prism-python-DQB1-hGx.js";const{HtmlTag:Pl,SvelteComponent:Ul,action_destroyer:Jl,attr:ke,binding_callbacks:Wl,bubble:ut,check_outros:Il,create_component:Vl,destroy_component:Kl,detach:Pe,element:tl,empty:Yl,flush:_e,group_outros:Zl,init:Gl,insert:Ue,listen:je,mount_component:Ql,noop:Xe,prevent_default:Xl,run_all:ll,safe_not_equal:xl,set_data:$l,set_input_value:qt,space:en,text:tn,toggle_class:xe,transition_in:_t,transition_out:ft}=window.__gradio__svelte__internal,{createEventDispatcher:ln}=window.__gradio__svelte__internal;function Ct(n){let e,t,l;return{c(){e=tl("input"),ke(e,"role","textbox"),ke(e,"tabindex","-1"),ke(e,"class","svelte-z9gpua"),xe(e,"header",n[5])},m(s,o){Ue(s,e,o),n[18](e),qt(e,n[10]),t||(l=[je(e,"input",n[19]),je(e,"blur",n[12]),Jl(n[11].call(null,e)),je(e,"keydown",n[17])],t=!0)},p(s,o){o&1024&&e.value!==s[10]&&qt(e,s[10]),o&32&&xe(e,"header",s[5])},d(s){s&&Pe(e),n[18](null),t=!1,ll(l)}}}function nn(n){let e=(n[9]?n[0]:n[3]||n[0])+"",t;return{c(){t=tn(e)},m(l,s){Ue(l,t,s)},p(l,s){s&521&&e!==(e=(l[9]?l[0]:l[3]||l[0])+"")&&$l(t,e)},i:Xe,o:Xe,d(l){l&&Pe(t)}}}function sn(n){let e,t;return e=new Fl({props:{message:n[0].toLocaleString(),latex_delimiters:n[7],line_breaks:n[8],chatbot:!1}}),{c(){Vl(e.$$.fragment)},m(l,s){Ql(e,l,s),t=!0},p(l,s){const o={};s&1&&(o.message=l[0].toLocaleString()),s&128&&(o.latex_delimiters=l[7]),s&256&&(o.line_breaks=l[8]),e.$set(o)},i(l){t||(_t(e.$$.fragment,l),t=!0)},o(l){ft(e.$$.fragment,l),t=!1},d(l){Kl(e,l)}}}function rn(n){let e,t;return{c(){e=new Pl(!1),t=Yl(),e.a=t},m(l,s){e.m(n[0],l,s),Ue(l,t,s)},p(l,s){s&1&&e.p(l[0])},i:Xe,o:Xe,d(l){l&&(Pe(t),e.d())}}}function an(n){let e,t,l,s,o,_,i,r=n[2]&&Ct(n);const c=[rn,sn,nn],u=[];function d(g,w){return g[6]==="html"?0:g[6]==="markdown"?1:2}return l=d(n),s=u[l]=c[l](n),{c(){r&&r.c(),e=en(),t=tl("span"),s.c(),ke(t,"tabindex","-1"),ke(t,"role","button"),ke(t,"style",n[4]),ke(t,"class","svelte-z9gpua"),xe(t,"edit",n[2])},m(g,w){r&&r.m(g,w),Ue(g,e,w),Ue(g,t,w),u[l].m(t,null),o=!0,_||(i=[je(t,"dblclick",n[15]),je(t,"focus",Xl(n[16]))],_=!0)},p(g,[w]){g[2]?r?r.p(g,w):(r=Ct(g),r.c(),r.m(e.parentNode,e)):r&&(r.d(1),r=null);let S=l;l=d(g),l===S?u[l].p(g,w):(Zl(),ft(u[S],1,1,()=>{u[S]=null}),Il(),s=u[l],s?s.p(g,w):(s=u[l]=c[l](g),s.c()),_t(s,1),s.m(t,null)),(!o||w&16)&&ke(t,"style",g[4]),(!o||w&4)&&xe(t,"edit",g[2])},i(g){o||(_t(s),o=!0)},o(g){ft(s),o=!1},d(g){g&&(Pe(e),Pe(t)),r&&r.d(g),u[l].d(),_=!1,ll(i)}}}function on(n,e,t){let l,{edit:s}=e,{value:o=""}=e,{display_value:_=null}=e,{styling:i=""}=e,{header:r=!1}=e,{datatype:c="str"}=e,{latex_delimiters:u}=e,{clear_on_focus:d=!1}=e,{select_on_focus:g=!1}=e,{line_breaks:w=!0}=e,{editable:S=!0}=e;const L=ln();let{el:z}=e;function H(k){return d&&t(10,l=""),g&&k.select(),k.focus(),{}}function R({currentTarget:k}){t(0,o=k.value),L("blur")}function A(k){ut.call(this,n,k)}function p(k){ut.call(this,n,k)}function E(k){ut.call(this,n,k)}function v(k){Wl[k?"unshift":"push"](()=>{z=k,t(1,z)})}function M(){l=this.value,t(10,l),t(0,o)}return n.$$set=k=>{"edit"in k&&t(2,s=k.edit),"value"in k&&t(0,o=k.value),"display_value"in k&&t(3,_=k.display_value),"styling"in k&&t(4,i=k.styling),"header"in k&&t(5,r=k.header),"datatype"in k&&t(6,c=k.datatype),"latex_delimiters"in k&&t(7,u=k.latex_delimiters),"clear_on_focus"in k&&t(13,d=k.clear_on_focus),"select_on_focus"in k&&t(14,g=k.select_on_focus),"line_breaks"in k&&t(8,w=k.line_breaks),"editable"in k&&t(9,S=k.editable),"el"in k&&t(1,z=k.el)},n.$$.update=()=>{n.$$.dirty&1&&t(10,l=o)},[o,z,s,_,i,r,c,u,w,S,l,H,R,d,g,A,p,E,v,M]}class tt extends Ul{constructor(e){super(),Gl(this,e,on,an,xl,{edit:2,value:0,display_value:3,styling:4,header:5,datatype:6,latex_delimiters:7,clear_on_focus:13,select_on_focus:14,line_breaks:8,editable:9,el:1})}get edit(){return this.$$.ctx[2]}set edit(e){this.$$set({edit:e}),_e()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_e()}get display_value(){return this.$$.ctx[3]}set display_value(e){this.$$set({display_value:e}),_e()}get styling(){return this.$$.ctx[4]}set styling(e){this.$$set({styling:e}),_e()}get header(){return this.$$.ctx[5]}set header(e){this.$$set({header:e}),_e()}get datatype(){return this.$$.ctx[6]}set datatype(e){this.$$set({datatype:e}),_e()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),_e()}get clear_on_focus(){return this.$$.ctx[13]}set clear_on_focus(e){this.$$set({clear_on_focus:e}),_e()}get select_on_focus(){return this.$$.ctx[14]}set select_on_focus(e){this.$$set({select_on_focus:e}),_e()}get line_breaks(){return this.$$.ctx[8]}set line_breaks(e){this.$$set({line_breaks:e}),_e()}get editable(){return this.$$.ctx[9]}set editable(e){this.$$set({editable:e}),_e()}get el(){return this.$$.ctx[1]}set el(e){this.$$set({el:e}),_e()}}const{ResizeObserverSingleton:un,SvelteComponent:_n,add_iframe_resize_listener:Dt,add_render_callback:Lt,append:He,attr:Ze,binding_callbacks:Rt,check_outros:nl,create_slot:ht,detach:lt,element:Oe,empty:sl,ensure_array_like:Bt,flush:Le,get_all_dirty_from_scope:ct,get_slot_changes:dt,group_outros:il,init:fn,insert:nt,listen:hn,outro_and_destroy_block:cn,resize_observer_content_box:dn,safe_not_equal:gn,set_style:ce,space:Et,text:bn,transition_in:Re,transition_out:Ne,update_keyed_each:mn,update_slot_base:gt}=window.__gradio__svelte__internal,{onMount:wn,tick:Ge}=window.__gradio__svelte__internal,kn=n=>({}),Ht=n=>({});function Nt(n,e,t){const l=n.slice();return l[34]=e[t],l}const pn=n=>({item:n[0]&256,index:n[0]&256}),Tt=n=>({item:n[34].data,index:n[34].index}),vn=n=>({}),Ot=n=>({});function Ft(n){let e=[],t=new Map,l,s,o=Bt(n[8]);const _=i=>i[34].data[0].id;for(let i=0;i<o.length;i+=1){let r=Nt(n,o,i),c=_(r);t.set(c,e[i]=jt(c,r))}return{c(){for(let i=0;i<e.length;i+=1)e[i].c();l=sl()},m(i,r){for(let c=0;c<e.length;c+=1)e[c]&&e[c].m(i,r);nt(i,l,r),s=!0},p(i,r){r[0]&1048832&&(o=Bt(i[8]),il(),e=mn(e,r,_,1,i,o,t,l.parentNode,cn,jt,l,Nt),nl())},i(i){if(!s){for(let r=0;r<o.length;r+=1)Re(e[r]);s=!0}},o(i){for(let r=0;r<e.length;r+=1)Ne(e[r]);s=!1},d(i){i&&lt(l);for(let r=0;r<e.length;r+=1)e[r].d(i)}}}function yn(n){let e;return{c(){e=bn(`Missing Table Row
					`)},m(t,l){nt(t,e,l)},d(t){t&&lt(e)}}}function jt(n,e){let t,l;const s=e[21].tbody,o=ht(s,e,e[20],Tt),_=o||yn();return{key:n,first:null,c(){t=sl(),_&&_.c(),this.first=t},m(i,r){nt(i,t,r),_&&_.m(i,r),l=!0},p(i,r){e=i,o&&o.p&&(!l||r[0]&1048832)&&gt(o,s,e,e[20],l?dt(s,e[20],r,pn):ct(e[20]),Tt)},i(i){l||(Re(_,i),l=!0)},o(i){Ne(_,i),l=!1},d(i){i&&lt(t),_&&_.d(i)}}}function An(n){let e,t,l,s,o,_,i,r,c,u,d,g,w;const S=n[21].thead,L=ht(S,n,n[20],Ot);let z=n[8].length&&n[8][0].data.length&&Ft(n);const H=n[21].tfoot,R=ht(H,n,n[20],Ht);return{c(){e=Oe("svelte-virtual-table-viewport"),t=Oe("table"),l=Oe("thead"),L&&L.c(),o=Et(),_=Oe("tbody"),z&&z.c(),i=Et(),r=Oe("tfoot"),R&&R.c(),Ze(l,"class","thead svelte-1txh5yn"),Lt(()=>n[22].call(l)),Ze(_,"class","tbody svelte-1txh5yn"),Ze(r,"class","tfoot svelte-1txh5yn"),Lt(()=>n[24].call(r)),Ze(t,"class","table svelte-1txh5yn"),ce(t,"height",Sn),ce(t,"--bw-svt-p-top",n[6]+"px"),ce(t,"--bw-svt-p-bottom",n[2]+"px"),ce(t,"--bw-svt-head-height",n[4]+"px"),ce(t,"--bw-svt-foot-height",n[5]+"px"),ce(t,"--bw-svt-avg-row-height",n[1]+"px")},m(A,p){nt(A,e,p),He(e,t),He(t,l),L&&L.m(l,null),s=Dt(l,n[22].bind(l)),He(t,o),He(t,_),z&&z.m(_,null),n[23](_),He(t,i),He(t,r),R&&R.m(r,null),c=Dt(r,n[24].bind(r)),n[25](t),u=dn.observe(t,n[26].bind(t)),d=!0,g||(w=hn(t,"scroll",n[9]),g=!0)},p(A,p){L&&L.p&&(!d||p[0]&1048576)&&gt(L,S,A,A[20],d?dt(S,A[20],p,vn):ct(A[20]),Ot),A[8].length&&A[8][0].data.length?z?(z.p(A,p),p[0]&256&&Re(z,1)):(z=Ft(A),z.c(),Re(z,1),z.m(_,null)):z&&(il(),Ne(z,1,1,()=>{z=null}),nl()),R&&R.p&&(!d||p[0]&1048576)&&gt(R,H,A,A[20],d?dt(H,A[20],p,kn):ct(A[20]),Ht),(!d||p[0]&64)&&ce(t,"--bw-svt-p-top",A[6]+"px"),(!d||p[0]&4)&&ce(t,"--bw-svt-p-bottom",A[2]+"px"),(!d||p[0]&16)&&ce(t,"--bw-svt-head-height",A[4]+"px"),(!d||p[0]&32)&&ce(t,"--bw-svt-foot-height",A[5]+"px"),(!d||p[0]&2)&&ce(t,"--bw-svt-avg-row-height",A[1]+"px")},i(A){d||(Re(L,A),Re(z),Re(R,A),d=!0)},o(A){Ne(L,A),Ne(z),Ne(R,A),d=!1},d(A){A&&lt(e),L&&L.d(A),s(),z&&z.d(),n[23](null),R&&R.d(A),c(),n[25](null),u(),g=!1,w()}}}let Sn="100%";function zn(n,e){if(!n)return 0;const t=getComputedStyle(n);return parseInt(t.getPropertyValue(e))}function Mn(n,e,t){let l,{$$slots:s={},$$scope:o}=e,{items:_=[]}=e,{max_height:i}=e,{actual_height:r}=e,{table_scrollbar_width:c}=e,{start:u=0}=e,{end:d=0}=e,{selected:g}=e,w,S=0,L,z=0,H=0,R=[],A,p,E=0,v,M=0,k=[],$,ee=0;async function N(C){if(M===0)return;const{scrollTop:Q}=v;t(13,c=v.offsetWidth-v.clientWidth),ee=E-(Q-z);let te=u;for(;ee<i&&te<C.length;){let j=p[te-u];j||(t(11,d=te+1),await Ge(),j=p[te-u]);let le=j?.getBoundingClientRect().height;le||(le=w);const fe=R[te]=le;ee+=fe,te+=1}t(11,d=te);const re=C.length-d,ae=v.offsetHeight-v.clientHeight;ae>0&&(ee+=ae);let ne=R.filter(j=>typeof j=="number");t(1,w=ne.reduce((j,le)=>j+le,0)/ne.length),t(2,S=re*w),R.length=C.length,await Ge(),i?ee<i?t(12,r=ee+2):t(12,r=i):t(12,r=ee+1),await Ge()}async function be(C){requestAnimationFrame(async()=>{if(typeof C!="number")return;const Q=typeof C!="number"?!1:F(C);Q!==!0&&(Q==="back"&&await T(C,{behavior:"instant"}),Q==="forwards"&&await T(C,{behavior:"instant"},!0))})}function F(C){const Q=p&&p[C-u];if(!Q&&C<u)return"back";if(!Q&&C>=d-1)return"forwards";const{top:te}=v.getBoundingClientRect(),{top:re,bottom:ae}=Q.getBoundingClientRect();return re-te<37?"back":ae-te>M?"forwards":!0}async function ue(C){const Q=v.scrollTop;p=L.children;const te=l.length<u,re=zn(p[1],"border-top-width"),ae=0;te&&await T(l.length-1,{behavior:"auto"});let ne=0;for(let Z=0;Z<p.length;Z+=1)R[u+Z]=p[Z].getBoundingClientRect().height;let j=0,le=z+re/2,fe=[];for(;j<l.length;){const Z=R[j]||w;if(fe[j]=Z,le+Z+ae>Q){ne=j,t(6,E=le-(z+re/2));break}le+=Z,j+=1}for(ne=Math.max(0,ne);j<l.length;){const Z=R[j]||w;if(le+=Z,j+=1,le>Q+M)break}t(10,u=ne),t(11,d=j);const De=l.length-d;d===0&&t(11,d=10),t(1,w=(le-z)/d);let Ie=De*w;for(;j<l.length;)j+=1,R[j]=w;t(2,S=Ie),isFinite(S)||t(2,S=2e5)}async function T(C,Q,te=!1){await Ge();const re=w;let ae=C*re;te&&(ae=ae-M+re+z);const ne=v.offsetHeight-v.clientHeight;ne>0&&(ae+=ne);const j={top:ae,behavior:"smooth",...Q};v.scrollTo(j)}wn(()=>{p=L.children,t(18,A=!0),N(_)});function b(){z=this.offsetHeight,t(4,z)}function y(C){Rt[C?"unshift":"push"](()=>{L=C,t(3,L)})}function O(){H=this.offsetHeight,t(5,H)}function h(C){Rt[C?"unshift":"push"](()=>{v=C,t(7,v)})}function I(){$=un.entries.get(this)?.contentRect,t(0,$)}return n.$$set=C=>{"items"in C&&t(14,_=C.items),"max_height"in C&&t(15,i=C.max_height),"actual_height"in C&&t(12,r=C.actual_height),"table_scrollbar_width"in C&&t(13,c=C.table_scrollbar_width),"start"in C&&t(10,u=C.start),"end"in C&&t(11,d=C.end),"selected"in C&&t(16,g=C.selected),"$$scope"in C&&t(20,o=C.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&1&&(M=$?.height||0),n.$$.dirty[0]&16384&&t(19,l=_),n.$$.dirty[0]&786432&&A&&requestAnimationFrame(()=>N(l)),n.$$.dirty[0]&65536&&be(g),n.$$.dirty[0]&527360&&t(8,k=l.slice(u,d).map((C,Q)=>({index:Q+u,data:C})))},[$,w,S,L,z,H,E,v,k,ue,u,d,r,c,_,i,g,T,A,l,o,s,b,y,O,h,I]}class qn extends _n{constructor(e){super(),fn(this,e,Mn,An,gn,{items:14,max_height:15,actual_height:12,table_scrollbar_width:13,start:10,end:11,selected:16,scroll_to_index:17},null,[-1,-1])}get items(){return this.$$.ctx[14]}set items(e){this.$$set({items:e}),Le()}get max_height(){return this.$$.ctx[15]}set max_height(e){this.$$set({max_height:e}),Le()}get actual_height(){return this.$$.ctx[12]}set actual_height(e){this.$$set({actual_height:e}),Le()}get table_scrollbar_width(){return this.$$.ctx[13]}set table_scrollbar_width(e){this.$$set({table_scrollbar_width:e}),Le()}get start(){return this.$$.ctx[10]}set start(e){this.$$set({start:e}),Le()}get end(){return this.$$.ctx[11]}set end(e){this.$$set({end:e}),Le()}get selected(){return this.$$.ctx[16]}set selected(e){this.$$set({selected:e}),Le()}get scroll_to_index(){return this.$$.ctx[17]}}const{ResizeObserverSingleton:Cn,SvelteComponent:Dn,action_destroyer:Ln,add_flush_callback:ye,append:B,attr:m,bind:Ae,binding_callbacks:de,check_outros:Be,create_component:Me,destroy_component:qe,detach:K,element:U,empty:Rn,ensure_array_like:pe,flush:G,group_outros:Ee,init:Bn,insert:Y,listen:ve,mount_component:Ce,outro_and_destroy_block:$e,resize_observer_content_box:En,run_all:rl,safe_not_equal:Hn,set_data:Je,set_style:Se,space:ie,svg_element:ze,text:We,toggle_class:J,transition_in:V,transition_out:x,update_keyed_each:et}=window.__gradio__svelte__internal,{createEventDispatcher:Nn,tick:Qe,onMount:Tn}=window.__gradio__svelte__internal;function Pt(n,e,t){const l=n.slice();return l[90]=e[t].value,l[91]=e[t].id,l[94]=e,l[95]=t,l}function Ut(n,e,t){const l=n.slice();return l[90]=e[t].value,l[91]=e[t].id,l[92]=e,l[93]=t,l}function Jt(n,e,t){const l=n.slice();return l[90]=e[t].value,l[91]=e[t].id,l[96]=e,l[93]=t,l}function Wt(n,e,t){const l=n.slice();return l[90]=e[t].value,l[91]=e[t].id,l[95]=t,l}function It(n){let e,t;return{c(){e=U("p"),t=We(n[1]),m(e,"class","svelte-1bvc1p0")},m(l,s){Y(l,e,s),B(e,t)},p(l,s){s[0]&2&&Je(t,l[1])},d(l){l&&K(e)}}}function Vt(n){let e,t;return{c(){e=U("caption"),t=We(n[1]),m(e,"class","sr-only")},m(l,s){Y(l,e,s),B(e,t)},p(l,s){s[0]&2&&Je(t,l[1])},d(l){l&&K(e)}}}function Kt(n,e){let t,l,s,o,_,i,r,c,u,d,g;return s=new tt({props:{value:e[90],latex_delimiters:e[5],line_breaks:e[11],header:!0,edit:!1,el:null}}),{key:n,first:null,c(){t=U("th"),l=U("div"),Me(s.$$.fragment),o=ie(),_=U("div"),i=ze("svg"),r=ze("path"),u=ie(),m(r,"d","M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z"),m(i,"width","1em"),m(i,"height","1em"),m(i,"viewBox","0 0 9 7"),m(i,"fill","none"),m(i,"xmlns","http://www.w3.org/2000/svg"),m(i,"class","svelte-1bvc1p0"),m(_,"class",c="sort-button "+e[19]+" svelte-1bvc1p0"),J(_,"sorted",e[20]===e[95]),J(_,"des",e[20]===e[95]&&e[19]==="des"),m(l,"class","cell-wrap svelte-1bvc1p0"),m(t,"aria-sort",d=e[37](e[90],e[20],e[19])),m(t,"class","svelte-1bvc1p0"),J(t,"editing",e[27]===e[95]),Se(t,"width",e[12].length?e[12][e[95]]:void 0),this.first=t},m(w,S){Y(w,t,S),B(t,l),Ce(s,l,null),B(l,o),B(l,_),B(_,i),B(i,r),B(t,u),g=!0},p(w,S){e=w;const L={};S[0]&33554432&&(L.value=e[90]),S[0]&32&&(L.latex_delimiters=e[5]),S[0]&2048&&(L.line_breaks=e[11]),s.$set(L),(!g||S[0]&524288&&c!==(c="sort-button "+e[19]+" svelte-1bvc1p0"))&&m(_,"class",c),(!g||S[0]&35127296)&&J(_,"sorted",e[20]===e[95]),(!g||S[0]&35127296)&&J(_,"des",e[20]===e[95]&&e[19]==="des"),(!g||S[0]&35127296&&d!==(d=e[37](e[90],e[20],e[19])))&&m(t,"aria-sort",d),(!g||S[0]&167772160)&&J(t,"editing",e[27]===e[95]),S[0]&33558528&&Se(t,"width",e[12].length?e[12][e[95]]:void 0)},i(w){g||(V(s.$$.fragment,w),g=!0)},o(w){x(s.$$.fragment,w),g=!1},d(w){w&&K(t),qe(s)}}}function Yt(n,e){let t,l,s,o,_=e[93],i;s=new tt({props:{value:e[90],latex_delimiters:e[5],line_breaks:e[11],datatype:Array.isArray(e[0])?e[0][e[93]]:e[0],edit:!1,el:null}});const r=()=>e[54](t,_),c=()=>e[54](null,_);return{key:n,first:null,c(){t=U("td"),l=U("div"),Me(s.$$.fragment),o=ie(),m(l,"class","cell-wrap svelte-1bvc1p0"),m(t,"tabindex","-1"),m(t,"class","svelte-1bvc1p0"),this.first=t},m(u,d){Y(u,t,d),B(t,l),Ce(s,l,null),B(t,o),r(),i=!0},p(u,d){e=u;const g={};d[1]&32&&(g.value=e[90]),d[0]&32&&(g.latex_delimiters=e[5]),d[0]&2048&&(g.line_breaks=e[11]),d[0]&1|d[1]&32&&(g.datatype=Array.isArray(e[0])?e[0][e[93]]:e[0]),s.$set(g),_!==e[93]&&(c(),_=e[93],r())},i(u){i||(V(s.$$.fragment,u),i=!0)},o(u){x(s.$$.fragment,u),i=!1},d(u){u&&K(t),qe(s),c()}}}function Zt(n){let e,t;return{c(){e=U("caption"),t=We(n[1]),m(e,"class","sr-only")},m(l,s){Y(l,e,s),B(e,t)},p(l,s){s[0]&2&&Je(t,l[1])},d(l){l&&K(e)}}}function On(n){let e,t=n[1]&&n[1].length!==0&&Zt(n);return{c(){t&&t.c(),e=Rn()},m(l,s){t&&t.m(l,s),Y(l,e,s)},p(l,s){l[1]&&l[1].length!==0?t?t.p(l,s):(t=Zt(l),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(l){l&&K(e),t&&t.d(l)}}}function Gt(n,e){let t,l,s,o,_,i,r,c,u,d,g,w,S,L,z;function H(v){e[63](v,e[95])}function R(v){e[64](v,e[91])}function A(){return e[65](e[95])}let p={latex_delimiters:e[5],line_breaks:e[11],edit:e[27]===e[95],select_on_focus:e[28],header:!0};e[25][e[95]].value!==void 0&&(p.value=e[25][e[95]].value),e[24][e[91]].input!==void 0&&(p.el=e[24][e[91]].input),s=new tt({props:p}),de.push(()=>Ae(s,"value",H)),de.push(()=>Ae(s,"el",R)),s.$on("keydown",e[43]),s.$on("dblclick",A);function E(){return e[66](e[95])}return{key:n,first:null,c(){t=U("th"),l=U("div"),Me(s.$$.fragment),i=ie(),r=U("div"),c=ze("svg"),u=ze("path"),g=ie(),m(u,"d","M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z"),m(c,"width","1em"),m(c,"height","1em"),m(c,"viewBox","0 0 9 7"),m(c,"fill","none"),m(c,"xmlns","http://www.w3.org/2000/svg"),m(c,"class","svelte-1bvc1p0"),m(r,"class",d="sort-button "+e[19]+" svelte-1bvc1p0"),J(r,"sorted",e[20]===e[95]),J(r,"des",e[20]===e[95]&&e[19]==="des"),m(l,"class","cell-wrap svelte-1bvc1p0"),m(t,"aria-sort",w=e[37](e[90],e[20],e[19])),Se(t,"width","var(--cell-width-"+e[95]+")"),m(t,"class","svelte-1bvc1p0"),J(t,"focus",e[27]===e[95]||e[29]===e[95]),this.first=t},m(v,M){Y(v,t,M),B(t,l),Ce(s,l,null),B(l,i),B(l,r),B(r,c),B(c,u),B(t,g),S=!0,L||(z=ve(r,"click",E),L=!0)},p(v,M){e=v;const k={};M[0]&32&&(k.latex_delimiters=e[5]),M[0]&2048&&(k.line_breaks=e[11]),M[0]&167772160&&(k.edit=e[27]===e[95]),M[0]&268435456&&(k.select_on_focus=e[28]),!o&&M[0]&33554432&&(o=!0,k.value=e[25][e[95]].value,ye(()=>o=!1)),!_&&M[0]&50331648&&(_=!0,k.el=e[24][e[91]].input,ye(()=>_=!1)),s.$set(k),(!S||M[0]&524288&&d!==(d="sort-button "+e[19]+" svelte-1bvc1p0"))&&m(r,"class",d),(!S||M[0]&35127296)&&J(r,"sorted",e[20]===e[95]),(!S||M[0]&35127296)&&J(r,"des",e[20]===e[95]&&e[19]==="des"),(!S||M[0]&35127296&&w!==(w=e[37](e[90],e[20],e[19])))&&m(t,"aria-sort",w),(!S||M[0]&33554432)&&Se(t,"width","var(--cell-width-"+e[95]+")"),(!S||M[0]&704643072)&&J(t,"focus",e[27]===e[95]||e[29]===e[95])},i(v){S||(V(s.$$.fragment,v),S=!0)},o(v){x(s.$$.fragment,v),S=!1},d(v){v&&K(t),qe(s),L=!1,z()}}}function Fn(n){let e,t=[],l=new Map,s,o=pe(n[25]);const _=i=>i[91];for(let i=0;i<o.length;i+=1){let r=Pt(n,o,i),c=_(r);l.set(c,t[i]=Gt(c,r))}return{c(){e=U("tr");for(let i=0;i<t.length;i+=1)t[i].c();m(e,"slot","thead"),m(e,"class","svelte-1bvc1p0")},m(i,r){Y(i,e,r);for(let c=0;c<t.length;c+=1)t[c]&&t[c].m(e,null);s=!0},p(i,r){r[0]&991430688|r[1]&7232&&(o=pe(i[25]),Ee(),t=et(t,r,_,1,i,o,l,e,$e,Gt,null,Pt),Be())},i(i){if(!s){for(let r=0;r<o.length;r+=1)V(t[r]);s=!0}},o(i){for(let r=0;r<t.length;r+=1)x(t[r]);s=!1},d(i){i&&K(e);for(let r=0;r<t.length;r+=1)t[r].d()}}}function Qt(n,e){let t,l,s,o,_,i,r,c=`var(--cell-width-${e[93]})`,u,d,g;function w(A){e[57](A,e[88],e[93])}function S(A){e[58](A,e[91])}let L={display_value:e[15]?.[e[88]]?.[e[93]],latex_delimiters:e[5],line_breaks:e[11],editable:e[6],edit:ge(e[23],[e[88],e[93]]),datatype:Array.isArray(e[0])?e[0][e[93]]:e[0],clear_on_focus:e[26]};e[18][e[88]][e[93]].value!==void 0&&(L.value=e[18][e[88]][e[93]].value),e[24][e[91]].input!==void 0&&(L.el=e[24][e[91]].input),s=new tt({props:L}),de.push(()=>Ae(s,"value",w)),de.push(()=>Ae(s,"el",S)),s.$on("blur",e[59]);function z(){return e[60](e[88],e[93])}function H(){return e[61](e[88],e[93])}function R(){return e[62](e[88],e[93])}return{key:n,first:null,c(){t=U("td"),l=U("div"),Me(s.$$.fragment),i=ie(),m(l,"class","cell-wrap svelte-1bvc1p0"),m(t,"tabindex","0"),m(t,"style",r=e[16]?.[e[88]]?.[e[93]]||""),m(t,"class","svelte-1bvc1p0"),J(t,"focus",ge(e[17],[e[88],e[93]])),Se(t,"width",c),this.first=t},m(A,p){Y(A,t,p),B(t,l),Ce(s,l,null),B(t,i),u=!0,d||(g=[ve(t,"touchstart",z,{passive:!0}),ve(t,"click",H),ve(t,"dblclick",R)],d=!0)},p(A,p){e=A;const E={};p[0]&32768|p[2]&201326592&&(E.display_value=e[15]?.[e[88]]?.[e[93]]),p[0]&32&&(E.latex_delimiters=e[5]),p[0]&2048&&(E.line_breaks=e[11]),p[0]&64&&(E.editable=e[6]),p[0]&8388608|p[2]&201326592&&(E.edit=ge(e[23],[e[88],e[93]])),p[0]&1|p[2]&134217728&&(E.datatype=Array.isArray(e[0])?e[0][e[93]]:e[0]),p[0]&67108864&&(E.clear_on_focus=e[26]),!o&&p[0]&262144|p[2]&201326592&&(o=!0,E.value=e[18][e[88]][e[93]].value,ye(()=>o=!1)),!_&&p[0]&16777216|p[2]&134217728&&(_=!0,E.el=e[24][e[91]].input,ye(()=>_=!1)),s.$set(E),(!u||p[0]&65536|p[2]&201326592&&r!==(r=e[16]?.[e[88]]?.[e[93]]||""))&&m(t,"style",r),(!u||p[0]&131072|p[2]&201326592)&&J(t,"focus",ge(e[17],[e[88],e[93]]));const v=p[0]&65536|p[2]&201326592;(p[0]&65536|p[2]&201326592&&c!==(c=`var(--cell-width-${e[93]})`)||v)&&Se(t,"width",c)},i(A){u||(V(s.$$.fragment,A),u=!0)},o(A){x(s.$$.fragment,A),u=!1},d(A){A&&K(t),qe(s),d=!1,rl(g)}}}function jn(n){let e,t=[],l=new Map,s,o=pe(n[89]);const _=i=>i[91];for(let i=0;i<o.length;i+=1){let r=Ut(n,o,i),c=_(r);l.set(c,t[i]=Qt(c,r))}return{c(){e=U("tr");for(let i=0;i<t.length;i+=1)t[i].c();m(e,"slot","tbody"),m(e,"class","svelte-1bvc1p0"),J(e,"row_odd",n[88]%2===0)},m(i,r){Y(i,e,r);for(let c=0;c<t.length;c+=1)t[c]&&t[c].m(e,null);s=!0},p(i,r){r[0]&92768353|r[1]&641|r[2]&201326592&&(o=pe(i[89]),Ee(),t=et(t,r,_,1,i,o,l,e,$e,Qt,null,Ut),Be()),(!s||r[2]&67108864)&&J(e,"row_odd",i[88]%2===0)},i(i){if(!s){for(let r=0;r<o.length;r+=1)V(t[r]);s=!0}},o(i){for(let r=0;r<t.length;r+=1)x(t[r]);s=!1},d(i){i&&K(e);for(let r=0;r<t.length;r+=1)t[r].d()}}}function Pn(n){let e,t,l,s,o;function _(u){n[67](u)}function i(u){n[68](u)}function r(u){n[69](u)}let c={max_height:n[10],selected:n[35],$$slots:{tbody:[jn,({index:u,item:d})=>({88:u,89:d}),({index:u,item:d})=>[0,0,(u?67108864:0)|(d?134217728:0)]],thead:[Fn],default:[On]},$$scope:{ctx:n}};return n[18]!==void 0&&(c.items=n[18]),n[33]!==void 0&&(c.actual_height=n[33]),n[34]!==void 0&&(c.table_scrollbar_width=n[34]),e=new qn({props:c}),de.push(()=>Ae(e,"items",_)),de.push(()=>Ae(e,"actual_height",i)),de.push(()=>Ae(e,"table_scrollbar_width",r)),{c(){Me(e.$$.fragment)},m(u,d){Ce(e,u,d),o=!0},p(u,d){const g={};d[0]&1024&&(g.max_height=u[10]),d[1]&16&&(g.selected=u[35]),d[0]&1067419747|d[1]&1|d[2]&201326592|d[3]&32&&(g.$$scope={dirty:d,ctx:u}),!t&&d[0]&262144&&(t=!0,g.items=u[18],ye(()=>t=!1)),!l&&d[1]&4&&(l=!0,g.actual_height=u[33],ye(()=>l=!1)),!s&&d[1]&8&&(s=!0,g.table_scrollbar_width=u[34],ye(()=>s=!1)),e.$set(g)},i(u){o||(V(e.$$.fragment,u),o=!0)},o(u){x(e.$$.fragment,u),o=!1},d(u){qe(e,u)}}}function Xt(n){let e,t,l,s=n[4][1]==="dynamic"&&xt(n),o=n[3][1]==="dynamic"&&$t(n);return{c(){e=U("div"),s&&s.c(),t=ie(),o&&o.c(),m(e,"class","controls-wrap svelte-1bvc1p0")},m(_,i){Y(_,e,i),s&&s.m(e,null),B(e,t),o&&o.m(e,null),l=!0},p(_,i){_[4][1]==="dynamic"?s?(s.p(_,i),i[0]&16&&V(s,1)):(s=xt(_),s.c(),V(s,1),s.m(e,t)):s&&(Ee(),x(s,1,1,()=>{s=null}),Be()),_[3][1]==="dynamic"?o?(o.p(_,i),i[0]&8&&V(o,1)):(o=$t(_),o.c(),V(o,1),o.m(e,null)):o&&(Ee(),x(o,1,1,()=>{o=null}),Be())},i(_){l||(V(s),V(o),l=!0)},o(_){x(s),x(o),l=!1},d(_){_&&K(e),s&&s.d(),o&&o.d()}}}function xt(n){let e,t,l;return t=new el({props:{variant:"secondary",size:"sm",$$slots:{default:[Un]},$$scope:{ctx:n}}}),t.$on("click",n[74]),{c(){e=U("span"),Me(t.$$.fragment),m(e,"class","button-wrap svelte-1bvc1p0")},m(s,o){Y(s,e,o),Ce(t,e,null),l=!0},p(s,o){const _={};o[0]&512|o[3]&32&&(_.$$scope={dirty:o,ctx:s}),t.$set(_)},i(s){l||(V(t.$$.fragment,s),l=!0)},o(s){x(t.$$.fragment,s),l=!1},d(s){s&&K(e),qe(t)}}}function Un(n){let e,t,l,s=n[9]("dataframe.new_row")+"",o;return{c(){e=ze("svg"),t=ze("path"),l=ie(),o=We(s),m(t,"fill","currentColor"),m(t,"d","M24.59 16.59L17 24.17V4h-2v20.17l-7.59-7.58L6 18l10 10l10-10l-1.41-1.41z"),m(e,"xmlns","http://www.w3.org/2000/svg"),m(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),m(e,"aria-hidden","true"),m(e,"role","img"),m(e,"width","1em"),m(e,"height","1em"),m(e,"preserveAspectRatio","xMidYMid meet"),m(e,"viewBox","0 0 32 32"),m(e,"class","svelte-1bvc1p0")},m(_,i){Y(_,e,i),B(e,t),Y(_,l,i),Y(_,o,i)},p(_,i){i[0]&512&&s!==(s=_[9]("dataframe.new_row")+"")&&Je(o,s)},d(_){_&&(K(e),K(l),K(o))}}}function $t(n){let e,t,l;return t=new el({props:{variant:"secondary",size:"sm",$$slots:{default:[Jn]},$$scope:{ctx:n}}}),t.$on("click",n[75]),{c(){e=U("span"),Me(t.$$.fragment),m(e,"class","button-wrap svelte-1bvc1p0")},m(s,o){Y(s,e,o),Ce(t,e,null),l=!0},p(s,o){const _={};o[0]&512|o[3]&32&&(_.$$scope={dirty:o,ctx:s}),t.$set(_)},i(s){l||(V(t.$$.fragment,s),l=!0)},o(s){x(t.$$.fragment,s),l=!1},d(s){s&&K(e),qe(t)}}}function Jn(n){let e,t,l,s=n[9]("dataframe.new_column")+"",o;return{c(){e=ze("svg"),t=ze("path"),l=ie(),o=We(s),m(t,"fill","currentColor"),m(t,"d","m18 6l-1.43 1.393L24.15 15H4v2h20.15l-7.58 7.573L18 26l10-10L18 6z"),m(e,"xmlns","http://www.w3.org/2000/svg"),m(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),m(e,"aria-hidden","true"),m(e,"role","img"),m(e,"width","1em"),m(e,"height","1em"),m(e,"preserveAspectRatio","xMidYMid meet"),m(e,"viewBox","0 0 32 32"),m(e,"class","svelte-1bvc1p0")},m(_,i){Y(_,e,i),B(e,t),Y(_,l,i),Y(_,o,i)},p(_,i){i[0]&512&&s!==(s=_[9]("dataframe.new_column")+"")&&Je(o,s)},d(_){_&&(K(e),K(l),K(o))}}}function Wn(n){let e,t,l,s,o,_,i,r=[],c=new Map,u,d,g,w=[],S=new Map,L,z,H,R,A,p,E,v,M=n[1]&&n[1].length!==0&&n[2]&&It(n),k=n[1]&&n[1].length!==0&&Vt(n),$=pe(n[25]);const ee=b=>b[91];for(let b=0;b<$.length;b+=1){let y=Wt(n,$,b),O=ee(y);c.set(O,r[b]=Kt(O,y))}let N=pe(n[36]);const be=b=>b[91];for(let b=0;b<N.length;b+=1){let y=Jt(n,N,b),O=be(y);S.set(O,w[b]=Yt(O,y))}function F(b){n[70](b)}let ue={upload:n[13],stream_handler:n[14],flex:!1,center:!1,boundedheight:!1,disable_click:!0,root:n[8],$$slots:{default:[Pn]},$$scope:{ctx:n}};n[30]!==void 0&&(ue.dragging=n[30]),H=new Ol({props:ue}),de.push(()=>Ae(H,"dragging",F)),H.$on("load",n[71]);let T=n[6]&&Xt(n);return{c(){e=U("div"),M&&M.c(),t=ie(),l=U("div"),s=U("table"),k&&k.c(),o=ie(),_=U("thead"),i=U("tr");for(let b=0;b<r.length;b+=1)r[b].c();u=ie(),d=U("tbody"),g=U("tr");for(let b=0;b<w.length;b+=1)w[b].c();z=ie(),Me(H.$$.fragment),A=ie(),T&&T.c(),m(i,"class","svelte-1bvc1p0"),m(_,"class","svelte-1bvc1p0"),m(g,"class","svelte-1bvc1p0"),m(s,"class","svelte-1bvc1p0"),J(s,"fixed-layout",n[12].length!=0),m(l,"class","table-wrap svelte-1bvc1p0"),Se(l,"height",n[33]+"px"),m(l,"role","grid"),m(l,"tabindex","0"),J(l,"dragging",n[30]),J(l,"no-wrap",!n[7]),m(e,"class","svelte-1bvc1p0"),J(e,"label",n[1]&&n[1].length!==0)},m(b,y){Y(b,e,y),M&&M.m(e,null),B(e,t),B(e,l),B(l,s),k&&k.m(s,null),B(s,o),B(s,_),B(_,i);for(let O=0;O<r.length;O+=1)r[O]&&r[O].m(i,null);B(s,u),B(s,d),B(d,g);for(let O=0;O<w.length;O+=1)w[O]&&w[O].m(g,null);L=En.observe(s,n[55].bind(s)),n[56](s),B(l,z),Ce(H,l,null),n[72](l),B(e,A),T&&T.m(e,null),p=!0,E||(v=[ve(window,"click",n[46]),ve(window,"touchstart",n[46]),ve(window,"resize",n[53]),ve(l,"keydown",n[73]),Ln(Tl.call(null,e))],E=!0)},p(b,y){b[1]&&b[1].length!==0&&b[2]?M?M.p(b,y):(M=It(b),M.c(),M.m(e,t)):M&&(M.d(1),M=null),b[1]&&b[1].length!==0?k?k.p(b,y):(k=Vt(b),k.c(),k.m(s,o)):k&&(k.d(1),k=null),y[0]&169351200|y[1]&64&&($=pe(b[25]),Ee(),r=et(r,y,ee,1,b,$,c,i,$e,Kt,null,Wt),Be()),y[0]&2099233|y[1]&32&&(N=pe(b[36]),Ee(),w=et(w,y,be,1,b,N,S,g,$e,Yt,null,Jt),Be()),(!p||y[0]&4096)&&J(s,"fixed-layout",b[12].length!=0);const O={};y[0]&8192&&(O.upload=b[13]),y[0]&16384&&(O.stream_handler=b[14]),y[0]&256&&(O.root=b[8]),y[0]&1067420771|y[1]&29|y[3]&32&&(O.$$scope={dirty:y,ctx:b}),!R&&y[0]&1073741824&&(R=!0,O.dragging=b[30],ye(()=>R=!1)),H.$set(O),(!p||y[1]&4)&&Se(l,"height",b[33]+"px"),(!p||y[0]&1073741824)&&J(l,"dragging",b[30]),(!p||y[0]&128)&&J(l,"no-wrap",!b[7]),b[6]?T?(T.p(b,y),y[0]&64&&V(T,1)):(T=Xt(b),T.c(),V(T,1),T.m(e,null)):T&&(Ee(),x(T,1,1,()=>{T=null}),Be()),(!p||y[0]&2)&&J(e,"label",b[1]&&b[1].length!==0)},i(b){if(!p){for(let y=0;y<$.length;y+=1)V(r[y]);for(let y=0;y<N.length;y+=1)V(w[y]);V(H.$$.fragment,b),V(T),p=!0}},o(b){for(let y=0;y<r.length;y+=1)x(r[y]);for(let y=0;y<w.length;y+=1)x(w[y]);x(H.$$.fragment,b),x(T),p=!1},d(b){b&&K(e),M&&M.d(),k&&k.d();for(let y=0;y<r.length;y+=1)r[y].d();for(let y=0;y<w.length;y+=1)w[y].d();L(),n[56](null),qe(H),n[72](null),T&&T.d(),E=!1,rl(v)}}}function Fe(){return Math.random().toString(36).substring(2,15)}function In(n,e){return e.filter(t);function t(l){var s=-1;return n.split(`
`).every(o);function o(_){if(!_)return!0;var i=_.split(l).length;return s<0&&(s=i),s===i&&i>1}}}function Vn(n){const e=atob(n.split(",")[1]),t=n.split(",")[0].split(":")[1].split(";")[0],l=new ArrayBuffer(e.length),s=new Uint8Array(l);for(let o=0;o<e.length;o++)s[o]=e.charCodeAt(o);return new Blob([l],{type:t})}function Kn(n,e,t){let l,s,{datatype:o}=e,{label:_=null}=e,{show_label:i=!0}=e,{headers:r=[]}=e,{values:c=[]}=e,{col_count:u}=e,{row_count:d}=e,{latex_delimiters:g}=e,{editable:w=!0}=e,{wrap:S=!1}=e,{root:L}=e,{i18n:z}=e,{height:H=500}=e,{line_breaks:R=!0}=e,{column_widths:A=[]}=e,{upload:p}=e,{stream_handler:E}=e,v=!1,{display_value:M=null}=e,{styling:k=null}=e,$;const ee=Nn();let N=!1;const be=(a,f)=>h?.[a]?.[f]?.value;let F={};function ue(a){let f=a||[];if(u[1]==="fixed"&&f.length<u[0]){const q=Array(u[0]-f.length).fill("").map((D,P)=>`${P+f.length}`);f=f.concat(q)}return!f||f.length===0?Array(u[0]).fill(0).map((q,D)=>{const P=Fe();return t(24,F[P]={cell:null,input:null},F),{id:P,value:JSON.stringify(D+1)}}):f.map((q,D)=>{const P=Fe();return t(24,F[P]={cell:null,input:null},F),{id:P,value:q??""}})}function T(a){const f=a.length;return Array(d[1]==="fixed"||f<d[0]?d[0]:f).fill(0).map((q,D)=>Array(u[1]==="fixed"?u[0]:f>0?a[0].length:r.length).fill(0).map((P,se)=>{const he=Fe();return t(24,F[he]=F[he]||{input:null,cell:null},F),{value:a?.[D]?.[se]??"",id:he}}))}let b=ue(r),y;function O(){t(25,b=ue(r)),t(51,y=r.slice()),C()}let h=[[]],I;async function C(){ee("change",{data:h.map(a=>a.map(({value:f})=>f)),headers:b.map(a=>a.value),metadata:w?null:{display_value:M,styling:k}})}function Q(a,f,q){if(!f)return"none";if(r[f]===a){if(q==="asc")return"ascending";if(q==="des")return"descending"}return"none"}function te(a){return h.reduce((f,q,D)=>{const P=q.reduce((se,he,Ye)=>a===he.id?Ye:se,-1);return P===-1?f:[D,P]},[-1,-1])}async function re(a,f){!w||ge(N,[a,f])||t(23,N=[a,f])}function ae(a,f){const q={ArrowRight:[0,1],ArrowLeft:[0,-1],ArrowDown:[1,0],ArrowUp:[-1,0]}[a],D=f[0]+q[0],P=f[1]+q[1];if(D<0&&P<=0)t(29,X=P),t(17,v=!1);else{const se=h[D]?.[P];t(17,v=se?[D,P]:v)}}let ne=!1;async function j(a){if(X!==!1&&Z===!1)switch(a.key){case"ArrowDown":t(17,v=[0,X]),t(29,X=!1);return;case"ArrowLeft":t(29,X=X>0?X-1:X);return;case"ArrowRight":t(29,X=X<b.length-1?X+1:X);return;case"Escape":a.preventDefault(),t(29,X=!1);break;case"Enter":a.preventDefault();break}if(!v)return;const[f,q]=v;switch(a.key){case"ArrowRight":case"ArrowLeft":case"ArrowDown":case"ArrowUp":if(N)break;a.preventDefault(),ae(a.key,[f,q]);break;case"Escape":if(!w)break;a.preventDefault(),t(23,N=!1);break;case"Enter":if(!w)break;a.preventDefault(),a.shiftKey?(it(f),await Qe(),t(17,v=[f+1,q])):ge(N,[f,q])?(t(23,N=!1),await Qe(),t(17,v=[f,q])):t(23,N=[f,q]);break;case"Backspace":if(!w)break;N||(a.preventDefault(),t(18,h[f][q].value="",h));break;case"Delete":if(!w)break;N||(a.preventDefault(),t(18,h[f][q].value="",h));break;case"Tab":let D=a.shiftKey?-1:1,P=h[f][q+D],se=h?.[f+D]?.[D>0?0:b.length-1];(P||se)&&(a.preventDefault(),t(17,v=P?[f,q+D]:[f+D,D>0?0:b.length-1])),t(23,N=!1);break;default:if(!w)break;(!N||N&&ge(N,[f,q]))&&a.key.length===1&&(t(26,ne=!0),t(23,N=[f,q]))}}async function le(a,f){ge(N,[a,f])||(t(27,Z=!1),t(29,X=!1),t(23,N=!1),t(17,v=[a,f]),await Qe(),oe.focus())}let fe,De;function Ie(a){typeof De!="number"||De!==a?(t(19,fe="asc"),t(20,De=a)):fe==="asc"?t(19,fe="des"):fe==="des"&&t(19,fe="asc")}let Z,vt=!1,X=!1;async function st(a,f=!1){!w||u[1]!=="dynamic"||Z===a||(t(17,v=!1),t(29,X=a),t(27,Z=a),t(28,vt=f))}function al(a){if(w)switch(a.key){case"Escape":case"Enter":case"Tab":a.preventDefault(),t(17,v=!1),t(29,X=Z),t(27,Z=!1),oe.focus();break}}async function it(a){if(oe.focus(),d[1]==="dynamic"){if(h.length===0){t(50,c=[Array(r.length).fill("")]);return}h.splice(a?a+1:h.length,0,Array(h[0].length).fill(0).map((f,q)=>{const D=Fe();return t(24,F[D]={cell:null,input:null},F),{id:D,value:""}})),t(18,h),t(50,c),t(52,I),t(17,v=[a?a+1:h.length-1,0])}}async function yt(){if(oe.focus(),u[1]==="dynamic"){for(let a=0;a<h.length;a++){const f=Fe();t(24,F[f]={cell:null,input:null},F),h[a].push({id:f,value:""})}r.push(`Header ${r.length+1}`),t(18,h),t(50,c),t(52,I),t(49,r),await Qe(),requestAnimationFrame(()=>{st(r.length-1,!0);const a=oe.querySelectorAll("tbody")[1].offsetWidth;oe.querySelectorAll("table")[1].scrollTo({left:a})})}}function ol(a){a.stopImmediatePropagation();const[f]=a.composedPath();oe.contains(f)||(t(23,N=!1),t(27,Z=!1),t(29,X=!1),t(17,v=!1))}function At(a){const f=new FileReader;function q(D){if(!D?.target?.result||typeof D.target.result!="string")return;const[P]=In(D.target.result,[",","	"]),[se,...he]=jl(P).parseRows(D.target.result);t(25,b=ue(u[1]==="fixed"?se.slice(0,u[0]):se)),t(50,c=he),f.removeEventListener("loadend",q)}f.addEventListener("loadend",q),f.readAsText(a)}let rt=!1;function ul(a){let f=a[0].slice();for(let q=0;q<a.length;q++)for(let D=0;D<a[q].length;D++)`${f[D].value}`.length<`${a[q][D].value}`.length&&(f[D]=a[q][D]);return f}let Te=[],oe,at;function Ve(){const a=Te.map((f,q)=>f?.clientWidth||0);if(a.length!==0)for(let f=0;f<a.length;f++)oe.style.setProperty(`--cell-width-${f}`,`${a[f]-Ke/a.length}px`)}let ot=H,Ke=0;function _l(a,f,q,D,P){let se=null;if(v&&v[0]in h&&v[1]in h[v[0]]&&(se=h[v[0]][v[1]].id),typeof D!="number"||!P)return;const he=[...Array(a.length).keys()];if(P==="asc")he.sort((me,we)=>a[me][D].value<a[we][D].value?-1:1);else if(P==="des")he.sort((me,we)=>a[me][D].value>a[we][D].value?-1:1);else return;const Ye=[...a],zt=f?[...f]:null,Mt=q?[...q]:null;if(he.forEach((me,we)=>{a[we]=Ye[me],f&&zt&&(f[we]=zt[me]),q&&Mt&&(q[we]=Mt[me])}),t(18,h),t(50,c),t(52,I),se){const[me,we]=te(se);t(17,v=[me,we])}}let St=!1;Tn(()=>{const a=new IntersectionObserver((f,q)=>{f.forEach(D=>{D.isIntersecting&&!St&&(Ve(),t(18,h),t(50,c),t(52,I)),St=D.isIntersecting})});return a.observe(oe),()=>{a.disconnect()}});const fl=()=>Ve();function hl(a,f){de[a?"unshift":"push"](()=>{Te[f]=a,t(21,Te)})}function cl(){$=Cn.entries.get(this)?.contentRect,t(22,$)}function dl(a){de[a?"unshift":"push"](()=>{at=a,t(32,at)})}function gl(a,f,q){n.$$.not_equal(h[f][q].value,a)&&(h[f][q].value=a,t(18,h),t(50,c),t(52,I))}function bl(a,f){n.$$.not_equal(F[f].input,a)&&(F[f].input=a,t(24,F))}const ml=()=>(t(26,ne=!1),oe.focus()),wl=(a,f)=>re(a,f),kl=(a,f)=>le(a,f),pl=(a,f)=>re(a,f);function vl(a,f){n.$$.not_equal(b[f].value,a)&&(b[f].value=a,t(25,b))}function yl(a,f){n.$$.not_equal(F[f].input,a)&&(F[f].input=a,t(24,F))}const Al=a=>st(a),Sl=a=>Ie(a);function zl(a){h=a,t(18,h),t(50,c),t(52,I)}function Ml(a){ot=a,t(33,ot)}function ql(a){Ke=a,t(34,Ke)}function Cl(a){rt=a,t(30,rt)}const Dl=a=>At(Vn(a.detail.data));function Ll(a){de[a?"unshift":"push"](()=>{oe=a,t(31,oe)})}const Rl=a=>j(a),Bl=a=>(a.stopPropagation(),it()),El=a=>(a.stopPropagation(),yt());return n.$$set=a=>{"datatype"in a&&t(0,o=a.datatype),"label"in a&&t(1,_=a.label),"show_label"in a&&t(2,i=a.show_label),"headers"in a&&t(49,r=a.headers),"values"in a&&t(50,c=a.values),"col_count"in a&&t(3,u=a.col_count),"row_count"in a&&t(4,d=a.row_count),"latex_delimiters"in a&&t(5,g=a.latex_delimiters),"editable"in a&&t(6,w=a.editable),"wrap"in a&&t(7,S=a.wrap),"root"in a&&t(8,L=a.root),"i18n"in a&&t(9,z=a.i18n),"height"in a&&t(10,H=a.height),"line_breaks"in a&&t(11,R=a.line_breaks),"column_widths"in a&&t(12,A=a.column_widths),"upload"in a&&t(13,p=a.upload),"stream_handler"in a&&t(14,E=a.stream_handler),"display_value"in a&&t(15,M=a.display_value),"styling"in a&&t(16,k=a.styling)},n.$$.update=()=>{if(n.$$.dirty[0]&131072&&v!==!1){const[a,f]=v;!isNaN(a)&&!isNaN(f)&&ee("select",{index:[a,f],value:be(a,f)})}n.$$.dirty[1]&1310720&&(ge(r,y)||O()),n.$$.dirty[1]&2621440&&(ge(c,I)||(t(18,h=T(c)),t(52,I=c))),n.$$.dirty[0]&262144&&h&&C(),n.$$.dirty[0]&262144&&t(36,l=ul(h)),n.$$.dirty[0]&2097152&&Te[0]&&Ve(),n.$$.dirty[0]&1933312&&_l(h,M,k,De,fe),n.$$.dirty[0]&131072&&t(35,s=!!v&&v[0])},[o,_,i,u,d,g,w,S,L,z,H,R,A,p,E,M,k,v,h,fe,De,Te,$,N,F,b,ne,Z,vt,X,rt,oe,at,ot,Ke,s,l,Q,re,j,le,Ie,st,al,it,yt,ol,At,Ve,r,c,y,I,fl,hl,cl,dl,gl,bl,ml,wl,kl,pl,vl,yl,Al,Sl,zl,Ml,ql,Cl,Dl,Ll,Rl,Bl,El]}class Yn extends Dn{constructor(e){super(),Bn(this,e,Kn,Wn,Hn,{datatype:0,label:1,show_label:2,headers:49,values:50,col_count:3,row_count:4,latex_delimiters:5,editable:6,wrap:7,root:8,i18n:9,height:10,line_breaks:11,column_widths:12,upload:13,stream_handler:14,display_value:15,styling:16},null,[-1,-1,-1,-1])}get datatype(){return this.$$.ctx[0]}set datatype(e){this.$$set({datatype:e}),G()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),G()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),G()}get headers(){return this.$$.ctx[49]}set headers(e){this.$$set({headers:e}),G()}get values(){return this.$$.ctx[50]}set values(e){this.$$set({values:e}),G()}get col_count(){return this.$$.ctx[3]}set col_count(e){this.$$set({col_count:e}),G()}get row_count(){return this.$$.ctx[4]}set row_count(e){this.$$set({row_count:e}),G()}get latex_delimiters(){return this.$$.ctx[5]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),G()}get editable(){return this.$$.ctx[6]}set editable(e){this.$$set({editable:e}),G()}get wrap(){return this.$$.ctx[7]}set wrap(e){this.$$set({wrap:e}),G()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),G()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),G()}get height(){return this.$$.ctx[10]}set height(e){this.$$set({height:e}),G()}get line_breaks(){return this.$$.ctx[11]}set line_breaks(e){this.$$set({line_breaks:e}),G()}get column_widths(){return this.$$.ctx[12]}set column_widths(e){this.$$set({column_widths:e}),G()}get upload(){return this.$$.ctx[13]}set upload(e){this.$$set({upload:e}),G()}get stream_handler(){return this.$$.ctx[14]}set stream_handler(e){this.$$set({stream_handler:e}),G()}get display_value(){return this.$$.ctx[15]}set display_value(e){this.$$set({display_value:e}),G()}get styling(){return this.$$.ctx[16]}set styling(e){this.$$set({styling:e}),G()}}const Zn=Yn,{SvelteComponent:Gn,assign:Qn,create_component:bt,destroy_component:mt,detach:Xn,flush:W,get_spread_object:xn,get_spread_update:$n,init:es,insert:ts,mount_component:wt,safe_not_equal:ls,space:ns,transition_in:kt,transition_out:pt}=window.__gradio__svelte__internal,{afterUpdate:ss,tick:is}=window.__gradio__svelte__internal;function rs(n){let e,t,l,s;const o=[{autoscroll:n[14].autoscroll},{i18n:n[14].i18n},n[17]];let _={};for(let i=0;i<o.length;i+=1)_=Qn(_,o[i]);return e=new Nl({props:_}),e.$on("clear_status",n[28]),l=new Zn({props:{root:n[11],label:n[5],show_label:n[6],row_count:n[4],col_count:n[3],values:n[22],display_value:n[20],styling:n[21],headers:n[19],wrap:n[7],datatype:n[8],latex_delimiters:n[15],editable:n[18],height:n[16],i18n:n[14].i18n,line_breaks:n[12],column_widths:n[13],upload:n[14].client.upload,stream_handler:n[14].client.stream}}),l.$on("change",n[29]),l.$on("select",n[30]),{c(){bt(e.$$.fragment),t=ns(),bt(l.$$.fragment)},m(i,r){wt(e,i,r),ts(i,t,r),wt(l,i,r),s=!0},p(i,r){const c=r[0]&147456?$n(o,[r[0]&16384&&{autoscroll:i[14].autoscroll},r[0]&16384&&{i18n:i[14].i18n},r[0]&131072&&xn(i[17])]):{};e.$set(c);const u={};r[0]&2048&&(u.root=i[11]),r[0]&32&&(u.label=i[5]),r[0]&64&&(u.show_label=i[6]),r[0]&16&&(u.row_count=i[4]),r[0]&8&&(u.col_count=i[3]),r[0]&4194304&&(u.values=i[22]),r[0]&1048576&&(u.display_value=i[20]),r[0]&2097152&&(u.styling=i[21]),r[0]&524288&&(u.headers=i[19]),r[0]&128&&(u.wrap=i[7]),r[0]&256&&(u.datatype=i[8]),r[0]&32768&&(u.latex_delimiters=i[15]),r[0]&262144&&(u.editable=i[18]),r[0]&65536&&(u.height=i[16]),r[0]&16384&&(u.i18n=i[14].i18n),r[0]&4096&&(u.line_breaks=i[12]),r[0]&8192&&(u.column_widths=i[13]),r[0]&16384&&(u.upload=i[14].client.upload),r[0]&16384&&(u.stream_handler=i[14].client.stream),l.$set(u)},i(i){s||(kt(e.$$.fragment,i),kt(l.$$.fragment,i),s=!0)},o(i){pt(e.$$.fragment,i),pt(l.$$.fragment,i),s=!1},d(i){i&&Xn(t),mt(e,i),mt(l,i)}}}function as(n){let e,t;return e=new Hl({props:{visible:n[2],padding:!1,elem_id:n[0],elem_classes:n[1],container:!1,scale:n[9],min_width:n[10],allow_overflow:!1,$$slots:{default:[rs]},$$scope:{ctx:n}}}),{c(){bt(e.$$.fragment)},m(l,s){wt(e,l,s),t=!0},p(l,s){const o={};s[0]&4&&(o.visible=l[2]),s[0]&1&&(o.elem_id=l[0]),s[0]&2&&(o.elem_classes=l[1]),s[0]&512&&(o.scale=l[9]),s[0]&1024&&(o.min_width=l[10]),s[0]&8387064|s[1]&2&&(o.$$scope={dirty:s,ctx:l}),e.$set(o)},i(l){t||(kt(e.$$.fragment,l),t=!0)},o(l){pt(e.$$.fragment,l),t=!1},d(l){mt(e,l)}}}function os(n,e,t){let{headers:l=[]}=e,{elem_id:s=""}=e,{elem_classes:o=[]}=e,{visible:_=!0}=e,{value:i={data:[["","",""]],headers:["1","2","3"],metadata:null}}=e,r="",{value_is_output:c=!1}=e,{col_count:u}=e,{row_count:d}=e,{label:g=null}=e,{show_label:w=!0}=e,{wrap:S}=e,{datatype:L}=e,{scale:z=null}=e,{min_width:H=void 0}=e,{root:R}=e,{line_breaks:A=!0}=e,{column_widths:p=[]}=e,{gradio:E}=e,{latex_delimiters:v}=e,{height:M=void 0}=e,{loading_status:k}=e,{interactive:$}=e,ee,N,be,F;async function ue(h){let I=h||i;t(19,ee=[...I.headers||l]),t(22,F=I.data?[...I.data]:[]),t(20,N=I?.metadata?.display_value?[...I?.metadata?.display_value]:null),t(21,be=I?.metadata?.styling?[...I?.metadata?.styling]:null),await is(),E.dispatch("change"),c||E.dispatch("input")}ue(),ss(()=>{t(25,c=!1)}),(Array.isArray(i)&&i?.[0]?.length===0||i.data?.[0]?.length===0)&&(i={data:[Array(u?.[0]||3).fill("")],headers:Array(u?.[0]||3).fill("").map((h,I)=>`${I+1}`),metadata:null});async function T(h){JSON.stringify(h)!==r&&(t(24,i={...h}),t(27,r=JSON.stringify(i)),ue(h))}const b=()=>E.dispatch("clear_status",k),y=h=>T(h.detail),O=h=>E.dispatch("select",h.detail);return n.$$set=h=>{"headers"in h&&t(26,l=h.headers),"elem_id"in h&&t(0,s=h.elem_id),"elem_classes"in h&&t(1,o=h.elem_classes),"visible"in h&&t(2,_=h.visible),"value"in h&&t(24,i=h.value),"value_is_output"in h&&t(25,c=h.value_is_output),"col_count"in h&&t(3,u=h.col_count),"row_count"in h&&t(4,d=h.row_count),"label"in h&&t(5,g=h.label),"show_label"in h&&t(6,w=h.show_label),"wrap"in h&&t(7,S=h.wrap),"datatype"in h&&t(8,L=h.datatype),"scale"in h&&t(9,z=h.scale),"min_width"in h&&t(10,H=h.min_width),"root"in h&&t(11,R=h.root),"line_breaks"in h&&t(12,A=h.line_breaks),"column_widths"in h&&t(13,p=h.column_widths),"gradio"in h&&t(14,E=h.gradio),"latex_delimiters"in h&&t(15,v=h.latex_delimiters),"height"in h&&t(16,M=h.height),"loading_status"in h&&t(17,k=h.loading_status),"interactive"in h&&t(18,$=h.interactive)},n.$$.update=()=>{n.$$.dirty[0]&150994944&&r&&JSON.stringify(i)!==r&&(t(27,r=JSON.stringify(i)),ue())},[s,o,_,u,d,g,w,S,L,z,H,R,A,p,E,v,M,k,$,ee,N,be,F,T,i,c,l,r,b,y,O]}class ps extends Gn{constructor(e){super(),es(this,e,os,as,ls,{headers:26,elem_id:0,elem_classes:1,visible:2,value:24,value_is_output:25,col_count:3,row_count:4,label:5,show_label:6,wrap:7,datatype:8,scale:9,min_width:10,root:11,line_breaks:12,column_widths:13,gradio:14,latex_delimiters:15,height:16,loading_status:17,interactive:18},null,[-1,-1])}get headers(){return this.$$.ctx[26]}set headers(e){this.$$set({headers:e}),W()}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),W()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),W()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),W()}get value(){return this.$$.ctx[24]}set value(e){this.$$set({value:e}),W()}get value_is_output(){return this.$$.ctx[25]}set value_is_output(e){this.$$set({value_is_output:e}),W()}get col_count(){return this.$$.ctx[3]}set col_count(e){this.$$set({col_count:e}),W()}get row_count(){return this.$$.ctx[4]}set row_count(e){this.$$set({row_count:e}),W()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),W()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),W()}get wrap(){return this.$$.ctx[7]}set wrap(e){this.$$set({wrap:e}),W()}get datatype(){return this.$$.ctx[8]}set datatype(e){this.$$set({datatype:e}),W()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),W()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),W()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),W()}get line_breaks(){return this.$$.ctx[12]}set line_breaks(e){this.$$set({line_breaks:e}),W()}get column_widths(){return this.$$.ctx[13]}set column_widths(e){this.$$set({column_widths:e}),W()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),W()}get latex_delimiters(){return this.$$.ctx[15]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),W()}get height(){return this.$$.ctx[16]}set height(e){this.$$set({height:e}),W()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),W()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),W()}}export{Zn as BaseDataFrame,As as BaseExample,ps as default};
//# sourceMappingURL=Index-DqFs2tUO.js.map
