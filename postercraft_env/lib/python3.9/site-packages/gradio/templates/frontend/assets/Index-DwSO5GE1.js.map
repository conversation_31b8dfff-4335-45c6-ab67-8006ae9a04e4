{"version": 3, "file": "Index-DwSO5GE1.js", "sources": ["../../../../js/button/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { default as BaseButton } from \"./shared/Button.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { type FileData } from \"@gradio/client\";\n\n\timport Button from \"./shared/Button.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string | null;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let interactive: boolean;\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let scale: number | null = null;\n\texport let icon: FileData | null = null;\n\texport let link: string | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tclick: never;\n\t}>;\n</script>\n\n<Button\n\t{value}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{size}\n\t{scale}\n\t{link}\n\t{icon}\n\t{min_width}\n\t{visible}\n\tdisabled={!interactive}\n\ton:click={() => gradio.dispatch(\"click\")}\n>\n\t{value ? gradio.i18n(value) : \"\"}\n</Button>\n"], "names": ["ctx", "set_data", "t", "t_value", "elem_id", "$$props", "elem_classes", "visible", "value", "variant", "interactive", "size", "scale", "icon", "link", "min_width", "gradio"], "mappings": "0WAwCEA,EAAK,CAAA,EAAGA,EAAM,EAAA,EAAC,KAAKA,EAAK,CAAA,CAAA,EAAI,IAAE,gEAA/BA,EAAK,CAAA,EAAGA,EAAM,EAAA,EAAC,KAAKA,EAAK,CAAA,CAAA,EAAI,IAAE,KAAAC,EAAAC,EAAAC,CAAA,sMAHrBH,EAAW,CAAA,sXAAXA,EAAW,CAAA,oJA3BX,GAAA,CAAA,QAAAI,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAoB,EAAAH,EACpB,CAAA,QAAAI,EAA4C,WAAW,EAAAJ,GACvD,YAAAK,CAAoB,EAAAL,EACpB,CAAA,KAAAM,EAAoB,IAAI,EAAAN,EACxB,CAAA,MAAAO,EAAuB,IAAI,EAAAP,EAC3B,CAAA,KAAAQ,EAAwB,IAAI,EAAAR,EAC5B,CAAA,KAAAS,EAAsB,IAAI,EAAAT,EAC1B,CAAA,UAAAU,EAAgC,MAAS,EAAAV,GACzC,OAAAW,CAET,EAAAX,cAecW,EAAO,SAAS,OAAO"}