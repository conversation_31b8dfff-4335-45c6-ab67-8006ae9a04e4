import{S as H}from"./Index-WGC0_FkS.js";import{B as L}from"./Button-8nmImwVJ.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";const{SvelteComponent:M,attr:k,detach:T,element:B,flush:h,init:z,insert:D,noop:S,safe_not_equal:E,toggle_class:g}=window.__gradio__svelte__internal,{createEventDispatcher:I}=window.__gradio__svelte__internal;function A(i){let e,n;return{c(){e=B("div"),k(e,"class",n="prose "+i[0].join(" ")+" svelte-1ybaih5"),g(e,"min",i[3]),g(e,"hide",!i[2])},m(s,l){D(s,e,l),e.innerHTML=i[1]},p(s,[l]){l&2&&(e.innerHTML=s[1]),l&1&&n!==(n="prose "+s[0].join(" ")+" svelte-1ybaih5")&&k(e,"class",n),l&9&&g(e,"min",s[3]),l&5&&g(e,"hide",!s[2])},i:S,o:S,d(s){s&&T(e)}}}function F(i,e,n){let{elem_classes:s=[]}=e,{value:l}=e,{visible:_=!0}=e,{min_height:r=!1}=e;const c=I();return i.$$set=t=>{"elem_classes"in t&&n(0,s=t.elem_classes),"value"in t&&n(1,l=t.value),"visible"in t&&n(2,_=t.visible),"min_height"in t&&n(3,r=t.min_height)},i.$$.update=()=>{i.$$.dirty&2&&c("change")},[s,l,_,r]}class G extends M{constructor(e){super(),z(this,e,F,A,E,{elem_classes:0,value:1,visible:2,min_height:3})}get elem_classes(){return this.$$.ctx[0]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),h()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),h()}get min_height(){return this.$$.ctx[3]}set min_height(e){this.$$set({min_height:e}),h()}}const{SvelteComponent:J,assign:K,attr:N,create_component:d,destroy_component:v,detach:j,element:O,flush:m,get_spread_object:P,get_spread_update:Q,init:R,insert:q,mount_component:b,safe_not_equal:U,space:V,toggle_class:C,transition_in:$,transition_out:w}=window.__gradio__svelte__internal;function W(i){let e,n,s,l,_;const r=[{autoscroll:i[5].autoscroll},{i18n:i[5].i18n},i[4],{variant:"center"}];let c={};for(let t=0;t<r.length;t+=1)c=K(c,r[t]);return e=new H({props:c}),e.$on("clear_status",i[7]),l=new G({props:{min_height:i[4]&&i[4]?.status!=="complete",value:i[3],elem_classes:i[1],visible:i[2]}}),l.$on("change",i[8]),{c(){d(e.$$.fragment),n=V(),s=O("div"),d(l.$$.fragment),N(s,"class","svelte-1ed2p3z"),C(s,"pending",i[4]?.status==="pending")},m(t,a){b(e,t,a),q(t,n,a),q(t,s,a),b(l,s,null),_=!0},p(t,a){const f=a&48?Q(r,[a&32&&{autoscroll:t[5].autoscroll},a&32&&{i18n:t[5].i18n},a&16&&P(t[4]),r[3]]):{};e.$set(f);const o={};a&16&&(o.min_height=t[4]&&t[4]?.status!=="complete"),a&8&&(o.value=t[3]),a&2&&(o.elem_classes=t[1]),a&4&&(o.visible=t[2]),l.$set(o),(!_||a&16)&&C(s,"pending",t[4]?.status==="pending")},i(t){_||($(e.$$.fragment,t),$(l.$$.fragment,t),_=!0)},o(t){w(e.$$.fragment,t),w(l.$$.fragment,t),_=!1},d(t){t&&(j(n),j(s)),v(e,t),v(l)}}}function X(i){let e,n;return e=new L({props:{visible:i[2],elem_id:i[0],elem_classes:i[1],container:!1,$$slots:{default:[W]},$$scope:{ctx:i}}}),{c(){d(e.$$.fragment)},m(s,l){b(e,s,l),n=!0},p(s,[l]){const _={};l&4&&(_.visible=s[2]),l&1&&(_.elem_id=s[0]),l&2&&(_.elem_classes=s[1]),l&574&&(_.$$scope={dirty:l,ctx:s}),e.$set(_)},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){w(e.$$.fragment,s),n=!1},d(s){v(e,s)}}}function Y(i,e,n){let{label:s}=e,{elem_id:l=""}=e,{elem_classes:_=[]}=e,{visible:r=!0}=e,{value:c=""}=e,{loading_status:t}=e,{gradio:a}=e;const f=()=>a.dispatch("clear_status",t),o=()=>a.dispatch("change");return i.$$set=u=>{"label"in u&&n(6,s=u.label),"elem_id"in u&&n(0,l=u.elem_id),"elem_classes"in u&&n(1,_=u.elem_classes),"visible"in u&&n(2,r=u.visible),"value"in u&&n(3,c=u.value),"loading_status"in u&&n(4,t=u.loading_status),"gradio"in u&&n(5,a=u.gradio)},i.$$.update=()=>{i.$$.dirty&96&&a.dispatch("change")},[l,_,r,c,t,a,s,f,o]}class ee extends J{constructor(e){super(),R(this,e,Y,X,U,{label:6,elem_id:0,elem_classes:1,visible:2,value:3,loading_status:4,gradio:5})}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),m()}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),m()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),m()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),m()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),m()}get loading_status(){return this.$$.ctx[4]}set loading_status(e){this.$$set({loading_status:e}),m()}get gradio(){return this.$$.ctx[5]}set gradio(e){this.$$set({gradio:e}),m()}}export{ee as default};
//# sourceMappingURL=Index-HrY4d4EM.js.map
