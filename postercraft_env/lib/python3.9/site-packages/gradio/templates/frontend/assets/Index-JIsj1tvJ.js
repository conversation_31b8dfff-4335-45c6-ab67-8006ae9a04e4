import{P as G,a as H}from"./Plot-BaG7oz5l.js";import{B as J}from"./Button-uOcat6Z0.js";import{S as K}from"./Index-D21IHG0c.js";import{B as M}from"./BlockLabel-BXXlQleC.js";import"./index-D5ROCp7B.js";import"./svelte/svelte.js";import"./Empty-CLiqUlWX.js";const{SvelteComponent:N,assign:O,create_component:w,destroy_component:d,detach:L,flush:o,get_spread_object:Q,get_spread_update:R,init:T,insert:z,mount_component:k,safe_not_equal:U,space:A,transition_in:v,transition_out:B}=window.__gradio__svelte__internal;function V(l){let e,i,n,h,_,m;e=new M({props:{show_label:l[6],label:l[5]||l[14].i18n("plot.plot"),Icon:G}});const c=[{autoscroll:l[14].autoscroll},{i18n:l[14].i18n},l[4]];let f={};for(let t=0;t<c.length;t+=1)f=O(f,c[t]);return n=new K({props:f}),n.$on("clear_status",l[18]),_=new H({props:{value:l[0],target:l[7],theme_mode:l[11],caption:l[12],bokeh_version:l[13],show_actions_button:l[15],gradio:l[14],_selectable:l[16],x_lim:l[17]}}),_.$on("change",l[19]),_.$on("select",l[20]),{c(){w(e.$$.fragment),i=A(),w(n.$$.fragment),h=A(),w(_.$$.fragment)},m(t,a){k(e,t,a),z(t,i,a),k(n,t,a),z(t,h,a),k(_,t,a),m=!0},p(t,a){const r={};a&64&&(r.show_label=t[6]),a&16416&&(r.label=t[5]||t[14].i18n("plot.plot")),e.$set(r);const b=a&16400?R(c,[a&16384&&{autoscroll:t[14].autoscroll},a&16384&&{i18n:t[14].i18n},a&16&&Q(t[4])]):{};n.$set(b);const u={};a&1&&(u.value=t[0]),a&128&&(u.target=t[7]),a&2048&&(u.theme_mode=t[11]),a&4096&&(u.caption=t[12]),a&8192&&(u.bokeh_version=t[13]),a&32768&&(u.show_actions_button=t[15]),a&16384&&(u.gradio=t[14]),a&65536&&(u._selectable=t[16]),a&131072&&(u.x_lim=t[17]),_.$set(u)},i(t){m||(v(e.$$.fragment,t),v(n.$$.fragment,t),v(_.$$.fragment,t),m=!0)},o(t){B(e.$$.fragment,t),B(n.$$.fragment,t),B(_.$$.fragment,t),m=!1},d(t){t&&(L(i),L(h)),d(e,t),d(n,t),d(_,t)}}}function W(l){let e,i;return e=new J({props:{padding:!1,elem_id:l[1],elem_classes:l[2],visible:l[3],container:l[8],scale:l[9],min_width:l[10],allow_overflow:!1,$$slots:{default:[V]},$$scope:{ctx:l}}}),{c(){w(e.$$.fragment)},m(n,h){k(e,n,h),i=!0},p(n,[h]){const _={};h&2&&(_.elem_id=n[1]),h&4&&(_.elem_classes=n[2]),h&8&&(_.visible=n[3]),h&256&&(_.container=n[8]),h&512&&(_.scale=n[9]),h&1024&&(_.min_width=n[10]),h&2357489&&(_.$$scope={dirty:h,ctx:n}),e.$set(_)},i(n){i||(v(e.$$.fragment,n),i=!0)},o(n){B(e.$$.fragment,n),i=!1},d(n){d(e,n)}}}function X(l,e,i){let{value:n=null}=e,{elem_id:h=""}=e,{elem_classes:_=[]}=e,{visible:m=!0}=e,{loading_status:c}=e,{label:f}=e,{show_label:t}=e,{target:a}=e,{container:r=!0}=e,{scale:b=null}=e,{min_width:u=void 0}=e,{theme_mode:P}=e,{caption:S}=e,{bokeh_version:I}=e,{gradio:g}=e,{show_actions_button:j=!1}=e,{_selectable:q=!1}=e,{x_lim:C=null}=e;const D=()=>g.dispatch("clear_status",c),E=()=>g.dispatch("change"),F=s=>g.dispatch("select",s.detail);return l.$$set=s=>{"value"in s&&i(0,n=s.value),"elem_id"in s&&i(1,h=s.elem_id),"elem_classes"in s&&i(2,_=s.elem_classes),"visible"in s&&i(3,m=s.visible),"loading_status"in s&&i(4,c=s.loading_status),"label"in s&&i(5,f=s.label),"show_label"in s&&i(6,t=s.show_label),"target"in s&&i(7,a=s.target),"container"in s&&i(8,r=s.container),"scale"in s&&i(9,b=s.scale),"min_width"in s&&i(10,u=s.min_width),"theme_mode"in s&&i(11,P=s.theme_mode),"caption"in s&&i(12,S=s.caption),"bokeh_version"in s&&i(13,I=s.bokeh_version),"gradio"in s&&i(14,g=s.gradio),"show_actions_button"in s&&i(15,j=s.show_actions_button),"_selectable"in s&&i(16,q=s._selectable),"x_lim"in s&&i(17,C=s.x_lim)},[n,h,_,m,c,f,t,a,r,b,u,P,S,I,g,j,q,C,D,E,F]}class te extends N{constructor(e){super(),T(this,e,X,W,U,{value:0,elem_id:1,elem_classes:2,visible:3,loading_status:4,label:5,show_label:6,target:7,container:8,scale:9,min_width:10,theme_mode:11,caption:12,bokeh_version:13,gradio:14,show_actions_button:15,_selectable:16,x_lim:17})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),o()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),o()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),o()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),o()}get loading_status(){return this.$$.ctx[4]}set loading_status(e){this.$$set({loading_status:e}),o()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),o()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),o()}get target(){return this.$$.ctx[7]}set target(e){this.$$set({target:e}),o()}get container(){return this.$$.ctx[8]}set container(e){this.$$set({container:e}),o()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),o()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),o()}get theme_mode(){return this.$$.ctx[11]}set theme_mode(e){this.$$set({theme_mode:e}),o()}get caption(){return this.$$.ctx[12]}set caption(e){this.$$set({caption:e}),o()}get bokeh_version(){return this.$$.ctx[13]}set bokeh_version(e){this.$$set({bokeh_version:e}),o()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),o()}get show_actions_button(){return this.$$.ctx[15]}set show_actions_button(e){this.$$set({show_actions_button:e}),o()}get _selectable(){return this.$$.ctx[16]}set _selectable(e){this.$$set({_selectable:e}),o()}get x_lim(){return this.$$.ctx[17]}set x_lim(e){this.$$set({x_lim:e}),o()}}export{H as BasePlot,te as default};
//# sourceMappingURL=Index-JIsj1tvJ.js.map
