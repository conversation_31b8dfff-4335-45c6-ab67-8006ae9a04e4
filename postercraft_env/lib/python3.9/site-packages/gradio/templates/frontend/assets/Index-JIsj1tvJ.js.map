{"version": 3, "file": "Index-JIsj1tvJ.js", "sources": ["../../../../js/plot/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { default as BasePlot } from \"./shared/Plot.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport Plot from \"./shared/Plot.svelte\";\n\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport { Plot as PlotIcon } from \"@gradio/icons\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\ttype ThemeMode = \"system\" | \"light\" | \"dark\";\n\n\texport let value: null | string = null;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let loading_status: LoadingStatus;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let target: HTMLElement;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let theme_mode: ThemeMode;\n\texport let caption: string;\n\texport let bokeh_version: string | null;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear_status: LoadingStatus;\n\t\tselect: SelectData;\n\t}>;\n\texport let show_actions_button = false;\n\texport let _selectable = false;\n\texport let x_lim: [number, number] | null = null;\n</script>\n\n<Block\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<BlockLabel\n\t\t{show_label}\n\t\tlabel={label || gradio.i18n(\"plot.plot\")}\n\t\tIcon={PlotIcon}\n\t/>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<Plot\n\t\t{value}\n\t\t{target}\n\t\t{theme_mode}\n\t\t{caption}\n\t\t{bokeh_version}\n\t\t{show_actions_button}\n\t\t{gradio}\n\t\t{_selectable}\n\t\t{x_lim}\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t/>\n</Block>\n"], "names": ["ctx", "PlotIcon", "dirty", "blocklabel_changes", "value", "$$props", "elem_id", "elem_classes", "visible", "loading_status", "label", "show_label", "target", "container", "scale", "min_width", "theme_mode", "caption", "bokeh_version", "gradio", "show_actions_button", "_selectable", "x_lim", "clear_status_handler", "e"], "mappings": "6jBAoDS,MAAAA,EAAS,CAAA,GAAAA,EAAO,EAAA,EAAA,KAAK,WAAW,OACjCC,eAGM,WAAAD,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,meANXE,EAAA,QAAAC,EAAA,MAAAH,EAAS,CAAA,GAAAA,EAAO,EAAA,EAAA,KAAK,WAAW,4CAI3B,WAAAA,MAAO,YACbE,EAAA,OAAA,CAAA,KAAAF,MAAO,IAAI,UACbA,EAAc,CAAA,CAAA,ghBAjBV,wGAOO,mZAhCL,GAAA,CAAA,MAAAI,EAAuB,IAAI,EAAAC,EAC3B,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,GACd,eAAAI,CAA6B,EAAAJ,GAC7B,MAAAK,CAAa,EAAAL,GACb,WAAAM,CAAmB,EAAAN,GACnB,OAAAO,CAAmB,EAAAP,EACnB,CAAA,UAAAQ,EAAY,EAAI,EAAAR,EAChB,CAAA,MAAAS,EAAuB,IAAI,EAAAT,EAC3B,CAAA,UAAAU,EAAgC,MAAS,EAAAV,GACzC,WAAAW,CAAqB,EAAAX,GACrB,QAAAY,CAAe,EAAAZ,GACf,cAAAa,CAA4B,EAAAb,GAC5B,OAAAc,CAIT,EAAAd,EACS,CAAA,oBAAAe,EAAsB,EAAK,EAAAf,EAC3B,CAAA,YAAAgB,EAAc,EAAK,EAAAhB,EACnB,CAAA,MAAAiB,EAAiC,IAAI,EAAAjB,EAsBxB,MAAAkB,EAAA,IAAAJ,EAAO,SAAS,eAAgBV,CAAc,QAYpDU,EAAO,SAAS,QAAQ,IAC7BK,GAAML,EAAO,SAAS,SAAUK,EAAE,MAAM"}