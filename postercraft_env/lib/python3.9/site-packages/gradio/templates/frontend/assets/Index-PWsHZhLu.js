import{g as vl}from"./color-llR14loa.js";import{c as Be,S as wl}from"./Index-D21IHG0c.js";import{B as yl}from"./Button-uOcat6Z0.js";import{B as jl}from"./BlockLabel-BXXlQleC.js";import{E as Sl}from"./Empty-CLiqUlWX.js";import"./index-D5ROCp7B.js";import"./svelte/svelte.js";const{SvelteComponent:ut,append:Ie,attr:A,detach:dt,init:ht,insert:gt,noop:Te,safe_not_equal:mt,svg_element:Ne}=window.__gradio__svelte__internal;function bt(l){let e,t,n;return{c(){e=Ne("svg"),t=Ne("path"),n=Ne("path"),A(t,"fill","currentColor"),A(t,"d","M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z"),A(n,"fill","currentColor"),A(n,"d","M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z"),A(e,"xmlns","http://www.w3.org/2000/svg"),A(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),A(e,"aria-hidden","true"),A(e,"role","img"),A(e,"class","iconify iconify--carbon"),A(e,"width","100%"),A(e,"height","100%"),A(e,"preserveAspectRatio","xMidYMid meet"),A(e,"viewBox","0 0 32 32")},m(i,s){gt(i,e,s),Ie(e,t),Ie(e,n)},p:Te,i:Te,o:Te,d(i){i&&dt(e)}}}class Se extends ut{constructor(e){super(),ht(this,e,null,bt,mt,{})}}function qe(l,e,t){if(!t){var n=document.createElement("canvas");t=n.getContext("2d")}t.fillStyle=l,t.fillRect(0,0,1,1);const[i,s,o]=t.getImageData(0,0,1,1).data;return t.clearRect(0,0,1,1),`rgba(${i}, ${s}, ${o}, ${255/e})`}function $l(l,e,t,n){for(const i in l){const s=l[i].trim();s in Be?e[i]=Be[s]:e[i]={primary:t?qe(l[i],1,n):l[i],secondary:t?qe(l[i],.5,n):l[i]}}}function zl(l,e){let t=[],n=null,i=null;for(const s of l)i===s.class_or_confidence?n=n?n+s.token:s.token:(n!==null&&t.push({token:n,class_or_confidence:i}),n=s.token,i=s.class_or_confidence);return n!==null&&t.push({token:n,class_or_confidence:i}),t}const{SvelteComponent:kt,append:Q,attr:V,destroy_each:$e,detach:I,element:Z,empty:El,ensure_array_like:x,flush:fe,init:pt,insert:q,listen:_e,noop:Re,run_all:vt,safe_not_equal:wt,set_data:Oe,set_style:pe,space:re,text:ue,toggle_class:X}=window.__gradio__svelte__internal,{createEventDispatcher:yt}=window.__gradio__svelte__internal;function De(l,e,t){const n=l.slice();n[18]=e[t];const i=typeof n[18].class_or_confidence=="string"?parseInt(n[18].class_or_confidence):n[18].class_or_confidence;return n[27]=i,n}function Ae(l,e,t){const n=l.slice();return n[18]=e[t],n[20]=t,n}function Fe(l,e,t){const n=l.slice();return n[21]=e[t],n[23]=t,n}function Ze(l,e,t){const n=l.slice();return n[24]=e[t][0],n[25]=e[t][1],n[20]=t,n}function jt(l){let e,t,n=l[1]&&Ke(),i=x(l[0]),s=[];for(let o=0;o<i.length;o+=1)s[o]=Pe(De(l,i,o));return{c(){n&&n.c(),e=re(),t=Z("div");for(let o=0;o<s.length;o+=1)s[o].c();V(t,"class","textfield svelte-ju12zg"),V(t,"data-testid","highlighted-text:textfield")},m(o,a){n&&n.m(o,a),q(o,e,a),q(o,t,a);for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(t,null)},p(o,a){if(o[1]?n||(n=Ke(),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null),a&1){i=x(o[0]);let r;for(r=0;r<i.length;r+=1){const f=De(o,i,r);s[r]?s[r].p(f,a):(s[r]=Pe(f),s[r].c(),s[r].m(t,null))}for(;r<s.length;r+=1)s[r].d(1);s.length=i.length}},d(o){o&&(I(e),I(t)),n&&n.d(o),$e(s,o)}}}function St(l){let e,t,n=l[1]&&Ue(l),i=x(l[0]),s=[];for(let o=0;o<i.length;o+=1)s[o]=Xe(Ae(l,i,o));return{c(){n&&n.c(),e=re(),t=Z("div");for(let o=0;o<s.length;o+=1)s[o].c();V(t,"class","textfield svelte-ju12zg")},m(o,a){n&&n.m(o,a),q(o,e,a),q(o,t,a);for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(t,null)},p(o,a){if(o[1]?n?n.p(o,a):(n=Ue(o),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null),a&223){i=x(o[0]);let r;for(r=0;r<i.length;r+=1){const f=Ae(o,i,r);s[r]?s[r].p(f,a):(s[r]=Xe(f),s[r].c(),s[r].m(t,null))}for(;r<s.length;r+=1)s[r].d(1);s.length=i.length}},d(o){o&&(I(e),I(t)),n&&n.d(o),$e(s,o)}}}function Ke(l){let e;return{c(){e=Z("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",V(e,"class","color-legend svelte-ju12zg"),V(e,"data-testid","highlighted-text:color-legend")},m(t,n){q(t,e,n)},d(t){t&&I(e)}}}function Pe(l){let e,t,n=l[18].token+"",i,s,o;return{c(){e=Z("span"),t=Z("span"),i=ue(n),s=re(),V(t,"class","text svelte-ju12zg"),V(e,"class","textspan score-text svelte-ju12zg"),V(e,"style",o="background-color: rgba("+(l[27]&&l[27]<0?"128, 90, 213,"+-l[27]:"239, 68, 60,"+l[27])+")")},m(a,r){q(a,e,r),Q(e,t),Q(t,i),Q(e,s)},p(a,r){r&1&&n!==(n=a[18].token+"")&&Oe(i,n),r&1&&o!==(o="background-color: rgba("+(a[27]&&a[27]<0?"128, 90, 213,"+-a[27]:"239, 68, 60,"+a[27])+")")&&V(e,"style",o)},d(a){a&&I(e)}}}function Ue(l){let e,t=x(Object.entries(l[6])),n=[];for(let i=0;i<t.length;i+=1)n[i]=Ye(Ze(l,t,i));return{c(){e=Z("div");for(let i=0;i<n.length;i+=1)n[i].c();V(e,"class","category-legend svelte-ju12zg"),V(e,"data-testid","highlighted-text:category-legend")},m(i,s){q(i,e,s);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(e,null)},p(i,s){if(s&832){t=x(Object.entries(i[6]));let o;for(o=0;o<t.length;o+=1){const a=Ze(i,t,o);n[o]?n[o].p(a,s):(n[o]=Ye(a),n[o].c(),n[o].m(e,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(i){i&&I(e),$e(n,i)}}}function Ye(l){let e,t=l[24]+"",n,i,s,o;function a(){return l[11](l[24])}function r(){return l[12](l[24])}return{c(){e=Z("div"),n=ue(t),i=re(),V(e,"class","category-label svelte-ju12zg"),V(e,"style","background-color:"+l[25].secondary)},m(f,c){q(f,e,c),Q(e,n),Q(e,i),s||(o=[_e(e,"mouseover",a),_e(e,"focus",r),_e(e,"mouseout",l[13]),_e(e,"blur",l[14])],s=!0)},p(f,c){l=f},d(f){f&&I(e),s=!1,vt(o)}}}function Ge(l){let e,t,n=l[21]+"",i,s,o,a,r=!l[1]&&l[2]&&l[18].class_or_confidence!==null&&Je(l);function f(){return l[15](l[20],l[18])}return{c(){e=Z("span"),t=Z("span"),i=ue(n),s=re(),r&&r.c(),V(t,"class","text svelte-ju12zg"),X(t,"no-label",l[18].class_or_confidence===null||!l[6][l[18].class_or_confidence]),V(e,"class","textspan svelte-ju12zg"),X(e,"no-cat",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence),X(e,"hl",l[18].class_or_confidence!==null),X(e,"selectable",l[3]),pe(e,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].secondary)},m(c,g){q(c,e,g),Q(e,t),Q(t,i),Q(e,s),r&&r.m(e,null),o||(a=_e(e,"click",f),o=!0)},p(c,g){l=c,g&1&&n!==(n=l[21]+"")&&Oe(i,n),g&65&&X(t,"no-label",l[18].class_or_confidence===null||!l[6][l[18].class_or_confidence]),!l[1]&&l[2]&&l[18].class_or_confidence!==null?r?r.p(l,g):(r=Je(l),r.c(),r.m(e,null)):r&&(r.d(1),r=null),g&17&&X(e,"no-cat",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence),g&1&&X(e,"hl",l[18].class_or_confidence!==null),g&8&&X(e,"selectable",l[3]),g&17&&pe(e,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].secondary)},d(c){c&&I(e),r&&r.d(),o=!1,a()}}}function Je(l){let e,t,n=l[18].class_or_confidence+"",i;return{c(){e=ue(` 
								`),t=Z("span"),i=ue(n),V(t,"class","label svelte-ju12zg"),pe(t,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].primary)},m(s,o){q(s,e,o),q(s,t,o),Q(t,i)},p(s,o){o&1&&n!==(n=s[18].class_or_confidence+"")&&Oe(i,n),o&17&&pe(t,"background-color",s[18].class_or_confidence===null||s[4]&&s[4]!==s[18].class_or_confidence?"":s[6][s[18].class_or_confidence].primary)},d(s){s&&(I(e),I(t))}}}function Qe(l){let e;return{c(){e=Z("br")},m(t,n){q(t,e,n)},d(t){t&&I(e)}}}function We(l){let e=l[21].trim()!=="",t,n=l[23]<ve(l[18].token).length-1,i,s=e&&Ge(l),o=n&&Qe();return{c(){s&&s.c(),t=re(),o&&o.c(),i=El()},m(a,r){s&&s.m(a,r),q(a,t,r),o&&o.m(a,r),q(a,i,r)},p(a,r){r&1&&(e=a[21].trim()!==""),e?s?s.p(a,r):(s=Ge(a),s.c(),s.m(t.parentNode,t)):s&&(s.d(1),s=null),r&1&&(n=a[23]<ve(a[18].token).length-1),n?o||(o=Qe(),o.c(),o.m(i.parentNode,i)):o&&(o.d(1),o=null)},d(a){a&&(I(t),I(i)),s&&s.d(a),o&&o.d(a)}}}function Xe(l){let e,t=x(ve(l[18].token)),n=[];for(let i=0;i<t.length;i+=1)n[i]=We(Fe(l,t,i));return{c(){for(let i=0;i<n.length;i+=1)n[i].c();e=El()},m(i,s){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(i,s);q(i,e,s)},p(i,s){if(s&223){t=x(ve(i[18].token));let o;for(o=0;o<t.length;o+=1){const a=Fe(i,t,o);n[o]?n[o].p(a,s):(n[o]=We(a),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(i){i&&I(e),$e(n,i)}}}function $t(l){let e;function t(s,o){return s[5]==="categories"?St:jt}let n=t(l),i=n(l);return{c(){e=Z("div"),i.c(),V(e,"class","container svelte-ju12zg")},m(s,o){q(s,e,o),i.m(e,null)},p(s,[o]){n===(n=t(s))&&i?i.p(s,o):(i.d(1),i=n(s),i&&(i.c(),i.m(e,null)))},i:Re,o:Re,d(s){s&&I(e),i.d()}}}function ve(l){return l.split(`
`)}function zt(l,e,t){const n=typeof document<"u";let{value:i=[]}=e,{show_legend:s=!1}=e,{show_inline_category:o=!0}=e,{color_map:a={}}=e,{selectable:r=!1}=e,f,c={},g="";const k=yt();let m;function _(b){t(4,g=b)}function d(){t(4,g="")}const w=b=>_(b),S=b=>_(b),y=()=>d(),h=()=>d(),j=(b,z)=>{k("select",{index:b,value:[z.token,z.class_or_confidence]})};return l.$$set=b=>{"value"in b&&t(0,i=b.value),"show_legend"in b&&t(1,s=b.show_legend),"show_inline_category"in b&&t(2,o=b.show_inline_category),"color_map"in b&&t(10,a=b.color_map),"selectable"in b&&t(3,r=b.selectable)},l.$$.update=()=>{if(l.$$.dirty&1025){if(a||t(10,a={}),i.length>0){for(let b of i)if(b.class_or_confidence!==null)if(typeof b.class_or_confidence=="string"){if(t(5,m="categories"),!(b.class_or_confidence in a)){let z=vl(Object.keys(a).length);t(10,a[b.class_or_confidence]=z,a)}}else t(5,m="scores")}$l(a,c,n,f)}},[i,s,o,r,g,m,c,k,_,d,a,w,S,y,h,j]}class Et extends kt{constructor(e){super(),pt(this,e,zt,$t,wt,{value:0,show_legend:1,show_inline_category:2,color_map:10,selectable:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),fe()}get show_legend(){return this.$$.ctx[1]}set show_legend(e){this.$$set({show_legend:e}),fe()}get show_inline_category(){return this.$$.ctx[2]}set show_inline_category(e){this.$$set({show_inline_category:e}),fe()}get color_map(){return this.$$.ctx[10]}set color_map(e){this.$$set({color_map:e}),fe()}get selectable(){return this.$$.ctx[3]}set selectable(e){this.$$set({selectable:e}),fe()}}const Ct=Et,{SvelteComponent:Tt,attr:Y,detach:Le,element:Cl,empty:Nt,flush:J,init:Ot,insert:Me,listen:le,noop:xe,run_all:Tl,safe_not_equal:Lt,set_style:se}=window.__gradio__svelte__internal;function Mt(l){let e,t,n,i;return{c(){e=Cl("input"),Y(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,Y(e,"type","number"),Y(e,"step","0.1"),Y(e,"style",t="background-color: rgba("+(typeof l[1]=="number"&&l[1]<0?"128, 90, 213,"+-l[1]:"239, 68, 60,"+l[1])+")"),e.value=l[1],se(e,"width","7ch")},m(s,o){Me(s,e,o),e.focus(),n||(i=[le(e,"input",l[8]),le(e,"blur",l[14]),le(e,"keydown",l[15])],n=!0)},p(s,o){o&2&&t!==(t="background-color: rgba("+(typeof s[1]=="number"&&s[1]<0?"128, 90, 213,"+-s[1]:"239, 68, 60,"+s[1])+")")&&Y(e,"style",t),o&2&&e.value!==s[1]&&(e.value=s[1]);const a=o&2;(o&2||a)&&se(e,"width","7ch")},d(s){s&&Le(e),n=!1,Tl(i)}}}function Vt(l){let e,t,n,i;return{c(){e=Cl("input"),Y(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,Y(e,"id",t=`label-input-${l[3]}`),Y(e,"type","text"),Y(e,"placeholder","label"),e.value=l[1],se(e,"background-color",l[1]===null||l[2]&&l[2]!==l[1]?"":l[6][l[1]].primary),se(e,"width",l[7]?l[7].toString()?.length+4+"ch":"8ch")},m(s,o){Me(s,e,o),e.focus(),n||(i=[le(e,"input",l[8]),le(e,"blur",l[12]),le(e,"keydown",l[13]),le(e,"focus",Bt)],n=!0)},p(s,o){o&8&&t!==(t=`label-input-${s[3]}`)&&Y(e,"id",t),o&2&&e.value!==s[1]&&(e.value=s[1]),o&70&&se(e,"background-color",s[1]===null||s[2]&&s[2]!==s[1]?"":s[6][s[1]].primary),o&128&&se(e,"width",s[7]?s[7].toString()?.length+4+"ch":"8ch")},d(s){s&&Le(e),n=!1,Tl(i)}}}function Ht(l){let e;function t(s,o){return s[5]?Mt:Vt}let n=t(l),i=n(l);return{c(){i.c(),e=Nt()},m(s,o){i.m(s,o),Me(s,e,o)},p(s,[o]){n===(n=t(s))&&i?i.p(s,o):(i.d(1),i=n(s),i&&(i.c(),i.m(e.parentNode,e)))},i:xe,o:xe,d(s){s&&Le(e),i.d(s)}}}function Bt(l){let e=l.target;e&&e.placeholder&&(e.placeholder="")}function It(l,e,t){let{value:n}=e,{category:i}=e,{active:s}=e,{labelToEdit:o}=e,{indexOfLabel:a}=e,{text:r}=e,{handleValueChange:f}=e,{isScoresMode:c=!1}=e,{_color_map:g}=e,k=i;function m(h){let j=h.target;j&&t(7,k=j.value)}function _(h,j,b){let z=h.target;t(10,n=[...n.slice(0,j),{token:b,class_or_confidence:z.value===""?null:c?Number(z.value):z.value},...n.slice(j+1)]),f()}const d=h=>_(h,a,r),w=h=>{h.key==="Enter"&&(_(h,a,r),t(0,o=-1))},S=h=>_(h,a,r),y=h=>{h.key==="Enter"&&(_(h,a,r),t(0,o=-1))};return l.$$set=h=>{"value"in h&&t(10,n=h.value),"category"in h&&t(1,i=h.category),"active"in h&&t(2,s=h.active),"labelToEdit"in h&&t(0,o=h.labelToEdit),"indexOfLabel"in h&&t(3,a=h.indexOfLabel),"text"in h&&t(4,r=h.text),"handleValueChange"in h&&t(11,f=h.handleValueChange),"isScoresMode"in h&&t(5,c=h.isScoresMode),"_color_map"in h&&t(6,g=h._color_map)},[o,i,s,a,r,c,g,k,m,_,n,f,d,w,S,y]}class Nl extends Tt{constructor(e){super(),Ot(this,e,It,Ht,Lt,{value:10,category:1,active:2,labelToEdit:0,indexOfLabel:3,text:4,handleValueChange:11,isScoresMode:5,_color_map:6})}get value(){return this.$$.ctx[10]}set value(e){this.$$set({value:e}),J()}get category(){return this.$$.ctx[1]}set category(e){this.$$set({category:e}),J()}get active(){return this.$$.ctx[2]}set active(e){this.$$set({active:e}),J()}get labelToEdit(){return this.$$.ctx[0]}set labelToEdit(e){this.$$set({labelToEdit:e}),J()}get indexOfLabel(){return this.$$.ctx[3]}set indexOfLabel(e){this.$$set({indexOfLabel:e}),J()}get text(){return this.$$.ctx[4]}set text(e){this.$$set({text:e}),J()}get handleValueChange(){return this.$$.ctx[11]}set handleValueChange(e){this.$$set({handleValueChange:e}),J()}get isScoresMode(){return this.$$.ctx[5]}set isScoresMode(e){this.$$set({isScoresMode:e}),J()}get _color_map(){return this.$$.ctx[6]}set _color_map(e){this.$$set({_color_map:e}),J()}}const{SvelteComponent:qt,add_flush_callback:Ol,append:D,attr:p,bind:Ll,binding_callbacks:Ml,check_outros:oe,create_component:Vl,destroy_component:Hl,destroy_each:ze,detach:O,element:B,empty:Ve,ensure_array_like:ee,flush:ke,group_outros:ie,init:Rt,insert:L,listen:E,mount_component:Bl,run_all:ae,safe_not_equal:Dt,set_data:Ee,set_style:we,space:G,text:ge,toggle_class:F,transition_in:T,transition_out:R}=window.__gradio__svelte__internal,{createEventDispatcher:At,onMount:Ft}=window.__gradio__svelte__internal;function el(l,e,t){const n=l.slice();n[45]=e[t].token,n[46]=e[t].class_or_confidence,n[48]=t;const i=typeof n[46]=="string"?parseInt(n[46]):n[46];return n[54]=i,n}function ll(l,e,t){const n=l.slice();return n[45]=e[t].token,n[46]=e[t].class_or_confidence,n[48]=t,n}function tl(l,e,t){const n=l.slice();return n[49]=e[t],n[51]=t,n}function nl(l,e,t){const n=l.slice();return n[46]=e[t][0],n[52]=e[t][1],n[48]=t,n}function Zt(l){let e,t,n,i=l[1]&&ol(),s=ee(l[0]),o=[];for(let r=0;r<s.length;r+=1)o[r]=rl(el(l,s,r));const a=r=>R(o[r],1,1,()=>{o[r]=null});return{c(){i&&i.c(),e=G(),t=B("div");for(let r=0;r<o.length;r+=1)o[r].c();p(t,"class","textfield svelte-1ozsnjl"),p(t,"data-testid","highlighted-text:textfield")},m(r,f){i&&i.m(r,f),L(r,e,f),L(r,t,f);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(t,null);n=!0},p(r,f){if(r[1]?i||(i=ol(),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null),f[0]&889){s=ee(r[0]);let c;for(c=0;c<s.length;c+=1){const g=el(r,s,c);o[c]?(o[c].p(g,f),T(o[c],1)):(o[c]=rl(g),o[c].c(),T(o[c],1),o[c].m(t,null))}for(ie(),c=s.length;c<o.length;c+=1)a(c);oe()}},i(r){if(!n){for(let f=0;f<s.length;f+=1)T(o[f]);n=!0}},o(r){o=o.filter(Boolean);for(let f=0;f<o.length;f+=1)R(o[f]);n=!1},d(r){r&&(O(e),O(t)),i&&i.d(r),ze(o,r)}}}function Kt(l){let e,t,n,i=l[1]&&al(l),s=ee(l[0]),o=[];for(let r=0;r<s.length;r+=1)o[r]=bl(ll(l,s,r));const a=r=>R(o[r],1,1,()=>{o[r]=null});return{c(){i&&i.c(),e=G(),t=B("div");for(let r=0;r<o.length;r+=1)o[r].c();p(t,"class","textfield svelte-1ozsnjl")},m(r,f){i&&i.m(r,f),L(r,e,f),L(r,t,f);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(t,null);n=!0},p(r,f){if(r[1]?i?i.p(r,f):(i=al(r),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null),f[0]&13183){s=ee(r[0]);let c;for(c=0;c<s.length;c+=1){const g=ll(r,s,c);o[c]?(o[c].p(g,f),T(o[c],1)):(o[c]=bl(g),o[c].c(),T(o[c],1),o[c].m(t,null))}for(ie(),c=s.length;c<o.length;c+=1)a(c);oe()}},i(r){if(!n){for(let f=0;f<s.length;f+=1)T(o[f]);n=!0}},o(r){o=o.filter(Boolean);for(let f=0;f<o.length;f+=1)R(o[f]);n=!1},d(r){r&&(O(e),O(t)),i&&i.d(r),ze(o,r)}}}function ol(l){let e;return{c(){e=B("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",p(e,"class","color-legend svelte-1ozsnjl"),p(e,"data-testid","highlighted-text:color-legend")},m(t,n){L(t,e,n)},d(t){t&&O(e)}}}function il(l){let e,t,n;function i(o){l[32](o)}let s={labelToEdit:l[6],_color_map:l[3],category:l[46],active:l[5],indexOfLabel:l[48],text:l[45],handleValueChange:l[9],isScoresMode:!0};return l[0]!==void 0&&(s.value=l[0]),e=new Nl({props:s}),Ml.push(()=>Ll(e,"value",i)),{c(){Vl(e.$$.fragment)},m(o,a){Bl(e,o,a),n=!0},p(o,a){const r={};a[0]&64&&(r.labelToEdit=o[6]),a[0]&8&&(r._color_map=o[3]),a[0]&1&&(r.category=o[46]),a[0]&32&&(r.active=o[5]),a[0]&1&&(r.text=o[45]),!t&&a[0]&1&&(t=!0,r.value=o[0],Ol(()=>t=!1)),e.$set(r)},i(o){n||(T(e.$$.fragment,o),n=!0)},o(o){R(e.$$.fragment,o),n=!1},d(o){Hl(e,o)}}}function sl(l){let e,t,n;function i(){return l[37](l[48])}function s(...o){return l[38](l[48],...o)}return{c(){e=B("span"),e.textContent="×",p(e,"class","label-clear-button svelte-1ozsnjl"),p(e,"role","button"),p(e,"aria-roledescription","Remove label from text"),p(e,"tabindex","0")},m(o,a){L(o,e,a),t||(n=[E(e,"click",i),E(e,"keydown",s)],t=!0)},p(o,a){l=o},d(o){o&&O(e),t=!1,ae(n)}}}function rl(l){let e,t,n,i=l[45]+"",s,o,a,r,f,c,g,k,m=l[46]&&l[6]===l[48]&&il(l);function _(){return l[33](l[48])}function d(){return l[34](l[48])}function w(){return l[35](l[48])}function S(...h){return l[36](l[48],...h)}let y=l[46]&&l[4]===l[48]&&sl(l);return{c(){e=B("span"),t=B("span"),n=B("span"),s=ge(i),o=G(),m&&m.c(),r=G(),y&&y.c(),f=G(),p(n,"class","text svelte-1ozsnjl"),p(t,"class","textspan score-text svelte-1ozsnjl"),p(t,"role","button"),p(t,"tabindex","0"),p(t,"style",a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"),F(t,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),F(t,"hl",l[46]!==null),p(e,"class","score-text-container svelte-1ozsnjl")},m(h,j){L(h,e,j),D(e,t),D(t,n),D(n,s),D(t,o),m&&m.m(t,null),D(e,r),y&&y.m(e,null),D(e,f),c=!0,g||(k=[E(t,"mouseover",_),E(t,"focus",d),E(t,"click",w),E(t,"keydown",S)],g=!0)},p(h,j){l=h,(!c||j[0]&1)&&i!==(i=l[45]+"")&&Ee(s,i),l[46]&&l[6]===l[48]?m?(m.p(l,j),j[0]&65&&T(m,1)):(m=il(l),m.c(),T(m,1),m.m(t,null)):m&&(ie(),R(m,1,1,()=>{m=null}),oe()),(!c||j[0]&1&&a!==(a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"))&&p(t,"style",a),(!c||j[0]&33)&&F(t,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!c||j[0]&1)&&F(t,"hl",l[46]!==null),l[46]&&l[4]===l[48]?y?y.p(l,j):(y=sl(l),y.c(),y.m(e,f)):y&&(y.d(1),y=null)},i(h){c||(T(m),c=!0)},o(h){R(m),c=!1},d(h){h&&O(e),m&&m.d(),y&&y.d(),g=!1,ae(k)}}}function al(l){let e,t=l[3]&&cl(l);return{c(){e=B("div"),t&&t.c(),p(e,"class","class_or_confidence-legend svelte-1ozsnjl"),p(e,"data-testid","highlighted-text:class_or_confidence-legend")},m(n,i){L(n,e,i),t&&t.m(e,null)},p(n,i){n[3]?t?t.p(n,i):(t=cl(n),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(n){n&&O(e),t&&t.d()}}}function cl(l){let e,t=ee(Object.entries(l[3])),n=[];for(let i=0;i<t.length;i+=1)n[i]=fl(nl(l,t,i));return{c(){for(let i=0;i<n.length;i+=1)n[i].c();e=Ve()},m(i,s){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(i,s);L(i,e,s)},p(i,s){if(s[0]&3080){t=ee(Object.entries(i[3]));let o;for(o=0;o<t.length;o+=1){const a=nl(i,t,o);n[o]?n[o].p(a,s):(n[o]=fl(a),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(i){i&&O(e),ze(n,i)}}}function fl(l){let e,t=l[46]+"",n,i,s,o,a;function r(){return l[15](l[46])}function f(){return l[16](l[46])}return{c(){e=B("div"),n=ge(t),i=G(),p(e,"role","button"),p(e,"aria-roledescription","Categories of highlighted text. Hover to see text with this class_or_confidence highlighted."),p(e,"tabindex","0"),p(e,"class","class_or_confidence-label svelte-1ozsnjl"),p(e,"style",s="background-color:"+l[52].secondary)},m(c,g){L(c,e,g),D(e,n),D(e,i),o||(a=[E(e,"mouseover",r),E(e,"focus",f),E(e,"mouseout",l[17]),E(e,"blur",l[18])],o=!0)},p(c,g){l=c,g[0]&8&&t!==(t=l[46]+"")&&Ee(n,t),g[0]&8&&s!==(s="background-color:"+l[52].secondary)&&p(e,"style",s)},d(c){c&&O(e),o=!1,ae(a)}}}function _l(l){let e,t,n,i=l[49]+"",s,o,a,r,f,c,g;function k(){return l[20](l[48])}function m(){return l[21](l[48])}function _(){return l[22](l[48])}let d=!l[1]&&l[46]!==null&&l[6]!==l[48]&&ul(l),w=l[6]===l[48]&&l[46]!==null&&dl(l);function S(){return l[26](l[46],l[48],l[45])}function y(...z){return l[27](l[46],l[48],l[45],...z)}function h(){return l[28](l[48])}function j(){return l[29](l[48])}let b=l[46]!==null&&hl(l);return{c(){e=B("span"),t=B("span"),n=B("span"),s=ge(i),o=G(),d&&d.c(),a=G(),w&&w.c(),r=G(),b&&b.c(),p(n,"class","text svelte-1ozsnjl"),p(n,"role","button"),p(n,"tabindex","0"),F(n,"no-label",l[46]===null),p(t,"role","button"),p(t,"tabindex","0"),p(t,"class","textspan svelte-1ozsnjl"),F(t,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),F(t,"hl",l[46]!==null),F(t,"selectable",l[2]),we(t,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),p(e,"class","text-class_or_confidence-container svelte-1ozsnjl")},m(z,M){L(z,e,M),D(e,t),D(t,n),D(n,s),D(t,o),d&&d.m(t,null),D(t,a),w&&w.m(t,null),D(e,r),b&&b.m(e,null),f=!0,c||(g=[E(n,"keydown",l[19]),E(n,"focus",k),E(n,"mouseover",m),E(n,"click",_),E(t,"click",S),E(t,"keydown",y),E(t,"focus",h),E(t,"mouseover",j)],c=!0)},p(z,M){l=z,(!f||M[0]&1)&&i!==(i=l[49]+"")&&Ee(s,i),(!f||M[0]&1)&&F(n,"no-label",l[46]===null),!l[1]&&l[46]!==null&&l[6]!==l[48]?d?d.p(l,M):(d=ul(l),d.c(),d.m(t,a)):d&&(d.d(1),d=null),l[6]===l[48]&&l[46]!==null?w?(w.p(l,M),M[0]&65&&T(w,1)):(w=dl(l),w.c(),T(w,1),w.m(t,null)):w&&(ie(),R(w,1,1,()=>{w=null}),oe()),(!f||M[0]&33)&&F(t,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!f||M[0]&1)&&F(t,"hl",l[46]!==null),(!f||M[0]&4)&&F(t,"selectable",l[2]),M[0]&41&&we(t,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),l[46]!==null?b?b.p(l,M):(b=hl(l),b.c(),b.m(e,null)):b&&(b.d(1),b=null)},i(z){f||(T(w),f=!0)},o(z){R(w),f=!1},d(z){z&&O(e),d&&d.d(),w&&w.d(),b&&b.d(),c=!1,ae(g)}}}function ul(l){let e,t=l[46]+"",n,i,s;function o(){return l[23](l[48])}function a(){return l[24](l[48])}return{c(){e=B("span"),n=ge(t),p(e,"id",`label-tag-${l[48]}`),p(e,"class","label svelte-1ozsnjl"),p(e,"role","button"),p(e,"tabindex","0"),we(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},m(r,f){L(r,e,f),D(e,n),i||(s=[E(e,"click",o),E(e,"keydown",a)],i=!0)},p(r,f){l=r,f[0]&1&&t!==(t=l[46]+"")&&Ee(n,t),f[0]&41&&we(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},d(r){r&&O(e),i=!1,ae(s)}}}function dl(l){let e,t,n,i;function s(a){l[25](a)}let o={labelToEdit:l[6],category:l[46],active:l[5],_color_map:l[3],indexOfLabel:l[48],text:l[45],handleValueChange:l[9]};return l[0]!==void 0&&(o.value=l[0]),t=new Nl({props:o}),Ml.push(()=>Ll(t,"value",s)),{c(){e=ge(` 
									`),Vl(t.$$.fragment)},m(a,r){L(a,e,r),Bl(t,a,r),i=!0},p(a,r){const f={};r[0]&64&&(f.labelToEdit=a[6]),r[0]&1&&(f.category=a[46]),r[0]&32&&(f.active=a[5]),r[0]&8&&(f._color_map=a[3]),r[0]&1&&(f.text=a[45]),!n&&r[0]&1&&(n=!0,f.value=a[0],Ol(()=>n=!1)),t.$set(f)},i(a){i||(T(t.$$.fragment,a),i=!0)},o(a){R(t.$$.fragment,a),i=!1},d(a){a&&O(e),Hl(t,a)}}}function hl(l){let e,t,n;function i(){return l[30](l[48])}function s(...o){return l[31](l[48],...o)}return{c(){e=B("span"),e.textContent="×",p(e,"class","label-clear-button svelte-1ozsnjl"),p(e,"role","button"),p(e,"aria-roledescription","Remove label from text"),p(e,"tabindex","0")},m(o,a){L(o,e,a),t||(n=[E(e,"click",i),E(e,"keydown",s)],t=!0)},p(o,a){l=o},d(o){o&&O(e),t=!1,ae(n)}}}function gl(l){let e;return{c(){e=B("br")},m(t,n){L(t,e,n)},d(t){t&&O(e)}}}function ml(l){let e=l[49].trim()!=="",t,n=l[51]<ye(l[45]).length-1,i,s,o=e&&_l(l),a=n&&gl();return{c(){o&&o.c(),t=G(),a&&a.c(),i=Ve()},m(r,f){o&&o.m(r,f),L(r,t,f),a&&a.m(r,f),L(r,i,f),s=!0},p(r,f){f[0]&1&&(e=r[49].trim()!==""),e?o?(o.p(r,f),f[0]&1&&T(o,1)):(o=_l(r),o.c(),T(o,1),o.m(t.parentNode,t)):o&&(ie(),R(o,1,1,()=>{o=null}),oe()),f[0]&1&&(n=r[51]<ye(r[45]).length-1),n?a||(a=gl(),a.c(),a.m(i.parentNode,i)):a&&(a.d(1),a=null)},i(r){s||(T(o),s=!0)},o(r){R(o),s=!1},d(r){r&&(O(t),O(i)),o&&o.d(r),a&&a.d(r)}}}function bl(l){let e,t,n=ee(ye(l[45])),i=[];for(let o=0;o<n.length;o+=1)i[o]=ml(tl(l,n,o));const s=o=>R(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=Ve()},m(o,a){for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(o,a);L(o,e,a),t=!0},p(o,a){if(a[0]&13183){n=ee(ye(o[45]));let r;for(r=0;r<n.length;r+=1){const f=tl(o,n,r);i[r]?(i[r].p(f,a),T(i[r],1)):(i[r]=ml(f),i[r].c(),T(i[r],1),i[r].m(e.parentNode,e))}for(ie(),r=n.length;r<i.length;r+=1)s(r);oe()}},i(o){if(!t){for(let a=0;a<n.length;a+=1)T(i[a]);t=!0}},o(o){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)R(i[a]);t=!1},d(o){o&&O(e),ze(i,o)}}}function Pt(l){let e,t,n,i;const s=[Kt,Zt],o=[];function a(r,f){return r[7]==="categories"?0:1}return t=a(l),n=o[t]=s[t](l),{c(){e=B("div"),n.c(),p(e,"class","container svelte-1ozsnjl")},m(r,f){L(r,e,f),o[t].m(e,null),i=!0},p(r,f){let c=t;t=a(r),t===c?o[t].p(r,f):(ie(),R(o[c],1,1,()=>{o[c]=null}),oe(),n=o[t],n?n.p(r,f):(n=o[t]=s[t](r),n.c()),T(n,1),n.m(e,null))},i(r){i||(T(n),i=!0)},o(r){R(n),i=!1},d(r){r&&O(e),o[t].d()}}}function ye(l){return l.split(`
`)}function Ut(l,e,t){const n=typeof document<"u";let{value:i=[]}=e,{show_legend:s=!1}=e,{color_map:o={}}=e,{selectable:a=!1}=e,r=-1,f,c={},g="",k,m=-1;Ft(()=>{const u=()=>{k=window.getSelection(),z(),window.removeEventListener("mouseup",u)};window.addEventListener("mousedown",()=>{window.addEventListener("mouseup",u)})});async function _(u,N){if(k?.toString()&&r!==-1&&i[r].token.toString().includes(k.toString())){const W=Symbol(),ce=i[r].token,[at,ct,ft]=[ce.substring(0,u),ce.substring(u,N),ce.substring(N)];let me=[...i.slice(0,r),{token:at,class_or_confidence:null},{token:ct,class_or_confidence:y==="scores"?1:"label",flag:W},{token:ft,class_or_confidence:null},...i.slice(r+1)];t(6,m=me.findIndex(({flag:be})=>be===W)),me=me.filter(be=>be.token.trim()!==""),t(0,i=me.map(({flag:be,..._t})=>_t)),S(),document.getElementById(`label-input-${m}`)?.focus()}}const d=At();function w(u){!i||u<0||u>=i.length||(t(0,i[u].class_or_confidence=null,i),t(0,i=zl(i)),S(),window.getSelection()?.empty())}function S(){d("change",i),t(6,m=-1),s&&(t(14,o={}),t(3,c={}))}let y;function h(u){t(5,g=u)}function j(){t(5,g="")}async function b(u){k=window.getSelection(),u.key==="Enter"&&z()}function z(){if(k&&k?.toString().trim()!==""){const u=k.getRangeAt(0).startOffset,N=k.getRangeAt(0).endOffset;_(u,N)}}function M(u,N,W){d("select",{index:u,value:[N,W]})}const Ce=u=>h(u),v=u=>h(u),Dl=()=>j(),Al=()=>j(),Fl=u=>b(u),Zl=u=>t(4,r=u),Kl=u=>t(4,r=u),Pl=u=>t(6,m=u),Ul=u=>t(6,m=u),Yl=u=>t(6,m=u);function Gl(u){i=u,t(0,i)}const Jl=(u,N,W)=>{u!==null&&M(N,W,u)},Ql=(u,N,W,ce)=>{u!==null?(t(6,m=N),M(N,W,u)):b(ce)},Wl=u=>t(4,r=u),Xl=u=>t(4,r=u),xl=u=>w(u),et=(u,N)=>{N.key==="Enter"&&w(u)};function lt(u){i=u,t(0,i)}const tt=u=>t(4,r=u),nt=u=>t(4,r=u),ot=u=>t(6,m=u),it=(u,N)=>{N.key==="Enter"&&t(6,m=u)},st=u=>w(u),rt=(u,N)=>{N.key==="Enter"&&w(u)};return l.$$set=u=>{"value"in u&&t(0,i=u.value),"show_legend"in u&&t(1,s=u.show_legend),"color_map"in u&&t(14,o=u.color_map),"selectable"in u&&t(2,a=u.selectable)},l.$$.update=()=>{if(l.$$.dirty[0]&16393){if(o||t(14,o={}),i.length>0){for(let u of i)if(u.class_or_confidence!==null)if(typeof u.class_or_confidence=="string"){if(t(7,y="categories"),!(u.class_or_confidence in o)){let N=vl(Object.keys(o).length);t(14,o[u.class_or_confidence]=N,o)}}else t(7,y="scores")}$l(o,c,n,f)}},[i,s,a,c,r,g,m,y,w,S,h,j,b,M,o,Ce,v,Dl,Al,Fl,Zl,Kl,Pl,Ul,Yl,Gl,Jl,Ql,Wl,Xl,xl,et,lt,tt,nt,ot,it,st,rt]}class Yt extends qt{constructor(e){super(),Rt(this,e,Ut,Pt,Dt,{value:0,show_legend:1,color_map:14,selectable:2},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),ke()}get show_legend(){return this.$$.ctx[1]}set show_legend(e){this.$$set({show_legend:e}),ke()}get color_map(){return this.$$.ctx[14]}set color_map(e){this.$$set({color_map:e}),ke()}get selectable(){return this.$$.ctx[2]}set selectable(e){this.$$set({selectable:e}),ke()}}const Gt=Yt,{SvelteComponent:Jt,add_flush_callback:Qt,assign:Il,bind:Wt,binding_callbacks:Xt,check_outros:de,create_component:K,destroy_component:P,detach:te,empty:He,flush:H,get_spread_object:ql,get_spread_update:Rl,group_outros:he,init:xt,insert:ne,mount_component:U,safe_not_equal:en,space:je,transition_in:$,transition_out:C}=window.__gradio__svelte__internal;function ln(l){let e,t;return e=new yl({props:{variant:l[13]?"dashed":"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[9],scale:l[10],min_width:l[11],$$slots:{default:[rn]},$$scope:{ctx:l}}}),{c(){K(e.$$.fragment)},m(n,i){U(e,n,i),t=!0},p(n,i){const s={};i&8192&&(s.variant=n[13]?"dashed":"solid"),i&32&&(s.visible=n[5]),i&8&&(s.elem_id=n[3]),i&16&&(s.elem_classes=n[4]),i&512&&(s.container=n[9]),i&1024&&(s.scale=n[10]),i&2048&&(s.min_width=n[11]),i&4215623&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function tn(l){let e,t;return e=new yl({props:{variant:"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[9],scale:l[10],min_width:l[11],$$slots:{default:[_n]},$$scope:{ctx:l}}}),{c(){K(e.$$.fragment)},m(n,i){U(e,n,i),t=!0},p(n,i){const s={};i&32&&(s.visible=n[5]),i&8&&(s.elem_id=n[3]),i&16&&(s.elem_classes=n[4]),i&512&&(s.container=n[9]),i&1024&&(s.scale=n[10]),i&2048&&(s.min_width=n[11]),i&4215751&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function kl(l){let e,t;return e=new jl({props:{Icon:Se,label:l[8],float:!1,disable:l[9]===!1}}),{c(){K(e.$$.fragment)},m(n,i){U(e,n,i),t=!0},p(n,i){const s={};i&256&&(s.label=n[8]),i&512&&(s.disable=n[9]===!1),e.$set(s)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function nn(l){let e,t;return e=new Sl({props:{$$slots:{default:[sn]},$$scope:{ctx:l}}}),{c(){K(e.$$.fragment)},m(n,i){U(e,n,i),t=!0},p(n,i){const s={};i&4194304&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function on(l){let e,t,n;function i(o){l[20](o)}let s={selectable:l[12],show_legend:l[6],color_map:l[1]};return l[0]!==void 0&&(s.value=l[0]),e=new Gt({props:s}),Xt.push(()=>Wt(e,"value",i)),e.$on("change",l[21]),{c(){K(e.$$.fragment)},m(o,a){U(e,o,a),n=!0},p(o,a){const r={};a&4096&&(r.selectable=o[12]),a&64&&(r.show_legend=o[6]),a&2&&(r.color_map=o[1]),!t&&a&1&&(t=!0,r.value=o[0],Qt(()=>t=!1)),e.$set(r)},i(o){n||($(e.$$.fragment,o),n=!0)},o(o){C(e.$$.fragment,o),n=!1},d(o){P(e,o)}}}function sn(l){let e,t;return e=new Se({}),{c(){K(e.$$.fragment)},m(n,i){U(e,n,i),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function rn(l){let e,t,n,i,s,o,a;const r=[{autoscroll:l[2].autoscroll},l[14],{i18n:l[2].i18n}];let f={};for(let _=0;_<r.length;_+=1)f=Il(f,r[_]);e=new wl({props:f}),e.$on("clear_status",l[19]);let c=l[8]&&kl(l);const g=[on,nn],k=[];function m(_,d){return _[0]?0:1}return i=m(l),s=k[i]=g[i](l),{c(){K(e.$$.fragment),t=je(),c&&c.c(),n=je(),s.c(),o=He()},m(_,d){U(e,_,d),ne(_,t,d),c&&c.m(_,d),ne(_,n,d),k[i].m(_,d),ne(_,o,d),a=!0},p(_,d){const w=d&16388?Rl(r,[d&4&&{autoscroll:_[2].autoscroll},d&16384&&ql(_[14]),d&4&&{i18n:_[2].i18n}]):{};e.$set(w),_[8]?c?(c.p(_,d),d&256&&$(c,1)):(c=kl(_),c.c(),$(c,1),c.m(n.parentNode,n)):c&&(he(),C(c,1,1,()=>{c=null}),de());let S=i;i=m(_),i===S?k[i].p(_,d):(he(),C(k[S],1,1,()=>{k[S]=null}),de(),s=k[i],s?s.p(_,d):(s=k[i]=g[i](_),s.c()),$(s,1),s.m(o.parentNode,o))},i(_){a||($(e.$$.fragment,_),$(c),$(s),a=!0)},o(_){C(e.$$.fragment,_),C(c),C(s),a=!1},d(_){_&&(te(t),te(n),te(o)),P(e,_),c&&c.d(_),k[i].d(_)}}}function pl(l){let e,t;return e=new jl({props:{Icon:Se,label:l[8],float:!1,disable:l[9]===!1}}),{c(){K(e.$$.fragment)},m(n,i){U(e,n,i),t=!0},p(n,i){const s={};i&256&&(s.label=n[8]),i&512&&(s.disable=n[9]===!1),e.$set(s)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function an(l){let e,t;return e=new Sl({props:{$$slots:{default:[fn]},$$scope:{ctx:l}}}),{c(){K(e.$$.fragment)},m(n,i){U(e,n,i),t=!0},p(n,i){const s={};i&4194304&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function cn(l){let e,t;return e=new Ct({props:{selectable:l[12],value:l[0],show_legend:l[6],show_inline_category:l[7],color_map:l[1]}}),e.$on("select",l[18]),{c(){K(e.$$.fragment)},m(n,i){U(e,n,i),t=!0},p(n,i){const s={};i&4096&&(s.selectable=n[12]),i&1&&(s.value=n[0]),i&64&&(s.show_legend=n[6]),i&128&&(s.show_inline_category=n[7]),i&2&&(s.color_map=n[1]),e.$set(s)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function fn(l){let e,t;return e=new Se({}),{c(){K(e.$$.fragment)},m(n,i){U(e,n,i),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function _n(l){let e,t,n,i,s,o,a;const r=[{autoscroll:l[2].autoscroll},{i18n:l[2].i18n},l[14]];let f={};for(let _=0;_<r.length;_+=1)f=Il(f,r[_]);e=new wl({props:f}),e.$on("clear_status",l[17]);let c=l[8]&&pl(l);const g=[cn,an],k=[];function m(_,d){return _[0]?0:1}return i=m(l),s=k[i]=g[i](l),{c(){K(e.$$.fragment),t=je(),c&&c.c(),n=je(),s.c(),o=He()},m(_,d){U(e,_,d),ne(_,t,d),c&&c.m(_,d),ne(_,n,d),k[i].m(_,d),ne(_,o,d),a=!0},p(_,d){const w=d&16388?Rl(r,[d&4&&{autoscroll:_[2].autoscroll},d&4&&{i18n:_[2].i18n},d&16384&&ql(_[14])]):{};e.$set(w),_[8]?c?(c.p(_,d),d&256&&$(c,1)):(c=pl(_),c.c(),$(c,1),c.m(n.parentNode,n)):c&&(he(),C(c,1,1,()=>{c=null}),de());let S=i;i=m(_),i===S?k[i].p(_,d):(he(),C(k[S],1,1,()=>{k[S]=null}),de(),s=k[i],s?s.p(_,d):(s=k[i]=g[i](_),s.c()),$(s,1),s.m(o.parentNode,o))},i(_){a||($(e.$$.fragment,_),$(c),$(s),a=!0)},o(_){C(e.$$.fragment,_),C(c),C(s),a=!1},d(_){_&&(te(t),te(n),te(o)),P(e,_),c&&c.d(_),k[i].d(_)}}}function un(l){let e,t,n,i;const s=[tn,ln],o=[];function a(r,f){return r[13]?1:0}return e=a(l),t=o[e]=s[e](l),{c(){t.c(),n=He()},m(r,f){o[e].m(r,f),ne(r,n,f),i=!0},p(r,[f]){let c=e;e=a(r),e===c?o[e].p(r,f):(he(),C(o[c],1,1,()=>{o[c]=null}),de(),t=o[e],t?t.p(r,f):(t=o[e]=s[e](r),t.c()),$(t,1),t.m(n.parentNode,n))},i(r){i||($(t),i=!0)},o(r){C(t),i=!1},d(r){r&&te(n),o[e].d(r)}}}function dn(l,e,t){let{gradio:n}=e,{elem_id:i=""}=e,{elem_classes:s=[]}=e,{visible:o=!0}=e,{value:a}=e,r,{show_legend:f}=e,{show_inline_category:c}=e,{color_map:g={}}=e,{label:k=n.i18n("highlighted_text.highlighted_text")}=e,{container:m=!0}=e,{scale:_=null}=e,{min_width:d=void 0}=e,{_selectable:w=!1}=e,{combine_adjacent:S=!1}=e,{interactive:y}=e,{loading_status:h}=e;const j=()=>n.dispatch("clear_status",h),b=({detail:v})=>n.dispatch("select",v),z=()=>n.dispatch("clear_status",h);function M(v){a=v,t(0,a),t(15,S)}const Ce=()=>n.dispatch("change");return l.$$set=v=>{"gradio"in v&&t(2,n=v.gradio),"elem_id"in v&&t(3,i=v.elem_id),"elem_classes"in v&&t(4,s=v.elem_classes),"visible"in v&&t(5,o=v.visible),"value"in v&&t(0,a=v.value),"show_legend"in v&&t(6,f=v.show_legend),"show_inline_category"in v&&t(7,c=v.show_inline_category),"color_map"in v&&t(1,g=v.color_map),"label"in v&&t(8,k=v.label),"container"in v&&t(9,m=v.container),"scale"in v&&t(10,_=v.scale),"min_width"in v&&t(11,d=v.min_width),"_selectable"in v&&t(12,w=v._selectable),"combine_adjacent"in v&&t(15,S=v.combine_adjacent),"interactive"in v&&t(13,y=v.interactive),"loading_status"in v&&t(14,h=v.loading_status)},l.$$.update=()=>{l.$$.dirty&2&&!g&&Object.keys(g).length&&t(1,g),l.$$.dirty&32769&&a&&S&&t(0,a=zl(a)),l.$$.dirty&65541&&a!==r&&(t(16,r=a),n.dispatch("change"))},[a,g,n,i,s,o,f,c,k,m,_,d,w,y,h,S,r,j,b,z,M,Ce]}class wn extends Jt{constructor(e){super(),xt(this,e,dn,un,en,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,show_legend:6,show_inline_category:7,color_map:1,label:8,container:9,scale:10,min_width:11,_selectable:12,combine_adjacent:15,interactive:13,loading_status:14})}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),H()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),H()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),H()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),H()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),H()}get show_legend(){return this.$$.ctx[6]}set show_legend(e){this.$$set({show_legend:e}),H()}get show_inline_category(){return this.$$.ctx[7]}set show_inline_category(e){this.$$set({show_inline_category:e}),H()}get color_map(){return this.$$.ctx[1]}set color_map(e){this.$$set({color_map:e}),H()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),H()}get container(){return this.$$.ctx[9]}set container(e){this.$$set({container:e}),H()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),H()}get min_width(){return this.$$.ctx[11]}set min_width(e){this.$$set({min_width:e}),H()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),H()}get combine_adjacent(){return this.$$.ctx[15]}set combine_adjacent(e){this.$$set({combine_adjacent:e}),H()}get interactive(){return this.$$.ctx[13]}set interactive(e){this.$$set({interactive:e}),H()}get loading_status(){return this.$$.ctx[14]}set loading_status(e){this.$$set({loading_status:e}),H()}}export{Gt as BaseInteractiveHighlightedText,Ct as BaseStaticHighlightedText,wn as default};
//# sourceMappingURL=Index-PWsHZhLu.js.map
