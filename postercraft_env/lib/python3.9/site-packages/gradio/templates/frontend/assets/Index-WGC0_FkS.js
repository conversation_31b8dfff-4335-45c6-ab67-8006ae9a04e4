const __vite__fileDeps=["./Blocks-aR9ucLZz.js","./index-COY1HN2y.js","./index-luc1OtuK.css","./Button-8nmImwVJ.js","./Button-CTZL5Nos.css","./Blocks-yLdzXwzS.css","./Login-D5t5pW6w.js","./Index-DDCF2BFd.js","./Index-B0JJ6p9c.css","./Textbox-BCZjJ4Bh.js","./BlockTitle-Bkh4EzYf.js","./Info-COHEyv9U.js","./Check-Ck0iADAu.js","./Copy-ZPOKSMtK.js","./Textbox-D8IAzrZj.css","./Index-ChKBj1l7.js","./Index-CptIZeFZ.css","./Login-BCwzjozv.css","./Example-Cj3ii62O.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{m as fs,a as Kt,_ as er}from"./index-COY1HN2y.js";function Se(){}const Yc=e=>e;function _s(e){return e()}function hs(e){e.forEach(_s)}function ds(e){return typeof e=="function"}function ms(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function Wr(e,...t){if(e==null){for(const o of t)o(void 0);return Se}const r=e.subscribe(...t);return r.unsubscribe?()=>r.unsubscribe():r}function Jc(e){let t;return Wr(e,r=>t=r)(),t}function Qc(e){const t=typeof e=="string"&&e.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[e,"px"]}const $r=typeof window<"u";let tr=$r?()=>window.performance.now():()=>Date.now(),Yr=$r?e=>requestAnimationFrame(e):Se;const Ae=new Set;function Jr(e){Ae.forEach(t=>{t.c(e)||(Ae.delete(t),t.f())}),Ae.size!==0&&Yr(Jr)}function ps(e){let t;return Ae.size===0&&Yr(Jr),{promise:new Promise(r=>{Ae.add(t={c:e,f:r})}),abort(){Ae.delete(t)}}}const ke=[];function gs(e,t){return{subscribe:Ne(e,t).subscribe}}function Ne(e,t=Se){let r;const o=new Set;function n(a){if(ms(e,a)&&(e=a,r)){const c=!ke.length;for(const l of o)l[1](),ke.push(l,e);if(c){for(let l=0;l<ke.length;l+=2)ke[l][0](ke[l+1]);ke.length=0}}}function i(a){n(a(e))}function s(a,c=Se){const l=[a,c];return o.add(l),o.size===1&&(r=t(n,i)||Se),a(e),()=>{o.delete(l),o.size===0&&r&&(r(),r=null)}}return{set:n,update:i,subscribe:s}}function Le(e,t,r){const o=!Array.isArray(e),n=o?[e]:e;if(!n.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const i=t.length<2;return gs(r,(s,a)=>{let c=!1;const l=[];let u=0,f=Se;const _=()=>{if(u)return;f();const b=t(o?l[0]:l,s,a);i?s(b):f=ds(b)?b:Se},d=n.map((b,g)=>Wr(b,S=>{l[g]=S,u&=~(1<<g),c&&_()},()=>{u|=1<<g}));return c=!0,_(),function(){hs(d),f(),c=!1}})}const bs="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='10'%20height='10'%20fill='none'%3e%3cpath%20fill='%23FF3270'%20d='M1.93%206.03v2.04h2.04V6.03H1.93Z'/%3e%3cpath%20fill='%23861FFF'%20d='M6.03%206.03v2.04h2.04V6.03H6.03Z'/%3e%3cpath%20fill='%23097EFF'%20d='M1.93%201.93v2.04h2.04V1.93H1.93Z'/%3e%3cpath%20fill='%23000'%20fill-rule='evenodd'%20d='M.5%201.4c0-.5.4-.9.9-.9h3.1a.9.9%200%200%201%20.87.67A2.44%202.44%200%200%201%209.5%202.95c0%20.65-.25%201.24-.67%***********.67.46.67.88v3.08c0%20.5-.4.91-.9.91H1.4a.9.9%200%200%201-.9-.9V1.4Zm1.43.53v2.04h2.04V1.93H1.93Zm0%206.14V6.03h2.04v2.04H1.93Zm4.1%200V6.03h2.04v2.04H6.03Zm0-5.12a1.02%201.02%200%201%201%202.04%200%201.02%201.02%200%200%201-2.04%200Z'%20clip-rule='evenodd'/%3e%3cpath%20fill='%23FFD702'%20d='M7.05%201.93a1.02%201.02%200%201%200%200%202.04%201.02%201.02%200%200%200%200-2.04Z'/%3e%3c/svg%3e";var Kc=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function vs(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ef(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function o(){return this instanceof o?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(o){var n=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(r,o,n.get?n:{enumerable:!0,get:function(){return e[o]}})}),r}var ys=function(t){return ws(t)&&!Es(t)};function ws(e){return!!e&&typeof e=="object"}function Es(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||Ts(e)}var Ss=typeof Symbol=="function"&&Symbol.for,xs=Ss?Symbol.for("react.element"):60103;function Ts(e){return e.$$typeof===xs}function ks(e){return Array.isArray(e)?[]:{}}function Ze(e,t){return t.clone!==!1&&t.isMergeableObject(e)?Be(ks(e),e,t):e}function Hs(e,t,r){return e.concat(t).map(function(o){return Ze(o,r)})}function Ps(e,t){if(!t.customMerge)return Be;var r=t.customMerge(e);return typeof r=="function"?r:Be}function Os(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[]}function rr(e){return Object.keys(e).concat(Os(e))}function Qr(e,t){try{return t in e}catch{return!1}}function As(e,t){return Qr(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function Bs(e,t,r){var o={};return r.isMergeableObject(e)&&rr(e).forEach(function(n){o[n]=Ze(e[n],r)}),rr(t).forEach(function(n){As(e,n)||(Qr(e,n)&&r.isMergeableObject(t[n])?o[n]=Ps(n,r)(e[n],t[n],r):o[n]=Ze(t[n],r))}),o}function Be(e,t,r){r=r||{},r.arrayMerge=r.arrayMerge||Hs,r.isMergeableObject=r.isMergeableObject||ys,r.cloneUnlessOtherwiseSpecified=Ze;var o=Array.isArray(t),n=Array.isArray(e),i=o===n;return i?o?r.arrayMerge(e,t,r):Bs(e,t,r):Ze(t,r)}Be.all=function(t,r){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(o,n){return Be(o,n,r)},{})};var Cs=Be,Is=Cs;const Ms=vs(Is);var It=function(e,t){return It=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,o){r.__proto__=o}||function(r,o){for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(r[n]=o[n])},It(e,t)};function pt(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");It(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var H=function(){return H=Object.assign||function(t){for(var r,o=1,n=arguments.length;o<n;o++){r=arguments[o];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},H.apply(this,arguments)};function Ns(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r}function St(e,t,r){if(r||arguments.length===2)for(var o=0,n=t.length,i;o<n;o++)(i||!(o in t))&&(i||(i=Array.prototype.slice.call(t,0,o)),i[o]=t[o]);return e.concat(i||Array.prototype.slice.call(t))}var x;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(x||(x={}));var A;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(A||(A={}));var Ce;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(Ce||(Ce={}));function or(e){return e.type===A.literal}function Ls(e){return e.type===A.argument}function Kr(e){return e.type===A.number}function eo(e){return e.type===A.date}function to(e){return e.type===A.time}function ro(e){return e.type===A.select}function oo(e){return e.type===A.plural}function Rs(e){return e.type===A.pound}function no(e){return e.type===A.tag}function io(e){return!!(e&&typeof e=="object"&&e.type===Ce.number)}function Mt(e){return!!(e&&typeof e=="object"&&e.type===Ce.dateTime)}var so=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,Ds=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function js(e){var t={};return e.replace(Ds,function(r){var o=r.length;switch(r[0]){case"G":t.era=o===4?"long":o===5?"narrow":"short";break;case"y":t.year=o===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][o-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][o-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=o===4?"long":o===5?"narrow":"short";break;case"e":if(o<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][o-4];break;case"c":if(o<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][o-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][o-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][o-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][o-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][o-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][o-1];break;case"s":t.second=["numeric","2-digit"][o-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=o<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var Gs=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function Fs(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(Gs).filter(function(_){return _.length>0}),r=[],o=0,n=t;o<n.length;o++){var i=n[o],s=i.split("/");if(s.length===0)throw new Error("Invalid number skeleton");for(var a=s[0],c=s.slice(1),l=0,u=c;l<u.length;l++){var f=u[l];if(f.length===0)throw new Error("Invalid number skeleton")}r.push({stem:a,options:c})}return r}function Us(e){return e.replace(/^(.*?)-/,"")}var nr=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,ao=/^(@+)?(\+|#+)?[rs]?$/g,Vs=/(\*)(0+)|(#+)(0+)|(0+)/g,lo=/^(0+)$/;function ir(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(ao,function(r,o,n){return typeof n!="string"?(t.minimumSignificantDigits=o.length,t.maximumSignificantDigits=o.length):n==="+"?t.minimumSignificantDigits=o.length:o[0]==="#"?t.maximumSignificantDigits=o.length:(t.minimumSignificantDigits=o.length,t.maximumSignificantDigits=o.length+(typeof n=="string"?n.length:0)),""}),t}function uo(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function zs(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if(r==="+!"?(t.signDisplay="always",e=e.slice(2)):r==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!lo.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function sr(e){var t={},r=uo(e);return r||t}function qs(e){for(var t={},r=0,o=e;r<o.length;r++){var n=o[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=Us(n.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=H(H(H({},t),{notation:"scientific"}),n.options.reduce(function(c,l){return H(H({},c),sr(l))},{}));continue;case"engineering":t=H(H(H({},t),{notation:"engineering"}),n.options.reduce(function(c,l){return H(H({},c),sr(l))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");n.options[0].replace(Vs,function(c,l,u,f,_,d){if(l)t.minimumIntegerDigits=u.length;else{if(f&&_)throw new Error("We currently do not support maximum integer digits");if(d)throw new Error("We currently do not support exact integer digits")}return""});continue}if(lo.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(nr.test(n.stem)){if(n.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(nr,function(c,l,u,f,_,d){return u==="*"?t.minimumFractionDigits=l.length:f&&f[0]==="#"?t.maximumFractionDigits=f.length:_&&d?(t.minimumFractionDigits=_.length,t.maximumFractionDigits=_.length+d.length):(t.minimumFractionDigits=l.length,t.maximumFractionDigits=l.length),""});var i=n.options[0];i==="w"?t=H(H({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=H(H({},t),ir(i)));continue}if(ao.test(n.stem)){t=H(H({},t),ir(n.stem));continue}var s=uo(n.stem);s&&(t=H(H({},t),s));var a=zs(n.stem);a&&(t=H(H({},t),a))}return t}var tt={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function Xs(e,t){for(var r="",o=0;o<e.length;o++){var n=e.charAt(o);if(n==="j"){for(var i=0;o+1<e.length&&e.charAt(o+1)===n;)i++,o++;var s=1+(i&1),a=i<2?1:3+(i>>1),c="a",l=Zs(t);for((l=="H"||l=="k")&&(a=0);a-- >0;)r+=c;for(;s-- >0;)r=l+r}else n==="J"?r+="H":r+=n}return r}function Zs(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var r=e.language,o;r!=="root"&&(o=e.maximize().region);var n=tt[o||""]||tt[r||""]||tt["".concat(r,"-001")]||tt["001"];return n[0]}var xt,Ws=new RegExp("^".concat(so.source,"*")),$s=new RegExp("".concat(so.source,"*$"));function T(e,t){return{start:e,end:t}}var Ys=!!String.prototype.startsWith&&"_a".startsWith("a",1),Js=!!String.fromCodePoint,Qs=!!Object.fromEntries,Ks=!!String.prototype.codePointAt,ea=!!String.prototype.trimStart,ta=!!String.prototype.trimEnd,ra=!!Number.isSafeInteger,oa=ra?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},Nt=!0;try{var na=fo("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");Nt=((xt=na.exec("a"))===null||xt===void 0?void 0:xt[0])==="a"}catch{Nt=!1}var ar=Ys?function(t,r,o){return t.startsWith(r,o)}:function(t,r,o){return t.slice(o,o+r.length)===r},Lt=Js?String.fromCodePoint:function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var o="",n=t.length,i=0,s;n>i;){if(s=t[i++],s>1114111)throw RangeError(s+" is not a valid code point");o+=s<65536?String.fromCharCode(s):String.fromCharCode(((s-=65536)>>10)+55296,s%1024+56320)}return o},lr=Qs?Object.fromEntries:function(t){for(var r={},o=0,n=t;o<n.length;o++){var i=n[o],s=i[0],a=i[1];r[s]=a}return r},co=Ks?function(t,r){return t.codePointAt(r)}:function(t,r){var o=t.length;if(!(r<0||r>=o)){var n=t.charCodeAt(r),i;return n<55296||n>56319||r+1===o||(i=t.charCodeAt(r+1))<56320||i>57343?n:(n-55296<<10)+(i-56320)+65536}},ia=ea?function(t){return t.trimStart()}:function(t){return t.replace(Ws,"")},sa=ta?function(t){return t.trimEnd()}:function(t){return t.replace($s,"")};function fo(e,t){return new RegExp(e,t)}var Rt;if(Nt){var ur=fo("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");Rt=function(t,r){var o;ur.lastIndex=r;var n=ur.exec(t);return(o=n[1])!==null&&o!==void 0?o:""}}else Rt=function(t,r){for(var o=[];;){var n=co(t,r);if(n===void 0||_o(n)||ca(n))break;o.push(n),r+=n>=65536?2:1}return Lt.apply(void 0,o)};var aa=function(){function e(t,r){r===void 0&&(r={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!r.ignoreTag,this.locale=r.locale,this.requiresOtherClause=!!r.requiresOtherClause,this.shouldParseSkeletons=!!r.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,r,o){for(var n=[];!this.isEOF();){var i=this.char();if(i===123){var s=this.parseArgument(t,o);if(s.err)return s;n.push(s.val)}else{if(i===125&&t>0)break;if(i===35&&(r==="plural"||r==="selectordinal")){var a=this.clonePosition();this.bump(),n.push({type:A.pound,location:T(a,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(o)break;return this.error(x.UNMATCHED_CLOSING_TAG,T(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&Dt(this.peek()||0)){var s=this.parseTag(t,r);if(s.err)return s;n.push(s.val)}else{var s=this.parseLiteral(t,r);if(s.err)return s;n.push(s.val)}}}return{val:n,err:null}},e.prototype.parseTag=function(t,r){var o=this.clonePosition();this.bump();var n=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:A.literal,value:"<".concat(n,"/>"),location:T(o,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(t+1,r,!0);if(i.err)return i;var s=i.val,a=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!Dt(this.char()))return this.error(x.INVALID_TAG,T(a,this.clonePosition()));var c=this.clonePosition(),l=this.parseTagName();return n!==l?this.error(x.UNMATCHED_CLOSING_TAG,T(c,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:A.tag,value:n,children:s,location:T(o,this.clonePosition())},err:null}:this.error(x.INVALID_TAG,T(a,this.clonePosition())))}else return this.error(x.UNCLOSED_TAG,T(o,this.clonePosition()))}else return this.error(x.INVALID_TAG,T(o,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&ua(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,r){for(var o=this.clonePosition(),n="";;){var i=this.tryParseQuote(r);if(i){n+=i;continue}var s=this.tryParseUnquoted(t,r);if(s){n+=s;continue}var a=this.tryParseLeftAngleBracket();if(a){n+=a;continue}break}var c=T(o,this.clonePosition());return{val:{type:A.literal,value:n,location:c},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!la(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var r=[this.char()];for(this.bump();!this.isEOF();){var o=this.char();if(o===39)if(this.peek()===39)r.push(39),this.bump();else{this.bump();break}else r.push(o);this.bump()}return Lt.apply(void 0,r)},e.prototype.tryParseUnquoted=function(t,r){if(this.isEOF())return null;var o=this.char();return o===60||o===123||o===35&&(r==="plural"||r==="selectordinal")||o===125&&t>0?null:(this.bump(),Lt(o))},e.prototype.parseArgument=function(t,r){var o=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(x.EXPECT_ARGUMENT_CLOSING_BRACE,T(o,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(x.EMPTY_ARGUMENT,T(o,this.clonePosition()));var n=this.parseIdentifierIfPossible().value;if(!n)return this.error(x.MALFORMED_ARGUMENT,T(o,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(x.EXPECT_ARGUMENT_CLOSING_BRACE,T(o,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:A.argument,value:n,location:T(o,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(x.EXPECT_ARGUMENT_CLOSING_BRACE,T(o,this.clonePosition())):this.parseArgumentOptions(t,r,n,o);default:return this.error(x.MALFORMED_ARGUMENT,T(o,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),r=this.offset(),o=Rt(this.message,r),n=r+o.length;this.bumpTo(n);var i=this.clonePosition(),s=T(t,i);return{value:o,location:s}},e.prototype.parseArgumentOptions=function(t,r,o,n){var i,s=this.clonePosition(),a=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(a){case"":return this.error(x.EXPECT_ARGUMENT_TYPE,T(s,c));case"number":case"date":case"time":{this.bumpSpace();var l=null;if(this.bumpIf(",")){this.bumpSpace();var u=this.clonePosition(),f=this.parseSimpleArgStyleIfPossible();if(f.err)return f;var _=sa(f.val);if(_.length===0)return this.error(x.EXPECT_ARGUMENT_STYLE,T(this.clonePosition(),this.clonePosition()));var d=T(u,this.clonePosition());l={style:_,styleLocation:d}}var b=this.tryParseArgumentClose(n);if(b.err)return b;var g=T(n,this.clonePosition());if(l&&ar(l?.style,"::",0)){var S=ia(l.style.slice(2));if(a==="number"){var f=this.parseNumberSkeletonFromString(S,l.styleLocation);return f.err?f:{val:{type:A.number,value:o,location:g,style:f.val},err:null}}else{if(S.length===0)return this.error(x.EXPECT_DATE_TIME_SKELETON,g);var P=S;this.locale&&(P=Xs(S,this.locale));var _={type:Ce.dateTime,pattern:P,location:l.styleLocation,parsedOptions:this.shouldParseSkeletons?js(P):{}},m=a==="date"?A.date:A.time;return{val:{type:m,value:o,location:g,style:_},err:null}}}return{val:{type:a==="number"?A.number:a==="date"?A.date:A.time,value:o,location:g,style:(i=l?.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var h=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(x.EXPECT_SELECT_ARGUMENT_OPTIONS,T(h,H({},h)));this.bumpSpace();var y=this.parseIdentifierIfPossible(),C=0;if(a!=="select"&&y.value==="offset"){if(!this.bumpIf(":"))return this.error(x.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,T(this.clonePosition(),this.clonePosition()));this.bumpSpace();var f=this.tryParseDecimalInteger(x.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,x.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(f.err)return f;this.bumpSpace(),y=this.parseIdentifierIfPossible(),C=f.val}var V=this.tryParsePluralOrSelectOptions(t,a,r,y);if(V.err)return V;var b=this.tryParseArgumentClose(n);if(b.err)return b;var W=T(n,this.clonePosition());return a==="select"?{val:{type:A.select,value:o,options:lr(V.val),location:W},err:null}:{val:{type:A.plural,value:o,options:lr(V.val),offset:C,pluralType:a==="plural"?"cardinal":"ordinal",location:W},err:null}}default:return this.error(x.INVALID_ARGUMENT_TYPE,T(s,c))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(x.EXPECT_ARGUMENT_CLOSING_BRACE,T(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,r=this.clonePosition();!this.isEOF();){var o=this.char();switch(o){case 39:{this.bump();var n=this.clonePosition();if(!this.bumpUntil("'"))return this.error(x.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,T(n,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(r.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(r.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,r){var o=[];try{o=Fs(t)}catch{return this.error(x.INVALID_NUMBER_SKELETON,r)}return{val:{type:Ce.number,tokens:o,location:r,parsedOptions:this.shouldParseSkeletons?qs(o):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,r,o,n){for(var i,s=!1,a=[],c=new Set,l=n.value,u=n.location;;){if(l.length===0){var f=this.clonePosition();if(r!=="select"&&this.bumpIf("=")){var _=this.tryParseDecimalInteger(x.EXPECT_PLURAL_ARGUMENT_SELECTOR,x.INVALID_PLURAL_ARGUMENT_SELECTOR);if(_.err)return _;u=T(f,this.clonePosition()),l=this.message.slice(f.offset,this.offset())}else break}if(c.has(l))return this.error(r==="select"?x.DUPLICATE_SELECT_ARGUMENT_SELECTOR:x.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,u);l==="other"&&(s=!0),this.bumpSpace();var d=this.clonePosition();if(!this.bumpIf("{"))return this.error(r==="select"?x.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:x.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,T(this.clonePosition(),this.clonePosition()));var b=this.parseMessage(t+1,r,o);if(b.err)return b;var g=this.tryParseArgumentClose(d);if(g.err)return g;a.push([l,{value:b.val,location:T(d,this.clonePosition())}]),c.add(l),this.bumpSpace(),i=this.parseIdentifierIfPossible(),l=i.value,u=i.location}return a.length===0?this.error(r==="select"?x.EXPECT_SELECT_ARGUMENT_SELECTOR:x.EXPECT_PLURAL_ARGUMENT_SELECTOR,T(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(x.MISSING_OTHER_CLAUSE,T(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(t,r){var o=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(o=-1);for(var i=!1,s=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)i=!0,s=s*10+(a-48),this.bump();else break}var c=T(n,this.clonePosition());return i?(s*=o,oa(s)?{val:s,err:null}:this.error(r,c)):this.error(t,c)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var r=co(this.message,t);if(r===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return r},e.prototype.error=function(t,r){return{val:null,err:{kind:t,message:this.message,location:r}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(ar(this.message,t,this.offset())){for(var r=0;r<t.length;r++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var r=this.offset(),o=this.message.indexOf(t,r);return o>=0?(this.bumpTo(o),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var r=this.offset();if(r===t)break;if(r>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&_o(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),r=this.offset(),o=this.message.charCodeAt(r+(t>=65536?2:1));return o??null},e}();function Dt(e){return e>=97&&e<=122||e>=65&&e<=90}function la(e){return Dt(e)||e===47}function ua(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function _o(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function ca(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function jt(e){e.forEach(function(t){if(delete t.location,ro(t)||oo(t))for(var r in t.options)delete t.options[r].location,jt(t.options[r].value);else Kr(t)&&io(t.style)||(eo(t)||to(t))&&Mt(t.style)?delete t.style.location:no(t)&&jt(t.children)})}function fa(e,t){t===void 0&&(t={}),t=H({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var r=new aa(e,t).parse();if(r.err){var o=SyntaxError(x[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return t?.captureLocation||jt(r.val),r.val}function Tt(e,t){var r=t&&t.cache?t.cache:ga,o=t&&t.serializer?t.serializer:pa,n=t&&t.strategy?t.strategy:ha;return n(e,{cache:r,serializer:o})}function _a(e){return e==null||typeof e=="number"||typeof e=="boolean"}function ho(e,t,r,o){var n=_a(o)?o:r(o),i=t.get(n);return typeof i>"u"&&(i=e.call(this,o),t.set(n,i)),i}function mo(e,t,r){var o=Array.prototype.slice.call(arguments,3),n=r(o),i=t.get(n);return typeof i>"u"&&(i=e.apply(this,o),t.set(n,i)),i}function Zt(e,t,r,o,n){return r.bind(t,e,o,n)}function ha(e,t){var r=e.length===1?ho:mo;return Zt(e,this,r,t.cache.create(),t.serializer)}function da(e,t){return Zt(e,this,mo,t.cache.create(),t.serializer)}function ma(e,t){return Zt(e,this,ho,t.cache.create(),t.serializer)}var pa=function(){return JSON.stringify(arguments)};function Wt(){this.cache=Object.create(null)}Wt.prototype.get=function(e){return this.cache[e]};Wt.prototype.set=function(e,t){this.cache[e]=t};var ga={create:function(){return new Wt}},kt={variadic:da,monadic:ma},Ie;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(Ie||(Ie={}));var gt=function(e){pt(t,e);function t(r,o,n){var i=e.call(this,r)||this;return i.code=o,i.originalMessage=n,i}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),cr=function(e){pt(t,e);function t(r,o,n,i){return e.call(this,'Invalid values for "'.concat(r,'": "').concat(o,'". Options are "').concat(Object.keys(n).join('", "'),'"'),Ie.INVALID_VALUE,i)||this}return t}(gt),ba=function(e){pt(t,e);function t(r,o,n){return e.call(this,'Value for "'.concat(r,'" must be of type ').concat(o),Ie.INVALID_VALUE,n)||this}return t}(gt),va=function(e){pt(t,e);function t(r,o){return e.call(this,'The intl string context variable "'.concat(r,'" was not provided to the string "').concat(o,'"'),Ie.MISSING_VALUE,o)||this}return t}(gt),D;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(D||(D={}));function ya(e){return e.length<2?e:e.reduce(function(t,r){var o=t[t.length-1];return!o||o.type!==D.literal||r.type!==D.literal?t.push(r):o.value+=r.value,t},[])}function wa(e){return typeof e=="function"}function at(e,t,r,o,n,i,s){if(e.length===1&&or(e[0]))return[{type:D.literal,value:e[0].value}];for(var a=[],c=0,l=e;c<l.length;c++){var u=l[c];if(or(u)){a.push({type:D.literal,value:u.value});continue}if(Rs(u)){typeof i=="number"&&a.push({type:D.literal,value:r.getNumberFormat(t).format(i)});continue}var f=u.value;if(!(n&&f in n))throw new va(f,s);var _=n[f];if(Ls(u)){(!_||typeof _=="string"||typeof _=="number")&&(_=typeof _=="string"||typeof _=="number"?String(_):""),a.push({type:typeof _=="string"?D.literal:D.object,value:_});continue}if(eo(u)){var d=typeof u.style=="string"?o.date[u.style]:Mt(u.style)?u.style.parsedOptions:void 0;a.push({type:D.literal,value:r.getDateTimeFormat(t,d).format(_)});continue}if(to(u)){var d=typeof u.style=="string"?o.time[u.style]:Mt(u.style)?u.style.parsedOptions:o.time.medium;a.push({type:D.literal,value:r.getDateTimeFormat(t,d).format(_)});continue}if(Kr(u)){var d=typeof u.style=="string"?o.number[u.style]:io(u.style)?u.style.parsedOptions:void 0;d&&d.scale&&(_=_*(d.scale||1)),a.push({type:D.literal,value:r.getNumberFormat(t,d).format(_)});continue}if(no(u)){var b=u.children,g=u.value,S=n[g];if(!wa(S))throw new ba(g,"function",s);var P=at(b,t,r,o,n,i),m=S(P.map(function(C){return C.value}));Array.isArray(m)||(m=[m]),a.push.apply(a,m.map(function(C){return{type:typeof C=="string"?D.literal:D.object,value:C}}))}if(ro(u)){var h=u.options[_]||u.options.other;if(!h)throw new cr(u.value,_,Object.keys(u.options),s);a.push.apply(a,at(h.value,t,r,o,n));continue}if(oo(u)){var h=u.options["=".concat(_)];if(!h){if(!Intl.PluralRules)throw new gt(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,Ie.MISSING_INTL_API,s);var y=r.getPluralRules(t,{type:u.pluralType}).select(_-(u.offset||0));h=u.options[y]||u.options.other}if(!h)throw new cr(u.value,_,Object.keys(u.options),s);a.push.apply(a,at(h.value,t,r,o,n,_-(u.offset||0)));continue}}return ya(a)}function Ea(e,t){return t?H(H(H({},e||{}),t||{}),Object.keys(e).reduce(function(r,o){return r[o]=H(H({},e[o]),t[o]||{}),r},{})):e}function Sa(e,t){return t?Object.keys(e).reduce(function(r,o){return r[o]=Ea(e[o],t[o]),r},H({},e)):e}function Ht(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}function xa(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:Tt(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.NumberFormat).bind.apply(t,St([void 0],r,!1)))},{cache:Ht(e.number),strategy:kt.variadic}),getDateTimeFormat:Tt(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.DateTimeFormat).bind.apply(t,St([void 0],r,!1)))},{cache:Ht(e.dateTime),strategy:kt.variadic}),getPluralRules:Tt(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.PluralRules).bind.apply(t,St([void 0],r,!1)))},{cache:Ht(e.pluralRules),strategy:kt.variadic})}}var po=function(){function e(t,r,o,n){var i=this;if(r===void 0&&(r=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(c){var l=i.formatToParts(c);if(l.length===1)return l[0].value;var u=l.reduce(function(f,_){return!f.length||_.type!==D.literal||typeof f[f.length-1]!="string"?f.push(_.value):f[f.length-1]+=_.value,f},[]);return u.length<=1?u[0]||"":u},this.formatToParts=function(c){return at(i.ast,i.locales,i.formatters,i.formats,c,void 0,i.message)},this.resolvedOptions=function(){var c;return{locale:((c=i.resolvedLocale)===null||c===void 0?void 0:c.toString())||Intl.NumberFormat.supportedLocalesOf(i.locales)[0]}},this.getAst=function(){return i.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var s=n||{};s.formatters;var a=Ns(s,["formatters"]);this.ast=e.__parse(t,H(H({},a),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=Sa(e.formats,o),this.formatters=n&&n.formatters||xa(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var r=Intl.NumberFormat.supportedLocalesOf(t);return r.length>0?new Intl.Locale(r[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=fa,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();function Ta(e,t){if(t==null)return;if(t in e)return e[t];const r=t.split(".");let o=e;for(let n=0;n<r.length;n++)if(typeof o=="object"){if(n>0){const i=r.slice(n,r.length).join(".");if(i in o){o=o[i];break}}o=o[r[n]]}else o=void 0;return o}const ge={},ka=(e,t,r)=>r&&(t in ge||(ge[t]={}),e in ge[t]||(ge[t][e]=r),r),go=(e,t)=>{if(t==null)return;if(t in ge&&e in ge[t])return ge[t][e];const r=Ye(t);for(let o=0;o<r.length;o++){const n=r[o],i=Pa(n,e);if(i)return ka(e,t,i)}};let $t;const $e=Ne({});function Ha(e){return $t[e]||null}function bo(e){return e in $t}function Pa(e,t){if(!bo(e))return null;const r=Ha(e);return Ta(r,t)}function Oa(e){if(e==null)return;const t=Ye(e);for(let r=0;r<t.length;r++){const o=t[r];if(bo(o))return o}}function vo(e,...t){delete ge[e],$e.update(r=>(r[e]=Ms.all([r[e]||{},...t]),r))}Le([$e],([e])=>Object.keys(e));$e.subscribe(e=>$t=e);const lt={};function Aa(e,t){lt[e].delete(t),lt[e].size===0&&delete lt[e]}function yo(e){return lt[e]}function Ba(e){return Ye(e).map(t=>{const r=yo(t);return[t,r?[...r]:[]]}).filter(([,t])=>t.length>0)}function _t(e){return e==null?!1:Ye(e).some(t=>{var r;return(r=yo(t))==null?void 0:r.size})}function Ca(e,t){return Promise.all(t.map(o=>(Aa(e,o),o().then(n=>n.default||n)))).then(o=>vo(e,...o))}const Ve={};function wo(e){if(!_t(e))return e in Ve?Ve[e]:Promise.resolve();const t=Ba(e);return Ve[e]=Promise.all(t.map(([r,o])=>Ca(r,o))).then(()=>{if(_t(e))return wo(e);delete Ve[e]}),Ve[e]}var fr=Object.getOwnPropertySymbols,Ia=Object.prototype.hasOwnProperty,Ma=Object.prototype.propertyIsEnumerable,Na=(e,t)=>{var r={};for(var o in e)Ia.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&fr)for(var o of fr(e))t.indexOf(o)<0&&Ma.call(e,o)&&(r[o]=e[o]);return r};const La={number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};function Ra({locale:e,id:t}){console.warn(`[svelte-i18n] The message "${t}" was not found in "${Ye(e).join('", "')}".${_t(we())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const Da={fallbackLocale:null,loadingDelay:200,formats:La,warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0},ze=Da;function Me(){return ze}function ja(e){const t=e,{formats:r}=t,o=Na(t,["formats"]);let n=e.fallbackLocale;if(e.initialLocale)try{po.resolveLocale(e.initialLocale)&&(n=e.initialLocale)}catch{console.warn(`[svelte-i18n] The initial locale "${e.initialLocale}" is not a valid locale.`)}return o.warnOnMissingMessages&&(delete o.warnOnMissingMessages,o.handleMissingMessage==null?o.handleMissingMessage=Ra:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign(ze,o,{initialLocale:n}),r&&("number"in r&&Object.assign(ze.formats.number,r.number),"date"in r&&Object.assign(ze.formats.date,r.date),"time"in r&&Object.assign(ze.formats.time,r.time)),Re.set(n)}const Pt=Ne(!1);var Ga=Object.defineProperty,Fa=Object.defineProperties,Ua=Object.getOwnPropertyDescriptors,_r=Object.getOwnPropertySymbols,Va=Object.prototype.hasOwnProperty,za=Object.prototype.propertyIsEnumerable,hr=(e,t,r)=>t in e?Ga(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,qa=(e,t)=>{for(var r in t||(t={}))Va.call(t,r)&&hr(e,r,t[r]);if(_r)for(var r of _r(t))za.call(t,r)&&hr(e,r,t[r]);return e},Xa=(e,t)=>Fa(e,Ua(t));let Gt;const ht=Ne(null);function dr(e){return e.split("-").map((t,r,o)=>o.slice(0,r+1).join("-")).reverse()}function Ye(e,t=Me().fallbackLocale){const r=dr(e);return t?[...new Set([...r,...dr(t)])]:r}function we(){return Gt??void 0}ht.subscribe(e=>{Gt=e??void 0,typeof window<"u"&&e!=null&&document.documentElement.setAttribute("lang",e)});const Za=e=>{if(e&&Oa(e)&&_t(e)){const{loadingDelay:t}=Me();let r;return typeof window<"u"&&we()!=null&&t?r=window.setTimeout(()=>Pt.set(!0),t):Pt.set(!0),wo(e).then(()=>{ht.set(e)}).finally(()=>{clearTimeout(r),Pt.set(!1)})}return ht.set(e)},Re=Xa(qa({},ht),{set:Za}),Wa=()=>typeof window>"u"?null:window.navigator.language||window.navigator.languages[0],bt=e=>{const t=Object.create(null);return o=>{const n=JSON.stringify(o);return n in t?t[n]:t[n]=e(o)}};var $a=Object.defineProperty,dt=Object.getOwnPropertySymbols,Eo=Object.prototype.hasOwnProperty,So=Object.prototype.propertyIsEnumerable,mr=(e,t,r)=>t in e?$a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Yt=(e,t)=>{for(var r in t||(t={}))Eo.call(t,r)&&mr(e,r,t[r]);if(dt)for(var r of dt(t))So.call(t,r)&&mr(e,r,t[r]);return e},De=(e,t)=>{var r={};for(var o in e)Eo.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&dt)for(var o of dt(e))t.indexOf(o)<0&&So.call(e,o)&&(r[o]=e[o]);return r};const We=(e,t)=>{const{formats:r}=Me();if(e in r&&t in r[e])return r[e][t];throw new Error(`[svelte-i18n] Unknown "${t}" ${e} format.`)},Ya=bt(e=>{var t=e,{locale:r,format:o}=t,n=De(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return o&&(n=We("number",o)),new Intl.NumberFormat(r,n)}),Ja=bt(e=>{var t=e,{locale:r,format:o}=t,n=De(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return o?n=We("date",o):Object.keys(n).length===0&&(n=We("date","short")),new Intl.DateTimeFormat(r,n)}),Qa=bt(e=>{var t=e,{locale:r,format:o}=t,n=De(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return o?n=We("time",o):Object.keys(n).length===0&&(n=We("time","short")),new Intl.DateTimeFormat(r,n)}),Ka=(e={})=>{var t=e,{locale:r=we()}=t,o=De(t,["locale"]);return Ya(Yt({locale:r},o))},el=(e={})=>{var t=e,{locale:r=we()}=t,o=De(t,["locale"]);return Ja(Yt({locale:r},o))},tl=(e={})=>{var t=e,{locale:r=we()}=t,o=De(t,["locale"]);return Qa(Yt({locale:r},o))},rl=bt((e,t=we())=>new po(e,t,Me().formats,{ignoreTag:Me().ignoreTag})),ol=(e,t={})=>{var r,o,n,i;let s=t;typeof e=="object"&&(s=e,e=s.id);const{values:a,locale:c=we(),default:l}=s;if(c==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let u=go(e,c);if(!u)u=(i=(n=(o=(r=Me()).handleMissingMessage)==null?void 0:o.call(r,{locale:c,id:e,defaultValue:l}))!=null?n:l)!=null?i:e;else if(typeof u!="string")return console.warn(`[svelte-i18n] Message with id "${e}" must be of type "string", found: "${typeof u}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),u;if(!a)return u;let f=u;try{f=rl(u,c).format(a)}catch(_){_ instanceof Error&&console.warn(`[svelte-i18n] Message "${e}" has syntax error:`,_.message)}return f},nl=(e,t)=>tl(t).format(e),il=(e,t)=>el(t).format(e),sl=(e,t)=>Ka(t).format(e),al=(e,t=we())=>go(e,t),xo=Le([Re,$e],()=>ol);Le([Re],()=>nl);Le([Re],()=>il);Le([Re],()=>sl);Le([Re,$e],()=>al);const{SvelteComponent:ll,append:L,attr:R,binding_callbacks:ul,component_subscribe:cl,create_slot:fl,detach:To,element:_e,flush:pe,get_all_dirty_from_scope:_l,get_slot_changes:hl,init:dl,insert:ko,safe_not_equal:ml,set_data:Ot,set_style:rt,space:qe,text:ot,toggle_class:He,transition_in:pl,transition_out:gl,update_slot_base:bl}=window.__gradio__svelte__internal;function pr(e){let t,r,o,n,i,s,a,c=e[8]("common.built_with")+"",l,u,f,_,d,b,g=e[8]("common.hosted_on")+"",S,P,m;return{c(){t=_e("div"),r=_e("span"),o=_e("a"),n=ot(e[4]),s=qe(),a=_e("span"),l=ot(c),u=qe(),f=_e("a"),f.textContent="Gradio",_=ot("."),d=qe(),b=_e("span"),S=ot(g),P=qe(),m=_e("a"),m.innerHTML=`<span class="space-logo svelte-182fdeq"><img src="${bs}" alt="Hugging Face Space" class="svelte-182fdeq"/></span> Spaces`,R(o,"href",i="https://huggingface.co/spaces/"+e[4]),R(o,"class","title svelte-182fdeq"),R(r,"class","svelte-182fdeq"),R(f,"class","gradio svelte-182fdeq"),R(f,"href","https://gradio.app"),R(a,"class","svelte-182fdeq"),R(m,"class","hf svelte-182fdeq"),R(m,"href","https://huggingface.co/spaces"),R(b,"class","svelte-182fdeq"),R(t,"class","info svelte-182fdeq")},m(h,y){ko(h,t,y),L(t,r),L(r,o),L(o,n),L(t,s),L(t,a),L(a,l),L(a,u),L(a,f),L(a,_),L(t,d),L(t,b),L(b,S),L(b,P),L(b,m)},p(h,y){y&16&&Ot(n,h[4]),y&16&&i!==(i="https://huggingface.co/spaces/"+h[4])&&R(o,"href",i),y&256&&c!==(c=h[8]("common.built_with")+"")&&Ot(l,c),y&256&&g!==(g=h[8]("common.hosted_on")+"")&&Ot(S,g)},d(h){h&&To(t)}}}function vl(e){let t,r,o,n,i;const s=e[10].default,a=fl(s,e,e[9],null);let c=e[5]&&e[4]&&e[6]&&pr(e);return{c(){t=_e("div"),r=_e("div"),a&&a.c(),o=qe(),c&&c.c(),R(r,"class","main svelte-182fdeq"),R(t,"class",n="gradio-container gradio-container-"+e[1]+" svelte-182fdeq"),R(t,"data-iframe-height",""),He(t,"app",!e[5]&&!e[3]),He(t,"embed-container",e[5]),He(t,"with-info",e[6]),rt(t,"min-height",e[7]?"initial":e[2]),rt(t,"flex-grow",e[5]?"auto":"1")},m(l,u){ko(l,t,u),L(t,r),a&&a.m(r,null),L(t,o),c&&c.m(t,null),e[11](t),i=!0},p(l,[u]){a&&a.p&&(!i||u&512)&&bl(a,s,l,l[9],i?hl(s,l[9],u,null):_l(l[9]),null),l[5]&&l[4]&&l[6]?c?c.p(l,u):(c=pr(l),c.c(),c.m(t,null)):c&&(c.d(1),c=null),(!i||u&2&&n!==(n="gradio-container gradio-container-"+l[1]+" svelte-182fdeq"))&&R(t,"class",n),(!i||u&42)&&He(t,"app",!l[5]&&!l[3]),(!i||u&34)&&He(t,"embed-container",l[5]),(!i||u&66)&&He(t,"with-info",l[6]),u&132&&rt(t,"min-height",l[7]?"initial":l[2]),u&32&&rt(t,"flex-grow",l[5]?"auto":"1")},i(l){i||(pl(a,l),i=!0)},o(l){gl(a,l),i=!1},d(l){l&&To(t),a&&a.d(l),c&&c.d(),e[11](null)}}}function yl(e,t,r){let o;cl(e,xo,g=>r(8,o=g));let{$$slots:n={},$$scope:i}=t,{wrapper:s}=t,{version:a}=t,{initial_height:c}=t,{is_embed:l}=t,{space:u}=t,{display:f}=t,{info:_}=t,{loaded:d}=t;function b(g){ul[g?"unshift":"push"](()=>{s=g,r(0,s)})}return e.$$set=g=>{"wrapper"in g&&r(0,s=g.wrapper),"version"in g&&r(1,a=g.version),"initial_height"in g&&r(2,c=g.initial_height),"is_embed"in g&&r(3,l=g.is_embed),"space"in g&&r(4,u=g.space),"display"in g&&r(5,f=g.display),"info"in g&&r(6,_=g.info),"loaded"in g&&r(7,d=g.loaded),"$$scope"in g&&r(9,i=g.$$scope)},[s,a,c,l,u,f,_,d,o,i,n,b]}class wl extends ll{constructor(t){super(),dl(this,t,yl,vl,ml,{wrapper:0,version:1,initial_height:2,is_embed:3,space:4,display:5,info:6,loaded:7})}get wrapper(){return this.$$.ctx[0]}set wrapper(t){this.$$set({wrapper:t}),pe()}get version(){return this.$$.ctx[1]}set version(t){this.$$set({version:t}),pe()}get initial_height(){return this.$$.ctx[2]}set initial_height(t){this.$$set({initial_height:t}),pe()}get is_embed(){return this.$$.ctx[3]}set is_embed(t){this.$$set({is_embed:t}),pe()}get space(){return this.$$.ctx[4]}set space(t){this.$$set({space:t}),pe()}get display(){return this.$$.ctx[5]}set display(t){this.$$set({display:t}),pe()}get info(){return this.$$.ctx[6]}set info(t){this.$$set({info:t}),pe()}get loaded(){return this.$$.ctx[7]}set loaded(t){this.$$set({loaded:t}),pe()}}function Oe(e){let t=["","k","M","G","T","P","E","Z"],r=0;for(;e>1e3&&r<t.length-1;)e/=1e3,r++;let o=t[r];return(Number.isInteger(e)?e:e.toFixed(1))+o}function gr(e){return Object.prototype.toString.call(e)==="[object Date]"}function Ft(e,t,r,o){if(typeof r=="number"||gr(r)){const n=o-r,i=(r-t)/(e.dt||1/60),s=e.opts.stiffness*n,a=e.opts.damping*i,c=(s-a)*e.inv_mass,l=(i+c)*e.dt;return Math.abs(l)<e.opts.precision&&Math.abs(n)<e.opts.precision?o:(e.settled=!1,gr(r)?new Date(r.getTime()+l):r+l)}else{if(Array.isArray(r))return r.map((n,i)=>Ft(e,t[i],r[i],o[i]));if(typeof r=="object"){const n={};for(const i in r)n[i]=Ft(e,t[i],r[i],o[i]);return n}else throw new Error(`Cannot spring ${typeof r} values`)}}function br(e,t={}){const r=Ne(e),{stiffness:o=.15,damping:n=.8,precision:i=.01}=t;let s,a,c,l=e,u=e,f=1,_=0,d=!1;function b(S,P={}){u=S;const m=c={};return e==null||P.hard||g.stiffness>=1&&g.damping>=1?(d=!0,s=tr(),l=S,r.set(e=u),Promise.resolve()):(P.soft&&(_=1/((P.soft===!0?.5:+P.soft)*60),f=0),a||(s=tr(),d=!1,a=ps(h=>{if(d)return d=!1,a=null,!1;f=Math.min(f+_,1);const y={inv_mass:f,opts:g,settled:!0,dt:(h-s)*60/1e3},C=Ft(y,l,e,u);return s=h,l=e,r.set(e=C),y.settled&&(a=null),!y.settled})),new Promise(h=>{a.promise.then(()=>{m===c&&h()})}))}const g={set:b,update:(S,P)=>b(S(u,e),P),subscribe:r.subscribe,stiffness:o,damping:n,precision:i};return g}const{SvelteComponent:El,append:K,attr:k,component_subscribe:vr,detach:Sl,element:xl,flush:Tl,init:kl,insert:Hl,noop:yr,safe_not_equal:Pl,set_style:nt,svg_element:ee,toggle_class:wr}=window.__gradio__svelte__internal,{onMount:Ol}=window.__gradio__svelte__internal;function Al(e){let t,r,o,n,i,s,a,c,l,u,f,_;return{c(){t=xl("div"),r=ee("svg"),o=ee("g"),n=ee("path"),i=ee("path"),s=ee("path"),a=ee("path"),c=ee("g"),l=ee("path"),u=ee("path"),f=ee("path"),_=ee("path"),k(n,"d","M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z"),k(n,"fill","#FF7C00"),k(n,"fill-opacity","0.4"),k(n,"class","svelte-zyxd38"),k(i,"d","M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z"),k(i,"fill","#FF7C00"),k(i,"class","svelte-zyxd38"),k(s,"d","M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z"),k(s,"fill","#FF7C00"),k(s,"fill-opacity","0.4"),k(s,"class","svelte-zyxd38"),k(a,"d","M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z"),k(a,"fill","#FF7C00"),k(a,"class","svelte-zyxd38"),nt(o,"transform","translate("+e[1][0]+"px, "+e[1][1]+"px)"),k(l,"d","M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z"),k(l,"fill","#FF7C00"),k(l,"fill-opacity","0.4"),k(l,"class","svelte-zyxd38"),k(u,"d","M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z"),k(u,"fill","#FF7C00"),k(u,"class","svelte-zyxd38"),k(f,"d","M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z"),k(f,"fill","#FF7C00"),k(f,"fill-opacity","0.4"),k(f,"class","svelte-zyxd38"),k(_,"d","M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z"),k(_,"fill","#FF7C00"),k(_,"class","svelte-zyxd38"),nt(c,"transform","translate("+e[2][0]+"px, "+e[2][1]+"px)"),k(r,"viewBox","-1200 -1200 3000 3000"),k(r,"fill","none"),k(r,"xmlns","http://www.w3.org/2000/svg"),k(r,"class","svelte-zyxd38"),k(t,"class","svelte-zyxd38"),wr(t,"margin",e[0])},m(d,b){Hl(d,t,b),K(t,r),K(r,o),K(o,n),K(o,i),K(o,s),K(o,a),K(r,c),K(c,l),K(c,u),K(c,f),K(c,_)},p(d,[b]){b&2&&nt(o,"transform","translate("+d[1][0]+"px, "+d[1][1]+"px)"),b&4&&nt(c,"transform","translate("+d[2][0]+"px, "+d[2][1]+"px)"),b&1&&wr(t,"margin",d[0])},i:yr,o:yr,d(d){d&&Sl(t)}}}function Bl(e,t,r){let o,n,{margin:i=!0}=t;const s=br([0,0]);vr(e,s,_=>r(1,o=_));const a=br([0,0]);vr(e,a,_=>r(2,n=_));let c;async function l(){await Promise.all([s.set([125,140]),a.set([-125,-140])]),await Promise.all([s.set([-125,140]),a.set([125,-140])]),await Promise.all([s.set([-125,0]),a.set([125,-0])]),await Promise.all([s.set([125,0]),a.set([-125,0])])}async function u(){await l(),c||u()}async function f(){await Promise.all([s.set([125,0]),a.set([-125,0])]),u()}return Ol(()=>(f(),()=>c=!0)),e.$$set=_=>{"margin"in _&&r(0,i=_.margin)},[i,o,n,s,a]}class Cl extends El{constructor(t){super(),kl(this,t,Bl,Al,Pl,{margin:0})}get margin(){return this.$$.ctx[0]}set margin(t){this.$$set({margin:t}),Tl()}}const{SvelteComponent:Il,append:Ut,attr:fe,bubble:Ml,create_component:Nl,destroy_component:Ll,detach:Ho,element:Vt,flush:U,init:Rl,insert:Po,listen:Dl,mount_component:jl,safe_not_equal:Gl,set_data:Fl,set_style:Pe,space:Ul,text:Vl,toggle_class:G,transition_in:zl,transition_out:ql}=window.__gradio__svelte__internal;function Er(e){let t,r;return{c(){t=Vt("span"),r=Vl(e[1]),fe(t,"class","svelte-rk35yg")},m(o,n){Po(o,t,n),Ut(t,r)},p(o,n){n&2&&Fl(r,o[1])},d(o){o&&Ho(t)}}}function Xl(e){let t,r,o,n,i,s,a,c=e[2]&&Er(e);return n=new e[0]({}),{c(){t=Vt("button"),c&&c.c(),r=Ul(),o=Vt("div"),Nl(n.$$.fragment),fe(o,"class","svelte-rk35yg"),G(o,"small",e[4]==="small"),G(o,"large",e[4]==="large"),G(o,"medium",e[4]==="medium"),t.disabled=e[7],fe(t,"aria-label",e[1]),fe(t,"aria-haspopup",e[8]),fe(t,"title",e[1]),fe(t,"class","svelte-rk35yg"),G(t,"pending",e[3]),G(t,"padded",e[5]),G(t,"highlight",e[6]),G(t,"transparent",e[9]),Pe(t,"color",!e[7]&&e[12]?e[12]:"var(--block-label-text-color)"),Pe(t,"--bg-color",e[7]?"auto":e[10]),Pe(t,"margin-left",e[11]+"px")},m(l,u){Po(l,t,u),c&&c.m(t,null),Ut(t,r),Ut(t,o),jl(n,o,null),i=!0,s||(a=Dl(t,"click",e[14]),s=!0)},p(l,[u]){l[2]?c?c.p(l,u):(c=Er(l),c.c(),c.m(t,r)):c&&(c.d(1),c=null),(!i||u&16)&&G(o,"small",l[4]==="small"),(!i||u&16)&&G(o,"large",l[4]==="large"),(!i||u&16)&&G(o,"medium",l[4]==="medium"),(!i||u&128)&&(t.disabled=l[7]),(!i||u&2)&&fe(t,"aria-label",l[1]),(!i||u&256)&&fe(t,"aria-haspopup",l[8]),(!i||u&2)&&fe(t,"title",l[1]),(!i||u&8)&&G(t,"pending",l[3]),(!i||u&32)&&G(t,"padded",l[5]),(!i||u&64)&&G(t,"highlight",l[6]),(!i||u&512)&&G(t,"transparent",l[9]),u&4224&&Pe(t,"color",!l[7]&&l[12]?l[12]:"var(--block-label-text-color)"),u&1152&&Pe(t,"--bg-color",l[7]?"auto":l[10]),u&2048&&Pe(t,"margin-left",l[11]+"px")},i(l){i||(zl(n.$$.fragment,l),i=!0)},o(l){ql(n.$$.fragment,l),i=!1},d(l){l&&Ho(t),c&&c.d(),Ll(n),s=!1,a()}}}function Zl(e,t,r){let o,{Icon:n}=t,{label:i=""}=t,{show_label:s=!1}=t,{pending:a=!1}=t,{size:c="small"}=t,{padded:l=!0}=t,{highlight:u=!1}=t,{disabled:f=!1}=t,{hasPopup:_=!1}=t,{color:d="var(--block-label-text-color)"}=t,{transparent:b=!1}=t,{background:g="var(--background-fill-primary)"}=t,{offset:S=0}=t;function P(m){Ml.call(this,e,m)}return e.$$set=m=>{"Icon"in m&&r(0,n=m.Icon),"label"in m&&r(1,i=m.label),"show_label"in m&&r(2,s=m.show_label),"pending"in m&&r(3,a=m.pending),"size"in m&&r(4,c=m.size),"padded"in m&&r(5,l=m.padded),"highlight"in m&&r(6,u=m.highlight),"disabled"in m&&r(7,f=m.disabled),"hasPopup"in m&&r(8,_=m.hasPopup),"color"in m&&r(13,d=m.color),"transparent"in m&&r(9,b=m.transparent),"background"in m&&r(10,g=m.background),"offset"in m&&r(11,S=m.offset)},e.$$.update=()=>{e.$$.dirty&8256&&r(12,o=u?"var(--color-accent)":d)},[n,i,s,a,c,l,u,f,_,b,g,S,o,d,P]}class Wl extends Il{constructor(t){super(),Rl(this,t,Zl,Xl,Gl,{Icon:0,label:1,show_label:2,pending:3,size:4,padded:5,highlight:6,disabled:7,hasPopup:8,color:13,transparent:9,background:10,offset:11})}get Icon(){return this.$$.ctx[0]}set Icon(t){this.$$set({Icon:t}),U()}get label(){return this.$$.ctx[1]}set label(t){this.$$set({label:t}),U()}get show_label(){return this.$$.ctx[2]}set show_label(t){this.$$set({show_label:t}),U()}get pending(){return this.$$.ctx[3]}set pending(t){this.$$set({pending:t}),U()}get size(){return this.$$.ctx[4]}set size(t){this.$$set({size:t}),U()}get padded(){return this.$$.ctx[5]}set padded(t){this.$$set({padded:t}),U()}get highlight(){return this.$$.ctx[6]}set highlight(t){this.$$set({highlight:t}),U()}get disabled(){return this.$$.ctx[7]}set disabled(t){this.$$set({disabled:t}),U()}get hasPopup(){return this.$$.ctx[8]}set hasPopup(t){this.$$set({hasPopup:t}),U()}get color(){return this.$$.ctx[13]}set color(t){this.$$set({color:t}),U()}get transparent(){return this.$$.ctx[9]}set transparent(t){this.$$set({transparent:t}),U()}get background(){return this.$$.ctx[10]}set background(t){this.$$set({background:t}),U()}get offset(){return this.$$.ctx[11]}set offset(t){this.$$set({offset:t}),U()}}const{SvelteComponent:$l,append:At,attr:te,detach:Yl,init:Jl,insert:Ql,noop:Bt,safe_not_equal:Kl,set_style:ae,svg_element:it}=window.__gradio__svelte__internal;function eu(e){let t,r,o,n;return{c(){t=it("svg"),r=it("g"),o=it("path"),n=it("path"),te(o,"d","M18,6L6.087,17.913"),ae(o,"fill","none"),ae(o,"fill-rule","nonzero"),ae(o,"stroke-width","2px"),te(r,"transform","matrix(1.14096,-0.140958,-0.140958,1.14096,-0.0559523,0.0559523)"),te(n,"d","M4.364,4.364L19.636,19.636"),ae(n,"fill","none"),ae(n,"fill-rule","nonzero"),ae(n,"stroke-width","2px"),te(t,"width","100%"),te(t,"height","100%"),te(t,"viewBox","0 0 24 24"),te(t,"version","1.1"),te(t,"xmlns","http://www.w3.org/2000/svg"),te(t,"xmlns:xlink","http://www.w3.org/1999/xlink"),te(t,"xml:space","preserve"),te(t,"stroke","currentColor"),ae(t,"fill-rule","evenodd"),ae(t,"clip-rule","evenodd"),ae(t,"stroke-linecap","round"),ae(t,"stroke-linejoin","round")},m(i,s){Ql(i,t,s),At(t,r),At(r,o),At(t,n)},p:Bt,i:Bt,o:Bt,d(i){i&&Yl(t)}}}class tu extends $l{constructor(t){super(),Jl(this,t,null,eu,Kl,{})}}const tf=["red","green","blue","yellow","purple","teal","orange","cyan","lime","pink"],ru=[{color:"red",primary:600,secondary:100},{color:"green",primary:600,secondary:100},{color:"blue",primary:600,secondary:100},{color:"yellow",primary:500,secondary:100},{color:"purple",primary:600,secondary:100},{color:"teal",primary:600,secondary:100},{color:"orange",primary:600,secondary:100},{color:"cyan",primary:600,secondary:100},{color:"lime",primary:500,secondary:100},{color:"pink",primary:600,secondary:100}],Sr={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"}},rf=ru.reduce((e,{color:t,primary:r,secondary:o})=>({...e,[t]:{primary:Sr[t][r],secondary:Sr[t][o]}}),{}),{SvelteComponent:ou,append:Ee,attr:oe,binding_callbacks:xr,check_outros:zt,create_component:Oo,create_slot:Ao,destroy_component:Bo,destroy_each:Co,detach:w,element:le,empty:je,ensure_array_like:mt,flush:M,get_all_dirty_from_scope:Io,get_slot_changes:Mo,group_outros:qt,init:nu,insert:E,mount_component:No,noop:Xt,safe_not_equal:iu,set_data:Z,set_style:be,space:X,text:B,toggle_class:q,transition_in:re,transition_out:ue,update_slot_base:Lo}=window.__gradio__svelte__internal,{tick:su}=window.__gradio__svelte__internal,{onDestroy:au}=window.__gradio__svelte__internal,{createEventDispatcher:lu}=window.__gradio__svelte__internal,uu=e=>({}),Tr=e=>({}),cu=e=>({}),kr=e=>({});function Hr(e,t,r){const o=e.slice();return o[40]=t[r],o[42]=r,o}function Pr(e,t,r){const o=e.slice();return o[40]=t[r],o}function fu(e){let t,r,o,n,i=e[1]("common.error")+"",s,a,c;r=new Wl({props:{Icon:tu,label:e[1]("common.clear"),disabled:!1}}),r.$on("click",e[32]);const l=e[30].error,u=Ao(l,e,e[29],Tr);return{c(){t=le("div"),Oo(r.$$.fragment),o=X(),n=le("span"),s=B(i),a=X(),u&&u.c(),oe(t,"class","clear-status svelte-z7cif2"),oe(n,"class","error svelte-z7cif2")},m(f,_){E(f,t,_),No(r,t,null),E(f,o,_),E(f,n,_),Ee(n,s),E(f,a,_),u&&u.m(f,_),c=!0},p(f,_){const d={};_[0]&2&&(d.label=f[1]("common.clear")),r.$set(d),(!c||_[0]&2)&&i!==(i=f[1]("common.error")+"")&&Z(s,i),u&&u.p&&(!c||_[0]&536870912)&&Lo(u,l,f,f[29],c?Mo(l,f[29],_,uu):Io(f[29]),Tr)},i(f){c||(re(r.$$.fragment,f),re(u,f),c=!0)},o(f){ue(r.$$.fragment,f),ue(u,f),c=!1},d(f){f&&(w(t),w(o),w(n),w(a)),Bo(r),u&&u.d(f)}}}function _u(e){let t,r,o,n,i,s,a,c,l,u=e[8]==="default"&&e[18]&&e[6]==="full"&&Or(e);function f(h,y){if(h[7])return mu;if(h[2]!==null&&h[3]!==void 0&&h[2]>=0)return du;if(h[2]===0)return hu}let _=f(e),d=_&&_(e),b=e[5]&&Cr(e);const g=[vu,bu],S=[];function P(h,y){return h[15]!=null?0:h[6]==="full"?1:-1}~(i=P(e))&&(s=S[i]=g[i](e));let m=!e[5]&&jr(e);return{c(){u&&u.c(),t=X(),r=le("div"),d&&d.c(),o=X(),b&&b.c(),n=X(),s&&s.c(),a=X(),m&&m.c(),c=je(),oe(r,"class","progress-text svelte-z7cif2"),q(r,"meta-text-center",e[8]==="center"),q(r,"meta-text",e[8]==="default")},m(h,y){u&&u.m(h,y),E(h,t,y),E(h,r,y),d&&d.m(r,null),Ee(r,o),b&&b.m(r,null),E(h,n,y),~i&&S[i].m(h,y),E(h,a,y),m&&m.m(h,y),E(h,c,y),l=!0},p(h,y){h[8]==="default"&&h[18]&&h[6]==="full"?u?u.p(h,y):(u=Or(h),u.c(),u.m(t.parentNode,t)):u&&(u.d(1),u=null),_===(_=f(h))&&d?d.p(h,y):(d&&d.d(1),d=_&&_(h),d&&(d.c(),d.m(r,o))),h[5]?b?b.p(h,y):(b=Cr(h),b.c(),b.m(r,null)):b&&(b.d(1),b=null),(!l||y[0]&256)&&q(r,"meta-text-center",h[8]==="center"),(!l||y[0]&256)&&q(r,"meta-text",h[8]==="default");let C=i;i=P(h),i===C?~i&&S[i].p(h,y):(s&&(qt(),ue(S[C],1,1,()=>{S[C]=null}),zt()),~i?(s=S[i],s?s.p(h,y):(s=S[i]=g[i](h),s.c()),re(s,1),s.m(a.parentNode,a)):s=null),h[5]?m&&(qt(),ue(m,1,1,()=>{m=null}),zt()):m?(m.p(h,y),y[0]&32&&re(m,1)):(m=jr(h),m.c(),re(m,1),m.m(c.parentNode,c))},i(h){l||(re(s),re(m),l=!0)},o(h){ue(s),ue(m),l=!1},d(h){h&&(w(t),w(r),w(n),w(a),w(c)),u&&u.d(h),d&&d.d(),b&&b.d(),~i&&S[i].d(h),m&&m.d(h)}}}function Or(e){let t,r=`translateX(${(e[17]||0)*100-100}%)`;return{c(){t=le("div"),oe(t,"class","eta-bar svelte-z7cif2"),be(t,"transform",r)},m(o,n){E(o,t,n)},p(o,n){n[0]&131072&&r!==(r=`translateX(${(o[17]||0)*100-100}%)`)&&be(t,"transform",r)},d(o){o&&w(t)}}}function hu(e){let t;return{c(){t=B("processing |")},m(r,o){E(r,t,o)},p:Xt,d(r){r&&w(t)}}}function du(e){let t,r=e[2]+1+"",o,n,i,s;return{c(){t=B("queue: "),o=B(r),n=B("/"),i=B(e[3]),s=B(" |")},m(a,c){E(a,t,c),E(a,o,c),E(a,n,c),E(a,i,c),E(a,s,c)},p(a,c){c[0]&4&&r!==(r=a[2]+1+"")&&Z(o,r),c[0]&8&&Z(i,a[3])},d(a){a&&(w(t),w(o),w(n),w(i),w(s))}}}function mu(e){let t,r=mt(e[7]),o=[];for(let n=0;n<r.length;n+=1)o[n]=Br(Pr(e,r,n));return{c(){for(let n=0;n<o.length;n+=1)o[n].c();t=je()},m(n,i){for(let s=0;s<o.length;s+=1)o[s]&&o[s].m(n,i);E(n,t,i)},p(n,i){if(i[0]&128){r=mt(n[7]);let s;for(s=0;s<r.length;s+=1){const a=Pr(n,r,s);o[s]?o[s].p(a,i):(o[s]=Br(a),o[s].c(),o[s].m(t.parentNode,t))}for(;s<o.length;s+=1)o[s].d(1);o.length=r.length}},d(n){n&&w(t),Co(o,n)}}}function Ar(e){let t,r=e[40].unit+"",o,n,i=" ",s;function a(u,f){return u[40].length!=null?gu:pu}let c=a(e),l=c(e);return{c(){l.c(),t=X(),o=B(r),n=B(" | "),s=B(i)},m(u,f){l.m(u,f),E(u,t,f),E(u,o,f),E(u,n,f),E(u,s,f)},p(u,f){c===(c=a(u))&&l?l.p(u,f):(l.d(1),l=c(u),l&&(l.c(),l.m(t.parentNode,t))),f[0]&128&&r!==(r=u[40].unit+"")&&Z(o,r)},d(u){u&&(w(t),w(o),w(n),w(s)),l.d(u)}}}function pu(e){let t=Oe(e[40].index||0)+"",r;return{c(){r=B(t)},m(o,n){E(o,r,n)},p(o,n){n[0]&128&&t!==(t=Oe(o[40].index||0)+"")&&Z(r,t)},d(o){o&&w(r)}}}function gu(e){let t=Oe(e[40].index||0)+"",r,o,n=Oe(e[40].length)+"",i;return{c(){r=B(t),o=B("/"),i=B(n)},m(s,a){E(s,r,a),E(s,o,a),E(s,i,a)},p(s,a){a[0]&128&&t!==(t=Oe(s[40].index||0)+"")&&Z(r,t),a[0]&128&&n!==(n=Oe(s[40].length)+"")&&Z(i,n)},d(s){s&&(w(r),w(o),w(i))}}}function Br(e){let t,r=e[40].index!=null&&Ar(e);return{c(){r&&r.c(),t=je()},m(o,n){r&&r.m(o,n),E(o,t,n)},p(o,n){o[40].index!=null?r?r.p(o,n):(r=Ar(o),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},d(o){o&&w(t),r&&r.d(o)}}}function Cr(e){let t,r=e[0]?`/${e[19]}`:"",o,n;return{c(){t=B(e[20]),o=B(r),n=B("s")},m(i,s){E(i,t,s),E(i,o,s),E(i,n,s)},p(i,s){s[0]&1048576&&Z(t,i[20]),s[0]&524289&&r!==(r=i[0]?`/${i[19]}`:"")&&Z(o,r)},d(i){i&&(w(t),w(o),w(n))}}}function bu(e){let t,r;return t=new Cl({props:{margin:e[8]==="default"}}),{c(){Oo(t.$$.fragment)},m(o,n){No(t,o,n),r=!0},p(o,n){const i={};n[0]&256&&(i.margin=o[8]==="default"),t.$set(i)},i(o){r||(re(t.$$.fragment,o),r=!0)},o(o){ue(t.$$.fragment,o),r=!1},d(o){Bo(t,o)}}}function vu(e){let t,r,o,n,i,s=`${e[15]*100}%`,a=e[7]!=null&&Ir(e);return{c(){t=le("div"),r=le("div"),a&&a.c(),o=X(),n=le("div"),i=le("div"),oe(r,"class","progress-level-inner svelte-z7cif2"),oe(i,"class","progress-bar svelte-z7cif2"),be(i,"width",s),oe(n,"class","progress-bar-wrap svelte-z7cif2"),oe(t,"class","progress-level svelte-z7cif2")},m(c,l){E(c,t,l),Ee(t,r),a&&a.m(r,null),Ee(t,o),Ee(t,n),Ee(n,i),e[31](i)},p(c,l){c[7]!=null?a?a.p(c,l):(a=Ir(c),a.c(),a.m(r,null)):a&&(a.d(1),a=null),l[0]&32768&&s!==(s=`${c[15]*100}%`)&&be(i,"width",s)},i:Xt,o:Xt,d(c){c&&w(t),a&&a.d(),e[31](null)}}}function Ir(e){let t,r=mt(e[7]),o=[];for(let n=0;n<r.length;n+=1)o[n]=Dr(Hr(e,r,n));return{c(){for(let n=0;n<o.length;n+=1)o[n].c();t=je()},m(n,i){for(let s=0;s<o.length;s+=1)o[s]&&o[s].m(n,i);E(n,t,i)},p(n,i){if(i[0]&16512){r=mt(n[7]);let s;for(s=0;s<r.length;s+=1){const a=Hr(n,r,s);o[s]?o[s].p(a,i):(o[s]=Dr(a),o[s].c(),o[s].m(t.parentNode,t))}for(;s<o.length;s+=1)o[s].d(1);o.length=r.length}},d(n){n&&w(t),Co(o,n)}}}function Mr(e){let t,r,o,n,i=e[42]!==0&&yu(),s=e[40].desc!=null&&Nr(e),a=e[40].desc!=null&&e[14]&&e[14][e[42]]!=null&&Lr(),c=e[14]!=null&&Rr(e);return{c(){i&&i.c(),t=X(),s&&s.c(),r=X(),a&&a.c(),o=X(),c&&c.c(),n=je()},m(l,u){i&&i.m(l,u),E(l,t,u),s&&s.m(l,u),E(l,r,u),a&&a.m(l,u),E(l,o,u),c&&c.m(l,u),E(l,n,u)},p(l,u){l[40].desc!=null?s?s.p(l,u):(s=Nr(l),s.c(),s.m(r.parentNode,r)):s&&(s.d(1),s=null),l[40].desc!=null&&l[14]&&l[14][l[42]]!=null?a||(a=Lr(),a.c(),a.m(o.parentNode,o)):a&&(a.d(1),a=null),l[14]!=null?c?c.p(l,u):(c=Rr(l),c.c(),c.m(n.parentNode,n)):c&&(c.d(1),c=null)},d(l){l&&(w(t),w(r),w(o),w(n)),i&&i.d(l),s&&s.d(l),a&&a.d(l),c&&c.d(l)}}}function yu(e){let t;return{c(){t=B(" /")},m(r,o){E(r,t,o)},d(r){r&&w(t)}}}function Nr(e){let t=e[40].desc+"",r;return{c(){r=B(t)},m(o,n){E(o,r,n)},p(o,n){n[0]&128&&t!==(t=o[40].desc+"")&&Z(r,t)},d(o){o&&w(r)}}}function Lr(e){let t;return{c(){t=B("-")},m(r,o){E(r,t,o)},d(r){r&&w(t)}}}function Rr(e){let t=(100*(e[14][e[42]]||0)).toFixed(1)+"",r,o;return{c(){r=B(t),o=B("%")},m(n,i){E(n,r,i),E(n,o,i)},p(n,i){i[0]&16384&&t!==(t=(100*(n[14][n[42]]||0)).toFixed(1)+"")&&Z(r,t)},d(n){n&&(w(r),w(o))}}}function Dr(e){let t,r=(e[40].desc!=null||e[14]&&e[14][e[42]]!=null)&&Mr(e);return{c(){r&&r.c(),t=je()},m(o,n){r&&r.m(o,n),E(o,t,n)},p(o,n){o[40].desc!=null||o[14]&&o[14][o[42]]!=null?r?r.p(o,n):(r=Mr(o),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},d(o){o&&w(t),r&&r.d(o)}}}function jr(e){let t,r,o,n;const i=e[30]["additional-loading-text"],s=Ao(i,e,e[29],kr);return{c(){t=le("p"),r=B(e[9]),o=X(),s&&s.c(),oe(t,"class","loading svelte-z7cif2")},m(a,c){E(a,t,c),Ee(t,r),E(a,o,c),s&&s.m(a,c),n=!0},p(a,c){(!n||c[0]&512)&&Z(r,a[9]),s&&s.p&&(!n||c[0]&536870912)&&Lo(s,i,a,a[29],n?Mo(i,a[29],c,cu):Io(a[29]),kr)},i(a){n||(re(s,a),n=!0)},o(a){ue(s,a),n=!1},d(a){a&&(w(t),w(o)),s&&s.d(a)}}}function wu(e){let t,r,o,n,i;const s=[_u,fu],a=[];function c(l,u){return l[4]==="pending"?0:l[4]==="error"?1:-1}return~(r=c(e))&&(o=a[r]=s[r](e)),{c(){t=le("div"),o&&o.c(),oe(t,"class",n="wrap "+e[8]+" "+e[6]+" svelte-z7cif2"),q(t,"hide",!e[4]||e[4]==="complete"||e[6]==="hidden"),q(t,"translucent",e[8]==="center"&&(e[4]==="pending"||e[4]==="error")||e[11]||e[6]==="minimal"),q(t,"generating",e[4]==="generating"),q(t,"border",e[12]),be(t,"position",e[10]?"absolute":"static"),be(t,"padding",e[10]?"0":"var(--size-8) 0")},m(l,u){E(l,t,u),~r&&a[r].m(t,null),e[33](t),i=!0},p(l,u){let f=r;r=c(l),r===f?~r&&a[r].p(l,u):(o&&(qt(),ue(a[f],1,1,()=>{a[f]=null}),zt()),~r?(o=a[r],o?o.p(l,u):(o=a[r]=s[r](l),o.c()),re(o,1),o.m(t,null)):o=null),(!i||u[0]&320&&n!==(n="wrap "+l[8]+" "+l[6]+" svelte-z7cif2"))&&oe(t,"class",n),(!i||u[0]&336)&&q(t,"hide",!l[4]||l[4]==="complete"||l[6]==="hidden"),(!i||u[0]&2384)&&q(t,"translucent",l[8]==="center"&&(l[4]==="pending"||l[4]==="error")||l[11]||l[6]==="minimal"),(!i||u[0]&336)&&q(t,"generating",l[4]==="generating"),(!i||u[0]&4416)&&q(t,"border",l[12]),u[0]&1024&&be(t,"position",l[10]?"absolute":"static"),u[0]&1024&&be(t,"padding",l[10]?"0":"var(--size-8) 0")},i(l){i||(re(o),i=!0)},o(l){ue(o),i=!1},d(l){l&&w(t),~r&&a[r].d(),e[33](null)}}}let st=[],Ct=!1;async function Eu(e,t=!0){if(!(window.__gradio_mode__==="website"||window.__gradio_mode__!=="app"&&t!==!0)){if(st.push(e),!Ct)Ct=!0;else return;await su(),requestAnimationFrame(()=>{let r=[0,0];for(let o=0;o<st.length;o++){const i=st[o].getBoundingClientRect();(o===0||i.top+window.scrollY<=r[0])&&(r[0]=i.top+window.scrollY,r[1]=o)}window.scrollTo({top:r[0]-20,behavior:"smooth"}),Ct=!1,st=[]})}}function Su(e,t,r){let o,{$$slots:n={},$$scope:i}=t;const s=lu();let{i18n:a}=t,{eta:c=null}=t,{queue_position:l}=t,{queue_size:u}=t,{status:f}=t,{scroll_to_output:_=!1}=t,{timer:d=!0}=t,{show_progress:b="full"}=t,{message:g=null}=t,{progress:S=null}=t,{variant:P="default"}=t,{loading_text:m="Loading..."}=t,{absolute:h=!0}=t,{translucent:y=!1}=t,{border:C=!1}=t,{autoscroll:V}=t,W,me=!1,$=0,F=0,Y=null,O=null,Ge=0,ne=null,ie,J=null,Fe=!0;const Et=()=>{r(0,c=r(27,Y=r(19,Q=null))),r(25,$=performance.now()),r(26,F=0),me=!0,Je()};function Je(){requestAnimationFrame(()=>{r(26,F=(performance.now()-$)/1e3),me&&Je()})}function Ue(){r(26,F=0),r(0,c=r(27,Y=r(19,Q=null))),me&&(me=!1)}au(()=>{me&&Ue()});let Q=null;function se(v){xr[v?"unshift":"push"](()=>{J=v,r(16,J),r(7,S),r(14,ne),r(15,ie)})}const Qe=()=>{s("clear_status")};function Ke(v){xr[v?"unshift":"push"](()=>{W=v,r(13,W)})}return e.$$set=v=>{"i18n"in v&&r(1,a=v.i18n),"eta"in v&&r(0,c=v.eta),"queue_position"in v&&r(2,l=v.queue_position),"queue_size"in v&&r(3,u=v.queue_size),"status"in v&&r(4,f=v.status),"scroll_to_output"in v&&r(22,_=v.scroll_to_output),"timer"in v&&r(5,d=v.timer),"show_progress"in v&&r(6,b=v.show_progress),"message"in v&&r(23,g=v.message),"progress"in v&&r(7,S=v.progress),"variant"in v&&r(8,P=v.variant),"loading_text"in v&&r(9,m=v.loading_text),"absolute"in v&&r(10,h=v.absolute),"translucent"in v&&r(11,y=v.translucent),"border"in v&&r(12,C=v.border),"autoscroll"in v&&r(24,V=v.autoscroll),"$$scope"in v&&r(29,i=v.$$scope)},e.$$.update=()=>{e.$$.dirty[0]&436207617&&(c===null&&r(0,c=Y),c!=null&&Y!==c&&(r(28,O=(performance.now()-$)/1e3+c),r(19,Q=O.toFixed(1)),r(27,Y=c))),e.$$.dirty[0]&335544320&&r(17,Ge=O===null||O<=0||!F?null:Math.min(F/O,1)),e.$$.dirty[0]&128&&S!=null&&r(18,Fe=!1),e.$$.dirty[0]&114816&&(S!=null?r(14,ne=S.map(v=>{if(v.index!=null&&v.length!=null)return v.index/v.length;if(v.progress!=null)return v.progress})):r(14,ne=null),ne?(r(15,ie=ne[ne.length-1]),J&&(ie===0?r(16,J.style.transition="0",J):r(16,J.style.transition="150ms",J))):r(15,ie=void 0)),e.$$.dirty[0]&16&&(f==="pending"?Et():Ue()),e.$$.dirty[0]&20979728&&W&&_&&(f==="pending"||f==="complete")&&Eu(W,V),e.$$.dirty[0]&8388624,e.$$.dirty[0]&67108864&&r(20,o=F.toFixed(1))},[c,a,l,u,f,d,b,S,P,m,h,y,C,W,ne,ie,J,Ge,Fe,Q,o,s,_,g,V,$,F,Y,O,i,n,se,Qe,Ke]}class xu extends ou{constructor(t){super(),nu(this,t,Su,wu,iu,{i18n:1,eta:0,queue_position:2,queue_size:3,status:4,scroll_to_output:22,timer:5,show_progress:6,message:23,progress:7,variant:8,loading_text:9,absolute:10,translucent:11,border:12,autoscroll:24},null,[-1,-1])}get i18n(){return this.$$.ctx[1]}set i18n(t){this.$$set({i18n:t}),M()}get eta(){return this.$$.ctx[0]}set eta(t){this.$$set({eta:t}),M()}get queue_position(){return this.$$.ctx[2]}set queue_position(t){this.$$set({queue_position:t}),M()}get queue_size(){return this.$$.ctx[3]}set queue_size(t){this.$$set({queue_size:t}),M()}get status(){return this.$$.ctx[4]}set status(t){this.$$set({status:t}),M()}get scroll_to_output(){return this.$$.ctx[22]}set scroll_to_output(t){this.$$set({scroll_to_output:t}),M()}get timer(){return this.$$.ctx[5]}set timer(t){this.$$set({timer:t}),M()}get show_progress(){return this.$$.ctx[6]}set show_progress(t){this.$$set({show_progress:t}),M()}get message(){return this.$$.ctx[23]}set message(t){this.$$set({message:t}),M()}get progress(){return this.$$.ctx[7]}set progress(t){this.$$set({progress:t}),M()}get variant(){return this.$$.ctx[8]}set variant(t){this.$$set({variant:t}),M()}get loading_text(){return this.$$.ctx[9]}set loading_text(t){this.$$set({loading_text:t}),M()}get absolute(){return this.$$.ctx[10]}set absolute(t){this.$$set({absolute:t}),M()}get translucent(){return this.$$.ctx[11]}set translucent(t){this.$$set({translucent:t}),M()}get border(){return this.$$.ctx[12]}set border(t){this.$$set({border:t}),M()}get autoscroll(){return this.$$.ctx[24]}set autoscroll(t){this.$$set({autoscroll:t}),M()}}const Ro={built_with_gradio:"تم الإنشاء بإستخدام Gradio",clear:"أمسح",or:"أو",submit:"أرسل"},Do={click_to_upload:"إضغط للتحميل",drop_audio:"أسقط الملف الصوتي هنا",drop_csv:"أسقط ملف البيانات هنا",drop_file:"أسقط الملف هنا",drop_image:"أسقط الصورة هنا",drop_video:"أسقط الفيديو هنا"},Tu={common:Ro,upload_text:Do},ku=Object.freeze(Object.defineProperty({__proto__:null,common:Ro,default:Tu,upload_text:Do},Symbol.toStringTag,{value:"Module"})),jo={built_with_gradio:"Construït amb gradio",clear:"Neteja",empty:"Buit",error:"Error",loading:"S'està carregant",or:"o",submit:"Envia"},Go={click_to_upload:"Feu clic per pujar",drop_audio:"Deixeu anar l'àudio aquí",drop_csv:"Deixeu anar el CSV aquí",drop_file:"Deixeu anar el fitxer aquí",drop_image:"Deixeu anar la imatge aquí",drop_video:"Deixeu anar el vídeo aquí"},Hu={common:jo,upload_text:Go},Pu=Object.freeze(Object.defineProperty({__proto__:null,common:jo,default:Hu,upload_text:Go},Symbol.toStringTag,{value:"Module"})),Fo={annotated_image:"وێنەی نیشانە کراو"},Uo={allow_recording_access:"تکایە ڕێگە بدە بە بەکارهێنانی مایکرۆفۆنەکە بۆ تۆمارکردن.",audio:"دەنگ",record_from_microphone:"تۆمارکردن لە مایکەوە",stop_recording:"تۆمارکردن بوەستێنە"},Vo={connection_can_break:"لە مۆبایلدا، پەیوەندییەکە دەکرێت بپچڕێت ئەگەر ئەم تابە چالاک نەبێت یان ئامێرەکە بچێتە دۆخی پشوو، ئەمەش شوێنی خۆت لە ڕیزدا لەدەست دەدات.",long_requests_queue:"ڕیزێکی درێژی داواکاری هەیە. ئەم سپەیسە دووباد بکە بۆی چاوەڕوان نەبیت.",lost_connection:"پەیوەندی پچڕا بەهۆی جێهێشتنی پەیج. "},zo={checkbox:"بۆکسی هەڵبژاردن",checkbox_group:"گروپی بۆکسی هەڵبژاردن"},qo={code:"کۆد"},Xo={color_picker:"ڕەنگ هەڵبژاردە"},Zo={built_with:"دروستکراوە لەگەڵ...",built_with_gradio:"Gradio دروستکراوە بە",clear:"خاوێنکردنەوە",download:"دابەزاندن",edit:"بژارکردن",empty:"بەتاڵ",error:"هەڵە",hosted_on:"میوانداری کراوە لە",loading:"بارکردن",logo:"لۆگۆ",or:"یان",remove:"لابردن",share:"هاوبەشکردن",submit:"پێشکەشکردن",undo:"پووچکردنەوە"},Wo={incorrect_format:"فۆرماتێکی هەڵە، تەنها فایلەکانی CSV و TSV پشتگیری دەکرێن",new_column:"ستوونی نوێ",new_row:"ڕیزێکی نوێ"},$o={dropdown:"فڕێدانە خوار"},Yo={build_error:"هەڵەی دروستکردن هەیە",config_error:"هەڵەی ڕێکخستن هەیە",contact_page_author:"تکایە پەیوەندی بە نووسەری پەیجەوە بکەن بۆ ئەوەی ئاگاداریان بکەنەوە.",no_app_file:"هیچ فایلێکی ئەپ نییە",runtime_error:"هەڵەیەکی runtime هەیە",space_not_working:'"سپەیسەکە کارناکات چونکە" {0}',space_paused:"فەزاکە وەستاوە",use_via_api:"لە ڕێگەی API بەکاری بهێنە"},Jo={uploading:"بارکردن..."},Qo={highlighted_text:"دەقی ڕۆشن کراو"},Ko={allow_webcam_access:"تکایە ڕێگە بدە بە بەکارهێنانی وێبکامەکە بۆ تۆمارکردن.",brush_color:"ڕەنگی فڵچە",brush_radius:"تیژڕەوی فڵچە",image:"وێنە",remove_image:"لابردنی وێنە",select_brush_color:"ڕەنگی فڵچە هەڵبژێرە",start_drawing:"دەست بکە بە وێنەکێشان",use_brush:"فڵچە بەکاربهێنە"},en={label:"لەیبڵ"},tn={enable_cookies:"ئەگەر تۆ سەردانی HuggingFace Space دەکەیت لە دۆخی نادیاردا، پێویستە کووکی لایەنی سێیەم چالاک بکەیت.",incorrect_credentials:"بڕوانامەی هەڵە",login:"چونه‌ ژووره‌وه‌"},rn={number:"ژمارە"},on={plot:"هێڵکاری"},nn={radio:"ڕادیۆ"},sn={slider:"خلیسکە"},an={click_to_upload:"کلیک بکە بۆ بارکردن",drop_audio:"دەنگ لێرە دابنێ",drop_csv:"لێرەدا CSV دابنێ",drop_file:"فایل لێرە دابنێ",drop_image:"وێنە لێرەدا دابنێ",drop_video:"ڤیدیۆ لێرە دابنێ"},Ou={"3D_model":{"3d_model":"مۆدێلی سێ ڕەهەندی"},annotated_image:Fo,audio:Uo,blocks:Vo,checkbox:zo,code:qo,color_picker:Xo,common:Zo,dataframe:Wo,dropdown:$o,errors:Yo,file:Jo,highlighted_text:Qo,image:Ko,label:en,login:tn,number:rn,plot:on,radio:nn,slider:sn,upload_text:an},Au=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:Fo,audio:Uo,blocks:Vo,checkbox:zo,code:qo,color_picker:Xo,common:Zo,dataframe:Wo,default:Ou,dropdown:$o,errors:Yo,file:Jo,highlighted_text:Qo,image:Ko,label:en,login:tn,number:rn,plot:on,radio:nn,slider:sn,upload_text:an},Symbol.toStringTag,{value:"Module"})),ln={built_with_gradio:"Mit Gradio erstellt",clear:"Löschen",or:"oder",submit:"Absenden"},un={click_to_upload:"Hochladen",drop_audio:"Audio hier ablegen",drop_csv:"CSV Datei hier ablegen",drop_file:"Datei hier ablegen",drop_image:"Bild hier ablegen",drop_video:"Video hier ablegen"},Bu={common:ln,upload_text:un},Cu=Object.freeze(Object.defineProperty({__proto__:null,common:ln,default:Bu,upload_text:un},Symbol.toStringTag,{value:"Module"})),cn={annotated_image:"Annotated Image"},fn={allow_recording_access:"Please allow access to the microphone for recording.",audio:"Audio",record_from_microphone:"Record from microphone",stop_recording:"Stop recording",no_device_support:"Media devices could not be accessed. Check that you are running on a secure origin (https) or localhost (or you have passed a valid SSL certificate to ssl_verify), and you have allowed browser access to your device.",stop:"Stop",resume:"Resume",record:"Record",no_microphone:"No microphone found",pause:"Pause",play:"Play"},_n={connection_can_break:"On mobile, the connection can break if this tab is unfocused or the device sleeps, losing your position in queue.",long_requests_queue:"There is a long queue of requests pending. Duplicate this Space to skip.",lost_connection:"Lost connection due to leaving page. Rejoining queue..."},hn={checkbox:"Checkbox",checkbox_group:"Checkbox Group"},dn={code:"Code"},mn={color_picker:"Color Picker"},pn={built_with:"built with",built_with_gradio:"Built with Gradio",clear:"Clear",download:"Download",edit:"Edit",empty:"Empty",error:"Error",hosted_on:"Hosted on",loading:"Loading",logo:"logo",or:"or",remove:"Remove",share:"Share",submit:"Submit",undo:"Undo",no_devices:"No devices found"},gn={incorrect_format:"Incorrect format, only CSV and TSV files are supported",new_column:"New column",new_row:"New row"},bn={dropdown:"Dropdown"},vn={build_error:"there is a build error",config_error:"there is a config error",contact_page_author:"Please contact the author of the page to let them know.",no_app_file:"there is no app file",runtime_error:"there is a runtime error",space_not_working:`"Space isn't working because" {0}`,space_paused:"the space is paused",use_via_api:"Use via API"},yn={uploading:"Uploading..."},wn={highlighted_text:"Highlighted Text"},En={allow_webcam_access:"Please allow access to the webcam for recording.",brush_color:"Brush color",brush_radius:"Brush radius",image:"Image",remove_image:"Remove Image",select_brush_color:"Select brush color",start_drawing:"Start drawing",use_brush:"Use brush"},Sn={label:"Label"},xn={enable_cookies:"If you are visiting a HuggingFace Space in Incognito mode, you must enable third party cookies.",incorrect_credentials:"Incorrect Credentials",login:"Login"},Tn={number:"Number"},kn={plot:"Plot"},Hn={radio:"Radio"},Pn={slider:"Slider"},On={click_to_upload:"Click to Upload",drop_audio:"Drop Audio Here",drop_csv:"Drop CSV Here",drop_file:"Drop File Here",drop_image:"Drop Image Here",drop_video:"Drop Video Here",drop_gallery:"Drop Image(s) Here",paste_clipboard:"Paste from Clipboard"},Iu={"3D_model":{"3d_model":"3D Model"},annotated_image:cn,audio:fn,blocks:_n,checkbox:hn,code:dn,color_picker:mn,common:pn,dataframe:gn,dropdown:bn,errors:vn,file:yn,highlighted_text:wn,image:En,label:Sn,login:xn,number:Tn,plot:kn,radio:Hn,slider:Pn,upload_text:On},Mu=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:cn,audio:fn,blocks:_n,checkbox:hn,code:dn,color_picker:mn,common:pn,dataframe:gn,default:Iu,dropdown:bn,errors:vn,file:yn,highlighted_text:wn,image:En,label:Sn,login:xn,number:Tn,plot:kn,radio:Hn,slider:Pn,upload_text:On},Symbol.toStringTag,{value:"Module"})),An={built_with_gradio:"Construido con Gradio",clear:"Limpiar",or:"o",submit:"Enviar"},Bn={click_to_upload:"Haga click para cargar",drop_audio:"Coloque el audio aquí",drop_csv:"Coloque el CSV aquí",drop_file:"Coloque el archivo aquí",drop_image:"Coloque la imagen aquí",drop_video:"Coloque el video aquí",drop_gallery:"Coloque las imagenes aquí"},Nu={common:An,upload_text:Bn},Lu=Object.freeze(Object.defineProperty({__proto__:null,common:An,default:Nu,upload_text:Bn},Symbol.toStringTag,{value:"Module"})),Cn={built_with_gradio:"Gradiorekin eraikia",clear:"Garbitu",or:"edo",submit:"Bidali"},In={click_to_upload:"Klik egin kargatzeko",drop_audio:"Jarri hemen audioa",drop_csv:"Jarri hemen CSVa",drop_file:"Jarri hemen fitxategia",drop_image:"Jarri hemen irudia",drop_video:"Jarri hemen bideoa"},Ru={common:Cn,upload_text:In},Du=Object.freeze(Object.defineProperty({__proto__:null,common:Cn,default:Ru,upload_text:In},Symbol.toStringTag,{value:"Module"})),Mn={built_with_gradio:"ساخته شده با gradio",clear:"حذف",or:"یا",submit:"ارسال"},Nn={click_to_upload:"برای آپلود کلیک کنید",drop_audio:"صوت را اینجا رها کنید",drop_csv:"فایل csv را  اینجا رها کنید",drop_file:"فایل را اینجا رها کنید",drop_image:"تصویر را اینجا رها کنید",drop_video:"ویدیو را اینجا رها کنید"},ju={common:Mn,upload_text:Nn},Gu=Object.freeze(Object.defineProperty({__proto__:null,common:Mn,default:ju,upload_text:Nn},Symbol.toStringTag,{value:"Module"})),Ln={allow_recording_access:"Veuillez autoriser l'accès à l'enregistrement",audio:"Audio",record_from_microphone:"Enregistrer avec le microphone",stop_recording:"Arrêter l'enregistrement"},Rn={built_with:"Construit avec",built_with_gradio:"Construit avec Gradio",clear:"Effacer",download:"Télécharger",edit:"Éditer",error:"Erreur",loading:"Chargement",logo:"logo",or:"ou",remove:"Supprimer",share:"Partager",submit:"Soumettre"},Dn={click_to_upload:"Cliquer pour Télécharger",drop_audio:"Déposer l'Audio Ici",drop_csv:"Déposer le CSV Ici",drop_file:"Déposer le Fichier Ici",drop_image:"Déposer l'Image Ici",drop_video:"Déposer la Vidéo Ici"},Fu={audio:Ln,common:Rn,upload_text:Dn},Uu=Object.freeze(Object.defineProperty({__proto__:null,audio:Ln,common:Rn,default:Fu,upload_text:Dn},Symbol.toStringTag,{value:"Module"})),jn={built_with_gradio:"בנוי עם גרדיו",clear:"נקה",or:"או",submit:"שלח"},Gn={click_to_upload:"לחץ כדי להעלות",drop_audio:"גרור לכאן קובץ שמע",drop_csv:"גרור csv קובץ לכאן",drop_file:"גרור קובץ לכאן",drop_image:"גרור קובץ תמונה לכאן",drop_video:"גרור קובץ סרטון לכאן"},Vu={common:jn,upload_text:Gn},zu=Object.freeze(Object.defineProperty({__proto__:null,common:jn,default:Vu,upload_text:Gn},Symbol.toStringTag,{value:"Module"})),Fn={built_with_gradio:"Gradio से बना",clear:"हटाये",or:"या",submit:"सबमिट करे"},Un={click_to_upload:"अपलोड के लिए बटन दबायें",drop_audio:"यहाँ ऑडियो ड्रॉप करें",drop_csv:"यहाँ CSV ड्रॉप करें",drop_file:"यहाँ File ड्रॉप करें",drop_image:"यहाँ इमेज ड्रॉप करें",drop_video:"यहाँ वीडियो ड्रॉप करें"},qu={common:Fn,upload_text:Un},Xu=Object.freeze(Object.defineProperty({__proto__:null,common:Fn,default:qu,upload_text:Un},Symbol.toStringTag,{value:"Module"})),Vn={built_with_gradio:"gradioで作ろう",clear:"クリア",or:"または",submit:"送信"},zn={click_to_upload:"クリックしてアップロード",drop_audio:"ここに音声をドロップ",drop_csv:"ここにCSVをドロップ",drop_file:"ここにファイルをドロップ",drop_image:"ここに画像をドロップ",drop_video:"ここに動画をドロップ"},Zu={common:Vn,upload_text:zn},Wu=Object.freeze(Object.defineProperty({__proto__:null,common:Vn,default:Zu,upload_text:zn},Symbol.toStringTag,{value:"Module"})),qn={built_with_gradio:"gradio로 제작되었습니다",clear:"클리어",or:"또는",submit:"제출하기"},Xn={click_to_upload:"클릭해서 업로드하기",drop_audio:"오디오를 끌어 놓으세요",drop_csv:"CSV파일을 끌어 놓으세요",drop_file:"파일을 끌어 놓으세요",drop_image:"이미지를 끌어 놓으세요",drop_video:"비디오를 끌어 놓으세요"},$u={common:qn,upload_text:Xn},Yu=Object.freeze(Object.defineProperty({__proto__:null,common:qn,default:$u,upload_text:Xn},Symbol.toStringTag,{value:"Module"})),Zn={built_with_gradio:"sukurta su gradio",clear:"Trinti",or:"arba",submit:"Pateikti"},Wn={click_to_upload:"Spustelėkite norėdami įkelti",drop_audio:"Įkelkite garso įrašą čia",drop_csv:"Įkelkite CSV čia",drop_file:"Įkelkite bylą čia",drop_image:"Įkelkite paveikslėlį čia",drop_video:"Įkelkite vaizdo įrašą čia"},Ju={common:Zn,upload_text:Wn},Qu=Object.freeze(Object.defineProperty({__proto__:null,common:Zn,default:Ju,upload_text:Wn},Symbol.toStringTag,{value:"Module"})),$n={built_with_gradio:"gemaakt met gradio",clear:"Wis",or:"of",submit:"Zend in"},Yn={click_to_upload:"Klik om the Uploaden",drop_audio:"Sleep een Geluidsbestand hier",drop_csv:"Sleep een CSV hier",drop_file:"Sleep een Document hier",drop_image:"Sleep een Afbeelding hier",drop_video:"Sleep een Video hier"},Ku={common:$n,upload_text:Yn},ec=Object.freeze(Object.defineProperty({__proto__:null,common:$n,default:Ku,upload_text:Yn},Symbol.toStringTag,{value:"Module"})),Jn={built_with_gradio:"utworzone z gradio",clear:"Wyczyść",or:"lub",submit:"Zatwierdź"},Qn={click_to_upload:"Kliknij, aby przesłać",drop_audio:"Przeciągnij tutaj audio",drop_csv:"Przeciągnij tutaj CSV",drop_file:"Przeciągnij tutaj plik",drop_image:"Przeciągnij tutaj zdjęcie",drop_video:"Przeciągnij tutaj video"},tc={common:Jn,upload_text:Qn},rc=Object.freeze(Object.defineProperty({__proto__:null,common:Jn,default:tc,upload_text:Qn},Symbol.toStringTag,{value:"Module"})),Kn={built_with_gradio:"Construído com gradio",clear:"Limpar",error:"Erro",flag:"Marcar",loading:"Carregando",or:"ou",submit:"Enviar"},ei={click_to_upload:"Clique para o Upload",drop_audio:"Solte o Áudio Aqui",drop_csv:"Solte o CSV Aqui",drop_file:"Solte o Arquivo Aqui",drop_image:"Solte a Imagem Aqui",drop_video:"Solte o Vídeo Aqui"},oc={common:Kn,upload_text:ei},nc=Object.freeze(Object.defineProperty({__proto__:null,common:Kn,default:oc,upload_text:ei},Symbol.toStringTag,{value:"Module"})),ti={annotated_image:"Аннотированное изображение"},ri={allow_recording_access:"Пожалуйста, предоставьте доступ к микрофону для записи.",audio:"Аудио",record_from_microphone:"Записать с микрофона",stop_recording:"Остановить запись",no_device_support:"Не удалось получить доступ к медиаустройствам. Убедитесь, что вы работаете на защищенном источнике (https) или localhost (или передали действительный SSL-сертификат в ssl_verify), и разрешили браузеру доступ к устройству.",stop:"Стоп",resume:"Продолжить",record:"Записать",no_microphone:"Микрофон не найден",pause:"Пауза",play:"Воспроизвести"},oi={connection_can_break:"На мобильных устройствах соединение может прерваться, если вкладка будет переключена или устройство отключится, что приведет к потере вашей позиции в очереди.",long_requests_queue:"Очередь запросов длинная. Продублируйте это пространство, чтобы пропустить.",lost_connection:"Потеряно соединение из-за ухода со страницы. Повторное подключение..."},ni={checkbox:"Чекбокс",checkbox_group:"Группа чекбоксов"},ii={code:"Код"},si={color_picker:"Выбор цвета"},ai={built_with:"создано с",built_with_gradio:"Создано с помощью Gradio",clear:"Очистить",download:"Скачать",edit:"Изменить",empty:"Пусто",error:"Ошибка",hosted_on:"Размещено на",loading:"Загрузка",logo:"логотип",or:"или",remove:"Удалить",share:"Поделиться",submit:"Отправить",undo:"Отменить",no_devices:"Не найдено ни одного устройства"},li={incorrect_format:"Неправильный формат, поддерживаются только файлы CSV и TSV",new_column:"Новая колонка",new_row:"Новый ряд"},ui={dropdown:"Dropdown"},ci={build_error:"возникла ошибка сборки",config_error:"возникла ошибка конфигурации",contact_page_author:"Пожалуйста, свяжитесь с автором страницы, чтобы сообщить ему об этом.",no_app_file:"отсутствует файл приложения",runtime_error:"возникла проблема с выполнением",space_not_working:'"Пространство не работает, потому что" {0}',space_paused:"пространство приостановлено",use_via_api:"Использовать через API"},fi={uploading:"Загружаем..."},_i={highlighted_text:"Выделенный текст"},hi={allow_webcam_access:"Пожалуйста, разрешите доступ к веб-камере для записи.",brush_color:"Цвет кисти",brush_radius:"Радиус кисти",image:"Изображение",remove_image:"Удалить изображение",select_brush_color:"Выберите цвет кисти",start_drawing:"Начните рисовать",use_brush:"Используйте кисть"},di={label:"Лейбл"},mi={enable_cookies:"Если вы посещаете пространство HuggingFace в режиме инкогнито, вы должны разрешить сторонние файлы cookie.",incorrect_credentials:"Неправильные учетные данные",login:"Вход в систему"},pi={number:"Число"},gi={plot:"Схема"},bi={radio:"Радио"},vi={slider:"Слайдер"},yi={click_to_upload:"Нажмите, чтобы загрузить",drop_audio:"Перетащите аудио сюда",drop_csv:"Перетащите файл CSV сюда",drop_file:"Перетащите файл сюда",drop_image:"Перетащите изображение сюда",drop_video:"Перетащите видео сюда",drop_gallery:"Перетащите изображение(-я) сюда",paste_clipboard:"Вставка из буфера обмена"},ic={"3D_model":{"3d_model":"3D-модель"},annotated_image:ti,audio:ri,blocks:oi,checkbox:ni,code:ii,color_picker:si,common:ai,dataframe:li,dropdown:ui,errors:ci,file:fi,highlighted_text:_i,image:hi,label:di,login:mi,number:pi,plot:gi,radio:bi,slider:vi,upload_text:yi},sc=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:ti,audio:ri,blocks:oi,checkbox:ni,code:ii,color_picker:si,common:ai,dataframe:li,default:ic,dropdown:ui,errors:ci,file:fi,highlighted_text:_i,image:hi,label:di,login:mi,number:pi,plot:gi,radio:bi,slider:vi,upload_text:yi},Symbol.toStringTag,{value:"Module"})),wi={built_with_gradio:"கிரேடியோ வுடன் உருவாக்கப்பட்டது",clear:"அழிக்கவும்",or:"அல்லது",submit:"சமர்ப்பிக்கவும்"},Ei={click_to_upload:"பதிவேற்ற அழுத்தவும்",drop_audio:"ஆடியோவை பதிவேற்றவும்",drop_csv:"csv ஐ பதிவேற்றவும்",drop_file:"கோப்பை பதிவேற்றவும்",drop_image:"படத்தை பதிவேற்றவும்",drop_video:"காணொளியை பதிவேற்றவும்"},ac={common:wi,upload_text:Ei},lc=Object.freeze(Object.defineProperty({__proto__:null,common:wi,default:ac,upload_text:Ei},Symbol.toStringTag,{value:"Module"})),Si={built_with_gradio:"Gradio ile oluşturulmuştur",clear:"Temizle",or:"veya",submit:"Yükle"},xi={click_to_upload:"Yüklemek için Tıkla",drop_audio:"Kaydı Buraya Sürükle",drop_csv:"CSV'yi Buraya Sürükle",drop_file:"Dosyayı Buraya Sürükle",drop_image:"Resmi Buraya Sürükle",drop_video:"Videoyu Buraya Sürükle"},uc={common:Si,upload_text:xi},cc=Object.freeze(Object.defineProperty({__proto__:null,common:Si,default:uc,upload_text:xi},Symbol.toStringTag,{value:"Module"})),Ti={built_with_gradio:"Зроблено на основі gradio",clear:"Очистити",or:"або",submit:"Надіслати"},ki={click_to_upload:"Натисніть щоб завантажити",drop_audio:"Перетягніть аудіо сюди",drop_csv:"Перетягніть CSV-файл сюди",drop_file:"Перетягніть файл сюди",drop_image:"Перетягніть зображення сюди",drop_video:"Перетягніть відео сюди"},fc={common:Ti,upload_text:ki},_c=Object.freeze(Object.defineProperty({__proto__:null,common:Ti,default:fc,upload_text:ki},Symbol.toStringTag,{value:"Module"})),Hi={built_with_gradio:"کے ساتھ بنایا گیا Gradio",clear:"ہٹا دیں",or:"یا",submit:"جمع کریں"},Pi={click_to_upload:"اپ لوڈ کے لیے کلک کریں",drop_audio:"یہاں آڈیو ڈراپ کریں",drop_csv:"یہاں فائل ڈراپ کریں",drop_file:"یہاں فائل ڈراپ کریں",drop_image:"یہاں تصویر ڈراپ کریں",drop_video:"یہاں ویڈیو ڈراپ کریں"},hc={common:Hi,upload_text:Pi},dc=Object.freeze(Object.defineProperty({__proto__:null,common:Hi,default:hc,upload_text:Pi},Symbol.toStringTag,{value:"Module"})),Oi={built_with_gradio:"gradio bilan qilingan",clear:"Tozalash",submit:"Yubor"},Ai={click_to_upload:"Yuklash uchun Bosing",drop_audio:"Audioni Shu Yerga Tashlang",drop_csv:"CSVni Shu Yerga Tashlang",drop_file:"Faylni Shu Yerga Tashlang",drop_image:"Rasmni Shu Yerga Tashlang",drop_video:"Videoni Shu Yerga Tashlang"},mc={common:Oi,upload_text:Ai},pc=Object.freeze(Object.defineProperty({__proto__:null,common:Oi,default:mc,upload_text:Ai},Symbol.toStringTag,{value:"Module"})),Bi={annotated_image:"标注图像"},Ci={allow_recording_access:"请允许访问麦克风以进行录音。",audio:"音频",record_from_microphone:"从麦克风录制",stop_recording:"停止录制",no_device_support:"无法访问媒体设备。请检查您是否在安全来源（https）或本地主机上运行（或者您已经通过 ssl_verify 传递了有效的 SSL 证书），并且您已经允许浏览器访问您的设备。",stop:"停止",resume:"继续",record:"录制",no_microphone:"找不到麦克风",pause:"暂停",play:"播放"},Ii={connection_can_break:"在移动设备上，如果此标签页失去焦点或设备休眠，连接可能会中断，导致您在队列中失去位置。",long_requests_queue:"有一个长时间的待处理请求队列。复制此空间以跳过。",lost_connection:"由于离开页面，连接已丢失。重新加入队列..."},Mi={checkbox:"复选框",checkbox_group:"复选框组"},Ni={code:"代码"},Li={color_picker:"颜色选择器"},Ri={built_with:"构建于",built_with_gradio:"使用 Gradio 构建",clear:"清除",download:"下载",edit:"编辑",empty:"空",error:"错误",hosted_on:"托管在",loading:"加载中",logo:"标志",or:"或",remove:"移除",share:"分享",submit:"提交",undo:"撤销"},Di={incorrect_format:"格式不正确，仅支持 CSV 和 TSV 文件",new_column:"新列",new_row:"新行"},ji={dropdown:"下拉菜单"},Gi={build_error:"存在构建错误",config_error:"存在配置错误",contact_page_author:"请联系页面的作者并告知他们。",no_app_file:"不存在应用文件",runtime_error:"存在运行时错误",space_not_working:'"空间无法工作，原因：" {0}',space_paused:"空间已暂停",use_via_api:"通过 API 使用"},Fi={uploading:"正在上传..."},Ui={highlighted_text:"高亮文本"},Vi={allow_webcam_access:"请允许访问网络摄像头以进行录制。",brush_color:"画笔颜色",brush_radius:"画笔半径",image:"图像",remove_image:"移除图像",select_brush_color:"选择画笔颜色",start_drawing:"开始绘画",use_brush:"使用画笔"},zi={label:"标签"},qi={enable_cookies:"如果您正在使用隐身模式访问 HuggingFace 空间，您必须启用第三方 cookie。",incorrect_credentials:"凭据不正确",login:"登录"},Xi={number:"数字"},Zi={plot:"图表"},Wi={radio:"单选框"},$i={slider:"滑块"},Yi={click_to_upload:"点击上传",drop_audio:"将音频拖放到此处",drop_csv:"将 CSV 文件拖放到此处",drop_file:"将文件拖放到此处",drop_image:"将图像拖放到此处",drop_video:"将视频拖放到此处"},gc={"3D_model":{"3d_model":"3D模型"},annotated_image:Bi,audio:Ci,blocks:Ii,checkbox:Mi,code:Ni,color_picker:Li,common:Ri,dataframe:Di,dropdown:ji,errors:Gi,file:Fi,highlighted_text:Ui,image:Vi,label:zi,login:qi,number:Xi,plot:Zi,radio:Wi,slider:$i,upload_text:Yi},bc=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:Bi,audio:Ci,blocks:Ii,checkbox:Mi,code:Ni,color_picker:Li,common:Ri,dataframe:Di,default:gc,dropdown:ji,errors:Gi,file:Fi,highlighted_text:Ui,image:Vi,label:zi,login:qi,number:Xi,plot:Zi,radio:Wi,slider:$i,upload_text:Yi},Symbol.toStringTag,{value:"Module"})),Ji={built_with_gradio:"使用Gradio構建",clear:"清除",or:"或",submit:"提交"},Qi={click_to_upload:"點擊上傳",drop_audio:"拖放音訊至此處",drop_csv:"拖放CSV至此處",drop_file:"拖放檔案至此處",drop_image:"拖放圖片至此處",drop_video:"拖放影片至此處"},vc={common:Ji,upload_text:Qi},yc=Object.freeze(Object.defineProperty({__proto__:null,common:Ji,default:vc,upload_text:Qi},Symbol.toStringTag,{value:"Module"})),Gr=Object.assign({"./lang/ar.json":ku,"./lang/ca.json":Pu,"./lang/ckb.json":Au,"./lang/de.json":Cu,"./lang/en.json":Mu,"./lang/es.json":Lu,"./lang/eu.json":Du,"./lang/fa.json":Gu,"./lang/fr.json":Uu,"./lang/he.json":zu,"./lang/hi.json":Xu,"./lang/ja.json":Wu,"./lang/ko.json":Yu,"./lang/lt.json":Qu,"./lang/nl.json":ec,"./lang/pl.json":rc,"./lang/pt-BR.json":nc,"./lang/ru.json":sc,"./lang/ta.json":lc,"./lang/tr.json":cc,"./lang/uk.json":_c,"./lang/ur.json":dc,"./lang/uz.json":pc,"./lang/zh-CN.json":bc,"./lang/zh-TW.json":yc});function wc(){let e={};for(const t in Gr){const r=t.split("/").pop().split(".").shift();e[r]=Gr[t].default}return e}const Fr=wc();for(const e in Fr)vo(e,Fr[e]);async function Ec(){await ja({fallbackLocale:"en",initialLocale:Wa()})}const{setContext:Sc,getContext:xc}=window.__gradio__svelte__internal,Ki="WORKER_PROXY_CONTEXT_KEY";function Tc(e){Sc(Ki,e)}function of(){return xc(Ki)}const{SvelteComponent:kc,add_flush_callback:ut,append:de,assign:Hc,attr:ce,bind:ct,binding_callbacks:ft,check_outros:Ur,component_subscribe:Vr,create_component:vt,destroy_component:yt,detach:xe,element:ye,empty:Pc,flush:N,get_spread_object:Oc,get_spread_update:Ac,group_outros:zr,init:Bc,insert:Te,mount_component:wt,noop:Cc,safe_not_equal:Ic,set_data:es,space:ts,text:Xe,transition_in:he,transition_out:ve}=window.__gradio__svelte__internal,{onMount:qr,createEventDispatcher:Mc}=window.__gradio__svelte__internal;function Xr(e){let t,r;return t=new xu({props:{absolute:!e[4],status:e[14],timer:!1,queue_position:null,queue_size:null,translucent:!0,loading_text:e[15],i18n:e[22],autoscroll:e[0],$$slots:{error:[jc],"additional-loading-text":[Lc]},$$scope:{ctx:e}}}),{c(){vt(t.$$.fragment)},m(o,n){wt(t,o,n),r=!0},p(o,n){const i={};n[0]&16&&(i.absolute=!o[4]),n[0]&16384&&(i.status=o[14]),n[0]&32768&&(i.loading_text=o[15]),n[0]&4194304&&(i.i18n=o[22]),n[0]&1&&(i.autoscroll=o[0]),n[0]&4202752|n[1]&1048576&&(i.$$scope={dirty:n,ctx:o}),t.$set(i)},i(o){r||(he(t.$$.fragment,o),r=!0)},o(o){ve(t.$$.fragment,o),r=!1},d(o){yt(t,o)}}}function Nc(e){let t;return{c(){t=ye("p"),t.innerHTML='If your custom component never loads, consult the troubleshooting <a style="color: blue;" href="https://www.gradio.app/guides/frequently-asked-questions#the-development-server-didnt-work-for-me" class="svelte-y6l4b">guide</a>.'},m(r,o){Te(r,t,o)},d(r){r&&xe(t)}}}function Lc(e){let t,r=e[23]==="dev"&&Nc();return{c(){t=ye("div"),r&&r.c(),ce(t,"class","load-text"),ce(t,"slot","additional-loading-text")},m(o,n){Te(o,t,n),r&&r.m(t,null)},p:Cc,d(o){o&&xe(t),r&&r.d()}}}function Rc(e){let t,r=e[22]("errors.contact_page_author")+"",o;return{c(){t=ye("p"),o=Xe(r),ce(t,"class","svelte-y6l4b")},m(n,i){Te(n,t,i),de(t,o)},p(n,i){i[0]&4194304&&r!==(r=n[22]("errors.contact_page_author")+"")&&es(o,r)},d(n){n&&xe(t)}}}function Dc(e){let t,r,o,n,i,s;return{c(){t=ye("p"),r=Xe("Please "),o=ye("a"),n=Xe("contact the author of the space"),s=Xe(" to let them know."),ce(o,"href",i="https://huggingface.co/spaces/"+e[8]+"/discussions/new?title="+e[24].title(e[13]?.detail)+"&description="+e[24].description(e[13]?.detail,location.origin)),ce(o,"class","svelte-y6l4b"),ce(t,"class","svelte-y6l4b")},m(a,c){Te(a,t,c),de(t,r),de(t,o),de(o,n),de(t,s)},p(a,c){c[0]&8448&&i!==(i="https://huggingface.co/spaces/"+a[8]+"/discussions/new?title="+a[24].title(a[13]?.detail)+"&description="+a[24].description(a[13]?.detail,location.origin))&&ce(o,"href",i)},d(a){a&&xe(t)}}}function jc(e){let t,r,o,n=(e[13]?.message||"")+"",i,s;function a(u,f){return(u[13].status==="space_error"||u[13].status==="paused")&&u[13].discussions_enabled?Dc:Rc}let c=a(e),l=c(e);return{c(){t=ye("div"),r=ye("p"),o=ye("strong"),i=Xe(n),s=ts(),l.c(),ce(r,"class","svelte-y6l4b"),ce(t,"class","error svelte-y6l4b"),ce(t,"slot","error")},m(u,f){Te(u,t,f),de(t,r),de(r,o),de(o,i),de(t,s),l.m(t,null)},p(u,f){f[0]&8192&&n!==(n=(u[13]?.message||"")+"")&&es(i,n),c===(c=a(u))&&l?l.p(u,f):(l.d(1),l=c(u),l&&(l.c(),l.m(t,null)))},d(u){u&&xe(t),l.d()}}}function Gc(e){let t,r,o,n,i;const s=[{app:e[17]},e[12],{fill_height:!e[4]&&e[12].fill_height},{theme_mode:e[16]},{control_page_title:e[5]},{target:e[9]},{autoscroll:e[0]},{show_footer:!e[4]},{app_mode:e[3]},{version:e[1]}];function a(f){e[33](f)}function c(f){e[34](f)}function l(f){e[35](f)}let u={};for(let f=0;f<s.length;f+=1)u=Hc(u,s[f]);return e[10]!==void 0&&(u.ready=e[10]),e[11]!==void 0&&(u.render_complete=e[11]),e[21]!==void 0&&(u.add_new_message=e[21]),t=new e[19]({props:u}),ft.push(()=>ct(t,"ready",a)),ft.push(()=>ct(t,"render_complete",c)),ft.push(()=>ct(t,"add_new_message",l)),{c(){vt(t.$$.fragment)},m(f,_){wt(t,f,_),i=!0},p(f,_){const d=_[0]&201275?Ac(s,[_[0]&131072&&{app:f[17]},_[0]&4096&&Oc(f[12]),_[0]&4112&&{fill_height:!f[4]&&f[12].fill_height},_[0]&65536&&{theme_mode:f[16]},_[0]&32&&{control_page_title:f[5]},_[0]&512&&{target:f[9]},_[0]&1&&{autoscroll:f[0]},_[0]&16&&{show_footer:!f[4]},_[0]&8&&{app_mode:f[3]},_[0]&2&&{version:f[1]}]):{};!r&&_[0]&1024&&(r=!0,d.ready=f[10],ut(()=>r=!1)),!o&&_[0]&2048&&(o=!0,d.render_complete=f[11],ut(()=>o=!1)),!n&&_[0]&2097152&&(n=!0,d.add_new_message=f[21],ut(()=>n=!1)),t.$set(d)},i(f){i||(he(t.$$.fragment,f),i=!0)},o(f){ve(t.$$.fragment,f),i=!1},d(f){yt(t,f)}}}function Fc(e){let t,r;return t=new e[20]({props:{auth_message:e[12].auth_message,root:e[12].root,space_id:e[8],app_mode:e[3]}}),{c(){vt(t.$$.fragment)},m(o,n){wt(t,o,n),r=!0},p(o,n){const i={};n[0]&4096&&(i.auth_message=o[12].auth_message),n[0]&4096&&(i.root=o[12].root),n[0]&256&&(i.space_id=o[8]),n[0]&8&&(i.app_mode=o[3]),t.$set(i)},i(o){r||(he(t.$$.fragment,o),r=!0)},o(o){ve(t.$$.fragment,o),r=!1},d(o){yt(t,o)}}}function Uc(e){let t,r,o,n,i,s=(e[14]==="pending"||e[14]==="error")&&!(e[12]&&e[12]?.auth_required)&&Xr(e);const a=[Fc,Gc],c=[];function l(u,f){return u[12]?.auth_required&&u[20]?0:u[12]&&u[19]&&u[18]?1:-1}return~(r=l(e))&&(o=c[r]=a[r](e)),{c(){s&&s.c(),t=ts(),o&&o.c(),n=Pc()},m(u,f){s&&s.m(u,f),Te(u,t,f),~r&&c[r].m(u,f),Te(u,n,f),i=!0},p(u,f){(u[14]==="pending"||u[14]==="error")&&!(u[12]&&u[12]?.auth_required)?s?(s.p(u,f),f[0]&20480&&he(s,1)):(s=Xr(u),s.c(),he(s,1),s.m(t.parentNode,t)):s&&(zr(),ve(s,1,1,()=>{s=null}),Ur());let _=r;r=l(u),r===_?~r&&c[r].p(u,f):(o&&(zr(),ve(c[_],1,1,()=>{c[_]=null}),Ur()),~r?(o=c[r],o?o.p(u,f):(o=c[r]=a[r](u),o.c()),he(o,1),o.m(n.parentNode,n)):o=null)},i(u){i||(he(s),he(o),i=!0)},o(u){ve(s),ve(o),i=!1},d(u){u&&(xe(t),xe(n)),s&&s.d(u),~r&&c[r].d(u)}}}function Vc(e){let t,r,o;function n(s){e[36](s)}let i={display:e[6]&&e[4],is_embed:e[4],info:!!e[8]&&e[7],version:e[1],initial_height:e[2],space:e[8],loaded:e[14]==="complete",$$slots:{default:[Uc]},$$scope:{ctx:e}};return e[9]!==void 0&&(i.wrapper=e[9]),t=new wl({props:i}),ft.push(()=>ct(t,"wrapper",n)),{c(){vt(t.$$.fragment)},m(s,a){wt(t,s,a),o=!0},p(s,a){const c={};a[0]&80&&(c.display=s[6]&&s[4]),a[0]&16&&(c.is_embed=s[4]),a[0]&384&&(c.info=!!s[8]&&s[7]),a[0]&2&&(c.version=s[1]),a[0]&4&&(c.initial_height=s[2]),a[0]&256&&(c.space=s[8]),a[0]&16384&&(c.loaded=s[14]==="complete"),a[0]&8388411|a[1]&1048576&&(c.$$scope={dirty:a,ctx:s}),!r&&a[0]&512&&(r=!0,c.wrapper=s[9],ut(()=>r=!1)),t.$set(c)},i(s){o||(he(t.$$.fragment,s),o=!0)},o(s){ve(t.$$.fragment,s),o=!1},d(s){yt(t,s)}}}let zc=-1;function qc(){const e=Ne({}),t=new Map,r=new IntersectionObserver(n=>{n.forEach(i=>{if(i.isIntersecting){let s=t.get(i.target);s!==void 0&&e.update(a=>({...a,[s]:!0}))}})});function o(n,i){t.set(i,n),r.observe(i)}return{register:o,subscribe:e.subscribe}}const Zr=qc();async function Xc(e){if(e){const t=new DOMParser,r=Array.from(t.parseFromString(e,"text/html").head.children);if(r)for(let o of r){let n=document.createElement(o.tagName);if(Array.from(o.attributes).forEach(i=>{n.setAttribute(i.name,i.value)}),n.textContent=o.textContent,n.tagName=="META"&&n.getAttribute("property")){const s=Array.from(document.head.getElementsByTagName("meta")??[]).find(a=>a.getAttribute("property")==n.getAttribute("property")&&!a.isEqualNode(n));if(s){document.head.replaceChild(n,s);continue}}document.head.appendChild(n)}}}function Zc(e,t,r){let o,n;Vr(e,xo,p=>r(22,o=p)),Vr(e,Zr,p=>r(32,n=p)),Ec();const i=Mc();let{autoscroll:s}=t,{version:a}=t,{initial_height:c}=t,{app_mode:l}=t,{is_embed:u}=t,{theme_mode:f="system"}=t,{control_page_title:_}=t,{container:d}=t,{info:b}=t,{eager:g}=t,S,{mount_css:P=fs}=t,{Client:m}=t,{worker_proxy:h=void 0}=t;h&&(Tc(h),h.addEventListener("progress-update",p=>{r(15,Ge=p.detail+"...")}));let{space:y}=t,{host:C}=t,{src:V}=t,W=zc++,me="pending",$,F=!1,Y=!1,O,Ge=o("common.loading")+"...",ne,ie,J=null;async function Fe(p){p&&(J=Kt(p,a,J||void 0)),await P(O.root+"/theme.css",document.head),O.stylesheets&&await Promise.all(O.stylesheets.map(j=>j.startsWith("http:")||j.startsWith("https:")?P(j,document.head):fetch(O.root+"/"+j).then(z=>z.text()).then(z=>{Kt(z,a)})))}function Et(p){const j=window.__gradio_mode__==="website";let I;if(j)I="light";else{const cs=new URL(window.location.toString()).searchParams.get("__theme");I=f||cs||"system"}return I==="dark"||I==="light"?Ue(p,I):I=Je(p),I}function Je(p){const j=I();window?.matchMedia("(prefers-color-scheme: dark)")?.addEventListener("change",I);function I(){let z=window?.matchMedia?.("(prefers-color-scheme: dark)").matches?"dark":"light";return Ue(p,z),z}return j}function Ue(p,j){const I=u?p.parentElement:document.body,z=u?p:p.parentElement;z.style.background="var(--body-background-fill)",j==="dark"?I.classList.add("dark"):I.classList.remove("dark")}let Q={message:"",load_status:"pending",status:"sleeping",detail:"SLEEPING"},se,Qe=!1;function Ke(p){r(13,Q=p)}const v=window.__GRADIO_DEV__;qr(async()=>{r(16,ne=Et($));const p=window.__GRADIO__SERVER_PORT__;if(ie=v==="dev"?`http://localhost:${typeof p=="number"?p:7860}`:C||y||V||location.origin,r(17,se=await m.connect(ie,{status_callback:Ke,with_null_state:!0,events:["data","log","status","render"]})),!se.config)throw new Error("Could not resolve app config");r(12,O=se.config),window.__gradio_space__=O.space_id,r(13,Q={message:"",load_status:"complete",status:"running",detail:"RUNNING"}),await Fe(O.css),await Xc(O.head),r(18,Qe=!0),window.__is_colab__=O.is_colab,i("loaded"),O.dev_mode&&setTimeout(()=>{const{host:j}=new URL(ie);let I=new URL(`http://${j}/dev/reload`);S=new EventSource(I),S.addEventListener("error",async z=>{et("Error reloading app","error"),console.error(JSON.parse(z.data))}),S.addEventListener("reload",async z=>{if(se.close(),r(17,se=await m.connect(ie,{status_callback:Ke,events:["data","log","status","render"]})),!se.config)throw new Error("Could not resolve app config");r(12,O=se.config),window.__gradio_space__=O.space_id,await Fe(O.css)})},200)});let Jt,Qt;async function rs(){r(19,Jt=(await er(()=>import("./Blocks-aR9ucLZz.js").then(p=>p.B),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default)}async function os(){r(20,Qt=(await er(()=>import("./Login-D5t5pW6w.js"),__vite__mapDeps([6,7,8,9,10,11,12,13,3,1,2,4,14,15,16,17,18]),import.meta.url)).default)}function ns(){O.auth_required?os():rs()}const is={readable_error:{NO_APP_FILE:o("errors.no_app_file"),CONFIG_ERROR:o("errors.config_error"),BUILD_ERROR:o("errors.build_error"),RUNTIME_ERROR:o("errors.runtime_error"),PAUSED:o("errors.space_paused")},title(p){return encodeURIComponent(o("errors.space_not_working"))},description(p,j){return encodeURIComponent(`Hello,

Firstly, thanks for creating this space!

I noticed that the space isn't working correctly because there is ${this.readable_error[p]||"an error"}.

It would be great if you could take a look at this because this space is being embedded on ${j}.

Thanks!`)}};let et;qr(async()=>{Zr.register(W,$)});function ss(p){F=p,r(10,F)}function as(p){Y=p,r(11,Y)}function ls(p){et=p,r(21,et)}function us(p){$=p,r(9,$)}return e.$$set=p=>{"autoscroll"in p&&r(0,s=p.autoscroll),"version"in p&&r(1,a=p.version),"initial_height"in p&&r(2,c=p.initial_height),"app_mode"in p&&r(3,l=p.app_mode),"is_embed"in p&&r(4,u=p.is_embed),"theme_mode"in p&&r(25,f=p.theme_mode),"control_page_title"in p&&r(5,_=p.control_page_title),"container"in p&&r(6,d=p.container),"info"in p&&r(7,b=p.info),"eager"in p&&r(26,g=p.eager),"mount_css"in p&&r(27,P=p.mount_css),"Client"in p&&r(28,m=p.Client),"worker_proxy"in p&&r(29,h=p.worker_proxy),"space"in p&&r(8,y=p.space),"host"in p&&r(30,C=p.host),"src"in p&&r(31,V=p.src)},e.$$.update=()=>{e.$$.dirty[0]&4096&&O?.app_id&&O.app_id,e.$$.dirty[0]&9216&&r(14,me=!F&&Q.load_status!=="error"?"pending":!F&&Q.load_status==="error"?"error":Q.load_status),e.$$.dirty[0]&67112960|e.$$.dirty[1]&2&&O&&(g||n[W])&&ns(),e.$$.dirty[0]&2560&&Y&&$.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0}))},[s,a,c,l,u,_,d,b,y,$,F,Y,O,Q,me,Ge,ne,se,Qe,Jt,Qt,et,o,v,is,f,g,P,m,h,C,V,n,ss,as,ls,us]}class Wc extends kc{constructor(t){super(),Bc(this,t,Zc,Vc,Ic,{autoscroll:0,version:1,initial_height:2,app_mode:3,is_embed:4,theme_mode:25,control_page_title:5,container:6,info:7,eager:26,mount_css:27,Client:28,worker_proxy:29,space:8,host:30,src:31},null,[-1,-1])}get autoscroll(){return this.$$.ctx[0]}set autoscroll(t){this.$$set({autoscroll:t}),N()}get version(){return this.$$.ctx[1]}set version(t){this.$$set({version:t}),N()}get initial_height(){return this.$$.ctx[2]}set initial_height(t){this.$$set({initial_height:t}),N()}get app_mode(){return this.$$.ctx[3]}set app_mode(t){this.$$set({app_mode:t}),N()}get is_embed(){return this.$$.ctx[4]}set is_embed(t){this.$$set({is_embed:t}),N()}get theme_mode(){return this.$$.ctx[25]}set theme_mode(t){this.$$set({theme_mode:t}),N()}get control_page_title(){return this.$$.ctx[5]}set control_page_title(t){this.$$set({control_page_title:t}),N()}get container(){return this.$$.ctx[6]}set container(t){this.$$set({container:t}),N()}get info(){return this.$$.ctx[7]}set info(t){this.$$set({info:t}),N()}get eager(){return this.$$.ctx[26]}set eager(t){this.$$set({eager:t}),N()}get mount_css(){return this.$$.ctx[27]}set mount_css(t){this.$$set({mount_css:t}),N()}get Client(){return this.$$.ctx[28]}set Client(t){this.$$set({Client:t}),N()}get worker_proxy(){return this.$$.ctx[29]}set worker_proxy(t){this.$$set({worker_proxy:t}),N()}get space(){return this.$$.ctx[8]}set space(t){this.$$set({space:t}),N()}get host(){return this.$$.ctx[30]}set host(t){this.$$set({host:t}),N()}get src(){return this.$$.ctx[31]}set src(t){this.$$set({src:t}),N()}}const nf=Object.freeze(Object.defineProperty({__proto__:null,default:Wc},Symbol.toStringTag,{value:"Module"}));export{xo as $,tu as C,Wl as I,Cl as L,xu as S,vs as a,rf as b,Kc as c,ds as d,Jc as e,Ec as f,of as g,ef as h,Yc as i,br as j,nf as k,tf as o,Qc as s,Ne as w};
//# sourceMappingURL=Index-WGC0_FkS.js.map
