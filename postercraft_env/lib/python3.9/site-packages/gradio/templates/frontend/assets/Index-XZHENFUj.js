import{T as W}from"./Textbox-BCZjJ4Bh.js";import{B as X}from"./Button-8nmImwVJ.js";import{S as Y}from"./Index-WGC0_FkS.js";import{default as Te}from"./Example-C7XUkkid.js";import"./BlockTitle-Bkh4EzYf.js";import"./Info-COHEyv9U.js";import"./Check-Ck0iADAu.js";import"./Copy-ZPOKSMtK.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";/* empty css                                              */const{SvelteComponent:Z,add_flush_callback:F,assign:x,bind:G,binding_callbacks:H,check_outros:y,create_component:v,destroy_component:k,detach:p,flush:_,get_spread_object:$,get_spread_update:ee,group_outros:te,init:le,insert:se,mount_component:B,safe_not_equal:ie,space:ne,transition_in:g,transition_out:m}=window.__gradio__svelte__internal;function J(s){let e,l;const n=[{autoscroll:s[2].autoscroll},{i18n:s[2].i18n},s[17]];let f={};for(let u=0;u<n.length;u+=1)f=x(f,n[u]);return e=new Y({props:f}),e.$on("clear_status",s[23]),{c(){v(e.$$.fragment)},m(u,a){B(e,u,a),l=!0},p(u,a){const r=a[0]&131076?ee(n,[a[0]&4&&{autoscroll:u[2].autoscroll},a[0]&4&&{i18n:u[2].i18n},a[0]&131072&&$(u[17])]):{};e.$set(r)},i(u){l||(g(e.$$.fragment,u),l=!0)},o(u){m(e.$$.fragment,u),l=!1},d(u){k(e,u)}}}function ue(s){let e,l,n,f,u,a=s[17]&&J(s);function r(i){s[24](i)}function b(i){s[25](i)}let c={label:s[3],info:s[4],show_label:s[10],lines:s[8],type:s[12],rtl:s[18],text_align:s[19],max_lines:s[11]?s[11]:s[8]+1,placeholder:s[9],show_copy_button:s[16],autofocus:s[20],container:s[13],autoscroll:s[21],disabled:!s[22]};return s[0]!==void 0&&(c.value=s[0]),s[1]!==void 0&&(c.value_is_output=s[1]),l=new W({props:c}),H.push(()=>G(l,"value",r)),H.push(()=>G(l,"value_is_output",b)),l.$on("change",s[26]),l.$on("input",s[27]),l.$on("submit",s[28]),l.$on("blur",s[29]),l.$on("select",s[30]),l.$on("focus",s[31]),{c(){a&&a.c(),e=ne(),v(l.$$.fragment)},m(i,o){a&&a.m(i,o),se(i,e,o),B(l,i,o),u=!0},p(i,o){i[17]?a?(a.p(i,o),o[0]&131072&&g(a,1)):(a=J(i),a.c(),g(a,1),a.m(e.parentNode,e)):a&&(te(),m(a,1,1,()=>{a=null}),y());const h={};o[0]&8&&(h.label=i[3]),o[0]&16&&(h.info=i[4]),o[0]&1024&&(h.show_label=i[10]),o[0]&256&&(h.lines=i[8]),o[0]&4096&&(h.type=i[12]),o[0]&262144&&(h.rtl=i[18]),o[0]&524288&&(h.text_align=i[19]),o[0]&2304&&(h.max_lines=i[11]?i[11]:i[8]+1),o[0]&512&&(h.placeholder=i[9]),o[0]&65536&&(h.show_copy_button=i[16]),o[0]&1048576&&(h.autofocus=i[20]),o[0]&8192&&(h.container=i[13]),o[0]&2097152&&(h.autoscroll=i[21]),o[0]&4194304&&(h.disabled=!i[22]),!n&&o[0]&1&&(n=!0,h.value=i[0],F(()=>n=!1)),!f&&o[0]&2&&(f=!0,h.value_is_output=i[1],F(()=>f=!1)),l.$set(h)},i(i){u||(g(a),g(l.$$.fragment,i),u=!0)},o(i){m(a),m(l.$$.fragment,i),u=!1},d(i){i&&p(e),a&&a.d(i),k(l,i)}}}function ae(s){let e,l;return e=new X({props:{visible:s[7],elem_id:s[5],elem_classes:s[6],scale:s[14],min_width:s[15],allow_overflow:!1,padding:s[13],$$slots:{default:[ue]},$$scope:{ctx:s}}}),{c(){v(e.$$.fragment)},m(n,f){B(e,n,f),l=!0},p(n,f){const u={};f[0]&128&&(u.visible=n[7]),f[0]&32&&(u.elem_id=n[5]),f[0]&64&&(u.elem_classes=n[6]),f[0]&16384&&(u.scale=n[14]),f[0]&32768&&(u.min_width=n[15]),f[0]&8192&&(u.padding=n[13]),f[0]&8339231|f[1]&2&&(u.$$scope={dirty:f,ctx:n}),e.$set(u)},i(n){l||(g(e.$$.fragment,n),l=!0)},o(n){m(e.$$.fragment,n),l=!1},d(n){k(e,n)}}}function oe(s,e,l){let{gradio:n}=e,{label:f="Textbox"}=e,{info:u=void 0}=e,{elem_id:a=""}=e,{elem_classes:r=[]}=e,{visible:b=!0}=e,{value:c=""}=e,{lines:i}=e,{placeholder:o=""}=e,{show_label:h}=e,{max_lines:T}=e,{type:S="text"}=e,{container:j=!0}=e,{scale:q=null}=e,{min_width:C=void 0}=e,{show_copy_button:E=!1}=e,{loading_status:w=void 0}=e,{value_is_output:d=!1}=e,{rtl:I=!1}=e,{text_align:N=void 0}=e,{autofocus:z=!1}=e,{autoscroll:A=!0}=e,{interactive:D}=e;const K=()=>n.dispatch("clear_status",w);function L(t){c=t,l(0,c)}function M(t){d=t,l(1,d)}const O=()=>n.dispatch("change",c),P=()=>n.dispatch("input"),Q=()=>n.dispatch("submit"),R=()=>n.dispatch("blur"),U=t=>n.dispatch("select",t.detail),V=()=>n.dispatch("focus");return s.$$set=t=>{"gradio"in t&&l(2,n=t.gradio),"label"in t&&l(3,f=t.label),"info"in t&&l(4,u=t.info),"elem_id"in t&&l(5,a=t.elem_id),"elem_classes"in t&&l(6,r=t.elem_classes),"visible"in t&&l(7,b=t.visible),"value"in t&&l(0,c=t.value),"lines"in t&&l(8,i=t.lines),"placeholder"in t&&l(9,o=t.placeholder),"show_label"in t&&l(10,h=t.show_label),"max_lines"in t&&l(11,T=t.max_lines),"type"in t&&l(12,S=t.type),"container"in t&&l(13,j=t.container),"scale"in t&&l(14,q=t.scale),"min_width"in t&&l(15,C=t.min_width),"show_copy_button"in t&&l(16,E=t.show_copy_button),"loading_status"in t&&l(17,w=t.loading_status),"value_is_output"in t&&l(1,d=t.value_is_output),"rtl"in t&&l(18,I=t.rtl),"text_align"in t&&l(19,N=t.text_align),"autofocus"in t&&l(20,z=t.autofocus),"autoscroll"in t&&l(21,A=t.autoscroll),"interactive"in t&&l(22,D=t.interactive)},[c,d,n,f,u,a,r,b,i,o,h,T,S,j,q,C,E,w,I,N,z,A,D,K,L,M,O,P,Q,R,U,V]}class ve extends Z{constructor(e){super(),le(this,e,oe,ae,ie,{gradio:2,label:3,info:4,elem_id:5,elem_classes:6,visible:7,value:0,lines:8,placeholder:9,show_label:10,max_lines:11,type:12,container:13,scale:14,min_width:15,show_copy_button:16,loading_status:17,value_is_output:1,rtl:18,text_align:19,autofocus:20,autoscroll:21,interactive:22},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),_()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),_()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),_()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),_()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),_()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get lines(){return this.$$.ctx[8]}set lines(e){this.$$set({lines:e}),_()}get placeholder(){return this.$$.ctx[9]}set placeholder(e){this.$$set({placeholder:e}),_()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),_()}get max_lines(){return this.$$.ctx[11]}set max_lines(e){this.$$set({max_lines:e}),_()}get type(){return this.$$.ctx[12]}set type(e){this.$$set({type:e}),_()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),_()}get scale(){return this.$$.ctx[14]}set scale(e){this.$$set({scale:e}),_()}get min_width(){return this.$$.ctx[15]}set min_width(e){this.$$set({min_width:e}),_()}get show_copy_button(){return this.$$.ctx[16]}set show_copy_button(e){this.$$set({show_copy_button:e}),_()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),_()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),_()}get rtl(){return this.$$.ctx[18]}set rtl(e){this.$$set({rtl:e}),_()}get text_align(){return this.$$.ctx[19]}set text_align(e){this.$$set({text_align:e}),_()}get autofocus(){return this.$$.ctx[20]}set autofocus(e){this.$$set({autofocus:e}),_()}get autoscroll(){return this.$$.ctx[21]}set autoscroll(e){this.$$set({autoscroll:e}),_()}get interactive(){return this.$$.ctx[22]}set interactive(e){this.$$set({interactive:e}),_()}}export{Te as BaseExample,W as BaseTextbox,ve as default};
//# sourceMappingURL=Index-XZHENFUj.js.map
