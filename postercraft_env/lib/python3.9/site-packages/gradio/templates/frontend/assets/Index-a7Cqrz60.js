import{P as I}from"./prism-python-DQB1-hGx.js";import"./Index-WGC0_FkS.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";(function(a){a.languages.typescript=a.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),a.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete a.languages.typescript.parameter,delete a.languages.typescript["literal-property"];var e=a.languages.extend("typescript",{});delete e["class-name"],a.languages.typescript["class-name"].inside=e,a.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e}}}}),a.languages.ts=a.languages.typescript})(Prism);const{HtmlTag:G,SvelteComponent:K,append:d,attr:m,binding_callbacks:Q,destroy_block:U,detach:y,element:h,empty:j,ensure_array_like:L,flush:N,init:W,insert:b,listen:X,noop:T,safe_not_equal:Y,set_data:J,set_style:P,space:$,text:q,toggle_class:F,update_keyed_each:x}=window.__gradio__svelte__internal;function B(a,e,n){const t=a.slice();return t[10]=e[n].type,t[11]=e[n].description,t[12]=e[n].default,t[13]=e[n].name,t[14]=e,t[15]=n,t}function E(a){let e=[],n=new Map,t,l=L(a[1]);const i=s=>s[13];for(let s=0;s<l.length;s+=1){let o=B(a,l,s),f=i(o);n.set(f,e[s]=V(f,o))}return{c(){for(let s=0;s<e.length;s+=1)e[s].c();t=j()},m(s,o){for(let f=0;f<e.length;f+=1)e[f]&&e[f].m(s,o);b(s,t,o)},p(s,o){o&15&&(l=L(s[1]),e=x(e,o,i,1,s,l,n,t.parentNode,U,V,t,B))},d(s){s&&y(t);for(let o=0;o<e.length;o+=1)e[o].d(s)}}}function H(a){let e,n,t=a[10]+"",l;return{c(){e=q(": "),n=new G(!1),l=j(),n.a=l},m(i,s){b(i,e,s),n.m(t,i,s),b(i,l,s)},p(i,s){s&2&&t!==(t=i[10]+"")&&n.p(t)},d(i){i&&(y(e),y(l),n.d())}}}function M(a){let e,n,t=a[12]&&O(a),l=a[11]&&R(a);return{c(){t&&t.c(),e=$(),l&&l.c(),n=j()},m(i,s){t&&t.m(i,s),b(i,e,s),l&&l.m(i,s),b(i,n,s)},p(i,s){i[12]?t?t.p(i,s):(t=O(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),i[11]?l?l.p(i,s):(l=R(i),l.c(),l.m(n.parentNode,n)):l&&(l.d(1),l=null)},d(i){i&&(y(e),y(n)),t&&t.d(i),l&&l.d(i)}}}function O(a){let e,n,t,l,i,s,o=a[12]+"";return{c(){e=h("div"),n=h("span"),n.textContent="default",t=$(),l=h("code"),i=q("= "),s=new G(!1),m(n,"class","svelte-1n1otz8"),P(n,"padding-right","4px"),s.a=null,m(l,"class","svelte-1n1otz8"),m(e,"class","default svelte-1n1otz8"),F(e,"last",!a[11])},m(f,_){b(f,e,_),d(e,n),d(e,t),d(e,l),d(l,i),s.m(o,l)},p(f,_){_&2&&o!==(o=f[12]+"")&&s.p(o),_&2&&F(e,"last",!f[11])},d(f){f&&y(e)}}}function R(a){let e,n,t=a[11]+"",l;return{c(){e=h("div"),n=h("p"),l=q(t),m(e,"class","description svelte-1n1otz8")},m(i,s){b(i,e,s),d(e,n),d(n,l)},p(i,s){s&2&&t!==(t=i[11]+"")&&J(l,t)},d(i){i&&y(e)}}}function V(a,e){let n,t,l,i,s=e[13]+"",o,f=e[15],_,w,p,C,r,g,v,u=e[10]&&H(e);const S=()=>e[6](i,f),z=()=>e[6](null,f);function Z(){return e[7](e[15])}let c=e[3][e[15]]&&M(e);return{key:a,first:null,c(){n=h("div"),t=h("div"),l=h("pre"),i=h("code"),o=q(s),u&&u.c(),w=$(),p=h("button"),p.textContent="▲",C=$(),c&&c.c(),r=$(),m(i,"class","svelte-1n1otz8"),m(l,"class",_="language-"+e[0]+" svelte-1n1otz8"),m(p,"class","arrow svelte-1n1otz8"),F(p,"disabled",!e[11]&&!e[12]),F(p,"hidden",!e[3][e[15]]),m(t,"class","type svelte-1n1otz8"),m(n,"class","param md svelte-1n1otz8"),F(n,"open",e[3][e[15]]),this.first=n},m(A,k){b(A,n,k),d(n,t),d(t,l),d(l,i),d(i,o),u&&u.m(i,null),S(),d(t,w),d(t,p),d(n,C),c&&c.m(n,null),d(n,r),g||(v=X(p,"click",Z),g=!0)},p(A,k){e=A,k&2&&s!==(s=e[13]+"")&&J(o,s),e[10]?u?u.p(e,k):(u=H(e),u.c(),u.m(i,null)):u&&(u.d(1),u=null),f!==e[15]&&(z(),f=e[15],S()),k&1&&_!==(_="language-"+e[0]+" svelte-1n1otz8")&&m(l,"class",_),k&2&&F(p,"disabled",!e[11]&&!e[12]),k&10&&F(p,"hidden",!e[3][e[15]]),e[3][e[15]]?c?c.p(e,k):(c=M(e),c.c(),c.m(n,r)):c&&(c.d(1),c=null),k&10&&F(n,"open",e[3][e[15]])},d(A){A&&y(n),u&&u.d(),z(),c&&c.d(),g=!1,v()}}}function ee(a){let e,n=a[1]&&E(a);return{c(){e=h("div"),n&&n.c(),m(e,"class","wrap svelte-1n1otz8")},m(t,l){b(t,e,l),n&&n.m(e,null)},p(t,[l]){t[1]?n?n.p(t,l):(n=E(t),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},i:T,o:T,d(t){t&&y(e),n&&n.d()}}}function te(a,e,n){let t,{docs:l}=e,{lang:i="python"}=e,{linkify:s=[]}=e,o;function f(r,g){let v=I.highlight(r,I.languages[g],g);for(const u of s)v=v.replace(new RegExp(u,"g"),`<a href="#h-${u.toLocaleLowerCase()}">${u}</a>`);return v}function _(r,g){return Object.entries(r).map(([v,{type:u,description:S,default:z}])=>{let Z=u?f(u,g):null;return{name:v,type:Z,description:S,default:z?f(z,g):null}})}let w=[];function p(r,g){Q[r?"unshift":"push"](()=>{w[g]=r,n(2,w)})}const C=r=>n(3,t[r]=!t[r],t);return a.$$set=r=>{"docs"in r&&n(4,l=r.docs),"lang"in r&&n(0,i=r.lang),"linkify"in r&&n(5,s=r.linkify)},a.$$.update=()=>{a.$$.dirty&17&&setTimeout(()=>{n(1,o=_(l,i))},0),a.$$.dirty&2&&n(3,t=o&&o.map(r=>!1))},[i,o,w,t,l,s,p,C]}class ne extends K{constructor(e){super(),W(this,e,te,ee,Y,{docs:4,lang:0,linkify:5})}get docs(){return this.$$.ctx[4]}set docs(e){this.$$set({docs:e}),N()}get lang(){return this.$$.ctx[0]}set lang(e){this.$$set({lang:e}),N()}get linkify(){return this.$$.ctx[5]}set linkify(e){this.$$set({linkify:e}),N()}}const{SvelteComponent:le,create_component:ie,destroy_component:se,flush:D,init:ae,mount_component:oe,safe_not_equal:fe,transition_in:re,transition_out:ue}=window.__gradio__svelte__internal;function ce(a){let e,n;return e=new ne({props:{docs:a[0],linkify:a[1]}}),{c(){ie(e.$$.fragment)},m(t,l){oe(e,t,l),n=!0},p(t,[l]){const i={};l&1&&(i.docs=t[0]),l&2&&(i.linkify=t[1]),e.$set(i)},i(t){n||(re(e.$$.fragment,t),n=!0)},o(t){ue(e.$$.fragment,t),n=!1},d(t){se(e,t)}}}function de(a,e,n){let{value:t}=e,{linkify:l=[]}=e;return a.$$set=i=>{"value"in i&&n(0,t=i.value),"linkify"in i&&n(1,l=i.linkify)},[t,l]}class he extends le{constructor(e){super(),ae(this,e,de,ce,fe,{value:0,linkify:1})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),D()}get linkify(){return this.$$.ctx[1]}set linkify(e){this.$$set({linkify:e}),D()}}export{he as default};
//# sourceMappingURL=Index-a7Cqrz60.js.map
