import{B as fe}from"./Button-8nmImwVJ.js";import{S as me}from"./Index-WGC0_FkS.js";import{B as he}from"./BlockLabel-CJsotHlk.js";import{E as ce}from"./Empty-Vuj7-ssy.js";import{I as ee}from"./Image-Bsh8Umrh.js";import"./index-COY1HN2y.js";import{r as W}from"./file-url-Bf0nK4ai.js";import"./svelte/svelte.js";const{SvelteComponent:ge,append:F,assign:de,attr:b,check_outros:be,create_component:j,destroy_component:C,destroy_each:le,detach:v,element:E,empty:ve,ensure_array_like:G,flush:d,get_spread_object:we,get_spread_update:ke,group_outros:Be,init:Ie,insert:w,listen:P,mount_component:L,noop:X,run_all:Me,safe_not_equal:Se,set_data:qe,set_style:Y,space:N,src_url_equal:H,text:ze,toggle_class:z,transition_in:B,transition_out:I}=window.__gradio__svelte__internal;function Z(t,e,n){const l=t.slice();return l[27]=e[n],l[29]=n,l}function p(t,e,n){const l=t.slice();return l[27]=e[n],l[29]=n,l}function Ee(t){let e,n,l,i,a,s,f=G(t[14]?t[14]?.annotations:[]),m=[];for(let u=0;u<f.length;u+=1)m[u]=y(p(t,f,u));let h=t[6]&&t[14]&&$(t);return{c(){e=E("div"),n=E("img"),i=N();for(let u=0;u<m.length;u+=1)m[u].c();a=N(),h&&h.c(),s=ve(),b(n,"class","base-image svelte-m3v3vb"),H(n.src,l=t[14]?t[14].image.url:null)||b(n,"src",l),b(n,"alt","the base file that is annotated"),z(n,"fit-height",t[7]),b(e,"class","image-container svelte-m3v3vb")},m(u,c){w(u,e,c),F(e,n),F(e,i);for(let _=0;_<m.length;_+=1)m[_]&&m[_].m(e,null);w(u,a,c),h&&h.m(u,c),w(u,s,c)},p(u,c){if(c[0]&16384&&!H(n.src,l=u[14]?u[14].image.url:null)&&b(n,"src",l),c[0]&128&&z(n,"fit-height",u[7]),c[0]&49680){f=G(u[14]?u[14]?.annotations:[]);let _;for(_=0;_<f.length;_+=1){const k=p(u,f,_);m[_]?m[_].p(k,c):(m[_]=y(k),m[_].c(),m[_].m(e,null))}for(;_<m.length;_+=1)m[_].d(1);m.length=f.length}u[6]&&u[14]?h?h.p(u,c):(h=$(u),h.c(),h.m(s.parentNode,s)):h&&(h.d(1),h=null)},i:X,o:X,d(u){u&&(v(e),v(a),v(s)),le(m,u),h&&h.d(u)}}}function Pe(t){let e,n;return e=new ce({props:{size:"large",unpadded_box:!0,$$slots:{default:[je]},$$scope:{ctx:t}}}),{c(){j(e.$$.fragment)},m(l,i){L(e,l,i),n=!0},p(l,i){const a={};i[1]&1&&(a.$$scope={dirty:i,ctx:l}),e.$set(a)},i(l){n||(B(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){C(e,l)}}}function y(t){let e,n,l,i;return{c(){e=E("img"),b(e,"alt",n="segmentation mask identifying "+t[4]+" within the uploaded file"),b(e,"class","mask fit-height svelte-m3v3vb"),H(e.src,l=t[27].image.url)||b(e,"src",l),b(e,"style",i=t[9]&&t[27].label in t[9]?null:`filter: hue-rotate(${Math.round(t[29]*360/t[14]?.annotations.length)}deg);`),z(e,"active",t[15]==t[27].label),z(e,"inactive",t[15]!=t[27].label&&t[15]!=null)},m(a,s){w(a,e,s)},p(a,s){s[0]&16&&n!==(n="segmentation mask identifying "+a[4]+" within the uploaded file")&&b(e,"alt",n),s[0]&16384&&!H(e.src,l=a[27].image.url)&&b(e,"src",l),s[0]&16896&&i!==(i=a[9]&&a[27].label in a[9]?null:`filter: hue-rotate(${Math.round(a[29]*360/a[14]?.annotations.length)}deg);`)&&b(e,"style",i),s[0]&49152&&z(e,"active",a[15]==a[27].label),s[0]&49152&&z(e,"inactive",a[15]!=a[27].label&&a[15]!=null)},d(a){a&&v(e)}}}function $(t){let e,n=G(t[14].annotations),l=[];for(let i=0;i<n.length;i+=1)l[i]=x(Z(t,n,i));return{c(){e=E("div");for(let i=0;i<l.length;i+=1)l[i].c();b(e,"class","legend svelte-m3v3vb")},m(i,a){w(i,e,a);for(let s=0;s<l.length;s+=1)l[s]&&l[s].m(e,null)},p(i,a){if(a[0]&475648){n=G(i[14].annotations);let s;for(s=0;s<n.length;s+=1){const f=Z(i,n,s);l[s]?l[s].p(f,a):(l[s]=x(f),l[s].c(),l[s].m(e,null))}for(;s<l.length;s+=1)l[s].d(1);l.length=n.length}},d(i){i&&v(e),le(l,i)}}}function x(t){let e,n=t[27].label+"",l,i,a,s;function f(){return t[22](t[27])}function m(){return t[23](t[27])}function h(){return t[26](t[29],t[27])}return{c(){e=E("button"),l=ze(n),i=N(),b(e,"class","legend-item svelte-m3v3vb"),Y(e,"background-color",t[9]&&t[27].label in t[9]?t[9][t[27].label]+"88":`hsla(${Math.round(t[29]*360/t[14].annotations.length)}, 100%, 50%, 0.3)`)},m(u,c){w(u,e,c),F(e,l),F(e,i),a||(s=[P(e,"mouseover",f),P(e,"focus",m),P(e,"mouseout",t[24]),P(e,"blur",t[25]),P(e,"click",h)],a=!0)},p(u,c){t=u,c[0]&16384&&n!==(n=t[27].label+"")&&qe(l,n),c[0]&16896&&Y(e,"background-color",t[9]&&t[27].label in t[9]?t[9][t[27].label]+"88":`hsla(${Math.round(t[29]*360/t[14].annotations.length)}, 100%, 50%, 0.3)`)},d(u){u&&v(e),a=!1,Me(s)}}}function je(t){let e,n;return e=new ee({}),{c(){j(e.$$.fragment)},m(l,i){L(e,l,i),n=!0},i(l){n||(B(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){C(e,l)}}}function Ce(t){let e,n,l,i,a,s,f,m;const h=[{autoscroll:t[3].autoscroll},{i18n:t[3].i18n},t[13]];let u={};for(let r=0;r<h.length;r+=1)u=de(u,h[r]);e=new me({props:u}),l=new he({props:{show_label:t[5],Icon:ee,label:t[4]||t[3].i18n("image.image")}});const c=[Pe,Ee],_=[];function k(r,g){return r[14]==null?0:1}return s=k(t),f=_[s]=c[s](t),{c(){j(e.$$.fragment),n=N(),j(l.$$.fragment),i=N(),a=E("div"),f.c(),b(a,"class","container svelte-m3v3vb")},m(r,g){L(e,r,g),w(r,n,g),L(l,r,g),w(r,i,g),w(r,a,g),_[s].m(a,null),m=!0},p(r,g){const A=g[0]&8200?ke(h,[g[0]&8&&{autoscroll:r[3].autoscroll},g[0]&8&&{i18n:r[3].i18n},g[0]&8192&&we(r[13])]):{};e.$set(A);const M={};g[0]&32&&(M.show_label=r[5]),g[0]&24&&(M.label=r[4]||r[3].i18n("image.image")),l.$set(M);let S=s;s=k(r),s===S?_[s].p(r,g):(Be(),I(_[S],1,1,()=>{_[S]=null}),be(),f=_[s],f?f.p(r,g):(f=_[s]=c[s](r),f.c()),B(f,1),f.m(a,null))},i(r){m||(B(e.$$.fragment,r),B(l.$$.fragment,r),B(f),m=!0)},o(r){I(e.$$.fragment,r),I(l.$$.fragment,r),I(f),m=!1},d(r){r&&(v(n),v(i),v(a)),C(e,r),C(l,r),_[s].d()}}}function Le(t){let e,n;return e=new fe({props:{visible:t[2],elem_id:t[0],elem_classes:t[1],padding:!1,height:t[7],width:t[8],allow_overflow:!1,container:t[10],scale:t[11],min_width:t[12],$$slots:{default:[Ce]},$$scope:{ctx:t}}}),{c(){j(e.$$.fragment)},m(l,i){L(e,l,i),n=!0},p(l,i){const a={};i[0]&4&&(a.visible=l[2]),i[0]&1&&(a.elem_id=l[0]),i[0]&2&&(a.elem_classes=l[1]),i[0]&128&&(a.height=l[7]),i[0]&256&&(a.width=l[8]),i[0]&1024&&(a.container=l[10]),i[0]&2048&&(a.scale=l[11]),i[0]&4096&&(a.min_width=l[12]),i[0]&58104|i[1]&1&&(a.$$scope={dirty:i,ctx:l}),e.$set(a)},i(l){n||(B(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){C(e,l)}}}function Ne(t,e,n){let{elem_id:l=""}=e,{elem_classes:i=[]}=e,{visible:a=!0}=e,{value:s=null}=e,f=null,m=null,{gradio:h}=e,{label:u=h.i18n("annotated_image.annotated_image")}=e,{show_label:c=!0}=e,{show_legend:_=!0}=e,{height:k}=e,{width:r}=e,{color_map:g}=e,{container:A=!0}=e,{scale:M=null}=e,{min_width:S=void 0}=e,J=null,{loading_status:T}=e,K=null;function O(o){n(15,J=o)}function Q(){n(15,J=null)}function U(o,D){h.dispatch("select",{value:u,index:o})}const te=o=>O(o.label),ne=o=>O(o.label),ie=()=>Q(),se=()=>Q(),ae=(o,D)=>U(o,D.label);return t.$$set=o=>{"elem_id"in o&&n(0,l=o.elem_id),"elem_classes"in o&&n(1,i=o.elem_classes),"visible"in o&&n(2,a=o.visible),"value"in o&&n(19,s=o.value),"gradio"in o&&n(3,h=o.gradio),"label"in o&&n(4,u=o.label),"show_label"in o&&n(5,c=o.show_label),"show_legend"in o&&n(6,_=o.show_legend),"height"in o&&n(7,k=o.height),"width"in o&&n(8,r=o.width),"color_map"in o&&n(9,g=o.color_map),"container"in o&&n(10,A=o.container),"scale"in o&&n(11,M=o.scale),"min_width"in o&&n(12,S=o.min_width),"loading_status"in o&&n(13,T=o.loading_status)},t.$$.update=()=>{if(t.$$.dirty[0]&3670024)if(s!==f&&(n(20,f=s),h.dispatch("change")),s){const o={image:s.image,annotations:s.annotations.map(q=>({image:q.image,label:q.label}))};n(14,m=o);const D=W(o.image.url),oe=Promise.all(o.annotations.map(q=>W(q.image.url))),R=Promise.all([D,oe]);n(21,K=R),R.then(([q,ue])=>{if(K!==R)return;const re={image:{...o.image,url:q??void 0},annotations:o.annotations.map((V,_e)=>({...V,image:{...V.image,url:ue[_e]??void 0}}))};n(14,m=re)})}else n(14,m=null)},[l,i,a,h,u,c,_,k,r,g,A,M,S,T,m,J,O,Q,U,s,f,K,te,ne,ie,se,ae]}class Qe extends ge{constructor(e){super(),Ie(this,e,Ne,Le,Se,{elem_id:0,elem_classes:1,visible:2,value:19,gradio:3,label:4,show_label:5,show_legend:6,height:7,width:8,color_map:9,container:10,scale:11,min_width:12,loading_status:13},null,[-1,-1])}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),d()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),d()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),d()}get value(){return this.$$.ctx[19]}set value(e){this.$$set({value:e}),d()}get gradio(){return this.$$.ctx[3]}set gradio(e){this.$$set({gradio:e}),d()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),d()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),d()}get show_legend(){return this.$$.ctx[6]}set show_legend(e){this.$$set({show_legend:e}),d()}get height(){return this.$$.ctx[7]}set height(e){this.$$set({height:e}),d()}get width(){return this.$$.ctx[8]}set width(e){this.$$set({width:e}),d()}get color_map(){return this.$$.ctx[9]}set color_map(e){this.$$set({color_map:e}),d()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),d()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),d()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),d()}get loading_status(){return this.$$.ctx[13]}set loading_status(e){this.$$set({loading_status:e}),d()}}export{Qe as default};
//# sourceMappingURL=Index-i_GKsICI.js.map
