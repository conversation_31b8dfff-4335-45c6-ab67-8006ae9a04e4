import{T as W}from"./Textbox-CF9-08gf.js";import{B as X}from"./Button-uOcat6Z0.js";import{S as Y}from"./Index-D21IHG0c.js";import{default as Se}from"./Example-C7XUkkid.js";import"./BlockTitle-COFLSASJ.js";import"./Info-CMIMfBX8.js";import"./Check-CZUQOzJl.js";import"./Copy-B6RcHnoK.js";/* empty css                                              */import"./index-D5ROCp7B.js";import"./svelte/svelte.js";/* empty css                                              */const{SvelteComponent:Z,add_flush_callback:F,assign:y,bind:G,binding_callbacks:H,check_outros:p,create_component:v,destroy_component:k,detach:$,flush:o,get_spread_object:ee,get_spread_update:te,group_outros:le,init:ie,insert:se,mount_component:x,safe_not_equal:ne,space:ue,transition_in:g,transition_out:m}=window.__gradio__svelte__internal;function J(i){let e,l;const n=[{autoscroll:i[2].autoscroll},{i18n:i[2].i18n},i[17]];let f={};for(let u=0;u<n.length;u+=1)f=y(f,n[u]);return e=new Y({props:f}),e.$on("clear_status",i[24]),{c(){v(e.$$.fragment)},m(u,a){x(e,u,a),l=!0},p(u,a){const r=a[0]&131076?te(n,[a[0]&4&&{autoscroll:u[2].autoscroll},a[0]&4&&{i18n:u[2].i18n},a[0]&131072&&ee(u[17])]):{};e.$set(r)},i(u){l||(g(e.$$.fragment,u),l=!0)},o(u){m(e.$$.fragment,u),l=!1},d(u){k(e,u)}}}function ae(i){let e,l,n,f,u,a=i[17]&&J(i);function r(s){i[25](s)}function b(s){i[26](s)}let c={label:i[3],info:i[4],show_label:i[10],lines:i[8],type:i[12],rtl:i[18],text_align:i[19],max_lines:i[11]?i[11]:i[8]+1,placeholder:i[9],show_copy_button:i[16],autofocus:i[20],container:i[13],autoscroll:i[21],max_length:i[23],disabled:!i[22]};return i[0]!==void 0&&(c.value=i[0]),i[1]!==void 0&&(c.value_is_output=i[1]),l=new W({props:c}),H.push(()=>G(l,"value",r)),H.push(()=>G(l,"value_is_output",b)),l.$on("change",i[27]),l.$on("input",i[28]),l.$on("submit",i[29]),l.$on("blur",i[30]),l.$on("select",i[31]),l.$on("focus",i[32]),{c(){a&&a.c(),e=ue(),v(l.$$.fragment)},m(s,_){a&&a.m(s,_),se(s,e,_),x(l,s,_),u=!0},p(s,_){s[17]?a?(a.p(s,_),_[0]&131072&&g(a,1)):(a=J(s),a.c(),g(a,1),a.m(e.parentNode,e)):a&&(le(),m(a,1,1,()=>{a=null}),p());const h={};_[0]&8&&(h.label=s[3]),_[0]&16&&(h.info=s[4]),_[0]&1024&&(h.show_label=s[10]),_[0]&256&&(h.lines=s[8]),_[0]&4096&&(h.type=s[12]),_[0]&262144&&(h.rtl=s[18]),_[0]&524288&&(h.text_align=s[19]),_[0]&2304&&(h.max_lines=s[11]?s[11]:s[8]+1),_[0]&512&&(h.placeholder=s[9]),_[0]&65536&&(h.show_copy_button=s[16]),_[0]&1048576&&(h.autofocus=s[20]),_[0]&8192&&(h.container=s[13]),_[0]&2097152&&(h.autoscroll=s[21]),_[0]&8388608&&(h.max_length=s[23]),_[0]&4194304&&(h.disabled=!s[22]),!n&&_[0]&1&&(n=!0,h.value=s[0],F(()=>n=!1)),!f&&_[0]&2&&(f=!0,h.value_is_output=s[1],F(()=>f=!1)),l.$set(h)},i(s){u||(g(a),g(l.$$.fragment,s),u=!0)},o(s){m(a),m(l.$$.fragment,s),u=!1},d(s){s&&$(e),a&&a.d(s),k(l,s)}}}function _e(i){let e,l;return e=new X({props:{visible:i[7],elem_id:i[5],elem_classes:i[6],scale:i[14],min_width:i[15],allow_overflow:!1,padding:i[13],$$slots:{default:[ae]},$$scope:{ctx:i}}}),{c(){v(e.$$.fragment)},m(n,f){x(e,n,f),l=!0},p(n,f){const u={};f[0]&128&&(u.visible=n[7]),f[0]&32&&(u.elem_id=n[5]),f[0]&64&&(u.elem_classes=n[6]),f[0]&16384&&(u.scale=n[14]),f[0]&32768&&(u.min_width=n[15]),f[0]&8192&&(u.padding=n[13]),f[0]&16727839|f[1]&4&&(u.$$scope={dirty:f,ctx:n}),e.$set(u)},i(n){l||(g(e.$$.fragment,n),l=!0)},o(n){m(e.$$.fragment,n),l=!1},d(n){k(e,n)}}}function oe(i,e,l){let{gradio:n}=e,{label:f="Textbox"}=e,{info:u=void 0}=e,{elem_id:a=""}=e,{elem_classes:r=[]}=e,{visible:b=!0}=e,{value:c=""}=e,{lines:s}=e,{placeholder:_=""}=e,{show_label:h}=e,{max_lines:B}=e,{type:T="text"}=e,{container:S=!0}=e,{scale:j=null}=e,{min_width:q=void 0}=e,{show_copy_button:C=!1}=e,{loading_status:w=void 0}=e,{value_is_output:d=!1}=e,{rtl:E=!1}=e,{text_align:I=void 0}=e,{autofocus:N=!1}=e,{autoscroll:z=!0}=e,{interactive:A}=e,{max_length:D=void 0}=e;const K=()=>n.dispatch("clear_status",w);function L(t){c=t,l(0,c)}function M(t){d=t,l(1,d)}const O=()=>n.dispatch("change",c),P=()=>n.dispatch("input"),Q=()=>n.dispatch("submit"),R=()=>n.dispatch("blur"),U=t=>n.dispatch("select",t.detail),V=()=>n.dispatch("focus");return i.$$set=t=>{"gradio"in t&&l(2,n=t.gradio),"label"in t&&l(3,f=t.label),"info"in t&&l(4,u=t.info),"elem_id"in t&&l(5,a=t.elem_id),"elem_classes"in t&&l(6,r=t.elem_classes),"visible"in t&&l(7,b=t.visible),"value"in t&&l(0,c=t.value),"lines"in t&&l(8,s=t.lines),"placeholder"in t&&l(9,_=t.placeholder),"show_label"in t&&l(10,h=t.show_label),"max_lines"in t&&l(11,B=t.max_lines),"type"in t&&l(12,T=t.type),"container"in t&&l(13,S=t.container),"scale"in t&&l(14,j=t.scale),"min_width"in t&&l(15,q=t.min_width),"show_copy_button"in t&&l(16,C=t.show_copy_button),"loading_status"in t&&l(17,w=t.loading_status),"value_is_output"in t&&l(1,d=t.value_is_output),"rtl"in t&&l(18,E=t.rtl),"text_align"in t&&l(19,I=t.text_align),"autofocus"in t&&l(20,N=t.autofocus),"autoscroll"in t&&l(21,z=t.autoscroll),"interactive"in t&&l(22,A=t.interactive),"max_length"in t&&l(23,D=t.max_length)},[c,d,n,f,u,a,r,b,s,_,h,B,T,S,j,q,C,w,E,I,N,z,A,D,K,L,M,O,P,Q,R,U,V]}class xe extends Z{constructor(e){super(),ie(this,e,oe,_e,ne,{gradio:2,label:3,info:4,elem_id:5,elem_classes:6,visible:7,value:0,lines:8,placeholder:9,show_label:10,max_lines:11,type:12,container:13,scale:14,min_width:15,show_copy_button:16,loading_status:17,value_is_output:1,rtl:18,text_align:19,autofocus:20,autoscroll:21,interactive:22,max_length:23},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),o()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),o()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),o()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),o()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),o()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),o()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),o()}get lines(){return this.$$.ctx[8]}set lines(e){this.$$set({lines:e}),o()}get placeholder(){return this.$$.ctx[9]}set placeholder(e){this.$$set({placeholder:e}),o()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),o()}get max_lines(){return this.$$.ctx[11]}set max_lines(e){this.$$set({max_lines:e}),o()}get type(){return this.$$.ctx[12]}set type(e){this.$$set({type:e}),o()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),o()}get scale(){return this.$$.ctx[14]}set scale(e){this.$$set({scale:e}),o()}get min_width(){return this.$$.ctx[15]}set min_width(e){this.$$set({min_width:e}),o()}get show_copy_button(){return this.$$.ctx[16]}set show_copy_button(e){this.$$set({show_copy_button:e}),o()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),o()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),o()}get rtl(){return this.$$.ctx[18]}set rtl(e){this.$$set({rtl:e}),o()}get text_align(){return this.$$.ctx[19]}set text_align(e){this.$$set({text_align:e}),o()}get autofocus(){return this.$$.ctx[20]}set autofocus(e){this.$$set({autofocus:e}),o()}get autoscroll(){return this.$$.ctx[21]}set autoscroll(e){this.$$set({autoscroll:e}),o()}get interactive(){return this.$$.ctx[22]}set interactive(e){this.$$set({interactive:e}),o()}get max_length(){return this.$$.ctx[23]}set max_length(e){this.$$set({max_length:e}),o()}}export{Se as BaseExample,W as BaseTextbox,xe as default};
//# sourceMappingURL=Index-q5DoCKkE.js.map
