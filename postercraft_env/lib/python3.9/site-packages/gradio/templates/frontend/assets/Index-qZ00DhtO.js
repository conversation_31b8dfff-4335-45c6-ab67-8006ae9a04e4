import{a as el,B as Hl}from"./Button-uOcat6Z0.js";import{S as Nl}from"./Index-D21IHG0c.js";import{d as ge}from"./index-CnqicUFC.js";import{c as Tl}from"./Blocks-Dw_9NR1K.js";import{U as jl}from"./Upload-46YxStuW.js";/* empty css                                                   */import"./index-D5ROCp7B.js";import"./Index.svelte_svelte_type_style_lang-D199jKmZ.js";import{M as Ol}from"./Example.svelte_svelte_type_style_lang-qahm4PWk.js";import{d as Fl}from"./dsv-DB8NKgIY.js";import{default as Ms}from"./Example-BT2jlY4j.js";import"./svelte/svelte.js";import"./Check-CZUQOzJl.js";import"./Copy-B6RcHnoK.js";import"./prism-python-Bw3EYPE-.js";const{HtmlTag:Pl,SvelteComponent:Ul,action_destroyer:Jl,attr:we,binding_callbacks:Wl,bubble:ft,check_outros:Il,create_component:Vl,destroy_component:Kl,detach:Fe,element:tl,empty:Yl,flush:oe,group_outros:Zl,init:Gl,insert:Pe,listen:Oe,mount_component:Ql,noop:Xe,prevent_default:Xl,run_all:ll,safe_not_equal:xl,set_data:$l,set_input_value:Ct,space:en,text:tn,toggle_class:xe,transition_in:_t,transition_out:ht}=window.__gradio__svelte__internal,{createEventDispatcher:ln}=window.__gradio__svelte__internal;function Dt(n){let e,t,l;return{c(){e=tl("input"),we(e,"role","textbox"),we(e,"tabindex","-1"),we(e,"class","svelte-z9gpua"),xe(e,"header",n[5])},m(s,o){Pe(s,e,o),n[19](e),Ct(e,n[11]),t||(l=[Oe(e,"input",n[20]),Oe(e,"blur",n[13]),Jl(n[12].call(null,e)),Oe(e,"keydown",n[18])],t=!0)},p(s,o){o&2048&&e.value!==s[11]&&Ct(e,s[11]),o&32&&xe(e,"header",s[5])},d(s){s&&Fe(e),n[19](null),t=!1,ll(l)}}}function nn(n){let e=(n[9]?n[0]:n[3]||n[0])+"",t;return{c(){t=tn(e)},m(l,s){Pe(l,t,s)},p(l,s){s&521&&e!==(e=(l[9]?l[0]:l[3]||l[0])+"")&&$l(t,e)},i:Xe,o:Xe,d(l){l&&Fe(t)}}}function sn(n){let e,t;return e=new Ol({props:{message:n[0].toLocaleString(),latex_delimiters:n[7],line_breaks:n[8],chatbot:!1,root:n[10]}}),{c(){Vl(e.$$.fragment)},m(l,s){Ql(e,l,s),t=!0},p(l,s){const o={};s&1&&(o.message=l[0].toLocaleString()),s&128&&(o.latex_delimiters=l[7]),s&256&&(o.line_breaks=l[8]),s&1024&&(o.root=l[10]),e.$set(o)},i(l){t||(_t(e.$$.fragment,l),t=!0)},o(l){ht(e.$$.fragment,l),t=!1},d(l){Kl(e,l)}}}function rn(n){let e,t;return{c(){e=new Pl(!1),t=Yl(),e.a=t},m(l,s){e.m(n[0],l,s),Pe(l,t,s)},p(l,s){s&1&&e.p(l[0])},i:Xe,o:Xe,d(l){l&&(Fe(t),e.d())}}}function an(n){let e,t,l,s,o,f,i,r=n[2]&&Dt(n);const c=[rn,sn,nn],u=[];function d(g,b){return g[6]==="html"?0:g[6]==="markdown"?1:2}return l=d(n),s=u[l]=c[l](n),{c(){r&&r.c(),e=en(),t=tl("span"),s.c(),we(t,"tabindex","-1"),we(t,"role","button"),we(t,"style",n[4]),we(t,"class","svelte-z9gpua"),xe(t,"edit",n[2])},m(g,b){r&&r.m(g,b),Pe(g,e,b),Pe(g,t,b),u[l].m(t,null),o=!0,f||(i=[Oe(t,"dblclick",n[16]),Oe(t,"focus",Xl(n[17]))],f=!0)},p(g,[b]){g[2]?r?r.p(g,b):(r=Dt(g),r.c(),r.m(e.parentNode,e)):r&&(r.d(1),r=null);let S=l;l=d(g),l===S?u[l].p(g,b):(Zl(),ht(u[S],1,1,()=>{u[S]=null}),Il(),s=u[l],s?s.p(g,b):(s=u[l]=c[l](g),s.c()),_t(s,1),s.m(t,null)),(!o||b&16)&&we(t,"style",g[4]),(!o||b&4)&&xe(t,"edit",g[2])},i(g){o||(_t(s),o=!0)},o(g){ht(s),o=!1},d(g){g&&(Fe(e),Fe(t)),r&&r.d(g),u[l].d(),f=!1,ll(i)}}}function on(n,e,t){let l,{edit:s}=e,{value:o=""}=e,{display_value:f=null}=e,{styling:i=""}=e,{header:r=!1}=e,{datatype:c="str"}=e,{latex_delimiters:u}=e,{clear_on_focus:d=!1}=e,{select_on_focus:g=!1}=e,{line_breaks:b=!0}=e,{editable:S=!0}=e,{root:D}=e;const L=ln();let{el:E}=e;function B(A){return d&&t(11,l=""),g&&A.select(),A.focus(),{}}function y({currentTarget:A}){t(0,o=A.value),L("blur")}function k(A){ft.call(this,n,A)}function H(A){ft.call(this,n,A)}function p(A){ft.call(this,n,A)}function C(A){Wl[A?"unshift":"push"](()=>{E=A,t(1,E)})}function q(){l=this.value,t(11,l),t(0,o)}return n.$$set=A=>{"edit"in A&&t(2,s=A.edit),"value"in A&&t(0,o=A.value),"display_value"in A&&t(3,f=A.display_value),"styling"in A&&t(4,i=A.styling),"header"in A&&t(5,r=A.header),"datatype"in A&&t(6,c=A.datatype),"latex_delimiters"in A&&t(7,u=A.latex_delimiters),"clear_on_focus"in A&&t(14,d=A.clear_on_focus),"select_on_focus"in A&&t(15,g=A.select_on_focus),"line_breaks"in A&&t(8,b=A.line_breaks),"editable"in A&&t(9,S=A.editable),"root"in A&&t(10,D=A.root),"el"in A&&t(1,E=A.el)},n.$$.update=()=>{n.$$.dirty&1&&t(11,l=o)},[o,E,s,f,i,r,c,u,b,S,D,l,B,y,d,g,k,H,p,C,q]}class tt extends Ul{constructor(e){super(),Gl(this,e,on,an,xl,{edit:2,value:0,display_value:3,styling:4,header:5,datatype:6,latex_delimiters:7,clear_on_focus:14,select_on_focus:15,line_breaks:8,editable:9,root:10,el:1})}get edit(){return this.$$.ctx[2]}set edit(e){this.$$set({edit:e}),oe()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),oe()}get display_value(){return this.$$.ctx[3]}set display_value(e){this.$$set({display_value:e}),oe()}get styling(){return this.$$.ctx[4]}set styling(e){this.$$set({styling:e}),oe()}get header(){return this.$$.ctx[5]}set header(e){this.$$set({header:e}),oe()}get datatype(){return this.$$.ctx[6]}set datatype(e){this.$$set({datatype:e}),oe()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),oe()}get clear_on_focus(){return this.$$.ctx[14]}set clear_on_focus(e){this.$$set({clear_on_focus:e}),oe()}get select_on_focus(){return this.$$.ctx[15]}set select_on_focus(e){this.$$set({select_on_focus:e}),oe()}get line_breaks(){return this.$$.ctx[8]}set line_breaks(e){this.$$set({line_breaks:e}),oe()}get editable(){return this.$$.ctx[9]}set editable(e){this.$$set({editable:e}),oe()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),oe()}get el(){return this.$$.ctx[1]}set el(e){this.$$set({el:e}),oe()}}const{ResizeObserverSingleton:un,SvelteComponent:fn,add_iframe_resize_listener:Lt,add_render_callback:Rt,append:Ee,attr:Ze,binding_callbacks:qt,check_outros:nl,create_slot:ct,detach:lt,element:Te,empty:sl,ensure_array_like:Bt,flush:De,get_all_dirty_from_scope:dt,get_slot_changes:gt,group_outros:il,init:_n,insert:nt,listen:hn,outro_and_destroy_block:cn,resize_observer_content_box:dn,safe_not_equal:gn,set_style:he,space:Et,text:bn,transition_in:Le,transition_out:He,update_keyed_each:mn,update_slot_base:bt}=window.__gradio__svelte__internal,{onMount:wn,tick:Ge}=window.__gradio__svelte__internal,kn=n=>({}),Ht=n=>({});function Nt(n,e,t){const l=n.slice();return l[36]=e[t],l}const vn=n=>({item:n[0]&2,index:n[0]&2}),Tt=n=>({item:n[36].data,index:n[36].index}),pn=n=>({}),jt=n=>({});function Ot(n){let e=[],t=new Map,l,s,o=Bt(n[1]);const f=i=>i[36].data[0].id;for(let i=0;i<o.length;i+=1){let r=Nt(n,o,i),c=f(r);t.set(c,e[i]=Ft(c,r))}return{c(){for(let i=0;i<e.length;i+=1)e[i].c();l=sl()},m(i,r){for(let c=0;c<e.length;c+=1)e[c]&&e[c].m(i,r);nt(i,l,r),s=!0},p(i,r){r[0]&1048578&&(o=Bt(i[1]),il(),e=mn(e,r,f,1,i,o,t,l.parentNode,cn,Ft,l,Nt),nl())},i(i){if(!s){for(let r=0;r<o.length;r+=1)Le(e[r]);s=!0}},o(i){for(let r=0;r<e.length;r+=1)He(e[r]);s=!1},d(i){i&&lt(l);for(let r=0;r<e.length;r+=1)e[r].d(i)}}}function yn(n){let e;return{c(){e=bn(`Missing Table Row
					`)},m(t,l){nt(t,e,l)},d(t){t&&lt(e)}}}function Ft(n,e){let t,l;const s=e[21].tbody,o=ct(s,e,e[20],Tt),f=o||yn();return{key:n,first:null,c(){t=sl(),f&&f.c(),this.first=t},m(i,r){nt(i,t,r),f&&f.m(i,r),l=!0},p(i,r){e=i,o&&o.p&&(!l||r[0]&1048578)&&bt(o,s,e,e[20],l?gt(s,e[20],r,vn):dt(e[20]),Tt)},i(i){l||(Le(f,i),l=!0)},o(i){He(f,i),l=!1},d(i){i&&lt(t),f&&f.d(i)}}}function An(n){let e,t,l,s,o,f,i,r,c,u,d,g,b;const S=n[21].thead,D=ct(S,n,n[20],jt);let L=n[1].length&&n[1][0].data.length&&Ot(n);const E=n[21].tfoot,B=ct(E,n,n[20],Ht);return{c(){e=Te("svelte-virtual-table-viewport"),t=Te("table"),l=Te("thead"),D&&D.c(),o=Et(),f=Te("tbody"),L&&L.c(),i=Et(),r=Te("tfoot"),B&&B.c(),Ze(l,"class","thead svelte-82jkx"),Rt(()=>n[22].call(l)),Ze(f,"class","tbody svelte-82jkx"),Ze(r,"class","tfoot svelte-82jkx"),Rt(()=>n[24].call(r)),Ze(t,"class","table svelte-82jkx"),he(t,"height",Sn),he(t,"--bw-svt-p-top",n[7]+"px"),he(t,"--bw-svt-p-bottom",n[3]+"px"),he(t,"--bw-svt-head-height",n[5]+"px"),he(t,"--bw-svt-foot-height",n[6]+"px"),he(t,"--bw-svt-avg-row-height",n[0]+"px")},m(y,k){nt(y,e,k),Ee(e,t),Ee(t,l),D&&D.m(l,null),s=Lt(l,n[22].bind(l)),Ee(t,o),Ee(t,f),L&&L.m(f,null),n[23](f),Ee(t,i),Ee(t,r),B&&B.m(r,null),c=Lt(r,n[24].bind(r)),n[25](t),u=dn.observe(t,n[26].bind(t)),d=!0,g||(b=hn(t,"scroll",n[9]),g=!0)},p(y,k){D&&D.p&&(!d||k[0]&1048576)&&bt(D,S,y,y[20],d?gt(S,y[20],k,pn):dt(y[20]),jt),y[1].length&&y[1][0].data.length?L?(L.p(y,k),k[0]&2&&Le(L,1)):(L=Ot(y),L.c(),Le(L,1),L.m(f,null)):L&&(il(),He(L,1,1,()=>{L=null}),nl()),B&&B.p&&(!d||k[0]&1048576)&&bt(B,E,y,y[20],d?gt(E,y[20],k,kn):dt(y[20]),Ht),(!d||k[0]&128)&&he(t,"--bw-svt-p-top",y[7]+"px"),(!d||k[0]&8)&&he(t,"--bw-svt-p-bottom",y[3]+"px"),(!d||k[0]&32)&&he(t,"--bw-svt-head-height",y[5]+"px"),(!d||k[0]&64)&&he(t,"--bw-svt-foot-height",y[6]+"px"),(!d||k[0]&1)&&he(t,"--bw-svt-avg-row-height",y[0]+"px")},i(y){d||(Le(D,y),Le(L),Le(B,y),d=!0)},o(y){He(D,y),He(L),He(B,y),d=!1},d(y){y&&lt(e),D&&D.d(y),s(),L&&L.d(),n[23](null),B&&B.d(y),c(),n[25](null),u(),g=!1,b()}}}let Sn="100%";function zn(n,e){if(!n)return 0;const t=getComputedStyle(n);return parseInt(t.getPropertyValue(e))}function Mn(n,e,t){let l,{$$slots:s={},$$scope:o}=e,{items:f=[]}=e,{max_height:i}=e,{actual_height:r}=e,{table_scrollbar_width:c}=e,{start:u=0}=e,{end:d=20}=e,{selected:g}=e,b=30,S=0,D,L=0,E=0,B=[],y,k,H=0,p,C=200,q=[],A;const fe=typeof window<"u",T=fe?window.requestAnimationFrame:z=>z();let te=0;async function P(z){if(C===0)return;const{scrollTop:V}=p;t(13,c=p.offsetWidth-p.clientWidth),te=H-(V-L);let ee=u;for(;te<i&&ee<z.length;){let j=k[ee-u];j||(t(11,d=ee+1),await Ge(),j=k[ee-u]);let x=j?.getBoundingClientRect().height;x||(x=b);const Be=B[ee]=x;te+=Be,ee+=1}t(11,d=ee);const ie=z.length-d,le=p.offsetHeight-p.clientHeight;le>0&&(te+=le);let re=B.filter(j=>typeof j=="number");t(0,b=re.reduce((j,x)=>j+x,0)/re.length),t(3,S=ie*b),B.length=z.length,await Ge(),i?te<i?t(12,r=te+2):t(12,r=i):t(12,r=te+1),await Ge()}async function ue(z){T(async()=>{if(typeof z!="number")return;const V=typeof z!="number"?!1:U(z);V!==!0&&(V==="back"&&await v(z,{behavior:"instant"}),V==="forwards"&&await v(z,{behavior:"instant"},!0))})}function U(z){const V=k&&k[z-u];if(!V&&z<u)return"back";if(!V&&z>=d-1)return"forwards";const{top:ee}=p.getBoundingClientRect(),{top:ie,bottom:le}=V.getBoundingClientRect();return ie-ee<37?"back":le-ee>C?"forwards":!0}async function m(z){const V=p.scrollTop;k=D.children;const ee=l.length<u,ie=zn(k[1],"border-top-width"),le=0;ee&&await v(l.length-1,{behavior:"auto"});let re=0;for(let O=0;O<k.length;O+=1)B[u+O]=k[O].getBoundingClientRect().height;let j=0,x=L+ie/2,Be=[];for(;j<l.length;){const O=B[j]||b;if(Be[j]=O,x+O+le>V){re=j,t(7,H=x-(L+ie/2));break}x+=O,j+=1}for(re=Math.max(0,re);j<l.length;){const O=B[j]||b;if(x+=O,j+=1,x>V+C)break}t(10,u=re),t(11,d=j);const de=l.length-d;d===0&&t(11,d=10),t(0,b=(x-L)/d);let Ie=de*b;for(;j<l.length;)j+=1,B[j]=b;t(3,S=Ie),isFinite(S)||t(3,S=2e5)}async function v(z,V,ee=!1){await Ge();const ie=b;let le=z*ie;ee&&(le=le-C+ie+L);const re=p.offsetHeight-p.clientHeight;re>0&&(le+=re);const j={top:le,behavior:"smooth",...V};p.scrollTo(j)}wn(()=>{k=D.children,t(18,y=!0),P(f)});function F(){L=this.offsetHeight,t(5,L)}function h(z){qt[z?"unshift":"push"](()=>{D=z,t(4,D)})}function Y(){E=this.offsetHeight,t(6,E)}function We(z){qt[z?"unshift":"push"](()=>{p=z,t(8,p)})}function st(){A=un.entries.get(this)?.contentRect,t(2,A)}return n.$$set=z=>{"items"in z&&t(14,f=z.items),"max_height"in z&&t(15,i=z.max_height),"actual_height"in z&&t(12,r=z.actual_height),"table_scrollbar_width"in z&&t(13,c=z.table_scrollbar_width),"start"in z&&t(10,u=z.start),"end"in z&&t(11,d=z.end),"selected"in z&&t(16,g=z.selected),"$$scope"in z&&t(20,o=z.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&4&&(C=A?.height||200),n.$$.dirty[0]&16384&&t(19,l=f),n.$$.dirty[0]&786432&&y&&T(()=>P(l)),n.$$.dirty[0]&65536&&ue(g),n.$$.dirty[0]&560129&&t(1,q=fe?l.slice(u,d).map((z,V)=>({index:V+u,data:z})):l.slice(0,i/l.length*b+1).map((z,V)=>({index:V+u,data:z}))),n.$$.dirty[0]&3&&t(12,r=q.length*b+10)},[b,q,A,S,D,L,E,H,p,m,u,d,r,c,f,i,g,v,y,l,o,s,F,h,Y,We,st]}class Cn extends fn{constructor(e){super(),_n(this,e,Mn,An,gn,{items:14,max_height:15,actual_height:12,table_scrollbar_width:13,start:10,end:11,selected:16,scroll_to_index:17},null,[-1,-1])}get items(){return this.$$.ctx[14]}set items(e){this.$$set({items:e}),De()}get max_height(){return this.$$.ctx[15]}set max_height(e){this.$$set({max_height:e}),De()}get actual_height(){return this.$$.ctx[12]}set actual_height(e){this.$$set({actual_height:e}),De()}get table_scrollbar_width(){return this.$$.ctx[13]}set table_scrollbar_width(e){this.$$set({table_scrollbar_width:e}),De()}get start(){return this.$$.ctx[10]}set start(e){this.$$set({start:e}),De()}get end(){return this.$$.ctx[11]}set end(e){this.$$set({end:e}),De()}get selected(){return this.$$.ctx[16]}set selected(e){this.$$set({selected:e}),De()}get scroll_to_index(){return this.$$.ctx[17]}}const{ResizeObserverSingleton:Dn,SvelteComponent:Ln,action_destroyer:Rn,add_flush_callback:pe,append:N,attr:w,bind:ye,binding_callbacks:ce,check_outros:Re,create_component:ze,destroy_component:Me,detach:G,element:W,empty:qn,ensure_array_like:ke,flush:X,group_outros:qe,init:Bn,insert:Q,listen:ve,mount_component:Ce,outro_and_destroy_block:$e,resize_observer_content_box:En,run_all:rl,safe_not_equal:Hn,set_data:Ue,set_style:Ae,space:se,svg_element:Se,text:Je,toggle_class:I,transition_in:Z,transition_out:$,update_keyed_each:et}=window.__gradio__svelte__internal,{createEventDispatcher:Nn,tick:Qe,onMount:Tn}=window.__gradio__svelte__internal;function Pt(n,e,t){const l=n.slice();return l[90]=e[t].value,l[91]=e[t].id,l[94]=e,l[95]=t,l}function Ut(n,e,t){const l=n.slice();return l[90]=e[t].value,l[91]=e[t].id,l[92]=e,l[93]=t,l}function Jt(n,e,t){const l=n.slice();return l[90]=e[t].value,l[91]=e[t].id,l[96]=e,l[93]=t,l}function Wt(n,e,t){const l=n.slice();return l[90]=e[t].value,l[91]=e[t].id,l[95]=t,l}function It(n){let e,t;return{c(){e=W("p"),t=Je(n[1]),w(e,"class","svelte-1oa6fve")},m(l,s){Q(l,e,s),N(e,t)},p(l,s){s[0]&2&&Ue(t,l[1])},d(l){l&&G(e)}}}function Vt(n){let e,t;return{c(){e=W("caption"),t=Je(n[1]),w(e,"class","sr-only")},m(l,s){Q(l,e,s),N(e,t)},p(l,s){s[0]&2&&Ue(t,l[1])},d(l){l&&G(e)}}}function Kt(n,e){let t,l,s,o,f,i,r,c,u,d,g;return s=new tt({props:{value:e[90],latex_delimiters:e[5],line_breaks:e[11],header:!0,edit:!1,el:null,root:e[8]}}),{key:n,first:null,c(){t=W("th"),l=W("div"),ze(s.$$.fragment),o=se(),f=W("div"),i=Se("svg"),r=Se("path"),u=se(),w(r,"d","M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z"),w(i,"width","1em"),w(i,"height","1em"),w(i,"viewBox","0 0 9 7"),w(i,"fill","none"),w(i,"xmlns","http://www.w3.org/2000/svg"),w(i,"class","svelte-1oa6fve"),w(f,"class",c="sort-button "+e[19]+" svelte-1oa6fve"),I(f,"sorted",e[20]===e[95]),I(f,"des",e[20]===e[95]&&e[19]==="des"),w(l,"class","cell-wrap svelte-1oa6fve"),w(t,"aria-sort",d=e[37](e[90],e[20],e[19])),w(t,"class","svelte-1oa6fve"),I(t,"editing",e[27]===e[95]),Ae(t,"width",e[12].length?e[12][e[95]]:void 0),this.first=t},m(b,S){Q(b,t,S),N(t,l),Ce(s,l,null),N(l,o),N(l,f),N(f,i),N(i,r),N(t,u),g=!0},p(b,S){e=b;const D={};S[0]&33554432&&(D.value=e[90]),S[0]&32&&(D.latex_delimiters=e[5]),S[0]&2048&&(D.line_breaks=e[11]),S[0]&256&&(D.root=e[8]),s.$set(D),(!g||S[0]&524288&&c!==(c="sort-button "+e[19]+" svelte-1oa6fve"))&&w(f,"class",c),(!g||S[0]&35127296)&&I(f,"sorted",e[20]===e[95]),(!g||S[0]&35127296)&&I(f,"des",e[20]===e[95]&&e[19]==="des"),(!g||S[0]&35127296&&d!==(d=e[37](e[90],e[20],e[19])))&&w(t,"aria-sort",d),(!g||S[0]&167772160)&&I(t,"editing",e[27]===e[95]),S[0]&33558528&&Ae(t,"width",e[12].length?e[12][e[95]]:void 0)},i(b){g||(Z(s.$$.fragment,b),g=!0)},o(b){$(s.$$.fragment,b),g=!1},d(b){b&&G(t),Me(s)}}}function Yt(n,e){let t,l,s,o,f=e[93],i;s=new tt({props:{value:e[90],latex_delimiters:e[5],line_breaks:e[11],datatype:Array.isArray(e[0])?e[0][e[93]]:e[0],edit:!1,el:null,root:e[8]}});const r=()=>e[54](t,f),c=()=>e[54](null,f);return{key:n,first:null,c(){t=W("td"),l=W("div"),ze(s.$$.fragment),o=se(),w(l,"class","cell-wrap svelte-1oa6fve"),w(t,"tabindex","-1"),w(t,"class","svelte-1oa6fve"),this.first=t},m(u,d){Q(u,t,d),N(t,l),Ce(s,l,null),N(t,o),r(),i=!0},p(u,d){e=u;const g={};d[1]&32&&(g.value=e[90]),d[0]&32&&(g.latex_delimiters=e[5]),d[0]&2048&&(g.line_breaks=e[11]),d[0]&1|d[1]&32&&(g.datatype=Array.isArray(e[0])?e[0][e[93]]:e[0]),d[0]&256&&(g.root=e[8]),s.$set(g),f!==e[93]&&(c(),f=e[93],r())},i(u){i||(Z(s.$$.fragment,u),i=!0)},o(u){$(s.$$.fragment,u),i=!1},d(u){u&&G(t),Me(s),c()}}}function Zt(n){let e,t;return{c(){e=W("caption"),t=Je(n[1]),w(e,"class","sr-only")},m(l,s){Q(l,e,s),N(e,t)},p(l,s){s[0]&2&&Ue(t,l[1])},d(l){l&&G(e)}}}function jn(n){let e,t=n[1]&&n[1].length!==0&&Zt(n);return{c(){t&&t.c(),e=qn()},m(l,s){t&&t.m(l,s),Q(l,e,s)},p(l,s){l[1]&&l[1].length!==0?t?t.p(l,s):(t=Zt(l),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(l){l&&G(e),t&&t.d(l)}}}function Gt(n,e){let t,l,s,o,f,i,r,c,u,d,g,b,S,D,L;function E(p){e[63](p,e[95])}function B(p){e[64](p,e[91])}function y(){return e[65](e[95])}let k={latex_delimiters:e[5],line_breaks:e[11],edit:e[27]===e[95],select_on_focus:e[28],header:!0,root:e[8]};e[25][e[95]].value!==void 0&&(k.value=e[25][e[95]].value),e[24][e[91]].input!==void 0&&(k.el=e[24][e[91]].input),s=new tt({props:k}),ce.push(()=>ye(s,"value",E)),ce.push(()=>ye(s,"el",B)),s.$on("keydown",e[43]),s.$on("dblclick",y);function H(){return e[66](e[95])}return{key:n,first:null,c(){t=W("th"),l=W("div"),ze(s.$$.fragment),i=se(),r=W("div"),c=Se("svg"),u=Se("path"),g=se(),w(u,"d","M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z"),w(c,"width","1em"),w(c,"height","1em"),w(c,"viewBox","0 0 9 7"),w(c,"fill","none"),w(c,"xmlns","http://www.w3.org/2000/svg"),w(c,"class","svelte-1oa6fve"),w(r,"class",d="sort-button "+e[19]+" svelte-1oa6fve"),I(r,"sorted",e[20]===e[95]),I(r,"des",e[20]===e[95]&&e[19]==="des"),w(l,"class","cell-wrap svelte-1oa6fve"),w(t,"aria-sort",b=e[37](e[90],e[20],e[19])),Ae(t,"width","var(--cell-width-"+e[95]+")"),w(t,"class","svelte-1oa6fve"),I(t,"focus",e[27]===e[95]||e[29]===e[95]),this.first=t},m(p,C){Q(p,t,C),N(t,l),Ce(s,l,null),N(l,i),N(l,r),N(r,c),N(c,u),N(t,g),S=!0,D||(L=ve(r,"click",H),D=!0)},p(p,C){e=p;const q={};C[0]&32&&(q.latex_delimiters=e[5]),C[0]&2048&&(q.line_breaks=e[11]),C[0]&167772160&&(q.edit=e[27]===e[95]),C[0]&268435456&&(q.select_on_focus=e[28]),C[0]&256&&(q.root=e[8]),!o&&C[0]&33554432&&(o=!0,q.value=e[25][e[95]].value,pe(()=>o=!1)),!f&&C[0]&50331648&&(f=!0,q.el=e[24][e[91]].input,pe(()=>f=!1)),s.$set(q),(!S||C[0]&524288&&d!==(d="sort-button "+e[19]+" svelte-1oa6fve"))&&w(r,"class",d),(!S||C[0]&35127296)&&I(r,"sorted",e[20]===e[95]),(!S||C[0]&35127296)&&I(r,"des",e[20]===e[95]&&e[19]==="des"),(!S||C[0]&35127296&&b!==(b=e[37](e[90],e[20],e[19])))&&w(t,"aria-sort",b),(!S||C[0]&33554432)&&Ae(t,"width","var(--cell-width-"+e[95]+")"),(!S||C[0]&704643072)&&I(t,"focus",e[27]===e[95]||e[29]===e[95])},i(p){S||(Z(s.$$.fragment,p),S=!0)},o(p){$(s.$$.fragment,p),S=!1},d(p){p&&G(t),Me(s),D=!1,L()}}}function On(n){let e,t=[],l=new Map,s,o=ke(n[25]);const f=i=>i[91];for(let i=0;i<o.length;i+=1){let r=Pt(n,o,i),c=f(r);l.set(c,t[i]=Gt(c,r))}return{c(){e=W("tr");for(let i=0;i<t.length;i+=1)t[i].c();w(e,"slot","thead"),w(e,"class","svelte-1oa6fve")},m(i,r){Q(i,e,r);for(let c=0;c<t.length;c+=1)t[c]&&t[c].m(e,null);s=!0},p(i,r){r[0]&991430944|r[1]&7232&&(o=ke(i[25]),qe(),t=et(t,r,f,1,i,o,l,e,$e,Gt,null,Pt),Re())},i(i){if(!s){for(let r=0;r<o.length;r+=1)Z(t[r]);s=!0}},o(i){for(let r=0;r<t.length;r+=1)$(t[r]);s=!1},d(i){i&&G(e);for(let r=0;r<t.length;r+=1)t[r].d()}}}function Qt(n,e){let t,l,s,o,f,i,r,c=`var(--cell-width-${e[93]})`,u,d,g;function b(y){e[57](y,e[88],e[93])}function S(y){e[58](y,e[91])}let D={display_value:e[15]?.[e[88]]?.[e[93]],latex_delimiters:e[5],line_breaks:e[11],editable:e[6],edit:ge(e[23],[e[88],e[93]]),datatype:Array.isArray(e[0])?e[0][e[93]]:e[0],clear_on_focus:e[26],root:e[8]};e[18][e[88]][e[93]].value!==void 0&&(D.value=e[18][e[88]][e[93]].value),e[24][e[91]].input!==void 0&&(D.el=e[24][e[91]].input),s=new tt({props:D}),ce.push(()=>ye(s,"value",b)),ce.push(()=>ye(s,"el",S)),s.$on("blur",e[59]);function L(){return e[60](e[88],e[93])}function E(){return e[61](e[88],e[93])}function B(){return e[62](e[88],e[93])}return{key:n,first:null,c(){t=W("td"),l=W("div"),ze(s.$$.fragment),i=se(),w(l,"class","cell-wrap svelte-1oa6fve"),w(t,"tabindex","0"),w(t,"style",r=e[16]?.[e[88]]?.[e[93]]||""),w(t,"class","svelte-1oa6fve"),I(t,"focus",ge(e[17],[e[88],e[93]])),Ae(t,"width",c),this.first=t},m(y,k){Q(y,t,k),N(t,l),Ce(s,l,null),N(t,i),u=!0,d||(g=[ve(t,"touchstart",L,{passive:!0}),ve(t,"click",E),ve(t,"dblclick",B)],d=!0)},p(y,k){e=y;const H={};k[0]&32768|k[2]&201326592&&(H.display_value=e[15]?.[e[88]]?.[e[93]]),k[0]&32&&(H.latex_delimiters=e[5]),k[0]&2048&&(H.line_breaks=e[11]),k[0]&64&&(H.editable=e[6]),k[0]&8388608|k[2]&201326592&&(H.edit=ge(e[23],[e[88],e[93]])),k[0]&1|k[2]&134217728&&(H.datatype=Array.isArray(e[0])?e[0][e[93]]:e[0]),k[0]&67108864&&(H.clear_on_focus=e[26]),k[0]&256&&(H.root=e[8]),!o&&k[0]&262144|k[2]&201326592&&(o=!0,H.value=e[18][e[88]][e[93]].value,pe(()=>o=!1)),!f&&k[0]&16777216|k[2]&134217728&&(f=!0,H.el=e[24][e[91]].input,pe(()=>f=!1)),s.$set(H),(!u||k[0]&65536|k[2]&201326592&&r!==(r=e[16]?.[e[88]]?.[e[93]]||""))&&w(t,"style",r),(!u||k[0]&131072|k[2]&201326592)&&I(t,"focus",ge(e[17],[e[88],e[93]]));const p=k[0]&65536|k[2]&201326592;(k[0]&65536|k[2]&201326592&&c!==(c=`var(--cell-width-${e[93]})`)||p)&&Ae(t,"width",c)},i(y){u||(Z(s.$$.fragment,y),u=!0)},o(y){$(s.$$.fragment,y),u=!1},d(y){y&&G(t),Me(s),d=!1,rl(g)}}}function Fn(n){let e,t=[],l=new Map,s,o=ke(n[89]);const f=i=>i[91];for(let i=0;i<o.length;i+=1){let r=Ut(n,o,i),c=f(r);l.set(c,t[i]=Qt(c,r))}return{c(){e=W("tr");for(let i=0;i<t.length;i+=1)t[i].c();w(e,"slot","tbody"),w(e,"class","svelte-1oa6fve"),I(e,"row_odd",n[88]%2===0)},m(i,r){Q(i,e,r);for(let c=0;c<t.length;c+=1)t[c]&&t[c].m(e,null);s=!0},p(i,r){r[0]&92768609|r[1]&641|r[2]&201326592&&(o=ke(i[89]),qe(),t=et(t,r,f,1,i,o,l,e,$e,Qt,null,Ut),Re()),(!s||r[2]&67108864)&&I(e,"row_odd",i[88]%2===0)},i(i){if(!s){for(let r=0;r<o.length;r+=1)Z(t[r]);s=!0}},o(i){for(let r=0;r<t.length;r+=1)$(t[r]);s=!1},d(i){i&&G(e);for(let r=0;r<t.length;r+=1)t[r].d()}}}function Pn(n){let e,t,l,s,o;function f(u){n[67](u)}function i(u){n[68](u)}function r(u){n[69](u)}let c={max_height:n[10],selected:n[35],$$slots:{tbody:[Fn,({index:u,item:d})=>({88:u,89:d}),({index:u,item:d})=>[0,0,(u?67108864:0)|(d?134217728:0)]],thead:[On],default:[jn]},$$scope:{ctx:n}};return n[18]!==void 0&&(c.items=n[18]),n[33]!==void 0&&(c.actual_height=n[33]),n[34]!==void 0&&(c.table_scrollbar_width=n[34]),e=new Cn({props:c}),ce.push(()=>ye(e,"items",f)),ce.push(()=>ye(e,"actual_height",i)),ce.push(()=>ye(e,"table_scrollbar_width",r)),{c(){ze(e.$$.fragment)},m(u,d){Ce(e,u,d),o=!0},p(u,d){const g={};d[0]&1024&&(g.max_height=u[10]),d[1]&16&&(g.selected=u[35]),d[0]&1067420003|d[1]&1|d[2]&201326592|d[3]&32&&(g.$$scope={dirty:d,ctx:u}),!t&&d[0]&262144&&(t=!0,g.items=u[18],pe(()=>t=!1)),!l&&d[1]&4&&(l=!0,g.actual_height=u[33],pe(()=>l=!1)),!s&&d[1]&8&&(s=!0,g.table_scrollbar_width=u[34],pe(()=>s=!1)),e.$set(g)},i(u){o||(Z(e.$$.fragment,u),o=!0)},o(u){$(e.$$.fragment,u),o=!1},d(u){Me(e,u)}}}function Xt(n){let e,t,l,s=n[4][1]==="dynamic"&&xt(n),o=n[3][1]==="dynamic"&&$t(n);return{c(){e=W("div"),s&&s.c(),t=se(),o&&o.c(),w(e,"class","controls-wrap svelte-1oa6fve")},m(f,i){Q(f,e,i),s&&s.m(e,null),N(e,t),o&&o.m(e,null),l=!0},p(f,i){f[4][1]==="dynamic"?s?(s.p(f,i),i[0]&16&&Z(s,1)):(s=xt(f),s.c(),Z(s,1),s.m(e,t)):s&&(qe(),$(s,1,1,()=>{s=null}),Re()),f[3][1]==="dynamic"?o?(o.p(f,i),i[0]&8&&Z(o,1)):(o=$t(f),o.c(),Z(o,1),o.m(e,null)):o&&(qe(),$(o,1,1,()=>{o=null}),Re())},i(f){l||(Z(s),Z(o),l=!0)},o(f){$(s),$(o),l=!1},d(f){f&&G(e),s&&s.d(),o&&o.d()}}}function xt(n){let e,t,l;return t=new el({props:{variant:"secondary",size:"sm",$$slots:{default:[Un]},$$scope:{ctx:n}}}),t.$on("click",n[74]),{c(){e=W("span"),ze(t.$$.fragment),w(e,"class","button-wrap svelte-1oa6fve")},m(s,o){Q(s,e,o),Ce(t,e,null),l=!0},p(s,o){const f={};o[0]&512|o[3]&32&&(f.$$scope={dirty:o,ctx:s}),t.$set(f)},i(s){l||(Z(t.$$.fragment,s),l=!0)},o(s){$(t.$$.fragment,s),l=!1},d(s){s&&G(e),Me(t)}}}function Un(n){let e,t,l,s=n[9]("dataframe.new_row")+"",o;return{c(){e=Se("svg"),t=Se("path"),l=se(),o=Je(s),w(t,"fill","currentColor"),w(t,"d","M24.59 16.59L17 24.17V4h-2v20.17l-7.59-7.58L6 18l10 10l10-10l-1.41-1.41z"),w(e,"xmlns","http://www.w3.org/2000/svg"),w(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),w(e,"aria-hidden","true"),w(e,"role","img"),w(e,"width","1em"),w(e,"height","1em"),w(e,"preserveAspectRatio","xMidYMid meet"),w(e,"viewBox","0 0 32 32"),w(e,"class","svelte-1oa6fve")},m(f,i){Q(f,e,i),N(e,t),Q(f,l,i),Q(f,o,i)},p(f,i){i[0]&512&&s!==(s=f[9]("dataframe.new_row")+"")&&Ue(o,s)},d(f){f&&(G(e),G(l),G(o))}}}function $t(n){let e,t,l;return t=new el({props:{variant:"secondary",size:"sm",$$slots:{default:[Jn]},$$scope:{ctx:n}}}),t.$on("click",n[75]),{c(){e=W("span"),ze(t.$$.fragment),w(e,"class","button-wrap svelte-1oa6fve")},m(s,o){Q(s,e,o),Ce(t,e,null),l=!0},p(s,o){const f={};o[0]&512|o[3]&32&&(f.$$scope={dirty:o,ctx:s}),t.$set(f)},i(s){l||(Z(t.$$.fragment,s),l=!0)},o(s){$(t.$$.fragment,s),l=!1},d(s){s&&G(e),Me(t)}}}function Jn(n){let e,t,l,s=n[9]("dataframe.new_column")+"",o;return{c(){e=Se("svg"),t=Se("path"),l=se(),o=Je(s),w(t,"fill","currentColor"),w(t,"d","m18 6l-1.43 1.393L24.15 15H4v2h20.15l-7.58 7.573L18 26l10-10L18 6z"),w(e,"xmlns","http://www.w3.org/2000/svg"),w(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),w(e,"aria-hidden","true"),w(e,"role","img"),w(e,"width","1em"),w(e,"height","1em"),w(e,"preserveAspectRatio","xMidYMid meet"),w(e,"viewBox","0 0 32 32"),w(e,"class","svelte-1oa6fve")},m(f,i){Q(f,e,i),N(e,t),Q(f,l,i),Q(f,o,i)},p(f,i){i[0]&512&&s!==(s=f[9]("dataframe.new_column")+"")&&Ue(o,s)},d(f){f&&(G(e),G(l),G(o))}}}function Wn(n){let e,t,l,s,o,f,i,r=[],c=new Map,u,d,g,b=[],S=new Map,D,L,E,B,y,k,H,p,C=n[1]&&n[1].length!==0&&n[2]&&It(n),q=n[1]&&n[1].length!==0&&Vt(n),A=ke(n[25]);const fe=m=>m[91];for(let m=0;m<A.length;m+=1){let v=Wt(n,A,m),F=fe(v);c.set(F,r[m]=Kt(F,v))}let T=ke(n[36]);const te=m=>m[91];for(let m=0;m<T.length;m+=1){let v=Jt(n,T,m),F=te(v);S.set(F,b[m]=Yt(F,v))}function P(m){n[70](m)}let ue={upload:n[13],stream_handler:n[14],flex:!1,center:!1,boundedheight:!1,disable_click:!0,root:n[8],$$slots:{default:[Pn]},$$scope:{ctx:n}};n[30]!==void 0&&(ue.dragging=n[30]),E=new jl({props:ue}),ce.push(()=>ye(E,"dragging",P)),E.$on("load",n[71]);let U=n[6]&&Xt(n);return{c(){e=W("div"),C&&C.c(),t=se(),l=W("div"),s=W("table"),q&&q.c(),o=se(),f=W("thead"),i=W("tr");for(let m=0;m<r.length;m+=1)r[m].c();u=se(),d=W("tbody"),g=W("tr");for(let m=0;m<b.length;m+=1)b[m].c();L=se(),ze(E.$$.fragment),y=se(),U&&U.c(),w(i,"class","svelte-1oa6fve"),w(f,"class","svelte-1oa6fve"),w(g,"class","svelte-1oa6fve"),w(s,"class","svelte-1oa6fve"),I(s,"fixed-layout",n[12].length!=0),w(l,"class","table-wrap svelte-1oa6fve"),Ae(l,"height",n[33]+"px"),w(l,"role","grid"),w(l,"tabindex","0"),I(l,"dragging",n[30]),I(l,"no-wrap",!n[7]),w(e,"class","svelte-1oa6fve"),I(e,"label",n[1]&&n[1].length!==0)},m(m,v){Q(m,e,v),C&&C.m(e,null),N(e,t),N(e,l),N(l,s),q&&q.m(s,null),N(s,o),N(s,f),N(f,i);for(let F=0;F<r.length;F+=1)r[F]&&r[F].m(i,null);N(s,u),N(s,d),N(d,g);for(let F=0;F<b.length;F+=1)b[F]&&b[F].m(g,null);D=En.observe(s,n[55].bind(s)),n[56](s),N(l,L),Ce(E,l,null),n[72](l),N(e,y),U&&U.m(e,null),k=!0,H||(p=[ve(window,"click",n[46]),ve(window,"touchstart",n[46]),ve(window,"resize",n[53]),ve(l,"keydown",n[73]),Rn(Tl.call(null,e))],H=!0)},p(m,v){m[1]&&m[1].length!==0&&m[2]?C?C.p(m,v):(C=It(m),C.c(),C.m(e,t)):C&&(C.d(1),C=null),m[1]&&m[1].length!==0?q?q.p(m,v):(q=Vt(m),q.c(),q.m(s,o)):q&&(q.d(1),q=null),v[0]&169351456|v[1]&64&&(A=ke(m[25]),qe(),r=et(r,v,fe,1,m,A,c,i,$e,Kt,null,Wt),Re()),v[0]&2099489|v[1]&32&&(T=ke(m[36]),qe(),b=et(b,v,te,1,m,T,S,g,$e,Yt,null,Jt),Re()),(!k||v[0]&4096)&&I(s,"fixed-layout",m[12].length!=0);const F={};v[0]&8192&&(F.upload=m[13]),v[0]&16384&&(F.stream_handler=m[14]),v[0]&256&&(F.root=m[8]),v[0]&1067421027|v[1]&29|v[3]&32&&(F.$$scope={dirty:v,ctx:m}),!B&&v[0]&1073741824&&(B=!0,F.dragging=m[30],pe(()=>B=!1)),E.$set(F),(!k||v[1]&4)&&Ae(l,"height",m[33]+"px"),(!k||v[0]&1073741824)&&I(l,"dragging",m[30]),(!k||v[0]&128)&&I(l,"no-wrap",!m[7]),m[6]?U?(U.p(m,v),v[0]&64&&Z(U,1)):(U=Xt(m),U.c(),Z(U,1),U.m(e,null)):U&&(qe(),$(U,1,1,()=>{U=null}),Re()),(!k||v[0]&2)&&I(e,"label",m[1]&&m[1].length!==0)},i(m){if(!k){for(let v=0;v<A.length;v+=1)Z(r[v]);for(let v=0;v<T.length;v+=1)Z(b[v]);Z(E.$$.fragment,m),Z(U),k=!0}},o(m){for(let v=0;v<r.length;v+=1)$(r[v]);for(let v=0;v<b.length;v+=1)$(b[v]);$(E.$$.fragment,m),$(U),k=!1},d(m){m&&G(e),C&&C.d(),q&&q.d();for(let v=0;v<r.length;v+=1)r[v].d();for(let v=0;v<b.length;v+=1)b[v].d();D(),n[56](null),Me(E),n[72](null),U&&U.d(),H=!1,rl(p)}}}function je(){return Math.random().toString(36).substring(2,15)}function In(n,e){return e.filter(t);function t(l){var s=-1;return n.split(`
`).every(o);function o(f){if(!f)return!0;var i=f.split(l).length;return s<0&&(s=i),s===i&&i>1}}}function Vn(n){const e=atob(n.split(",")[1]),t=n.split(",")[0].split(":")[1].split(";")[0],l=new ArrayBuffer(e.length),s=new Uint8Array(l);for(let o=0;o<e.length;o++)s[o]=e.charCodeAt(o);return new Blob([l],{type:t})}function Kn(n,e,t){let l,s,{datatype:o}=e,{label:f=null}=e,{show_label:i=!0}=e,{headers:r=[]}=e,{values:c=[]}=e,{col_count:u}=e,{row_count:d}=e,{latex_delimiters:g}=e,{editable:b=!0}=e,{wrap:S=!1}=e,{root:D}=e,{i18n:L}=e,{height:E=500}=e,{line_breaks:B=!0}=e,{column_widths:y=[]}=e,{upload:k}=e,{stream_handler:H}=e,p=!1,{display_value:C=null}=e,{styling:q=null}=e,A;const fe=Nn();let T=!1;const te=(a,_)=>h?.[a]?.[_]?.value;let P={};function ue(a){let _=a||[];if(u[1]==="fixed"&&_.length<u[0]){const M=Array(u[0]-_.length).fill("").map((R,J)=>`${J+_.length}`);_=_.concat(M)}return!_||_.length===0?Array(u[0]).fill(0).map((M,R)=>{const J=je();return t(24,P[J]={cell:null,input:null},P),{id:J,value:JSON.stringify(R+1)}}):_.map((M,R)=>{const J=je();return t(24,P[J]={cell:null,input:null},P),{id:J,value:M??""}})}function U(a){const _=a.length;return Array(d[1]==="fixed"||_<d[0]?d[0]:_).fill(0).map((M,R)=>Array(u[1]==="fixed"?u[0]:_>0?a[0].length:r.length).fill(0).map((J,ne)=>{const _e=je();return t(24,P[_e]=P[_e]||{input:null,cell:null},P),{value:a?.[R]?.[ne]??"",id:_e}}))}let m=ue(r),v;function F(){t(25,m=ue(r)),t(51,v=r.slice()),We()}let h=[[]],Y;async function We(){fe("change",{data:h.map(a=>a.map(({value:_})=>_)),headers:m.map(a=>a.value),metadata:b?null:{display_value:C,styling:q}})}function st(a,_,M){if(!_)return"none";if(r[_]===a){if(M==="asc")return"ascending";if(M==="des")return"descending"}return"none"}function z(a){return h.reduce((_,M,R)=>{const J=M.reduce((ne,_e,Ye)=>a===_e.id?Ye:ne,-1);return J===-1?_:[R,J]},[-1,-1])}async function V(a,_){!b||ge(T,[a,_])||t(23,T=[a,_])}function ee(a,_){const M={ArrowRight:[0,1],ArrowLeft:[0,-1],ArrowDown:[1,0],ArrowUp:[-1,0]}[a],R=_[0]+M[0],J=_[1]+M[1];if(R<0&&J<=0)t(29,O=J),t(17,p=!1);else{const ne=h[R]?.[J];t(17,p=ne?[R,J]:p)}}let ie=!1;async function le(a){if(O!==!1&&de===!1)switch(a.key){case"ArrowDown":t(17,p=[0,O]),t(29,O=!1);return;case"ArrowLeft":t(29,O=O>0?O-1:O);return;case"ArrowRight":t(29,O=O<m.length-1?O+1:O);return;case"Escape":a.preventDefault(),t(29,O=!1);break;case"Enter":a.preventDefault();break}if(!p)return;const[_,M]=p;switch(a.key){case"ArrowRight":case"ArrowLeft":case"ArrowDown":case"ArrowUp":if(T)break;a.preventDefault(),ee(a.key,[_,M]);break;case"Escape":if(!b)break;a.preventDefault(),t(23,T=!1);break;case"Enter":if(!b)break;a.preventDefault(),a.shiftKey?(rt(_),await Qe(),t(17,p=[_+1,M])):ge(T,[_,M])?(t(23,T=!1),await Qe(),t(17,p=[_,M])):t(23,T=[_,M]);break;case"Backspace":if(!b)break;T||(a.preventDefault(),t(18,h[_][M].value="",h));break;case"Delete":if(!b)break;T||(a.preventDefault(),t(18,h[_][M].value="",h));break;case"Tab":let R=a.shiftKey?-1:1,J=h[_][M+R],ne=h?.[_+R]?.[R>0?0:m.length-1];(J||ne)&&(a.preventDefault(),t(17,p=J?[_,M+R]:[_+R,R>0?0:m.length-1])),t(23,T=!1);break;default:if(!b)break;(!T||T&&ge(T,[_,M]))&&a.key.length===1&&(t(26,ie=!0),t(23,T=[_,M]))}}async function re(a,_){ge(T,[a,_])||(t(27,de=!1),t(29,O=!1),t(23,T=!1),t(17,p=[a,_]),await Qe(),ae.focus())}let j,x;function Be(a){typeof x!="number"||x!==a?(t(19,j="asc"),t(20,x=a)):j==="asc"?t(19,j="des"):j==="des"&&t(19,j="asc")}let de,Ie=!1,O=!1;async function it(a,_=!1){!b||u[1]!=="dynamic"||de===a||(t(17,p=!1),t(29,O=a),t(27,de=a),t(28,Ie=_))}function al(a){if(b)switch(a.key){case"Escape":case"Enter":case"Tab":a.preventDefault(),t(17,p=!1),t(29,O=de),t(27,de=!1),ae.focus();break}}async function rt(a){if(ae.focus(),d[1]==="dynamic"){if(h.length===0){t(50,c=[Array(r.length).fill("")]);return}h.splice(a?a+1:h.length,0,Array(h[0].length).fill(0).map((_,M)=>{const R=je();return t(24,P[R]={cell:null,input:null},P),{id:R,value:""}})),t(18,h),t(50,c),t(52,Y),t(17,p=[a?a+1:h.length-1,0])}}async function yt(){if(ae.focus(),u[1]==="dynamic"){for(let a=0;a<h.length;a++){const _=je();t(24,P[_]={cell:null,input:null},P),h[a].push({id:_,value:""})}r.push(`Header ${r.length+1}`),t(18,h),t(50,c),t(52,Y),t(49,r),await Qe(),requestAnimationFrame(()=>{it(r.length-1,!0);const a=ae.querySelectorAll("tbody")[1].offsetWidth;ae.querySelectorAll("table")[1].scrollTo({left:a})})}}function ol(a){a.stopImmediatePropagation();const[_]=a.composedPath();ae.contains(_)||(t(23,T=!1),t(27,de=!1),t(29,O=!1),t(17,p=!1))}function At(a){const _=new FileReader;function M(R){if(!R?.target?.result||typeof R.target.result!="string")return;const[J]=In(R.target.result,[",","	"]),[ne,..._e]=Fl(J).parseRows(R.target.result);t(25,m=ue(u[1]==="fixed"?ne.slice(0,u[0]):ne)),t(50,c=_e),_.removeEventListener("loadend",M)}_.addEventListener("loadend",M),_.readAsText(a)}let at=!1;function ul(a){let _=a[0].slice();for(let M=0;M<a.length;M++)for(let R=0;R<a[M].length;R++)`${_[R].value}`.length<`${a[M][R].value}`.length&&(_[R]=a[M][R]);return _}let Ne=[],ae,ot;function Ve(){const a=Ne.map((_,M)=>_?.clientWidth||0);if(a.length!==0)for(let _=0;_<a.length;_++)ae.style.setProperty(`--cell-width-${_}`,`${a[_]-Ke/a.length}px`)}let ut=c.slice(0,E/c.length*37).length*37+37,Ke=0;function fl(a,_,M,R,J){let ne=null;if(p&&p[0]in h&&p[1]in h[p[0]]&&(ne=h[p[0]][p[1]].id),typeof R!="number"||!J)return;const _e=[...Array(a.length).keys()];if(J==="asc")_e.sort((be,me)=>a[be][R].value<a[me][R].value?-1:1);else if(J==="des")_e.sort((be,me)=>a[be][R].value>a[me][R].value?-1:1);else return;const Ye=[...a],zt=_?[..._]:null,Mt=M?[...M]:null;if(_e.forEach((be,me)=>{a[me]=Ye[be],_&&zt&&(_[me]=zt[be]),M&&Mt&&(M[me]=Mt[be])}),t(18,h),t(50,c),t(52,Y),ne){const[be,me]=z(ne);t(17,p=[be,me])}}let St=!1;Tn(()=>{const a=new IntersectionObserver((_,M)=>{_.forEach(R=>{R.isIntersecting&&!St&&(Ve(),t(18,h),t(50,c),t(52,Y)),St=R.isIntersecting})});return a.observe(ae),()=>{a.disconnect()}});const _l=()=>Ve();function hl(a,_){ce[a?"unshift":"push"](()=>{Ne[_]=a,t(21,Ne)})}function cl(){A=Dn.entries.get(this)?.contentRect,t(22,A)}function dl(a){ce[a?"unshift":"push"](()=>{ot=a,t(32,ot)})}function gl(a,_,M){n.$$.not_equal(h[_][M].value,a)&&(h[_][M].value=a,t(18,h),t(50,c),t(52,Y))}function bl(a,_){n.$$.not_equal(P[_].input,a)&&(P[_].input=a,t(24,P))}const ml=()=>(t(26,ie=!1),ae.focus()),wl=(a,_)=>V(a,_),kl=(a,_)=>re(a,_),vl=(a,_)=>V(a,_);function pl(a,_){n.$$.not_equal(m[_].value,a)&&(m[_].value=a,t(25,m))}function yl(a,_){n.$$.not_equal(P[_].input,a)&&(P[_].input=a,t(24,P))}const Al=a=>it(a),Sl=a=>Be(a);function zl(a){h=a,t(18,h),t(50,c),t(52,Y)}function Ml(a){ut=a,t(33,ut)}function Cl(a){Ke=a,t(34,Ke)}function Dl(a){at=a,t(30,at)}const Ll=a=>At(Vn(a.detail.data));function Rl(a){ce[a?"unshift":"push"](()=>{ae=a,t(31,ae)})}const ql=a=>le(a),Bl=a=>(a.stopPropagation(),rt()),El=a=>(a.stopPropagation(),yt());return n.$$set=a=>{"datatype"in a&&t(0,o=a.datatype),"label"in a&&t(1,f=a.label),"show_label"in a&&t(2,i=a.show_label),"headers"in a&&t(49,r=a.headers),"values"in a&&t(50,c=a.values),"col_count"in a&&t(3,u=a.col_count),"row_count"in a&&t(4,d=a.row_count),"latex_delimiters"in a&&t(5,g=a.latex_delimiters),"editable"in a&&t(6,b=a.editable),"wrap"in a&&t(7,S=a.wrap),"root"in a&&t(8,D=a.root),"i18n"in a&&t(9,L=a.i18n),"height"in a&&t(10,E=a.height),"line_breaks"in a&&t(11,B=a.line_breaks),"column_widths"in a&&t(12,y=a.column_widths),"upload"in a&&t(13,k=a.upload),"stream_handler"in a&&t(14,H=a.stream_handler),"display_value"in a&&t(15,C=a.display_value),"styling"in a&&t(16,q=a.styling)},n.$$.update=()=>{if(n.$$.dirty[1]&2621440&&(ge(c,Y)||(t(18,h=U(c)),t(52,Y=c))),n.$$.dirty[0]&393216&&p!==!1){const[a,_]=p;!isNaN(a)&&!isNaN(_)&&fe("select",{index:[a,_],value:te(a,_),row_value:h[a].map(M=>M.value)})}n.$$.dirty[1]&1310720&&(ge(r,v)||F()),n.$$.dirty[0]&262144&&h&&We(),n.$$.dirty[0]&262144&&t(36,l=ul(h)),n.$$.dirty[0]&2097152&&Ne[0]&&Ve(),n.$$.dirty[0]&1933312&&fl(h,C,q,x,j),n.$$.dirty[0]&131072&&t(35,s=!!p&&p[0])},[o,f,i,u,d,g,b,S,D,L,E,B,y,k,H,C,q,p,h,j,x,Ne,A,T,P,m,ie,de,Ie,O,at,ae,ot,ut,Ke,s,l,st,V,le,re,Be,it,al,rt,yt,ol,At,Ve,r,c,v,Y,_l,hl,cl,dl,gl,bl,ml,wl,kl,vl,pl,yl,Al,Sl,zl,Ml,Cl,Dl,Ll,Rl,ql,Bl,El]}class Yn extends Ln{constructor(e){super(),Bn(this,e,Kn,Wn,Hn,{datatype:0,label:1,show_label:2,headers:49,values:50,col_count:3,row_count:4,latex_delimiters:5,editable:6,wrap:7,root:8,i18n:9,height:10,line_breaks:11,column_widths:12,upload:13,stream_handler:14,display_value:15,styling:16},null,[-1,-1,-1,-1])}get datatype(){return this.$$.ctx[0]}set datatype(e){this.$$set({datatype:e}),X()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),X()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),X()}get headers(){return this.$$.ctx[49]}set headers(e){this.$$set({headers:e}),X()}get values(){return this.$$.ctx[50]}set values(e){this.$$set({values:e}),X()}get col_count(){return this.$$.ctx[3]}set col_count(e){this.$$set({col_count:e}),X()}get row_count(){return this.$$.ctx[4]}set row_count(e){this.$$set({row_count:e}),X()}get latex_delimiters(){return this.$$.ctx[5]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),X()}get editable(){return this.$$.ctx[6]}set editable(e){this.$$set({editable:e}),X()}get wrap(){return this.$$.ctx[7]}set wrap(e){this.$$set({wrap:e}),X()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),X()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),X()}get height(){return this.$$.ctx[10]}set height(e){this.$$set({height:e}),X()}get line_breaks(){return this.$$.ctx[11]}set line_breaks(e){this.$$set({line_breaks:e}),X()}get column_widths(){return this.$$.ctx[12]}set column_widths(e){this.$$set({column_widths:e}),X()}get upload(){return this.$$.ctx[13]}set upload(e){this.$$set({upload:e}),X()}get stream_handler(){return this.$$.ctx[14]}set stream_handler(e){this.$$set({stream_handler:e}),X()}get display_value(){return this.$$.ctx[15]}set display_value(e){this.$$set({display_value:e}),X()}get styling(){return this.$$.ctx[16]}set styling(e){this.$$set({styling:e}),X()}}const Zn=Yn,{SvelteComponent:Gn,assign:Qn,create_component:mt,destroy_component:wt,detach:Xn,flush:K,get_spread_object:xn,get_spread_update:$n,init:es,insert:ts,mount_component:kt,safe_not_equal:ls,space:ns,transition_in:vt,transition_out:pt}=window.__gradio__svelte__internal,{afterUpdate:ss,tick:is}=window.__gradio__svelte__internal;function rs(n){let e,t,l,s;const o=[{autoscroll:n[14].autoscroll},{i18n:n[14].i18n},n[17]];let f={};for(let i=0;i<o.length;i+=1)f=Qn(f,o[i]);return e=new Nl({props:f}),e.$on("clear_status",n[28]),l=new Zn({props:{root:n[11],label:n[5],show_label:n[6],row_count:n[4],col_count:n[3],values:n[22],display_value:n[20],styling:n[21],headers:n[19],wrap:n[7],datatype:n[8],latex_delimiters:n[15],editable:n[18],height:n[16],i18n:n[14].i18n,line_breaks:n[12],column_widths:n[13],upload:n[14].client.upload,stream_handler:n[14].client.stream}}),l.$on("change",n[29]),l.$on("select",n[30]),{c(){mt(e.$$.fragment),t=ns(),mt(l.$$.fragment)},m(i,r){kt(e,i,r),ts(i,t,r),kt(l,i,r),s=!0},p(i,r){const c=r[0]&147456?$n(o,[r[0]&16384&&{autoscroll:i[14].autoscroll},r[0]&16384&&{i18n:i[14].i18n},r[0]&131072&&xn(i[17])]):{};e.$set(c);const u={};r[0]&2048&&(u.root=i[11]),r[0]&32&&(u.label=i[5]),r[0]&64&&(u.show_label=i[6]),r[0]&16&&(u.row_count=i[4]),r[0]&8&&(u.col_count=i[3]),r[0]&4194304&&(u.values=i[22]),r[0]&1048576&&(u.display_value=i[20]),r[0]&2097152&&(u.styling=i[21]),r[0]&524288&&(u.headers=i[19]),r[0]&128&&(u.wrap=i[7]),r[0]&256&&(u.datatype=i[8]),r[0]&32768&&(u.latex_delimiters=i[15]),r[0]&262144&&(u.editable=i[18]),r[0]&65536&&(u.height=i[16]),r[0]&16384&&(u.i18n=i[14].i18n),r[0]&4096&&(u.line_breaks=i[12]),r[0]&8192&&(u.column_widths=i[13]),r[0]&16384&&(u.upload=i[14].client.upload),r[0]&16384&&(u.stream_handler=i[14].client.stream),l.$set(u)},i(i){s||(vt(e.$$.fragment,i),vt(l.$$.fragment,i),s=!0)},o(i){pt(e.$$.fragment,i),pt(l.$$.fragment,i),s=!1},d(i){i&&Xn(t),wt(e,i),wt(l,i)}}}function as(n){let e,t;return e=new Hl({props:{visible:n[2],padding:!1,elem_id:n[0],elem_classes:n[1],container:!1,scale:n[9],min_width:n[10],allow_overflow:!1,$$slots:{default:[rs]},$$scope:{ctx:n}}}),{c(){mt(e.$$.fragment)},m(l,s){kt(e,l,s),t=!0},p(l,s){const o={};s[0]&4&&(o.visible=l[2]),s[0]&1&&(o.elem_id=l[0]),s[0]&2&&(o.elem_classes=l[1]),s[0]&512&&(o.scale=l[9]),s[0]&1024&&(o.min_width=l[10]),s[0]&8387064|s[1]&2&&(o.$$scope={dirty:s,ctx:l}),e.$set(o)},i(l){t||(vt(e.$$.fragment,l),t=!0)},o(l){pt(e.$$.fragment,l),t=!1},d(l){wt(e,l)}}}function os(n,e,t){let{headers:l=[]}=e,{elem_id:s=""}=e,{elem_classes:o=[]}=e,{visible:f=!0}=e,{value:i={data:[["","",""]],headers:["1","2","3"],metadata:null}}=e,r="",{value_is_output:c=!1}=e,{col_count:u}=e,{row_count:d}=e,{label:g=null}=e,{show_label:b=!0}=e,{wrap:S}=e,{datatype:D}=e,{scale:L=null}=e,{min_width:E=void 0}=e,{root:B}=e,{line_breaks:y=!0}=e,{column_widths:k=[]}=e,{gradio:H}=e,{latex_delimiters:p}=e,{height:C=void 0}=e,{loading_status:q}=e,{interactive:A}=e,fe,T,te,P;async function ue(h){let Y=h||i;t(19,fe=[...Y.headers||l]),t(22,P=Y.data?[...Y.data]:[]),t(20,T=Y?.metadata?.display_value?[...Y?.metadata?.display_value]:null),t(21,te=!A&&Y?.metadata?.styling?[...Y?.metadata?.styling]:null),await is(),H.dispatch("change"),c||H.dispatch("input")}ue(),ss(()=>{t(25,c=!1)}),(Array.isArray(i)&&i?.[0]?.length===0||i.data?.[0]?.length===0)&&(i={data:[Array(u?.[0]||3).fill("")],headers:Array(u?.[0]||3).fill("").map((h,Y)=>`${Y+1}`),metadata:null});async function U(h){JSON.stringify(h)!==r&&(t(24,i={...h}),t(27,r=JSON.stringify(i)),ue(h))}const m=()=>H.dispatch("clear_status",q),v=h=>U(h.detail),F=h=>H.dispatch("select",h.detail);return n.$$set=h=>{"headers"in h&&t(26,l=h.headers),"elem_id"in h&&t(0,s=h.elem_id),"elem_classes"in h&&t(1,o=h.elem_classes),"visible"in h&&t(2,f=h.visible),"value"in h&&t(24,i=h.value),"value_is_output"in h&&t(25,c=h.value_is_output),"col_count"in h&&t(3,u=h.col_count),"row_count"in h&&t(4,d=h.row_count),"label"in h&&t(5,g=h.label),"show_label"in h&&t(6,b=h.show_label),"wrap"in h&&t(7,S=h.wrap),"datatype"in h&&t(8,D=h.datatype),"scale"in h&&t(9,L=h.scale),"min_width"in h&&t(10,E=h.min_width),"root"in h&&t(11,B=h.root),"line_breaks"in h&&t(12,y=h.line_breaks),"column_widths"in h&&t(13,k=h.column_widths),"gradio"in h&&t(14,H=h.gradio),"latex_delimiters"in h&&t(15,p=h.latex_delimiters),"height"in h&&t(16,C=h.height),"loading_status"in h&&t(17,q=h.loading_status),"interactive"in h&&t(18,A=h.interactive)},n.$$.update=()=>{n.$$.dirty[0]&150994944&&r&&JSON.stringify(i)!==r&&(t(27,r=JSON.stringify(i)),ue())},[s,o,f,u,d,g,b,S,D,L,E,B,y,k,H,p,C,q,A,fe,T,te,P,U,i,c,l,r,m,v,F]}class As extends Gn{constructor(e){super(),es(this,e,os,as,ls,{headers:26,elem_id:0,elem_classes:1,visible:2,value:24,value_is_output:25,col_count:3,row_count:4,label:5,show_label:6,wrap:7,datatype:8,scale:9,min_width:10,root:11,line_breaks:12,column_widths:13,gradio:14,latex_delimiters:15,height:16,loading_status:17,interactive:18},null,[-1,-1])}get headers(){return this.$$.ctx[26]}set headers(e){this.$$set({headers:e}),K()}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),K()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),K()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),K()}get value(){return this.$$.ctx[24]}set value(e){this.$$set({value:e}),K()}get value_is_output(){return this.$$.ctx[25]}set value_is_output(e){this.$$set({value_is_output:e}),K()}get col_count(){return this.$$.ctx[3]}set col_count(e){this.$$set({col_count:e}),K()}get row_count(){return this.$$.ctx[4]}set row_count(e){this.$$set({row_count:e}),K()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),K()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),K()}get wrap(){return this.$$.ctx[7]}set wrap(e){this.$$set({wrap:e}),K()}get datatype(){return this.$$.ctx[8]}set datatype(e){this.$$set({datatype:e}),K()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),K()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),K()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),K()}get line_breaks(){return this.$$.ctx[12]}set line_breaks(e){this.$$set({line_breaks:e}),K()}get column_widths(){return this.$$.ctx[13]}set column_widths(e){this.$$set({column_widths:e}),K()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),K()}get latex_delimiters(){return this.$$.ctx[15]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),K()}get height(){return this.$$.ctx[16]}set height(e){this.$$set({height:e}),K()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),K()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),K()}}export{Zn as BaseDataFrame,Ms as BaseExample,As as default};
//# sourceMappingURL=Index-qZ00DhtO.js.map
