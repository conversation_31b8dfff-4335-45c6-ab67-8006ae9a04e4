{"version": 3, "file": "Index-qZ00DhtO.js", "sources": ["../../../../js/dataframe/shared/EditableCell.svelte", "../../../../js/dataframe/shared/VirtualTable.svelte", "../../../../js/dataframe/shared/Table.svelte", "../../../../js/dataframe/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { MarkdownCode } from \"@gradio/markdown\";\n\n\texport let edit: boolean;\n\texport let value: string | number = \"\";\n\texport let display_value: string | null = null;\n\texport let styling = \"\";\n\texport let header = false;\n\texport let datatype:\n\t\t| \"str\"\n\t\t| \"markdown\"\n\t\t| \"html\"\n\t\t| \"number\"\n\t\t| \"bool\"\n\t\t| \"date\" = \"str\";\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let clear_on_focus = false;\n\texport let select_on_focus = false;\n\texport let line_breaks = true;\n\texport let editable = true;\n\texport let root: string;\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let el: HTMLInputElement | null;\n\t$: _value = value;\n\n\tfunction use_focus(node: HTMLInputElement): any {\n\t\tif (clear_on_focus) {\n\t\t\t_value = \"\";\n\t\t}\n\t\tif (select_on_focus) {\n\t\t\tnode.select();\n\t\t}\n\n\t\tnode.focus();\n\n\t\treturn {};\n\t}\n\n\tfunction handle_blur({\n\t\tcurrentTarget\n\t}: Event & {\n\t\tcurrentTarget: HTMLInputElement;\n\t}): void {\n\t\tvalue = currentTarget.value;\n\t\tdispatch(\"blur\");\n\t}\n</script>\n\n{#if edit}\n\t<input\n\t\trole=\"textbox\"\n\t\tbind:this={el}\n\t\tbind:value={_value}\n\t\tclass:header\n\t\ttabindex=\"-1\"\n\t\ton:blur={handle_blur}\n\t\tuse:use_focus\n\t\ton:keydown\n\t/>\n{/if}\n\n<span\n\ton:dblclick\n\ttabindex=\"-1\"\n\trole=\"button\"\n\tclass:edit\n\ton:focus|preventDefault\n\tstyle={styling}\n>\n\t{#if datatype === \"html\"}\n\t\t{@html value}\n\t{:else if datatype === \"markdown\"}\n\t\t<MarkdownCode\n\t\t\tmessage={value.toLocaleString()}\n\t\t\t{latex_delimiters}\n\t\t\t{line_breaks}\n\t\t\tchatbot={false}\n\t\t\t{root}\n\t\t/>\n\t{:else}\n\t\t{editable ? value : display_value || value}\n\t{/if}\n</span>\n\n<style>\n\tinput {\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tbottom: var(--size-2);\n\t\tleft: var(--size-2);\n\t\tflex: 1 1 0%;\n\t\ttransform: translateX(-0.1px);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t}\n\n\tspan {\n\t\tflex: 1 1 0%;\n\t\toutline: none;\n\t\tpadding: var(--size-2);\n\t\t-webkit-user-select: text;\n\t\t-moz-user-select: text;\n\t\t-ms-user-select: text;\n\t\tuser-select: text;\n\t}\n\n\t.header {\n\t\ttransform: translateX(0);\n\t\tfont: var(--weight-bold);\n\t}\n\n\t.edit {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount, tick } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let items: any[][] = [];\n\n\texport let max_height: number;\n\texport let actual_height: number;\n\texport let table_scrollbar_width: number;\n\texport let start = 0;\n\texport let end = 20;\n\texport let selected: number | false;\n\tlet height = \"100%\";\n\n\tlet average_height = 30;\n\tlet bottom = 0;\n\tlet contents: HTMLTableSectionElement;\n\tlet head_height = 0;\n\tlet foot_height = 0;\n\tlet height_map: number[] = [];\n\tlet mounted: boolean;\n\tlet rows: HTMLCollectionOf<HTMLTableRowElement>;\n\tlet top = 0;\n\tlet viewport: HTMLTableElement;\n\tlet viewport_height = 200;\n\tlet visible: { index: number; data: any[] }[] = [];\n\tlet viewport_box: DOMRectReadOnly;\n\n\t$: viewport_height = viewport_box?.height || 200;\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tconst raf = is_browser\n\t\t? window.requestAnimationFrame\n\t\t: (cb: (...args: any[]) => void) => cb();\n\n\t$: mounted && raf(() => refresh_height_map(sortedItems));\n\n\tlet content_height = 0;\n\tasync function refresh_height_map(_items: typeof items): Promise<void> {\n\t\tif (viewport_height === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst { scrollTop } = viewport;\n\t\ttable_scrollbar_width = viewport.offsetWidth - viewport.clientWidth;\n\n\t\tcontent_height = top - (scrollTop - head_height);\n\t\tlet i = start;\n\n\t\twhile (content_height < max_height && i < _items.length) {\n\t\t\tlet row = rows[i - start];\n\t\t\tif (!row) {\n\t\t\t\tend = i + 1;\n\t\t\t\tawait tick(); // render the newly visible row\n\t\t\t\trow = rows[i - start];\n\t\t\t}\n\t\t\tlet _h = row?.getBoundingClientRect().height;\n\t\t\tif (!_h) {\n\t\t\t\t_h = average_height;\n\t\t\t}\n\t\t\tconst row_height = (height_map[i] = _h);\n\t\t\tcontent_height += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tend = i;\n\t\tconst remaining = _items.length - end;\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tcontent_height += scrollbar_height;\n\t\t}\n\n\t\tlet filtered_height_map = height_map.filter((v) => typeof v === \"number\");\n\t\taverage_height =\n\t\t\tfiltered_height_map.reduce((a, b) => a + b, 0) /\n\t\t\tfiltered_height_map.length;\n\n\t\tbottom = remaining * average_height;\n\t\theight_map.length = _items.length;\n\t\tawait tick();\n\t\tif (!max_height) {\n\t\t\tactual_height = content_height + 1;\n\t\t} else if (content_height < max_height) {\n\t\t\tactual_height = content_height + 2;\n\t\t} else {\n\t\t\tactual_height = max_height;\n\t\t}\n\n\t\tawait tick();\n\t}\n\n\t$: scroll_and_render(selected);\n\n\tasync function scroll_and_render(n: number | false): Promise<void> {\n\t\traf(async () => {\n\t\t\tif (typeof n !== \"number\") return;\n\t\t\tconst direction = typeof n !== \"number\" ? false : is_in_view(n);\n\t\t\tif (direction === true) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (direction === \"back\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" });\n\t\t\t}\n\n\t\t\tif (direction === \"forwards\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" }, true);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction is_in_view(n: number): \"back\" | \"forwards\" | true {\n\t\tconst current = rows && rows[n - start];\n\t\tif (!current && n < start) {\n\t\t\treturn \"back\";\n\t\t}\n\t\tif (!current && n >= end - 1) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\tconst { top: viewport_top } = viewport.getBoundingClientRect();\n\t\tconst { top, bottom } = current.getBoundingClientRect();\n\n\t\tif (top - viewport_top < 37) {\n\t\t\treturn \"back\";\n\t\t}\n\n\t\tif (bottom - viewport_top > viewport_height) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\treturn true;\n\t}\n\n\tfunction get_computed_px_amount(elem: HTMLElement, property: string): number {\n\t\tif (!elem) {\n\t\t\treturn 0;\n\t\t}\n\t\tconst compStyle = getComputedStyle(elem);\n\n\t\tlet x = parseInt(compStyle.getPropertyValue(property));\n\t\treturn x;\n\t}\n\n\tasync function handle_scroll(e: Event): Promise<void> {\n\t\tconst scroll_top = viewport.scrollTop;\n\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tconst is_start_overflow = sortedItems.length < start;\n\n\t\tconst row_top_border = get_computed_px_amount(rows[1], \"border-top-width\");\n\n\t\tconst actual_border_collapsed_width = 0;\n\n\t\tif (is_start_overflow) {\n\t\t\tawait scroll_to_index(sortedItems.length - 1, { behavior: \"auto\" });\n\t\t}\n\n\t\tlet new_start = 0;\n\t\t// acquire height map for currently visible rows\n\t\tfor (let v = 0; v < rows.length; v += 1) {\n\t\t\theight_map[start + v] = rows[v].getBoundingClientRect().height;\n\t\t}\n\t\tlet i = 0;\n\t\t// start from top: thead, with its borders, plus the first border to afterwards neglect\n\t\tlet y = head_height + row_top_border / 2;\n\t\tlet row_heights = [];\n\t\t// loop items to find new start\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\trow_heights[i] = row_height;\n\t\t\t// we only want to jump if the full (incl. border) row is away\n\t\t\tif (y + row_height + actual_border_collapsed_width > scroll_top) {\n\t\t\t\t// this is the last index still inside the viewport\n\t\t\t\tnew_start = i;\n\t\t\t\ttop = y - (head_height + row_top_border / 2);\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tnew_start = Math.max(0, new_start);\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t\tif (y > scroll_top + viewport_height) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tstart = new_start;\n\t\tend = i;\n\t\tconst remaining = sortedItems.length - end;\n\t\tif (end === 0) {\n\t\t\tend = 10;\n\t\t}\n\t\taverage_height = (y - head_height) / end;\n\t\tlet remaining_height = remaining * average_height; // 0\n\t\t// compute height map for remaining items\n\t\twhile (i < sortedItems.length) {\n\t\t\ti += 1;\n\t\t\theight_map[i] = average_height;\n\t\t}\n\t\tbottom = remaining_height;\n\t\tif (!isFinite(bottom)) {\n\t\t\tbottom = 200000;\n\t\t}\n\t}\n\n\texport async function scroll_to_index(\n\t\tindex: number,\n\t\topts: ScrollToOptions,\n\t\talign_end = false\n\t): Promise<void> {\n\t\tawait tick();\n\n\t\tconst _itemHeight = average_height;\n\n\t\tlet distance = index * _itemHeight;\n\t\tif (align_end) {\n\t\t\tdistance = distance - viewport_height + _itemHeight + head_height;\n\t\t}\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tdistance += scrollbar_height;\n\t\t}\n\n\t\tconst _opts = {\n\t\t\ttop: distance,\n\t\t\tbehavior: \"smooth\" as ScrollBehavior,\n\t\t\t...opts\n\t\t};\n\n\t\tviewport.scrollTo(_opts);\n\t}\n\n\t$: sortedItems = items;\n\n\t$: visible = is_browser\n\t\t? sortedItems.slice(start, end).map((data, i) => {\n\t\t\t\treturn { index: i + start, data };\n\t\t\t})\n\t\t: sortedItems\n\t\t\t\t.slice(0, (max_height / sortedItems.length) * average_height + 1)\n\t\t\t\t.map((data, i) => {\n\t\t\t\t\treturn { index: i + start, data };\n\t\t\t\t});\n\n\t$: actual_height = visible.length * average_height + 10;\n\tonMount(() => {\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tmounted = true;\n\t\trefresh_height_map(items);\n\t});\n</script>\n\n<svelte-virtual-table-viewport>\n\t<table\n\t\tclass=\"table\"\n\t\tbind:this={viewport}\n\t\tbind:contentRect={viewport_box}\n\t\ton:scroll={handle_scroll}\n\t\tstyle=\"height: {height}; --bw-svt-p-top: {top}px; --bw-svt-p-bottom: {bottom}px; --bw-svt-head-height: {head_height}px; --bw-svt-foot-height: {foot_height}px; --bw-svt-avg-row-height: {average_height}px\"\n\t>\n\t\t<thead class=\"thead\" bind:offsetHeight={head_height}>\n\t\t\t<slot name=\"thead\" />\n\t\t</thead>\n\t\t<tbody bind:this={contents} class=\"tbody\">\n\t\t\t{#if visible.length && visible[0].data.length}\n\t\t\t\t{#each visible as item (item.data[0].id)}\n\t\t\t\t\t<slot name=\"tbody\" item={item.data} index={item.index}>\n\t\t\t\t\t\tMissing Table Row\n\t\t\t\t\t</slot>\n\t\t\t\t{/each}\n\t\t\t{/if}\n\t\t</tbody>\n\t\t<tfoot class=\"tfoot\" bind:offsetHeight={foot_height}>\n\t\t\t<slot name=\"tfoot\" />\n\t\t</tfoot>\n\t</table>\n</svelte-virtual-table-viewport>\n\n<style type=\"text/css\">\n\ttable {\n\t\tposition: relative;\n\t\toverflow-y: scroll;\n\t\toverflow-x: scroll;\n\t\t-webkit-overflow-scrolling: touch;\n\t\tmax-height: 100vh;\n\t\tbox-sizing: border-box;\n\t\tdisplay: block;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t\twidth: 100%;\n\t\tscroll-snap-type: x proximity;\n\t\tborder-collapse: separate;\n\t}\n\ttable :is(thead, tfoot, tbody) {\n\t\tdisplay: table;\n\t\ttable-layout: fixed;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\n\ttbody {\n\t\toverflow-x: scroll;\n\t\toverflow-y: hidden;\n\t}\n\n\ttable tbody {\n\t\tpadding-top: var(--bw-svt-p-top);\n\t\tpadding-bottom: var(--bw-svt-p-bottom);\n\t}\n\ttbody {\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tborder: 0px solid currentColor;\n\t}\n\n\ttbody > :global(tr:last-child) {\n\t\tborder: none;\n\t}\n\n\ttable :global(td) {\n\t\tscroll-snap-align: start;\n\t}\n\n\ttbody > :global(tr:nth-child(even)) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: var(--layer-1);\n\t\tbox-shadow: var(--shadow-drop);\n\t\toverflow: hidden;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick, onMount } from \"svelte\";\n\timport { dsvFormat } from \"d3-dsv\";\n\timport { dequal } from \"dequal/lite\";\n\timport { copy } from \"@gradio/utils\";\n\timport { Upload } from \"@gradio/upload\";\n\timport { BaseButton } from \"@gradio/button\";\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport { type Client } from \"@gradio/client\";\n\timport VirtualTable from \"./VirtualTable.svelte\";\n\timport type {\n\t\tHeaders,\n\t\tHeadersWithIDs,\n\t\tData,\n\t\tMetadata,\n\t\tDatatype\n\t} from \"./utils\";\n\n\texport let datatype: Datatype | Datatype[];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let headers: Headers = [];\n\texport let values: (string | number)[][] = [];\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\n\texport let editable = true;\n\texport let wrap = false;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\n\texport let height = 500;\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\n\tlet selected: false | [number, number] = false;\n\texport let display_value: string[][] | null = null;\n\texport let styling: string[][] | null = null;\n\tlet t_rect: DOMRectReadOnly;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: {\n\t\t\tdata: (string | number)[][];\n\t\t\theaders: string[];\n\t\t\tmetadata: Metadata;\n\t\t};\n\t\tselect: SelectData;\n\t}>();\n\n\tlet editing: false | [number, number] = false;\n\n\tconst get_data_at = (row: number, col: number): string | number =>\n\t\tdata?.[row]?.[col]?.value;\n\n\t$: {\n\t\tif (selected !== false) {\n\t\t\tconst [row, col] = selected;\n\t\t\tif (!isNaN(row) && !isNaN(col)) {\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: [row, col],\n\t\t\t\t\tvalue: get_data_at(row, col),\n\t\t\t\t\trow_value: data[row].map((d) => d.value)\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tlet els: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLInputElement }\n\t> = {};\n\n\tlet data_binding: Record<string, (typeof data)[0][0]> = {};\n\n\tfunction make_id(): string {\n\t\treturn Math.random().toString(36).substring(2, 15);\n\t}\n\n\tfunction make_headers(_head: Headers): HeadersWithIDs {\n\t\tlet _h = _head || [];\n\t\tif (col_count[1] === \"fixed\" && _h.length < col_count[0]) {\n\t\t\tconst fill = Array(col_count[0] - _h.length)\n\t\t\t\t.fill(\"\")\n\t\t\t\t.map((_, i) => `${i + _h.length}`);\n\t\t\t_h = _h.concat(fill);\n\t\t}\n\n\t\tif (!_h || _h.length === 0) {\n\t\t\treturn Array(col_count[0])\n\t\t\t\t.fill(0)\n\t\t\t\t.map((_, i) => {\n\t\t\t\t\tconst _id = make_id();\n\t\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\t\treturn { id: _id, value: JSON.stringify(i + 1) };\n\t\t\t\t});\n\t\t}\n\n\t\treturn _h.map((h, i) => {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\treturn { id: _id, value: h ?? \"\" };\n\t\t});\n\t}\n\n\tfunction process_data(_values: (string | number)[][]): {\n\t\tvalue: string | number;\n\t\tid: string;\n\t}[][] {\n\t\tconst data_row_length = _values.length;\n\t\treturn Array(\n\t\t\trow_count[1] === \"fixed\"\n\t\t\t\t? row_count[0]\n\t\t\t\t: data_row_length < row_count[0]\n\t\t\t\t\t? row_count[0]\n\t\t\t\t\t: data_row_length\n\t\t)\n\t\t\t.fill(0)\n\t\t\t.map((_, i) =>\n\t\t\t\tArray(\n\t\t\t\t\tcol_count[1] === \"fixed\"\n\t\t\t\t\t\t? col_count[0]\n\t\t\t\t\t\t: data_row_length > 0\n\t\t\t\t\t\t\t? _values[0].length\n\t\t\t\t\t\t\t: headers.length\n\t\t\t\t)\n\t\t\t\t\t.fill(0)\n\t\t\t\t\t.map((_, j) => {\n\t\t\t\t\t\tconst id = make_id();\n\t\t\t\t\t\tels[id] = els[id] || { input: null, cell: null };\n\t\t\t\t\t\tconst obj = { value: _values?.[i]?.[j] ?? \"\", id };\n\t\t\t\t\t\tdata_binding[id] = obj;\n\t\t\t\t\t\treturn obj;\n\t\t\t\t\t})\n\t\t\t);\n\t}\n\n\tlet _headers = make_headers(headers);\n\tlet old_headers: string[] | undefined;\n\n\t$: {\n\t\tif (!dequal(headers, old_headers)) {\n\t\t\ttrigger_headers();\n\t\t}\n\t}\n\n\tfunction trigger_headers(): void {\n\t\t_headers = make_headers(headers);\n\n\t\told_headers = headers.slice();\n\t\ttrigger_change();\n\t}\n\n\t$: if (!dequal(values, old_val)) {\n\t\tdata = process_data(values as (string | number)[][]);\n\t\told_val = values as (string | number)[][];\n\t}\n\n\tlet data: { id: string; value: string | number }[][] = [[]];\n\n\tlet old_val: undefined | (string | number)[][] = undefined;\n\n\tasync function trigger_change(): Promise<void> {\n\t\tdispatch(\"change\", {\n\t\t\tdata: data.map((r) => r.map(({ value }) => value)),\n\t\t\theaders: _headers.map((h) => h.value),\n\t\t\tmetadata: editable\n\t\t\t\t? null\n\t\t\t\t: { display_value: display_value, styling: styling }\n\t\t});\n\t}\n\n\tfunction get_sort_status(\n\t\tname: string,\n\t\t_sort?: number,\n\t\tdirection?: SortDirection\n\t): \"none\" | \"ascending\" | \"descending\" {\n\t\tif (!_sort) return \"none\";\n\t\tif (headers[_sort] === name) {\n\t\t\tif (direction === \"asc\") return \"ascending\";\n\t\t\tif (direction === \"des\") return \"descending\";\n\t\t}\n\n\t\treturn \"none\";\n\t}\n\n\tfunction get_current_indices(id: string): [number, number] {\n\t\treturn data.reduce(\n\t\t\t(acc, arr, i) => {\n\t\t\t\tconst j = arr.reduce(\n\t\t\t\t\t(_acc, _data, k) => (id === _data.id ? k : _acc),\n\t\t\t\t\t-1\n\t\t\t\t);\n\n\t\t\t\treturn j === -1 ? acc : [i, j];\n\t\t\t},\n\t\t\t[-1, -1]\n\t\t);\n\t}\n\n\tasync function start_edit(i: number, j: number): Promise<void> {\n\t\tif (!editable || dequal(editing, [i, j])) return;\n\n\t\tediting = [i, j];\n\t}\n\n\tfunction move_cursor(\n\t\tkey: \"ArrowRight\" | \"ArrowLeft\" | \"ArrowDown\" | \"ArrowUp\",\n\t\tcurrent_coords: [number, number]\n\t): void {\n\t\tconst dir = {\n\t\t\tArrowRight: [0, 1],\n\t\t\tArrowLeft: [0, -1],\n\t\t\tArrowDown: [1, 0],\n\t\t\tArrowUp: [-1, 0]\n\t\t}[key];\n\n\t\tconst i = current_coords[0] + dir[0];\n\t\tconst j = current_coords[1] + dir[1];\n\n\t\tif (i < 0 && j <= 0) {\n\t\t\tselected_header = j;\n\t\t\tselected = false;\n\t\t} else {\n\t\t\tconst is_data = data[i]?.[j];\n\t\t\tselected = is_data ? [i, j] : selected;\n\t\t}\n\t}\n\n\tlet clear_on_focus = false;\n\t// eslint-disable-next-line complexity\n\tasync function handle_keydown(event: KeyboardEvent): Promise<void> {\n\t\tif (selected_header !== false && header_edit === false) {\n\t\t\tswitch (event.key) {\n\t\t\t\tcase \"ArrowDown\":\n\t\t\t\t\tselected = [0, selected_header];\n\t\t\t\t\tselected_header = false;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"ArrowLeft\":\n\t\t\t\t\tselected_header =\n\t\t\t\t\t\tselected_header > 0 ? selected_header - 1 : selected_header;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"ArrowRight\":\n\t\t\t\t\tselected_header =\n\t\t\t\t\t\tselected_header < _headers.length - 1\n\t\t\t\t\t\t\t? selected_header + 1\n\t\t\t\t\t\t\t: selected_header;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"Escape\":\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tselected_header = false;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"Enter\":\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (!selected) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst [i, j] = selected;\n\n\t\tswitch (event.key) {\n\t\t\tcase \"ArrowRight\":\n\t\t\tcase \"ArrowLeft\":\n\t\t\tcase \"ArrowDown\":\n\t\t\tcase \"ArrowUp\":\n\t\t\t\tif (editing) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tmove_cursor(event.key, [i, j]);\n\t\t\t\tbreak;\n\n\t\t\tcase \"Escape\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tediting = false;\n\t\t\t\tbreak;\n\t\t\tcase \"Enter\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tevent.preventDefault();\n\n\t\t\t\tif (event.shiftKey) {\n\t\t\t\t\tadd_row(i);\n\t\t\t\t\tawait tick();\n\n\t\t\t\t\tselected = [i + 1, j];\n\t\t\t\t} else {\n\t\t\t\t\tif (dequal(editing, [i, j])) {\n\t\t\t\t\t\tediting = false;\n\t\t\t\t\t\tawait tick();\n\t\t\t\t\t\tselected = [i, j];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tediting = [i, j];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\tcase \"Backspace\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (!editing) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tdata[i][j].value = \"\";\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"Delete\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (!editing) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tdata[i][j].value = \"\";\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"Tab\":\n\t\t\t\tlet direction = event.shiftKey ? -1 : 1;\n\n\t\t\t\tlet is_data_x = data[i][j + direction];\n\t\t\t\tlet is_data_y =\n\t\t\t\t\tdata?.[i + direction]?.[direction > 0 ? 0 : _headers.length - 1];\n\n\t\t\t\tif (is_data_x || is_data_y) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tselected = is_data_x\n\t\t\t\t\t\t? [i, j + direction]\n\t\t\t\t\t\t: [i + direction, direction > 0 ? 0 : _headers.length - 1];\n\t\t\t\t}\n\t\t\t\tediting = false;\n\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (\n\t\t\t\t\t(!editing || (editing && dequal(editing, [i, j]))) &&\n\t\t\t\t\tevent.key.length === 1\n\t\t\t\t) {\n\t\t\t\t\tclear_on_focus = true;\n\t\t\t\t\tediting = [i, j];\n\t\t\t\t}\n\t\t}\n\t}\n\n\tasync function handle_cell_click(i: number, j: number): Promise<void> {\n\t\tif (dequal(editing, [i, j])) return;\n\t\theader_edit = false;\n\t\tselected_header = false;\n\t\tediting = false;\n\t\tselected = [i, j];\n\t\tawait tick();\n\t\tparent.focus();\n\t}\n\n\ttype SortDirection = \"asc\" | \"des\";\n\tlet sort_direction: SortDirection | undefined;\n\tlet sort_by: number | undefined;\n\n\tfunction handle_sort(col: number): void {\n\t\tif (typeof sort_by !== \"number\" || sort_by !== col) {\n\t\t\tsort_direction = \"asc\";\n\t\t\tsort_by = col;\n\t\t} else {\n\t\t\tif (sort_direction === \"asc\") {\n\t\t\t\tsort_direction = \"des\";\n\t\t\t} else if (sort_direction === \"des\") {\n\t\t\t\tsort_direction = \"asc\";\n\t\t\t}\n\t\t}\n\t}\n\n\tlet header_edit: number | false;\n\n\tlet select_on_focus = false;\n\tlet selected_header: number | false = false;\n\tasync function edit_header(i: number, _select = false): Promise<void> {\n\t\tif (!editable || col_count[1] !== \"dynamic\" || header_edit === i) return;\n\t\tselected = false;\n\t\tselected_header = i;\n\t\theader_edit = i;\n\t\tselect_on_focus = _select;\n\t}\n\n\tfunction end_header_edit(event: KeyboardEvent): void {\n\t\tif (!editable) return;\n\n\t\tswitch (event.key) {\n\t\t\tcase \"Escape\":\n\t\t\tcase \"Enter\":\n\t\t\tcase \"Tab\":\n\t\t\t\tevent.preventDefault();\n\t\t\t\tselected = false;\n\t\t\t\tselected_header = header_edit;\n\t\t\t\theader_edit = false;\n\t\t\t\tparent.focus();\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\tasync function add_row(index?: number): Promise<void> {\n\t\tparent.focus();\n\n\t\tif (row_count[1] !== \"dynamic\") return;\n\t\tif (data.length === 0) {\n\t\t\tvalues = [Array(headers.length).fill(\"\")];\n\t\t\treturn;\n\t\t}\n\n\t\tdata.splice(\n\t\t\tindex ? index + 1 : data.length,\n\t\t\t0,\n\t\t\tArray(data[0].length)\n\t\t\t\t.fill(0)\n\t\t\t\t.map((_, i) => {\n\t\t\t\t\tconst _id = make_id();\n\n\t\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\t\treturn { id: _id, value: \"\" };\n\t\t\t\t})\n\t\t);\n\n\t\tdata = data;\n\t\tselected = [index ? index + 1 : data.length - 1, 0];\n\t}\n\n\t$: data && trigger_change();\n\n\tasync function add_col(): Promise<void> {\n\t\tparent.focus();\n\t\tif (col_count[1] !== \"dynamic\") return;\n\t\tfor (let i = 0; i < data.length; i++) {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\tdata[i].push({ id: _id, value: \"\" });\n\t\t}\n\n\t\theaders.push(`Header ${headers.length + 1}`);\n\n\t\tdata = data;\n\t\theaders = headers;\n\n\t\tawait tick();\n\n\t\trequestAnimationFrame(() => {\n\t\t\tedit_header(headers.length - 1, true);\n\t\t\tconst new_w = parent.querySelectorAll(\"tbody\")[1].offsetWidth;\n\t\t\tparent.querySelectorAll(\"table\")[1].scrollTo({ left: new_w });\n\t\t});\n\t}\n\n\tfunction handle_click_outside(event: Event): void {\n\t\tevent.stopImmediatePropagation();\n\t\tconst [trigger] = event.composedPath() as HTMLElement[];\n\t\tif (parent.contains(trigger)) {\n\t\t\treturn;\n\t\t}\n\n\t\tediting = false;\n\t\theader_edit = false;\n\t\tselected_header = false;\n\t\tselected = false;\n\t}\n\n\tfunction guess_delimitaor(\n\t\ttext: string,\n\t\tpossibleDelimiters: string[]\n\t): string[] {\n\t\treturn possibleDelimiters.filter(weedOut);\n\n\t\tfunction weedOut(delimiter: string): boolean {\n\t\t\tvar cache = -1;\n\t\t\treturn text.split(\"\\n\").every(checkLength);\n\n\t\t\tfunction checkLength(line: string): boolean {\n\t\t\t\tif (!line) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tvar length = line.split(delimiter).length;\n\t\t\t\tif (cache < 0) {\n\t\t\t\t\tcache = length;\n\t\t\t\t}\n\t\t\t\treturn cache === length && length > 1;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction data_uri_to_blob(data_uri: string): Blob {\n\t\tconst byte_str = atob(data_uri.split(\",\")[1]);\n\t\tconst mime_str = data_uri.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\n\t\tconst ab = new ArrayBuffer(byte_str.length);\n\t\tconst ia = new Uint8Array(ab);\n\n\t\tfor (let i = 0; i < byte_str.length; i++) {\n\t\t\tia[i] = byte_str.charCodeAt(i);\n\t\t}\n\n\t\treturn new Blob([ab], { type: mime_str });\n\t}\n\n\tfunction blob_to_string(blob: Blob): void {\n\t\tconst reader = new FileReader();\n\n\t\tfunction handle_read(e: ProgressEvent<FileReader>): void {\n\t\t\tif (!e?.target?.result || typeof e.target.result !== \"string\") return;\n\n\t\t\tconst [delimiter] = guess_delimitaor(e.target.result, [\",\", \"\\t\"]);\n\n\t\t\tconst [head, ...rest] = dsvFormat(delimiter).parseRows(e.target.result);\n\n\t\t\t_headers = make_headers(\n\t\t\t\tcol_count[1] === \"fixed\" ? head.slice(0, col_count[0]) : head\n\t\t\t);\n\n\t\t\tvalues = rest;\n\t\t\treader.removeEventListener(\"loadend\", handle_read);\n\t\t}\n\n\t\treader.addEventListener(\"loadend\", handle_read);\n\n\t\treader.readAsText(blob);\n\t}\n\n\tlet dragging = false;\n\n\tfunction get_max(\n\t\t_d: { value: any; id: string }[][]\n\t): { value: any; id: string }[] {\n\t\tlet max = _d[0].slice();\n\t\tfor (let i = 0; i < _d.length; i++) {\n\t\t\tfor (let j = 0; j < _d[i].length; j++) {\n\t\t\t\tif (`${max[j].value}`.length < `${_d[i][j].value}`.length) {\n\t\t\t\t\tmax[j] = _d[i][j];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn max;\n\t}\n\n\t$: max = get_max(data);\n\n\t$: cells[0] && set_cell_widths();\n\tlet cells: HTMLTableCellElement[] = [];\n\tlet parent: HTMLDivElement;\n\tlet table: HTMLTableElement;\n\n\tfunction set_cell_widths(): void {\n\t\tconst widths = cells.map((el, i) => {\n\t\t\treturn el?.clientWidth || 0;\n\t\t});\n\t\tif (widths.length === 0) return;\n\t\tfor (let i = 0; i < widths.length; i++) {\n\t\t\tparent.style.setProperty(\n\t\t\t\t`--cell-width-${i}`,\n\t\t\t\t`${widths[i] - scrollbar_width / widths.length}px`\n\t\t\t);\n\t\t}\n\t}\n\n\tlet table_height: number =\n\t\tvalues.slice(0, (height / values.length) * 37).length * 37 + 37;\n\tlet scrollbar_width = 0;\n\n\tfunction sort_data(\n\t\t_data: typeof data,\n\t\t_display_value: string[][] | null,\n\t\t_styling: string[][] | null,\n\t\tcol?: number,\n\t\tdir?: SortDirection\n\t): void {\n\t\tlet id = null;\n\t\t//Checks if the selected cell is still in the data\n\t\tif (selected && selected[0] in data && selected[1] in data[selected[0]]) {\n\t\t\tid = data[selected[0]][selected[1]].id;\n\t\t}\n\t\tif (typeof col !== \"number\" || !dir) {\n\t\t\treturn;\n\t\t}\n\t\tconst indices = [...Array(_data.length).keys()];\n\n\t\tif (dir === \"asc\") {\n\t\t\tindices.sort((i, j) =>\n\t\t\t\t_data[i][col].value < _data[j][col].value ? -1 : 1\n\t\t\t);\n\t\t} else if (dir === \"des\") {\n\t\t\tindices.sort((i, j) =>\n\t\t\t\t_data[i][col].value > _data[j][col].value ? -1 : 1\n\t\t\t);\n\t\t} else {\n\t\t\treturn;\n\t\t}\n\n\t\t// sort all the data and metadata based on the values in the data\n\t\tconst temp_data = [..._data];\n\t\tconst temp_display_value = _display_value ? [..._display_value] : null;\n\t\tconst temp_styling = _styling ? [..._styling] : null;\n\t\tindices.forEach((originalIndex, sortedIndex) => {\n\t\t\t_data[sortedIndex] = temp_data[originalIndex];\n\t\t\tif (_display_value && temp_display_value)\n\t\t\t\t_display_value[sortedIndex] = temp_display_value[originalIndex];\n\t\t\tif (_styling && temp_styling)\n\t\t\t\t_styling[sortedIndex] = temp_styling[originalIndex];\n\t\t});\n\n\t\tdata = data;\n\n\t\tif (id) {\n\t\t\tconst [i, j] = get_current_indices(id);\n\t\t\tselected = [i, j];\n\t\t}\n\t}\n\n\t$: sort_data(data, display_value, styling, sort_by, sort_direction);\n\n\t$: selected_index = !!selected && selected[0];\n\n\tlet is_visible = false;\n\n\tonMount(() => {\n\t\tconst observer = new IntersectionObserver((entries, observer) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting && !is_visible) {\n\t\t\t\t\tset_cell_widths();\n\t\t\t\t\tdata = data;\n\t\t\t\t}\n\n\t\t\t\tis_visible = entry.isIntersecting;\n\t\t\t});\n\t\t});\n\n\t\tobserver.observe(parent);\n\n\t\treturn () => {\n\t\t\tobserver.disconnect();\n\t\t};\n\t});\n</script>\n\n<svelte:window\n\ton:click={handle_click_outside}\n\ton:touchstart={handle_click_outside}\n\ton:resize={() => set_cell_widths()}\n/>\n\n<div class:label={label && label.length !== 0} use:copy>\n\t{#if label && label.length !== 0 && show_label}\n\t\t<p>\n\t\t\t{label}\n\t\t</p>\n\t{/if}\n\t<div\n\t\tbind:this={parent}\n\t\tclass=\"table-wrap\"\n\t\tclass:dragging\n\t\tclass:no-wrap={!wrap}\n\t\tstyle=\"height:{table_height}px\"\n\t\ton:keydown={(e) => handle_keydown(e)}\n\t\trole=\"grid\"\n\t\ttabindex=\"0\"\n\t>\n\t\t<table\n\t\t\tbind:contentRect={t_rect}\n\t\t\tbind:this={table}\n\t\t\tclass:fixed-layout={column_widths.length != 0}\n\t\t>\n\t\t\t{#if label && label.length !== 0}\n\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t{/if}\n\t\t\t<thead>\n\t\t\t\t<tr>\n\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t<th\n\t\t\t\t\t\t\tclass:editing={header_edit === i}\n\t\t\t\t\t\t\taria-sort={get_sort_status(value, sort_by, sort_direction)}\n\t\t\t\t\t\t\tstyle:width={column_widths.length ? column_widths[i] : undefined}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\theader\n\t\t\t\t\t\t\t\t\tedit={false}\n\t\t\t\t\t\t\t\t\tel={null}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass:sorted={sort_by === i}\n\t\t\t\t\t\t\t\t\tclass:des={sort_by === i && sort_direction === \"des\"}\n\t\t\t\t\t\t\t\t\tclass=\"sort-button {sort_direction} \"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 9 7\"\n\t\t\t\t\t\t\t\t\t\tfill=\"none\"\n\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<path d=\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</th>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</thead>\n\t\t\t<tbody>\n\t\t\t\t<tr>\n\t\t\t\t\t{#each max as { value, id }, j (id)}\n\t\t\t\t\t\t<td tabindex=\"-1\" bind:this={cells[j]}>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\tedit={false}\n\t\t\t\t\t\t\t\t\tel={null}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</tbody>\n\t\t</table>\n\t\t<Upload\n\t\t\t{upload}\n\t\t\t{stream_handler}\n\t\t\tflex={false}\n\t\t\tcenter={false}\n\t\t\tboundedheight={false}\n\t\t\tdisable_click={true}\n\t\t\t{root}\n\t\t\ton:load={(e) => blob_to_string(data_uri_to_blob(e.detail.data))}\n\t\t\tbind:dragging\n\t\t>\n\t\t\t<VirtualTable\n\t\t\t\tbind:items={data}\n\t\t\t\tmax_height={height}\n\t\t\t\tbind:actual_height={table_height}\n\t\t\t\tbind:table_scrollbar_width={scrollbar_width}\n\t\t\t\tselected={selected_index}\n\t\t\t>\n\t\t\t\t{#if label && label.length !== 0}\n\t\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t\t{/if}\n\t\t\t\t<tr slot=\"thead\">\n\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t<th\n\t\t\t\t\t\t\tclass:focus={header_edit === i || selected_header === i}\n\t\t\t\t\t\t\taria-sort={get_sort_status(value, sort_by, sort_direction)}\n\t\t\t\t\t\t\tstyle=\"width: var(--cell-width-{i});\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\tbind:value={_headers[i].value}\n\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\tedit={header_edit === i}\n\t\t\t\t\t\t\t\t\ton:keydown={end_header_edit}\n\t\t\t\t\t\t\t\t\ton:dblclick={() => edit_header(i)}\n\t\t\t\t\t\t\t\t\t{select_on_focus}\n\t\t\t\t\t\t\t\t\theader\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass:sorted={sort_by === i}\n\t\t\t\t\t\t\t\t\tclass:des={sort_by === i && sort_direction === \"des\"}\n\t\t\t\t\t\t\t\t\tclass=\"sort-button {sort_direction} \"\n\t\t\t\t\t\t\t\t\ton:click={() => handle_sort(i)}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 9 7\"\n\t\t\t\t\t\t\t\t\t\tfill=\"none\"\n\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<path d=\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</th>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\n\t\t\t\t<tr slot=\"tbody\" let:item let:index class:row_odd={index % 2 === 0}>\n\t\t\t\t\t{#each item as { value, id }, j (id)}\n\t\t\t\t\t\t<td\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ton:touchstart={() => start_edit(index, j)}\n\t\t\t\t\t\t\ton:click={() => handle_cell_click(index, j)}\n\t\t\t\t\t\t\ton:dblclick={() => start_edit(index, j)}\n\t\t\t\t\t\t\tstyle:width=\"var(--cell-width-{j})\"\n\t\t\t\t\t\t\tstyle={styling?.[index]?.[j] || \"\"}\n\t\t\t\t\t\t\tclass:focus={dequal(selected, [index, j])}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\tbind:value={data[index][j].value}\n\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\tdisplay_value={display_value?.[index]?.[j]}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\t\tedit={dequal(editing, [index, j])}\n\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\ton:blur={() => ((clear_on_focus = false), parent.focus())}\n\t\t\t\t\t\t\t\t\t{clear_on_focus}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</VirtualTable>\n\t\t</Upload>\n\t</div>\n\t{#if editable}\n\t\t<div class=\"controls-wrap\">\n\t\t\t{#if row_count[1] === \"dynamic\"}\n\t\t\t\t<span class=\"button-wrap\">\n\t\t\t\t\t<BaseButton\n\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\ton:click={(e) => (e.stopPropagation(), add_row())}\n\t\t\t\t\t>\n\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\trole=\"img\"\n\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\tpreserveAspectRatio=\"xMidYMid meet\"\n\t\t\t\t\t\t\tviewBox=\"0 0 32 32\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<path\n\t\t\t\t\t\t\t\tfill=\"currentColor\"\n\t\t\t\t\t\t\t\td=\"M24.59 16.59L17 24.17V4h-2v20.17l-7.59-7.58L6 18l10 10l10-10l-1.41-1.41z\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t{i18n(\"dataframe.new_row\")}\n\t\t\t\t\t</BaseButton>\n\t\t\t\t</span>\n\t\t\t{/if}\n\t\t\t{#if col_count[1] === \"dynamic\"}\n\t\t\t\t<span class=\"button-wrap\">\n\t\t\t\t\t<BaseButton\n\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\ton:click={(e) => (e.stopPropagation(), add_col())}\n\t\t\t\t\t>\n\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\trole=\"img\"\n\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\tpreserveAspectRatio=\"xMidYMid meet\"\n\t\t\t\t\t\t\tviewBox=\"0 0 32 32\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<path\n\t\t\t\t\t\t\t\tfill=\"currentColor\"\n\t\t\t\t\t\t\t\td=\"m18 6l-1.43 1.393L24.15 15H4v2h20.15l-7.58 7.573L18 26l10-10L18 6z\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t{i18n(\"dataframe.new_column\")}\n\t\t\t\t\t</BaseButton>\n\t\t\t\t</span>\n\t\t\t{/if}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.button-wrap:hover svg {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.button-wrap svg {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: -5px;\n\t}\n\n\t.label p {\n\t\tposition: relative;\n\t\tz-index: var(--layer-4);\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--block-label-text-size);\n\t}\n\n\t.table-wrap {\n\t\tposition: relative;\n\t\ttransition: 150ms;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\toverflow: hidden;\n\t}\n\n\t.table-wrap:focus-within {\n\t\toutline: none;\n\t\tbackground-color: none;\n\t}\n\n\t.dragging {\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.no-wrap {\n\t\twhite-space: nowrap;\n\t}\n\n\ttable {\n\t\tposition: absolute;\n\t\topacity: 0;\n\t\ttransition: 150ms;\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t}\n\n\tdiv:not(.no-wrap) td {\n\t\toverflow-wrap: anywhere;\n\t}\n\n\tdiv.no-wrap td {\n\t\toverflow-x: hidden;\n\t}\n\n\ttable.fixed-layout {\n\t\ttable-layout: fixed;\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: var(--layer-1);\n\t\tbox-shadow: var(--shadow-drop);\n\t}\n\n\ttr {\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t}\n\n\ttr > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth,\n\ttd {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\toutline: none;\n\t\tbox-shadow: inset 0 0 0 1px var(--ring-color);\n\t\tpadding: 0;\n\t}\n\n\tth:first-child {\n\t\tborder-top-left-radius: var(--table-radius);\n\t}\n\n\tth:last-child {\n\t\tborder-top-right-radius: var(--table-radius);\n\t}\n\n\tth.focus,\n\ttd.focus {\n\t\t--ring-color: var(--color-accent);\n\t}\n\n\ttr:last-child td:first-child {\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t}\n\n\ttr:last-child td:last-child {\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\ttr th {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\tth svg {\n\t\tfill: currentColor;\n\t\tfont-size: 10px;\n\t}\n\n\t.sort-button {\n\t\tdisplay: flex;\n\t\tflex: none;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttransition: 150ms;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tline-height: var(--text-sm);\n\t}\n\n\t.sort-button:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.des {\n\t\ttransform: scaleY(-1);\n\t}\n\n\t.sort-button.sorted {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.editing {\n\t\tbackground: var(--table-editing);\n\t}\n\n\t.cell-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\toutline: none;\n\t\theight: var(--size-full);\n\t\tmin-height: var(--size-9);\n\t}\n\n\t.controls-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tpadding-top: var(--size-2);\n\t}\n\n\t.controls-wrap > * + * {\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.row_odd {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.row_odd.focus {\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\ttable {\n\t\tborder-collapse: separate;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseDataFrame } from \"./shared/Table.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport { afterUpdate, tick } from \"svelte\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Table from \"./shared/Table.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Headers, Data, Metadata, Datatype } from \"./shared/utils\";\n\texport let headers: Headers = [];\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { data: Data; headers: Headers; metadata: Metadata } = {\n\t\tdata: [[\"\", \"\", \"\"]],\n\t\theaders: [\"1\", \"2\", \"3\"],\n\t\tmetadata: null\n\t};\n\tlet old_value = \"\";\n\texport let value_is_output = false;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let wrap: boolean;\n\texport let datatype: Datatype | Datatype[];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let root: string;\n\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let height: number | undefined = undefined;\n\n\texport let loading_status: LoadingStatus;\n\texport let interactive: boolean;\n\n\tlet _headers: Headers;\n\tlet display_value: string[][] | null;\n\tlet styling: string[][] | null;\n\tlet values: (string | number)[][];\n\tasync function handle_change(data?: {\n\t\tdata: Data;\n\t\theaders: Headers;\n\t\tmetadata: Metadata;\n\t}): Promise<void> {\n\t\tlet _data = data || value;\n\n\t\t_headers = [...(_data.headers || headers)];\n\t\tvalues = _data.data ? [..._data.data] : [];\n\t\tdisplay_value = _data?.metadata?.display_value\n\t\t\t? [..._data?.metadata?.display_value]\n\t\t\t: null;\n\t\tstyling =\n\t\t\t!interactive && _data?.metadata?.styling\n\t\t\t\t? [..._data?.metadata?.styling]\n\t\t\t\t: null;\n\t\tawait tick();\n\n\t\tgradio.dispatch(\"change\");\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\n\thandle_change();\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\t$: {\n\t\tif (old_value && JSON.stringify(value) !== old_value) {\n\t\t\told_value = JSON.stringify(value);\n\t\t\thandle_change();\n\t\t}\n\t}\n\n\tif (\n\t\t(Array.isArray(value) && value?.[0]?.length === 0) ||\n\t\tvalue.data?.[0]?.length === 0\n\t) {\n\t\tvalue = {\n\t\t\tdata: [Array(col_count?.[0] || 3).fill(\"\")],\n\t\t\theaders: Array(col_count?.[0] || 3)\n\t\t\t\t.fill(\"\")\n\t\t\t\t.map((_, i) => `${i + 1}`),\n\t\t\tmetadata: null\n\t\t};\n\t}\n\n\tasync function handle_value_change(data: {\n\t\tdata: Data;\n\t\theaders: Headers;\n\t\tmetadata: Metadata;\n\t}): Promise<void> {\n\t\tif (JSON.stringify(data) !== old_value) {\n\t\t\tvalue = { ...data };\n\t\t\told_value = JSON.stringify(value);\n\t\t\thandle_change(data);\n\t\t}\n\t}\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\tcontainer={false}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<Table\n\t\t{root}\n\t\t{label}\n\t\t{show_label}\n\t\t{row_count}\n\t\t{col_count}\n\t\t{values}\n\t\t{display_value}\n\t\t{styling}\n\t\theaders={_headers}\n\t\ton:change={(e) => handle_value_change(e.detail)}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t{wrap}\n\t\t{datatype}\n\t\t{latex_delimiters}\n\t\teditable={interactive}\n\t\t{height}\n\t\ti18n={gradio.i18n}\n\t\t{line_breaks}\n\t\t{column_widths}\n\t\tupload={gradio.client.upload}\n\t\tstream_handler={gradio.client.stream}\n\t/>\n</Block>\n"], "names": ["createEventDispatcher", "insert", "target", "input", "anchor", "ctx", "set_data", "t_value", "dirty", "markdowncode_changes", "create_if_block_2", "span", "edit", "$$props", "value", "display_value", "styling", "header", "datatype", "latex_delimiters", "clear_on_focus", "select_on_focus", "line_breaks", "editable", "root", "dispatch", "el", "use_focus", "node", "$$invalidate", "_value", "handle_blur", "currentTarget", "$$value", "tick", "get_key", "i", "create_if_block", "height", "svelte_virtual_table_viewport", "append", "table", "thead", "tbody", "tfoot", "get_computed_px_amount", "elem", "property", "compStyle", "items", "max_height", "actual_height", "table_scrollbar_width", "start", "end", "selected", "average_height", "bottom", "contents", "head_height", "foot_height", "height_map", "mounted", "rows", "top", "viewport", "viewport_height", "visible", "viewport_box", "is_browser", "raf", "cb", "content_height", "refresh_height_map", "_items", "scrollTop", "row", "_h", "row_height", "remaining", "scrollbar_height", "filtered_height_map", "v", "a", "b", "scroll_and_render", "n", "direction", "is_in_view", "scroll_to_index", "current", "viewport_top", "handle_scroll", "e", "scroll_top", "is_start_overflow", "sortedItems", "row_top_border", "actual_border_collapsed_width", "new_start", "y", "row_heights", "remaining_height", "index", "opts", "align_end", "_itemHeight", "distance", "_opts", "onMount", "ResizeObserverSingleton", "data", "p", "caption", "toggle_class", "div0", "attr", "th", "th_aria_sort_value", "set_style", "div1", "svg", "path", "td", "div", "if_block", "create_if_block_3", "editablecell_changes", "tr", "dequal", "editablecell_props", "td_style_value", "style_changed", "create_if_block_1", "t1_value", "t1", "if_block0", "create_if_block_5", "if_block1", "create_if_block_4", "table_1", "tr0", "tr1", "make_id", "guess_delimitaor", "text", "possibleDelimiters", "weedOut", "delimiter", "cache", "checkLength", "line", "length", "data_uri_to_blob", "data_uri", "byte_str", "mime_str", "ab", "ia", "label", "show_label", "headers", "values", "col_count", "row_count", "wrap", "i18n", "column_widths", "upload", "stream_handler", "t_rect", "editing", "get_data_at", "col", "els", "make_headers", "_head", "fill", "_", "_id", "h", "process_data", "_values", "data_row_length", "j", "id", "_headers", "old_headers", "trigger_headers", "trigger_change", "old_val", "r", "get_sort_status", "name", "_sort", "get_current_indices", "acc", "arr", "_acc", "_data", "k", "start_edit", "move_cursor", "key", "current_coords", "dir", "selected_header", "is_data", "handle_keydown", "event", "header_edit", "add_row", "is_data_x", "is_data_y", "handle_cell_click", "parent", "sort_direction", "sort_by", "handle_sort", "edit_header", "_select", "end_header_edit", "add_col", "new_w", "handle_click_outside", "trigger", "blob_to_string", "blob", "reader", "handle_read", "head", "rest", "dsvFormat", "dragging", "get_max", "_d", "max", "cells", "set_cell_widths", "widths", "scrollbar_width", "table_height", "sort_data", "_display_value", "_styling", "indices", "temp_data", "temp_display_value", "temp_styling", "originalIndex", "sortedIndex", "is_visible", "observer", "entries", "entry", "$$self", "blur_handler", "dblclick_handler_1", "click_handler_1", "click_handler_2", "click_handler_3", "d", "selected_index", "table_changes", "elem_id", "elem_classes", "old_value", "value_is_output", "scale", "min_width", "gradio", "loading_status", "interactive", "handle_change", "afterUpdate", "handle_value_change", "clear_status_handler", "change_handler"], "mappings": "ikCACU,uBAAAA,EAAA,SAAqC,oLAuD9CC,GASCC,EAAAC,EAAAC,CAAA,gBANYC,EAAM,EAAA,CAAA,yCAGTA,EAAW,EAAA,CAAA,gFAHRA,EAAM,EAAA,QAANA,EAAM,EAAA,CAAA,0FA4BjBA,EAAQ,CAAA,EAAGA,EAAK,CAAA,EAAGA,MAAiBA,EAAK,CAAA,GAAA,iEAAzCA,EAAQ,CAAA,EAAGA,EAAK,CAAA,EAAGA,MAAiBA,EAAK,CAAA,GAAA,KAAAC,GAAA,EAAAC,CAAA,4EAPhC,QAAAF,KAAM,eAAc,iDAGpB,kFAHAG,EAAA,IAAAC,EAAA,QAAAJ,KAAM,2QAHTA,EAAK,CAAA,EAAAH,EAAAE,CAAA,6BAALC,EAAK,CAAA,CAAA,wEAtBTA,EAAI,CAAA,GAAAK,GAAAL,CAAA,0CAqBH,OAAAA,OAAa,OAAM,EAEdA,OAAa,WAAU,kIAJ1BA,EAAO,CAAA,CAAA,gFANfJ,GAqBMC,EAAAS,EAAAP,CAAA,6FAlCDC,EAAI,CAAA,oOAmBDA,EAAO,CAAA,CAAA,uJAtEH,KAAAO,CAAa,EAAAC,EACb,CAAA,MAAAC,EAAyB,EAAE,EAAAD,EAC3B,CAAA,cAAAE,EAA+B,IAAI,EAAAF,EACnC,CAAA,QAAAG,EAAU,EAAE,EAAAH,EACZ,CAAA,OAAAI,EAAS,EAAK,EAAAJ,EACd,CAAA,SAAAK,EAMC,KAAK,EAAAL,GACN,iBAAAM,CAIR,EAAAN,EACQ,CAAA,eAAAO,EAAiB,EAAK,EAAAP,EACtB,CAAA,gBAAAQ,EAAkB,EAAK,EAAAR,EACvB,CAAA,YAAAS,EAAc,EAAI,EAAAT,EAClB,CAAA,SAAAU,EAAW,EAAI,EAAAV,GACf,KAAAW,CAAY,EAAAX,EAEjB,MAAAY,EAAWzB,SAEN,GAAA0B,CAA2B,EAAAb,EAG7B,SAAAc,EAAUC,EAAsB,QACpCR,GACHS,EAAA,GAAAC,EAAS,EAAE,EAERT,GACHO,EAAK,OAAM,EAGZA,EAAK,MAAK,KAKF,SAAAG,GACR,cAAAC,GAAa,KAIblB,EAAQkB,EAAc,KAAK,EAC3BP,EAAS,MAAM,4IAOJC,EAAEO,wBACDH,EAAM,KAAA,uhBA7BlBD,EAAA,GAAEC,EAAShB,CAAK,64DC7BD,KAAAoB,EAAM,EAAA,OAAgB,2JA+QT,KAAA7B,MAAK,KAAa,MAAAA,MAAK,yEAD1CA,EAAO,CAAA,CAAA,EAAU,MAAA8B,EAAA9B,GAAAA,EAAK,EAAA,EAAA,KAAK,CAAC,EAAE,mBAAnC,OAAI,GAAA,EAAA,sMAACA,EAAO,CAAA,CAAA,sFAAZ,OAAI+B,GAAA,6JACiD;AAAA,MAEtD,mcAJG/B,EAAO,CAAA,EAAC,QAAUA,KAAQ,CAAC,EAAE,KAAK,QAAMgC,GAAAhC,CAAA,iYAN9BiC,EAAM,wBAAoBjC,EAAG,CAAA,EAAA,IAAA,2BAAyBA,EAAM,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,iCAA+BA,EAAc,CAAA,EAAA,IAAA,UANzMJ,GAwB+BC,EAAAqC,EAAAnC,CAAA,EAvB9BoC,GAsBOD,EAAAE,CAAA,EAfND,GAEOC,EAAAC,CAAA,+CACPF,GAQOC,EAAAE,CAAA,kCACPH,GAEOC,EAAAG,CAAA,uGAjBIvC,EAAa,CAAA,CAAA,4FAOlBA,EAAO,CAAA,EAAC,QAAUA,KAAQ,CAAC,EAAE,KAAK,gOANEA,EAAG,CAAA,EAAA,IAAA,yCAAyBA,EAAM,CAAA,EAAA,IAAA,6CAA4BA,EAAW,CAAA,EAAA,IAAA,6CAA4BA,EAAW,CAAA,EAAA,IAAA,+CAA+BA,EAAc,CAAA,EAAA,IAAA,oKA5PpM,IAAAiC,GAAS,gBA0HJO,GAAuBC,EAAmBC,EAAgB,KAC7DD,QACG,SAEFE,EAAY,iBAAiBF,CAAI,SAE/B,SAASE,EAAU,iBAAiBD,CAAQ,CAAA,uDAxI1C,MAAAE,EAAK,EAAA,EAAApC,GAEL,WAAAqC,CAAkB,EAAArC,GAClB,cAAAsC,CAAqB,EAAAtC,GACrB,sBAAAuC,CAA6B,EAAAvC,EAC7B,CAAA,MAAAwC,EAAQ,CAAC,EAAAxC,EACT,CAAA,IAAAyC,EAAM,EAAE,EAAAzC,GACR,SAAA0C,CAAwB,EAAA1C,EAG/B2C,EAAiB,GACjBC,EAAS,EACTC,EACAC,EAAc,EACdC,EAAc,EACdC,EAAU,CAAA,EACVC,EACAC,EACAC,EAAM,EACNC,EACAC,EAAkB,IAClBC,EAAO,CAAA,EACPC,QAIEC,GAAU,OAAU,OAAW,IAC/BC,EAAMD,GACT,OAAO,sBACNE,GAAiCA,IAIjC,IAAAC,GAAiB,EACN,eAAAC,EAAmBC,EAAoB,CACjD,GAAAR,IAAoB,SAIhB,KAAA,CAAA,UAAAS,GAAcV,EACtBpC,EAAA,GAAAuB,EAAwBa,EAAS,YAAcA,EAAS,WAAW,EAEnEO,GAAiBR,GAAOW,EAAYhB,GAChC,IAAAvB,GAAIiB,EAED,KAAAmB,GAAiBtB,GAAcd,GAAIsC,EAAO,QAAM,CAClD,IAAAE,EAAMb,EAAK3B,GAAIiB,CAAK,EACnBuB,SACJtB,EAAMlB,GAAI,CAAC,QACLF,GAAI,EACV0C,EAAMb,EAAK3B,GAAIiB,CAAK,GAEjB,IAAAwB,EAAKD,GAAK,sBAAqB,EAAG,OACjCC,IACJA,EAAKrB,GAEA,MAAAsB,GAAcjB,EAAWzB,EAAC,EAAIyC,EACpCL,IAAkBM,GAClB1C,IAAK,EAGNP,EAAA,GAAAyB,EAAMlB,EAAC,EACD,MAAA2C,GAAYL,EAAO,OAASpB,EAE5B0B,GAAmBf,EAAS,aAAeA,EAAS,aACtDe,GAAmB,IACtBR,IAAkBQ,QAGfC,GAAsBpB,EAAW,OAAQqB,GAAC,OAAYA,GAAM,QAAQ,EACxErD,EAAA,EAAA2B,EACCyB,GAAoB,OAAM,CAAEE,EAAGC,IAAMD,EAAIC,EAAG,CAAC,EAC7CH,GAAoB,MAAM,MAE3BxB,EAASsB,GAAYvB,CAAc,EACnCK,EAAW,OAASa,EAAO,aACrBxC,GAAI,EACLgB,EAEMsB,GAAiBtB,OAC3BC,EAAgBqB,GAAiB,CAAC,EAElC3C,EAAA,GAAAsB,EAAgBD,CAAU,OAJ1BC,EAAgBqB,GAAiB,CAAC,QAO7BtC,GAAI,EAKI,eAAAmD,GAAkBC,EAAiB,CACjDhB,EAAG,SAAA,CACS,GAAA,OAAAgB,GAAM,SAAQ,aACnBC,EAAS,OAAUD,GAAM,SAAW,GAAQE,EAAWF,CAAC,EAC1DC,IAAc,KAGdA,IAAc,QACX,MAAAE,EAAgBH,EAAK,CAAA,SAAU,SAAS,CAAA,EAG3CC,IAAc,YACX,MAAAE,EAAgBH,EAAC,CAAI,SAAU,SAAS,EAAI,EAAI,KAKhD,SAAAE,EAAWF,EAAS,CACtB,MAAAI,EAAU3B,GAAQA,EAAKuB,EAAIjC,CAAK,MACjCqC,GAAWJ,EAAIjC,QACZ,OAEH,GAAA,CAAAqC,GAAWJ,GAAKhC,EAAM,QACnB,WAGA,KAAA,CAAA,IAAKqC,EAAiB,EAAA1B,EAAS,sBAAqB,EACpD,CAAA,IAAAD,GAAK,OAAAP,EAAW,EAAAiC,EAAQ,sBAAqB,SAEjD1B,GAAM2B,GAAe,GACjB,OAGJlC,GAASkC,GAAezB,EACpB,WAGD,GAaO,eAAA0B,EAAcC,EAAQ,OAC9BC,EAAa7B,EAAS,UAE5BF,EAAOL,EAAS,SACV,MAAAqC,GAAoBC,EAAY,OAAS3C,EAEzC4C,GAAiBpD,GAAuBkB,EAAK,CAAC,EAAG,kBAAkB,EAEnEmC,GAAgC,EAElCH,UACGN,EAAgBO,EAAY,OAAS,EAAC,CAAI,SAAU,MAAM,CAAA,EAG7D,IAAAG,GAAY,UAEPjB,EAAI,EAAGA,EAAInB,EAAK,OAAQmB,GAAK,EACrCrB,EAAWR,EAAQ6B,CAAC,EAAInB,EAAKmB,CAAC,EAAE,sBAAqB,EAAG,OAErD,IAAA9C,EAAI,EAEJgE,EAAIzC,EAAcsC,GAAiB,EACnCI,GAAW,CAAA,OAERjE,EAAI4D,EAAY,QAAM,CACtB,MAAAlB,EAAajB,EAAWzB,CAAC,GAAKoB,EAGhC,GAFJ6C,GAAYjE,CAAC,EAAI0C,EAEbsB,EAAItB,EAAaoB,GAAgCJ,EAAU,CAE9DK,GAAY/D,EACZP,EAAA,EAAAmC,EAAMoC,GAAKzC,EAAcsC,GAAiB,EAAC,QAG5CG,GAAKtB,EACL1C,GAAK,MAGN+D,GAAY,KAAK,IAAI,EAAGA,EAAS,EAC1B/D,EAAI4D,EAAY,QAAM,CACtB,MAAAlB,EAAajB,EAAWzB,CAAC,GAAKoB,KACpC4C,GAAKtB,EACL1C,GAAK,EACDgE,EAAIN,EAAa5B,QAItBrC,EAAA,GAAAwB,EAAQ8C,EAAS,EACjBtE,EAAA,GAAAyB,EAAMlB,CAAC,EACD,MAAA2C,GAAYiB,EAAY,OAAS1C,EACnCA,IAAQ,GACXzB,EAAA,GAAAyB,EAAM,EAAE,EAETzB,EAAA,EAAA2B,GAAkB4C,EAAIzC,GAAeL,CAAG,MACpCgD,GAAmBvB,GAAYvB,OAE5BpB,EAAI4D,EAAY,QACtB5D,GAAK,EACLyB,EAAWzB,CAAC,EAAIoB,EAEjB3B,EAAA,EAAA4B,EAAS6C,EAAgB,EACpB,SAAS7C,CAAM,GACnB5B,EAAA,EAAA4B,EAAS,GAAM,EAIK,eAAAgC,EACrBc,EACAC,EACAC,GAAY,GAAK,OAEXvE,GAAI,EAEJ,MAAAwE,GAAclD,MAEhBmD,GAAWJ,EAAQG,GACnBD,KACHE,GAAWA,GAAWzC,EAAkBwC,GAAc/C,GAGjD,MAAAqB,GAAmBf,EAAS,aAAeA,EAAS,aACtDe,GAAmB,IACtB2B,IAAY3B,UAGP4B,EAAK,CACV,IAAKD,GACL,SAAU,SACP,GAAAH,GAGJvC,EAAS,SAAS2C,CAAK,EAgBxBC,GAAO,IAAA,CACN9C,EAAOL,EAAS,SAChB7B,EAAA,GAAAiC,EAAU,EAAI,EACdW,EAAmBxB,CAAK,iBAYgBU,EAAW,KAAA,8DAGjCD,EAAQzB,wBASc2B,EAAW,KAAA,+DAjBxCK,EAAQhC,yBACDmC,EAAY0C,GAAA,QAAA,IAAA,IAAA,GAAA,kXA1O5B5C,EAAkBE,GAAc,QAAU,0BAkN5CvC,EAAA,GAAEmE,EAAc/C,CAAK,wBA3MnBa,GAAWQ,EAAU,IAAAG,EAAmBuB,CAAW,CAAA,uBAyDnDX,GAAkB9B,CAAQ,wBAoJ5B1B,EAAA,EAAEsC,EAAUE,GACV2B,EAAY,MAAM3C,EAAOC,CAAG,EAAE,IAAG,CAAEyD,EAAM3E,KAChC,CAAA,MAAOA,EAAIiB,EAAO,KAAA0D,CAAI,IAE/Bf,EACC,MAAM,EAAI9C,EAAa8C,EAAY,OAAUxC,EAAiB,CAAC,EAC/D,IAAK,CAAAuD,EAAM3E,KACF,CAAA,MAAOA,EAAIiB,EAAO,KAAA0D,CAAI,sBAGlClF,EAAA,GAAEsB,EAAgBgB,EAAQ,OAASX,EAAiB,EAAE,m8CCzP9C,CAAA,sBAAAxD,GAAA,KAAuBkC,GAAM,QAAA2E,EAAA,SAAuB,ubA4oB1DxG,EAAK,CAAA,CAAA,wCADPJ,EAEGC,EAAA8G,EAAA5G,CAAA,8BADDC,EAAK,CAAA,CAAA,wEAmBqBA,EAAK,CAAA,CAAA,iCAA/BJ,EAAyCC,EAAA+G,EAAA7G,CAAA,8BAAfC,EAAK,CAAA,CAAA,wJAgBpB,MACF,+WAOgBA,EAAc,EAAA,EAAA,iBAAA,EAFpB6G,EAAAC,EAAA,SAAA9G,QAAYA,EAAC,EAAA,CAAA,EAChB6G,EAAAC,EAAA,MAAA9G,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,0CAhB3C+G,EAAAC,EAAA,YAAAC,EAAAjH,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,CAAA,gCAD1C6G,EAAAG,EAAA,UAAAhH,QAAgBA,EAAC,EAAA,CAAA,EAEnBkH,GAAAF,EAAA,QAAAhH,MAAc,OAASA,MAAcA,EAAC,EAAA,CAAA,EAAI,MAAS,uBAHjEJ,EAgCIC,EAAAmH,EAAAjH,CAAA,EA3BHoC,EA0BK6E,EAAAG,CAAA,sBAfJhF,EAcKgF,EAAAL,CAAA,EATJ3E,EAQK2E,EAAAM,CAAA,EADJjF,EAAsDiF,EAAAC,CAAA,kNATnCrH,EAAc,EAAA,EAAA,yDAFpB6G,EAAAC,EAAA,SAAA9G,QAAYA,EAAC,EAAA,CAAA,uBAChB6G,EAAAC,EAAA,MAAA9G,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,GAhB3C,CAAAqF,GAAAlF,EAAA,CAAA,EAAA,UAAA8G,KAAAA,EAAAjH,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,8CAD1C6G,EAAAG,EAAA,UAAAhH,QAAgBA,EAAC,EAAA,CAAA,iBAEnBkH,GAAAF,EAAA,QAAAhH,MAAc,OAASA,MAAcA,EAAC,EAAA,CAAA,EAAI,MAAS,8MA0CpD,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,EAAA,CAAA,EAAIA,EAAQ,CAAA,OACpD,MACF,uPARPJ,EAYIC,EAAAyH,EAAAvH,CAAA,EAXHoC,EAUKmF,EAAAC,CAAA,+KALO,MAAM,QAAQvH,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,EAAA,CAAA,EAAIA,EAAQ,CAAA,mNA8BpCA,EAAK,CAAA,CAAA,iCAA/BJ,EAAyCC,EAAA+G,EAAA7G,CAAA,8BAAfC,EAAK,CAAA,CAAA,uCAD3BwH,EAAAxH,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACyH,GAAAzH,CAAA,kEAA3BA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,2RAgBpB,KAAAA,QAAgBA,EAAC,EAAA,6CAJXA,EAAQ,EAAA,EAACA,EAAC,EAAA,CAAA,EAAE,QAAK,iBAAjBA,EAAQ,EAAA,EAACA,EAAC,EAAA,CAAA,EAAE,OACfA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,kGAILA,EAAe,EAAA,CAAA,0ZAaPA,EAAc,EAAA,EAAA,iBAAA,EAFpB6G,EAAAC,EAAA,SAAA9G,QAAYA,EAAC,EAAA,CAAA,EAChB6G,EAAAC,EAAA,MAAA9G,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,0CAtB3C+G,EAAAC,EAAA,YAAAC,EAAAjH,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,CAAA,mCACzBA,EAAC,EAAA,EAAA,GAAA,gCAFpB6G,EAAAG,EAAA,QAAAhH,EAAgB,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAoBA,EAAC,EAAA,CAAA,uBADxDJ,EAuCIC,EAAAmH,EAAAjH,CAAA,EAlCHoC,EAiCK6E,EAAAG,CAAA,sBAhBJhF,EAeKgF,EAAAL,CAAA,EATJ3E,EAQK2E,EAAAM,CAAA,EADJjF,EAAsDiF,EAAAC,CAAA,qIAxBjDlH,EAAA,CAAA,EAAA,YAAAuH,EAAA,KAAA1H,QAAgBA,EAAC,EAAA,sGAJXA,EAAQ,EAAA,EAACA,EAAC,EAAA,CAAA,EAAE,kDACfA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,sEAiBGA,EAAc,EAAA,EAAA,yDAFpB6G,EAAAC,EAAA,SAAA9G,QAAYA,EAAC,EAAA,CAAA,uBAChB6G,EAAAC,EAAA,MAAA9G,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,GAtB3C,CAAAqF,GAAAlF,EAAA,CAAA,EAAA,UAAA8G,KAAAA,EAAAjH,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,8EACzBA,EAAC,EAAA,EAAA,GAAA,wBAFpB6G,EAAAG,EAAA,QAAAhH,EAAgB,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAoBA,EAAC,EAAA,CAAA,6IAFlDA,EAAQ,EAAA,CAAA,aAAsBA,EAAE,EAAA,kBAArC,OAAI,GAAA,EAAA,kKADPJ,EA2CIC,EAAA8H,EAAA5H,CAAA,gGA1CIC,EAAQ,EAAA,CAAA,8EAAb,OAAI+B,GAAA,6KAmD2B/B,EAAC,EAAA,CAAA,+FAQfA,EAAa,EAAA,IAAGA,EAAK,EAAA,CAAA,IAAIA,EAAC,EAAA,CAAA,wDAInC,KAAA4H,GAAO5H,EAAU,EAAA,EAAA,CAAAA,MAAOA,EAAC,EAAA,CAAA,CAAA,WACrB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,EAAA,CAAA,EAAIA,EAAQ,CAAA,kCAP9CA,EAAK,EAAA,EAAAA,EAAO,EAAA,CAAA,EAAAA,OAAG,QAAK,SAApB6H,EAAA,MAAA7H,EAAK,EAAA,EAAAA,EAAO,EAAA,CAAA,EAAAA,OAAG,OAClBA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,iWANZ+G,EAAAO,EAAA,QAAAQ,EAAA9H,EAAU,EAAA,IAAAA,EAAS,EAAA,CAAA,IAAAA,QAAM,EAAE,gCACrB6G,EAAAS,EAAA,QAAAM,GAAO5H,EAAW,EAAA,EAAA,CAAAA,MAAOA,EAAC,EAAA,CAAA,CAAA,CAAA,uCAPxCJ,EAwBIC,EAAAyH,EAAAvH,CAAA,EAfHoC,EAcKmF,EAAAC,CAAA,iLAVYvH,EAAa,EAAA,IAAGA,EAAK,EAAA,CAAA,IAAIA,EAAC,EAAA,CAAA,kGAInCG,EAAA,CAAA,EAAA,QAAAA,EAAA,CAAA,EAAA,YAAAuH,EAAA,KAAAE,GAAO5H,EAAU,EAAA,EAAA,CAAAA,MAAOA,EAAC,EAAA,CAAA,CAAA,sCACrB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,EAAA,CAAA,EAAIA,EAAQ,CAAA,yGAP9C0H,EAAA,MAAA1H,EAAK,EAAA,EAAAA,EAAO,EAAA,CAAA,EAAAA,OAAG,iEAClBA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,+BANZ,CAAAqF,GAAAlF,EAAA,CAAA,EAAA,MAAAA,EAAA,CAAA,EAAA,WAAA2H,KAAAA,EAAA9H,EAAU,EAAA,IAAAA,EAAS,EAAA,CAAA,IAAAA,QAAM,uDACnB6G,EAAAS,EAAA,QAAAM,GAAO5H,EAAW,EAAA,EAAA,CAAAA,MAAOA,EAAC,EAAA,CAAA,CAAA,CAAA,2FAFRA,EAAC,EAAA,CAAA,MAAA+H,iKAN3B/H,EAAI,EAAA,CAAA,aAAsBA,EAAE,EAAA,kBAAjC,OAAI,GAAA,EAAA,wKAD4CA,EAAK,EAAA,EAAG,IAAM,CAAC,UAAlEJ,EA4BIC,EAAA8H,EAAA5H,CAAA,6GA3BIC,EAAI,EAAA,CAAA,oFADuCA,EAAK,EAAA,EAAG,IAAM,CAAC,+BAC/D,OAAI+B,GAAA,wOAtDK/B,EAAM,EAAA,WAGRA,EAAc,EAAA,kKAJZA,EAAI,EAAA,IAAA,iBAAJA,EAAI,EAAA,GAEIA,EAAY,EAAA,IAAA,yBAAZA,EAAY,EAAA,GACJA,EAAe,EAAA,IAAA,iCAAfA,EAAe,EAAA,gOAF/BA,EAAM,EAAA,wBAGRA,EAAc,EAAA,6GAJZA,EAAI,EAAA,kDAEIA,EAAY,EAAA,0DACJA,EAAe,EAAA,sIAqFvCA,EAAS,CAAA,EAAC,CAAC,IAAM,WAASK,GAAAL,CAAA,IA0B1BA,EAAS,CAAA,EAAC,CAAC,IAAM,WAASgI,GAAAhI,CAAA,qGA3BhCJ,EAqDKC,EAAA0H,EAAAxH,CAAA,oDApDCC,EAAS,CAAA,EAAC,CAAC,IAAM,4GA0BjBA,EAAS,CAAA,EAAC,CAAC,IAAM,kaAzBrBJ,EAuBMC,EAAAS,EAAAP,CAAA,+MAFHkI,EAAAjI,KAAK,mBAAmB,EAAA,kcAfzBJ,EAcKC,EAAAuH,EAAArH,CAAA,EAJJoC,EAGCiF,EAAAC,CAAA,4BAEDlH,EAAA,CAAA,EAAA,KAAA8H,KAAAA,EAAAjI,KAAK,mBAAmB,EAAA,KAAAC,GAAAiI,EAAAD,CAAA,iQAK3BrI,EAuBMC,EAAAS,EAAAP,CAAA,+MAFHkI,EAAAjI,KAAK,sBAAsB,EAAA,4bAf5BJ,EAcKC,EAAAuH,EAAArH,CAAA,EAJJoC,EAGCiF,EAAAC,CAAA,4BAEDlH,EAAA,CAAA,EAAA,KAAA8H,KAAAA,EAAAjI,KAAK,sBAAsB,EAAA,KAAAC,GAAAiI,EAAAD,CAAA,mHAtO5BE,EAAAnI,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,GAAAoI,GAAApI,CAAA,EAoBvCqI,EAAArI,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACsI,GAAAtI,CAAA,OAKvBA,EAAQ,EAAA,CAAA,cAAsBA,EAAE,EAAA,kBAArC,OAAI+B,GAAA,EAAA,wDAuCC/B,EAAG,EAAA,CAAA,cAAsBA,EAAE,EAAA,kBAAhC,OAAI+B,GAAA,EAAA,qHAqBF,UACE,iBACO,iBACA,wKA4FZ/B,EAAQ,CAAA,GAAAgC,GAAAhC,CAAA,mYAlKSA,EAAa,EAAA,EAAC,QAAU,CAAC,yDAR/BA,EAAY,EAAA,EAAA,IAAA,8EADXA,EAAI,CAAA,CAAA,gCAVJ6G,EAAAM,EAAA,QAAAnH,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,CAAC,UAA7CJ,EA6OKC,EAAAsH,EAAApH,CAAA,wBAvOJoC,EA8KKgF,EAAAL,CAAA,EApKJ3E,EAkEO2E,EAAAyB,CAAA,wBA1DNpG,EAsCOoG,EAAAlG,CAAA,EArCNF,EAoCIE,EAAAmG,CAAA,0DAELrG,EAkBOoG,EAAAjG,CAAA,EAjBNH,EAgBIG,EAAAmG,CAAA,2KArFGzI,EAAoB,EAAA,CAAA,yBACfA,EAAoB,EAAA,CAAA,qFAK9BA,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,yDAoBvCA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,uFAKtBA,EAAQ,EAAA,CAAA,8EAuCRA,EAAG,EAAA,CAAA,sFA9CQA,EAAa,EAAA,EAAC,QAAU,CAAC,mQAR/BA,EAAY,EAAA,EAAA,IAAA,8EADXA,EAAI,CAAA,CAAA,EA2KhBA,EAAQ,CAAA,qHArLI6G,EAAAM,EAAA,QAAAnH,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,CAAC,+BA0BtC,OAAI+B,GAAA,0BAuCJ,OAAIA,GAAA,8TAxnBD2G,IAAO,QACR,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,EAAG,EAAE,WA+XzCC,GACRC,EACAC,EAA4B,QAErBA,EAAmB,OAAOC,CAAO,EAE/B,SAAAA,EAAQC,EAAiB,CAC7B,IAAAC,KACG,OAAAJ,EAAK,MAAM;AAAA,CAAI,EAAE,MAAMK,CAAW,EAEhC,SAAAA,EAAYC,EAAY,KAC3BA,QACG,GAGJ,IAAAC,EAASD,EAAK,MAAMH,CAAS,EAAE,OAC/B,OAAAC,EAAQ,IACXA,EAAQG,GAEFH,IAAUG,GAAUA,EAAS,IAK9B,SAAAC,GAAiBC,EAAgB,OACnCC,EAAW,KAAKD,EAAS,MAAM,GAAG,EAAE,CAAC,CAAA,EACrCE,EAAWF,EAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAE5DG,EAAS,IAAA,YAAYF,EAAS,MAAM,EACpCG,EAAE,IAAO,WAAWD,CAAE,UAEnBzH,EAAI,EAAGA,EAAIuH,EAAS,OAAQvH,IACpC0H,EAAG1H,CAAC,EAAIuH,EAAS,WAAWvH,CAAC,EAGnB,OAAA,IAAA,KAAM,CAAAyH,CAAE,EAAK,CAAA,KAAMD,CAAQ,CAAA,8BAle5B,SAAA1I,CAA+B,EAAAL,EAC/B,CAAA,MAAAkJ,EAAuB,IAAI,EAAAlJ,EAC3B,CAAA,WAAAmJ,EAAa,EAAI,EAAAnJ,GACjB,QAAAoJ,EAAO,EAAA,EAAApJ,GACP,OAAAqJ,EAAM,EAAA,EAAArJ,GACN,UAAAsJ,CAAwC,EAAAtJ,GACxC,UAAAuJ,CAAwC,EAAAvJ,GACxC,iBAAAM,CAIR,EAAAN,EAEQ,CAAA,SAAAU,EAAW,EAAI,EAAAV,EACf,CAAA,KAAAwJ,EAAO,EAAK,EAAAxJ,GACZ,KAAAW,CAAY,EAAAX,GACZ,KAAAyJ,CAAmB,EAAAzJ,EAEnB,CAAA,OAAAyB,EAAS,GAAG,EAAAzB,EACZ,CAAA,YAAAS,EAAc,EAAI,EAAAT,GAClB,cAAA0J,EAAa,EAAA,EAAA1J,GACb,OAAA2J,CAAwB,EAAA3J,GACxB,eAAA4J,CAAgC,EAAA5J,EAEvC0C,EAAqC,GAC9B,CAAA,cAAAxC,EAAmC,IAAI,EAAAF,EACvC,CAAA,QAAAG,EAA6B,IAAI,EAAAH,EACxC6J,EAEE,MAAAjJ,GAAWzB,KASb,IAAA2K,EAAoC,SAElCC,GAAW,CAAIhG,EAAaiG,IACjC9D,IAAOnC,CAAG,IAAIiG,CAAG,GAAG,UAejBC,EAAG,CAAA,EAWE,SAAAC,GAAaC,EAAc,CAC/B,IAAAnG,EAAKmG,GAAK,MACVb,EAAU,CAAC,IAAM,SAAWtF,EAAG,OAASsF,EAAU,CAAC,EAAA,OAChDc,EAAO,MAAMd,EAAU,CAAC,EAAItF,EAAG,MAAM,EACzC,KAAK,EAAE,EACP,IAAG,CAAEqG,EAAG9I,IAAC,GAAQA,EAAIyC,EAAG,MAAM,EAAA,EAChCA,EAAKA,EAAG,OAAOoG,CAAI,EAGf,MAAA,CAAApG,GAAMA,EAAG,SAAW,EACjB,MAAMsF,EAAU,CAAC,CACtB,EAAA,KAAK,CAAC,EACN,IAAK,CAAAe,EAAG9I,IAAC,CACH,MAAA+I,EAAMpC,iBACZ+B,EAAIK,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAL,CAAA,GAC3B,GAAIK,EAAK,MAAO,KAAK,UAAU/I,EAAI,CAAC,KAIzCyC,EAAG,IAAK,CAAAuG,EAAGhJ,IAAC,CACZ,MAAA+I,EAAMpC,iBACZ+B,EAAIK,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAL,CAAA,EAC3B,CAAA,GAAIK,EAAK,MAAOC,GAAK,EAAE,IAIzB,SAAAC,EAAaC,EAA8B,OAI7CC,EAAkBD,EAAQ,OACzB,OAAA,MACNlB,EAAU,CAAC,IAAM,SAEdmB,EAAkBnB,EAAU,CAAC,EAD7BA,EAAU,CAAC,EAGVmB,CAAe,EAElB,KAAK,CAAC,EACN,IAAK,CAAAL,EAAG9I,IACR,MACC+H,EAAU,CAAC,IAAM,QACdA,EAAU,CAAC,EACXoB,EAAkB,EACjBD,EAAQ,CAAC,EAAE,OACXrB,EAAQ,MAAM,EAEjB,KAAK,CAAC,EACN,IAAK,CAAAiB,EAAGM,KAAC,CACH,MAAAC,GAAK1C,KACX,OAAAlH,EAAA,GAAAiJ,EAAIW,EAAE,EAAIX,EAAIW,EAAE,GAAO,CAAA,MAAO,KAAM,KAAM,IAAI,EAAAX,CAAA,EACrC,CAAK,MAAOQ,IAAUlJ,CAAC,IAAIoJ,EAAC,GAAK,GAAI,GAAAC,WAO/CC,EAAWX,GAAad,CAAO,EAC/B0B,WAQKC,GAAe,MACvBF,EAAWX,GAAad,CAAO,CAAA,OAE/B0B,EAAc1B,EAAQ,MAAK,CAAA,EAC3B4B,SAQG9E,EAAI,CAAA,CAAA,CAAA,EAEJ+E,iBAEWD,IAAc,CAC5BpK,GAAS,SAAQ,CAChB,KAAMsF,EAAK,IAAKgF,GAAMA,EAAE,IAAO,CAAA,CAAA,MAAAjL,KAAYA,CAAK,CAAA,EAChD,QAAS4K,EAAS,IAAKN,GAAMA,EAAE,KAAK,EACpC,SAAU7J,EACP,KACiB,CAAA,cAAAR,EAAwB,QAAAC,CAAO,IAI5C,SAAAgL,GACRC,EACAC,EACA3G,EAAyB,CAEpB,GAAA,CAAA2G,QAAc,UACfjC,EAAQiC,CAAK,IAAMD,EAAI,IACtB1G,IAAc,MAAK,MAAS,eAC5BA,IAAc,MAAK,MAAS,mBAG1B,OAGC,SAAA4G,EAAoBV,EAAU,CAC/B,OAAA1E,EAAK,QACVqF,EAAKC,EAAKjK,IAAC,OACLoJ,EAAIa,EAAI,QACZC,GAAMC,GAAOC,KAAOf,IAAOc,GAAM,GAAKC,GAAIF,GAAI,EAC7C,EAGI,OAAAd,OAAWY,EAAO,CAAAhK,EAAGoJ,CAAC,GAE5B,CAAA,KAAK,kBAIMiB,EAAWrK,EAAWoJ,EAAS,CACxC,CAAAjK,GAAY0G,GAAO0C,EAAU,CAAAvI,EAAGoJ,CAAC,CAAA,QAEtCb,EAAO,CAAIvI,EAAGoJ,CAAC,CAAA,WAGPkB,GACRC,EACAC,EAAgC,OAE1BC,EAAG,CACR,WAAU,CAAG,EAAG,CAAC,EACjB,UAAS,CAAG,EAAC,EAAI,EACjB,UAAS,CAAG,EAAG,CAAC,EAChB,QAAO,CAAA,GAAO,CAAC,GACdF,CAAG,EAECvK,EAAIwK,EAAe,CAAC,EAAIC,EAAI,CAAC,EAC7BrB,EAAIoB,EAAe,CAAC,EAAIC,EAAI,CAAC,EAE/B,GAAAzK,EAAI,GAAKoJ,GAAK,EACjB3J,EAAA,GAAAiL,EAAkBtB,CAAC,EACnB3J,EAAA,GAAA0B,EAAW,EAAK,OAEV,MAAAwJ,GAAUhG,EAAK3E,CAAC,IAAIoJ,CAAC,EAC3B3J,EAAA,GAAA0B,EAAWwJ,GAAO,CAAI3K,EAAGoJ,CAAC,EAAIjI,CAAQ,GAIpC,IAAAnC,GAAiB,GAEN,eAAA4L,GAAeC,EAAoB,CAC7C,GAAAH,IAAoB,IAASI,KAAgB,GACxC,OAAAD,EAAM,IAAG,KACX,iBACJ1J,EAAQ,CAAI,EAAGuJ,CAAe,CAAA,EAC9BjL,EAAA,GAAAiL,EAAkB,EAAK,aAEnB,iBACJA,EACCA,EAAkB,EAAIA,EAAkB,EAAIA,CAAe,aAExD,aACJjL,EAAA,GAAAiL,EACCA,EAAkBpB,EAAS,OAAS,EACjCoB,EAAkB,EAClBA,CAAe,aAEf,SACJG,EAAM,eAAc,EACpBpL,EAAA,GAAAiL,EAAkB,EAAK,YAEnB,QACJG,EAAM,eAAc,YAIlB1J,eAIEnB,EAAGoJ,CAAC,EAAIjI,EAEP,OAAA0J,EAAM,IAAG,KACX,iBACA,gBACA,gBACA,aACAtC,EAAO,MACXsC,EAAM,eAAc,EACpBP,GAAYO,EAAM,IAAM,CAAA7K,EAAGoJ,CAAC,CAAA,YAGxB,aACCjK,EAAQ,MACb0L,EAAM,eAAc,EACpBpL,EAAA,GAAA8I,EAAU,EAAK,YAEX,YACCpJ,EAAQ,MACb0L,EAAM,eAAc,EAEhBA,EAAM,UACTE,GAAQ/K,CAAC,QACHF,GAAI,EAEVL,EAAA,GAAA0B,EAAY,CAAAnB,EAAI,EAAGoJ,CAAC,CAAA,GAEhBvD,GAAO0C,EAAU,CAAAvI,EAAGoJ,CAAC,CAAA,GACxB3J,EAAA,GAAA8I,EAAU,EAAK,QACTzI,GAAI,OACVqB,EAAQ,CAAInB,EAAGoJ,CAAC,CAAA,QAEhBb,EAAO,CAAIvI,EAAGoJ,CAAC,CAAA,YAKb,gBACCjK,EAAQ,MACRoJ,IACJsC,EAAM,eAAc,EACpBpL,EAAA,GAAAkF,EAAK3E,CAAC,EAAEoJ,CAAC,EAAE,MAAQ,GAAEzE,CAAA,aAGlB,aACCxF,EAAQ,MACRoJ,IACJsC,EAAM,eAAc,EACpBpL,EAAA,GAAAkF,EAAK3E,CAAC,EAAEoJ,CAAC,EAAE,MAAQ,GAAEzE,CAAA,aAGlB,MACA,IAAAxB,EAAY0H,EAAM,SAAY,GAAI,EAElCG,EAAYrG,EAAK3E,CAAC,EAAEoJ,EAAIjG,CAAS,EACjC8H,GACHtG,IAAO3E,EAAImD,CAAS,IAAIA,EAAY,EAAI,EAAImG,EAAS,OAAS,CAAC,GAE5D0B,GAAaC,MAChBJ,EAAM,eAAc,EACpBpL,EAAA,GAAA0B,EAAW6J,GACPhL,EAAGoJ,EAAIjG,CAAS,EAChB,CAAAnD,EAAImD,EAAWA,EAAY,EAAI,EAAImG,EAAS,OAAS,CAAC,CAAA,GAE3D7J,EAAA,GAAA8I,EAAU,EAAK,oBAIVpJ,EAAQ,OAEV,CAAAoJ,GAAYA,GAAW1C,GAAO0C,EAAU,CAAAvI,EAAGoJ,CAAC,KAC9CyB,EAAM,IAAI,SAAW,IAErBpL,EAAA,GAAAT,GAAiB,EAAI,OACrBuJ,EAAO,CAAIvI,EAAGoJ,CAAC,CAAA,mBAKJ8B,GAAkBlL,EAAWoJ,EAAS,CAChDvD,GAAO0C,EAAU,CAAAvI,EAAGoJ,CAAC,CAAA,IACzB3J,EAAA,GAAAqL,GAAc,EAAK,EACnBrL,EAAA,GAAAiL,EAAkB,EAAK,EACvBjL,EAAA,GAAA8I,EAAU,EAAK,OACfpH,EAAQ,CAAInB,EAAGoJ,CAAC,CAAA,QACVtJ,GAAI,EACVqL,GAAO,MAAK,OAITC,EACAC,EAEK,SAAAC,GAAY7C,EAAW,CACpB,OAAA4C,GAAY,UAAYA,IAAY5C,GAC9ChJ,EAAA,GAAA2L,EAAiB,KAAK,EACtB3L,EAAA,GAAA4L,EAAU5C,CAAG,GAET2C,IAAmB,MACtB3L,EAAA,GAAA2L,EAAiB,KAAK,EACZA,IAAmB,OAC7B3L,EAAA,GAAA2L,EAAiB,KAAK,MAKrBN,GAEA7L,GAAkB,GAClByL,EAAkC,GACvB,eAAAa,GAAYvL,EAAWwL,EAAU,GAAK,EAC/CrM,GAAY4I,EAAU,CAAC,IAAM,WAAa+C,KAAgB9K,IAC/DP,EAAA,GAAA0B,EAAW,EAAK,EAChB1B,EAAA,GAAAiL,EAAkB1K,CAAC,EACnBP,EAAA,GAAAqL,GAAc9K,CAAC,EACfP,EAAA,GAAAR,GAAkBuM,CAAO,GAGjB,SAAAC,GAAgBZ,EAAoB,IACvC1L,EAEG,OAAA0L,EAAM,IAAG,KACX,aACA,YACA,MACJA,EAAM,eAAc,EACpBpL,EAAA,GAAA0B,EAAW,EAAK,EAChB1B,EAAA,GAAAiL,EAAkBI,EAAW,EAC7BrL,EAAA,GAAAqL,GAAc,EAAK,EACnBK,GAAO,MAAK,SAKA,eAAAJ,GAAQ5G,EAAc,IACpCgH,GAAO,MAAK,EAERnD,EAAU,CAAC,IAAM,cACjBrD,EAAK,SAAW,EAAC,MACpBmD,EAAM,CAAI,MAAMD,EAAQ,MAAM,EAAE,KAAK,EAAE,CAAA,CAAA,SAIxClD,EAAK,OACJR,EAAQA,EAAQ,EAAIQ,EAAK,OACzB,EACA,MAAMA,EAAK,CAAC,EAAE,MAAM,EAClB,KAAK,CAAC,EACN,IAAK,CAAA,EAAG3E,IAAC,CACH,MAAA+I,EAAMpC,iBAEZ+B,EAAIK,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAL,CAAA,EAC3B,CAAA,GAAIK,EAAK,MAAO,EAAE,6BAK9BtJ,EAAA,GAAA0B,EAAY,CAAAgD,EAAQA,EAAQ,EAAIQ,EAAK,OAAS,EAAG,CAAC,CAAA,kBAKpC+G,IAAO,IACrBP,GAAO,MAAK,EACRpD,EAAU,CAAC,IAAM,mBACZ/H,EAAI,EAAGA,EAAI2E,EAAK,OAAQ3E,IAAC,CAC3B,MAAA+I,EAAMpC,UACZ+B,EAAIK,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAL,CAAA,EACpC/D,EAAK3E,CAAC,EAAE,KAAI,CAAG,GAAI+I,EAAK,MAAO,EAAE,CAAA,EAGlClB,EAAQ,KAAI,UAAWA,EAAQ,OAAS,CAAC,EAAA,wCAKnC/H,GAAI,EAEV,sBAAqB,IAAA,CACpByL,GAAY1D,EAAQ,OAAS,EAAG,EAAI,QAC9B8D,EAAQR,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,YAClDA,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,SAAQ,CAAG,KAAMQ,CAAK,CAAA,KAInD,SAAAC,GAAqBf,EAAY,CACzCA,EAAM,yBAAwB,QACvBgB,CAAO,EAAIhB,EAAM,aAAY,EAChCM,GAAO,SAASU,CAAO,IAI3BpM,EAAA,GAAA8I,EAAU,EAAK,EACf9I,EAAA,GAAAqL,GAAc,EAAK,EACnBrL,EAAA,GAAAiL,EAAkB,EAAK,EACvBjL,EAAA,GAAA0B,EAAW,EAAK,GAyCR,SAAA2K,GAAeC,EAAU,CAC3B,MAAAC,MAAa,WAEV,SAAAC,EAAYxI,EAA4B,KAC3CA,GAAG,QAAQ,QAAM,OAAWA,EAAE,OAAO,QAAW,SAAQ,aAEtDuD,CAAS,EAAIJ,GAAiBnD,EAAE,OAAO,OAAM,CAAG,IAAK,GAAI,CAAA,EAEzD,CAAAyI,GAAS,GAAAC,EAAI,EAAIC,GAAUpF,CAAS,EAAE,UAAUvD,EAAE,OAAO,MAAM,EAEtEhE,EAAA,GAAA6J,EAAWX,GACVZ,EAAU,CAAC,IAAM,QAAUmE,GAAK,MAAM,EAAGnE,EAAU,CAAC,CAAA,EAAKmE,EAAI,CAAA,EAG9DzM,EAAA,GAAAqI,EAASqE,EAAI,EACbH,EAAO,oBAAoB,UAAWC,CAAW,EAGlDD,EAAO,iBAAiB,UAAWC,CAAW,EAE9CD,EAAO,WAAWD,CAAI,EAGnB,IAAAM,GAAW,GAEN,SAAAC,GACRC,EAAkC,CAE9B,IAAAC,EAAMD,EAAG,CAAC,EAAE,MAAK,UACZvM,EAAI,EAAGA,EAAIuM,EAAG,OAAQvM,YACrBoJ,EAAI,EAAGA,EAAImD,EAAGvM,CAAC,EAAE,OAAQoJ,IAC1B,GAAAoD,EAAIpD,CAAC,EAAE,KAAK,GAAG,OAAM,GAAMmD,EAAGvM,CAAC,EAAEoJ,CAAC,EAAE,KAAK,GAAG,SAClDoD,EAAIpD,CAAC,EAAImD,EAAGvM,CAAC,EAAEoJ,CAAC,UAKZoD,MAMJC,GAAK,CAAA,EACLtB,GACA9K,YAEKqM,IAAe,CACjB,MAAAC,EAASF,GAAM,IAAK,CAAAnN,EAAIU,IACtBV,GAAI,aAAe,MAEvBqN,EAAO,SAAW,UACb3M,EAAI,EAAGA,EAAI2M,EAAO,OAAQ3M,IAClCmL,GAAO,MAAM,4BACInL,CAAC,GAAA,GACd2M,EAAO3M,CAAC,EAAI4M,GAAkBD,EAAO,MAAM,IAAA,EAK7C,IAAAE,GACH/E,EAAO,MAAM,EAAI5H,EAAS4H,EAAO,OAAU,EAAE,EAAE,OAAS,GAAK,GAC1D8E,GAAkB,WAEbE,GACR3C,EACA4C,EACAC,EACAvE,EACAgC,EAAmB,CAEf,IAAApB,GAAK,QAELlI,GAAYA,EAAS,CAAC,IAAKwD,GAAQxD,EAAS,CAAC,IAAKwD,EAAKxD,EAAS,CAAC,CAAA,IACpEkI,GAAK1E,EAAKxD,EAAS,CAAC,CAAA,EAAGA,EAAS,CAAC,CAAA,EAAG,WAE1BsH,GAAQ,UAAQ,CAAKgC,SAG1B,MAAAwC,OAAc,MAAM9C,EAAM,MAAM,EAAE,KAAI,CAAA,EAExC,GAAAM,IAAQ,MACXwC,GAAQ,KAAI,CAAEjN,GAAGoJ,KAChBe,EAAMnK,EAAC,EAAEyI,CAAG,EAAE,MAAQ0B,EAAMf,EAAC,EAAEX,CAAG,EAAE,MAAK,GAAQ,CAAC,UAEzCgC,IAAQ,MAClBwC,GAAQ,KAAI,CAAEjN,GAAGoJ,KAChBe,EAAMnK,EAAC,EAAEyI,CAAG,EAAE,MAAQ0B,EAAMf,EAAC,EAAEX,CAAG,EAAE,MAAK,GAAQ,CAAC,cAO9C,MAAAyE,OAAgB/C,CAAK,EACrBgD,GAAqBJ,EAAqB,CAAA,GAAAA,CAAc,EAAI,KAC5DK,GAAeJ,EAAe,CAAA,GAAAA,CAAQ,EAAI,QAChDC,GAAQ,QAAS,CAAAI,GAAeC,KAAW,CAC1CnD,EAAMmD,EAAW,EAAIJ,GAAUG,EAAa,EACxCN,GAAkBI,KACrBJ,EAAeO,EAAW,EAAIH,GAAmBE,EAAa,GAC3DL,GAAYI,KACfJ,EAASM,EAAW,EAAIF,GAAaC,EAAa,6BAKhDhE,GAAE,CACE,KAAA,CAAArJ,GAAGoJ,EAAC,EAAIW,EAAoBV,EAAE,OACrClI,EAAQ,CAAInB,GAAGoJ,EAAC,CAAA,GAQd,IAAAmE,GAAa,GAEjB9I,GAAO,IAAA,CACA,MAAA+I,EAAe,IAAA,qBAAsB,CAAAC,EAASD,IAAQ,CAC3DC,EAAQ,QAASC,GAAK,CACjBA,EAAM,gBAAc,CAAKH,KAC5Bb,8BAIDa,GAAaG,EAAM,mBAIrB,OAAAF,EAAS,QAAQrC,EAAM,OAGtBqC,EAAS,WAAU,kBAQJd,kDAqEiBD,GAAMrD,CAAC,EAAAvJ,2BAjDrByI,EAAM5D,GAAA,QAAA,IAAA,IAAA,GAAA,+DACbrE,GAAKR,gCAgJE8N,EAAA,GAAA,UAAAhJ,EAAKR,CAAK,EAAEiF,CAAC,EAAE,MAAK1K,CAAA,IAApBiG,EAAKR,CAAK,EAAEiF,CAAC,EAAE,MAAK1K,2DACvBgK,EAAIW,CAAE,EAAE,MAAK3K,CAAA,IAAbgK,EAAIW,CAAE,EAAE,MAAK3K,WAOL,MAAAkP,GAAA,KAAAnO,EAAA,GAAAT,GAAiB,EAAK,EAAGmM,GAAO,MAAK,aAjBnCd,EAAWlG,EAAOiF,CAAC,YACxB8B,GAAkB/G,EAAOiF,CAAC,YACvBiB,EAAWlG,EAAOiF,CAAC,kCA1CxBE,EAAStJ,CAAC,EAAE,MAAKtB,CAAA,IAAjB4K,EAAStJ,CAAC,EAAE,MAAKtB,2CACpBgK,EAAIW,CAAE,EAAE,MAAK3K,CAAA,IAAbgK,EAAIW,CAAE,EAAE,MAAK3K,WAKH,MAAAmP,GAAA7N,GAAAuL,GAAYvL,CAAC,EAahB8N,GAAA9N,GAAAsL,GAAYtL,CAAC,iBArCtB2E,EAAIjG,yCAEImO,GAAYnO,0BACJkO,GAAelO,iDAPlC+E,GAAMqI,GAAezE,GAAiB5D,EAAE,OAAO,IAAI,CAAA,6CApFnD0H,GAAMtL,sBAKJ4D,GAAMmH,GAAenH,CAAC,EAgLpBsK,GAAAtK,IAAOA,EAAE,kBAAmBsH,GAAO,GA0BnCiD,GAAAvK,IAAOA,EAAE,kBAAmBiI,GAAO,kuBA/rB3C7F,GAAOiC,EAAQ4B,CAAO,SAC7B/E,EAAOsE,EAAanB,CAA+B,CAAA,EACnDrI,EAAA,GAAAiK,EAAU5B,CAA+B,0BAnGrC3G,IAAa,GAAK,OACdqB,EAAKiG,CAAG,EAAItH,EACd,CAAA,MAAMqB,CAAG,GAAM,CAAA,MAAMiG,CAAG,GAC5BpJ,GAAS,SAAQ,CAChB,MAAK,CAAGmD,EAAKiG,CAAG,EAChB,MAAOD,GAAYhG,EAAKiG,CAAG,EAC3B,UAAW9D,EAAKnC,CAAG,EAAE,IAAKyL,GAAMA,EAAE,KAAK,4BA+ErCpI,GAAOgC,EAAS0B,CAAW,GAC/BC,2BAuRC7E,GAAQ8E,GAAc,wBAoHtBhK,EAAA,GAAA+M,EAAMF,GAAQ3H,CAAI,CAAA,yBAElB8H,GAAM,CAAC,GAAKC,4BAuEZI,GAAUnI,EAAMhG,EAAeC,EAASyM,EAASD,CAAc,6BAE/D8C,EAAc,CAAA,CAAK/M,GAAYA,EAAS,CAAC,CAAA,21ECtmBxB,KAAArB,IAAM,OAAgB,gEA4H7B,WAAA7B,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,wPAYTA,EAAQ,EAAA,0DAMPA,EAAW,EAAA,eAEf,KAAAA,MAAO,kDAGLA,EAAM,EAAA,EAAC,OAAO,sBACNA,EAAM,EAAA,EAAC,OAAO,gMA1BlB,WAAAA,MAAO,YACbG,EAAA,CAAA,EAAA,OAAA,CAAA,KAAAH,MAAO,IAAI,kBACbA,EAAc,EAAA,CAAA,+RAYTA,EAAQ,EAAA,uHAMPA,EAAW,EAAA,gCAEfG,EAAA,CAAA,EAAA,QAAA+P,EAAA,KAAAlQ,MAAO,gGAGLA,EAAM,EAAA,EAAC,OAAO,sCACNA,EAAM,EAAA,EAAC,OAAO,kOAnCtB,4CAGE,6CAGK,2ZAlHL,QAAA4J,EAAO,EAAA,EAAApJ,EACP,CAAA,QAAA2P,EAAU,EAAE,EAAA3P,GACZ,aAAA4P,EAAY,EAAA,EAAA5P,EACZ,CAAA,QAAAsD,EAAU,EAAI,EAAAtD,GACd,MAAAC,EAAK,CACf,KAAQ,CAAA,CAAA,GAAI,GAAI,EAAE,CAAA,EAClB,QAAU,CAAA,IAAK,IAAK,GAAG,EACvB,SAAU,SAEP4P,EAAY,GACL,CAAA,gBAAAC,EAAkB,EAAK,EAAA9P,GACvB,UAAAsJ,CAAwC,EAAAtJ,GACxC,UAAAuJ,CAAwC,EAAAvJ,EACxC,CAAA,MAAAkJ,EAAuB,IAAI,EAAAlJ,EAC3B,CAAA,WAAAmJ,EAAa,EAAI,EAAAnJ,GACjB,KAAAwJ,CAAa,EAAAxJ,GACb,SAAAK,CAA+B,EAAAL,EAC/B,CAAA,MAAA+P,EAAuB,IAAI,EAAA/P,EAC3B,CAAA,UAAAgQ,EAAgC,MAAS,EAAAhQ,GACzC,KAAAW,CAAY,EAAAX,EAEZ,CAAA,YAAAS,EAAc,EAAI,EAAAT,GAClB,cAAA0J,EAAa,EAAA,EAAA1J,GACb,OAAAiQ,CAKT,EAAAjQ,GACS,iBAAAM,CAIR,EAAAN,EACQ,CAAA,OAAAyB,EAA6B,MAAS,EAAAzB,GAEtC,eAAAkQ,CAA6B,EAAAlQ,GAC7B,YAAAmQ,CAAoB,EAAAnQ,EAE3B6K,GACA3K,EACAC,GACAkJ,EACW,eAAA+G,GAAclK,EAI5B,KACIwF,EAAQxF,GAAQjG,EAEpBe,EAAA,GAAA6J,GAAgB,CAAA,GAAAa,EAAM,SAAWtC,CAAO,CAAA,EACxCpI,EAAA,GAAAqI,EAASqC,EAAM,KAAW,CAAA,GAAAA,EAAM,IAAI,EAAA,CAAA,CAAA,EACpC1K,EAAA,GAAAd,EAAgBwL,GAAO,UAAU,kBAC1BA,GAAO,UAAU,aAAa,EAClC,IAAI,EACP1K,EAAA,GAAAb,IACEgQ,GAAezE,GAAO,UAAU,YAC1BA,GAAO,UAAU,OAAO,EAC5B,IAAI,QACFrK,GAAI,EAEV4O,EAAO,SAAS,QAAQ,EACnBH,GACJG,EAAO,SAAS,OAAO,EAIzBG,KAEAC,GAAW,IAAA,CACVrP,EAAA,GAAA8O,EAAkB,EAAK,KAWtB,MAAM,QAAQ7P,CAAK,GAAKA,IAAQ,CAAC,GAAG,SAAW,GAChDA,EAAM,OAAO,CAAC,GAAG,SAAW,KAE5BA,EAAK,CACJ,KAAI,CAAG,MAAMqJ,IAAY,CAAC,GAAK,CAAC,EAAE,KAAK,EAAE,CAAA,EACzC,QAAS,MAAMA,IAAY,CAAC,GAAK,CAAC,EAChC,KAAK,EAAE,EACP,IAAK,CAAAe,EAAG9I,IAAC,GAAQA,EAAI,CAAC,EAAA,EACxB,SAAU,OAIG,eAAA+O,EAAoBpK,EAIlC,CACI,KAAK,UAAUA,CAAI,IAAM2J,IAC5B7O,EAAA,GAAAf,MAAaiG,CAAI,CAAA,EACjBlF,EAAA,GAAA6O,EAAY,KAAK,UAAU5P,CAAK,CAAA,EAChCmQ,GAAclK,CAAI,GAmBI,MAAAqK,EAAA,IAAAN,EAAO,SAAS,eAAgBC,CAAc,EAYzDM,EAAAxL,GAAMsL,EAAoBtL,EAAE,MAAM,IAClCA,GAAMiL,EAAO,SAAS,SAAUjL,EAAE,MAAM,01BA3DhD6K,GAAa,KAAK,UAAU5P,CAAK,IAAM4P,IAC1C7O,EAAA,GAAA6O,EAAY,KAAK,UAAU5P,CAAK,CAAA,EAChCmQ"}