import{B as Qe}from"./Button-8nmImwVJ.js";import{I as Ye,S as Ze}from"./Index-WGC0_FkS.js";import{U as $e}from"./UploadText-DlCTYTPP.js";import{B as xe}from"./BlockLabel-CJsotHlk.js";import{E as et}from"./Empty-Vuj7-ssy.js";import{S as tt}from"./ShareButton-Ds9bG3Tz.js";import{a as lt}from"./DownloadLink-DYBmO3sz.js";import{I as qe}from"./Image-Bsh8Umrh.js";import"./index-COY1HN2y.js";import"./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js";import{M as Ne}from"./ModifyUpload-DZAlpNPL.js";import{I as we}from"./Image-BZaARumT.js";import{u as nt}from"./Blocks-aR9ucLZz.js";import{B as it}from"./FileUpload-fvQx1gQG.js";/* empty css                                              */import"./Upload-Cp8Go_XF.js";import"./file-url-Bf0nK4ai.js";import"./svelte/svelte.js";import"./Undo-CpmTQw3B.js";import"./File-BQ_9P3Ye.js";var be=Object.prototype.hasOwnProperty;function ve(l,e,t){for(t of l.keys())if(x(t,e))return t}function x(l,e){var t,n,i;if(l===e)return!0;if(l&&e&&(t=l.constructor)===e.constructor){if(t===Date)return l.getTime()===e.getTime();if(t===RegExp)return l.toString()===e.toString();if(t===Array){if((n=l.length)===e.length)for(;n--&&x(l[n],e[n]););return n===-1}if(t===Set){if(l.size!==e.size)return!1;for(n of l)if(i=n,i&&typeof i=="object"&&(i=ve(e,i),!i)||!e.has(i))return!1;return!0}if(t===Map){if(l.size!==e.size)return!1;for(n of l)if(i=n[0],i&&typeof i=="object"&&(i=ve(e,i),!i)||!x(n[1],e.get(i)))return!1;return!0}if(t===ArrayBuffer)l=new Uint8Array(l),e=new Uint8Array(e);else if(t===DataView){if((n=l.byteLength)===e.byteLength)for(;n--&&l.getInt8(n)===e.getInt8(n););return n===-1}if(ArrayBuffer.isView(l)){if((n=l.byteLength)===e.byteLength)for(;n--&&l[n]===e[n];);return n===-1}if(!t||typeof l=="object"){n=0;for(t in l)if(be.call(l,t)&&++n&&!be.call(e,t)||!(t in e)||!x(l[t],e[t]))return!1;return Object.keys(e).length===n}}return l!==l&&e!==e}async function st(l){return l?`<div style="display: flex; flex-wrap: wrap; gap: 16px">${(await Promise.all(l.map(async([t,n])=>t===null||!t.url?"":await nt(t.url,"url")))).map(t=>`<img src="${t}" style="height: 400px" />`).join("")}</div>`:""}const{SvelteComponent:ot,add_render_callback:rt,append:T,attr:A,binding_callbacks:pe,bubble:ke,check_outros:W,create_component:M,destroy_component:F,destroy_each:Me,detach:R,element:E,empty:at,ensure_array_like:ue,flush:L,globals:ut,group_outros:J,init:ft,insert:C,listen:ee,mount_component:H,run_all:_t,safe_not_equal:ct,set_data:Fe,set_style:N,space:G,text:He,toggle_class:K,transition_in:p,transition_out:y}=window.__gradio__svelte__internal,{window:Pe}=ut,{createEventDispatcher:ht}=window.__gradio__svelte__internal,{tick:gt}=window.__gradio__svelte__internal;function ze(l,e,t){const n=l.slice();return n[41]=e[t],n[43]=t,n}function je(l,e,t){const n=l.slice();return n[44]=e[t],n[45]=e,n[43]=t,n}function ye(l){let e,t;return e=new xe({props:{show_label:l[2],Icon:qe,label:l[3]||"Gallery"}}),{c(){M(e.$$.fragment)},m(n,i){H(e,n,i),t=!0},p(n,i){const o={};i[0]&4&&(o.show_label=n[2]),i[0]&8&&(o.label=n[3]||"Gallery"),e.$set(o)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){y(e.$$.fragment,n),t=!1},d(n){F(e,n)}}}function mt(l){let e,t,n,i,o,g,d=l[17]&&l[7]&&Be(l),s=l[12]&&Te(l),u=l[9]&&Se(l),c=ue(l[13]),m=[];for(let r=0;r<c.length;r+=1)m[r]=De(ze(l,c,r));const a=r=>y(m[r],1,1,()=>{m[r]=null});return{c(){d&&d.c(),e=G(),t=E("div"),n=E("div"),s&&s.c(),i=G(),u&&u.c(),o=G();for(let r=0;r<m.length;r+=1)m[r].c();A(n,"class","grid-container svelte-hpz95u"),N(n,"--grid-cols",l[4]),N(n,"--grid-rows",l[5]),N(n,"--object-fit",l[8]),N(n,"height",l[6]),K(n,"pt-6",l[2]),A(t,"class","grid-wrap svelte-hpz95u"),K(t,"fixed-height",!l[6]||l[6]=="auto")},m(r,h){d&&d.m(r,h),C(r,e,h),C(r,t,h),T(t,n),s&&s.m(n,null),T(n,i),u&&u.m(n,null),T(n,o);for(let w=0;w<m.length;w+=1)m[w]&&m[w].m(n,null);g=!0},p(r,h){if(r[17]&&r[7]?d?(d.p(r,h),h[0]&131200&&p(d,1)):(d=Be(r),d.c(),p(d,1),d.m(e.parentNode,e)):d&&(J(),y(d,1,1,()=>{d=null}),W()),r[12]?s?(s.p(r,h),h[0]&4096&&p(s,1)):(s=Te(r),s.c(),p(s,1),s.m(n,i)):s&&(J(),y(s,1,1,()=>{s=null}),W()),r[9]?u?(u.p(r,h),h[0]&512&&p(u,1)):(u=Se(r),u.c(),p(u,1),u.m(n,o)):u&&(J(),y(u,1,1,()=>{u=null}),W()),h[0]&8194){c=ue(r[13]);let w;for(w=0;w<c.length;w+=1){const I=ze(r,c,w);m[w]?(m[w].p(I,h),p(m[w],1)):(m[w]=De(I),m[w].c(),p(m[w],1),m[w].m(n,null))}for(J(),w=c.length;w<m.length;w+=1)a(w);W()}(!g||h[0]&16)&&N(n,"--grid-cols",r[4]),(!g||h[0]&32)&&N(n,"--grid-rows",r[5]),(!g||h[0]&256)&&N(n,"--object-fit",r[8]),(!g||h[0]&64)&&N(n,"height",r[6]),(!g||h[0]&4)&&K(n,"pt-6",r[2]),(!g||h[0]&64)&&K(t,"fixed-height",!r[6]||r[6]=="auto")},i(r){if(!g){p(d),p(s),p(u);for(let h=0;h<c.length;h+=1)p(m[h]);g=!0}},o(r){y(d),y(s),y(u),m=m.filter(Boolean);for(let h=0;h<m.length;h+=1)y(m[h]);g=!1},d(r){r&&(R(e),R(t)),d&&d.d(r),s&&s.d(),u&&u.d(),Me(m,r)}}}function dt(l){let e,t;return e=new et({props:{unpadded_box:!0,size:"large",$$slots:{default:[wt]},$$scope:{ctx:l}}}),{c(){M(e.$$.fragment)},m(n,i){H(e,n,i),t=!0},p(n,i){const o={};i[1]&32768&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){y(e.$$.fragment,n),t=!1},d(n){F(e,n)}}}function Be(l){let e,t,n,i,o,g,d,s,u,c,m,a,r,h=l[10]&&Ae(l);i=new Ne({props:{i18n:l[11],absolute:!1}}),i.$on("clear",l[28]),d=new we({props:{"data-testid":"detailed-image",src:l[17].image.url,alt:l[17].caption||"",title:l[17].caption||null,class:l[17].caption&&"with-caption",loading:"lazy"}});let w=l[17]?.caption&&Ie(l),I=ue(l[13]),z=[];for(let b=0;b<I.length;b+=1)z[b]=Le(je(l,I,b));const v=b=>y(z[b],1,1,()=>{z[b]=null});return{c(){e=E("button"),t=E("div"),h&&h.c(),n=G(),M(i.$$.fragment),o=G(),g=E("button"),M(d.$$.fragment),s=G(),w&&w.c(),u=G(),c=E("div");for(let b=0;b<z.length;b+=1)z[b].c();A(t,"class","icon-buttons svelte-hpz95u"),A(g,"class","image-button svelte-hpz95u"),N(g,"height","calc(100% - "+(l[17].caption?"80px":"60px")+")"),A(g,"aria-label","detailed view of selected image"),A(c,"class","thumbnails scroll-hide svelte-hpz95u"),A(c,"data-testid","container_el"),A(e,"class","preview svelte-hpz95u")},m(b,k){C(b,e,k),T(e,t),h&&h.m(t,null),T(t,n),H(i,t,null),T(e,o),T(e,g),H(d,g,null),T(e,s),w&&w.m(e,null),T(e,u),T(e,c);for(let U=0;U<z.length;U+=1)z[U]&&z[U].m(c,null);l[32](c),m=!0,a||(r=[ee(g,"click",l[29]),ee(e,"keydown",l[19])],a=!0)},p(b,k){b[10]?h?(h.p(b,k),k[0]&1024&&p(h,1)):(h=Ae(b),h.c(),p(h,1),h.m(t,n)):h&&(J(),y(h,1,1,()=>{h=null}),W());const U={};k[0]&2048&&(U.i18n=b[11]),i.$set(U);const S={};if(k[0]&131072&&(S.src=b[17].image.url),k[0]&131072&&(S.alt=b[17].caption||""),k[0]&131072&&(S.title=b[17].caption||null),k[0]&131072&&(S.class=b[17].caption&&"with-caption"),d.$set(S),(!m||k[0]&131072)&&N(g,"height","calc(100% - "+(b[17].caption?"80px":"60px")+")"),b[17]?.caption?w?w.p(b,k):(w=Ie(b),w.c(),w.m(e,u)):w&&(w.d(1),w=null),k[0]&24578){I=ue(b[13]);let j;for(j=0;j<I.length;j+=1){const P=je(b,I,j);z[j]?(z[j].p(P,k),p(z[j],1)):(z[j]=Le(P),z[j].c(),p(z[j],1),z[j].m(c,null))}for(J(),j=I.length;j<z.length;j+=1)v(j);W()}},i(b){if(!m){p(h),p(i.$$.fragment,b),p(d.$$.fragment,b);for(let k=0;k<I.length;k+=1)p(z[k]);m=!0}},o(b){y(h),y(i.$$.fragment,b),y(d.$$.fragment,b),z=z.filter(Boolean);for(let k=0;k<z.length;k+=1)y(z[k]);m=!1},d(b){b&&R(e),h&&h.d(),F(i),F(d),w&&w.d(),Me(z,b),l[32](null),a=!1,_t(r)}}}function Ae(l){let e,t,n;return t=new Ye({props:{Icon:lt,label:l[11]("common.download")}}),t.$on("click",l[27]),{c(){e=E("div"),M(t.$$.fragment),A(e,"class","download-button-container svelte-hpz95u")},m(i,o){C(i,e,o),H(t,e,null),n=!0},p(i,o){const g={};o[0]&2048&&(g.label=i[11]("common.download")),t.$set(g)},i(i){n||(p(t.$$.fragment,i),n=!0)},o(i){y(t.$$.fragment,i),n=!1},d(i){i&&R(e),F(t)}}}function Ie(l){let e,t=l[17].caption+"",n;return{c(){e=E("caption"),n=He(t),A(e,"class","caption svelte-hpz95u")},m(i,o){C(i,e,o),T(e,n)},p(i,o){o[0]&131072&&t!==(t=i[17].caption+"")&&Fe(n,t)},d(i){i&&R(e)}}}function Le(l){let e,t,n,i,o=l[43],g,d,s;t=new we({props:{src:l[44].image.url,title:l[44].caption||null,"data-testid":"thumbnail "+(l[43]+1),alt:"",loading:"lazy"}});const u=()=>l[30](e,o),c=()=>l[30](null,o);function m(){return l[31](l[43])}return{c(){e=E("button"),M(t.$$.fragment),n=G(),A(e,"class","thumbnail-item thumbnail-small svelte-hpz95u"),A(e,"aria-label",i="Thumbnail "+(l[43]+1)+" of "+l[13].length),K(e,"selected",l[1]===l[43])},m(a,r){C(a,e,r),H(t,e,null),T(e,n),u(),g=!0,d||(s=ee(e,"click",m),d=!0)},p(a,r){l=a;const h={};r[0]&8192&&(h.src=l[44].image.url),r[0]&8192&&(h.title=l[44].caption||null),t.$set(h),(!g||r[0]&8192&&i!==(i="Thumbnail "+(l[43]+1)+" of "+l[13].length))&&A(e,"aria-label",i),o!==l[43]&&(c(),o=l[43],u()),(!g||r[0]&2)&&K(e,"selected",l[1]===l[43])},i(a){g||(p(t.$$.fragment,a),g=!0)},o(a){y(t.$$.fragment,a),g=!1},d(a){a&&R(e),F(t),c(),d=!1,s()}}}function Te(l){let e,t,n;return t=new Ne({props:{i18n:l[11],absolute:!1}}),t.$on("clear",l[33]),{c(){e=E("div"),M(t.$$.fragment),A(e,"class","icon-button svelte-hpz95u")},m(i,o){C(i,e,o),H(t,e,null),n=!0},p(i,o){const g={};o[0]&2048&&(g.i18n=i[11]),t.$set(g)},i(i){n||(p(t.$$.fragment,i),n=!0)},o(i){y(t.$$.fragment,i),n=!1},d(i){i&&R(e),F(t)}}}function Se(l){let e,t,n;return t=new tt({props:{i18n:l[11],value:l[13],formatter:st}}),t.$on("share",l[34]),t.$on("error",l[35]),{c(){e=E("div"),M(t.$$.fragment),A(e,"class","icon-button svelte-hpz95u")},m(i,o){C(i,e,o),H(t,e,null),n=!0},p(i,o){const g={};o[0]&2048&&(g.i18n=i[11]),o[0]&8192&&(g.value=i[13]),t.$set(g)},i(i){n||(p(t.$$.fragment,i),n=!0)},o(i){y(t.$$.fragment,i),n=!1},d(i){i&&R(e),F(t)}}}function Ue(l){let e,t=l[41].caption+"",n;return{c(){e=E("div"),n=He(t),A(e,"class","caption-label svelte-hpz95u")},m(i,o){C(i,e,o),T(e,n)},p(i,o){o[0]&8192&&t!==(t=i[41].caption+"")&&Fe(n,t)},d(i){i&&R(e)}}}function De(l){let e,t,n,i,o,g,d,s;t=new we({props:{alt:l[41].caption||"",src:typeof l[41].image=="string"?l[41].image:l[41].image.url,loading:"lazy"}});let u=l[41].caption&&Ue(l);function c(){return l[36](l[43])}return{c(){e=E("button"),M(t.$$.fragment),n=G(),u&&u.c(),i=G(),A(e,"class","thumbnail-item thumbnail-lg svelte-hpz95u"),A(e,"aria-label",o="Thumbnail "+(l[43]+1)+" of "+l[13].length),K(e,"selected",l[1]===l[43])},m(m,a){C(m,e,a),H(t,e,null),T(e,n),u&&u.m(e,null),T(e,i),g=!0,d||(s=ee(e,"click",c),d=!0)},p(m,a){l=m;const r={};a[0]&8192&&(r.alt=l[41].caption||""),a[0]&8192&&(r.src=typeof l[41].image=="string"?l[41].image:l[41].image.url),t.$set(r),l[41].caption?u?u.p(l,a):(u=Ue(l),u.c(),u.m(e,i)):u&&(u.d(1),u=null),(!g||a[0]&8192&&o!==(o="Thumbnail "+(l[43]+1)+" of "+l[13].length))&&A(e,"aria-label",o),(!g||a[0]&2)&&K(e,"selected",l[1]===l[43])},i(m){g||(p(t.$$.fragment,m),g=!0)},o(m){y(t.$$.fragment,m),g=!1},d(m){m&&R(e),F(t),u&&u.d(),d=!1,s()}}}function wt(l){let e,t;return e=new qe({}),{c(){M(e.$$.fragment)},m(n,i){H(e,n,i),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){y(e.$$.fragment,n),t=!1},d(n){F(e,n)}}}function bt(l){let e,t,n,i,o,g,d;rt(l[26]);let s=l[2]&&ye(l);const u=[dt,mt],c=[];function m(a,r){return a[0]==null||a[13]==null||a[13].length===0?0:1}return t=m(l),n=c[t]=u[t](l),{c(){s&&s.c(),e=G(),n.c(),i=at()},m(a,r){s&&s.m(a,r),C(a,e,r),c[t].m(a,r),C(a,i,r),o=!0,g||(d=ee(Pe,"resize",l[26]),g=!0)},p(a,r){a[2]?s?(s.p(a,r),r[0]&4&&p(s,1)):(s=ye(a),s.c(),p(s,1),s.m(e.parentNode,e)):s&&(J(),y(s,1,1,()=>{s=null}),W());let h=t;t=m(a),t===h?c[t].p(a,r):(J(),y(c[h],1,1,()=>{c[h]=null}),W(),n=c[t],n?n.p(a,r):(n=c[t]=u[t](a),n.c()),p(n,1),n.m(i.parentNode,i))},i(a){o||(p(s),p(n),o=!0)},o(a){y(s),y(n),o=!1},d(a){a&&(R(e),R(i)),s&&s.d(a),c[t].d(a),g=!1,d()}}}function vt(l,e,t){let n,i,o,{show_label:g=!0}=e,{label:d}=e,{value:s=null}=e,{columns:u=[2]}=e,{rows:c=void 0}=e,{height:m="auto"}=e,{preview:a}=e,{allow_preview:r=!0}=e,{object_fit:h="cover"}=e,{show_share_button:w=!1}=e,{show_download_button:I=!1}=e,{i18n:z}=e,{selected_index:v=null}=e,{interactive:b}=e,{_fetch:k}=e;const U=ht();let S=!0,j=null,P=s;v==null&&a&&s?.length&&(v=0);let D=v;function ie(f){const q=f.target,X=f.offsetX,Z=q.offsetWidth/2;X<Z?t(1,v=n):t(1,v=i)}function fe(f){switch(f.code){case"Escape":f.preventDefault(),t(1,v=null);break;case"ArrowLeft":f.preventDefault(),t(1,v=n);break;case"ArrowRight":f.preventDefault(),t(1,v=i);break}}let V=[],O;async function _e(f){if(typeof f!="number"||(await gt(),V[f]===void 0))return;V[f]?.focus();const{left:q,width:X}=O.getBoundingClientRect(),{left:re,width:Z}=V[f].getBoundingClientRect(),$=re-q+Z/2-X/2+O.scrollLeft;O&&typeof O.scrollTo=="function"&&O.scrollTo({left:$<0?0:$,behavior:"smooth"})}let se=0;async function oe(f,q){let X;try{X=await k(f)}catch($){if($ instanceof TypeError){window.open(f,"_blank","noreferrer");return}throw $}const re=await X.blob(),Z=URL.createObjectURL(re),ae=document.createElement("a");ae.href=Z,ae.download=q,ae.click(),URL.revokeObjectURL(Z)}function ce(){t(16,se=Pe.innerHeight)}const he=()=>{const f=o?.image;if(f==null)return;const{url:q,orig_name:X}=f;q&&oe(q,X??"image")},ge=()=>t(1,v=null),_=f=>ie(f);function me(f,q){pe[f?"unshift":"push"](()=>{V[q]=f,t(14,V)})}const de=f=>t(1,v=f);function Ve(f){pe[f?"unshift":"push"](()=>{O=f,t(15,O)})}const Xe=()=>t(0,s=null);function We(f){ke.call(this,l,f)}function Je(f){ke.call(this,l,f)}const Ke=f=>t(1,v=f);return l.$$set=f=>{"show_label"in f&&t(2,g=f.show_label),"label"in f&&t(3,d=f.label),"value"in f&&t(0,s=f.value),"columns"in f&&t(4,u=f.columns),"rows"in f&&t(5,c=f.rows),"height"in f&&t(6,m=f.height),"preview"in f&&t(21,a=f.preview),"allow_preview"in f&&t(7,r=f.allow_preview),"object_fit"in f&&t(8,h=f.object_fit),"show_share_button"in f&&t(9,w=f.show_share_button),"show_download_button"in f&&t(10,I=f.show_download_button),"i18n"in f&&t(11,z=f.i18n),"selected_index"in f&&t(1,v=f.selected_index),"interactive"in f&&t(12,b=f.interactive),"_fetch"in f&&t(22,k=f._fetch)},l.$$.update=()=>{l.$$.dirty[0]&8388609&&t(23,S=s==null||s.length===0?!0:S),l.$$.dirty[0]&1&&t(13,j=s==null?null:s.map(f=>({image:f.image,caption:f.caption}))),l.$$.dirty[0]&27262979&&(x(P,s)||(S?(t(1,v=a&&s?.length?0:null),t(23,S=!1)):t(1,v=v!=null&&s!=null&&v<s.length?v:null),U("change"),t(24,P=s))),l.$$.dirty[0]&8194&&(n=((v??0)+(j?.length??0)-1)%(j?.length??0)),l.$$.dirty[0]&8194&&(i=((v??0)+1)%(j?.length??0)),l.$$.dirty[0]&33562626&&v!==D&&(t(25,D=v),v!==null&&U("select",{index:v,value:j?.[v]})),l.$$.dirty[0]&130&&r&&_e(v),l.$$.dirty[0]&8194&&t(17,o=v!=null&&j!=null?j[v]:null)},[s,v,g,d,u,c,m,r,h,w,I,z,b,j,V,O,se,o,ie,fe,oe,a,k,S,P,D,ce,he,ge,_,me,de,Ve,Xe,We,Je,Ke]}class pt extends ot{constructor(e){super(),ft(this,e,vt,bt,ct,{show_label:2,label:3,value:0,columns:4,rows:5,height:6,preview:21,allow_preview:7,object_fit:8,show_share_button:9,show_download_button:10,i18n:11,selected_index:1,interactive:12,_fetch:22},null,[-1,-1])}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),L()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),L()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),L()}get columns(){return this.$$.ctx[4]}set columns(e){this.$$set({columns:e}),L()}get rows(){return this.$$.ctx[5]}set rows(e){this.$$set({rows:e}),L()}get height(){return this.$$.ctx[6]}set height(e){this.$$set({height:e}),L()}get preview(){return this.$$.ctx[21]}set preview(e){this.$$set({preview:e}),L()}get allow_preview(){return this.$$.ctx[7]}set allow_preview(e){this.$$set({allow_preview:e}),L()}get object_fit(){return this.$$.ctx[8]}set object_fit(e){this.$$set({object_fit:e}),L()}get show_share_button(){return this.$$.ctx[9]}set show_share_button(e){this.$$set({show_share_button:e}),L()}get show_download_button(){return this.$$.ctx[10]}set show_download_button(e){this.$$set({show_download_button:e}),L()}get i18n(){return this.$$.ctx[11]}set i18n(e){this.$$set({i18n:e}),L()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),L()}get interactive(){return this.$$.ctx[12]}set interactive(e){this.$$set({interactive:e}),L()}get _fetch(){return this.$$.ctx[22]}set _fetch(e){this.$$set({_fetch:e}),L()}}const kt=pt,{SvelteComponent:zt,add_flush_callback:Ee,assign:jt,bind:Re,binding_callbacks:Ce,check_outros:yt,create_component:te,destroy_component:le,detach:Ge,empty:Bt,flush:B,get_spread_object:At,get_spread_update:It,group_outros:Lt,init:Tt,insert:Oe,mount_component:ne,safe_not_equal:St,space:Ut,transition_in:Q,transition_out:Y}=window.__gradio__svelte__internal,{createEventDispatcher:Dt}=window.__gradio__svelte__internal;function Et(l){let e,t,n,i;function o(s){l[26](s)}function g(s){l[27](s)}let d={label:l[4],show_label:l[3],columns:l[12],rows:l[13],height:l[14],preview:l[15],object_fit:l[17],interactive:l[19],allow_preview:l[16],show_share_button:l[18],show_download_button:l[20],i18n:l[21].i18n,_fetch:l[21].client.fetch};return l[1]!==void 0&&(d.selected_index=l[1]),l[0]!==void 0&&(d.value=l[0]),e=new kt({props:d}),Ce.push(()=>Re(e,"selected_index",o)),Ce.push(()=>Re(e,"value",g)),e.$on("change",l[28]),e.$on("select",l[29]),e.$on("share",l[30]),e.$on("error",l[31]),{c(){te(e.$$.fragment)},m(s,u){ne(e,s,u),i=!0},p(s,u){const c={};u[0]&16&&(c.label=s[4]),u[0]&8&&(c.show_label=s[3]),u[0]&4096&&(c.columns=s[12]),u[0]&8192&&(c.rows=s[13]),u[0]&16384&&(c.height=s[14]),u[0]&32768&&(c.preview=s[15]),u[0]&131072&&(c.object_fit=s[17]),u[0]&524288&&(c.interactive=s[19]),u[0]&65536&&(c.allow_preview=s[16]),u[0]&262144&&(c.show_share_button=s[18]),u[0]&1048576&&(c.show_download_button=s[20]),u[0]&2097152&&(c.i18n=s[21].i18n),u[0]&2097152&&(c._fetch=s[21].client.fetch),!t&&u[0]&2&&(t=!0,c.selected_index=s[1],Ee(()=>t=!1)),!n&&u[0]&1&&(n=!0,c.value=s[0],Ee(()=>n=!1)),e.$set(c)},i(s){i||(Q(e.$$.fragment,s),i=!0)},o(s){Y(e.$$.fragment,s),i=!1},d(s){le(e,s)}}}function Rt(l){let e,t;return e=new it({props:{value:null,root:l[5],label:l[4],max_file_size:l[21].max_file_size,file_count:"multiple",file_types:["image"],i18n:l[21].i18n,upload:l[21].client.upload,stream_handler:l[21].client.stream,$$slots:{default:[Ct]},$$scope:{ctx:l}}}),e.$on("upload",l[24]),e.$on("error",l[25]),{c(){te(e.$$.fragment)},m(n,i){ne(e,n,i),t=!0},p(n,i){const o={};i[0]&32&&(o.root=n[5]),i[0]&16&&(o.label=n[4]),i[0]&2097152&&(o.max_file_size=n[21].max_file_size),i[0]&2097152&&(o.i18n=n[21].i18n),i[0]&2097152&&(o.upload=n[21].client.upload),i[0]&2097152&&(o.stream_handler=n[21].client.stream),i[0]&2097152|i[1]&4&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){Y(e.$$.fragment,n),t=!1},d(n){le(e,n)}}}function Ct(l){let e,t;return e=new $e({props:{i18n:l[21].i18n,type:"gallery"}}),{c(){te(e.$$.fragment)},m(n,i){ne(e,n,i),t=!0},p(n,i){const o={};i[0]&2097152&&(o.i18n=n[21].i18n),e.$set(o)},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){Y(e.$$.fragment,n),t=!1},d(n){le(e,n)}}}function Gt(l){let e,t,n,i,o,g;const d=[{autoscroll:l[21].autoscroll},{i18n:l[21].i18n},l[2]];let s={};for(let a=0;a<d.length;a+=1)s=jt(s,d[a]);e=new Ze({props:s}),e.$on("clear_status",l[23]);const u=[Rt,Et],c=[];function m(a,r){return a[19]&&a[22]?0:1}return n=m(l),i=c[n]=u[n](l),{c(){te(e.$$.fragment),t=Ut(),i.c(),o=Bt()},m(a,r){ne(e,a,r),Oe(a,t,r),c[n].m(a,r),Oe(a,o,r),g=!0},p(a,r){const h=r[0]&2097156?It(d,[r[0]&2097152&&{autoscroll:a[21].autoscroll},r[0]&2097152&&{i18n:a[21].i18n},r[0]&4&&At(a[2])]):{};e.$set(h);let w=n;n=m(a),n===w?c[n].p(a,r):(Lt(),Y(c[w],1,1,()=>{c[w]=null}),yt(),i=c[n],i?i.p(a,r):(i=c[n]=u[n](a),i.c()),Q(i,1),i.m(o.parentNode,o))},i(a){g||(Q(e.$$.fragment,a),Q(i),g=!0)},o(a){Y(e.$$.fragment,a),Y(i),g=!1},d(a){a&&(Ge(t),Ge(o)),le(e,a),c[n].d(a)}}}function Ot(l){let e,t;return e=new Qe({props:{visible:l[8],variant:"solid",padding:!1,elem_id:l[6],elem_classes:l[7],container:l[9],scale:l[10],min_width:l[11],allow_overflow:!1,height:typeof l[14]=="number"?l[14]:void 0,$$slots:{default:[Gt]},$$scope:{ctx:l}}}),{c(){te(e.$$.fragment)},m(n,i){ne(e,n,i),t=!0},p(n,i){const o={};i[0]&256&&(o.visible=n[8]),i[0]&64&&(o.elem_id=n[6]),i[0]&128&&(o.elem_classes=n[7]),i[0]&512&&(o.container=n[9]),i[0]&1024&&(o.scale=n[10]),i[0]&2048&&(o.min_width=n[11]),i[0]&16384&&(o.height=typeof n[14]=="number"?n[14]:void 0),i[0]&8384575|i[1]&4&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){Y(e.$$.fragment,n),t=!1},d(n){le(e,n)}}}function qt(l,e,t){let n,{loading_status:i}=e,{show_label:o}=e,{label:g}=e,{root:d}=e,{elem_id:s=""}=e,{elem_classes:u=[]}=e,{visible:c=!0}=e,{value:m=null}=e,{container:a=!0}=e,{scale:r=null}=e,{min_width:h=void 0}=e,{columns:w=[2]}=e,{rows:I=void 0}=e,{height:z="auto"}=e,{preview:v}=e,{allow_preview:b=!0}=e,{selected_index:k=null}=e,{object_fit:U="cover"}=e,{show_share_button:S=!1}=e,{interactive:j}=e,{show_download_button:P=!1}=e,{gradio:D}=e;const ie=Dt(),fe=()=>D.dispatch("clear_status",i),V=_=>{const me=Array.isArray(_.detail)?_.detail:[_.detail];t(0,m=me.map(de=>({image:de,caption:null}))),D.dispatch("upload",m)},O=({detail:_})=>{t(2,i=i||{}),t(2,i.status="error",i),D.dispatch("error",_)};function _e(_){k=_,t(1,k)}function se(_){m=_,t(0,m)}const oe=()=>D.dispatch("change",m),ce=_=>D.dispatch("select",_.detail),he=_=>D.dispatch("share",_.detail),ge=_=>D.dispatch("error",_.detail);return l.$$set=_=>{"loading_status"in _&&t(2,i=_.loading_status),"show_label"in _&&t(3,o=_.show_label),"label"in _&&t(4,g=_.label),"root"in _&&t(5,d=_.root),"elem_id"in _&&t(6,s=_.elem_id),"elem_classes"in _&&t(7,u=_.elem_classes),"visible"in _&&t(8,c=_.visible),"value"in _&&t(0,m=_.value),"container"in _&&t(9,a=_.container),"scale"in _&&t(10,r=_.scale),"min_width"in _&&t(11,h=_.min_width),"columns"in _&&t(12,w=_.columns),"rows"in _&&t(13,I=_.rows),"height"in _&&t(14,z=_.height),"preview"in _&&t(15,v=_.preview),"allow_preview"in _&&t(16,b=_.allow_preview),"selected_index"in _&&t(1,k=_.selected_index),"object_fit"in _&&t(17,U=_.object_fit),"show_share_button"in _&&t(18,S=_.show_share_button),"interactive"in _&&t(19,j=_.interactive),"show_download_button"in _&&t(20,P=_.show_download_button),"gradio"in _&&t(21,D=_.gradio)},l.$$.update=()=>{l.$$.dirty[0]&1&&t(22,n=Array.isArray(m)?m.length===0:!m),l.$$.dirty[0]&2&&ie("prop_change",{selected_index:k})},[m,k,i,o,g,d,s,u,c,a,r,h,w,I,z,v,b,U,S,j,P,D,n,fe,V,O,_e,se,oe,ce,he,ge]}class sl extends zt{constructor(e){super(),Tt(this,e,qt,Ot,St,{loading_status:2,show_label:3,label:4,root:5,elem_id:6,elem_classes:7,visible:8,value:0,container:9,scale:10,min_width:11,columns:12,rows:13,height:14,preview:15,allow_preview:16,selected_index:1,object_fit:17,show_share_button:18,interactive:19,show_download_button:20,gradio:21},null,[-1,-1])}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),B()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),B()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),B()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),B()}get elem_id(){return this.$$.ctx[6]}set elem_id(e){this.$$set({elem_id:e}),B()}get elem_classes(){return this.$$.ctx[7]}set elem_classes(e){this.$$set({elem_classes:e}),B()}get visible(){return this.$$.ctx[8]}set visible(e){this.$$set({visible:e}),B()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),B()}get container(){return this.$$.ctx[9]}set container(e){this.$$set({container:e}),B()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),B()}get min_width(){return this.$$.ctx[11]}set min_width(e){this.$$set({min_width:e}),B()}get columns(){return this.$$.ctx[12]}set columns(e){this.$$set({columns:e}),B()}get rows(){return this.$$.ctx[13]}set rows(e){this.$$set({rows:e}),B()}get height(){return this.$$.ctx[14]}set height(e){this.$$set({height:e}),B()}get preview(){return this.$$.ctx[15]}set preview(e){this.$$set({preview:e}),B()}get allow_preview(){return this.$$.ctx[16]}set allow_preview(e){this.$$set({allow_preview:e}),B()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),B()}get object_fit(){return this.$$.ctx[17]}set object_fit(e){this.$$set({object_fit:e}),B()}get show_share_button(){return this.$$.ctx[18]}set show_share_button(e){this.$$set({show_share_button:e}),B()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),B()}get show_download_button(){return this.$$.ctx[20]}set show_download_button(e){this.$$set({show_download_button:e}),B()}get gradio(){return this.$$.ctx[21]}set gradio(e){this.$$set({gradio:e}),B()}}export{kt as BaseGallery,sl as default};
//# sourceMappingURL=Index-rf9QTX8y.js.map
