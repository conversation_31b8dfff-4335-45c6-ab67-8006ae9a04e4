{"version": 3, "file": "Index-rf9QTX8y.js", "sources": ["../../../../node_modules/.pnpm/dequal@2.0.2/node_modules/dequal/dist/index.mjs", "../../../../js/gallery/shared/utils.ts", "../../../../js/gallery/shared/Gallery.svelte", "../../../../js/gallery/Index.svelte"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "import { uploadToHuggingFace } from \"@gradio/utils\";\nimport type { FileData } from \"@gradio/client\";\n\nexport async function format_gallery_for_sharing(\n\tvalue: [FileData, string | null][] | null\n): Promise<string> {\n\tif (!value) return \"\";\n\tlet urls = await Promise.all(\n\t\tvalue.map(async ([image, _]) => {\n\t\t\tif (image === null || !image.url) return \"\";\n\t\t\treturn await uploadToHuggingFace(image.url, \"url\");\n\t\t})\n\t);\n\n\treturn `<div style=\"display: flex; flex-wrap: wrap; gap: 16px\">${urls\n\t\t.map((url) => `<img src=\"${url}\" style=\"height: 400px\" />`)\n\t\t.join(\"\")}</div>`;\n}\n", "<script lang=\"ts\">\n\timport { BlockLabel, Empty, ShareButton } from \"@gradio/atoms\";\n\timport { ModifyUpload } from \"@gradio/upload\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport { dequal } from \"dequal\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { tick } from \"svelte\";\n\n\timport { Download, Image as ImageIcon } from \"@gradio/icons\";\n\timport { FileData } from \"@gradio/client\";\n\timport { format_gallery_for_sharing } from \"./utils\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\ttype GalleryImage = { image: FileData; caption: string | null };\n\ttype GalleryData = GalleryImage[];\n\n\texport let show_label = true;\n\texport let label: string;\n\texport let value: GalleryData | null = null;\n\texport let columns: number | number[] | undefined = [2];\n\texport let rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let show_download_button = false;\n\texport let i18n: I18nFormatter;\n\texport let selected_index: number | null = null;\n\texport let interactive: boolean;\n\texport let _fetch: typeof fetch;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\t// tracks whether the value of the gallery was reset\n\tlet was_reset = true;\n\n\t$: was_reset = value == null || value.length === 0 ? true : was_reset;\n\n\tlet resolved_value: GalleryData | null = null;\n\t$: resolved_value =\n\t\tvalue == null\n\t\t\t? null\n\t\t\t: value.map((data) => ({\n\t\t\t\t\timage: data.image as FileData,\n\t\t\t\t\tcaption: data.caption\n\t\t\t\t}));\n\n\tlet prev_value: GalleryData | null = value;\n\tif (selected_index == null && preview && value?.length) {\n\t\tselected_index = 0;\n\t}\n\tlet old_selected_index: number | null = selected_index;\n\n\t$: if (!dequal(prev_value, value)) {\n\t\t// When value is falsy (clear button or first load),\n\t\t// preview determines the selected image\n\t\tif (was_reset) {\n\t\t\tselected_index = preview && value?.length ? 0 : null;\n\t\t\twas_reset = false;\n\t\t\t// Otherwise we keep the selected_index the same if the\n\t\t\t// gallery has at least as many elements as it did before\n\t\t} else {\n\t\t\tselected_index =\n\t\t\t\tselected_index != null && value != null && selected_index < value.length\n\t\t\t\t\t? selected_index\n\t\t\t\t\t: null;\n\t\t}\n\t\tdispatch(\"change\");\n\t\tprev_value = value;\n\t}\n\n\t$: previous =\n\t\t((selected_index ?? 0) + (resolved_value?.length ?? 0) - 1) %\n\t\t(resolved_value?.length ?? 0);\n\t$: next = ((selected_index ?? 0) + 1) % (resolved_value?.length ?? 0);\n\n\tfunction handle_preview_click(event: MouseEvent): void {\n\t\tconst element = event.target as HTMLElement;\n\t\tconst x = event.offsetX;\n\t\tconst width = element.offsetWidth;\n\t\tconst centerX = width / 2;\n\n\t\tif (x < centerX) {\n\t\t\tselected_index = previous;\n\t\t} else {\n\t\t\tselected_index = next;\n\t\t}\n\t}\n\n\tfunction on_keydown(e: KeyboardEvent): void {\n\t\tswitch (e.code) {\n\t\t\tcase \"Escape\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = null;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowLeft\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = previous;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowRight\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = next;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (selected_index !== old_selected_index) {\n\t\t\told_selected_index = selected_index;\n\t\t\tif (selected_index !== null) {\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: selected_index,\n\t\t\t\t\tvalue: resolved_value?.[selected_index]\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\t$: if (allow_preview) {\n\t\tscroll_to_img(selected_index);\n\t}\n\n\tlet el: HTMLButtonElement[] = [];\n\tlet container_element: HTMLDivElement;\n\n\tasync function scroll_to_img(index: number | null): Promise<void> {\n\t\tif (typeof index !== \"number\") return;\n\t\tawait tick();\n\n\t\tif (el[index] === undefined) return;\n\n\t\tel[index]?.focus();\n\n\t\tconst { left: container_left, width: container_width } =\n\t\t\tcontainer_element.getBoundingClientRect();\n\t\tconst { left, width } = el[index].getBoundingClientRect();\n\n\t\tconst relative_left = left - container_left;\n\n\t\tconst pos =\n\t\t\trelative_left +\n\t\t\twidth / 2 -\n\t\t\tcontainer_width / 2 +\n\t\t\tcontainer_element.scrollLeft;\n\n\t\tif (container_element && typeof container_element.scrollTo === \"function\") {\n\t\t\tcontainer_element.scrollTo({\n\t\t\t\tleft: pos < 0 ? 0 : pos,\n\t\t\t\tbehavior: \"smooth\"\n\t\t\t});\n\t\t}\n\t}\n\n\tlet window_height = 0;\n\n\t// Unlike `gr.Image()`, images specified via remote URLs are not cached in the server\n\t// and their remote URLs are directly passed to the client as `value[].image.url`.\n\t// The `download` attribute of the <a> tag doesn't work for remote URLs (https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#download),\n\t// so we need to download the image via JS as below.\n\tasync function download(file_url: string, name: string): Promise<void> {\n\t\tlet response;\n\t\ttry {\n\t\t\tresponse = await _fetch(file_url);\n\t\t} catch (error) {\n\t\t\tif (error instanceof TypeError) {\n\t\t\t\t// If CORS is not allowed (https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch#checking_that_the_fetch_was_successful),\n\t\t\t\t// open the link in a new tab instead, mimicing the behavior of the `download` attribute for remote URLs,\n\t\t\t\t// which is not ideal, but a reasonable fallback.\n\t\t\t\twindow.open(file_url, \"_blank\", \"noreferrer\");\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthrow error;\n\t\t}\n\t\tconst blob = await response.blob();\n\t\tconst url = URL.createObjectURL(blob);\n\t\tconst link = document.createElement(\"a\");\n\t\tlink.href = url;\n\t\tlink.download = name;\n\t\tlink.click();\n\t\tURL.revokeObjectURL(url);\n\t}\n\n\t$: selected_image =\n\t\tselected_index != null && resolved_value != null\n\t\t\t? resolved_value[selected_index]\n\t\t\t: null;\n</script>\n\n<svelte:window bind:innerHeight={window_height} />\n\n{#if show_label}\n\t<BlockLabel {show_label} Icon={ImageIcon} label={label || \"Gallery\"} />\n{/if}\n{#if value == null || resolved_value == null || resolved_value.length === 0}\n\t<Empty unpadded_box={true} size=\"large\"><ImageIcon /></Empty>\n{:else}\n\t{#if selected_image && allow_preview}\n\t\t<button on:keydown={on_keydown} class=\"preview\">\n\t\t\t<div class=\"icon-buttons\">\n\t\t\t\t{#if show_download_button}\n\t\t\t\t\t<div class=\"download-button-container\">\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Download}\n\t\t\t\t\t\t\tlabel={i18n(\"common.download\")}\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tconst image = selected_image?.image;\n\t\t\t\t\t\t\t\tif (image == null) {\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tconst { url, orig_name } = image;\n\t\t\t\t\t\t\t\tif (url) {\n\t\t\t\t\t\t\t\t\tdownload(url, orig_name ?? \"image\");\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t/>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\n\t\t\t\t<ModifyUpload\n\t\t\t\t\t{i18n}\n\t\t\t\t\tabsolute={false}\n\t\t\t\t\ton:clear={() => (selected_index = null)}\n\t\t\t\t/>\n\t\t\t</div>\n\t\t\t<button\n\t\t\t\tclass=\"image-button\"\n\t\t\t\ton:click={(event) => handle_preview_click(event)}\n\t\t\t\tstyle=\"height: calc(100% - {selected_image.caption ? '80px' : '60px'})\"\n\t\t\t\taria-label=\"detailed view of selected image\"\n\t\t\t>\n\t\t\t\t<Image\n\t\t\t\t\tdata-testid=\"detailed-image\"\n\t\t\t\t\tsrc={selected_image.image.url}\n\t\t\t\t\talt={selected_image.caption || \"\"}\n\t\t\t\t\ttitle={selected_image.caption || null}\n\t\t\t\t\tclass={selected_image.caption && \"with-caption\"}\n\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t/>\n\t\t\t</button>\n\t\t\t{#if selected_image?.caption}\n\t\t\t\t<caption class=\"caption\">\n\t\t\t\t\t{selected_image.caption}\n\t\t\t\t</caption>\n\t\t\t{/if}\n\t\t\t<div\n\t\t\t\tbind:this={container_element}\n\t\t\t\tclass=\"thumbnails scroll-hide\"\n\t\t\t\tdata-testid=\"container_el\"\n\t\t\t>\n\t\t\t\t{#each resolved_value as image, i}\n\t\t\t\t\t<button\n\t\t\t\t\t\tbind:this={el[i]}\n\t\t\t\t\t\ton:click={() => (selected_index = i)}\n\t\t\t\t\t\tclass=\"thumbnail-item thumbnail-small\"\n\t\t\t\t\t\tclass:selected={selected_index === i}\n\t\t\t\t\t\taria-label={\"Thumbnail \" + (i + 1) + \" of \" + resolved_value.length}\n\t\t\t\t\t>\n\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\tsrc={image.image.url}\n\t\t\t\t\t\t\ttitle={image.caption || null}\n\t\t\t\t\t\t\tdata-testid={\"thumbnail \" + (i + 1)}\n\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</button>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</button>\n\t{/if}\n\n\t<div class=\"grid-wrap\" class:fixed-height={!height || height == \"auto\"}>\n\t\t<div\n\t\t\tclass=\"grid-container\"\n\t\t\tstyle=\"--grid-cols:{columns}; --grid-rows:{rows}; --object-fit: {object_fit}; height: {height};\"\n\t\t\tclass:pt-6={show_label}\n\t\t>\n\t\t\t{#if interactive}\n\t\t\t\t<div class=\"icon-button\">\n\t\t\t\t\t<ModifyUpload\n\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\tabsolute={false}\n\t\t\t\t\t\ton:clear={() => (value = null)}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t\t{#if show_share_button}\n\t\t\t\t<div class=\"icon-button\">\n\t\t\t\t\t<ShareButton\n\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\ton:share\n\t\t\t\t\t\ton:error\n\t\t\t\t\t\tvalue={resolved_value}\n\t\t\t\t\t\tformatter={format_gallery_for_sharing}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t\t{#each resolved_value as entry, i}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"thumbnail-item thumbnail-lg\"\n\t\t\t\t\tclass:selected={selected_index === i}\n\t\t\t\t\ton:click={() => (selected_index = i)}\n\t\t\t\t\taria-label={\"Thumbnail \" + (i + 1) + \" of \" + resolved_value.length}\n\t\t\t\t>\n\t\t\t\t\t<Image\n\t\t\t\t\t\talt={entry.caption || \"\"}\n\t\t\t\t\t\tsrc={typeof entry.image === \"string\"\n\t\t\t\t\t\t\t? entry.image\n\t\t\t\t\t\t\t: entry.image.url}\n\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t/>\n\t\t\t\t\t{#if entry.caption}\n\t\t\t\t\t\t<div class=\"caption-label\">\n\t\t\t\t\t\t\t{entry.caption}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t</button>\n\t\t\t{/each}\n\t\t</div>\n\t</div>\n{/if}\n\n<style lang=\"postcss\">\n\t.preview {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tflex-direction: column;\n\t\tz-index: var(--layer-2);\n\t\tborder-radius: calc(var(--block-radius) - var(--block-border-width));\n\t\t-webkit-backdrop-filter: blur(8px);\n\t\tbackdrop-filter: blur(8px);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.preview::before {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\tz-index: var(--layer-below);\n\t\tbackground: var(--background-fill-primary);\n\t\topacity: 0.9;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.fixed-height {\n\t\tmin-height: var(--size-80);\n\t\tmax-height: 55vh;\n\t}\n\n\t@media (--screen-xl) {\n\t\t.fixed-height {\n\t\t\tmin-height: 450px;\n\t\t}\n\t}\n\n\t.image-button {\n\t\theight: calc(100% - 60px);\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t}\n\t.image-button :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n\t.thumbnails :global(img) {\n\t\tobject-fit: cover;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\t.preview :global(img.with-caption) {\n\t\theight: var(--size-full);\n\t}\n\n\t.caption {\n\t\tpadding: var(--size-2) var(--size-3);\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\ttext-align: center;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\talign-self: center;\n\t}\n\n\t.thumbnails {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-14);\n\t\toverflow-x: scroll;\n\t}\n\n\t.thumbnail-item {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow:\n\t\t\t0 0 0 2px var(--ring-color),\n\t\t\tvar(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\taspect-ratio: var(--ratio-square);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: clip;\n\t}\n\n\t.thumbnail-item:hover {\n\t\t--ring-color: var(--color-accent);\n\t\tfilter: brightness(1.1);\n\t}\n\n\t.thumbnail-item.selected {\n\t\t--ring-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small {\n\t\tflex: none;\n\t\ttransform: scale(0.9);\n\t\ttransition: 0.075s;\n\t\twidth: var(--size-9);\n\t\theight: var(--size-9);\n\t}\n\n\t.thumbnail-small.selected {\n\t\t--ring-color: var(--color-accent);\n\t\ttransform: scale(1);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small > img {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.grid-wrap {\n\t\tposition: relative;\n\t\tpadding: var(--size-2);\n\t\theight: var(--size-full);\n\t\toverflow-y: scroll;\n\t}\n\n\t.grid-container {\n\t\tdisplay: grid;\n\t\tposition: relative;\n\t\tgrid-template-rows: repeat(var(--grid-rows), minmax(100px, 1fr));\n\t\tgrid-template-columns: repeat(var(--grid-cols), minmax(100px, 1fr));\n\t\tgrid-auto-rows: minmax(100px, 1fr);\n\t\tgap: var(--spacing-lg);\n\t}\n\n\t.thumbnail-lg > :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.thumbnail-lg:hover .caption-label {\n\t\topacity: 0.5;\n\t}\n\n\t.caption-label {\n\t\tposition: absolute;\n\t\tright: var(--block-label-margin);\n\t\tbottom: var(--block-label-margin);\n\t\tz-index: var(--layer-1);\n\t\tborder-top: 1px solid var(--border-color-primary);\n\t\tborder-left: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-label-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--block-label-padding);\n\t\tmax-width: 80%;\n\t\toverflow: hidden;\n\t\tfont-size: var(--block-label-text-size);\n\t\ttext-align: left;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.icon-button {\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tright: 0;\n\t}\n\n\t.icon-buttons .download-button-container {\n\t\tmargin: var(--size-1) 0;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseGallery } from \"./shared/Gallery.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData, SelectData } from \"@gradio/utils\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\timport Gallery from \"./shared/Gallery.svelte\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { BaseFileUpload } from \"@gradio/file\";\n\n\texport let loading_status: LoadingStatus;\n\texport let show_label: boolean;\n\texport let label: string;\n\texport let root: string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { image: FileData; caption: string | null }[] | null = null;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let columns: number | number[] | undefined = [2];\n\texport let rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let selected_index: number | null = null;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let interactive: boolean;\n\texport let show_download_button = false;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tupload: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tprop_change: Record<string, any>;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\n\tconst dispatch = createEventDispatcher();\n\n\t$: no_value = Array.isArray(value) ? value.length === 0 : !value;\n\t$: selected_index, dispatch(\"prop_change\", { selected_index });\n</script>\n\n<Block\n\t{visible}\n\tvariant=\"solid\"\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\theight={typeof height === \"number\" ? height : undefined}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t{#if interactive && no_value}\n\t\t<BaseFileUpload\n\t\t\tvalue={null}\n\t\t\t{root}\n\t\t\t{label}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\tfile_count={\"multiple\"}\n\t\t\tfile_types={[\"image\"]}\n\t\t\ti18n={gradio.i18n}\n\t\t\tupload={gradio.client.upload}\n\t\t\tstream_handler={gradio.client.stream}\n\t\t\ton:upload={(e) => {\n\t\t\t\tconst files = Array.isArray(e.detail) ? e.detail : [e.detail];\n\t\t\t\tvalue = files.map((x) => ({ image: x, caption: null }));\n\t\t\t\tgradio.dispatch(\"upload\", value);\n\t\t\t}}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"gallery\" />\n\t\t</BaseFileUpload>\n\t{:else}\n\t\t<Gallery\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{columns}\n\t\t\t{rows}\n\t\t\t{height}\n\t\t\t{preview}\n\t\t\t{object_fit}\n\t\t\t{interactive}\n\t\t\t{allow_preview}\n\t\t\tbind:selected_index\n\t\t\tbind:value\n\t\t\t{show_share_button}\n\t\t\t{show_download_button}\n\t\t\ti18n={gradio.i18n}\n\t\t\t_fetch={gradio.client.fetch}\n\t\t/>\n\t{/if}\n</Block>\n"], "names": ["has", "find", "iter", "tar", "key", "dequal", "foo", "bar", "ctor", "len", "tmp", "format_gallery_for_sharing", "value", "image", "_", "uploadToHuggingFace", "url", "createEventDispatcher", "tick", "ImageIcon", "ctx", "dirty", "blocklabel_changes", "if_block0", "create_if_block_4", "create_if_block_3", "create_if_block_2", "i", "insert", "target", "div1", "anchor", "append", "div0", "each_blocks", "create_if_block_6", "if_block1", "create_if_block_5", "set_style", "button0", "button1", "Download", "div", "iconbutton_changes", "t_value", "caption", "set_data", "t", "toggle_class", "button", "if_block", "create_if_block_1", "create_if_block_7", "show_label", "$$props", "label", "columns", "rows", "height", "preview", "allow_preview", "object_fit", "show_share_button", "show_download_button", "i18n", "selected_index", "interactive", "_fetch", "dispatch", "was_reset", "resolved_value", "prev_value", "old_selected_index", "handle_preview_click", "event", "element", "x", "centerX", "$$invalidate", "previous", "next", "on_keydown", "e", "el", "container_element", "scroll_to_img", "index", "container_left", "container_width", "left", "width", "pos", "window_height", "download", "file_url", "name", "response", "error", "blob", "link", "selected_image", "orig_name", "clear_handler", "$$value", "click_handler_2", "clear_handler_1", "click_handler_3", "data", "gallery_changes", "basefileupload_changes", "uploadtext_changes", "block_changes", "loading_status", "root", "elem_id", "elem_classes", "visible", "container", "scale", "min_width", "gradio", "clear_status_handler", "files", "detail", "change_handler", "no_value"], "mappings": "+zBAAA,IAAIA,GAAM,OAAO,UAAU,eAE3B,SAASC,GAAKC,EAAMC,EAAKC,EAAK,CAC7B,IAAKA,KAAOF,EAAK,OAChB,GAAIG,EAAOD,EAAKD,CAAG,EAAG,OAAOC,CAE/B,CAEO,SAASC,EAAOC,EAAKC,EAAK,CAChC,IAAIC,EAAMC,EAAKC,EACf,GAAIJ,IAAQC,EAAK,MAAO,GAExB,GAAID,GAAOC,IAAQC,EAAKF,EAAI,eAAiBC,EAAI,YAAa,CAC7D,GAAIC,IAAS,KAAM,OAAOF,EAAI,YAAcC,EAAI,UAChD,GAAIC,IAAS,OAAQ,OAAOF,EAAI,aAAeC,EAAI,WAEnD,GAAIC,IAAS,MAAO,CACnB,IAAKC,EAAIH,EAAI,UAAYC,EAAI,OAC5B,KAAOE,KAASJ,EAAOC,EAAIG,CAAG,EAAGF,EAAIE,CAAG,CAAC,GAAE,CAE5C,OAAOA,IAAQ,EACf,CAED,GAAID,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EACFC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACH,EAAI,IAAIG,CAAG,EAAG,MAAO,GAE3B,MAAO,EACP,CAED,GAAIF,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EAAI,CAAC,EACPC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACL,EAAOI,EAAI,CAAC,EAAGF,EAAI,IAAIG,CAAG,CAAC,EAC/B,MAAO,GAGT,MAAO,EACP,CAED,GAAIF,IAAS,YACZF,EAAM,IAAI,WAAWA,CAAG,EACxBC,EAAM,IAAI,WAAWA,CAAG,UACdC,IAAS,SAAU,CAC7B,IAAKC,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAI,QAAQG,CAAG,IAAMF,EAAI,QAAQE,CAAG,GAAE,CAEvD,OAAOA,IAAQ,EACf,CAED,GAAI,YAAY,OAAOH,CAAG,EAAG,CAC5B,IAAKG,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAIG,CAAG,IAAMF,EAAIE,CAAG,GAAE,CAEvC,OAAOA,IAAQ,EACf,CAED,GAAI,CAACD,GAAQ,OAAOF,GAAQ,SAAU,CACrCG,EAAM,EACN,IAAKD,KAAQF,EAEZ,GADIN,GAAI,KAAKM,EAAKE,CAAI,GAAK,EAAEC,GAAO,CAACT,GAAI,KAAKO,EAAKC,CAAI,GACnD,EAAEA,KAAQD,IAAQ,CAACF,EAAOC,EAAIE,CAAI,EAAGD,EAAIC,CAAI,CAAC,EAAG,MAAO,GAE7D,OAAO,OAAO,KAAKD,CAAG,EAAE,SAAWE,CACnC,CACD,CAED,OAAOH,IAAQA,GAAOC,IAAQA,CAC/B,CChFA,eAAsBI,GACrBC,EACkB,CAClB,OAAKA,EAQE,2DAPI,MAAM,QAAQ,IACxBA,EAAM,IAAI,MAAO,CAACC,EAAOC,CAAC,IACrBD,IAAU,MAAQ,CAACA,EAAM,IAAY,GAClC,MAAME,GAAoBF,EAAM,IAAK,KAAK,CACjD,CAAA,GAIA,IAAKG,GAAQ,aAAaA,CAAG,4BAA4B,EACzD,KAAK,EAAE,CAAC,SAVS,EAWpB,6cCXU,uBAAAC,EAAA,SAAqC,2BACrC,CAAA,KAAAC,WAAoB,yOAkMEC,GAAkB,MAAAC,MAAS,wGAATC,EAAA,CAAA,EAAA,IAAAC,EAAA,MAAAF,MAAS,oIAKrDG,EAAAH,OAAkBA,EAAa,CAAA,GAAAI,GAAAJ,CAAA,IAgF7BA,EAAW,EAAA,GAAAK,GAAAL,CAAA,IASXA,EAAiB,CAAA,GAAAM,GAAAN,CAAA,OAWfA,EAAc,EAAA,CAAA,uBAAnB,OAAIO,GAAA,6OAvBcP,EAAO,CAAA,CAAA,oBAAgBA,EAAI,CAAA,CAAA,qBAAkBA,EAAU,CAAA,CAAA,eAAYA,EAAM,CAAA,CAAA,aACjFA,EAAU,CAAA,CAAA,6DAJoBA,EAAM,CAAA,GAAIA,EAAM,CAAA,GAAI,MAAM,+BAAtEQ,EAgDKC,EAAAC,EAAAC,CAAA,EA/CJC,EA8CKF,EAAAG,CAAA,+GAzHDb,OAAkBA,EAAa,CAAA,iHAgF7BA,EAAW,EAAA,oGASXA,EAAiB,CAAA,mHAWfA,EAAc,EAAA,CAAA,oBAAnB,OAAIO,GAAA,EAAA,2GAAJ,OAAIA,EAAAO,EAAA,OAAAP,GAAA,4CAvBcP,EAAO,CAAA,CAAA,mCAAgBA,EAAI,CAAA,CAAA,qCAAkBA,EAAU,CAAA,CAAA,8BAAYA,EAAM,CAAA,CAAA,2BACjFA,EAAU,CAAA,CAAA,qCAJoBA,EAAM,CAAA,GAAIA,EAAM,CAAA,GAAI,MAAM,8CA0BlE,OAAIO,GAAA,yNAtGa,0SAKbP,EAAoB,EAAA,GAAAe,GAAAf,CAAA,uCAqBd,EAAK,6EAYVA,EAAc,EAAA,EAAC,MAAM,QACrBA,EAAc,EAAA,EAAC,SAAW,SACxBA,EAAc,EAAA,EAAC,SAAW,WAC1BA,EAAc,EAAA,EAAC,SAAW,iCAI9B,IAAAgB,EAAAhB,OAAgB,SAAOiB,GAAAjB,CAAA,OAUpBA,EAAc,EAAA,CAAA,uBAAnB,OAAIO,GAAA,8TAtBsBW,EAAAC,EAAA,SAAA,gBAAAnB,EAAe,EAAA,EAAA,QAAU,OAAS,QAAM,GAAA,yLA9BtEQ,EAsEQC,EAAAW,EAAAT,CAAA,EArEPC,EAyBKQ,EAAAP,CAAA,2CACLD,EAcQQ,EAAAD,CAAA,2CAMRP,EAsBKQ,EAAAV,CAAA,2GArEcV,EAAU,EAAA,CAAA,iBAEvBA,EAAoB,EAAA,qLAiCnBA,EAAc,EAAA,EAAC,MAAM,yBACrBA,EAAc,EAAA,EAAC,SAAW,0BACxBA,EAAc,EAAA,EAAC,SAAW,4BAC1BA,EAAc,EAAA,EAAC,SAAW,6CARNkB,EAAAC,EAAA,SAAA,gBAAAnB,EAAe,EAAA,EAAA,QAAU,OAAS,QAAM,GAAA,EAYhEA,OAAgB,gFAUbA,EAAc,EAAA,CAAA,oBAAnB,OAAIO,GAAA,EAAA,2GAAJ,OAAIA,EAAAO,EAAA,OAAAP,GAAA,oFAAJ,OAAIA,GAAA,+PA/CGc,GACC,MAAArB,MAAK,iBAAiB,0HAH/BQ,EAeKC,EAAAa,EAAAX,CAAA,sCAZIV,EAAA,CAAA,EAAA,OAAAsB,EAAA,MAAAvB,MAAK,iBAAiB,wHAsC9BwB,EAAAxB,MAAe,QAAO,oFADxBQ,EAESC,EAAAgB,EAAAd,CAAA,iBADPV,EAAA,CAAA,EAAA,QAAAuB,KAAAA,EAAAxB,MAAe,QAAO,KAAA0B,GAAAC,EAAAH,CAAA,gFAiBhBxB,EAAK,EAAA,EAAC,MAAM,UACVA,EAAK,EAAA,EAAC,SAAW,mBACX,cAAgBA,EAAC,EAAA,EAAG,uOALtB,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,MAAM,EADnD4B,EAAAC,EAAA,WAAA7B,OAAmBA,EAAC,EAAA,CAAA,UAJrCQ,EAcQC,EAAAoB,EAAAlB,CAAA,mGANDX,EAAK,EAAA,EAAC,MAAM,yBACVA,EAAK,EAAA,EAAC,SAAW,uCAJb,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,yEAD7C4B,EAAAC,EAAA,WAAA7B,OAAmBA,EAAC,EAAA,CAAA,0KA0B1B,EAAK,2GAHjBQ,EAMKC,EAAAa,EAAAX,CAAA,kOAQIX,EAAc,EAAA,YACVT,kIANbiB,EAQKC,EAAAa,EAAAX,CAAA,oFAHIX,EAAc,EAAA,wHAqBnBwB,EAAAxB,MAAM,QAAO,sFADfQ,EAEKC,EAAAa,EAAAX,CAAA,iBADHV,EAAA,CAAA,EAAA,MAAAuB,KAAAA,EAAAxB,MAAM,QAAO,KAAA0B,GAAAC,EAAAH,CAAA,0EARVxB,EAAK,EAAA,EAAC,SAAW,cACVA,EAAK,EAAA,EAAC,OAAU,SACzBA,EAAM,EAAA,EAAA,MACNA,EAAK,EAAA,EAAC,MAAM,sBAGX,IAAA8B,EAAA9B,MAAM,SAAO+B,GAAA/B,CAAA,8KATN,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,MAAM,EAFnD4B,EAAAC,EAAA,WAAA7B,OAAmBA,EAAC,EAAA,CAAA,UAFrCQ,EAkBQC,EAAAoB,EAAAlB,CAAA,qHAXDX,EAAK,EAAA,EAAC,SAAW,6BACVA,EAAK,EAAA,EAAC,OAAU,SACzBA,EAAM,EAAA,EAAA,MACNA,EAAK,EAAA,EAAC,MAAM,eAGXA,MAAM,sFATC,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,4CAF7C4B,EAAAC,EAAA,WAAA7B,OAAmBA,EAAC,EAAA,CAAA,kUA7GpCA,EAAU,CAAA,GAAAgC,GAAAhC,CAAA,8CAGVA,EAAK,CAAA,GAAI,MAAQA,EAAc,EAAA,GAAI,MAAQA,EAAc,EAAA,EAAC,SAAW,EAAC,mKAHtEA,EAAU,CAAA,uXAtLH,CAAA,WAAAiC,EAAa,EAAI,EAAAC,GACjB,MAAAC,CAAa,EAAAD,EACb,CAAA,MAAA1C,EAA4B,IAAI,EAAA0C,EAChC,CAAA,QAAAE,GAA0C,CAAC,CAAA,EAAAF,EAC3C,CAAA,KAAAG,EAAsC,MAAS,EAAAH,EAC/C,CAAA,OAAAI,EAA0B,MAAM,EAAAJ,GAChC,QAAAK,CAAgB,EAAAL,EAChB,CAAA,cAAAM,EAAgB,EAAI,EAAAN,EACpB,CAAA,WAAAO,EACV,OAAO,EAAAP,EACG,CAAA,kBAAAQ,EAAoB,EAAK,EAAAR,EACzB,CAAA,qBAAAS,EAAuB,EAAK,EAAAT,GAC5B,KAAAU,CAAmB,EAAAV,EACnB,CAAA,eAAAW,EAAgC,IAAI,EAAAX,GACpC,YAAAY,CAAoB,EAAAZ,GACpB,OAAAa,CAAoB,EAAAb,EAEzB,MAAAc,EAAWnD,KAMb,IAAAoD,EAAY,GAIZC,EAAqC,KASrCC,EAAiC3D,EACjCqD,GAAkB,MAAQN,GAAW/C,GAAO,SAC/CqD,EAAiB,GAEd,IAAAO,EAAoCP,EAyB/B,SAAAQ,GAAqBC,EAAiB,OACxCC,EAAUD,EAAM,OAChBE,EAAIF,EAAM,QAEVG,EADQF,EAAQ,YACE,EAEpBC,EAAIC,EACPC,EAAA,EAAAb,EAAiBc,CAAQ,EAEzBD,EAAA,EAAAb,EAAiBe,CAAI,EAId,SAAAC,GAAWC,EAAgB,CAC3B,OAAAA,EAAE,KAAI,KACR,SACJA,EAAE,eAAc,EAChBJ,EAAA,EAAAb,EAAiB,IAAI,YAEjB,YACJiB,EAAE,eAAc,EAChBJ,EAAA,EAAAb,EAAiBc,CAAQ,YAErB,aACJG,EAAE,eAAc,EAChBJ,EAAA,EAAAb,EAAiBe,CAAI,aAuBpBG,EAAE,CAAA,EACFC,EAEW,eAAAC,GAAcC,EAAoB,IACrC,OAAAA,GAAU,iBACfpE,GAAI,EAENiE,EAAGG,CAAK,IAAM,QAAS,OAE3BH,EAAGG,CAAK,GAAG,cAEH,KAAMC,EAAgB,MAAOC,GACpCJ,EAAkB,wBACX,CAAA,KAAAK,GAAM,MAAAC,CAAK,EAAKP,EAAGG,CAAK,EAAE,wBAI5BK,EAFgBF,GAAOF,EAI5BG,EAAQ,EACRF,EAAkB,EAClBJ,EAAkB,WAEfA,GAA4B,OAAAA,EAAkB,UAAa,YAC9DA,EAAkB,SAAQ,CACzB,KAAMO,EAAM,EAAI,EAAIA,EACpB,SAAU,WAKT,IAAAC,GAAgB,iBAMLC,GAASC,EAAkBC,EAAY,KACjDC,MAEHA,EAAQ,MAAS7B,EAAO2B,CAAQ,QACxBG,EAAK,CACT,GAAAA,aAAiB,UAAS,CAI7B,OAAO,KAAKH,EAAU,SAAU,YAAY,eAIvCG,QAEDC,GAAI,MAASF,EAAS,OACtBhF,EAAM,IAAI,gBAAgBkF,EAAI,EAC9BC,GAAO,SAAS,cAAc,GAAG,EACvCA,GAAK,KAAOnF,EACZmF,GAAK,SAAWJ,EAChBI,GAAK,MAAK,EACV,IAAI,gBAAgBnF,CAAG,4DA0BXH,EAAQuF,GAAgB,MAC1B,GAAAvF,GAAS,kBAGL,IAAAG,EAAK,UAAAqF,CAAS,EAAKxF,EACvBG,GACH6E,GAAS7E,EAAKqF,GAAa,OAAO,GAUrBC,GAAA,IAAAxB,EAAA,EAAAb,EAAiB,IAAI,IAK5BS,GAAUD,GAAqBC,CAAK,+CAyBlCS,EAAGxD,CAAC,EAAA4E,YACE,MAAAC,GAAA7E,GAAAmD,EAAA,EAAAb,EAAiBtC,CAAC,6CAP1ByD,EAAiBmB,YAoCT,MAAAE,GAAA,IAAA3B,EAAA,EAAAlE,EAAQ,IAAI,oEAmBb,MAAA8F,GAAA/E,GAAAmD,EAAA,EAAAb,EAAiBtC,CAAC,umBA3QpCmD,EAAA,GAAAT,EAAYzD,GAAS,MAAQA,EAAM,SAAW,EAAI,GAAOyD,CAAS,mBAGlES,EAAA,GAAAR,EACF1D,GAAS,KACN,KACAA,EAAM,IAAK+F,KACX,MAAOA,EAAK,MACZ,QAASA,EAAK,OAAA,EAAA,CAAA,2BASVtG,EAAOkE,EAAY3D,CAAK,IAG3ByD,OACHJ,EAAiBN,GAAW/C,GAAO,OAAS,EAAI,IAAI,EACpDkE,EAAA,GAAAT,EAAY,EAAK,OAIjBJ,EACCA,GAAkB,MAAQrD,GAAS,MAAQqD,EAAiBrD,EAAM,OAC/DqD,EACA,MAELG,EAAS,QAAQ,EACjBU,EAAA,GAAAP,EAAa3D,CAAK,yBAGhBmE,IACAd,GAAkB,IAAMK,GAAgB,QAAU,GAAK,IACxDA,GAAgB,QAAU,yBACzBU,IAASf,GAAkB,GAAK,IAAMK,GAAgB,QAAU,4BAmC9DL,IAAmBO,IACtBM,EAAA,GAAAN,EAAqBP,CAAc,EAC/BA,IAAmB,MACtBG,EAAS,SAAQ,CAChB,MAAOH,EACP,MAAOK,IAAiBL,CAAc,wBAMnCL,GACNyB,GAAcpB,CAAc,sBAgE5Ba,EAAA,GAAEsB,EACFnC,GAAkB,MAAQK,GAAkB,KACzCA,EAAeL,CAAc,EAC7B,IAAI,o+DCxLC,CAAA,sBAAAhD,WAAqC,oSAsGtC,KAAAG,MAAO,YACLA,EAAM,EAAA,EAAC,OAAO,sqBADhBC,EAAA,CAAA,EAAA,UAAAuF,EAAA,KAAAxF,MAAO,8BACLA,EAAM,EAAA,EAAC,OAAO,0PA1Cf,0BAGQ,cAAAA,MAAO,yBACV,uBACC,OAAO,EACd,KAAAA,MAAO,YACLA,EAAM,EAAA,EAAC,OAAO,sBACNA,EAAM,EAAA,EAAC,OAAO,4MALfC,EAAA,CAAA,EAAA,UAAAwF,EAAA,cAAAzF,MAAO,eAGhBC,EAAA,CAAA,EAAA,UAAAwF,EAAA,KAAAzF,MAAO,8BACLA,EAAM,EAAA,EAAC,OAAO,wCACNA,EAAM,EAAA,EAAC,OAAO,mMAYZ,KAAAA,MAAO,wFAAPC,EAAA,CAAA,EAAA,UAAAyF,EAAA,KAAA1F,MAAO,0IA3Bd,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,0IAGd,OAAAA,OAAeA,EAAQ,EAAA,EAAA,qLALf,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,wWAZV,4FAMO,GACD,OAAA,OAAAA,OAAW,SAAWA,EAAM,EAAA,EAAG,8RAA/BC,EAAA,CAAA,EAAA,QAAA0F,EAAA,OAAA,OAAA3F,OAAW,SAAWA,EAAM,EAAA,EAAG,8KAhDnC,eAAA4F,CAA6B,EAAA1D,GAC7B,WAAAD,CAAmB,EAAAC,GACnB,MAAAC,CAAa,EAAAD,GACb,KAAA2D,CAAY,EAAA3D,EACZ,CAAA,QAAA4D,EAAU,EAAE,EAAA5D,GACZ,aAAA6D,EAAY,EAAA,EAAA7D,EACZ,CAAA,QAAA8D,EAAU,EAAI,EAAA9D,EACd,CAAA,MAAA1C,EAA8D,IAAI,EAAA0C,EAClE,CAAA,UAAA+D,EAAY,EAAI,EAAA/D,EAChB,CAAA,MAAAgE,EAAuB,IAAI,EAAAhE,EAC3B,CAAA,UAAAiE,EAAgC,MAAS,EAAAjE,EACzC,CAAA,QAAAE,GAA0C,CAAC,CAAA,EAAAF,EAC3C,CAAA,KAAAG,EAAsC,MAAS,EAAAH,EAC/C,CAAA,OAAAI,EAA0B,MAAM,EAAAJ,GAChC,QAAAK,CAAgB,EAAAL,EAChB,CAAA,cAAAM,EAAgB,EAAI,EAAAN,EACpB,CAAA,eAAAW,EAAgC,IAAI,EAAAX,EACpC,CAAA,WAAAO,EACV,OAAO,EAAAP,EACG,CAAA,kBAAAQ,EAAoB,EAAK,EAAAR,GACzB,YAAAY,CAAoB,EAAAZ,EACpB,CAAA,qBAAAS,EAAuB,EAAK,EAAAT,GAC5B,OAAAkE,CAQT,EAAAlE,EAEI,MAAAc,GAAWnD,KAsBOwG,GAAA,IAAAD,EAAO,SAAS,eAAgBR,CAAc,IAaxD9B,GAAC,CACN,MAAAwC,GAAQ,MAAM,QAAQxC,EAAE,MAAM,EAAIA,EAAE,OAAU,CAAAA,EAAE,MAAM,EAC5DJ,EAAA,EAAAlE,EAAQ8G,GAAM,IAAK9C,KAAS,CAAA,MAAOA,GAAG,QAAS,IAAI,EAAA,CAAA,EACnD4C,EAAO,SAAS,SAAU5G,CAAK,OAEnB,OAAA+G,KAAM,CAClB7C,EAAA,EAAAkC,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BQ,EAAO,SAAS,QAASG,CAAM,uDAOf,MAAAC,GAAA,IAAAJ,EAAO,SAAS,SAAU5G,CAAK,KACpCsE,GAAMsC,EAAO,SAAS,SAAUtC,EAAE,MAAM,KACzCA,GAAMsC,EAAO,SAAS,QAAStC,EAAE,MAAM,KACvCA,GAAMsC,EAAO,SAAS,QAAStC,EAAE,MAAM,o2BAnDjDJ,EAAA,GAAA+C,EAAW,MAAM,QAAQjH,CAAK,EAAIA,EAAM,SAAW,GAAKA,CAAK,mBAC7CwD,GAAS,eAAiB,eAAAH,CAAc,CAAA", "x_google_ignoreList": [0]}