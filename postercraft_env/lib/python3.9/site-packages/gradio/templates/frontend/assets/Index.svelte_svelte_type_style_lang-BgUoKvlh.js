import"./Index-WGC0_FkS.js";import{c as k}from"./Blocks-aR9ucLZz.js";import{M as v}from"./Example.svelte_svelte_type_style_lang-BBpfzd83.js";const{SvelteComponent:b,action_destroyer:w,attr:c,create_component:z,destroy_component:M,detach:x,element:j,flush:_,init:C,insert:q,mount_component:D,safe_not_equal:E,toggle_class:d,transition_in:S,transition_out:A}=window.__gradio__svelte__internal,{createEventDispatcher:B}=window.__gradio__svelte__internal;function F(l){let e,s,h,m,a,u,f;return s=new v({props:{message:l[2],latex_delimiters:l[7],sanitize_html:l[5],line_breaks:l[6],chatbot:!1,header_links:l[8]}}),{c(){e=j("div"),z(s.$$.fragment),c(e,"class",h="prose "+l[0].join(" ")+" svelte-1yrv54"),c(e,"data-testid","markdown"),c(e,"dir",m=l[4]?"rtl":"ltr"),d(e,"min",l[3]),d(e,"hide",!l[1])},m(t,n){q(t,e,n),D(s,e,null),a=!0,u||(f=w(k.call(null,e)),u=!0)},p(t,[n]){const r={};n&4&&(r.message=t[2]),n&128&&(r.latex_delimiters=t[7]),n&32&&(r.sanitize_html=t[5]),n&64&&(r.line_breaks=t[6]),n&256&&(r.header_links=t[8]),s.$set(r),(!a||n&1&&h!==(h="prose "+t[0].join(" ")+" svelte-1yrv54"))&&c(e,"class",h),(!a||n&16&&m!==(m=t[4]?"rtl":"ltr"))&&c(e,"dir",m),(!a||n&9)&&d(e,"min",t[3]),(!a||n&3)&&d(e,"hide",!t[1])},i(t){a||(S(s.$$.fragment,t),a=!0)},o(t){A(s.$$.fragment,t),a=!1},d(t){t&&x(e),M(s),u=!1,f()}}}function G(l,e,s){let{elem_classes:h=[]}=e,{visible:m=!0}=e,{value:a}=e,{min_height:u=!1}=e,{rtl:f=!1}=e,{sanitize_html:t=!0}=e,{line_breaks:n=!1}=e,{latex_delimiters:r}=e,{header_links:o=!1}=e;const g=B();return l.$$set=i=>{"elem_classes"in i&&s(0,h=i.elem_classes),"visible"in i&&s(1,m=i.visible),"value"in i&&s(2,a=i.value),"min_height"in i&&s(3,u=i.min_height),"rtl"in i&&s(4,f=i.rtl),"sanitize_html"in i&&s(5,t=i.sanitize_html),"line_breaks"in i&&s(6,n=i.line_breaks),"latex_delimiters"in i&&s(7,r=i.latex_delimiters),"header_links"in i&&s(8,o=i.header_links)},l.$$.update=()=>{l.$$.dirty&4&&g("change")},[h,m,a,u,f,t,n,r,o]}class H extends b{constructor(e){super(),C(this,e,G,F,E,{elem_classes:0,visible:1,value:2,min_height:3,rtl:4,sanitize_html:5,line_breaks:6,latex_delimiters:7,header_links:8})}get elem_classes(){return this.$$.ctx[0]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get visible(){return this.$$.ctx[1]}set visible(e){this.$$set({visible:e}),_()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),_()}get min_height(){return this.$$.ctx[3]}set min_height(e){this.$$set({min_height:e}),_()}get rtl(){return this.$$.ctx[4]}set rtl(e){this.$$set({rtl:e}),_()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),_()}get line_breaks(){return this.$$.ctx[6]}set line_breaks(e){this.$$set({line_breaks:e}),_()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),_()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),_()}}const L=H;export{L as M};
//# sourceMappingURL=Index.svelte_svelte_type_style_lang-BgUoKvlh.js.map
