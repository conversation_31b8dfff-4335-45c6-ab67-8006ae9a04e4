import{S as _e,e as pe,s as le,f as x,g as m,h as O,j as Z,n as L,k as T,m as K,o as $,N as U,w,r as be,u as D,v as ve,a0 as mr,a1 as gr,C as it,D as Vt,p as ee,b as _r,R as j,F as H,G as P,H as W,Q as Be,ag as V,M as de,I as ti,y as qt,ar as ji,O as pr,a7 as Ne,ak as br,ao as ii,K as ri,q as vr,B as Vi,E as qi,ap as wt,aG as wr,U as me,X as ge,a9 as yr,ab as kr,ac as Er,ad as Dr,Z as Mr,ae as Or,V as Tr,W as Cr}from"./index-c99b2410.js";import{f as ai,B as Rr}from"./Button-9c502b18.js";import{B as Ar}from"./BlockLabel-def07c98.js";import{I as ni}from"./Image-c3fe7982.js";import{g as Sr}from"./utils-90f3612b.js";import{I as $e}from"./IconButton-0f3d06d2.js";import{C as Ir,M as Dt}from"./ModifyUpload-643080ff.js";import{U as Nr}from"./Undo-61b53ec5.js";import{U as zr}from"./Upload-ac8c778e.js";import{U as Br}from"./UploadText-96773547.js";function Lr(r){let e,t,i;return{c(){e=x("svg"),t=x("path"),i=x("path"),m(t,"d","M28.828 3.172a4.094 4.094 0 0 0-5.656 0L4.05 22.292A6.954 6.954 0 0 0 2 27.242V30h2.756a6.952 6.952 0 0 0 4.95-2.05L28.828 8.829a3.999 3.999 0 0 0 0-5.657zM10.91 18.26l2.829 2.829l-2.122 2.121l-2.828-2.828zm-2.619 8.276A4.966 4.966 0 0 1 4.756 28H4v-.759a4.967 4.967 0 0 1 1.464-3.535l1.91-1.91l2.829 2.828zM27.415 7.414l-12.261 12.26l-2.829-2.828l12.262-12.26a2.047 2.047 0 0 1 2.828 0a2 2 0 0 1 0 2.828z"),m(t,"fill","currentColor"),m(i,"d","M6.5 15a3.5 3.5 0 0 1-2.475-5.974l3.5-3.5a1.502 1.502 0 0 0 0-2.121a1.537 1.537 0 0 0-2.121 0L3.415 5.394L2 3.98l1.99-1.988a3.585 3.585 0 0 1 4.95 0a3.504 3.504 0 0 1 0 4.949L5.439 10.44a1.502 1.502 0 0 0 0 2.121a1.537 1.537 0 0 0 2.122 0l4.024-4.024L13 9.95l-4.025 4.024A3.475 3.475 0 0 1 6.5 15z"),m(i,"fill","currentColor"),m(e,"width","100%"),m(e,"height","100%"),m(e,"viewBox","0 0 32 32")},m(n,a){O(n,e,a),Z(e,t),Z(e,i)},p:L,i:L,o:L,d(n){n&&T(e)}}}class Hr extends _e{constructor(e){super(),pe(this,e,null,Lr,le,{})}}function Pr(r){let e,t,i;return{c(){e=x("svg"),t=x("path"),i=x("circle"),m(t,"d","M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"),m(i,"cx","12"),m(i,"cy","13"),m(i,"r","4"),m(e,"xmlns","http://www.w3.org/2000/svg"),m(e,"width","100%"),m(e,"height","100%"),m(e,"viewBox","0 0 24 24"),m(e,"fill","none"),m(e,"stroke","currentColor"),m(e,"stroke-width","1.5"),m(e,"stroke-linecap","round"),m(e,"stroke-linejoin","round"),m(e,"class","feather feather-camera")},m(n,a){O(n,e,a),Z(e,t),Z(e,i)},p:L,i:L,o:L,d(n){n&&T(e)}}}class Wr extends _e{constructor(e){super(),pe(this,e,null,Pr,le,{})}}function Xr(r){let e,t;return{c(){e=x("svg"),t=x("circle"),m(t,"cx","12"),m(t,"cy","12"),m(t,"r","10"),m(e,"xmlns","http://www.w3.org/2000/svg"),m(e,"width","100%"),m(e,"height","100%"),m(e,"viewBox","0 0 24 24"),m(e,"fill","red"),m(e,"stroke","red"),m(e,"stroke-width","1.5"),m(e,"stroke-linecap","round"),m(e,"stroke-linejoin","round"),m(e,"class","feather feather-circle")},m(i,n){O(i,e,n),Z(e,t)},p:L,i:L,o:L,d(i){i&&T(e)}}}class Yr extends _e{constructor(e){super(),pe(this,e,null,Xr,le,{})}}function Ur(r){let e,t,i,n,a,s,l;return{c(){e=x("svg"),t=x("circle"),i=x("circle"),n=x("circle"),a=x("circle"),s=x("circle"),l=x("path"),m(t,"cx","10"),m(t,"cy","12"),m(t,"r","2"),m(t,"fill","currentColor"),m(i,"cx","16"),m(i,"cy","9"),m(i,"r","2"),m(i,"fill","currentColor"),m(n,"cx","22"),m(n,"cy","12"),m(n,"r","2"),m(n,"fill","currentColor"),m(a,"cx","23"),m(a,"cy","18"),m(a,"r","2"),m(a,"fill","currentColor"),m(s,"cx","19"),m(s,"cy","23"),m(s,"r","2"),m(s,"fill","currentColor"),m(l,"fill","currentColor"),m(l,"d","M16.54 2A14 14 0 0 0 2 16a4.82 4.82 0 0 0 6.09 4.65l1.12-.31a3 3 0 0 1 3.79 2.9V27a3 3 0 0 0 3 3a14 14 0 0 0 14-14.54A14.05 14.05 0 0 0 16.54 2Zm8.11 22.31A11.93 11.93 0 0 1 16 28a1 1 0 0 1-1-1v-3.76a5 5 0 0 0-5-5a5.07 5.07 0 0 0-1.33.18l-1.12.31A2.82 2.82 0 0 1 4 16A12 12 0 0 1 16.47 4A12.18 12.18 0 0 1 28 15.53a11.89 11.89 0 0 1-3.35 8.79Z"),m(e,"width","100%"),m(e,"height","100%"),m(e,"viewBox","0 0 32 32")},m(o,h){O(o,e,h),Z(e,t),Z(e,i),Z(e,n),Z(e,a),Z(e,s),Z(e,l)},p:L,i:L,o:L,d(o){o&&T(e)}}}class jr extends _e{constructor(e){super(),pe(this,e,null,Ur,le,{})}}function Vr(r){let e,t;return{c(){e=x("svg"),t=x("path"),m(t,"fill","currentColor"),m(t,"d","M7 27h23v2H7zm20.38-16.49l-7.93-7.92a2 2 0 0 0-2.83 0l-14 14a2 2 0 0 0 0 2.83L7.13 24h9.59l10.66-10.66a2 2 0 0 0 0-2.83zM15.89 22H8l-4-4l6.31-6.31l7.93 7.92zm3.76-3.76l-7.92-7.93L18 4l8 7.93z"),m(e,"xmlns","http://www.w3.org/2000/svg"),m(e,"width","100%"),m(e,"height","100%"),m(e,"viewBox","0 0 32 32")},m(i,n){O(i,e,n),Z(e,t)},p:L,i:L,o:L,d(i){i&&T(e)}}}class qr extends _e{constructor(e){super(),pe(this,e,null,Vr,le,{})}}function Gr(r){let e,t;return{c(){e=x("svg"),t=x("path"),m(t,"d","M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"),m(e,"xmlns","http://www.w3.org/2000/svg"),m(e,"width","100%"),m(e,"height","100%"),m(e,"viewBox","0 0 24 24"),m(e,"fill","none"),m(e,"stroke","currentColor"),m(e,"stroke-width","1.5"),m(e,"stroke-linecap","round"),m(e,"stroke-linejoin","round"),m(e,"class","feather feather-edit-2")},m(i,n){O(i,e,n),Z(e,t)},p:L,i:L,o:L,d(i){i&&T(e)}}}let si=class extends _e{constructor(e){super(),pe(this,e,null,Gr,le,{})}};function Fr(r){let e,t;return{c(){e=x("svg"),t=x("rect"),m(t,"x","3"),m(t,"y","3"),m(t,"width","18"),m(t,"height","18"),m(t,"rx","2"),m(t,"ry","2"),m(e,"xmlns","http://www.w3.org/2000/svg"),m(e,"width","100%"),m(e,"height","100%"),m(e,"viewBox","0 0 24 24"),m(e,"fill","red"),m(e,"stroke","red"),m(e,"stroke-width","1.5"),m(e,"stroke-linecap","round"),m(e,"stroke-linejoin","round"),m(e,"class","feather feather-square")},m(i,n){O(i,e,n),Z(e,t)},p:L,i:L,o:L,d(i){i&&T(e)}}}class Zr extends _e{constructor(e){super(),pe(this,e,null,Fr,le,{})}}function oi(r){let e,t,i,n,a,s,l;const o=[Qr,Kr],h=[];function u(f,c){return f[1]==="video"?0:1}return t=u(r),i=h[t]=o[t](r),{c(){e=K("button"),i.c(),m(e,"aria-label",n=r[1]==="image"?"capture photo":"start recording"),m(e,"class","svelte-425ent")},m(f,c){O(f,e,c),h[t].m(e,null),a=!0,s||(l=ee(e,"click",function(){_r(r[1]==="image"?r[5]:r[6])&&(r[1]==="image"?r[5]:r[6]).apply(this,arguments)}),s=!0)},p(f,c){r=f;let d=t;t=u(r),t===d?h[t].p(r,c):(be(),D(h[d],1,1,()=>{h[d]=null}),ve(),i=h[t],i?i.p(r,c):(i=h[t]=o[t](r),i.c()),w(i,1),i.m(e,null)),(!a||c&2&&n!==(n=r[1]==="image"?"capture photo":"start recording"))&&m(e,"aria-label",n)},i(f){a||(w(i),a=!0)},o(f){D(i),a=!1},d(f){f&&T(e),h[t].d(),s=!1,l()}}}function Kr(r){let e,t,i;return t=new Wr({}),{c(){e=K("div"),H(t.$$.fragment),m(e,"class","icon svelte-425ent"),m(e,"title","capture photo")},m(n,a){O(n,e,a),P(t,e,null),i=!0},p:L,i(n){i||(w(t.$$.fragment,n),i=!0)},o(n){D(t.$$.fragment,n),i=!1},d(n){n&&T(e),W(t)}}}function Qr(r){let e,t,i,n;const a=[xr,Jr],s=[];function l(o,h){return o[4]?0:1}return e=l(r),t=s[e]=a[e](r),{c(){t.c(),i=Be()},m(o,h){s[e].m(o,h),O(o,i,h),n=!0},p(o,h){let u=e;e=l(o),e!==u&&(be(),D(s[u],1,1,()=>{s[u]=null}),ve(),t=s[e],t||(t=s[e]=a[e](o),t.c()),w(t,1),t.m(i.parentNode,i))},i(o){n||(w(t),n=!0)},o(o){D(t),n=!1},d(o){o&&T(i),s[e].d(o)}}}function Jr(r){let e,t,i;return t=new Yr({}),{c(){e=K("div"),H(t.$$.fragment),m(e,"class","icon svelte-425ent"),m(e,"title","start recording")},m(n,a){O(n,e,a),P(t,e,null),i=!0},i(n){i||(w(t.$$.fragment,n),i=!0)},o(n){D(t.$$.fragment,n),i=!1},d(n){n&&T(e),W(t)}}}function xr(r){let e,t,i;return t=new Zr({}),{c(){e=K("div"),H(t.$$.fragment),m(e,"class","icon svelte-425ent"),m(e,"title","stop recording")},m(n,a){O(n,e,a),P(t,e,null),i=!0},i(n){i||(w(t.$$.fragment,n),i=!0)},o(n){D(t.$$.fragment,n),i=!1},d(n){n&&T(e),W(t)}}}function $r(r){let e,t,i,n,a=!r[0]&&oi(r);return{c(){e=K("div"),t=K("video"),i=$(),a&&a.c(),m(t,"class","svelte-425ent"),U(t,"flip",r[2]),m(e,"class","wrap svelte-425ent")},m(s,l){O(s,e,l),Z(e,t),r[9](t),Z(e,i),a&&a.m(e,null),n=!0},p(s,[l]){(!n||l&4)&&U(t,"flip",s[2]),s[0]?a&&(be(),D(a,1,1,()=>{a=null}),ve()):a?(a.p(s,l),l&1&&w(a,1)):(a=oi(s),a.c(),w(a,1),a.m(e,null))},i(s){n||(w(a),n=!0)},o(s){D(a),n=!1},d(s){s&&T(e),r[9](null),a&&a.d()}}}function ea(r,e,t){let i;mr(r,gr,y=>t(15,i=y));let n,a,{streaming:s=!1}=e,{pending:l=!1}=e,{mode:o="image"}=e,{mirror_webcam:h}=e,{include_audio:u}=e;const f=it();Vt(()=>a=document.createElement("canvas"));async function c(){try{k=await navigator.mediaDevices.getUserMedia({video:!0,audio:u}),t(3,n.srcObject=k,n),t(3,n.muted=!0,n),n.play()}catch(y){if(y instanceof DOMException&&y.name=="NotAllowedError")f("error",i("image.allow_webcam_access"));else throw y}}function d(){var y=a.getContext("2d");if(n.videoWidth&&n.videoHeight){a.width=n.videoWidth,a.height=n.videoHeight,y.drawImage(n,0,0,n.videoWidth,n.videoHeight);var g=a.toDataURL("image/png");f(s?"stream":"capture",g)}}let _=!1,v=[],k,N,C;function S(){if(_){C.stop();let y=new Blob(v,{type:N}),g=new FileReader;g.onload=function(M){M.target&&(f("capture",{data:M.target.result,name:"sample."+N.substring(6),is_example:!1}),f("stop_recording"))},g.readAsDataURL(y)}else{f("start_recording"),v=[];let y=["video/webm","video/mp4"];for(let g of y)if(MediaRecorder.isTypeSupported(g)){N=g;break}if(N===null){console.error("No supported MediaRecorder mimeType");return}C=new MediaRecorder(k,{mimeType:N}),C.addEventListener("dataavailable",function(g){v.push(g.data)}),C.start(200)}t(4,_=!_)}c(),s&&o==="image"&&window.setInterval(()=>{n&&!l&&d()},500);function ie(y){j[y?"unshift":"push"](()=>{n=y,t(3,n)})}return r.$$set=y=>{"streaming"in y&&t(0,s=y.streaming),"pending"in y&&t(7,l=y.pending),"mode"in y&&t(1,o=y.mode),"mirror_webcam"in y&&t(2,h=y.mirror_webcam),"include_audio"in y&&t(8,u=y.include_audio)},[s,o,h,n,_,d,S,l,u,ie]}class ta extends _e{constructor(e){super(),pe(this,e,ea,$r,le,{streaming:0,pending:7,mode:1,mirror_webcam:2,include_audio:8})}}const ia=ta;/*!
 * Cropper.js v1.5.12
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2021-06-12T08:00:17.411Z
 */function li(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);e&&(i=i.filter(function(n){return Object.getOwnPropertyDescriptor(r,n).enumerable})),t.push.apply(t,i)}return t}function Gi(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?li(Object(t),!0).forEach(function(i){na(r,i,t[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):li(Object(t)).forEach(function(i){Object.defineProperty(r,i,Object.getOwnPropertyDescriptor(t,i))})}return r}function vt(r){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?vt=function(e){return typeof e}:vt=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vt(r)}function ra(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function hi(r,e){for(var t=0;t<e.length;t++){var i=e[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(r,i.key,i)}}function aa(r,e,t){return e&&hi(r.prototype,e),t&&hi(r,t),r}function na(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Fi(r){return sa(r)||oa(r)||la(r)||ha()}function sa(r){if(Array.isArray(r))return zt(r)}function oa(r){if(typeof Symbol<"u"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function la(r,e){if(r){if(typeof r=="string")return zt(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);if(t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set")return Array.from(r);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return zt(r,e)}}function zt(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=r[t];return i}function ha(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var Mt=typeof window<"u"&&typeof window.document<"u",De=Mt?window:{},Gt=Mt&&De.document.documentElement?"ontouchstart"in De.document.documentElement:!1,Ft=Mt?"PointerEvent"in De:!1,q="cropper",Zt="all",Zi="crop",Ki="move",Qi="zoom",Se="e",Ie="w",Xe="s",Te="n",Ze="ne",Ke="nw",Qe="se",Je="sw",Bt="".concat(q,"-crop"),ui="".concat(q,"-disabled"),fe="".concat(q,"-hidden"),ci="".concat(q,"-hide"),ua="".concat(q,"-invisible"),yt="".concat(q,"-modal"),Lt="".concat(q,"-move"),et="".concat(q,"Action"),pt="".concat(q,"Preview"),Kt="crop",Ji="move",xi="none",Ht="crop",Pt="cropend",Wt="cropmove",Xt="cropstart",fi="dblclick",ca=Gt?"touchstart":"mousedown",fa=Gt?"touchmove":"mousemove",da=Gt?"touchend touchcancel":"mouseup",di=Ft?"pointerdown":ca,mi=Ft?"pointermove":fa,gi=Ft?"pointerup pointercancel":da,_i="ready",pi="resize",bi="wheel",Yt="zoom",vi="image/jpeg",ma=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,ga=/^data:/,_a=/^data:image\/jpeg;base64,/,pa=/^img|canvas$/i,$i=200,er=100,wi={viewMode:0,dragMode:Kt,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:$i,minContainerHeight:er,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},ba='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',va=Number.isNaN||De.isNaN;function I(r){return typeof r=="number"&&!va(r)}var yi=function(e){return e>0&&e<1/0};function It(r){return typeof r>"u"}function ze(r){return vt(r)==="object"&&r!==null}var wa=Object.prototype.hasOwnProperty;function Ye(r){if(!ze(r))return!1;try{var e=r.constructor,t=e.prototype;return e&&t&&wa.call(t,"isPrototypeOf")}catch{return!1}}function ce(r){return typeof r=="function"}var ya=Array.prototype.slice;function tr(r){return Array.from?Array.from(r):ya.call(r)}function te(r,e){return r&&ce(e)&&(Array.isArray(r)||I(r.length)?tr(r).forEach(function(t,i){e.call(r,t,i,r)}):ze(r)&&Object.keys(r).forEach(function(t){e.call(r,r[t],t,r)})),r}var G=Object.assign||function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return ze(e)&&i.length>0&&i.forEach(function(a){ze(a)&&Object.keys(a).forEach(function(s){e[s]=a[s]})}),e},ka=/\.\d*(?:0|9){12}\d*$/;function je(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return ka.test(r)?Math.round(r*e)/e:r}var Ea=/^width|height|left|top|marginLeft|marginTop$/;function Ce(r,e){var t=r.style;te(e,function(i,n){Ea.test(n)&&I(i)&&(i="".concat(i,"px")),t[n]=i})}function Da(r,e){return r.classList?r.classList.contains(e):r.className.indexOf(e)>-1}function ne(r,e){if(e){if(I(r.length)){te(r,function(i){ne(i,e)});return}if(r.classList){r.classList.add(e);return}var t=r.className.trim();t?t.indexOf(e)<0&&(r.className="".concat(t," ").concat(e)):r.className=e}}function Ee(r,e){if(e){if(I(r.length)){te(r,function(t){Ee(t,e)});return}if(r.classList){r.classList.remove(e);return}r.className.indexOf(e)>=0&&(r.className=r.className.replace(e,""))}}function Ue(r,e,t){if(e){if(I(r.length)){te(r,function(i){Ue(i,e,t)});return}t?ne(r,e):Ee(r,e)}}var Ma=/([a-z\d])([A-Z])/g;function Qt(r){return r.replace(Ma,"$1-$2").toLowerCase()}function Ut(r,e){return ze(r[e])?r[e]:r.dataset?r.dataset[e]:r.getAttribute("data-".concat(Qt(e)))}function tt(r,e,t){ze(t)?r[e]=t:r.dataset?r.dataset[e]=t:r.setAttribute("data-".concat(Qt(e)),t)}function Oa(r,e){if(ze(r[e]))try{delete r[e]}catch{r[e]=void 0}else if(r.dataset)try{delete r.dataset[e]}catch{r.dataset[e]=void 0}else r.removeAttribute("data-".concat(Qt(e)))}var ir=/\s\s*/,rr=function(){var r=!1;if(Mt){var e=!1,t=function(){},i=Object.defineProperty({},"once",{get:function(){return r=!0,e},set:function(a){e=a}});De.addEventListener("test",t,i),De.removeEventListener("test",t,i)}return r}();function ke(r,e,t){var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},n=t;e.trim().split(ir).forEach(function(a){if(!rr){var s=r.listeners;s&&s[a]&&s[a][t]&&(n=s[a][t],delete s[a][t],Object.keys(s[a]).length===0&&delete s[a],Object.keys(s).length===0&&delete r.listeners)}r.removeEventListener(a,n,i)})}function ye(r,e,t){var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},n=t;e.trim().split(ir).forEach(function(a){if(i.once&&!rr){var s=r.listeners,l=s===void 0?{}:s;n=function(){delete l[a][t],r.removeEventListener(a,n,i);for(var h=arguments.length,u=new Array(h),f=0;f<h;f++)u[f]=arguments[f];t.apply(r,u)},l[a]||(l[a]={}),l[a][t]&&r.removeEventListener(a,l[a][t],i),l[a][t]=n,r.listeners=l}r.addEventListener(a,n,i)})}function Ve(r,e,t){var i;return ce(Event)&&ce(CustomEvent)?i=new CustomEvent(e,{detail:t,bubbles:!0,cancelable:!0}):(i=document.createEvent("CustomEvent"),i.initCustomEvent(e,!0,!0,t)),r.dispatchEvent(i)}function ar(r){var e=r.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var Nt=De.location,Ta=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function ki(r){var e=r.match(Ta);return e!==null&&(e[1]!==Nt.protocol||e[2]!==Nt.hostname||e[3]!==Nt.port)}function Ei(r){var e="timestamp=".concat(new Date().getTime());return r+(r.indexOf("?")===-1?"?":"&")+e}function xe(r){var e=r.rotate,t=r.scaleX,i=r.scaleY,n=r.translateX,a=r.translateY,s=[];I(n)&&n!==0&&s.push("translateX(".concat(n,"px)")),I(a)&&a!==0&&s.push("translateY(".concat(a,"px)")),I(e)&&e!==0&&s.push("rotate(".concat(e,"deg)")),I(t)&&t!==1&&s.push("scaleX(".concat(t,")")),I(i)&&i!==1&&s.push("scaleY(".concat(i,")"));var l=s.length?s.join(" "):"none";return{WebkitTransform:l,msTransform:l,transform:l}}function Ca(r){var e=Gi({},r),t=0;return te(r,function(i,n){delete e[n],te(e,function(a){var s=Math.abs(i.startX-a.startX),l=Math.abs(i.startY-a.startY),o=Math.abs(i.endX-a.endX),h=Math.abs(i.endY-a.endY),u=Math.sqrt(s*s+l*l),f=Math.sqrt(o*o+h*h),c=(f-u)/u;Math.abs(c)>Math.abs(t)&&(t=c)})}),t}function bt(r,e){var t=r.pageX,i=r.pageY,n={endX:t,endY:i};return e?n:Gi({startX:t,startY:i},n)}function Ra(r){var e=0,t=0,i=0;return te(r,function(n){var a=n.startX,s=n.startY;e+=a,t+=s,i+=1}),e/=i,t/=i,{pageX:e,pageY:t}}function Re(r){var e=r.aspectRatio,t=r.height,i=r.width,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",a=yi(i),s=yi(t);if(a&&s){var l=t*e;n==="contain"&&l>i||n==="cover"&&l<i?t=i/e:i=t*e}else a?t=i/e:s&&(i=t*e);return{width:i,height:t}}function Aa(r){var e=r.width,t=r.height,i=r.degree;if(i=Math.abs(i)%180,i===90)return{width:t,height:e};var n=i%90*Math.PI/180,a=Math.sin(n),s=Math.cos(n),l=e*s+t*a,o=e*a+t*s;return i>90?{width:o,height:l}:{width:l,height:o}}function Sa(r,e,t,i){var n=e.aspectRatio,a=e.naturalWidth,s=e.naturalHeight,l=e.rotate,o=l===void 0?0:l,h=e.scaleX,u=h===void 0?1:h,f=e.scaleY,c=f===void 0?1:f,d=t.aspectRatio,_=t.naturalWidth,v=t.naturalHeight,k=i.fillColor,N=k===void 0?"transparent":k,C=i.imageSmoothingEnabled,S=C===void 0?!0:C,ie=i.imageSmoothingQuality,y=ie===void 0?"low":ie,g=i.maxWidth,M=g===void 0?1/0:g,R=i.maxHeight,B=R===void 0?1/0:R,re=i.minWidth,oe=re===void 0?0:re,se=i.minHeight,X=se===void 0?0:se,F=document.createElement("canvas"),Y=F.getContext("2d"),Q=Re({aspectRatio:d,width:M,height:B}),he=Re({aspectRatio:d,width:oe,height:X},"cover"),we=Math.min(Q.width,Math.max(he.width,_)),ue=Math.min(Q.height,Math.max(he.height,v)),E=Re({aspectRatio:n,width:M,height:B}),Le=Re({aspectRatio:n,width:oe,height:X},"cover"),Oe=Math.min(E.width,Math.max(Le.width,a)),He=Math.min(E.height,Math.max(Le.height,s)),Pe=[-Oe/2,-He/2,Oe,He];return F.width=je(we),F.height=je(ue),Y.fillStyle=N,Y.fillRect(0,0,we,ue),Y.save(),Y.translate(we/2,ue/2),Y.rotate(o*Math.PI/180),Y.scale(u,c),Y.imageSmoothingEnabled=S,Y.imageSmoothingQuality=y,Y.drawImage.apply(Y,[r].concat(Fi(Pe.map(function(Ge){return Math.floor(je(Ge))})))),Y.restore(),F}var nr=String.fromCharCode;function Ia(r,e,t){var i="";t+=e;for(var n=e;n<t;n+=1)i+=nr(r.getUint8(n));return i}var Na=/^data:.*,/;function za(r){var e=r.replace(Na,""),t=atob(e),i=new ArrayBuffer(t.length),n=new Uint8Array(i);return te(n,function(a,s){n[s]=t.charCodeAt(s)}),i}function Ba(r,e){for(var t=[],i=8192,n=new Uint8Array(r);n.length>0;)t.push(nr.apply(null,tr(n.subarray(0,i)))),n=n.subarray(i);return"data:".concat(e,";base64,").concat(btoa(t.join("")))}function La(r){var e=new DataView(r),t;try{var i,n,a;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(var s=e.byteLength,l=2;l+1<s;){if(e.getUint8(l)===255&&e.getUint8(l+1)===225){n=l;break}l+=1}if(n){var o=n+4,h=n+10;if(Ia(e,o,4)==="Exif"){var u=e.getUint16(h);if(i=u===18761,(i||u===19789)&&e.getUint16(h+2,i)===42){var f=e.getUint32(h+4,i);f>=8&&(a=h+f)}}}if(a){var c=e.getUint16(a,i),d,_;for(_=0;_<c;_+=1)if(d=a+_*12+2,e.getUint16(d,i)===274){d+=8,t=e.getUint16(d,i),e.setUint16(d,1,i);break}}}catch{t=1}return t}function Ha(r){var e=0,t=1,i=1;switch(r){case 2:t=-1;break;case 3:e=-180;break;case 4:i=-1;break;case 5:e=90,i=-1;break;case 6:e=90;break;case 7:e=90,t=-1;break;case 8:e=-90;break}return{rotate:e,scaleX:t,scaleY:i}}var Pa={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var e=this.element,t=this.options,i=this.container,n=this.cropper,a=Number(t.minContainerWidth),s=Number(t.minContainerHeight);ne(n,fe),Ee(e,fe);var l={width:Math.max(i.offsetWidth,a>=0?a:$i),height:Math.max(i.offsetHeight,s>=0?s:er)};this.containerData=l,Ce(n,{width:l.width,height:l.height}),ne(e,fe),Ee(n,fe)},initCanvas:function(){var e=this.containerData,t=this.imageData,i=this.options.viewMode,n=Math.abs(t.rotate)%180===90,a=n?t.naturalHeight:t.naturalWidth,s=n?t.naturalWidth:t.naturalHeight,l=a/s,o=e.width,h=e.height;e.height*l>e.width?i===3?o=e.height*l:h=e.width/l:i===3?h=e.width/l:o=e.height*l;var u={aspectRatio:l,naturalWidth:a,naturalHeight:s,width:o,height:h};this.canvasData=u,this.limited=i===1||i===2,this.limitCanvas(!0,!0),u.width=Math.min(Math.max(u.width,u.minWidth),u.maxWidth),u.height=Math.min(Math.max(u.height,u.minHeight),u.maxHeight),u.left=(e.width-u.width)/2,u.top=(e.height-u.height)/2,u.oldLeft=u.left,u.oldTop=u.top,this.initialCanvasData=G({},u)},limitCanvas:function(e,t){var i=this.options,n=this.containerData,a=this.canvasData,s=this.cropBoxData,l=i.viewMode,o=a.aspectRatio,h=this.cropped&&s;if(e){var u=Number(i.minCanvasWidth)||0,f=Number(i.minCanvasHeight)||0;l>1?(u=Math.max(u,n.width),f=Math.max(f,n.height),l===3&&(f*o>u?u=f*o:f=u/o)):l>0&&(u?u=Math.max(u,h?s.width:0):f?f=Math.max(f,h?s.height:0):h&&(u=s.width,f=s.height,f*o>u?u=f*o:f=u/o));var c=Re({aspectRatio:o,width:u,height:f});u=c.width,f=c.height,a.minWidth=u,a.minHeight=f,a.maxWidth=1/0,a.maxHeight=1/0}if(t)if(l>(h?0:1)){var d=n.width-a.width,_=n.height-a.height;a.minLeft=Math.min(0,d),a.minTop=Math.min(0,_),a.maxLeft=Math.max(0,d),a.maxTop=Math.max(0,_),h&&this.limited&&(a.minLeft=Math.min(s.left,s.left+(s.width-a.width)),a.minTop=Math.min(s.top,s.top+(s.height-a.height)),a.maxLeft=s.left,a.maxTop=s.top,l===2&&(a.width>=n.width&&(a.minLeft=Math.min(0,d),a.maxLeft=Math.max(0,d)),a.height>=n.height&&(a.minTop=Math.min(0,_),a.maxTop=Math.max(0,_))))}else a.minLeft=-a.width,a.minTop=-a.height,a.maxLeft=n.width,a.maxTop=n.height},renderCanvas:function(e,t){var i=this.canvasData,n=this.imageData;if(t){var a=Aa({width:n.naturalWidth*Math.abs(n.scaleX||1),height:n.naturalHeight*Math.abs(n.scaleY||1),degree:n.rotate||0}),s=a.width,l=a.height,o=i.width*(s/i.naturalWidth),h=i.height*(l/i.naturalHeight);i.left-=(o-i.width)/2,i.top-=(h-i.height)/2,i.width=o,i.height=h,i.aspectRatio=s/l,i.naturalWidth=s,i.naturalHeight=l,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,Ce(this.canvas,G({width:i.width,height:i.height},xe({translateX:i.left,translateY:i.top}))),this.renderImage(e),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(e){var t=this.canvasData,i=this.imageData,n=i.naturalWidth*(t.width/t.naturalWidth),a=i.naturalHeight*(t.height/t.naturalHeight);G(i,{width:n,height:a,left:(t.width-n)/2,top:(t.height-a)/2}),Ce(this.image,G({width:i.width,height:i.height},xe(G({translateX:i.left,translateY:i.top},i)))),e&&this.output()},initCropBox:function(){var e=this.options,t=this.canvasData,i=e.aspectRatio||e.initialAspectRatio,n=Number(e.autoCropArea)||.8,a={width:t.width,height:t.height};i&&(t.height*i>t.width?a.height=a.width/i:a.width=a.height*i),this.cropBoxData=a,this.limitCropBox(!0,!0),a.width=Math.min(Math.max(a.width,a.minWidth),a.maxWidth),a.height=Math.min(Math.max(a.height,a.minHeight),a.maxHeight),a.width=Math.max(a.minWidth,a.width*n),a.height=Math.max(a.minHeight,a.height*n),a.left=t.left+(t.width-a.width)/2,a.top=t.top+(t.height-a.height)/2,a.oldLeft=a.left,a.oldTop=a.top,this.initialCropBoxData=G({},a)},limitCropBox:function(e,t){var i=this.options,n=this.containerData,a=this.canvasData,s=this.cropBoxData,l=this.limited,o=i.aspectRatio;if(e){var h=Number(i.minCropBoxWidth)||0,u=Number(i.minCropBoxHeight)||0,f=l?Math.min(n.width,a.width,a.width+a.left,n.width-a.left):n.width,c=l?Math.min(n.height,a.height,a.height+a.top,n.height-a.top):n.height;h=Math.min(h,n.width),u=Math.min(u,n.height),o&&(h&&u?u*o>h?u=h/o:h=u*o:h?u=h/o:u&&(h=u*o),c*o>f?c=f/o:f=c*o),s.minWidth=Math.min(h,f),s.minHeight=Math.min(u,c),s.maxWidth=f,s.maxHeight=c}t&&(l?(s.minLeft=Math.max(0,a.left),s.minTop=Math.max(0,a.top),s.maxLeft=Math.min(n.width,a.left+a.width)-s.width,s.maxTop=Math.min(n.height,a.top+a.height)-s.height):(s.minLeft=0,s.minTop=0,s.maxLeft=n.width-s.width,s.maxTop=n.height-s.height))},renderCropBox:function(){var e=this.options,t=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,e.movable&&e.cropBoxMovable&&tt(this.face,et,i.width>=t.width&&i.height>=t.height?Ki:Zt),Ce(this.cropBox,G({width:i.width,height:i.height},xe({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),Ve(this.element,Ht,this.getData())}},Wa={initPreview:function(){var e=this.element,t=this.crossOrigin,i=this.options.preview,n=t?this.crossOriginUrl:this.url,a=e.alt||"The image to preview",s=document.createElement("img");if(t&&(s.crossOrigin=t),s.src=n,s.alt=a,this.viewBox.appendChild(s),this.viewBoxImage=s,!!i){var l=i;typeof i=="string"?l=e.ownerDocument.querySelectorAll(i):i.querySelector&&(l=[i]),this.previews=l,te(l,function(o){var h=document.createElement("img");tt(o,pt,{width:o.offsetWidth,height:o.offsetHeight,html:o.innerHTML}),t&&(h.crossOrigin=t),h.src=n,h.alt=a,h.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',o.innerHTML="",o.appendChild(h)})}},resetPreview:function(){te(this.previews,function(e){var t=Ut(e,pt);Ce(e,{width:t.width,height:t.height}),e.innerHTML=t.html,Oa(e,pt)})},preview:function(){var e=this.imageData,t=this.canvasData,i=this.cropBoxData,n=i.width,a=i.height,s=e.width,l=e.height,o=i.left-t.left-e.left,h=i.top-t.top-e.top;!this.cropped||this.disabled||(Ce(this.viewBoxImage,G({width:s,height:l},xe(G({translateX:-o,translateY:-h},e)))),te(this.previews,function(u){var f=Ut(u,pt),c=f.width,d=f.height,_=c,v=d,k=1;n&&(k=c/n,v=a*k),a&&v>d&&(k=d/a,_=n*k,v=d),Ce(u,{width:_,height:v}),Ce(u.getElementsByTagName("img")[0],G({width:s*k,height:l*k},xe(G({translateX:-o*k,translateY:-h*k},e))))}))}},Xa={bind:function(){var e=this.element,t=this.options,i=this.cropper;ce(t.cropstart)&&ye(e,Xt,t.cropstart),ce(t.cropmove)&&ye(e,Wt,t.cropmove),ce(t.cropend)&&ye(e,Pt,t.cropend),ce(t.crop)&&ye(e,Ht,t.crop),ce(t.zoom)&&ye(e,Yt,t.zoom),ye(i,di,this.onCropStart=this.cropStart.bind(this)),t.zoomable&&t.zoomOnWheel&&ye(i,bi,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),t.toggleDragModeOnDblclick&&ye(i,fi,this.onDblclick=this.dblclick.bind(this)),ye(e.ownerDocument,mi,this.onCropMove=this.cropMove.bind(this)),ye(e.ownerDocument,gi,this.onCropEnd=this.cropEnd.bind(this)),t.responsive&&ye(window,pi,this.onResize=this.resize.bind(this))},unbind:function(){var e=this.element,t=this.options,i=this.cropper;ce(t.cropstart)&&ke(e,Xt,t.cropstart),ce(t.cropmove)&&ke(e,Wt,t.cropmove),ce(t.cropend)&&ke(e,Pt,t.cropend),ce(t.crop)&&ke(e,Ht,t.crop),ce(t.zoom)&&ke(e,Yt,t.zoom),ke(i,di,this.onCropStart),t.zoomable&&t.zoomOnWheel&&ke(i,bi,this.onWheel,{passive:!1,capture:!0}),t.toggleDragModeOnDblclick&&ke(i,fi,this.onDblclick),ke(e.ownerDocument,mi,this.onCropMove),ke(e.ownerDocument,gi,this.onCropEnd),t.responsive&&ke(window,pi,this.onResize)}},Ya={resize:function(){if(!this.disabled){var e=this.options,t=this.container,i=this.containerData,n=t.offsetWidth/i.width,a=t.offsetHeight/i.height,s=Math.abs(n-1)>Math.abs(a-1)?n:a;if(s!==1){var l,o;e.restore&&(l=this.getCanvasData(),o=this.getCropBoxData()),this.render(),e.restore&&(this.setCanvasData(te(l,function(h,u){l[u]=h*s})),this.setCropBoxData(te(o,function(h,u){o[u]=h*s})))}}},dblclick:function(){this.disabled||this.options.dragMode===xi||this.setDragMode(Da(this.dragBox,Bt)?Ji:Kt)},wheel:function(e){var t=this,i=Number(this.options.wheelZoomRatio)||.1,n=1;this.disabled||(e.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){t.wheeling=!1},50),e.deltaY?n=e.deltaY>0?1:-1:e.wheelDelta?n=-e.wheelDelta/120:e.detail&&(n=e.detail>0?1:-1),this.zoom(-n*i,e)))},cropStart:function(e){var t=e.buttons,i=e.button;if(!(this.disabled||(e.type==="mousedown"||e.type==="pointerdown"&&e.pointerType==="mouse")&&(I(t)&&t!==1||I(i)&&i!==0||e.ctrlKey))){var n=this.options,a=this.pointers,s;e.changedTouches?te(e.changedTouches,function(l){a[l.identifier]=bt(l)}):a[e.pointerId||0]=bt(e),Object.keys(a).length>1&&n.zoomable&&n.zoomOnTouch?s=Qi:s=Ut(e.target,et),ma.test(s)&&Ve(this.element,Xt,{originalEvent:e,action:s})!==!1&&(e.preventDefault(),this.action=s,this.cropping=!1,s===Zi&&(this.cropping=!0,ne(this.dragBox,yt)))}},cropMove:function(e){var t=this.action;if(!(this.disabled||!t)){var i=this.pointers;e.preventDefault(),Ve(this.element,Wt,{originalEvent:e,action:t})!==!1&&(e.changedTouches?te(e.changedTouches,function(n){G(i[n.identifier]||{},bt(n,!0))}):G(i[e.pointerId||0]||{},bt(e,!0)),this.change(e))}},cropEnd:function(e){if(!this.disabled){var t=this.action,i=this.pointers;e.changedTouches?te(e.changedTouches,function(n){delete i[n.identifier]}):delete i[e.pointerId||0],t&&(e.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,Ue(this.dragBox,yt,this.cropped&&this.options.modal)),Ve(this.element,Pt,{originalEvent:e,action:t}))}}},Ua={change:function(e){var t=this.options,i=this.canvasData,n=this.containerData,a=this.cropBoxData,s=this.pointers,l=this.action,o=t.aspectRatio,h=a.left,u=a.top,f=a.width,c=a.height,d=h+f,_=u+c,v=0,k=0,N=n.width,C=n.height,S=!0,ie;!o&&e.shiftKey&&(o=f&&c?f/c:1),this.limited&&(v=a.minLeft,k=a.minTop,N=v+Math.min(n.width,i.width,i.left+i.width),C=k+Math.min(n.height,i.height,i.top+i.height));var y=s[Object.keys(s)[0]],g={x:y.endX-y.startX,y:y.endY-y.startY},M=function(B){switch(B){case Se:d+g.x>N&&(g.x=N-d);break;case Ie:h+g.x<v&&(g.x=v-h);break;case Te:u+g.y<k&&(g.y=k-u);break;case Xe:_+g.y>C&&(g.y=C-_);break}};switch(l){case Zt:h+=g.x,u+=g.y;break;case Se:if(g.x>=0&&(d>=N||o&&(u<=k||_>=C))){S=!1;break}M(Se),f+=g.x,f<0&&(l=Ie,f=-f,h-=f),o&&(c=f/o,u+=(a.height-c)/2);break;case Te:if(g.y<=0&&(u<=k||o&&(h<=v||d>=N))){S=!1;break}M(Te),c-=g.y,u+=g.y,c<0&&(l=Xe,c=-c,u-=c),o&&(f=c*o,h+=(a.width-f)/2);break;case Ie:if(g.x<=0&&(h<=v||o&&(u<=k||_>=C))){S=!1;break}M(Ie),f-=g.x,h+=g.x,f<0&&(l=Se,f=-f,h-=f),o&&(c=f/o,u+=(a.height-c)/2);break;case Xe:if(g.y>=0&&(_>=C||o&&(h<=v||d>=N))){S=!1;break}M(Xe),c+=g.y,c<0&&(l=Te,c=-c,u-=c),o&&(f=c*o,h+=(a.width-f)/2);break;case Ze:if(o){if(g.y<=0&&(u<=k||d>=N)){S=!1;break}M(Te),c-=g.y,u+=g.y,f=c*o}else M(Te),M(Se),g.x>=0?d<N?f+=g.x:g.y<=0&&u<=k&&(S=!1):f+=g.x,g.y<=0?u>k&&(c-=g.y,u+=g.y):(c-=g.y,u+=g.y);f<0&&c<0?(l=Je,c=-c,f=-f,u-=c,h-=f):f<0?(l=Ke,f=-f,h-=f):c<0&&(l=Qe,c=-c,u-=c);break;case Ke:if(o){if(g.y<=0&&(u<=k||h<=v)){S=!1;break}M(Te),c-=g.y,u+=g.y,f=c*o,h+=a.width-f}else M(Te),M(Ie),g.x<=0?h>v?(f-=g.x,h+=g.x):g.y<=0&&u<=k&&(S=!1):(f-=g.x,h+=g.x),g.y<=0?u>k&&(c-=g.y,u+=g.y):(c-=g.y,u+=g.y);f<0&&c<0?(l=Qe,c=-c,f=-f,u-=c,h-=f):f<0?(l=Ze,f=-f,h-=f):c<0&&(l=Je,c=-c,u-=c);break;case Je:if(o){if(g.x<=0&&(h<=v||_>=C)){S=!1;break}M(Ie),f-=g.x,h+=g.x,c=f/o}else M(Xe),M(Ie),g.x<=0?h>v?(f-=g.x,h+=g.x):g.y>=0&&_>=C&&(S=!1):(f-=g.x,h+=g.x),g.y>=0?_<C&&(c+=g.y):c+=g.y;f<0&&c<0?(l=Ze,c=-c,f=-f,u-=c,h-=f):f<0?(l=Qe,f=-f,h-=f):c<0&&(l=Ke,c=-c,u-=c);break;case Qe:if(o){if(g.x>=0&&(d>=N||_>=C)){S=!1;break}M(Se),f+=g.x,c=f/o}else M(Xe),M(Se),g.x>=0?d<N?f+=g.x:g.y>=0&&_>=C&&(S=!1):f+=g.x,g.y>=0?_<C&&(c+=g.y):c+=g.y;f<0&&c<0?(l=Ke,c=-c,f=-f,u-=c,h-=f):f<0?(l=Je,f=-f,h-=f):c<0&&(l=Ze,c=-c,u-=c);break;case Ki:this.move(g.x,g.y),S=!1;break;case Qi:this.zoom(Ca(s),e),S=!1;break;case Zi:if(!g.x||!g.y){S=!1;break}ie=ar(this.cropper),h=y.startX-ie.left,u=y.startY-ie.top,f=a.minWidth,c=a.minHeight,g.x>0?l=g.y>0?Qe:Ze:g.x<0&&(h-=f,l=g.y>0?Je:Ke),g.y<0&&(u-=c),this.cropped||(Ee(this.cropBox,fe),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}S&&(a.width=f,a.height=c,a.left=h,a.top=u,this.action=l,this.renderCropBox()),te(s,function(R){R.startX=R.endX,R.startY=R.endY})}},ja={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&ne(this.dragBox,yt),Ee(this.cropBox,fe),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=G({},this.initialImageData),this.canvasData=G({},this.initialCanvasData),this.cropBoxData=G({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(G(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),Ee(this.dragBox,yt),ne(this.cropBox,fe)),this},replace:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&e&&(this.isImg&&(this.element.src=e),t?(this.url=e,this.image.src=e,this.ready&&(this.viewBoxImage.src=e,te(this.previews,function(i){i.getElementsByTagName("img")[0].src=e}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(e))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,Ee(this.cropper,ui)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,ne(this.cropper,ui)),this},destroy:function(){var e=this.element;return e[q]?(e[q]=void 0,this.isImg&&this.replaced&&(e.src=this.originalUrl),this.uncreate(),this):this},move:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,i=this.canvasData,n=i.left,a=i.top;return this.moveTo(It(e)?e:n+Number(e),It(t)?t:a+Number(t))},moveTo:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,i=this.canvasData,n=!1;return e=Number(e),t=Number(t),this.ready&&!this.disabled&&this.options.movable&&(I(e)&&(i.left=e,n=!0),I(t)&&(i.top=t,n=!0),n&&this.renderCanvas(!0)),this},zoom:function(e,t){var i=this.canvasData;return e=Number(e),e<0?e=1/(1-e):e=1+e,this.zoomTo(i.width*e/i.naturalWidth,null,t)},zoomTo:function(e,t,i){var n=this.options,a=this.canvasData,s=a.width,l=a.height,o=a.naturalWidth,h=a.naturalHeight;if(e=Number(e),e>=0&&this.ready&&!this.disabled&&n.zoomable){var u=o*e,f=h*e;if(Ve(this.element,Yt,{ratio:e,oldRatio:s/o,originalEvent:i})===!1)return this;if(i){var c=this.pointers,d=ar(this.cropper),_=c&&Object.keys(c).length?Ra(c):{pageX:i.pageX,pageY:i.pageY};a.left-=(u-s)*((_.pageX-d.left-a.left)/s),a.top-=(f-l)*((_.pageY-d.top-a.top)/l)}else Ye(t)&&I(t.x)&&I(t.y)?(a.left-=(u-s)*((t.x-a.left)/s),a.top-=(f-l)*((t.y-a.top)/l)):(a.left-=(u-s)/2,a.top-=(f-l)/2);a.width=u,a.height=f,this.renderCanvas(!0)}return this},rotate:function(e){return this.rotateTo((this.imageData.rotate||0)+Number(e))},rotateTo:function(e){return e=Number(e),I(e)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=e%360,this.renderCanvas(!0,!0)),this},scaleX:function(e){var t=this.imageData.scaleY;return this.scale(e,I(t)?t:1)},scaleY:function(e){var t=this.imageData.scaleX;return this.scale(I(t)?t:1,e)},scale:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,i=this.imageData,n=!1;return e=Number(e),t=Number(t),this.ready&&!this.disabled&&this.options.scalable&&(I(e)&&(i.scaleX=e,n=!0),I(t)&&(i.scaleY=t,n=!0),n&&this.renderCanvas(!0,!0)),this},getData:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,t=this.options,i=this.imageData,n=this.canvasData,a=this.cropBoxData,s;if(this.ready&&this.cropped){s={x:a.left-n.left,y:a.top-n.top,width:a.width,height:a.height};var l=i.width/i.naturalWidth;if(te(s,function(u,f){s[f]=u/l}),e){var o=Math.round(s.y+s.height),h=Math.round(s.x+s.width);s.x=Math.round(s.x),s.y=Math.round(s.y),s.width=h-s.x,s.height=o-s.y}}else s={x:0,y:0,width:0,height:0};return t.rotatable&&(s.rotate=i.rotate||0),t.scalable&&(s.scaleX=i.scaleX||1,s.scaleY=i.scaleY||1),s},setData:function(e){var t=this.options,i=this.imageData,n=this.canvasData,a={};if(this.ready&&!this.disabled&&Ye(e)){var s=!1;t.rotatable&&I(e.rotate)&&e.rotate!==i.rotate&&(i.rotate=e.rotate,s=!0),t.scalable&&(I(e.scaleX)&&e.scaleX!==i.scaleX&&(i.scaleX=e.scaleX,s=!0),I(e.scaleY)&&e.scaleY!==i.scaleY&&(i.scaleY=e.scaleY,s=!0)),s&&this.renderCanvas(!0,!0);var l=i.width/i.naturalWidth;I(e.x)&&(a.left=e.x*l+n.left),I(e.y)&&(a.top=e.y*l+n.top),I(e.width)&&(a.width=e.width*l),I(e.height)&&(a.height=e.height*l),this.setCropBoxData(a)}return this},getContainerData:function(){return this.ready?G({},this.containerData):{}},getImageData:function(){return this.sized?G({},this.imageData):{}},getCanvasData:function(){var e=this.canvasData,t={};return this.ready&&te(["left","top","width","height","naturalWidth","naturalHeight"],function(i){t[i]=e[i]}),t},setCanvasData:function(e){var t=this.canvasData,i=t.aspectRatio;return this.ready&&!this.disabled&&Ye(e)&&(I(e.left)&&(t.left=e.left),I(e.top)&&(t.top=e.top),I(e.width)?(t.width=e.width,t.height=e.width/i):I(e.height)&&(t.height=e.height,t.width=e.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var e=this.cropBoxData,t;return this.ready&&this.cropped&&(t={left:e.left,top:e.top,width:e.width,height:e.height}),t||{}},setCropBoxData:function(e){var t=this.cropBoxData,i=this.options.aspectRatio,n,a;return this.ready&&this.cropped&&!this.disabled&&Ye(e)&&(I(e.left)&&(t.left=e.left),I(e.top)&&(t.top=e.top),I(e.width)&&e.width!==t.width&&(n=!0,t.width=e.width),I(e.height)&&e.height!==t.height&&(a=!0,t.height=e.height),i&&(n?t.height=t.width/i:a&&(t.width=t.height*i)),this.renderCropBox()),this},getCroppedCanvas:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var t=this.canvasData,i=Sa(this.image,this.imageData,t,e);if(!this.cropped)return i;var n=this.getData(),a=n.x,s=n.y,l=n.width,o=n.height,h=i.width/Math.floor(t.naturalWidth);h!==1&&(a*=h,s*=h,l*=h,o*=h);var u=l/o,f=Re({aspectRatio:u,width:e.maxWidth||1/0,height:e.maxHeight||1/0}),c=Re({aspectRatio:u,width:e.minWidth||0,height:e.minHeight||0},"cover"),d=Re({aspectRatio:u,width:e.width||(h!==1?i.width:l),height:e.height||(h!==1?i.height:o)}),_=d.width,v=d.height;_=Math.min(f.width,Math.max(c.width,_)),v=Math.min(f.height,Math.max(c.height,v));var k=document.createElement("canvas"),N=k.getContext("2d");k.width=je(_),k.height=je(v),N.fillStyle=e.fillColor||"transparent",N.fillRect(0,0,_,v);var C=e.imageSmoothingEnabled,S=C===void 0?!0:C,ie=e.imageSmoothingQuality;N.imageSmoothingEnabled=S,ie&&(N.imageSmoothingQuality=ie);var y=i.width,g=i.height,M=a,R=s,B,re,oe,se,X,F;M<=-l||M>y?(M=0,B=0,oe=0,X=0):M<=0?(oe=-M,M=0,B=Math.min(y,l+M),X=B):M<=y&&(oe=0,B=Math.min(l,y-M),X=B),B<=0||R<=-o||R>g?(R=0,re=0,se=0,F=0):R<=0?(se=-R,R=0,re=Math.min(g,o+R),F=re):R<=g&&(se=0,re=Math.min(o,g-R),F=re);var Y=[M,R,B,re];if(X>0&&F>0){var Q=_/l;Y.push(oe*Q,se*Q,X*Q,F*Q)}return N.drawImage.apply(N,[i].concat(Fi(Y.map(function(he){return Math.floor(je(he))})))),k},setAspectRatio:function(e){var t=this.options;return!this.disabled&&!It(e)&&(t.aspectRatio=Math.max(0,e)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(e){var t=this.options,i=this.dragBox,n=this.face;if(this.ready&&!this.disabled){var a=e===Kt,s=t.movable&&e===Ji;e=a||s?e:xi,t.dragMode=e,tt(i,et,e),Ue(i,Bt,a),Ue(i,Lt,s),t.cropBoxMovable||(tt(n,et,e),Ue(n,Bt,a),Ue(n,Lt,s))}return this}},Va=De.Cropper,sr=function(){function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(ra(this,r),!e||!pa.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=G({},wi,Ye(t)&&t),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return aa(r,[{key:"init",value:function(){var t=this.element,i=t.tagName.toLowerCase(),n;if(!t[q]){if(t[q]=this,i==="img"){if(this.isImg=!0,n=t.getAttribute("src")||"",this.originalUrl=n,!n)return;n=t.src}else i==="canvas"&&window.HTMLCanvasElement&&(n=t.toDataURL());this.load(n)}}},{key:"load",value:function(t){var i=this;if(t){this.url=t,this.imageData={};var n=this.element,a=this.options;if(!a.rotatable&&!a.scalable&&(a.checkOrientation=!1),!a.checkOrientation||!window.ArrayBuffer){this.clone();return}if(ga.test(t)){_a.test(t)?this.read(za(t)):this.clone();return}var s=new XMLHttpRequest,l=this.clone.bind(this);this.reloading=!0,this.xhr=s,s.onabort=l,s.onerror=l,s.ontimeout=l,s.onprogress=function(){s.getResponseHeader("content-type")!==vi&&s.abort()},s.onload=function(){i.read(s.response)},s.onloadend=function(){i.reloading=!1,i.xhr=null},a.checkCrossOrigin&&ki(t)&&n.crossOrigin&&(t=Ei(t)),s.open("GET",t,!0),s.responseType="arraybuffer",s.withCredentials=n.crossOrigin==="use-credentials",s.send()}}},{key:"read",value:function(t){var i=this.options,n=this.imageData,a=La(t),s=0,l=1,o=1;if(a>1){this.url=Ba(t,vi);var h=Ha(a);s=h.rotate,l=h.scaleX,o=h.scaleY}i.rotatable&&(n.rotate=s),i.scalable&&(n.scaleX=l,n.scaleY=o),this.clone()}},{key:"clone",value:function(){var t=this.element,i=this.url,n=t.crossOrigin,a=i;this.options.checkCrossOrigin&&ki(i)&&(n||(n="anonymous"),a=Ei(i)),this.crossOrigin=n,this.crossOriginUrl=a;var s=document.createElement("img");n&&(s.crossOrigin=n),s.src=a||i,s.alt=t.alt||"The image to crop",this.image=s,s.onload=this.start.bind(this),s.onerror=this.stop.bind(this),ne(s,ci),t.parentNode.insertBefore(s,t.nextSibling)}},{key:"start",value:function(){var t=this,i=this.image;i.onload=null,i.onerror=null,this.sizing=!0;var n=De.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(De.navigator.userAgent),a=function(h,u){G(t.imageData,{naturalWidth:h,naturalHeight:u,aspectRatio:h/u}),t.initialImageData=G({},t.imageData),t.sizing=!1,t.sized=!0,t.build()};if(i.naturalWidth&&!n){a(i.naturalWidth,i.naturalHeight);return}var s=document.createElement("img"),l=document.body||document.documentElement;this.sizingImage=s,s.onload=function(){a(s.width,s.height),n||l.removeChild(s)},s.src=i.src,n||(s.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",l.appendChild(s))}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var t=this.element,i=this.options,n=this.image,a=t.parentNode,s=document.createElement("div");s.innerHTML=ba;var l=s.querySelector(".".concat(q,"-container")),o=l.querySelector(".".concat(q,"-canvas")),h=l.querySelector(".".concat(q,"-drag-box")),u=l.querySelector(".".concat(q,"-crop-box")),f=u.querySelector(".".concat(q,"-face"));this.container=a,this.cropper=l,this.canvas=o,this.dragBox=h,this.cropBox=u,this.viewBox=l.querySelector(".".concat(q,"-view-box")),this.face=f,o.appendChild(n),ne(t,fe),a.insertBefore(l,t.nextSibling),this.isImg||Ee(n,ci),this.initPreview(),this.bind(),i.initialAspectRatio=Math.max(0,i.initialAspectRatio)||NaN,i.aspectRatio=Math.max(0,i.aspectRatio)||NaN,i.viewMode=Math.max(0,Math.min(3,Math.round(i.viewMode)))||0,ne(u,fe),i.guides||ne(u.getElementsByClassName("".concat(q,"-dashed")),fe),i.center||ne(u.getElementsByClassName("".concat(q,"-center")),fe),i.background&&ne(l,"".concat(q,"-bg")),i.highlight||ne(f,ua),i.cropBoxMovable&&(ne(f,Lt),tt(f,et,Zt)),i.cropBoxResizable||(ne(u.getElementsByClassName("".concat(q,"-line")),fe),ne(u.getElementsByClassName("".concat(q,"-point")),fe)),this.render(),this.ready=!0,this.setDragMode(i.dragMode),i.autoCrop&&this.crop(),this.setData(i.data),ce(i.ready)&&ye(t,_i,i.ready,{once:!0}),Ve(t,_i)}}},{key:"unbuild",value:function(){this.ready&&(this.ready=!1,this.unbind(),this.resetPreview(),this.cropper.parentNode.removeChild(this.cropper),Ee(this.element,fe))}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=Va,r}},{key:"setDefaults",value:function(t){G(wi,Ye(t)&&t)}}]),r}();G(sr.prototype,Pa,Wa,Xa,Ya,Ua,ja);function qa(r){let e,t;return{c(){e=K("img"),de(e.src,t=r[0])||m(e,"src",t),m(e,"alt","")},m(i,n){O(i,e,n),r[4](e)},p(i,[n]){n&1&&!de(e.src,t=i[0])&&m(e,"src",t)},i:L,o:L,d(i){i&&T(e),r[4](null)}}}function Ga(r,e,t){let{image:i}=e,n;const a=it();let s;function l(){s.destroy()}function o(){s&&l(),s=new sr(n,{autoCropArea:1,cropend(){const u=s.getCroppedCanvas().toDataURL();a("crop",u)}}),a("crop",i)}function h(u){j[u?"unshift":"push"](()=>{n=u,t(1,n)})}return r.$$set=u=>{"image"in u&&t(0,i=u.image)},[i,n,l,o,h]}class or extends _e{constructor(e){super(),pe(this,e,Ga,qa,le,{image:0,destroy:2,create:3})}get image(){return this.$$.ctx[0]}set image(e){this.$$set({image:e}),V()}get destroy(){return this.$$.ctx[2]}get create(){return this.$$.ctx[3]}}class Di{constructor(e,t){this.x=e,this.y=t}}class Mi extends Di{update(e){this.x=e.x,this.y=e.y}moveByAngle(e,t){const i=e+Math.PI/2;this.x=this.x+Math.sin(i)*t,this.y=this.y-Math.cos(i)*t}equalsTo(e){return this.x===e.x&&this.y===e.y}getDifferenceTo(e){return new Di(this.x-e.x,this.y-e.y)}getDistanceTo(e){const t=this.getDifferenceTo(e);return Math.sqrt(Math.pow(t.x,2)+Math.pow(t.y,2))}getAngleTo(e){const t=this.getDifferenceTo(e);return Math.atan2(t.y,t.x)}toObject(){return{x:this.x,y:this.y}}}const Fa=30;class Za{constructor({radius:e=Fa,enabled:t=!0,initialPoint:i={x:0,y:0}}={}){this.radius=e,this._isEnabled=t,this.pointer=new Mi(i.x,i.y),this.brush=new Mi(i.x,i.y),this.angle=0,this.distance=0,this._hasMoved=!1}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}isEnabled(){return this._isEnabled}setRadius(e){this.radius=e}getRadius(){return this.radius}getBrushCoordinates(){return this.brush.toObject()}getPointerCoordinates(){return this.pointer.toObject()}getBrush(){return this.brush}getPointer(){return this.pointer}getAngle(){return this.angle}getDistance(){return this.distance}brushHasMoved(){return this._hasMoved}update(e,{both:t=!1}={}){return this._hasMoved=!1,this.pointer.equalsTo(e)&&!t?!1:(this.pointer.update(e),t?(this._hasMoved=!0,this.brush.update(e),!0):(this._isEnabled?(this.distance=this.pointer.getDistanceTo(this.brush),this.angle=this.pointer.getAngleTo(this.brush),this.distance>this.radius&&(this.brush.moveByAngle(this.angle,this.distance-this.radius),this._hasMoved=!0)):(this.distance=0,this.angle=0,this.brush.update(e),this._hasMoved=!0),!0))}}var lr=function(){if(typeof Map<"u")return Map;function r(e,t){var i=-1;return e.some(function(n,a){return n[0]===t?(i=a,!0):!1}),i}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(t){var i=r(this.__entries__,t),n=this.__entries__[i];return n&&n[1]},e.prototype.set=function(t,i){var n=r(this.__entries__,t);~n?this.__entries__[n][1]=i:this.__entries__.push([t,i])},e.prototype.delete=function(t){var i=this.__entries__,n=r(i,t);~n&&i.splice(n,1)},e.prototype.has=function(t){return!!~r(this.__entries__,t)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(t,i){i===void 0&&(i=null);for(var n=0,a=this.__entries__;n<a.length;n++){var s=a[n];t.call(i,s[1],s[0])}},e}()}(),jt=typeof window<"u"&&typeof document<"u"&&window.document===document,kt=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),Ka=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(kt):function(r){return setTimeout(function(){return r(Date.now())},1e3/60)}}(),Qa=2;function Ja(r,e){var t=!1,i=!1,n=0;function a(){t&&(t=!1,r()),i&&l()}function s(){Ka(a)}function l(){var o=Date.now();if(t){if(o-n<Qa)return;i=!0}else t=!0,i=!1,setTimeout(s,e);n=o}return l}var xa=20,$a=["top","right","bottom","left","width","height","size","weight"],en=typeof MutationObserver<"u",tn=function(){function r(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=Ja(this.refresh.bind(this),xa)}return r.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},r.prototype.removeObserver=function(e){var t=this.observers_,i=t.indexOf(e);~i&&t.splice(i,1),!t.length&&this.connected_&&this.disconnect_()},r.prototype.refresh=function(){var e=this.updateObservers_();e&&this.refresh()},r.prototype.updateObservers_=function(){var e=this.observers_.filter(function(t){return t.gatherActive(),t.hasActive()});return e.forEach(function(t){return t.broadcastActive()}),e.length>0},r.prototype.connect_=function(){!jt||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),en?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},r.prototype.disconnect_=function(){!jt||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},r.prototype.onTransitionEnd_=function(e){var t=e.propertyName,i=t===void 0?"":t,n=$a.some(function(a){return!!~i.indexOf(a)});n&&this.refresh()},r.getInstance=function(){return this.instance_||(this.instance_=new r),this.instance_},r.instance_=null,r}(),hr=function(r,e){for(var t=0,i=Object.keys(e);t<i.length;t++){var n=i[t];Object.defineProperty(r,n,{value:e[n],enumerable:!1,writable:!1,configurable:!0})}return r},qe=function(r){var e=r&&r.ownerDocument&&r.ownerDocument.defaultView;return e||kt},ur=Ot(0,0,0,0);function Et(r){return parseFloat(r)||0}function Oi(r){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return e.reduce(function(i,n){var a=r["border-"+n+"-width"];return i+Et(a)},0)}function rn(r){for(var e=["top","right","bottom","left"],t={},i=0,n=e;i<n.length;i++){var a=n[i],s=r["padding-"+a];t[a]=Et(s)}return t}function an(r){var e=r.getBBox();return Ot(0,0,e.width,e.height)}function nn(r){var e=r.clientWidth,t=r.clientHeight;if(!e&&!t)return ur;var i=qe(r).getComputedStyle(r),n=rn(i),a=n.left+n.right,s=n.top+n.bottom,l=Et(i.width),o=Et(i.height);if(i.boxSizing==="border-box"&&(Math.round(l+a)!==e&&(l-=Oi(i,"left","right")+a),Math.round(o+s)!==t&&(o-=Oi(i,"top","bottom")+s)),!on(r)){var h=Math.round(l+a)-e,u=Math.round(o+s)-t;Math.abs(h)!==1&&(l-=h),Math.abs(u)!==1&&(o-=u)}return Ot(n.left,n.top,l,o)}var sn=function(){return typeof SVGGraphicsElement<"u"?function(r){return r instanceof qe(r).SVGGraphicsElement}:function(r){return r instanceof qe(r).SVGElement&&typeof r.getBBox=="function"}}();function on(r){return r===qe(r).document.documentElement}function ln(r){return jt?sn(r)?an(r):nn(r):ur}function hn(r){var e=r.x,t=r.y,i=r.width,n=r.height,a=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,s=Object.create(a.prototype);return hr(s,{x:e,y:t,width:i,height:n,top:t,right:e+i,bottom:n+t,left:e}),s}function Ot(r,e,t,i){return{x:r,y:e,width:t,height:i}}var un=function(){function r(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Ot(0,0,0,0),this.target=e}return r.prototype.isActive=function(){var e=ln(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},r.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},r}(),cn=function(){function r(e,t){var i=hn(t);hr(this,{target:e,contentRect:i})}return r}(),fn=function(){function r(e,t,i){if(this.activeObservations_=[],this.observations_=new lr,typeof e!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=i}return r.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(e instanceof qe(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new un(e)),this.controller_.addObserver(this),this.controller_.refresh())}},r.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(e instanceof qe(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},r.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},r.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&e.activeObservations_.push(t)})},r.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map(function(i){return new cn(i.target,i.broadcastRect())});this.callback_.call(e,t,e),this.clearActive()}},r.prototype.clearActive=function(){this.activeObservations_.splice(0)},r.prototype.hasActive=function(){return this.activeObservations_.length>0},r}(),cr=typeof WeakMap<"u"?new WeakMap:new lr,fr=function(){function r(e){if(!(this instanceof r))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var t=tn.getInstance(),i=new fn(e,t,this);cr.set(this,i)}return r}();["observe","unobserve","disconnect"].forEach(function(r){fr.prototype[r]=function(){var e;return(e=cr.get(this))[r].apply(e,arguments)}});var dn=function(){return typeof kt.ResizeObserver<"u"?kt.ResizeObserver:fr}();function Ti(r,e,t){const i=r.slice();return i[61]=e[t].name,i[62]=e[t].zIndex,i[63]=e[t].opacity,i[64]=e,i[65]=t,i}function Ci(r){let e,t,i;return{c(){e=K("div"),e.textContent="Start drawing",m(e,"class","start-prompt svelte-yigbas")},m(n,a){O(n,e,a),i=!0},i(n){i||(n&&qt(()=>{i&&(t||(t=ii(e,ai,{duration:50},!0)),t.run(1))}),i=!0)},o(n){n&&(t||(t=ii(e,ai,{duration:50},!1)),t.run(0)),i=!1},d(n){n&&T(e),n&&t&&t.end()}}}function Ri(r){let e,t=r[61],i,n;const a=()=>r[31](e,t),s=()=>r[31](null,t);return{c(){e=K("canvas"),m(e,"key",r[61]),ri(e,"z-index",r[62]),m(e,"class","svelte-yigbas"),U(e,"lr",r[5]),U(e,"tb",!r[5]),ri(e,"opacity",r[63])},m(l,o){O(l,e,o),a(),i||(n=[ee(e,"mousedown",r[61]==="interface"?r[7]:void 0),ee(e,"mousemove",r[61]==="interface"?r[8]:void 0),ee(e,"mouseup",r[61]==="interface"?r[9]:void 0),ee(e,"mouseout",r[61]==="interface"?r[9]:void 0),ee(e,"blur",r[61]==="interface"?r[9]:void 0),ee(e,"touchstart",r[61]==="interface"?r[7]:void 0),ee(e,"touchmove",r[61]==="interface"?r[8]:void 0),ee(e,"touchend",r[61]==="interface"?r[9]:void 0),ee(e,"touchcancel",r[61]==="interface"?r[9]:void 0),ee(e,"click",vr(r[30]))],i=!0)},p(l,o){r=l,t!==r[61]&&(s(),t=r[61],a()),o[0]&32&&U(e,"lr",r[5]),o[0]&32&&U(e,"tb",!r[5])},d(l){l&&T(e),s(),i=!1,Vi(n)}}}function mn(r){let e,t,i,n=r[4]===0&&Ci(),a=ti(r[6]),s=[];for(let l=0;l<a.length;l+=1)s[l]=Ri(Ti(r,a,l));return{c(){e=K("div"),n&&n.c(),t=$();for(let l=0;l<s.length;l+=1)s[l].c();m(e,"class","wrap svelte-yigbas"),qt(()=>r[33].call(e))},m(l,o){O(l,e,o),n&&n.m(e,null),Z(e,t);for(let h=0;h<s.length;h+=1)s[h]&&s[h].m(e,null);r[32](e),i=ji(e,r[33].bind(e))},p(l,o){if(l[4]===0?n?o[0]&16&&w(n,1):(n=Ci(),n.c(),w(n,1),n.m(e,t)):n&&(be(),D(n,1,1,()=>{n=null}),ve()),o[0]&993){a=ti(l[6]);let h;for(h=0;h<a.length;h+=1){const u=Ti(l,a,h);s[h]?s[h].p(u,o):(s[h]=Ri(u),s[h].c(),s[h].m(e,null))}for(;h<s.length;h+=1)s[h].d(1);s.length=a.length}},i(l){w(n)},o(l){D(n)},d(l){l&&T(e),n&&n.d(),pr(s,l),r[32](null),i()}}}let gn="#aaa";function _n(r,e){return{x:r.x+(e.x-r.x)/2,y:r.y+(e.y-r.y)/2}}function pn(r,e,t){let i;const n=it();let{value:a}=e,{value_img:s}=e,{mode:l="sketch"}=e,{brush_color:o="#0b0f19"}=e,{brush_radius:h}=e,{mask_opacity:u=.7}=e,{source:f}=e,{width:c=400}=e,{height:d=200}=e,{container_height:_=200}=e,{shape:v}=e,k,N=c,C=d,S;const ie=[{name:"interface",zIndex:15},{name:"mask",zIndex:13,opacity:u},{name:"drawing",zIndex:11},{name:"temp",zIndex:12}];let y={},g={},M=[],R=[],B=!0,re=!0,oe=!1,se=!1,X=null,F=null,Y=null,Q=0;function he(){if(!v){g.temp.drawImage(s,0,0,c,d);return}let b=s.naturalWidth,A=s.naturalHeight;const z=v[0]/v[1],ae=b/A;let p=0,J=0;z<ae?(b=v[1]*ae,A=v[1],p=(v[0]-b)/2):z>ae?(b=v[0],A=v[0]/ae,J=(v[1]-A)/2):(p=0,J=0,b=v[0],A=v[1]),g.temp.drawImage(s,p,J,b,A)}Vt(async()=>{Object.keys(y).forEach(b=>{t(27,g[b]=y[b].getContext("2d"),g)}),await Ne(),s&&(s.addEventListener("load",b=>{f==="webcam"?(g.temp.save(),g.temp.translate(c,0),g.temp.scale(-1,1),g.temp.drawImage(s,0,0),g.temp.restore()):he(),g.drawing.drawImage(y.temp,0,0,c,d),Me()}),setTimeout(()=>{f==="webcam"?(g.temp.save(),g.temp.translate(c,0),g.temp.scale(-1,1),g.temp.drawImage(s,0,0),g.temp.restore()):he(),g.drawing.drawImage(y.temp,0,0,c,d),Oe({lines:R.slice()}),Me()},100)),t(29,X=new Za({radius:h*.05,enabled:!0,initialPoint:{x:c/2,y:d/2}})),Y=new dn((b,A,...z)=>{ot()}),Y.observe(F),Fe(),t(25,k=!0),requestAnimationFrame(()=>{we(),requestAnimationFrame(()=>{Ae()})})});function we(){const b=c/2,A=d/2;X.update({x:b,y:A},{both:!0}),X.update({x:b,y:A},{both:!1}),B=!0,re=!0}br(()=>{t(25,k=!1),Y.unobserve(F)});function ue(b){dt(),s&&(f==="webcam"?(g.temp.save(),g.temp.translate(c,0),g.temp.scale(-1,1),g.temp.drawImage(s,0,0),g.temp.restore()):he(),(!R||!R.length)&&g.drawing.drawImage(y.temp,0,0,c,d)),Oe({lines:b}),t(4,Q=b.length),t(28,R=b),g.drawing.drawImage(y.temp,0,0,c,d),R.length==0&&n("clear")}function E(){ue([]),Me()}function Le(){const b=R.slice(0,-1);ue(b),Me()}let Oe=({lines:b})=>{b.forEach(A=>{const{points:z,brush_color:ae,brush_radius:p}=A;ut({points:z,brush_color:ae,brush_radius:p,mask:l==="mask"})}),ft(),l==="mask"&&ct()},He=b=>{b.preventDefault(),se=!0;const{x:A,y:z}=lt(b);b.touches&&b.touches.length>0&&X.update({x:A,y:z},{both:!0}),ht(A,z),t(4,Q+=1)},Pe=b=>{b.preventDefault();const{x:A,y:z}=lt(b);ht(A,z)},Ge=b=>{b.preventDefault(),Pe(b),oe=!1,se=!1,ft(),l==="mask"&&ct()},rt=0,at=0,nt=0,st=!1,ot=async()=>{if(v&&F){const z=F?.getBoundingClientRect(),ae=v[0]/v[1],p=z.width/z.height;t(5,st=ae<p)}if(c===rt&&d===at&&nt===_)return;const b={width:c,height:d},A={height:_,width:_*(b.width/b.height)};await Promise.all([We(y.interface,b,A),We(y.drawing,b,A),We(y.temp,b,A),We(y.mask,b,A,!1)]),h||t(10,h=20*(b.width/A.width)),Fe({once:!0}),setTimeout(()=>{at=d,rt=c,nt=_},10),await Ne(),Ae()},We=async(b,A,z,ae=!0)=>{if(!k)return;await Ne();const p=window.devicePixelRatio||1;b.width=A.width*(ae?p:1),b.height=A.height*(ae?p:1);const J=b.getContext("2d");ae&&J.scale(p,p),b.style.width=`${z.width}px`,b.style.height=`${z.height}px`},lt=b=>{const A=y.interface.getBoundingClientRect();let z=b.clientX,ae=b.clientY;return b.changedTouches&&b.changedTouches.length>0&&(z=b.changedTouches[0].clientX,ae=b.changedTouches[0].clientY),{x:(z-A.left)/A.width*c,y:(ae-A.top)/A.height*d}},ht=(b,A)=>{X.update({x:b,y:A});const z=!X.isEnabled();(se&&!oe||z&&se)&&(oe=!0,M.push(X.brush.toObject())),oe&&(M.push(X.brush.toObject()),ut({points:M,brush_color:o,brush_radius:h,mask:l==="mask"})),B=!0},ut=({points:b,brush_color:A,brush_radius:z,mask:ae})=>{if(!b||b.length<2)return;let p=ae?g.mask:g.temp;p.lineJoin="round",p.lineCap="round",p.strokeStyle=A,p.lineWidth=z;let J=b[0],gt=b[1];p.moveTo(gt.x,gt.y),p.beginPath();for(var _t=1,dr=b.length;_t<dr;_t++){var ei=_n(J,gt);p.quadraticCurveTo(J.x,J.y,ei.x,ei.y),J=b[_t],gt=b[_t+1]}p.lineTo(J.x,J.y),p.stroke()},ct=()=>{M.length<1||(M.length=0,Me())},ft=()=>{M.length<1||(R.push({points:M.slice(),brush_color:o,brush_radius:h}),l!=="mask"&&(M.length=0),g.drawing.drawImage(y.temp,0,0,c,d),Me())},Me=()=>{const b=mt();n("change",b)};function Ae(){return t(28,R=[]),dt(),t(4,Q=0),!0}function dt(){re=!0,g.temp.clearRect(0,0,c,d),t(27,g.temp.fillStyle=l==="mask"?"transparent":"#FFFFFF",g),g.temp.fillRect(0,0,c,d),l==="mask"&&g.mask.clearRect(0,0,y.mask.width,y.mask.height)}let Fe=({once:b=!1}={})=>{if(B||re){const A=X.getPointerCoordinates(),z=X.getBrushCoordinates();Tt(g.interface,A,z),B=!1,re=!1}b||window.requestAnimationFrame(()=>{Fe()})},Tt=(b,A,z)=>{b.clearRect(0,0,c,d),b.beginPath(),b.fillStyle=o,b.arc(z.x,z.y,h/2,0,Math.PI*2,!0),b.fill(),b.beginPath(),b.fillStyle=gn,b.arc(z.x,z.y,i,0,Math.PI*2,!0),b.fill()};function mt(){return l==="mask"?y.mask.toDataURL("image/png"):y.drawing.toDataURL("image/jpg")}function Ct(b){qi.call(this,r,b)}function Rt(b,A){j[b?"unshift":"push"](()=>{y[A]=b,t(0,y)})}function At(b){j[b?"unshift":"push"](()=>{F=b,t(3,F)})}function St(){N=this.offsetWidth,C=this.offsetHeight,t(1,N),t(2,C)}return r.$$set=b=>{"value"in b&&t(13,a=b.value),"value_img"in b&&t(14,s=b.value_img),"mode"in b&&t(15,l=b.mode),"brush_color"in b&&t(16,o=b.brush_color),"brush_radius"in b&&t(10,h=b.brush_radius),"mask_opacity"in b&&t(17,u=b.mask_opacity),"source"in b&&t(18,f=b.source),"width"in b&&t(11,c=b.width),"height"in b&&t(12,d=b.height),"container_height"in b&&t(19,_=b.container_height),"shape"in b&&t(20,v=b.shape)},r.$$.update=()=>{r.$$.dirty[0]&1054720&&v&&(c||d)&&(t(11,c=v[0]),t(12,d=v[1])),r.$$.dirty[0]&33562624&&k&&!a&&Ae(),r.$$.dirty[0]&503601153&&k&&s!==S&&(t(26,S=s),Ae(),setTimeout(()=>{f==="webcam"?(g.temp.save(),g.temp.translate(c,0),g.temp.scale(-1,1),g.temp.drawImage(s,0,0),g.temp.restore()):he(),g.drawing.drawImage(y.temp,0,0,c,d),Oe({lines:R.slice()}),Me()},50)),r.$$.dirty[0]&536871936&&X&&(we(),X.setRadius(h*.05)),r.$$.dirty[0]&6144&&(c||d)&&ot(),r.$$.dirty[0]&1024&&(i=h*.075)},[y,N,C,F,Q,st,ie,He,Pe,Ge,h,c,d,a,s,l,o,u,f,_,v,E,Le,Ae,mt,k,S,g,R,X,Ct,Rt,At,St]}class Jt extends _e{constructor(e){super(),pe(this,e,pn,mn,le,{value:13,value_img:14,mode:15,brush_color:16,brush_radius:10,mask_opacity:17,source:18,width:11,height:12,container_height:19,shape:20,clear_mask:21,undo:22,clear:23,get_image_data:24},null,[-1,-1,-1])}get clear_mask(){return this.$$.ctx[21]}get undo(){return this.$$.ctx[22]}get clear(){return this.$$.ctx[23]}get get_image_data(){return this.$$.ctx[24]}}function Ai(r){let e,t;return e=new $e({props:{Icon:qr,label:"Clear"}}),e.$on("click",r[3]),{c(){H(e.$$.fragment)},m(i,n){P(e,i,n),t=!0},p:L,i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){D(e.$$.fragment,i),t=!1},d(i){W(e,i)}}}function bn(r){let e,t,i,n,a,s;t=new $e({props:{Icon:Nr,label:"Undo"}}),t.$on("click",r[2]);let l=r[0]&&Ai(r);return a=new $e({props:{Icon:Ir,label:"Remove Image"}}),a.$on("click",r[4]),{c(){e=K("div"),H(t.$$.fragment),i=$(),l&&l.c(),n=$(),H(a.$$.fragment),m(e,"class","svelte-s6ybro")},m(o,h){O(o,e,h),P(t,e,null),Z(e,i),l&&l.m(e,null),Z(e,n),P(a,e,null),s=!0},p(o,[h]){o[0]?l?(l.p(o,h),h&1&&w(l,1)):(l=Ai(o),l.c(),w(l,1),l.m(e,n)):l&&(be(),D(l,1,1,()=>{l=null}),ve())},i(o){s||(w(t.$$.fragment,o),w(l),w(a.$$.fragment,o),s=!0)},o(o){D(t.$$.fragment,o),D(l),D(a.$$.fragment,o),s=!1},d(o){o&&T(e),W(t),l&&l.d(),W(a)}}}function vn(r,e,t){const i=it();let{show_eraser:n=!1}=e;const a=()=>i("undo"),s=o=>{i("clear_mask"),o.stopPropagation()},l=o=>{i("remove_image"),o.stopPropagation()};return r.$$set=o=>{"show_eraser"in o&&t(0,n=o.show_eraser)},[n,i,a,s,l]}class xt extends _e{constructor(e){super(),pe(this,e,vn,bn,le,{show_eraser:0})}}function Si(r){let e,t,i,n,a;return{c(){e=K("input"),m(e,"aria-label","Brush radius"),m(e,"type","range"),m(e,"min",t=.5*(r[2]/r[6])),m(e,"max",i=75*(r[2]/r[6])),m(e,"class","svelte-p4aq0j")},m(s,l){O(s,e,l),wt(e,r[0]),n||(a=[ee(e,"change",r[10]),ee(e,"input",r[10])],n=!0)},p(s,l){l&68&&t!==(t=.5*(s[2]/s[6]))&&m(e,"min",t),l&68&&i!==(i=75*(s[2]/s[6]))&&m(e,"max",i),l&1&&wt(e,s[0])},d(s){s&&T(e),n=!1,Vi(a)}}}function Ii(r){let e,t,i,n;t=new $e({props:{Icon:jr,label:"Select brush color"}}),t.$on("click",r[11]);let a=r[5]&&Ni(r);return{c(){e=K("span"),H(t.$$.fragment),i=$(),a&&a.c(),m(e,"class","col svelte-p4aq0j")},m(s,l){O(s,e,l),P(t,e,null),Z(e,i),a&&a.m(e,null),n=!0},p(s,l){s[5]?a?a.p(s,l):(a=Ni(s),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},i(s){n||(w(t.$$.fragment,s),n=!0)},o(s){D(t.$$.fragment,s),n=!1},d(s){s&&T(e),W(t),a&&a.d()}}}function Ni(r){let e,t,i;return{c(){e=K("input"),m(e,"aria-label","Brush color"),m(e,"type","color"),m(e,"class","svelte-p4aq0j")},m(n,a){O(n,e,a),wt(e,r[1]),t||(i=ee(e,"input",r[12]),t=!0)},p(n,a){a&2&&wt(e,n[1])},d(n){n&&T(e),t=!1,i()}}}function wn(r){let e,t,i,n,a,s;i=new $e({props:{Icon:Hr,label:"Use brush"}}),i.$on("click",r[9]);let l=r[4]&&Si(r),o=r[3]!=="mask"&&Ii(r);return{c(){e=K("div"),t=K("span"),H(i.$$.fragment),n=$(),l&&l.c(),a=$(),o&&o.c(),m(t,"class","brush svelte-p4aq0j"),m(e,"class","wrap svelte-p4aq0j")},m(h,u){O(h,e,u),Z(e,t),P(i,t,null),Z(t,n),l&&l.m(t,null),Z(e,a),o&&o.m(e,null),s=!0},p(h,[u]){h[4]?l?l.p(h,u):(l=Si(h),l.c(),l.m(t,null)):l&&(l.d(1),l=null),h[3]!=="mask"?o?(o.p(h,u),u&8&&w(o,1)):(o=Ii(h),o.c(),w(o,1),o.m(e,null)):o&&(be(),D(o,1,1,()=>{o=null}),ve())},i(h){s||(w(i.$$.fragment,h),w(o),s=!0)},o(h){D(i.$$.fragment,h),D(o),s=!1},d(h){h&&T(e),W(i),l&&l.d(),o&&o.d()}}}function yn(r,e,t){let i,n=!1,a=!1,{brush_radius:s=20}=e,{brush_color:l="#000"}=e,{container_height:o}=e,{img_width:h}=e,{img_height:u}=e,{mode:f="other"}=e;const c=()=>t(4,n=!n);function d(){s=wr(this.value),t(0,s)}const _=()=>t(5,a=!a);function v(){l=this.value,t(1,l)}return r.$$set=k=>{"brush_radius"in k&&t(0,s=k.brush_radius),"brush_color"in k&&t(1,l=k.brush_color),"container_height"in k&&t(7,o=k.container_height),"img_width"in k&&t(2,h=k.img_width),"img_height"in k&&t(8,u=k.img_height),"mode"in k&&t(3,f=k.mode)},r.$$.update=()=>{r.$$.dirty&388&&t(6,i=o*(h/u))},[s,l,h,f,n,a,i,o,u,c,d,_,v]}class $t extends _e{constructor(e){super(),pe(this,e,yn,wn,le,{brush_radius:0,brush_color:1,container_height:7,img_width:2,img_height:8,mode:3})}}function kn(r){let e,t,i,n;return{c(){e=K("img"),de(e.src,t=r[0].image||r[0])||m(e,"src",t),m(e,"alt",""),m(e,"loading","lazy"),m(e,"class","svelte-p3y7hu"),U(e,"webcam",r[6]==="webcam"&&r[10]),U(e,"selectable",r[12])},m(a,s){O(a,e,s),i||(n=ee(e,"click",r[30]),i=!0)},p(a,s){s[0]&1&&!de(e.src,t=a[0].image||a[0])&&m(e,"src",t),s[0]&1088&&U(e,"webcam",a[6]==="webcam"&&a[10]),s[0]&4096&&U(e,"selectable",a[12])},i:L,o:L,d(a){a&&T(e),i=!1,n()}}}function En(r){let e=r[23],t,i,n,a=zi(r),s=r[17]>0&&Bi(r);return{c(){a.c(),t=$(),s&&s.c(),i=Be()},m(l,o){a.m(l,o),O(l,t,o),s&&s.m(l,o),O(l,i,o),n=!0},p(l,o){o[0]&8388608&&le(e,e=l[23])?(a.d(1),a=zi(l),a.c(),a.m(t.parentNode,t)):a.p(l,o),l[17]>0?s?(s.p(l,o),o[0]&131072&&w(s,1)):(s=Bi(l),s.c(),w(s,1),s.m(i.parentNode,i)):s&&(be(),D(s,1,1,()=>{s=null}),ve())},i(l){n||(w(s),n=!0)},o(l){D(s),n=!1},d(l){l&&(T(t),T(i)),a.d(l),s&&s.d(l)}}}function Dn(r){let e,t,i,n,a,s,l;return e=new Dt({props:{editable:!0}}),e.$on("edit",r[53]),e.$on("clear",r[25]),{c(){H(e.$$.fragment),t=$(),i=K("img"),de(i.src,n=r[0])||m(i,"src",n),m(i,"alt",""),m(i,"loading","lazy"),m(i,"class","svelte-p3y7hu"),U(i,"selectable",r[12]),U(i,"webcam",r[6]==="webcam"&&r[10])},m(o,h){P(e,o,h),O(o,t,h),O(o,i,h),a=!0,s||(l=ee(i,"click",r[30]),s=!0)},p(o,h){(!a||h[0]&1&&!de(i.src,n=o[0]))&&m(i,"src",n),(!a||h[0]&4096)&&U(i,"selectable",o[12]),(!a||h[0]&1088)&&U(i,"webcam",o[6]==="webcam"&&o[10])},i(o){a||(w(e.$$.fragment,o),a=!0)},o(o){D(e.$$.fragment,o),a=!1},d(o){o&&(T(t),T(i)),W(e,o),s=!1,l()}}}function Mn(r){let e,t,i,n,a={image:r[0]};return e=new or({props:a}),r[51](e),e.$on("crop",r[26]),i=new Dt({}),i.$on("clear",r[52]),{c(){H(e.$$.fragment),t=$(),H(i.$$.fragment)},m(s,l){P(e,s,l),O(s,t,l),P(i,s,l),n=!0},p(s,l){const o={};l[0]&1&&(o.image=s[0]),e.$set(o)},i(s){n||(w(e.$$.fragment,s),w(i.$$.fragment,s),n=!0)},o(s){D(e.$$.fragment,s),D(i.$$.fragment,s),n=!1},d(s){s&&T(t),r[51](null),W(e,s),W(i,s)}}}function On(r){let e,t,i=r[6]==="webcam"&&!r[23]&&Hi(r);return{c(){i&&i.c(),e=Be()},m(n,a){i&&i.m(n,a),O(n,e,a),t=!0},p(n,a){n[6]==="webcam"&&!n[23]?i?(i.p(n,a),a[0]&8388672&&w(i,1)):(i=Hi(n),i.c(),w(i,1),i.m(e.parentNode,e)):i&&(be(),D(i,1,1,()=>{i=null}),ve())},i(n){t||(w(i),t=!0)},o(n){D(i),t=!1},d(n){n&&T(e),i&&i.d(n)}}}function Tn(r){let e,t,i,n,a,s,l;e=new xt({}),e.$on("undo",r[43]),e.$on("remove_image",r[28]);let o=r[1]==="color-sketch"&&Pi(r);function h(c){r[46](c)}function u(c){r[47](c)}let f={value:r[0],mode:r[19],width:r[17]||r[22],height:r[16]||r[21],container_height:r[18]||r[21],shape:r[7]};return r[2]!==void 0&&(f.brush_radius=r[2]),r[3]!==void 0&&(f.brush_color=r[3]),n=new Jt({props:f}),j.push(()=>me(n,"brush_radius",h)),j.push(()=>me(n,"brush_color",u)),r[48](n),n.$on("change",r[26]),n.$on("clear",r[28]),{c(){H(e.$$.fragment),t=$(),o&&o.c(),i=$(),H(n.$$.fragment)},m(c,d){P(e,c,d),O(c,t,d),o&&o.m(c,d),O(c,i,d),P(n,c,d),l=!0},p(c,d){c[1]==="color-sketch"?o?(o.p(c,d),d[0]&2&&w(o,1)):(o=Pi(c),o.c(),w(o,1),o.m(i.parentNode,i)):o&&(be(),D(o,1,1,()=>{o=null}),ve());const _={};d[0]&1&&(_.value=c[0]),d[0]&524288&&(_.mode=c[19]),d[0]&4325376&&(_.width=c[17]||c[22]),d[0]&2162688&&(_.height=c[16]||c[21]),d[0]&2359296&&(_.container_height=c[18]||c[21]),d[0]&128&&(_.shape=c[7]),!a&&d[0]&4&&(a=!0,_.brush_radius=c[2],ge(()=>a=!1)),!s&&d[0]&8&&(s=!0,_.brush_color=c[3],ge(()=>s=!1)),n.$set(_)},i(c){l||(w(e.$$.fragment,c),w(o),w(n.$$.fragment,c),l=!0)},o(c){D(e.$$.fragment,c),D(o),D(n.$$.fragment,c),l=!1},d(c){c&&(T(t),T(i)),W(e,c),o&&o.d(c),r[48](null),W(n,c)}}}function Cn(r){let e,t,i;function n(s){r[42](s)}let a={filetype:"image/*",include_file_metadata:!1,disable_click:!!r[0],$$slots:{default:[zn]},$$scope:{ctx:r}};return r[14]!==void 0&&(a.dragging=r[14]),e=new zr({props:a}),j.push(()=>me(e,"dragging",n)),e.$on("load",r[24]),{c(){H(e.$$.fragment)},m(s,l){P(e,s,l),i=!0},p(s,l){const o={};l[0]&1&&(o.disable_click=!!s[0]),l[0]&16760271|l[2]&1&&(o.$$scope={dirty:l,ctx:s}),!t&&l[0]&16384&&(t=!0,o.dragging=s[14],ge(()=>t=!1)),e.$set(o)},i(s){i||(w(e.$$.fragment,s),i=!0)},o(s){D(e.$$.fragment,s),i=!1},d(s){W(e,s)}}}function zi(r){let e,t,i,n;return{c(){e=K("img"),m(e,"class","absolute-img svelte-p3y7hu"),de(e.src,t=r[23]||r[0]?.image||r[0])||m(e,"src",t),m(e,"alt",""),m(e,"loading","lazy"),U(e,"webcam",r[6]==="webcam"&&r[10])},m(a,s){O(a,e,s),r[54](e),i||(n=ee(e,"load",r[27]),i=!0)},p(a,s){s[0]&8388609&&!de(e.src,t=a[23]||a[0]?.image||a[0])&&m(e,"src",t),s[0]&1088&&U(e,"webcam",a[6]==="webcam"&&a[10])},d(a){a&&T(e),r[54](null),i=!1,n()}}}function Bi(r){let e,t,i,n,a,s,l,o;function h(d){r[56](d)}function u(d){r[57](d)}let f={value:r[0],mode:r[19],width:r[17]||r[22],height:r[16]||r[21],container_height:r[18]||r[21],value_img:r[20],source:r[6]};r[2]!==void 0&&(f.brush_radius=r[2]),r[3]!==void 0&&(f.brush_color=r[3]),e=new Jt({props:f}),r[55](e),j.push(()=>me(e,"brush_radius",h)),j.push(()=>me(e,"brush_color",u)),e.$on("change",r[26]),a=new xt({}),a.$on("undo",r[58]),a.$on("remove_image",r[28]);let c=(r[1]==="color-sketch"||r[1]==="sketch")&&Li(r);return{c(){H(e.$$.fragment),n=$(),H(a.$$.fragment),s=$(),c&&c.c(),l=Be()},m(d,_){P(e,d,_),O(d,n,_),P(a,d,_),O(d,s,_),c&&c.m(d,_),O(d,l,_),o=!0},p(d,_){const v={};_[0]&1&&(v.value=d[0]),_[0]&524288&&(v.mode=d[19]),_[0]&4325376&&(v.width=d[17]||d[22]),_[0]&2162688&&(v.height=d[16]||d[21]),_[0]&2359296&&(v.container_height=d[18]||d[21]),_[0]&1048576&&(v.value_img=d[20]),_[0]&64&&(v.source=d[6]),!t&&_[0]&4&&(t=!0,v.brush_radius=d[2],ge(()=>t=!1)),!i&&_[0]&8&&(i=!0,v.brush_color=d[3],ge(()=>i=!1)),e.$set(v),d[1]==="color-sketch"||d[1]==="sketch"?c?(c.p(d,_),_[0]&2&&w(c,1)):(c=Li(d),c.c(),w(c,1),c.m(l.parentNode,l)):c&&(be(),D(c,1,1,()=>{c=null}),ve())},i(d){o||(w(e.$$.fragment,d),w(a.$$.fragment,d),w(c),o=!0)},o(d){D(e.$$.fragment,d),D(a.$$.fragment,d),D(c),o=!1},d(d){d&&(T(n),T(s),T(l)),r[55](null),W(e,d),W(a,d),c&&c.d(d)}}}function Li(r){let e,t,i,n;function a(o){r[59](o)}function s(o){r[60](o)}let l={container_height:r[18]||r[21],img_width:r[17]||r[22],img_height:r[16]||r[21],mode:r[19]};return r[2]!==void 0&&(l.brush_radius=r[2]),r[3]!==void 0&&(l.brush_color=r[3]),e=new $t({props:l}),j.push(()=>me(e,"brush_radius",a)),j.push(()=>me(e,"brush_color",s)),{c(){H(e.$$.fragment)},m(o,h){P(e,o,h),n=!0},p(o,h){const u={};h[0]&2359296&&(u.container_height=o[18]||o[21]),h[0]&4325376&&(u.img_width=o[17]||o[22]),h[0]&2162688&&(u.img_height=o[16]||o[21]),h[0]&524288&&(u.mode=o[19]),!t&&h[0]&4&&(t=!0,u.brush_radius=o[2],ge(()=>t=!1)),!i&&h[0]&8&&(i=!0,u.brush_color=o[3],ge(()=>i=!1)),e.$set(u)},i(o){n||(w(e.$$.fragment,o),n=!0)},o(o){D(e.$$.fragment,o),n=!1},d(o){W(e,o)}}}function Hi(r){let e,t;return e=new ia({props:{streaming:r[8],pending:r[9],mirror_webcam:r[10]}}),e.$on("capture",r[49]),e.$on("stream",r[26]),e.$on("error",r[50]),{c(){H(e.$$.fragment)},m(i,n){P(e,i,n),t=!0},p(i,n){const a={};n[0]&256&&(a.streaming=i[8]),n[0]&512&&(a.pending=i[9]),n[0]&1024&&(a.mirror_webcam=i[10]),e.$set(a)},i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){D(e.$$.fragment,i),t=!1},d(i){W(e,i)}}}function Pi(r){let e,t,i,n;function a(o){r[44](o)}function s(o){r[45](o)}let l={container_height:r[18]||r[21],img_width:r[17]||r[22],img_height:r[16]||r[21]};return r[2]!==void 0&&(l.brush_radius=r[2]),r[3]!==void 0&&(l.brush_color=r[3]),e=new $t({props:l}),j.push(()=>me(e,"brush_radius",a)),j.push(()=>me(e,"brush_color",s)),{c(){H(e.$$.fragment)},m(o,h){P(e,o,h),n=!0},p(o,h){const u={};h[0]&2359296&&(u.container_height=o[18]||o[21]),h[0]&4325376&&(u.img_width=o[17]||o[22]),h[0]&2162688&&(u.img_height=o[16]||o[21]),!t&&h[0]&4&&(t=!0,u.brush_radius=o[2],ge(()=>t=!1)),!i&&h[0]&8&&(i=!0,u.brush_color=o[3],ge(()=>i=!1)),e.$set(u)},i(o){n||(w(e.$$.fragment,o),n=!0)},o(o){D(e.$$.fragment,o),n=!1},d(o){W(e,o)}}}function Rn(r){let e,t,i,n;return{c(){e=K("img"),de(e.src,t=r[0].image||r[0])||m(e,"src",t),m(e,"alt",""),m(e,"loading","lazy"),m(e,"class","svelte-p3y7hu"),U(e,"webcam",r[6]==="webcam"&&r[10]),U(e,"selectable",r[12])},m(a,s){O(a,e,s),i||(n=ee(e,"click",r[30]),i=!0)},p(a,s){s[0]&1&&!de(e.src,t=a[0].image||a[0])&&m(e,"src",t),s[0]&1088&&U(e,"webcam",a[6]==="webcam"&&a[10]),s[0]&4096&&U(e,"selectable",a[12])},i:L,o:L,d(a){a&&T(e),i=!1,n()}}}function An(r){let e=r[23],t,i,n,a=Wi(r),s=r[17]>0&&Xi(r);return{c(){a.c(),t=$(),s&&s.c(),i=Be()},m(l,o){a.m(l,o),O(l,t,o),s&&s.m(l,o),O(l,i,o),n=!0},p(l,o){o[0]&8388608&&le(e,e=l[23])?(a.d(1),a=Wi(l),a.c(),a.m(t.parentNode,t)):a.p(l,o),l[17]>0?s?(s.p(l,o),o[0]&131072&&w(s,1)):(s=Xi(l),s.c(),w(s,1),s.m(i.parentNode,i)):s&&(be(),D(s,1,1,()=>{s=null}),ve())},i(l){n||(w(s),n=!0)},o(l){D(s),n=!1},d(l){l&&(T(t),T(i)),a.d(l),s&&s.d(l)}}}function Sn(r){let e,t,i,n,a,s,l;return e=new Dt({props:{editable:!0}}),e.$on("edit",r[34]),e.$on("clear",r[25]),{c(){H(e.$$.fragment),t=$(),i=K("img"),de(i.src,n=r[0])||m(i,"src",n),m(i,"alt",""),m(i,"loading","lazy"),m(i,"class","svelte-p3y7hu"),U(i,"scale-x-[-1]",r[6]==="webcam"&&r[10]),U(i,"selectable",r[12])},m(o,h){P(e,o,h),O(o,t,h),O(o,i,h),a=!0,s||(l=ee(i,"click",r[30]),s=!0)},p(o,h){(!a||h[0]&1&&!de(i.src,n=o[0]))&&m(i,"src",n),(!a||h[0]&1088)&&U(i,"scale-x-[-1]",o[6]==="webcam"&&o[10]),(!a||h[0]&4096)&&U(i,"selectable",o[12])},i(o){a||(w(e.$$.fragment,o),a=!0)},o(o){D(e.$$.fragment,o),a=!1},d(o){o&&(T(t),T(i)),W(e,o),s=!1,l()}}}function In(r){let e,t,i,n,a={image:r[0]};return e=new or({props:a}),r[32](e),e.$on("crop",r[26]),i=new Dt({}),i.$on("clear",r[33]),{c(){H(e.$$.fragment),t=$(),H(i.$$.fragment)},m(s,l){P(e,s,l),O(s,t,l),P(i,s,l),n=!0},p(s,l){const o={};l[0]&1&&(o.image=s[0]),e.$set(o)},i(s){n||(w(e.$$.fragment,s),w(i.$$.fragment,s),n=!0)},o(s){D(e.$$.fragment,s),D(i.$$.fragment,s),n=!1},d(s){s&&T(t),r[32](null),W(e,s),W(i,s)}}}function Nn(r){let e;const t=r[31].default,i=yr(t,r,r[62],null);return{c(){i&&i.c()},m(n,a){i&&i.m(n,a),e=!0},p(n,a){i&&i.p&&(!e||a[2]&1)&&kr(i,t,n,n[62],e?Dr(t,n[62],a,null):Er(n[62]),null)},i(n){e||(w(i,n),e=!0)},o(n){D(i,n),e=!1},d(n){i&&i.d(n)}}}function Wi(r){let e,t,i,n;return{c(){e=K("img"),m(e,"class","absolute-img svelte-p3y7hu"),de(e.src,t=r[23]||r[0]?.image||r[0])||m(e,"src",t),m(e,"alt",""),m(e,"loading","lazy"),U(e,"webcam",r[6]==="webcam"&&r[10])},m(a,s){O(a,e,s),r[35](e),i||(n=ee(e,"load",r[27]),i=!0)},p(a,s){s[0]&8388609&&!de(e.src,t=a[23]||a[0]?.image||a[0])&&m(e,"src",t),s[0]&1088&&U(e,"webcam",a[6]==="webcam"&&a[10])},d(a){a&&T(e),r[35](null),i=!1,n()}}}function Xi(r){let e,t,i,n,a,s,l,o;function h(d){r[37](d)}function u(d){r[38](d)}let f={value:r[0],mask_opacity:r[11],mode:r[19],width:r[17]||r[22],height:r[16]||r[21],container_height:r[18]||r[21],value_img:r[20],source:r[6],shape:r[7]};r[2]!==void 0&&(f.brush_radius=r[2]),r[3]!==void 0&&(f.brush_color=r[3]),e=new Jt({props:f}),r[36](e),j.push(()=>me(e,"brush_radius",h)),j.push(()=>me(e,"brush_color",u)),e.$on("change",r[26]),a=new xt({props:{show_eraser:r[20]}}),a.$on("undo",r[39]),a.$on("clear_mask",r[29]),a.$on("remove_image",r[28]);let c=(r[1]==="color-sketch"||r[1]==="sketch")&&Yi(r);return{c(){H(e.$$.fragment),n=$(),H(a.$$.fragment),s=$(),c&&c.c(),l=Be()},m(d,_){P(e,d,_),O(d,n,_),P(a,d,_),O(d,s,_),c&&c.m(d,_),O(d,l,_),o=!0},p(d,_){const v={};_[0]&1&&(v.value=d[0]),_[0]&2048&&(v.mask_opacity=d[11]),_[0]&524288&&(v.mode=d[19]),_[0]&4325376&&(v.width=d[17]||d[22]),_[0]&2162688&&(v.height=d[16]||d[21]),_[0]&2359296&&(v.container_height=d[18]||d[21]),_[0]&1048576&&(v.value_img=d[20]),_[0]&64&&(v.source=d[6]),_[0]&128&&(v.shape=d[7]),!t&&_[0]&4&&(t=!0,v.brush_radius=d[2],ge(()=>t=!1)),!i&&_[0]&8&&(i=!0,v.brush_color=d[3],ge(()=>i=!1)),e.$set(v);const k={};_[0]&1048576&&(k.show_eraser=d[20]),a.$set(k),d[1]==="color-sketch"||d[1]==="sketch"?c?(c.p(d,_),_[0]&2&&w(c,1)):(c=Yi(d),c.c(),w(c,1),c.m(l.parentNode,l)):c&&(be(),D(c,1,1,()=>{c=null}),ve())},i(d){o||(w(e.$$.fragment,d),w(a.$$.fragment,d),w(c),o=!0)},o(d){D(e.$$.fragment,d),D(a.$$.fragment,d),D(c),o=!1},d(d){d&&(T(n),T(s),T(l)),r[36](null),W(e,d),W(a,d),c&&c.d(d)}}}function Yi(r){let e,t,i,n;function a(o){r[40](o)}function s(o){r[41](o)}let l={container_height:r[18]||r[21],img_width:r[17]||r[22],img_height:r[16]||r[21],mode:r[19]};return r[2]!==void 0&&(l.brush_radius=r[2]),r[3]!==void 0&&(l.brush_color=r[3]),e=new $t({props:l}),j.push(()=>me(e,"brush_radius",a)),j.push(()=>me(e,"brush_color",s)),{c(){H(e.$$.fragment)},m(o,h){P(e,o,h),n=!0},p(o,h){const u={};h[0]&2359296&&(u.container_height=o[18]||o[21]),h[0]&4325376&&(u.img_width=o[17]||o[22]),h[0]&2162688&&(u.img_height=o[16]||o[21]),h[0]&524288&&(u.mode=o[19]),!t&&h[0]&4&&(t=!0,u.brush_radius=o[2],ge(()=>t=!1)),!i&&h[0]&8&&(i=!0,u.brush_color=o[3],ge(()=>i=!1)),e.$set(u)},i(o){n||(w(e.$$.fragment,o),n=!0)},o(o){D(e.$$.fragment,o),n=!1},d(o){W(e,o)}}}function zn(r){let e,t,i,n;const a=[Nn,In,Sn,An,Rn],s=[];function l(o,h){return o[0]===null&&!o[23]||o[8]?0:o[1]==="select"?1:o[1]==="editor"?2:(o[1]==="sketch"||o[1]==="color-sketch")&&(o[0]!==null||o[23])?3:4}return e=l(r),t=s[e]=a[e](r),{c(){t.c(),i=Be()},m(o,h){s[e].m(o,h),O(o,i,h),n=!0},p(o,h){let u=e;e=l(o),e===u?s[e].p(o,h):(be(),D(s[u],1,1,()=>{s[u]=null}),ve(),t=s[e],t?t.p(o,h):(t=s[e]=a[e](o),t.c()),w(t,1),t.m(i.parentNode,i))},i(o){n||(w(t),n=!0)},o(o){D(t),n=!1},d(o){o&&T(i),s[e].d(o)}}}function Bn(r){let e,t,i,n,a,s,l;e=new Ar({props:{show_label:r[5],Icon:r[6]==="canvas"?si:ni,label:r[4]||(r[6]==="canvas"?"Sketch":"Image")}});const o=[Cn,Tn,On,Mn,Dn,En,kn],h=[];function u(f,c){return f[6]==="upload"?0:f[6]==="canvas"?1:f[0]===null&&!f[23]||f[8]?2:f[1]==="select"?3:f[1]==="editor"?4:(f[1]==="sketch"||f[1]==="color-sketch")&&(f[0]!==null||f[23])?5:6}return n=u(r),a=h[n]=o[n](r),{c(){H(e.$$.fragment),t=$(),i=K("div"),a.c(),m(i,"data-testid","image"),m(i,"class","image-container svelte-p3y7hu"),qt(()=>r[61].call(i))},m(f,c){P(e,f,c),O(f,t,c),O(f,i,c),h[n].m(i,null),s=ji(i,r[61].bind(i)),l=!0},p(f,c){const d={};c[0]&32&&(d.show_label=f[5]),c[0]&64&&(d.Icon=f[6]==="canvas"?si:ni),c[0]&80&&(d.label=f[4]||(f[6]==="canvas"?"Sketch":"Image")),e.$set(d);let _=n;n=u(f),n===_?h[n].p(f,c):(be(),D(h[_],1,1,()=>{h[_]=null}),ve(),a=h[n],a?a.p(f,c):(a=h[n]=o[n](f),a.c()),w(a,1),a.m(i,null))},i(f){l||(w(e.$$.fragment,f),w(a),l=!0)},o(f){D(e.$$.fragment,f),D(a),l=!1},d(f){f&&(T(t),T(i)),W(e,f),h[n].d(),s()}}}function Ln(r,e,t){let{$$slots:i={},$$scope:n}=e,{value:a}=e,{label:s=void 0}=e,{show_label:l}=e,{source:o="upload"}=e,{tool:h="editor"}=e,{shape:u}=e,{streaming:f=!1}=e,{pending:c=!1}=e,{mirror_webcam:d}=e,{brush_radius:_}=e,{brush_color:v="#000000"}=e,{mask_opacity:k}=e,{selectable:N=!1}=e,C,S;a&&(o==="upload"||o==="webcam")&&h==="sketch"&&(a={image:a,mask:null});function ie({detail:p}){h==="color-sketch"?t(23,ue=p):t(0,a=(o==="upload"||o==="webcam")&&h==="sketch"?{image:p,mask:null}:p),M("upload",p)}function y({detail:p}){t(0,a=null),t(23,ue=void 0),M("clear")}async function g({detail:p},J){Y==="mask"?o==="webcam"&&J?t(0,a={image:p,mask:null}):t(0,a={image:typeof a=="string"?a:a?.image||null,mask:p}):(o==="upload"||o==="webcam")&&h==="sketch"?t(0,a={image:p,mask:null}):t(0,a=p),await Ne(),M(f?"stream":"edit")}const M=it();let R=!1;function B(p){const J=p.currentTarget;t(17,X=J.naturalWidth),t(16,se=J.naturalHeight),t(18,F=J.getBoundingClientRect().height)}async function re(){M("clear"),C.clear(),await Ne(),t(0,a=null),t(23,ue=void 0)}async function oe(){C.clear_mask(),await Ne()}let se=0,X=0,F=0,Y,Q,he,we,ue;Vt(async()=>{h==="color-sketch"&&a&&typeof a=="string"&&(t(23,ue=a),await Ne(),B({currentTarget:Q}))});function E(p){let J=Sr(p);J&&M("select",{index:J,value:null})}function Le(p){j[p?"unshift":"push"](()=>{S=p,t(13,S),t(0,a)})}const Oe=p=>(y(p),t(1,h="editor")),He=()=>t(1,h="select");function Pe(p){j[p?"unshift":"push"](()=>{Q=p,t(20,Q)})}function Ge(p){j[p?"unshift":"push"](()=>{C=p,t(15,C)})}function rt(p){_=p,t(2,_)}function at(p){v=p,t(3,v)}const nt=()=>C.undo();function st(p){_=p,t(2,_)}function ot(p){v=p,t(3,v)}function We(p){R=p,t(14,R)}const lt=()=>C.undo();function ht(p){_=p,t(2,_)}function ut(p){v=p,t(3,v)}function ct(p){_=p,t(2,_)}function ft(p){v=p,t(3,v)}function Me(p){j[p?"unshift":"push"](()=>{C=p,t(15,C)})}const Ae=p=>h==="color-sketch"?ie(p):g(p,!0);function dt(p){qi.call(this,r,p)}function Fe(p){j[p?"unshift":"push"](()=>{S=p,t(13,S),t(0,a)})}const Tt=p=>(y(p),t(1,h="editor")),mt=()=>t(1,h="select");function Ct(p){j[p?"unshift":"push"](()=>{Q=p,t(20,Q)})}function Rt(p){j[p?"unshift":"push"](()=>{C=p,t(15,C)})}function At(p){_=p,t(2,_)}function St(p){v=p,t(3,v)}const b=()=>C.undo();function A(p){_=p,t(2,_)}function z(p){v=p,t(3,v)}function ae(){he=this.offsetHeight,we=this.offsetWidth,t(21,he),t(22,we)}return r.$$set=p=>{"value"in p&&t(0,a=p.value),"label"in p&&t(4,s=p.label),"show_label"in p&&t(5,l=p.show_label),"source"in p&&t(6,o=p.source),"tool"in p&&t(1,h=p.tool),"shape"in p&&t(7,u=p.shape),"streaming"in p&&t(8,f=p.streaming),"pending"in p&&t(9,c=p.pending),"mirror_webcam"in p&&t(10,d=p.mirror_webcam),"brush_radius"in p&&t(2,_=p.brush_radius),"brush_color"in p&&t(3,v=p.brush_color),"mask_opacity"in p&&t(11,k=p.mask_opacity),"selectable"in p&&t(12,N=p.selectable),"$$scope"in p&&t(62,n=p.$$scope)},r.$$.update=()=>{r.$$.dirty[0]&16384&&M("drag",R),r.$$.dirty[0]&66&&(o==="canvas"&&h==="sketch"?t(19,Y="bw-sketch"):h==="color-sketch"?t(19,Y="color-sketch"):(o==="upload"||o==="webcam")&&h==="sketch"?t(19,Y="mask"):t(19,Y="editor")),r.$$.dirty[0]&1&&(a===null||a.image===null&&a.mask===null)&&t(23,ue=void 0),r.$$.dirty[0]&8193&&S&&(a?(t(13,S.image=a,S),S.create()):S.destroy())},[a,h,_,v,s,l,o,u,f,c,d,k,N,S,R,C,se,X,F,Y,Q,he,we,ue,ie,y,g,B,re,oe,E,i,Le,Oe,He,Pe,Ge,rt,at,nt,st,ot,We,lt,ht,ut,ct,ft,Me,Ae,dt,Fe,Tt,mt,Ct,Rt,At,St,b,A,z,ae,n]}class Hn extends _e{constructor(e){super(),pe(this,e,Ln,Bn,le,{value:0,label:4,show_label:5,source:6,tool:1,shape:7,streaming:8,pending:9,mirror_webcam:10,brush_radius:2,brush_color:3,mask_opacity:11,selectable:12},null,[-1,-1,-1])}}function Pn(r){let e,t;return e=new Br({props:{type:"image"}}),{c(){H(e.$$.fragment)},m(i,n){P(e,i,n),t=!0},p:L,i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){D(e.$$.fragment,i),t=!1},d(i){W(e,i)}}}function Wn(r){let e,t,i,n,a;const s=[r[1]];let l={};for(let u=0;u<s.length;u+=1)l=Mr(l,s[u]);e=new Or({props:l});function o(u){r[24](u)}let h={brush_radius:r[15],brush_color:r[16],shape:r[14],source:r[5],tool:r[6],selectable:r[18],mask_opacity:r[17],label:r[7],show_label:r[8],pending:r[10],streaming:r[9],mirror_webcam:r[13],$$slots:{default:[Pn]},$$scope:{ctx:r}};return r[0]!==void 0&&(h.value=r[0]),i=new Hn({props:h}),j.push(()=>me(i,"value",o)),i.$on("edit",r[25]),i.$on("clear",r[26]),i.$on("stream",r[27]),i.$on("drag",r[28]),i.$on("upload",r[29]),i.$on("select",r[30]),i.$on("share",r[31]),i.$on("error",r[32]),{c(){H(e.$$.fragment),t=$(),H(i.$$.fragment)},m(u,f){P(e,u,f),O(u,t,f),P(i,u,f),a=!0},p(u,f){const c=f[0]&2?Tr(s,[Cr(u[1])]):{};e.$set(c);const d={};f[0]&32768&&(d.brush_radius=u[15]),f[0]&65536&&(d.brush_color=u[16]),f[0]&16384&&(d.shape=u[14]),f[0]&32&&(d.source=u[5]),f[0]&64&&(d.tool=u[6]),f[0]&262144&&(d.selectable=u[18]),f[0]&131072&&(d.mask_opacity=u[17]),f[0]&128&&(d.label=u[7]),f[0]&256&&(d.show_label=u[8]),f[0]&1024&&(d.pending=u[10]),f[0]&512&&(d.streaming=u[9]),f[0]&8192&&(d.mirror_webcam=u[13]),f[1]&4&&(d.$$scope={dirty:f,ctx:u}),!n&&f[0]&1&&(n=!0,d.value=u[0],ge(()=>n=!1)),i.$set(d)},i(u){a||(w(e.$$.fragment,u),w(i.$$.fragment,u),a=!0)},o(u){D(e.$$.fragment,u),D(i.$$.fragment,u),a=!1},d(u){u&&T(t),W(e,u),W(i,u)}}}function Xn(r){let e,t;return e=new Rr({props:{visible:r[4],variant:r[0]===null&&r[5]==="upload"?"dashed":"solid",border_mode:r[23]?"focus":"base",padding:!1,elem_id:r[2],elem_classes:r[3],height:r[11]||(r[5]==="webcam"?void 0:Ui),width:r[12],allow_overflow:!1,container:r[19],scale:r[20],min_width:r[21],$$slots:{default:[Wn]},$$scope:{ctx:r}}}),{c(){H(e.$$.fragment)},m(i,n){P(e,i,n),t=!0},p(i,n){const a={};n[0]&16&&(a.visible=i[4]),n[0]&33&&(a.variant=i[0]===null&&i[5]==="upload"?"dashed":"solid"),n[0]&8388608&&(a.border_mode=i[23]?"focus":"base"),n[0]&4&&(a.elem_id=i[2]),n[0]&8&&(a.elem_classes=i[3]),n[0]&2080&&(a.height=i[11]||(i[5]==="webcam"?void 0:Ui)),n[0]&4096&&(a.width=i[12]),n[0]&524288&&(a.container=i[19]),n[0]&1048576&&(a.scale=i[20]),n[0]&2097152&&(a.min_width=i[21]),n[0]&13101027|n[1]&4&&(a.$$scope={dirty:n,ctx:i}),e.$set(a)},i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){D(e.$$.fragment,i),t=!1},d(i){W(e,i)}}}const Ui=240;function Yn(r,e,t){let{elem_id:i=""}=e,{elem_classes:n=[]}=e,{visible:a=!0}=e,{value:s=null}=e,{source:l="upload"}=e,{tool:o="editor"}=e,{label:h}=e,{show_label:u}=e,{streaming:f}=e,{pending:c}=e,{height:d}=e,{width:_}=e,{mirror_webcam:v}=e,{shape:k}=e,{brush_radius:N}=e,{brush_color:C}=e,{mask_opacity:S}=e,{selectable:ie=!1}=e,{container:y=!0}=e,{scale:g=null}=e,{min_width:M=void 0}=e,{loading_status:R}=e,{gradio:B}=e,re;function oe(E){s=E,t(0,s)}const se=()=>B.dispatch("edit"),X=()=>B.dispatch("clear"),F=()=>B.dispatch("stream"),Y=({detail:E})=>t(23,re=E),Q=()=>B.dispatch("upload"),he=({detail:E})=>B.dispatch("select",E),we=({detail:E})=>B.dispatch("share",E),ue=({detail:E})=>{t(1,R=R||{}),t(1,R.status="error",R),B.dispatch("error",E)};return r.$$set=E=>{"elem_id"in E&&t(2,i=E.elem_id),"elem_classes"in E&&t(3,n=E.elem_classes),"visible"in E&&t(4,a=E.visible),"value"in E&&t(0,s=E.value),"source"in E&&t(5,l=E.source),"tool"in E&&t(6,o=E.tool),"label"in E&&t(7,h=E.label),"show_label"in E&&t(8,u=E.show_label),"streaming"in E&&t(9,f=E.streaming),"pending"in E&&t(10,c=E.pending),"height"in E&&t(11,d=E.height),"width"in E&&t(12,_=E.width),"mirror_webcam"in E&&t(13,v=E.mirror_webcam),"shape"in E&&t(14,k=E.shape),"brush_radius"in E&&t(15,N=E.brush_radius),"brush_color"in E&&t(16,C=E.brush_color),"mask_opacity"in E&&t(17,S=E.mask_opacity),"selectable"in E&&t(18,ie=E.selectable),"container"in E&&t(19,y=E.container),"scale"in E&&t(20,g=E.scale),"min_width"in E&&t(21,M=E.min_width),"loading_status"in E&&t(1,R=E.loading_status),"gradio"in E&&t(22,B=E.gradio)},r.$$.update=()=>{r.$$.dirty[0]&1&&t(0,s=s||null),r.$$.dirty[0]&4194305&&B.dispatch("change")},[s,R,i,n,a,l,o,h,u,f,c,d,_,v,k,N,C,S,ie,y,g,M,B,re,oe,se,X,F,Y,Q,he,we,ue]}class Un extends _e{constructor(e){super(),pe(this,e,Yn,Xn,le,{elem_id:2,elem_classes:3,visible:4,value:0,source:5,tool:6,label:7,show_label:8,streaming:9,pending:10,height:11,width:12,mirror_webcam:13,shape:14,brush_radius:15,brush_color:16,mask_opacity:17,selectable:18,container:19,scale:20,min_width:21,loading_status:1,gradio:22},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),V()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),V()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),V()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),V()}get source(){return this.$$.ctx[5]}set source(e){this.$$set({source:e}),V()}get tool(){return this.$$.ctx[6]}set tool(e){this.$$set({tool:e}),V()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),V()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),V()}get streaming(){return this.$$.ctx[9]}set streaming(e){this.$$set({streaming:e}),V()}get pending(){return this.$$.ctx[10]}set pending(e){this.$$set({pending:e}),V()}get height(){return this.$$.ctx[11]}set height(e){this.$$set({height:e}),V()}get width(){return this.$$.ctx[12]}set width(e){this.$$set({width:e}),V()}get mirror_webcam(){return this.$$.ctx[13]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),V()}get shape(){return this.$$.ctx[14]}set shape(e){this.$$set({shape:e}),V()}get brush_radius(){return this.$$.ctx[15]}set brush_radius(e){this.$$set({brush_radius:e}),V()}get brush_color(){return this.$$.ctx[16]}set brush_color(e){this.$$set({brush_color:e}),V()}get mask_opacity(){return this.$$.ctx[17]}set mask_opacity(e){this.$$set({mask_opacity:e}),V()}get selectable(){return this.$$.ctx[18]}set selectable(e){this.$$set({selectable:e}),V()}get container(){return this.$$.ctx[19]}set container(e){this.$$set({container:e}),V()}get scale(){return this.$$.ctx[20]}set scale(e){this.$$set({scale:e}),V()}get min_width(){return this.$$.ctx[21]}set min_width(e){this.$$set({min_width:e}),V()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),V()}get gradio(){return this.$$.ctx[22]}set gradio(e){this.$$set({gradio:e}),V()}}const es=Un;export{es as I,ia as W};
//# sourceMappingURL=InteractiveImage-c35fd512.js.map
