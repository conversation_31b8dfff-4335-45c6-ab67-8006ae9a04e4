import{S as L,e as M,s as O,ag as f,F as w,G as v,w as g,u as b,H as k,C as P,R as H,U as N,o as Q,h as Y,r as y,v as p,X as R,k as $,Z as ee,ae as te,V as se,W as le}from"./index-c99b2410.js";import{T as ie}from"./Textbox-8fe0a9da.js";import{B as ne}from"./Button-9c502b18.js";function U(l){let e,t;const u=[l[17]];let a={};for(let n=0;n<u.length;n+=1)a=ee(a,u[n]);return e=new te({props:a}),{c(){w(e.$$.fragment)},m(n,_){v(e,n,_),t=!0},p(n,_){const c=_[0]&131072?se(u,[le(n[17])]):{};e.$set(c)},i(n){t||(g(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ae(l){let e,t,u,a,n,_=l[17]&&U(l);function c(i){l[22](i)}function m(i){l[23](i)}let r={label:l[3],info:l[4],show_label:l[10],lines:l[8],type:l[12],rtl:l[18],text_align:l[19],max_lines:l[11]?l[11]:l[8]+1,placeholder:l[9],show_copy_button:l[16],autofocus:l[20],container:l[13],autoscroll:l[21]};return l[0]!==void 0&&(r.value=l[0]),l[1]!==void 0&&(r.value_is_output=l[1]),t=new ie({props:r}),H.push(()=>N(t,"value",c)),H.push(()=>N(t,"value_is_output",m)),t.$on("change",l[24]),t.$on("input",l[25]),t.$on("submit",l[26]),t.$on("blur",l[27]),t.$on("select",l[28]),t.$on("focus",l[29]),{c(){_&&_.c(),e=Q(),w(t.$$.fragment)},m(i,o){_&&_.m(i,o),Y(i,e,o),v(t,i,o),n=!0},p(i,o){i[17]?_?(_.p(i,o),o[0]&131072&&g(_,1)):(_=U(i),_.c(),g(_,1),_.m(e.parentNode,e)):_&&(y(),b(_,1,1,()=>{_=null}),p());const h={};o[0]&8&&(h.label=i[3]),o[0]&16&&(h.info=i[4]),o[0]&1024&&(h.show_label=i[10]),o[0]&256&&(h.lines=i[8]),o[0]&4096&&(h.type=i[12]),o[0]&262144&&(h.rtl=i[18]),o[0]&524288&&(h.text_align=i[19]),o[0]&2304&&(h.max_lines=i[11]?i[11]:i[8]+1),o[0]&512&&(h.placeholder=i[9]),o[0]&65536&&(h.show_copy_button=i[16]),o[0]&1048576&&(h.autofocus=i[20]),o[0]&8192&&(h.container=i[13]),o[0]&2097152&&(h.autoscroll=i[21]),!u&&o[0]&1&&(u=!0,h.value=i[0],R(()=>u=!1)),!a&&o[0]&2&&(a=!0,h.value_is_output=i[1],R(()=>a=!1)),t.$set(h)},i(i){n||(g(_),g(t.$$.fragment,i),n=!0)},o(i){b(_),b(t.$$.fragment,i),n=!1},d(i){i&&$(e),_&&_.d(i),k(t,i)}}}function ue(l){let e,t;return e=new ne({props:{visible:l[7],elem_id:l[5],elem_classes:l[6],scale:l[14],min_width:l[15],allow_overflow:!1,padding:l[13],$$slots:{default:[ae]},$$scope:{ctx:l}}}),{c(){w(e.$$.fragment)},m(u,a){v(e,u,a),t=!0},p(u,a){const n={};a[0]&128&&(n.visible=u[7]),a[0]&32&&(n.elem_id=u[5]),a[0]&64&&(n.elem_classes=u[6]),a[0]&16384&&(n.scale=u[14]),a[0]&32768&&(n.min_width=u[15]),a[0]&8192&&(n.padding=u[13]),a[0]&4144927|a[1]&1&&(n.$$scope={dirty:a,ctx:u}),e.$set(n)},i(u){t||(g(e.$$.fragment,u),t=!0)},o(u){b(e.$$.fragment,u),t=!1},d(u){k(e,u)}}}function _e(l,e,t){const u=P();let{gradio:a={dispatch:u}}=e,{label:n="Textbox"}=e,{info:_=void 0}=e,{elem_id:c=""}=e,{elem_classes:m=[]}=e,{visible:r=!0}=e,{value:i=""}=e,{lines:o}=e,{placeholder:h=""}=e,{show_label:T}=e,{max_lines:S}=e,{type:x="text"}=e,{container:B=!0}=e,{scale:C=null}=e,{min_width:I=void 0}=e,{show_copy_button:j=!1}=e,{loading_status:q=void 0}=e,{value_is_output:d=!1}=e,{rtl:D=!1}=e,{text_align:E=void 0}=e,{autofocus:F=!1}=e,{autoscroll:G=!0}=e;function V(s){i=s,t(0,i)}function W(s){d=s,t(1,d)}const X=()=>a.dispatch("change",i),Z=()=>a.dispatch("input"),z=()=>a.dispatch("submit"),A=()=>a.dispatch("blur"),J=s=>a.dispatch("select",s.detail),K=()=>a.dispatch("focus");return l.$$set=s=>{"gradio"in s&&t(2,a=s.gradio),"label"in s&&t(3,n=s.label),"info"in s&&t(4,_=s.info),"elem_id"in s&&t(5,c=s.elem_id),"elem_classes"in s&&t(6,m=s.elem_classes),"visible"in s&&t(7,r=s.visible),"value"in s&&t(0,i=s.value),"lines"in s&&t(8,o=s.lines),"placeholder"in s&&t(9,h=s.placeholder),"show_label"in s&&t(10,T=s.show_label),"max_lines"in s&&t(11,S=s.max_lines),"type"in s&&t(12,x=s.type),"container"in s&&t(13,B=s.container),"scale"in s&&t(14,C=s.scale),"min_width"in s&&t(15,I=s.min_width),"show_copy_button"in s&&t(16,j=s.show_copy_button),"loading_status"in s&&t(17,q=s.loading_status),"value_is_output"in s&&t(1,d=s.value_is_output),"rtl"in s&&t(18,D=s.rtl),"text_align"in s&&t(19,E=s.text_align),"autofocus"in s&&t(20,F=s.autofocus),"autoscroll"in s&&t(21,G=s.autoscroll)},[i,d,a,n,_,c,m,r,o,h,T,S,x,B,C,I,j,q,D,E,F,G,V,W,X,Z,z,A,J,K]}class ce extends L{constructor(e){super(),M(this,e,_e,ue,O,{gradio:2,label:3,info:4,elem_id:5,elem_classes:6,visible:7,value:0,lines:8,placeholder:9,show_label:10,max_lines:11,type:12,container:13,scale:14,min_width:15,show_copy_button:16,loading_status:17,value_is_output:1,rtl:18,text_align:19,autofocus:20,autoscroll:21},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),f()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),f()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),f()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get lines(){return this.$$.ctx[8]}set lines(e){this.$$set({lines:e}),f()}get placeholder(){return this.$$.ctx[9]}set placeholder(e){this.$$set({placeholder:e}),f()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),f()}get max_lines(){return this.$$.ctx[11]}set max_lines(e){this.$$set({max_lines:e}),f()}get type(){return this.$$.ctx[12]}set type(e){this.$$set({type:e}),f()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[14]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[15]}set min_width(e){this.$$set({min_width:e}),f()}get show_copy_button(){return this.$$.ctx[16]}set show_copy_button(e){this.$$set({show_copy_button:e}),f()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),f()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),f()}get rtl(){return this.$$.ctx[18]}set rtl(e){this.$$set({rtl:e}),f()}get text_align(){return this.$$.ctx[19]}set text_align(e){this.$$set({text_align:e}),f()}get autofocus(){return this.$$.ctx[20]}set autofocus(e){this.$$set({autofocus:e}),f()}get autoscroll(){return this.$$.ctx[21]}set autoscroll(e){this.$$set({autoscroll:e}),f()}}export{ce as I};
//# sourceMappingURL=InteractiveTextbox-5244a148.js.map
