import{S as k,e as w,s as v,f as b,g as a,K as u,h as $,j as g,n as h,k as C,m as D,o as z,F as I,N as B,G as M,w as d,r as U,u as p,v as E,H as L,a0 as F,a1 as G,C as H}from"./index-c99b2410.js";import"./Button-9c502b18.js";import{I as j}from"./IconButton-0f3d06d2.js";import{U as K}from"./Undo-61b53ec5.js";function N(r){let e,o,t,n;return{c(){e=b("svg"),o=b("g"),t=b("path"),n=b("path"),a(t,"d","M18,6L6.087,17.913"),u(t,"fill","none"),u(t,"fill-rule","nonzero"),u(t,"stroke-width","2px"),a(o,"transform","matrix(1.14096,-0.140958,-0.140958,1.14096,-0.0559523,0.0559523)"),a(n,"d","M4.364,4.364L19.636,19.636"),u(n,"fill","none"),u(n,"fill-rule","nonzero"),u(n,"stroke-width","2px"),a(e,"width","100%"),a(e,"height","100%"),a(e,"viewBox","0 0 24 24"),a(e,"version","1.1"),a(e,"xmlns","http://www.w3.org/2000/svg"),a(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),a(e,"xml:space","preserve"),a(e,"stroke","currentColor"),u(e,"fill-rule","evenodd"),u(e,"clip-rule","evenodd"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(c,l){$(c,e,l),g(e,o),g(o,t),g(e,n)},p:h,i:h,o:h,d(c){c&&C(e)}}}class P extends k{constructor(e){super(),w(this,e,null,N,v,{})}}function x(r){let e,o;return{c(){e=b("svg"),o=b("path"),a(o,"d","M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"),a(e,"xmlns","http://www.w3.org/2000/svg"),a(e,"width","100%"),a(e,"height","100%"),a(e,"viewBox","0 0 24 24"),a(e,"fill","none"),a(e,"stroke","currentColor"),a(e,"stroke-width","1.5"),a(e,"stroke-linecap","round"),a(e,"stroke-linejoin","round"),a(e,"class","feather feather-edit-2")},m(t,n){$(t,e,n),g(e,o)},p:h,i:h,o:h,d(t){t&&C(e)}}}class A extends k{constructor(e){super(),w(this,e,null,x,v,{})}}function S(r){let e,o;return e=new j({props:{Icon:A,label:r[3]("common.edit")}}),e.$on("click",r[5]),{c(){I(e.$$.fragment)},m(t,n){M(e,t,n),o=!0},p(t,n){const c={};n&8&&(c.label=t[3]("common.edit")),e.$set(c)},i(t){o||(d(e.$$.fragment,t),o=!0)},o(t){p(e.$$.fragment,t),o=!1},d(t){L(e,t)}}}function q(r){let e,o;return e=new j({props:{Icon:K,label:r[3]("common.undo")}}),e.$on("click",r[6]),{c(){I(e.$$.fragment)},m(t,n){M(e,t,n),o=!0},p(t,n){const c={};n&8&&(c.label=t[3]("common.undo")),e.$set(c)},i(t){o||(d(e.$$.fragment,t),o=!0)},o(t){p(e.$$.fragment,t),o=!1},d(t){L(e,t)}}}function J(r){let e,o,t,n,c,l=r[0]&&S(r),s=r[1]&&q(r);return n=new j({props:{Icon:P,label:r[3]("common.clear")}}),n.$on("click",r[7]),{c(){e=D("div"),l&&l.c(),o=z(),s&&s.c(),t=z(),I(n.$$.fragment),a(e,"class","svelte-19sk1im"),B(e,"not-absolute",!r[2]),u(e,"position",r[2]?"absolute":"static")},m(i,f){$(i,e,f),l&&l.m(e,null),g(e,o),s&&s.m(e,null),g(e,t),M(n,e,null),c=!0},p(i,[f]){i[0]?l?(l.p(i,f),f&1&&d(l,1)):(l=S(i),l.c(),d(l,1),l.m(e,o)):l&&(U(),p(l,1,1,()=>{l=null}),E()),i[1]?s?(s.p(i,f),f&2&&d(s,1)):(s=q(i),s.c(),d(s,1),s.m(e,t)):s&&(U(),p(s,1,1,()=>{s=null}),E());const _={};f&8&&(_.label=i[3]("common.clear")),n.$set(_),(!c||f&4)&&B(e,"not-absolute",!i[2]),f&4&&u(e,"position",i[2]?"absolute":"static")},i(i){c||(d(l),d(s),d(n.$$.fragment,i),c=!0)},o(i){p(l),p(s),p(n.$$.fragment,i),c=!1},d(i){i&&C(e),l&&l.d(),s&&s.d(),L(n)}}}function O(r,e,o){let t;F(r,G,m=>o(3,t=m));let{editable:n=!1}=e,{undoable:c=!1}=e,{absolute:l=!0}=e;const s=H(),i=()=>s("edit"),f=()=>s("undo"),_=m=>{s("clear"),m.stopPropagation()};return r.$$set=m=>{"editable"in m&&o(0,n=m.editable),"undoable"in m&&o(1,c=m.undoable),"absolute"in m&&o(2,l=m.absolute)},[n,c,l,t,s,i,f,_]}class W extends k{constructor(e){super(),w(this,e,O,J,v,{editable:0,undoable:1,absolute:2})}}export{P as C,W as M};
//# sourceMappingURL=ModifyUpload-643080ff.js.map
