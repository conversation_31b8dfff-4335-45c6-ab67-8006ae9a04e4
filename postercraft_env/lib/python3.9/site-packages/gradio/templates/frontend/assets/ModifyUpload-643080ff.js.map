{"version": 3, "file": "ModifyUpload-643080ff.js", "sources": ["../../../../js/icons/src/Clear.svelte", "../../../../js/icons/src/Edit.svelte", "../../../../js/upload/src/ModifyUpload.svelte"], "sourcesContent": ["<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tversion=\"1.1\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\txml:space=\"preserve\"\n\tstroke=\"currentColor\"\n\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;\"\n>\n\t<g\n\t\ttransform=\"matrix(1.14096,-0.140958,-0.140958,1.14096,-0.0559523,0.0559523)\"\n\t>\n\t\t<path\n\t\t\td=\"M18,6L6.087,17.913\"\n\t\t\tstyle=\"fill:none;fill-rule:nonzero;stroke-width:2px;\"\n\t\t/>\n\t</g>\n\t<path\n\t\td=\"M4.364,4.364L19.636,19.636\"\n\t\tstyle=\"fill:none;fill-rule:nonzero;stroke-width:2px;\"\n\t/>\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-edit-2\"\n>\n\t<path d=\"M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z\" />\n</svg>\n", "<script lang=\"ts\">\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Edit, Clear, Undo } from \"@gradio/icons\";\n\n\timport { createEventDispatcher } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let editable = false;\n\texport let undoable = false;\n\texport let absolute = true;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tedit: never;\n\t\tclear: never;\n\t\tundo: never;\n\t}>();\n</script>\n\n<div\n\tclass:not-absolute={!absolute}\n\tstyle:position={absolute ? \"absolute\" : \"static\"}\n>\n\t{#if editable}\n\t\t<IconButton\n\t\t\tIcon={Edit}\n\t\t\tlabel={$_(\"common.edit\")}\n\t\t\ton:click={() => dispatch(\"edit\")}\n\t\t/>\n\t{/if}\n\n\t{#if undoable}\n\t\t<IconButton\n\t\t\tIcon={Undo}\n\t\t\tlabel={$_(\"common.undo\")}\n\t\t\ton:click={() => dispatch(\"undo\")}\n\t\t/>\n\t{/if}\n\n\t<IconButton\n\t\tIcon={Clear}\n\t\tlabel={$_(\"common.clear\")}\n\t\ton:click={(event) => {\n\t\t\tdispatch(\"clear\");\n\t\t\tevent.stopPropagation();\n\t\t}}\n\t/>\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.not-absolute {\n\t\tmargin: var(--size-1);\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "g", "path0", "path1", "path", "Edit", "ctx", "dirty", "iconbutton_changes", "Undo", "create_if_block_1", "create_if_block", "Clear", "div", "editable", "$$props", "undoable", "absolute", "dispatch", "createEventDispatcher", "click_handler", "click_handler_1", "event"], "mappings": "uhCAAAA,EAuBKC,EAAAC,EAAAC,CAAA,EAZJC,EAOGF,EAAAG,CAAA,EAJFD,EAGCC,EAAAC,CAAA,EAEFF,EAGCF,EAAAK,CAAA,4fCtBFP,EAaKC,EAAAC,EAAAC,CAAA,EADJC,EAAmEF,EAAAM,CAAA,gJCY3DC,EACC,MAAAC,KAAG,aAAa,wFAAhBC,EAAA,IAAAC,EAAA,MAAAF,KAAG,aAAa,+IAOjBG,EACC,MAAAH,KAAG,aAAa,wFAAhBC,EAAA,IAAAC,EAAA,MAAAF,KAAG,aAAa,2HAXpBA,EAAQ,CAAA,GAAAI,EAAAJ,CAAA,IAQRA,EAAQ,CAAA,GAAAK,EAAAL,CAAA,8BASNM,EACC,MAAAN,KAAG,cAAc,0IArBJA,EAAQ,CAAA,CAAA,iBACbA,EAAQ,CAAA,EAAG,WAAa,QAAQ,UAFjDV,EA4BKC,EAAAgB,EAAAd,CAAA,yEAxBCO,EAAQ,CAAA,6FAQRA,EAAQ,CAAA,wGAULC,EAAA,IAAAC,EAAA,MAAAF,KAAG,cAAc,4CArBJA,EAAQ,CAAA,CAAA,sBACbA,EAAQ,CAAA,EAAG,WAAa,QAAQ,yKAbrC,GAAA,CAAA,SAAAQ,EAAW,EAAK,EAAAC,EAChB,CAAA,SAAAC,EAAW,EAAK,EAAAD,EAChB,CAAA,SAAAE,EAAW,EAAI,EAAAF,EAEpB,MAAAG,EAAWC,IAeCC,EAAA,IAAAF,EAAS,MAAM,EAQfG,EAAA,IAAAH,EAAS,MAAM,IAOrBI,GAAK,CACfJ,EAAS,OAAO,EAChBI,EAAM,gBAAe"}