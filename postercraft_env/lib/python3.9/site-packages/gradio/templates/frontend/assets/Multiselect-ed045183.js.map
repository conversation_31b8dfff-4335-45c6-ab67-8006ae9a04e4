{"version": 3, "file": "Multiselect-ed045183.js", "sources": ["../../../../js/icons/src/DropdownArrow.svelte", "../../../../js/icons/src/Remove.svelte", "../../../../js/dropdown/shared/DropdownOptions.svelte", "../../../../js/dropdown/shared/utils.ts", "../../../../js/dropdown/shared/Dropdown.svelte", "../../../../js/dropdown/shared/Multiselect.svelte"], "sourcesContent": ["<svg\n\tclass=\"dropdown-arrow\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"18\"\n\theight=\"18\"\n\tviewBox=\"0 0 18 18\"\n>\n\t<path d=\"M5 8l4 4 4-4z\" />\n</svg>\n\n<style>\n\t.dropdown-arrow {\n\t\tfill: var(--body-text-color);\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-5);\n\t}\n</style>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"16\"\n\theight=\"16\"\n\tviewBox=\"0 0 24 24\"\n>\n\t<path\n\t\td=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { fly } from \"svelte/transition\";\n\timport { createEventDispatcher } from \"svelte\";\n\texport let choices: [string, string | number][];\n\texport let filtered_indices: number[];\n\texport let show_options = false;\n\texport let disabled = false;\n\texport let selected_indices: (string | number)[] = [];\n\texport let active_index: number | null = null;\n\n\tlet distance_from_top: number;\n\tlet distance_from_bottom: number;\n\tlet input_height: number;\n\tlet input_width: number;\n\tlet refElement: HTMLDivElement;\n\tlet listElement: HTMLUListElement;\n\tlet top: string | null, bottom: string | null, max_height: number;\n\tlet innerHeight: number;\n\n\tfunction calculate_window_distance(): void {\n\t\tconst { top: ref_top, bottom: ref_bottom } =\n\t\t\trefElement.getBoundingClientRect();\n\t\tdistance_from_top = ref_top;\n\t\tdistance_from_bottom = innerHeight - ref_bottom;\n\t}\n\n\tlet scroll_timeout: NodeJS.Timeout | null = null;\n\tfunction scroll_listener(): void {\n\t\tif (!show_options) return;\n\t\tif (scroll_timeout !== null) {\n\t\t\tclearTimeout(scroll_timeout);\n\t\t}\n\n\t\tscroll_timeout = setTimeout(() => {\n\t\t\tcalculate_window_distance();\n\t\t\tscroll_timeout = null;\n\t\t}, 10);\n\t}\n\n\t$: {\n\t\tif (show_options && refElement) {\n\t\t\tif (listElement && selected_indices.length > 0) {\n\t\t\t\tlet elements = listElement.querySelectorAll(\"li\");\n\t\t\t\tfor (const element of Array.from(elements)) {\n\t\t\t\t\tif (\n\t\t\t\t\t\telement.getAttribute(\"data-index\") ===\n\t\t\t\t\t\tselected_indices[0].toString()\n\t\t\t\t\t) {\n\t\t\t\t\t\tlistElement?.scrollTo?.(0, (element as HTMLLIElement).offsetTop);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcalculate_window_distance();\n\t\t\tconst rect = refElement.parentElement?.getBoundingClientRect();\n\t\t\tinput_height = rect?.height || 0;\n\t\t\tinput_width = rect?.width || 0;\n\t\t}\n\t\tif (distance_from_bottom > distance_from_top) {\n\t\t\ttop = `${distance_from_top}px`;\n\t\t\tmax_height = distance_from_bottom;\n\t\t\tbottom = null;\n\t\t} else {\n\t\t\tbottom = `${distance_from_bottom + input_height}px`;\n\t\t\tmax_height = distance_from_top - input_height;\n\t\t\ttop = null;\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<svelte:window on:scroll={scroll_listener} bind:innerHeight />\n\n<div class=\"reference\" bind:this={refElement} />\n{#if show_options && !disabled}\n\t<ul\n\t\tclass=\"options\"\n\t\ttransition:fly={{ duration: 200, y: 5 }}\n\t\ton:mousedown|preventDefault={(e) => dispatch(\"change\", e)}\n\t\tstyle:top\n\t\tstyle:bottom\n\t\tstyle:max-height={`calc(${max_height}px - var(--window-padding))`}\n\t\tstyle:width={input_width + \"px\"}\n\t\tbind:this={listElement}\n\t\trole=\"listbox\"\n\t>\n\t\t{#each filtered_indices as index}\n\t\t\t<li\n\t\t\t\tclass=\"item\"\n\t\t\t\tclass:selected={selected_indices.includes(index)}\n\t\t\t\tclass:active={index === active_index}\n\t\t\t\tclass:bg-gray-100={index === active_index}\n\t\t\t\tclass:dark:bg-gray-600={index === active_index}\n\t\t\t\tdata-index={index}\n\t\t\t\taria-label={choices[index][0]}\n\t\t\t\tdata-testid=\"dropdown-option\"\n\t\t\t\trole=\"option\"\n\t\t\t\taria-selected={selected_indices.includes(index)}\n\t\t\t>\n\t\t\t\t<span class:hide={!selected_indices.includes(index)} class=\"inner-item\">\n\t\t\t\t\t✓\n\t\t\t\t</span>\n\t\t\t\t{choices[index][0]}\n\t\t\t</li>\n\t\t{/each}\n\t</ul>\n{/if}\n\n<style>\n\t.options {\n\t\t--window-padding: var(--size-8);\n\t\tposition: fixed;\n\t\tz-index: var(--layer-top);\n\t\tmargin-left: 0;\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--background-fill-primary);\n\t\tmin-width: fit-content;\n\t\tmax-width: inherit;\n\t\toverflow: auto;\n\t\tcolor: var(--body-text-color);\n\t\tlist-style: none;\n\t}\n\n\t.item {\n\t\tdisplay: flex;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t}\n\n\t.item:hover,\n\t.active {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.inner-item {\n\t\tpadding-right: var(--size-1);\n\t}\n\n\t.hide {\n\t\tvisibility: hidden;\n\t}\n</style>\n", "function positive_mod(n: number, m: number): number {\n\treturn ((n % m) + m) % m;\n}\n\nexport function handle_filter(\n\tchoices: [string, string | number][],\n\tinput_text: string\n): number[] {\n\treturn choices.reduce((filtered_indices, o, index) => {\n\t\tif (\n\t\t\tinput_text ? o[0].toLowerCase().includes(input_text.toLowerCase()) : true\n\t\t) {\n\t\t\tfiltered_indices.push(index);\n\t\t}\n\t\treturn filtered_indices;\n\t}, [] as number[]);\n}\n\nexport function handle_change(\n\tdispatch: any,\n\tvalue: string | number | (string | number)[] | undefined,\n\tvalue_is_output: boolean\n): void {\n\tdispatch(\"change\", value);\n\tif (!value_is_output) {\n\t\tdispatch(\"input\");\n\t}\n}\n\nexport function handle_shared_keys(\n\te: KeyboardEvent,\n\tactive_index: number | null,\n\tfiltered_indices: number[]\n): [boolean, number | null] {\n\tif (e.key === \"Escape\") {\n\t\treturn [false, active_index];\n\t}\n\tif (e.key === \"ArrowDown\" || e.key === \"ArrowUp\") {\n\t\tif (filtered_indices.length >= 0) {\n\t\t\tif (active_index === null) {\n\t\t\t\tactive_index =\n\t\t\t\t\te.key === \"ArrowDown\"\n\t\t\t\t\t\t? filtered_indices[0]\n\t\t\t\t\t\t: filtered_indices[filtered_indices.length - 1];\n\t\t\t} else {\n\t\t\t\tconst index_in_filtered = filtered_indices.indexOf(active_index);\n\t\t\t\tconst increment = e.key === \"ArrowUp\" ? -1 : 1;\n\t\t\t\tactive_index =\n\t\t\t\t\tfiltered_indices[\n\t\t\t\t\t\tpositive_mod(index_in_filtered + increment, filtered_indices.length)\n\t\t\t\t\t];\n\t\t\t}\n\t\t}\n\t}\n\treturn [true, active_index];\n}\n", "<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { DropdownArrow } from \"@gradio/icons\";\n\timport DropdownOptions from \"./DropdownOptions.svelte\";\n\timport { handle_filter, handle_change, handle_shared_keys } from \"./utils\";\n\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let value: string | number | (string | number)[] | undefined = [];\n\tlet old_value: string | number | (string | number)[] | undefined = [];\n\texport let value_is_output = false;\n\texport let choices: [string, string | number][];\n\tlet old_choices: [string, string | number][];\n\texport let disabled = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let allow_custom_value = false;\n\texport let filterable = true;\n\n\tlet filter_input: HTMLElement;\n\n\tlet show_options = false;\n\tlet choices_names: string[];\n\tlet choices_values: (string | number)[];\n\tlet input_text = \"\";\n\tlet old_input_text = \"\";\n\tlet initialized = false;\n\n\t// All of these are indices with respect to the choices array\n\tlet filtered_indices: number[] = [];\n\tlet active_index: number | null = null;\n\t// selected_index is null if allow_custom_value is true and the input_text is not in choices_names\n\tlet selected_index: number | null = null;\n\tlet old_selected_index: number | null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | undefined;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t}>();\n\n\t// Setting the initial value of the dropdown\n\tif (value) {\n\t\told_selected_index = choices.map((c) => c[1]).indexOf(value as string);\n\t\tselected_index = old_selected_index;\n\t\tif (selected_index === -1) {\n\t\t\told_value = value;\n\t\t\tselected_index = null;\n\t\t} else {\n\t\t\t[input_text, old_value] = choices[selected_index];\n\t\t\told_input_text = input_text;\n\t\t}\n\t} else if (choices.length > 0) {\n\t\told_selected_index = 0;\n\t\tselected_index = 0;\n\t\t[input_text, value] = choices[selected_index];\n\t\told_value = value;\n\t\told_input_text = input_text;\n\t}\n\n\t$: {\n\t\tif (\n\t\t\tselected_index !== old_selected_index &&\n\t\t\tselected_index !== null &&\n\t\t\tinitialized\n\t\t) {\n\t\t\t[input_text, value] = choices[selected_index];\n\t\t\told_selected_index = selected_index;\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: selected_index,\n\t\t\t\tvalue: choices_values[selected_index],\n\t\t\t\tselected: true\n\t\t\t});\n\t\t}\n\t}\n\n\t$: {\n\t\tif (value != old_value) {\n\t\t\tset_input_text();\n\t\t\thandle_change(dispatch, value, value_is_output);\n\t\t\told_value = value;\n\t\t}\n\t}\n\n\t$: {\n\t\tchoices_names = choices.map((c) => c[0]);\n\t\tchoices_values = choices.map((c) => c[1]);\n\t}\n\n\t$: {\n\t\tif (choices !== old_choices || input_text !== old_input_text) {\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\told_choices = choices;\n\t\t\told_input_text = input_text;\n\t\t\tif (!allow_custom_value && filtered_indices.length > 0) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction set_input_text(): void {\n\t\tif (value === undefined) {\n\t\t\tinput_text = \"\";\n\t\t} else if (choices_values.includes(value as string)) {\n\t\t\tinput_text = choices_names[choices_values.indexOf(value as string)];\n\t\t} else if (allow_custom_value) {\n\t\t\tinput_text = value as string;\n\t\t} else {\n\t\t\tinput_text = \"\";\n\t\t}\n\t}\n\n\tfunction handle_option_selected(e: any): void {\n\t\tselected_index = parseInt(e.detail.target.dataset.index);\n\t\tif (isNaN(selected_index)) {\n\t\t\t// This is the case when the user clicks on the scrollbar\n\t\t\tselected_index = null;\n\t\t\treturn;\n\t\t}\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tfilter_input.blur();\n\t}\n\n\tfunction handle_focus(e: FocusEvent): void {\n\t\tfiltered_indices = choices.map((_, i) => i);\n\t\tshow_options = true;\n\t\tdispatch(\"focus\");\n\t}\n\n\tfunction handle_blur(): void {\n\t\tif (!allow_custom_value) {\n\t\t\tinput_text = choices_names[choices_values.indexOf(value as string)];\n\t\t}\n\t\tvalue = input_text;\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction handle_key_down(e: KeyboardEvent): void {\n\t\t[show_options, active_index] = handle_shared_keys(\n\t\t\te,\n\t\t\tactive_index,\n\t\t\tfiltered_indices\n\t\t);\n\t\tif (e.key === \"Enter\") {\n\t\t\tif (active_index !== null) {\n\t\t\t\tselected_index = active_index;\n\t\t\t\tshow_options = false;\n\t\t\t\tfilter_input.blur();\n\t\t\t\tactive_index = null;\n\t\t\t} else if (choices_names.includes(input_text)) {\n\t\t\t\tselected_index = choices_names.indexOf(input_text);\n\t\t\t\tshow_options = false;\n\t\t\t\tactive_index = null;\n\t\t\t\tfilter_input.blur();\n\t\t\t} else if (allow_custom_value) {\n\t\t\t\tvalue = input_text;\n\t\t\t\tselected_index = null;\n\t\t\t\tshow_options = false;\n\t\t\t\tactive_index = null;\n\t\t\t\tfilter_input.blur();\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t\tinitialized = true;\n\t});\n</script>\n\n<label class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"wrap-inner\" class:show_options>\n\t\t\t<div class=\"secondary-wrap\">\n\t\t\t\t<input\n\t\t\t\t\tclass=\"border-none\"\n\t\t\t\t\tclass:subdued={!choices_names.includes(input_text) &&\n\t\t\t\t\t\t!allow_custom_value}\n\t\t\t\t\t{disabled}\n\t\t\t\t\tautocomplete=\"off\"\n\t\t\t\t\tbind:value={input_text}\n\t\t\t\t\tbind:this={filter_input}\n\t\t\t\t\ton:keydown={handle_key_down}\n\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\ton:focus={handle_focus}\n\t\t\t\t\treadonly={!filterable}\n\t\t\t\t/>\n\t\t\t\t{#if !disabled}\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t\t<DropdownOptions\n\t\t\t{show_options}\n\t\t\t{choices}\n\t\t\t{filtered_indices}\n\t\t\t{disabled}\n\t\t\tselected_indices={selected_index === null ? [] : [selected_index]}\n\t\t\t{active_index}\n\t\t\ton:change={handle_option_selected}\n\t\t/>\n\t</div>\n</label>\n\n<style>\n\tlabel:not(.container),\n\tlabel:not(.container) .wrap,\n\tlabel:not(.container) .wrap-inner,\n\tlabel:not(.container) .secondary-wrap,\n\tlabel:not(.container) input {\n\t\theight: 100%;\n\t}\n\t.container .wrap {\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--border-color-primary);\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t}\n\n\t.wrap:focus-within {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\t.wrap-inner {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tgap: var(--checkbox-label-gap);\n\t\tpadding: var(--checkbox-label-padding);\n\t}\n\t.secondary-wrap {\n\t\tdisplay: flex;\n\t\tflex: 1 1 0%;\n\t\talign-items: center;\n\t\tborder: none;\n\t\tmin-width: min-content;\n\t}\n\n\tinput {\n\t\tmargin: var(--spacing-sm);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: inherit;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\t.subdued {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tinput[readonly] {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher } from \"svelte\";\n\timport { _, number } from \"svelte-i18n\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Remove, DropdownArrow } from \"@gradio/icons\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport DropdownOptions from \"./DropdownOptions.svelte\";\n\timport { handle_filter, handle_change, handle_shared_keys } from \"./utils\";\n\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let value: string | number | (string | number)[] | undefined = [];\n\tlet old_value: string | number | (string | number)[] | undefined = [];\n\texport let value_is_output = false;\n\texport let max_choices: number | null = null;\n\texport let choices: [string, string | number][];\n\tlet old_choices: [string, string | number][];\n\texport let disabled = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let allow_custom_value = false;\n\texport let filterable = true;\n\n\tlet filter_input: HTMLElement;\n\tlet input_text = \"\";\n\tlet old_input_text = \"\";\n\tlet show_options = false;\n\tlet choices_names: string[];\n\tlet choices_values: (string | number)[];\n\n\t// All of these are indices with respect to the choices array\n\tlet filtered_indices: number[] = [];\n\tlet active_index: number | null = null;\n\t// selected_index consists of indices from choices or strings if allow_custom_value is true and user types in a custom value\n\tlet selected_indices: (number | string)[] = [];\n\tlet old_selected_index: (number | string)[] = [];\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | string[] | undefined;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t}>();\n\n\t// Setting the initial value of the multiselect dropdown\n\tif (Array.isArray(value)) {\n\t\tvalue.forEach((element) => {\n\t\t\tconst index = choices.map((c) => c[1]).indexOf(element);\n\t\t\tif (index !== -1) {\n\t\t\t\tselected_indices.push(index);\n\t\t\t} else {\n\t\t\t\tselected_indices.push(element);\n\t\t\t}\n\t\t});\n\t}\n\n\t$: {\n\t\tchoices_names = choices.map((c) => c[0]);\n\t\tchoices_values = choices.map((c) => c[1]);\n\t}\n\n\t$: {\n\t\tif (choices !== old_choices || input_text !== old_input_text) {\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\told_choices = choices;\n\t\t\told_input_text = input_text;\n\t\t\tif (!allow_custom_value) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t}\n\t}\n\n\t$: {\n\t\tif (JSON.stringify(value) != JSON.stringify(old_value)) {\n\t\t\thandle_change(dispatch, value, value_is_output);\n\t\t\told_value = Array.isArray(value) ? value.slice() : value;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (\n\t\t\tJSON.stringify(selected_indices) != JSON.stringify(old_selected_index)\n\t\t) {\n\t\t\tvalue = selected_indices.map((index) =>\n\t\t\t\ttypeof index === \"number\" ? choices_values[index] : index\n\t\t\t);\n\t\t\told_selected_index = selected_indices.slice();\n\t\t}\n\t}\n\n\tfunction handle_blur(): void {\n\t\tif (!allow_custom_value) {\n\t\t\tinput_text = \"\";\n\t\t}\n\n\t\tif (allow_custom_value && input_text !== \"\") {\n\t\t\tadd_selected_choice(input_text);\n\t\t\tinput_text = \"\";\n\t\t}\n\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction remove_selected_choice(option_index: number | string): void {\n\t\tselected_indices = selected_indices.filter((v) => v !== option_index);\n\t\tdispatch(\"select\", {\n\t\t\tindex: typeof option_index === \"number\" ? option_index : -1,\n\t\t\tvalue:\n\t\t\t\ttypeof option_index === \"number\"\n\t\t\t\t\t? choices_values[option_index]\n\t\t\t\t\t: option_index,\n\t\t\tselected: false,\n\t\t});\n\t}\n\n\tfunction add_selected_choice(option_index: number | string): void {\n\t\tif (max_choices === null || selected_indices.length < max_choices) {\n\t\t\tselected_indices = [...selected_indices, option_index];\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: typeof option_index === \"number\" ? option_index : -1,\n\t\t\t\tvalue:\n\t\t\t\t\ttypeof option_index === \"number\"\n\t\t\t\t\t\t? choices_values[option_index]\n\t\t\t\t\t\t: option_index,\n\t\t\t\tselected: true,\n\t\t\t});\n\t\t}\n\t\tif (selected_indices.length === max_choices) {\n\t\t\tshow_options = false;\n\t\t\tactive_index = null;\n\t\t\tfilter_input.blur();\n\t\t}\n\t}\n\n\tfunction handle_option_selected(e: any): void {\n\t\tconst option_index = parseInt(e.detail.target.dataset.index);\n\t\tadd_or_remove_index(option_index);\n\t}\n\n\tfunction add_or_remove_index(option_index: number): void {\n\t\tif (selected_indices.includes(option_index)) {\n\t\t\tremove_selected_choice(option_index);\n\t\t} else {\n\t\t\tadd_selected_choice(option_index);\n\t\t}\n\t\tinput_text = \"\";\n\t}\n\n\tfunction remove_all(e: any): void {\n\t\tselected_indices = [];\n\t\tinput_text = \"\";\n\t\te.preventDefault();\n\t}\n\n\tfunction handle_focus(e: FocusEvent): void {\n\t\tfiltered_indices = choices.map((_, i) => i);\n\t\tif (max_choices === null || selected_indices.length < max_choices) {\n\t\t\tshow_options = true;\n\t\t}\n\t\tdispatch(\"focus\");\n\t}\n\n\tfunction handle_key_down(e: KeyboardEvent): void {\n\t\t[show_options, active_index] = handle_shared_keys(\n\t\t\te,\n\t\t\tactive_index,\n\t\t\tfiltered_indices\n\t\t);\n\t\tif (e.key === \"Enter\") {\n\t\t\tif (active_index !== null) {\n\t\t\t\tadd_or_remove_index(active_index);\n\t\t\t} else {\n\t\t\t\tif (allow_custom_value) {\n\t\t\t\t\tadd_selected_choice(input_text);\n\t\t\t\t\tinput_text = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (e.key === \"Backspace\" && input_text === \"\") {\n\t\t\tselected_indices = [...selected_indices.slice(0, -1)];\n\t\t}\n\t\tif (selected_indices.length === max_choices) {\n\t\t\tshow_options = false;\n\t\t\tactive_index = null;\n\t\t}\n\t}\n\n\tfunction set_selected_indices(): void {\n\t\tif (value === undefined) {\n\t\t\tselected_indices = [];\n\t\t} else if (Array.isArray(value)) {\n\t\t\tselected_indices = value\n\t\t\t\t.map((v) => {\n\t\t\t\t\tconst index = choices_values.indexOf(v);\n\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\treturn index;\n\t\t\t\t\t}\n\t\t\t\t\tif (allow_custom_value) {\n\t\t\t\t\t\treturn v;\n\t\t\t\t\t}\n\t\t\t\t\t// Instead of returning null, skip this iteration\n\t\t\t\t\treturn undefined;\n\t\t\t\t})\n\t\t\t\t.filter((val): val is string | number => val !== undefined);\n\t\t}\n\t}\n\n\t$: value, set_selected_indices();\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n</script>\n\n<label class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"wrap-inner\" class:show_options>\n\t\t\t{#each selected_indices as s}\n\t\t\t\t<div class=\"token\">\n\t\t\t\t\t<span>\n\t\t\t\t\t\t{#if typeof s === \"number\"}\n\t\t\t\t\t\t\t{choices_names[s]}\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t{s}\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</span>\n\t\t\t\t\t{#if !disabled}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tclass=\"token-remove\"\n\t\t\t\t\t\t\ton:click|preventDefault={() => remove_selected_choice(s)}\n\t\t\t\t\t\t\ton:keydown|preventDefault={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremove_selected_choice(s);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ttitle={$_(\"common.remove\") + \" \" + s}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Remove />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\t\t\t{/each}\n\t\t\t<div class=\"secondary-wrap\">\n\t\t\t\t<input\n\t\t\t\t\tclass=\"border-none\"\n\t\t\t\t\tclass:subdued={(!choices_names.includes(input_text) &&\n\t\t\t\t\t\t!allow_custom_value) ||\n\t\t\t\t\t\tselected_indices.length === max_choices}\n\t\t\t\t\t{disabled}\n\t\t\t\t\tautocomplete=\"off\"\n\t\t\t\t\tbind:value={input_text}\n\t\t\t\t\tbind:this={filter_input}\n\t\t\t\t\ton:keydown={handle_key_down}\n\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\ton:focus={handle_focus}\n\t\t\t\t\treadonly={!filterable}\n\t\t\t\t/>\n\n\t\t\t\t{#if !disabled}\n\t\t\t\t\t{#if selected_indices.length > 0}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\tclass=\"token-remove remove-all\"\n\t\t\t\t\t\t\ttitle={$_(\"common.clear\")}\n\t\t\t\t\t\t\ton:click={remove_all}\n\t\t\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremove_all(event);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Remove />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t\t<DropdownOptions\n\t\t\t{show_options}\n\t\t\t{choices}\n\t\t\t{filtered_indices}\n\t\t\t{disabled}\n\t\t\t{selected_indices}\n\t\t\t{active_index}\n\t\t\ton:change={handle_option_selected}\n\t\t/>\n\t</div>\n</label>\n\n<style>\n\tlabel:not(.container),\n\tlabel:not(.container) .wrap,\n\tlabel:not(.container) .wrap-inner,\n\tlabel:not(.container) .secondary-wrap,\n\tlabel:not(.container) .token,\n\tlabel:not(.container) input {\n\t\theight: 100%;\n\t}\n\t.container .wrap {\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--border-color-primary);\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t}\n\n\t.wrap:focus-within {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\t.wrap-inner {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tgap: var(--checkbox-label-gap);\n\t\tpadding: var(--checkbox-label-padding);\n\t}\n\n\t.token {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--checkbox-label-shadow);\n\t\tborder: var(--checkbox-label-border-width) solid\n\t\t\tvar(--checkbox-label-border-color);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--checkbox-label-background-fill);\n\t\tpadding: var(--checkbox-label-padding);\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\t.token > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.token-remove {\n\t\tfill: var(--body-text-color);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tborder: var(--checkbox-border-width) solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-0-5);\n\t\twidth: 18px;\n\t\theight: 18px;\n\t}\n\n\t.secondary-wrap {\n\t\tdisplay: flex;\n\t\tflex: 1 1 0%;\n\t\talign-items: center;\n\t\tborder: none;\n\t\tmin-width: min-content;\n\t}\n\n\tinput {\n\t\tmargin: var(--spacing-sm);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: inherit;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\t.remove-all {\n\t\tmargin-left: var(--size-1);\n\t\twidth: 20px;\n\t\theight: 20px;\n\t}\n\t.subdued {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\tinput[readonly] {\n\t\tcursor: pointer;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "ctx", "i", "set_style", "ul", "ul_transition", "create_bidirectional_transition", "fly", "toggle_class", "li", "span", "set_data", "t2", "t2_value", "if_block", "create_if_block", "div", "choices", "$$props", "filtered_indices", "show_options", "disabled", "selected_indices", "active_index", "distance_from_top", "distance_from_bottom", "input_height", "input_width", "refElement", "listElement", "top", "bottom", "max_height", "innerHeight", "calculate_window_distance", "ref_top", "ref_bottom", "$$invalidate", "scroll_timeout", "scroll_listener", "dispatch", "createEventDispatcher", "$$value", "mousedown_handler", "e", "elements", "element", "rect", "positive_mod", "m", "handle_filter", "input_text", "o", "index", "handle_change", "value", "value_is_output", "handle_shared_keys", "index_in_filtered", "increment", "input", "label_1", "div2", "div1", "div0", "dirty", "dropdownoptions_changes", "label", "info", "old_value", "old_choices", "show_label", "container", "allow_custom_value", "filterable", "filter_input", "choices_names", "choices_values", "old_input_text", "initialized", "selected_index", "old_selected_index", "c", "set_input_text", "handle_option_selected", "handle_focus", "_", "handle_blur", "handle_key_down", "afterUpdate", "t", "t_value", "attr", "div_title_value", "current", "create_if_block_3", "create_if_block_2", "create_if_block_1", "each_blocks", "max_choices", "add_selected_choice", "remove_selected_choice", "option_index", "v", "add_or_remove_index", "remove_all", "set_selected_indices", "val", "click_handler", "s", "event"], "mappings": "6nBAAAA,EAQKC,EAAAC,EAAAC,CAAA,EADJC,EAAyBF,EAAAG,CAAA,8XCP1BL,EASKC,EAAAC,EAAAC,CAAA,EAHJC,EAECF,EAAAG,CAAA,2LC+EOC,EAAgB,CAAA,CAAA,uBAArB,OAAIC,GAAA,wMALoBD,EAAU,EAAA,8BAAA,EACvBE,EAAAC,EAAA,QAAAH,KAAc,IAAI,UAPhCN,EA8BIC,EAAAQ,EAAAN,CAAA,8HAnBIG,EAAgB,CAAA,CAAA,oBAArB,OAAIC,GAAA,EAAA,mHAAJ,2FALwBD,EAAU,EAAA,8BAAA,SACvBE,EAAAC,EAAA,QAAAH,KAAc,IAAI,2BALbI,IAAAA,EAAAC,GAAAF,EAAAG,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,+BAAnBF,IAAAA,EAAAC,GAAAF,EAAAG,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,wGAyBlCN,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,EAAA,yIAHEA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,wDANtCA,EAAK,EAAA,CAAA,qBACLA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,CAAA,kFAGbA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,iBAR9BA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,EACjCO,EAAAC,EAAA,SAAAR,QAAUA,EAAY,CAAA,CAAA,EACjBO,EAAAC,EAAA,cAAAR,QAAUA,EAAY,CAAA,CAAA,EACjBO,EAAAC,EAAA,mBAAAR,QAAUA,EAAY,CAAA,CAAA,UAL/CN,EAgBIC,EAAAa,EAAAX,CAAA,EAJHC,EAEMU,EAAAC,CAAA,iDAFaT,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,cAGjDA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,EAAA,KAAAU,GAAAC,EAAAC,CAAA,cATLZ,EAAK,EAAA,oCACLA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,qCAGbA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,gDAR9BA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,QACjCO,EAAAC,EAAA,SAAAR,QAAUA,EAAY,CAAA,CAAA,QACjBO,EAAAC,EAAA,cAAAR,QAAUA,EAAY,CAAA,CAAA,QACjBO,EAAAC,EAAA,mBAAAR,QAAUA,EAAY,CAAA,CAAA,yDAlB7C,IAAAa,EAAAb,OAAiBA,EAAQ,CAAA,GAAAc,GAAAd,CAAA,+EAD9BN,EAA+CC,EAAAoB,EAAAlB,CAAA,kEAFrBG,EAAe,EAAA,CAAA,4CAGpCA,OAAiBA,EAAQ,CAAA,oNAxElB,QAAAgB,CAAoC,EAAAC,GACpC,iBAAAC,CAA0B,EAAAD,EAC1B,CAAA,aAAAE,EAAe,EAAK,EAAAF,EACpB,CAAA,SAAAG,EAAW,EAAK,EAAAH,GAChB,iBAAAI,EAAgB,EAAA,EAAAJ,EAChB,CAAA,aAAAK,EAA8B,IAAI,EAAAL,EAEzCM,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAoBC,EAAuBC,EAC3CC,WAEKC,GAAyB,OACzB,IAAKC,EAAS,OAAQC,GAC7BR,EAAW,wBACZS,EAAA,GAAAb,EAAoBW,CAAO,OAC3BV,EAAuBQ,EAAcG,CAAU,EAG5C,IAAAE,EAAwC,cACnCC,GAAe,CAClBnB,IACDkB,IAAmB,MACtB,aAAaA,CAAc,EAG5BA,EAAiB,gBAChBJ,IACAI,EAAiB,MACf,KAiCE,MAAAE,EAAWC,uFAKgBb,EAAUc,WAKZ,MAAAC,EAAAC,GAAMJ,EAAS,SAAUI,CAAC,4CAK7Cf,EAAWa,+TA7CtB,CACI,GAAAtB,GAAgBQ,EAAU,CACzB,GAAAC,GAAeP,EAAiB,OAAS,EAAC,CACzC,IAAAuB,EAAWhB,EAAY,iBAAiB,IAAI,EACrC,UAAAiB,KAAW,MAAM,KAAKD,CAAQ,KAEvCC,EAAQ,aAAa,YAAY,IACjCxB,EAAiB,CAAC,EAAE,WAAQ,CAE5BO,GAAa,WAAW,EAAIiB,EAA0B,SAAS,SAKlEZ,IACM,MAAAa,EAAOnB,EAAW,eAAe,sBAAqB,EAC5DS,EAAA,GAAAX,EAAeqB,GAAM,QAAU,CAAC,EAChCV,EAAA,EAAAV,EAAcoB,GAAM,OAAS,CAAC,EAE3BtB,EAAuBD,GAC1Ba,EAAA,EAAAP,KAASN,KAAiB,EAC1Ba,EAAA,GAAAL,EAAaP,CAAoB,EACjCY,EAAA,GAAAN,EAAS,IAAI,SAEbA,EAAM,GAAMN,EAAuBC,KAAY,OAC/CM,EAAaR,EAAoBE,CAAY,EAC7CW,EAAA,EAAAP,EAAM,IAAI,2MCjEb,SAASkB,GAAa,EAAWC,EAAmB,CAC1C,OAAA,EAAIA,EAAKA,GAAKA,CACxB,CAEgB,SAAAC,GACfjC,EACAkC,EACW,CACX,OAAOlC,EAAQ,OAAO,CAACE,EAAkBiC,EAAGC,MAE1C,CAAAF,GAAaC,EAAE,CAAC,EAAE,YAAA,EAAc,SAASD,EAAW,aAAa,IAEjEhC,EAAiB,KAAKkC,CAAK,EAErBlC,GACL,CAAc,CAAA,CAClB,CAEgB,SAAAmC,GACfd,EACAe,EACAC,EACO,CACPhB,EAAS,SAAUe,CAAK,EACnBC,GACJhB,EAAS,OAAO,CAElB,CAEgB,SAAAiB,GACfb,EACArB,EACAJ,EAC2B,CACvB,GAAAyB,EAAE,MAAQ,SACN,MAAA,CAAC,GAAOrB,CAAY,EAE5B,IAAIqB,EAAE,MAAQ,aAAeA,EAAE,MAAQ,YAClCzB,EAAiB,QAAU,EAC9B,GAAII,IAAiB,KAEnBA,EAAAqB,EAAE,MAAQ,YACPzB,EAAiB,CAAC,EAClBA,EAAiBA,EAAiB,OAAS,CAAC,MAC1C,CACA,MAAAuC,EAAoBvC,EAAiB,QAAQI,CAAY,EACzDoC,EAAYf,EAAE,MAAQ,UAAY,GAAK,EAC7CrB,EACCJ,EACC6B,GAAaU,EAAoBC,EAAWxC,EAAiB,MAAM,CACpE,EAIG,MAAA,CAAC,GAAMI,CAAY,CAC3B,sCC4HkCtB,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,oUAkB7BA,EAAQ,CAAA,GAAAc,GAAA,8FAUG,iBAAAd,QAAmB,SAAaA,EAAc,EAAA,CAAA,uCAErDA,EAAsB,EAAA,CAAA,gOAdpBA,EAAU,CAAA,EATLO,EAAAoD,EAAA,UAAA,CAAA3D,EAAc,EAAA,EAAA,SAASA,QACrCA,EAAkB,CAAA,CAAA,6MATzBN,EAkCOC,EAAAiE,EAAA/D,CAAA,qBA/BNC,EA8BK8D,EAAAC,CAAA,EA7BJ/D,EAmBK+D,EAAAC,CAAA,EAlBJhE,EAiBKgE,EAAAC,CAAA,EAhBJjE,EAYCiE,EAAAJ,CAAA,OANY3D,EAAU,CAAA,CAAA,iGAEVA,EAAe,EAAA,CAAA,aAClBA,EAAW,EAAA,CAAA,cACVA,EAAY,EAAA,CAAA,2LACXA,EAAU,CAAA,wCALTA,EAAU,CAAA,QAAVA,EAAU,CAAA,CAAA,mBAJNO,EAAAoD,EAAA,UAAA,CAAA3D,EAAc,EAAA,EAAA,SAASA,QACrCA,EAAkB,CAAA,CAAA,EAUfA,EAAQ,CAAA,uQAUGgE,EAAA,CAAA,EAAA,OAAAC,EAAA,iBAAAjE,QAAmB,SAAaA,EAAc,EAAA,CAAA,uRAtMvD,MAAAkE,CAAa,EAAAjD,EACb,CAAA,KAAAkD,EAA2B,MAAS,EAAAlD,GACpC,MAAAqC,EAAK,EAAA,EAAArC,EACZmD,EAAS,CAAA,EACF,CAAA,gBAAAb,EAAkB,EAAK,EAAAtC,GACvB,QAAAD,CAAoC,EAAAC,EAC3CoD,EACO,CAAA,SAAAjD,EAAW,EAAK,EAAAH,GAChB,WAAAqD,CAAmB,EAAArD,EACnB,CAAA,UAAAsD,EAAY,EAAI,EAAAtD,EAChB,CAAA,mBAAAuD,EAAqB,EAAK,EAAAvD,EAC1B,CAAA,WAAAwD,EAAa,EAAI,EAAAxD,EAExByD,EAEAvD,EAAe,GACfwD,EACAC,EACA1B,EAAa,GACb2B,EAAiB,GACjBC,EAAc,GAGd5D,EAAgB,CAAA,EAChBI,EAA8B,KAE9ByD,EAAgC,KAChCC,EAEE,MAAAzC,EAAWC,KASbc,GACH0B,EAAqBhE,EAAQ,IAAKiE,GAAMA,EAAE,CAAC,CAAG,EAAA,QAAQ3B,CAAe,EACrEyB,EAAiBC,EACbD,QACHX,EAAYd,EACZyB,EAAiB,OAEhB,CAAA7B,EAAYkB,CAAS,EAAIpD,EAAQ+D,CAAc,EAChDF,EAAiB3B,IAERlC,EAAQ,OAAS,IAC3BgE,EAAqB,EACrBD,EAAiB,EAChB,CAAA7B,EAAYI,CAAK,EAAItC,EAAQ+D,CAAc,EAC5CX,EAAYd,EACZuB,EAAiB3B,YA2CTgC,GAAc,CAClB5B,IAAU,OACblB,EAAA,EAAAc,EAAa,EAAE,EACL0B,EAAe,SAAStB,CAAe,EACjDlB,EAAA,EAAAc,EAAayB,EAAcC,EAAe,QAAQtB,CAAe,CAAA,CAAA,EACvDkB,EACVpC,EAAA,EAAAc,EAAaI,CAAe,EAE5BlB,EAAA,EAAAc,EAAa,EAAE,EAIR,SAAAiC,EAAuBxC,EAAM,CAEjC,QADJoC,EAAiB,SAASpC,EAAE,OAAO,OAAO,QAAQ,KAAK,CAAA,EACnD,MAAMoC,CAAc,EAAA,CAEvB3C,EAAA,GAAA2C,EAAiB,IAAI,SAGtB3C,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnBoD,EAAa,KAAI,EAGT,SAAAU,EAAazC,EAAa,KAClCzB,EAAmBF,EAAQ,KAAKqE,GAAGpF,KAAMA,EAAC,CAAA,EAC1CmC,EAAA,GAAAjB,EAAe,EAAI,EACnBoB,EAAS,OAAO,WAGR+C,GAAW,CACdd,GACJpC,EAAA,EAAAc,EAAayB,EAAcC,EAAe,QAAQtB,CAAe,CAAA,CAAA,EAElElB,EAAA,GAAAkB,EAAQJ,CAAU,EAClBd,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnBiB,EAAS,MAAM,EAGP,SAAAgD,GAAgB5C,EAAgB,OACvCxB,EAAcG,CAAY,EAAIkC,GAC9Bb,EACArB,EACAJ,CAAgB,EAAAC,GAAAiB,EAAA,GAAAd,CAAA,EAAAc,EAAA,EAAApB,CAAA,EAAAoB,EAAA,GAAAiC,CAAA,EAAAjC,EAAA,EAAAc,CAAA,EAAAd,EAAA,GAAAyC,CAAA,EAAAzC,EAAA,EAAAoC,CAAA,EAAApC,EAAA,EAAAlB,CAAA,EAAAkB,EAAA,GAAA2C,CAAA,EAAA3C,EAAA,GAAA4C,CAAA,EAAA5C,EAAA,GAAA0C,CAAA,EAAA1C,EAAA,GAAAwC,CAAA,EAAA,EAEbjC,EAAE,MAAQ,UACTrB,IAAiB,MACpBc,EAAA,GAAA2C,EAAiBzD,CAAY,EAC7Bc,EAAA,GAAAjB,EAAe,EAAK,EACpBuD,EAAa,KAAI,EACjBtC,EAAA,GAAAd,EAAe,IAAI,GACTqD,EAAc,SAASzB,CAAU,GAC3Cd,EAAA,GAAA2C,EAAiBJ,EAAc,QAAQzB,CAAU,CAAA,EACjDd,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnBoD,EAAa,KAAI,GACPF,IACVpC,EAAA,GAAAkB,EAAQJ,CAAU,EAClBd,EAAA,GAAA2C,EAAiB,IAAI,EACrB3C,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnBoD,EAAa,KAAI,IAKpBc,GAAW,IAAA,CACVpD,EAAA,GAAAmB,EAAkB,EAAK,EACvBnB,EAAA,GAAA0C,EAAc,EAAI,kBAgBH5B,EAAU,KAAA,+FACXwB,EAAYjC,obArG1BkC,EAAgB3D,EAAQ,IAAKiE,GAAMA,EAAE,CAAC,CAAA,CAAA,OACtCL,EAAiB5D,EAAQ,IAAKiE,GAAMA,EAAE,CAAC,CAAA,CAAA,4BAxBtCF,IAAmBC,GACnBD,IAAmB,MACnBD,IAEC1C,EAAA,EAAA,CAAAc,EAAYI,CAAK,EAAItC,EAAQ+D,CAAc,EAAA7B,GAAAd,EAAA,GAAAkB,CAAA,EAAAlB,EAAA,GAAA2C,CAAA,EAAA3C,EAAA,GAAA4C,CAAA,EAAA5C,EAAA,GAAA0C,CAAA,EAAA1C,EAAA,EAAApB,CAAA,EAAAoB,EAAA,GAAAwC,CAAA,IAC5CxC,EAAA,GAAA4C,EAAqBD,CAAc,EACnCxC,EAAS,SAAQ,CAChB,MAAOwC,EACP,MAAOH,EAAeG,CAAc,EACpC,SAAU,6BAMRzB,GAASc,IACZc,IACA7B,GAAcd,EAAUe,EAAOC,CAAe,EAC9CnB,EAAA,GAAAgC,EAAYd,CAAK,4BAUdtC,IAAYqD,GAAenB,IAAe2B,KAC7CzC,EAAA,EAAAlB,EAAmB+B,GAAcjC,EAASkC,CAAU,CAAA,EACpDd,EAAA,GAAAiC,EAAcrD,CAAO,EACrBoB,EAAA,GAAAyC,EAAiB3B,CAAU,EACtB,CAAAsB,GAAsBtD,EAAiB,OAAS,QACpDI,EAAeJ,EAAiB,CAAC,CAAA,wWCsHHlB,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,uCAU/BA,EAAC,EAAA,EAAA,mEAADA,EAAC,EAAA,EAAA,KAAAU,GAAA+E,EAAAC,CAAA,iCAFD,IAAAA,EAAA1F,MAAcA,EAAC,EAAA,CAAA,EAAA,iDAAfgE,EAAA,CAAA,EAAA,OAAA0B,KAAAA,EAAA1F,MAAcA,EAAC,EAAA,CAAA,EAAA,KAAAU,GAAA+E,EAAAC,CAAA,mQAgBTC,EAAA5E,EAAA,QAAA6E,EAAA5F,EAAG,EAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,CAAA,UAVrCN,EAaKC,EAAAoB,EAAAlB,CAAA,sFAHG,CAAAgG,GAAA7B,EAAA,CAAA,EAAA,OAAA4B,KAAAA,EAAA5F,EAAG,EAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,gKAjBzB,OAAA,OAAAA,OAAM,SAAQ8F,2BAMrB9F,EAAQ,CAAA,GAAA+F,GAAA/F,CAAA,iIARfN,EAwBKC,EAAAoB,EAAAlB,CAAA,EAvBJC,EAMMiB,EAAAN,CAAA,iHACAT,EAAQ,CAAA,oMAmCTA,EAAgB,EAAA,EAAC,OAAS,GAACgG,GAAAhG,CAAA,8GAA3BA,EAAgB,EAAA,EAAC,OAAS,6YAKtB2F,EAAA5E,EAAA,QAAA6E,EAAA5F,MAAG,cAAc,CAAA,UAJzBN,EAaKC,EAAAoB,EAAAlB,CAAA,sCARMG,EAAU,EAAA,CAAA,uCADb,CAAA6F,GAAA7B,EAAA,CAAA,EAAA,OAAA4B,KAAAA,EAAA5F,MAAG,cAAc,oQAjDrBA,EAAgB,EAAA,CAAA,uBAArB,OAAIC,GAAA,mEA2CCD,EAAQ,CAAA,GAAAc,GAAAd,CAAA,2JA4BJA,EAAsB,EAAA,CAAA,2QA/BpBA,EAAU,CAAA,iBAVJA,EAAa,EAAA,EAAC,SAASA,EAAU,CAAA,CAAA,GAAA,CAChDA,EAAkB,CAAA,GACnBA,EAAgB,EAAA,EAAC,SAAWA,EAAW,CAAA,CAAA,6MArC7CN,EA+EOC,EAAAiE,EAAA/D,CAAA,qBA5ENC,EA2EK8D,EAAAC,CAAA,EA1EJ/D,EAgEK+D,EAAAC,CAAA,0DApCJhE,EAmCKgE,EAAAC,CAAA,EAlCJjE,EAaCiE,EAAAJ,CAAA,OANY3D,EAAU,CAAA,CAAA,iGAEVA,EAAe,EAAA,CAAA,aAClBA,EAAW,EAAA,CAAA,cACVA,EAAY,EAAA,CAAA,4JAvCjBA,EAAgB,EAAA,CAAA,oBAArB,OAAIC,GAAA,EAAA,wGAAJ,OAAIA,EAAAgG,EAAA,OAAAhG,GAAA,mEAwCOD,EAAU,CAAA,wCALTA,EAAU,CAAA,QAAVA,EAAU,CAAA,CAAA,mCALLA,EAAa,EAAA,EAAC,SAASA,EAAU,CAAA,CAAA,GAAA,CAChDA,EAAkB,CAAA,GACnBA,EAAgB,EAAA,EAAC,SAAWA,EAAW,CAAA,CAAA,EAWnCA,EAAQ,CAAA,+bA3Cb,OAAIC,GAAA,2QArNG,MAAAiE,CAAa,EAAAjD,EACb,CAAA,KAAAkD,EAA2B,MAAS,EAAAlD,GACpC,MAAAqC,EAAK,EAAA,EAAArC,EACZmD,EAAS,CAAA,EACF,CAAA,gBAAAb,EAAkB,EAAK,EAAAtC,EACvB,CAAA,YAAAiF,EAA6B,IAAI,EAAAjF,GACjC,QAAAD,CAAoC,EAAAC,EAC3CoD,EACO,CAAA,SAAAjD,EAAW,EAAK,EAAAH,GAChB,WAAAqD,CAAmB,EAAArD,EACnB,CAAA,UAAAsD,EAAY,EAAI,EAAAtD,EAChB,CAAA,mBAAAuD,EAAqB,EAAK,EAAAvD,EAC1B,CAAA,WAAAwD,EAAa,EAAI,EAAAxD,EAExByD,EACAxB,EAAa,GACb2B,EAAiB,GACjB1D,EAAe,GACfwD,EACAC,EAGA1D,EAAgB,CAAA,EAChBI,EAA8B,KAE9BD,EAAgB,CAAA,EAChB2D,EAAkB,CAAA,EAEhB,MAAAzC,EAAWC,KASb,MAAM,QAAQc,CAAK,GACtBA,EAAM,QAAST,GAAO,CACf,MAAAO,EAAQpC,EAAQ,IAAKiE,IAAMA,GAAE,CAAC,CAAG,EAAA,QAAQpC,CAAO,EAClDO,OACH/B,EAAiB,KAAK+B,CAAK,EAE3B/B,EAAiB,KAAKwB,CAAO,aAuCvByC,GAAW,CACdd,GACJpC,EAAA,EAAAc,EAAa,EAAE,EAGZsB,GAAsBtB,IAAe,KACxCiD,EAAoBjD,CAAU,EAC9Bd,EAAA,EAAAc,EAAa,EAAE,GAGhBd,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnBiB,EAAS,MAAM,EAGP,SAAA6D,EAAuBC,EAA6B,MAC5DhF,EAAmBA,EAAiB,OAAQiF,GAAMA,IAAMD,CAAY,CAAA,EACpE9D,EAAS,SAAQ,CAChB,aAAc8D,GAAiB,SAAWA,KAC1C,MAAK,OACGA,GAAiB,SACrBzB,EAAeyB,CAAY,EAC3BA,EACJ,SAAU,KAIH,SAAAF,EAAoBE,EAA6B,EACrDH,IAAgB,MAAQ7E,EAAiB,OAAS6E,UACrD7E,EAAgB,CAAA,GAAOA,EAAkBgF,CAAY,CAAA,EACrD9D,EAAS,SAAQ,CAChB,aAAc8D,GAAiB,SAAWA,KAC1C,MAAK,OACGA,GAAiB,SACrBzB,EAAeyB,CAAY,EAC3BA,EACJ,SAAU,MAGRhF,EAAiB,SAAW6E,IAC/B9D,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnBoD,EAAa,KAAI,GAIV,SAAAS,GAAuBxC,EAAM,OAC/B0D,EAAe,SAAS1D,EAAE,OAAO,OAAO,QAAQ,KAAK,EAC3D4D,GAAoBF,CAAY,EAGxB,SAAAE,GAAoBF,EAAoB,CAC5ChF,EAAiB,SAASgF,CAAY,EACzCD,EAAuBC,CAAY,EAEnCF,EAAoBE,CAAY,EAEjCjE,EAAA,EAAAc,EAAa,EAAE,EAGP,SAAAsD,GAAW7D,EAAM,MACzBtB,EAAgB,CAAA,CAAA,EAChBe,EAAA,EAAAc,EAAa,EAAE,EACfP,EAAE,eAAc,EAGR,SAAAyC,EAAazC,EAAa,MAClCzB,EAAmBF,EAAQ,KAAKqE,EAAGpF,KAAMA,EAAC,CAAA,GACtCiG,IAAgB,MAAQ7E,EAAiB,OAAS6E,IACrD9D,EAAA,GAAAjB,EAAe,EAAI,EAEpBoB,EAAS,OAAO,EAGR,SAAAgD,GAAgB5C,EAAgB,OACvCxB,EAAcG,CAAY,EAAIkC,GAC9Bb,EACArB,EACAJ,CAAgB,EAAAC,GAAAiB,EAAA,GAAAd,CAAA,EAAAc,EAAA,EAAApB,CAAA,EAAAoB,EAAA,GAAAiC,CAAA,EAAAjC,EAAA,EAAAc,CAAA,EAAAd,EAAA,GAAAyC,CAAA,EAAAzC,EAAA,EAAAoC,CAAA,EAAApC,EAAA,GAAAlB,CAAA,IAEbyB,EAAE,MAAQ,UACTrB,IAAiB,KACpBiF,GAAoBjF,CAAY,EAE5BkD,IACH2B,EAAoBjD,CAAU,EAC9Bd,EAAA,EAAAc,EAAa,EAAE,IAIdP,EAAE,MAAQ,aAAeO,IAAe,IAC3Cd,EAAA,GAAAf,MAAuBA,EAAiB,MAAM,IAAK,CAAA,CAAA,EAEhDA,EAAiB,SAAW6E,IAC/B9D,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,YAIZmF,IAAoB,CACxBnD,IAAU,YACbjC,EAAgB,CAAA,CAAA,EACN,MAAM,QAAQiC,CAAK,GAC7BlB,EAAA,GAAAf,EAAmBiC,EACjB,IAAKgD,GAAC,CACA,MAAAlD,EAAQwB,EAAe,QAAQ0B,CAAC,EAClC,GAAAlD,cACIA,KAEJoB,SACI8B,CAKR,CAAA,EAAA,OAAQI,GAAgCA,IAAQ,MAAS,CAAA,EAM7DlB,GAAW,IAAA,CACVpD,EAAA,GAAAmB,EAAkB,EAAK,IAqBa,MAAAoD,GAAAC,GAAAR,EAAuBQ,CAAC,QAC3BC,IAAK,CAC5BA,EAAM,MAAQ,SACjBT,EAAuBQ,CAAC,iBAoBhB1D,EAAU,KAAA,wDACXwB,EAAYjC,qBAeRoE,GAAK,CACbA,EAAM,MAAQ,SACjBL,GAAWK,CAAK,mdAzNvBlC,EAAgB3D,EAAQ,IAAKiE,GAAMA,EAAE,CAAC,CAAA,CAAA,OACtCL,EAAiB5D,EAAQ,IAAKiE,GAAMA,EAAE,CAAC,CAAA,CAAA,6BAInCjE,IAAYqD,GAAenB,IAAe2B,KAC7CzC,EAAA,GAAAlB,EAAmB+B,GAAcjC,EAASkC,CAAU,CAAA,EACpDd,EAAA,GAAAiC,EAAcrD,CAAO,EACrBoB,EAAA,GAAAyC,EAAiB3B,CAAU,EACtBsB,QACJlD,EAAeJ,EAAiB,CAAC,CAAA,4BAclC,KAAK,UAAUG,CAAgB,GAAK,KAAK,UAAU2D,CAAkB,SAErE1B,EAAQjC,EAAiB,IAAK+B,GAAK,OAC3BA,GAAU,SAAWwB,EAAexB,CAAK,EAAIA,CAAK,CAAA,OAE1D4B,EAAqB3D,EAAiB,MAAK,CAAA,2BAbxC,KAAK,UAAUiC,CAAK,GAAK,KAAK,UAAUc,CAAS,IACpDf,GAAcd,EAAUe,EAAOC,CAAe,OAC9Ca,EAAY,MAAM,QAAQd,CAAK,EAAIA,EAAM,QAAUA,CAAK,0BAsIhDmD,GAAoB"}