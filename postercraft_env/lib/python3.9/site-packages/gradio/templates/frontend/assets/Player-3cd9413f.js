import{S as G,e as I,s as J,f as w,g as n,h as q,j as u,n as g,k as N,R as Q,U as W,m as C,F as S,o as Y,t as ne,N as oe,G as $,p as B,ai as se,q as ge,X as Z,r as me,u as M,v as he,w as P,x as ae,H as D,B as ve,C as ke,M as ue,E as ce}from"./index-c99b2410.js";import{U as be}from"./Undo-61b53ec5.js";import{V as we}from"./Player.svelte_svelte_type_style_lang-fbdfbb5c.js";function ye(i){let e,r;return{c(){e=w("svg"),r=w("path"),n(r,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round")},m(t,s){q(t,e,s),u(e,r)},p:g,i:g,o:g,d(t){t&&N(e)}}}class Te extends G{constructor(e){super(),I(this,e,null,ye,J,{})}}function Ce(i){let e,r,t;return{c(){e=w("svg"),r=w("rect"),t=w("rect"),n(r,"x","6"),n(r,"y","4"),n(r,"width","4"),n(r,"height","16"),n(t,"x","14"),n(t,"y","4"),n(t,"width","4"),n(t,"height","16"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round")},m(s,h){q(s,e,h),u(e,r),u(e,t)},p:g,i:g,o:g,d(s){s&&N(e)}}}class ze extends G{constructor(e){super(),I(this,e,null,Ce,J,{})}}function Be(i){let e,r;return{c(){e=w("svg"),r=w("polygon"),n(r,"points","5 3 19 12 5 21 5 3"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round")},m(t,s){q(t,e,s),u(e,r)},p:g,i:g,o:g,d(t){t&&N(e)}}}class Me extends G{constructor(e){super(),I(this,e,null,Be,J,{})}}function Pe(i){let e,r,t;return{c(){e=w("svg"),r=w("polygon"),t=w("rect"),n(r,"points","23 7 16 12 23 17 23 7"),n(t,"x","1"),n(t,"y","5"),n(t,"width","15"),n(t,"height","14"),n(t,"rx","2"),n(t,"ry","2"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round"),n(e,"class","feather feather-video")},m(s,h){q(s,e,h),u(e,r),u(e,t)},p:g,i:g,o:g,d(s){s&&N(e)}}}class Fe extends G{constructor(e){super(),I(this,e,null,Pe,J,{})}}function Ve(i){let e,r;return{c(){e=C("track"),n(e,"kind","captions"),ue(e.src,r=i[1])||n(e,"src",r),e.default=!0},m(t,s){q(t,e,s)},p(t,s){s&2&&!ue(e.src,r=t[1])&&n(e,"src",r)},d(t){t&&N(e)}}}function je(i){let e,r;return e=new ze({}),{c(){S(e.$$.fragment)},m(t,s){$(e,t,s),r=!0},i(t){r||(P(e.$$.fragment,t),r=!0)},o(t){M(e.$$.fragment,t),r=!1},d(t){D(e,t)}}}function Ee(i){let e,r;return e=new Me({}),{c(){S(e.$$.fragment)},m(t,s){$(e,t,s),r=!0},i(t){r||(P(e.$$.fragment,t),r=!0)},o(t){M(e.$$.fragment,t),r=!1},d(t){D(e,t)}}}function Re(i){let e,r;return e=new be({}),{c(){S(e.$$.fragment)},m(t,s){$(e,t,s),r=!0},i(t){r||(P(e.$$.fragment,t),r=!0)},o(t){M(e.$$.fragment,t),r=!1},d(t){D(e,t)}}}function Xe(i){let e,r,t,s,h,V,j,U,v,c,d,a,p,K,y,E=x(i[5])+"",A,L,R=x(i[6])+"",F,O,k,X,l,_,m,b,ee,re;function fe(o){i[14](o)}function de(o){i[15](o)}function _e(o){i[16](o)}function pe(o){i[17](o)}let H={src:i[0],preload:"auto",autoplay:i[3],"data-testid":`${i[4]}-player`,$$slots:{default:[Ve]},$$scope:{ctx:i}};i[5]!==void 0&&(H.currentTime=i[5]),i[6]!==void 0&&(H.duration=i[6]),i[7]!==void 0&&(H.paused=i[7]),i[8]!==void 0&&(H.node=i[8]),t=new we({props:H}),Q.push(()=>W(t,"currentTime",fe)),Q.push(()=>W(t,"duration",de)),Q.push(()=>W(t,"paused",_e)),Q.push(()=>W(t,"node",pe)),t.$on("click",i[10]),t.$on("play",i[18]),t.$on("pause",i[19]),t.$on("ended",i[12]);const ie=[Re,Ee,je],z=[];function le(o,f){return o[5]===o[6]?0:o[7]?1:2}return a=le(i),p=z[a]=ie[a](i),m=new Te({}),{c(){e=C("div"),r=C("div"),S(t.$$.fragment),U=Y(),v=C("div"),c=C("div"),d=C("span"),p.c(),K=Y(),y=C("span"),A=ne(E),L=ne(" / "),F=ne(R),O=Y(),k=C("progress"),l=Y(),_=C("div"),S(m.$$.fragment),n(r,"class","svelte-lcpz3o"),oe(r,"mirror",i[2]),n(d,"role","button"),n(d,"tabindex","0"),n(d,"class","icon svelte-lcpz3o"),n(d,"aria-label","play-pause-replay-button"),n(y,"class","time svelte-lcpz3o"),k.value=X=i[5]/i[6]||0,n(k,"class","svelte-lcpz3o"),n(_,"role","button"),n(_,"tabindex","0"),n(_,"class","icon svelte-lcpz3o"),n(_,"aria-label","full-screen"),n(c,"class","inner svelte-lcpz3o"),n(v,"class","controls svelte-lcpz3o"),n(e,"class","wrap svelte-lcpz3o")},m(o,f){q(o,e,f),u(e,r),$(t,r,null),u(e,U),u(e,v),u(v,c),u(c,d),z[a].m(d,null),u(c,K),u(c,y),u(y,A),u(y,L),u(y,F),u(c,O),u(c,k),u(c,l),u(c,_),$(m,_,null),b=!0,ee||(re=[B(d,"click",i[10]),B(d,"keydown",i[10]),B(k,"mousemove",i[9]),B(k,"touchmove",se(i[9])),B(k,"click",ge(se(i[11]))),B(_,"click",i[13]),B(_,"keypress",i[13])],ee=!0)},p(o,[f]){const T={};f&1&&(T.src=o[0]),f&8&&(T.autoplay=o[3]),f&16&&(T["data-testid"]=`${o[4]}-player`),f&2097154&&(T.$$scope={dirty:f,ctx:o}),!s&&f&32&&(s=!0,T.currentTime=o[5],Z(()=>s=!1)),!h&&f&64&&(h=!0,T.duration=o[6],Z(()=>h=!1)),!V&&f&128&&(V=!0,T.paused=o[7],Z(()=>V=!1)),!j&&f&256&&(j=!0,T.node=o[8],Z(()=>j=!1)),t.$set(T),(!b||f&4)&&oe(r,"mirror",o[2]);let te=a;a=le(o),a!==te&&(me(),M(z[te],1,1,()=>{z[te]=null}),he(),p=z[a],p||(p=z[a]=ie[a](o),p.c()),P(p,1),p.m(d,null)),(!b||f&32)&&E!==(E=x(o[5])+"")&&ae(A,E),(!b||f&64)&&R!==(R=x(o[6])+"")&&ae(F,R),(!b||f&96&&X!==(X=o[5]/o[6]||0))&&(k.value=X)},i(o){b||(P(t.$$.fragment,o),P(p),P(m.$$.fragment,o),b=!0)},o(o){M(t.$$.fragment,o),M(p),M(m.$$.fragment,o),b=!1},d(o){o&&N(e),D(t),z[a].d(),D(m),ee=!1,ve(re)}}}function x(i){if(isNaN(i)||!isFinite(i))return"...";const e=Math.floor(i/60);let r=Math.floor(i%60);return r<10&&(r=`0${r}`),`${e}:${r}`}function qe(i,e,r){let{src:t}=e,{subtitle:s=null}=e,{mirror:h}=e,{autoplay:V}=e,{label:j="test"}=e;const U=ke();let v=0,c,d=!0,a;function p(l){if(!c)return;if(l.type==="click"){y(l);return}if(l.type!=="touchmove"&&!(l.buttons&1))return;const _=l.type==="touchmove"?l.touches[0].clientX:l.clientX,{left:m,right:b}=l.currentTarget.getBoundingClientRect();r(5,v=c*(_-m)/(b-m))}async function K(){document.fullscreenElement!=a&&(a.currentTime>0&&!a.paused&&!a.ended&&a.readyState>a.HAVE_CURRENT_DATA?a.pause():await a.play())}function y(l){const{left:_,right:m}=l.currentTarget.getBoundingClientRect();r(5,v=c*(l.clientX-_)/(m-_))}function E(){U("stop"),U("end")}function A(){a.requestFullscreen()}function L(l){v=l,r(5,v)}function R(l){c=l,r(6,c)}function F(l){d=l,r(7,d)}function O(l){a=l,r(8,a)}function k(l){ce.call(this,i,l)}function X(l){ce.call(this,i,l)}return i.$$set=l=>{"src"in l&&r(0,t=l.src),"subtitle"in l&&r(1,s=l.subtitle),"mirror"in l&&r(2,h=l.mirror),"autoplay"in l&&r(3,V=l.autoplay),"label"in l&&r(4,j=l.label)},[t,s,h,V,j,v,c,d,a,p,K,y,E,A,L,R,F,O,k,X]}class He extends G{constructor(e){super(),I(this,e,qe,Xe,J,{src:0,subtitle:1,mirror:2,autoplay:3,label:4})}}export{He as P,Fe as V};
//# sourceMappingURL=Player-3cd9413f.js.map
