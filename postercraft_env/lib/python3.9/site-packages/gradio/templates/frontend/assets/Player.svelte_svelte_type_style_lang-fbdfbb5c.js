import{S as M,e as P,s as S,Q as A,h as v,w as V,u as j,k as p,C as D,Z as B,ah as T,R as G,m as q,t as Q,K as R,j as Z,x as z,n as b,a9 as H,M as C,g as y,y as J,p as c,af as O,ab as U,ac as W,ad as X,b as Y,B as w,aC as x}from"./index-c99b2410.js";import{h as E,r as N,u as $}from"./file-url-1f704b8f.js";const de=a=>{let e=["B","KB","MB","GB","PB"],l=0;for(;a>1024;)a/=1024,l++;let s=e[l];return a.toFixed(1)+" "+s},oe=()=>!0;function ee(a,{autoplay:e}){async function l(){e&&await a.play()}return a.addEventListener("loadeddata",l),{destroy(){a.removeEventListener("loadeddata",l)}}}function ae(a){let e,l=a[19].message+"",s;return{c(){e=q("p"),s=Q(l),R(e,"color","red")},m(t,i){v(t,e,i),Z(e,s)},p(t,i){i&16&&l!==(l=t[19].message+"")&&z(s,l)},i:b,o:b,d(t){t&&p(e)}}}function le(a){let e,l,s,t=!1,i,d=!0,_,o,k,g;const m=a[13].default,r=H(m,a,a[12],null);function h(){cancelAnimationFrame(i),e.paused||(i=x(h),t=!0),a[14].call(e)}return{c(){e=q("video"),r&&r.c(),C(e.src,l=a[18])||y(e,"src",l),e.muted=a[5],e.playsInline=a[6],y(e,"preload",a[7]),e.autoplay=a[8],e.controls=a[9],y(e,"data-testid",s=a[11]["data-testid"]),y(e,"class","svelte-hpjiw0"),a[1]===void 0&&J(()=>a[15].call(e))},m(n,f){v(n,e,f),r&&r.m(e,null),a[17](e),o=!0,k||(g=[c(e,"loadeddata",a[10].bind(null,"loadeddata")),c(e,"click",a[10].bind(null,"click")),c(e,"play",a[10].bind(null,"play")),c(e,"pause",a[10].bind(null,"pause")),c(e,"ended",a[10].bind(null,"ended")),c(e,"mouseover",a[10].bind(null,"mouseover")),c(e,"mouseout",a[10].bind(null,"mouseout")),c(e,"focus",a[10].bind(null,"focus")),c(e,"blur",a[10].bind(null,"blur")),c(e,"timeupdate",h),c(e,"durationchange",a[15]),c(e,"play",a[16]),c(e,"pause",a[16]),O(_=ee.call(null,e,{autoplay:a[8]??!1}))],k=!0)},p(n,f){r&&r.p&&(!o||f&4096)&&U(r,m,n,n[12],o?X(m,n[12],f,null):W(n[12]),null),(!o||f&16&&!C(e.src,l=n[18]))&&y(e,"src",l),(!o||f&32)&&(e.muted=n[5]),(!o||f&64)&&(e.playsInline=n[6]),(!o||f&128)&&y(e,"preload",n[7]),(!o||f&256)&&(e.autoplay=n[8]),(!o||f&512)&&(e.controls=n[9]),(!o||f&2048&&s!==(s=n[11]["data-testid"]))&&y(e,"data-testid",s),!t&&f&1&&!isNaN(n[0])&&(e.currentTime=n[0]),t=!1,f&4&&d!==(d=n[2])&&e[d?"pause":"play"](),_&&Y(_.update)&&f&256&&_.update.call(null,{autoplay:n[8]??!1})},i(n){o||(V(r,n),o=!0)},o(n){j(r,n),o=!1},d(n){n&&p(e),r&&r.d(n),a[17](null),k=!1,w(g)}}}function ne(a){return{c:b,m:b,p:b,i:b,o:b,d:b}}function te(a){let e,l,s,t={ctx:a,current:null,token:null,hasCatch:!0,pending:ne,then:le,catch:ae,value:18,error:19,blocks:[,,,]};return E(l=N(a[4]),t),{c(){e=A(),t.block.c()},m(i,d){v(i,e,d),t.block.m(i,t.anchor=d),t.mount=()=>e.parentNode,t.anchor=e,s=!0},p(i,[d]){a=i,t.ctx=a,d&16&&l!==(l=N(a[4]))&&E(l,t)||$(t,a,d)},i(i){s||(V(t.block),s=!0)},o(i){for(let d=0;d<3;d+=1){const _=t.blocks[d];j(_)}s=!1},d(i){i&&p(e),t.block.d(i),t.token=null,t=null}}}function ue(a,e,l){let{$$slots:s={},$$scope:t}=e,{src:i=void 0}=e,{muted:d=void 0}=e,{playsinline:_=void 0}=e,{preload:o=void 0}=e,{autoplay:k=void 0}=e,{controls:g=void 0}=e,{currentTime:m=void 0}=e,{duration:r=void 0}=e,{paused:h=void 0}=e,{node:n=void 0}=e;const f=D();function F(){m=this.currentTime,l(0,m)}function I(){r=this.duration,l(1,r)}function K(){h=this.paused,l(2,h)}function L(u){G[u?"unshift":"push"](()=>{n=u,l(3,n)})}return a.$$set=u=>{l(11,e=B(B({},e),T(u))),"src"in u&&l(4,i=u.src),"muted"in u&&l(5,d=u.muted),"playsinline"in u&&l(6,_=u.playsinline),"preload"in u&&l(7,o=u.preload),"autoplay"in u&&l(8,k=u.autoplay),"controls"in u&&l(9,g=u.controls),"currentTime"in u&&l(0,m=u.currentTime),"duration"in u&&l(1,r=u.duration),"paused"in u&&l(2,h=u.paused),"node"in u&&l(3,n=u.node),"$$scope"in u&&l(12,t=u.$$scope)},e=T(e),[m,r,h,n,i,d,_,o,k,g,f,e,t,s,F,I,K,L]}class re extends M{constructor(e){super(),P(this,e,ue,te,S,{src:4,muted:5,playsinline:6,preload:7,autoplay:8,controls:9,currentTime:0,duration:1,paused:2,node:3})}}export{re as V,oe as a,de as p};
//# sourceMappingURL=Player.svelte_svelte_type_style_lang-fbdfbb5c.js.map
