{"version": 3, "file": "Player.svelte_svelte_type_style_lang-fbdfbb5c.js", "sources": ["../../../../js/video/shared/utils.ts", "../../../../js/video/shared/Video.svelte"], "sourcesContent": ["import type { ActionReturn } from \"svelte/action\";\n\nexport const prettyBytes = (bytes: number): string => {\n\tlet units = [\"B\", \"KB\", \"MB\", \"GB\", \"PB\"];\n\tlet i = 0;\n\twhile (bytes > 1024) {\n\t\tbytes /= 1024;\n\t\ti++;\n\t}\n\tlet unit = units[i];\n\treturn bytes.toFixed(1) + \" \" + unit;\n};\n\nexport const playable = (): boolean => {\n\t// TODO: Fix this\n\t// let video_element = document.createElement(\"video\");\n\t// let mime_type = mime.lookup(filename);\n\t// return video_element.canPlayType(mime_type) != \"\";\n\treturn true; // FIX BEFORE COMMIT - mime import causing issues\n};\n\nexport function loaded(\n\tnode: HTMLVideoElement,\n\t{ autoplay }: { autoplay: boolean }\n): ActionReturn {\n\tasync function handle_playback(): Promise<void> {\n\t\tif (!autoplay) return;\n\t\tawait node.play();\n\t}\n\n\tnode.addEventListener(\"loadeddata\", handle_playback);\n\n\treturn {\n\t\tdestroy(): void {\n\t\t\tnode.removeEventListener(\"loadeddata\", handle_playback);\n\t\t}\n\t};\n}\n", "<script lang=\"ts\">\n\timport type { HTMLVideoAttributes } from \"svelte/elements\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { loaded } from \"./utils\";\n\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let src: HTMLVideoAttributes[\"src\"] = undefined;\n\n\texport let muted: HTMLVideoAttributes[\"muted\"] = undefined;\n\texport let playsinline: HTMLVideoAttributes[\"playsinline\"] = undefined;\n\texport let preload: HTMLVideoAttributes[\"preload\"] = undefined;\n\texport let autoplay: HTMLVideoAttributes[\"autoplay\"] = undefined;\n\texport let controls: HTMLVideoAttributes[\"controls\"] = undefined;\n\n\texport let currentTime: number | undefined = undefined;\n\texport let duration: number | undefined = undefined;\n\texport let paused: boolean | undefined = undefined;\n\n\texport let node: HTMLVideoElement | undefined = undefined;\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n{#await resolve_wasm_src(src) then resolved_src}\n\t<!--\n\tThe spread operator with `$$props` or `$$restProps` can't be used here\n\tto pass props from the parent component to the <video> element\n\tbecause of its unexpected behavior: https://github.com/sveltejs/svelte/issues/7404\n\tFor example, if we add {...$$props} or {...$$restProps}, the boolean props aside it like `controls` will be compiled as string \"true\" or \"false\" on the actual DOM.\n\tThen, even when `controls` is false, the compiled DOM would be `<video controls=\"false\">` which is equivalent to `<video controls>` since the string \"false\" is even truthy.\n-->\n\t<video\n\t\tsrc={resolved_src}\n\t\t{muted}\n\t\t{playsinline}\n\t\t{preload}\n\t\t{autoplay}\n\t\t{controls}\n\t\ton:loadeddata={dispatch.bind(null, \"loadeddata\")}\n\t\ton:click={dispatch.bind(null, \"click\")}\n\t\ton:play={dispatch.bind(null, \"play\")}\n\t\ton:pause={dispatch.bind(null, \"pause\")}\n\t\ton:ended={dispatch.bind(null, \"ended\")}\n\t\ton:mouseover={dispatch.bind(null, \"mouseover\")}\n\t\ton:mouseout={dispatch.bind(null, \"mouseout\")}\n\t\ton:focus={dispatch.bind(null, \"focus\")}\n\t\ton:blur={dispatch.bind(null, \"blur\")}\n\t\tbind:currentTime\n\t\tbind:duration\n\t\tbind:paused\n\t\tbind:this={node}\n\t\tuse:loaded={{ autoplay: autoplay ?? false }}\n\t\tdata-testid={$$props[\"data-testid\"]}\n\t>\n\t\t<slot />\n\t</video>\n{:catch error}\n\t<p style=\"color: red;\">{error.message}</p>\n{/await}\n\n<style>\n\tvideo {\n\t\tposition: inherit;\n\t\tbackground-color: black;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n</style>\n"], "names": ["prettyBytes", "bytes", "units", "i", "unit", "playable", "loaded", "node", "autoplay", "handle_playback", "t_value", "ctx", "insert", "target", "p", "anchor", "dirty", "set_data", "t", "attr", "video", "video_src_value", "video_data_testid_value", "listen", "current", "handle_promise", "promise", "resolve_wasm_src", "info", "src", "$$props", "muted", "playsinline", "preload", "controls", "currentTime", "duration", "paused", "dispatch", "createEventDispatcher", "$$value"], "mappings": "mTAEa,MAAAA,GAAeC,GAA0B,CACrD,IAAIC,EAAQ,CAAC,IAAK,KAAM,KAAM,KAAM,IAAI,EACpCC,EAAI,EACR,KAAOF,EAAQ,MACLA,GAAA,KACTE,IAEG,IAAAC,EAAOF,EAAMC,CAAC,EAClB,OAAOF,EAAM,QAAQ,CAAC,EAAI,IAAMG,CACjC,EAEaC,GAAW,IAKhB,GAGD,SAASC,GACfC,EACA,CAAE,SAAAC,GACa,CACf,eAAeC,GAAiC,CAC1CD,GACL,MAAMD,EAAK,MACZ,CAEK,OAAAA,EAAA,iBAAiB,aAAcE,CAAe,EAE5C,CACN,SAAgB,CACVF,EAAA,oBAAoB,aAAcE,CAAe,CACvD,CAAA,CAEF,sBCqByBC,EAAAC,MAAM,QAAO,2DAArCC,EAAyCC,EAAAC,EAAAC,CAAA,iBAAjBC,EAAA,IAAAN,KAAAA,EAAAC,MAAM,QAAO,KAAAM,EAAAC,EAAAR,CAAA,uOAzB/BC,EAAY,EAAA,CAAA,GAAAQ,EAAAC,EAAA,MAAAC,CAAA,sFAoBJF,EAAAC,EAAA,cAAAE,EAAAX,MAAQ,aAAa,CAAA,2EArBnCC,EAwBOC,EAAAO,EAAAL,CAAA,sCAjBSQ,EAAAH,EAAA,aAAAT,EAAS,EAAA,EAAA,KAAK,KAAM,YAAY,CAAA,EACrCY,EAAAH,EAAA,QAAAT,EAAS,EAAA,EAAA,KAAK,KAAM,OAAO,CAAA,EAC5BY,EAAAH,EAAA,OAAAT,EAAS,EAAA,EAAA,KAAK,KAAM,MAAM,CAAA,EACzBY,EAAAH,EAAA,QAAAT,EAAS,EAAA,EAAA,KAAK,KAAM,OAAO,CAAA,EAC3BY,EAAAH,EAAA,QAAAT,EAAS,EAAA,EAAA,KAAK,KAAM,OAAO,CAAA,EACvBY,EAAAH,EAAA,YAAAT,EAAS,EAAA,EAAA,KAAK,KAAM,WAAW,CAAA,EAChCY,EAAAH,EAAA,WAAAT,EAAS,EAAA,EAAA,KAAK,KAAM,UAAU,CAAA,EACjCY,EAAAH,EAAA,QAAAT,EAAS,EAAA,EAAA,KAAK,KAAM,OAAO,CAAA,EAC5BY,EAAAH,EAAA,OAAAT,EAAS,EAAA,EAAA,KAAK,KAAM,MAAM,CAAA,2GAKrB,SAAUA,EAAQ,CAAA,GAAI,EAAK,CAAA,CAAA,8GAnBpCA,EAAY,EAAA,CAAA,6KAoBJ,CAAAa,GAAAR,EAAA,MAAAM,KAAAA,EAAAX,MAAQ,aAAa,+JADpB,SAAUA,EAAQ,CAAA,GAAI,EAAK,CAAA,4QA5BnC,OAAAc,EAAAC,EAAAC,EAAiBhB,EAAG,CAAA,CAAA,EAAAiB,CAAA,iIAApBZ,EAAA,IAAAU,KAAAA,EAAAC,EAAiBhB,EAAG,CAAA,CAAA,IAAAc,EAAAC,EAAAE,CAAA,mMAjBhB,CAAA,IAAAC,EAAkC,MAAS,EAAAC,EAE3C,CAAA,MAAAC,EAAsC,MAAS,EAAAD,EAC/C,CAAA,YAAAE,EAAkD,MAAS,EAAAF,EAC3D,CAAA,QAAAG,EAA0C,MAAS,EAAAH,EACnD,CAAA,SAAAtB,EAA4C,MAAS,EAAAsB,EACrD,CAAA,SAAAI,EAA4C,MAAS,EAAAJ,EAErD,CAAA,YAAAK,EAAkC,MAAS,EAAAL,EAC3C,CAAA,SAAAM,EAA+B,MAAS,EAAAN,EACxC,CAAA,OAAAO,EAA8B,MAAS,EAAAP,EAEvC,CAAA,KAAAvB,EAAqC,MAAS,EAAAuB,EAEnD,MAAAQ,EAAWC,0JA8BLhC,EAAIiC"}