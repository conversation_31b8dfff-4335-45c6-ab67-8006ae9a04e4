import{S as F,e as H,s as T,m as w,F as U,o as E,g as t,h as R,j as k,G as A,ap as z,p as v,aG as B,w as J,u as K,k as S,H as O,B as P,C as Q,an as V,t as W,x as X,R as C}from"./index-c99b2410.js";import"./Button-9c502b18.js";import{B as Y}from"./BlockTitle-ebc4bfe4.js";function Z(e){let f;return{c(){f=W(e[5])},m(n,m){R(n,f,m)},p(n,m){m&32&&X(f,n[5])},d(n){n&&S(f)}}}function y(e){let f,n,m,b,g,l,r,p,i,d,_,o,h;return b=new Y({props:{show_label:e[7],info:e[6],$$slots:{default:[Z]},$$scope:{ctx:e}}}),{c(){f=w("div"),n=w("div"),m=w("label"),U(b.$$.fragment),g=E(),l=w("input"),p=E(),i=w("input"),t(m,"for",e[10]),t(l,"aria-label",r=`number input for ${e[5]}`),t(l,"data-testid","number-input"),t(l,"type","number"),t(l,"min",e[1]),t(l,"max",e[2]),t(l,"step",e[3]),l.disabled=e[4],t(l,"class","svelte-1e6r6zc"),t(n,"class","head svelte-1e6r6zc"),t(f,"class","wrap svelte-1e6r6zc"),t(i,"type","range"),t(i,"id",e[10]),t(i,"name","cowbell"),t(i,"min",e[1]),t(i,"max",e[2]),t(i,"step",e[3]),i.disabled=e[4],t(i,"aria-label",d=`range slider for ${e[5]}`),t(i,"class","svelte-1e6r6zc")},m(a,u){R(a,f,u),k(f,n),k(n,m),A(b,m,null),k(n,g),k(n,l),z(l,e[0]),e[15](l),R(a,p,u),R(a,i,u),z(i,e[0]),e[17](i),_=!0,o||(h=[v(l,"input",e[14]),v(l,"blur",e[12]),v(l,"pointerup",e[11]),v(i,"change",e[16]),v(i,"input",e[16]),v(i,"pointerup",e[11])],o=!0)},p(a,[u]){const c={};u&128&&(c.show_label=a[7]),u&64&&(c.info=a[6]),u&4194336&&(c.$$scope={dirty:u,ctx:a}),b.$set(c),(!_||u&32&&r!==(r=`number input for ${a[5]}`))&&t(l,"aria-label",r),(!_||u&2)&&t(l,"min",a[1]),(!_||u&4)&&t(l,"max",a[2]),(!_||u&8)&&t(l,"step",a[3]),(!_||u&16)&&(l.disabled=a[4]),u&1&&B(l.value)!==a[0]&&z(l,a[0]),(!_||u&2)&&t(i,"min",a[1]),(!_||u&4)&&t(i,"max",a[2]),(!_||u&8)&&t(i,"step",a[3]),(!_||u&16)&&(i.disabled=a[4]),(!_||u&32&&d!==(d=`range slider for ${a[5]}`))&&t(i,"aria-label",d),u&1&&z(i,a[0])},i(a){_||(J(b.$$.fragment,a),_=!0)},o(a){K(b.$$.fragment,a),_=!1},d(a){a&&(S(f),S(p),S(i)),O(b),e[15](null),e[17](null),o=!1,P(h)}}}let x=0;function $(e,f,n){let{value:m=0}=f,{value_is_output:b=!1}=f,{minimum:g=0}=f,{maximum:l=100}=f,{step:r=1}=f,{disabled:p=!1}=f,{label:i}=f,{info:d=void 0}=f,{show_label:_}=f,o,h;const a=`range_id_${x++}`,u=Q();function c(){u("change",m),b||u("input")}V(()=>{n(13,b=!1),L()});function G(s){u("release",m)}function I(){u("release",m),n(0,m=Math.min(Math.max(m,g),l))}function L(){N(),o.addEventListener("input",N),h.addEventListener("input",N)}function N(){n(8,o.style.backgroundSize=(Number(o.value)-Number(o.min))/(Number(o.max)-Number(o.min))*100+"% 100%",o)}function M(){m=B(this.value),n(0,m)}function j(s){C[s?"unshift":"push"](()=>{h=s,n(9,h)})}function q(){m=B(this.value),n(0,m)}function D(s){C[s?"unshift":"push"](()=>{o=s,n(8,o)})}return e.$$set=s=>{"value"in s&&n(0,m=s.value),"value_is_output"in s&&n(13,b=s.value_is_output),"minimum"in s&&n(1,g=s.minimum),"maximum"in s&&n(2,l=s.maximum),"step"in s&&n(3,r=s.step),"disabled"in s&&n(4,p=s.disabled),"label"in s&&n(5,i=s.label),"info"in s&&n(6,d=s.info),"show_label"in s&&n(7,_=s.show_label)},e.$$.update=()=>{e.$$.dirty&1&&c()},[m,g,l,r,p,i,d,_,o,h,a,G,I,b,M,j,q,D]}class le extends F{constructor(f){super(),H(this,f,$,y,T,{value:0,value_is_output:13,minimum:1,maximum:2,step:3,disabled:4,label:5,info:6,show_label:7})}}export{le as R};
//# sourceMappingURL=Range-cfc0b42a.js.map
