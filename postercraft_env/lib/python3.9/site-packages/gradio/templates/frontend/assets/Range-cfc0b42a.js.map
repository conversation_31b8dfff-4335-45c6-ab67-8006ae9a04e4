{"version": 3, "file": "Range-cfc0b42a.js", "sources": ["../../../../js/slider/shared/Range.svelte"], "sourcesContent": ["<script context=\"module\">\n\tlet _id = 0;\n</script>\n\n<script lang=\"ts\">\n\timport { createEventDispatcher, afterUpdate } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\n\texport let value = 0;\n\texport let value_is_output = false;\n\texport let minimum = 0;\n\texport let maximum = 100;\n\texport let step = 1;\n\texport let disabled = false;\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let show_label: boolean;\n\tlet rangeInput: HTMLInputElement;\n\tlet numberInput: HTMLInputElement;\n\n\tconst id = `range_id_${_id++}`;\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: number;\n\t\tinput: undefined;\n\t\trelease: number;\n\t}>();\n\n\tfunction handle_change(): void {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t\tsetSlider();\n\t});\n\t$: value, handle_change();\n\n\tfunction handle_release(e: MouseEvent): void {\n\t\tdispatch(\"release\", value);\n\t}\n\tfunction clamp(): void {\n\t\tdispatch(\"release\", value);\n\t\tvalue = Math.min(Math.max(value, minimum), maximum);\n\t}\n\n\tfunction setSlider(): void {\n\t\tsetSliderRange();\n\t\trangeInput.addEventListener(\"input\", setSliderRange);\n\t\tnumberInput.addEventListener(\"input\", setSliderRange);\n\t}\n\tfunction setSliderRange(): void {\n\t\trangeInput.style.backgroundSize =\n\t\t\t((Number(rangeInput.value) - Number(rangeInput.min)) /\n\t\t\t\t(Number(rangeInput.max) - Number(rangeInput.min))) *\n\t\t\t\t100 +\n\t\t\t\"% 100%\";\n\t}\n</script>\n\n<div class=\"wrap\">\n\t<div class=\"head\">\n\t\t<label for={id}>\n\t\t\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\t\t</label>\n\n\t\t<input\n\t\t\taria-label={`number input for ${label}`}\n\t\t\tdata-testid=\"number-input\"\n\t\t\ttype=\"number\"\n\t\t\tbind:value\n\t\t\tbind:this={numberInput}\n\t\t\tmin={minimum}\n\t\t\tmax={maximum}\n\t\t\ton:blur={clamp}\n\t\t\t{step}\n\t\t\t{disabled}\n\t\t\ton:pointerup={handle_release}\n\t\t/>\n\t</div>\n</div>\n\n<input\n\ttype=\"range\"\n\t{id}\n\tname=\"cowbell\"\n\tbind:value\n\tbind:this={rangeInput}\n\tmin={minimum}\n\tmax={maximum}\n\t{step}\n\t{disabled}\n\ton:pointerup={handle_release}\n\taria-label={`range slider for ${label}`}\n/>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\twidth: 100%;\n\t}\n\n\t.head {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t}\n\tinput[type=\"number\"] {\n\t\tdisplay: block;\n\t\tposition: relative;\n\t\toutline: none !important;\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--input-border-color);\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t\tpadding: var(--size-2) var(--size-2);\n\t\theight: var(--size-6);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-sm);\n\t\ttext-align: center;\n\t}\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t}\n\n\tinput[type=\"number\"]:focus {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\tinput::placeholder {\n\t\tcolor: var(--input-placeholder-color);\n\t}\n\n\tinput[disabled] {\n\t\tcursor: not-allowed;\n\t}\n\n\tinput[type=\"range\"] {\n\t\t-webkit-appearance: none;\n\t\tappearance: none;\n\t\twidth: 100%;\n\t\taccent-color: var(--slider-color);\n\t\theight: 4px;\n\t\tbackground: var(--neutral-200);\n\t\tborder-radius: 5px;\n\t\tbackground-image: linear-gradient(var(--slider-color), var(--slider-color));\n\t\tbackground-size: 0% 100%;\n\t\tbackground-repeat: no-repeat;\n\t}\n\n\tinput[type=\"range\"]::-webkit-slider-thumb {\n\t\t-webkit-appearance: none;\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: solid 0.5px #ddd;\n\t\theight: 20px;\n\t\twidth: 20px;\n\t\tborder-radius: 50%;\n\t\tbackground-color: white;\n\t\tcursor: pointer;\n\t\tmargin-top: -2px;\n\t\ttransition: background-color 0.1s ease;\n\t}\n\n\tinput[type=\"range\"]::-webkit-slider-thumb:hover {\n\t\tbackground: var(--neutral-50);\n\t}\n\n\tinput[type=\"range\"]::-webkit-slider-runnable-track {\n\t\t-webkit-appearance: none;\n\t\tbox-shadow: none;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t\theight: 400%;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div1", "anchor", "append", "div0", "label_1", "input0", "input1", "_id", "value", "$$props", "value_is_output", "minimum", "maximum", "step", "disabled", "label", "info", "show_label", "rangeInput", "numberInput", "id", "dispatch", "createEventDispatcher", "handle_change", "afterUpdate", "$$invalidate", "set<PERSON><PERSON><PERSON>", "handle_release", "e", "clamp", "setSliderRange", "$$value"], "mappings": "kTAgEoCA,EAAK,CAAA,CAAA,qCAALA,EAAK,CAAA,CAAA,iQAD3BA,EAAE,EAAA,CAAA,yCAKmBA,EAAK,CAAA,GAAA,mEAKhCA,EAAO,CAAA,CAAA,YACPA,EAAO,CAAA,CAAA,2MAeTA,EAAO,CAAA,CAAA,YACPA,EAAO,CAAA,CAAA,0EAIoBA,EAAK,CAAA,GAAA,wCAjCtCC,EAoBKC,EAAAC,EAAAC,CAAA,EAnBJC,EAkBKF,EAAAG,CAAA,EAjBJD,EAEOC,EAAAC,CAAA,qBAEPF,EAYCC,EAAAE,CAAA,8BAIHP,EAYCC,EAAAO,EAAAL,CAAA,+DApBWJ,EAAK,EAAA,CAAA,kBAGAA,EAAc,EAAA,CAAA,yDAehBA,EAAc,EAAA,CAAA,oKAzBMA,EAAK,CAAA,gDAKhCA,EAAO,CAAA,CAAA,uBACPA,EAAO,CAAA,CAAA,mHAeTA,EAAO,CAAA,CAAA,uBACPA,EAAO,CAAA,CAAA,kGAIoBA,EAAK,CAAA,8KA7FjC,IAAAU,EAAM,oBAOC,GAAA,CAAA,MAAAC,EAAQ,CAAC,EAAAC,EACT,CAAA,gBAAAC,EAAkB,EAAK,EAAAD,EACvB,CAAA,QAAAE,EAAU,CAAC,EAAAF,EACX,CAAA,QAAAG,EAAU,GAAG,EAAAH,EACb,CAAA,KAAAI,EAAO,CAAC,EAAAJ,EACR,CAAA,SAAAK,EAAW,EAAK,EAAAL,GAChB,MAAAM,CAAa,EAAAN,EACb,CAAA,KAAAO,EAA2B,MAAS,EAAAP,GACpC,WAAAQ,CAAmB,EAAAR,EAC1BS,EACAC,EAEE,MAAAC,cAAiBb,MACjBc,EAAWC,aAMRC,GAAa,CACrBF,EAAS,SAAUb,CAAK,EACnBE,GACJW,EAAS,OAAO,EAGlBG,EAAW,IAAA,CACVC,EAAA,GAAAf,EAAkB,EAAK,EACvBgB,MAIQ,SAAAC,EAAeC,EAAa,CACpCP,EAAS,UAAWb,CAAK,WAEjBqB,GAAK,CACbR,EAAS,UAAWb,CAAK,EACzBiB,EAAA,EAAAjB,EAAQ,KAAK,IAAI,KAAK,IAAIA,EAAOG,CAAO,EAAGC,CAAO,CAAA,WAG1Cc,GAAS,CACjBI,IACAZ,EAAW,iBAAiB,QAASY,CAAc,EACnDX,EAAY,iBAAiB,QAASW,CAAc,WAE5CA,GAAc,CACtBL,EAAA,EAAAP,EAAW,MAAM,gBACd,OAAOA,EAAW,KAAK,EAAI,OAAOA,EAAW,GAAG,IAChD,OAAOA,EAAW,GAAG,EAAI,OAAOA,EAAW,GAAG,GAC/C,IACD,SAAQA,CAAA,+EAeGC,EAAWY,wFAgBbb,EAAUa,mWAnDXR,EAAa"}