{"version": 3, "file": "StaticColumn-8bfb5fd3.js", "sources": ["../../../../js/column/static/StaticColumn.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let scale: number | null = null;\n\texport let gap = true;\n\texport let min_width = 0;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let variant: \"default\" | \"panel\" | \"compact\" = \"default\";\n</script>\n\n<div\n\tid={elem_id}\n\tclass={elem_classes.join(\" \")}\n\tclass:gap\n\tclass:compact={variant === \"compact\"}\n\tclass:panel={variant === \"panel\"}\n\tclass:hide={!visible}\n\tstyle:flex-grow={scale}\n\tstyle:min-width=\"calc(min({min_width}px, 100%))\"\n>\n\t<slot />\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t}\n\n\tdiv > :global(*),\n\tdiv > :global(.form > *) {\n\t\twidth: var(--size-full);\n\t}\n\n\t.gap {\n\t\tgap: var(--layout-gap);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.compact > :global(*),\n\t.compact :global(.box) {\n\t\tborder-radius: 0;\n\t}\n\n\t.compact,\n\t.panel {\n\t\tborder: solid var(--panel-border-width) var(--panel-border-color);\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--panel-background-fill);\n\t\tpadding: var(--spacing-lg);\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert", "target", "anchor", "scale", "$$props", "gap", "min_width", "elem_id", "elem_classes", "visible", "variant"], "mappings": "0LAkB4BA,EAAS,CAAA,gGAPhCA,EAAO,CAAA,CAAA,kBACJA,EAAY,CAAA,EAAC,KAAK,GAAG,CAAA,EAAA,gBAAA,kBAEbC,EAAAC,EAAA,UAAAF,OAAY,SAAS,EACvBC,EAAAC,EAAA,QAAAF,OAAY,OAAO,cACnBA,EAAO,CAAA,CAAA,kBACHA,EAAK,CAAA,CAAA,6BAPvBG,EAWKC,EAAAF,EAAAG,CAAA,uHAVAL,EAAO,CAAA,CAAA,sBACJA,EAAY,CAAA,EAAC,KAAK,GAAG,CAAA,EAAA,2EAEbC,EAAAC,EAAA,UAAAF,OAAY,SAAS,cACvBC,EAAAC,EAAA,QAAAF,OAAY,OAAO,0BACnBA,EAAO,CAAA,CAAA,uBACHA,EAAK,CAAA,CAAA,0BACKA,EAAS,CAAA,sJAjBzB,CAAA,MAAAM,EAAuB,IAAI,EAAAC,EAC3B,CAAA,IAAAC,EAAM,EAAI,EAAAD,EACV,CAAA,UAAAE,EAAY,CAAC,EAAAF,EACb,CAAA,QAAAG,EAAU,EAAE,EAAAH,GACZ,aAAAI,EAAY,EAAA,EAAAJ,EACZ,CAAA,QAAAK,EAAU,EAAI,EAAAL,EACd,CAAA,QAAAM,EAA2C,SAAS,EAAAN"}