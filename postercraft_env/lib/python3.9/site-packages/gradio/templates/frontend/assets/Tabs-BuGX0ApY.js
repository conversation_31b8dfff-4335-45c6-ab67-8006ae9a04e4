import{w as B}from"./Index-WGC0_FkS.js";const{SvelteComponent:L,append:p,attr:c,component_subscribe:D,create_slot:O,destroy_block:P,detach:w,element:j,empty:C,ensure_array_like:E,flush:I,get_all_dirty_from_scope:Q,get_slot_changes:R,init:U,insert:T,listen:V,safe_not_equal:W,set_data:H,set_store_value:M,space:N,text:J,toggle_class:y,transition_in:X,transition_out:Y,update_keyed_each:Z,update_slot_base:$}=window.__gradio__svelte__internal,{setContext:x,createEventDispatcher:ee}=window.__gradio__svelte__internal;function z(t,e,n){const s=t.slice();return s[14]=e[n],s[16]=n,s}function F(t){let e;function n(a,o){return a[14].id===a[4]?le:te}let s=n(t),i=s(t);return{c(){i.c(),e=C()},m(a,o){i.m(a,o),T(a,e,o)},p(a,o){s===(s=n(a))&&i?i.p(a,o):(i.d(1),i=s(a),i&&(i.c(),i.m(e.parentNode,e)))},d(a){a&&w(e),i.d(a)}}}function te(t){let e,n=t[14].name+"",s,i,a,o,r,f,h,m;function d(){return t[12](t[14],t[16])}return{c(){e=j("button"),s=J(n),i=N(),c(e,"role","tab"),c(e,"aria-selected",!1),c(e,"aria-controls",a=t[14].elem_id),e.disabled=o=!t[14].interactive,c(e,"aria-disabled",r=!t[14].interactive),c(e,"id",f=t[14].elem_id?t[14].elem_id+"-button":null),c(e,"class","svelte-1uw5tnk")},m(l,u){T(l,e,u),p(e,s),p(e,i),h||(m=V(e,"click",d),h=!0)},p(l,u){t=l,u&8&&n!==(n=t[14].name+"")&&H(s,n),u&8&&a!==(a=t[14].elem_id)&&c(e,"aria-controls",a),u&8&&o!==(o=!t[14].interactive)&&(e.disabled=o),u&8&&r!==(r=!t[14].interactive)&&c(e,"aria-disabled",r),u&8&&f!==(f=t[14].elem_id?t[14].elem_id+"-button":null)&&c(e,"id",f)},d(l){l&&w(e),h=!1,m()}}}function le(t){let e,n=t[14].name+"",s,i,a,o;return{c(){e=j("button"),s=J(n),i=N(),c(e,"role","tab"),c(e,"class","selected svelte-1uw5tnk"),c(e,"aria-selected",!0),c(e,"aria-controls",a=t[14].elem_id),c(e,"id",o=t[14].elem_id?t[14].elem_id+"-button":null)},m(r,f){T(r,e,f),p(e,s),p(e,i)},p(r,f){f&8&&n!==(n=r[14].name+"")&&H(s,n),f&8&&a!==(a=r[14].elem_id)&&c(e,"aria-controls",a),f&8&&o!==(o=r[14].elem_id?r[14].elem_id+"-button":null)&&c(e,"id",o)},d(r){r&&w(e)}}}function G(t,e){let n,s,i=e[14].visible&&F(e);return{key:t,first:null,c(){n=C(),i&&i.c(),s=C(),this.first=n},m(a,o){T(a,n,o),i&&i.m(a,o),T(a,s,o)},p(a,o){e=a,e[14].visible?i?i.p(e,o):(i=F(e),i.c(),i.m(s.parentNode,s)):i&&(i.d(1),i=null)},d(a){a&&(w(n),w(s)),i&&i.d(a)}}}function ie(t){let e,n,s=[],i=new Map,a,o,r,f=E(t[3]);const h=l=>l[14].id;for(let l=0;l<f.length;l+=1){let u=z(t,f,l),v=h(u);i.set(v,s[l]=G(v,u))}const m=t[11].default,d=O(m,t,t[10],null);return{c(){e=j("div"),n=j("div");for(let l=0;l<s.length;l+=1)s[l].c();a=N(),d&&d.c(),c(n,"class","tab-nav scroll-hide svelte-1uw5tnk"),c(n,"role","tablist"),c(e,"class",o="tabs "+t[2].join(" ")+" svelte-1uw5tnk"),c(e,"id",t[1]),y(e,"hide",!t[0])},m(l,u){T(l,e,u),p(e,n);for(let v=0;v<s.length;v+=1)s[v]&&s[v].m(n,null);p(e,a),d&&d.m(e,null),r=!0},p(l,[u]){u&408&&(f=E(l[3]),s=Z(s,u,h,1,l,f,i,n,P,G,null,z)),d&&d.p&&(!r||u&1024)&&$(d,m,l,l[10],r?R(m,l[10],u,null):Q(l[10]),null),(!r||u&4&&o!==(o="tabs "+l[2].join(" ")+" svelte-1uw5tnk"))&&c(e,"class",o),(!r||u&2)&&c(e,"id",l[1]),(!r||u&5)&&y(e,"hide",!l[0])},i(l){r||(X(d,l),r=!0)},o(l){Y(d,l),r=!1},d(l){l&&w(e);for(let u=0;u<s.length;u+=1)s[u].d();d&&d.d(l)}}}const ne={};function se(t,e,n){let s,i,{$$slots:a={},$$scope:o}=e,{visible:r=!0}=e,{elem_id:f="id"}=e,{elem_classes:h=[]}=e,{selected:m}=e,d=[];const l=B(!1);D(t,l,_=>n(4,i=_));const u=B(0);D(t,u,_=>n(13,s=_));const v=ee();x(ne,{register_tab:_=>{let b;return d.find(g=>g.id===_.id)?(b=d.findIndex(g=>g.id===_.id),n(3,d[b]={...d[b],..._},d)):(d.push({name:_.name,id:_.id,elem_id:_.elem_id,visible:_.visible,interactive:_.interactive}),b=d.length-1),l.update(g=>{if(g===!1&&_.visible&&_.interactive)return _.id;let S=d.find(q=>q.visible&&q.interactive);return S?S.id:g}),n(3,d),b},unregister_tab:_=>{const b=d.findIndex(k=>k.id===_.id);d.splice(b,1),l.update(k=>k===_.id?d[b]?.id||d[d.length-1]?.id:k)},selected_tab:l,selected_tab_index:u});function A(_){const b=d.find(k=>k.id===_);b&&b.interactive&&b.visible?(n(9,m=_),M(l,i=_,i),M(u,s=d.findIndex(k=>k.id===_),s),v("change")):console.warn("Attempted to select a non-interactive or hidden tab.")}const K=(_,b)=>{A(_.id),v("select",{value:_.name,index:b})};return t.$$set=_=>{"visible"in _&&n(0,r=_.visible),"elem_id"in _&&n(1,f=_.elem_id),"elem_classes"in _&&n(2,h=_.elem_classes),"selected"in _&&n(9,m=_.selected),"$$scope"in _&&n(10,o=_.$$scope)},t.$$.update=()=>{t.$$.dirty&520&&m!==null&&A(m)},[r,f,h,d,i,l,u,v,A,m,o,a,K]}class ae extends L{constructor(e){super(),U(this,e,se,ie,W,{visible:0,elem_id:1,elem_classes:2,selected:9})}get visible(){return this.$$.ctx[0]}set visible(e){this.$$set({visible:e}),I()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),I()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),I()}get selected(){return this.$$.ctx[9]}set selected(e){this.$$set({selected:e}),I()}}export{ae as T,ne as a};
//# sourceMappingURL=Tabs-BuGX0ApY.js.map
