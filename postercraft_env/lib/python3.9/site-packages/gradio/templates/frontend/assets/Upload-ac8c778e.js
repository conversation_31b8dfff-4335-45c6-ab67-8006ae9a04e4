import{S as M,e as O,s as Q,a9 as V,m as B,o as W,g as o,N as F,h as X,j as C,p as f,q as m,ai as b,ab as Y,ac as Z,ad as x,w as $,u as ee,k as ae,B as le,C as te,E as k,R as ie}from"./index-c99b2410.js";import{d as ne}from"./Button-9c502b18.js";function re(l){let a,n,r,c,g,s,i,y,p;const w=l[15].default,d=V(w,l,l[14],null);return{c(){a=B("button"),d&&d.c(),n=W(),r=B("input"),o(r,"type","file"),o(r,"accept",l[0]),r.multiple=c=l[4]==="multiple"||void 0,o(r,"webkitdirectory",g=l[4]==="directory"||void 0),o(r,"mozdirectory",s=l[4]==="directory"||void 0),o(r,"class","svelte-izfbkb"),o(a,"class","svelte-izfbkb"),F(a,"center",l[2]),F(a,"boundedheight",l[1]),F(a,"flex",l[3])},m(t,u){X(t,a,u),d&&d.m(a,null),C(a,n),C(a,r),l[23](r),i=!0,y||(p=[f(r,"change",l[8]),f(a,"drag",m(b(l[16]))),f(a,"dragstart",m(b(l[17]))),f(a,"dragend",m(b(l[18]))),f(a,"dragover",m(b(l[19]))),f(a,"dragenter",m(b(l[20]))),f(a,"dragleave",m(b(l[21]))),f(a,"drop",m(b(l[22]))),f(a,"click",l[7]),f(a,"drop",l[9]),f(a,"dragenter",l[6]),f(a,"dragleave",l[6])],y=!0)},p(t,[u]){d&&d.p&&(!i||u&16384)&&Y(d,w,t,t[14],i?x(w,t[14],u,null):Z(t[14]),null),(!i||u&1)&&o(r,"accept",t[0]),(!i||u&16&&c!==(c=t[4]==="multiple"||void 0))&&(r.multiple=c),(!i||u&16&&g!==(g=t[4]==="directory"||void 0))&&o(r,"webkitdirectory",g),(!i||u&16&&s!==(s=t[4]==="directory"||void 0))&&o(r,"mozdirectory",s),(!i||u&4)&&F(a,"center",t[2]),(!i||u&2)&&F(a,"boundedheight",t[1]),(!i||u&8)&&F(a,"flex",t[3])},i(t){i||($(d,t),i=!0)},o(t){ee(d,t),i=!1},d(t){t&&ae(a),d&&d.d(t),l[23](null),y=!1,le(p)}}}function de(l,a,n){let{$$slots:r={},$$scope:c}=a,{filetype:g=null}=a,{include_file_metadata:s=!0}=a,{dragging:i=!1}=a,{boundedheight:y=!0}=a,{center:p=!0}=a,{flex:w=!0}=a,{file_count:d="single"}=a,{disable_click:t=!1}=a,{parse_to_data_url:u=!0}=a,z;const E=te();function R(){n(10,i=!i)}function S(){t||(n(5,z.value="",z),z.click())}async function T(e){let h=Array.from(e);if(!(!e.length||!window.FileReader)){if(d==="single"&&(h=[e[0]]),s)var q=h.map(_=>({name:_.name,size:_.size}));var U=[],v=[];u?v=await Promise.all(h.map(_=>ne(_))):v=h,s?u?U=v.map((_,D)=>({data:_,...q[D]})):U=v.map((_,D)=>({data:"",blob:_,...q[D]})):U=v,E("load",d==="single"?U[0]:U)}}async function j(e){const h=e.target;h.files&&await T(h.files)}async function A(e){n(10,i=!1),e.dataTransfer?.files&&await T(e.dataTransfer.files)}function N(e){k.call(this,l,e)}function P(e){k.call(this,l,e)}function G(e){k.call(this,l,e)}function H(e){k.call(this,l,e)}function I(e){k.call(this,l,e)}function J(e){k.call(this,l,e)}function K(e){k.call(this,l,e)}function L(e){ie[e?"unshift":"push"](()=>{z=e,n(5,z)})}return l.$$set=e=>{"filetype"in e&&n(0,g=e.filetype),"include_file_metadata"in e&&n(11,s=e.include_file_metadata),"dragging"in e&&n(10,i=e.dragging),"boundedheight"in e&&n(1,y=e.boundedheight),"center"in e&&n(2,p=e.center),"flex"in e&&n(3,w=e.flex),"file_count"in e&&n(4,d=e.file_count),"disable_click"in e&&n(12,t=e.disable_click),"parse_to_data_url"in e&&n(13,u=e.parse_to_data_url),"$$scope"in e&&n(14,c=e.$$scope)},[g,y,p,w,d,z,R,S,j,A,i,s,t,u,c,r,N,P,G,H,I,J,K,L]}class se extends M{constructor(a){super(),O(this,a,de,re,Q,{filetype:0,include_file_metadata:11,dragging:10,boundedheight:1,center:2,flex:3,file_count:4,disable_click:12,parse_to_data_url:13})}}export{se as U};
//# sourceMappingURL=Upload-ac8c778e.js.map
