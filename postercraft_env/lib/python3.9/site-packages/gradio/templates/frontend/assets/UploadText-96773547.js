import{S as y,e as S,s as U,m as g,t as d,o as x,g as h,h as j,j as s,x as f,n as b,k as q,a0 as w,a1 as C}from"./index-c99b2410.js";import"./Button-9c502b18.js";function T(e){let t,l=e[1](e[2][e[0]])+"",n,i,o,p,u=e[1]("common.or")+"",c,v,k,r=e[1]("upload_text.click_to_upload")+"",m;return{c(){t=g("div"),n=d(l),i=x(),o=g("span"),p=d("- "),c=d(u),v=d(" -"),k=x(),m=d(r),h(o,"class","or svelte-1ck5uk8"),h(t,"class","wrap svelte-1ck5uk8")},m(a,_){j(a,t,_),s(t,n),s(t,i),s(t,o),s(o,p),s(o,c),s(o,v),s(t,k),s(t,m)},p(a,[_]){_&3&&l!==(l=a[1](a[2][a[0]])+"")&&f(n,l),_&2&&u!==(u=a[1]("common.or")+"")&&f(c,u),_&2&&r!==(r=a[1]("upload_text.click_to_upload")+"")&&f(m,r)},i:b,o:b,d(a){a&&q(t)}}}function z(e,t,l){let n;w(e,C,p=>l(1,n=p));let{type:i="file"}=t;const o={image:"upload_text.drop_image",video:"upload_text.drop_video",audio:"upload_text.drop_audio",file:"upload_text.drop_file",csv:"upload_text.drop_csv"};return e.$$set=p=>{"type"in p&&l(0,i=p.type)},[i,n,o]}class D extends y{constructor(t){super(),S(this,t,z,T,U,{type:0})}}export{D as U};
//# sourceMappingURL=UploadText-96773547.js.map
