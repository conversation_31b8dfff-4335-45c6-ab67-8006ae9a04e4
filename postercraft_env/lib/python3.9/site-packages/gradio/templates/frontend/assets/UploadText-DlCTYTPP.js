import"./Index-WGC0_FkS.js";import{U as A,I as B}from"./Upload-Cp8Go_XF.js";const{SvelteComponent:D,append:m,attr:U,check_outros:E,create_component:T,destroy_component:j,detach:v,element:I,flush:g,group_outros:F,init:G,insert:b,mount_component:z,safe_not_equal:H,set_data:q,space:x,text:h,toggle_class:P,transition_in:k,transition_out:y}=window.__gradio__svelte__internal;function J(i){let e,o;return e=new A({}),{c(){T(e.$$.fragment)},m(t,n){z(e,t,n),o=!0},i(t){o||(k(e.$$.fragment,t),o=!0)},o(t){y(e.$$.fragment,t),o=!1},d(t){j(e,t)}}}function K(i){let e,o;return e=new B({}),{c(){T(e.$$.fragment)},m(t,n){z(e,t,n),o=!0},i(t){o||(k(e.$$.fragment,t),o=!0)},o(t){y(e.$$.fragment,t),o=!1},d(t){j(e,t)}}}function S(i){let e,o,t=i[1]("common.or")+"",n,d,a,u=(i[2]||i[1]("upload_text.click_to_upload"))+"",_;return{c(){e=I("span"),o=h("- "),n=h(t),d=h(" -"),a=x(),_=h(u),U(e,"class","or svelte-b0hvie")},m(l,f){b(l,e,f),m(e,o),m(e,n),m(e,d),b(l,a,f),b(l,_,f)},p(l,f){f&2&&t!==(t=l[1]("common.or")+"")&&q(n,t),f&6&&u!==(u=(l[2]||l[1]("upload_text.click_to_upload"))+"")&&q(_,u)},d(l){l&&(v(e),v(a),v(_))}}}function L(i){let e,o,t,n,d,a=i[1](i[5][i[0]]||i[5].file)+"",u,_,l;const f=[K,J],c=[];function C(r,p){return r[0]==="clipboard"?0:1}t=C(i),n=c[t]=f[t](i);let s=i[3]!=="short"&&S(i);return{c(){e=I("div"),o=I("span"),n.c(),d=x(),u=h(a),_=x(),s&&s.c(),U(o,"class","icon-wrap svelte-b0hvie"),P(o,"hovered",i[4]),U(e,"class","wrap svelte-b0hvie")},m(r,p){b(r,e,p),m(e,o),c[t].m(o,null),m(e,d),m(e,u),m(e,_),s&&s.m(e,null),l=!0},p(r,[p]){let w=t;t=C(r),t!==w&&(F(),y(c[w],1,1,()=>{c[w]=null}),E(),n=c[t],n||(n=c[t]=f[t](r),n.c()),k(n,1),n.m(o,null)),(!l||p&16)&&P(o,"hovered",r[4]),(!l||p&3)&&a!==(a=r[1](r[5][r[0]]||r[5].file)+"")&&q(u,a),r[3]!=="short"?s?s.p(r,p):(s=S(r),s.c(),s.m(e,null)):s&&(s.d(1),s=null)},i(r){l||(k(n),l=!0)},o(r){y(n),l=!1},d(r){r&&v(e),c[t].d(),s&&s.d()}}}function M(i,e,o){let{type:t="file"}=e,{i18n:n}=e,{message:d=void 0}=e,{mode:a="full"}=e,{hovered:u=!1}=e;const _={image:"upload_text.drop_image",video:"upload_text.drop_video",audio:"upload_text.drop_audio",file:"upload_text.drop_file",csv:"upload_text.drop_csv",gallery:"upload_text.drop_gallery",clipboard:"upload_text.paste_clipboard"};return i.$$set=l=>{"type"in l&&o(0,t=l.type),"i18n"in l&&o(1,n=l.i18n),"message"in l&&o(2,d=l.message),"mode"in l&&o(3,a=l.mode),"hovered"in l&&o(4,u=l.hovered)},[t,n,d,a,u,_]}class Q extends D{constructor(e){super(),G(this,e,M,L,H,{type:0,i18n:1,message:2,mode:3,hovered:4})}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),g()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),g()}get message(){return this.$$.ctx[2]}set message(e){this.$$set({message:e}),g()}get mode(){return this.$$.ctx[3]}set mode(e){this.$$set({mode:e}),g()}get hovered(){return this.$$.ctx[4]}set hovered(e){this.$$set({hovered:e}),g()}}export{Q as U};
//# sourceMappingURL=UploadText-DlCTYTPP.js.map
