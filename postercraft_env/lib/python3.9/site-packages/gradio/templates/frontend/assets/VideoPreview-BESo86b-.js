import{I as $t}from"./Index-D21IHG0c.js";import{B as St}from"./BlockLabel-BXXlQleC.js";import{E as Vt}from"./Empty-CLiqUlWX.js";import{S as Dt}from"./ShareButton-OlciWAJu.js";import{D as qt}from"./Download-DVtk-Jv3.js";import{V as kt}from"./Video-fsmLZWjA.js";import{f as _e,u as Ct}from"./Blocks-Dw_9NR1K.js";import{D as Rt}from"./DownloadLink-BgAM71ly.js";import{T as Lt,P as Mt,a as Pt}from"./Trim-UKwaW4UI.js";import{U as yt}from"./Undo-CpmTQw3B.js";import{b as Bt,t as At,V as Ht}from"./Video-ClgvEI2i.js";import{b as Xt}from"./index-D5ROCp7B.js";const{SvelteComponent:It,append:Nt,attr:O,detach:Ut,init:jt,insert:Ft,noop:Fe,safe_not_equal:Ot,svg_element:tt}=window.__gradio__svelte__internal;function zt(l){let e,n;return{c(){e=tt("svg"),n=tt("path"),O(n,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),O(e,"xmlns","http://www.w3.org/2000/svg"),O(e,"width","100%"),O(e,"height","100%"),O(e,"viewBox","0 0 24 24"),O(e,"fill","none"),O(e,"stroke","currentColor"),O(e,"stroke-width","1.5"),O(e,"stroke-linecap","round"),O(e,"stroke-linejoin","round")},m(t,i){Ft(t,e,i),Nt(e,n)},p:Fe,i:Fe,o:Fe,d(t){t&&Ut(e)}}}class Wt extends It{constructor(e){super(),jt(this,e,null,zt,Ot,{})}}const{SvelteComponent:Gt,append:de,attr:C,destroy_block:Jt,detach:Xe,element:oe,ensure_array_like:nt,flush:pe,init:Kt,insert:Ie,listen:fe,noop:Je,run_all:Qt,safe_not_equal:Yt,set_style:x,space:Oe,src_url_equal:lt,update_keyed_each:Zt}=window.__gradio__svelte__internal,{onMount:it,onDestroy:xt}=window.__gradio__svelte__internal;function ot(l,e,n){const t=l.slice();return t[20]=e[n],t[22]=n,t}function en(l){let e,n,t,i,r,o=[],d=new Map,s,_,u,h,m=nt(l[1]);const a=c=>c[22];for(let c=0;c<m.length;c+=1){let g=ot(l,m,c),w=a(g);d.set(w,o[c]=rt(w,g))}return{c(){e=oe("div"),n=oe("button"),t=Oe(),i=oe("div"),r=Oe();for(let c=0;c<o.length;c+=1)o[c].c();s=Oe(),_=oe("button"),C(n,"aria-label","start drag handle for trimming video"),C(n,"class","handle left svelte-10c4beq"),x(n,"left",l[2]+"%"),C(i,"class","opaque-layer svelte-10c4beq"),x(i,"left",l[2]+"%"),x(i,"right",100-l[3]+"%"),C(_,"aria-label","end drag handle for trimming video"),C(_,"class","handle right svelte-10c4beq"),x(_,"left",l[3]+"%"),C(e,"id","timeline"),C(e,"class","thumbnail-wrapper svelte-10c4beq")},m(c,g){Ie(c,e,g),de(e,n),de(e,t),de(e,i),de(e,r);for(let w=0;w<o.length;w+=1)o[w]&&o[w].m(e,null);de(e,s),de(e,_),u||(h=[fe(n,"mousedown",l[10]),fe(n,"blur",l[5]),fe(n,"keydown",l[11]),fe(_,"mousedown",l[12]),fe(_,"blur",l[5]),fe(_,"keydown",l[13])],u=!0)},p(c,g){g&4&&x(n,"left",c[2]+"%"),g&4&&x(i,"left",c[2]+"%"),g&8&&x(i,"right",100-c[3]+"%"),g&2&&(m=nt(c[1]),o=Zt(o,g,a,1,c,m,d,e,Jt,rt,s,ot)),g&8&&x(_,"left",c[3]+"%")},d(c){c&&Xe(e);for(let g=0;g<o.length;g+=1)o[g].d();u=!1,Qt(h)}}}function tn(l){let e;return{c(){e=oe("div"),e.innerHTML='<span aria-label="loading timeline" class="loader svelte-10c4beq"></span>',C(e,"class","load-wrap svelte-10c4beq")},m(n,t){Ie(n,e,t)},p:Je,d(n){n&&Xe(e)}}}function rt(l,e){let n,t,i;return{key:l,first:null,c(){n=oe("img"),lt(n.src,t=e[20])||C(n,"src",t),C(n,"alt",i=`frame-${e[22]}`),C(n,"draggable","false"),C(n,"class","svelte-10c4beq"),this.first=n},m(r,o){Ie(r,n,o)},p(r,o){e=r,o&2&&!lt(n.src,t=e[20])&&C(n,"src",t),o&2&&i!==(i=`frame-${e[22]}`)&&C(n,"alt",i)},d(r){r&&Xe(n)}}}function nn(l){let e;function n(r,o){return r[0]?tn:en}let t=n(l),i=t(l);return{c(){e=oe("div"),i.c(),C(e,"class","container svelte-10c4beq")},m(r,o){Ie(r,e,o),i.m(e,null)},p(r,[o]){t===(t=n(r))&&i?i.p(r,o):(i.d(1),i=t(r),i&&(i.c(),i.m(e,null)))},i:Je,o:Je,d(r){r&&Xe(e),i.d()}}}let ze=10;function ln(l,e,n){let{videoElement:t}=e,{trimmedDuration:i}=e,{dragStart:r}=e,{dragEnd:o}=e,{loadingTimeline:d}=e,s=[],_,u=0,h=100,m=null;const a=f=>{m=f},c=()=>{m=null},g=(f,E)=>{if(m){const v=document.getElementById("timeline");if(!v)return;const T=v.getBoundingClientRect();let q=(f.clientX-T.left)/T.width*100;if(E?q=m==="left"?u+E:h+E:q=(f.clientX-T.left)/T.width*100,q=Math.max(0,Math.min(q,100)),m==="left"){n(2,u=Math.min(q,h));const D=u/100*_;n(6,t.currentTime=D,t),n(8,r=D)}else if(m==="right"){n(3,h=Math.max(q,u));const D=h/100*_;n(6,t.currentTime=D,t),n(9,o=D)}const ee=u/100*_,Q=h/100*_;n(7,i=Q-ee),n(2,u),n(3,h)}},w=f=>{if(m){const E=1/_*100;f.key==="ArrowLeft"?g({clientX:0},-E):f.key==="ArrowRight"&&g({clientX:0},E)}},A=()=>{const f=document.createElement("canvas"),E=f.getContext("2d");if(!E)return;f.width=t.videoWidth,f.height=t.videoHeight,E.drawImage(t,0,0,f.width,f.height);const v=f.toDataURL("image/jpeg",.7);n(1,s=[...s,v])};it(()=>{const f=()=>{_=t.duration;const E=_/ze;let v=0;const T=()=>{A(),v++,v<ze?n(6,t.currentTime+=E,t):t.removeEventListener("seeked",T)};t.addEventListener("seeked",T),n(6,t.currentTime=0,t)};t.readyState>=1?f():t.addEventListener("loadedmetadata",f)}),xt(()=>{window.removeEventListener("mousemove",g),window.removeEventListener("mouseup",c),window.removeEventListener("keydown",w)}),it(()=>{window.addEventListener("mousemove",g),window.addEventListener("mouseup",c),window.addEventListener("keydown",w)});const y=()=>a("left"),L=f=>{(f.key==="ArrowLeft"||f.key=="ArrowRight")&&a("left")},X=()=>a("right"),F=f=>{(f.key==="ArrowLeft"||f.key=="ArrowRight")&&a("right")};return l.$$set=f=>{"videoElement"in f&&n(6,t=f.videoElement),"trimmedDuration"in f&&n(7,i=f.trimmedDuration),"dragStart"in f&&n(8,r=f.dragStart),"dragEnd"in f&&n(9,o=f.dragEnd),"loadingTimeline"in f&&n(0,d=f.loadingTimeline)},l.$$.update=()=>{l.$$.dirty&2&&n(0,d=s.length!==ze)},[d,s,u,h,a,c,t,i,r,o,y,L,X,F]}class on extends Gt{constructor(e){super(),Kt(this,e,ln,nn,Yt,{videoElement:6,trimmedDuration:7,dragStart:8,dragEnd:9,loadingTimeline:0})}get videoElement(){return this.$$.ctx[6]}set videoElement(e){this.$$set({videoElement:e}),pe()}get trimmedDuration(){return this.$$.ctx[7]}set trimmedDuration(e){this.$$set({trimmedDuration:e}),pe()}get dragStart(){return this.$$.ctx[8]}set dragStart(e){this.$$set({dragStart:e}),pe()}get dragEnd(){return this.$$.ctx[9]}set dragEnd(e){this.$$set({dragEnd:e}),pe()}get loadingTimeline(){return this.$$.ctx[0]}set loadingTimeline(e){this.$$set({loadingTimeline:e}),pe()}}const{SvelteComponent:rn,add_flush_callback:$e,append:ce,attr:B,bind:Se,binding_callbacks:Ve,check_outros:De,create_component:Ye,destroy_component:Ze,detach:G,element:J,empty:sn,flush:ne,group_outros:qe,init:an,insert:K,listen:Be,mount_component:xe,noop:Ke,run_all:un,safe_not_equal:dn,set_data:fn,space:Ce,text:cn,toggle_class:me,transition_in:P,transition_out:j}=window.__gradio__svelte__internal,{onMount:_n}=window.__gradio__svelte__internal;function st(l){let e,n,t,i,r,o,d;function s(a){l[13](a)}function _(a){l[14](a)}function u(a){l[15](a)}function h(a){l[16](a)}let m={videoElement:l[2]};return l[9]!==void 0&&(m.dragStart=l[9]),l[10]!==void 0&&(m.dragEnd=l[10]),l[7]!==void 0&&(m.trimmedDuration=l[7]),l[11]!==void 0&&(m.loadingTimeline=l[11]),n=new on({props:m}),Ve.push(()=>Se(n,"dragStart",s)),Ve.push(()=>Se(n,"dragEnd",_)),Ve.push(()=>Se(n,"trimmedDuration",u)),Ve.push(()=>Se(n,"loadingTimeline",h)),{c(){e=J("div"),Ye(n.$$.fragment),B(e,"class","timeline-wrapper svelte-sxyn79")},m(a,c){K(a,e,c),xe(n,e,null),d=!0},p(a,c){const g={};c&4&&(g.videoElement=a[2]),!t&&c&512&&(t=!0,g.dragStart=a[9],$e(()=>t=!1)),!i&&c&1024&&(i=!0,g.dragEnd=a[10],$e(()=>i=!1)),!r&&c&128&&(r=!0,g.trimmedDuration=a[7],$e(()=>r=!1)),!o&&c&2048&&(o=!0,g.loadingTimeline=a[11],$e(()=>o=!1)),n.$set(g)},i(a){d||(P(n.$$.fragment,a),d=!0)},o(a){j(n.$$.fragment,a),d=!1},d(a){a&&G(e),Ze(n)}}}function mn(l){let e;return{c(){e=J("div"),B(e,"class","svelte-sxyn79")},m(n,t){K(n,e,t)},p:Ke,d(n){n&&G(e)}}}function hn(l){let e,n=_e(l[7])+"",t;return{c(){e=J("time"),t=cn(n),B(e,"aria-label","duration of selected region in seconds"),B(e,"class","svelte-sxyn79"),me(e,"hidden",l[11])},m(i,r){K(i,e,r),ce(e,t)},p(i,r){r&128&&n!==(n=_e(i[7])+"")&&fn(t,n),r&2048&&me(e,"hidden",i[11])},d(i){i&&G(e)}}}function at(l){let e,n,t,i,r;return n=new yt({}),{c(){e=J("button"),Ye(n.$$.fragment),B(e,"class","action icon svelte-sxyn79"),e.disabled=l[1],B(e,"aria-label","Reset video to initial value")},m(o,d){K(o,e,d),xe(n,e,null),t=!0,i||(r=Be(e,"click",l[17]),i=!0)},p(o,d){(!t||d&2)&&(e.disabled=o[1])},i(o){t||(P(n.$$.fragment,o),t=!0)},o(o){j(n.$$.fragment,o),t=!1},d(o){o&&G(e),Ze(n),i=!1,r()}}}function ut(l){let e,n,t,i;const r=[bn,gn],o=[];function d(s,_){return s[0]===""?0:1}return e=d(l),n=o[e]=r[e](l),{c(){n.c(),t=sn()},m(s,_){o[e].m(s,_),K(s,t,_),i=!0},p(s,_){let u=e;e=d(s),e===u?o[e].p(s,_):(qe(),j(o[u],1,1,()=>{o[u]=null}),De(),n=o[e],n?n.p(s,_):(n=o[e]=r[e](s),n.c()),P(n,1),n.m(t.parentNode,t))},i(s){i||(P(n),i=!0)},o(s){j(n),i=!1},d(s){s&&G(t),o[e].d(s)}}}function gn(l){let e,n,t,i,r;return{c(){e=J("button"),e.textContent="Trim",n=Ce(),t=J("button"),t.textContent="Cancel",B(e,"class","text-button svelte-sxyn79"),me(e,"hidden",l[11]),B(t,"class","text-button svelte-sxyn79"),me(t,"hidden",l[11])},m(o,d){K(o,e,d),K(o,n,d),K(o,t,d),i||(r=[Be(e,"click",l[18]),Be(t,"click",l[12])],i=!0)},p(o,d){d&2048&&me(e,"hidden",o[11]),d&2048&&me(t,"hidden",o[11])},i:Ke,o:Ke,d(o){o&&(G(e),G(n),G(t)),i=!1,un(r)}}}function bn(l){let e,n,t,i,r;return n=new Lt({}),{c(){e=J("button"),Ye(n.$$.fragment),e.disabled=l[1],B(e,"class","action icon svelte-sxyn79"),B(e,"aria-label","Trim video to selection")},m(o,d){K(o,e,d),xe(n,e,null),t=!0,i||(r=Be(e,"click",l[12]),i=!0)},p(o,d){(!t||d&2)&&(e.disabled=o[1])},i(o){t||(P(n.$$.fragment,o),t=!0)},o(o){j(n.$$.fragment,o),t=!1},d(o){o&&G(e),Ze(n),i=!1,r()}}}function vn(l){let e,n,t,i,r,o,d,s=l[0]==="edit"&&st(l);function _(c,g){return c[0]==="edit"&&c[7]!==null?hn:mn}let u=_(l),h=u(l),m=l[3]&&l[0]===""&&at(l),a=l[4]&&ut(l);return{c(){e=J("div"),s&&s.c(),n=Ce(),t=J("div"),h.c(),i=Ce(),r=J("div"),m&&m.c(),o=Ce(),a&&a.c(),B(r,"class","settings-wrapper svelte-sxyn79"),B(t,"class","controls svelte-sxyn79"),B(t,"data-testid","waveform-controls"),B(e,"class","container svelte-sxyn79")},m(c,g){K(c,e,g),s&&s.m(e,null),ce(e,n),ce(e,t),h.m(t,null),ce(t,i),ce(t,r),m&&m.m(r,null),ce(r,o),a&&a.m(r,null),d=!0},p(c,[g]){c[0]==="edit"?s?(s.p(c,g),g&1&&P(s,1)):(s=st(c),s.c(),P(s,1),s.m(e,n)):s&&(qe(),j(s,1,1,()=>{s=null}),De()),u===(u=_(c))&&h?h.p(c,g):(h.d(1),h=u(c),h&&(h.c(),h.m(t,i))),c[3]&&c[0]===""?m?(m.p(c,g),g&9&&P(m,1)):(m=at(c),m.c(),P(m,1),m.m(r,o)):m&&(qe(),j(m,1,1,()=>{m=null}),De()),c[4]?a?(a.p(c,g),g&16&&P(a,1)):(a=ut(c),a.c(),P(a,1),a.m(r,null)):a&&(qe(),j(a,1,1,()=>{a=null}),De())},i(c){d||(P(s),P(m),P(a),d=!0)},o(c){j(s),j(m),j(a),d=!1},d(c){c&&G(e),s&&s.d(),h.d(),m&&m.d(),a&&a.d()}}}function pn(l,e,n){let{videoElement:t}=e,{showRedo:i=!1}=e,{interactive:r=!0}=e,{mode:o=""}=e,{handle_reset_value:d}=e,{handle_trim_video:s}=e,{processingVideo:_=!1}=e,u;_n(async()=>{n(8,u=await Bt())});let h=null,m=0,a=0,c=!1;const g=()=>{o==="edit"?(n(0,o=""),n(7,h=t.duration)):n(0,o="edit")};function w(f){m=f,n(9,m)}function A(f){a=f,n(10,a)}function y(f){h=f,n(7,h),n(0,o),n(2,t)}function L(f){c=f,n(11,c)}const X=()=>{d(),n(0,o="")},F=()=>{n(0,o=""),n(1,_=!0),At(u,m,a,t).then(f=>{s(f)}).then(()=>{n(1,_=!1)})};return l.$$set=f=>{"videoElement"in f&&n(2,t=f.videoElement),"showRedo"in f&&n(3,i=f.showRedo),"interactive"in f&&n(4,r=f.interactive),"mode"in f&&n(0,o=f.mode),"handle_reset_value"in f&&n(5,d=f.handle_reset_value),"handle_trim_video"in f&&n(6,s=f.handle_trim_video),"processingVideo"in f&&n(1,_=f.processingVideo)},l.$$.update=()=>{l.$$.dirty&133&&o==="edit"&&h===null&&t&&n(7,h=t.duration)},[o,_,t,i,r,d,s,h,u,m,a,c,g,w,A,y,L,X,F]}class wn extends rn{constructor(e){super(),an(this,e,pn,vn,dn,{videoElement:2,showRedo:3,interactive:4,mode:0,handle_reset_value:5,handle_trim_video:6,processingVideo:1})}get videoElement(){return this.$$.ctx[2]}set videoElement(e){this.$$set({videoElement:e}),ne()}get showRedo(){return this.$$.ctx[3]}set showRedo(e){this.$$set({showRedo:e}),ne()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),ne()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),ne()}get handle_reset_value(){return this.$$.ctx[5]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),ne()}get handle_trim_video(){return this.$$.ctx[6]}set handle_trim_video(e){this.$$set({handle_trim_video:e}),ne()}get processingVideo(){return this.$$.ctx[1]}set processingVideo(e){this.$$set({processingVideo:e}),ne()}}const{SvelteComponent:kn,add_flush_callback:ke,append:M,attr:S,bind:ye,binding_callbacks:Ee,bubble:We,check_outros:dt,create_component:he,destroy_component:ge,detach:Re,element:Y,empty:yn,flush:N,group_outros:ft,init:En,insert:Le,listen:le,mount_component:be,prevent_default:ct,run_all:Tn,safe_not_equal:$n,set_data:_t,space:we,src_url_equal:mt,stop_propagation:Sn,text:Ge,toggle_class:ht,transition_in:U,transition_out:W}=window.__gradio__svelte__internal,{createEventDispatcher:Vn}=window.__gradio__svelte__internal;function Dn(l){let e,n;return{c(){e=Y("track"),S(e,"kind","captions"),mt(e.src,n=l[1])||S(e,"src",n),e.default=!0},m(t,i){Le(t,e,i)},p(t,i){i[0]&2&&!mt(e.src,n=t[1])&&S(e,"src",n)},d(t){t&&Re(e)}}}function qn(l){let e,n;return e=new Mt({}),{c(){he(e.$$.fragment)},m(t,i){be(e,t,i),n=!0},i(t){n||(U(e.$$.fragment,t),n=!0)},o(t){W(e.$$.fragment,t),n=!1},d(t){ge(e,t)}}}function Cn(l){let e,n;return e=new Pt({}),{c(){he(e.$$.fragment)},m(t,i){be(e,t,i),n=!0},i(t){n||(U(e.$$.fragment,t),n=!0)},o(t){W(e.$$.fragment,t),n=!1},d(t){ge(e,t)}}}function Rn(l){let e,n;return e=new yt({}),{c(){he(e.$$.fragment)},m(t,i){be(e,t,i),n=!0},i(t){n||(U(e.$$.fragment,t),n=!0)},o(t){W(e.$$.fragment,t),n=!1},d(t){ge(e,t)}}}function gt(l){let e,n,t;function i(o){l[29](o)}let r={videoElement:l[11],showRedo:!0,handle_trim_video:l[17],handle_reset_value:l[7]};return l[12]!==void 0&&(r.processingVideo=l[12]),e=new wn({props:r}),Ee.push(()=>ye(e,"processingVideo",i)),{c(){he(e.$$.fragment)},m(o,d){be(e,o,d),t=!0},p(o,d){const s={};d[0]&2048&&(s.videoElement=o[11]),d[0]&128&&(s.handle_reset_value=o[7]),!n&&d[0]&4096&&(n=!0,s.processingVideo=o[12],ke(()=>n=!1)),e.$set(s)},i(o){t||(U(e.$$.fragment,o),t=!0)},o(o){W(e.$$.fragment,o),t=!1},d(o){ge(e,o)}}}function Ln(l){let e,n,t,i,r,o,d,s,_,u,h,m,a,c,g,w=_e(l[8])+"",A,y,L=_e(l[9])+"",X,F,f,E,v,T,q,ee,Q,D,ve,Te;function Ne(p){l[22](p)}function Ue(p){l[23](p)}function b(p){l[24](p)}function Z(p){l[25](p)}let H={src:l[0],preload:"auto",autoplay:l[3],loop:l[4],"data-testid":`${l[5]}-player`,processingVideo:l[12],$$slots:{default:[Dn]},$$scope:{ctx:l}};l[8]!==void 0&&(H.currentTime=l[8]),l[9]!==void 0&&(H.duration=l[9]),l[10]!==void 0&&(H.paused=l[10]),l[11]!==void 0&&(H.node=l[11]),t=new Ht({props:H}),Ee.push(()=>ye(t,"currentTime",Ne)),Ee.push(()=>ye(t,"duration",Ue)),Ee.push(()=>ye(t,"paused",b)),Ee.push(()=>ye(t,"node",Z)),t.$on("click",l[14]),t.$on("play",l[26]),t.$on("pause",l[27]),t.$on("ended",l[16]),t.$on("load",l[28]);const ue=[Rn,Cn,qn],te=[];function et(p,k){return p[8]===p[9]?0:p[10]?1:2}m=et(l),a=te[m]=ue[m](l),q=new Wt({});let $=l[6]&&gt(l);return{c(){e=Y("div"),n=Y("div"),he(t.$$.fragment),s=we(),_=Y("div"),u=Y("div"),h=Y("span"),a.c(),c=we(),g=Y("span"),A=Ge(w),y=Ge(" / "),X=Ge(L),F=we(),f=Y("progress"),v=we(),T=Y("div"),he(q.$$.fragment),ee=we(),$&&$.c(),Q=yn(),S(n,"class","mirror-wrap svelte-euo1cw"),ht(n,"mirror",l[2]),S(h,"role","button"),S(h,"tabindex","0"),S(h,"class","icon svelte-euo1cw"),S(h,"aria-label","play-pause-replay-button"),S(g,"class","time svelte-euo1cw"),f.value=E=l[8]/l[9]||0,S(f,"class","svelte-euo1cw"),S(T,"role","button"),S(T,"tabindex","0"),S(T,"class","icon svelte-euo1cw"),S(T,"aria-label","full-screen"),S(u,"class","inner svelte-euo1cw"),S(_,"class","controls svelte-euo1cw"),S(e,"class","wrap svelte-euo1cw")},m(p,k){Le(p,e,k),M(e,n),be(t,n,null),M(e,s),M(e,_),M(_,u),M(u,h),te[m].m(h,null),M(u,c),M(u,g),M(g,A),M(g,y),M(g,X),M(u,F),M(u,f),M(u,v),M(u,T),be(q,T,null),Le(p,ee,k),$&&$.m(p,k),Le(p,Q,k),D=!0,ve||(Te=[le(h,"click",l[14]),le(h,"keydown",l[14]),le(f,"mousemove",l[13]),le(f,"touchmove",ct(l[13])),le(f,"click",Sn(ct(l[15]))),le(T,"click",l[18]),le(T,"keypress",l[18])],ve=!0)},p(p,k){const I={};k[0]&1&&(I.src=p[0]),k[0]&8&&(I.autoplay=p[3]),k[0]&16&&(I.loop=p[4]),k[0]&32&&(I["data-testid"]=`${p[5]}-player`),k[0]&4096&&(I.processingVideo=p[12]),k[0]&2|k[1]&1&&(I.$$scope={dirty:k,ctx:p}),!i&&k[0]&256&&(i=!0,I.currentTime=p[8],ke(()=>i=!1)),!r&&k[0]&512&&(r=!0,I.duration=p[9],ke(()=>r=!1)),!o&&k[0]&1024&&(o=!0,I.paused=p[10],ke(()=>o=!1)),!d&&k[0]&2048&&(d=!0,I.node=p[11],ke(()=>d=!1)),t.$set(I),(!D||k[0]&4)&&ht(n,"mirror",p[2]);let je=m;m=et(p),m!==je&&(ft(),W(te[je],1,1,()=>{te[je]=null}),dt(),a=te[m],a||(a=te[m]=ue[m](p),a.c()),U(a,1),a.m(h,null)),(!D||k[0]&256)&&w!==(w=_e(p[8])+"")&&_t(A,w),(!D||k[0]&512)&&L!==(L=_e(p[9])+"")&&_t(X,L),(!D||k[0]&768&&E!==(E=p[8]/p[9]||0))&&(f.value=E),p[6]?$?($.p(p,k),k[0]&64&&U($,1)):($=gt(p),$.c(),U($,1),$.m(Q.parentNode,Q)):$&&(ft(),W($,1,1,()=>{$=null}),dt())},i(p){D||(U(t.$$.fragment,p),U(a),U(q.$$.fragment,p),U($),D=!0)},o(p){W(t.$$.fragment,p),W(a),W(q.$$.fragment,p),W($),D=!1},d(p){p&&(Re(e),Re(ee),Re(Q)),ge(t),te[m].d(),ge(q),$&&$.d(p),ve=!1,Tn(Te)}}}function Mn(l,e,n){let{root:t=""}=e,{src:i}=e,{subtitle:r=null}=e,{mirror:o}=e,{autoplay:d}=e,{loop:s}=e,{label:_="test"}=e,{interactive:u=!1}=e,{handle_change:h=()=>{}}=e,{handle_reset_value:m=()=>{}}=e,{upload:a}=e;const c=Vn();let g=0,w,A=!0,y,L=!1;function X(b){if(!w)return;if(b.type==="click"){f(b);return}if(b.type!=="touchmove"&&!(b.buttons&1))return;const Z=b.type==="touchmove"?b.touches[0].clientX:b.clientX,{left:H,right:ue}=b.currentTarget.getBoundingClientRect();n(8,g=w*(Z-H)/(ue-H))}async function F(){document.fullscreenElement!=y&&(y.currentTime>0&&!y.paused&&!y.ended&&y.readyState>y.HAVE_CURRENT_DATA?y.pause():await y.play())}function f(b){const{left:Z,right:H}=b.currentTarget.getBoundingClientRect();n(8,g=w*(b.clientX-Z)/(H-Z))}function E(){c("stop"),c("end")}const v=async b=>{let Z=new File([b],"video.mp4");const H=await Xt([Z]);let ue=(await a(H,t))?.filter(Boolean)[0];h(ue)};function T(){y.requestFullscreen()}function q(b){g=b,n(8,g)}function ee(b){w=b,n(9,w)}function Q(b){A=b,n(10,A)}function D(b){y=b,n(11,y)}function ve(b){We.call(this,l,b)}function Te(b){We.call(this,l,b)}function Ne(b){We.call(this,l,b)}function Ue(b){L=b,n(12,L)}return l.$$set=b=>{"root"in b&&n(19,t=b.root),"src"in b&&n(0,i=b.src),"subtitle"in b&&n(1,r=b.subtitle),"mirror"in b&&n(2,o=b.mirror),"autoplay"in b&&n(3,d=b.autoplay),"loop"in b&&n(4,s=b.loop),"label"in b&&n(5,_=b.label),"interactive"in b&&n(6,u=b.interactive),"handle_change"in b&&n(20,h=b.handle_change),"handle_reset_value"in b&&n(7,m=b.handle_reset_value),"upload"in b&&n(21,a=b.upload)},[i,r,o,d,s,_,u,m,g,w,A,y,L,X,F,f,E,v,T,t,h,a,q,ee,Q,D,ve,Te,Ne,Ue]}class Pn extends kn{constructor(e){super(),En(this,e,Mn,Ln,$n,{root:19,src:0,subtitle:1,mirror:2,autoplay:3,loop:4,label:5,interactive:6,handle_change:20,handle_reset_value:7,upload:21},null,[-1,-1])}get root(){return this.$$.ctx[19]}set root(e){this.$$set({root:e}),N()}get src(){return this.$$.ctx[0]}set src(e){this.$$set({src:e}),N()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),N()}get mirror(){return this.$$.ctx[2]}set mirror(e){this.$$set({mirror:e}),N()}get autoplay(){return this.$$.ctx[3]}set autoplay(e){this.$$set({autoplay:e}),N()}get loop(){return this.$$.ctx[4]}set loop(e){this.$$set({loop:e}),N()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),N()}get interactive(){return this.$$.ctx[6]}set interactive(e){this.$$set({interactive:e}),N()}get handle_change(){return this.$$.ctx[20]}set handle_change(e){this.$$set({handle_change:e}),N()}get handle_reset_value(){return this.$$.ctx[7]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),N()}get upload(){return this.$$.ctx[21]}set upload(e){this.$$set({upload:e}),N()}}const Bn=Pn,{SvelteComponent:An,append:Hn,attr:bt,bubble:ie,check_outros:Me,create_component:re,destroy_component:se,detach:Ae,element:Xn,empty:In,flush:z,group_outros:Pe,init:Nn,insert:He,mount_component:ae,noop:Et,safe_not_equal:Tt,space:Qe,transition_in:V,transition_out:R}=window.__gradio__svelte__internal,{createEventDispatcher:Un,afterUpdate:jn,tick:Fn}=window.__gradio__svelte__internal;function On(l){let e=l[0].url,n,t,i,r,o=vt(l),d=l[6]&&pt(l),s=l[5]&&wt(l);return{c(){o.c(),n=Qe(),t=Xn("div"),d&&d.c(),i=Qe(),s&&s.c(),bt(t,"class","icon-buttons svelte-rvdo70"),bt(t,"data-testid","download-div")},m(_,u){o.m(_,u),He(_,n,u),He(_,t,u),d&&d.m(t,null),Hn(t,i),s&&s.m(t,null),r=!0},p(_,u){u&1&&Tt(e,e=_[0].url)?(Pe(),R(o,1,1,Et),Me(),o=vt(_),o.c(),V(o,1),o.m(n.parentNode,n)):o.p(_,u),_[6]?d?(d.p(_,u),u&64&&V(d,1)):(d=pt(_),d.c(),V(d,1),d.m(t,i)):d&&(Pe(),R(d,1,1,()=>{d=null}),Me()),_[5]?s?(s.p(_,u),u&32&&V(s,1)):(s=wt(_),s.c(),V(s,1),s.m(t,null)):s&&(Pe(),R(s,1,1,()=>{s=null}),Me())},i(_){r||(V(o),V(d),V(s),r=!0)},o(_){R(o),R(d),R(s),r=!1},d(_){_&&(Ae(n),Ae(t)),o.d(_),d&&d.d(),s&&s.d()}}}function zn(l){let e,n;return e=new Vt({props:{unpadded_box:!0,size:"large",$$slots:{default:[Gn]},$$scope:{ctx:l}}}),{c(){re(e.$$.fragment)},m(t,i){ae(e,t,i),n=!0},p(t,i){const r={};i&2097152&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){n||(V(e.$$.fragment,t),n=!0)},o(t){R(e.$$.fragment,t),n=!1},d(t){se(e,t)}}}function vt(l){let e,n;return e=new Bn({props:{src:l[0].url,subtitle:l[1]?.url,autoplay:l[4],mirror:!1,label:l[2],loop:l[7],interactive:!1,upload:l[9]}}),e.$on("play",l[10]),e.$on("pause",l[11]),e.$on("stop",l[12]),e.$on("end",l[13]),e.$on("load",l[14]),{c(){re(e.$$.fragment)},m(t,i){ae(e,t,i),n=!0},p(t,i){const r={};i&1&&(r.src=t[0].url),i&2&&(r.subtitle=t[1]?.url),i&16&&(r.autoplay=t[4]),i&4&&(r.label=t[2]),i&128&&(r.loop=t[7]),i&512&&(r.upload=t[9]),e.$set(r)},i(t){n||(V(e.$$.fragment,t),n=!0)},o(t){R(e.$$.fragment,t),n=!1},d(t){se(e,t)}}}function pt(l){let e,n;return e=new Rt({props:{href:l[0].url,download:l[0].orig_name||l[0].path,$$slots:{default:[Wn]},$$scope:{ctx:l}}}),{c(){re(e.$$.fragment)},m(t,i){ae(e,t,i),n=!0},p(t,i){const r={};i&1&&(r.href=t[0].url),i&1&&(r.download=t[0].orig_name||t[0].path),i&2097152&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){n||(V(e.$$.fragment,t),n=!0)},o(t){R(e.$$.fragment,t),n=!1},d(t){se(e,t)}}}function Wn(l){let e,n;return e=new $t({props:{Icon:qt,label:"Download"}}),{c(){re(e.$$.fragment)},m(t,i){ae(e,t,i),n=!0},p:Et,i(t){n||(V(e.$$.fragment,t),n=!0)},o(t){R(e.$$.fragment,t),n=!1},d(t){se(e,t)}}}function wt(l){let e,n;return e=new Dt({props:{i18n:l[8],value:l[0],formatter:l[15]}}),e.$on("error",l[16]),e.$on("share",l[17]),{c(){re(e.$$.fragment)},m(t,i){ae(e,t,i),n=!0},p(t,i){const r={};i&256&&(r.i18n=t[8]),i&1&&(r.value=t[0]),e.$set(r)},i(t){n||(V(e.$$.fragment,t),n=!0)},o(t){R(e.$$.fragment,t),n=!1},d(t){se(e,t)}}}function Gn(l){let e,n;return e=new kt({}),{c(){re(e.$$.fragment)},m(t,i){ae(e,t,i),n=!0},i(t){n||(V(e.$$.fragment,t),n=!0)},o(t){R(e.$$.fragment,t),n=!1},d(t){se(e,t)}}}function Jn(l){let e,n,t,i,r,o;e=new St({props:{show_label:l[3],Icon:kt,label:l[2]||"Video"}});const d=[zn,On],s=[];function _(u,h){return u[0]===null||u[0].url===void 0?0:1}return t=_(l),i=s[t]=d[t](l),{c(){re(e.$$.fragment),n=Qe(),i.c(),r=In()},m(u,h){ae(e,u,h),He(u,n,h),s[t].m(u,h),He(u,r,h),o=!0},p(u,[h]){const m={};h&8&&(m.show_label=u[3]),h&4&&(m.label=u[2]||"Video"),e.$set(m);let a=t;t=_(u),t===a?s[t].p(u,h):(Pe(),R(s[a],1,1,()=>{s[a]=null}),Me(),i=s[t],i?i.p(u,h):(i=s[t]=d[t](u),i.c()),V(i,1),i.m(r.parentNode,r))},i(u){o||(V(e.$$.fragment,u),V(i),o=!0)},o(u){R(e.$$.fragment,u),R(i),o=!1},d(u){u&&(Ae(n),Ae(r)),se(e,u),s[t].d(u)}}}function Kn(l,e,n){let{value:t=null}=e,{subtitle:i=null}=e,{label:r=void 0}=e,{show_label:o=!0}=e,{autoplay:d}=e,{show_share_button:s=!0}=e,{show_download_button:_=!0}=e,{loop:u}=e,{i18n:h}=e,{upload:m}=e,a=null,c=null;const g=Un();jn(async()=>{t!==a&&i!==c&&c!==null&&(a=t,n(0,t=null),await Fn(),n(0,t=a)),a=t,c=i});function w(v){ie.call(this,l,v)}function A(v){ie.call(this,l,v)}function y(v){ie.call(this,l,v)}function L(v){ie.call(this,l,v)}function X(v){ie.call(this,l,v)}const F=async v=>v?await Ct(v.data):"";function f(v){ie.call(this,l,v)}function E(v){ie.call(this,l,v)}return l.$$set=v=>{"value"in v&&n(0,t=v.value),"subtitle"in v&&n(1,i=v.subtitle),"label"in v&&n(2,r=v.label),"show_label"in v&&n(3,o=v.show_label),"autoplay"in v&&n(4,d=v.autoplay),"show_share_button"in v&&n(5,s=v.show_share_button),"show_download_button"in v&&n(6,_=v.show_download_button),"loop"in v&&n(7,u=v.loop),"i18n"in v&&n(8,h=v.i18n),"upload"in v&&n(9,m=v.upload)},l.$$.update=()=>{l.$$.dirty&1&&t&&g("change",t)},[t,i,r,o,d,s,_,u,h,m,w,A,y,L,X,F,f,E]}class Qn extends An{constructor(e){super(),Nn(this,e,Kn,Jn,Tt,{value:0,subtitle:1,label:2,show_label:3,autoplay:4,show_share_button:5,show_download_button:6,loop:7,i18n:8,upload:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),z()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),z()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),z()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),z()}get autoplay(){return this.$$.ctx[4]}set autoplay(e){this.$$set({autoplay:e}),z()}get show_share_button(){return this.$$.ctx[5]}set show_share_button(e){this.$$set({show_share_button:e}),z()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),z()}get loop(){return this.$$.ctx[7]}set loop(e){this.$$set({loop:e}),z()}get i18n(){return this.$$.ctx[8]}set i18n(e){this.$$set({i18n:e}),z()}get upload(){return this.$$.ctx[9]}set upload(e){this.$$set({upload:e}),z()}}const ul=Object.freeze(Object.defineProperty({__proto__:null,default:Qn},Symbol.toStringTag,{value:"Module"}));export{Bn as P,Qn as V,ul as a};
//# sourceMappingURL=VideoPreview-BESo86b-.js.map
