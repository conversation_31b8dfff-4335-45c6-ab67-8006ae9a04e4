{"version": 3, "file": "file-url-1f704b8f.js", "sources": ["../../../../node_modules/.pnpm/svelte@4.0.0/node_modules/svelte/src/runtime/internal/await_block.js", "../../../../js/wasm/network/host.ts", "../../../../js/wasm/svelte/file-url.ts"], "sourcesContent": ["import { is_promise } from './utils.js';\nimport { check_outros, group_outros, transition_in, transition_out } from './transitions.js';\nimport { flush } from './scheduler.js';\nimport { get_current_component, set_current_component } from './lifecycle.js';\n\n/**\n * @template T\n * @param {Promise<T>} promise\n * @param {import('./private.js').PromiseInfo<T>} info\n * @returns {boolean}\n */\nexport function handle_promise(promise, info) {\n\tconst token = (info.token = {});\n\t/**\n\t * @param {import('./private.js').FragmentFactory} type\n\t * @param {0 | 1 | 2} index\n\t * @param {number} [key]\n\t * @param {any} [value]\n\t * @returns {void}\n\t */\n\tfunction update(type, index, key, value) {\n\t\tif (info.token !== token) return;\n\t\tinfo.resolved = value;\n\t\tlet child_ctx = info.ctx;\n\t\tif (key !== undefined) {\n\t\t\tchild_ctx = child_ctx.slice();\n\t\t\tchild_ctx[key] = value;\n\t\t}\n\t\tconst block = type && (info.current = type)(child_ctx);\n\t\tlet needs_flush = false;\n\t\tif (info.block) {\n\t\t\tif (info.blocks) {\n\t\t\t\tinfo.blocks.forEach((block, i) => {\n\t\t\t\t\tif (i !== index && block) {\n\t\t\t\t\t\tgroup_outros();\n\t\t\t\t\t\ttransition_out(block, 1, 1, () => {\n\t\t\t\t\t\t\tif (info.blocks[i] === block) {\n\t\t\t\t\t\t\t\tinfo.blocks[i] = null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tcheck_outros();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tinfo.block.d(1);\n\t\t\t}\n\t\t\tblock.c();\n\t\t\ttransition_in(block, 1);\n\t\t\tblock.m(info.mount(), info.anchor);\n\t\t\tneeds_flush = true;\n\t\t}\n\t\tinfo.block = block;\n\t\tif (info.blocks) info.blocks[index] = block;\n\t\tif (needs_flush) {\n\t\t\tflush();\n\t\t}\n\t}\n\tif (is_promise(promise)) {\n\t\tconst current_component = get_current_component();\n\t\tpromise.then(\n\t\t\t(value) => {\n\t\t\t\tset_current_component(current_component);\n\t\t\t\tupdate(info.then, 1, info.value, value);\n\t\t\t\tset_current_component(null);\n\t\t\t},\n\t\t\t(error) => {\n\t\t\t\tset_current_component(current_component);\n\t\t\t\tupdate(info.catch, 2, info.error, error);\n\t\t\t\tset_current_component(null);\n\t\t\t\tif (!info.hasCatch) {\n\t\t\t\t\tthrow error;\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t\t// if we previously had a then/catch block, destroy it\n\t\tif (info.current !== info.pending) {\n\t\t\tupdate(info.pending, 0);\n\t\t\treturn true;\n\t\t}\n\t} else {\n\t\tif (info.current !== info.then) {\n\t\t\tupdate(info.then, 1, info.value, promise);\n\t\t\treturn true;\n\t\t}\n\t\tinfo.resolved = /** @type {T} */ (promise);\n\t}\n}\n\n/** @returns {void} */\nexport function update_await_block_branch(info, ctx, dirty) {\n\tconst child_ctx = ctx.slice();\n\tconst { resolved } = info;\n\tif (info.current === info.then) {\n\t\tchild_ctx[info.value] = resolved;\n\t}\n\tif (info.current === info.catch) {\n\t\tchild_ctx[info.error] = resolved;\n\t}\n\tinfo.block.p(child_ctx, dirty);\n}\n", "export function is_self_host(url: URL): boolean {\n\treturn (\n\t\turl.host === window.location.host ||\n\t\turl.host === \"localhost:7860\" ||\n\t\turl.host === \"127.0.0.1:7860\" || // Ref: https://github.com/gradio-app/gradio/blob/v3.32.0/js/app/src/Index.svelte#L194\n\t\turl.host === \"lite.local\" // A special hostname set when the endpoint is a local file (`file:/*`). See `determine_protocol()` in `client/js/src/utils.ts`\n\t);\n}\n", "import { getWorkerProxyContext } from \"./context\";\nimport { is_self_host } from \"../network/host\";\n\ntype MediaSrc = string | undefined | null;\n\nexport async function resolve_wasm_src(src: MediaSrc): Promise<MediaSrc> {\n\tif (src == null) {\n\t\treturn src;\n\t}\n\n\tconst url = new URL(src);\n\tif (!is_self_host(url)) {\n\t\t// `src` is not accessing a local server resource, so we don't need to proxy this request to the Wasm worker.\n\t\treturn src;\n\t}\n\tif (url.protocol !== \"http:\" && url.protocol !== \"https:\") {\n\t\t// `src` can be a data URL.\n\t\treturn src;\n\t}\n\n\tconst maybeWorkerProxy = getWorkerProxyContext();\n\tif (maybeWorkerProxy == null) {\n\t\t// We are not in the Wasm env. Just use the src as is.\n\t\treturn src;\n\t}\n\n\tconst path = url.pathname;\n\treturn maybeWorkerProxy\n\t\t.httpRequest({\n\t\t\tmethod: \"GET\",\n\t\t\tpath,\n\t\t\theaders: {},\n\t\t\tquery_string: \"\"\n\t\t})\n\t\t.then((response) => {\n\t\t\tif (response.status !== 200) {\n\t\t\t\tthrow new Error(`Failed to get file ${path} from the Wasm worker.`);\n\t\t\t}\n\t\t\tconst blob = new Blob([response.body], {\n\t\t\t\ttype: response.headers[\"Content-Type\"]\n\t\t\t});\n\t\t\tconst blobUrl = URL.createObjectURL(blob);\n\t\t\treturn blobUrl;\n\t\t});\n}\n"], "names": ["handle_promise", "promise", "info", "token", "update", "type", "index", "key", "value", "child_ctx", "block", "needs_flush", "i", "group_outros", "transition_out", "check_outros", "transition_in", "flush", "is_promise", "current_component", "get_current_component", "set_current_component", "error", "update_await_block_branch", "ctx", "dirty", "resolved", "is_self_host", "url", "resolve_wasm_src", "src", "maybeWorkerProxy", "getWorkerProxyContext", "path", "response", "blob"], "mappings": "qGAWO,SAASA,EAAeC,EAASC,EAAM,CAC7C,MAAMC,EAASD,EAAK,MAAQ,CAAE,EAQ9B,SAASE,EAAOC,EAAMC,EAAOC,EAAKC,EAAO,CACxC,GAAIN,EAAK,QAAUC,EAAO,OAC1BD,EAAK,SAAWM,EAChB,IAAIC,EAAYP,EAAK,IACjBK,IAAQ,SACXE,EAAYA,EAAU,QACtBA,EAAUF,CAAG,EAAIC,GAElB,MAAME,EAAQL,IAASH,EAAK,QAAUG,GAAMI,CAAS,EACrD,IAAIE,EAAc,GACdT,EAAK,QACJA,EAAK,OACRA,EAAK,OAAO,QAAQ,CAACQ,EAAOE,IAAM,CAC7BA,IAAMN,GAASI,IAClBG,IACAC,EAAeJ,EAAO,EAAG,EAAG,IAAM,CAC7BR,EAAK,OAAOU,CAAC,IAAMF,IACtBR,EAAK,OAAOU,CAAC,EAAI,KAEzB,CAAO,EACDG,IAEN,CAAK,EAEDb,EAAK,MAAM,EAAE,CAAC,EAEfQ,EAAM,EAAC,EACPM,EAAcN,EAAO,CAAC,EACtBA,EAAM,EAAER,EAAK,MAAK,EAAIA,EAAK,MAAM,EACjCS,EAAc,IAEfT,EAAK,MAAQQ,EACTR,EAAK,SAAQA,EAAK,OAAOI,CAAK,EAAII,GAClCC,GACHM,GAED,CACD,GAAIC,EAAWjB,CAAO,EAAG,CACxB,MAAMkB,EAAoBC,IAiB1B,GAhBAnB,EAAQ,KACNO,GAAU,CACVa,EAAsBF,CAAiB,EACvCf,EAAOF,EAAK,KAAM,EAAGA,EAAK,MAAOM,CAAK,EACtCa,EAAsB,IAAI,CAC1B,EACAC,GAAU,CAIV,GAHAD,EAAsBF,CAAiB,EACvCf,EAAOF,EAAK,MAAO,EAAGA,EAAK,MAAOoB,CAAK,EACvCD,EAAsB,IAAI,EACtB,CAACnB,EAAK,SACT,MAAMoB,CAEP,CACJ,EAEMpB,EAAK,UAAYA,EAAK,QACzB,OAAAE,EAAOF,EAAK,QAAS,CAAC,EACf,OAEF,CACN,GAAIA,EAAK,UAAYA,EAAK,KACzB,OAAAE,EAAOF,EAAK,KAAM,EAAGA,EAAK,MAAOD,CAAO,EACjC,GAERC,EAAK,SAA6BD,EAEpC,CAGO,SAASsB,EAA0BrB,EAAMsB,EAAKC,EAAO,CAC3D,MAAMhB,EAAYe,EAAI,QAChB,CAAE,SAAAE,CAAU,EAAGxB,EACjBA,EAAK,UAAYA,EAAK,OACzBO,EAAUP,EAAK,KAAK,EAAIwB,GAErBxB,EAAK,UAAYA,EAAK,QACzBO,EAAUP,EAAK,KAAK,EAAIwB,GAEzBxB,EAAK,MAAM,EAAEO,EAAWgB,CAAK,CAC9B,CCnGO,SAASE,EAAaC,EAAmB,CAE9C,OAAAA,EAAI,OAAS,OAAO,SAAS,MAC7BA,EAAI,OAAS,kBACbA,EAAI,OAAS,kBACbA,EAAI,OAAS,YAEf,CCFA,eAAsBC,EAAiBC,EAAkC,CACxE,GAAIA,GAAO,KACH,OAAAA,EAGF,MAAAF,EAAM,IAAI,IAAIE,CAAG,EAKvB,GAJI,CAACH,EAAaC,CAAG,GAIjBA,EAAI,WAAa,SAAWA,EAAI,WAAa,SAEzC,OAAAE,EAGR,MAAMC,EAAmBC,IACzB,GAAID,GAAoB,KAEhB,OAAAD,EAGR,MAAMG,EAAOL,EAAI,SACjB,OAAOG,EACL,YAAY,CACZ,OAAQ,MACR,KAAAE,EACA,QAAS,CAAC,EACV,aAAc,EAAA,CACd,EACA,KAAMC,GAAa,CACf,GAAAA,EAAS,SAAW,IACjB,MAAA,IAAI,MAAM,sBAAsBD,yBAA4B,EAEnE,MAAME,EAAO,IAAI,KAAK,CAACD,EAAS,IAAI,EAAG,CACtC,KAAMA,EAAS,QAAQ,cAAc,CAAA,CACrC,EAEM,OADS,IAAI,gBAAgBC,CAAI,CACjC,CACP,CACH", "x_google_ignoreList": [0]}