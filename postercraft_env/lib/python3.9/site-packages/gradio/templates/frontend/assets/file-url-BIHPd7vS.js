import{g as i}from"./Index-D21IHG0c.js";const a="lite.local";function f(t){return t.host===window.location.host||t.host==="localhost:7860"||t.host==="127.0.0.1:7860"||t.host===a}function u(t,r){const e=r.toLowerCase();for(const[n,o]of Object.entries(t))if(n.toLowerCase()===e)return o}function c(t){const r=typeof window<"u";if(t==null||!r)return!1;const e=new URL(t,window.location.href);return!(!f(e)||e.protocol!=="http:"&&e.protocol!=="https:")}let s;async function d(t){const r=typeof window<"u";if(t==null||!r||!c(t))return t;if(s==null)try{s=i()}catch{return t}if(s==null)return t;const n=new URL(t,window.location.href).pathname;return s.httpRequest({method:"GET",path:n,headers:{},query_string:""}).then(o=>{if(o.status!==200)throw new Error(`Failed to get file ${n} from the Wasm worker.`);const l=new Blob([o.body],{type:u(o.headers,"content-type")});return URL.createObjectURL(l)})}export{u as g,d as r,c as s};
//# sourceMappingURL=file-url-BIHPd7vS.js.map
