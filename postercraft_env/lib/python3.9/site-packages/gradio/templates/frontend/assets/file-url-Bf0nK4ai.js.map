{"version": 3, "file": "file-url-Bf0nK4ai.js", "sources": ["../../../../js/wasm/network/host.ts", "../../../../js/wasm/src/http.ts", "../../../../js/wasm/svelte/file-url.ts"], "sourcesContent": ["export function is_self_host(url: URL): boolean {\n\treturn (\n\t\turl.host === window.location.host ||\n\t\turl.host === \"localhost:7860\" ||\n\t\turl.host === \"127.0.0.1:7860\" || // Ref: https://github.com/gradio-app/gradio/blob/v3.32.0/js/app/src/Index.svelte#L194\n\t\turl.host === \"lite.local\" // A special hostname set when the endpoint is a local file (`file:/*`). See `determine_protocol()` in `client/js/src/utils.ts`\n\t);\n}\n", "export interface HttpRequest {\n\tmethod: \"GET\" | \"POST\" | \"PUT\" | \"DELETE\";\n\tpath: string;\n\tquery_string: string; // This field must not contain the leading `?`, as it's directly used in the ASGI spec which requires this.\n\theaders: Record<string, string>;\n\tbody?: Uint8Array | ReadableStream<Uint8Array> | null;\n}\nexport interface HttpResponse {\n\tstatus: number;\n\theaders: Record<string, string>;\n\tbody: Uint8Array;\n}\n\n// Inspired by https://github.com/rstudio/shinylive/blob/v0.1.2/src/messageporthttp.ts\nexport function headersToASGI(\n\theaders: HttpRequest[\"headers\"]\n): [string, string][] {\n\tconst result: [string, string][] = [];\n\tfor (const [key, value] of Object.entries(headers)) {\n\t\tresult.push([key, value]);\n\t}\n\treturn result;\n}\n\nexport function uint8ArrayToString(buf: Uint8Array): string {\n\tlet result = \"\";\n\tfor (let i = 0; i < buf.length; i++) {\n\t\tresult += String.fromCharCode(buf[i]);\n\t}\n\treturn result;\n}\n\nexport function asgiHeadersToRecord(headers: any): Record<string, string> {\n\theaders = headers.map(([key, val]: [Uint8Array, Uint8Array]) => {\n\t\treturn [uint8ArrayToString(key), uint8ArrayToString(val)];\n\t});\n\treturn Object.fromEntries(headers);\n}\n\nexport function getHeaderValue(\n\theaders: HttpRequest[\"headers\"],\n\tkey: string\n): string | undefined {\n\t// The keys in `headers` are case-insensitive.\n\tconst unifiedKey = key.toLowerCase();\n\tfor (const [k, v] of Object.entries(headers)) {\n\t\tif (k.toLowerCase() === unifiedKey) {\n\t\t\treturn v;\n\t\t}\n\t}\n}\n\nexport function logHttpReqRes(\n\trequest: HttpRequest,\n\tresponse: HttpResponse\n): void {\n\tif (Math.floor(response.status / 100) !== 2) {\n\t\tlet bodyText: string;\n\t\tlet bodyJson: unknown;\n\t\ttry {\n\t\t\tbodyText = new TextDecoder().decode(response.body);\n\t\t} catch (e) {\n\t\t\tbodyText = \"(failed to decode body)\";\n\t\t}\n\t\ttry {\n\t\t\tbodyJson = JSON.parse(bodyText);\n\t\t} catch (e) {\n\t\t\tbodyJson = \"(failed to parse body as JSON)\";\n\t\t}\n\t\tconsole.error(\"Wasm HTTP error\", {\n\t\t\trequest,\n\t\t\tresponse,\n\t\t\tbodyText,\n\t\t\tbodyJson\n\t\t});\n\t}\n}\n", "import { getWorkerProxyContext } from \"./context\";\nimport { is_self_host } from \"../network/host\";\nimport { getHeaderValue } from \"../src/http\";\n\ntype MediaSrc = string | undefined | null;\n\nexport function should_proxy_wasm_src(src: MediaSrc): boolean {\n\tif (src == null) {\n\t\treturn false;\n\t}\n\n\tconst url = new URL(src, window.location.href);\n\tif (!is_self_host(url)) {\n\t\t// `src` is not accessing a local server resource, so we don't need to proxy this request to the Wasm worker.\n\t\treturn false;\n\t}\n\tif (url.protocol !== \"http:\" && url.protocol !== \"https:\") {\n\t\t// `src` can be a data URL.\n\t\treturn false;\n\t}\n\n\treturn true;\n}\n\nexport async function resolve_wasm_src(src: MediaSrc): Promise<MediaSrc> {\n\tif (src == null || !should_proxy_wasm_src(src)) {\n\t\treturn src;\n\t}\n\n\tconst maybeWorkerProxy = getWorkerProxyContext();\n\tif (maybeWorkerProxy == null) {\n\t\t// We are not in the Wasm env. Just use the src as is.\n\t\treturn src;\n\t}\n\n\tconst url = new URL(src, window.location.href);\n\tconst path = url.pathname;\n\treturn maybeWorkerProxy\n\t\t.httpRequest({\n\t\t\tmethod: \"GET\",\n\t\t\tpath,\n\t\t\theaders: {},\n\t\t\tquery_string: \"\"\n\t\t})\n\t\t.then((response) => {\n\t\t\tif (response.status !== 200) {\n\t\t\t\tthrow new Error(`Failed to get file ${path} from the Wasm worker.`);\n\t\t\t}\n\t\t\tconst blob = new Blob([response.body], {\n\t\t\t\ttype: getHeaderValue(response.headers, \"content-type\")\n\t\t\t});\n\t\t\tconst blobUrl = URL.createObjectURL(blob);\n\t\t\treturn blobUrl;\n\t\t});\n}\n"], "names": ["is_self_host", "url", "getHeaderValue", "headers", "key", "unifiedKey", "k", "v", "should_proxy_wasm_src", "src", "resolve_wasm_src", "maybeWorkerProxy", "getWorkerProxyContext", "path", "response", "blob"], "mappings": "wCAAO,SAASA,EAAaC,EAAmB,CAE9C,OAAAA,EAAI,OAAS,OAAO,SAAS,MAC7BA,EAAI,OAAS,kBACbA,EAAI,OAAS,kBACbA,EAAI,OAAS,YAEf,CCgCgB,SAAAC,EACfC,EACAC,EACqB,CAEf,MAAAC,EAAaD,EAAI,cACvB,SAAW,CAACE,EAAGC,CAAC,IAAK,OAAO,QAAQJ,CAAO,EACtC,GAAAG,EAAE,YAAY,IAAMD,EAChB,OAAAE,CAGV,CC5CO,SAASC,EAAsBC,EAAwB,CAC7D,GAAIA,GAAO,KACH,MAAA,GAGR,MAAMR,EAAM,IAAI,IAAIQ,EAAK,OAAO,SAAS,IAAI,EAK7C,MAJI,GAACT,EAAaC,CAAG,GAIjBA,EAAI,WAAa,SAAWA,EAAI,WAAa,SAMlD,CAEA,eAAsBS,EAAiBD,EAAkC,CACxE,GAAIA,GAAO,MAAQ,CAACD,EAAsBC,CAAG,EACrC,OAAAA,EAGR,MAAME,EAAmBC,IACzB,GAAID,GAAoB,KAEhB,OAAAF,EAIR,MAAMI,EADM,IAAI,IAAIJ,EAAK,OAAO,SAAS,IAAI,EAC5B,SACjB,OAAOE,EACL,YAAY,CACZ,OAAQ,MACR,KAAAE,EACA,QAAS,CAAC,EACV,aAAc,EAAA,CACd,EACA,KAAMC,GAAa,CACf,GAAAA,EAAS,SAAW,IACvB,MAAM,IAAI,MAAM,sBAAsBD,CAAI,wBAAwB,EAEnE,MAAME,EAAO,IAAI,KAAK,CAACD,EAAS,IAAI,EAAG,CACtC,KAAMZ,EAAeY,EAAS,QAAS,cAAc,CAAA,CACrD,EAEM,OADS,IAAI,gBAAgBC,CAAI,CACjC,CACP,CACH"}