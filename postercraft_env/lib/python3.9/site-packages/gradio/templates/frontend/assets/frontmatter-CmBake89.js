import{s as m,t as a,f as s,c as i,p,S as l}from"./Index-C9nqmP2b.js";import{yaml as f}from"./yaml-DsCXHVTH.js";import"./index-COY1HN2y.js";import"./svelte/svelte.js";import"./Button-8nmImwVJ.js";import"./Index-WGC0_FkS.js";import"./Check-Ck0iADAu.js";import"./Copy-ZPOKSMtK.js";import"./DownloadLink-DYBmO3sz.js";import"./file-url-Bf0nK4ai.js";import"./BlockLabel-CJsotHlk.js";import"./Empty-Vuj7-ssy.js";import"./Example-Wp-_4AVX.js";const n=/^---\s*$/m,I={defineNodes:[{name:"Frontmatter",block:!0},"FrontmatterMark"],props:[m({Frontmatter:[a.documentMeta,a.monospace],FrontmatterMark:a.processingInstruction}),s.add({Frontmatter:i,FrontmatterMark:()=>null})],wrap:p(t=>{const{parser:e}=l.define(f);return t.type.name==="Frontmatter"?{parser:e,overlay:[{from:t.from+4,to:t.to-4}]}:null}),parseBlock:[{name:"Frontmatter",before:"HorizontalRule",parse:(t,e)=>{let r;const o=new Array;if(t.lineStart===0&&n.test(e.text)){for(o.push(t.elt("FrontmatterMark",0,4));t.nextLine();)if(n.test(e.text)){r=t.lineStart+4;break}return r!==void 0&&(o.push(t.elt("FrontmatterMark",r-4,r)),t.addElement(t.elt("Frontmatter",0,r,o))),!0}return!1}}]};export{I as frontmatter};
//# sourceMappingURL=frontmatter-CmBake89.js.map
