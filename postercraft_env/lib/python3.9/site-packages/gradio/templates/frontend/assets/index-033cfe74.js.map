{"version": 3, "file": "index-033cfe74.js", "sources": ["../../../../js/button/static/StaticButton.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\n\timport Button from \"./Button.svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let mode: \"static\" | \"dynamic\" = \"dynamic\";\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let scale: number | null = null;\n\texport let icon: string | null = null;\n\texport let link: string | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tclick: never;\n\t}>;\n\texport let root = \"\";\n\texport let root_url: null | string = null;\n</script>\n\n<Button\n\t{value}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{size}\n\t{scale}\n\t{link}\n\t{icon}\n\t{min_width}\n\t{visible}\n\t{root}\n\t{root_url}\n\tdisabled={mode === \"static\"}\n\ton:click={() => gradio.dispatch(\"click\")}\n>\n\t{$_(value)}\n</Button>\n"], "names": ["t_value", "ctx", "dirty", "set_data", "t", "button_changes", "elem_id", "$$props", "elem_classes", "visible", "value", "variant", "mode", "size", "scale", "icon", "link", "min_width", "gradio", "root", "root_url"], "mappings": "4LAwCE,IAAAA,EAAAC,MAAGA,EAAK,CAAA,CAAA,EAAA,gDAARC,EAAA,OAAAF,KAAAA,EAAAC,MAAGA,EAAK,CAAA,CAAA,EAAA,KAAAE,EAAAC,EAAAJ,CAAA,sNAHC,SAAAC,OAAS,4ZAATC,EAAA,KAAAG,EAAA,SAAAJ,OAAS,qLA/BR,GAAA,CAAA,QAAAK,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAa,EAAAH,EACb,CAAA,QAAAI,EAA4C,WAAW,EAAAJ,EACvD,CAAA,KAAAK,EAA6B,SAAS,EAAAL,EACtC,CAAA,KAAAM,EAAoB,IAAI,EAAAN,EACxB,CAAA,MAAAO,EAAuB,IAAI,EAAAP,EAC3B,CAAA,KAAAQ,EAAsB,IAAI,EAAAR,EAC1B,CAAA,KAAAS,EAAsB,IAAI,EAAAT,EAC1B,CAAA,UAAAU,EAAgC,MAAS,EAAAV,GACzC,OAAAW,CAET,EAAAX,EACS,CAAA,KAAAY,EAAO,EAAE,EAAAZ,EACT,CAAA,SAAAa,EAA0B,IAAI,EAAAb,cAiBzBW,EAAO,SAAS,OAAO"}