{"version": 3, "file": "index-0522c304.js", "sources": ["../../../../js/accordion/static/Accordion.svelte", "../../../../js/accordion/static/StaticAccordion.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let label = \"\";\n\texport let open = true;\n</script>\n\n<button on:click={() => (open = !open)} class=\"label-wrap\" class:open>\n\t<span>{label}</span>\n\t<span style:transform={open ? \"rotate(0)\" : \"rotate(90deg)\"} class=\"icon\">\n\t\t▼\n\t</span>\n</button>\n<div style:display={open ? \"block\" : \"none\"}>\n\t<slot />\n</div>\n\n<style>\n\tspan {\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--section-header-text-size);\n\t}\n\t.label-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t}\n\t.label-wrap.open {\n\t\tmargin-bottom: var(--size-2);\n\t}\n\n\t.icon {\n\t\ttransition: 150ms;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport Accordion from \"./Accordion.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport Column from \"@gradio/column\";\n\n\texport let label: string;\n\texport let elem_id: string;\n\texport let elem_classes: string[];\n\texport let visible = true;\n\texport let open = true;\n\texport let loading_status: LoadingStatus;\n</script>\n\n<Block {elem_id} {elem_classes} {visible}>\n\t<StatusTracker {...loading_status} />\n\n\t<Accordion {label} {open}>\n\t\t<Column>\n\t\t\t<slot />\n\t\t</Column>\n\t</Accordion>\n</Block>\n"], "names": ["ctx", "insert", "target", "button", "anchor", "append", "span0", "span1", "div", "label", "$$props", "open", "click_handler", "$$invalidate", "elem_id", "elem_classes", "visible", "loading_status"], "mappings": "kbAMQA,EAAK,CAAA,CAAA,+IACWA,EAAI,CAAA,EAAG,YAAc,eAAe,yEAIxCA,EAAI,CAAA,EAAG,QAAU,MAAM,UAN3CC,EAKQC,EAAAC,EAAAC,CAAA,EAJPC,EAAmBF,EAAAG,CAAA,gBACnBD,EAEMF,EAAAI,CAAA,WAEPN,EAEKC,EAAAM,EAAAJ,CAAA,6EAPGJ,EAAK,CAAA,CAAA,uBACWA,EAAI,CAAA,EAAG,YAAc,eAAe,kHAIxCA,EAAI,CAAA,EAAG,QAAU,MAAM,0IAV/B,CAAA,MAAAS,EAAQ,EAAE,EAAAC,EACV,CAAA,KAAAC,EAAO,EAAI,EAAAD,EAGE,MAAAE,EAAA,IAAAC,EAAA,EAAAF,GAAQA,CAAI,uuBCYjBX,EAAc,CAAA,CAAA,4QAAdA,EAAc,CAAA,CAAA,CAAA,CAAA,4qBATtB,MAAAS,CAAa,EAAAC,GACb,QAAAI,CAAe,EAAAJ,GACf,aAAAK,CAAsB,EAAAL,EACtB,CAAA,QAAAM,EAAU,EAAI,EAAAN,EACd,CAAA,KAAAC,EAAO,EAAI,EAAAD,GACX,eAAAO,CAA6B,EAAAP"}