{"version": 3, "file": "index-0e36daec.js", "sources": ["../../../../js/number/interactive/InteractiveNumber.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport Number from \"../shared\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let label = $_(\"number.number\");\n\texport let info: string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let value = 0;\n\texport let show_label: boolean;\n\texport let minimum: number | undefined = undefined;\n\texport let maximum: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let value_is_output = false;\n\texport let step: number | null = null;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\tsubmit: never;\n\t\tblur: never;\n\t\tfocus: never;\n\t}>;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={container}\n\tallow_overflow={false}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\n\t<Number\n\t\tbind:value\n\t\tbind:value_is_output\n\t\t{label}\n\t\t{info}\n\t\t{show_label}\n\t\t{minimum}\n\t\t{maximum}\n\t\t{step}\n\t\t{container}\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\ton:submit={() => gradio.dispatch(\"submit\")}\n\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t/>\n</Block>\n"], "names": ["ctx", "label", "$_", "$$props", "info", "elem_id", "elem_classes", "visible", "container", "scale", "min_width", "value", "show_label", "minimum", "maximum", "loading_status", "value_is_output", "step", "gradio"], "mappings": "qWAyCoBA,EAAc,EAAA,CAAA,qlBAAdA,EAAc,EAAA,CAAA,CAAA,CAAA,ghBALxBA,EAAS,CAAA,iBACF,mOADPA,EAAS,CAAA,qOA5BP,MAAAC,EAAQC,EAAG,eAAe,CAAA,EAAAC,EAC1B,CAAA,KAAAC,EAA2B,MAAS,EAAAD,EACpC,CAAA,QAAAE,EAAU,EAAE,EAAAF,GACZ,aAAAG,EAAY,EAAA,EAAAH,EACZ,CAAA,QAAAI,EAAU,EAAI,EAAAJ,EACd,CAAA,UAAAK,EAAY,EAAI,EAAAL,EAChB,CAAA,MAAAM,EAAuB,IAAI,EAAAN,EAC3B,CAAA,UAAAO,EAAgC,MAAS,EAAAP,EACzC,CAAA,MAAAQ,EAAQ,CAAC,EAAAR,GACT,WAAAS,CAAmB,EAAAT,EACnB,CAAA,QAAAU,EAA8B,MAAS,EAAAV,EACvC,CAAA,QAAAW,EAA8B,MAAS,EAAAX,GACvC,eAAAY,CAA6B,EAAAZ,EAC7B,CAAA,gBAAAa,EAAkB,EAAK,EAAAb,EACvB,CAAA,KAAAc,EAAsB,IAAI,EAAAd,GAC1B,OAAAe,CAMT,EAAAf,gEAwBgBe,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,QAC1BA,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO"}