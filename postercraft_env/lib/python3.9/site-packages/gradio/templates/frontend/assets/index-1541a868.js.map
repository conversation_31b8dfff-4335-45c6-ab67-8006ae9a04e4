{"version": 3, "file": "index-1541a868.js", "sources": ["../../../../js/icons/src/Chat.svelte", "../../../../js/icons/src/Dislike.svelte", "../../../../js/icons/src/Like.svelte", "../../../../js/chatbot/utils.ts", "../../../../js/chatbot/static/Copy.svelte", "../../../../js/chatbot/static/LikeDislike.svelte", "../../../../js/chatbot/static/Pending.svelte", "../../../../js/chatbot/static/ChatBot.svelte", "../../../../js/chatbot/static/StaticChatbot.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z\"\n\t/>\n\t<path fill=\"currentColor\" d=\"M8 10h16v2H8zm0 6h10v2H8z\" />\n</svg>\n", "<script lang=\"ts\">\n\texport let actioned: boolean;\n</script>\n\n<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"15px\"\n\theight=\"15px\"\n\tviewBox=\"0 0 24 24\"\n\tfill={actioned ? \"currentColor\" : \"none\"}\n\tstroke-width=\"1.5\"\n\tcolor=\"currentColor\"\n\t><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\td=\"M16.472 3.5H4.1a.6.6 0 0 0-.6.6v9.8a.6.6 0 0 0 .6.6h2.768a2 2 0 0 1 1.715.971l2.71 4.517a1.631 1.631 0 0 0 2.961-1.308l-1.022-3.408a.6.6 0 0 1 .574-.772h4.575a2 2 0 0 0 1.93-2.526l-1.91-7A2 2 0 0 0 16.473 3.5Z\"\n\t/><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M7 14.5v-11\"\n\t/></svg\n>\n", "<script lang=\"ts\">\n\texport let actioned: boolean;\n</script>\n\n<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"15px\"\n\theight=\"15px\"\n\tviewBox=\"0 0 24 24\"\n\tfill={actioned ? \"currentColor\" : \"none\"}\n\tstroke-width=\"1.5\"\n\tcolor=\"currentColor\"\n\t><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\td=\"M16.472 20H4.1a.6.6 0 0 1-.6-.6V9.6a.6.6 0 0 1 .6-.6h2.768a2 2 0 0 0 1.715-.971l2.71-4.517a1.631 1.631 0 0 1 2.961 1.308l-1.022 3.408a.6.6 0 0 0 .574.772h4.575a2 2 0 0 1 1.93 2.526l-1.91 7A2 2 0 0 1 16.473 20Z\"\n\t/><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M7 20V9\"\n\t/></svg\n>\n", "import type { FileData } from \"@gradio/upload\";\nimport { uploadToHuggingFace } from \"@gradio/utils\";\n\nexport const format_chat_for_sharing = async (\n\tchat: [string | FileData | null, string | FileData | null][]\n): Promise<string> => {\n\tlet messages = await Promise.all(\n\t\tchat.map(async (message_pair) => {\n\t\t\treturn await Promise.all(\n\t\t\t\tmessage_pair.map(async (message, i) => {\n\t\t\t\t\tif (message === null) return \"\";\n\t\t\t\t\tlet speaker_emoji = i === 0 ? \"😃\" : \"🤖\";\n\t\t\t\t\tlet html_content = \"\";\n\n\t\t\t\t\tif (typeof message === \"string\") {\n\t\t\t\t\t\tconst regexPatterns = {\n\t\t\t\t\t\t\taudio: /<audio.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\tvideo: /<video.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\timage: /<img.*?src=\"(\\/file=.*?)\".*?\\/>|!\\[.*?\\]\\((\\/file=.*?)\\)/g\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\thtml_content = message;\n\n\t\t\t\t\t\tfor (let [_, regex] of Object.entries(regexPatterns)) {\n\t\t\t\t\t\t\tlet match;\n\n\t\t\t\t\t\t\twhile ((match = regex.exec(message)) !== null) {\n\t\t\t\t\t\t\t\tconst fileUrl = match[1] || match[2];\n\t\t\t\t\t\t\t\tconst newUrl = await uploadToHuggingFace(fileUrl, \"url\");\n\t\t\t\t\t\t\t\thtml_content = html_content.replace(fileUrl, newUrl);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst file_url = await uploadToHuggingFace(message.data, \"url\");\n\t\t\t\t\t\tif (message.mime_type?.includes(\"audio\")) {\n\t\t\t\t\t\t\thtml_content = `<audio controls src=\"${file_url}\"></audio>`;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"video\")) {\n\t\t\t\t\t\t\thtml_content = file_url;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"image\")) {\n\t\t\t\t\t\t\thtml_content = `<img src=\"${file_url}\" />`;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn `${speaker_emoji}: ${html_content}`;\n\t\t\t\t})\n\t\t\t);\n\t\t})\n\t);\n\treturn messages\n\t\t.map((message_pair) =>\n\t\t\tmessage_pair.join(\n\t\t\t\tmessage_pair[0] !== \"\" && message_pair[1] !== \"\" ? \"\\n\" : \"\"\n\t\t\t)\n\t\t)\n\t\t.join(\"\\n\");\n};\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\n\tlet copied = false;\n\texport let value: string;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t} else {\n\t\t\tconst textArea = document.createElement(\"textarea\");\n\t\t\ttextArea.value = value;\n\n\t\t\ttextArea.style.position = \"absolute\";\n\t\t\ttextArea.style.left = \"-999999px\";\n\n\t\t\tdocument.body.prepend(textArea);\n\t\t\ttextArea.select();\n\n\t\t\ttry {\n\t\t\t\tdocument.execCommand(\"copy\");\n\t\t\t\tcopy_feedback();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(error);\n\t\t\t} finally {\n\t\t\t\ttextArea.remove();\n\t\t\t}\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<button\n\ton:click={handle_copy}\n\ttitle=\"copy\"\n\taria-label={copied ? \"Copied message\" : \"Copy message\"}\n>\n\t{#if !copied}\n\t\t<Copy />\n\t{/if}\n\t{#if copied}\n\t\t<Check />\n\t{/if}\n</button>\n\n<style>\n\tbutton {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tmargin-right: 5px;\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Like } from \"@gradio/icons\";\n\timport { Dislike } from \"@gradio/icons\";\n\n\texport let action: \"like\" | \"dislike\";\n\texport let handle_action: () => void;\n\n\tlet actioned = false;\n\tlet Icon = action === \"like\" ? Like : Dislike;\n\n\tfunction action_feedback(): void {\n\t\tactioned = true;\n\t}\n</script>\n\n<button\n\ton:click={() => {\n\t\taction_feedback();\n\t\thandle_action();\n\t}}\n\ton:keydown={(e) => {\n\t\tif (e.key === \"Enter\") {\n\t\t\taction_feedback();\n\t\t\thandle_action();\n\t\t}\n\t}}\n\ttitle={action + \" message\"}\n\taria-label={actioned ? `clicked ${action}` : action}\n>\n\t<Icon {actioned} />\n</button>\n\n<style>\n\tbutton {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t\twidth: 17px;\n\t\theight: 17px;\n\t\tmargin-right: 5px;\n\t}\n\n\tbutton:hover,\n\tbutton:focus {\n\t\tcolor: var(--body-text-color);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let layout = \"bubble\";\n</script>\n\n<div\n\tclass=\"message pending\"\n\trole=\"status\"\n\taria-label=\"Loading response\"\n\taria-live=\"polite\"\n\tstyle:border-radius={layout === \"bubble\" ? \"var(--radius-xxl)\" : \"none\"}\n>\n\t<span class=\"sr-only\">Loading content</span>\n\t<div class=\"dot-flashing\" />\n\t&nbsp;\n\t<div class=\"dot-flashing\" />\n\t&nbsp;\n\t<div class=\"dot-flashing\" />\n</div>\n\n<style>\n\t.pending {\n\t\tbackground: var(--background-fill-secondary);\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\talign-self: center;\n\t\tgap: 2px;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\t.dot-flashing {\n\t\tanimation: dot-flashing 1s infinite linear alternate;\n\t\tborder-radius: 5px;\n\t\tbackground-color: var(--body-text-color);\n\t\twidth: 5px;\n\t\theight: 5px;\n\t\tcolor: var(--body-text-color);\n\t}\n\t.dot-flashing:nth-child(2) {\n\t\tanimation-delay: 0.33s;\n\t}\n\t.dot-flashing:nth-child(3) {\n\t\tanimation-delay: 0.66s;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { format_chat_for_sharing } from \"../utils\";\n\timport { copy } from \"@gradio/utils\";\n\n\timport { dequal } from \"dequal/lite\";\n\timport { beforeUpdate, afterUpdate, createEventDispatcher } from \"svelte\";\n\timport { ShareButton } from \"@gradio/atoms\";\n\timport type { SelectData, LikeData } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown/static\";\n\timport { get_fetchable_url_or_file } from \"@gradio/upload\";\n\timport Copy from \"./Copy.svelte\";\n\timport LikeDislike from \"./LikeDislike.svelte\";\n\timport Pending from \"./Pending.svelte\";\n\n\texport let value:\n\t\t| [string | FileData | null, string | FileData | null][]\n\t\t| null;\n\tlet old_value: [string | FileData | null, string | FileData | null][] | null =\n\t\tnull;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let pending_message = false;\n\texport let selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let avatar_images: [string | null, string | null] = [null, null];\n\texport let sanitize_html = true;\n\texport let bubble_full_width = true;\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\n\tlet div: HTMLDivElement;\n\tlet autoscroll: boolean;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t\tlike: LikeData;\n\t}>();\n\n\tbeforeUpdate(() => {\n\t\tautoscroll =\n\t\t\tdiv && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\n\t});\n\n\tconst scroll = (): void => {\n\t\tif (autoscroll) {\n\t\t\tdiv.scrollTo(0, div.scrollHeight);\n\t\t}\n\t};\n\tafterUpdate(() => {\n\t\tif (autoscroll) {\n\t\t\tscroll();\n\t\t\tdiv.querySelectorAll(\"img\").forEach((n) => {\n\t\t\t\tn.addEventListener(\"load\", () => {\n\t\t\t\t\tscroll();\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t});\n\n\t$: {\n\t\tif (!dequal(value, old_value)) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\");\n\t\t}\n\t}\n\n\tfunction handle_select(\n\t\ti: number,\n\t\tj: number,\n\t\tmessage: string | FileData | null\n\t): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: [i, j],\n\t\t\tvalue: message\n\t\t});\n\t}\n\n\tfunction handle_like(\n\t\ti: number,\n\t\tj: number,\n\t\tmessage: string | FileData | null,\n\t\tliked: boolean\n\t): void {\n\t\tdispatch(\"like\", {\n\t\t\tindex: [i, j],\n\t\t\tvalue: message,\n\t\t\tliked: liked\n\t\t});\n\t}\n</script>\n\n{#if show_share_button && value !== null && value.length > 0}\n\t<div class=\"share-button\">\n\t\t<ShareButton\n\t\t\ton:error\n\t\t\ton:share\n\t\t\tformatter={format_chat_for_sharing}\n\t\t\t{value}\n\t\t/>\n\t</div>\n{/if}\n\n<div\n\tclass={layout === \"bubble\" ? \"bubble-wrap\" : \"panel-wrap\"}\n\tbind:this={div}\n\trole=\"log\"\n\taria-label=\"chatbot conversation\"\n\taria-live=\"polite\"\n>\n\t<div class=\"message-wrap\" class:bubble-gap={layout === \"bubble\"} use:copy>\n\t\t{#if value !== null}\n\t\t\t{#each value as message_pair, i}\n\t\t\t\t{#each message_pair as message, j}\n\t\t\t\t\t{#if message !== null || pending_message}\n\t\t\t\t\t\t<div class=\"message-row {layout} {j == 0 ? 'user-row' : 'bot-row'}\">\n\t\t\t\t\t\t\t{#if avatar_images[j] !== null}\n\t\t\t\t\t\t\t\t<div class=\"avatar-container\">\n\t\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\t\tclass=\"avatar-image\"\n\t\t\t\t\t\t\t\t\t\tsrc={get_fetchable_url_or_file(\n\t\t\t\t\t\t\t\t\t\t\tavatar_images[j],\n\t\t\t\t\t\t\t\t\t\t\troot,\n\t\t\t\t\t\t\t\t\t\t\troot_url\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\talt=\"{j == 0 ? 'user' : 'bot'} avatar\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclass=\"message {j == 0 ? 'user' : 'bot'}\"\n\t\t\t\t\t\t\t\tclass:message-fit={layout === \"bubble\" && !bubble_full_width}\n\t\t\t\t\t\t\t\tclass:panel-full-width={layout === \"panel\"}\n\t\t\t\t\t\t\t\tclass:message-bubble-border={layout === \"bubble\"}\n\t\t\t\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\tdata-testid={j == 0 ? \"user\" : \"bot\"}\n\t\t\t\t\t\t\t\t\tclass:latest={i === value.length - 1}\n\t\t\t\t\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\t\t\tstyle:text-align=\"left\"\n\t\t\t\t\t\t\t\t\ton:click={() => handle_select(i, j, message)}\n\t\t\t\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\t\t\t\tif (e.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\t\t\thandle_select(i, j, message);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\t\t\t\t\t\taria-label={(j == 0 ? \"user\" : \"bot\") +\n\t\t\t\t\t\t\t\t\t\t\"'s message:' \" +\n\t\t\t\t\t\t\t\t\t\tmessage}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{#if typeof message === \"string\"}\n\t\t\t\t\t\t\t\t\t\t<Markdown\n\t\t\t\t\t\t\t\t\t\t\t{message}\n\t\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\t\ton:load={scroll}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{:else if message !== null && message.mime_type?.includes(\"audio\")}\n\t\t\t\t\t\t\t\t\t\t<audio\n\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-audio\"\n\t\t\t\t\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\t\t\t\t\tpreload=\"metadata\"\n\t\t\t\t\t\t\t\t\t\t\tsrc={message.data}\n\t\t\t\t\t\t\t\t\t\t\ttitle={message.alt_text}\n\t\t\t\t\t\t\t\t\t\t\ton:play\n\t\t\t\t\t\t\t\t\t\t\ton:pause\n\t\t\t\t\t\t\t\t\t\t\ton:ended\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{:else if message !== null && message.mime_type?.includes(\"video\")}\n\t\t\t\t\t\t\t\t\t\t<video\n\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-video\"\n\t\t\t\t\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\t\t\t\t\tsrc={message.data}\n\t\t\t\t\t\t\t\t\t\t\ttitle={message.alt_text}\n\t\t\t\t\t\t\t\t\t\t\tpreload=\"auto\"\n\t\t\t\t\t\t\t\t\t\t\ton:play\n\t\t\t\t\t\t\t\t\t\t\ton:pause\n\t\t\t\t\t\t\t\t\t\t\ton:ended\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<track kind=\"captions\" />\n\t\t\t\t\t\t\t\t\t\t</video>\n\t\t\t\t\t\t\t\t\t{:else if message !== null && message.mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-image\"\n\t\t\t\t\t\t\t\t\t\t\tsrc={message.data}\n\t\t\t\t\t\t\t\t\t\t\talt={message.alt_text}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{:else if message !== null && message.data !== null}\n\t\t\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-file\"\n\t\t\t\t\t\t\t\t\t\t\thref={message.data}\n\t\t\t\t\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\t\t\t\t\tdownload={window.__is_colab__\n\t\t\t\t\t\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t\t\t\t\t\t: message.orig_name || message.name}\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t{message.orig_name || message.name}\n\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t{:else if pending_message && j === 1}\n\t\t\t\t\t\t\t\t\t\t<Pending {layout} />\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{#if (likeable && j !== 0) || (show_copy_button && message && typeof message === \"string\")}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"message-buttons-{j == 0\n\t\t\t\t\t\t\t\t\t\t? 'user'\n\t\t\t\t\t\t\t\t\t\t: 'bot'} message-buttons-{layout} {avatar_images[j] !==\n\t\t\t\t\t\t\t\t\t\tnull && 'with-avatar'}\"\n\t\t\t\t\t\t\t\t\tclass:message-buttons-fit={layout === \"bubble\" &&\n\t\t\t\t\t\t\t\t\t\t!bubble_full_width}\n\t\t\t\t\t\t\t\t\tclass:bubble-buttons-user={layout === \"bubble\"}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{#if likeable && j == 1}\n\t\t\t\t\t\t\t\t\t\t<LikeDislike\n\t\t\t\t\t\t\t\t\t\t\taction=\"like\"\n\t\t\t\t\t\t\t\t\t\t\thandle_action={() => handle_like(i, j, message, true)}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t<LikeDislike\n\t\t\t\t\t\t\t\t\t\t\taction=\"dislike\"\n\t\t\t\t\t\t\t\t\t\t\thandle_action={() => handle_like(i, j, message, false)}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t{#if show_copy_button && message && typeof message === \"string\"}\n\t\t\t\t\t\t\t\t\t\t<Copy value={message} />\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{/each}\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.bubble-wrap {\n\t\tpadding: var(--block-padding);\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.panel-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t}\n\n\t.bubble-gap {\n\t\tgap: calc(var(--spacing-xxl) + var(--spacing-lg));\n\t}\n\n\t.message-wrap > div :not(.avatar-container) :global(img) {\n\t\tborder-radius: 13px;\n\t\tmax-width: 30vw;\n\t}\n\n\t.message-wrap > div :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.message-wrap :global(audio) {\n\t\twidth: 100%;\n\t}\n\n\t.message {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-self: flex-end;\n\t\ttext-align: left;\n\t\tbackground: var(--background-fill-secondary);\n\t\twidth: calc(100% - var(--spacing-xxl));\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-lg);\n\t\tline-height: var(--line-lg);\n\t\toverflow-wrap: break-word;\n\t\toverflow-x: hidden;\n\t\tpadding-right: calc(var(--spacing-xxl) + var(--spacing-md));\n\t\tpadding: calc(var(--spacing-xxl) + var(--spacing-sm));\n\t}\n\n\t.message-bubble-border {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-xxl);\n\t}\n\n\t.message-fit {\n\t\twidth: fit-content !important;\n\t}\n\n\t.panel-full-width {\n\t\tpadding: calc(var(--spacing-xxl) * 2);\n\t\twidth: 100%;\n\t}\n\t.message-markdown-disabled {\n\t\twhite-space: pre-line;\n\t}\n\n\t@media (max-width: 480px) {\n\t\t.panel-full-width {\n\t\t\tpadding: calc(var(--spacing-xxl) * 2);\n\t\t}\n\t}\n\n\t.user {\n\t\talign-self: flex-start;\n\t\tborder-bottom-right-radius: 0;\n\t\ttext-align: right;\n\t}\n\t.bot {\n\t\tborder-bottom-left-radius: 0;\n\t}\n\n\t/* Colors */\n\t.bot {\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.user {\n\t\tborder-color: var(--border-color-accent-subdued);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\t.message-row {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tposition: relative;\n\t}\n\n\t.message-row.panel.user-row {\n\t\tbackground: var(--color-accent-soft);\n\t}\n\n\t.message-row.panel.bot-row {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.message-row:last-of-type {\n\t\tmargin-bottom: var(--spacing-xxl);\n\t}\n\n\t.user-row.bubble {\n\t\tflex-direction: row;\n\t\tjustify-content: flex-end;\n\t}\n\t@media (max-width: 480px) {\n\t\t.user-row.bubble {\n\t\t\talign-self: flex-end;\n\t\t}\n\n\t\t.bot-row.bubble {\n\t\t\talign-self: flex-start;\n\t\t}\n\t\t.message {\n\t\t\twidth: auto;\n\t\t}\n\t}\n\t.avatar-container {\n\t\talign-self: flex-end;\n\t\tposition: relative;\n\t\tjustify-content: center;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t}\n\t.user-row.bubble > .avatar-container {\n\t\torder: 2;\n\t\tmargin-left: 10px;\n\t}\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-right: 10px;\n\t}\n\n\t.panel > .avatar-container {\n\t\tmargin-left: 25px;\n\t\talign-self: center;\n\t}\n\timg.avatar-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t}\n\n\t.message-buttons-user,\n\t.message-buttons-bot {\n\t\tborder-radius: var(--radius-md);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbottom: 0;\n\t\theight: var(--size-7);\n\t\talign-self: self-end;\n\t\tposition: absolute;\n\t\tbottom: -15px;\n\t\tmargin: 2px;\n\t\tpadding-left: 5px;\n\t\tz-index: 1;\n\t}\n\t.message-buttons-bot {\n\t\tleft: 10px;\n\t}\n\t.message-buttons-user {\n\t\tright: 5px;\n\t}\n\n\t.message-buttons-bot.message-buttons-bubble.with-avatar {\n\t\tleft: 50px;\n\t}\n\t.message-buttons-user.message-buttons-bubble.with-avatar {\n\t\tright: 50px;\n\t}\n\n\t.message-buttons-bubble {\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.message-buttons-panel {\n\t\tleft: unset;\n\t\tright: 0px;\n\t\ttop: 0px;\n\t}\n\n\t.share-button {\n\t\tposition: absolute;\n\t\ttop: 4px;\n\t\tright: 6px;\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\t@keyframes dot-flashing {\n\t\t0% {\n\t\t\topacity: 0.8;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\t.message-wrap .message :global(img) {\n\t\tmargin: var(--size-2);\n\t\tmax-height: 200px;\n\t}\n\t.message-wrap .message :global(a) {\n\t\tcolor: var(--color-text-link);\n\t\ttext-decoration: underline;\n\t}\n\n\t.message-wrap .bot :global(table),\n\t.message-wrap .bot :global(tr),\n\t.message-wrap .bot :global(td),\n\t.message-wrap .bot :global(th) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.message-wrap .user :global(table),\n\t.message-wrap .user :global(tr),\n\t.message-wrap .user :global(td),\n\t.message-wrap .user :global(th) {\n\t\tborder: 1px solid var(--border-color-accent);\n\t}\n\n\t/* Lists */\n\t.message-wrap :global(ol),\n\t.message-wrap :global(ul) {\n\t\tpadding-inline-start: 2em;\n\t}\n\n\t/* KaTeX */\n\t.message-wrap :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\t/* Copy button */\n\t.message-wrap :global(div[class*=\"code_wrap\"] > button) {\n\t\tposition: absolute;\n\t\ttop: var(--spacing-md);\n\t\tright: var(--spacing-md);\n\t\tz-index: 1;\n\t\tcursor: pointer;\n\t\tborder-bottom-left-radius: var(--radius-sm);\n\t\tpadding: 5px;\n\t\tpadding: var(--spacing-md);\n\t\twidth: 25px;\n\t\theight: 25px;\n\t}\n\n\t.message-wrap :global(code > button > span) {\n\t\tposition: absolute;\n\t\ttop: var(--spacing-md);\n\t\tright: var(--spacing-md);\n\t\twidth: 12px;\n\t\theight: 12px;\n\t}\n\t.message-wrap :global(.check) {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\topacity: 0;\n\t\tz-index: var(--layer-top);\n\t\ttransition: opacity 0.2s;\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-1);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.message-wrap :global(pre) {\n\t\tposition: relative;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, SelectData, LikeData } from \"@gradio/utils\";\n\n\timport ChatBot from \"./ChatBot.svelte\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Chat } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [string | FileData | null, string | FileData | null][] = [];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string;\n\texport let show_label = true;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let sanitize_html = true;\n\texport let bubble_full_width = true;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tlike: LikeData;\n\t}>;\n\texport let avatar_images: [string | null, string | null] = [null, null];\n\n\tlet _value: [string | FileData | null, string | FileData | null][];\n\n\tconst redirect_src_url = (src: string): string =>\n\t\tsrc.replace('src=\"/file', `src=\"${root}file`);\n\n\t$: _value = value\n\t\t? value.map(([user_msg, bot_msg]) => [\n\t\t\t\ttypeof user_msg === \"string\"\n\t\t\t\t\t? redirect_src_url(user_msg)\n\t\t\t\t\t: normalise_file(user_msg, root, root_url),\n\t\t\t\ttypeof bot_msg === \"string\"\n\t\t\t\t\t? redirect_src_url(bot_msg)\n\t\t\t\t\t: normalise_file(bot_msg, root, root_url),\n\t\t  ])\n\t\t: [];\n\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height = 400;\n</script>\n\n<Block\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\tpadding={false}\n\t{scale}\n\t{min_width}\n\t{height}\n\tallow_overflow={false}\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\t{...loading_status}\n\t\t\tshow_progress={loading_status.show_progress === \"hidden\"\n\t\t\t\t? \"hidden\"\n\t\t\t\t: \"minimal\"}\n\t\t/>\n\t{/if}\n\t<div class=\"wrapper\">\n\t\t{#if show_label}\n\t\t\t<BlockLabel\n\t\t\t\t{show_label}\n\t\t\t\tIcon={Chat}\n\t\t\t\tfloat={false}\n\t\t\t\tlabel={label || \"Chatbot\"}\n\t\t\t/>\n\t\t{/if}\n\t\t<ChatBot\n\t\t\t{selectable}\n\t\t\t{likeable}\n\t\t\t{show_share_button}\n\t\t\tvalue={_value}\n\t\t\t{latex_delimiters}\n\t\t\t{render_markdown}\n\t\t\tpending_message={loading_status?.status === \"pending\"}\n\t\t\t{rtl}\n\t\t\t{show_copy_button}\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:like={(e) => gradio.dispatch(\"like\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\t{avatar_images}\n\t\t\t{sanitize_html}\n\t\t\t{bubble_full_width}\n\t\t\t{line_breaks}\n\t\t\t{layout}\n\t\t\t{root_url}\n\t\t\t{root}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\talign-items: start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "ctx", "actioned", "$$props", "format_chat_for_sharing", "chat", "message_pair", "message", "i", "speaker_emoji", "html_content", "regexPatterns", "_", "regex", "match", "fileUrl", "newUrl", "uploadToHuggingFace", "file_url", "create_if_block_1", "create_if_block", "button", "copied", "value", "timer", "copy_feedback", "$$invalidate", "handle_copy", "textArea", "error", "onDestroy", "attr", "button_title_value", "current", "dirty", "action", "handle_action", "Icon", "Like", "Dislike", "action_feedback", "e", "set_style", "div3", "layout", "div_1", "each_blocks", "create_if_block_11", "create_if_block_2", "toggle_class", "div0", "div1", "div1_class_value", "src_url_equal", "img", "img_src_value", "get_fetchable_url_or_file", "t_value", "a", "a_href_value", "a_download_value", "set_data", "t", "img_alt_value", "video", "video_src_value", "video_title_value", "track", "audio", "audio_src_value", "audio_title_value", "create_if_block_4", "if_block1", "create_if_block_3", "div_1_class_value", "create_if_block_12", "null_to_empty", "old_value", "latex_delimiters", "pending_message", "selectable", "likeable", "show_share_button", "rtl", "show_copy_button", "avatar_images", "sanitize_html", "bubble_full_width", "render_markdown", "line_breaks", "root", "root_url", "div", "autoscroll", "dispatch", "createEventDispatcher", "beforeUpdate", "scroll", "afterUpdate", "n", "handle_select", "j", "handle_like", "liked", "click_handler", "func", "func_1", "$$value", "dequal", "Cha<PERSON>", "blocklabel_changes", "elem_id", "elem_classes", "visible", "scale", "min_width", "label", "show_label", "gradio", "_value", "redirect_src_url", "src", "loading_status", "height", "change_handler", "user_msg", "bot_msg", "normalise_file"], "mappings": "0zCAAAA,EAgBKC,EAAAC,EAAAC,CAAA,EALJC,EAGCF,EAAAG,CAAA,EACDD,EAAyDF,EAAAI,CAAA,iuBCNnDC,EAAQ,CAAA,EAAG,eAAiB,MAAM,gEALzCP,EAoBAC,EAAAC,EAAAC,CAAA,EAZEC,EAKCF,EAAAG,CAAA,EAAAD,EAMAF,EAAAI,CAAA,wBAdIC,EAAQ,CAAA,EAAG,eAAiB,sEARvB,SAAAC,CAAiB,EAAAC,mwBCQtBF,EAAQ,CAAA,EAAG,eAAiB,MAAM,gEALzCP,EAoBAC,EAAAC,EAAAC,CAAA,EAZEC,EAKCF,EAAAG,CAAA,EAAAD,EAMAF,EAAAI,CAAA,wBAdIC,EAAQ,CAAA,EAAG,eAAiB,sEARvB,SAAAC,CAAiB,EAAAC,sICEhB,MAAAC,GAA0B,MACtCC,IAEe,MAAM,QAAQ,IAC5BA,EAAK,IAAI,MAAOC,GACR,MAAM,QAAQ,IACpBA,EAAa,IAAI,MAAOC,EAASC,IAAM,CACtC,GAAID,IAAY,KAAa,MAAA,GACzB,IAAAE,EAAgBD,IAAM,EAAI,KAAO,KACjCE,EAAe,GAEf,GAAA,OAAOH,GAAY,SAAU,CAChC,MAAMI,EAAgB,CACrB,MAAO,+BACP,MAAO,+BACP,MAAO,2DAAA,EAGOD,EAAAH,EAEf,OAAS,CAACK,EAAGC,CAAK,IAAK,OAAO,QAAQF,CAAa,EAAG,CACjD,IAAAG,EAEJ,MAAQA,EAAQD,EAAM,KAAKN,CAAO,KAAO,MAAM,CAC9C,MAAMQ,EAAUD,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC7BE,EAAS,MAAMC,GAAoBF,EAAS,KAAK,EACxCL,EAAAA,EAAa,QAAQK,EAASC,CAAM,QAG/C,CACN,MAAME,EAAW,MAAMD,GAAoBV,EAAQ,KAAM,KAAK,EAC1DA,EAAQ,WAAW,SAAS,OAAO,EACtCG,EAAe,wBAAwBQ,cAC7BX,EAAQ,WAAW,SAAS,OAAO,EAC9BG,EAAAQ,EACLX,EAAQ,WAAW,SAAS,OAAO,IAC7CG,EAAe,aAAaQ,SAI9B,MAAO,GAAGT,MAAkBC,GAAA,CAC5B,CAAA,CAEF,CAAA,GAGA,IAAKJ,GACLA,EAAa,KACZA,EAAa,CAAC,IAAM,IAAMA,EAAa,CAAC,IAAM,GAAK;AAAA,EAAO,EAC3D,CAAA,EAEA,KAAK;AAAA,CAAI,kXCHLL,EAAM,CAAA,GAAAkB,GAAA,IAGPlB,EAAM,CAAA,GAAAmB,GAAA,0FALCnB,EAAM,CAAA,EAAG,iBAAmB,cAAc,wCAHvDP,EAWQC,EAAA0B,EAAAxB,CAAA,8DAVGI,EAAW,CAAA,CAAA,kBAIfA,EAAM,CAAA,kFAGPA,EAAM,CAAA,sGALCA,EAAM,CAAA,EAAG,iBAAmB,mJA7CpC,IAAAqB,EAAS,IACF,MAAAC,CAAa,EAAApB,EACpBqB,WAEKC,GAAa,CACrBC,EAAA,EAAAJ,EAAS,EAAI,EACTE,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACPE,EAAA,EAAAJ,EAAS,EAAK,GACZ,oBAGWK,GAAW,CACrB,GAAA,cAAe,UACZ,MAAA,UAAU,UAAU,UAAUJ,CAAK,EACzCE,SAEM,MAAAG,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQL,EAEjBK,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEtB,SAAS,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAM,MAGd,SAAS,YAAY,MAAM,EAC3BH,UACQI,GACR,QAAQ,MAAMA,CAAK,UAEnBD,EAAS,OAAM,IAKlB,OAAAE,GAAS,IAAA,CACJN,GAAO,aAAaA,CAAK,4OChBvBO,EAAAV,EAAA,QAAAW,EAAA/B,KAAS,UAAU,qBACdA,EAAQ,CAAA,aAAcA,EAAM,CAAA,IAAKA,EAAM,CAAA,CAAA,uCAZpDP,EAeQC,EAAA0B,EAAAxB,CAAA,8HAJA,CAAAoC,GAAAC,EAAA,GAAAF,KAAAA,EAAA/B,KAAS,8CACJA,EAAQ,CAAA,aAAcA,EAAM,CAAA,IAAKA,EAAM,CAAA,iJAvBxC,OAAAkC,CAA0B,EAAAhC,GAC1B,cAAAiC,CAAyB,EAAAjC,EAEhCD,EAAW,GACXmC,EAAOF,IAAW,OAASG,GAAOC,YAE7BC,GAAe,CACvBd,EAAA,EAAAxB,EAAW,EAAI,eAMfsC,IACAJ,OAEYK,GAAC,CACTA,EAAE,MAAQ,UACbD,IACAJ;;;;oLCdmBM,GAAAC,EAAA,gBAAA1C,OAAW,SAAW,oBAAsB,MAAM,UALxEP,EAaKC,EAAAgD,EAAA9C,CAAA,iBARiB6C,GAAAC,EAAA,gBAAA1C,OAAW,SAAW,oBAAsB,MAAM,6CAR5D,GAAA,CAAA,OAAA2C,EAAS,QAAQ,EAAAzC,6TC0GfC,+IAJbV,EAOKC,EAAAkD,EAAAhD,CAAA,yLAYII,EAAK,CAAA,CAAA,uBAAV,OAAIO,GAAA,oNAACP,EAAK,CAAA,CAAA,oBAAV,OAAI,GAAA,EAAA,mHAAJ,OAAI,EAAA6C,EAAA,OAAA,GAAA,yCAAJ,OAAItC,GAAA,kKAIGP,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,IAAM,MAAI8C,GAAA9C,CAAA,mDAsChB,0DAAA,OAAAA,OAAY,SAAQ,kBAStBA,EAAO,EAAA,IAAK,MAAQA,MAAQ,WAAW,SAAS,OAAO,wBAWvDA,EAAO,EAAA,IAAK,MAAQA,MAAQ,WAAW,SAAS,OAAO,wBAavDA,EAAO,EAAA,IAAK,MAAQA,MAAQ,WAAW,SAAS,OAAO,QAMvDA,QAAY,MAAQA,EAAQ,EAAA,EAAA,OAAS,KAAI,EAWzCA,EAAe,CAAA,GAAIA,EAAC,EAAA,IAAK,EAAC,sJAKhCA,EAAQ,CAAA,GAAIA,EAAC,EAAA,IAAK,GAAOA,EAAgB,CAAA,GAAIA,EAAO,EAAA,GAAA,OAAWA,EAAO,EAAA,GAAK,WAAQ+C,GAAA/C,CAAA,8FAvE1E8B,EAAAV,EAAA,cAAApB,EAAK,EAAA,GAAA,EAAI,OAAS,KAAK,cAW/BA,EAAG,CAAA,EAAG,MAAQ,KAAK,sBACXA,EAAC,EAAA,GAAI,EAAI,OAAS,OAC9B,gBACAA,EAAO,EAAA,CAAA,gCAbMgD,EAAA5B,EAAA,SAAApB,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mCACFA,EAAe,EAAA,CAAA,mDATlC8B,EAAAmB,EAAA,QAAA,YAAAjD,EAAK,EAAA,GAAA,EAAI,OAAS,OAAK,iBAAA,oBACpBA,EAAM,EAAA,IAAK,UAAQ,CAAKA,EAAiB,EAAA,CAAA,EACpCgD,EAAAC,EAAA,mBAAAjD,QAAW,OAAO,EACbgD,EAAAC,EAAA,wBAAAjD,QAAW,QAAQ,mCACdA,EAAe,EAAA,CAAA,EApB1B8B,EAAAoB,EAAA,QAAAC,EAAA,eAAAnD,WAASA,EAAC,EAAA,GAAI,EAAI,WAAa,WAAS,iBAAA,UAAjEP,EAuHKC,EAAAwD,EAAAtD,CAAA,wBAxGJC,EA8EKqD,EAAAD,CAAA,EAvEJpD,EAsEQoD,EAAA7B,CAAA,iHA3FJpB,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,IAAM,2OAiCnBA,EAAG,CAAA,EAAG,MAAQ,2CACNA,EAAC,EAAA,GAAI,EAAI,OAAS,OAC9B,gBACAA,EAAO,EAAA,uCAbMgD,EAAA5B,EAAA,SAAApB,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,oDACFA,EAAe,EAAA,CAAA,2EAR/BA,EAAM,EAAA,IAAK,UAAQ,CAAKA,EAAiB,EAAA,CAAA,oBACpCgD,EAAAC,EAAA,mBAAAjD,QAAW,OAAO,oBACbgD,EAAAC,EAAA,wBAAAjD,QAAW,QAAQ,oDACdA,EAAe,EAAA,CAAA,EA0E5CA,EAAQ,CAAA,GAAIA,EAAC,EAAA,IAAK,GAAOA,EAAgB,CAAA,GAAIA,EAAO,EAAA,GAAA,OAAWA,EAAO,EAAA,GAAK,2GA9FzD,CAAAgC,GAAAC,EAAA,CAAA,EAAA,OAAAkB,KAAAA,EAAA,eAAAnD,WAASA,EAAC,EAAA,GAAI,EAAI,WAAa,WAAS,kPAKxDoD,EAAAC,EAAA,IAAAC,EAAAC,GACJvD,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,EACfA,EACA,EAAA,EAAAA,EAAA,EAAA,CAAA,CAAA,GAAA8B,EAAAuB,EAAA,MAAAC,CAAA,EAEKxB,EAAAuB,EAAA,OAAArD,EAAK,EAAA,GAAA,EAAI,OAAS,OAAK,SAAA,yDAR/BP,EAUKC,EAAAkD,EAAAhD,CAAA,EATJC,EAQC+C,EAAAS,CAAA,UANKpB,EAAA,CAAA,EAAA,OAAA,CAAAmB,EAAAC,EAAA,IAAAC,EAAAC,GACJvD,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,EACfA,EACA,EAAA,EAAAA,EAAA,EAAA,CAAA,CAAA,0SA+ECwD,GAAAxD,EAAQ,EAAA,EAAA,WAAaA,MAAQ,MAAI,uEAN5B8B,EAAA2B,EAAA,OAAAC,EAAA1D,MAAQ,IAAI,yBAER8B,EAAA2B,EAAA,WAAAE,EAAA,OAAO,aACd,KACA3D,EAAQ,EAAA,EAAA,WAAaA,MAAQ,IAAI,wCANrCP,EASGC,EAAA+D,EAAA7D,CAAA,iBADDqC,EAAA,CAAA,EAAA,GAAAuB,KAAAA,GAAAxD,EAAQ,EAAA,EAAA,WAAaA,MAAQ,MAAI,KAAA4D,GAAAC,EAAAL,CAAA,EAN5BvB,EAAA,CAAA,EAAA,GAAAyB,KAAAA,EAAA1D,MAAQ,qBAEJiC,EAAA,CAAA,EAAA,GAAA0B,KAAAA,EAAA,OAAO,aACd,KACA3D,EAAQ,EAAA,EAAA,WAAaA,MAAQ,mIAV3BoD,EAAAC,EAAA,IAAAC,EAAAtD,MAAQ,IAAI,GAAA8B,EAAAuB,EAAA,MAAAC,CAAA,EACZxB,EAAAuB,EAAA,MAAAS,EAAA9D,MAAQ,QAAQ,wCAHtBP,EAICC,EAAA2D,EAAAzD,CAAA,UAFKqC,EAAA,CAAA,EAAA,GAAA,CAAAmB,EAAAC,EAAA,IAAAC,EAAAtD,MAAQ,IAAI,gBACZiC,EAAA,CAAA,EAAA,GAAA6B,KAAAA,EAAA9D,MAAQ,0NAbRoD,EAAAW,EAAA,IAAAC,EAAAhE,MAAQ,IAAI,GAAA8B,EAAAiC,EAAA,MAAAC,CAAA,EACVlC,EAAAiC,EAAA,QAAAE,EAAAjE,MAAQ,QAAQ,8DAJxBP,EAWOC,EAAAqE,EAAAnE,CAAA,EADNC,EAAwBkE,EAAAG,CAAA,gFAPnBjC,EAAA,CAAA,EAAA,GAAA,CAAAmB,EAAAW,EAAA,IAAAC,EAAAhE,MAAQ,IAAI,gBACViC,EAAA,CAAA,EAAA,GAAAgC,KAAAA,EAAAjE,MAAQ,6LAXVoD,EAAAe,EAAA,IAAAC,EAAApE,MAAQ,IAAI,GAAA8B,EAAAqC,EAAA,MAAAC,CAAA,EACVtC,EAAAqC,EAAA,QAAAE,EAAArE,MAAQ,QAAQ,wCALxBP,EASCC,EAAAyE,EAAAvE,CAAA,gFALKqC,EAAA,CAAA,EAAA,GAAA,CAAAmB,EAAAe,EAAA,IAAAC,EAAApE,MAAQ,IAAI,gBACViC,EAAA,CAAA,EAAA,GAAAoC,KAAAA,EAAArE,MAAQ,2NARNA,EAAM,EAAA,CAAA,2VA0DZA,EAAQ,CAAA,GAAIA,EAAC,EAAA,GAAI,GAACsE,GAAAtE,CAAA,EAUlBuE,EAAAvE,EAAoB,CAAA,GAAAA,EAAkB,EAAA,GAAA,OAAAA,OAAY,UAAQwE,GAAAxE,CAAA,gDAlBvC8B,EAAAc,EAAA,QAAA6B,EAAA,oBAAAzE,EAAK,EAAA,GAAA,EAC1B,OACA,OAAwB,oBAAAA,EAAS,EAAA,EAAA,KAAAA,EAAc,CAAA,EAAAA,EACjD,EAAA,CAAA,IAAA,MAAQ,eAAa,iBAAA,4BACKA,EAAM,EAAA,IAAK,UAAQ,CAC5CA,EAAiB,EAAA,CAAA,EACQgD,EAAAJ,EAAA,sBAAA5C,QAAW,QAAQ,UAP/CP,EAsBKC,EAAAkD,EAAAhD,CAAA,oDAbCI,EAAQ,CAAA,GAAIA,EAAC,EAAA,GAAI,kGAUjBA,EAAoB,CAAA,GAAAA,EAAkB,EAAA,GAAA,OAAAA,OAAY,8GAlB/B,CAAAgC,GAAAC,EAAA,CAAA,EAAA,OAAAwC,KAAAA,EAAA,oBAAAzE,EAAK,EAAA,GAAA,EAC1B,OACA,OAAwB,oBAAAA,EAAS,EAAA,EAAA,KAAAA,EAAc,CAAA,EAAAA,EACjD,EAAA,CAAA,IAAA,MAAQ,eAAa,gFACKA,EAAM,EAAA,IAAK,UAAQ,CAC5CA,EAAiB,EAAA,CAAA,oBACQgD,EAAAJ,EAAA,sBAAA5C,QAAW,QAAQ,mrBAahCA,EAAO,EAAA,CAAA,CAAA,CAAA,kFAAPA,EAAO,EAAA,uHApHpBA,EAAO,EAAA,IAAK,MAAQA,EAAe,CAAA,IAAAkB,GAAAlB,CAAA,uEAAnCA,EAAO,EAAA,IAAK,MAAQA,EAAe,CAAA,uMADlCA,EAAY,EAAA,CAAA,uBAAjB,OAAIO,GAAA,oNAACP,EAAY,EAAA,CAAA,oBAAjB,OAAI,GAAA,EAAA,mHAAJ,OAAI,EAAA6C,EAAA,OAAA,GAAA,yCAAJ,OAAItC,GAAA,8IArBLP,EAAiB,CAAA,GAAIA,EAAU,CAAA,IAAA,MAAQA,EAAK,CAAA,EAAC,OAAS,GAAC0E,GAAA1E,CAAA,EAmBrDuE,EAAAvE,OAAU,MAAImB,GAAAnB,CAAA,sGADwBgD,EAAAC,EAAA,aAAAjD,QAAW,QAAQ,EANxD8B,EAAAoB,EAAA,QAAAC,EAAAwB,GAAA3E,QAAW,SAAW,cAAgB,YAAY,EAAA,iBAAA,oHAD1DP,EAyIKC,EAAAwD,EAAAtD,CAAA,EAlIJC,EAiIKqD,EAAAD,CAAA,uEAnJDjD,EAAiB,CAAA,GAAIA,EAAU,CAAA,IAAA,MAAQA,EAAK,CAAA,EAAC,OAAS,6GAmBpDA,OAAU,yHAD4BgD,EAAAC,EAAA,aAAAjD,QAAW,QAAQ,GANxD,CAAAgC,GAAAC,EAAA,CAAA,EAAA,OAAAkB,KAAAA,EAAAwB,GAAA3E,QAAW,SAAW,cAAgB,YAAY,EAAA,yKAnG9C,MAAAsB,CAEJ,EAAApB,EACH0E,EACH,MACU,iBAAAC,CAIR,EAAA3E,EACQ,CAAA,gBAAA4E,EAAkB,EAAK,EAAA5E,EACvB,CAAA,WAAA6E,EAAa,EAAK,EAAA7E,EAClB,CAAA,SAAA8E,EAAW,EAAK,EAAA9E,EAChB,CAAA,kBAAA+E,EAAoB,EAAK,EAAA/E,EACzB,CAAA,IAAAgF,EAAM,EAAK,EAAAhF,EACX,CAAA,iBAAAiF,EAAmB,EAAK,EAAAjF,GACxB,cAAAkF,EAAa,CAAoC,KAAM,IAAI,CAAA,EAAAlF,EAC3D,CAAA,cAAAmF,EAAgB,EAAI,EAAAnF,EACpB,CAAA,kBAAAoF,EAAoB,EAAI,EAAApF,EACxB,CAAA,gBAAAqF,EAAkB,EAAI,EAAArF,EACtB,CAAA,YAAAsF,EAAc,EAAI,EAAAtF,GAClB,KAAAuF,CAAY,EAAAvF,GACZ,SAAAwF,CAAuB,EAAAxF,EACvB,CAAA,OAAAyC,EAA6B,QAAQ,EAAAzC,EAE5CyF,EACAC,EAEE,MAAAC,EAAWC,KAMjBC,GAAY,IAAA,CACXH,EACCD,GAAOA,EAAI,aAAeA,EAAI,UAAYA,EAAI,aAAe,YAGzDK,EAAM,IAAA,CACPJ,GACHD,EAAI,SAAS,EAAGA,EAAI,YAAY,GAGlCM,GAAW,IAAA,CACNL,IACHI,IACAL,EAAI,iBAAiB,KAAK,EAAE,QAASO,GAAC,CACrCA,EAAE,iBAAiB,OAAM,IAAA,CACxBF,WAaK,SAAAG,EACR5F,EACA6F,EACA9F,EAAiC,CAEjCuF,EAAS,SAAQ,CAChB,MAAK,CAAGtF,EAAG6F,CAAC,EACZ,MAAO9F,CAAA,CAAA,EAIA,SAAA+F,EACR9F,EACA6F,EACA9F,EACAgG,GAAc,CAEdT,EAAS,OACR,CAAA,MAAQ,CAAAtF,EAAG6F,CAAC,EACZ,MAAO9F,EACA,MAAAgG,EAAA,CAAA,gQAwDe,MAAAC,GAAA,CAAAhG,EAAA6F,EAAA9F,IAAA6F,EAAc5F,EAAG6F,EAAG9F,CAAO,WAC9BkC,KAAC,CACTA,GAAE,MAAQ,SACb2D,EAAc5F,EAAG6F,EAAG9F,CAAO,GA4ENkG,GAAA,CAAAjG,EAAA6F,EAAA9F,IAAA+F,EAAY9F,EAAG6F,EAAG9F,EAAS,EAAI,EAI/BmG,GAAA,CAAAlG,EAAA6F,EAAA9F,IAAA+F,EAAY9F,EAAG6F,EAAG9F,EAAS,EAAK,6CAzHpDqF,EAAGe,itBA5CRC,GAAOrF,EAAOsD,CAAS,IAC3BnD,EAAA,GAAAmD,EAAYtD,CAAK,EACjBuE,EAAS,QAAQ,waCIb7F,EAAc,EAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,2KAHCA,EAAc,EAAA,CAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,8KAOI4G,SACC,GACA,MAAA5G,MAAS,0GAATiC,EAAA,CAAA,EAAA,KAAA4E,EAAA,MAAA7G,MAAS,oIAddA,EAAc,EAAA,GAAAkB,GAAAlB,CAAA,IASbA,EAAU,CAAA,GAAAmB,GAAAnB,CAAA,wFAYPA,EAAM,EAAA,+DAGIA,EAAc,EAAA,GAAE,SAAW,gZAhB9CP,EAgCKC,EAAAiG,EAAA/F,CAAA,iDAxCAI,EAAc,EAAA,kHASbA,EAAU,CAAA,2OAYPA,EAAM,EAAA,oHAGIA,EAAc,EAAA,GAAE,SAAW,miBA9BrC,yDAIO,+aA9DL,GAAA,CAAA,QAAA8G,EAAU,EAAE,EAAA5G,GACZ,aAAA6G,EAAY,EAAA,EAAA7G,EACZ,CAAA,QAAA8G,EAAU,EAAI,EAAA9G,GACd,MAAAoB,EAAK,EAAA,EAAApB,EACL,CAAA,MAAA+G,EAAuB,IAAI,EAAA/G,EAC3B,CAAA,UAAAgH,EAAgC,MAAS,EAAAhH,GACzC,MAAAiH,CAAa,EAAAjH,EACb,CAAA,WAAAkH,EAAa,EAAI,EAAAlH,GACjB,KAAAuF,CAAY,EAAAvF,GACZ,SAAAwF,CAAuB,EAAAxF,EACvB,CAAA,WAAA6E,EAAa,EAAK,EAAA7E,EAClB,CAAA,SAAA8E,EAAW,EAAK,EAAA9E,EAChB,CAAA,kBAAA+E,EAAoB,EAAK,EAAA/E,EACzB,CAAA,IAAAgF,EAAM,EAAK,EAAAhF,EACX,CAAA,iBAAAiF,EAAmB,EAAK,EAAAjF,EACxB,CAAA,cAAAmF,EAAgB,EAAI,EAAAnF,EACpB,CAAA,kBAAAoF,EAAoB,EAAI,EAAApF,EACxB,CAAA,OAAAyC,EAA6B,QAAQ,EAAAzC,EACrC,CAAA,gBAAAqF,EAAkB,EAAI,EAAArF,EACtB,CAAA,YAAAsF,EAAc,EAAI,EAAAtF,GAClB,iBAAA2E,CAIR,EAAA3E,GACQ,OAAAmH,CAMT,EAAAnH,GACS,cAAAkF,EAAa,CAAoC,KAAM,IAAI,CAAA,EAAAlF,EAElEoH,QAEEC,EAAoBC,GACzBA,EAAI,QAAQ,aAAY,QAAU/B,OAAI,EAa5B,GAAA,CAAA,eAAAgC,GAA4C,MAAS,EAAAvH,EACrD,CAAA,OAAAwH,GAAS,GAAG,EAAAxH,EAwCJ,MAAAyH,GAAA,IAAAN,EAAO,SAAS,SAAU/F,CAAK,KACpCkB,GAAM6E,EAAO,SAAS,SAAU7E,EAAE,MAAM,KAC1CA,GAAM6E,EAAO,SAAS,OAAQ7E,EAAE,MAAM,KACrCA,GAAM6E,EAAO,SAAS,QAAS7E,EAAE,MAAM,KACvCA,GAAM6E,EAAO,SAAS,QAAS7E,EAAE,MAAM,m/BAxDnDf,EAAA,GAAE6F,EAAShG,EACTA,EAAM,IAAM,CAAA,CAAAsG,EAAUC,EAAO,IAAA,CACtB,OAAAD,GAAa,SACjBL,EAAiBK,CAAQ,EACzBE,GAAeF,EAAUnC,EAAMC,CAAQ,EACnC,OAAAmC,IAAY,SAChBN,EAAiBM,EAAO,EACxBC,GAAeD,GAASpC,EAAMC,CAAQ"}