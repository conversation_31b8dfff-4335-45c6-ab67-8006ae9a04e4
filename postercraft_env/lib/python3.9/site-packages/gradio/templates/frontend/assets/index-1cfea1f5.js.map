{"version": 3, "file": "index-1cfea1f5.js", "sources": ["../../../../js/row/static/StaticRow.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let equal_height = true;\n\texport let elem_id: string;\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let variant: \"default\" | \"panel\" | \"compact\" = \"default\";\n</script>\n\n<div\n\tclass:compact={variant === \"compact\"}\n\tclass:panel={variant === \"panel\"}\n\tclass:unequal-height={equal_height === false}\n\tclass:stretch={equal_height}\n\tclass:hide={!visible}\n\tid={elem_id}\n\tclass={elem_classes.join(\" \")}\n>\n\t<slot />\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--layout-gap);\n\t\twidth: var(--size-full);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\t.compact > :global(*),\n\t.compact :global(.box) {\n\t\tborder-radius: 0;\n\t}\n\t.compact,\n\t.panel {\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--size-2);\n\t}\n\t.unequal-height {\n\t\talign-items: flex-start;\n\t}\n\n\t.stretch {\n\t\talign-items: stretch;\n\t}\n\n\tdiv > :global(*),\n\tdiv > :global(.form > *) {\n\t\tflex: 1 1 0%;\n\t\tflex-wrap: wrap;\n\t\tmin-width: min(160px, 100%);\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert", "target", "anchor", "equal_height", "$$props", "elem_id", "elem_classes", "visible", "variant"], "mappings": "uPAcKA,EAAO,CAAA,CAAA,kBACJA,EAAY,CAAA,EAAC,KAAK,GAAG,CAAA,EAAA,iBAAA,EANbC,EAAAC,EAAA,UAAAF,OAAY,SAAS,EACvBC,EAAAC,EAAA,QAAAF,OAAY,OAAO,EACVC,EAAAC,EAAA,iBAAAF,OAAiB,EAAK,gBAC7BA,EAAY,CAAA,CAAA,cACdA,EAAO,CAAA,CAAA,UALrBG,EAUKC,EAAAF,EAAAG,CAAA,sHAJAL,EAAO,CAAA,CAAA,qBACJA,EAAY,CAAA,EAAC,KAAK,GAAG,CAAA,EAAA,gDANbC,EAAAC,EAAA,UAAAF,OAAY,SAAS,cACvBC,EAAAC,EAAA,QAAAF,OAAY,OAAO,aACVC,EAAAC,EAAA,iBAAAF,OAAiB,EAAK,2BAC7BA,EAAY,CAAA,CAAA,0BACdA,EAAO,CAAA,CAAA,qHAZT,CAAA,aAAAM,EAAe,EAAI,EAAAC,GACnB,QAAAC,CAAe,EAAAD,GACf,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,EACd,CAAA,QAAAI,EAA2C,SAAS,EAAAJ"}