import{S as ke,e as ve,s as je,y as ye,o as E,Q as Ne,h as G,p as W,w as A,r as K,u as S,v as Q,k as L,a0 as Oe,a1 as Ue,C as qe,a5 as Fe,F as M,G as N,H as O,a7 as Xe,R as ee,E as ae,I as J,m as I,g as d,K as V,N as C,j as D,ar as Pe,O as ze,M as F,B as We,t as Ae,x as Be,Z as Ke,ae as Qe,U as Ze,V as Je,W as Ye,X as pe}from"./index-c99b2410.js";import{n as fe,B as xe}from"./Button-9c502b18.js";import{B as $e}from"./BlockLabel-def07c98.js";import{I as el}from"./IconButton-0f3d06d2.js";import{E as ll}from"./Empty-16e1c9d8.js";import{S as tl}from"./ShareButton-0769c1c4.js";import{M as nl}from"./ModifyUpload-643080ff.js";import{D as il}from"./Download-907a3aaf.js";import{I as Se}from"./Image-c3fe7982.js";import{u as al}from"./utils-c3e3db58.js";import"./Undo-61b53ec5.js";var se=Object.prototype.hasOwnProperty;function re(e,l,t){for(t of e.keys())if(P(t,l))return t}function P(e,l){var t,n,i;if(e===l)return!0;if(e&&l&&(t=e.constructor)===l.constructor){if(t===Date)return e.getTime()===l.getTime();if(t===RegExp)return e.toString()===l.toString();if(t===Array){if((n=e.length)===l.length)for(;n--&&P(e[n],l[n]););return n===-1}if(t===Set){if(e.size!==l.size)return!1;for(n of e)if(i=n,i&&typeof i=="object"&&(i=re(l,i),!i)||!l.has(i))return!1;return!0}if(t===Map){if(e.size!==l.size)return!1;for(n of e)if(i=n[0],i&&typeof i=="object"&&(i=re(l,i),!i)||!P(n[1],l.get(i)))return!1;return!0}if(t===ArrayBuffer)e=new Uint8Array(e),l=new Uint8Array(l);else if(t===DataView){if((n=e.byteLength)===l.byteLength)for(;n--&&e.getInt8(n)===l.getInt8(n););return n===-1}if(ArrayBuffer.isView(e)){if((n=e.byteLength)===l.byteLength)for(;n--&&e[n]===l[n];);return n===-1}if(!t||typeof e=="object"){n=0;for(t in e)if(se.call(e,t)&&++n&&!se.call(l,t)||!(t in l)||!P(e[t],l[t]))return!1;return Object.keys(l).length===n}}return e!==e&&l!==l}async function fl(e){return e?`<div style="display: flex; flex-wrap: wrap; gap: 16px">${(await Promise.all(e.map(async([t,n])=>t===null?"":await al(t.data,"url")))).map(t=>`<img src="${t}" style="height: 400px" />`).join("")}</div>`:""}const{window:De}=Fe;function oe(e,l,t){const n=e.slice();return n[39]=l[t][0],n[40]=l[t][1],n[42]=t,n}function ue(e,l,t){const n=e.slice();return n[39]=l[t],n[43]=l,n[42]=t,n}function _e(e){let l,t;return l=new $e({props:{show_label:e[1],Icon:Se,label:e[2]||"Gallery"}}),{c(){M(l.$$.fragment)},m(n,i){N(l,n,i),t=!0},p(n,i){const r={};i[0]&2&&(r.show_label=n[1]),i[0]&4&&(r.label=n[2]||"Gallery"),l.$set(r)},i(n){t||(A(l.$$.fragment,n),t=!0)},o(n){S(l.$$.fragment,n),t=!1},d(n){O(l,n)}}}function sl(e){let l,t,n,i,r,m,u=e[0]!==null&&e[7]&&ce(e),_=e[9]&&de(e),b=J(e[11]),a=[];for(let f=0;f<b.length;f+=1)a[f]=be(oe(e,b,f));return{c(){u&&u.c(),l=E(),t=I("div"),n=I("div"),_&&_.c(),i=E();for(let f=0;f<a.length;f+=1)a[f].c();d(n,"class","grid-container svelte-fiatpe"),V(n,"--grid-cols",e[4]),V(n,"--grid-rows",e[5]),V(n,"--object-fit",e[8]),V(n,"height",e[6]),C(n,"pt-6",e[1]),d(t,"class","grid-wrap svelte-fiatpe"),ye(()=>e[34].call(t)),C(t,"fixed-height",!e[6]||e[6]=="auto")},m(f,c){u&&u.m(f,c),G(f,l,c),G(f,t,c),D(t,n),_&&_.m(n,null),D(n,i);for(let s=0;s<a.length;s+=1)a[s]&&a[s].m(n,null);r=Pe(t,e[34].bind(t)),m=!0},p(f,c){if(f[0]!==null&&f[7]?u?(u.p(f,c),c[0]&129&&A(u,1)):(u=ce(f),u.c(),A(u,1),u.m(l.parentNode,l)):u&&(K(),S(u,1,1,()=>{u=null}),Q()),f[9]?_?(_.p(f,c),c[0]&512&&A(_,1)):(_=de(f),_.c(),A(_,1),_.m(n,i)):_&&(K(),S(_,1,1,()=>{_=null}),Q()),c[0]&2049){b=J(f[11]);let s;for(s=0;s<b.length;s+=1){const k=oe(f,b,s);a[s]?a[s].p(k,c):(a[s]=be(k),a[s].c(),a[s].m(n,null))}for(;s<a.length;s+=1)a[s].d(1);a.length=b.length}(!m||c[0]&16)&&V(n,"--grid-cols",f[4]),(!m||c[0]&32)&&V(n,"--grid-rows",f[5]),(!m||c[0]&256)&&V(n,"--object-fit",f[8]),(!m||c[0]&64)&&V(n,"height",f[6]),(!m||c[0]&2)&&C(n,"pt-6",f[1]),(!m||c[0]&64)&&C(t,"fixed-height",!f[6]||f[6]=="auto")},i(f){m||(A(u),A(_),m=!0)},o(f){S(u),S(_),m=!1},d(f){f&&(L(l),L(t)),u&&u.d(f),_&&_.d(),ze(a,f),r()}}}function rl(e){let l,t;return l=new ll({props:{unpadded_box:!0,size:"large",$$slots:{default:[ol]},$$scope:{ctx:e}}}),{c(){M(l.$$.fragment)},m(n,i){N(l,n,i),t=!0},p(n,i){const r={};i[1]&8192&&(r.$$scope={dirty:i,ctx:n}),l.$set(r)},i(n){t||(A(l.$$.fragment,n),t=!0)},o(n){S(l.$$.fragment,n),t=!1},d(n){O(l,n)}}}function ce(e){let l,t,n,i,r,m,u,_,b,a,f,c,s,k,H,U,v=e[10]&&he(e);i=new nl({props:{absolute:!1}}),i.$on("clear",e[26]);let B=e[11][e[0]][1]&&me(e),w=J(e[11]),y=[];for(let g=0;g<w.length;g+=1)y[g]=ge(ue(e,w,g));return{c(){l=I("button"),t=I("div"),v&&v.c(),n=E(),M(i.$$.fragment),r=E(),m=I("button"),u=I("img"),f=E(),B&&B.c(),c=E(),s=I("div");for(let g=0;g<y.length;g+=1)y[g].c();d(t,"class","icon-buttons svelte-fiatpe"),d(u,"data-testid","detailed-image"),F(u.src,_=e[11][e[0]][0].data)||d(u,"src",_),d(u,"alt",b=e[11][e[0]][1]||""),d(u,"title",a=e[11][e[0]][1]||null),d(u,"loading","lazy"),d(u,"class","svelte-fiatpe"),C(u,"with-caption",!!e[11][e[0]][1]),d(m,"class","image-button svelte-fiatpe"),V(m,"height","calc(100% - "+(e[11][e[0]][1]?"80px":"60px")+")"),d(m,"aria-label","detailed view of selected image"),d(s,"class","thumbnails scroll-hide svelte-fiatpe"),d(s,"data-testid","container_el"),d(l,"class","preview svelte-fiatpe")},m(g,z){G(g,l,z),D(l,t),v&&v.m(t,null),D(t,n),N(i,t,null),D(l,r),D(l,m),D(m,u),D(l,f),B&&B.m(l,null),D(l,c),D(l,s);for(let j=0;j<y.length;j+=1)y[j]&&y[j].m(s,null);e[30](s),k=!0,H||(U=[W(m,"click",e[27]),W(l,"keydown",e[18])],H=!0)},p(g,z){if(g[10]?v?(v.p(g,z),z[0]&1024&&A(v,1)):(v=he(g),v.c(),A(v,1),v.m(t,n)):v&&(K(),S(v,1,1,()=>{v=null}),Q()),(!k||z[0]&2049&&!F(u.src,_=g[11][g[0]][0].data))&&d(u,"src",_),(!k||z[0]&2049&&b!==(b=g[11][g[0]][1]||""))&&d(u,"alt",b),(!k||z[0]&2049&&a!==(a=g[11][g[0]][1]||null))&&d(u,"title",a),(!k||z[0]&2049)&&C(u,"with-caption",!!g[11][g[0]][1]),(!k||z[0]&2049)&&V(m,"height","calc(100% - "+(g[11][g[0]][1]?"80px":"60px")+")"),g[11][g[0]][1]?B?B.p(g,z):(B=me(g),B.c(),B.m(l,c)):B&&(B.d(1),B=null),z[0]&6145){w=J(g[11]);let j;for(j=0;j<w.length;j+=1){const T=ue(g,w,j);y[j]?y[j].p(T,z):(y[j]=ge(T),y[j].c(),y[j].m(s,null))}for(;j<y.length;j+=1)y[j].d(1);y.length=w.length}},i(g){k||(A(v),A(i.$$.fragment,g),k=!0)},o(g){S(v),S(i.$$.fragment,g),k=!1},d(g){g&&L(l),v&&v.d(),O(i),B&&B.d(),ze(y,g),e[30](null),H=!1,We(U)}}}function he(e){let l,t,n,i;return t=new el({props:{Icon:il,label:e[16]("common.download")}}),{c(){l=I("a"),M(t.$$.fragment),d(l,"href",n=le(e[3][e[0]])),d(l,"target",window.__is_colab__?"_blank":null),d(l,"download","image"),d(l,"class","svelte-fiatpe")},m(r,m){G(r,l,m),N(t,l,null),i=!0},p(r,m){const u={};m[0]&65536&&(u.label=r[16]("common.download")),t.$set(u),(!i||m[0]&9&&n!==(n=le(r[3][r[0]])))&&d(l,"href",n)},i(r){i||(A(t.$$.fragment,r),i=!0)},o(r){S(t.$$.fragment,r),i=!1},d(r){r&&L(l),O(t)}}}function me(e){let l,t=e[11][e[0]][1]+"",n;return{c(){l=I("caption"),n=Ae(t),d(l,"class","caption svelte-fiatpe")},m(i,r){G(i,l,r),D(l,n)},p(i,r){r[0]&2049&&t!==(t=i[11][i[0]][1]+"")&&Be(n,t)},d(i){i&&L(l)}}}function ge(e){let l,t,n,i,r,m,u=e[42],_,b;const a=()=>e[28](l,u),f=()=>e[28](null,u);function c(){return e[29](e[42])}return{c(){l=I("button"),t=I("img"),r=E(),F(t.src,n=e[39][0].data)||d(t,"src",n),d(t,"title",i=e[39][1]||null),d(t,"alt",""),d(t,"loading","lazy"),d(t,"class","svelte-fiatpe"),d(l,"class","thumbnail-item thumbnail-small svelte-fiatpe"),d(l,"aria-label",m="Thumbnail "+(e[42]+1)+" of "+e[11].length),C(l,"selected",e[0]===e[42])},m(s,k){G(s,l,k),D(l,t),D(l,r),a(),_||(b=W(l,"click",c),_=!0)},p(s,k){e=s,k[0]&2048&&!F(t.src,n=e[39][0].data)&&d(t,"src",n),k[0]&2048&&i!==(i=e[39][1]||null)&&d(t,"title",i),k[0]&2048&&m!==(m="Thumbnail "+(e[42]+1)+" of "+e[11].length)&&d(l,"aria-label",m),u!==e[42]&&(f(),u=e[42],a()),k[0]&1&&C(l,"selected",e[0]===e[42])},d(s){s&&L(l),f(),_=!1,b()}}}function de(e){let l,t,n;return t=new tl({props:{value:e[11],formatter:fl}}),t.$on("share",e[31]),t.$on("error",e[32]),{c(){l=I("div"),M(t.$$.fragment),d(l,"class","icon-button svelte-fiatpe")},m(i,r){G(i,l,r),N(t,l,null),n=!0},p(i,r){const m={};r[0]&2048&&(m.value=i[11]),t.$set(m)},i(i){n||(A(t.$$.fragment,i),n=!0)},o(i){S(t.$$.fragment,i),n=!1},d(i){i&&L(l),O(t)}}}function we(e){let l,t=e[40]+"",n;return{c(){l=I("div"),n=Ae(t),d(l,"class","caption-label svelte-fiatpe")},m(i,r){G(i,l,r),D(l,n)},p(i,r){r[0]&2048&&t!==(t=i[40]+"")&&Be(n,t)},d(i){i&&L(l)}}}function be(e){let l,t,n,i,r,m,u,_,b,a=e[40]&&we(e);function f(){return e[33](e[42])}return{c(){l=I("button"),t=I("img"),r=E(),a&&a.c(),m=E(),d(t,"alt",n=e[40]||""),F(t.src,i=typeof e[39]=="string"?e[39]:e[39].data)||d(t,"src",i),d(t,"loading","lazy"),d(t,"class","svelte-fiatpe"),d(l,"class","thumbnail-item thumbnail-lg svelte-fiatpe"),d(l,"aria-label",u="Thumbnail "+(e[42]+1)+" of "+e[11].length),C(l,"selected",e[0]===e[42])},m(c,s){G(c,l,s),D(l,t),D(l,r),a&&a.m(l,null),D(l,m),_||(b=W(l,"click",f),_=!0)},p(c,s){e=c,s[0]&2048&&n!==(n=e[40]||"")&&d(t,"alt",n),s[0]&2048&&!F(t.src,i=typeof e[39]=="string"?e[39]:e[39].data)&&d(t,"src",i),e[40]?a?a.p(e,s):(a=we(e),a.c(),a.m(l,m)):a&&(a.d(1),a=null),s[0]&2048&&u!==(u="Thumbnail "+(e[42]+1)+" of "+e[11].length)&&d(l,"aria-label",u),s[0]&1&&C(l,"selected",e[0]===e[42])},d(c){c&&L(l),a&&a.d(),_=!1,b()}}}function ol(e){let l,t;return l=new Se({}),{c(){M(l.$$.fragment)},m(n,i){N(l,n,i),t=!0},i(n){t||(A(l.$$.fragment,n),t=!0)},o(n){S(l.$$.fragment,n),t=!1},d(n){O(l,n)}}}function ul(e){let l,t,n,i,r,m,u;ye(e[25]);let _=e[1]&&_e(e);const b=[rl,sl],a=[];function f(c,s){return c[3]===null||c[11]===null||c[11].length===0?0:1}return t=f(e),n=a[t]=b[t](e),{c(){_&&_.c(),l=E(),n.c(),i=Ne()},m(c,s){_&&_.m(c,s),G(c,l,s),a[t].m(c,s),G(c,i,s),r=!0,m||(u=W(De,"resize",e[25]),m=!0)},p(c,s){c[1]?_?(_.p(c,s),s[0]&2&&A(_,1)):(_=_e(c),_.c(),A(_,1),_.m(l.parentNode,l)):_&&(K(),S(_,1,1,()=>{_=null}),Q());let k=t;t=f(c),t===k?a[t].p(c,s):(K(),S(a[k],1,1,()=>{a[k]=null}),Q(),n=a[t],n?n.p(c,s):(n=a[t]=b[t](c),n.c()),A(n,1),n.m(i.parentNode,i))},i(c){r||(A(_),A(n),r=!0)},o(c){S(_),S(n),r=!1},d(c){c&&(L(l),L(i)),_&&_.d(c),a[t].d(c),m=!1,u()}}}function _l(e){return typeof e=="object"&&e!==null&&"data"in e}function le(e){return _l(e)?e.data:typeof e=="string"?e:Array.isArray(e)?le(e[0]):""}function cl(e,l,t){let n,i,r;Oe(e,Ue,o=>t(16,r=o));let{show_label:m=!0}=l,{label:u}=l,{root:_=""}=l,{root_url:b=null}=l,{value:a=null}=l,{columns:f=[2]}=l,{rows:c=void 0}=l,{height:s="auto"}=l,{preview:k}=l,{allow_preview:H=!0}=l,{object_fit:U="cover"}=l,{show_share_button:v=!1}=l,{show_download_button:B=!1}=l,{selected_index:w=null}=l;const y=qe();let g=!0,z=null,j=a;w===null&&k&&a?.length&&(w=0);let T=w;function Z(o){const X=o.target,x=o.clientX,$=X.offsetWidth/2;x<$?t(0,w=n):t(0,w=i)}function Y(o){switch(o.code){case"Escape":o.preventDefault(),t(0,w=null);break;case"ArrowLeft":o.preventDefault(),t(0,w=n);break;case"ArrowRight":o.preventDefault(),t(0,w=i);break}}let q=[],R;async function p(o){if(typeof o!="number")return;await Xe(),q[o].focus();const{left:X,width:x}=R.getBoundingClientRect(),{left:ne,width:$}=q[o].getBoundingClientRect(),ie=ne-X+$/2-x/2+R.scrollLeft;R&&typeof R.scrollTo=="function"&&R.scrollTo({left:ie<0?0:ie,behavior:"smooth"})}let h=0,te=0;function Ie(){t(15,te=De.innerHeight)}const Te=()=>t(0,w=null),Ge=o=>Z(o);function Le(o,X){ee[o?"unshift":"push"](()=>{q[X]=o,t(12,q)})}const Ee=o=>t(0,w=o);function He(o){ee[o?"unshift":"push"](()=>{R=o,t(13,R)})}function Re(o){ae.call(this,e,o)}function Ve(o){ae.call(this,e,o)}const Ce=o=>t(0,w=o);function Me(){h=this.clientHeight,t(14,h)}return e.$$set=o=>{"show_label"in o&&t(1,m=o.show_label),"label"in o&&t(2,u=o.label),"root"in o&&t(19,_=o.root),"root_url"in o&&t(20,b=o.root_url),"value"in o&&t(3,a=o.value),"columns"in o&&t(4,f=o.columns),"rows"in o&&t(5,c=o.rows),"height"in o&&t(6,s=o.height),"preview"in o&&t(21,k=o.preview),"allow_preview"in o&&t(7,H=o.allow_preview),"object_fit"in o&&t(8,U=o.object_fit),"show_share_button"in o&&t(9,v=o.show_share_button),"show_download_button"in o&&t(10,B=o.show_download_button),"selected_index"in o&&t(0,w=o.selected_index)},e.$$.update=()=>{e.$$.dirty[0]&4194312&&t(22,g=a==null||a.length==0?!0:g),e.$$.dirty[0]&1572872&&t(11,z=a===null?null:a.map(o=>Array.isArray(o)?[fe(o[0],_,b),o[1]]:[fe(o,_,b),null])),e.$$.dirty[0]&14680073&&(P(j,a)||(g?(t(0,w=k&&a?.length?0:null),t(22,g=!1)):t(0,w=w!==null&&a!==null&&w<a.length?w:null),y("change"),t(23,j=a))),e.$$.dirty[0]&2049&&(n=((w??0)+(z?.length??0)-1)%(z?.length??0)),e.$$.dirty[0]&2049&&(i=((w??0)+1)%(z?.length??0)),e.$$.dirty[0]&16779265&&w!==T&&(t(24,T=w),w!==null&&y("select",{index:w,value:[z?.[w][0].data,z?.[w][1]]})),e.$$.dirty[0]&129&&H&&p(w)},[w,m,u,a,f,c,s,H,U,v,B,z,q,R,h,te,r,Z,Y,_,b,k,g,j,T,Ie,Te,Ge,Le,Ee,He,Re,Ve,Ce,Me]}class hl extends ke{constructor(l){super(),ve(this,l,cl,ul,je,{show_label:1,label:2,root:19,root_url:20,value:3,columns:4,rows:5,height:6,preview:21,allow_preview:7,object_fit:8,show_share_button:9,show_download_button:10,selected_index:0},null,[-1,-1])}}function ml(e){let l,t,n,i,r;const m=[e[1]];let u={};for(let a=0;a<m.length;a+=1)u=Ke(u,m[a]);l=new Qe({props:u});function _(a){e[22](a)}let b={label:e[3],value:e[9],show_label:e[2],root:e[4],root_url:e[5],columns:e[13],rows:e[14],height:e[15],preview:e[16],object_fit:e[18],allow_preview:e[17],show_share_button:e[19],show_download_button:e[20]};return e[0]!==void 0&&(b.selected_index=e[0]),n=new hl({props:b}),ee.push(()=>Ze(n,"selected_index",_)),n.$on("change",e[23]),n.$on("select",e[24]),n.$on("share",e[25]),n.$on("error",e[26]),{c(){M(l.$$.fragment),t=E(),M(n.$$.fragment)},m(a,f){N(l,a,f),G(a,t,f),N(n,a,f),r=!0},p(a,f){const c=f&2?Je(m,[Ye(a[1])]):{};l.$set(c);const s={};f&8&&(s.label=a[3]),f&512&&(s.value=a[9]),f&4&&(s.show_label=a[2]),f&16&&(s.root=a[4]),f&32&&(s.root_url=a[5]),f&8192&&(s.columns=a[13]),f&16384&&(s.rows=a[14]),f&32768&&(s.height=a[15]),f&65536&&(s.preview=a[16]),f&262144&&(s.object_fit=a[18]),f&131072&&(s.allow_preview=a[17]),f&524288&&(s.show_share_button=a[19]),f&1048576&&(s.show_download_button=a[20]),!i&&f&1&&(i=!0,s.selected_index=a[0],pe(()=>i=!1)),n.$set(s)},i(a){r||(A(l.$$.fragment,a),A(n.$$.fragment,a),r=!0)},o(a){S(l.$$.fragment,a),S(n.$$.fragment,a),r=!1},d(a){a&&L(t),O(l,a),O(n,a)}}}function gl(e){let l,t;return l=new xe({props:{visible:e[8],variant:"solid",padding:!1,elem_id:e[6],elem_classes:e[7],container:e[10],scale:e[11],min_width:e[12],allow_overflow:!1,height:typeof e[15]=="number"?e[15]:void 0,$$slots:{default:[ml]},$$scope:{ctx:e}}}),{c(){M(l.$$.fragment)},m(n,i){N(l,n,i),t=!0},p(n,[i]){const r={};i&256&&(r.visible=n[8]),i&64&&(r.elem_id=n[6]),i&128&&(r.elem_classes=n[7]),i&1024&&(r.container=n[10]),i&2048&&(r.scale=n[11]),i&4096&&(r.min_width=n[12]),i&32768&&(r.height=typeof n[15]=="number"?n[15]:void 0),i&138404415&&(r.$$scope={dirty:i,ctx:n}),l.$set(r)},i(n){t||(A(l.$$.fragment,n),t=!0)},o(n){S(l.$$.fragment,n),t=!1},d(n){O(l,n)}}}function dl(e,l,t){let{loading_status:n}=l,{show_label:i}=l,{label:r}=l,{root:m}=l,{root_url:u}=l,{elem_id:_=""}=l,{elem_classes:b=[]}=l,{visible:a=!0}=l,{value:f=null}=l,{container:c=!0}=l,{scale:s=null}=l,{min_width:k=void 0}=l,{columns:H=[2]}=l,{rows:U=void 0}=l,{height:v="auto"}=l,{preview:B}=l,{allow_preview:w=!0}=l,{selected_index:y=null}=l,{object_fit:g="cover"}=l,{show_share_button:z=!1}=l,{show_download_button:j=!1}=l,{gradio:T}=l;function Z(h){y=h,t(0,y)}const Y=()=>T.dispatch("change",f),q=h=>T.dispatch("select",h.detail),R=h=>T.dispatch("share",h.detail),p=h=>T.dispatch("error",h.detail);return e.$$set=h=>{"loading_status"in h&&t(1,n=h.loading_status),"show_label"in h&&t(2,i=h.show_label),"label"in h&&t(3,r=h.label),"root"in h&&t(4,m=h.root),"root_url"in h&&t(5,u=h.root_url),"elem_id"in h&&t(6,_=h.elem_id),"elem_classes"in h&&t(7,b=h.elem_classes),"visible"in h&&t(8,a=h.visible),"value"in h&&t(9,f=h.value),"container"in h&&t(10,c=h.container),"scale"in h&&t(11,s=h.scale),"min_width"in h&&t(12,k=h.min_width),"columns"in h&&t(13,H=h.columns),"rows"in h&&t(14,U=h.rows),"height"in h&&t(15,v=h.height),"preview"in h&&t(16,B=h.preview),"allow_preview"in h&&t(17,w=h.allow_preview),"selected_index"in h&&t(0,y=h.selected_index),"object_fit"in h&&t(18,g=h.object_fit),"show_share_button"in h&&t(19,z=h.show_share_button),"show_download_button"in h&&t(20,j=h.show_download_button),"gradio"in h&&t(21,T=h.gradio)},[y,n,i,r,m,u,_,b,a,f,c,s,k,H,U,v,B,w,g,z,j,T,Z,Y,q,R,p]}class wl extends ke{constructor(l){super(),ve(this,l,dl,gl,je,{loading_status:1,show_label:2,label:3,root:4,root_url:5,elem_id:6,elem_classes:7,visible:8,value:9,container:10,scale:11,min_width:12,columns:13,rows:14,height:15,preview:16,allow_preview:17,selected_index:0,object_fit:18,show_share_button:19,show_download_button:20,gradio:21})}}const Gl=wl;export{Gl as default};
//# sourceMappingURL=index-431f40ef.js.map
