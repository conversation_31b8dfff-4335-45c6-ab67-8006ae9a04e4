{"version": 3, "file": "index-431f40ef.js", "sources": ["../../../../node_modules/.pnpm/dequal@2.0.2/node_modules/dequal/dist/index.mjs", "../../../../js/gallery/static/utils.ts", "../../../../js/gallery/static/Gallery.svelte", "../../../../js/gallery/static/StaticGallery.svelte"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "import { uploadToHuggingFace } from \"@gradio/utils\";\nimport type { FileData } from \"@gradio/upload\";\n\nexport async function format_gallery_for_sharing(\n\tvalue: [FileData, string | null][] | null\n): Promise<string> {\n\tif (!value) return \"\";\n\tlet urls = await Promise.all(\n\t\tvalue.map(async ([image, _]) => {\n\t\t\tif (image === null) return \"\";\n\t\t\treturn await uploadToHuggingFace(image.data, \"url\");\n\t\t})\n\t);\n\n\treturn `<div style=\"display: flex; flex-wrap: wrap; gap: 16px\">${urls\n\t\t.map((url) => `<img src=\"${url}\" style=\"height: 400px\" />`)\n\t\t.join(\"\")}</div>`;\n}\n", "<script lang=\"ts\">\n\timport { BlockLabel, Empty, ShareButton } from \"@gradio/atoms\";\n\timport { ModifyUpload } from \"@gradio/upload\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { dequal } from \"dequal\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { tick } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\timport { Download, Image } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport { format_gallery_for_sharing } from \"./utils\";\n\timport { IconButton } from \"@gradio/atoms\";\n\n\texport let show_label = true;\n\texport let label: string;\n\texport let root = \"\";\n\texport let root_url: null | string = null;\n\texport let value: (FileData | string | [FileData | string, string])[] | null =\n\t\tnull;\n\texport let columns: number | number[] | undefined = [2];\n\texport let rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let show_download_button = false;\n\texport let selected_index: number | null = null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\t// tracks whether the value of the gallery was reset\n\tlet was_reset = true;\n\n\t$: was_reset = value == null || value.length == 0 ? true : was_reset;\n\n\tlet _value: [FileData, string | null][] | null = null;\n\t$: _value =\n\t\tvalue === null\n\t\t\t? null\n\t\t\t: value.map((img) =>\n\t\t\t\t\tArray.isArray(img)\n\t\t\t\t\t\t? [normalise_file(img[0], root, root_url) as FileData, img[1]]\n\t\t\t\t\t\t: [normalise_file(img, root, root_url) as FileData, null]\n\t\t\t  );\n\n\tlet prevValue: (FileData | string | [FileData | string, string])[] | null =\n\t\tvalue;\n\tif (selected_index === null && preview && value?.length) {\n\t\tselected_index = 0;\n\t}\n\tlet old_selected_index: number | null = selected_index;\n\n\t$: if (!dequal(prevValue, value)) {\n\t\t// When value is falsy (clear button or first load),\n\t\t// preview determines the selected image\n\t\tif (was_reset) {\n\t\t\tselected_index = preview && value?.length ? 0 : null;\n\t\t\twas_reset = false;\n\t\t\t// Otherwise we keep the selected_index the same if the\n\t\t\t// gallery has at least as many elements as it did before\n\t\t} else {\n\t\t\tselected_index =\n\t\t\t\tselected_index !== null &&\n\t\t\t\tvalue !== null &&\n\t\t\t\tselected_index < value.length\n\t\t\t\t\t? selected_index\n\t\t\t\t\t: null;\n\t\t}\n\t\tdispatch(\"change\");\n\t\tprevValue = value;\n\t}\n\n\t$: previous =\n\t\t((selected_index ?? 0) + (_value?.length ?? 0) - 1) % (_value?.length ?? 0);\n\t$: next = ((selected_index ?? 0) + 1) % (_value?.length ?? 0);\n\n\tfunction handle_preview_click(event: MouseEvent): void {\n\t\tconst element = event.target as HTMLElement;\n\t\tconst x = event.clientX;\n\t\tconst width = element.offsetWidth;\n\t\tconst centerX = width / 2;\n\n\t\tif (x < centerX) {\n\t\t\tselected_index = previous;\n\t\t} else {\n\t\t\tselected_index = next;\n\t\t}\n\t}\n\n\tfunction on_keydown(e: KeyboardEvent): void {\n\t\tswitch (e.code) {\n\t\t\tcase \"Escape\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = null;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowLeft\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = previous;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowRight\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = next;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\tfunction isFileData(obj: any): obj is FileData {\n\t\treturn typeof obj === \"object\" && obj !== null && \"data\" in obj;\n\t}\n\n\tfunction getHrefValue(selected: any): string {\n\t\tif (isFileData(selected)) {\n\t\t\treturn selected.data;\n\t\t} else if (typeof selected === \"string\") {\n\t\t\treturn selected;\n\t\t} else if (Array.isArray(selected)) {\n\t\t\treturn getHrefValue(selected[0]);\n\t\t}\n\t\treturn \"\";\n\t}\n\n\t$: {\n\t\tif (selected_index !== old_selected_index) {\n\t\t\told_selected_index = selected_index;\n\t\t\tif (selected_index !== null) {\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: selected_index,\n\t\t\t\t\tvalue: [_value?.[selected_index][0].data, _value?.[selected_index][1]]\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\t$: if (allow_preview) {\n\t\tscroll_to_img(selected_index);\n\t}\n\n\tlet el: HTMLButtonElement[] = [];\n\tlet container_element: HTMLDivElement;\n\n\tasync function scroll_to_img(index: number | null): Promise<void> {\n\t\tif (typeof index !== \"number\") return;\n\t\tawait tick();\n\n\t\tel[index].focus();\n\n\t\tconst { left: container_left, width: container_width } =\n\t\t\tcontainer_element.getBoundingClientRect();\n\t\tconst { left, width } = el[index].getBoundingClientRect();\n\n\t\tconst relative_left = left - container_left;\n\n\t\tconst pos =\n\t\t\trelative_left +\n\t\t\twidth / 2 -\n\t\t\tcontainer_width / 2 +\n\t\t\tcontainer_element.scrollLeft;\n\n\t\tif (container_element && typeof container_element.scrollTo === \"function\") {\n\t\t\tcontainer_element.scrollTo({\n\t\t\t\tleft: pos < 0 ? 0 : pos,\n\t\t\t\tbehavior: \"smooth\"\n\t\t\t});\n\t\t}\n\t}\n\n\tlet client_height = 0;\n\tlet window_height = 0;\n</script>\n\n<svelte:window bind:innerHeight={window_height} />\n\n{#if show_label}\n\t<BlockLabel {show_label} Icon={Image} label={label || \"Gallery\"} />\n{/if}\n{#if value === null || _value === null || _value.length === 0}\n\t<Empty unpadded_box={true} size=\"large\"><Image /></Empty>\n{:else}\n\t{#if selected_index !== null && allow_preview}\n\t\t<button on:keydown={on_keydown} class=\"preview\">\n\t\t\t<div class=\"icon-buttons\">\n\t\t\t\t{#if show_download_button}\n\t\t\t\t\t<a\n\t\t\t\t\t\thref={getHrefValue(value[selected_index])}\n\t\t\t\t\t\ttarget={window.__is_colab__ ? \"_blank\" : null}\n\t\t\t\t\t\tdownload=\"image\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<IconButton Icon={Download} label={$_(\"common.download\")} />\n\t\t\t\t\t</a>\n\t\t\t\t{/if}\n\n\t\t\t\t<ModifyUpload\n\t\t\t\t\tabsolute={false}\n\t\t\t\t\ton:clear={() => (selected_index = null)}\n\t\t\t\t/>\n\t\t\t</div>\n\t\t\t<button\n\t\t\t\tclass=\"image-button\"\n\t\t\t\ton:click={(event) => handle_preview_click(event)}\n\t\t\t\tstyle=\"height: calc(100% - {_value[selected_index][1]\n\t\t\t\t\t? '80px'\n\t\t\t\t\t: '60px'})\"\n\t\t\t\taria-label=\"detailed view of selected image\"\n\t\t\t>\n\t\t\t\t<img\n\t\t\t\t\tdata-testid=\"detailed-image\"\n\t\t\t\t\tsrc={_value[selected_index][0].data}\n\t\t\t\t\talt={_value[selected_index][1] || \"\"}\n\t\t\t\t\ttitle={_value[selected_index][1] || null}\n\t\t\t\t\tclass:with-caption={!!_value[selected_index][1]}\n\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t/>\n\t\t\t</button>\n\t\t\t{#if _value[selected_index][1]}\n\t\t\t\t<caption class=\"caption\">\n\t\t\t\t\t{_value[selected_index][1]}\n\t\t\t\t</caption>\n\t\t\t{/if}\n\t\t\t<div\n\t\t\t\tbind:this={container_element}\n\t\t\t\tclass=\"thumbnails scroll-hide\"\n\t\t\t\tdata-testid=\"container_el\"\n\t\t\t>\n\t\t\t\t{#each _value as image, i}\n\t\t\t\t\t<button\n\t\t\t\t\t\tbind:this={el[i]}\n\t\t\t\t\t\ton:click={() => (selected_index = i)}\n\t\t\t\t\t\tclass=\"thumbnail-item thumbnail-small\"\n\t\t\t\t\t\tclass:selected={selected_index === i}\n\t\t\t\t\t\taria-label={\"Thumbnail \" + (i + 1) + \" of \" + _value.length}\n\t\t\t\t\t>\n\t\t\t\t\t\t<img\n\t\t\t\t\t\t\tsrc={image[0].data}\n\t\t\t\t\t\t\ttitle={image[1] || null}\n\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</button>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</button>\n\t{/if}\n\n\t<div\n\t\tbind:clientHeight={client_height}\n\t\tclass=\"grid-wrap\"\n\t\tclass:fixed-height={!height || height == \"auto\"}\n\t>\n\t\t<div\n\t\t\tclass=\"grid-container\"\n\t\t\tstyle=\"--grid-cols:{columns}; --grid-rows:{rows}; --object-fit: {object_fit}; height: {height};\"\n\t\t\tclass:pt-6={show_label}\n\t\t>\n\t\t\t{#if show_share_button}\n\t\t\t\t<div class=\"icon-button\">\n\t\t\t\t\t<ShareButton\n\t\t\t\t\t\ton:share\n\t\t\t\t\t\ton:error\n\t\t\t\t\t\tvalue={_value}\n\t\t\t\t\t\tformatter={format_gallery_for_sharing}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t\t{#each _value as [image, caption], i}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"thumbnail-item thumbnail-lg\"\n\t\t\t\t\tclass:selected={selected_index === i}\n\t\t\t\t\ton:click={() => (selected_index = i)}\n\t\t\t\t\taria-label={\"Thumbnail \" + (i + 1) + \" of \" + _value.length}\n\t\t\t\t>\n\t\t\t\t\t<img\n\t\t\t\t\t\talt={caption || \"\"}\n\t\t\t\t\t\tsrc={typeof image === \"string\" ? image : image.data}\n\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t/>\n\t\t\t\t\t{#if caption}\n\t\t\t\t\t\t<div class=\"caption-label\">\n\t\t\t\t\t\t\t{caption}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t</button>\n\t\t\t{/each}\n\t\t</div>\n\t</div>\n{/if}\n\n<style lang=\"postcss\">\n\t.preview {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tbottom: 0px;\n\t\tleft: 0px;\n\t\tflex-direction: column;\n\t\tz-index: var(--layer-2);\n\t\tbackdrop-filter: blur(8px);\n\t\tbackground: var(--background-fill-primary);\n\t\theight: var(--size-full);\n\t}\n\n\t.fixed-height {\n\t\tmin-height: var(--size-80);\n\t\tmax-height: 55vh;\n\t}\n\n\t@media (--screen-xl) {\n\t\t.fixed-height {\n\t\t\tmin-height: 450px;\n\t\t}\n\t}\n\n\t.image-button {\n\t\theight: calc(100% - 60px);\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t}\n\t.preview img {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n\n\t.preview img.with-caption {\n\t\theight: var(--size-full);\n\t}\n\n\t.caption {\n\t\tpadding: var(--size-2) var(--size-3);\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\ttext-align: center;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\talign-self: center;\n\t}\n\n\t.thumbnails {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-14);\n\t\toverflow-x: scroll;\n\t}\n\n\t.thumbnail-item {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow:\n\t\t\t0 0 0 2px var(--ring-color),\n\t\t\tvar(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\taspect-ratio: var(--ratio-square);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: clip;\n\t}\n\n\t.thumbnail-item:hover {\n\t\t--ring-color: var(--color-accent);\n\t\tfilter: brightness(1.1);\n\t}\n\n\t.thumbnail-item.selected {\n\t\t--ring-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small {\n\t\tflex: none;\n\t\ttransform: scale(0.9);\n\t\ttransition: 0.075s;\n\t\twidth: var(--size-9);\n\t\theight: var(--size-9);\n\t}\n\n\t.thumbnail-small.selected {\n\t\t--ring-color: var(--color-accent);\n\t\ttransform: scale(1);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small > img {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.grid-wrap {\n\t\tposition: relative;\n\t\tpadding: var(--size-2);\n\t\theight: var(--size-full);\n\t\toverflow-y: scroll;\n\t}\n\n\t.grid-container {\n\t\tdisplay: grid;\n\t\tposition: relative;\n\t\tgrid-template-rows: repeat(var(--grid-rows), minmax(100px, 1fr));\n\t\tgrid-template-columns: repeat(var(--grid-cols), minmax(100px, 1fr));\n\t\tgrid-auto-rows: minmax(100px, 1fr);\n\t\tgap: var(--spacing-lg);\n\t}\n\n\t.thumbnail-lg > img {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.thumbnail-lg:hover .caption-label {\n\t\topacity: 0.5;\n\t}\n\n\t.caption-label {\n\t\tposition: absolute;\n\t\tright: var(--block-label-margin);\n\t\tbottom: var(--block-label-margin);\n\t\tz-index: var(--layer-1);\n\t\tborder-top: 1px solid var(--border-color-primary);\n\t\tborder-left: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-label-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--block-label-padding);\n\t\tmax-width: 80%;\n\t\toverflow: hidden;\n\t\tfont-size: var(--block-label-text-size);\n\t\ttext-align: left;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.icon-button {\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tright: 0;\n\t}\n\n\t.icon-buttons a {\n\t\tmargin: var(--size-1) 0;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, ShareData, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Gallery from \"./Gallery.svelte\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { FileData } from \"@gradio/upload\";\n\n\texport let loading_status: LoadingStatus;\n\texport let show_label: boolean;\n\texport let label: string;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: (FileData | string | [FileData | string, string])[] | null =\n\t\tnull;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let columns: number | number[] | undefined = [2];\n\texport let rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let selected_index: number | null = null;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let show_download_button = false;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t}>;\n</script>\n\n<Block\n\t{visible}\n\tvariant=\"solid\"\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\theight={typeof height === \"number\" ? height : undefined}\n>\n\t<StatusTracker {...loading_status} />\n\t<Gallery\n\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t{label}\n\t\t{value}\n\t\t{show_label}\n\t\t{root}\n\t\t{root_url}\n\t\t{columns}\n\t\t{rows}\n\t\t{height}\n\t\t{preview}\n\t\t{object_fit}\n\t\t{allow_preview}\n\t\tbind:selected_index\n\t\t{show_share_button}\n\t\t{show_download_button}\n\t/>\n</Block>\n"], "names": ["has", "find", "iter", "tar", "key", "dequal", "foo", "bar", "ctor", "len", "tmp", "format_gallery_for_sharing", "value", "image", "_", "uploadToHuggingFace", "url", "Image", "ctx", "dirty", "blocklabel_changes", "create_if_block_3", "create_if_block_2", "i", "insert", "target", "div1", "anchor", "append", "div0", "create_if_block_5", "create_if_block_4", "src_url_equal", "img", "img_src_value", "attr", "img_alt_value", "img_title_value", "button1", "button0", "current", "Download", "getHrefValue", "a", "iconbutton_changes", "caption", "set_data", "t", "t_value", "toggle_class", "button", "div", "create_if_block_1", "create_if_block_6", "isFileData", "obj", "selected", "show_label", "$$props", "label", "root", "root_url", "columns", "rows", "height", "preview", "allow_preview", "object_fit", "show_share_button", "show_download_button", "selected_index", "dispatch", "createEventDispatcher", "was_reset", "_value", "prevValue", "old_selected_index", "handle_preview_click", "event", "element", "centerX", "$$invalidate", "previous", "next", "on_keydown", "e", "el", "container_element", "scroll_to_img", "index", "tick", "container_left", "container_width", "left", "width", "pos", "client_height", "window_height", "clear_handler", "$$value", "click_handler_1", "click_handler_2", "normalise_file", "block_changes", "loading_status", "elem_id", "elem_classes", "visible", "container", "scale", "min_width", "gradio", "change_handler"], "mappings": "4wBAAA,IAAIA,GAAM,OAAO,UAAU,eAE3B,SAASC,GAAKC,EAAMC,EAAKC,EAAK,CAC7B,IAAKA,KAAOF,EAAK,OAChB,GAAIG,EAAOD,EAAKD,CAAG,EAAG,OAAOC,CAE/B,CAEO,SAASC,EAAOC,EAAKC,EAAK,CAChC,IAAIC,EAAMC,EAAKC,EACf,GAAIJ,IAAQC,EAAK,MAAO,GAExB,GAAID,GAAOC,IAAQC,EAAKF,EAAI,eAAiBC,EAAI,YAAa,CAC7D,GAAIC,IAAS,KAAM,OAAOF,EAAI,YAAcC,EAAI,UAChD,GAAIC,IAAS,OAAQ,OAAOF,EAAI,aAAeC,EAAI,WAEnD,GAAIC,IAAS,MAAO,CACnB,IAAKC,EAAIH,EAAI,UAAYC,EAAI,OAC5B,KAAOE,KAASJ,EAAOC,EAAIG,CAAG,EAAGF,EAAIE,CAAG,CAAC,GAAE,CAE5C,OAAOA,IAAQ,GAGhB,GAAID,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EACFC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACH,EAAI,IAAIG,CAAG,EAAG,MAAO,GAE3B,MAAO,GAGR,GAAIF,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EAAI,CAAC,EACPC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACL,EAAOI,EAAI,CAAC,EAAGF,EAAI,IAAIG,CAAG,CAAC,EAC/B,MAAO,GAGT,MAAO,GAGR,GAAIF,IAAS,YACZF,EAAM,IAAI,WAAWA,CAAG,EACxBC,EAAM,IAAI,WAAWA,CAAG,UACdC,IAAS,SAAU,CAC7B,IAAKC,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAI,QAAQG,CAAG,IAAMF,EAAI,QAAQE,CAAG,GAAE,CAEvD,OAAOA,IAAQ,GAGhB,GAAI,YAAY,OAAOH,CAAG,EAAG,CAC5B,IAAKG,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAIG,CAAG,IAAMF,EAAIE,CAAG,GAAE,CAEvC,OAAOA,IAAQ,GAGhB,GAAI,CAACD,GAAQ,OAAOF,GAAQ,SAAU,CACrCG,EAAM,EACN,IAAKD,KAAQF,EAEZ,GADIN,GAAI,KAAKM,EAAKE,CAAI,GAAK,EAAEC,GAAO,CAACT,GAAI,KAAKO,EAAKC,CAAI,GACnD,EAAEA,KAAQD,IAAQ,CAACF,EAAOC,EAAIE,CAAI,EAAGD,EAAIC,CAAI,CAAC,EAAG,MAAO,GAE7D,OAAO,OAAO,KAAKD,CAAG,EAAE,SAAWE,GAIrC,OAAOH,IAAQA,GAAOC,IAAQA,CAC/B,CChFA,eAAsBI,GACrBC,EACkB,CAClB,OAAKA,EAQE,2DAPI,MAAM,QAAQ,IACxBA,EAAM,IAAI,MAAO,CAACC,EAAOC,CAAC,IACrBD,IAAU,KAAa,GACpB,MAAME,GAAoBF,EAAM,KAAM,KAAK,CAClD,CAAA,GAIA,IAAKG,GAAQ,aAAaA,6BAA+B,EACzD,KAAK,EAAE,UAVU,EAWpB,oPCqKgCC,GAAc,MAAAC,MAAS,wGAATC,EAAA,CAAA,EAAA,IAAAC,EAAA,MAAAF,MAAS,sIAKjDA,EAAc,CAAA,IAAK,MAAQA,EAAa,CAAA,GAAAG,GAAAH,CAAA,IA2EtCA,EAAiB,CAAA,GAAAI,GAAAJ,CAAA,MAUfA,EAAM,EAAA,CAAA,uBAAX,OAAIK,GAAA,uLAbcL,EAAO,CAAA,CAAA,oBAAgBA,EAAI,CAAA,CAAA,qBAAkBA,EAAU,CAAA,CAAA,eAAYA,EAAM,CAAA,CAAA,aACjFA,EAAU,CAAA,CAAA,mFALFA,EAAM,CAAA,GAAIA,EAAM,CAAA,GAAI,MAAM,+BAHhDM,EAwCKC,EAAAC,EAAAC,CAAA,EAnCJC,EAkCKF,EAAAG,CAAA,+GAxGDX,EAAc,CAAA,IAAK,MAAQA,EAAa,CAAA,8GA2EtCA,EAAiB,CAAA,kHAUfA,EAAM,EAAA,CAAA,oBAAX,OAAIK,GAAA,EAAA,mHAAJ,wCAbkBL,EAAO,CAAA,CAAA,mCAAgBA,EAAI,CAAA,CAAA,qCAAkBA,EAAU,CAAA,CAAA,8BAAYA,EAAM,CAAA,CAAA,2BACjFA,EAAU,CAAA,CAAA,qCALFA,EAAM,CAAA,GAAIA,EAAM,CAAA,GAAI,MAAM,mKAtE3B,+SAKbA,EAAoB,EAAA,GAAAY,GAAAZ,CAAA,4BAWd,EAAK,CAAA,CAAA,6BAqBZA,EAAM,EAAA,EAACA,EAAc,CAAA,CAAA,EAAE,CAAC,GAAAa,GAAAb,CAAA,MAUrBA,EAAM,EAAA,CAAA,uBAAX,OAAIK,GAAA,2QAjBAS,EAAAC,EAAA,IAAAC,EAAAhB,EAAO,EAAA,EAAAA,EAAgB,CAAA,CAAA,EAAA,CAAC,EAAE,IAAI,GAAAiB,EAAAF,EAAA,MAAAC,CAAA,EAC9BC,EAAAF,EAAA,MAAAG,EAAAlB,EAAO,EAAA,EAAAA,EAAgB,CAAA,CAAA,EAAA,CAAC,GAAK,EAAE,EAC7BiB,EAAAF,EAAA,QAAAI,EAAAnB,EAAO,EAAA,EAAAA,EAAgB,CAAA,CAAA,EAAA,CAAC,GAAK,IAAI,0EAClBA,EAAM,EAAA,EAACA,EAAc,CAAA,CAAA,EAAE,CAAC,CAAA,yEAVnBA,EAAM,EAAA,EAACA,EAAc,CAAA,CAAA,EAAE,CAAC,EACjD,OACA,QAAM,GAAA,yLAtBXM,EA6DQC,EAAAa,EAAAX,CAAA,EA5DPC,EAeKU,EAAAT,CAAA,2CACLD,EAgBQU,EAAAC,CAAA,EARPX,EAOCW,EAAAN,CAAA,+BAOFL,EAqBKU,EAAAZ,CAAA,yGA5DcR,EAAU,EAAA,CAAA,oBAEvBA,EAAoB,EAAA,qGAyBnB,CAAAsB,GAAArB,EAAA,CAAA,EAAA,MAAA,CAAAa,EAAAC,EAAA,IAAAC,EAAAhB,EAAO,EAAA,EAAAA,EAAgB,CAAA,CAAA,EAAA,CAAC,EAAE,IAAI,kBAC9B,CAAAsB,GAAArB,EAAA,CAAA,EAAA,MAAAiB,KAAAA,EAAAlB,EAAO,EAAA,EAAAA,EAAgB,CAAA,CAAA,EAAA,CAAC,GAAK,oBAC3B,CAAAsB,GAAArB,EAAA,CAAA,EAAA,MAAAkB,KAAAA,EAAAnB,EAAO,EAAA,EAAAA,EAAgB,CAAA,CAAA,EAAA,CAAC,GAAK,6DACdA,EAAM,EAAA,EAACA,EAAc,CAAA,CAAA,EAAE,CAAC,CAAA,gDAVnBA,EAAM,EAAA,EAACA,EAAc,CAAA,CAAA,EAAE,CAAC,EACjD,OACA,QAAM,GAAA,EAYLA,EAAM,EAAA,EAACA,EAAc,CAAA,CAAA,EAAE,CAAC,wEAUrBA,EAAM,EAAA,CAAA,oBAAX,OAAIK,GAAA,EAAA,mHAAJ,iNApCkBkB,GAAiB,MAAAvB,MAAG,iBAAiB,iDAJjDwB,GAAaxB,EAAK,CAAA,EAACA,EAAc,CAAA,CAAA,CAAA,CAAA,EAC/BiB,EAAAQ,EAAA,SAAA,OAAO,aAAe,SAAW,IAAI,+DAF9CnB,EAMGC,EAAAkB,EAAAhB,CAAA,sCADiCR,EAAA,CAAA,EAAA,QAAAyB,EAAA,MAAA1B,MAAG,iBAAiB,iCAJjDwB,GAAaxB,EAAK,CAAA,EAACA,EAAc,CAAA,CAAA,CAAA,gIAgCvCA,EAAM,EAAA,EAACA,EAAc,CAAA,CAAA,EAAE,CAAC,EAAA,oFAD1BM,EAESC,EAAAoB,EAAAlB,CAAA,mCADPT,EAAM,EAAA,EAACA,EAAc,CAAA,CAAA,EAAE,CAAC,EAAA,KAAA4B,GAAAC,EAAAC,CAAA,6LAiBlB9B,EAAK,EAAA,EAAC,CAAC,EAAE,IAAI,GAAAiB,EAAAF,EAAA,MAAAC,CAAA,gBACXhB,EAAK,EAAA,EAAC,CAAC,GAAK,IAAI,kJAJZ,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAM,EAAA,EAAC,MAAM,EAD3C+B,EAAAC,EAAA,WAAAhC,OAAmBA,EAAC,EAAA,CAAA,UAJrCM,EAaQC,EAAAyB,EAAAvB,CAAA,EANPC,EAKCsB,EAAAjB,CAAA,0EAJKf,EAAK,EAAA,EAAC,CAAC,EAAE,IAAI,kCACXA,EAAK,EAAA,EAAC,CAAC,GAAK,wCAJR,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAM,EAAA,EAAC,kEADrC+B,EAAAC,EAAA,WAAAhC,OAAmBA,EAAC,EAAA,CAAA,sFA8B7BA,EAAM,EAAA,YACFP,kIALba,EAOKC,EAAA0B,EAAAxB,CAAA,0DAHIT,EAAM,EAAA,0HAmBXA,EAAO,EAAA,EAAA,sFADTM,EAEKC,EAAA0B,EAAAxB,CAAA,mCADHT,EAAO,EAAA,EAAA,KAAA4B,GAAAC,EAAAC,CAAA,yDAFL9B,EAAO,EAAA,GAAAkC,GAAAlC,CAAA,4FAJNiB,EAAAF,EAAA,MAAAG,EAAAlB,OAAW,EAAE,EACNc,EAAAC,EAAA,IAAAC,EAAA,OAAAhB,OAAU,SAAWA,EAAK,EAAA,EAAGA,MAAM,IAAI,GAAAiB,EAAAF,EAAA,MAAAC,CAAA,iIAJxC,cAAgBhB,EAAI,EAAA,EAAA,GAAK,OAASA,EAAM,EAAA,EAAC,MAAM,EAF3C+B,EAAAC,EAAA,WAAAhC,OAAmBA,EAAC,EAAA,CAAA,UAFrCM,EAgBQC,EAAAyB,EAAAvB,CAAA,EAVPC,EAICsB,EAAAjB,CAAA,sEAHKd,EAAA,CAAA,EAAA,MAAAiB,KAAAA,EAAAlB,OAAW,kBACJC,EAAA,CAAA,EAAA,MAAA,CAAAa,EAAAC,EAAA,IAAAC,EAAA,OAAAhB,OAAU,SAAWA,EAAK,EAAA,EAAGA,MAAM,IAAI,gBAG/CA,EAAO,EAAA,2EAPA,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAM,EAAA,EAAC,qCAFrC+B,EAAAC,EAAA,WAAAhC,OAAmBA,EAAC,EAAA,CAAA,4PA9FpCA,EAAU,CAAA,GAAAmC,GAAAnC,CAAA,8CAGVA,EAAK,CAAA,IAAK,MAAQA,EAAM,EAAA,IAAK,MAAQA,EAAM,EAAA,EAAC,SAAW,EAAC,kKAHxDA,EAAU,CAAA,0VAlEL,SAAAoC,GAAWC,EAAQ,eACbA,GAAQ,UAAYA,IAAQ,MAAQ,SAAUA,EAGpD,SAAAb,GAAac,EAAa,CAC9B,OAAAF,GAAWE,CAAQ,EACfA,EAAS,KACC,OAAAA,GAAa,SACvBA,EACG,MAAM,QAAQA,CAAQ,EACzBd,GAAac,EAAS,CAAC,CAAA,EAExB,sDAhHG,GAAA,CAAA,WAAAC,EAAa,EAAI,EAAAC,GACjB,MAAAC,CAAa,EAAAD,EACb,CAAA,KAAAE,EAAO,EAAE,EAAAF,EACT,CAAA,SAAAG,EAA0B,IAAI,EAAAH,EAC9B,CAAA,MAAA9C,EACV,IAAI,EAAA8C,EACM,CAAA,QAAAI,GAA0C,CAAC,CAAA,EAAAJ,EAC3C,CAAA,KAAAK,EAAsC,MAAS,EAAAL,EAC/C,CAAA,OAAAM,EAA0B,MAAM,EAAAN,GAChC,QAAAO,CAAgB,EAAAP,EAChB,CAAA,cAAAQ,EAAgB,EAAI,EAAAR,EACpB,CAAA,WAAAS,EACV,OAAO,EAAAT,EACG,CAAA,kBAAAU,EAAoB,EAAK,EAAAV,EACzB,CAAA,qBAAAW,EAAuB,EAAK,EAAAX,EAC5B,CAAA,eAAAY,EAAgC,IAAI,EAAAZ,EAEzC,MAAAa,EAAWC,KAMb,IAAAC,EAAY,GAIZC,EAA6C,KAU7CC,EACH/D,EACG0D,IAAmB,MAAQL,GAAWrD,GAAO,SAChD0D,EAAiB,GAEd,IAAAM,EAAoCN,EA0B/B,SAAAO,EAAqBC,EAAiB,OACxCC,EAAUD,EAAM,OAChB,EAAIA,EAAM,QAEVE,EADQD,EAAQ,YACE,EAEpB,EAAIC,EACPC,EAAA,EAAAX,EAAiBY,CAAQ,EAEzBD,EAAA,EAAAX,EAAiBa,CAAI,EAId,SAAAC,EAAWC,EAAgB,CAC3B,OAAAA,EAAE,KAAI,KACR,SACJA,EAAE,eAAc,EAChBJ,EAAA,EAAAX,EAAiB,IAAI,YAEjB,YACJe,EAAE,eAAc,EAChBJ,EAAA,EAAAX,EAAiBY,CAAQ,YAErB,aACJG,EAAE,eAAc,EAChBJ,EAAA,EAAAX,EAAiBa,CAAI,aAsCpBG,EAAE,CAAA,EACFC,EAEW,eAAAC,EAAcC,EAAoB,CACrC,GAAA,OAAAA,GAAU,SAAQ,aACvBC,GAAI,EAEVJ,EAAGG,CAAK,EAAE,cAEF,KAAME,EAAgB,MAAOC,GACpCL,EAAkB,wBACX,CAAA,KAAAM,GAAM,MAAAC,CAAK,EAAKR,EAAGG,CAAK,EAAE,wBAI5BM,GAFgBF,GAAOF,EAI5BG,EAAQ,EACRF,EAAkB,EAClBL,EAAkB,WAEfA,GAA4B,OAAAA,EAAkB,UAAa,YAC9DA,EAAkB,SAAQ,CACzB,KAAMQ,GAAM,EAAI,EAAIA,GACpB,SAAU,WAKT,IAAAC,EAAgB,EAChBC,GAAgB,wCA0BC,MAAAC,GAAA,IAAAjB,EAAA,EAAAX,EAAiB,IAAI,KAK5BQ,GAAUD,EAAqBC,CAAK,+CA2BlCQ,EAAG/D,CAAC,EAAA4E,YACE,MAAAC,GAAA7E,GAAA0D,EAAA,EAAAX,EAAiB/C,CAAC,6CAP1BgE,EAAiBY,8EAgDV,MAAAE,GAAA9E,GAAA0D,EAAA,EAAAX,EAAiB/C,CAAC,gBAvBnByE,EAAa,KAAA,qlBArN9Bf,EAAA,GAAAR,EAAY7D,GAAS,MAAQA,EAAM,QAAU,EAAI,GAAO6D,CAAS,yBAGjEQ,EAAA,GAAAP,EACF9D,IAAU,KACP,KACAA,EAAM,IAAKqB,GACX,MAAM,QAAQA,CAAG,GACbqE,GAAerE,EAAI,CAAC,EAAG2B,EAAMC,CAAQ,EAAe5B,EAAI,CAAC,CAAA,EACzD,CAAAqE,GAAerE,EAAK2B,EAAMC,CAAQ,EAAe,IAAI,CAAA,CAAA,2BAUrDxD,EAAOsE,EAAW/D,CAAK,IAG1B6D,OACHH,EAAiBL,GAAWrD,GAAO,OAAS,EAAI,IAAI,EACpDqE,EAAA,GAAAR,EAAY,EAAK,OAIjBH,EACCA,IAAmB,MACnB1D,IAAU,MACV0D,EAAiB1D,EAAM,OACpB0D,EACA,MAELC,EAAS,QAAQ,EACjBU,EAAA,GAAAN,EAAY/D,CAAK,yBAGfsE,IACAZ,GAAkB,IAAMI,GAAQ,QAAU,GAAK,IAAMA,GAAQ,QAAU,yBACvES,IAASb,GAAkB,GAAK,IAAMI,GAAQ,QAAU,4BAkDtDJ,IAAmBM,IACtBK,EAAA,GAAAL,EAAqBN,CAAc,EAC/BA,IAAmB,MACtBC,EAAS,SAAQ,CAChB,MAAOD,EACP,MAAQ,CAAAI,IAASJ,CAAc,EAAE,CAAC,EAAE,KAAMI,IAASJ,CAAc,EAAE,CAAC,CAAA,wBAMjEJ,GACNsB,EAAclB,CAAc,2XC5FVpD,EAAc,CAAA,CAAA,imBAAdA,EAAc,CAAA,CAAA,CAAA,CAAA,upBATxB,6FAMO,GACD,OAAA,OAAAA,OAAW,SAAWA,EAAM,EAAA,EAAG,8QAA/BC,EAAA,QAAAoF,EAAA,OAAA,OAAArF,OAAW,SAAWA,EAAM,EAAA,EAAG,kKAzCnC,eAAAsF,CAA6B,EAAA9C,GAC7B,WAAAD,CAAmB,EAAAC,GACnB,MAAAC,CAAa,EAAAD,GACb,KAAAE,CAAY,EAAAF,GACZ,SAAAG,CAAuB,EAAAH,EACvB,CAAA,QAAA+C,EAAU,EAAE,EAAA/C,GACZ,aAAAgD,EAAY,EAAA,EAAAhD,EACZ,CAAA,QAAAiD,EAAU,EAAI,EAAAjD,EACd,CAAA,MAAA9C,EACV,IAAI,EAAA8C,EACM,CAAA,UAAAkD,EAAY,EAAI,EAAAlD,EAChB,CAAA,MAAAmD,EAAuB,IAAI,EAAAnD,EAC3B,CAAA,UAAAoD,EAAgC,MAAS,EAAApD,EACzC,CAAA,QAAAI,GAA0C,CAAC,CAAA,EAAAJ,EAC3C,CAAA,KAAAK,EAAsC,MAAS,EAAAL,EAC/C,CAAA,OAAAM,EAA0B,MAAM,EAAAN,GAChC,QAAAO,CAAgB,EAAAP,EAChB,CAAA,cAAAQ,EAAgB,EAAI,EAAAR,EACpB,CAAA,eAAAY,EAAgC,IAAI,EAAAZ,EACpC,CAAA,WAAAS,EACV,OAAO,EAAAT,EACG,CAAA,kBAAAU,EAAoB,EAAK,EAAAV,EACzB,CAAA,qBAAAW,EAAuB,EAAK,EAAAX,GAC5B,OAAAqD,CAKT,EAAArD,2BAiBgB,MAAAsD,EAAA,IAAAD,EAAO,SAAS,SAAUnG,CAAK,IACpCyE,GAAM0B,EAAO,SAAS,SAAU1B,EAAE,MAAM,IACzCA,GAAM0B,EAAO,SAAS,QAAS1B,EAAE,MAAM,IACvCA,GAAM0B,EAAO,SAAS,QAAS1B,EAAE,MAAM", "x_google_ignoreList": [0]}