import{S as c,e as m,s as g,a9 as b,m as r,g as f,K as o,N as d,h as v,j as p,ab as h,ac as w,ad as y,w as j,u as k,k as G}from"./index-c99b2410.js";function S(n){let s,l,u,i;const _=n[4].default,a=b(_,n,n[3],null);return{c(){s=r("div"),l=r("div"),a&&a.c(),f(l,"class","styler svelte-iyf88w"),o(l,"--block-radius","0px"),o(l,"--block-border-width","0px"),o(l,"--layout-gap","1px"),o(l,"--form-gap-width","1px"),o(l,"--button-border-width","0px"),o(l,"--button-large-radius","0px"),o(l,"--button-small-radius","0px"),f(s,"id",n[0]),f(s,"class",u="gr-group "+n[1].join(" ")+" svelte-iyf88w"),d(s,"hide",!n[2])},m(e,t){v(e,s,t),p(s,l),a&&a.m(l,null),i=!0},p(e,[t]){a&&a.p&&(!i||t&8)&&h(a,_,e,e[3],i?y(_,e[3],t,null):w(e[3]),null),(!i||t&1)&&f(s,"id",e[0]),(!i||t&2&&u!==(u="gr-group "+e[1].join(" ")+" svelte-iyf88w"))&&f(s,"class",u),(!i||t&6)&&d(s,"hide",!e[2])},i(e){i||(j(a,e),i=!0)},o(e){k(a,e),i=!1},d(e){e&&G(s),a&&a.d(e)}}}function q(n,s,l){let{$$slots:u={},$$scope:i}=s,{elem_id:_=""}=s,{elem_classes:a=[]}=s,{visible:e=!0}=s;return n.$$set=t=>{"elem_id"in t&&l(0,_=t.elem_id),"elem_classes"in t&&l(1,a=t.elem_classes),"visible"in t&&l(2,e=t.visible),"$$scope"in t&&l(3,i=t.$$scope)},[_,a,e,i,u]}class C extends c{constructor(s){super(),m(this,s,q,S,g,{elem_id:0,elem_classes:1,visible:2})}}const N=C;export{N as default};
//# sourceMappingURL=index-47bfd288.js.map
