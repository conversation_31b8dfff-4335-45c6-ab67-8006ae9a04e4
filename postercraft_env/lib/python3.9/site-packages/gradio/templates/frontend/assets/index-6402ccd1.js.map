{"version": 3, "file": "index-6402ccd1.js", "sources": ["../../../../js/video/static/VideoPreview.svelte", "../../../../js/video/static/StaticVideo.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, afterUpdate, tick } from \"svelte\";\n\timport { BlockLabel, Empty, IconButton, ShareButton } from \"@gradio/atoms\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { Video, Download } from \"@gradio/icons\";\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\n\timport { Player } from \"../shared\";\n\n\texport let value: FileData | null = null;\n\texport let subtitle: FileData | null = null;\n\texport let label: string | undefined = undefined;\n\texport let show_label = true;\n\texport let autoplay: boolean;\n\texport let show_share_button = true;\n\n\tlet old_value: FileData | null = null;\n\tlet old_subtitle: FileData | null = null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tstop: undefined;\n\t}>();\n\n\t$: value && dispatch(\"change\", value);\n\n\tafterUpdate(async () => {\n\t\t// needed to bust subtitle caching issues on Chrome\n\t\tif (\n\t\t\tvalue !== old_value &&\n\t\t\tsubtitle !== old_subtitle &&\n\t\t\told_subtitle !== null\n\t\t) {\n\t\t\told_value = value;\n\t\t\tvalue = null;\n\t\t\tawait tick();\n\t\t\tvalue = old_value;\n\t\t}\n\t\told_value = value;\n\t\told_subtitle = subtitle;\n\t});\n</script>\n\n<BlockLabel {show_label} Icon={Video} label={label || \"Video\"} />\n{#if value === null}\n\t<Empty unpadded_box={true} size=\"large\"><Video /></Empty>\n{:else}\n\t{#key value.data}\n\t\t<Player\n\t\t\tsrc={value.data}\n\t\t\tsubtitle={subtitle?.data}\n\t\t\t{autoplay}\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:stop\n\t\t\ton:end\n\t\t\tmirror={false}\n\t\t\t{label}\n\t\t/>\n\t{/key}\n\t<div class=\"icon-buttons\" data-testid=\"download-div\">\n\t\t<a\n\t\t\thref={value.data}\n\t\t\ttarget={window.__is_colab__ ? \"_blank\" : null}\n\t\t\tdownload={value.orig_name || value.name}\n\t\t>\n\t\t\t<IconButton Icon={Download} label=\"Download\" />\n\t\t</a>\n\t\t{#if show_share_button}\n\t\t\t<ShareButton\n\t\t\t\ton:error\n\t\t\t\ton:share\n\t\t\t\t{value}\n\t\t\t\tformatter={async (value) => {\n\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\tlet url = await uploadToHuggingFace(value.data, \"url\");\n\t\t\t\t\treturn url;\n\t\t\t\t}}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t\tgap: var(--size-1);\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport { Block } from \"@gradio/atoms\";\n\timport StaticVideo from \"./VideoPreview.svelte\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [FileData, FileData | null] | null = null;\n\tlet old_value: [FileData, FileData | null] | null = null;\n\n\texport let label: string;\n\texport let source: \"upload\" | \"webcam\";\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let show_label: boolean;\n\texport let loading_status: LoadingStatus;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\n\texport let container = false;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let autoplay = false;\n\texport let show_share_button = true;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tupload: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tstart_recording: never;\n\t\tstop_recording: never;\n\t\tshare: ShareData;\n\t\terror: string;\n\t}>;\n\n\tlet _video: FileData | null = null;\n\tlet _subtitle: FileData | null = null;\n\n\t$: {\n\t\tif (value != null) {\n\t\t\t_video = normalise_file(value[0], root, root_url);\n\t\t\t_subtitle = normalise_file(value[1], root, root_url);\n\t\t} else {\n\t\t\t_video = null;\n\t\t\t_subtitle = null;\n\t\t}\n\t}\n\n\tlet dragging = false;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null && source === \"upload\" ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{height}\n\t{width}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker {...loading_status} />\n\n\t<StaticVideo\n\t\tvalue={_video}\n\t\tsubtitle={_subtitle}\n\t\t{label}\n\t\t{show_label}\n\t\t{autoplay}\n\t\t{show_share_button}\n\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\ton:end={() => gradio.dispatch(\"end\")}\n\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\ton:error={({ detail }) => gradio.dispatch(\"error\", detail)}\n\t/>\n</Block>\n"], "names": ["previous_key", "ctx", "Download", "create_if_block_1", "attr", "a_href_value", "a_download_value", "insert", "target", "div", "anchor", "append", "dirty", "safe_not_equal", "current", "player_changes", "Video", "blocklabel_changes", "value", "$$props", "subtitle", "label", "show_label", "autoplay", "show_share_button", "old_value", "old_subtitle", "dispatch", "createEventDispatcher", "afterUpdate", "$$invalidate", "tick", "uploadToHuggingFace", "block_changes", "elem_id", "elem_classes", "visible", "source", "root", "root_url", "loading_status", "height", "width", "container", "scale", "min_width", "gradio", "_video", "_subtitle", "share_handler", "detail", "error_handler", "normalise_file"], "mappings": "ytBAkDO,IAAAA,EAAAC,KAAM,kDAmBQC,GAAQ,MAAA,UAAA,UAEtBD,EAAiB,CAAA,GAAAE,EAAAF,CAAA,6EANfG,EAAA,EAAA,OAAAC,EAAAJ,KAAM,IAAI,EACRG,EAAA,EAAA,SAAA,OAAO,aAAe,SAAW,IAAI,EACnCA,EAAA,EAAA,WAAAE,EAAAL,EAAM,CAAA,EAAA,WAAaA,KAAM,IAAI,wGAJzCM,EAoBKC,EAAAC,EAAAC,CAAA,EAnBJC,EAMGF,EAAA,CAAA,iDApBEG,EAAA,GAAAC,EAAAb,EAAAA,EAAAC,KAAM,IAAI,0EAeR,CAAAa,GAAAF,EAAA,GAAAP,KAAAA,EAAAJ,KAAM,uBAEF,CAAAa,GAAAF,EAAA,GAAAN,KAAAA,EAAAL,EAAM,CAAA,EAAA,WAAaA,KAAM,0BAI/BA,EAAiB,CAAA,8RAvBF,sSAId,IAAAA,KAAM,KACD,SAAAA,MAAU,0BAMZ,4JAPHW,EAAA,IAAAG,EAAA,IAAAd,KAAM,MACDW,EAAA,IAAAG,EAAA,SAAAd,MAAU,0pBAPQe,EAAc,MAAAf,MAAS,gDACjD,OAAAA,OAAU,KAAI,2KAD0BW,EAAA,IAAAK,EAAA,MAAAhB,MAAS,oSArC1C,GAAA,CAAA,MAAAiB,EAAyB,IAAI,EAAAC,EAC7B,CAAA,SAAAC,EAA4B,IAAI,EAAAD,EAChC,CAAA,MAAAE,EAA4B,MAAS,EAAAF,EACrC,CAAA,WAAAG,EAAa,EAAI,EAAAH,GACjB,SAAAI,CAAiB,EAAAJ,EACjB,CAAA,kBAAAK,EAAoB,EAAI,EAAAL,EAE/BM,EAA6B,KAC7BC,EAAgC,KAE9B,MAAAC,EAAWC,KAUjBC,GAAW,SAAA,CAGTX,IAAUO,GACVL,IAAaM,GACbA,IAAiB,OAEjBD,EAAYP,EACZY,EAAA,EAAAZ,EAAQ,IAAI,QACNa,GAAI,EACVD,EAAA,EAAAZ,EAAQO,CAAS,GAElBA,EAAYP,EACZQ,EAAeN,8IAkCKF,GACZA,QACWc,GAAoBd,EAAM,KAAM,KAAK,EADlC,0UAlDpBA,GAASS,EAAS,SAAUT,CAAK,yMCyDjBjB,EAAc,CAAA,CAAA,sGAGzBA,EAAM,EAAA,WACHA,EAAS,EAAA,qTAJDA,EAAc,CAAA,CAAA,CAAA,CAAA,4CAGzBA,EAAM,EAAA,yBACHA,EAAS,EAAA,6TAhBX,QAAAA,EAAU,CAAA,IAAA,MAAQA,OAAW,SAAW,SAAW,oBAC1B,eACzB,oHAQO,oIAVPW,EAAA,KAAAqB,EAAA,QAAAhC,EAAU,CAAA,IAAA,MAAQA,OAAW,SAAW,SAAW,6UA3DjD,GAAA,CAAA,QAAAiC,EAAU,EAAE,EAAAf,GACZ,aAAAgB,EAAY,EAAA,EAAAhB,EACZ,CAAA,QAAAiB,EAAU,EAAI,EAAAjB,EACd,CAAA,MAAAD,EAA4C,IAAI,EAAAC,EACvDM,EAAgD,MAEzC,MAAAJ,CAAa,EAAAF,GACb,OAAAkB,CAA2B,EAAAlB,GAC3B,KAAAmB,CAAY,EAAAnB,GACZ,SAAAoB,CAAuB,EAAApB,GACvB,WAAAG,CAAmB,EAAAH,GACnB,eAAAqB,CAA6B,EAAArB,GAC7B,OAAAsB,CAA0B,EAAAtB,GAC1B,MAAAuB,CAAyB,EAAAvB,EAEzB,CAAA,UAAAwB,EAAY,EAAK,EAAAxB,EACjB,CAAA,MAAAyB,EAAuB,IAAI,EAAAzB,EAC3B,CAAA,UAAA0B,EAAgC,MAAS,EAAA1B,EACzC,CAAA,SAAAI,EAAW,EAAK,EAAAJ,EAChB,CAAA,kBAAAK,EAAoB,EAAI,EAAAL,GACxB,OAAA2B,CAYT,EAAA3B,EAEE4B,EAA0B,KAC1BC,EAA6B,iBA6CjBF,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,QACxBA,EAAO,SAAS,MAAM,QACvBA,EAAO,SAAS,KAAK,EACtBG,EAAA,CAAA,CAAA,OAAAC,KAAaJ,EAAO,SAAS,QAASI,CAAM,EAC5CC,EAAA,CAAA,CAAA,OAAAD,KAAaJ,EAAO,SAAS,QAASI,CAAM,qqBA/CrDhC,GAAS,WACZ6B,EAASK,EAAelC,EAAM,CAAC,EAAGoB,EAAMC,CAAQ,CAAA,OAChDS,EAAYI,EAAelC,EAAM,CAAC,EAAGoB,EAAMC,CAAQ,CAAA,IAEnDT,EAAA,GAAAiB,EAAS,IAAI,EACbjB,EAAA,GAAAkB,EAAY,IAAI,wBAOb,KAAK,UAAU9B,CAAK,IAAM,KAAK,UAAUO,CAAS,IACrDK,EAAA,GAAAL,EAAYP,CAAK,EACjB4B,EAAO,SAAS,QAAQ"}