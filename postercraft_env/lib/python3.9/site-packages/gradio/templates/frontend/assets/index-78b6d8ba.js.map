{"version": 3, "file": "index-78b6d8ba.js", "sources": ["../../../../js/audio/static/AudioPlayer.svelte", "../../../../js/audio/static/StaticAudio.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\timport { Empty } from \"@gradio/atoms\";\n\timport { _ } from \"svelte-i18n\";\n\texport interface AudioData extends FileData {\n\t\tcrop_min?: number;\n\t\tcrop_max?: number;\n\t}\n</script>\n\n<script lang=\"ts\">\n\timport { createEventDispatcher, tick } from \"svelte\";\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport { BlockLabel, ShareButton, IconButton } from \"@gradio/atoms\";\n\timport { Music, Download } from \"@gradio/icons\";\n\n\timport { loaded } from \"../shared/utils\";\n\n\texport let value: null | { name: string; data: string } = null;\n\texport let label: string;\n\texport let name: string;\n\texport let show_label = true;\n\texport let autoplay: boolean;\n\texport let show_download_button = true;\n\texport let show_share_button = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: AudioData;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tstop: undefined;\n\t}>();\n\n\t$: value &&\n\t\tdispatch(\"change\", {\n\t\t\tname: name,\n\t\t\tdata: value?.data\n\t\t});\n\n\tfunction handle_ended(): void {\n\t\tdispatch(\"stop\");\n\t\tdispatch(\"end\");\n\t}\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={Music}\n\tfloat={false}\n\tlabel={label || $_(\"audio.audio\")}\n/>\n{#if value !== null}\n\t<div class=\"icon-buttons\">\n\t\t{#if show_download_button}\n\t\t\t<a\n\t\t\t\thref={value.data}\n\t\t\t\ttarget={window.__is_colab__ ? \"_blank\" : null}\n\t\t\t\tdownload={value.name}\n\t\t\t>\n\t\t\t\t<IconButton Icon={Download} label={$_(\"common.download\")} />\n\t\t\t</a>\n\t\t{/if}\n\t\t{#if show_share_button}\n\t\t\t<ShareButton\n\t\t\t\ton:error\n\t\t\t\ton:share\n\t\t\t\tformatter={async (value) => {\n\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\tlet url = await uploadToHuggingFace(value.data, \"url\");\n\t\t\t\t\treturn `<audio controls src=\"${url}\"></audio>`;\n\t\t\t\t}}\n\t\t\t\t{value}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n{/if}\n\n{#if value === null}\n\t<Empty size=\"small\">\n\t\t<Music />\n\t</Empty>\n{:else}\n\t<audio\n\t\tuse:loaded={{ autoplay }}\n\t\tcontrols\n\t\tpreload=\"metadata\"\n\t\tsrc={value?.data}\n\t\ton:play\n\t\ton:pause\n\t\ton:ended={handle_ended}\n\t\tdata-testid={`${label}-audio`}\n\t/>\n{/if}\n\n<style>\n\taudio {\n\t\tpadding: var(--size-2);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-14);\n\t}\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t\tgap: var(--size-1);\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\timport { _ } from \"svelte-i18n\";\n\n\timport type { FileData } from \"@gradio/upload\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport StaticAudio from \"./AudioPlayer.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { Block } from \"@gradio/atoms\";\n\n\timport { normalise_file } from \"@gradio/upload\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let mode: \"static\" | \"interactive\";\n\texport let value: null | FileData | string = null;\n\texport let source: \"microphone\" | \"upload\";\n\texport let label: string;\n\texport let root: string;\n\texport let show_label: boolean;\n\texport let root_url: null | string;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let autoplay = false;\n\texport let show_download_button = true;\n\texport let show_share_button = false;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tshare: ShareData;\n\t\terror: string;\n\t}>;\n\n\tlet old_value: null | FileData | string = null;\n\n\tlet _value: null | FileData;\n\t$: _value = normalise_file(value, root, root_url);\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\tlet dragging: boolean;\n</script>\n\n<Block\n\tvariant={mode === \"interactive\" && value === null && source === \"upload\"\n\t\t? \"dashed\"\n\t\t: \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\n\t<StaticAudio\n\t\t{autoplay}\n\t\t{show_label}\n\t\t{show_download_button}\n\t\t{show_share_button}\n\t\tvalue={_value}\n\t\tname={_value?.name || \"audio_file\"}\n\t\t{label}\n\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t/>\n</Block>\n"], "names": ["ctx", "create_if_block_3", "create_if_block_2", "insert", "target", "div", "anchor", "Download", "attr", "a", "a_href_value", "a_download_value", "dirty", "iconbutton_changes", "current", "src_url_equal", "audio", "audio_src_value", "action_destroyer", "loaded_action", "loaded", "is_function", "Music", "if_block0", "create_if_block_1", "value", "$$props", "label", "name", "show_label", "autoplay", "show_download_button", "show_share_button", "dispatch", "createEventDispatcher", "handle_ended", "uploadToHuggingFace", "elem_id", "elem_classes", "visible", "mode", "source", "root", "root_url", "container", "scale", "min_width", "loading_status", "gradio", "old_value", "_value", "dragging", "e", "$$invalidate", "normalise_file"], "mappings": "opBAsDOA,EAAoB,CAAA,GAAAC,EAAAD,CAAA,IASpBA,EAAiB,CAAA,GAAAE,EAAAF,CAAA,kGAVvBG,EAsBKC,EAAAC,EAAAC,CAAA,qDArBCN,EAAoB,CAAA,8FASpBA,EAAiB,CAAA,2OAHFO,GAAiB,MAAAP,KAAG,iBAAiB,oCAJjDQ,EAAAC,EAAA,OAAAC,EAAAV,KAAM,IAAI,EACRQ,EAAAC,EAAA,SAAA,OAAO,aAAe,SAAW,IAAI,EACnCD,EAAAC,EAAA,WAAAE,EAAAX,KAAM,IAAI,UAHrBG,EAMGC,EAAAK,EAAAH,CAAA,sCADiCM,EAAA,KAAAC,EAAA,MAAAb,KAAG,iBAAiB,cAJjD,CAAAc,GAAAF,EAAA,GAAAF,KAAAA,EAAAV,KAAM,uBAEF,CAAAc,GAAAF,EAAA,GAAAD,KAAAA,EAAAX,KAAM,kfA6Bbe,EAAAC,EAAA,IAAAC,EAAAjB,MAAO,IAAI,GAAAQ,EAAAQ,EAAA,MAAAC,CAAA,yBAIAjB,EAAK,CAAA,SAAA,uCARtBG,EASCC,EAAAY,EAAAV,CAAA,SARcY,GAAAC,EAAAC,GAAA,KAAA,KAAAJ,EAAA,CAAA,SAAAhB,EAAQ,CAAA,CAAA,CAAA,CAAA,kDAMZA,EAAY,CAAA,CAAA,iBAHjBY,EAAA,GAAA,CAAAG,EAAAC,EAAA,IAAAC,EAAAjB,MAAO,IAAI,+BAIAA,EAAK,CAAA,iCAPPmB,GAAAE,GAAAF,EAAA,MAAA,GAAAP,EAAA,GAAAO,EAAA,OAAA,KAAA,KAAA,CAAA,SAAAnB,EAAQ,CAAA,CAAA,CAAA,sjBApCjBsB,QACC,SACAtB,EAAK,CAAA,GAAIA,EAAE,CAAA,EAAC,aAAa,KAE5B,IAAAuB,EAAAvB,OAAU,MAAIwB,EAAAxB,CAAA,uCA0Bd,OAAAA,OAAU,KAAI,8NA5BXA,EAAK,CAAA,GAAIA,EAAE,CAAA,EAAC,aAAa,aAE5BA,OAAU,8aAlCH,GAAA,CAAA,MAAAyB,EAA+C,IAAI,EAAAC,GACnD,MAAAC,CAAa,EAAAD,GACb,KAAAE,CAAY,EAAAF,EACZ,CAAA,WAAAG,EAAa,EAAI,EAAAH,GACjB,SAAAI,CAAiB,EAAAJ,EACjB,CAAA,qBAAAK,EAAuB,EAAI,EAAAL,EAC3B,CAAA,kBAAAM,EAAoB,EAAK,EAAAN,EAE9B,MAAAO,EAAWC,cAcRC,GAAY,CACpBF,EAAS,MAAM,EACfA,EAAS,KAAK,8EAyBMR,GACZA,gCACWW,GAAoBX,EAAM,KAAM,KAAK,cADlC,8XAlCpBA,GACFQ,EAAS,SACF,CAAA,KAAAL,EACN,KAAMH,GAAO,IAAA,CAAA,8NC6BIzB,EAAc,EAAA,CAAA,wLAOzBA,EAAM,EAAA,OACPA,EAAM,EAAA,GAAE,MAAQ,0LARJA,EAAc,EAAA,CAAA,CAAA,CAAA,8KAOzBA,EAAM,EAAA,oBACPA,EAAM,EAAA,GAAE,MAAQ,yOApBdA,EAAI,CAAA,IAAK,eAAiBA,OAAU,MAAQA,EAAM,CAAA,IAAK,SAC7D,SACA,oBACUA,EAAQ,EAAA,EAAG,QAAU,eACzB,qNAJAA,EAAI,CAAA,IAAK,eAAiBA,OAAU,MAAQA,EAAM,CAAA,IAAK,SAC7D,SACA,gTAzCQ,GAAA,CAAA,QAAAqC,EAAU,EAAE,EAAAX,GACZ,aAAAY,EAAY,EAAA,EAAAZ,EACZ,CAAA,QAAAa,EAAU,EAAI,EAAAb,GACd,KAAAc,CAA8B,EAAAd,EAC9B,CAAA,MAAAD,EAAkC,IAAI,EAAAC,GACtC,OAAAe,CAA+B,EAAAf,GAC/B,MAAAC,CAAa,EAAAD,GACb,KAAAgB,CAAY,EAAAhB,GACZ,WAAAG,CAAmB,EAAAH,GACnB,SAAAiB,CAAuB,EAAAjB,EACvB,CAAA,UAAAkB,EAAY,EAAI,EAAAlB,EAChB,CAAA,MAAAmB,EAAuB,IAAI,EAAAnB,EAC3B,CAAA,UAAAoB,EAAgC,MAAS,EAAApB,GACzC,eAAAqB,CAA6B,EAAArB,EAC7B,CAAA,SAAAI,EAAW,EAAK,EAAAJ,EAChB,CAAA,qBAAAK,EAAuB,EAAI,EAAAL,EAC3B,CAAA,kBAAAM,EAAoB,EAAK,EAAAN,GACzB,OAAAsB,CAIT,EAAAtB,EAEEuB,EAAsC,KAEtCC,EAUAC,UA0BQC,GAAMJ,EAAO,SAAS,QAASI,EAAE,MAAM,IACvCA,GAAMJ,EAAO,SAAS,QAASI,EAAE,MAAM,8rBApClDC,EAAA,GAAEH,EAASI,GAAe7B,EAAOiB,EAAMC,CAAQ,CAAA,sBAG3C,KAAK,UAAUlB,CAAK,IAAM,KAAK,UAAUwB,CAAS,IACrDI,EAAA,GAAAJ,EAAYxB,CAAK,EACjBuB,EAAO,SAAS,QAAQ"}