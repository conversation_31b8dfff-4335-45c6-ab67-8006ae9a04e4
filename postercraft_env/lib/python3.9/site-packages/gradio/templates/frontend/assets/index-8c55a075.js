import{S as D,e as F,s as G,f as O,g as u,h as q,j as g,n as E,k as p,m as w,t as I,o as j,N as A,K as V,x as K,C as y,I as Q,Q as U,O as x,p as $,F as C,G as N,w as v,u as k,H as Z,a0 as ee,a1 as le,Z as te,ae as ne,V as ae,W as se,r as R,v as W}from"./index-c99b2410.js";import{B as ie}from"./Button-9c502b18.js";import{B as ce}from"./BlockLabel-def07c98.js";import{E as fe}from"./Empty-16e1c9d8.js";function oe(a){let e,t;return{c(){e=O("svg"),t=O("path"),u(t,"fill","currentColor"),u(t,"d","M4 2H2v26a2 2 0 0 0 2 2h26v-2H4v-3h22v-8H4v-4h14V5H4Zm20 17v4H4v-4ZM16 7v4H4V7Z"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),u(e,"aria-hidden","true"),u(e,"role","img"),u(e,"class","iconify iconify--carbon"),u(e,"width","100%"),u(e,"height","100%"),u(e,"preserveAspectRatio","xMidYMid meet"),u(e,"viewBox","0 0 32 32")},m(l,n){q(l,e,n),g(e,t)},p:E,i:E,o:E,d(l){l&&p(e)}}}class X extends D{constructor(e){super(),F(this,e,null,oe,G,{})}}function Y(a,e,t){const l=a.slice();return l[5]=e[t],l[7]=t,l}function z(a){let e,t=Q(a[0].confidences),l=[];for(let n=0;n<t.length;n+=1)l[n]=P(Y(a,t,n));return{c(){for(let n=0;n<l.length;n+=1)l[n].c();e=U()},m(n,s){for(let i=0;i<l.length;i+=1)l[i]&&l[i].m(n,s);q(n,e,s)},p(n,s){if(s&13){t=Q(n[0].confidences);let i;for(i=0;i<t.length;i+=1){const o=Y(n,t,i);l[i]?l[i].p(o,s):(l[i]=P(o),l[i].c(),l[i].m(e.parentNode,e))}for(;i<l.length;i+=1)l[i].d(1);l.length=t.length}},d(n){n&&p(e),x(l,n)}}}function J(a){let e,t,l=Math.round(a[5].confidence*100)+"",n,s;return{c(){e=w("div"),t=w("dd"),n=I(l),s=I("%"),u(e,"class","line svelte-1pq4gst"),u(t,"class","confidence svelte-1pq4gst")},m(i,o){q(i,e,o),q(i,t,o),g(t,n),g(t,s)},p(i,o){o&1&&l!==(l=Math.round(i[5].confidence*100)+"")&&K(n,l)},d(i){i&&(p(e),p(t))}}}function P(a){let e,t,l,n,s,i,o,r,h=a[5].label+"",_,M,m,H,f,b,L,d=a[0].confidences&&J(a);function S(){return a[4](a[7],a[5])}return{c(){e=w("button"),t=w("div"),l=w("meter"),i=j(),o=w("dl"),r=w("dt"),_=I(h),M=j(),d&&d.c(),H=j(),u(l,"aria-labelledby","meter-text"),u(l,"class","bar svelte-1pq4gst"),u(l,"min","0"),u(l,"max","100"),V(l,"width",a[5].confidence*100+"%"),V(l,"background","var(--stat-background-fill)"),l.value=n=a[5].confidence*100,u(l,"aria-label",s=Math.round(a[5].confidence*100)+"%"),u(r,"id",m=`meter-text-${a[5].label}`),u(r,"class","text svelte-1pq4gst"),u(o,"class","label svelte-1pq4gst"),u(t,"class","inner-wrap svelte-1pq4gst"),u(e,"class","confidence-set group svelte-1pq4gst"),u(e,"data-testid",f=`${a[5].label}-confidence-set`),A(e,"selectable",a[2])},m(B,c){q(B,e,c),g(e,t),g(t,l),g(t,i),g(t,o),g(o,r),g(r,_),g(r,M),d&&d.m(o,null),g(e,H),b||(L=$(e,"click",S),b=!0)},p(B,c){a=B,c&1&&V(l,"width",a[5].confidence*100+"%"),c&1&&n!==(n=a[5].confidence*100)&&(l.value=n),c&1&&s!==(s=Math.round(a[5].confidence*100)+"%")&&u(l,"aria-label",s),c&1&&h!==(h=a[5].label+"")&&K(_,h),c&1&&m!==(m=`meter-text-${a[5].label}`)&&u(r,"id",m),a[0].confidences?d?d.p(a,c):(d=J(a),d.c(),d.m(o,null)):d&&(d.d(1),d=null),c&1&&f!==(f=`${a[5].label}-confidence-set`)&&u(e,"data-testid",f),c&4&&A(e,"selectable",a[2])},d(B){B&&p(e),d&&d.d(),b=!1,L()}}}function re(a){let e,t,l=a[0].label+"",n,s,i=typeof a[0]=="object"&&a[0].confidences&&z(a);return{c(){e=w("div"),t=w("h2"),n=I(l),s=j(),i&&i.c(),u(t,"class","output-class svelte-1pq4gst"),u(t,"data-testid","label-output-value"),A(t,"no-confidence",!("confidences"in a[0])),V(t,"background-color",a[1]||"transparent"),u(e,"class","container svelte-1pq4gst")},m(o,r){q(o,e,r),g(e,t),g(t,n),g(e,s),i&&i.m(e,null)},p(o,[r]){r&1&&l!==(l=o[0].label+"")&&K(n,l),r&1&&A(t,"no-confidence",!("confidences"in o[0])),r&2&&V(t,"background-color",o[1]||"transparent"),typeof o[0]=="object"&&o[0].confidences?i?i.p(o,r):(i=z(o),i.c(),i.m(e,null)):i&&(i.d(1),i=null)},i:E,o:E,d(o){o&&p(e),i&&i.d()}}}function ue(a,e,t){let{value:l}=e;const n=y();let{color:s=void 0}=e,{selectable:i=!1}=e;const o=(r,h)=>{n("select",{index:r,value:h.label})};return a.$$set=r=>{"value"in r&&t(0,l=r.value),"color"in r&&t(1,s=r.color),"selectable"in r&&t(2,i=r.selectable)},[l,s,i,n,o]}class _e extends D{constructor(e){super(),F(this,e,ue,re,G,{value:0,color:1,selectable:2})}}function T(a){let e,t;return e=new ce({props:{Icon:X,label:a[5],disable:a[6]===!1}}),{c(){C(e.$$.fragment)},m(l,n){N(e,l,n),t=!0},p(l,n){const s={};n&32&&(s.label=l[5]),n&64&&(s.disable=l[6]===!1),e.$set(s)},i(l){t||(v(e.$$.fragment,l),t=!0)},o(l){k(e.$$.fragment,l),t=!1},d(l){Z(e,l)}}}function de(a){let e,t;return e=new fe({props:{unpadded_box:!0,$$slots:{default:[me]},$$scope:{ctx:a}}}),{c(){C(e.$$.fragment)},m(l,n){N(e,l,n),t=!0},p(l,n){const s={};n&131072&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(v(e.$$.fragment,l),t=!0)},o(l){k(e.$$.fragment,l),t=!1},d(l){Z(e,l)}}}function be(a){let e,t;return e=new _e({props:{selectable:a[11],value:a[4],color:a[3]}}),e.$on("select",a[15]),{c(){C(e.$$.fragment)},m(l,n){N(e,l,n),t=!0},p(l,n){const s={};n&2048&&(s.selectable=l[11]),n&16&&(s.value=l[4]),n&8&&(s.color=l[3]),e.$set(s)},i(l){t||(v(e.$$.fragment,l),t=!0)},o(l){k(e.$$.fragment,l),t=!1},d(l){Z(e,l)}}}function me(a){let e,t;return e=new X({}),{c(){C(e.$$.fragment)},m(l,n){N(e,l,n),t=!0},i(l){t||(v(e.$$.fragment,l),t=!0)},o(l){k(e.$$.fragment,l),t=!1},d(l){Z(e,l)}}}function ge(a){let e,t,l,n,s,i,o;const r=[a[9]];let h={};for(let f=0;f<r.length;f+=1)h=te(h,r[f]);e=new ne({props:h});let _=a[10]&&T(a);const M=[be,de],m=[];function H(f,b){return f[13]!==void 0&&f[13]!==null?0:1}return n=H(a),s=m[n]=M[n](a),{c(){C(e.$$.fragment),t=j(),_&&_.c(),l=j(),s.c(),i=U()},m(f,b){N(e,f,b),q(f,t,b),_&&_.m(f,b),q(f,l,b),m[n].m(f,b),q(f,i,b),o=!0},p(f,b){const L=b&512?ae(r,[se(f[9])]):{};e.$set(L),f[10]?_?(_.p(f,b),b&1024&&v(_,1)):(_=T(f),_.c(),v(_,1),_.m(l.parentNode,l)):_&&(R(),k(_,1,1,()=>{_=null}),W());let d=n;n=H(f),n===d?m[n].p(f,b):(R(),k(m[d],1,1,()=>{m[d]=null}),W(),s=m[n],s?s.p(f,b):(s=m[n]=M[n](f),s.c()),v(s,1),s.m(i.parentNode,i))},i(f){o||(v(e.$$.fragment,f),v(_),v(s),o=!0)},o(f){k(e.$$.fragment,f),k(_),k(s),o=!1},d(f){f&&(p(t),p(l),p(i)),Z(e,f),_&&_.d(f),m[n].d(f)}}}function he(a){let e,t;return e=new ie({props:{test_id:"label",visible:a[2],elem_id:a[0],elem_classes:a[1],container:a[6],scale:a[7],min_width:a[8],padding:!1,$$slots:{default:[ge]},$$scope:{ctx:a}}}),{c(){C(e.$$.fragment)},m(l,n){N(e,l,n),t=!0},p(l,[n]){const s={};n&4&&(s.visible=l[2]),n&1&&(s.elem_id=l[0]),n&2&&(s.elem_classes=l[1]),n&64&&(s.container=l[6]),n&128&&(s.scale=l[7]),n&256&&(s.min_width=l[8]),n&147064&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(v(e.$$.fragment,l),t=!0)},o(l){k(e.$$.fragment,l),t=!1},d(l){Z(e,l)}}}function ve(a,e,t){let l,n,s;ee(a,le,c=>t(16,s=c));let{elem_id:i=""}=e,{elem_classes:o=[]}=e,{visible:r=!0}=e,{color:h=void 0}=e,{value:_={}}=e,{label:M=s("label.label")}=e,{container:m=!0}=e,{scale:H=null}=e,{min_width:f=void 0}=e,{loading_status:b}=e,{show_label:L=!0}=e,{selectable:d=!1}=e,{gradio:S}=e;const B=({detail:c})=>S.dispatch("select",c);return a.$$set=c=>{"elem_id"in c&&t(0,i=c.elem_id),"elem_classes"in c&&t(1,o=c.elem_classes),"visible"in c&&t(2,r=c.visible),"color"in c&&t(3,h=c.color),"value"in c&&t(4,_=c.value),"label"in c&&t(5,M=c.label),"container"in c&&t(6,m=c.container),"scale"in c&&t(7,H=c.scale),"min_width"in c&&t(8,f=c.min_width),"loading_status"in c&&t(9,b=c.loading_status),"show_label"in c&&t(10,L=c.show_label),"selectable"in c&&t(11,d=c.selectable),"gradio"in c&&t(12,S=c.gradio)},a.$$.update=()=>{a.$$.dirty&16&&t(14,{confidences:l,label:n}=_,l,(t(13,n),t(4,_))),a.$$.dirty&28672&&S.dispatch("change")},[i,o,r,h,_,M,m,H,f,b,L,d,S,n,l,B]}class ke extends D{constructor(e){super(),F(this,e,ve,he,G,{elem_id:0,elem_classes:1,visible:2,color:3,value:4,label:5,container:6,scale:7,min_width:8,loading_status:9,show_label:10,selectable:11,gradio:12})}}const He=ke;export{He as default};
//# sourceMappingURL=index-8c55a075.js.map
