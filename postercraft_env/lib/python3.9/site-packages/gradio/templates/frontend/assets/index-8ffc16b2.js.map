{"version": 3, "file": "index-8ffc16b2.js", "sources": ["../../../../node_modules/.pnpm/d3-dsv@3.0.1/node_modules/d3-dsv/src/tsv.js", "../../../../js/number/example/Number.svelte", "../../../../js/dropdown/example/Dropdown.svelte", "../../../../js/checkbox/example/Checkbox.svelte", "../../../../js/checkboxgroup/example/Checkboxgroup.svelte", "../../../../js/slider/example/Slider.svelte", "../../../../js/radio/example/Radio.svelte", "../../../../js/image/shared/Image.svelte", "../../../../js/image/example/Image.svelte", "../../../../js/textbox/example/Textbox.svelte", "../../../../js/audio/example/Audio.svelte", "../../../../js/video/example/Video.svelte", "../../../../js/file/example/File.svelte", "../../../../js/dataframe/example/Dataframe.svelte", "../../../../js/model3D/example/Model3d.svelte", "../../../../js/colorpicker/example/Colorpicker.svelte", "../../../../js/timeseries/example/Timeseries.svelte", "../../../../js/markdown/example/Markdown.svelte", "../../../../js/html/example/Html.svelte", "../../../../js/code/example/Code.svelte", "../../../../js/fileexplorer/example/File.svelte", "../../../../js/app/src/components/Dataset/directory.ts", "../../../../js/app/src/components/Dataset/Dataset.svelte"], "sourcesContent": ["import dsv from \"./dsv.js\";\n\nvar tsv = dsv(\"\\t\");\n\nexport var tsvParse = tsv.parse;\nexport var tsvParseRows = tsv.parseRows;\nexport var tsvFormat = tsv.format;\nexport var tsvFormatBody = tsv.formatBody;\nexport var tsvFormatRows = tsv.formatRows;\nexport var tsvFormatRow = tsv.formatRow;\nexport var tsvFormatValue = tsv.formatValue;\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: boolean;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value.toLocaleString()}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string[];\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{#each value as check, i}{check.toLocaleString()}{#if i !== value.length - 1},&nbsp;{/if}{/each}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { HTMLImgAttributes } from \"svelte/elements\";\n\ttype $$Props = HTMLImgAttributes;\n\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let src: HTMLImgAttributes[\"src\"] = undefined;\n</script>\n\n{#await resolve_wasm_src(src) then resolved_src}\n\t<!-- svelte-ignore a11y-missing-attribute -->\n\t<img src={resolved_src} {...$$restProps} />\n{:catch error}\n\t<p style=\"color: red;\">{error.message}</p>\n{/await}\n\n<style>\n\timg {\n\t\tmax-width: 100%;\n\t\tmax-height: 100%;\n\t\tborder-radius: var(--radius-lg);\n\t\tmax-width: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport Image from \"../shared/Image.svelte\";\n\n\texport let value: string;\n\texport let samples_dir: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass=\"container\"\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t<Image src={samples_dir + value} alt=\"\" />\n</div>\n\n<style>\n\t.container.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\t.container.table {\n\t\tmargin: 0 auto;\n\t\tborder: 2px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-lg);\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n\n\t.container.gallery {\n\t\tborder: 2px solid var(--border-color-primary);\n\t\theight: var(--size-20);\n\t\tmax-height: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\n\tlet size: number;\n\tlet el: HTMLDivElement;\n\n\tfunction set_styles(element: HTMLElement, el_width: number): void {\n\t\tif (!element || !el_width) return;\n\t\tel.style.setProperty(\n\t\t\t\"--local-text-width\",\n\t\t\t`${el_width < 150 ? el_width : 200}px`\n\t\t);\n\t\tel.style.whiteSpace = \"unset\";\n\t}\n\n\tonMount(() => {\n\t\tset_styles(el, size);\n\t});\n</script>\n\n<div\n\tbind:clientWidth={size}\n\tbind:this={el}\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n\n\tdiv {\n\t\toverflow: hidden;\n\t\tmin-width: var(--local-text-width);\n\n\t\twhite-space: nowrap;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { playable, Video } from \"../shared\";\n\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let value: string;\n\texport let samples_dir: string;\n\tlet video: HTMLVideoElement;\n\n\tasync function init(): Promise<void> {\n\t\tvideo.muted = true;\n\t\tvideo.playsInline = true;\n\t\tvideo.controls = false;\n\t\tvideo.setAttribute(\"muted\", \"\");\n\n\t\tawait video.play();\n\t\tvideo.pause();\n\t}\n</script>\n\n{#if playable()}\n\t<div\n\t\tclass=\"container\"\n\t\tclass:table={type === \"table\"}\n\t\tclass:gallery={type === \"gallery\"}\n\t\tclass:selected\n\t>\n\t\t<Video\n\t\t\tmuted\n\t\t\tplaysinline\n\t\t\tbind:node={video}\n\t\t\ton:loadeddata={init}\n\t\t\ton:mouseover={video.play.bind(video)}\n\t\t\ton:mouseout={video.pause.bind(video)}\n\t\t\tsrc={samples_dir + value}\n\t\t/>\n\t</div>\n{:else}\n\t<div>{value}</div>\n{/if}\n\n<style>\n\t.container {\n\t\tflex: none;\n\t\tborder: 2px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-lg);\n\t\tmax-width: none;\n\t}\n\n\t.container:hover,\n\t.container.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\t.container.table {\n\t\tmargin: 0 auto;\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n\n\t.container.gallery {\n\t\theight: var(--size-20);\n\t\tmax-height: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\n\texport let value: FileData;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{Array.isArray(value) ? value.join(\", \") : value}\n</div>\n\n<style>\n\tdiv {\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\t.gallery {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\ttext-align: left;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { csvParseRows, tsvParseRows } from \"d3-dsv\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let value: (string | number)[][] | string;\n\texport let samples_dir: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let index: number;\n\n\tlet hovered = false;\n\tlet loaded_value: (string | number)[][] | string = value;\n\tlet loaded = Array.isArray(loaded_value);\n\n\t$: if (!loaded && typeof value === \"string\" && /\\.[a-zA-Z]+$/.test(value)) {\n\t\tfetch(samples_dir + value)\n\t\t\t.then((v) => v.text())\n\t\t\t.then((v) => {\n\t\t\t\ttry {\n\t\t\t\t\tif ((value as string).endsWith(\"csv\")) {\n\t\t\t\t\t\tconst small_df = v\n\t\t\t\t\t\t\t.split(\"\\n\")\n\t\t\t\t\t\t\t.slice(0, 4)\n\t\t\t\t\t\t\t.map((v) => v.split(\",\").slice(0, 4).join(\",\"))\n\t\t\t\t\t\t\t.join(\"\\n\");\n\n\t\t\t\t\t\tloaded_value = csvParseRows(small_df);\n\t\t\t\t\t} else if ((value as string).endsWith(\"tsv\")) {\n\t\t\t\t\t\tconst small_df = v\n\t\t\t\t\t\t\t.split(\"\\n\")\n\t\t\t\t\t\t\t.slice(0, 4)\n\t\t\t\t\t\t\t.map((v) => v.split(\"\\t\").slice(0, 4).join(\"\\t\"))\n\t\t\t\t\t\t\t.join(\"\\n\");\n\n\t\t\t\t\t\tloaded_value = tsvParseRows(small_df);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error($_(\"dataframe.incorrect_format\"));\n\t\t\t\t\t}\n\n\t\t\t\t\tloaded = true;\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(e);\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch((e) => {\n\t\t\t\tloaded_value = value;\n\t\t\t\tloaded = true;\n\t\t\t});\n\t}\n</script>\n\n{#if loaded}\n\t<!-- TODO: fix-->\n\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t<div\n\t\tclass:table={type === \"table\"}\n\t\tclass:gallery={type === \"gallery\"}\n\t\tclass:selected\n\t\ton:mouseenter={() => (hovered = true)}\n\t\ton:mouseleave={() => (hovered = false)}\n\t>\n\t\t{#if typeof loaded_value === \"string\"}\n\t\t\t{loaded_value}\n\t\t{:else}\n\t\t\t<table class=\"\">\n\t\t\t\t{#each loaded_value.slice(0, 3) as row, i}\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t{#each row.slice(0, 3) as cell, j}\n\t\t\t\t\t\t\t<td>{cell}</td>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t{#if row.length > 3}\n\t\t\t\t\t\t\t<td>…</td>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</tr>\n\t\t\t\t{/each}\n\t\t\t\t{#if value.length > 3}\n\t\t\t\t\t<div\n\t\t\t\t\t\tclass=\"overlay\"\n\t\t\t\t\t\tclass:odd={index % 2 != 0}\n\t\t\t\t\t\tclass:even={index % 2 == 0}\n\t\t\t\t\t\tclass:button={type === \"gallery\"}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t</table>\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\ttable {\n\t\tposition: relative;\n\t}\n\n\ttd {\n\t\tborder: 1px solid var(--table-border-color);\n\t\tpadding: var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.selected td {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\t.table {\n\t\tdisplay: inline-block;\n\t\tmargin: 0 auto;\n\t}\n\n\t.gallery td:first-child {\n\t\tborder-left: none;\n\t}\n\n\t.gallery tr:first-child td {\n\t\tborder-top: none;\n\t}\n\n\t.gallery td:last-child {\n\t\tborder-right: none;\n\t}\n\n\t.gallery tr:last-child td {\n\t\tborder-bottom: none;\n\t}\n\n\t.overlay {\n\t\t--gradient-to: transparent;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tbackground: linear-gradient(to bottom, transparent, var(--gradient-to));\n\t\twidth: var(--size-full);\n\t\theight: 50%;\n\t}\n\n\t/* i dont know what i've done here but it is what it is */\n\t.odd {\n\t\t--gradient-to: var(--table-even-background-fill);\n\t}\n\n\t.even {\n\t\t--gradient-to: var(--table-odd-background-fill);\n\t}\n\n\t.button {\n\t\t--gradient-to: var(--background-fill-primary);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tstyle=\"background-color: {value}\"\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n/>\n\n<style>\n\tdiv {\n\t\twidth: var(--size-10);\n\t\theight: var(--size-10);\n\t}\n\t.table {\n\t\tmargin: 0 auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass=\"prose\"\n>\n\t{@html value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass=\"prose\"\n>\n\t{@html value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<pre\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected>{value}</pre>\n\n<style>\n\tpre {\n\t\ttext-align: left;\n\t}\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\n\texport let value: string[] | string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<ul\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{#each Array.isArray(value) ? value.slice(0, 3) : [value] as path}\n\t\t<li><code>./{path}</code></li>\n\t{/each}\n\t{#if Array.isArray(value) && value.length > 3}\n\t\t<li class=\"extra\">...</li>\n\t{/if}\n</ul>\n\n<style>\n\tul {\n\t\twhite-space: nowrap;\n\t\tmax-height: 100px;\n\t\tlist-style: none;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t}\n\n\t.extra {\n\t\ttext-align: center;\n\t}\n\n\t.gallery {\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\ttext-align: left;\n\t}\n</style>\n", "import ExampleNumber from \"@gradio/number/example\";\nimport ExampleDropdown from \"@gradio/dropdown/example\";\nimport ExampleCheckbox from \"@gradio/checkbox/example\";\nimport ExampleCheckboxGroup from \"@gradio/checkboxgroup/example\";\nimport ExampleSlider from \"@gradio/slider/example\";\nimport ExampleRadio from \"@gradio/radio/example\";\nimport ExampleImage from \"@gradio/image/example\";\nimport ExampleTextbox from \"@gradio/textbox/example\";\nimport ExampleAudio from \"@gradio/audio/example\";\nimport ExampleVideo from \"@gradio/video/example\";\nimport ExampleFile from \"@gradio/file/example\";\nimport ExampleDataframe from \"@gradio/dataframe/example\";\nimport ExampleModel3D from \"@gradio/model3d/example\";\nimport ExampleColorPicker from \"@gradio/colorpicker/example\";\nimport ExampleTimeSeries from \"@gradio/timeseries/example\";\nimport ExampleMarkdown from \"@gradio/markdown/example\";\nimport ExampleHTML from \"@gradio/html/example\";\nimport ExampleCode from \"@gradio/code/example\";\nimport ExampleFileExplorer from \"@gradio/fileexplorer/example\";\n\nexport const component_map = {\n\tdropdown: ExampleDropdown,\n\tcheckbox: ExampleCheckbox,\n\tcheckboxgroup: ExampleCheckboxGroup,\n\tnumber: ExampleNumber,\n\tslider: ExampleSlider,\n\tradio: ExampleRadio,\n\timage: ExampleImage,\n\ttextbox: ExampleTextbox,\n\taudio: ExampleAudio,\n\tvideo: ExampleVideo,\n\tfile: ExampleFile,\n\tdataframe: ExampleDataframe,\n\tmodel3d: ExampleModel3D,\n\tcolorpicker: ExampleColorPicker,\n\ttimeseries: ExampleTimeSeries,\n\tmarkdown: ExampleMarkdown,\n\thtml: ExampleHTML,\n\tcode: ExampleCode,\n\tfileexplorer: ExampleFileExplorer\n};\n", "<script lang=\"ts\">\n\timport { Block } from \"@gradio/atoms\";\n\timport type { SvelteComponent, ComponentType } from \"svelte\";\n\timport { component_map } from \"./directory\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { get_fetchable_url_or_file } from \"@gradio/upload\";\n\texport let components: (keyof typeof component_map)[];\n\texport let label = \"Examples\";\n\texport let headers: string[];\n\texport let samples: any[][];\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: number | null = null;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let samples_per_page = 10;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tclick: number;\n\t\tselect: SelectData;\n\t}>;\n\n\tlet samples_dir: string = get_fetchable_url_or_file(null, root, root_url);\n\tlet page = 0;\n\t$: gallery = components.length < 2;\n\tlet paginate = samples.length > samples_per_page;\n\n\tlet selected_samples: any[][];\n\tlet page_count: number;\n\tlet visible_pages: number[] = [];\n\n\tlet current_hover = -1;\n\n\tfunction handle_mouseenter(i: number): void {\n\t\tcurrent_hover = i;\n\t}\n\tfunction handle_mouseleave(): void {\n\t\tcurrent_hover = -1;\n\t}\n\n\t$: {\n\t\tif (paginate) {\n\t\t\tvisible_pages = [];\n\t\t\tselected_samples = samples.slice(\n\t\t\t\tpage * samples_per_page,\n\t\t\t\t(page + 1) * samples_per_page\n\t\t\t);\n\t\t\tpage_count = Math.ceil(samples.length / samples_per_page);\n\t\t\t[0, page, page_count - 1].forEach((anchor) => {\n\t\t\t\tfor (let i = anchor - 2; i <= anchor + 2; i++) {\n\t\t\t\t\tif (i >= 0 && i < page_count && !visible_pages.includes(i)) {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tvisible_pages.length > 0 &&\n\t\t\t\t\t\t\ti - visible_pages[visible_pages.length - 1] > 1\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tvisible_pages.push(-1);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvisible_pages.push(i);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tselected_samples = samples.slice();\n\t\t}\n\t}\n\n\t$: component_meta = selected_samples.map((sample_row) =>\n\t\tsample_row.map((sample_cell, j) => ({\n\t\t\tvalue: sample_cell,\n\t\t\tcomponent: component_map[components[j]] as ComponentType<SvelteComponent>\n\t\t}))\n\t);\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\tcontainer={false}\n>\n\t<div class=\"label\">\n\t\t<svg\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\t\t\taria-hidden=\"true\"\n\t\t\trole=\"img\"\n\t\t\twidth=\"1em\"\n\t\t\theight=\"1em\"\n\t\t\tpreserveAspectRatio=\"xMidYMid meet\"\n\t\t\tviewBox=\"0 0 32 32\"\n\t\t>\n\t\t\t<path\n\t\t\t\tfill=\"currentColor\"\n\t\t\t\td=\"M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z\"\n\t\t\t/>\n\t\t</svg>\n\t\t{label}\n\t</div>\n\t{#if gallery}\n\t\t<div class=\"gallery\">\n\t\t\t{#each selected_samples as sample_row, i}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"gallery-item\"\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tvalue = i + page * samples_per_page;\n\t\t\t\t\t\tgradio.dispatch(\"click\", value);\n\t\t\t\t\t\tgradio.dispatch(\"select\", { index: value, value: sample_row });\n\t\t\t\t\t}}\n\t\t\t\t\ton:mouseenter={() => handle_mouseenter(i)}\n\t\t\t\t\ton:mouseleave={() => handle_mouseleave()}\n\t\t\t\t>\n\t\t\t\t\t{#if Object.keys(component_map).includes(components[0]) && component_map[components[0]]}\n\t\t\t\t\t\t<svelte:component\n\t\t\t\t\t\t\tthis={component_meta[0][0].component}\n\t\t\t\t\t\t\tvalue={sample_row[0]}\n\t\t\t\t\t\t\t{samples_dir}\n\t\t\t\t\t\t\ttype=\"gallery\"\n\t\t\t\t\t\t\tselected={current_hover === i}\n\t\t\t\t\t\t\tindex={i}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t</button>\n\t\t\t{/each}\n\t\t</div>\n\t{:else}\n\t\t<div class=\"table-wrap\">\n\t\t\t<table tabindex=\"0\" role=\"grid\">\n\t\t\t\t<thead>\n\t\t\t\t\t<tr class=\"tr-head\">\n\t\t\t\t\t\t{#each headers as header}\n\t\t\t\t\t\t\t<th>\n\t\t\t\t\t\t\t\t{header}\n\t\t\t\t\t\t\t</th>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t</thead>\n\t\t\t\t<tbody>\n\t\t\t\t\t{#each component_meta as sample_row, i}\n\t\t\t\t\t\t<tr\n\t\t\t\t\t\t\tclass=\"tr-body\"\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tvalue = i + page * samples_per_page;\n\t\t\t\t\t\t\t\tgradio.dispatch(\"click\", value);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\ton:mouseenter={() => handle_mouseenter(i)}\n\t\t\t\t\t\t\ton:mouseleave={() => handle_mouseleave()}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#each sample_row as { value, component }, j}\n\t\t\t\t\t\t\t\t{@const component_name = components[j]}\n\t\t\t\t\t\t\t\t{#if component_name !== undefined && component_map[component_name] !== undefined}\n\t\t\t\t\t\t\t\t\t<td\n\t\t\t\t\t\t\t\t\t\tstyle=\"max-width: {component_name === 'textbox'\n\t\t\t\t\t\t\t\t\t\t\t? '35ch'\n\t\t\t\t\t\t\t\t\t\t\t: 'auto'}\"\n\t\t\t\t\t\t\t\t\t\tclass={component_name}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<svelte:component\n\t\t\t\t\t\t\t\t\t\t\tthis={component}\n\t\t\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t\t\t{samples_dir}\n\t\t\t\t\t\t\t\t\t\t\ttype=\"table\"\n\t\t\t\t\t\t\t\t\t\t\tselected={current_hover === i}\n\t\t\t\t\t\t\t\t\t\t\tindex={i}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t{/each}\n\t\t\t\t</tbody>\n\t\t\t</table>\n\t\t</div>\n\t{/if}\n\t{#if paginate}\n\t\t<div class=\"paginate\">\n\t\t\tPages:\n\t\t\t{#each visible_pages as visible_page}\n\t\t\t\t{#if visible_page === -1}\n\t\t\t\t\t<div>...</div>\n\t\t\t\t{:else}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass:current-page={page === visible_page}\n\t\t\t\t\t\ton:click={() => (page = visible_page)}\n\t\t\t\t\t>\n\t\t\t\t\t\t{visible_page + 1}\n\t\t\t\t\t</button>\n\t\t\t\t{/if}\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</Block>\n\n<style>\n\t.wrap {\n\t\tdisplay: inline-block;\n\t\twidth: var(--size-full);\n\t\tmax-width: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--block-label-text-weight);\n\t\tfont-size: var(--block-label-text-size);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tsvg {\n\t\tmargin-right: var(--size-1);\n\t}\n\n\t.gallery {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--spacing-lg);\n\t}\n\n\t.gallery-item {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--button-large-radius);\n\t\toverflow: hidden;\n\t}\n\n\t.gallery-item:hover {\n\t\tborder-color: var(--border-color-accent);\n\t\tbackground: var(--table-row-focus);\n\t}\n\n\t.table-wrap {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\toverflow-x: auto;\n\t\tline-height: var(--line-sm);\n\t}\n\ttable {\n\t\twidth: var(--size-full);\n\t}\n\n\t.tr-head {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t}\n\n\t.tr-head > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth {\n\t\tpadding: var(--size-2);\n\t\twhite-space: nowrap;\n\t}\n\n\t.tr-body {\n\t\tcursor: pointer;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\t.tr-body:last-child {\n\t\tborder: none;\n\t}\n\n\t.tr-body:nth-child(odd) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.tr-body:hover {\n\t\tbackground: var(--table-row-focus);\n\t}\n\n\t.tr-body > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\t.tr-body:hover > * + * {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\ttd {\n\t\tpadding: var(--size-2);\n\t\ttext-align: center;\n\t}\n\n\t.paginate {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-sm);\n\t\tmargin-top: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--text-sm);\n\t}\n\n\tbutton.current-page {\n\t\tfont-weight: var(--weight-bold);\n\t}\n</style>\n"], "names": ["tsv", "dsv", "tsvParseRows", "ctx", "toggle_class", "div", "insert", "target", "anchor", "value", "$$props", "type", "selected", "t_value", "dirty", "set_data", "if_block", "create_if_block", "t", "i", "p", "img", "img_src_value", "handle_promise", "promise", "resolve_wasm_src", "info", "src", "image_changes", "samples_dir", "size", "el", "set_styles", "element", "el_width", "$$invalidate", "onMount", "$$value", "is_function", "video_1_changes", "video", "init", "create_if_block_1", "each_value", "ensure_array_like", "create_if_block_2", "table", "td", "each_value_1", "create_if_block_3", "tr", "index", "hovered", "loaded_value", "loaded", "mouseenter_handler", "mouseleave_handler", "v", "small_df", "csvParseRows", "$_", "e", "pre", "li", "append", "code", "t1", "t1_value", "ul", "component_map", "ExampleDropdown", "ExampleCheckbox", "ExampleCheckboxGroup", "ExampleNumber", "ExampleSlider", "ExampleRadio", "ExampleImage", "ExampleTextbox", "ExampleAudio", "ExampleVideo", "ExampleFile", "ExampleDataframe", "ExampleModel3D", "ExampleColorPicker", "ExampleTimeSeries", "ExampleMarkdown", "ExampleHTML", "ExampleCode", "ExampleFileExplorer", "constants_0", "child_ctx", "thead", "tbody", "each_blocks", "th", "t0", "t0_value", "set_style", "switch_instance_changes", "create_if_block_4", "switch_value", "show_if", "button", "svg", "path", "if_block1", "components", "label", "headers", "samples", "elem_id", "elem_classes", "visible", "root", "root_url", "samples_per_page", "scale", "min_width", "gradio", "get_fetchable_url_or_file", "page", "paginate", "selected_samples", "page_count", "visible_pages", "current_hover", "handle_mouseenter", "handle_mouseleave", "sample_row", "mouseenter_handler_1", "click_handler_2", "visible_page", "gallery", "component_meta", "sample_cell", "j"], "mappings": "knBAEA,IAAIA,GAAMC,GAAI,GAAI,EAGPC,GAAeF,GAAI,2DCM5BG,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCQ1BP,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,oOCQ1BG,EAAAV,KAAM,eAAc,EAAA,gEAJRC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,mBADHM,EAAA,GAAAD,KAAAA,EAAAV,KAAM,eAAc,EAAA,KAAAY,EAAA,EAAAF,CAAA,OAJRT,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAc,EAAAC,GACd,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,kTCQkD,IAAO,kDAA1D,IAAAG,EAAAV,KAAM,eAAc,EAAA,OAAQa,EAAAb,EAAM,CAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,GAACc,GAAA,iFAAlDH,EAAA,GAAAD,KAAAA,EAAAV,KAAM,eAAc,EAAA,KAAAY,EAAAG,EAAAL,CAAA,EAAQV,EAAM,CAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,qHAApEA,EAAK,CAAA,CAAA,uBAAV,OAAIgB,GAAA,8GAJOf,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,yEADGL,EAAK,CAAA,CAAA,oBAAV,OAAI,GAAA,EAAA,mHAAJ,YAJWC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,kFAPtB,MAAAM,CAAe,EAAAC,GACf,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCQ1BP,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCQ1BP,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,oOCUHG,EAAAV,KAAM,QAAO,2DAArCG,EAAyCC,EAAAa,EAAAZ,CAAA,iBAAjBM,EAAA,GAAAD,KAAAA,EAAAV,KAAM,QAAO,KAAAY,EAAA,EAAAF,CAAA,mDAF3BV,EAAY,CAAA,GAAMA,EAAW,CAAA,+GAAvCG,EAA0CC,EAAAc,EAAAb,CAAA,wCAAhCL,EAAY,CAAA,CAAA,GAAA,CAAA,IAAAmB,CAAA,OAAMnB,EAAW,CAAA,qMAFhC,OAAAoB,GAAAC,EAAAC,GAAiBtB,EAAG,CAAA,CAAA,EAAAuB,CAAA,4HAApBZ,EAAA,GAAAU,KAAAA,EAAAC,GAAiBtB,EAAG,CAAA,CAAA,IAAAoB,GAAAC,EAAAE,CAAA,uHAHhB,CAAA,IAAAC,EAAgC,MAAS,EAAAjB,8MCSxC,IAAAP,KAAcA,EAAK,CAAA,oFAJlBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAHlCG,EAOKC,EAAAF,EAAAG,CAAA,wCADQM,EAAA,IAAAc,EAAA,IAAAzB,KAAcA,EAAK,CAAA,wBAJlBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,aACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+IATtB,MAAAM,CAAa,EAAAC,GACb,YAAAmB,CAAmB,EAAAnB,GACnB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,0TCyB1BP,EAAK,CAAA,CAAA,qDAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAJlCG,EAQKC,EAAAF,EAAAG,CAAA,yDADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,0FAzBtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,EAEvBoB,EACAC,WAEKC,EAAWC,EAAsBC,EAAgB,CACpD,CAAAD,IAAYC,IACjBH,EAAG,MAAM,YACR,qBACG,GAAAG,EAAW,IAAMA,EAAW,OAAG,EAEnCC,EAAA,EAAAJ,EAAG,MAAM,WAAa,QAAOA,CAAA,GAG9BK,GAAO,IAAA,CACNJ,EAAWD,EAAID,CAAI,iBAKFA,EAAI,KAAA,6DACXC,EAAEM,iRCfZlC,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCmCrBP,EAAK,CAAA,CAAA,UAAXG,EAAiBC,EAAAF,EAAAG,CAAA,0BAAXL,EAAK,CAAA,CAAA,0GAJJ,IAAAA,KAAcA,EAAK,CAAA,UAJbA,EAAK,CAAA,IAAA,gBAALA,EAAK,CAAA,sEACDA,EAAI,CAAA,CAAA,+BACLmC,GAAAnC,EAAM,CAAA,EAAA,KAAK,KAAKA,EAAhB,CAAA,CAAA,CAAA,GAAAA,EAAM,CAAA,EAAA,KAAK,KAAKA,EAAK,CAAA,CAAA,EAAA,MAAA,KAAA,SAAA,gCACtBmC,GAAAnC,EAAM,CAAA,EAAA,MAAM,KAAKA,EAAjB,CAAA,CAAA,CAAA,GAAAA,EAAM,CAAA,EAAA,MAAM,KAAKA,EAAK,CAAA,CAAA,EAAA,MAAA,KAAA,SAAA,6EAVvBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAHlCG,EAeKC,EAAAF,EAAAG,CAAA,0CAFEM,EAAA,KAAAyB,EAAA,IAAApC,KAAcA,EAAK,CAAA,0BAJbA,EAAK,CAAA,qCAPJC,EAAAC,EAAA,QAAAF,OAAS,OAAO,aACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,wLAJtB,MAAA,2LAjBD,KAAAQ,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,GAChB,MAAAD,CAAa,EAAAC,GACb,YAAAmB,CAAmB,EAAAnB,EAC1B8B,iBAEWC,GAAI,KAClBD,EAAM,MAAQ,GAAIA,CAAA,MAClBA,EAAM,YAAc,GAAIA,CAAA,MACxBA,EAAM,SAAW,GAAKA,CAAA,EACtBA,EAAM,aAAa,QAAS,EAAE,EAExB,MAAAA,EAAM,OACZA,EAAM,MAAK,gBAcCA,EAAK/B,4SCjBjB,MAAM,QAAQN,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,EAAC,KAAK,IAAI,EAAIA,EAAK,CAAA,GAAA,+DAJnCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,gCADH,MAAM,QAAQL,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,EAAC,KAAK,IAAI,EAAIA,EAAK,CAAA,GAAA,KAAAY,EAAA,EAAAF,CAAA,OAJnCT,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAe,EAAAC,GACf,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,+XCwDd,OAAA,OAAAP,MAAiB,SAAQuC,kFANxBtC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EA+BKC,EAAAF,EAAAG,CAAA,wJA9BSJ,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,oFASxBwC,EAAAC,EAAAzC,EAAa,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,uBAA5B,OAAI,GAAA,2BAUDA,EAAK,CAAA,EAAC,OAAS,GAAC0C,GAAA1C,CAAA,qHAXtBG,EAmBOC,EAAAuC,EAAAtC,CAAA,0FAlBCmC,EAAAC,EAAAzC,EAAa,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,oBAA5B,OAAIgB,GAAA,EAAA,gHAAJ,OAUGhB,EAAK,CAAA,EAAC,OAAS,gIAbpBA,EAAY,CAAA,CAAA,qCAAZA,EAAY,CAAA,CAAA,yCAMJA,EAAI,EAAA,EAAA,uEAATG,EAAcC,EAAAwC,EAAAvC,CAAA,8BAATL,EAAI,EAAA,EAAA,KAAAY,EAAA,EAAAF,CAAA,oHAGTP,EAASC,EAAAwC,EAAAvC,CAAA,yCAJHwC,EAAAJ,EAAAzC,EAAI,EAAA,EAAA,MAAM,EAAG,CAAC,CAAA,uBAAnB,OAAI,GAAA,2BAGDA,EAAG,EAAA,EAAC,OAAS,GAAC8C,GAAA,mFAJpB3C,EAOIC,EAAA2C,EAAA1C,CAAA,0FANIwC,EAAAJ,EAAAzC,EAAI,EAAA,EAAA,MAAM,EAAG,CAAC,CAAA,oBAAnB,OAAIgB,GAAA,EAAA,gHAAJ,OAGGhB,EAAG,EAAA,EAAC,OAAS,8KAQPA,EAAK,CAAA,EAAG,GAAK,CAAC,aACbA,EAAK,CAAA,EAAG,GAAK,CAAC,EACZC,EAAAC,EAAA,SAAAF,OAAS,SAAS,UAJjCG,EAKCC,EAAAF,EAAAG,CAAA,yBAHWL,EAAK,CAAA,EAAG,GAAK,CAAC,kBACbA,EAAK,CAAA,EAAG,GAAK,CAAC,OACZC,EAAAC,EAAA,SAAAF,OAAS,SAAS,yCA7BjCA,EAAM,CAAA,GAAAc,GAAAd,CAAA,mEAANA,EAAM,CAAA,wJA/CC,MAAAM,CAAqC,EAAAC,GACrC,YAAAmB,CAAmB,EAAAnB,GACnB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,GAChB,MAAAyC,CAAa,EAAAzC,EAEpB0C,EAAU,GACVC,EAA+C5C,EAC/C6C,EAAS,MAAM,QAAQD,CAAY,EA8ChB,MAAAE,EAAA,IAAApB,EAAA,EAAAiB,EAAU,EAAI,EACdI,EAAA,IAAArB,EAAA,EAAAiB,EAAU,EAAK,mNA7CrC,CAAOE,GAAM,OAAW7C,GAAU,UAAY,eAAe,KAAKA,CAAK,GACvE,MAAMoB,EAAcpB,CAAK,EACvB,KAAMgD,GAAMA,EAAE,KACd,CAAA,EAAA,KAAMA,GAAC,QAEDhD,EAAiB,SAAS,KAAK,EAAA,CAC7B,MAAAiD,EAAWD,EACf,MAAM;AAAA,CAAI,EACV,MAAM,EAAG,CAAC,EACV,IAAKA,GAAMA,EAAE,MAAM,GAAG,EAAE,MAAM,EAAG,CAAC,EAAE,KAAK,GAAG,CAC5C,EAAA,KAAK;AAAA,CAAI,MAEXJ,EAAeM,GAAaD,CAAQ,CAAA,UACzBjD,EAAiB,SAAS,KAAK,EAAA,CACpC,MAAAiD,EAAWD,EACf,MAAM;AAAA,CAAI,EACV,MAAM,EAAG,CAAC,EACV,IAAKA,GAAMA,EAAE,MAAM,GAAI,EAAE,MAAM,EAAG,CAAC,EAAE,KAAK,GAAI,CAC9C,EAAA,KAAK;AAAA,CAAI,MAEXJ,EAAenD,GAAawD,CAAQ,CAAA,iBAE1B,MAAME,EAAG,4BAA4B,CAAA,EAGhDzB,EAAA,EAAAmB,EAAS,EAAI,QACLO,GACR,QAAQ,MAAMA,CAAC,EAGhB,CAAA,EAAA,MAAOA,GAAC,CACR1B,EAAA,EAAAkB,EAAe5C,CAAK,EACpB0B,EAAA,EAAAmB,EAAS,EAAI,6LCnCfnD,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,iRCIDP,EAAK,CAAA,CAAA,+BAClBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAHlCG,EAKCC,EAAAF,EAAAG,CAAA,wCAJ0BL,EAAK,CAAA,CAAA,OAClBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EARtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCQ1BP,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,8RCIdN,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAOKC,EAAAF,EAAAG,CAAA,cADGL,EAAK,CAAA,8BAALA,EAAK,CAAA,QALCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,6RCIdN,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAOKC,EAAAF,EAAAG,CAAA,cADGL,EAAK,CAAA,8BAALA,EAAK,CAAA,QALCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCMXP,EAAK,CAAA,CAAA,+BAFRC,EAAA0D,EAAA,QAAA3D,OAAS,OAAO,EACdC,EAAA0D,EAAA,UAAA3D,OAAS,SAAS,+BAFlCG,EAG4BC,EAAAuD,EAAAtD,CAAA,4BAAXL,EAAK,CAAA,CAAA,OAFRC,EAAA0D,EAAA,QAAA3D,OAAS,OAAO,OACdC,EAAA0D,EAAA,UAAA3D,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,kSCWbP,EAAI,CAAA,EAAA,0CAAP,IAAE,iBAAZG,EAA6BC,EAAAwD,EAAAvD,CAAA,EAAzBwD,EAAqBD,EAAAE,CAAA,oCAAZ9D,EAAI,CAAA,EAAA,KAAAY,EAAAmD,EAAAC,CAAA,0HAGjB7D,EAAyBC,EAAAwD,EAAAvD,CAAA,2CADrB,MAAM,QAAQL,OAAUA,EAAK,CAAA,EAAC,OAAS,MAHrC,MAAM,QAAQA,EAAK,CAAA,CAAA,EAAIA,EAAM,CAAA,EAAA,MAAM,EAAG,CAAC,GAAKA,EAAK,CAAA,CAAA,CAAA,uBAAtD,OAAIgB,GAAA,wIAJOf,EAAAgE,EAAA,QAAAjE,OAAS,OAAO,EACdC,EAAAgE,EAAA,UAAAjE,OAAS,SAAS,+BAFlCG,EAWIC,EAAA6D,EAAA5D,CAAA,+FANI,MAAM,QAAQL,EAAK,CAAA,CAAA,EAAIA,EAAM,CAAA,EAAA,MAAM,EAAG,CAAC,GAAKA,EAAK,CAAA,CAAA,CAAA,oBAAtD,OAAIgB,GAAA,EAAA,gHAAJ,eAGG,MAAM,QAAQhB,OAAUA,EAAK,CAAA,EAAC,OAAS,2DAP/BC,EAAAgE,EAAA,QAAAjE,OAAS,OAAO,OACdC,EAAAgE,EAAA,UAAAjE,OAAS,SAAS,2FAPtB,MAAAM,CAAwB,EAAAC,GACxB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,+MCerB,MAAM2D,EAAgB,CAC5B,SAAUC,GACV,SAAUC,GACV,cAAeC,GACf,OAAQC,GACR,OAAQC,GACR,MAAOC,GACP,MAAOC,GACP,QAASC,GACT,MAAOC,GACP,MAAOC,GACP,KAAMC,GACN,UAAWC,GACX,QAASC,GACT,YAAaC,GACb,WAAYC,GACZ,SAAUC,GACV,KAAMC,GACN,KAAMC,GACN,aAAcC,EACf,8MCkHiC,MAAAC,EAAAC,KAAWA,EAAC,EAAA,CAAA,kLAnBhCvF,EAAO,CAAA,CAAA,uBAAZ,OAAIgB,GAAA,6BAQAhB,EAAc,EAAA,CAAA,uBAAnB,OAAIgB,GAAA,+WAZTb,EA8CKC,EAAAF,EAAAG,CAAA,EA7CJwD,EA4CO3D,EAAAyC,CAAA,EA3CNkB,EAQOlB,EAAA6C,CAAA,EAPN3B,EAMI2B,EAAAzC,CAAA,0DAELc,EAiCOlB,EAAA8C,CAAA,+EAxCEzF,EAAO,CAAA,CAAA,oBAAZ,OAAIgB,GAAA,EAAA,mHAAJ,4BAQIhB,EAAc,EAAA,CAAA,oBAAnB,OAAIgB,GAAA,EAAA,2GAAJ,OAAIA,EAAA0E,EAAA,OAAA1E,GAAA,yCAAJ,OAAIA,GAAA,4IArCDhB,EAAgB,EAAA,CAAA,uBAArB,OAAI,GAAA,qKADPG,EAwBKC,EAAAF,EAAAG,CAAA,qFAvBGL,EAAgB,EAAA,CAAA,oBAArB,OAAIgB,GAAA,EAAA,2GAAJ,OAAIA,EAAA0E,EAAA,OAAA1E,GAAA,yCAAJ,OAAIA,GAAA,iIA+BAhB,EAAM,EAAA,EAAA,+EADRG,EAEIC,EAAAuF,EAAAtF,CAAA,uCADFL,EAAM,EAAA,EAAA,KAAAY,EAAAgF,EAAAC,CAAA,mDA0BE7F,EAAS,EAAA,wEAIL,SAAAA,QAAkBA,EAAC,EAAA,QACtBA,EAAC,EAAA,+DAXU8F,EAAAlD,EAAA,YAAA5C,QAAmB,UACnC,OACA,MAAM,mBACFA,EAAc,EAAA,CAAA,EAAA,iBAAA,UAJtBG,EAcIC,EAAAwC,EAAAvC,CAAA,uEAHQM,EAAA,CAAA,EAAA,QAAAoF,EAAA,SAAA/F,QAAkBA,EAAC,EAAA,sBAJvBA,EAAS,EAAA,GAAA,kKANG8F,EAAAlD,EAAA,YAAA5C,QAAmB,UACnC,OACA,MAAM,yBACFA,EAAc,EAAA,CAAA,EAAA,0JALlBa,EAAAb,QAAmB,QAAakE,EAAclE,SAAoB,QAASgG,GAAAhG,CAAA,sEAA3EA,QAAmB,QAAakE,EAAclE,SAAoB,iNAFjEA,EAAU,EAAA,CAAA,uBAAf,OAAIgB,GAAA,4OATPb,EA6BIC,EAAA2C,EAAA1C,CAAA,wKApBIL,EAAU,EAAA,CAAA,oBAAf,OAAIgB,GAAA,EAAA,wGAAJ,OAAIA,EAAA0E,EAAA,OAAA1E,GAAA,yCAAJ,OAAIA,GAAA,8IAlCA,IAAAiF,EAAAjG,EAAe,EAAA,EAAA,CAAC,EAAE,CAAC,EAAE,sCACpB,MAAAA,MAAW,CAAC,mCAGT,SAAAA,QAAkBA,EAAC,EAAA,QACtBA,EAAC,EAAA,gHALF,GACCW,EAAA,CAAA,EAAA,OAAAoF,EAAA,MAAA/F,MAAW,CAAC,GAGTW,EAAA,CAAA,EAAA,QAAAoF,EAAA,SAAA/F,QAAkBA,EAAC,EAAA,GAJvBW,EAAA,CAAA,EAAA,OAAAsF,KAAAA,EAAAjG,EAAe,EAAA,EAAA,CAAC,EAAE,CAAC,EAAE,WAAS,kRAFjCkG,EAAA,OAAO,KAAKhC,CAAa,EAAE,SAASlE,EAAU,CAAA,EAAC,CAAC,CAAM,GAAAkE,EAAclE,KAAW,CAAC,CAAA,wLAVtFG,EAoBQC,EAAA+F,EAAA9F,CAAA,kHAVFM,EAAA,CAAA,EAAA,IAAAuF,EAAA,OAAO,KAAKhC,CAAa,EAAE,SAASlE,EAAU,CAAA,EAAC,CAAC,CAAM,GAAAkE,EAAclE,KAAW,CAAC,CAAA,wMAiEhFA,EAAa,EAAA,CAAA,uBAAlB,OAAIgB,GAAA,+CAFc;AAAA,IAEpB,sFAFDb,EAcKC,EAAAF,EAAAG,CAAA,qFAZGL,EAAa,EAAA,CAAA,oBAAlB,OAAIgB,GAAA,EAAA,mHAAJ,oDAQE6E,EAAA7F,MAAe,EAAC,gHAHGC,EAAAkG,EAAA,eAAAnG,QAASA,EAAY,EAAA,CAAA,UAD1CG,EAKQC,EAAA+F,EAAA9F,CAAA,uDADNM,EAAA,CAAA,EAAA,MAAAkF,KAAAA,EAAA7F,MAAe,EAAC,KAAAY,EAAAgF,EAAAC,CAAA,cAHG5F,EAAAkG,EAAA,eAAAnG,QAASA,EAAY,EAAA,CAAA,kGAH1CG,EAAaC,EAAAF,EAAAG,CAAA,2DADT,OAAAL,WAAmBuC,qQA/EtBvC,EAAO,EAAA,EAAA,gCA2EPA,EAAQ,EAAA,GAAAc,GAAAd,CAAA,2DA7EXA,EAAK,CAAA,CAAA,qdAhBPG,EAiBKC,EAAAF,EAAAG,CAAA,EAhBJwD,EAcK3D,EAAAkG,CAAA,EAJJvC,EAGCuC,EAAAC,CAAA,kGAEDrG,EAAK,CAAA,CAAA,6IA6EFA,EAAQ,EAAA,GAAAsG,EAAA,EAAAtG,EAAAW,CAAA,gKArGJ,2EAKO,aACL,+ZA9EA,WAAA4F,CAA0C,EAAAhG,EAC1C,CAAA,MAAAiG,EAAQ,UAAU,EAAAjG,GAClB,QAAAkG,CAAiB,EAAAlG,GACjB,QAAAmG,CAAgB,EAAAnG,EAChB,CAAA,QAAAoG,EAAU,EAAE,EAAApG,GACZ,aAAAqG,EAAY,EAAA,EAAArG,EACZ,CAAA,QAAAsG,EAAU,EAAI,EAAAtG,EACd,CAAA,MAAAD,EAAuB,IAAI,EAAAC,GAC3B,KAAAuG,CAAY,EAAAvG,GACZ,SAAAwG,CAAuB,EAAAxG,EACvB,CAAA,iBAAAyG,EAAmB,EAAE,EAAAzG,EACrB,CAAA,MAAA0G,EAAuB,IAAI,EAAA1G,EAC3B,CAAA,UAAA2G,EAAgC,MAAS,EAAA3G,GACzC,OAAA4G,CAGT,EAAA5G,EAEEmB,GAAsB0F,GAA0B,KAAMN,EAAMC,CAAQ,EACpEM,EAAO,EAEPC,GAAWZ,EAAQ,OAASM,EAE5BO,EACAC,EACAC,EAAa,CAAA,EAEbC,KAEK,SAAAC,EAAkB3G,EAAS,CACnCgB,EAAA,GAAA0F,EAAgB1G,CAAC,WAET4G,IAAiB,CACzB5F,EAAA,GAAA0F,IAAkB,mBAuEd1F,EAAA,EAAA1B,EAAQU,EAAIqG,EAAOL,CAAgB,EACnCG,EAAO,SAAS,QAAS7G,CAAK,EAC9B6G,EAAO,SAAS,SAAQ,CAAI,MAAO7G,EAAO,MAAOuH,CAAU,CAAA,GAEvCzE,GAAApC,GAAA2G,EAAkB3G,CAAC,SACnB4G,YAgClB5F,EAAA,EAAA1B,EAAQU,EAAIqG,EAAOL,CAAgB,EACnCG,EAAO,SAAS,QAAS7G,CAAK,GAEVwH,GAAA9G,GAAA2G,EAAkB3G,CAAC,SACnB4G,KAqCLG,GAAAC,GAAAhG,EAAA,GAAAqF,EAAOW,CAAY,qhBAlKtCC,EAAU1B,EAAW,OAAS,CAAC,2BAiB7Be,SACHG,EAAa,CAAA,CAAA,EACbzF,EAAA,GAAAuF,EAAmBb,EAAQ,MAC1BW,EAAOL,GACNK,EAAO,GAAKL,CAAgB,CAAA,OAE9BQ,EAAa,KAAK,KAAKd,EAAQ,OAASM,CAAgB,CAAA,GACvD,EAAGK,EAAMG,EAAa,CAAC,EAAE,QAASnH,GAAM,SAC/BW,EAAIX,EAAS,EAAGW,GAAKX,EAAS,EAAGW,IACrCA,GAAK,GAAKA,EAAIwG,GAAU,CAAKC,EAAc,SAASzG,CAAC,IAEvDyG,EAAc,OAAS,GACvBzG,EAAIyG,EAAcA,EAAc,OAAS,CAAC,EAAI,GAE9CA,EAAc,KAAI,EAAG,EAEtBA,EAAc,KAAKzG,CAAC,WAKvBuG,EAAmBb,EAAQ,MAAK,CAAA,uBAI/B1E,EAAA,GAAAkG,EAAiBX,EAAiB,IAAKM,GACzCA,EAAW,IAAK,CAAAM,EAAaC,MAAC,CAC7B,MAAOD,EACP,UAAWjE,EAAcqC,EAAW6B,EAAC,CAAA", "x_google_ignoreList": [0]}