const __vite__fileDeps=["./module-LTv20P1S.js","./module-C-VadMaF.js","./index-D5ROCp7B.js","./index-DuXXhepF.css","./module-BA06XY8_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{s as ct,W as rn,p as ft,a as ot,A as ln,S as an}from"./StaticAudio-ivH08Mmp.js";import{_ as dt,b as un}from"./index-D5ROCp7B.js";import{U as _n}from"./Upload-46YxStuW.js";import{M as Ut}from"./ModifyUpload-By7WzcPJ.js";import{S as qt}from"./Index-D21IHG0c.js";import{B as cn}from"./BlockLabel-BXXlQleC.js";import{M as fn}from"./Music-CDm0RGMk.js";import{S as dn}from"./SelectSource-lBfDLlH8.js";import{P as mn}from"./Trim-UKwaW4UI.js";import{f as se}from"./Blocks-Dw_9NR1K.js";import{B as Ot}from"./Button-uOcat6Z0.js";import{U as hn}from"./UploadText-Dnj0K08n.js";import{default as So}from"./Example-BQyGztrG.js";import"./Empty-CLiqUlWX.js";import"./ShareButton-OlciWAJu.js";import"./Download-DVtk-Jv3.js";import"./Undo-CpmTQw3B.js";import"./file-url-BIHPd7vS.js";import"./DownloadLink-BgAM71ly.js";import"./svelte/svelte.js";/* empty css                                                   */import"./Upload-Cp8Go_XF.js";function it(n,e,t,i){return new(t||(t=Promise))(function(o,a){function s(f){try{r(i.next(f))}catch(l){a(l)}}function u(f){try{r(i.throw(f))}catch(l){a(l)}}function r(f){var l;f.done?o(f.value):(l=f.value,l instanceof t?l:new t(function(c){c(l)})).then(s,u)}r((i=i.apply(n,[])).next())})}class gn{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const o=()=>{this.removeEventListener(e,o),this.removeEventListener(e,t)};return this.addEventListener(e,o),o}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class bn extends gn{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}const pn=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class Le extends bn{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:(t=e.audioBitsPerSecond)!==null&&t!==void 0?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new Le(e||{})}renderMicStream(e){const t=new AudioContext,i=t.createMediaStreamSource(e),o=t.createAnalyser();i.connect(o);const a=o.frequencyBinCount,s=new Float32Array(a),u=a/t.sampleRate;let r;const f=()=>{o.getFloatTimeDomainData(s),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load("",[s],u)),r=requestAnimationFrame(f)};return f(),()=>{cancelAnimationFrame(r),i?.disconnect(),t?.close()}}startMic(e){return it(this,void 0,void 0,function*(){let t;try{t=yield navigator.mediaDevices.getUserMedia({audio:!e?.deviceId||{deviceId:e.deviceId}})}catch(o){throw new Error("Error accessing the microphone: "+o.message)}const i=this.renderMicStream(t);return this.subscriptions.push(this.once("destroy",i)),this.stream=t,t})}stopMic(){this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(e){return it(this,void 0,void 0,function*(){const t=this.stream||(yield this.startMic(e)),i=this.mediaRecorder||new MediaRecorder(t,{mimeType:this.options.mimeType||pn.find(a=>MediaRecorder.isTypeSupported(a)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=i,this.stopRecording();const o=[];i.ondataavailable=a=>{a.data.size>0&&o.push(a.data)},i.onstop=()=>{var a;const s=new Blob(o,{type:i.mimeType});this.emit("record-end",s),this.options.renderRecordedAudio!==!1&&((a=this.wavesurfer)===null||a===void 0||a.load(URL.createObjectURL(s)))},i.start(),this.emit("record-start")})}isRecording(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="recording"}isPaused(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="paused"}stopRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.stop())}pauseRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.pause(),this.emit("record-pause"))}resumeRecording(){var e;this.isPaused()&&((e=this.mediaRecorder)===null||e===void 0||e.resume(),this.emit("record-resume"))}static getAvailableAudioDevices(){return it(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(e=>e.filter(t=>t.kind==="audioinput"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}const{SvelteComponent:wn,append:Wt,attr:mt,destroy_each:vn,detach:Oe,element:rt,empty:kn,ensure_array_like:ht,flush:gt,init:yn,insert:We,noop:bt,safe_not_equal:Dn,set_data:Nt,set_input_value:st,text:jt}=window.__gradio__svelte__internal,{createEventDispatcher:Rn}=window.__gradio__svelte__internal;function pt(n,e,t){const i=n.slice();return i[3]=e[t],i}function Sn(n){let e,t=ht(n[0]),i=[];for(let o=0;o<t.length;o+=1)i[o]=wt(pt(n,t,o));return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=kn()},m(o,a){for(let s=0;s<i.length;s+=1)i[s]&&i[s].m(o,a);We(o,e,a)},p(o,a){if(a&1){t=ht(o[0]);let s;for(s=0;s<t.length;s+=1){const u=pt(o,t,s);i[s]?i[s].p(u,a):(i[s]=wt(u),i[s].c(),i[s].m(e.parentNode,e))}for(;s<i.length;s+=1)i[s].d(1);i.length=t.length}},d(o){o&&Oe(e),vn(i,o)}}}function En(n){let e,t=n[1]("audio.no_microphone")+"",i;return{c(){e=rt("option"),i=jt(t),e.__value="",st(e,e.__value)},m(o,a){We(o,e,a),Wt(e,i)},p(o,a){a&2&&t!==(t=o[1]("audio.no_microphone")+"")&&Nt(i,t)},d(o){o&&Oe(e)}}}function wt(n){let e,t=n[3].label+"",i,o;return{c(){e=rt("option"),i=jt(t),e.__value=o=n[3].deviceId,st(e,e.__value)},m(a,s){We(a,e,s),Wt(e,i)},p(a,s){s&1&&t!==(t=a[3].label+"")&&Nt(i,t),s&1&&o!==(o=a[3].deviceId)&&(e.__value=o,st(e,e.__value))},d(a){a&&Oe(e)}}}function Mn(n){let e,t;function i(s,u){return s[0].length===0?En:Sn}let o=i(n),a=o(n);return{c(){e=rt("select"),a.c(),mt(e,"class","mic-select svelte-1v4948z"),mt(e,"aria-label","Select input device"),e.disabled=t=n[0].length===0},m(s,u){We(s,e,u),a.m(e,null)},p(s,[u]){o===(o=i(s))&&a?a.p(s,u):(a.d(1),a=o(s),a&&(a.c(),a.m(e,null))),u&1&&t!==(t=s[0].length===0)&&(e.disabled=t)},i:bt,o:bt,d(s){s&&Oe(e),a.d()}}}function An(n,e,t){let{i18n:i}=e,{micDevices:o=[]}=e;const a=Rn();return n.$$set=s=>{"i18n"in s&&t(1,i=s.i18n),"micDevices"in s&&t(0,o=s.micDevices)},n.$$.update=()=>{if(n.$$.dirty&2)try{let s=[];Le.getAvailableAudioDevices().then(u=>{t(0,o=u),u.forEach(r=>{r.deviceId&&s.push(r)}),t(0,o=s)})}catch(s){throw s instanceof DOMException&&s.name=="NotAllowedError"&&a("error",i("audio.allow_recording_access")),s}},[o,i]}class Ft extends wn{constructor(e){super(),yn(this,e,An,Mn,Dn,{i18n:1,micDevices:0})}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),gt()}get micDevices(){return this.$$.ctx[0]}set micDevices(e){this.$$set({micDevices:e}),gt()}}const{SvelteComponent:Cn,add_flush_callback:Pn,append:U,attr:J,bind:Ln,binding_callbacks:we,create_component:vt,destroy_component:kt,detach:Ht,element:ne,flush:Me,init:Bn,insert:Vt,listen:Ae,mount_component:yt,run_all:In,safe_not_equal:zn,set_data:Ce,space:pe,text:Pe,transition_in:Dt,transition_out:Rt}=window.__gradio__svelte__internal;function St(n){let e,t;return{c(){e=ne("time"),t=Pe(n[2]),J(e,"class","duration-button duration svelte-1d9m1oy")},m(i,o){Vt(i,e,o),U(e,t)},p(i,o){o&4&&Ce(t,i[2])},d(i){i&&Ht(e)}}}function Tn(n){let e,t,i,o=n[1]("audio.record")+"",a,s,u,r=n[1]("audio.stop")+"",f,l,c,d,m=n[1]("audio.stop")+"",p,g,w,y,R,v,z=n[1]("audio.resume")+"",q,M,W,C,h,S,L,P;y=new mn({});let D=n[4]&&!n[3]&&St(n);function F(k){n[21](k)}let B={i18n:n[1]};return n[5]!==void 0&&(B.micDevices=n[5]),C=new Ft({props:B}),we.push(()=>Ln(C,"micDevices",F)),{c(){e=ne("div"),t=ne("div"),i=ne("button"),a=Pe(o),s=pe(),u=ne("button"),f=Pe(r),c=pe(),d=ne("button"),p=Pe(m),g=pe(),w=ne("button"),vt(y.$$.fragment),R=pe(),v=ne("button"),q=Pe(z),M=pe(),D&&D.c(),W=pe(),vt(C.$$.fragment),J(i,"class","record record-button svelte-1d9m1oy"),J(u,"class",l="stop-button "+(n[0].isPaused()?"stop-button-paused":"")+" svelte-1d9m1oy"),J(d,"id","stop-paused"),J(d,"class","stop-button-paused svelte-1d9m1oy"),J(w,"aria-label","pause"),J(w,"class","pause-button svelte-1d9m1oy"),J(v,"class","resume-button svelte-1d9m1oy"),J(t,"class","wrapper svelte-1d9m1oy"),J(e,"class","controls svelte-1d9m1oy")},m(k,I){Vt(k,e,I),U(e,t),U(t,i),U(i,a),n[11](i),U(t,s),U(t,u),U(u,f),n[13](u),U(t,c),U(t,d),U(d,p),n[15](d),U(t,g),U(t,w),yt(y,w,null),n[17](w),U(t,R),U(t,v),U(v,q),n[19](v),U(t,M),D&&D.m(t,null),U(e,W),yt(C,e,null),S=!0,L||(P=[Ae(i,"click",n[12]),Ae(u,"click",n[14]),Ae(d,"click",n[16]),Ae(w,"click",n[18]),Ae(v,"click",n[20])],L=!0)},p(k,[I]){(!S||I&2)&&o!==(o=k[1]("audio.record")+"")&&Ce(a,o),(!S||I&2)&&r!==(r=k[1]("audio.stop")+"")&&Ce(f,r),(!S||I&1&&l!==(l="stop-button "+(k[0].isPaused()?"stop-button-paused":"")+" svelte-1d9m1oy"))&&J(u,"class",l),(!S||I&2)&&m!==(m=k[1]("audio.stop")+"")&&Ce(p,m),(!S||I&2)&&z!==(z=k[1]("audio.resume")+"")&&Ce(q,z),k[4]&&!k[3]?D?D.p(k,I):(D=St(k),D.c(),D.m(t,null)):D&&(D.d(1),D=null);const K={};I&2&&(K.i18n=k[1]),!h&&I&32&&(h=!0,K.micDevices=k[5],Pn(()=>h=!1)),C.$set(K)},i(k){S||(Dt(y.$$.fragment,k),Dt(C.$$.fragment,k),S=!0)},o(k){Rt(y.$$.fragment,k),Rt(C.$$.fragment,k),S=!1},d(k){k&&Ht(e),n[11](null),n[13](null),n[15](null),kt(y),n[17](null),n[19](null),D&&D.d(),kt(C),L=!1,In(P)}}}function Un(n,e,t){let{record:i}=e,{i18n:o}=e,a=[],s,u,r,f,l,{record_time:c}=e,{show_recording_waveform:d}=e,{timing:m=!1}=e;function p(h){we[h?"unshift":"push"](()=>{s=h,t(6,s),t(0,i)})}const g=()=>i.startRecording();function w(h){we[h?"unshift":"push"](()=>{f=h,t(9,f),t(0,i)})}const y=()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopRecording()};function R(h){we[h?"unshift":"push"](()=>{l=h,t(10,l),t(0,i)})}const v=()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopRecording()};function z(h){we[h?"unshift":"push"](()=>{u=h,t(7,u),t(0,i)})}const q=()=>i.pauseRecording();function M(h){we[h?"unshift":"push"](()=>{r=h,t(8,r),t(0,i)})}const W=()=>i.resumeRecording();function C(h){a=h,t(5,a)}return n.$$set=h=>{"record"in h&&t(0,i=h.record),"i18n"in h&&t(1,o=h.i18n),"record_time"in h&&t(2,c=h.record_time),"show_recording_waveform"in h&&t(3,d=h.show_recording_waveform),"timing"in h&&t(4,m=h.timing)},n.$$.update=()=>{n.$$.dirty&1&&i.on("record-start",()=>{i.startMic(),t(6,s.style.display="none",s),t(9,f.style.display="flex",f),t(7,u.style.display="block",u)}),n.$$.dirty&1&&i.on("record-end",()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopMic(),t(6,s.style.display="flex",s),t(9,f.style.display="none",f),t(7,u.style.display="none",u),t(6,s.disabled=!1,s)}),n.$$.dirty&1&&i.on("record-pause",()=>{t(7,u.style.display="none",u),t(8,r.style.display="block",r),t(9,f.style.display="none",f),t(10,l.style.display="flex",l)}),n.$$.dirty&1&&i.on("record-resume",()=>{t(7,u.style.display="block",u),t(8,r.style.display="none",r),t(6,s.style.display="none",s),t(9,f.style.display="flex",f),t(10,l.style.display="none",l)})},[i,o,c,d,m,a,s,u,r,f,l,p,g,w,y,R,v,z,q,M,W,C]}class qn extends Cn{constructor(e){super(),Bn(this,e,Un,Tn,zn,{record:0,i18n:1,record_time:2,show_recording_waveform:3,timing:4})}get record(){return this.$$.ctx[0]}set record(e){this.$$set({record:e}),Me()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),Me()}get record_time(){return this.$$.ctx[2]}set record_time(e){this.$$set({record_time:e}),Me()}get show_recording_waveform(){return this.$$.ctx[3]}set show_recording_waveform(e){this.$$set({show_recording_waveform:e}),Me()}get timing(){return this.$$.ctx[4]}set timing(e){this.$$set({timing:e}),Me()}}const{SvelteComponent:On,add_flush_callback:ze,append:V,attr:Z,bind:Te,binding_callbacks:oe,check_outros:Et,create_component:Jt,destroy_component:Gt,detach:Be,element:$,flush:ue,group_outros:Mt,init:Wn,insert:Ie,mount_component:Yt,noop:Nn,safe_not_equal:jn,set_data:Kt,space:ve,text:Qt,transition_in:ie,transition_out:ke}=window.__gradio__svelte__internal,{onMount:Fn}=window.__gradio__svelte__internal,{createEventDispatcher:Hn}=window.__gradio__svelte__internal;function At(n){let e,t,i,o,a,s=n[0]==="edit"&&n[16]>0&&Ct(n);function u(l,c){return l[15]?Jn:Vn}let r=u(n),f=r(n);return{c(){e=$("div"),t=$("time"),t.textContent="0:00",i=ve(),o=$("div"),s&&s.c(),a=ve(),f.c(),Z(t,"class","time svelte-9n45fh"),Z(e,"class","timestamps svelte-9n45fh")},m(l,c){Ie(l,e,c),V(e,t),n[22](t),V(e,i),V(e,o),s&&s.m(o,null),V(o,a),f.m(o,null)},p(l,c){l[0]==="edit"&&l[16]>0?s?s.p(l,c):(s=Ct(l),s.c(),s.m(o,a)):s&&(s.d(1),s=null),r===(r=u(l))&&f?f.p(l,c):(f.d(1),f=r(l),f&&(f.c(),f.m(o,null)))},d(l){l&&Be(e),n[22](null),s&&s.d(),f.d()}}}function Ct(n){let e,t=se(n[16])+"",i;return{c(){e=$("time"),i=Qt(t),Z(e,"class","trim-duration svelte-9n45fh")},m(o,a){Ie(o,e,a),V(e,i)},p(o,a){a[0]&65536&&t!==(t=se(o[16])+"")&&Kt(i,t)},d(o){o&&Be(e)}}}function Vn(n){let e;return{c(){e=$("time"),e.textContent="0:00",Z(e,"class","duration svelte-9n45fh")},m(t,i){Ie(t,e,i),n[23](e)},p:Nn,d(t){t&&Be(e),n[23](null)}}}function Jn(n){let e,t=se(n[14])+"",i;return{c(){e=$("time"),i=Qt(t),Z(e,"class","duration svelte-9n45fh")},m(o,a){Ie(o,e,a),V(e,i)},p(o,a){a[0]&16384&&t!==(t=se(o[14])+"")&&Kt(i,t)},d(o){o&&Be(e)}}}function Pt(n){let e,t,i;function o(s){n[24](s)}let a={i18n:n[1],timing:n[15],show_recording_waveform:n[2].show_recording_waveform,record_time:se(n[14])};return n[6]!==void 0&&(a.record=n[6]),e=new qn({props:a}),oe.push(()=>Te(e,"record",o)),{c(){Jt(e.$$.fragment)},m(s,u){Yt(e,s,u),i=!0},p(s,u){const r={};u[0]&2&&(r.i18n=s[1]),u[0]&32768&&(r.timing=s[15]),u[0]&4&&(r.show_recording_waveform=s[2].show_recording_waveform),u[0]&16384&&(r.record_time=se(s[14])),!t&&u[0]&64&&(t=!0,r.record=s[6],ze(()=>t=!1)),e.$set(r)},i(s){i||(ie(e.$$.fragment,s),i=!0)},o(s){ke(e.$$.fragment,s),i=!1},d(s){Gt(e,s)}}}function Lt(n){let e,t,i,o,a;function s(l){n[25](l)}function u(l){n[26](l)}function r(l){n[27](l)}let f={container:n[10],playing:n[9],audio_duration:n[13],i18n:n[1],editable:n[4],interactive:!0,handle_trim_audio:n[17],show_redo:!0,handle_reset_value:n[3],waveform_options:n[2]};return n[5]!==void 0&&(f.waveform=n[5]),n[16]!==void 0&&(f.trimDuration=n[16]),n[0]!==void 0&&(f.mode=n[0]),e=new rn({props:f}),oe.push(()=>Te(e,"waveform",s)),oe.push(()=>Te(e,"trimDuration",u)),oe.push(()=>Te(e,"mode",r)),{c(){Jt(e.$$.fragment)},m(l,c){Yt(e,l,c),a=!0},p(l,c){const d={};c[0]&1024&&(d.container=l[10]),c[0]&512&&(d.playing=l[9]),c[0]&8192&&(d.audio_duration=l[13]),c[0]&2&&(d.i18n=l[1]),c[0]&16&&(d.editable=l[4]),c[0]&8&&(d.handle_reset_value=l[3]),c[0]&4&&(d.waveform_options=l[2]),!t&&c[0]&32&&(t=!0,d.waveform=l[5],ze(()=>t=!1)),!i&&c[0]&65536&&(i=!0,d.trimDuration=l[16],ze(()=>i=!1)),!o&&c[0]&1&&(o=!0,d.mode=l[0],ze(()=>o=!1)),e.$set(d)},i(l){a||(ie(e.$$.fragment,l),a=!0)},o(l){ke(e.$$.fragment,l),a=!1},d(l){Gt(e,l)}}}function Gn(n){let e,t,i,o,a,s,u,r,f=(n[15]||n[12])&&n[2].show_recording_waveform&&At(n),l=n[11]&&!n[12]&&Pt(n),c=n[5]&&n[12]&&Lt(n);return{c(){e=$("div"),t=$("div"),i=ve(),o=$("div"),a=ve(),f&&f.c(),s=ve(),l&&l.c(),u=ve(),c&&c.c(),Z(t,"class","microphone svelte-9n45fh"),Z(t,"data-testid","microphone-waveform"),Z(o,"data-testid","recording-waveform"),Z(e,"class","component-wrapper svelte-9n45fh")},m(d,m){Ie(d,e,m),V(e,t),n[20](t),V(e,i),V(e,o),n[21](o),V(e,a),f&&f.m(e,null),V(e,s),l&&l.m(e,null),V(e,u),c&&c.m(e,null),r=!0},p(d,m){(d[15]||d[12])&&d[2].show_recording_waveform?f?f.p(d,m):(f=At(d),f.c(),f.m(e,s)):f&&(f.d(1),f=null),d[11]&&!d[12]?l?(l.p(d,m),m[0]&6144&&ie(l,1)):(l=Pt(d),l.c(),ie(l,1),l.m(e,u)):l&&(Mt(),ke(l,1,1,()=>{l=null}),Et()),d[5]&&d[12]?c?(c.p(d,m),m[0]&4128&&ie(c,1)):(c=Lt(d),c.c(),ie(c,1),c.m(e,null)):c&&(Mt(),ke(c,1,1,()=>{c=null}),Et())},i(d){r||(ie(l),ie(c),r=!0)},o(d){ke(l),ke(c),r=!1},d(d){d&&Be(e),n[20](null),n[21](null),f&&f.d(),l&&l.d(),c&&c.d()}}}function Yn(n,e,t){let{mode:i}=e,{i18n:o}=e,{dispatch_blob:a}=e,{waveform_settings:s}=e,{waveform_options:u={show_recording_waveform:!0}}=e,{handle_reset_value:r}=e,{editable:f=!0}=e,l,c,d=!1,m,p,g,w=null,y,R,v,z=0,q,M=!1,W=0;const C=()=>{clearInterval(q),q=setInterval(()=>{t(14,z++,z)},1e3)},h=Hn();function S(){if(C(),t(15,M=!0),h("start_recording"),u.show_recording_waveform){let b=p;b&&(b.style.display="block")}}async function L(b){t(14,z=0),t(15,M=!1),clearInterval(q);try{const H=await b.arrayBuffer(),Q=await new AudioContext({sampleRate:s.sampleRate}).decodeAudioData(H);Q&&await ft(Q).then(async be=>{await a([be],"change"),await a([be],"stop_recording")})}catch(H){console.error(H)}}const P=()=>{p&&t(11,p.innerHTML="",p),l!==void 0&&l.destroy(),p&&(l=ot.create({...s,normalize:!1,container:p}),t(6,g=l.registerPlugin(Le.create())),g.startMic(),g?.on("record-end",L),g?.on("record-start",S),g?.on("record-pause",()=>{h("pause_recording"),clearInterval(q)}),g?.on("record-end",b=>{t(12,w=URL.createObjectURL(b));const H=p,Y=m;H&&(H.style.display="none"),Y&&w&&(Y.innerHTML="",D())}))},D=()=>{let b=m;!w||!b||t(5,c=ot.create({container:b,url:w,...s}))},F=async(b,H)=>{t(0,i="edit");const Y=c.getDecodedData();Y&&await ft(Y,b,H).then(async Q=>{await a([Q],"change"),await a([Q],"stop_recording"),c.destroy(),D()}),h("edit")};Fn(()=>{P(),window.addEventListener("keydown",b=>{b.key==="ArrowRight"?ct(c,.1):b.key==="ArrowLeft"&&ct(c,-.1)})});function B(b){oe[b?"unshift":"push"](()=>{p=b,t(11,p)})}function k(b){oe[b?"unshift":"push"](()=>{m=b,t(10,m)})}function I(b){oe[b?"unshift":"push"](()=>{y=b,t(7,y),t(5,c)})}function K(b){oe[b?"unshift":"push"](()=>{R=b,t(8,R),t(5,c)})}function T(b){g=b,t(6,g)}function G(b){c=b,t(5,c)}function Re(b){W=b,t(16,W)}function Se(b){i=b,t(0,i)}return n.$$set=b=>{"mode"in b&&t(0,i=b.mode),"i18n"in b&&t(1,o=b.i18n),"dispatch_blob"in b&&t(18,a=b.dispatch_blob),"waveform_settings"in b&&t(19,s=b.waveform_settings),"waveform_options"in b&&t(2,u=b.waveform_options),"handle_reset_value"in b&&t(3,r=b.handle_reset_value),"editable"in b&&t(4,f=b.editable)},n.$$.update=()=>{n.$$.dirty[0]&64&&g?.on("record-resume",()=>{C()}),n.$$.dirty[0]&288&&c?.on("decode",b=>{t(13,v=b),R&&t(8,R.textContent=se(b),R)}),n.$$.dirty[0]&160&&c?.on("timeupdate",b=>y&&t(7,y.textContent=se(b),y)),n.$$.dirty[0]&32&&c?.on("pause",()=>{h("pause"),t(9,d=!1)}),n.$$.dirty[0]&32&&c?.on("play",()=>{h("play"),t(9,d=!0)}),n.$$.dirty[0]&32&&c?.on("finish",()=>{h("stop"),t(9,d=!1)})},[i,o,u,r,f,c,g,y,R,d,m,p,w,v,z,M,W,F,a,s,B,k,I,K,T,G,Re,Se]}class Kn extends On{constructor(e){super(),Wn(this,e,Yn,Gn,jn,{mode:0,i18n:1,dispatch_blob:18,waveform_settings:19,waveform_options:2,handle_reset_value:3,editable:4},null,[-1,-1])}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),ue()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),ue()}get dispatch_blob(){return this.$$.ctx[18]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),ue()}get waveform_settings(){return this.$$.ctx[19]}set waveform_settings(e){this.$$set({waveform_settings:e}),ue()}get waveform_options(){return this.$$.ctx[2]}set waveform_options(e){this.$$set({waveform_options:e}),ue()}get handle_reset_value(){return this.$$.ctx[3]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),ue()}get editable(){return this.$$.ctx[4]}set editable(e){this.$$set({editable:e}),ue()}}const{SvelteComponent:Qn,add_flush_callback:Xn,append:x,attr:fe,bind:Zn,binding_callbacks:Xt,create_component:$n,destroy_component:xn,detach:Ne,element:de,flush:_e,init:ei,insert:je,listen:Zt,mount_component:ti,null_to_empty:Bt,safe_not_equal:ni,set_data:$t,set_style:It,space:Ue,text:xt,transition_in:ii,transition_out:oi}=window.__gradio__svelte__internal,{onMount:si}=window.__gradio__svelte__internal;function zt(n){let e;return{c(){e=de("div"),It(e,"display",n[0]?"block":"none")},m(t,i){je(t,e,i),n[10](e)},p(t,i){i&1&&It(e,"display",t[0]?"block":"none")},d(t){t&&Ne(e),n[10](null)}}}function ri(n){let e,t,i,o=n[4]("audio.record")+"",a,s,u;return{c(){e=de("button"),t=de("span"),t.innerHTML='<span class="dot"></span>',i=Ue(),a=xt(o),fe(t,"class","record-icon"),fe(e,"class","record-button svelte-1m31gsz")},m(r,f){je(r,e,f),x(e,t),x(e,i),x(e,a),s||(u=Zt(e,"click",n[12]),s=!0)},p(r,f){f&16&&o!==(o=r[4]("audio.record")+"")&&$t(a,o)},d(r){r&&Ne(e),s=!1,u()}}}function li(n){let e,t,i,o=(n[1]?n[4]("audio.pause"):n[4]("audio.stop"))+"",a,s,u,r;return{c(){e=de("button"),t=de("span"),t.innerHTML='<span class="pinger"></span> <span class="dot"></span>',i=Ue(),a=xt(o),fe(t,"class","record-icon"),fe(e,"class",s=Bt(n[1]?"stop-button-paused":"stop-button")+" svelte-1m31gsz")},m(f,l){je(f,e,l),x(e,t),x(e,i),x(e,a),u||(r=Zt(e,"click",n[11]),u=!0)},p(f,l){l&18&&o!==(o=(f[1]?f[4]("audio.pause"):f[4]("audio.stop"))+"")&&$t(a,o),l&2&&s!==(s=Bt(f[1]?"stop-button-paused":"stop-button")+" svelte-1m31gsz")&&fe(e,"class",s)},d(f){f&&Ne(e),u=!1,r()}}}function ai(n){let e,t,i,o,a,s,u,r=n[5].show_recording_waveform&&zt(n);function f(p,g){return p[0]?li:ri}let l=f(n),c=l(n);function d(p){n[13](p)}let m={i18n:n[4]};return n[8]!==void 0&&(m.micDevices=n[8]),a=new Ft({props:m}),Xt.push(()=>Zn(a,"micDevices",d)),{c(){e=de("div"),r&&r.c(),t=Ue(),i=de("div"),c.c(),o=Ue(),$n(a.$$.fragment),fe(i,"class","controls svelte-1m31gsz"),fe(e,"class","mic-wrap svelte-1m31gsz")},m(p,g){je(p,e,g),r&&r.m(e,null),x(e,t),x(e,i),c.m(i,null),x(i,o),ti(a,i,null),u=!0},p(p,[g]){p[5].show_recording_waveform?r?r.p(p,g):(r=zt(p),r.c(),r.m(e,t)):r&&(r.d(1),r=null),l===(l=f(p))&&c?c.p(p,g):(c.d(1),c=l(p),c&&(c.c(),c.m(i,o)));const w={};g&16&&(w.i18n=p[4]),!s&&g&256&&(s=!0,w.micDevices=p[8],Xn(()=>s=!1)),a.$set(w)},i(p){u||(ii(a.$$.fragment,p),u=!0)},o(p){oi(a.$$.fragment,p),u=!1},d(p){p&&Ne(e),r&&r.d(),c.d(),xn(a)}}}function ui(n,e,t){let{recording:i=!1}=e,{paused_recording:o=!1}=e,{stop:a}=e,{record:s}=e,{i18n:u}=e,{waveform_settings:r}=e,{waveform_options:f={show_recording_waveform:!0}}=e,l,c,d,m=[];si(()=>{p()});const p=()=>{l!==void 0&&l.destroy(),d&&(l=ot.create({...r,height:100,container:d}),t(6,c=l.registerPlugin(Le.create())))};function g(v){Xt[v?"unshift":"push"](()=>{d=v,t(7,d)})}const w=()=>{c?.stopMic(),a()},y=()=>{c?.startMic(),s()};function R(v){m=v,t(8,m)}return n.$$set=v=>{"recording"in v&&t(0,i=v.recording),"paused_recording"in v&&t(1,o=v.paused_recording),"stop"in v&&t(2,a=v.stop),"record"in v&&t(3,s=v.record),"i18n"in v&&t(4,u=v.i18n),"waveform_settings"in v&&t(9,r=v.waveform_settings),"waveform_options"in v&&t(5,f=v.waveform_options)},[i,o,a,s,u,f,c,d,m,r,g,w,y,R]}class _i extends Qn{constructor(e){super(),ei(this,e,ui,ai,ni,{recording:0,paused_recording:1,stop:2,record:3,i18n:4,waveform_settings:9,waveform_options:5})}get recording(){return this.$$.ctx[0]}set recording(e){this.$$set({recording:e}),_e()}get paused_recording(){return this.$$.ctx[1]}set paused_recording(e){this.$$set({paused_recording:e}),_e()}get stop(){return this.$$.ctx[2]}set stop(e){this.$$set({stop:e}),_e()}get record(){return this.$$.ctx[3]}set record(e){this.$$set({record:e}),_e()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),_e()}get waveform_settings(){return this.$$.ctx[9]}set waveform_settings(e){this.$$set({waveform_settings:e}),_e()}get waveform_options(){return this.$$.ctx[5]}set waveform_options(e){this.$$set({waveform_options:e}),_e()}}const{SvelteComponent:ci,add_flush_callback:Fe,append:fi,attr:di,bind:He,binding_callbacks:Ve,bubble:ce,check_outros:lt,create_component:re,create_slot:mi,destroy_component:le,detach:ye,element:hi,empty:en,flush:A,get_all_dirty_from_scope:gi,get_slot_changes:bi,group_outros:at,init:pi,insert:De,mount_component:ae,safe_not_equal:wi,space:qe,transition_in:N,transition_out:j,update_slot_base:vi}=window.__gradio__svelte__internal,{onDestroy:ki,createEventDispatcher:yi}=window.__gradio__svelte__internal;function Di(n){let e,t,i,o,a;e=new Ut({props:{i18n:n[10],download:n[7]?n[1].url:null,absolute:!0}}),e.$on("clear",n[24]),e.$on("edit",n[38]);function s(r){n[39](r)}let u={value:n[1],label:n[3],i18n:n[10],dispatch_blob:n[22],waveform_settings:n[11],waveform_options:n[13],trim_region_settings:n[12],handle_reset_value:n[14],editable:n[15],loop:n[5],interactive:!0};return n[20]!==void 0&&(u.mode=n[20]),i=new ln({props:u}),Ve.push(()=>He(i,"mode",s)),i.$on("stop",n[40]),i.$on("play",n[41]),i.$on("pause",n[42]),i.$on("edit",n[43]),{c(){re(e.$$.fragment),t=qe(),re(i.$$.fragment)},m(r,f){ae(e,r,f),De(r,t,f),ae(i,r,f),a=!0},p(r,f){const l={};f[0]&1024&&(l.i18n=r[10]),f[0]&130&&(l.download=r[7]?r[1].url:null),e.$set(l);const c={};f[0]&2&&(c.value=r[1]),f[0]&8&&(c.label=r[3]),f[0]&1024&&(c.i18n=r[10]),f[0]&2048&&(c.waveform_settings=r[11]),f[0]&8192&&(c.waveform_options=r[13]),f[0]&4096&&(c.trim_region_settings=r[12]),f[0]&16384&&(c.handle_reset_value=r[14]),f[0]&32768&&(c.editable=r[15]),f[0]&32&&(c.loop=r[5]),!o&&f[0]&1048576&&(o=!0,c.mode=r[20],Fe(()=>o=!1)),i.$set(c)},i(r){a||(N(e.$$.fragment,r),N(i.$$.fragment,r),a=!0)},o(r){j(e.$$.fragment,r),j(i.$$.fragment,r),a=!1},d(r){r&&ye(t),le(e,r),le(i,r)}}}function Ri(n){let e,t,i,o;const a=[Ei,Si],s=[];function u(r,f){return r[2]==="microphone"?0:r[2]==="upload"?1:-1}return~(e=u(n))&&(t=s[e]=a[e](n)),{c(){t&&t.c(),i=en()},m(r,f){~e&&s[e].m(r,f),De(r,i,f),o=!0},p(r,f){let l=e;e=u(r),e===l?~e&&s[e].p(r,f):(t&&(at(),j(s[l],1,1,()=>{s[l]=null}),lt()),~e?(t=s[e],t?t.p(r,f):(t=s[e]=a[e](r),t.c()),N(t,1),t.m(i.parentNode,i)):t=null)},i(r){o||(N(t),o=!0)},o(r){j(t),o=!1},d(r){r&&ye(i),~e&&s[e].d(r)}}}function Si(n){let e,t,i;function o(s){n[36](s)}let a={filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",root:n[4],max_file_size:n[16],upload:n[17],stream_handler:n[18],$$slots:{default:[Mi]},$$scope:{ctx:n}};return n[0]!==void 0&&(a.dragging=n[0]),e=new _n({props:a}),Ve.push(()=>He(e,"dragging",o)),e.$on("load",n[25]),e.$on("error",n[37]),{c(){re(e.$$.fragment)},m(s,u){ae(e,s,u),i=!0},p(s,u){const r={};u[0]&16&&(r.root=s[4]),u[0]&65536&&(r.max_file_size=s[16]),u[0]&131072&&(r.upload=s[17]),u[0]&262144&&(r.stream_handler=s[18]),u[1]&16384&&(r.$$scope={dirty:u,ctx:s}),!t&&u[0]&1&&(t=!0,r.dragging=s[0],Fe(()=>t=!1)),e.$set(r)},i(s){i||(N(e.$$.fragment,s),i=!0)},o(s){j(e.$$.fragment,s),i=!1},d(s){le(e,s)}}}function Ei(n){let e,t,i,o,a,s;e=new Ut({props:{i18n:n[10],absolute:!0}}),e.$on("clear",n[24]);const u=[Ci,Ai],r=[];function f(l,c){return l[9]?0:1}return i=f(n),o=r[i]=u[i](n),{c(){re(e.$$.fragment),t=qe(),o.c(),a=en()},m(l,c){ae(e,l,c),De(l,t,c),r[i].m(l,c),De(l,a,c),s=!0},p(l,c){const d={};c[0]&1024&&(d.i18n=l[10]),e.$set(d);let m=i;i=f(l),i===m?r[i].p(l,c):(at(),j(r[m],1,1,()=>{r[m]=null}),lt(),o=r[i],o?o.p(l,c):(o=r[i]=u[i](l),o.c()),N(o,1),o.m(a.parentNode,a))},i(l){s||(N(e.$$.fragment,l),N(o),s=!0)},o(l){j(e.$$.fragment,l),j(o),s=!1},d(l){l&&(ye(t),ye(a)),le(e,l),r[i].d(l)}}}function Mi(n){let e;const t=n[31].default,i=mi(t,n,n[45],null);return{c(){i&&i.c()},m(o,a){i&&i.m(o,a),e=!0},p(o,a){i&&i.p&&(!e||a[1]&16384)&&vi(i,t,o,o[45],e?bi(t,o[45],a,null):gi(o[45]),null)},i(o){e||(N(i,o),e=!0)},o(o){j(i,o),e=!1},d(o){i&&i.d(o)}}}function Ai(n){let e,t,i;function o(s){n[32](s)}let a={i18n:n[10],editable:n[15],dispatch_blob:n[22],waveform_settings:n[11],waveform_options:n[13],handle_reset_value:n[14]};return n[20]!==void 0&&(a.mode=n[20]),e=new Kn({props:a}),Ve.push(()=>He(e,"mode",o)),e.$on("start_recording",n[33]),e.$on("pause_recording",n[34]),e.$on("stop_recording",n[35]),{c(){re(e.$$.fragment)},m(s,u){ae(e,s,u),i=!0},p(s,u){const r={};u[0]&1024&&(r.i18n=s[10]),u[0]&32768&&(r.editable=s[15]),u[0]&2048&&(r.waveform_settings=s[11]),u[0]&8192&&(r.waveform_options=s[13]),u[0]&16384&&(r.handle_reset_value=s[14]),!t&&u[0]&1048576&&(t=!0,r.mode=s[20],Fe(()=>t=!1)),e.$set(r)},i(s){i||(N(e.$$.fragment,s),i=!0)},o(s){j(e.$$.fragment,s),i=!1},d(s){le(e,s)}}}function Ci(n){let e,t;return e=new _i({props:{record:n[23],recording:n[19],stop:n[26],i18n:n[10],waveform_settings:n[11],waveform_options:n[13]}}),{c(){re(e.$$.fragment)},m(i,o){ae(e,i,o),t=!0},p(i,o){const a={};o[0]&524288&&(a.recording=i[19]),o[0]&1024&&(a.i18n=i[10]),o[0]&2048&&(a.waveform_settings=i[11]),o[0]&8192&&(a.waveform_options=i[13]),e.$set(a)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){j(e.$$.fragment,i),t=!1},d(i){le(e,i)}}}function Pi(n){let e,t,i,o,a,s,u,r,f;e=new cn({props:{show_label:n[6],Icon:fn,float:n[2]==="upload"&&n[1]===null,label:n[3]||n[10]("audio.audio")}});const l=[Ri,Di],c=[];function d(g,w){return g[1]===null||g[9]?0:1}o=d(n),a=c[o]=l[o](n);function m(g){n[44](g)}let p={sources:n[8],handle_clear:n[24]};return n[2]!==void 0&&(p.active_source=n[2]),u=new dn({props:p}),Ve.push(()=>He(u,"active_source",m)),{c(){re(e.$$.fragment),t=qe(),i=hi("div"),a.c(),s=qe(),re(u.$$.fragment),di(i,"class","audio-container svelte-cbyffp")},m(g,w){ae(e,g,w),De(g,t,w),De(g,i,w),c[o].m(i,null),fi(i,s),ae(u,i,null),f=!0},p(g,w){const y={};w[0]&64&&(y.show_label=g[6]),w[0]&6&&(y.float=g[2]==="upload"&&g[1]===null),w[0]&1032&&(y.label=g[3]||g[10]("audio.audio")),e.$set(y);let R=o;o=d(g),o===R?c[o].p(g,w):(at(),j(c[R],1,1,()=>{c[R]=null}),lt(),a=c[o],a?a.p(g,w):(a=c[o]=l[o](g),a.c()),N(a,1),a.m(i,s));const v={};w[0]&256&&(v.sources=g[8]),!r&&w[0]&4&&(r=!0,v.active_source=g[2],Fe(()=>r=!1)),u.$set(v)},i(g){f||(N(e.$$.fragment,g),N(a),N(u.$$.fragment,g),f=!0)},o(g){j(e.$$.fragment,g),j(a),j(u.$$.fragment,g),f=!1},d(g){g&&(ye(t),ye(i)),le(e,g),c[o].d(),le(u)}}}const Li=500,Tt=44;function Bi(n,e,t){let{$$slots:i={},$$scope:o}=e,{value:a=null}=e,{label:s}=e,{root:u}=e,{loop:r}=e,{show_label:f=!0}=e,{show_download_button:l=!1}=e,{sources:c=["microphone","upload"]}=e,{pending:d=!1}=e,{streaming:m=!1}=e,{i18n:p}=e,{waveform_settings:g}=e,{trim_region_settings:w={}}=e,{waveform_options:y={}}=e,{dragging:R}=e,{active_source:v}=e,{handle_reset_value:z=()=>{}}=e,{editable:q=!0}=e,{max_file_size:M=null}=e,{upload:W}=e,{stream_handler:C}=e,h=!1,S,L="",P,D=[],F=!1,B=!1,k=[],I;function K(){I=[dt(()=>import("./module-LTv20P1S.js"),__vite__mapDeps([0,1,2,3]),import.meta.url),dt(()=>import("./module-BA06XY8_.js"),__vite__mapDeps([4,1]),import.meta.url)]}m&&K();const T=yi(),G=async(_,O)=>{let X=new File(_,"audio.wav");const Ee=await un([X],O==="stream");t(1,a=(await W(Ee,u,void 0,M||void 0))?.filter(Boolean)[0]),T(O,a)};ki(()=>{m&&S&&S.state!=="inactive"&&S.stop()});async function Re(){let _;try{_=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(O){if(!navigator.mediaDevices){T("error",p("audio.no_device_support"));return}if(O instanceof DOMException&&O.name=="NotAllowedError"){T("error",p("audio.allow_recording_access"));return}throw O}if(_!=null){if(m){const[{MediaRecorder:O,register:X},{connect:Ee}]=await Promise.all(I);await X(await Ee()),S=new O(_,{mimeType:"audio/wav"}),S.addEventListener("dataavailable",Se)}else S=new MediaRecorder(_),S.addEventListener("dataavailable",O=>{k.push(O.data)}),S.addEventListener("stop",async()=>{t(19,h=!1),await G(k,"change"),await G(k,"stop_recording"),k=[]});B=!0}}async function Se(_){let O=await _.data.arrayBuffer(),X=new Uint8Array(O);if(P||(t(28,P=new Uint8Array(O.slice(0,Tt))),X=new Uint8Array(O.slice(Tt))),d)D.push(X);else{let Ee=[P].concat(D,[X]);G(Ee,"stream"),t(29,D=[])}}async function b(){t(19,h=!0),T("start_recording"),B||await Re(),t(28,P=void 0),m&&S.start(Li)}function H(){T("change",null),T("clear"),t(20,L=""),t(1,a=null)}function Y({detail:_}){t(1,a=_),T("change",_),T("upload",_)}function Q(){t(19,h=!1),m&&(T("stop_recording"),S.stop(),d&&t(30,F=!0),G(k,"stop_recording"),T("clear"),t(20,L=""))}function be(_){L=_,t(20,L)}function Je(_){ce.call(this,n,_)}function Ge(_){ce.call(this,n,_)}function Ye(_){ce.call(this,n,_)}function Ke(_){R=_,t(0,R)}const Qe=({detail:_})=>T("error",_),Xe=()=>t(20,L="edit");function Ze(_){L=_,t(20,L)}function $e(_){ce.call(this,n,_)}function xe(_){ce.call(this,n,_)}function et(_){ce.call(this,n,_)}function tt(_){ce.call(this,n,_)}function nt(_){v=_,t(2,v)}return n.$$set=_=>{"value"in _&&t(1,a=_.value),"label"in _&&t(3,s=_.label),"root"in _&&t(4,u=_.root),"loop"in _&&t(5,r=_.loop),"show_label"in _&&t(6,f=_.show_label),"show_download_button"in _&&t(7,l=_.show_download_button),"sources"in _&&t(8,c=_.sources),"pending"in _&&t(27,d=_.pending),"streaming"in _&&t(9,m=_.streaming),"i18n"in _&&t(10,p=_.i18n),"waveform_settings"in _&&t(11,g=_.waveform_settings),"trim_region_settings"in _&&t(12,w=_.trim_region_settings),"waveform_options"in _&&t(13,y=_.waveform_options),"dragging"in _&&t(0,R=_.dragging),"active_source"in _&&t(2,v=_.active_source),"handle_reset_value"in _&&t(14,z=_.handle_reset_value),"editable"in _&&t(15,q=_.editable),"max_file_size"in _&&t(16,M=_.max_file_size),"upload"in _&&t(17,W=_.upload),"stream_handler"in _&&t(18,C=_.stream_handler),"$$scope"in _&&t(45,o=_.$$scope)},n.$$.update=()=>{if(n.$$.dirty[0]&1&&T("drag",R),n.$$.dirty[0]&2013265920&&F&&d===!1&&(t(30,F=!1),P&&D)){let _=[P].concat(D);t(29,D=[]),G(_,"stream")}},[R,a,v,s,u,r,f,l,c,m,p,g,w,y,z,q,M,W,C,h,L,T,G,b,H,Y,Q,d,P,D,F,i,be,Je,Ge,Ye,Ke,Qe,Xe,Ze,$e,xe,et,tt,nt,o]}class Ii extends ci{constructor(e){super(),pi(this,e,Bi,Pi,wi,{value:1,label:3,root:4,loop:5,show_label:6,show_download_button:7,sources:8,pending:27,streaming:9,i18n:10,waveform_settings:11,trim_region_settings:12,waveform_options:13,dragging:0,active_source:2,handle_reset_value:14,editable:15,max_file_size:16,upload:17,stream_handler:18},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),A()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),A()}get root(){return this.$$.ctx[4]}set root(e){this.$$set({root:e}),A()}get loop(){return this.$$.ctx[5]}set loop(e){this.$$set({loop:e}),A()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),A()}get show_download_button(){return this.$$.ctx[7]}set show_download_button(e){this.$$set({show_download_button:e}),A()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),A()}get pending(){return this.$$.ctx[27]}set pending(e){this.$$set({pending:e}),A()}get streaming(){return this.$$.ctx[9]}set streaming(e){this.$$set({streaming:e}),A()}get i18n(){return this.$$.ctx[10]}set i18n(e){this.$$set({i18n:e}),A()}get waveform_settings(){return this.$$.ctx[11]}set waveform_settings(e){this.$$set({waveform_settings:e}),A()}get trim_region_settings(){return this.$$.ctx[12]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),A()}get waveform_options(){return this.$$.ctx[13]}set waveform_options(e){this.$$set({waveform_options:e}),A()}get dragging(){return this.$$.ctx[0]}set dragging(e){this.$$set({dragging:e}),A()}get active_source(){return this.$$.ctx[2]}set active_source(e){this.$$set({active_source:e}),A()}get handle_reset_value(){return this.$$.ctx[14]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),A()}get editable(){return this.$$.ctx[15]}set editable(e){this.$$set({editable:e}),A()}get max_file_size(){return this.$$.ctx[16]}set max_file_size(e){this.$$set({max_file_size:e}),A()}get upload(){return this.$$.ctx[17]}set upload(e){this.$$set({upload:e}),A()}get stream_handler(){return this.$$.ctx[18]}set stream_handler(e){this.$$set({stream_handler:e}),A()}}const zi=Ii,{SvelteComponent:Ti,add_flush_callback:Ui,assign:tn,bind:qi,binding_callbacks:Oi,check_outros:Wi,create_component:me,destroy_component:he,detach:ut,empty:Ni,flush:E,get_spread_object:nn,get_spread_update:on,group_outros:ji,init:Fi,insert:_t,mount_component:ge,safe_not_equal:Hi,space:sn,transition_in:ee,transition_out:te}=window.__gradio__svelte__internal,{afterUpdate:Vi,onMount:Ji}=window.__gradio__svelte__internal;function Gi(n){let e,t;return e=new Ot({props:{variant:n[0]===null&&n[21]==="upload"?"dashed":"solid",border_mode:n[22]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:n[2],elem_classes:n[3],visible:n[4],container:n[10],scale:n[11],min_width:n[12],$$slots:{default:[Qi]},$$scope:{ctx:n}}}),{c(){me(e.$$.fragment)},m(i,o){ge(e,i,o),t=!0},p(i,o){const a={};o[0]&2097153&&(a.variant=i[0]===null&&i[21]==="upload"?"dashed":"solid"),o[0]&4194304&&(a.border_mode=i[22]?"focus":"base"),o[0]&4&&(a.elem_id=i[2]),o[0]&8&&(a.elem_classes=i[3]),o[0]&16&&(a.visible=i[4]),o[0]&1024&&(a.container=i[10]),o[0]&2048&&(a.scale=i[11]),o[0]&4096&&(a.min_width=i[12]),o[0]&16737219|o[1]&4194304&&(a.$$scope={dirty:o,ctx:i}),e.$set(a)},i(i){t||(ee(e.$$.fragment,i),t=!0)},o(i){te(e.$$.fragment,i),t=!1},d(i){he(e,i)}}}function Yi(n){let e,t;return e=new Ot({props:{variant:"solid",border_mode:n[22]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:n[2],elem_classes:n[3],visible:n[4],container:n[10],scale:n[11],min_width:n[12],$$slots:{default:[Xi]},$$scope:{ctx:n}}}),{c(){me(e.$$.fragment)},m(i,o){ge(e,i,o),t=!0},p(i,o){const a={};o[0]&4194304&&(a.border_mode=i[22]?"focus":"base"),o[0]&4&&(a.elem_id=i[2]),o[0]&8&&(a.elem_classes=i[3]),o[0]&16&&(a.visible=i[4]),o[0]&1024&&(a.container=i[10]),o[0]&2048&&(a.scale=i[11]),o[0]&4096&&(a.min_width=i[12]),o[0]&9691779|o[1]&4194304&&(a.$$scope={dirty:o,ctx:i}),e.$set(a)},i(i){t||(ee(e.$$.fragment,i),t=!0)},o(i){te(e.$$.fragment,i),t=!1},d(i){he(e,i)}}}function Ki(n){let e,t;return e=new hn({props:{i18n:n[20].i18n,type:"audio"}}),{c(){me(e.$$.fragment)},m(i,o){ge(e,i,o),t=!0},p(i,o){const a={};o[0]&1048576&&(a.i18n=i[20].i18n),e.$set(a)},i(i){t||(ee(e.$$.fragment,i),t=!0)},o(i){te(e.$$.fragment,i),t=!1},d(i){he(e,i)}}}function Qi(n){let e,t,i,o,a;const s=[{autoscroll:n[20].autoscroll},{i18n:n[20].i18n},n[1]];let u={};for(let l=0;l<s.length;l+=1)u=tn(u,s[l]);e=new qt({props:u}),e.$on("clear_status",n[37]);function r(l){n[38](l)}let f={label:n[7],show_label:n[9],show_download_button:n[14],value:n[0],root:n[8],sources:n[6],active_source:n[21],pending:n[18],streaming:n[19],loop:n[13],max_file_size:n[20].max_file_size,handle_reset_value:n[24],editable:n[16],i18n:n[20].i18n,waveform_settings:n[23],waveform_options:n[17],trim_region_settings:n[25],upload:n[20].client.upload,stream_handler:n[20].client.stream,$$slots:{default:[Ki]},$$scope:{ctx:n}};return n[22]!==void 0&&(f.dragging=n[22]),i=new zi({props:f}),Oi.push(()=>qi(i,"dragging",r)),i.$on("change",n[39]),i.$on("stream",n[40]),i.$on("drag",n[41]),i.$on("edit",n[42]),i.$on("play",n[43]),i.$on("pause",n[44]),i.$on("stop",n[45]),i.$on("start_recording",n[46]),i.$on("pause_recording",n[47]),i.$on("stop_recording",n[48]),i.$on("upload",n[49]),i.$on("clear",n[50]),i.$on("error",n[26]),{c(){me(e.$$.fragment),t=sn(),me(i.$$.fragment)},m(l,c){ge(e,l,c),_t(l,t,c),ge(i,l,c),a=!0},p(l,c){const d=c[0]&1048578?on(s,[c[0]&1048576&&{autoscroll:l[20].autoscroll},c[0]&1048576&&{i18n:l[20].i18n},c[0]&2&&nn(l[1])]):{};e.$set(d);const m={};c[0]&128&&(m.label=l[7]),c[0]&512&&(m.show_label=l[9]),c[0]&16384&&(m.show_download_button=l[14]),c[0]&1&&(m.value=l[0]),c[0]&256&&(m.root=l[8]),c[0]&64&&(m.sources=l[6]),c[0]&2097152&&(m.active_source=l[21]),c[0]&262144&&(m.pending=l[18]),c[0]&524288&&(m.streaming=l[19]),c[0]&8192&&(m.loop=l[13]),c[0]&1048576&&(m.max_file_size=l[20].max_file_size),c[0]&65536&&(m.editable=l[16]),c[0]&1048576&&(m.i18n=l[20].i18n),c[0]&8388608&&(m.waveform_settings=l[23]),c[0]&131072&&(m.waveform_options=l[17]),c[0]&1048576&&(m.upload=l[20].client.upload),c[0]&1048576&&(m.stream_handler=l[20].client.stream),c[0]&1048576|c[1]&4194304&&(m.$$scope={dirty:c,ctx:l}),!o&&c[0]&4194304&&(o=!0,m.dragging=l[22],Ui(()=>o=!1)),i.$set(m)},i(l){a||(ee(e.$$.fragment,l),ee(i.$$.fragment,l),a=!0)},o(l){te(e.$$.fragment,l),te(i.$$.fragment,l),a=!1},d(l){l&&ut(t),he(e,l),he(i,l)}}}function Xi(n){let e,t,i,o;const a=[{autoscroll:n[20].autoscroll},{i18n:n[20].i18n},n[1]];let s={};for(let u=0;u<a.length;u+=1)s=tn(s,a[u]);return e=new qt({props:s}),e.$on("clear_status",n[31]),i=new an({props:{i18n:n[20].i18n,show_label:n[9],show_download_button:n[14],show_share_button:n[15],value:n[0],label:n[7],loop:n[13],waveform_settings:n[23],waveform_options:n[17],editable:n[16]}}),i.$on("share",n[32]),i.$on("error",n[33]),i.$on("play",n[34]),i.$on("pause",n[35]),i.$on("stop",n[36]),{c(){me(e.$$.fragment),t=sn(),me(i.$$.fragment)},m(u,r){ge(e,u,r),_t(u,t,r),ge(i,u,r),o=!0},p(u,r){const f=r[0]&1048578?on(a,[r[0]&1048576&&{autoscroll:u[20].autoscroll},r[0]&1048576&&{i18n:u[20].i18n},r[0]&2&&nn(u[1])]):{};e.$set(f);const l={};r[0]&1048576&&(l.i18n=u[20].i18n),r[0]&512&&(l.show_label=u[9]),r[0]&16384&&(l.show_download_button=u[14]),r[0]&32768&&(l.show_share_button=u[15]),r[0]&1&&(l.value=u[0]),r[0]&128&&(l.label=u[7]),r[0]&8192&&(l.loop=u[13]),r[0]&8388608&&(l.waveform_settings=u[23]),r[0]&131072&&(l.waveform_options=u[17]),r[0]&65536&&(l.editable=u[16]),i.$set(l)},i(u){o||(ee(e.$$.fragment,u),ee(i.$$.fragment,u),o=!0)},o(u){te(e.$$.fragment,u),te(i.$$.fragment,u),o=!1},d(u){u&&ut(t),he(e,u),he(i,u)}}}function Zi(n){let e,t,i,o;const a=[Yi,Gi],s=[];function u(r,f){return r[5]?1:0}return e=u(n),t=s[e]=a[e](n),{c(){t.c(),i=Ni()},m(r,f){s[e].m(r,f),_t(r,i,f),o=!0},p(r,f){let l=e;e=u(r),e===l?s[e].p(r,f):(ji(),te(s[l],1,1,()=>{s[l]=null}),Wi(),t=s[e],t?t.p(r,f):(t=s[e]=a[e](r),t.c()),ee(t,1),t.m(i.parentNode,i))},i(r){o||(ee(t),o=!0)},o(r){te(t),o=!1},d(r){r&&ut(i),s[e].d(r)}}}function $i(n,e,t){let{value_is_output:i=!1}=e,{elem_id:o=""}=e,{elem_classes:a=[]}=e,{visible:s=!0}=e,{interactive:u}=e,{value:r=null}=e,{sources:f}=e,{label:l}=e,{root:c}=e,{show_label:d}=e,{container:m=!0}=e,{scale:p=null}=e,{min_width:g=void 0}=e,{loading_status:w}=e,{autoplay:y=!1}=e,{loop:R=!1}=e,{show_download_button:v}=e,{show_share_button:z=!1}=e,{editable:q=!0}=e,{waveform_options:M={}}=e,{pending:W}=e,{streaming:C}=e,{gradio:h}=e,S=null,L,P=r;const D=()=>{P===null||r===P||t(0,r=P)};let F,B,k="darkorange";Ji(()=>{k=getComputedStyle(document?.documentElement).getPropertyValue("--color-accent"),K(),t(23,B.waveColor=M.waveform_color||"#9ca3af",B),t(23,B.progressColor=M.waveform_progress_color||k,B),t(23,B.mediaControls=M.show_controls,B),t(23,B.sampleRate=M.sample_rate||44100,B)});const I={color:M.trim_region_color,drag:!0,resize:!0};function K(){document.documentElement.style.setProperty("--trim-region-color",I.color||k)}function T({detail:_}){const[O,X]=_.includes("Invalid file type")?["warning","complete"]:["error","error"];t(1,w=w||{}),t(1,w.status=X,w),t(1,w.message=_,w),h.dispatch(O,_)}Vi(()=>{t(27,i=!1)});const G=()=>h.dispatch("clear_status",w),Re=_=>h.dispatch("share",_.detail),Se=_=>h.dispatch("error",_.detail),b=()=>h.dispatch("play"),H=()=>h.dispatch("pause"),Y=()=>h.dispatch("stop"),Q=()=>h.dispatch("clear_status",w);function be(_){F=_,t(22,F)}const Je=({detail:_})=>t(0,r=_),Ge=({detail:_})=>{t(0,r=_),h.dispatch("stream",r)},Ye=({detail:_})=>t(22,F=_),Ke=()=>h.dispatch("edit"),Qe=()=>h.dispatch("play"),Xe=()=>h.dispatch("pause"),Ze=()=>h.dispatch("stop"),$e=()=>h.dispatch("start_recording"),xe=()=>h.dispatch("pause_recording"),et=_=>h.dispatch("stop_recording"),tt=()=>h.dispatch("upload"),nt=()=>h.dispatch("clear");return n.$$set=_=>{"value_is_output"in _&&t(27,i=_.value_is_output),"elem_id"in _&&t(2,o=_.elem_id),"elem_classes"in _&&t(3,a=_.elem_classes),"visible"in _&&t(4,s=_.visible),"interactive"in _&&t(5,u=_.interactive),"value"in _&&t(0,r=_.value),"sources"in _&&t(6,f=_.sources),"label"in _&&t(7,l=_.label),"root"in _&&t(8,c=_.root),"show_label"in _&&t(9,d=_.show_label),"container"in _&&t(10,m=_.container),"scale"in _&&t(11,p=_.scale),"min_width"in _&&t(12,g=_.min_width),"loading_status"in _&&t(1,w=_.loading_status),"autoplay"in _&&t(28,y=_.autoplay),"loop"in _&&t(13,R=_.loop),"show_download_button"in _&&t(14,v=_.show_download_button),"show_share_button"in _&&t(15,z=_.show_share_button),"editable"in _&&t(16,q=_.editable),"waveform_options"in _&&t(17,M=_.waveform_options),"pending"in _&&t(18,W=_.pending),"streaming"in _&&t(19,C=_.streaming),"gradio"in _&&t(20,h=_.gradio)},n.$$.update=()=>{n.$$.dirty[0]&1073741825&&r&&P===null&&t(30,P=r),n.$$.dirty[0]&672137217&&JSON.stringify(r)!==JSON.stringify(S)&&(t(29,S=r),h.dispatch("change"),i||h.dispatch("input")),n.$$.dirty[0]&2097216&&!L&&f&&t(21,L=f[0]),n.$$.dirty[0]&268435456&&t(23,B={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:y,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20})},[r,w,o,a,s,u,f,l,c,d,m,p,g,R,v,z,q,M,W,C,h,L,F,B,D,I,T,i,y,S,P,G,Re,Se,b,H,Y,Q,be,Je,Ge,Ye,Ke,Qe,Xe,Ze,$e,xe,et,tt,nt]}class xi extends Ti{constructor(e){super(),Fi(this,e,$i,Zi,Hi,{value_is_output:27,elem_id:2,elem_classes:3,visible:4,interactive:5,value:0,sources:6,label:7,root:8,show_label:9,container:10,scale:11,min_width:12,loading_status:1,autoplay:28,loop:13,show_download_button:14,show_share_button:15,editable:16,waveform_options:17,pending:18,streaming:19,gradio:20},null,[-1,-1])}get value_is_output(){return this.$$.ctx[27]}set value_is_output(e){this.$$set({value_is_output:e}),E()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),E()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),E()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),E()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),E()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),E()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),E()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),E()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),E()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),E()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),E()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),E()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),E()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),E()}get autoplay(){return this.$$.ctx[28]}set autoplay(e){this.$$set({autoplay:e}),E()}get loop(){return this.$$.ctx[13]}set loop(e){this.$$set({loop:e}),E()}get show_download_button(){return this.$$.ctx[14]}set show_download_button(e){this.$$set({show_download_button:e}),E()}get show_share_button(){return this.$$.ctx[15]}set show_share_button(e){this.$$set({show_share_button:e}),E()}get editable(){return this.$$.ctx[16]}set editable(e){this.$$set({editable:e}),E()}get waveform_options(){return this.$$.ctx[17]}set waveform_options(e){this.$$set({waveform_options:e}),E()}get pending(){return this.$$.ctx[18]}set pending(e){this.$$set({pending:e}),E()}get streaming(){return this.$$.ctx[19]}set streaming(e){this.$$set({streaming:e}),E()}get gradio(){return this.$$.ctx[20]}set gradio(e){this.$$set({gradio:e}),E()}}const yo=xi;export{So as BaseExample,zi as BaseInteractiveAudio,ln as BasePlayer,an as BaseStaticAudio,yo as default};
//# sourceMappingURL=index-BTDJHSno.js.map
