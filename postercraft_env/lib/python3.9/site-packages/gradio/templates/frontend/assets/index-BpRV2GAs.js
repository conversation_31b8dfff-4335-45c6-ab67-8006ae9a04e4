const __vite__fileDeps=["./module-BTDC6jSQ.js","./module-C-VadMaF.js","./Index-WGC0_FkS.js","./index-COY1HN2y.js","./index-luc1OtuK.css","./Index-hBVU0Tzp.css","./module-BA06XY8_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{I as is,S as Ei}from"./Index-WGC0_FkS.js";import{f as de,u as ss}from"./Blocks-aR9ucLZz.js";import{B as Di}from"./BlockLabel-CJsotHlk.js";import{E as Ri}from"./Empty-Vuj7-ssy.js";import{S as os}from"./ShareButton-Ds9bG3Tz.js";import{D as rs,a as ls}from"./DownloadLink-DYBmO3sz.js";import{M as Ot}from"./Music-CDm0RGMk.js";import{a as as,P as Si,T as us}from"./Trim-UKwaW4UI.js";import{U as cs}from"./Undo-CpmTQw3B.js";import{r as zn}from"./file-url-Bf0nK4ai.js";import{_ as $n,p as ds}from"./index-COY1HN2y.js";import{U as fs}from"./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js";import{M as Li}from"./ModifyUpload-DZAlpNPL.js";import{S as hs}from"./SelectSource-ghC4bkgc.js";import{B as Mi}from"./Button-8nmImwVJ.js";import{U as _s}from"./UploadText-DlCTYTPP.js";import{default as Oa}from"./Example-BQyGztrG.js";import"./svelte/svelte.js";import"./Upload-Cp8Go_XF.js";const{SvelteComponent:ms,append:gs,attr:ae,detach:ps,init:vs,insert:bs,noop:rn,safe_not_equal:ws,svg_element:Wn}=window.__gradio__svelte__internal;function ks(i){let e,t;return{c(){e=Wn("svg"),t=Wn("path"),ae(t,"stroke","currentColor"),ae(t,"stroke-width","1.5"),ae(t,"stroke-linecap","round"),ae(t,"stroke-linejoin","round"),ae(t,"d","M21.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.889-5.813a.6.6 0 0 1 0-.966l7.89-5.813ZM10.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.888-5.813a.6.6 0 0 1 0-.966l7.888-5.813Z"),ae(e,"xmlns","http://www.w3.org/2000/svg"),ae(e,"width","24px"),ae(e,"height","24px"),ae(e,"fill","currentColor"),ae(e,"stroke-width","1.5"),ae(e,"viewBox","0 0 24 24"),ae(e,"color","currentColor")},m(n,s){bs(n,e,s),gs(e,t)},p:rn,i:rn,o:rn,d(n){n&&ps(e)}}}class ys extends ms{constructor(e){super(),vs(this,e,null,ks,ws,{})}}const{SvelteComponent:Cs,append:Es,attr:ue,detach:Ds,init:Rs,insert:Ss,noop:ln,safe_not_equal:Ls,svg_element:On}=window.__gradio__svelte__internal;function Ms(i){let e,t;return{c(){e=On("svg"),t=On("path"),ue(t,"stroke","currentColor"),ue(t,"stroke-width","1.5"),ue(t,"stroke-linecap","round"),ue(t,"stroke-linejoin","round"),ue(t,"d","M2.956 5.704A.6.6 0 0 0 2 6.187v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813ZM13.956 5.704a.6.6 0 0 0-.956.483v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813Z"),ue(e,"xmlns","http://www.w3.org/2000/svg"),ue(e,"width","24px"),ue(e,"height","24px"),ue(e,"fill","currentColor"),ue(e,"stroke-width","1.5"),ue(e,"viewBox","0 0 24 24"),ue(e,"color","currentColor")},m(n,s){Ss(n,e,s),Es(e,t)},p:ln,i:ln,o:ln,d(n){n&&Ds(e)}}}class Ps extends Cs{constructor(e){super(),Rs(this,e,null,Ms,Ls,{})}}const{SvelteComponent:As,append:Dt,attr:se,detach:Ts,init:zs,insert:$s,noop:an,safe_not_equal:Ws,svg_element:Rt,text:Os}=window.__gradio__svelte__internal;function Bs(i){let e,t,n,s,o;return{c(){e=Rt("svg"),t=Rt("title"),n=Os("Low volume"),s=Rt("path"),o=Rt("path"),se(s,"d","M19.5 7.5C19.5 7.5 21 9 21 11.5C21 14 19.5 15.5 19.5 15.5"),se(s,"stroke-width","1.5"),se(s,"stroke-linecap","round"),se(s,"stroke-linejoin","round"),se(o,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),se(o,"stroke-width","1.5"),se(e,"width","100%"),se(e,"height","100%"),se(e,"viewBox","0 0 24 24"),se(e,"stroke-width","1.5"),se(e,"fill","none"),se(e,"xmlns","http://www.w3.org/2000/svg"),se(e,"stroke","currentColor"),se(e,"color","currentColor")},m(r,u){$s(r,e,u),Dt(e,t),Dt(t,n),Dt(e,s),Dt(e,o)},p:an,i:an,o:an,d(r){r&&Ts(e)}}}class Hs extends As{constructor(e){super(),zs(this,e,null,Bs,Ws,{})}}const{SvelteComponent:Ns,append:ct,attr:F,detach:Vs,init:qs,insert:Is,noop:un,safe_not_equal:js,svg_element:dt,text:Us}=window.__gradio__svelte__internal;function Fs(i){let e,t,n,s,o,r;return{c(){e=dt("svg"),t=dt("title"),n=Us("High volume"),s=dt("path"),o=dt("path"),r=dt("path"),F(s,"d","M1 13.8571V10.1429C1 9.03829 1.89543 8.14286 3 8.14286H5.9C6.09569 8.14286 6.28708 8.08544 6.45046 7.97772L12.4495 4.02228C13.1144 3.5839 14 4.06075 14 4.85714V19.1429C14 19.9392 13.1144 20.4161 12.4495 19.9777L6.45046 16.0223C6.28708 15.9146 6.09569 15.8571 5.9 15.8571H3C1.89543 15.8571 1 14.9617 1 13.8571Z"),F(s,"stroke-width","1.5"),F(o,"d","M17.5 7.5C17.5 7.5 19 9 19 11.5C19 14 17.5 15.5 17.5 15.5"),F(o,"stroke-width","1.5"),F(o,"stroke-linecap","round"),F(o,"stroke-linejoin","round"),F(r,"d","M20.5 4.5C20.5 4.5 23 7 23 11.5C23 16 20.5 18.5 20.5 18.5"),F(r,"stroke-width","1.5"),F(r,"stroke-linecap","round"),F(r,"stroke-linejoin","round"),F(e,"width","100%"),F(e,"height","100%"),F(e,"viewBox","0 0 24 24"),F(e,"stroke-width","1.5"),F(e,"fill","none"),F(e,"stroke","currentColor"),F(e,"xmlns","http://www.w3.org/2000/svg"),F(e,"color","currentColor")},m(u,a){Is(u,e,a),ct(e,t),ct(t,n),ct(e,s),ct(e,o),ct(e,r)},p:un,i:un,o:un,d(u){u&&Vs(e)}}}class Xs extends Ns{constructor(e){super(),qs(this,e,null,Fs,js,{})}}const{SvelteComponent:Gs,append:Ee,attr:j,detach:Ys,init:Zs,insert:Js,noop:cn,safe_not_equal:Ks,svg_element:De,text:Qs}=window.__gradio__svelte__internal;function xs(i){let e,t,n,s,o,r,u,a,d;return{c(){e=De("svg"),t=De("title"),n=Qs("Muted volume"),s=De("g"),o=De("path"),r=De("path"),u=De("defs"),a=De("clipPath"),d=De("rect"),j(o,"d","M18 14L20.0005 12M22 10L20.0005 12M20.0005 12L18 10M20.0005 12L22 14"),j(o,"stroke-width","1.5"),j(o,"stroke-linecap","round"),j(o,"stroke-linejoin","round"),j(r,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),j(r,"stroke-width","1.5"),j(s,"clip-path","url(#clip0_3173_16686)"),j(d,"width","24"),j(d,"height","24"),j(d,"fill","white"),j(a,"id","clip0_3173_16686"),j(e,"width","100%"),j(e,"height","100%"),j(e,"viewBox","0 0 24 24"),j(e,"stroke-width","1.5"),j(e,"fill","none"),j(e,"xmlns","http://www.w3.org/2000/svg"),j(e,"stroke","currentColor"),j(e,"color","currentColor")},m(l,c){Js(l,e,c),Ee(e,t),Ee(t,n),Ee(e,s),Ee(s,o),Ee(s,r),Ee(e,u),Ee(u,a),Ee(a,d)},p:cn,i:cn,o:cn,d(l){l&&Ys(e)}}}class eo extends Gs{constructor(e){super(),Zs(this,e,null,xs,Ks,{})}}var to=function(i,e,t,n){function s(o){return o instanceof t?o:new t(function(r){r(o)})}return new(t||(t=Promise))(function(o,r){function u(l){try{d(n.next(l))}catch(c){r(c)}}function a(l){try{d(n.throw(l))}catch(c){r(c)}}function d(l){l.done?o(l.value):s(l.value).then(u,a)}d((n=n.apply(i,e||[])).next())})};function no(i,e){return to(this,void 0,void 0,function*(){const t=new AudioContext({sampleRate:e});return t.decodeAudioData(i).finally(()=>t.close())})}function io(i){const e=i[0];if(e.some(t=>t>1||t<-1)){const t=e.length;let n=0;for(let s=0;s<t;s++){const o=Math.abs(e[s]);o>n&&(n=o)}for(const s of i)for(let o=0;o<t;o++)s[o]/=n}return i}function so(i,e){return typeof i[0]=="number"&&(i=[i]),io(i),{duration:e,length:i[0].length,sampleRate:i[0].length/e,numberOfChannels:i.length,getChannelData:t=>i?.[t],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}const dn={decode:no,createBuffer:so};var Bn=function(i,e,t,n){function s(o){return o instanceof t?o:new t(function(r){r(o)})}return new(t||(t=Promise))(function(o,r){function u(l){try{d(n.next(l))}catch(c){r(c)}}function a(l){try{d(n.throw(l))}catch(c){r(c)}}function d(l){l.done?o(l.value):s(l.value).then(u,a)}d((n=n.apply(i,e||[])).next())})};function oo(i,e,t){var n,s;return Bn(this,void 0,void 0,function*(){const o=yield fetch(i,t);{const r=(n=o.clone().body)===null||n===void 0?void 0:n.getReader(),u=Number((s=o.headers)===null||s===void 0?void 0:s.get("Content-Length"));let a=0;const d=(l,c)=>Bn(this,void 0,void 0,function*(){if(l)return;a+=c?.length||0;const f=Math.round(a/u*100);return e(f),r?.read().then(({done:_,value:m})=>d(_,m))});r?.read().then(({done:l,value:c})=>d(l,c))}return o.blob()})}const ro={fetchBlob:oo};class Bt{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,n){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),n?.once){const s=()=>{this.removeEventListener(e,s),this.removeEventListener(e,t)};return this.addEventListener(e,s),s}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var n;(n=this.listeners[e])===null||n===void 0||n.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(n=>n(...t))}}class lo extends Bt{constructor(e){super(),this.isExternalMedia=!1,e.media?(this.media=e.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),e.mediaControls&&(this.media.controls=!0),e.autoplay&&(this.media.autoplay=!0),e.playbackRate!=null&&this.onceMediaEvent("canplay",()=>{e.playbackRate!=null&&(this.media.playbackRate=e.playbackRate)})}onMediaEvent(e,t,n){return this.media.addEventListener(e,t,n),()=>this.media.removeEventListener(e,t)}onceMediaEvent(e,t){return this.onMediaEvent(e,t,{once:!0})}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const e=this.getSrc();e.startsWith("blob:")&&URL.revokeObjectURL(e)}setSrc(e,t){if(this.getSrc()===e)return;this.revokeSrc();const s=t instanceof Blob?URL.createObjectURL(t):e;this.media.src=s,this.media.load()}destroy(){this.media.pause(),!this.isExternalMedia&&(this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(e){this.media=e}play(){return this.media.play()}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(e){this.media.currentTime=e}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(e){this.media.volume=e}getMuted(){return this.media.muted}setMuted(e){this.media.muted=e}getPlaybackRate(){return this.media.playbackRate}setPlaybackRate(e,t){t!=null&&(this.media.preservesPitch=t),this.media.playbackRate=e}getMediaElement(){return this.media}setSinkId(e){return this.media.setSinkId(e)}}function ao(i,e,t,n,s=5){let o=()=>{};if(!i)return o;const r=u=>{if(u.button===2)return;u.preventDefault(),u.stopPropagation(),i.style.touchAction="none";let a=u.clientX,d=u.clientY,l=!1;const c=m=>{m.preventDefault(),m.stopPropagation();const h=m.clientX,g=m.clientY;if(l||Math.abs(h-a)>=s||Math.abs(g-d)>=s){const{left:k,top:D}=i.getBoundingClientRect();l||(l=!0,t?.(a-k,d-D)),e(h-a,g-d,h-k,g-D),a=h,d=g}},f=m=>{l&&(m.preventDefault(),m.stopPropagation())},_=()=>{i.style.touchAction="",l&&n?.(),o()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",_),document.addEventListener("pointerleave",_),document.addEventListener("click",f,!0),o=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",_),document.removeEventListener("pointerleave",_),setTimeout(()=>{document.removeEventListener("click",f,!0)},10)}};return i.addEventListener("pointerdown",r),()=>{o(),i.removeEventListener("pointerdown",r)}}class Ht extends Bt{constructor(e,t){super(),this.timeouts=[],this.isScrolling=!1,this.audioData=null,this.resizeObserver=null,this.isDragging=!1,this.options=e;const n=this.parentFromOptionsContainer(e.container);this.parent=n;const[s,o]=this.initHtml();n.appendChild(s),this.container=s,this.scrollContainer=o.querySelector(".scroll"),this.wrapper=o.querySelector(".wrapper"),this.canvasWrapper=o.querySelector(".canvases"),this.progressWrapper=o.querySelector(".progress"),this.cursor=o.querySelector(".cursor"),t&&o.appendChild(t),this.initEvents()}parentFromOptionsContainer(e){let t;if(typeof e=="string"?t=document.querySelector(e):e instanceof HTMLElement&&(t=e),!t)throw new Error("Container not found");return t}initEvents(){const e=n=>{const s=this.wrapper.getBoundingClientRect(),o=n.clientX-s.left,r=n.clientX-s.left,u=o/s.width,a=r/s.height;return[u,a]};this.wrapper.addEventListener("click",n=>{const[s,o]=e(n);this.emit("click",s,o)}),this.wrapper.addEventListener("dblclick",n=>{const[s,o]=e(n);this.emit("dblclick",s,o)}),this.options.dragToSeek&&this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:n,scrollWidth:s,clientWidth:o}=this.scrollContainer,r=n/s,u=(n+o)/s;this.emit("scroll",r,u)});const t=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{t(()=>this.reRender())}),this.resizeObserver.observe(this.scrollContainer)}initDrag(){ao(this.wrapper,(e,t,n)=>{this.emit("drag",Math.max(0,Math.min(1,n/this.wrapper.getBoundingClientRect().width)))},()=>this.isDragging=!0,()=>this.isDragging=!1)}getHeight(){return this.options.height==null?128:isNaN(Number(this.options.height))?this.options.height==="auto"&&this.parent.clientHeight||128:Number(this.options.height)}initHtml(){const e=document.createElement("div"),t=e.attachShadow({mode:"open"});return t.innerHTML=`
      <style>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight()}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[e,t]}setOptions(e){if(this.options.container!==e.container){const t=this.parentFromOptionsContainer(e.container);t.appendChild(this.container),this.parent=t}e.dragToSeek&&!this.options.dragToSeek&&this.initDrag(),this.options=e,this.reRender()}getWrapper(){return this.wrapper}getScroll(){return this.scrollContainer.scrollLeft}destroy(){var e;this.container.remove(),(e=this.resizeObserver)===null||e===void 0||e.disconnect()}createDelay(e=10){const t={};return this.timeouts.push(t),n=>{t.timeout&&clearTimeout(t.timeout),t.timeout=setTimeout(n,e)}}convertColorValues(e){if(!Array.isArray(e))return e||"";if(e.length<2)return e[0]||"";const t=document.createElement("canvas"),s=t.getContext("2d").createLinearGradient(0,0,0,t.height),o=1/(e.length-1);return e.forEach((r,u)=>{const a=u*o;s.addColorStop(a,r)}),s}renderBarWaveform(e,t,n,s){const o=e[0],r=e[1]||e[0],u=o.length,{width:a,height:d}=n.canvas,l=d/2,c=window.devicePixelRatio||1,f=t.barWidth?t.barWidth*c:1,_=t.barGap?t.barGap*c:t.barWidth?f/2:0,m=t.barRadius||0,h=a/(f+_)/u,g=m&&"roundRect"in n?"roundRect":"rect";n.beginPath();let k=0,D=0,p=0;for(let L=0;L<=u;L++){const E=Math.round(L*h);if(E>k){const A=Math.round(D*l*s),C=Math.round(p*l*s),T=A+C||1;let N=l-A;t.barAlign==="top"?N=0:t.barAlign==="bottom"&&(N=d-T),n[g](k*(f+_),N,f,T,m),k=E,D=0,p=0}const P=Math.abs(o[L]||0),R=Math.abs(r[L]||0);P>D&&(D=P),R>p&&(p=R)}n.fill(),n.closePath()}renderLineWaveform(e,t,n,s){const o=r=>{const u=e[r]||e[0],a=u.length,{height:d}=n.canvas,l=d/2,c=n.canvas.width/a;n.moveTo(0,l);let f=0,_=0;for(let m=0;m<=a;m++){const h=Math.round(m*c);if(h>f){const k=Math.round(_*l*s)||1,D=l+k*(r===0?-1:1);n.lineTo(f,D),f=h,_=0}const g=Math.abs(u[m]||0);g>_&&(_=g)}n.lineTo(f,l)};n.beginPath(),o(0),o(1),n.fill(),n.closePath()}renderWaveform(e,t,n){if(n.fillStyle=this.convertColorValues(t.waveColor),t.renderFunction){t.renderFunction(e,n);return}let s=t.barHeight||1;if(t.normalize){const o=Array.from(e[0]).reduce((r,u)=>Math.max(r,Math.abs(u)),0);s=o?1/o:1}if(t.barWidth||t.barGap||t.barAlign){this.renderBarWaveform(e,t,n,s);return}this.renderLineWaveform(e,t,n,s)}renderSingleCanvas(e,t,n,s,o,r,u,a){const d=window.devicePixelRatio||1,l=document.createElement("canvas"),c=e[0].length;l.width=Math.round(n*(r-o)/c),l.height=s*d,l.style.width=`${Math.floor(l.width/d)}px`,l.style.height=`${s}px`,l.style.left=`${Math.floor(o*n/d/c)}px`,u.appendChild(l);const f=l.getContext("2d");if(this.renderWaveform(e.map(_=>_.slice(o,r)),t,f),l.width>0&&l.height>0){const _=l.cloneNode(),m=_.getContext("2d");m.drawImage(l,0,0),m.globalCompositeOperation="source-in",m.fillStyle=this.convertColorValues(t.progressColor),m.fillRect(0,0,l.width,l.height),a.appendChild(_)}}renderChannel(e,t,n){const s=document.createElement("div"),o=this.getHeight();s.style.height=`${o}px`,this.canvasWrapper.style.minHeight=`${o}px`,this.canvasWrapper.appendChild(s);const r=s.cloneNode();this.progressWrapper.appendChild(r);const{scrollLeft:u,scrollWidth:a,clientWidth:d}=this.scrollContainer,l=e[0].length,c=l/a;let f=Math.min(Ht.MAX_CANVAS_WIDTH,d);if(t.barWidth||t.barGap){const E=t.barWidth||.5,P=t.barGap||E/2,R=E+P;f%R!==0&&(f=Math.floor(f/R)*R)}const _=Math.floor(Math.abs(u)*c),m=Math.floor(_+f*c),h=m-_,g=(E,P)=>{this.renderSingleCanvas(e,t,n,o,Math.max(0,E),Math.min(P,l),s,r)},k=this.createDelay(),D=this.createDelay(),p=(E,P)=>{g(E,P),E>0&&k(()=>{p(E-h,P-h)})},L=(E,P)=>{g(E,P),P<l&&D(()=>{L(E+h,P+h)})};p(_,m),m<l&&L(m,m+h)}render(e){this.timeouts.forEach(u=>u.timeout&&clearTimeout(u.timeout)),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.wrapper.style.width="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const t=window.devicePixelRatio||1,n=this.scrollContainer.clientWidth,s=Math.ceil(e.duration*(this.options.minPxPerSec||0));this.isScrolling=s>n;const o=this.options.fillParent&&!this.isScrolling,r=(o?n:s)*t;if(this.wrapper.style.width=o?"100%":`${s}px`,this.scrollContainer.style.overflowX=this.isScrolling?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.options.splitChannels)for(let u=0;u<e.numberOfChannels;u++){const a=Object.assign(Object.assign({},this.options),this.options.splitChannels[u]);this.renderChannel([e.getChannelData(u)],a,r)}else{const u=[e.getChannelData(0)];e.numberOfChannels>1&&u.push(e.getChannelData(1)),this.renderChannel(u,this.options,r)}this.audioData=e,this.emit("render")}reRender(){if(!this.audioData)return;const e=this.progressWrapper.clientWidth;this.render(this.audioData);const t=this.progressWrapper.clientWidth;this.scrollContainer.scrollLeft+=t-e}zoom(e){this.options.minPxPerSec=e,this.reRender()}scrollIntoView(e,t=!1){const{clientWidth:n,scrollLeft:s,scrollWidth:o}=this.scrollContainer,r=o*e,u=n/2,a=t&&this.options.autoCenter&&!this.isDragging?u:n;if(r>s+a||r<s)if(this.options.autoCenter&&!this.isDragging){const d=u/20;r-(s+u)>=d&&r<s+n?this.scrollContainer.scrollLeft+=d:this.scrollContainer.scrollLeft=r-u}else this.isDragging?this.scrollContainer.scrollLeft=r<s?r-10:r-n+10:this.scrollContainer.scrollLeft=r;{const{scrollLeft:d}=this.scrollContainer,l=d/o,c=(d+n)/o;this.emit("scroll",l,c)}}renderProgress(e,t){if(isNaN(e))return;const n=e*100;this.canvasWrapper.style.clipPath=`polygon(${n}% 0, 100% 0, 100% 100%, ${n}% 100%)`,this.progressWrapper.style.width=`${n}%`,this.cursor.style.left=`${n}%`,this.cursor.style.marginLeft=Math.round(n)===100?`-${this.options.cursorWidth}px`:"",this.isScrolling&&this.options.autoScroll&&this.scrollIntoView(e,t)}}Ht.MAX_CANVAS_WIDTH=4e3;class uo extends Bt{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}var fn=function(i,e,t,n){function s(o){return o instanceof t?o:new t(function(r){r(o)})}return new(t||(t=Promise))(function(o,r){function u(l){try{d(n.next(l))}catch(c){r(c)}}function a(l){try{d(n.throw(l))}catch(c){r(c)}}function d(l){l.done?o(l.value):s(l.value).then(u,a)}d((n=n.apply(i,e||[])).next())})};class co extends Bt{constructor(e=new AudioContext){super(),this.bufferNode=null,this.autoplay=!1,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.audioContext=e,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return fn(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(e){this.currentSrc=e,fetch(e).then(t=>t.arrayBuffer()).then(t=>this.audioContext.decodeAudioData(t)).then(t=>{this.buffer=t,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play()})}_play(){var e;this.paused&&(this.paused=!1,(e=this.bufferNode)===null||e===void 0||e.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.bufferNode.buffer=this.buffer,this.bufferNode.connect(this.gainNode),this.playedDuration>=this.duration&&(this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,this.playedDuration),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))})}_pause(){var e;this.paused||(this.paused=!0,(e=this.bufferNode)===null||e===void 0||e.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime)}play(){return fn(this,void 0,void 0,function*(){this._play(),this.emit("play")})}pause(){this._pause(),this.emit("pause")}setSinkId(e){return fn(this,void 0,void 0,function*(){return this.audioContext.setSinkId(e)})}get playbackRate(){var e,t;return(t=(e=this.bufferNode)===null||e===void 0?void 0:e.playbackRate.value)!==null&&t!==void 0?t:1}set playbackRate(e){this.bufferNode&&(this.bufferNode.playbackRate.value=e)}get currentTime(){return this.paused?this.playedDuration:this.playedDuration+this.audioContext.currentTime-this.playStartTime}set currentTime(e){this.emit("seeking"),this.paused?this.playedDuration=e:(this._pause(),this.playedDuration=e,this._play()),this.emit("timeupdate")}get duration(){var e;return((e=this.buffer)===null||e===void 0?void 0:e.duration)||0}get volume(){return this.gainNode.gain.value}set volume(e){this.gainNode.gain.value=e,this.emit("volumechange")}get muted(){return this._muted}set muted(e){this._muted!==e&&(this._muted=e,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}getGainNode(){return this.gainNode}}var Qe=function(i,e,t,n){function s(o){return o instanceof t?o:new t(function(r){r(o)})}return new(t||(t=Promise))(function(o,r){function u(l){try{d(n.next(l))}catch(c){r(c)}}function a(l){try{d(n.throw(l))}catch(c){r(c)}}function d(l){l.done?o(l.value):s(l.value).then(u,a)}d((n=n.apply(i,e||[])).next())})};const fo={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class rt extends lo{static create(e){return new rt(e)}constructor(e){const t=e.media||(e.backend==="WebAudio"?new co:void 0);super({media:t,mediaControls:e.mediaControls,autoplay:e.autoplay,playbackRate:e.audioRate}),this.plugins=[],this.decodedData=null,this.subscriptions=[],this.mediaSubscriptions=[],this.options=Object.assign({},fo,e),this.timer=new uo;const n=t?void 0:this.getMediaElement();this.renderer=new Ht(this.options,n),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const s=this.options.url||this.getSrc();s?this.load(s,this.options.peaks,this.options.duration):this.options.peaks&&this.options.duration&&this.loadPredecoded()}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),!0),this.emit("timeupdate",e),this.emit("audioprocess",e)}))}initPlayerEvents(){this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),this.isPlaying()),this.emit("timeupdate",e)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop()}),this.onMediaEvent("emptied",()=>{this.timer.stop()}),this.onMediaEvent("ended",()=>{this.emit("finish")}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(e,t)=>{this.options.interact&&(this.seekTo(e),this.emit("interaction",e*this.getDuration()),this.emit("click",e,t))}),this.renderer.on("dblclick",(e,t)=>{this.emit("dblclick",e,t)}),this.renderer.on("scroll",(e,t)=>{const n=this.getDuration();this.emit("scroll",e*n,t*n)}),this.renderer.on("render",()=>{this.emit("redraw")}));{let e;this.subscriptions.push(this.renderer.on("drag",t=>{this.options.interact&&(this.renderer.renderProgress(t),clearTimeout(e),e=setTimeout(()=>{this.seekTo(t)},this.isPlaying()?0:200),this.emit("interaction",t*this.getDuration()),this.emit("drag",t))}))}}initPlugins(){var e;!((e=this.options.plugins)===null||e===void 0)&&e.length&&this.options.plugins.forEach(t=>{this.registerPlugin(t)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(e=>e()),this.mediaSubscriptions=[]}setOptions(e){this.options=Object.assign({},this.options,e),this.renderer.setOptions(this.options),e.audioRate&&this.setPlaybackRate(e.audioRate),e.mediaControls!=null&&(this.getMediaElement().controls=e.mediaControls)}registerPlugin(e){return e.init(this),this.plugins.push(e),this.subscriptions.push(e.once("destroy",()=>{this.plugins=this.plugins.filter(t=>t!==e)})),e}getWrapper(){return this.renderer.getWrapper()}getScroll(){return this.renderer.getScroll()}getActivePlugins(){return this.plugins}loadPredecoded(){return Qe(this,void 0,void 0,function*(){this.options.peaks&&this.options.duration&&(this.decodedData=dn.createBuffer(this.options.peaks,this.options.duration),yield Promise.resolve(),this.renderDecoded())})}renderDecoded(){return Qe(this,void 0,void 0,function*(){this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData))})}loadAudio(e,t,n,s){return Qe(this,void 0,void 0,function*(){if(this.emit("load",e),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,!t&&!n){const o=r=>this.emit("loading",r);t=yield ro.fetchBlob(e,o,this.options.fetchParams)}if(this.setSrc(e,t),s=(yield Promise.resolve(s||this.getDuration()))||(yield new Promise(o=>{this.onceMediaEvent("loadedmetadata",()=>o(this.getDuration()))}))||(yield Promise.resolve(0)),n)this.decodedData=dn.createBuffer(n,s);else if(t){const o=yield t.arrayBuffer();this.decodedData=yield dn.decode(o,this.options.sampleRate)}this.renderDecoded(),this.emit("ready",this.getDuration())})}load(e,t,n){return Qe(this,void 0,void 0,function*(){yield this.loadAudio(e,void 0,t,n)})}loadBlob(e,t,n){return Qe(this,void 0,void 0,function*(){yield this.loadAudio("blob",e,t,n)})}zoom(e){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(e),this.emit("zoom",e)}getDecodedData(){return this.decodedData}exportPeaks({channels:e=2,maxLength:t=8e3,precision:n=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const s=Math.min(e,this.decodedData.numberOfChannels),o=[];for(let r=0;r<s;r++){const u=this.decodedData.getChannelData(r),a=[],d=Math.round(u.length/t);for(let l=0;l<t;l++){const c=u.slice(l*d,(l+1)*d),f=Math.max(...c);a.push(Math.round(f*n)/n)}o.push(a)}return o}getDuration(){let e=super.getDuration()||0;return(e===0||e===1/0)&&this.decodedData&&(e=this.decodedData.duration),e}toggleInteraction(e){this.options.interact=e}seekTo(e){const t=this.getDuration()*e;this.setTime(t)}playPause(){return Qe(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(e){this.setTime(this.getCurrentTime()+e)}empty(){this.load("",[[0]],.001)}setMediaElement(e){this.unsubscribePlayerEvents(),super.setMediaElement(e),this.initPlayerEvents()}destroy(){this.emit("destroy"),this.plugins.forEach(e=>e.destroy()),this.subscriptions.forEach(e=>e()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}function ho(i){const e=i.numberOfChannels,t=i.length*e*2+44,n=new ArrayBuffer(t),s=new DataView(n);let o=0;const r=function(u,a,d){for(let l=0;l<d.length;l++)u.setUint8(a+l,d.charCodeAt(l))};r(s,o,"RIFF"),o+=4,s.setUint32(o,t-8,!0),o+=4,r(s,o,"WAVE"),o+=4,r(s,o,"fmt "),o+=4,s.setUint32(o,16,!0),o+=4,s.setUint16(o,1,!0),o+=2,s.setUint16(o,e,!0),o+=2,s.setUint32(o,i.sampleRate,!0),o+=4,s.setUint32(o,i.sampleRate*2*e,!0),o+=4,s.setUint16(o,e*2,!0),o+=2,s.setUint16(o,16,!0),o+=2,r(s,o,"data"),o+=4,s.setUint32(o,i.length*e*2,!0),o+=4;for(let u=0;u<i.length;u++)for(let a=0;a<e;a++){const d=Math.max(-1,Math.min(1,i.getChannelData(a)[u]));s.setInt16(o,d*32767,!0),o+=2}return new Uint8Array(n)}const pn=async(i,e,t,n)=>{const s=new AudioContext({sampleRate:n||i.sampleRate}),o=i.numberOfChannels,r=n||i.sampleRate;let u=i.length,a=0;e&&t&&(a=Math.round(e*r),u=Math.round(t*r)-a);const d=s.createBuffer(o,u,r);for(let l=0;l<o;l++){const c=i.getChannelData(l),f=d.getChannelData(l);for(let _=0;_<u;_++)f[_]=c[a+_]}return ho(d)},At=(i,e)=>{i&&i.skip(e)},nt=(i,e)=>(e||(e=5),i/100*e||5);let Pi=class{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,n){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),n?.once){const s=()=>{this.removeEventListener(e,s),this.removeEventListener(e,t)};return this.addEventListener(e,s),s}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var n;(n=this.listeners[e])===null||n===void 0||n.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(n=>n(...t))}},_o=class extends Pi{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}};function Lt(i,e,t,n,s=5){let o=()=>{};if(!i)return o;const r=u=>{if(u.button===2)return;u.preventDefault(),u.stopPropagation(),i.style.touchAction="none";let a=u.clientX,d=u.clientY,l=!1;const c=m=>{m.preventDefault(),m.stopPropagation();const h=m.clientX,g=m.clientY;if(l||Math.abs(h-a)>=s||Math.abs(g-d)>=s){const{left:k,top:D}=i.getBoundingClientRect();l||(l=!0,t?.(a-k,d-D)),e(h-a,g-d,h-k,g-D),a=h,d=g}},f=m=>{l&&(m.preventDefault(),m.stopPropagation())},_=()=>{i.style.touchAction="",l&&n?.(),o()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",_),document.addEventListener("pointerleave",_),document.addEventListener("click",f,!0),o=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",_),document.removeEventListener("pointerleave",_),setTimeout(()=>{document.removeEventListener("click",f,!0)},10)}};return i.addEventListener("pointerdown",r),()=>{o(),i.removeEventListener("pointerdown",r)}}class Hn extends Pi{constructor(e,t,n=0){var s,o,r,u,a,d,l;super(),this.totalDuration=t,this.numberOfChannels=n,this.minLength=0,this.maxLength=1/0,this.id=e.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(e.start),this.end=this.clampPosition((s=e.end)!==null&&s!==void 0?s:e.start),this.drag=(o=e.drag)===null||o===void 0||o,this.resize=(r=e.resize)===null||r===void 0||r,this.color=(u=e.color)!==null&&u!==void 0?u:"rgba(0, 0, 0, 0.1)",this.minLength=(a=e.minLength)!==null&&a!==void 0?a:this.minLength,this.maxLength=(d=e.maxLength)!==null&&d!==void 0?d:this.maxLength,this.channelIdx=(l=e.channelIdx)!==null&&l!==void 0?l:-1,this.element=this.initElement(),this.setContent(e.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(e){return Math.max(0,Math.min(this.totalDuration,e))}setPart(){const e=this.start===this.end;this.element.setAttribute("part",`${e?"marker":"region"} ${this.id}`)}addResizeHandles(e){const t=document.createElement("div");t.setAttribute("data-resize","left"),t.setAttribute("style",`
        position: absolute;
        z-index: 2;
        width: 6px;
        height: 100%;
        top: 0;
        left: 0;
        border-left: 2px solid rgba(0, 0, 0, 0.5);
        border-radius: 2px 0 0 2px;
        cursor: ew-resize;
        word-break: keep-all;
      `),t.setAttribute("part","region-handle region-handle-left");const n=t.cloneNode();n.setAttribute("data-resize","right"),n.style.left="",n.style.right="0",n.style.borderRight=n.style.borderLeft,n.style.borderLeft="",n.style.borderRadius="0 2px 2px 0",n.setAttribute("part","region-handle region-handle-right"),e.appendChild(t),e.appendChild(n),Lt(t,s=>this.onResize(s,"start"),()=>null,()=>this.onEndResizing(),1),Lt(n,s=>this.onResize(s,"end"),()=>null,()=>this.onEndResizing(),1)}removeResizeHandles(e){const t=e.querySelector('[data-resize="left"]'),n=e.querySelector('[data-resize="right"]');t&&e.removeChild(t),n&&e.removeChild(n)}initElement(){const e=document.createElement("div"),t=this.start===this.end;let n=0,s=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(s=100/this.numberOfChannels,n=s*this.channelIdx),e.setAttribute("style",`
      position: absolute;
      top: ${n}%;
      height: ${s}%;
      background-color: ${t?"none":this.color};
      border-left: ${t?"2px solid "+this.color:"none"};
      border-radius: 2px;
      box-sizing: border-box;
      transition: background-color 0.2s ease;
      cursor: ${this.drag?"grab":"default"};
      pointer-events: all;
    `),!t&&this.resize&&this.addResizeHandles(e),e}renderPosition(){const e=this.start/this.totalDuration,t=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*e+"%",this.element.style.right=100*t+"%"}initMouseEvents(){const{element:e}=this;e&&(e.addEventListener("click",t=>this.emit("click",t)),e.addEventListener("mouseenter",t=>this.emit("over",t)),e.addEventListener("mouseleave",t=>this.emit("leave",t)),e.addEventListener("dblclick",t=>this.emit("dblclick",t)),Lt(e,t=>this.onMove(t),()=>this.onStartMoving(),()=>this.onEndMoving()))}onStartMoving(){this.drag&&(this.element.style.cursor="grabbing")}onEndMoving(){this.drag&&(this.element.style.cursor="grab",this.emit("update-end"))}_onUpdate(e,t){if(!this.element.parentElement)return;const n=e/this.element.parentElement.clientWidth*this.totalDuration,s=t&&t!=="start"?this.start:this.start+n,o=t&&t!=="end"?this.end:this.end+n,r=o-s;s>=0&&o<=this.totalDuration&&s<=o&&r>=this.minLength&&r<=this.maxLength&&(this.start=s,this.end=o,this.renderPosition(),this.emit("update"))}onMove(e){this.drag&&this._onUpdate(e)}onResize(e,t){this.resize&&this._onUpdate(e,t)}onEndResizing(){this.resize&&this.emit("update-end")}_setTotalDuration(e){this.totalDuration=e,this.renderPosition()}play(){this.emit("play")}setContent(e){var t;if((t=this.content)===null||t===void 0||t.remove(),e){if(typeof e=="string"){this.content=document.createElement("div");const n=this.start===this.end;this.content.style.padding=`0.2em ${n?.2:.4}em`,this.content.textContent=e}else this.content=e;this.content.setAttribute("part","region-content"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(e){var t,n;if(e.color&&(this.color=e.color,this.element.style.backgroundColor=this.color),e.drag!==void 0&&(this.drag=e.drag,this.element.style.cursor=this.drag?"grab":"default"),e.start!==void 0||e.end!==void 0){const s=this.start===this.end;this.start=this.clampPosition((t=e.start)!==null&&t!==void 0?t:this.start),this.end=this.clampPosition((n=e.end)!==null&&n!==void 0?n:s?this.start:this.end),this.renderPosition(),this.setPart()}if(e.content&&this.setContent(e.content),e.id&&(this.id=e.id,this.setPart()),e.resize!==void 0&&e.resize!==this.resize){const s=this.start===this.end;this.resize=e.resize,this.resize&&!s?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit("remove"),this.element.remove(),this.element=null}}let mo=class Ai extends _o{constructor(e){super(e),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(e){return new Ai(e)}onInit(){if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let e=[];this.subscriptions.push(this.wavesurfer.on("timeupdate",t=>{const n=this.regions.filter(s=>s.start<=t&&s.end>=t);n.forEach(s=>{e.includes(s)||this.emit("region-in",s)}),e.forEach(s=>{n.includes(s)||this.emit("region-out",s)}),e=n}))}initRegionsContainer(){const e=document.createElement("div");return e.setAttribute("style",`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
      pointer-events: none;
    `),e}getRegions(){return this.regions}avoidOverlapping(e){if(!e.content)return;const t=e.content,n=t.getBoundingClientRect().left,s=e.element.scrollWidth,o=this.regions.filter(r=>{if(r===e||!r.content)return!1;const u=r.content.getBoundingClientRect().left,a=r.element.scrollWidth;return n<u+a&&u<n+s}).map(r=>{var u;return((u=r.content)===null||u===void 0?void 0:u.getBoundingClientRect().height)||0}).reduce((r,u)=>r+u,0);t.style.marginTop=`${o}px`}saveRegion(e){this.regionsContainer.appendChild(e.element),this.avoidOverlapping(e),this.regions.push(e);const t=[e.on("update-end",()=>{this.avoidOverlapping(e),this.emit("region-updated",e)}),e.on("play",()=>{var n,s;(n=this.wavesurfer)===null||n===void 0||n.play(),(s=this.wavesurfer)===null||s===void 0||s.setTime(e.start)}),e.on("click",n=>{this.emit("region-clicked",e,n)}),e.on("dblclick",n=>{this.emit("region-double-clicked",e,n)}),e.once("remove",()=>{t.forEach(n=>n()),this.regions=this.regions.filter(n=>n!==e)})];this.subscriptions.push(...t),this.emit("region-created",e)}addRegion(e){var t,n;if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");const s=this.wavesurfer.getDuration(),o=(n=(t=this.wavesurfer)===null||t===void 0?void 0:t.getDecodedData())===null||n===void 0?void 0:n.numberOfChannels,r=new Hn(e,s,o);return s?this.saveRegion(r):this.subscriptions.push(this.wavesurfer.once("ready",u=>{r._setTotalDuration(u),this.saveRegion(r)})),r}enableDragSelection(e){var t,n;const s=(n=(t=this.wavesurfer)===null||t===void 0?void 0:t.getWrapper())===null||n===void 0?void 0:n.querySelector("div");if(!s)return()=>{};let o=null,r=0;return Lt(s,(u,a,d)=>{o&&o._onUpdate(u,d>r?"end":"start")},u=>{var a,d;if(r=u,!this.wavesurfer)return;const l=this.wavesurfer.getDuration(),c=(d=(a=this.wavesurfer)===null||a===void 0?void 0:a.getDecodedData())===null||d===void 0?void 0:d.numberOfChannels,f=this.wavesurfer.getWrapper().clientWidth,_=u/f*l,m=(u+5)/f*l;o=new Hn(Object.assign(Object.assign({},e),{start:_,end:m}),l,c),this.regionsContainer.appendChild(o.element)},()=>{o&&(this.saveRegion(o),o=null)})}clearRegions(){this.regions.forEach(e=>e.remove())}destroy(){this.clearRegions(),super.destroy()}};const{SvelteComponent:go,check_outros:po,create_component:yn,destroy_component:Cn,detach:vo,empty:bo,flush:wo,group_outros:ko,init:yo,insert:Co,mount_component:En,safe_not_equal:Eo,transition_in:wt,transition_out:kt}=window.__gradio__svelte__internal;function Do(i){let e,t;return e=new Xs({}),{c(){yn(e.$$.fragment)},m(n,s){En(e,n,s),t=!0},i(n){t||(wt(e.$$.fragment,n),t=!0)},o(n){kt(e.$$.fragment,n),t=!1},d(n){Cn(e,n)}}}function Ro(i){let e,t;return e=new Hs({}),{c(){yn(e.$$.fragment)},m(n,s){En(e,n,s),t=!0},i(n){t||(wt(e.$$.fragment,n),t=!0)},o(n){kt(e.$$.fragment,n),t=!1},d(n){Cn(e,n)}}}function So(i){let e,t;return e=new eo({}),{c(){yn(e.$$.fragment)},m(n,s){En(e,n,s),t=!0},i(n){t||(wt(e.$$.fragment,n),t=!0)},o(n){kt(e.$$.fragment,n),t=!1},d(n){Cn(e,n)}}}function Lo(i){let e,t,n,s;const o=[So,Ro,Do],r=[];function u(a,d){return a[0]==0?0:a[0]<.5?1:a[0]>=.5?2:-1}return~(e=u(i))&&(t=r[e]=o[e](i)),{c(){t&&t.c(),n=bo()},m(a,d){~e&&r[e].m(a,d),Co(a,n,d),s=!0},p(a,[d]){let l=e;e=u(a),e!==l&&(t&&(ko(),kt(r[l],1,1,()=>{r[l]=null}),po()),~e?(t=r[e],t||(t=r[e]=o[e](a),t.c()),wt(t,1),t.m(n.parentNode,n)):t=null)},i(a){s||(wt(t),s=!0)},o(a){kt(t),s=!1},d(a){a&&vo(n),~e&&r[e].d(a)}}}function Mo(i,e,t){let{currentVolume:n}=e;return i.$$set=s=>{"currentVolume"in s&&t(0,n=s.currentVolume)},[n]}class Po extends go{constructor(e){super(),yo(this,e,Mo,Lo,Eo,{currentVolume:0})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),wo()}}const{SvelteComponent:Ao,attr:xe,binding_callbacks:To,detach:zo,element:$o,flush:hn,init:Wo,insert:Oo,listen:Nn,noop:Vn,run_all:Bo,safe_not_equal:Ho}=window.__gradio__svelte__internal,{onMount:No}=window.__gradio__svelte__internal;function Vo(i){let e,t,n;return{c(){e=$o("input"),xe(e,"id","volume"),xe(e,"class","volume-slider svelte-wuo8j5"),xe(e,"type","range"),xe(e,"min","0"),xe(e,"max","1"),xe(e,"step","0.01"),e.value=i[0]},m(s,o){Oo(s,e,o),i[4](e),t||(n=[Nn(e,"focusout",i[5]),Nn(e,"input",i[6])],t=!0)},p(s,[o]){o&1&&(e.value=s[0])},i:Vn,o:Vn,d(s){s&&zo(e),i[4](null),t=!1,Bo(n)}}}function qo(i,e,t){let{currentVolume:n=1}=e,{show_volume_slider:s=!1}=e,{waveform:o}=e,r;No(()=>{u()});const u=()=>{let c=r;c&&(c.style.background=`linear-gradient(to right, var(--color-accent) ${n*100}%, var(--neutral-400) ${n*100}%)`)};function a(c){To[c?"unshift":"push"](()=>{r=c,t(3,r)})}const d=()=>t(1,s=!1),l=c=>{c.target instanceof HTMLInputElement&&(t(0,n=parseFloat(c.target.value)),o.setVolume(n))};return i.$$set=c=>{"currentVolume"in c&&t(0,n=c.currentVolume),"show_volume_slider"in c&&t(1,s=c.show_volume_slider),"waveform"in c&&t(2,o=c.waveform)},i.$$.update=()=>{i.$$.dirty&1&&u()},[n,s,o,r,a,d,l]}class Io extends Ao{constructor(e){super(),Wo(this,e,qo,Vo,Ho,{currentVolume:0,show_volume_slider:1,waveform:2})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),hn()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),hn()}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),hn()}}const{SvelteComponent:jo,add_flush_callback:qn,append:Z,attr:V,bind:In,binding_callbacks:jn,check_outros:pt,create_component:Pe,destroy_component:Ae,detach:Te,element:oe,empty:Uo,flush:ee,group_outros:vt,init:Fo,insert:ze,listen:ve,mount_component:$e,noop:bt,run_all:Ti,safe_not_equal:Xo,set_data:Go,set_style:Un,space:Se,text:Fn,toggle_class:Xn,transition_in:I,transition_out:X}=window.__gradio__svelte__internal;function Gn(i){let e,t,n,s;function o(a){i[27](a)}function r(a){i[28](a)}let u={waveform:i[2]};return i[12]!==void 0&&(u.currentVolume=i[12]),i[1]!==void 0&&(u.show_volume_slider=i[1]),e=new Io({props:u}),jn.push(()=>In(e,"currentVolume",o)),jn.push(()=>In(e,"show_volume_slider",r)),{c(){Pe(e.$$.fragment)},m(a,d){$e(e,a,d),s=!0},p(a,d){const l={};d[0]&4&&(l.waveform=a[2]),!t&&d[0]&4096&&(t=!0,l.currentVolume=a[12],qn(()=>t=!1)),!n&&d[0]&2&&(n=!0,l.show_volume_slider=a[1],qn(()=>n=!1)),e.$set(l)},i(a){s||(I(e.$$.fragment,a),s=!0)},o(a){X(e.$$.fragment,a),s=!1},d(a){Ae(e,a)}}}function Yo(i){let e,t;return e=new as({}),{c(){Pe(e.$$.fragment)},m(n,s){$e(e,n,s),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){X(e.$$.fragment,n),t=!1},d(n){Ae(e,n)}}}function Zo(i){let e,t;return e=new Si({}),{c(){Pe(e.$$.fragment)},m(n,s){$e(e,n,s),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){X(e.$$.fragment,n),t=!1},d(n){Ae(e,n)}}}function Yn(i){let e,t,n,s,o,r=i[6]&&i[0]===""&&Zn(i);const u=[Ko,Jo],a=[];function d(l,c){return l[0]===""?0:1}return t=d(i),n=a[t]=u[t](i),{c(){r&&r.c(),e=Se(),n.c(),s=Uo()},m(l,c){r&&r.m(l,c),ze(l,e,c),a[t].m(l,c),ze(l,s,c),o=!0},p(l,c){l[6]&&l[0]===""?r?(r.p(l,c),c[0]&65&&I(r,1)):(r=Zn(l),r.c(),I(r,1),r.m(e.parentNode,e)):r&&(vt(),X(r,1,1,()=>{r=null}),pt());let f=t;t=d(l),t===f?a[t].p(l,c):(vt(),X(a[f],1,1,()=>{a[f]=null}),pt(),n=a[t],n?n.p(l,c):(n=a[t]=u[t](l),n.c()),I(n,1),n.m(s.parentNode,s))},i(l){o||(I(r),I(n),o=!0)},o(l){X(r),X(n),o=!1},d(l){l&&(Te(e),Te(s)),r&&r.d(l),a[t].d(l)}}}function Zn(i){let e,t,n,s,o;return t=new cs({}),{c(){e=oe("button"),Pe(t.$$.fragment),V(e,"class","action icon svelte-ije4bl"),V(e,"aria-label","Reset audio")},m(r,u){ze(r,e,u),$e(t,e,null),n=!0,s||(o=ve(e,"click",i[33]),s=!0)},p:bt,i(r){n||(I(t.$$.fragment,r),n=!0)},o(r){X(t.$$.fragment,r),n=!1},d(r){r&&Te(e),Ae(t),s=!1,o()}}}function Jo(i){let e,t,n,s,o;return{c(){e=oe("button"),e.textContent="Trim",t=Se(),n=oe("button"),n.textContent="Cancel",V(e,"class","text-button svelte-ije4bl"),V(n,"class","text-button svelte-ije4bl")},m(r,u){ze(r,e,u),ze(r,t,u),ze(r,n,u),s||(o=[ve(e,"click",i[14]),ve(n,"click",i[16])],s=!0)},p:bt,i:bt,o:bt,d(r){r&&(Te(e),Te(t),Te(n)),s=!1,Ti(o)}}}function Ko(i){let e,t,n,s,o;return t=new us({}),{c(){e=oe("button"),Pe(t.$$.fragment),V(e,"class","action icon svelte-ije4bl"),V(e,"aria-label","Trim audio to selection")},m(r,u){ze(r,e,u),$e(t,e,null),n=!0,s||(o=ve(e,"click",i[16]),s=!0)},p:bt,i(r){n||(I(t.$$.fragment,r),n=!0)},o(r){X(t.$$.fragment,r),n=!1},d(r){r&&Te(e),Ae(t),s=!1,o()}}}function Qo(i){let e,t,n,s,o,r,u,a,d,l,c,f,_,m,h,g,k,D,p,L,E,P,R,A,C,T,N,W,B,G;s=new Po({props:{currentVolume:i[12]}});let $=i[1]&&Gn(i);h=new ys({});const z=[Zo,Yo],b=[];function H(M,w){return M[5]?0:1}p=H(i),L=b[p]=z[p](i),A=new Ps({});let O=i[10]&&i[7]&&Yn(i);return{c(){e=oe("div"),t=oe("div"),n=oe("button"),Pe(s.$$.fragment),o=Se(),$&&$.c(),r=Se(),u=oe("button"),a=oe("span"),d=Fn(i[11]),l=Fn("x"),f=Se(),_=oe("div"),m=oe("button"),Pe(h.$$.fragment),k=Se(),D=oe("button"),L.c(),P=Se(),R=oe("button"),Pe(A.$$.fragment),T=Se(),N=oe("div"),O&&O.c(),V(n,"class","action icon volume svelte-ije4bl"),V(n,"aria-label","Adjust volume"),Un(n,"color",i[1]?"var(--color-accent)":"var(--neutral-400)"),V(u,"class","playback icon svelte-ije4bl"),V(u,"aria-label",c=`Adjust playback speed to ${i[13][(i[13].indexOf(i[11])+1)%i[13].length]}x`),Xn(u,"hidden",i[1]),V(t,"class","control-wrapper svelte-ije4bl"),V(m,"class","rewind icon svelte-ije4bl"),V(m,"aria-label",g=`Skip backwards by ${nt(i[3],i[9].skip_length)} seconds`),V(D,"class","play-pause-button icon svelte-ije4bl"),V(D,"aria-label",E=i[5]?i[4]("audio.pause"):i[4]("audio.play")),V(R,"class","skip icon svelte-ije4bl"),V(R,"aria-label",C="Skip forward by "+nt(i[3],i[9].skip_length)+" seconds"),V(_,"class","play-pause-wrapper svelte-ije4bl"),V(N,"class","settings-wrapper svelte-ije4bl"),V(e,"class","controls svelte-ije4bl"),V(e,"data-testid","waveform-controls")},m(M,w){ze(M,e,w),Z(e,t),Z(t,n),$e(s,n,null),Z(t,o),$&&$.m(t,null),Z(t,r),Z(t,u),Z(u,a),Z(a,d),Z(a,l),Z(e,f),Z(e,_),Z(_,m),$e(h,m,null),Z(_,k),Z(_,D),b[p].m(D,null),Z(_,P),Z(_,R),$e(A,R,null),Z(e,T),Z(e,N),O&&O.m(N,null),W=!0,B||(G=[ve(n,"click",i[26]),ve(u,"click",i[29]),ve(m,"click",i[30]),ve(D,"click",i[31]),ve(R,"click",i[32])],B=!0)},p(M,w){const Y={};w[0]&4096&&(Y.currentVolume=M[12]),s.$set(Y),w[0]&2&&Un(n,"color",M[1]?"var(--color-accent)":"var(--neutral-400)"),M[1]?$?($.p(M,w),w[0]&2&&I($,1)):($=Gn(M),$.c(),I($,1),$.m(t,r)):$&&(vt(),X($,1,1,()=>{$=null}),pt()),(!W||w[0]&2048)&&Go(d,M[11]),(!W||w[0]&2048&&c!==(c=`Adjust playback speed to ${M[13][(M[13].indexOf(M[11])+1)%M[13].length]}x`))&&V(u,"aria-label",c),(!W||w[0]&2)&&Xn(u,"hidden",M[1]),(!W||w[0]&520&&g!==(g=`Skip backwards by ${nt(M[3],M[9].skip_length)} seconds`))&&V(m,"aria-label",g);let y=p;p=H(M),p!==y&&(vt(),X(b[y],1,1,()=>{b[y]=null}),pt(),L=b[p],L||(L=b[p]=z[p](M),L.c()),I(L,1),L.m(D,null)),(!W||w[0]&48&&E!==(E=M[5]?M[4]("audio.pause"):M[4]("audio.play")))&&V(D,"aria-label",E),(!W||w[0]&520&&C!==(C="Skip forward by "+nt(M[3],M[9].skip_length)+" seconds"))&&V(R,"aria-label",C),M[10]&&M[7]?O?(O.p(M,w),w[0]&1152&&I(O,1)):(O=Yn(M),O.c(),I(O,1),O.m(N,null)):O&&(vt(),X(O,1,1,()=>{O=null}),pt())},i(M){W||(I(s.$$.fragment,M),I($),I(h.$$.fragment,M),I(L),I(A.$$.fragment,M),I(O),W=!0)},o(M){X(s.$$.fragment,M),X($),X(h.$$.fragment,M),X(L),X(A.$$.fragment,M),X(O),W=!1},d(M){M&&Te(e),Ae(s),$&&$.d(),Ae(h),b[p].d(),Ae(A),O&&O.d(),B=!1,Ti(G)}}}function xo(i,e,t){let{waveform:n}=e,{audio_duration:s}=e,{i18n:o}=e,{playing:r}=e,{show_redo:u=!1}=e,{interactive:a=!1}=e,{handle_trim_audio:d}=e,{mode:l=""}=e,{container:c}=e,{handle_reset_value:f}=e,{waveform_options:_={}}=e,{trim_region_settings:m={}}=e,{show_volume_slider:h=!1}=e,{editable:g=!0}=e,{trimDuration:k=0}=e,D=[.5,1,1.5,2],p=D[1],L,E=null,P,R,A="",C=1;const T=()=>{t(22,E=L.addRegion({start:s/4,end:s/2,...m})),t(17,k=E.end-E.start)},N=()=>{if(n&&L&&E){const y=E.start,K=E.end;d(y,K),t(0,l=""),t(22,E=null)}},W=()=>{L?.getRegions().forEach(y=>{y.remove()}),L?.clearRegions()},B=()=>{W(),l==="edit"?t(0,l=""):(t(0,l="edit"),T())},G=(y,K)=>{let le,me;E&&(y==="left"?K==="ArrowLeft"?(le=E.start-.05,me=E.end):(le=E.start+.05,me=E.end):K==="ArrowLeft"?(le=E.start,me=E.end-.05):(le=E.start,me=E.end+.05),E.setOptions({start:le,end:me}),t(17,k=E.end-E.start))},$=()=>t(1,h=!h);function z(y){C=y,t(12,C)}function b(y){h=y,t(1,h)}const H=()=>{t(11,p=D[(D.indexOf(p)+1)%D.length]),n.setPlaybackRate(p)},O=()=>n.skip(nt(s,_.skip_length)*-1),M=()=>n.playPause(),w=()=>n.skip(nt(s,_.skip_length)),Y=()=>{f(),W(),t(0,l="")};return i.$$set=y=>{"waveform"in y&&t(2,n=y.waveform),"audio_duration"in y&&t(3,s=y.audio_duration),"i18n"in y&&t(4,o=y.i18n),"playing"in y&&t(5,r=y.playing),"show_redo"in y&&t(6,u=y.show_redo),"interactive"in y&&t(7,a=y.interactive),"handle_trim_audio"in y&&t(18,d=y.handle_trim_audio),"mode"in y&&t(0,l=y.mode),"container"in y&&t(19,c=y.container),"handle_reset_value"in y&&t(8,f=y.handle_reset_value),"waveform_options"in y&&t(9,_=y.waveform_options),"trim_region_settings"in y&&t(20,m=y.trim_region_settings),"show_volume_slider"in y&&t(1,h=y.show_volume_slider),"editable"in y&&t(10,g=y.editable),"trimDuration"in y&&t(17,k=y.trimDuration)},i.$$.update=()=>{if(i.$$.dirty[0]&4&&t(21,L=n.registerPlugin(mo.create())),i.$$.dirty[0]&2097152&&L?.on("region-out",y=>{y.play()}),i.$$.dirty[0]&2097152&&L?.on("region-updated",y=>{t(17,k=y.end-y.start)}),i.$$.dirty[0]&2097152&&L?.on("region-clicked",(y,K)=>{K.stopPropagation(),t(22,E=y),y.play()}),i.$$.dirty[0]&31981568&&E){const y=c.children[0].shadowRoot;t(24,R=y.querySelector('[data-resize="right"]')),t(23,P=y.querySelector('[data-resize="left"]')),P&&R&&(P.setAttribute("role","button"),R.setAttribute("role","button"),P?.setAttribute("aria-label","Drag to adjust start time"),R?.setAttribute("aria-label","Drag to adjust end time"),P?.setAttribute("tabindex","0"),R?.setAttribute("tabindex","0"),P.addEventListener("focus",()=>{L&&t(25,A="left")}),R.addEventListener("focus",()=>{L&&t(25,A="right")}))}i.$$.dirty[0]&35651584&&L&&window.addEventListener("keydown",y=>{y.key==="ArrowLeft"?G(A,"ArrowLeft"):y.key==="ArrowRight"&&G(A,"ArrowRight")})},[l,h,n,s,o,r,u,a,f,_,g,p,C,D,N,W,B,k,d,c,m,L,E,P,R,A,$,z,b,H,O,M,w,Y]}class zi extends jo{constructor(e){super(),Fo(this,e,xo,Qo,Xo,{waveform:2,audio_duration:3,i18n:4,playing:5,show_redo:6,interactive:7,handle_trim_audio:18,mode:0,container:19,handle_reset_value:8,waveform_options:9,trim_region_settings:20,show_volume_slider:1,editable:10,trimDuration:17},null,[-1,-1])}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),ee()}get audio_duration(){return this.$$.ctx[3]}set audio_duration(e){this.$$set({audio_duration:e}),ee()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),ee()}get playing(){return this.$$.ctx[5]}set playing(e){this.$$set({playing:e}),ee()}get show_redo(){return this.$$.ctx[6]}set show_redo(e){this.$$set({show_redo:e}),ee()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),ee()}get handle_trim_audio(){return this.$$.ctx[18]}set handle_trim_audio(e){this.$$set({handle_trim_audio:e}),ee()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),ee()}get container(){return this.$$.ctx[19]}set container(e){this.$$set({container:e}),ee()}get handle_reset_value(){return this.$$.ctx[8]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),ee()}get waveform_options(){return this.$$.ctx[9]}set waveform_options(e){this.$$set({waveform_options:e}),ee()}get trim_region_settings(){return this.$$.ctx[20]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),ee()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),ee()}get editable(){return this.$$.ctx[10]}set editable(e){this.$$set({editable:e}),ee()}get trimDuration(){return this.$$.ctx[17]}set trimDuration(e){this.$$set({trimDuration:e}),ee()}}const{SvelteComponent:er,add_flush_callback:_n,append:he,attr:x,bind:mn,binding_callbacks:ot,check_outros:$i,create_component:Dn,destroy_component:Rn,detach:Nt,element:pe,empty:tr,flush:fe,group_outros:Wi,init:nr,insert:Vt,mount_component:Sn,noop:Jn,safe_not_equal:ir,set_data:sr,space:St,src_url_equal:Kn,text:or,transition_in:We,transition_out:Ue}=window.__gradio__svelte__internal,{onMount:rr}=window.__gradio__svelte__internal,{createEventDispatcher:lr}=window.__gradio__svelte__internal;function ar(i){let e,t,n,s,o,r,u,a,d,l,c,f,_,m=i[0]==="edit"&&i[16]>0&&Qn(i),h=i[11]&&xn(i);return{c(){e=pe("div"),t=pe("div"),n=pe("div"),s=St(),o=pe("div"),r=pe("time"),r.textContent="0:00",u=St(),a=pe("div"),m&&m.c(),d=St(),l=pe("time"),l.textContent="0:00",c=St(),h&&h.c(),x(n,"id","waveform"),x(n,"class","svelte-1ark3ru"),x(t,"class","waveform-container svelte-1ark3ru"),x(r,"id","time"),x(r,"class","svelte-1ark3ru"),x(l,"id","duration"),x(l,"class","svelte-1ark3ru"),x(o,"class","timestamps svelte-1ark3ru"),x(e,"class","component-wrapper svelte-1ark3ru"),x(e,"data-testid",f=i[2]?"waveform-"+i[2]:"unlabelled-audio")},m(g,k){Vt(g,e,k),he(e,t),he(t,n),i[21](n),he(e,s),he(e,o),he(o,r),i[22](r),he(o,u),he(o,a),m&&m.m(a,null),he(a,d),he(a,l),i[23](l),he(e,c),h&&h.m(e,null),_=!0},p(g,k){g[0]==="edit"&&g[16]>0?m?m.p(g,k):(m=Qn(g),m.c(),m.m(a,d)):m&&(m.d(1),m=null),g[11]?h?(h.p(g,k),k&2048&&We(h,1)):(h=xn(g),h.c(),We(h,1),h.m(e,null)):h&&(Wi(),Ue(h,1,1,()=>{h=null}),$i()),(!_||k&4&&f!==(f=g[2]?"waveform-"+g[2]:"unlabelled-audio"))&&x(e,"data-testid",f)},i(g){_||(We(h),_=!0)},o(g){Ue(h),_=!1},d(g){g&&Nt(e),i[21](null),i[22](null),m&&m.d(),i[23](null),h&&h.d()}}}function ur(i){let e,t,n;return{c(){e=pe("audio"),x(e,"class","standard-player svelte-1ark3ru"),Kn(e.src,t=i[1].url)||x(e,"src",t),e.controls=!0,e.autoplay=n=i[7].autoplay},m(s,o){Vt(s,e,o)},p(s,o){o&2&&!Kn(e.src,t=s[1].url)&&x(e,"src",t),o&128&&n!==(n=s[7].autoplay)&&(e.autoplay=n)},i:Jn,o:Jn,d(s){s&&Nt(e)}}}function cr(i){let e,t;return e=new Ri({props:{size:"small",$$slots:{default:[dr]},$$scope:{ctx:i}}}),{c(){Dn(e.$$.fragment)},m(n,s){Sn(e,n,s),t=!0},p(n,s){const o={};s&1073741824&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(We(e.$$.fragment,n),t=!0)},o(n){Ue(e.$$.fragment,n),t=!1},d(n){Rn(e,n)}}}function Qn(i){let e,t=de(i[16])+"",n;return{c(){e=pe("time"),n=or(t),x(e,"id","trim-duration"),x(e,"class","svelte-1ark3ru")},m(s,o){Vt(s,e,o),he(e,n)},p(s,o){o&65536&&t!==(t=de(s[16])+"")&&sr(n,t)},d(s){s&&Nt(e)}}}function xn(i){let e,t,n,s,o;function r(l){i[24](l)}function u(l){i[25](l)}function a(l){i[26](l)}let d={container:i[10],waveform:i[11],playing:i[14],audio_duration:i[15],i18n:i[3],interactive:i[4],handle_trim_audio:i[18],show_redo:i[4],handle_reset_value:i[9],waveform_options:i[8],trim_region_settings:i[6],editable:i[5]};return i[0]!==void 0&&(d.mode=i[0]),i[16]!==void 0&&(d.trimDuration=i[16]),i[17]!==void 0&&(d.show_volume_slider=i[17]),e=new zi({props:d}),ot.push(()=>mn(e,"mode",r)),ot.push(()=>mn(e,"trimDuration",u)),ot.push(()=>mn(e,"show_volume_slider",a)),{c(){Dn(e.$$.fragment)},m(l,c){Sn(e,l,c),o=!0},p(l,c){const f={};c&1024&&(f.container=l[10]),c&2048&&(f.waveform=l[11]),c&16384&&(f.playing=l[14]),c&32768&&(f.audio_duration=l[15]),c&8&&(f.i18n=l[3]),c&16&&(f.interactive=l[4]),c&16&&(f.show_redo=l[4]),c&512&&(f.handle_reset_value=l[9]),c&256&&(f.waveform_options=l[8]),c&64&&(f.trim_region_settings=l[6]),c&32&&(f.editable=l[5]),!t&&c&1&&(t=!0,f.mode=l[0],_n(()=>t=!1)),!n&&c&65536&&(n=!0,f.trimDuration=l[16],_n(()=>n=!1)),!s&&c&131072&&(s=!0,f.show_volume_slider=l[17],_n(()=>s=!1)),e.$set(f)},i(l){o||(We(e.$$.fragment,l),o=!0)},o(l){Ue(e.$$.fragment,l),o=!1},d(l){Rn(e,l)}}}function dr(i){let e,t;return e=new Ot({}),{c(){Dn(e.$$.fragment)},m(n,s){Sn(e,n,s),t=!0},i(n){t||(We(e.$$.fragment,n),t=!0)},o(n){Ue(e.$$.fragment,n),t=!1},d(n){Rn(e,n)}}}function fr(i){let e,t,n,s;const o=[cr,ur,ar],r=[];function u(a,d){return a[1]===null?0:a[1].is_stream?1:2}return e=u(i),t=r[e]=o[e](i),{c(){t.c(),n=tr()},m(a,d){r[e].m(a,d),Vt(a,n,d),s=!0},p(a,[d]){let l=e;e=u(a),e===l?r[e].p(a,d):(Wi(),Ue(r[l],1,1,()=>{r[l]=null}),$i(),t=r[e],t?t.p(a,d):(t=r[e]=o[e](a),t.c()),We(t,1),t.m(n.parentNode,n))},i(a){s||(We(t),s=!0)},o(a){Ue(t),s=!1},d(a){a&&Nt(n),r[e].d(a)}}}function hr(i,e,t){let n,{value:s=null}=e,{label:o}=e,{i18n:r}=e,{dispatch_blob:u=()=>Promise.resolve()}=e,{interactive:a=!1}=e,{editable:d=!0}=e,{trim_region_settings:l={}}=e,{waveform_settings:c}=e,{waveform_options:f}=e,{mode:_=""}=e,{handle_reset_value:m=()=>{}}=e,h,g,k=!1,D,p,L,E=0,P=!1;const R=lr(),A=()=>{t(11,g=rt.create({container:h,...c})),zn(s?.url).then(b=>{if(b&&g)return g.load(b)})},C=async(b,H)=>{t(0,_="");const O=g?.getDecodedData();O&&await pn(O,b,H,c.sampleRate).then(async M=>{await u([M],"change"),g?.destroy(),t(10,h.innerHTML="",h)}),R("edit")};async function T(b){await zn(b).then(H=>{if(!(!H||s?.is_stream))return g?.load(H)})}rr(()=>{window.addEventListener("keydown",b=>{!g||P||(b.key==="ArrowRight"&&_!=="edit"?At(g,.1):b.key==="ArrowLeft"&&_!=="edit"&&At(g,-.1))})});function N(b){ot[b?"unshift":"push"](()=>{h=b,t(10,h),t(11,g)})}function W(b){ot[b?"unshift":"push"](()=>{D=b,t(12,D),t(11,g)})}function B(b){ot[b?"unshift":"push"](()=>{p=b,t(13,p),t(11,g)})}function G(b){_=b,t(0,_)}function $(b){E=b,t(16,E)}function z(b){P=b,t(17,P)}return i.$$set=b=>{"value"in b&&t(1,s=b.value),"label"in b&&t(2,o=b.label),"i18n"in b&&t(3,r=b.i18n),"dispatch_blob"in b&&t(19,u=b.dispatch_blob),"interactive"in b&&t(4,a=b.interactive),"editable"in b&&t(5,d=b.editable),"trim_region_settings"in b&&t(6,l=b.trim_region_settings),"waveform_settings"in b&&t(7,c=b.waveform_settings),"waveform_options"in b&&t(8,f=b.waveform_options),"mode"in b&&t(0,_=b.mode),"handle_reset_value"in b&&t(9,m=b.handle_reset_value)},i.$$.update=()=>{i.$$.dirty&2&&t(20,n=s?.url),i.$$.dirty&3072&&h!==void 0&&(g!==void 0&&g.destroy(),t(10,h.innerHTML="",h),A(),t(14,k=!1)),i.$$.dirty&10240&&g?.on("decode",b=>{t(15,L=b),p&&t(13,p.textContent=de(b),p)}),i.$$.dirty&6144&&g?.on("timeupdate",b=>D&&t(12,D.textContent=de(b),D)),i.$$.dirty&2176&&g?.on("ready",()=>{c.autoplay?g?.play():g?.stop()}),i.$$.dirty&2048&&g?.on("finish",()=>{t(14,k=!1),R("stop")}),i.$$.dirty&2048&&g?.on("pause",()=>{t(14,k=!1),R("pause")}),i.$$.dirty&2048&&g?.on("play",()=>{t(14,k=!0),R("play")}),i.$$.dirty&1048576&&n&&T(n)},[_,s,o,r,a,d,l,c,f,m,h,g,D,p,k,L,E,P,C,u,n,N,W,B,G,$,z]}class _r extends er{constructor(e){super(),nr(this,e,hr,fr,ir,{value:1,label:2,i18n:3,dispatch_blob:19,interactive:4,editable:5,trim_region_settings:6,waveform_settings:7,waveform_options:8,mode:0,handle_reset_value:9})}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),fe()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),fe()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),fe()}get dispatch_blob(){return this.$$.ctx[19]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),fe()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),fe()}get editable(){return this.$$.ctx[5]}set editable(e){this.$$set({editable:e}),fe()}get trim_region_settings(){return this.$$.ctx[6]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),fe()}get waveform_settings(){return this.$$.ctx[7]}set waveform_settings(e){this.$$set({waveform_settings:e}),fe()}get waveform_options(){return this.$$.ctx[8]}set waveform_options(e){this.$$set({waveform_options:e}),fe()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),fe()}get handle_reset_value(){return this.$$.ctx[9]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),fe()}}const Oi=_r,{SvelteComponent:mr,append:gr,attr:pr,bubble:ft,check_outros:vn,create_component:Ze,destroy_component:Je,detach:Tt,element:vr,empty:br,flush:ge,group_outros:bn,init:wr,insert:zt,mount_component:Ke,safe_not_equal:kr,space:wn,transition_in:te,transition_out:re}=window.__gradio__svelte__internal,{createEventDispatcher:yr}=window.__gradio__svelte__internal;function Cr(i){let e,t;return e=new Ri({props:{size:"small",$$slots:{default:[Dr]},$$scope:{ctx:i}}}),{c(){Ze(e.$$.fragment)},m(n,s){Ke(e,n,s),t=!0},p(n,s){const o={};s&65536&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(te(e.$$.fragment,n),t=!0)},o(n){re(e.$$.fragment,n),t=!1},d(n){Je(e,n)}}}function Er(i){let e,t,n,s,o,r=i[3]&&ei(i),u=i[4]&&ti(i);return s=new Oi({props:{value:i[0],label:i[1],i18n:i[5],waveform_settings:i[6],waveform_options:i[7],editable:i[8]}}),s.$on("pause",i[12]),s.$on("play",i[13]),s.$on("stop",i[14]),{c(){e=vr("div"),r&&r.c(),t=wn(),u&&u.c(),n=wn(),Ze(s.$$.fragment),pr(e,"class","icon-buttons svelte-rvdo70")},m(a,d){zt(a,e,d),r&&r.m(e,null),gr(e,t),u&&u.m(e,null),zt(a,n,d),Ke(s,a,d),o=!0},p(a,d){a[3]?r?(r.p(a,d),d&8&&te(r,1)):(r=ei(a),r.c(),te(r,1),r.m(e,t)):r&&(bn(),re(r,1,1,()=>{r=null}),vn()),a[4]?u?(u.p(a,d),d&16&&te(u,1)):(u=ti(a),u.c(),te(u,1),u.m(e,null)):u&&(bn(),re(u,1,1,()=>{u=null}),vn());const l={};d&1&&(l.value=a[0]),d&2&&(l.label=a[1]),d&32&&(l.i18n=a[5]),d&64&&(l.waveform_settings=a[6]),d&128&&(l.waveform_options=a[7]),d&256&&(l.editable=a[8]),s.$set(l)},i(a){o||(te(r),te(u),te(s.$$.fragment,a),o=!0)},o(a){re(r),re(u),re(s.$$.fragment,a),o=!1},d(a){a&&(Tt(e),Tt(n)),r&&r.d(),u&&u.d(),Je(s,a)}}}function Dr(i){let e,t;return e=new Ot({}),{c(){Ze(e.$$.fragment)},m(n,s){Ke(e,n,s),t=!0},i(n){t||(te(e.$$.fragment,n),t=!0)},o(n){re(e.$$.fragment,n),t=!1},d(n){Je(e,n)}}}function ei(i){let e,t;return e=new rs({props:{href:i[0].url,download:i[0].orig_name||i[0].path,$$slots:{default:[Rr]},$$scope:{ctx:i}}}),{c(){Ze(e.$$.fragment)},m(n,s){Ke(e,n,s),t=!0},p(n,s){const o={};s&1&&(o.href=n[0].url),s&1&&(o.download=n[0].orig_name||n[0].path),s&65568&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(te(e.$$.fragment,n),t=!0)},o(n){re(e.$$.fragment,n),t=!1},d(n){Je(e,n)}}}function Rr(i){let e,t;return e=new is({props:{Icon:ls,label:i[5]("common.download")}}),{c(){Ze(e.$$.fragment)},m(n,s){Ke(e,n,s),t=!0},p(n,s){const o={};s&32&&(o.label=n[5]("common.download")),e.$set(o)},i(n){t||(te(e.$$.fragment,n),t=!0)},o(n){re(e.$$.fragment,n),t=!1},d(n){Je(e,n)}}}function ti(i){let e,t;return e=new os({props:{i18n:i[5],formatter:i[9],value:i[0]}}),e.$on("error",i[10]),e.$on("share",i[11]),{c(){Ze(e.$$.fragment)},m(n,s){Ke(e,n,s),t=!0},p(n,s){const o={};s&32&&(o.i18n=n[5]),s&1&&(o.value=n[0]),e.$set(o)},i(n){t||(te(e.$$.fragment,n),t=!0)},o(n){re(e.$$.fragment,n),t=!1},d(n){Je(e,n)}}}function Sr(i){let e,t,n,s,o,r;e=new Di({props:{show_label:i[2],Icon:Ot,float:!1,label:i[1]||i[5]("audio.audio")}});const u=[Er,Cr],a=[];function d(l,c){return l[0]!==null?0:1}return n=d(i),s=a[n]=u[n](i),{c(){Ze(e.$$.fragment),t=wn(),s.c(),o=br()},m(l,c){Ke(e,l,c),zt(l,t,c),a[n].m(l,c),zt(l,o,c),r=!0},p(l,[c]){const f={};c&4&&(f.show_label=l[2]),c&34&&(f.label=l[1]||l[5]("audio.audio")),e.$set(f);let _=n;n=d(l),n===_?a[n].p(l,c):(bn(),re(a[_],1,1,()=>{a[_]=null}),vn(),s=a[n],s?s.p(l,c):(s=a[n]=u[n](l),s.c()),te(s,1),s.m(o.parentNode,o))},i(l){r||(te(e.$$.fragment,l),te(s),r=!0)},o(l){re(e.$$.fragment,l),re(s),r=!1},d(l){l&&(Tt(t),Tt(o)),Je(e,l),a[n].d(l)}}}function Lr(i,e,t){let{value:n=null}=e,{label:s}=e,{show_label:o=!0}=e,{show_download_button:r=!0}=e,{show_share_button:u=!1}=e,{i18n:a}=e,{waveform_settings:d}=e,{waveform_options:l}=e,{editable:c=!0}=e;const f=yr(),_=async p=>p?`<audio controls src="${await ss(p.url,"url")}"></audio>`:"";function m(p){ft.call(this,i,p)}function h(p){ft.call(this,i,p)}function g(p){ft.call(this,i,p)}function k(p){ft.call(this,i,p)}function D(p){ft.call(this,i,p)}return i.$$set=p=>{"value"in p&&t(0,n=p.value),"label"in p&&t(1,s=p.label),"show_label"in p&&t(2,o=p.show_label),"show_download_button"in p&&t(3,r=p.show_download_button),"show_share_button"in p&&t(4,u=p.show_share_button),"i18n"in p&&t(5,a=p.i18n),"waveform_settings"in p&&t(6,d=p.waveform_settings),"waveform_options"in p&&t(7,l=p.waveform_options),"editable"in p&&t(8,c=p.editable)},i.$$.update=()=>{i.$$.dirty&1&&n&&f("change",n)},[n,s,o,r,u,a,d,l,c,_,m,h,g,k,D]}class Mr extends mr{constructor(e){super(),wr(this,e,Lr,Sr,kr,{value:0,label:1,show_label:2,show_download_button:3,show_share_button:4,i18n:5,waveform_settings:6,waveform_options:7,editable:8})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),ge()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),ge()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),ge()}get show_download_button(){return this.$$.ctx[3]}set show_download_button(e){this.$$set({show_download_button:e}),ge()}get show_share_button(){return this.$$.ctx[4]}set show_share_button(e){this.$$set({show_share_button:e}),ge()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),ge()}get waveform_settings(){return this.$$.ctx[6]}set waveform_settings(e){this.$$set({waveform_settings:e}),ge()}get waveform_options(){return this.$$.ctx[7]}set waveform_options(e){this.$$set({waveform_options:e}),ge()}get editable(){return this.$$.ctx[8]}set editable(e){this.$$set({editable:e}),ge()}}const Pr=Mr;function gn(i,e,t,n){return new(t||(t=Promise))(function(s,o){function r(d){try{a(n.next(d))}catch(l){o(l)}}function u(d){try{a(n.throw(d))}catch(l){o(l)}}function a(d){var l;d.done?s(d.value):(l=d.value,l instanceof t?l:new t(function(c){c(l)})).then(r,u)}a((n=n.apply(i,[])).next())})}class Ar{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,n){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),n?.once){const s=()=>{this.removeEventListener(e,s),this.removeEventListener(e,t)};return this.addEventListener(e,s),s}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var n;(n=this.listeners[e])===null||n===void 0||n.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(n=>n(...t))}}class Tr extends Ar{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}const zr=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class yt extends Tr{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:(t=e.audioBitsPerSecond)!==null&&t!==void 0?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new yt(e||{})}renderMicStream(e){const t=new AudioContext,n=t.createMediaStreamSource(e),s=t.createAnalyser();n.connect(s);const o=s.frequencyBinCount,r=new Float32Array(o),u=o/t.sampleRate;let a;const d=()=>{s.getFloatTimeDomainData(r),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load("",[r],u)),a=requestAnimationFrame(d)};return d(),()=>{cancelAnimationFrame(a),n?.disconnect(),t?.close()}}startMic(e){return gn(this,void 0,void 0,function*(){let t;try{t=yield navigator.mediaDevices.getUserMedia({audio:!e?.deviceId||{deviceId:e.deviceId}})}catch(s){throw new Error("Error accessing the microphone: "+s.message)}const n=this.renderMicStream(t);return this.subscriptions.push(this.once("destroy",n)),this.stream=t,t})}stopMic(){this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(e){return gn(this,void 0,void 0,function*(){const t=this.stream||(yield this.startMic(e)),n=this.mediaRecorder||new MediaRecorder(t,{mimeType:this.options.mimeType||zr.find(o=>MediaRecorder.isTypeSupported(o)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=n,this.stopRecording();const s=[];n.ondataavailable=o=>{o.data.size>0&&s.push(o.data)},n.onstop=()=>{var o;const r=new Blob(s,{type:n.mimeType});this.emit("record-end",r),this.options.renderRecordedAudio!==!1&&((o=this.wavesurfer)===null||o===void 0||o.load(URL.createObjectURL(r)))},n.start(),this.emit("record-start")})}isRecording(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="recording"}isPaused(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="paused"}stopRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.stop())}pauseRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.pause(),this.emit("record-pause"))}resumeRecording(){var e;this.isPaused()&&((e=this.mediaRecorder)===null||e===void 0||e.resume(),this.emit("record-resume"))}static getAvailableAudioDevices(){return gn(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(e=>e.filter(t=>t.kind==="audioinput"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}const{SvelteComponent:$r,append:Bi,attr:ni,destroy_each:Wr,detach:qt,element:Ln,empty:Or,ensure_array_like:ii,flush:si,init:Br,insert:It,noop:oi,safe_not_equal:Hr,set_data:Hi,set_input_value:kn,text:Ni}=window.__gradio__svelte__internal,{createEventDispatcher:Nr}=window.__gradio__svelte__internal;function ri(i,e,t){const n=i.slice();return n[3]=e[t],n}function Vr(i){let e,t=ii(i[0]),n=[];for(let s=0;s<t.length;s+=1)n[s]=li(ri(i,t,s));return{c(){for(let s=0;s<n.length;s+=1)n[s].c();e=Or()},m(s,o){for(let r=0;r<n.length;r+=1)n[r]&&n[r].m(s,o);It(s,e,o)},p(s,o){if(o&1){t=ii(s[0]);let r;for(r=0;r<t.length;r+=1){const u=ri(s,t,r);n[r]?n[r].p(u,o):(n[r]=li(u),n[r].c(),n[r].m(e.parentNode,e))}for(;r<n.length;r+=1)n[r].d(1);n.length=t.length}},d(s){s&&qt(e),Wr(n,s)}}}function qr(i){let e,t=i[1]("audio.no_microphone")+"",n;return{c(){e=Ln("option"),n=Ni(t),e.__value="",kn(e,e.__value)},m(s,o){It(s,e,o),Bi(e,n)},p(s,o){o&2&&t!==(t=s[1]("audio.no_microphone")+"")&&Hi(n,t)},d(s){s&&qt(e)}}}function li(i){let e,t=i[3].label+"",n,s;return{c(){e=Ln("option"),n=Ni(t),e.__value=s=i[3].deviceId,kn(e,e.__value)},m(o,r){It(o,e,r),Bi(e,n)},p(o,r){r&1&&t!==(t=o[3].label+"")&&Hi(n,t),r&1&&s!==(s=o[3].deviceId)&&(e.__value=s,kn(e,e.__value))},d(o){o&&qt(e)}}}function Ir(i){let e,t;function n(r,u){return r[0].length===0?qr:Vr}let s=n(i),o=s(i);return{c(){e=Ln("select"),o.c(),ni(e,"class","mic-select svelte-1v4948z"),ni(e,"aria-label","Select input device"),e.disabled=t=i[0].length===0},m(r,u){It(r,e,u),o.m(e,null)},p(r,[u]){s===(s=n(r))&&o?o.p(r,u):(o.d(1),o=s(r),o&&(o.c(),o.m(e,null))),u&1&&t!==(t=r[0].length===0)&&(e.disabled=t)},i:oi,o:oi,d(r){r&&qt(e),o.d()}}}function jr(i,e,t){let{i18n:n}=e,{micDevices:s=[]}=e;const o=Nr();return i.$$set=r=>{"i18n"in r&&t(1,n=r.i18n),"micDevices"in r&&t(0,s=r.micDevices)},i.$$.update=()=>{if(i.$$.dirty&2)try{let r=[];yt.getAvailableAudioDevices().then(u=>{t(0,s=u),u.forEach(a=>{a.deviceId&&r.push(a)}),t(0,s=r)})}catch(r){throw r instanceof DOMException&&r.name=="NotAllowedError"&&o("error",n("audio.allow_recording_access")),r}},[s,n]}class Vi extends $r{constructor(e){super(),Br(this,e,jr,Ir,Hr,{i18n:1,micDevices:0})}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),si()}get micDevices(){return this.$$.ctx[0]}set micDevices(e){this.$$set({micDevices:e}),si()}}const{SvelteComponent:Ur,add_flush_callback:Fr,append:J,attr:_e,bind:Xr,binding_callbacks:tt,create_component:ai,destroy_component:ui,detach:qi,element:Re,flush:ht,init:Gr,insert:Ii,listen:_t,mount_component:ci,run_all:Yr,safe_not_equal:Zr,set_data:mt,space:et,text:gt,transition_in:di,transition_out:fi}=window.__gradio__svelte__internal;function hi(i){let e,t;return{c(){e=Re("time"),t=gt(i[2]),_e(e,"class","duration-button duration svelte-1d9m1oy")},m(n,s){Ii(n,e,s),J(e,t)},p(n,s){s&4&&mt(t,n[2])},d(n){n&&qi(e)}}}function Jr(i){let e,t,n,s=i[1]("audio.record")+"",o,r,u,a=i[1]("audio.stop")+"",d,l,c,f,_=i[1]("audio.stop")+"",m,h,g,k,D,p,L=i[1]("audio.resume")+"",E,P,R,A,C,T,N,W;k=new Si({});let B=i[4]&&!i[3]&&hi(i);function G(z){i[21](z)}let $={i18n:i[1]};return i[5]!==void 0&&($.micDevices=i[5]),A=new Vi({props:$}),tt.push(()=>Xr(A,"micDevices",G)),{c(){e=Re("div"),t=Re("div"),n=Re("button"),o=gt(s),r=et(),u=Re("button"),d=gt(a),c=et(),f=Re("button"),m=gt(_),h=et(),g=Re("button"),ai(k.$$.fragment),D=et(),p=Re("button"),E=gt(L),P=et(),B&&B.c(),R=et(),ai(A.$$.fragment),_e(n,"class","record record-button svelte-1d9m1oy"),_e(u,"class",l="stop-button "+(i[0].isPaused()?"stop-button-paused":"")+" svelte-1d9m1oy"),_e(f,"id","stop-paused"),_e(f,"class","stop-button-paused svelte-1d9m1oy"),_e(g,"aria-label","pause"),_e(g,"class","pause-button svelte-1d9m1oy"),_e(p,"class","resume-button svelte-1d9m1oy"),_e(t,"class","wrapper svelte-1d9m1oy"),_e(e,"class","controls svelte-1d9m1oy")},m(z,b){Ii(z,e,b),J(e,t),J(t,n),J(n,o),i[11](n),J(t,r),J(t,u),J(u,d),i[13](u),J(t,c),J(t,f),J(f,m),i[15](f),J(t,h),J(t,g),ci(k,g,null),i[17](g),J(t,D),J(t,p),J(p,E),i[19](p),J(t,P),B&&B.m(t,null),J(e,R),ci(A,e,null),T=!0,N||(W=[_t(n,"click",i[12]),_t(u,"click",i[14]),_t(f,"click",i[16]),_t(g,"click",i[18]),_t(p,"click",i[20])],N=!0)},p(z,[b]){(!T||b&2)&&s!==(s=z[1]("audio.record")+"")&&mt(o,s),(!T||b&2)&&a!==(a=z[1]("audio.stop")+"")&&mt(d,a),(!T||b&1&&l!==(l="stop-button "+(z[0].isPaused()?"stop-button-paused":"")+" svelte-1d9m1oy"))&&_e(u,"class",l),(!T||b&2)&&_!==(_=z[1]("audio.stop")+"")&&mt(m,_),(!T||b&2)&&L!==(L=z[1]("audio.resume")+"")&&mt(E,L),z[4]&&!z[3]?B?B.p(z,b):(B=hi(z),B.c(),B.m(t,null)):B&&(B.d(1),B=null);const H={};b&2&&(H.i18n=z[1]),!C&&b&32&&(C=!0,H.micDevices=z[5],Fr(()=>C=!1)),A.$set(H)},i(z){T||(di(k.$$.fragment,z),di(A.$$.fragment,z),T=!0)},o(z){fi(k.$$.fragment,z),fi(A.$$.fragment,z),T=!1},d(z){z&&qi(e),i[11](null),i[13](null),i[15](null),ui(k),i[17](null),i[19](null),B&&B.d(),ui(A),N=!1,Yr(W)}}}function Kr(i,e,t){let{record:n}=e,{i18n:s}=e,o=[],r,u,a,d,l,{record_time:c}=e,{show_recording_waveform:f}=e,{timing:_=!1}=e;function m(C){tt[C?"unshift":"push"](()=>{r=C,t(6,r),t(0,n)})}const h=()=>n.startRecording();function g(C){tt[C?"unshift":"push"](()=>{d=C,t(9,d),t(0,n)})}const k=()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopRecording()};function D(C){tt[C?"unshift":"push"](()=>{l=C,t(10,l),t(0,n)})}const p=()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopRecording()};function L(C){tt[C?"unshift":"push"](()=>{u=C,t(7,u),t(0,n)})}const E=()=>n.pauseRecording();function P(C){tt[C?"unshift":"push"](()=>{a=C,t(8,a),t(0,n)})}const R=()=>n.resumeRecording();function A(C){o=C,t(5,o)}return i.$$set=C=>{"record"in C&&t(0,n=C.record),"i18n"in C&&t(1,s=C.i18n),"record_time"in C&&t(2,c=C.record_time),"show_recording_waveform"in C&&t(3,f=C.show_recording_waveform),"timing"in C&&t(4,_=C.timing)},i.$$.update=()=>{i.$$.dirty&1&&n.on("record-start",()=>{n.startMic(),t(6,r.style.display="none",r),t(9,d.style.display="flex",d),t(7,u.style.display="block",u)}),i.$$.dirty&1&&n.on("record-end",()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopMic(),t(6,r.style.display="flex",r),t(9,d.style.display="none",d),t(7,u.style.display="none",u),t(6,r.disabled=!1,r)}),i.$$.dirty&1&&n.on("record-pause",()=>{t(7,u.style.display="none",u),t(8,a.style.display="block",a),t(9,d.style.display="none",d),t(10,l.style.display="flex",l)}),i.$$.dirty&1&&n.on("record-resume",()=>{t(7,u.style.display="block",u),t(8,a.style.display="none",a),t(6,r.style.display="none",r),t(9,d.style.display="flex",d),t(10,l.style.display="none",l)})},[n,s,c,f,_,o,r,u,a,d,l,m,h,g,k,D,p,L,E,P,R,A]}class Qr extends Ur{constructor(e){super(),Gr(this,e,Kr,Jr,Zr,{record:0,i18n:1,record_time:2,show_recording_waveform:3,timing:4})}get record(){return this.$$.ctx[0]}set record(e){this.$$set({record:e}),ht()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),ht()}get record_time(){return this.$$.ctx[2]}set record_time(e){this.$$set({record_time:e}),ht()}get show_recording_waveform(){return this.$$.ctx[3]}set show_recording_waveform(e){this.$$set({show_recording_waveform:e}),ht()}get timing(){return this.$$.ctx[4]}set timing(e){this.$$set({timing:e}),ht()}}const{SvelteComponent:xr,add_flush_callback:Mt,append:ce,attr:be,bind:Pt,binding_callbacks:Me,check_outros:_i,create_component:ji,destroy_component:Ui,detach:Ct,element:we,flush:Ve,group_outros:mi,init:el,insert:Et,mount_component:Fi,noop:tl,safe_not_equal:nl,set_data:Xi,space:it,text:Gi,transition_in:Le,transition_out:st}=window.__gradio__svelte__internal,{onMount:il}=window.__gradio__svelte__internal,{createEventDispatcher:sl}=window.__gradio__svelte__internal;function gi(i){let e,t,n,s,o,r=i[0]==="edit"&&i[16]>0&&pi(i);function u(l,c){return l[15]?rl:ol}let a=u(i),d=a(i);return{c(){e=we("div"),t=we("time"),t.textContent="0:00",n=it(),s=we("div"),r&&r.c(),o=it(),d.c(),be(t,"class","time svelte-9n45fh"),be(e,"class","timestamps svelte-9n45fh")},m(l,c){Et(l,e,c),ce(e,t),i[23](t),ce(e,n),ce(e,s),r&&r.m(s,null),ce(s,o),d.m(s,null)},p(l,c){l[0]==="edit"&&l[16]>0?r?r.p(l,c):(r=pi(l),r.c(),r.m(s,o)):r&&(r.d(1),r=null),a===(a=u(l))&&d?d.p(l,c):(d.d(1),d=a(l),d&&(d.c(),d.m(s,null)))},d(l){l&&Ct(e),i[23](null),r&&r.d(),d.d()}}}function pi(i){let e,t=de(i[16])+"",n;return{c(){e=we("time"),n=Gi(t),be(e,"class","trim-duration svelte-9n45fh")},m(s,o){Et(s,e,o),ce(e,n)},p(s,o){o[0]&65536&&t!==(t=de(s[16])+"")&&Xi(n,t)},d(s){s&&Ct(e)}}}function ol(i){let e;return{c(){e=we("time"),e.textContent="0:00",be(e,"class","duration svelte-9n45fh")},m(t,n){Et(t,e,n),i[24](e)},p:tl,d(t){t&&Ct(e),i[24](null)}}}function rl(i){let e,t=de(i[14])+"",n;return{c(){e=we("time"),n=Gi(t),be(e,"class","duration svelte-9n45fh")},m(s,o){Et(s,e,o),ce(e,n)},p(s,o){o[0]&16384&&t!==(t=de(s[14])+"")&&Xi(n,t)},d(s){s&&Ct(e)}}}function vi(i){let e,t,n;function s(r){i[25](r)}let o={i18n:i[1],timing:i[15],show_recording_waveform:i[2].show_recording_waveform,record_time:de(i[14])};return i[8]!==void 0&&(o.record=i[8]),e=new Qr({props:o}),Me.push(()=>Pt(e,"record",s)),{c(){ji(e.$$.fragment)},m(r,u){Fi(e,r,u),n=!0},p(r,u){const a={};u[0]&2&&(a.i18n=r[1]),u[0]&32768&&(a.timing=r[15]),u[0]&4&&(a.show_recording_waveform=r[2].show_recording_waveform),u[0]&16384&&(a.record_time=de(r[14])),!t&&u[0]&256&&(t=!0,a.record=r[8],Mt(()=>t=!1)),e.$set(a)},i(r){n||(Le(e.$$.fragment,r),n=!0)},o(r){st(e.$$.fragment,r),n=!1},d(r){Ui(e,r)}}}function bi(i){let e,t,n,s,o;function r(l){i[26](l)}function u(l){i[27](l)}function a(l){i[28](l)}let d={container:i[6],playing:i[12],audio_duration:i[13],i18n:i[1],editable:i[4],interactive:!0,handle_trim_audio:i[17],show_redo:!0,handle_reset_value:i[3],waveform_options:i[2]};return i[5]!==void 0&&(d.waveform=i[5]),i[16]!==void 0&&(d.trimDuration=i[16]),i[0]!==void 0&&(d.mode=i[0]),e=new zi({props:d}),Me.push(()=>Pt(e,"waveform",r)),Me.push(()=>Pt(e,"trimDuration",u)),Me.push(()=>Pt(e,"mode",a)),{c(){ji(e.$$.fragment)},m(l,c){Fi(e,l,c),o=!0},p(l,c){const f={};c[0]&64&&(f.container=l[6]),c[0]&4096&&(f.playing=l[12]),c[0]&8192&&(f.audio_duration=l[13]),c[0]&2&&(f.i18n=l[1]),c[0]&16&&(f.editable=l[4]),c[0]&8&&(f.handle_reset_value=l[3]),c[0]&4&&(f.waveform_options=l[2]),!t&&c[0]&32&&(t=!0,f.waveform=l[5],Mt(()=>t=!1)),!n&&c[0]&65536&&(n=!0,f.trimDuration=l[16],Mt(()=>n=!1)),!s&&c[0]&1&&(s=!0,f.mode=l[0],Mt(()=>s=!1)),e.$set(f)},i(l){o||(Le(e.$$.fragment,l),o=!0)},o(l){st(e.$$.fragment,l),o=!1},d(l){Ui(e,l)}}}function ll(i){let e,t,n,s,o,r,u,a,d=(i[15]||i[9])&&i[2].show_recording_waveform&&gi(i),l=i[7]&&!i[9]&&vi(i),c=i[5]&&i[9]&&bi(i);return{c(){e=we("div"),t=we("div"),n=it(),s=we("div"),o=it(),d&&d.c(),r=it(),l&&l.c(),u=it(),c&&c.c(),be(t,"class","microphone svelte-9n45fh"),be(t,"data-testid","microphone-waveform"),be(s,"data-testid","recording-waveform"),be(e,"class","component-wrapper svelte-9n45fh")},m(f,_){Et(f,e,_),ce(e,t),i[21](t),ce(e,n),ce(e,s),i[22](s),ce(e,o),d&&d.m(e,null),ce(e,r),l&&l.m(e,null),ce(e,u),c&&c.m(e,null),a=!0},p(f,_){(f[15]||f[9])&&f[2].show_recording_waveform?d?d.p(f,_):(d=gi(f),d.c(),d.m(e,r)):d&&(d.d(1),d=null),f[7]&&!f[9]?l?(l.p(f,_),_[0]&640&&Le(l,1)):(l=vi(f),l.c(),Le(l,1),l.m(e,u)):l&&(mi(),st(l,1,1,()=>{l=null}),_i()),f[5]&&f[9]?c?(c.p(f,_),_[0]&544&&Le(c,1)):(c=bi(f),c.c(),Le(c,1),c.m(e,null)):c&&(mi(),st(c,1,1,()=>{c=null}),_i())},i(f){a||(Le(l),Le(c),a=!0)},o(f){st(l),st(c),a=!1},d(f){f&&Ct(e),i[21](null),i[22](null),d&&d.d(),l&&l.d(),c&&c.d()}}}function al(i,e,t){let{mode:n}=e,{i18n:s}=e,{dispatch_blob:o}=e,{waveform_settings:r}=e,{waveform_options:u={show_recording_waveform:!0}}=e,{handle_reset_value:a}=e,{editable:d=!0}=e,l,c,f=!1,_,m,h,g=null,k,D,p,L=0,E,P=!1,R=0;const A=()=>{clearInterval(E),t(20,E=setInterval(()=>{t(14,L++,L)},1e3))},C=sl(),T=()=>{m&&t(7,m.innerHTML="",m),l!==void 0&&l.destroy(),m&&(l=rt.create({...r,normalize:!1,container:m}),t(8,h=l.registerPlugin(yt.create())),h.startMic())},N=()=>{let w=_;!g||!w||t(5,c=rt.create({container:w,url:g,...r}))},W=async(w,Y)=>{t(0,n="edit");const y=c.getDecodedData();y&&await pn(y,w,Y).then(async K=>{await o([K],"change"),await o([K],"stop_recording"),c.destroy(),N()}),C("edit")};il(()=>{T(),window.addEventListener("keydown",w=>{w.key==="ArrowRight"?At(c,.1):w.key==="ArrowLeft"&&At(c,-.1)})});function B(w){Me[w?"unshift":"push"](()=>{m=w,t(7,m)})}function G(w){Me[w?"unshift":"push"](()=>{_=w,t(6,_)})}function $(w){Me[w?"unshift":"push"](()=>{k=w,t(10,k),t(5,c)})}function z(w){Me[w?"unshift":"push"](()=>{D=w,t(11,D),t(5,c)})}function b(w){h=w,t(8,h)}function H(w){c=w,t(5,c)}function O(w){R=w,t(16,R)}function M(w){n=w,t(0,n)}return i.$$set=w=>{"mode"in w&&t(0,n=w.mode),"i18n"in w&&t(1,s=w.i18n),"dispatch_blob"in w&&t(18,o=w.dispatch_blob),"waveform_settings"in w&&t(19,r=w.waveform_settings),"waveform_options"in w&&t(2,u=w.waveform_options),"handle_reset_value"in w&&t(3,a=w.handle_reset_value),"editable"in w&&t(4,d=w.editable)},i.$$.update=()=>{i.$$.dirty[0]&388&&h?.on("record-start",()=>{if(A(),t(15,P=!0),C("start_recording"),u.show_recording_waveform){let w=m;w&&(w.style.display="block")}}),i.$$.dirty[0]&1835264&&h?.on("record-end",async w=>{t(14,L=0),t(15,P=!1),clearInterval(E);try{const Y=await w.arrayBuffer(),K=await new AudioContext({sampleRate:r.sampleRate}).decodeAudioData(Y);K&&await pn(K).then(async le=>{await o([le],"change"),await o([le],"stop_recording")})}catch(Y){console.error(Y)}}),i.$$.dirty[0]&1048832&&h?.on("record-pause",()=>{C("pause_recording"),clearInterval(E)}),i.$$.dirty[0]&256&&h?.on("record-resume",()=>{A()}),i.$$.dirty[0]&2080&&c?.on("decode",w=>{t(13,p=w),D&&t(11,D.textContent=de(w),D)}),i.$$.dirty[0]&1056&&c?.on("timeupdate",w=>k&&t(10,k.textContent=de(w),k)),i.$$.dirty[0]&32&&c?.on("pause",()=>{C("pause"),t(12,f=!1)}),i.$$.dirty[0]&32&&c?.on("play",()=>{C("play"),t(12,f=!0)}),i.$$.dirty[0]&32&&c?.on("finish",()=>{C("stop"),t(12,f=!1)}),i.$$.dirty[0]&960&&h?.on("record-end",w=>{t(9,g=URL.createObjectURL(w));const Y=m,y=_;Y&&(Y.style.display="none"),y&&g&&(y.innerHTML="",N())})},[n,s,u,a,d,c,_,m,h,g,k,D,f,p,L,P,R,W,o,r,E,B,G,$,z,b,H,O,M]}class ul extends xr{constructor(e){super(),el(this,e,al,ll,nl,{mode:0,i18n:1,dispatch_blob:18,waveform_settings:19,waveform_options:2,handle_reset_value:3,editable:4},null,[-1,-1])}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),Ve()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),Ve()}get dispatch_blob(){return this.$$.ctx[18]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),Ve()}get waveform_settings(){return this.$$.ctx[19]}set waveform_settings(e){this.$$set({waveform_settings:e}),Ve()}get waveform_options(){return this.$$.ctx[2]}set waveform_options(e){this.$$set({waveform_options:e}),Ve()}get handle_reset_value(){return this.$$.ctx[3]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),Ve()}get editable(){return this.$$.ctx[4]}set editable(e){this.$$set({editable:e}),Ve()}}const{SvelteComponent:cl,add_flush_callback:dl,append:ke,attr:je,bind:fl,binding_callbacks:Yi,create_component:hl,destroy_component:_l,detach:jt,element:Fe,flush:qe,init:ml,insert:Ut,listen:Zi,mount_component:gl,null_to_empty:wi,safe_not_equal:pl,set_data:Ji,set_style:ki,space:$t,text:Ki,transition_in:vl,transition_out:bl}=window.__gradio__svelte__internal,{onMount:wl}=window.__gradio__svelte__internal;function yi(i){let e;return{c(){e=Fe("div"),ki(e,"display",i[0]?"block":"none")},m(t,n){Ut(t,e,n),i[10](e)},p(t,n){n&1&&ki(e,"display",t[0]?"block":"none")},d(t){t&&jt(e),i[10](null)}}}function kl(i){let e,t,n,s=i[4]("audio.record")+"",o,r,u;return{c(){e=Fe("button"),t=Fe("span"),t.innerHTML='<span class="dot"></span>',n=$t(),o=Ki(s),je(t,"class","record-icon"),je(e,"class","record-button svelte-1m31gsz")},m(a,d){Ut(a,e,d),ke(e,t),ke(e,n),ke(e,o),r||(u=Zi(e,"click",i[12]),r=!0)},p(a,d){d&16&&s!==(s=a[4]("audio.record")+"")&&Ji(o,s)},d(a){a&&jt(e),r=!1,u()}}}function yl(i){let e,t,n,s=(i[1]?i[4]("audio.pause"):i[4]("audio.stop"))+"",o,r,u,a;return{c(){e=Fe("button"),t=Fe("span"),t.innerHTML='<span class="pinger"></span> <span class="dot"></span>',n=$t(),o=Ki(s),je(t,"class","record-icon"),je(e,"class",r=wi(i[1]?"stop-button-paused":"stop-button")+" svelte-1m31gsz")},m(d,l){Ut(d,e,l),ke(e,t),ke(e,n),ke(e,o),u||(a=Zi(e,"click",i[11]),u=!0)},p(d,l){l&18&&s!==(s=(d[1]?d[4]("audio.pause"):d[4]("audio.stop"))+"")&&Ji(o,s),l&2&&r!==(r=wi(d[1]?"stop-button-paused":"stop-button")+" svelte-1m31gsz")&&je(e,"class",r)},d(d){d&&jt(e),u=!1,a()}}}function Cl(i){let e,t,n,s,o,r,u,a=i[5].show_recording_waveform&&yi(i);function d(m,h){return m[0]?yl:kl}let l=d(i),c=l(i);function f(m){i[13](m)}let _={i18n:i[4]};return i[8]!==void 0&&(_.micDevices=i[8]),o=new Vi({props:_}),Yi.push(()=>fl(o,"micDevices",f)),{c(){e=Fe("div"),a&&a.c(),t=$t(),n=Fe("div"),c.c(),s=$t(),hl(o.$$.fragment),je(n,"class","controls svelte-1m31gsz"),je(e,"class","mic-wrap svelte-1m31gsz")},m(m,h){Ut(m,e,h),a&&a.m(e,null),ke(e,t),ke(e,n),c.m(n,null),ke(n,s),gl(o,n,null),u=!0},p(m,[h]){m[5].show_recording_waveform?a?a.p(m,h):(a=yi(m),a.c(),a.m(e,t)):a&&(a.d(1),a=null),l===(l=d(m))&&c?c.p(m,h):(c.d(1),c=l(m),c&&(c.c(),c.m(n,s)));const g={};h&16&&(g.i18n=m[4]),!r&&h&256&&(r=!0,g.micDevices=m[8],dl(()=>r=!1)),o.$set(g)},i(m){u||(vl(o.$$.fragment,m),u=!0)},o(m){bl(o.$$.fragment,m),u=!1},d(m){m&&jt(e),a&&a.d(),c.d(),_l(o)}}}function El(i,e,t){let{recording:n=!1}=e,{paused_recording:s=!1}=e,{stop:o}=e,{record:r}=e,{i18n:u}=e,{waveform_settings:a}=e,{waveform_options:d={show_recording_waveform:!0}}=e,l,c,f,_=[];wl(()=>{m()});const m=()=>{l!==void 0&&l.destroy(),f&&(l=rt.create({...a,height:100,container:f}),t(6,c=l.registerPlugin(yt.create())))};function h(p){Yi[p?"unshift":"push"](()=>{f=p,t(7,f)})}const g=()=>{c?.stopMic(),o()},k=()=>{c?.startMic(),r()};function D(p){_=p,t(8,_)}return i.$$set=p=>{"recording"in p&&t(0,n=p.recording),"paused_recording"in p&&t(1,s=p.paused_recording),"stop"in p&&t(2,o=p.stop),"record"in p&&t(3,r=p.record),"i18n"in p&&t(4,u=p.i18n),"waveform_settings"in p&&t(9,a=p.waveform_settings),"waveform_options"in p&&t(5,d=p.waveform_options)},[n,s,o,r,u,d,c,f,_,a,h,g,k,D]}class Dl extends cl{constructor(e){super(),ml(this,e,El,Cl,pl,{recording:0,paused_recording:1,stop:2,record:3,i18n:4,waveform_settings:9,waveform_options:5})}get recording(){return this.$$.ctx[0]}set recording(e){this.$$set({recording:e}),qe()}get paused_recording(){return this.$$.ctx[1]}set paused_recording(e){this.$$set({paused_recording:e}),qe()}get stop(){return this.$$.ctx[2]}set stop(e){this.$$set({stop:e}),qe()}get record(){return this.$$.ctx[3]}set record(e){this.$$set({record:e}),qe()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),qe()}get waveform_settings(){return this.$$.ctx[9]}set waveform_settings(e){this.$$set({waveform_settings:e}),qe()}get waveform_options(){return this.$$.ctx[5]}set waveform_options(e){this.$$set({waveform_options:e}),qe()}}const{SvelteComponent:Rl,add_flush_callback:Ft,append:Sl,attr:Ll,bind:Xt,binding_callbacks:Gt,bubble:Ie,check_outros:Mn,create_component:Oe,create_slot:Ml,destroy_component:Be,detach:lt,element:Pl,empty:Qi,flush:U,get_all_dirty_from_scope:Al,get_slot_changes:Tl,group_outros:Pn,init:zl,insert:at,mount_component:He,safe_not_equal:$l,space:Wt,transition_in:ne,transition_out:ie,update_slot_base:Wl}=window.__gradio__svelte__internal,{onDestroy:Ol,createEventDispatcher:Bl}=window.__gradio__svelte__internal;function Hl(i){let e,t,n,s,o;e=new Li({props:{i18n:i[9],download:i[6]?i[1].url:null,absolute:!0}}),e.$on("clear",i[23]),e.$on("edit",i[37]);function r(a){i[38](a)}let u={value:i[1],label:i[3],i18n:i[9],dispatch_blob:i[21],waveform_settings:i[10],waveform_options:i[12],trim_region_settings:i[11],handle_reset_value:i[13],editable:i[14],interactive:!0};return i[19]!==void 0&&(u.mode=i[19]),n=new Oi({props:u}),Gt.push(()=>Xt(n,"mode",r)),n.$on("stop",i[39]),n.$on("play",i[40]),n.$on("pause",i[41]),n.$on("edit",i[42]),{c(){Oe(e.$$.fragment),t=Wt(),Oe(n.$$.fragment)},m(a,d){He(e,a,d),at(a,t,d),He(n,a,d),o=!0},p(a,d){const l={};d[0]&512&&(l.i18n=a[9]),d[0]&66&&(l.download=a[6]?a[1].url:null),e.$set(l);const c={};d[0]&2&&(c.value=a[1]),d[0]&8&&(c.label=a[3]),d[0]&512&&(c.i18n=a[9]),d[0]&1024&&(c.waveform_settings=a[10]),d[0]&4096&&(c.waveform_options=a[12]),d[0]&2048&&(c.trim_region_settings=a[11]),d[0]&8192&&(c.handle_reset_value=a[13]),d[0]&16384&&(c.editable=a[14]),!s&&d[0]&524288&&(s=!0,c.mode=a[19],Ft(()=>s=!1)),n.$set(c)},i(a){o||(ne(e.$$.fragment,a),ne(n.$$.fragment,a),o=!0)},o(a){ie(e.$$.fragment,a),ie(n.$$.fragment,a),o=!1},d(a){a&&lt(t),Be(e,a),Be(n,a)}}}function Nl(i){let e,t,n,s;const o=[ql,Vl],r=[];function u(a,d){return a[2]==="microphone"?0:a[2]==="upload"?1:-1}return~(e=u(i))&&(t=r[e]=o[e](i)),{c(){t&&t.c(),n=Qi()},m(a,d){~e&&r[e].m(a,d),at(a,n,d),s=!0},p(a,d){let l=e;e=u(a),e===l?~e&&r[e].p(a,d):(t&&(Pn(),ie(r[l],1,1,()=>{r[l]=null}),Mn()),~e?(t=r[e],t?t.p(a,d):(t=r[e]=o[e](a),t.c()),ne(t,1),t.m(n.parentNode,n)):t=null)},i(a){s||(ne(t),s=!0)},o(a){ie(t),s=!1},d(a){a&&lt(n),~e&&r[e].d(a)}}}function Vl(i){let e,t,n;function s(r){i[35](r)}let o={filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",root:i[4],max_file_size:i[15],upload:i[16],stream_handler:i[17],$$slots:{default:[Il]},$$scope:{ctx:i}};return i[0]!==void 0&&(o.dragging=i[0]),e=new fs({props:o}),Gt.push(()=>Xt(e,"dragging",s)),e.$on("load",i[24]),e.$on("error",i[36]),{c(){Oe(e.$$.fragment)},m(r,u){He(e,r,u),n=!0},p(r,u){const a={};u[0]&16&&(a.root=r[4]),u[0]&32768&&(a.max_file_size=r[15]),u[0]&65536&&(a.upload=r[16]),u[0]&131072&&(a.stream_handler=r[17]),u[1]&8192&&(a.$$scope={dirty:u,ctx:r}),!t&&u[0]&1&&(t=!0,a.dragging=r[0],Ft(()=>t=!1)),e.$set(a)},i(r){n||(ne(e.$$.fragment,r),n=!0)},o(r){ie(e.$$.fragment,r),n=!1},d(r){Be(e,r)}}}function ql(i){let e,t,n,s,o,r;e=new Li({props:{i18n:i[9],absolute:!0}}),e.$on("clear",i[23]);const u=[Ul,jl],a=[];function d(l,c){return l[8]?0:1}return n=d(i),s=a[n]=u[n](i),{c(){Oe(e.$$.fragment),t=Wt(),s.c(),o=Qi()},m(l,c){He(e,l,c),at(l,t,c),a[n].m(l,c),at(l,o,c),r=!0},p(l,c){const f={};c[0]&512&&(f.i18n=l[9]),e.$set(f);let _=n;n=d(l),n===_?a[n].p(l,c):(Pn(),ie(a[_],1,1,()=>{a[_]=null}),Mn(),s=a[n],s?s.p(l,c):(s=a[n]=u[n](l),s.c()),ne(s,1),s.m(o.parentNode,o))},i(l){r||(ne(e.$$.fragment,l),ne(s),r=!0)},o(l){ie(e.$$.fragment,l),ie(s),r=!1},d(l){l&&(lt(t),lt(o)),Be(e,l),a[n].d(l)}}}function Il(i){let e;const t=i[30].default,n=Ml(t,i,i[44],null);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),e=!0},p(s,o){n&&n.p&&(!e||o[1]&8192)&&Wl(n,t,s,s[44],e?Tl(t,s[44],o,null):Al(s[44]),null)},i(s){e||(ne(n,s),e=!0)},o(s){ie(n,s),e=!1},d(s){n&&n.d(s)}}}function jl(i){let e,t,n;function s(r){i[31](r)}let o={i18n:i[9],editable:i[14],dispatch_blob:i[21],waveform_settings:i[10],waveform_options:i[12],handle_reset_value:i[13]};return i[19]!==void 0&&(o.mode=i[19]),e=new ul({props:o}),Gt.push(()=>Xt(e,"mode",s)),e.$on("start_recording",i[32]),e.$on("pause_recording",i[33]),e.$on("stop_recording",i[34]),{c(){Oe(e.$$.fragment)},m(r,u){He(e,r,u),n=!0},p(r,u){const a={};u[0]&512&&(a.i18n=r[9]),u[0]&16384&&(a.editable=r[14]),u[0]&1024&&(a.waveform_settings=r[10]),u[0]&4096&&(a.waveform_options=r[12]),u[0]&8192&&(a.handle_reset_value=r[13]),!t&&u[0]&524288&&(t=!0,a.mode=r[19],Ft(()=>t=!1)),e.$set(a)},i(r){n||(ne(e.$$.fragment,r),n=!0)},o(r){ie(e.$$.fragment,r),n=!1},d(r){Be(e,r)}}}function Ul(i){let e,t;return e=new Dl({props:{record:i[22],recording:i[18],stop:i[25],i18n:i[9],waveform_settings:i[10],waveform_options:i[12]}}),{c(){Oe(e.$$.fragment)},m(n,s){He(e,n,s),t=!0},p(n,s){const o={};s[0]&262144&&(o.recording=n[18]),s[0]&512&&(o.i18n=n[9]),s[0]&1024&&(o.waveform_settings=n[10]),s[0]&4096&&(o.waveform_options=n[12]),e.$set(o)},i(n){t||(ne(e.$$.fragment,n),t=!0)},o(n){ie(e.$$.fragment,n),t=!1},d(n){Be(e,n)}}}function Fl(i){let e,t,n,s,o,r,u,a,d;e=new Di({props:{show_label:i[5],Icon:Ot,float:i[2]==="upload"&&i[1]===null,label:i[3]||i[9]("audio.audio")}});const l=[Nl,Hl],c=[];function f(h,g){return h[1]===null||h[8]?0:1}s=f(i),o=c[s]=l[s](i);function _(h){i[43](h)}let m={sources:i[7],handle_clear:i[23]};return i[2]!==void 0&&(m.active_source=i[2]),u=new hs({props:m}),Gt.push(()=>Xt(u,"active_source",_)),{c(){Oe(e.$$.fragment),t=Wt(),n=Pl("div"),o.c(),r=Wt(),Oe(u.$$.fragment),Ll(n,"class","audio-container svelte-cbyffp")},m(h,g){He(e,h,g),at(h,t,g),at(h,n,g),c[s].m(n,null),Sl(n,r),He(u,n,null),d=!0},p(h,g){const k={};g[0]&32&&(k.show_label=h[5]),g[0]&6&&(k.float=h[2]==="upload"&&h[1]===null),g[0]&520&&(k.label=h[3]||h[9]("audio.audio")),e.$set(k);let D=s;s=f(h),s===D?c[s].p(h,g):(Pn(),ie(c[D],1,1,()=>{c[D]=null}),Mn(),o=c[s],o?o.p(h,g):(o=c[s]=l[s](h),o.c()),ne(o,1),o.m(n,r));const p={};g[0]&128&&(p.sources=h[7]),!a&&g[0]&4&&(a=!0,p.active_source=h[2],Ft(()=>a=!1)),u.$set(p)},i(h){d||(ne(e.$$.fragment,h),ne(o),ne(u.$$.fragment,h),d=!0)},o(h){ie(e.$$.fragment,h),ie(o),ie(u.$$.fragment,h),d=!1},d(h){h&&(lt(t),lt(n)),Be(e,h),c[s].d(),Be(u)}}}const Xl=500,Ci=44;function Gl(i,e,t){let{$$slots:n={},$$scope:s}=e,{value:o=null}=e,{label:r}=e,{root:u}=e,{show_label:a=!0}=e,{show_download_button:d=!1}=e,{sources:l=["microphone","upload"]}=e,{pending:c=!1}=e,{streaming:f=!1}=e,{i18n:_}=e,{waveform_settings:m}=e,{trim_region_settings:h={}}=e,{waveform_options:g={}}=e,{dragging:k}=e,{active_source:D}=e,{handle_reset_value:p=()=>{}}=e,{editable:L=!0}=e,{max_file_size:E=null}=e,{upload:P}=e,{stream_handler:R}=e,A=!1,C,T="",N,W=[],B=!1,G=!1,$=[],z;function b(){z=[$n(()=>import("./module-BTDC6jSQ.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url),$n(()=>import("./module-BA06XY8_.js"),__vite__mapDeps([6,1]),import.meta.url)]}f&&b();const H=Bl(),O=async(v,Q)=>{let Ne=new File(v,"audio.wav");const ut=await ds([Ne],Q==="stream");t(1,o=(await P(ut,u,void 0,E||void 0))?.filter(Boolean)[0]),H(Q,o)};Ol(()=>{f&&C&&C.state!=="inactive"&&C.stop()});async function M(){let v;try{v=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(Q){if(!navigator.mediaDevices){H("error",_("audio.no_device_support"));return}if(Q instanceof DOMException&&Q.name=="NotAllowedError"){H("error",_("audio.allow_recording_access"));return}throw Q}if(v!=null){if(f){const[{MediaRecorder:Q,register:Ne},{connect:ut}]=await Promise.all(z);await Ne(await ut()),C=new Q(v,{mimeType:"audio/wav"}),C.addEventListener("dataavailable",w)}else C=new MediaRecorder(v),C.addEventListener("dataavailable",Q=>{$.push(Q.data)}),C.addEventListener("stop",async()=>{t(18,A=!1),await O($,"change"),await O($,"stop_recording"),$=[]});G=!0}}async function w(v){let Q=await v.data.arrayBuffer(),Ne=new Uint8Array(Q);if(N||(t(27,N=new Uint8Array(Q.slice(0,Ci))),Ne=new Uint8Array(Q.slice(Ci))),c)W.push(Ne);else{let ut=[N].concat(W,[Ne]);O(ut,"stream"),t(28,W=[])}}async function Y(){t(18,A=!0),H("start_recording"),G||await M(),t(27,N=void 0),f&&C.start(Xl)}function y(){H("change",null),H("clear"),t(19,T=""),t(1,o=null)}function K({detail:v}){t(1,o=v),H("change",v),H("upload",v)}function le(){t(18,A=!1),f&&(H("stop_recording"),C.stop(),c&&t(29,B=!0),O($,"stop_recording"),H("clear"),t(19,T=""))}function me(v){T=v,t(19,T)}function Yt(v){Ie.call(this,i,v)}function Zt(v){Ie.call(this,i,v)}function Jt(v){Ie.call(this,i,v)}function Kt(v){k=v,t(0,k)}const Qt=({detail:v})=>H("error",v),xt=()=>t(19,T="edit");function en(v){T=v,t(19,T)}function tn(v){Ie.call(this,i,v)}function nn(v){Ie.call(this,i,v)}function sn(v){Ie.call(this,i,v)}function on(v){Ie.call(this,i,v)}function S(v){D=v,t(2,D)}return i.$$set=v=>{"value"in v&&t(1,o=v.value),"label"in v&&t(3,r=v.label),"root"in v&&t(4,u=v.root),"show_label"in v&&t(5,a=v.show_label),"show_download_button"in v&&t(6,d=v.show_download_button),"sources"in v&&t(7,l=v.sources),"pending"in v&&t(26,c=v.pending),"streaming"in v&&t(8,f=v.streaming),"i18n"in v&&t(9,_=v.i18n),"waveform_settings"in v&&t(10,m=v.waveform_settings),"trim_region_settings"in v&&t(11,h=v.trim_region_settings),"waveform_options"in v&&t(12,g=v.waveform_options),"dragging"in v&&t(0,k=v.dragging),"active_source"in v&&t(2,D=v.active_source),"handle_reset_value"in v&&t(13,p=v.handle_reset_value),"editable"in v&&t(14,L=v.editable),"max_file_size"in v&&t(15,E=v.max_file_size),"upload"in v&&t(16,P=v.upload),"stream_handler"in v&&t(17,R=v.stream_handler),"$$scope"in v&&t(44,s=v.$$scope)},i.$$.update=()=>{if(i.$$.dirty[0]&1&&H("drag",k),i.$$.dirty[0]&1006632960&&B&&c===!1&&(t(29,B=!1),N&&W)){let v=[N].concat(W);t(28,W=[]),O(v,"stream")}},[k,o,D,r,u,a,d,l,f,_,m,h,g,p,L,E,P,R,A,T,H,O,Y,y,K,le,c,N,W,B,n,me,Yt,Zt,Jt,Kt,Qt,xt,en,tn,nn,sn,on,S,s]}class Yl extends Rl{constructor(e){super(),zl(this,e,Gl,Fl,$l,{value:1,label:3,root:4,show_label:5,show_download_button:6,sources:7,pending:26,streaming:8,i18n:9,waveform_settings:10,trim_region_settings:11,waveform_options:12,dragging:0,active_source:2,handle_reset_value:13,editable:14,max_file_size:15,upload:16,stream_handler:17},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),U()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),U()}get root(){return this.$$.ctx[4]}set root(e){this.$$set({root:e}),U()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),U()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),U()}get sources(){return this.$$.ctx[7]}set sources(e){this.$$set({sources:e}),U()}get pending(){return this.$$.ctx[26]}set pending(e){this.$$set({pending:e}),U()}get streaming(){return this.$$.ctx[8]}set streaming(e){this.$$set({streaming:e}),U()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),U()}get waveform_settings(){return this.$$.ctx[10]}set waveform_settings(e){this.$$set({waveform_settings:e}),U()}get trim_region_settings(){return this.$$.ctx[11]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),U()}get waveform_options(){return this.$$.ctx[12]}set waveform_options(e){this.$$set({waveform_options:e}),U()}get dragging(){return this.$$.ctx[0]}set dragging(e){this.$$set({dragging:e}),U()}get active_source(){return this.$$.ctx[2]}set active_source(e){this.$$set({active_source:e}),U()}get handle_reset_value(){return this.$$.ctx[13]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),U()}get editable(){return this.$$.ctx[14]}set editable(e){this.$$set({editable:e}),U()}get max_file_size(){return this.$$.ctx[15]}set max_file_size(e){this.$$set({max_file_size:e}),U()}get upload(){return this.$$.ctx[16]}set upload(e){this.$$set({upload:e}),U()}get stream_handler(){return this.$$.ctx[17]}set stream_handler(e){this.$$set({stream_handler:e}),U()}}const Zl=Yl,{SvelteComponent:Jl,add_flush_callback:Kl,assign:xi,bind:Ql,binding_callbacks:xl,check_outros:ea,create_component:Xe,destroy_component:Ge,detach:An,empty:ta,flush:q,get_spread_object:es,get_spread_update:ts,group_outros:na,init:ia,insert:Tn,mount_component:Ye,safe_not_equal:sa,space:ns,transition_in:ye,transition_out:Ce}=window.__gradio__svelte__internal;function oa(i){let e,t;return e=new Mi({props:{variant:i[0]===null&&i[20]==="upload"?"dashed":"solid",border_mode:i[21]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:i[2],elem_classes:i[3],visible:i[4],container:i[10],scale:i[11],min_width:i[12],$$slots:{default:[aa]},$$scope:{ctx:i}}}),{c(){Xe(e.$$.fragment)},m(n,s){Ye(e,n,s),t=!0},p(n,s){const o={};s[0]&1048577&&(o.variant=n[0]===null&&n[20]==="upload"?"dashed":"solid"),s[0]&2097152&&(o.border_mode=n[21]?"focus":"base"),s[0]&4&&(o.elem_id=n[2]),s[0]&8&&(o.elem_classes=n[3]),s[0]&16&&(o.visible=n[4]),s[0]&1024&&(o.container=n[10]),s[0]&2048&&(o.scale=n[11]),s[0]&4096&&(o.min_width=n[12]),s[0]&8364995|s[1]&1048576&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(ye(e.$$.fragment,n),t=!0)},o(n){Ce(e.$$.fragment,n),t=!1},d(n){Ge(e,n)}}}function ra(i){let e,t;return e=new Mi({props:{variant:"solid",border_mode:i[21]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:i[2],elem_classes:i[3],visible:i[4],container:i[10],scale:i[11],min_width:i[12],$$slots:{default:[ua]},$$scope:{ctx:i}}}),{c(){Xe(e.$$.fragment)},m(n,s){Ye(e,n,s),t=!0},p(n,s){const o={};s[0]&2097152&&(o.border_mode=n[21]?"focus":"base"),s[0]&4&&(o.elem_id=n[2]),s[0]&8&&(o.elem_classes=n[3]),s[0]&16&&(o.visible=n[4]),s[0]&1024&&(o.container=n[10]),s[0]&2048&&(o.scale=n[11]),s[0]&4096&&(o.min_width=n[12]),s[0]&4842115|s[1]&1048576&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(ye(e.$$.fragment,n),t=!0)},o(n){Ce(e.$$.fragment,n),t=!1},d(n){Ge(e,n)}}}function la(i){let e,t;return e=new _s({props:{i18n:i[19].i18n,type:"audio"}}),{c(){Xe(e.$$.fragment)},m(n,s){Ye(e,n,s),t=!0},p(n,s){const o={};s[0]&524288&&(o.i18n=n[19].i18n),e.$set(o)},i(n){t||(ye(e.$$.fragment,n),t=!0)},o(n){Ce(e.$$.fragment,n),t=!1},d(n){Ge(e,n)}}}function aa(i){let e,t,n,s,o;const r=[{autoscroll:i[19].autoscroll},{i18n:i[19].i18n},i[1]];let u={};for(let l=0;l<r.length;l+=1)u=xi(u,r[l]);e=new Ei({props:u}),e.$on("clear_status",i[35]);function a(l){i[36](l)}let d={label:i[7],show_label:i[9],show_download_button:i[13],value:i[0],root:i[8],sources:i[6],active_source:i[20],pending:i[17],streaming:i[18],max_file_size:i[19].max_file_size,handle_reset_value:i[23],editable:i[15],i18n:i[19].i18n,waveform_settings:i[22],waveform_options:i[16],trim_region_settings:i[24],upload:i[19].client.upload,stream_handler:i[19].client.stream,$$slots:{default:[la]},$$scope:{ctx:i}};return i[21]!==void 0&&(d.dragging=i[21]),n=new Zl({props:d}),xl.push(()=>Ql(n,"dragging",a)),n.$on("change",i[37]),n.$on("stream",i[38]),n.$on("drag",i[39]),n.$on("edit",i[40]),n.$on("play",i[41]),n.$on("pause",i[42]),n.$on("stop",i[43]),n.$on("start_recording",i[44]),n.$on("pause_recording",i[45]),n.$on("stop_recording",i[46]),n.$on("upload",i[47]),n.$on("clear",i[48]),n.$on("error",i[25]),{c(){Xe(e.$$.fragment),t=ns(),Xe(n.$$.fragment)},m(l,c){Ye(e,l,c),Tn(l,t,c),Ye(n,l,c),o=!0},p(l,c){const f=c[0]&524290?ts(r,[c[0]&524288&&{autoscroll:l[19].autoscroll},c[0]&524288&&{i18n:l[19].i18n},c[0]&2&&es(l[1])]):{};e.$set(f);const _={};c[0]&128&&(_.label=l[7]),c[0]&512&&(_.show_label=l[9]),c[0]&8192&&(_.show_download_button=l[13]),c[0]&1&&(_.value=l[0]),c[0]&256&&(_.root=l[8]),c[0]&64&&(_.sources=l[6]),c[0]&1048576&&(_.active_source=l[20]),c[0]&131072&&(_.pending=l[17]),c[0]&262144&&(_.streaming=l[18]),c[0]&524288&&(_.max_file_size=l[19].max_file_size),c[0]&32768&&(_.editable=l[15]),c[0]&524288&&(_.i18n=l[19].i18n),c[0]&4194304&&(_.waveform_settings=l[22]),c[0]&65536&&(_.waveform_options=l[16]),c[0]&524288&&(_.upload=l[19].client.upload),c[0]&524288&&(_.stream_handler=l[19].client.stream),c[0]&524288|c[1]&1048576&&(_.$$scope={dirty:c,ctx:l}),!s&&c[0]&2097152&&(s=!0,_.dragging=l[21],Kl(()=>s=!1)),n.$set(_)},i(l){o||(ye(e.$$.fragment,l),ye(n.$$.fragment,l),o=!0)},o(l){Ce(e.$$.fragment,l),Ce(n.$$.fragment,l),o=!1},d(l){l&&An(t),Ge(e,l),Ge(n,l)}}}function ua(i){let e,t,n,s;const o=[{autoscroll:i[19].autoscroll},{i18n:i[19].i18n},i[1]];let r={};for(let u=0;u<o.length;u+=1)r=xi(r,o[u]);return e=new Ei({props:r}),e.$on("clear_status",i[29]),n=new Pr({props:{i18n:i[19].i18n,show_label:i[9],show_download_button:i[13],show_share_button:i[14],value:i[0],label:i[7],waveform_settings:i[22],waveform_options:i[16],editable:i[15]}}),n.$on("share",i[30]),n.$on("error",i[31]),n.$on("play",i[32]),n.$on("pause",i[33]),n.$on("stop",i[34]),{c(){Xe(e.$$.fragment),t=ns(),Xe(n.$$.fragment)},m(u,a){Ye(e,u,a),Tn(u,t,a),Ye(n,u,a),s=!0},p(u,a){const d=a[0]&524290?ts(o,[a[0]&524288&&{autoscroll:u[19].autoscroll},a[0]&524288&&{i18n:u[19].i18n},a[0]&2&&es(u[1])]):{};e.$set(d);const l={};a[0]&524288&&(l.i18n=u[19].i18n),a[0]&512&&(l.show_label=u[9]),a[0]&8192&&(l.show_download_button=u[13]),a[0]&16384&&(l.show_share_button=u[14]),a[0]&1&&(l.value=u[0]),a[0]&128&&(l.label=u[7]),a[0]&4194304&&(l.waveform_settings=u[22]),a[0]&65536&&(l.waveform_options=u[16]),a[0]&32768&&(l.editable=u[15]),n.$set(l)},i(u){s||(ye(e.$$.fragment,u),ye(n.$$.fragment,u),s=!0)},o(u){Ce(e.$$.fragment,u),Ce(n.$$.fragment,u),s=!1},d(u){u&&An(t),Ge(e,u),Ge(n,u)}}}function ca(i){let e,t,n,s;const o=[ra,oa],r=[];function u(a,d){return a[5]?1:0}return e=u(i),t=r[e]=o[e](i),{c(){t.c(),n=ta()},m(a,d){r[e].m(a,d),Tn(a,n,d),s=!0},p(a,d){let l=e;e=u(a),e===l?r[e].p(a,d):(na(),Ce(r[l],1,1,()=>{r[l]=null}),ea(),t=r[e],t?t.p(a,d):(t=r[e]=o[e](a),t.c()),ye(t,1),t.m(n.parentNode,n))},i(a){s||(ye(t),s=!0)},o(a){Ce(t),s=!1},d(a){a&&An(n),r[e].d(a)}}}function da(i,e,t){let{elem_id:n=""}=e,{elem_classes:s=[]}=e,{visible:o=!0}=e,{interactive:r}=e,{value:u=null}=e,{sources:a}=e,{label:d}=e,{root:l}=e,{show_label:c}=e,{container:f=!0}=e,{scale:_=null}=e,{min_width:m=void 0}=e,{loading_status:h}=e,{autoplay:g=!1}=e,{show_download_button:k}=e,{show_share_button:D=!1}=e,{editable:p=!0}=e,{waveform_options:L={}}=e,{pending:E}=e,{streaming:P}=e,{gradio:R}=e,A=null,C,T=u;const N=()=>{T===null||u===T||t(0,u=T)};let W,B,G=getComputedStyle(document.documentElement).getPropertyValue("--color-accent");const $={color:L.trim_region_color,drag:!0,resize:!0};function z(){document.documentElement.style.setProperty("--trim-region-color",$.color||G)}z();function b({detail:S}){const[v,Q]=S.includes("Invalid file type")?["warning","complete"]:["error","error"];t(1,h=h||{}),t(1,h.status=Q,h),t(1,h.message=S,h),R.dispatch(v,S)}const H=()=>R.dispatch("clear_status",h),O=S=>R.dispatch("share",S.detail),M=S=>R.dispatch("error",S.detail),w=()=>R.dispatch("play"),Y=()=>R.dispatch("pause"),y=()=>R.dispatch("stop"),K=()=>R.dispatch("clear_status",h);function le(S){W=S,t(21,W)}const me=({detail:S})=>t(0,u=S),Yt=({detail:S})=>{t(0,u=S),R.dispatch("stream",u)},Zt=({detail:S})=>t(21,W=S),Jt=()=>R.dispatch("edit"),Kt=()=>R.dispatch("play"),Qt=()=>R.dispatch("pause"),xt=()=>R.dispatch("stop"),en=()=>R.dispatch("start_recording"),tn=()=>R.dispatch("pause_recording"),nn=S=>R.dispatch("stop_recording"),sn=()=>R.dispatch("upload"),on=()=>R.dispatch("clear");return i.$$set=S=>{"elem_id"in S&&t(2,n=S.elem_id),"elem_classes"in S&&t(3,s=S.elem_classes),"visible"in S&&t(4,o=S.visible),"interactive"in S&&t(5,r=S.interactive),"value"in S&&t(0,u=S.value),"sources"in S&&t(6,a=S.sources),"label"in S&&t(7,d=S.label),"root"in S&&t(8,l=S.root),"show_label"in S&&t(9,c=S.show_label),"container"in S&&t(10,f=S.container),"scale"in S&&t(11,_=S.scale),"min_width"in S&&t(12,m=S.min_width),"loading_status"in S&&t(1,h=S.loading_status),"autoplay"in S&&t(26,g=S.autoplay),"show_download_button"in S&&t(13,k=S.show_download_button),"show_share_button"in S&&t(14,D=S.show_share_button),"editable"in S&&t(15,p=S.editable),"waveform_options"in S&&t(16,L=S.waveform_options),"pending"in S&&t(17,E=S.pending),"streaming"in S&&t(18,P=S.streaming),"gradio"in S&&t(19,R=S.gradio)},i.$$.update=()=>{i.$$.dirty[0]&268435457&&u&&T===null&&t(28,T=u),i.$$.dirty[0]&134742017&&JSON.stringify(u)!==JSON.stringify(A)&&(t(27,A=u),R.dispatch("change")),i.$$.dirty[0]&1048640&&!C&&a&&t(20,C=a[0]),i.$$.dirty[0]&67174400&&t(22,B={height:50,waveColor:L.waveform_color||"#9ca3af",progressColor:L.waveform_progress_color||G,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:g,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20,mediaControls:L.show_controls,sampleRate:L.sample_rate||44100})},[u,h,n,s,o,r,a,d,l,c,f,_,m,k,D,p,L,E,P,R,C,W,B,N,$,b,g,A,T,H,O,M,w,Y,y,K,le,me,Yt,Zt,Jt,Kt,Qt,xt,en,tn,nn,sn,on]}class fa extends Jl{constructor(e){super(),ia(this,e,da,ca,sa,{elem_id:2,elem_classes:3,visible:4,interactive:5,value:0,sources:6,label:7,root:8,show_label:9,container:10,scale:11,min_width:12,loading_status:1,autoplay:26,show_download_button:13,show_share_button:14,editable:15,waveform_options:16,pending:17,streaming:18,gradio:19},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),q()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),q()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),q()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),q()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),q()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),q()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),q()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),q()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),q()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),q()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),q()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),q()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),q()}get autoplay(){return this.$$.ctx[26]}set autoplay(e){this.$$set({autoplay:e}),q()}get show_download_button(){return this.$$.ctx[13]}set show_download_button(e){this.$$set({show_download_button:e}),q()}get show_share_button(){return this.$$.ctx[14]}set show_share_button(e){this.$$set({show_share_button:e}),q()}get editable(){return this.$$.ctx[15]}set editable(e){this.$$set({editable:e}),q()}get waveform_options(){return this.$$.ctx[16]}set waveform_options(e){this.$$set({waveform_options:e}),q()}get pending(){return this.$$.ctx[17]}set pending(e){this.$$set({pending:e}),q()}get streaming(){return this.$$.ctx[18]}set streaming(e){this.$$set({streaming:e}),q()}get gradio(){return this.$$.ctx[19]}set gradio(e){this.$$set({gradio:e}),q()}}const za=fa;export{Oa as BaseExample,Zl as BaseInteractiveAudio,Oi as BasePlayer,Pr as BaseStaticAudio,za as default};
//# sourceMappingURL=index-BpRV2GAs.js.map
