const __vite__fileDeps=["./wrapper-CviSselG-DCvi549i.js","./__vite-browser-external-DFe-p4yY.js","./Index-WGC0_FkS.js","./Index-hBVU0Tzp.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import*as pe from"./svelte/svelte.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&n(r)}).observe(document,{childList:!0,subtree:!0});function e(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(i){if(i.ep)return;i.ep=!0;const o=e(i);fetch(i.href,o)}})();const Xe="modulepreload",et=function(t,s){return new URL(t,s).href},Ce={},X=function(s,e,n){let i=Promise.resolve();if(e&&e.length>0){const o=document.getElementsByTagName("link"),r=document.querySelector("meta[property=csp-nonce]"),a=r?.nonce||r?.getAttribute("nonce");i=Promise.all(e.map(u=>{if(u=et(u,n),u in Ce)return;Ce[u]=!0;const c=u.endsWith(".css"),d=c?'[rel="stylesheet"]':"";if(!!n)for(let y=o.length-1;y>=0;y--){const b=o[y];if(b.href===u&&(!c||b.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${d}`))return;const p=document.createElement("link");if(p.rel=c?"stylesheet":Xe,c||(p.as="script",p.crossOrigin=""),p.href=u,a&&p.setAttribute("nonce",a),document.head.appendChild(p),c)return new Promise((y,b)=>{p.addEventListener("load",y),p.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${u}`)))})}))}return i.then(()=>s()).catch(o=>{const r=new Event("vite:preloadError",{cancelable:!0});if(r.payload=o,window.dispatchEvent(r),!r.defaultPrevented)throw o})};var tt=Object.defineProperty,st=(t,s,e)=>s in t?tt(t,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[s]=e,_=(t,s,e)=>(st(t,typeof s!="symbol"?s+"":s,e),e),Le=(t,s,e)=>{if(!s.has(t))throw TypeError("Cannot "+e)},K=(t,s,e)=>(Le(t,s,"read from private field"),e?e.call(t):s.get(t)),nt=(t,s,e)=>{if(s.has(t))throw TypeError("Cannot add the same private member more than once");s instanceof WeakSet?s.add(t):s.set(t,e)},ot=(t,s,e,n)=>(Le(t,s,"write to private field"),s.set(t,e),e),z,he=new Intl.Collator(0,{numeric:1}).compare;function xe(t,s,e){return t=t.split("."),s=s.split("."),he(t[0],s[0])||he(t[1],s[1])||(s[2]=s.slice(2).join("."),e=/[.-]/.test(t[2]=t.slice(2).join(".")),e==/[.-]/.test(s[2])?he(t[2],s[2]):e?-1:1)}const it="host",Re="upload",rt="login",at="config",ct="info",lt="runtime",ut="sleeptime",pt="https://gradio-space-api-fetcher-v2.hf.space/api",Ie="This application is currently busy. Please try again. ",B="Connection errored out. ",W="Could not resolve app config. ",ht="Could not get space status. ",dt="Could not get API info. ",ve="Space metadata could not be loaded. ",ft="Invalid URL. A full URL path is required.",_t="Not authorized to access this space. ",qe="Invalid credentials. Could not login. ",mt="Login credentials are required to access this space.",gt="File system access is only available in Node.js environments",je="Root URL not found in client config",wt="Error uploading file";function Ue(t,s,e){return s.startsWith("http://")||s.startsWith("https://")?e?t:s:t+s}async function Oe(t,s,e){try{return(await(await fetch(`https://huggingface.co/api/spaces/${t}/jwt`,{headers:{Authorization:`Bearer ${s}`,...e?{Cookie:e}:{}}})).json()).token||!1}catch{return!1}}function yt(t){let s={};return t.forEach(({api_name:e,id:n})=>{e&&(s[e]=n)}),s}async function vt(t){var s;const e=this.options.hf_token?{Authorization:`Bearer ${this.options.hf_token}`}:{};if(e["Content-Type"]="application/json",typeof window<"u"&&window.gradio_config&&location.origin!=="http://localhost:9876"&&!window.gradio_config.dev_mode){const n=window.gradio_config.root,i=window.gradio_config;let o=Ue(t,i.root,!1);return i.root=o,{...i,path:n}}else if(t){const n=Me(t,at),i=await this.fetch(n,{headers:e,credentials:"include"});if(i?.status===401&&!this.options.auth)throw new Error(mt);if(i?.status===401&&this.options.auth)throw new Error(qe);if(i?.status===200){let o=await i.json();return o.path=o.path??"",o.root=t,(s=o.dependencies)==null||s.forEach((r,a)=>{r.id===void 0&&(r.id=a)}),o}else if(i?.status===401)throw new Error(_t);throw new Error(W)}throw new Error(W)}async function bt(){const{http_protocol:t,host:s}=await te(this.app_reference,this.options.hf_token);try{if(this.options.auth){const e=await ze(t,s,this.options.auth,this.fetch,this.options.hf_token);e&&this.set_cookies(e)}}catch(e){throw Error(e.message)}}async function ze(t,s,e,n,i){const o=new FormData;o.append("username",e?.[0]),o.append("password",e?.[1]);let r={};i&&(r.Authorization=`Bearer ${i}`);const a=await n(`${t}//${s}/${rt}`,{headers:r,method:"POST",body:o,credentials:"include"});if(a.status===200)return a.headers.get("set-cookie");throw a.status===401?new Error(qe):new Error(ve)}function de(t){if(t.startsWith("http")){const{protocol:s,host:e,pathname:n}=new URL(t);return e.endsWith("hf.space")?{ws_protocol:"wss",host:e,http_protocol:s}:{ws_protocol:s==="https:"?"wss":"ws",http_protocol:s,host:e+(n!=="/"?n:"")}}else if(t.startsWith("file:"))return{ws_protocol:"ws",http_protocol:"http:",host:"lite.local"};return{ws_protocol:"wss",http_protocol:"https:",host:t}}const Fe=t=>{let s=[];return t.split(/,(?=\s*[^\s=;]+=[^\s=;]+)/).forEach(n=>{const[i,o]=n.split(";")[0].split("=");i&&o&&s.push(`${i.trim()}=${o.trim()}`)}),s},Be=/^[a-zA-Z0-9_\-\.]+\/[a-zA-Z0-9_\-\.]+$/,St=/.*hf\.space\/{0,1}$/;async function te(t,s){const e={};s&&(e.Authorization=`Bearer ${s}`);const n=t.trim().replace(/\/$/,"");if(Be.test(n))try{const o=(await(await fetch(`https://huggingface.co/api/spaces/${n}/${it}`,{headers:e})).json()).host;return{space_id:t,...de(o)}}catch{throw new Error(ve)}if(St.test(n)){const{ws_protocol:i,http_protocol:o,host:r}=de(n);return{space_id:r.replace(".hf.space",""),ws_protocol:i,http_protocol:o,host:r}}return{space_id:!1,...de(n)}}const Me=(...t)=>{try{return t.reduce((s,e)=>(s=s.replace(/\/+$/,""),e=e.replace(/^\/+/,""),new URL(e,s+"/").toString()))}catch{throw new Error(ft)}};function Et(t,s,e){const n={named_endpoints:{},unnamed_endpoints:{}};return Object.keys(t).forEach(i=>{(i==="named_endpoints"||i==="unnamed_endpoints")&&(n[i]={},Object.entries(t[i]).forEach(([o,{parameters:r,returns:a}])=>{var u,c,d,h;const p=((u=s.dependencies.find(l=>l.api_name===o||l.api_name===o.replace("/","")))==null?void 0:u.id)||e[o.replace("/","")]||-1,y=p!==-1?(c=s.dependencies.find(l=>l.id==p))==null?void 0:c.types:{continuous:!1,generator:!1,cancel:!1};if(p!==-1&&((h=(d=s.dependencies.find(l=>l.id==p))==null?void 0:d.inputs)==null?void 0:h.length)!==r.length){const l=s.dependencies.find(m=>m.id==p).inputs.map(m=>{var E;return(E=s.components.find(T=>T.id===m))==null?void 0:E.type});try{l.forEach((m,E)=>{if(m==="state"){const T={component:"state",example:null,parameter_default:null,parameter_has_default:!0,parameter_name:null,hidden:!0};r.splice(E,0,T)}})}catch(m){console.error(m)}}const b=(l,m,E,T)=>({...l,description:kt(l?.type,E),type:$t(l?.type,m,E,T)||""});n[i][o]={parameters:r.map(l=>b(l,l?.component,l?.serializer,"parameter")),returns:a.map(l=>b(l,l?.component,l?.serializer,"return")),type:y}}))}),n}function $t(t,s,e,n){switch(t?.type){case"string":return"string";case"boolean":return"boolean";case"number":return"number"}if(e==="JSONSerializable"||e==="StringSerializable")return"any";if(e==="ListStringSerializable")return"string[]";if(s==="Image")return n==="parameter"?"Blob | File | Buffer":"string";if(e==="FileSerializable")return t?.type==="array"?n==="parameter"?"(Blob | File | Buffer)[]":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]":n==="parameter"?"Blob | File | Buffer":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}";if(e==="GallerySerializable")return n==="parameter"?"[(Blob | File | Buffer), (string | null)][]":"[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]"}function kt(t,s){return s==="GallerySerializable"?"array of [file, label] tuples":s==="ListStringSerializable"?"array of strings":s==="FileSerializable"?"array of files or single file":t?.description}function fe(t,s){switch(t.msg){case"send_data":return{type:"data"};case"send_hash":return{type:"hash"};case"queue_full":return{type:"update",status:{queue:!0,message:Ie,stage:"error",code:t.code,success:t.success}};case"heartbeat":return{type:"heartbeat"};case"unexpected_error":return{type:"unexpected_error",status:{queue:!0,message:t.message,stage:"error",success:!1}};case"estimation":return{type:"update",status:{queue:!0,stage:s||"pending",code:t.code,size:t.queue_size,position:t.rank,eta:t.rank_eta,success:t.success}};case"progress":return{type:"update",status:{queue:!0,stage:"pending",code:t.code,progress_data:t.progress_data,success:t.success}};case"log":return{type:"log",data:t};case"process_generating":return{type:"generating",status:{queue:!0,message:t.success?null:t.output.error,stage:t.success?"generating":"error",code:t.code,progress_data:t.progress_data,eta:t.average_duration},data:t.success?t.output:null};case"process_completed":return"error"in t.output?{type:"update",status:{queue:!0,message:t.output.error,stage:"error",code:t.code,success:t.success}}:{type:"complete",status:{queue:!0,message:t.success?void 0:t.output.error,stage:t.success?"complete":"error",code:t.code,progress_data:t.progress_data,changed_state_ids:t.success?t.output.changed_state_ids:void 0},data:t.success?t.output:null};case"process_starts":return{type:"update",status:{queue:!0,stage:"pending",code:t.code,size:t.rank,position:0,success:t.success,eta:t.eta}}}return{type:"none",status:{stage:"error",queue:!0}}}const Ct=(t,s)=>{const e=Object.values(s.named_endpoints).flatMap(o=>o.parameters);if(Array.isArray(t))return t.length>e.length&&console.warn("Too many arguments provided for the endpoint."),t;const n=[],i=Object.keys(t);return e.forEach((o,r)=>{if(t.hasOwnProperty(o.parameter_name))n[r]=t[o.parameter_name];else if(o.parameter_has_default)n[r]=o.parameter_default;else throw new Error(`No value provided for required parameter: ${o.parameter_name}`)}),i.forEach(o=>{if(!e.some(r=>r.parameter_name===o))throw new Error(`Parameter \`${o}\` is not a valid keyword argument. Please refer to the API for usage.`)}),n.forEach((o,r)=>{if(o===void 0&&!e[r].parameter_has_default)throw new Error(`No value provided for required parameter: ${e[r].parameter_name}`)}),n};async function Rt(){if(this.api_info)return this.api_info;const{hf_token:t}=this.options,{config:s}=this,e={"Content-Type":"application/json"};if(t&&(e.Authorization=`Bearer ${t}`),!!s)try{let n;if(xe(s?.version||"2.0.0","3.30")<0)n=await this.fetch(pt,{method:"POST",body:JSON.stringify({serialize:!1,config:JSON.stringify(s)}),headers:e,credentials:"include"});else{const o=Me(s.root,ct);n=await this.fetch(o,{headers:e,credentials:"include"})}if(!n.ok)throw new Error(B);let i=await n.json();return"api"in i&&(i=i.api),i.named_endpoints["/predict"]&&!i.unnamed_endpoints[0]&&(i.unnamed_endpoints[0]=i.named_endpoints["/predict"]),Et(i,s,this.api_map)}catch(n){""+n.message}}async function Ot(t,s,e){var n;const i={};(n=this==null?void 0:this.options)!=null&&n.hf_token&&(i.Authorization=`Bearer ${this.options.hf_token}`);const o=1e3,r=[];let a;for(let u=0;u<s.length;u+=o){const c=s.slice(u,u+o),d=new FormData;c.forEach(p=>{d.append("files",p)});try{const p=e?`${t}/${Re}?upload_id=${e}`:`${t}/${Re}`;a=await this.fetch(p,{method:"POST",body:d,headers:i,credentials:"include"})}catch(p){throw new Error(B+p.message)}if(!a.ok){const p=await a.text();return{error:`HTTP ${a.status}: ${p}`}}const h=await a.json();h&&r.push(...h)}return{files:r}}async function Tt(t,s,e,n){let i=(Array.isArray(t)?t:[t]).map(r=>r.blob);const o=i.filter(r=>r.size>(n??1/0));if(o.length)throw new Error(`File size exceeds the maximum allowed size of ${n} bytes: ${o.map(r=>r.name).join(", ")}`);return await Promise.all(await this.upload_files(s,i,e).then(async r=>{if(r.error)throw new Error(r.error);return r.files?r.files.map((a,u)=>new se({...t[u],path:a,url:s+"/file="+a})):[]}))}async function rs(t,s){return t.map(e=>new se({path:e.name,orig_name:e.name,blob:e,size:e.size,mime_type:e.type,is_stream:s}))}class se{constructor({path:s,url:e,orig_name:n,size:i,blob:o,is_stream:r,mime_type:a,alt_text:u}){_(this,"path"),_(this,"url"),_(this,"orig_name"),_(this,"size"),_(this,"blob"),_(this,"is_stream"),_(this,"mime_type"),_(this,"alt_text"),_(this,"meta",{_type:"gradio.FileData"}),this.path=s,this.url=e,this.orig_name=n,this.size=i,this.blob=e?void 0:o,this.is_stream=r,this.mime_type=a,this.alt_text=u}}class At{constructor(s,e){_(this,"type"),_(this,"command"),_(this,"meta"),_(this,"fileData"),this.type="command",this.command=s,this.meta=e}}typeof process<"u"&&process.versions&&process.versions.node;function Te(t,s,e){for(;e.length>1;){const i=e.shift();if(typeof i=="string"||typeof i=="number")t=t[i];else throw new Error("Invalid key type")}const n=e.shift();if(typeof n=="string"||typeof n=="number")t[n]=s;else throw new Error("Invalid key type")}async function _e(t,s=void 0,e=[],n=!1,i=void 0){if(Array.isArray(t)){let o=[];return await Promise.all(t.map(async(r,a)=>{var u;let c=e.slice();c.push(String(a));const d=await _e(t[a],n?((u=i?.parameters[a])==null?void 0:u.component)||void 0:s,c,!1,i);o=o.concat(d)})),o}else{if(globalThis.Buffer&&t instanceof globalThis.Buffer||t instanceof Blob)return[{path:e,blob:new Blob([t]),type:s}];if(typeof t=="object"&&t!==null){let o=[];for(const r of Object.keys(t)){const a=[...e,r],u=t[r];o=o.concat(await _e(u,void 0,a,!1,i))}return o}}return[]}function Nt(t,s){var e,n;let i=(n=(e=s?.dependencies)==null?void 0:e.find(o=>o.id==t))==null?void 0:n.queue;return i!=null?!i:!s.enable_queue}function Pt(t,s){return new Promise((e,n)=>{const i=new MessageChannel;i.port1.onmessage=({data:o})=>{i.port1.close(),e(o)},window.parent.postMessage(t,s,[i.port2])})}function Z(t,s,e,n,i=!1){if(n==="input"&&!i)throw new Error("Invalid code path. Cannot skip state inputs for input.");if(n==="output"&&i)return t;let o=[],r=0;for(let a=0;a<s.inputs.length;a++){const u=s.inputs[a],c=e.find(d=>d.id===u);if(c?.type==="state"){if(i)if(t.length===s.inputs.length){const d=t[r];o.push(d),r++}else o.push(null);else{r++;continue}continue}else{const d=t[r];o.push(d),r++}}return o}async function Dt(t,s,e){const n=this;await Lt(n,s);const i=await _e(s,void 0,[],!0,e);return(await Promise.all(i.map(async({path:r,blob:a,type:u})=>{if(!a)return{path:r,type:u};const c=await n.upload_files(t,[a]),d=c.files&&c.files[0];return{path:r,file_url:d,type:u,name:a instanceof File?a?.name:void 0}}))).forEach(({path:r,file_url:a,type:u,name:c})=>{if(u==="Gallery")Te(s,a,r);else if(a){const d=new se({path:a,orig_name:c});Te(s,d,r)}}),s}async function Lt(t,s){var e,n;if(!(((e=t.config)==null?void 0:e.root)||((n=t.config)==null?void 0:n.root_url)))throw new Error(je);await Ge(t,s)}async function Ge(t,s,e=[]){for(const n in s)s[n]instanceof At?await xt(t,s,n):typeof s[n]=="object"&&s[n]!==null&&await Ge(t,s[n],[...e,n])}async function xt(t,s,e){var n,i;let o=s[e];const r=((n=t.config)==null?void 0:n.root)||((i=t.config)==null?void 0:i.root_url);if(!r)throw new Error(je);try{let a,u;if(typeof process<"u"&&process.versions&&process.versions.node){const p=await X(()=>import("./__vite-browser-external-DFe-p4yY.js").then(b=>b._),[],import.meta.url);u=(await X(()=>import("./__vite-browser-external-DFe-p4yY.js").then(b=>b._),[],import.meta.url)).resolve(process.cwd(),o.meta.path),a=await p.readFile(u)}else throw new Error(gt);const c=new Blob([a],{type:"application/octet-stream"}),d=await t.upload_files(r,[c]),h=d.files&&d.files[0];if(h){const p=new se({path:h,orig_name:o.meta.name||""});s[e]=p}}catch(a){console.error(wt,a)}}async function It(t,s,e){const n={"Content-Type":"application/json"};this.options.hf_token&&(n.Authorization=`Bearer ${this.options.hf_token}`);try{var i=await this.fetch(t,{method:"POST",body:JSON.stringify(s),headers:{...n,...e},credentials:"include"})}catch{return[{error:B},500]}let o,r;try{o=await i.json(),r=i.status}catch(a){o={error:`Could not parse server response: ${a}`},r=500}return[o,r]}async function qt(t,s){let e=!1,n=!1,i;if(!this.config)throw new Error("Could not resolve app config");if(typeof t=="number")i=this.config.dependencies.find(o=>o.id==t);else{const o=t.replace(/^\//,"");i=this.config.dependencies.find(r=>r.id==this.api_map[o])}if(i?.types.continuous)throw new Error("Cannot call predict on this function as it may run forever. Use submit instead");return new Promise(async(o,r)=>{const a=this.submit(t,s,null,null,!0);let u;for await(const c of a)c.type==="data"&&(n&&o(u),e=!0,u=c),c.type==="status"&&(c.stage==="error"&&r(c),c.stage==="complete"&&(n=!0,e&&o(u)))})}async function me(t,s,e){let n=s==="subdomain"?`https://huggingface.co/api/spaces/by-subdomain/${t}`:`https://huggingface.co/api/spaces/${t}`,i,o;try{if(i=await fetch(n),o=i.status,o!==200)throw new Error;i=await i.json()}catch{e({status:"error",load_status:"error",message:ht,detail:"NOT_FOUND"});return}if(!i||o!==200)return;const{runtime:{stage:r},id:a}=i;switch(r){case"STOPPED":case"SLEEPING":e({status:"sleeping",load_status:"pending",message:"Space is asleep. Waking it up...",detail:r}),setTimeout(()=>{me(t,s,e)},1e3);break;case"PAUSED":e({status:"paused",load_status:"error",message:"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.",detail:r,discussions_enabled:await Ae(a)});break;case"RUNNING":case"RUNNING_BUILDING":e({status:"running",load_status:"complete",message:"",detail:r});break;case"BUILDING":e({status:"building",load_status:"pending",message:"Space is building...",detail:r}),setTimeout(()=>{me(t,s,e)},1e3);break;default:e({status:"space_error",load_status:"error",message:"This space is experiencing an issue.",detail:r,discussions_enabled:await Ae(a)});break}}const jt=/^(?=[^]*\b[dD]iscussions{0,1}\b)(?=[^]*\b[dD]isabled\b)[^]*$/;async function Ae(t){try{const s=await fetch(`https://huggingface.co/api/spaces/${t}/discussions`,{method:"HEAD"}),e=s.headers.get("x-error-message");return!(!s.ok||e&&jt.test(e))}catch{return!1}}async function Ut(t,s){const e={};s&&(e.Authorization=`Bearer ${s}`);try{const n=await fetch(`https://huggingface.co/api/spaces/${t}/${lt}`,{headers:e});if(n.status!==200)throw new Error("Space hardware could not be obtained.");const{hardware:i}=await n.json();return i.current}catch(n){throw new Error(n.message)}}async function zt(t,s,e){const n={};e&&(n.Authorization=`Bearer ${e}`);const i={seconds:s};try{const o=await fetch(`https://huggingface.co/api/spaces/${t}/${ut}`,{method:"POST",headers:{"Content-Type":"application/json",...n},body:JSON.stringify(i)});if(o.status!==200)throw new Error("Could not set sleep timeout on duplicated Space. Please visit *ADD HF LINK TO SETTINGS* to set a timeout manually to reduce billing charges.");return await o.json()}catch(o){throw new Error(o.message)}}const Ne=["cpu-basic","cpu-upgrade","cpu-xl","t4-small","t4-medium","a10g-small","a10g-large","a10g-largex2","a10g-largex4","a100-large","zero-a10g","h100","h100x8"];async function Ft(t,s){const{hf_token:e,private:n,hardware:i,timeout:o,auth:r}=s;if(i&&!Ne.includes(i))throw new Error(`Invalid hardware type provided. Valid types are: ${Ne.map(m=>`"${m}"`).join(",")}.`);const{http_protocol:a,host:u}=await te(t,e);let c=null;if(r){const m=await ze(a,u,r,fetch);m&&(c=Fe(m))}const d={Authorization:`Bearer ${e}`,"Content-Type":"application/json",...c?{Cookie:c.join("; ")}:{}},h=(await(await fetch("https://huggingface.co/api/whoami-v2",{headers:d})).json()).name,p=t.split("/")[1],y={repository:`${h}/${p}`};n&&(y.private=!0);let b;try{i||(b=await Ut(t,e))}catch(m){throw Error(ve+m.message)}const l=i||b||"cpu-basic";y.hardware=l;try{const m=await fetch(`https://huggingface.co/api/spaces/${t}/duplicate`,{method:"POST",headers:d,body:JSON.stringify(y)});if(m.status===409)try{return await ee.connect(`${h}/${p}`,s)}catch(T){throw console.error("Failed to connect Client instance:",T),T}else if(m.status!==200)throw new Error(m.statusText);const E=await m.json();return await zt(`${h}/${p}`,o||300,e),await ee.connect(Bt(E.url),s)}catch(m){throw new Error(m)}}function Bt(t){const s=/https:\/\/huggingface.co\/spaces\/([^/]+\/[^/]+)/,e=t.match(s);if(e)return e[1]}class Mt extends TransformStream{constructor(s={allowCR:!1}){super({transform:(e,n)=>{for(e=K(this,z)+e;;){const i=e.indexOf(`
`),o=s.allowCR?e.indexOf("\r"):-1;if(o!==-1&&o!==e.length-1&&(i===-1||i-1>o)){n.enqueue(e.slice(0,o)),e=e.slice(o+1);continue}if(i===-1)break;const r=e[i-1]==="\r"?i-1:i;n.enqueue(e.slice(0,r)),e=e.slice(i+1)}ot(this,z,e)},flush:e=>{if(K(this,z)==="")return;const n=s.allowCR&&K(this,z).endsWith("\r")?K(this,z).slice(0,-1):K(this,z);e.enqueue(n)}}),nt(this,z,"")}}z=new WeakMap;function Gt(t){let s=new TextDecoderStream,e=new Mt({allowCR:!0});return t.pipeThrough(s).pipeThrough(e)}function Jt(t){let e=/[:]\s*/.exec(t),n=e&&e.index;if(n)return[t.substring(0,n),t.substring(n+e[0].length)]}function Pe(t,s,e){t.get(s)||t.set(s,e)}async function*Wt(t,s){if(!t.body)return;let e=Gt(t.body),n,i=e.getReader(),o;for(;;){if(s&&s.aborted)return i.cancel();if(n=await i.read(),n.done)return;if(!n.value){o&&(yield o),o=void 0;continue}let[r,a]=Jt(n.value)||[];r&&(r==="data"?(o||(o={}),o[r]=o[r]?o[r]+`
`+a:a):r==="event"?(o||(o={}),o[r]=a):r==="id"?(o||(o={}),o[r]=+a||a):r==="retry"&&(o||(o={}),o[r]=+a||void 0))}}async function Ht(t,s){let e=new Request(t,s);Pe(e.headers,"Accept","text/event-stream"),Pe(e.headers,"Content-Type","application/json");let n=await fetch(e);if(!n.ok)throw n;return Wt(n,e.signal)}async function Vt(){let{event_callbacks:t,unclosed_events:s,pending_stream_messages:e,stream_status:n,config:i,jwt:o}=this;const r=this;if(!i)throw new Error("Could not resolve app config");n.open=!0;let a=null,u=new URLSearchParams({session_hash:this.session_hash}).toString(),c=new URL(`${i.root}/queue/data?${u}`);if(o&&c.searchParams.set("__sign",o),a=this.stream(c),!a){console.warn("Cannot connect to SSE endpoint: "+c.toString());return}a.onmessage=async function(d){let h=JSON.parse(d.data);if(h.msg==="close_stream"){ge(n,r.abort_controller);return}const p=h.event_id;if(!p)await Promise.all(Object.keys(t).map(y=>t[y](h)));else if(t[p]&&i){h.msg==="process_completed"&&["sse","sse_v1","sse_v2","sse_v2.1","sse_v3"].includes(i.protocol)&&s.delete(p);let y=t[p];typeof window<"u"&&typeof document<"u"?setTimeout(y,0,h):y(h)}else e[p]||(e[p]=[]),e[p].push(h)},a.onerror=async function(){await Promise.all(Object.keys(t).map(d=>t[d]({msg:"unexpected_error",message:B})))}}function ge(t,s){t&&(t.open=!1,s?.abort())}function Kt(t,s,e){!t[s]?(t[s]=[],e.data.forEach((i,o)=>{t[s][o]=i})):e.data.forEach((i,o)=>{let r=Zt(t[s][o],i);t[s][o]=r,e.data[o]=r})}function Zt(t,s){return s.forEach(([e,n,i])=>{t=Yt(t,n,e,i)}),t}function Yt(t,s,e,n){if(s.length===0){if(e==="replace")return n;if(e==="append")return t+n;throw new Error(`Unsupported action: ${e}`)}let i=t;for(let r=0;r<s.length-1;r++)i=i[s[r]];const o=s[s.length-1];switch(e){case"replace":i[o]=n;break;case"append":i[o]+=n;break;case"add":Array.isArray(i)?i.splice(Number(o),0,n):i[o]=n;break;case"delete":Array.isArray(i)?i.splice(Number(o),1):delete i[o];break;default:throw new Error(`Unknown action: ${e}`)}return t}function Qt(t,s={}){const e={close:()=>{throw new Error("Method not implemented.")},onerror:null,onmessage:null,onopen:null,readyState:0,url:t.toString(),withCredentials:!1,CONNECTING:0,OPEN:1,CLOSED:2,addEventListener:()=>{throw new Error("Method not implemented.")},dispatchEvent:()=>{throw new Error("Method not implemented.")},removeEventListener:()=>{throw new Error("Method not implemented.")}};return Ht(t,s).then(async n=>{e.readyState=e.OPEN;try{for await(const i of n)e.onmessage&&e.onmessage(i);e.readyState=e.CLOSED}catch(i){e.onerror&&e.onerror(i),e.readyState=e.CLOSED}}).catch(n=>{console.error(n),e.onerror&&e.onerror(n),e.readyState=e.CLOSED}),e}function Xt(t,s,e,n,i){var o;try{let r=function(w){(i||Ze[w.type])&&d(w)},a=function(){for(ce=!0;V.length>0;)V.shift()({value:void 0,done:!0})},u=function(w){ce||(V.length>0?V.shift()(w):le.push(w))},c=function(w){u(es(w)),a()},d=function(w){u({value:w,done:!1})},h=function(){return le.length>0?Promise.resolve(le.shift()):ce?Promise.resolve({value:void 0,done:!0}):new Promise(w=>V.push(w))};const{hf_token:p}=this.options,{fetch:y,app_reference:b,config:l,session_hash:m,api_info:E,api_map:T,stream_status:Y,pending_stream_messages:ne,pending_diff_streams:oe,event_callbacks:ie,unclosed_events:He,post_data:re,options:M}=this,be=this;if(!E)throw new Error("No API found");if(!l)throw new Error("Could not resolve app config");let{fn_index:f,endpoint_info:Ve,dependency:G}=ts(E,t,T,l),Ke=Ct(s,E),k,L,x=l.protocol??"ws";const g=typeof t=="number"?"/predict":t;let H,$=null,C=!1,ae={},F=typeof window<"u"&&typeof document<"u"?new URLSearchParams(window.location.search).toString():"";const Ze=((o=M?.events)==null?void 0:o.reduce((w,A)=>(w[A]=!0,w),{}))||{};async function Ye(){const w={stage:"complete",queue:!1,time:new Date};C=w,r({...w,type:"status",endpoint:g,fn_index:f});let A={},J={};x==="ws"?(k&&k.readyState===0?k.addEventListener("open",()=>{k.close()}):k.close(),A={fn_index:f,session_hash:m}):(ge(Y,be.abort_controller),a(),A={event_id:$},J={event_id:$,session_hash:m,fn_index:f});try{if(!l)throw new Error("Could not resolve app config");"event_id"in J&&await y(`${l.root}/cancel`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(J)}),await y(`${l.root}/reset`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(A)})}catch{console.warn("The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.")}}const Qe=async w=>{await this._resolve_hearbeat(w)};async function Se(w){if(!l)return;let A=w.render_id;l.components=[...l.components.filter(v=>v.props.rendered_in!==A),...w.components],l.dependencies=[...l.dependencies.filter(v=>v.rendered_in!==A),...w.dependencies];const J=l.components.some(v=>v.type==="state"),Q=l.dependencies.some(v=>v.targets.some(q=>q[1]==="unload"));l.connect_heartbeat=J||Q,await Qe(l),r({type:"render",data:w,endpoint:g,fn_index:f})}this.handle_blob(l.root,Ke,Ve).then(async w=>{var A;if(H={data:Z(w,G,l.components,"input",!0)||[],event_data:e,fn_index:f,trigger_id:n},Nt(f,l))r({type:"status",endpoint:g,stage:"pending",queue:!1,fn_index:f,time:new Date}),re(`${l.root}/run${g.startsWith("/")?g:`/${g}`}${F?"?"+F:""}`,{...H,session_hash:m}).then(([v,q])=>{const I=v.data;q==200?(r({type:"data",endpoint:g,fn_index:f,data:Z(I,G,l.components,"output",M.with_null_state),time:new Date,event_data:e,trigger_id:n}),v.render_config&&Se(v.render_config),r({type:"status",endpoint:g,fn_index:f,stage:"complete",eta:v.average_duration,queue:!1,time:new Date})):r({type:"status",stage:"error",endpoint:g,fn_index:f,message:v.error,queue:!1,time:new Date})}).catch(v=>{r({type:"status",stage:"error",message:v.message,endpoint:g,fn_index:f,queue:!1,time:new Date})});else if(x=="ws"){const{ws_protocol:v,host:q}=await te(b,p);r({type:"status",stage:"pending",queue:!0,endpoint:g,fn_index:f,time:new Date});let I=new URL(`${v}://${Ue(q,l.path,!0)}/queue/join${F?"?"+F:""}`);this.jwt&&I.searchParams.set("__sign",this.jwt),k=new WebSocket(I),k.onclose=N=>{N.wasClean||r({type:"status",stage:"error",broken:!0,message:B,queue:!0,endpoint:g,fn_index:f,time:new Date})},k.onmessage=function(N){const R=JSON.parse(N.data),{type:O,status:P,data:D}=fe(R,ae[f]);if(O==="update"&&P&&!C)r({type:"status",endpoint:g,fn_index:f,time:new Date,...P}),P.stage==="error"&&k.close();else if(O==="hash"){k.send(JSON.stringify({fn_index:f,session_hash:m}));return}else O==="data"?k.send(JSON.stringify({...H,session_hash:m})):O==="complete"?C=P:O==="log"?r({type:"log",log:D.log,level:D.level,endpoint:g,fn_index:f}):O==="generating"&&r({type:"status",time:new Date,...P,stage:P?.stage,queue:!0,endpoint:g,fn_index:f});D&&(r({type:"data",time:new Date,data:Z(D.data,G,l.components,"output",M.with_null_state),endpoint:g,fn_index:f,event_data:e,trigger_id:n}),C&&(r({type:"status",time:new Date,...C,stage:P?.stage,queue:!0,endpoint:g,fn_index:f}),k.close()))},xe(l.version||"2.0.0","3.6")<0&&addEventListener("open",()=>k.send(JSON.stringify({hash:m})))}else if(x=="sse"){r({type:"status",stage:"pending",queue:!0,endpoint:g,fn_index:f,time:new Date});var Q=new URLSearchParams({fn_index:f.toString(),session_hash:m}).toString();let v=new URL(`${l.root}/queue/join?${F?F+"&":""}${Q}`);if(this.jwt&&v.searchParams.set("__sign",this.jwt),L=this.stream(v),!L)return Promise.reject(new Error("Cannot connect to SSE endpoint: "+v.toString()));L.onmessage=async function(q){const I=JSON.parse(q.data),{type:N,status:R,data:O}=fe(I,ae[f]);if(N==="update"&&R&&!C)r({type:"status",endpoint:g,fn_index:f,time:new Date,...R}),R.stage==="error"&&(L?.close(),a());else if(N==="data"){$=I.event_id;let[P,D]=await re(`${l.root}/queue/data`,{...H,session_hash:m,event_id:$});D!==200&&(r({type:"status",stage:"error",message:B,queue:!0,endpoint:g,fn_index:f,time:new Date}),L?.close(),a())}else N==="complete"?C=R:N==="log"?r({type:"log",log:O.log,level:O.level,endpoint:g,fn_index:f}):N==="generating"&&r({type:"status",time:new Date,...R,stage:R?.stage,queue:!0,endpoint:g,fn_index:f});O&&(r({type:"data",time:new Date,data:Z(O.data,G,l.components,"output",M.with_null_state),endpoint:g,fn_index:f,event_data:e,trigger_id:n}),C&&(r({type:"status",time:new Date,...C,stage:R?.stage,queue:!0,endpoint:g,fn_index:f}),L?.close(),a()))}}else if(x=="sse_v1"||x=="sse_v2"||x=="sse_v2.1"||x=="sse_v3"){r({type:"status",stage:"pending",queue:!0,endpoint:g,fn_index:f,time:new Date});let v="";typeof window<"u"&&typeof document<"u"&&(v=(A=window?.location)==null?void 0:A.hostname);const I=v.includes(".dev.")?`https://moon-${v.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co",N=typeof window<"u"&&typeof document<"u"&&window.parent!=window,R=G.zerogpu&&l.space_id;(N&&R?Pt("zerogpu-headers",I):Promise.resolve(null)).then(D=>re(`${l.root}/queue/join?${F}`,{...H,session_hash:m},D)).then(async([D,$e])=>{if($e===503)r({type:"status",stage:"error",message:Ie,queue:!0,endpoint:g,fn_index:f,time:new Date});else if($e!==200)r({type:"status",stage:"error",message:B,queue:!0,endpoint:g,fn_index:f,time:new Date});else{$=D.event_id;let ke=async function(ue){try{const{type:j,status:S,data:U}=fe(ue,ae[f]);if(j=="heartbeat")return;if(j==="update"&&S&&!C)r({type:"status",endpoint:g,fn_index:f,time:new Date,...S});else if(j==="complete")C=S;else if(j=="unexpected_error")console.error("Unexpected error",S?.message),r({type:"status",stage:"error",message:S?.message||"An Unexpected Error Occurred!",queue:!0,endpoint:g,fn_index:f,time:new Date});else if(j==="log"){r({type:"log",log:U.log,level:U.level,endpoint:g,fn_index:f});return}else j==="generating"&&(r({type:"status",time:new Date,...S,stage:S?.stage,queue:!0,endpoint:g,fn_index:f}),U&&["sse_v2","sse_v2.1","sse_v3"].includes(x)&&Kt(oe,$,U));U&&(r({type:"data",time:new Date,data:Z(U.data,G,l.components,"output",M.with_null_state),endpoint:g,fn_index:f}),U.render_config&&await Se(U.render_config),C&&r({type:"status",time:new Date,...C,stage:S?.stage,queue:!0,endpoint:g,fn_index:f})),(S?.stage==="complete"||S?.stage==="error")&&(ie[$]&&delete ie[$],$ in oe&&delete oe[$])}catch(j){console.error("Unexpected client exception",j),r({type:"status",stage:"error",message:"An Unexpected Error Occurred!",queue:!0,endpoint:g,fn_index:f,time:new Date}),["sse_v2","sse_v2.1","sse_v3"].includes(x)&&(ge(Y,be.abort_controller),Y.open=!1,a())}};$ in ne&&(ne[$].forEach(ue=>ke(ue)),delete ne[$]),ie[$]=ke,He.add($),Y.open||await this.open_stream()}})}});let ce=!1;const le=[],V=[],Ee={[Symbol.asyncIterator]:()=>Ee,next:h,throw:async w=>(c(w),h()),return:async()=>(a(),h()),cancel:Ye};return Ee}catch(r){throw console.error("Submit function encountered an error:",r),r}}function es(t){return{then:(s,e)=>e(t)}}function ts(t,s,e,n){let i,o,r;if(typeof s=="number")i=s,o=t.unnamed_endpoints[i],r=n.dependencies.find(a=>a.id==s);else{const a=s.replace(/^\//,"");i=e[a],o=t.named_endpoints[s.trim()],r=n.dependencies.find(u=>u.id==e[a])}if(typeof i!="number")throw new Error("There is no endpoint matching that name of fn_index matching that number.");return{fn_index:i,endpoint_info:o,dependency:r}}class ee{constructor(s,e={events:["data"]}){_(this,"app_reference"),_(this,"options"),_(this,"config"),_(this,"api_info"),_(this,"api_map",{}),_(this,"session_hash",Math.random().toString(36).substring(2)),_(this,"jwt",!1),_(this,"last_status",{}),_(this,"cookies",null),_(this,"stream_status",{open:!1}),_(this,"pending_stream_messages",{}),_(this,"pending_diff_streams",{}),_(this,"event_callbacks",{}),_(this,"unclosed_events",new Set),_(this,"heartbeat_event",null),_(this,"abort_controller",null),_(this,"stream_instance",null),_(this,"view_api"),_(this,"upload_files"),_(this,"upload"),_(this,"handle_blob"),_(this,"post_data"),_(this,"submit"),_(this,"predict"),_(this,"open_stream"),_(this,"resolve_config"),_(this,"resolve_cookies"),this.app_reference=s,e.events||(e.events=["data"]),this.options=e,this.view_api=Rt.bind(this),this.upload_files=Ot.bind(this),this.handle_blob=Dt.bind(this),this.post_data=It.bind(this),this.submit=Xt.bind(this),this.predict=qt.bind(this),this.open_stream=Vt.bind(this),this.resolve_config=vt.bind(this),this.resolve_cookies=bt.bind(this),this.upload=Tt.bind(this)}fetch(s,e){const n=new Headers(e?.headers||{});return this&&this.cookies&&n.append("Cookie",this.cookies),fetch(s,{...e,headers:n})}stream(s){return this.abort_controller=new AbortController,this.stream_instance=Qt(s.toString(),{signal:this.abort_controller.signal}),this.stream_instance}async init(){var s;if((typeof window>"u"||!("WebSocket"in window))&&!global.WebSocket){const e=await X(()=>import("./wrapper-CviSselG-DCvi549i.js"),__vite__mapDeps([0,1]),import.meta.url);global.WebSocket=e.WebSocket}try{this.options.auth&&await this.resolve_cookies(),await this._resolve_config().then(({config:e})=>this._resolve_hearbeat(e))}catch(e){throw Error(e)}this.api_info=await this.view_api(),this.api_map=yt(((s=this.config)==null?void 0:s.dependencies)||[])}async _resolve_hearbeat(s){if(s&&(this.config=s,this.config&&this.config.connect_heartbeat&&this.config.space_id&&this.options.hf_token&&(this.jwt=await Oe(this.config.space_id,this.options.hf_token,this.cookies))),s.space_id&&this.options.hf_token&&(this.jwt=await Oe(s.space_id,this.options.hf_token)),this.config&&this.config.connect_heartbeat){const e=new URL(`${this.config.root}/heartbeat/${this.session_hash}`);this.jwt&&e.searchParams.set("__sign",this.jwt),this.heartbeat_event||(this.heartbeat_event=this.stream(e))}}static async connect(s,e={events:["data"]}){const n=new this(s,e);return await n.init(),n}close(){var s;(s=this.heartbeat_event)==null||s.close()}static async duplicate(s,e={events:["data"]}){return Ft(s,e)}async _resolve_config(){const{http_protocol:s,host:e,space_id:n}=await te(this.app_reference,this.options.hf_token),{status_callback:i}=this.options;let o;try{if(o=await this.resolve_config(`${s}//${e}`),!o)throw new Error(W);return this.config_success(o)}catch(r){if(n&&i)me(n,Be.test(n)?"space_name":"subdomain",this.handle_space_success);else throw i&&i({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),Error(r)}}async config_success(s){if(this.config=s,typeof window<"u"&&typeof document<"u"&&window.location.protocol==="https:"&&(this.config.root=this.config.root.replace("http://","https://")),this.config.auth_required)return this.prepare_return_obj();try{this.api_info=await this.view_api()}catch(e){console.error(dt+e.message)}return this.prepare_return_obj()}async handle_space_success(s){if(!this)throw new Error(W);const{status_callback:e}=this.options;if(e&&e(s),s.status==="running")try{if(this.config=await this._resolve_config(),!this.config)throw new Error(W);return await this.config_success(this.config)}catch(n){throw e&&e({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),n}}async component_server(s,e,n){var i;if(!this.config)throw new Error(W);const o={},{hf_token:r}=this.options,{session_hash:a}=this;r&&(o.Authorization=`Bearer ${this.options.hf_token}`);let u,c=this.config.components.find(h=>h.id===s);(i=c?.props)!=null&&i.root_url?u=c.props.root_url:u=this.config.root;let d;if("binary"in n){d=new FormData;for(const h in n.data)h!=="binary"&&d.append(h,n.data[h]);d.set("component_id",s.toString()),d.set("fn_name",e),d.set("session_hash",a)}else d=JSON.stringify({data:n,component_id:s,fn_name:e,session_hash:a}),o["Content-Type"]="application/json";r&&(o.Authorization=`Bearer ${r}`);try{const h=await this.fetch(`${u}/component_server/`,{method:"POST",body:d,headers:o,credentials:"include"});if(!h.ok)throw new Error("Could not connect to component server: "+h.statusText);return await h.json()}catch(h){console.warn(h)}}set_cookies(s){this.cookies=Fe(s).join("; ")}prepare_return_obj(){return{config:this.config,predict:this.predict,submit:this.submit,view_api:this.view_api,component_server:this.component_server}}}let Je=!1;"attachShadow"in Element.prototype&&"adoptedStyleSheets"in Document.prototype&&(Je="adoptedStyleSheets"in document.createElement("div").attachShadow({mode:"open"}));function De(t,s){const e=new URL(import.meta.url).origin,n=new URL(t,e).href;if(document.querySelector(`link[href='${n}']`))return Promise.resolve();const o=document.createElement("link");return o.rel="stylesheet",o.href=n,new Promise((r,a)=>{o.addEventListener("load",()=>r()),o.addEventListener("error",()=>{console.error(`Unable to preload CSS for ${n}`),r()}),s.appendChild(o)})}function as(t,s,e=document.createElement("style")){if(!Je)return null;e.remove();const n=new CSSStyleSheet;n.replaceSync(t);let i="";t=t.replace(/@import\s+url\((.*?)\);\s*/g,(u,c)=>(i+=`@import url(${c});
`,""));const o=n.cssRules;let r="",a=`gradio-app .gradio-container.gradio-container-${s} .contain `;for(let u=0;u<o.length;u++){const c=o[u];let d=c.cssText.includes(".dark");if(c instanceof CSSStyleRule){const h=c.selectorText;if(h){const p=h.replace(".dark","").split(",").map(y=>`${d?".dark":""} ${a} ${y.trim()} `).join(",");r+=c.cssText,r+=c.cssText.replace(h,p)}}else if(c instanceof CSSMediaRule){let h=`@media ${c.media.mediaText} {`;for(let p=0;p<c.cssRules.length;p++){const y=c.cssRules[p];if(y instanceof CSSStyleRule){let b=y.cssText.includes(".dark ");const l=y.selectorText,m=l.replace(".dark","").split(",").map(E=>`${b?".dark":""} ${a} ${E.trim()} `).join(",");h+=y.cssText.replace(l,m)}}h+="}",r+=h}else if(c instanceof CSSKeyframesRule){r+=`@keyframes ${c.name} {`;for(let h=0;h<c.cssRules.length;h++){const p=c.cssRules[h];p instanceof CSSKeyframeRule&&(r+=`${p.keyText} { ${p.style.cssText} }`)}r+="}"}else c instanceof CSSFontFaceRule&&(r+=`@font-face { ${c.style.cssText} }`)}return r=i+r,e.textContent=r,document.head.appendChild(e),e}const ss="./assets/index-luc1OtuK.css";let we;we=[];let ye,We,ns=new Promise(t=>{We=t});async function os(){ye=(await X(()=>import("./Index-WGC0_FkS.js").then(t=>t.k),__vite__mapDeps([2,3]),import.meta.url)).default,We()}function is(){const t={SvelteComponent:pe.SvelteComponent};for(const e in pe)e!=="SvelteComponent"&&(e==="SvelteComponentDev"?t[e]=t.SvelteComponent:t[e]=pe[e]);window.__gradio__svelte__internal=t;class s extends HTMLElement{constructor(){super(),this.host=this.getAttribute("host"),this.space=this.getAttribute("space"),this.src=this.getAttribute("src"),this.control_page_title=this.getAttribute("control_page_title"),this.initial_height=this.getAttribute("initial_height")??"300px",this.is_embed=this.getAttribute("embed")??"true",this.container=this.getAttribute("container")??"true",this.info=this.getAttribute("info")??!0,this.autoscroll=this.getAttribute("autoscroll"),this.eager=this.getAttribute("eager"),this.theme_mode=this.getAttribute("theme_mode"),this.updating=!1,this.loading=!1}async connectedCallback(){await os(),this.loading=!0,this.app&&this.app.$destroy(),typeof we!="string"&&we.forEach(o=>De(o,document.head)),await De(ss,document.head);const n=new CustomEvent("domchange",{bubbles:!0,cancelable:!1,composed:!0});new MutationObserver(o=>{this.dispatchEvent(n)}).observe(this,{childList:!0}),this.app=new ye({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"4-36-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:ee,app_mode:window.__gradio_mode__==="app"}}),this.updating&&this.setAttribute(this.updating.name,this.updating.value),this.loading=!1}static get observedAttributes(){return["src","space","host"]}async attributeChangedCallback(n,i,o){if(await ns,(n==="host"||n==="space"||n==="src")&&o!==i){if(this.updating={name:n,value:o},this.loading)return;this.app&&this.app.$destroy(),this.space=null,this.host=null,this.src=null,n==="host"?this.host=o:n==="space"?this.space=o:n==="src"&&(this.src=o),this.app=new ye({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"4-36-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:ee,app_mode:window.__gradio_mode__==="app"}}),this.updating=!1}}}customElements.get("gradio-app")||customElements.define("gradio-app",s)}is();export{X as _,as as a,De as m,rs as p};
//# sourceMappingURL=index-COY1HN2y.js.map
