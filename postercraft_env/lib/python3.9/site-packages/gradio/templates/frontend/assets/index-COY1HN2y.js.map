{"version": 3, "mappings": ";suDAAA,IAAIA,GAAY,OAAO,eACnBC,GAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMF,GAAUE,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAO,GAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAgB,CAACH,EAAKC,EAAKC,KAC7BH,GAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELE,GAAgB,CAACJ,EAAKK,EAAQC,IAAQ,CACxC,GAAI,CAACD,EAAO,IAAIL,CAAG,EACjB,MAAM,UAAU,UAAYM,CAAG,CACnC,EACIC,EAAe,CAACP,EAAKK,EAAQG,KAC/BJ,GAAcJ,EAAKK,EAAQ,yBAAyB,EAC7CG,EAASA,EAAO,KAAKR,CAAG,EAAIK,EAAO,IAAIL,CAAG,GAE/CS,GAAe,CAACT,EAAKK,EAAQH,IAAU,CACzC,GAAIG,EAAO,IAAIL,CAAG,EAChB,MAAM,UAAU,mDAAmD,EACrEK,aAAkB,QAAUA,EAAO,IAAIL,CAAG,EAAIK,EAAO,IAAIL,EAAKE,CAAK,CACrE,EACIQ,GAAe,CAACV,EAAKK,EAAQH,EAAOS,KACtCP,GAAcJ,EAAKK,EAAQ,wBAAwB,EAChBA,EAAO,IAAIL,EAAKE,CAAK,EACjDA,GAELU,EACAC,GAAK,IAAI,KAAK,SAAS,EAAG,CAAE,QAAS,EAAG,EAAE,QAC9C,SAASC,GAAQC,EAAGC,EAAGC,EAAM,CAC3B,OAAAF,EAAIA,EAAE,MAAM,GAAG,EACfC,EAAIA,EAAE,MAAM,GAAG,EACRH,GAAGE,EAAE,CAAC,EAAGC,EAAE,CAAC,CAAC,GAAKH,GAAGE,EAAE,CAAC,EAAGC,EAAE,CAAC,CAAC,IAAMA,EAAE,CAAC,EAAIA,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,EAAGC,EAAO,OAAO,KAAKF,EAAE,CAAC,EAAIA,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC,EAAGE,GAAQ,OAAO,KAAKD,EAAE,CAAC,CAAC,EAAIH,GAAGE,EAAE,CAAC,EAAGC,EAAE,CAAC,CAAC,EAAIC,EAAO,GAAK,EACrL,CACA,MAAMC,GAAW,OACXC,GAAa,SACbC,GAAY,QACZC,GAAa,SACbC,GAAe,OACfC,GAAc,UACdC,GAAgB,YAChBC,GAAoB,mDACpBC,GAAiB,yDACjBC,EAAwB,2BACxBC,EAAmB,iCACnBC,GAAyB,+BACzBC,GAAqB,2BACrBC,GAA2B,uCAC3BC,GAAkB,4CAClBC,GAAmB,wCACnBC,GAA0B,yCAC1BC,GAA0B,uDAC1BC,GAAsB,+DACtBC,GAAqB,sCACrBC,GAA4B,uBAClC,SAASC,GAAaC,EAAUC,EAAWC,EAAiB,CAC1D,OAAID,EAAU,WAAW,SAAS,GAAKA,EAAU,WAAW,UAAU,EAC7DC,EAAkBF,EAAWC,EAE/BD,EAAWC,CACpB,CACA,eAAeE,GAAQC,EAAOC,EAAOC,EAAS,CAC5C,GAAI,CAQF,OADa,MANH,MAAM,MAAM,qCAAqCF,CAAK,OAAQ,CACtE,QAAS,CACP,cAAe,UAAUC,CAAK,GAC9B,GAAGC,EAAU,CAAE,OAAQA,CAAS,EAAG,CAAE,CACtC,CACP,CAAK,GACoB,KAAI,GAAI,OACf,EACf,MAAW,CACV,MAAO,EACR,CACH,CACA,SAASC,GAAiBC,EAAK,CAC7B,IAAIC,EAAO,GACX,OAAAD,EAAI,QAAQ,CAAC,CAAE,SAAAE,EAAU,GAAAC,CAAE,IAAO,CAC5BD,IACFD,EAAKC,CAAQ,EAAIC,EACvB,CAAG,EACMF,CACT,CACA,eAAeG,GAAeC,EAAU,CACtC,IAAIC,EACJ,MAAMC,EAAU,KAAK,QAAQ,SAAW,CAAE,cAAe,UAAU,KAAK,QAAQ,QAAQ,EAAE,EAAK,GAE/F,GADAA,EAAQ,cAAc,EAAI,mBACtB,OAAO,OAAW,KAAe,OAAO,eAAiB,SAAS,SAAW,yBAA2B,CAAC,OAAO,cAAc,SAAU,CAC1I,MAAMC,EAAO,OAAO,cAAc,KAC5BC,EAAS,OAAO,cACtB,IAAIC,EAAcnB,GAAac,EAAUI,EAAO,KAAM,EAAK,EAC3D,OAAAA,EAAO,KAAOC,EACP,CAAE,GAAGD,EAAQ,KAAAD,EACrB,SAAUH,EAAU,CACnB,MAAMM,EAAaC,GAAUP,EAAUhC,EAAU,EAC3CwC,EAAW,MAAM,KAAK,MAAMF,EAAY,CAC5C,QAAAJ,EACA,YAAa,SACnB,CAAK,EACD,GAAiCM,GAAS,SAAY,KAAO,CAAC,KAAK,QAAQ,KACzE,MAAM,IAAI,MAAM1B,EAAuB,EAClC,GAAiC0B,GAAS,SAAY,KAAO,KAAK,QAAQ,KAC/E,MAAM,IAAI,MAAM3B,EAAuB,EAEzC,GAAiC2B,GAAS,SAAY,IAAK,CACzD,IAAIJ,EAAS,MAAMI,EAAS,OAC5B,OAAAJ,EAAO,KAAOA,EAAO,MAAQ,GAC7BA,EAAO,KAAOJ,GACbC,EAAKG,EAAO,eAAiB,MAAgBH,EAAG,QAAQ,CAACQ,EAAKC,IAAM,CAC/DD,EAAI,KAAO,SACbA,EAAI,GAAKC,EAEnB,CAAO,EACMN,CACb,SAA4CI,GAAS,SAAY,IAC3D,MAAM,IAAI,MAAM5B,EAAgB,EAElC,MAAM,IAAI,MAAML,CAAgB,CACjC,CACD,MAAM,IAAI,MAAMA,CAAgB,CAClC,CACA,eAAeoC,IAAkB,CAC/B,KAAM,CAAE,cAAAC,EAAe,KAAAC,CAAM,EAAG,MAAMC,GACpC,KAAK,cACL,KAAK,QAAQ,QACjB,EACE,GAAI,CACF,GAAI,KAAK,QAAQ,KAAM,CACrB,MAAMC,EAAgB,MAAMC,GAC1BJ,EACAC,EACA,KAAK,QAAQ,KACb,KAAK,MACL,KAAK,QAAQ,QACrB,EACUE,GACF,KAAK,YAAYA,CAAa,CACjC,CACF,OAAQ,EAAG,CACV,MAAM,MAAM,EAAE,OAAO,CACtB,CACH,CACA,eAAeC,GAAkBJ,EAAeC,EAAMI,EAAMC,EAAQC,EAAU,CAC5E,MAAMC,EAAW,IAAI,SACrBA,EAAS,OAAO,WAAoCH,IAAK,CAAC,CAAC,EAC3DG,EAAS,OAAO,WAAoCH,IAAK,CAAC,CAAC,EAC3D,IAAIf,EAAU,GACViB,IACFjB,EAAQ,cAAgB,UAAUiB,CAAQ,IAE5C,MAAME,EAAM,MAAMH,EAAO,GAAGN,CAAa,KAAKC,CAAI,IAAI9C,EAAS,GAAI,CACjE,QAAAmC,EACA,OAAQ,OACR,KAAMkB,EACN,YAAa,SACjB,CAAG,EACD,GAAIC,EAAI,SAAW,IACjB,OAAOA,EAAI,QAAQ,IAAI,YAAY,EAC9B,MAAIA,EAAI,SAAW,IAClB,IAAI,MAAMxC,EAAuB,EAEjC,IAAI,MAAMH,EAAwB,CAE5C,CACA,SAAS4C,GAAmBtB,EAAU,CACpC,GAAIA,EAAS,WAAW,MAAM,EAAG,CAC/B,KAAM,CAAE,SAAAuB,EAAU,KAAAV,EAAM,SAAAW,CAAU,EAAG,IAAI,IAAIxB,CAAQ,EACrD,OAAIa,EAAK,SAAS,UAAU,EACnB,CACL,YAAa,MACb,KAAAA,EACA,cAAeU,CACvB,EAEW,CACL,YAAaA,IAAa,SAAW,MAAQ,KAC7C,cAAeA,EACf,KAAMV,GAAQW,IAAa,IAAMA,EAAW,GAClD,CACG,SAAUxB,EAAS,WAAW,OAAO,EACpC,MAAO,CACL,YAAa,KACb,cAAe,QACf,KAAM,YAEZ,EAEE,MAAO,CACL,YAAa,MACb,cAAe,SACf,KAAMA,CACV,CACA,CACA,MAAMyB,GAAyBV,GAAkB,CAC/C,IAAItB,EAAU,GAEd,OADcsB,EAAc,MAAM,2BAA2B,EACvD,QAASW,GAAW,CACxB,KAAM,CAACC,EAAaC,CAAY,EAAIF,EAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAC9DC,GAAeC,GACjBnC,EAAQ,KAAK,GAAGkC,EAAY,KAAI,CAAE,IAAIC,EAAa,MAAM,EAAE,CAEjE,CAAG,EACMnC,CACT,EACMoC,GAAgB,yCAChBC,GAAkB,sBACxB,eAAehB,GAAiBiB,EAAeZ,EAAU,CACvD,MAAMjB,EAAU,GACZiB,IACFjB,EAAQ,cAAgB,UAAUiB,CAAQ,IAE5C,MAAMa,EAAiBD,EAAc,KAAI,EAAG,QAAQ,MAAO,EAAE,EAC7D,GAAIF,GAAc,KAAKG,CAAc,EACnC,GAAI,CAKF,MAAMC,GAAS,MAJH,MAAM,MAChB,qCAAqCD,CAAc,IAAInE,EAAQ,GAC/D,CAAE,QAAAqC,CAAS,CACnB,GAC+B,KAAI,GAAI,KACjC,MAAO,CACL,SAAU6B,EACV,GAAGT,GAAmBW,CAAK,CACnC,CACK,MAAW,CACV,MAAM,IAAI,MAAMvD,EAAwB,CACzC,CAEH,GAAIoD,GAAgB,KAAKE,CAAc,EAAG,CACxC,KAAM,CAAE,YAAAE,EAAa,cAAAtB,EAAe,KAAAC,CAAI,EAAKS,GAAmBU,CAAc,EAC9E,MAAO,CACL,SAAUnB,EAAK,QAAQ,YAAa,EAAE,EACtC,YAAAqB,EACA,cAAAtB,EACA,KAAAC,CACN,CACG,CACD,MAAO,CACL,SAAU,GACV,GAAGS,GAAmBU,CAAc,CACxC,CACA,CACA,MAAMzB,GAAY,IAAI4B,IAAS,CAC7B,GAAI,CACF,OAAOA,EAAK,OAAO,CAAChD,EAAUiD,KAC5BjD,EAAWA,EAAS,QAAQ,OAAQ,EAAE,EACtCiD,EAAOA,EAAK,QAAQ,OAAQ,EAAE,EACvB,IAAI,IAAIA,EAAMjD,EAAW,GAAG,EAAE,WACtC,CACF,MAAW,CACV,MAAM,IAAI,MAAMR,EAAe,CAChC,CACH,EACA,SAAS0D,GAAmBC,EAAUlC,EAAQmC,EAAS,CACrD,MAAMC,EAAmB,CACvB,gBAAiB,CAAE,EACnB,kBAAmB,CAAE,CACzB,EACE,cAAO,KAAKF,CAAQ,EAAE,QAASG,GAAa,EACtCA,IAAa,mBAAqBA,IAAa,uBACjDD,EAAiBC,CAAQ,EAAI,GAC7B,OAAO,QAAQH,EAASG,CAAQ,CAAC,EAAE,QACjC,CAAC,CAACzC,EAAU,CAAE,WAAA0C,EAAY,QAAAC,CAAS,KAAM,CACvC,IAAI1C,EAAI2C,EAAIC,EAAIC,EAChB,MAAMC,IAAoB9C,EAAKG,EAAO,aAAa,KAChDK,GAAQA,EAAI,WAAaT,GAAYS,EAAI,WAAaT,EAAS,QAAQ,IAAK,EAAE,CAChF,IAAK,KAAO,OAASC,EAAG,KAAOsC,EAAQvC,EAAS,QAAQ,IAAK,EAAE,CAAC,GAAK,GAChEgD,EAAkBD,IAAoB,IAAMH,EAAKxC,EAAO,aAAa,KAAMK,GAAQA,EAAI,IAAMsC,CAAe,IAAM,KAAO,OAASH,EAAG,MAAQ,CAAE,WAAY,GAAO,UAAW,GAAO,OAAQ,EAAK,EACvM,GAAIG,IAAoB,MAAQD,GAAMD,EAAKzC,EAAO,aAAa,KAAMK,GAAQA,EAAI,IAAMsC,CAAe,IAAM,KAAO,OAASF,EAAG,SAAW,KAAO,OAASC,EAAG,UAAYJ,EAAW,OAAQ,CAC1L,MAAMO,EAAa7C,EAAO,aAAa,KAAMK,GAAQA,EAAI,IAAMsC,CAAe,EAAE,OAAO,IACpFG,GAAU,CACT,IAAIC,EACJ,OAAQA,EAAM/C,EAAO,WAAW,KAAMgD,GAAMA,EAAE,KAAOF,CAAK,IAAM,KAAO,OAASC,EAAI,IACrF,CACf,EACY,GAAI,CACFF,EAAW,QAAQ,CAACI,EAAMC,IAAQ,CAChC,GAAID,IAAS,QAAS,CACpB,MAAME,EAAY,CAChB,UAAW,QACX,QAAS,KACT,kBAAmB,KACnB,sBAAuB,GACvB,eAAgB,KAChB,OAAQ,EAC5B,EACkBb,EAAW,OAAOY,EAAK,EAAGC,CAAS,CACpC,CACjB,CAAe,CACF,OAAQC,EAAG,CACV,QAAQ,MAAMA,CAAC,CAChB,CACF,CACD,MAAMC,EAAiB,CAACC,EAAMC,EAAWC,EAAYC,KAAoB,CACvE,GAAGH,EACH,YAAaI,GAAwCJ,GAAK,KAAME,CAAU,EAC1E,KAAMG,GAAiCL,GAAK,KAAMC,EAAWC,EAAYC,CAAc,GAAK,EACxG,GACUrB,EAAiBC,CAAQ,EAAEzC,CAAQ,EAAI,CACrC,WAAY0C,EAAW,IACpBsB,GAAMP,EAAeO,EAAwBA,GAAE,UAAgCA,GAAE,WAAY,WAAW,CAC1G,EACD,QAASrB,EAAQ,IACdsB,GAAMR,EAAeQ,EAAwBA,GAAE,UAAgCA,GAAE,WAAY,QAAQ,CACvG,EACD,KAAMjB,CAClB,CACS,CACT,EAEA,CAAG,EACMR,CACT,CACA,SAASuB,GAASG,EAAMP,EAAWC,EAAYC,EAAgB,CAC7D,OAAgCK,GAAK,KAAI,CACvC,IAAK,SACH,MAAO,SACT,IAAK,UACH,MAAO,UACT,IAAK,SACH,MAAO,QACV,CACD,GAAIN,IAAe,oBAAsBA,IAAe,qBACtD,MAAO,MACF,GAAIA,IAAe,yBACxB,MAAO,WACF,GAAID,IAAc,QACvB,OAAOE,IAAmB,YAAc,uBAAyB,SAC5D,GAAID,IAAe,mBACxB,OAA6BM,GAAK,OAAU,QACnCL,IAAmB,YAAc,2BAA6B,wFAEhEA,IAAmB,YAAc,uBAAyB,sFAC5D,GAAID,IAAe,sBACxB,OAAOC,IAAmB,YAAc,8CAAgD,2GAE5F,CACA,SAASC,GAAgBI,EAAMN,EAAY,CACzC,OAAIA,IAAe,sBACV,gCACEA,IAAe,yBACjB,mBACEA,IAAe,mBACjB,gCAEsBM,GAAK,WACtC,CACA,SAASC,GAAeT,EAAMU,EAAa,CAEzC,OAAQV,EAAK,IAAG,CACd,IAAK,YACH,MAAO,CAAE,KAAM,QACjB,IAAK,YACH,MAAO,CAAE,KAAM,QACjB,IAAK,aACH,MAAO,CACL,KAAM,SACN,OAAQ,CACN,SACA,QAASrF,GACT,MAAO,QACP,KAAMqF,EAAK,KACX,QAASA,EAAK,OACf,CACT,EACI,IAAK,YACH,MAAO,CACL,KAAM,WACd,EACI,IAAK,mBACH,MAAO,CACL,KAAM,mBACN,OAAQ,CACN,SACA,QAASA,EAAK,QACd,MAAO,QACP,QAAS,EACV,CACT,EACI,IAAK,aACH,MAAO,CACL,KAAM,SACN,OAAQ,CACN,SACA,MAAOU,GAAe,UACtB,KAAMV,EAAK,KACX,KAAMA,EAAK,WACX,SAAUA,EAAK,KACf,IAAKA,EAAK,SACV,QAASA,EAAK,OACf,CACT,EACI,IAAK,WACH,MAAO,CACL,KAAM,SACN,OAAQ,CACN,SACA,MAAO,UACP,KAAMA,EAAK,KACX,cAAeA,EAAK,cACpB,QAASA,EAAK,OACf,CACT,EACI,IAAK,MACH,MAAO,CAAE,KAAM,MAAO,KAAAA,GACxB,IAAK,qBACH,MAAO,CACL,KAAM,aACN,OAAQ,CACN,SACA,QAAUA,EAAK,QAA8B,KAApBA,EAAK,OAAO,MACrC,MAAOA,EAAK,QAAU,aAAe,QACrC,KAAMA,EAAK,KACX,cAAeA,EAAK,cACpB,IAAKA,EAAK,gBACX,EACD,KAAMA,EAAK,QAAUA,EAAK,OAAS,IAC3C,EACI,IAAK,oBACH,MAAI,UAAWA,EAAK,OACX,CACL,KAAM,SACN,OAAQ,CACN,SACA,QAASA,EAAK,OAAO,MACrB,MAAO,QACP,KAAMA,EAAK,KACX,QAASA,EAAK,OACf,CACX,EAEa,CACL,KAAM,WACN,OAAQ,CACN,SACA,QAAUA,EAAK,QAA8B,OAApBA,EAAK,OAAO,MACrC,MAAOA,EAAK,QAAU,WAAa,QACnC,KAAMA,EAAK,KACX,cAAeA,EAAK,cACpB,kBAAmBA,EAAK,QAAUA,EAAK,OAAO,kBAAoB,MACnE,EACD,KAAMA,EAAK,QAAUA,EAAK,OAAS,IAC3C,EACI,IAAK,iBACH,MAAO,CACL,KAAM,SACN,OAAQ,CACN,SACA,MAAO,UACP,KAAMA,EAAK,KACX,KAAMA,EAAK,KACX,SAAU,EACV,QAASA,EAAK,QACd,IAAKA,EAAK,GACX,CACT,CACG,CACD,MAAO,CAAE,KAAM,OAAQ,OAAQ,CAAE,MAAO,QAAS,QAAK,EACxD,CACA,MAAMW,GAAqB,CAACX,EAAMpB,IAAa,CAC7C,MAAMI,EAAa,OAAO,OAAOJ,EAAS,eAAe,EAAE,QACxDgC,GAAWA,EAAO,UACvB,EACE,GAAI,MAAM,QAAQZ,CAAI,EACpB,OAAIA,EAAK,OAAShB,EAAW,QAC3B,QAAQ,KAAK,+CAA+C,EAEvDgB,EAET,MAAMa,EAAgB,GAChBC,EAAgB,OAAO,KAAKd,CAAI,EACtC,OAAAhB,EAAW,QAAQ,CAAC+B,EAAOC,IAAU,CACnC,GAAIhB,EAAK,eAAee,EAAM,cAAc,EAC1CF,EAAcG,CAAK,EAAIhB,EAAKe,EAAM,cAAc,UACvCA,EAAM,sBACfF,EAAcG,CAAK,EAAID,EAAM,sBAE7B,OAAM,IAAI,MACR,6CAA6CA,EAAM,cAAc,EACzE,CAEA,CAAG,EACDD,EAAc,QAAS5H,GAAQ,CAC7B,GAAI,CAAC8F,EAAW,KAAM+B,GAAUA,EAAM,iBAAmB7H,CAAG,EAC1D,MAAM,IAAI,MACR,eAAeA,CAAG,wEAC1B,CAEA,CAAG,EACD2H,EAAc,QAAQ,CAAC1H,EAAOyG,IAAQ,CACpC,GAAIzG,IAAU,QAAU,CAAC6F,EAAWY,CAAG,EAAE,sBACvC,MAAM,IAAI,MACR,6CAA6CZ,EAAWY,CAAG,EAAE,cAAc,EACnF,CAEA,CAAG,EACMiB,CACT,EACA,eAAeI,IAAW,CACxB,GAAI,KAAK,SACP,OAAO,KAAK,SACd,KAAM,CAAE,SAAAxD,CAAQ,EAAK,KAAK,QACpB,CAAE,OAAAf,CAAQ,EAAG,KACbF,EAAU,CAAE,eAAgB,oBAIlC,GAHIiB,IACFjB,EAAQ,cAAgB,UAAUiB,CAAQ,IAExC,EAACf,EAGL,GAAI,CACF,IAAII,EACJ,GAAI/C,GAAmC2C,GAAO,SAAY,QAAS,MAAM,EAAI,EAC3EI,EAAW,MAAM,KAAK,MAAMpC,GAAmB,CAC7C,OAAQ,OACR,KAAM,KAAK,UAAU,CACnB,UAAW,GACX,OAAQ,KAAK,UAAUgC,CAAM,CACvC,CAAS,EACD,QAAAF,EACA,YAAa,SACrB,CAAO,MACI,CACL,MAAM0E,EAAMrE,GAAUH,EAAO,KAAMnC,EAAY,EAC/CuC,EAAW,MAAM,KAAK,MAAMoE,EAAK,CAC/B,QAAA1E,EACA,YAAa,SACrB,CAAO,CACF,CACD,GAAI,CAACM,EAAS,GACZ,MAAM,IAAI,MAAMlC,CAAqB,EAEvC,IAAIgE,EAAW,MAAM9B,EAAS,OAC9B,MAAI,QAAS8B,IACXA,EAAWA,EAAS,KAElBA,EAAS,gBAAgB,UAAU,GAAK,CAACA,EAAS,kBAAkB,CAAG,IACzEA,EAAS,kBAAkB,CAAC,EAAIA,EAAS,gBAAgB,UAAU,GAE9DD,GAAmBC,EAAUlC,EAAQ,KAAK,OAAO,CACzD,OAAQoD,EAAG,CACV,GAA6BA,EAAE,OAChC,CACH,CACA,eAAeqB,GAAaC,EAAUC,EAAOC,EAAW,CACtD,IAAI/E,EACJ,MAAMC,EAAU,IACXD,EAAK,MAAQ,KAAO,OAAS,KAAK,UAAY,MAAgBA,EAAG,WACpEC,EAAQ,cAAgB,UAAU,KAAK,QAAQ,QAAQ,IAEzD,MAAM+E,EAAY,IACZC,EAAkB,GACxB,IAAI1E,EACJ,QAASE,EAAI,EAAGA,EAAIqE,EAAM,OAAQrE,GAAKuE,EAAW,CAChD,MAAME,EAAQJ,EAAM,MAAMrE,EAAGA,EAAIuE,CAAS,EACpC7D,EAAW,IAAI,SACrB+D,EAAM,QAASC,GAAS,CACtBhE,EAAS,OAAO,QAASgE,CAAI,CACnC,CAAK,EACD,GAAI,CACF,MAAMC,EAAaL,EAAY,GAAGF,CAAQ,IAAIhH,EAAU,cAAckH,CAAS,GAAK,GAAGF,CAAQ,IAAIhH,EAAU,GAC7G0C,EAAW,MAAM,KAAK,MAAM6E,EAAY,CACtC,OAAQ,OACR,KAAMjE,EACN,QAAAlB,EACA,YAAa,SACrB,CAAO,CACF,OAAQsD,EAAG,CACV,MAAM,IAAI,MAAMlF,EAAwBkF,EAAE,OAAO,CAClD,CACD,GAAI,CAAChD,EAAS,GAAI,CAChB,MAAM8E,EAAa,MAAM9E,EAAS,OAClC,MAAO,CAAE,MAAO,QAAQA,EAAS,MAAM,KAAK8E,CAAU,GACvD,CACD,MAAMC,EAAS,MAAM/E,EAAS,OAC1B+E,GACFL,EAAgB,KAAK,GAAGK,CAAM,CAEjC,CACD,MAAO,CAAE,MAAOL,EAClB,CACA,eAAeM,GAAOC,EAAWX,EAAUE,EAAWU,EAAe,CACnE,IAAIX,GAAS,MAAM,QAAQU,CAAS,EAAIA,EAAY,CAACA,CAAS,GAAG,IAC9DE,GAAeA,EAAW,IAC/B,EACE,MAAMC,EAAkBb,EAAM,OAC3Bc,GAAMA,EAAE,MAAQH,GAAiB,IACtC,EACE,GAAIE,EAAgB,OAClB,MAAM,IAAI,MACR,iDAAiDF,CAAa,WAAWE,EAAgB,IAAKC,GAAMA,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC,EAC5H,EAEE,OAAO,MAAM,QAAQ,IACnB,MAAM,KAAK,aAAaf,EAAUC,EAAOC,CAAS,EAAE,KAClD,MAAOxE,GAAa,CAClB,GAAIA,EAAS,MACX,MAAM,IAAI,MAAMA,EAAS,KAAK,EAE9B,OAAIA,EAAS,MACJA,EAAS,MAAM,IAAI,CAACqF,EAAGnF,IACf,IAAIoF,GAAS,CACxB,GAAGL,EAAU/E,CAAC,EACd,KAAMmF,EACN,IAAKf,EAAW,SAAWe,CAC3C,CAAe,CAEF,EAEI,EAEV,CACF,CACL,CACA,CACA,eAAeE,GAAchB,EAAOiB,EAAW,CAC7C,OAAOjB,EAAM,IACVc,GAAM,IAAIC,GAAS,CAClB,KAAMD,EAAE,KACR,UAAWA,EAAE,KACb,KAAMA,EACN,KAAMA,EAAE,KACR,UAAWA,EAAE,KACb,UAAAG,CACN,CAAK,CACL,CACA,CACA,MAAMF,EAAS,CACb,YAAY,CACV,KAAA3F,EACA,IAAAyE,EACA,UAAAqB,EACA,KAAAC,EACA,KAAAC,EACA,UAAAH,EACA,UAAAI,EACA,SAAAC,CACJ,EAAK,CACDvJ,EAAc,KAAM,MAAM,EAC1BA,EAAc,KAAM,KAAK,EACzBA,EAAc,KAAM,WAAW,EAC/BA,EAAc,KAAM,MAAM,EAC1BA,EAAc,KAAM,MAAM,EAC1BA,EAAc,KAAM,WAAW,EAC/BA,EAAc,KAAM,WAAW,EAC/BA,EAAc,KAAM,UAAU,EAC9BA,EAAc,KAAM,OAAQ,CAAE,MAAO,iBAAmB,GACxD,KAAK,KAAOqD,EACZ,KAAK,IAAMyE,EACX,KAAK,UAAYqB,EACjB,KAAK,KAAOC,EACZ,KAAK,KAAOtB,EAAM,OAASuB,EAC3B,KAAK,UAAYH,EACjB,KAAK,UAAYI,EACjB,KAAK,SAAWC,CACjB,CACH,CACA,MAAMC,EAAQ,CACZ,YAAYC,EAASC,EAAM,CACzB1J,EAAc,KAAM,MAAM,EAC1BA,EAAc,KAAM,SAAS,EAC7BA,EAAc,KAAM,MAAM,EAC1BA,EAAc,KAAM,UAAU,EAC9B,KAAK,KAAO,UACZ,KAAK,QAAUyJ,EACf,KAAK,KAAOC,CACb,CACH,CACgB,OAAO,QAAY,KAAe,QAAQ,UAAY,QAAQ,SAAS,KACvF,SAASC,GAAcC,EAAQC,EAAUC,EAAO,CAC9C,KAAOA,EAAM,OAAS,GAAG,CACvB,MAAMC,EAAOD,EAAM,QACnB,GAAI,OAAOC,GAAS,UAAY,OAAOA,GAAS,SAC9CH,EAASA,EAAOG,CAAI,MAEpB,OAAM,IAAI,MAAM,kBAAkB,CAErC,CACD,MAAMjK,EAAMgK,EAAM,QAClB,GAAI,OAAOhK,GAAQ,UAAY,OAAOA,GAAQ,SAC5C8J,EAAO9J,CAAG,EAAI+J,MAEd,OAAM,IAAI,MAAM,kBAAkB,CAEtC,CACA,eAAeG,GAAqBpD,EAAMQ,EAAO,OAAQ/D,EAAO,CAAE,EAAE4G,EAAO,GAAOC,EAAgB,OAAQ,CACxG,GAAI,MAAM,QAAQtD,CAAI,EAAG,CACvB,IAAIuD,EAAY,GAChB,aAAM,QAAQ,IACZvD,EAAK,IAAI,MAAOwD,EAAGxC,IAAU,CAC3B,IAAIzE,EACJ,IAAIkH,EAAWhH,EAAK,QACpBgH,EAAS,KAAK,OAAOzC,CAAK,CAAC,EAC3B,MAAM0C,EAAa,MAAMN,GACvBpD,EAAKgB,CAAK,EACVqC,IAAS9G,EAAsC+G,GAAc,WAAWtC,CAAK,IAAM,KAAO,OAASzE,EAAG,YAAc,OAASiE,EAC7HiD,EACA,GACAH,CACV,EACQC,EAAYA,EAAU,OAAOG,CAAU,CAC/C,CAAO,CACP,EACWH,CACX,KAAS,IAAI,WAAW,QAAUvD,aAAgB,WAAW,QAAUA,aAAgB,KACnF,MAAO,CACL,CACE,KAAAvD,EACA,KAAM,IAAI,KAAK,CAACuD,CAAI,CAAC,EACrB,KAAAQ,CACD,CACP,EACS,GAAI,OAAOR,GAAS,UAAYA,IAAS,KAAM,CACpD,IAAIuD,EAAY,GAChB,UAAWrK,KAAO,OAAO,KAAK8G,CAAI,EAAG,CACnC,MAAMyD,EAAW,CAAC,GAAGhH,EAAMvD,CAAG,EACxBC,EAAQ6G,EAAK9G,CAAG,EACtBqK,EAAYA,EAAU,OACpB,MAAMH,GACJjK,EACA,OACAsK,EACA,GACAH,CACD,CACT,CACK,CACD,OAAOC,CACR,EACD,MAAO,EACT,CACA,SAASI,GAAWvH,EAAIM,EAAQ,CAC9B,IAAIH,EAAI2C,EACR,IAAI0E,GAAY1E,GAAM3C,EAA+BG,GAAO,eAAiB,KAAO,OAASH,EAAG,KAAMQ,GAAQA,EAAI,IAAMX,CAAE,IAAM,KAAO,OAAS8C,EAAG,MACnJ,OAAI0E,GAAY,KACP,CAACA,EAEH,CAAClH,EAAO,YACjB,CACA,SAASmH,GAAaC,EAASC,EAAQ,CACrC,OAAO,IAAI,QAAQ,CAACpG,EAAKqG,IAAS,CAChC,MAAMC,EAAU,IAAI,eACpBA,EAAQ,MAAM,UAAY,CAAC,CAAE,KAAAjE,CAAI,IAAO,CACtCiE,EAAQ,MAAM,QACdtG,EAAIqC,CAAI,CACd,EACI,OAAO,OAAO,YAAY8D,EAASC,EAAQ,CAACE,EAAQ,KAAK,CAAC,CAC9D,CAAG,CACH,CAsCA,SAASC,EAAeC,EAAkBC,EAAY7E,EAAYiB,EAAM6D,EAAkB,GAAO,CAC/F,GAAI7D,IAAS,SAAW,CAAC6D,EACvB,MAAM,IAAI,MAAM,wDAAwD,EAE1E,GAAI7D,IAAS,UAAY6D,EACvB,OAAOF,EAET,IAAIG,EAAkB,GAClBC,EAAgB,EACpB,QAASvH,EAAI,EAAGA,EAAIoH,EAAW,OAAO,OAAQpH,IAAK,CACjD,MAAMwH,EAAWJ,EAAW,OAAOpH,CAAC,EAC9BiD,EAAYV,EAAW,KAAMG,GAAMA,EAAE,KAAO8E,CAAQ,EAC1D,GAAkCvE,GAAU,OAAU,QAAS,CAC7D,GAAIoE,EACF,GAAIF,EAAiB,SAAWC,EAAW,OAAO,OAAQ,CACxD,MAAMjL,EAAQgL,EAAiBI,CAAa,EAC5CD,EAAgB,KAAKnL,CAAK,EAC1BoL,GACV,MACUD,EAAgB,KAAK,IAAI,MAEtB,CACLC,IACA,QACD,CACD,QACN,KAAW,CACL,MAAMpL,EAAQgL,EAAiBI,CAAa,EAC5CD,EAAgB,KAAKnL,CAAK,EAC1BoL,GACD,CACF,CACD,OAAOD,CACT,CACA,eAAeG,GAAYnI,EAAU0D,EAAMpB,EAAU,CACnD,MAAM8F,EAAO,KACb,MAAMC,GAA4BD,EAAM1E,CAAI,EAC5C,MAAM4E,EAAW,MAAMxB,GACrBpD,EACA,OACA,CAAE,EACF,GACApB,CACJ,EAeE,OAdgB,MAAM,QAAQ,IAC5BgG,EAAS,IAAI,MAAO,CAAE,KAAAnI,EAAM,KAAAgG,EAAM,KAAAjC,CAAI,IAAO,CAC3C,GAAI,CAACiC,EACH,MAAO,CAAE,KAAAhG,EAAM,KAAA+D,GACjB,MAAM1D,EAAW,MAAM4H,EAAK,aAAapI,EAAU,CAACmG,CAAI,CAAC,EACnDoC,EAAW/H,EAAS,OAASA,EAAS,MAAM,CAAC,EACnD,MAAO,CACL,KAAAL,EACA,SAAAoI,EACA,KAAArE,EACA,KAAMiC,aAAgB,KAA+BA,GAAK,KAAO,MACzE,CACA,CAAK,CACL,GACU,QAAQ,CAAC,CAAE,KAAAhG,EAAM,SAAAoI,EAAU,KAAArE,EAAM,KAAAsE,KAAW,CAClD,GAAItE,IAAS,UACXuC,GAAc/C,EAAM6E,EAAUpI,CAAI,UACzBoI,EAAU,CACnB,MAAMnD,EAAO,IAAIU,GAAS,CAAE,KAAMyC,EAAU,UAAWC,CAAI,CAAE,EAC7D/B,GAAc/C,EAAM0B,EAAMjF,CAAI,CAC/B,CACL,CAAG,EACMuD,CACT,CACA,eAAe2E,GAA4BI,EAAS/E,EAAM,CACxD,IAAIzD,EAAI2C,EAER,GAAI,IADW3C,EAAKwI,EAAQ,SAAW,KAAO,OAASxI,EAAG,SAAW2C,EAAK6F,EAAQ,SAAW,KAAO,OAAS7F,EAAG,WAE9G,MAAM,IAAI,MAAM5D,EAAkB,EAEpC,MAAM0J,GAA6BD,EAAS/E,CAAI,CAClD,CACA,eAAegF,GAA6BD,EAAS/E,EAAMvD,EAAO,GAAI,CACpE,UAAWvD,KAAO8G,EACZA,EAAK9G,CAAG,YAAa0J,GACvB,MAAMqC,GAAuBF,EAAS/E,EAAM9G,CAAG,EACtC,OAAO8G,EAAK9G,CAAG,GAAM,UAAY8G,EAAK9G,CAAG,IAAM,MACxD,MAAM8L,GAA6BD,EAAS/E,EAAK9G,CAAG,EAAG,CAAC,GAAGuD,EAAMvD,CAAG,CAAC,CAG3E,CACA,eAAe+L,GAAuBF,EAAS/E,EAAM9G,EAAK,CACxD,IAAIqD,EAAI2C,EACR,IAAIgG,EAAWlF,EAAK9G,CAAG,EACvB,MAAMmK,IAAS9G,EAAKwI,EAAQ,SAAW,KAAO,OAASxI,EAAG,SAAW2C,EAAK6F,EAAQ,SAAW,KAAO,OAAS7F,EAAG,UAChH,GAAI,CAACmE,EACH,MAAM,IAAI,MAAM/H,EAAkB,EAEpC,GAAI,CACF,IAAI6J,EACAC,EACJ,GAAI,OAAO,QAAY,KAAe,QAAQ,UAAY,QAAQ,SAAS,KAAM,CAC/E,MAAMC,EAAK,MAAKC,EAAA,IAAC,OAAO,uCAAa,OAAAC,KAAA,uBAErCH,GADa,YAAM,OAAO,uCAAM,OAAAG,KAAA,wBAChB,QAAQ,QAAQ,IAAG,EAAIL,EAAS,KAAK,IAAI,EACzDC,EAAa,MAAME,EAAG,SAASD,CAAQ,CAC7C,KACM,OAAM,IAAI,MAAM/J,EAAmB,EAErC,MAAMqG,EAAO,IAAI,KAAK,CAACyD,CAAU,EAAG,CAAE,KAAM,0BAA0B,CAAE,EAClErI,EAAW,MAAMiI,EAAQ,aAAa1B,EAAM,CAAC3B,CAAI,CAAC,EAClDmD,EAAW/H,EAAS,OAASA,EAAS,MAAM,CAAC,EACnD,GAAI+H,EAAU,CACZ,MAAMW,EAAW,IAAIpD,GAAS,CAC5B,KAAMyC,EACN,UAAWK,EAAS,KAAK,MAAQ,EACzC,CAAO,EACDlF,EAAK9G,CAAG,EAAIsM,CACb,CACF,OAAQC,EAAO,CACd,QAAQ,MAAMlK,GAA2BkK,CAAK,CAC/C,CACH,CACA,eAAeC,GAAUxE,EAAKyE,EAAMC,EAAoB,CACtD,MAAMpJ,EAAU,CAAE,eAAgB,oBAC9B,KAAK,QAAQ,WACfA,EAAQ,cAAgB,UAAU,KAAK,QAAQ,QAAQ,IAEzD,GAAI,CACF,IAAIM,EAAW,MAAM,KAAK,MAAMoE,EAAK,CACnC,OAAQ,OACR,KAAM,KAAK,UAAUyE,CAAI,EACzB,QAAS,CAAE,GAAGnJ,EAAS,GAAGoJ,CAAoB,EAC9C,YAAa,SACnB,CAAK,CACF,MAAW,CACV,MAAO,CAAC,CAAE,MAAOhL,CAAuB,EAAE,GAAG,CAC9C,CACD,IAAIiH,EACAgE,EACJ,GAAI,CACFhE,EAAS,MAAM/E,EAAS,OACxB+I,EAAS/I,EAAS,MACnB,OAAQgD,EAAG,CACV+B,EAAS,CAAE,MAAO,oCAAoC/B,CAAC,EAAE,EACzD+F,EAAS,GACV,CACD,MAAO,CAAChE,EAAQgE,CAAM,CACxB,CACA,eAAeC,GAAQxJ,EAAU0D,EAAM,CACrC,IAAI+F,EAAgB,GAChBC,EAAkB,GAClB5B,EACJ,GAAI,CAAC,KAAK,OACR,MAAM,IAAI,MAAM,8BAA8B,EAEhD,GAAI,OAAO9H,GAAa,SACtB8H,EAAa,KAAK,OAAO,aAAa,KAAMrH,GAAQA,EAAI,IAAMT,CAAQ,MACjE,CACL,MAAM2J,EAAmB3J,EAAS,QAAQ,MAAO,EAAE,EACnD8H,EAAa,KAAK,OAAO,aAAa,KACnCrH,GAAQA,EAAI,IAAM,KAAK,QAAQkJ,CAAgB,CACtD,CACG,CACD,GAAkC7B,GAAW,MAAM,WACjD,MAAM,IAAI,MACR,gFACN,EAEE,OAAO,IAAI,QAAQ,MAAO8B,EAASC,IAAW,CAC5C,MAAMC,EAAM,KAAK,OAAO9J,EAAU0D,EAAM,KAAM,KAAM,EAAI,EACxD,IAAIqG,EACJ,gBAAiBvC,KAAWsC,EACtBtC,EAAQ,OAAS,SACfkC,GACFE,EAAQG,CAAM,EAEhBN,EAAgB,GAChBM,EAASvC,GAEPA,EAAQ,OAAS,WACfA,EAAQ,QAAU,SACpBqC,EAAOrC,CAAO,EACZA,EAAQ,QAAU,aACpBkC,EAAkB,GACdD,GACFG,EAAQG,CAAM,GAK1B,CAAG,CACH,CACA,eAAeC,GAAmBlK,EAAIoE,EAAM+F,EAAiB,CAC3D,IAAIjK,EAAWkE,IAAS,YAAc,kDAAkDpE,CAAE,GAAK,qCAAqCA,CAAE,GAClIU,EACA0J,EACJ,GAAI,CAGF,GAFA1J,EAAW,MAAM,MAAMR,CAAQ,EAC/BkK,EAAU1J,EAAS,OACf0J,IAAY,IACd,MAAM,IAAI,MAEZ1J,EAAW,MAAMA,EAAS,MAC3B,MAAW,CACVyJ,EAAgB,CACd,OAAQ,QACR,YAAa,QACb,QAASzL,GACT,OAAQ,WACd,CAAK,EACD,MACD,CACD,GAAI,CAACgC,GAAY0J,IAAY,IAC3B,OACF,KAAM,CACJ,QAAS,CAAE,MAAAC,CAAO,EAClB,GAAIC,CACL,EAAG5J,EACJ,OAAQ2J,EAAK,CACX,IAAK,UACL,IAAK,WACHF,EAAgB,CACd,OAAQ,WACR,YAAa,UACb,QAAS,mCACT,OAAQE,CAChB,CAAO,EACD,WAAW,IAAM,CACfH,GAAmBlK,EAAIoE,EAAM+F,CAAe,CAC7C,EAAE,GAAG,EACN,MACF,IAAK,SACHA,EAAgB,CACd,OAAQ,SACR,YAAa,QACb,QAAS,gHACT,OAAQE,EACR,oBAAqB,MAAME,GAAoBD,CAAU,CACjE,CAAO,EACD,MACF,IAAK,UACL,IAAK,mBACHH,EAAgB,CACd,OAAQ,UACR,YAAa,WACb,QAAS,GACT,OAAQE,CAChB,CAAO,EACD,MACF,IAAK,WACHF,EAAgB,CACd,OAAQ,WACR,YAAa,UACb,QAAS,uBACT,OAAQE,CAChB,CAAO,EACD,WAAW,IAAM,CACfH,GAAmBlK,EAAIoE,EAAM+F,CAAe,CAC7C,EAAE,GAAG,EACN,MACF,QACEA,EAAgB,CACd,OAAQ,cACR,YAAa,QACb,QAAS,uCACT,OAAQE,EACR,oBAAqB,MAAME,GAAoBD,CAAU,CACjE,CAAO,EACD,KACH,CACH,CACA,MAAME,GAAyB,+DAC/B,eAAeD,GAAoBE,EAAU,CAC3C,GAAI,CACF,MAAMtG,EAAI,MAAM,MACd,qCAAqCsG,CAAQ,eAC7C,CACE,OAAQ,MACT,CACP,EACUpB,EAAQlF,EAAE,QAAQ,IAAI,iBAAiB,EAC7C,MAAI,GAACA,EAAE,IAAMkF,GAASmB,GAAuB,KAAKnB,CAAK,EAGxD,MAAW,CACV,MAAO,EACR,CACH,CACA,eAAeqB,GAAmBD,EAAUpJ,EAAU,CACpD,MAAMjB,EAAU,GACZiB,IACFjB,EAAQ,cAAgB,UAAUiB,CAAQ,IAE5C,GAAI,CACF,MAAME,EAAM,MAAM,MAChB,qCAAqCkJ,CAAQ,IAAIrM,EAAW,GAC5D,CAAE,QAAAgC,CAAS,CACjB,EACI,GAAImB,EAAI,SAAW,IACjB,MAAM,IAAI,MAAM,uCAAuC,EACzD,KAAM,CAAE,SAAAoJ,CAAU,EAAG,MAAMpJ,EAAI,KAAI,EACnC,OAAOoJ,EAAS,OACjB,OAAQjH,EAAG,CACV,MAAM,IAAI,MAAMA,EAAE,OAAO,CAC1B,CACH,CACA,eAAekH,GAAkBH,EAAUI,EAASxJ,EAAU,CAC5D,MAAMjB,EAAU,GACZiB,IACFjB,EAAQ,cAAgB,UAAUiB,CAAQ,IAE5C,MAAMkI,EAAO,CACX,QAASsB,CACb,EACE,GAAI,CACF,MAAMtJ,EAAM,MAAM,MAChB,qCAAqCkJ,CAAQ,IAAIpM,EAAa,GAC9D,CACE,OAAQ,OACR,QAAS,CAAE,eAAgB,mBAAoB,GAAG+B,CAAS,EAC3D,KAAM,KAAK,UAAUmJ,CAAI,CAC1B,CACP,EACI,GAAIhI,EAAI,SAAW,IACjB,MAAM,IAAI,MACR,8IACR,EAGI,OADiB,MAAMA,EAAI,MAE5B,OAAQmC,EAAG,CACV,MAAM,IAAI,MAAMA,EAAE,OAAO,CAC1B,CACH,CACA,MAAMoH,GAAiB,CACrB,YACA,cACA,SACA,WACA,YACA,aACA,aACA,eACA,eACA,aACA,YACA,OACA,QACF,EACA,eAAeC,GAAU9I,EAAe+I,EAAS,CAC/C,KAAM,CAAE,SAAA3J,EAAU,QAAS4J,EAAU,SAAAN,EAAU,QAAAE,EAAS,KAAA1J,CAAM,EAAG6J,EACjE,GAAIL,GAAY,CAACG,GAAe,SAASH,CAAQ,EAC/C,MAAM,IAAI,MACR,oDAAoDG,GAAe,IAAKI,GAAM,IAAIA,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,GACvG,EAEE,KAAM,CAAE,cAAApK,EAAe,KAAAC,CAAM,EAAG,MAAMC,GACpCiB,EACAZ,CACJ,EACE,IAAI1B,EAAU,KACd,GAAIwB,EAAM,CACR,MAAMF,EAAgB,MAAMC,GAC1BJ,EACAC,EACAI,EACA,KACN,EACQF,IACFtB,EAAUgC,GAAsBV,CAAa,EAChD,CACD,MAAMb,EAAU,CACd,cAAe,UAAUiB,CAAQ,GACjC,eAAgB,mBAChB,GAAG1B,EAAU,CAAE,OAAQA,EAAQ,KAAK,IAAI,CAAC,EAAK,CAAE,CACpD,EACQwL,GAAQ,MAAO,MAAM,MAAM,uCAAwC,CACvE,QAAA/K,CACJ,CAAG,GAAG,KAAM,GAAE,KACNkK,EAAarI,EAAc,MAAM,GAAG,EAAE,CAAC,EACvCsH,EAAO,CACX,WAAY,GAAG4B,CAAI,IAAIb,CAAU,EACrC,EACMW,IACF1B,EAAK,QAAU,IAEjB,IAAI6B,EACJ,GAAI,CACGT,IACHS,EAAoB,MAAMV,GAAmBzI,EAAeZ,CAAQ,EAEvE,OAAQqC,EAAG,CACV,MAAM,MAAM9E,GAA2B8E,EAAE,OAAO,CACjD,CACD,MAAM2H,EAAqBV,GAAYS,GAAqB,YAC5D7B,EAAK,SAAW8B,EAChB,GAAI,CACF,MAAM3K,EAAW,MAAM,MACrB,qCAAqCuB,CAAa,aAClD,CACE,OAAQ,OACR,QAAA7B,EACA,KAAM,KAAK,UAAUmJ,CAAI,CAC1B,CACP,EACI,GAAI7I,EAAS,SAAW,IACtB,GAAI,CAEF,OADgB,MAAM4K,GAAO,QAAQ,GAAGH,CAAI,IAAIb,CAAU,GAAIU,CAAO,CAEtE,OAAQ3B,EAAO,CACd,cAAQ,MAAM,qCAAsCA,CAAK,EACnDA,CACP,SACQ3I,EAAS,SAAW,IAC7B,MAAM,IAAI,MAAMA,EAAS,UAAU,EAErC,MAAM6K,EAAmB,MAAM7K,EAAS,OACxC,aAAMkK,GAAkB,GAAGO,CAAI,IAAIb,CAAU,GAAIO,GAAW,IAAKxJ,CAAQ,EAClE,MAAMiK,GAAO,QAClBE,GAAoBD,EAAiB,GAAG,EACxCP,CACN,CACG,OAAQtH,EAAG,CACV,MAAM,IAAI,MAAMA,CAAC,CAClB,CACH,CACA,SAAS8H,GAAoB1G,EAAK,CAChC,MAAM2G,EAAQ,mDACRC,EAAQ5G,EAAI,MAAM2G,CAAK,EAC7B,GAAIC,EACF,OAAOA,EAAM,CAAC,CAElB,CACA,MAAMC,WAAuB,eAAgB,CAE3C,YAAYX,EAAU,CAAE,QAAS,EAAK,EAAI,CACxC,MAAM,CACJ,UAAW,CAACY,EAAOC,IAAe,CAEhC,IADAD,EAAQxO,EAAa,KAAMK,CAAY,EAAImO,IAC9B,CACX,MAAME,EAAUF,EAAM,QAAQ;AAAA,CAAI,EAC5BG,EAAUf,EAAQ,QAAUY,EAAM,QAAQ,IAAI,EAAI,GACxD,GAAIG,IAAY,IAAMA,IAAYH,EAAM,OAAS,IAAME,IAAY,IAAMA,EAAU,EAAIC,GAAU,CAC/FF,EAAW,QAAQD,EAAM,MAAM,EAAGG,CAAO,CAAC,EAC1CH,EAAQA,EAAM,MAAMG,EAAU,CAAC,EAC/B,QACD,CACD,GAAID,IAAY,GACd,MACF,MAAME,EAAWJ,EAAME,EAAU,CAAC,IAAM,KAAOA,EAAU,EAAIA,EAC7DD,EAAW,QAAQD,EAAM,MAAM,EAAGI,CAAQ,CAAC,EAC3CJ,EAAQA,EAAM,MAAME,EAAU,CAAC,CAChC,CACDvO,GAAa,KAAME,EAAcmO,CAAK,CACvC,EACD,MAAQC,GAAe,CACrB,GAAIzO,EAAa,KAAMK,CAAY,IAAM,GACvC,OACF,MAAMwO,EAAcjB,EAAQ,SAAW5N,EAAa,KAAMK,CAAY,EAAE,SAAS,IAAI,EAAIL,EAAa,KAAMK,CAAY,EAAE,MAAM,EAAG,EAAE,EAAIL,EAAa,KAAMK,CAAY,EACxKoO,EAAW,QAAQI,CAAW,CAC/B,CACP,CAAK,EACD3O,GAAa,KAAMG,EAAc,EAAE,CACpC,CACH,CACAA,EAAe,IAAI,QACnB,SAASyO,GAAS9I,EAAO,CACvB,IAAI+I,EAAU,IAAI,kBACdC,EAAS,IAAIT,GAAe,CAAE,QAAS,EAAM,GACjD,OAAOvI,EAAM,YAAY+I,CAAO,EAAE,YAAYC,CAAM,CACtD,CACA,SAASC,GAAMjJ,EAAO,CAEpB,IAAIsI,EADM,SACM,KAAKtI,CAAK,EACtBI,EAAMkI,GAASA,EAAM,MACzB,GAAIlI,EACF,MAAO,CACLJ,EAAM,UAAU,EAAGI,CAAG,EACtBJ,EAAM,UAAUI,EAAMkI,EAAM,CAAC,EAAE,MAAM,CAC3C,CAEA,CACA,SAASY,GAASlM,EAAStD,EAAKC,EAAO,CAC3BqD,EAAQ,IAAItD,CAAG,GAEvBsD,EAAQ,IAAItD,EAAKC,CAAK,CAC1B,CACA,eAAgBwP,GAAOhL,EAAKiL,EAAQ,CAClC,GAAI,CAACjL,EAAI,KACP,OACF,IAAIkL,EAAOP,GAAS3K,EAAI,IAAI,EACxBmL,EAAMC,EAASF,EAAK,UAAS,EAC7BG,EACJ,OAAW,CACT,GAAIJ,GAAUA,EAAO,QACnB,OAAOG,EAAO,SAGhB,GADAD,EAAO,MAAMC,EAAO,OAChBD,EAAK,KACP,OACF,GAAI,CAACA,EAAK,MAAO,CACXE,IACF,MAAMA,GACRA,EAAQ,OACR,QACD,CACD,GAAI,CAACC,EAAO9P,CAAK,EAAIsP,GAAMK,EAAK,KAAK,GAAK,GACrCG,IAEDA,IAAU,QACZD,IAAUA,EAAQ,IAClBA,EAAMC,CAAK,EAAID,EAAMC,CAAK,EAAID,EAAMC,CAAK,EAAI;AAAA,EAAO9P,EAAQA,GACnD8P,IAAU,SACnBD,IAAUA,EAAQ,IAClBA,EAAMC,CAAK,EAAI9P,GACN8P,IAAU,MACnBD,IAAUA,EAAQ,IAClBA,EAAMC,CAAK,EAAI,CAAC9P,GAASA,GAChB8P,IAAU,UACnBD,IAAUA,EAAQ,IAClBA,EAAMC,CAAK,EAAI,CAAC9P,GAAS,QAE5B,CACH,CACA,eAAe+P,GAAO1J,EAAO2J,EAAM,CACjC,IAAIC,EAAM,IAAI,QAAQ5J,EAAO2J,CAAI,EACjCT,GAASU,EAAI,QAAS,SAAU,mBAAmB,EACnDV,GAASU,EAAI,QAAS,eAAgB,kBAAkB,EACxD,IAAI7I,EAAI,MAAM,MAAM6I,CAAG,EACvB,GAAI,CAAC7I,EAAE,GACL,MAAMA,EACR,OAAOoI,GAAOpI,EAAG6I,EAAI,MAAM,CAC7B,CACA,eAAeC,IAAc,CAC3B,GAAI,CACF,gBAAAC,EACA,gBAAAC,EACA,wBAAAC,EACA,cAAAC,EACA,OAAA/M,EACA,IAAAgN,CACD,EAAG,KACJ,MAAMC,EAAO,KACb,GAAI,CAACjN,EACH,MAAM,IAAI,MAAM,8BAA8B,EAEhD+M,EAAc,KAAO,GACrB,IAAIG,EAAU,KACVC,EAAS,IAAI,gBAAgB,CAC/B,aAAc,KAAK,YACvB,CAAG,EAAE,SAAQ,EACP3I,EAAM,IAAI,IAAI,GAAGxE,EAAO,IAAI,eAAemN,CAAM,EAAE,EAKvD,GAJIH,GACFxI,EAAI,aAAa,IAAI,SAAUwI,CAAG,EAEpCE,EAAU,KAAK,OAAO1I,CAAG,EACrB,CAAC0I,EAAS,CACZ,QAAQ,KAAK,mCAAqC1I,EAAI,SAAU,GAChE,MACD,CACD0I,EAAQ,UAAY,eAAeZ,EAAO,CACxC,IAAIc,EAAQ,KAAK,MAAMd,EAAM,IAAI,EACjC,GAAIc,EAAM,MAAQ,eAAgB,CAChCC,GAAaN,EAAeE,EAAK,gBAAgB,EACjD,MACD,CACD,MAAMK,EAAWF,EAAM,SACvB,GAAI,CAACE,EACH,MAAM,QAAQ,IACZ,OAAO,KAAKV,CAAe,EAAE,IAC1BW,GAAcX,EAAgBW,CAAS,EAAEH,CAAK,CAChD,CACT,UACeR,EAAgBU,CAAQ,GAAKtN,EAAQ,CAC1CoN,EAAM,MAAQ,qBAAuB,CAAC,MAAO,SAAU,SAAU,WAAY,QAAQ,EAAE,SACzFpN,EAAO,QACf,GACQ6M,EAAgB,OAAOS,CAAQ,EAEjC,IAAIE,EAAMZ,EAAgBU,CAAQ,EAC9B,OAAO,OAAW,KAAe,OAAO,SAAa,IACvD,WAAWE,EAAK,EAAGJ,CAAK,EAExBI,EAAIJ,CAAK,CAEjB,MACWN,EAAwBQ,CAAQ,IACnCR,EAAwBQ,CAAQ,EAAI,IAEtCR,EAAwBQ,CAAQ,EAAE,KAAKF,CAAK,CAElD,EACEF,EAAQ,QAAU,gBAAiB,CACjC,MAAM,QAAQ,IACZ,OAAO,KAAKN,CAAe,EAAE,IAC1BU,GAAaV,EAAgBU,CAAQ,EAAE,CACtC,IAAK,mBACL,QAASpP,CACnB,CAAS,CACF,CACP,CACA,CACA,CACA,SAASmP,GAAaN,EAAeU,EAAkB,CACjDV,IACFA,EAAc,KAAO,GACeU,GAAiB,MAAK,EAE9D,CACA,SAASC,GAAkBC,EAAsBL,EAAUhK,EAAM,CACrC,CAACqK,EAAqBL,CAAQ,GAEtDK,EAAqBL,CAAQ,EAAI,GACjChK,EAAK,KAAK,QAAQ,CAAC7G,EAAO6D,IAAM,CAC9BqN,EAAqBL,CAAQ,EAAEhN,CAAC,EAAI7D,CAC1C,CAAK,GAED6G,EAAK,KAAK,QAAQ,CAAC7G,EAAO6D,IAAM,CAC9B,IAAIsN,EAAWC,GAAWF,EAAqBL,CAAQ,EAAEhN,CAAC,EAAG7D,CAAK,EAClEkR,EAAqBL,CAAQ,EAAEhN,CAAC,EAAIsN,EACpCtK,EAAK,KAAKhD,CAAC,EAAIsN,CACrB,CAAK,CAEL,CACA,SAASC,GAAWtR,EAAKuR,EAAM,CAC7B,OAAAA,EAAK,QAAQ,CAAC,CAACC,EAAQhO,EAAMtD,CAAK,IAAM,CACtCF,EAAMyR,GAAWzR,EAAKwD,EAAMgO,EAAQtR,CAAK,CAC7C,CAAG,EACMF,CACT,CACA,SAASyR,GAAWC,EAAQlO,EAAMgO,EAAQtR,EAAO,CAC/C,GAAIsD,EAAK,SAAW,EAAG,CACrB,GAAIgO,IAAW,UACb,OAAOtR,EACF,GAAIsR,IAAW,SACpB,OAAOE,EAASxR,EAElB,MAAM,IAAI,MAAM,uBAAuBsR,CAAM,EAAE,CAChD,CACD,IAAIG,EAAUD,EACd,QAAS3N,EAAI,EAAGA,EAAIP,EAAK,OAAS,EAAGO,IACnC4N,EAAUA,EAAQnO,EAAKO,CAAC,CAAC,EAE3B,MAAM6N,EAAYpO,EAAKA,EAAK,OAAS,CAAC,EACtC,OAAQgO,EAAM,CACZ,IAAK,UACHG,EAAQC,CAAS,EAAI1R,EACrB,MACF,IAAK,SACHyR,EAAQC,CAAS,GAAK1R,EACtB,MACF,IAAK,MACC,MAAM,QAAQyR,CAAO,EACvBA,EAAQ,OAAO,OAAOC,CAAS,EAAG,EAAG1R,CAAK,EAE1CyR,EAAQC,CAAS,EAAI1R,EAEvB,MACF,IAAK,SACC,MAAM,QAAQyR,CAAO,EACvBA,EAAQ,OAAO,OAAOC,CAAS,EAAG,CAAC,EAEnC,OAAOD,EAAQC,CAAS,EAE1B,MACF,QACE,MAAM,IAAI,MAAM,mBAAmBJ,CAAM,EAAE,CAC9C,CACD,OAAOE,CACT,CACA,SAASG,GAAgBtL,EAAO2J,EAAO,GAAI,CACzC,MAAM4B,EAAW,CACf,MAAO,IAAM,CACX,MAAM,IAAI,MAAM,yBAAyB,CAC1C,EACD,QAAS,KACT,UAAW,KACX,OAAQ,KACR,WAAY,EACZ,IAAKvL,EAAM,SAAU,EACrB,gBAAiB,GACjB,WAAY,EACZ,KAAM,EACN,OAAQ,EACR,iBAAkB,IAAM,CACtB,MAAM,IAAI,MAAM,yBAAyB,CAC1C,EACD,cAAe,IAAM,CACnB,MAAM,IAAI,MAAM,yBAAyB,CAC1C,EACD,oBAAqB,IAAM,CACzB,MAAM,IAAI,MAAM,yBAAyB,CAC1C,CACL,EACE,OAAA0J,GAAO1J,EAAO2J,CAAI,EAAE,KAAK,MAAOxL,GAAQ,CACtCoN,EAAS,WAAaA,EAAS,KAC/B,GAAI,CACF,gBAAiBtJ,KAAS9D,EACxBoN,EAAS,WAAaA,EAAS,UAAUtJ,CAAK,EAEhDsJ,EAAS,WAAaA,EAAS,MAChC,OAAQjL,EAAG,CACViL,EAAS,SAAWA,EAAS,QAAQjL,CAAC,EACtCiL,EAAS,WAAaA,EAAS,MAChC,CACL,CAAG,EAAE,MAAOjL,GAAM,CACd,QAAQ,MAAMA,CAAC,EACfiL,EAAS,SAAWA,EAAS,QAAQjL,CAAC,EACtCiL,EAAS,WAAaA,EAAS,MACnC,CAAG,EACMA,CACT,CACA,SAASC,GAAO1O,EAAU0D,EAAMiL,EAAYC,EAAYC,EAAY,CAClE,IAAI5O,EACJ,GAAI,CACF,IAAI6O,EAAa,SAASpC,EAAO,EAC3BmC,GAAcE,GAAkBrC,EAAM,IAAI,IAC5CsC,EAAWtC,CAAK,CAEnB,EAAEuC,EAAQ,UAAW,CAEpB,IADAC,GAAO,GACAC,EAAU,OAAS,GACxBA,EAAU,MAAK,EAAG,CAChB,MAAO,OACP,KAAM,EAChB,CAAS,CACT,EAAOC,EAAO,SAASC,EAAO,CACpBH,KAEAC,EAAU,OAAS,EACrBA,EAAU,QAAQE,CAAK,EAEvB/K,GAAO,KAAK+K,CAAK,EAEzB,EAAOC,EAAa,SAASnG,EAAO,CAC9BiG,EAAKG,GAAgBpG,CAAK,CAAC,EAC3B8F,GACN,EAAOD,EAAa,SAAStC,EAAO,CAC9B0C,EAAK,CAAE,MAAO1C,EAAO,KAAM,EAAO,EACnC,EAAE8C,EAAO,UAAW,CACnB,OAAIlL,GAAO,OAAS,EACX,QAAQ,QAAQA,GAAO,MAAO,GACnC4K,GACK,QAAQ,QAAQ,CAAE,MAAO,OAAQ,KAAM,EAAI,CAAE,EAC/C,IAAI,QAAStF,GAAYuF,EAAU,KAAKvF,CAAO,CAAC,CAC7D,EACI,KAAM,CAAE,SAAAzI,CAAQ,EAAK,KAAK,QACpB,CACJ,MAAOsO,EACP,cAAA1N,EACA,OAAA3B,EACA,aAAAsP,EACA,SAAApN,EACA,QAAAC,EACA,cAAA4K,EACA,wBAAAD,GACA,qBAAAa,GACA,gBAAAf,GACA,gBAAAC,GACA,UAAW0C,GACX,QAAA7E,CACD,EAAG,KACEuC,GAAO,KACb,GAAI,CAAC/K,EACH,MAAM,IAAI,MAAM,cAAc,EAChC,GAAI,CAAClC,EACH,MAAM,IAAI,MAAM,8BAA8B,EAChD,GAAI,CAAE,SAAAwP,EAAU,cAAA5I,GAAe,WAAAc,CAAY,EAAG+H,GAC5CvN,EACAtC,EACAuC,EACAnC,CACN,EACQmE,GAAgBF,GAAmBX,EAAMpB,CAAQ,EACjDwN,EACAxC,EACA/L,EAAWnB,EAAO,UAAY,KAClC,MAAM2P,EAAY,OAAO/P,GAAa,SAAW,WAAaA,EAC9D,IAAIgQ,EACAtC,EAAW,KACXuC,EAAW,GACX7L,GAAc,GACd8L,EAAa,OAAO,OAAW,KAAe,OAAO,SAAa,IAAc,IAAI,gBAAgB,OAAO,SAAS,MAAM,EAAE,SAAQ,EAAK,GAC7I,MAAMnB,KAAsB9O,EAAgC6K,GAAQ,SAAW,KAAO,OAAS7K,EAAG,OAChG,CAACkQ,EAAKzD,KACJyD,EAAIzD,CAAK,EAAI,GACNyD,GAET,CAAE,CACH,IAAK,GACN,eAAeC,IAAS,CACtB,MAAMlG,EAAU,CACd,MAAO,WACP,MAAO,GACP,KAAsB,IAAI,IAClC,EACM+F,EAAW/F,EACX4E,EAAW,CACT,GAAG5E,EACH,KAAM,SACN,SAAU6F,EACV,SAAAH,CACR,CAAO,EACD,IAAIS,EAAgB,GAChBC,EAAiB,GACjB/O,IAAa,MACXuO,GAAaA,EAAU,aAAe,EACxCA,EAAU,iBAAiB,OAAQ,IAAM,CACvCA,EAAU,MAAK,CAC3B,CAAW,EAEDA,EAAU,MAAK,EAEjBO,EAAgB,CAAE,SAAAT,EAAU,aAAAF,KAE5BjC,GAAaN,EAAeE,GAAK,gBAAgB,EACjD4B,IACAoB,EAAgB,CAAE,SAAA3C,GAClB4C,EAAiB,CAAE,SAAA5C,EAAU,aAAAgC,EAAc,SAAAE,CAAQ,GAErD,GAAI,CACF,GAAI,CAACxP,EACH,MAAM,IAAI,MAAM,8BAA8B,EAE5C,aAAckQ,GAChB,MAAMb,EAAO,GAAGrP,EAAO,IAAI,UAAW,CACpC,QAAS,CAAE,eAAgB,kBAAoB,EAC/C,OAAQ,OACR,KAAM,KAAK,UAAUkQ,CAAc,CAC/C,CAAW,EAEH,MAAMb,EAAO,GAAGrP,EAAO,IAAI,SAAU,CACnC,QAAS,CAAE,eAAgB,kBAAoB,EAC/C,OAAQ,OACR,KAAM,KAAK,UAAUiQ,CAAa,CAC5C,CAAS,CACF,MAAW,CACV,QAAQ,KACN,2FACV,CACO,CACF,CACD,MAAME,GAAoB,MAAOC,GAAY,CAC3C,MAAM,KAAK,kBAAkBA,CAAO,CAC1C,EACI,eAAeC,GAAqBC,EAAe,CACjD,GAAI,CAACtQ,EACH,OACF,IAAIuQ,EAAYD,EAAc,UAC9BtQ,EAAO,WAAa,CAClB,GAAGA,EAAO,WAAW,OAAQgD,GAAMA,EAAE,MAAM,cAAgBuN,CAAS,EACpE,GAAGD,EAAc,UACzB,EACMtQ,EAAO,aAAe,CACpB,GAAGA,EAAO,aAAa,OAAQwQ,GAAMA,EAAE,cAAgBD,CAAS,EAChE,GAAGD,EAAc,YACzB,EACM,MAAMG,EAAYzQ,EAAO,WAAW,KAAMgD,GAAMA,EAAE,OAAS,OAAO,EAC5D0N,EAAa1Q,EAAO,aAAa,KACpCwQ,GAAMA,EAAE,QAAQ,KAAMG,GAAMA,EAAE,CAAC,IAAM,QAAQ,CACtD,EACM3Q,EAAO,kBAAoByQ,GAAaC,EACxC,MAAMP,GAAkBnQ,CAAM,EAC9B0O,EAAW,CACT,KAAM,SACN,KAAM4B,EACN,SAAUX,EACV,SAAAH,CACR,CAAO,CACF,CACD,KAAK,YAAYxP,EAAO,KAAMmE,GAAeyC,EAAa,EAAE,KAC1D,MAAOgK,GAAa,CAClB,IAAI7N,EAcJ,GANA6M,EAAU,CACR,KARepI,EACfoJ,EACAlJ,EACA1H,EAAO,WACP,QACA,EACV,GAE8B,CAAE,EACtB,WAAAuO,EACA,SAAAiB,EACA,WAAAhB,CACV,EACYvH,GAAWuI,EAAUxP,CAAM,EAC7B0O,EAAW,CACT,KAAM,SACN,SAAUiB,EACV,MAAO,UACP,MAAO,GACP,SAAAH,EACA,KAAsB,IAAI,IACtC,CAAW,EACDD,GACE,GAAGvP,EAAO,IAAI,OAAO2P,EAAU,WAAW,GAAG,EAAIA,EAAY,IAAIA,CAAS,EAAE,GAAGG,EAAa,IAAMA,EAAa,EAAE,GACjH,CACE,GAAGF,EACH,aAAAN,CACD,CACF,EAAC,KAAK,CAAC,CAACnK,EAAQ0L,CAAW,IAAM,CAChC,MAAM5B,EAAQ9J,EAAO,KACjB0L,GAAe,KACjBnC,EAAW,CACT,KAAM,OACN,SAAUiB,EACV,SAAAH,EACA,KAAMhI,EACJyH,EACAvH,EACA1H,EAAO,WACP,SACA0K,EAAQ,eACT,EACD,KAAsB,IAAI,KAC1B,WAAA6D,EACA,WAAAC,CAChB,CAAe,EACGrJ,EAAO,eACTkL,GAAqBlL,EAAO,aAAa,EAE3CuJ,EAAW,CACT,KAAM,SACN,SAAUiB,EACV,SAAAH,EACA,MAAO,WACP,IAAKrK,EAAO,iBACZ,MAAO,GACP,KAAsB,IAAI,IAC1C,CAAe,GAEDuJ,EAAW,CACT,KAAM,SACN,MAAO,QACP,SAAUiB,EACV,SAAAH,EACA,QAASrK,EAAO,MAChB,MAAO,GACP,KAAsB,IAAI,IAC1C,CAAe,CAEf,CAAW,EAAE,MAAO/B,GAAM,CACdsL,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAAStL,EAAE,QACX,SAAUuM,EACV,SAAAH,EACA,MAAO,GACP,KAAsB,IAAI,IACxC,CAAa,CACb,CAAW,UACQrO,GAAY,KAAM,CAC3B,KAAM,CAAE,YAAAW,EAAa,KAAArB,CAAM,EAAG,MAAMC,GAClCiB,EACAZ,CACZ,EACU2N,EAAW,CACT,KAAM,SACN,MAAO,UACP,MAAO,GACP,SAAUiB,EACV,SAAAH,EACA,KAAsB,IAAI,IACtC,CAAW,EACD,IAAIhL,EAAM,IAAI,IACZ,GAAG1C,CAAW,MAAMhD,GAClB2B,EACAT,EAAO,KACP,EACD,eAAc8P,EAAa,IAAMA,EAAa,EAAE,EAC7D,EACc,KAAK,KACPtL,EAAI,aAAa,IAAI,SAAU,KAAK,GAAG,EAEzCkL,EAAY,IAAI,UAAUlL,CAAG,EAC7BkL,EAAU,QAAWoB,GAAQ,CACtBA,EAAI,UACPpC,EAAW,CACT,KAAM,SACN,MAAO,QACP,OAAQ,GACR,QAASxQ,EACT,MAAO,GACP,SAAUyR,EACV,SAAAH,EACA,KAAsB,IAAI,IAC1C,CAAe,CAEf,EACUE,EAAU,UAAY,SAASpD,EAAO,CACpC,MAAMc,EAAQ,KAAK,MAAMd,EAAM,IAAI,EAC7B,CAAE,KAAAxI,EAAM,OAAAqF,EAAQ,KAAM8F,CAAO,EAAGlL,GACpCqJ,EACApJ,GAAYwL,CAAQ,CAClC,EACY,GAAI1L,IAAS,UAAYqF,GAAU,CAAC0G,EAClCnB,EAAW,CACT,KAAM,SACN,SAAUiB,EACV,SAAAH,EACA,KAAsB,IAAI,KAC1B,GAAGrG,CACnB,CAAe,EACGA,EAAO,QAAU,SACnBuG,EAAU,MAAK,UAER5L,IAAS,OAAQ,CAC1B4L,EAAU,KAAK,KAAK,UAAU,CAAE,SAAAF,EAAU,aAAAF,CAAc,EAAC,EACzD,MACd,MAAuBxL,IAAS,OAClB4L,EAAU,KAAK,KAAK,UAAU,CAAE,GAAGE,EAAS,aAAAN,CAAc,EAAC,EAClDxL,IAAS,WAClB+L,EAAW1G,EACFrF,IAAS,MAClB4K,EAAW,CACT,KAAM,MACN,IAAKO,EAAM,IACX,MAAOA,EAAM,MACb,SAAUU,EACV,SAAAH,CAChB,CAAe,EACQ1L,IAAS,cAClB4K,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGvF,EACH,MAAiCA,GAAO,MACxC,MAAO,GACP,SAAUwG,EACV,SAAAH,CAChB,CAAe,EAECP,IACFP,EAAW,CACT,KAAM,OACN,KAAsB,IAAI,KAC1B,KAAMlH,EACJyH,EAAM,KACNvH,EACA1H,EAAO,WACP,SACA0K,EAAQ,eACT,EACD,SAAUiF,EACV,SAAAH,EACA,WAAAjB,EACA,WAAAC,CAChB,CAAe,EACGqB,IACFnB,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGmB,EACH,MAAiC1G,GAAO,MACxC,MAAO,GACP,SAAUwG,EACV,SAAAH,CAClB,CAAiB,EACDE,EAAU,MAAK,GAG/B,EACcrS,GAAQ2C,EAAO,SAAW,QAAS,KAAK,EAAI,GAC9C,iBACE,OACA,IAAM0P,EAAU,KAAK,KAAK,UAAU,CAAE,KAAMJ,CAAY,CAAE,CAAC,CACzE,CAEA,SAAmBnO,GAAY,MAAO,CAC5BuN,EAAW,CACT,KAAM,SACN,MAAO,UACP,MAAO,GACP,SAAUiB,EACV,SAAAH,EACA,KAAsB,IAAI,IACtC,CAAW,EACD,IAAIrC,EAAS,IAAI,gBAAgB,CAC/B,SAAUqC,EAAS,SAAU,EAC7B,aAAAF,CACZ,CAAW,EAAE,SAAQ,EACX,IAAI9K,EAAM,IAAI,IACZ,GAAGxE,EAAO,IAAI,eAAe8P,EAAaA,EAAa,IAAM,EAAE,GAAG3C,CAAM,EACpF,EAKU,GAJI,KAAK,KACP3I,EAAI,aAAa,IAAI,SAAU,KAAK,GAAG,EAEzC0I,EAAU,KAAK,OAAO1I,CAAG,EACrB,CAAC0I,EACH,OAAO,QAAQ,OACb,IAAI,MAAM,mCAAqC1I,EAAI,SAAQ,CAAE,CAC3E,EAEU0I,EAAQ,UAAY,eAAeZ,EAAO,CACxC,MAAMc,EAAQ,KAAK,MAAMd,EAAM,IAAI,EAC7B,CAAE,KAAAxI,EAAM,OAAAqF,EAAQ,KAAM8F,CAAO,EAAGlL,GACpCqJ,EACApJ,GAAYwL,CAAQ,CAClC,EACY,GAAI1L,IAAS,UAAYqF,GAAU,CAAC0G,EAClCnB,EAAW,CACT,KAAM,SACN,SAAUiB,EACV,SAAAH,EACA,KAAsB,IAAI,KAC1B,GAAGrG,CACnB,CAAe,EACGA,EAAO,QAAU,UACQ+D,GAAQ,MAAK,EACxC2B,aAEO/K,IAAS,OAAQ,CAC1BwJ,EAAWF,EAAM,SACjB,GAAI,CAACtG,EAAGiK,CAAO,EAAI,MAAMxB,GAAW,GAAGvP,EAAO,IAAI,cAAe,CAC/D,GAAG4P,EACH,aAAAN,EACA,SAAAhC,CAChB,CAAe,EACGyD,IAAY,MACdrC,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAASxQ,EACT,MAAO,GACP,SAAUyR,EACV,SAAAH,EACA,KAAsB,IAAI,IAC5C,CAAiB,EAC0BtC,GAAQ,MAAK,EACxC2B,IAEhB,MAAuB/K,IAAS,WAClB+L,EAAW1G,EACFrF,IAAS,MAClB4K,EAAW,CACT,KAAM,MACN,IAAKO,EAAM,IACX,MAAOA,EAAM,MACb,SAAUU,EACV,SAAAH,CAChB,CAAe,EACQ1L,IAAS,cAClB4K,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGvF,EACH,MAAiCA,GAAO,MACxC,MAAO,GACP,SAAUwG,EACV,SAAAH,CAChB,CAAe,EAECP,IACFP,EAAW,CACT,KAAM,OACN,KAAsB,IAAI,KAC1B,KAAMlH,EACJyH,EAAM,KACNvH,EACA1H,EAAO,WACP,SACA0K,EAAQ,eACT,EACD,SAAUiF,EACV,SAAAH,EACA,WAAAjB,EACA,WAAAC,CAChB,CAAe,EACGqB,IACFnB,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGmB,EACH,MAAiC1G,GAAO,MACxC,MAAO,GACP,SAAUwG,EACV,SAAAH,CAClB,CAAiB,EAC0BtC,GAAQ,MAAK,EACxC2B,KAGhB,CACA,SAAmB1N,GAAY,UAAYA,GAAY,UAAYA,GAAY,YAAcA,GAAY,SAAU,CACzGuN,EAAW,CACT,KAAM,SACN,MAAO,UACP,MAAO,GACP,SAAUiB,EACV,SAAAH,EACA,KAAsB,IAAI,IACtC,CAAW,EACD,IAAIwB,EAAW,GACX,OAAO,OAAW,KAAe,OAAO,SAAa,MACvDA,GAAYjO,EAAgC,QAAO,WAAa,KAAO,OAASA,EAAI,UAGtF,MAAMsE,EAAS2J,EAAS,SAAS,OAAO,EAAI,gBAAgBA,EAAS,MAAM,GAAG,EAAE,CAAC,CAAC,+BAAiB,yBAC7FC,EAAY,OAAO,OAAW,KAAe,OAAO,SAAa,KAAe,OAAO,QAAU,OACjGC,EAAmBxJ,EAAW,SAAW1H,EAAO,UACzBiR,GAAaC,EAAmB/J,GAAa,kBAAmBE,CAAM,EAAI,QAAQ,QAAQ,IAAI,GAC5E,KAAMvH,GAC5CyP,GACL,GAAGvP,EAAO,IAAI,eAAe8P,CAAU,GACvC,CACE,GAAGF,EACH,aAAAN,CACD,EACDxP,CACd,CACW,EACiB,KAAK,MAAO,CAACM,EAAU+I,EAAM,IAAM,CACnD,GAAIA,KAAW,IACbuF,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAASzQ,GACT,MAAO,GACP,SAAU0R,EACV,SAAAH,EACA,KAAsB,IAAI,IAC1C,CAAe,UACQrG,KAAW,IACpBuF,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAASxQ,EACT,MAAO,GACP,SAAUyR,EACV,SAAAH,EACA,KAAsB,IAAI,IAC1C,CAAe,MACI,CACLlC,EAAWlN,EAAS,SACpB,IAAI+Q,GAAW,eAAe/D,GAAO,CACnC,GAAI,CACF,KAAM,CAAE,KAAAtJ,EAAM,OAAQiN,EAAS,KAAM9B,CAAK,EAAKlL,GAC7CqJ,GACApJ,GAAYwL,CAAQ,CACxC,EACkB,GAAI1L,GAAQ,YACV,OAEF,GAAIA,IAAS,UAAYiN,GAAW,CAAClB,EACnCnB,EAAW,CACT,KAAM,SACN,SAAUiB,EACV,SAAAH,EACA,KAAsB,IAAI,KAC1B,GAAGuB,CACzB,CAAqB,UACQjN,IAAS,WAClB+L,EAAWkB,UACFjN,GAAQ,mBACjB,QAAQ,MAAM,mBAA+CiN,GAAQ,OAAO,EAC5ErC,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAAqCqC,GAAQ,SAAY,gCACzD,MAAO,GACP,SAAUpB,EACV,SAAAH,EACA,KAAsB,IAAI,IAChD,CAAqB,UACQ1L,IAAS,MAAO,CACzB4K,EAAW,CACT,KAAM,MACN,IAAKO,EAAM,IACX,MAAOA,EAAM,MACb,SAAUU,EACV,SAAAH,CACtB,CAAqB,EACD,MACpB,MAA6B1L,IAAS,eAClB4K,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGqC,EACH,MAAkCA,GAAQ,MAC1C,MAAO,GACP,SAAUpB,EACV,SAAAH,CACtB,CAAqB,EACGP,GAAS,CAAC,SAAU,WAAY,QAAQ,EAAE,SAAS9N,CAAQ,GAC7DuM,GAAkBC,GAAsBL,EAAU2B,CAAK,GAGvDA,IACFP,EAAW,CACT,KAAM,OACN,KAAsB,IAAI,KAC1B,KAAMlH,EACJyH,EAAM,KACNvH,EACA1H,EAAO,WACP,SACA0K,EAAQ,eACT,EACD,SAAUiF,EACV,SAAAH,CACtB,CAAqB,EACGP,EAAM,eACR,MAAMoB,GAAqBpB,EAAM,aAAa,EAE5CY,GACFnB,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGmB,EACH,MAAkCkB,GAAQ,MAC1C,MAAO,GACP,SAAUpB,EACV,SAAAH,CACxB,CAAuB,IAG2BuB,GAAQ,QAAW,YAA0CA,GAAQ,QAAW,WAC1GnE,GAAgBU,CAAQ,GAC1B,OAAOV,GAAgBU,CAAQ,EAE7BA,KAAYK,IACd,OAAOA,GAAqBL,CAAQ,EAGzC,OAAQlK,EAAG,CACV,QAAQ,MAAM,8BAA+BA,CAAC,EAC9CsL,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAAS,gCACT,MAAO,GACP,SAAUiB,EACV,SAAAH,EACA,KAAsB,IAAI,IAC9C,CAAmB,EACG,CAAC,SAAU,WAAY,QAAQ,EAAE,SAASrO,CAAQ,IACpDkM,GAAaN,EAAeE,GAAK,gBAAgB,EACjDF,EAAc,KAAO,GACrB8B,IAEH,CACjB,EACkBvB,KAAYR,KACdA,GAAwBQ,CAAQ,EAAE,QAC/BzQ,IAAQsU,GAAStU,EAAG,CACvC,EACgB,OAAOiQ,GAAwBQ,CAAQ,GAEzCV,GAAgBU,CAAQ,EAAI6D,GAC5BtE,GAAgB,IAAIS,CAAQ,EACvBP,EAAc,MACjB,MAAM,KAAK,aAEd,CACb,CAAW,CACF,CACF,CACP,EACI,IAAI+B,GAAO,GACX,MAAM5K,GAAS,GACT6K,EAAY,GACZqC,GAAW,CACf,CAAC,OAAO,aAAa,EAAG,IAAMA,GAC9B,KAAAhC,EACA,MAAO,MAAO3S,IACZyS,EAAWzS,CAAK,EACT2S,EAAI,GAEb,OAAQ,UACNP,IACOO,EAAI,GAEb,OAAAY,EACN,EACI,OAAOoB,EACR,OAAQrI,EAAO,CACd,cAAQ,MAAM,wCAAyCA,CAAK,EACtDA,CACP,CACH,CACA,SAASoG,GAAgBpG,EAAO,CAC9B,MAAO,CACL,KAAM,CAACS,EAASC,IAAWA,EAAOV,CAAK,CAC3C,CACA,CACA,SAAS0G,GAAkBvN,EAAUtC,EAAUuC,EAASnC,EAAQ,CAC9D,IAAIwP,EACA5I,EACAc,EACJ,GAAI,OAAO9H,GAAa,SACtB4P,EAAW5P,EACXgH,EAAgB1E,EAAS,kBAAkBsN,CAAQ,EACnD9H,EAAa1H,EAAO,aAAa,KAAMK,GAAQA,EAAI,IAAMT,CAAQ,MAC5D,CACL,MAAM2J,EAAmB3J,EAAS,QAAQ,MAAO,EAAE,EACnD4P,EAAWrN,EAAQoH,CAAgB,EACnC3C,EAAgB1E,EAAS,gBAAgBtC,EAAS,KAAM,GACxD8H,EAAa1H,EAAO,aAAa,KAC9BK,GAAQA,EAAI,IAAM8B,EAAQoH,CAAgB,CACjD,CACG,CACD,GAAI,OAAOiG,GAAa,SACtB,MAAM,IAAI,MACR,2EACN,EAEE,MAAO,CAAE,SAAAA,EAAU,cAAA5I,EAAe,WAAAc,EACpC,CACA,MAAMsD,EAAO,CACX,YAAYrJ,EAAe+I,EAAU,CAAE,OAAQ,CAAC,MAAM,GAAK,CACzDhO,EAAc,KAAM,eAAe,EACnCA,EAAc,KAAM,SAAS,EAC7BA,EAAc,KAAM,QAAQ,EAC5BA,EAAc,KAAM,UAAU,EAC9BA,EAAc,KAAM,UAAW,EAAE,EACjCA,EAAc,KAAM,eAAgB,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,CAAC,CAAC,EAC3EA,EAAc,KAAM,MAAO,EAAK,EAChCA,EAAc,KAAM,cAAe,EAAE,EACrCA,EAAc,KAAM,UAAW,IAAI,EAEnCA,EAAc,KAAM,gBAAiB,CAAE,KAAM,EAAO,GACpDA,EAAc,KAAM,0BAA2B,EAAE,EACjDA,EAAc,KAAM,uBAAwB,EAAE,EAC9CA,EAAc,KAAM,kBAAmB,EAAE,EACzCA,EAAc,KAAM,kBAAmC,IAAI,GAAK,EAChEA,EAAc,KAAM,kBAAmB,IAAI,EAC3CA,EAAc,KAAM,mBAAoB,IAAI,EAC5CA,EAAc,KAAM,kBAAmB,IAAI,EAC3CA,EAAc,KAAM,UAAU,EAC9BA,EAAc,KAAM,cAAc,EAClCA,EAAc,KAAM,QAAQ,EAC5BA,EAAc,KAAM,aAAa,EACjCA,EAAc,KAAM,WAAW,EAC/BA,EAAc,KAAM,QAAQ,EAC5BA,EAAc,KAAM,SAAS,EAC7BA,EAAc,KAAM,aAAa,EACjCA,EAAc,KAAM,gBAAgB,EACpCA,EAAc,KAAM,iBAAiB,EACrC,KAAK,cAAgBiF,EAChB+I,EAAQ,SACXA,EAAQ,OAAS,CAAC,MAAM,GAE1B,KAAK,QAAUA,EACf,KAAK,SAAWnG,GAAS,KAAK,IAAI,EAClC,KAAK,aAAeE,GAAa,KAAK,IAAI,EAC1C,KAAK,YAAcsD,GAAY,KAAK,IAAI,EACxC,KAAK,UAAYiB,GAAU,KAAK,IAAI,EACpC,KAAK,OAASsF,GAAO,KAAK,IAAI,EAC9B,KAAK,QAAUlF,GAAQ,KAAK,IAAI,EAChC,KAAK,YAAcuD,GAAY,KAAK,IAAI,EACxC,KAAK,eAAiBhN,GAAe,KAAK,IAAI,EAC9C,KAAK,gBAAkBY,GAAgB,KAAK,IAAI,EAChD,KAAK,OAAS6E,GAAO,KAAK,IAAI,CAC/B,CACD,MAAMtC,EAAO2J,EAAM,CACjB,MAAM3M,EAAU,IAAI,QAAiC2M,GAAK,SAAY,EAAE,EACxE,OAAI,MAAQ,KAAK,SACf3M,EAAQ,OAAO,SAAU,KAAK,OAAO,EAEhC,MAAMgD,EAAO,CAAE,GAAG2J,EAAM,QAAA3M,CAAS,EACzC,CACD,OAAO0E,EAAK,CACV,YAAK,iBAAmB,IAAI,gBAC5B,KAAK,gBAAkB4J,GAAgB5J,EAAI,SAAQ,EAAI,CACrD,OAAQ,KAAK,iBAAiB,MACpC,CAAK,EACM,KAAK,eACb,CACD,MAAM,MAAO,CACX,IAAI3E,EACJ,IAAK,OAAO,OAAW,KAAe,EAAE,cAAe,UAAY,CAAC,OAAO,UAAW,CACpF,MAAMwR,EAAK,YAAM,OAAO,gCAAuB,0CAC/C,OAAO,UAAYA,EAAG,SACvB,CACD,GAAI,CACE,KAAK,QAAQ,MACf,MAAM,KAAK,kBAEb,MAAM,KAAK,gBAAe,EAAG,KAC3B,CAAC,CAAE,OAAArR,CAAQ,IAAK,KAAK,kBAAkBA,CAAM,CACrD,CACK,OAAQ,EAAG,CACV,MAAM,MAAM,CAAC,CACd,CACD,KAAK,SAAW,MAAM,KAAK,SAAQ,EACnC,KAAK,QAAUV,KAAmBO,EAAK,KAAK,SAAW,KAAO,OAASA,EAAG,eAAiB,CAAE,EAC9F,CACD,MAAM,kBAAkByR,EAAS,CAgB/B,GAfIA,IACF,KAAK,OAASA,EACV,KAAK,QAAU,KAAK,OAAO,mBACzB,KAAK,OAAO,UAAY,KAAK,QAAQ,WACvC,KAAK,IAAM,MAAMpS,GACf,KAAK,OAAO,SACZ,KAAK,QAAQ,SACb,KAAK,OACjB,IAIQoS,EAAQ,UAAY,KAAK,QAAQ,WACnC,KAAK,IAAM,MAAMpS,GAAQoS,EAAQ,SAAU,KAAK,QAAQ,QAAQ,GAE9D,KAAK,QAAU,KAAK,OAAO,kBAAmB,CAChD,MAAMC,EAAgB,IAAI,IACxB,GAAG,KAAK,OAAO,IAAI,cAAc,KAAK,YAAY,EAC1D,EACU,KAAK,KACPA,EAAc,aAAa,IAAI,SAAU,KAAK,GAAG,EAE9C,KAAK,kBACR,KAAK,gBAAkB,KAAK,OAAOA,CAAa,EAEnD,CACF,CACD,aAAa,QAAQ5P,EAAe+I,EAAU,CAC5C,OAAQ,CAAC,MAAM,CACnB,EAAK,CACD,MAAMrC,EAAU,IAAI,KAAK1G,EAAe+I,CAAO,EAC/C,aAAMrC,EAAQ,OACPA,CACR,CACD,OAAQ,CACN,IAAIxI,GACHA,EAAK,KAAK,kBAAoB,MAAgBA,EAAG,OACnD,CACD,aAAa,UAAU8B,EAAe+I,EAAU,CAC9C,OAAQ,CAAC,MAAM,CACnB,EAAK,CACD,OAAOD,GAAU9I,EAAe+I,CAAO,CACxC,CACD,MAAM,iBAAkB,CACtB,KAAM,CAAE,cAAAlK,EAAe,KAAAC,EAAM,SAAA0J,CAAQ,EAAK,MAAMzJ,GAC9C,KAAK,cACL,KAAK,QAAQ,QACnB,EACU,CAAE,gBAAAmJ,CAAe,EAAK,KAAK,QACjC,IAAI7J,EACJ,GAAI,CAEF,GADAA,EAAS,MAAM,KAAK,eAAe,GAAGQ,CAAa,KAAKC,CAAI,EAAE,EAC1D,CAACT,EACH,MAAM,IAAI,MAAM7B,CAAgB,EAElC,OAAO,KAAK,eAAe6B,CAAM,CAClC,OAAQoD,EAAG,CACV,GAAI+G,GAAYN,EACdD,GACEO,EACA1I,GAAc,KAAK0I,CAAQ,EAAI,aAAe,YAC9C,KAAK,oBACf,MAEQ,OAAIN,GACFA,EAAgB,CACd,OAAQ,QACR,QAAS,6BACT,YAAa,QACb,OAAQ,WACpB,CAAW,EACG,MAAMzG,CAAC,CAEhB,CACF,CACD,MAAM,eAAekO,EAAS,CAO5B,GANA,KAAK,OAASA,EACV,OAAO,OAAW,KAAe,OAAO,SAAa,KACnD,OAAO,SAAS,WAAa,WAC/B,KAAK,OAAO,KAAO,KAAK,OAAO,KAAK,QAAQ,UAAW,UAAU,GAGjE,KAAK,OAAO,cACd,OAAO,KAAK,qBAEd,GAAI,CACF,KAAK,SAAW,MAAM,KAAK,SAAQ,CACpC,OAAQ,EAAG,CACV,QAAQ,MAAMjT,GAAqB,EAAE,OAAO,CAC7C,CACD,OAAO,KAAK,oBACb,CACD,MAAM,qBAAqB8K,EAAQ,CACjC,GAAI,CAAC,KACH,MAAM,IAAI,MAAMhL,CAAgB,EAElC,KAAM,CAAE,gBAAA0L,CAAe,EAAK,KAAK,QAGjC,GAFIA,GACFA,EAAgBV,CAAM,EACpBA,EAAO,SAAW,UACpB,GAAI,CAEF,GADA,KAAK,OAAS,MAAM,KAAK,gBAAe,EACpC,CAAC,KAAK,OACR,MAAM,IAAI,MAAMhL,CAAgB,EAGlC,OADgB,MAAM,KAAK,eAAe,KAAK,MAAM,CAEtD,OAAQiF,EAAG,CACV,MAAIyG,GACFA,EAAgB,CACd,OAAQ,QACR,QAAS,6BACT,YAAa,QACb,OAAQ,WACpB,CAAW,EAEGzG,CACP,CAEJ,CACD,MAAM,iBAAiBoO,EAAcC,EAASnO,EAAM,CAClD,IAAIzD,EACJ,GAAI,CAAC,KAAK,OACR,MAAM,IAAI,MAAM1B,CAAgB,EAElC,MAAM2B,EAAU,GACV,CAAE,SAAAiB,CAAQ,EAAK,KAAK,QACpB,CAAE,aAAAuO,CAAc,EAAG,KACrBvO,IACFjB,EAAQ,cAAgB,UAAU,KAAK,QAAQ,QAAQ,IAEzD,IAAI4E,EACAnB,EAAY,KAAK,OAAO,WAAW,KACpCN,GAASA,EAAK,KAAOuO,CAC5B,GACS3R,EAAkC0D,GAAU,QAAU,MAAgB1D,EAAG,SAC5E6E,EAAWnB,EAAU,MAAM,SAE3BmB,EAAW,KAAK,OAAO,KAEzB,IAAIuE,EACJ,GAAI,WAAY3F,EAAM,CACpB2F,EAAO,IAAI,SACX,UAAWzM,KAAO8G,EAAK,KACjB9G,IAAQ,UAEZyM,EAAK,OAAOzM,EAAK8G,EAAK,KAAK9G,CAAG,CAAC,EAEjCyM,EAAK,IAAI,eAAgBuI,EAAa,SAAU,GAChDvI,EAAK,IAAI,UAAWwI,CAAO,EAC3BxI,EAAK,IAAI,eAAgBqG,CAAY,CAC3C,MACMrG,EAAO,KAAK,UAAU,CACpB,KAAA3F,EACA,aAAAkO,EACA,QAAAC,EACA,aAAAnC,CACR,CAAO,EACDxP,EAAQ,cAAc,EAAI,mBAExBiB,IACFjB,EAAQ,cAAgB,UAAUiB,CAAQ,IAE5C,GAAI,CACF,MAAMX,EAAW,MAAM,KAAK,MAAM,GAAGsE,CAAQ,qBAAsB,CACjE,OAAQ,OACR,KAAAuE,EACA,QAAAnJ,EACA,YAAa,SACrB,CAAO,EACD,GAAI,CAACM,EAAS,GACZ,MAAM,IAAI,MACR,0CAA4CA,EAAS,UAC/D,EAGM,OADe,MAAMA,EAAS,MAE/B,OAAQgD,EAAG,CACV,QAAQ,KAAKA,CAAC,CACf,CACF,CACD,YAAYsO,EAAa,CACvB,KAAK,QAAUrQ,GAAsBqQ,CAAW,EAAE,KAAK,IAAI,CAC5D,CACD,oBAAqB,CACnB,MAAO,CACL,OAAQ,KAAK,OACb,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,SAAU,KAAK,SACf,iBAAkB,KAAK,gBAC7B,CACG,CACH,CC55EA,IAAIC,GAA+B,GAGlC,iBAAkB,QAAQ,WAC1B,uBAAwB,SAAS,YAMjCA,GAA+B,uBAHN,SACvB,cAAc,KAAK,EACnB,aAAa,CAAE,KAAM,OAAQ,GAIhB,SAAAC,GAAUpN,EAAayJ,EAAoC,CAC1E,MAAM4D,EAAO,IAAI,IAAI,YAAY,GAAG,EAAE,OAChCC,EAAO,IAAI,IAAItN,EAAKqN,CAAI,EAAE,KAG5B,GAFkB,SAAS,cAAc,cAAcC,CAAI,IAAI,EAEhD,OAAO,QAAQ,UAE5B,MAAAC,EAAO,SAAS,cAAc,MAAM,EAC1C,OAAAA,EAAK,IAAM,aACXA,EAAK,KAAOD,EAEL,IAAI,QAAQ,CAAC7Q,EAAK+Q,IAAQ,CAChCD,EAAK,iBAAiB,OAAQ,IAAM9Q,EAAK,GACpC8Q,EAAA,iBAAiB,QAAS,IAAM,CAC5B,cAAM,6BAA6BD,CAAI,EAAE,EAC7C7Q,GAAA,CACJ,EACDgN,EAAO,YAAY8D,CAAI,EACvB,CACF,CAEO,SAASE,GACfC,EACAC,EACAC,EAAgB,SAAS,cAAc,OAAO,EACpB,CAC1B,GAAI,CAACT,GAAqC,YAC1CS,EAAc,OAAO,EAEf,MAAAC,EAAa,IAAI,cACvBA,EAAW,YAAYH,CAAM,EAE7B,IAAII,EAAe,GACnBJ,EAASA,EAAO,QAAQ,8BAA+B,CAAC9G,EAAO5G,KAC9D8N,GAAgB,eAAe9N,CAAG;AAAA,EAC3B,GACP,EAED,MAAM+N,EAAQF,EAAW,SAEzB,IAAIG,EAAa,GACbC,EAAmB,iDAAiDN,CAAO,aAE/E,QAAS7R,EAAI,EAAGA,EAAIiS,EAAM,OAAQjS,IAAK,CAChC,MAAAoS,EAAOH,EAAMjS,CAAC,EAEpB,IAAIqS,EAAeD,EAAK,QAAQ,SAAS,OAAO,EAChD,GAAIA,aAAgB,aAAc,CACjC,MAAME,EAAWF,EAAK,aACtB,GAAIE,EAAU,CACP,MAAAC,EAAeD,EACnB,QAAQ,QAAS,EAAE,EACnB,MAAM,GAAG,EACT,IACCE,GACA,GAAGH,EAAe,QAAU,EAAE,IAAIF,CAAgB,IAAIK,EAAE,MAAM,KAE/D,KAAK,GAAG,EAEVN,GAAcE,EAAK,QACnBF,GAAcE,EAAK,QAAQ,QAAQE,EAAUC,CAAY,CAC1D,UACUH,aAAgB,aAAc,CACxC,IAAIK,EAAiB,UAAUL,EAAK,MAAM,SAAS,KACnD,QAASM,EAAI,EAAGA,EAAIN,EAAK,SAAS,OAAQM,IAAK,CACxC,MAAAC,EAAYP,EAAK,SAASM,CAAC,EACjC,GAAIC,aAAqB,aAAc,CACtC,IAAIN,EAAeM,EAAU,QAAQ,SAAS,QAAQ,EACtD,MAAML,EAAWK,EAAU,aACrBJ,EAAeD,EACnB,QAAQ,QAAS,EAAE,EACnB,MAAM,GAAG,EACT,IACCE,GACA,GACCH,EAAe,QAAU,EAC1B,IAAIF,CAAgB,IAAIK,EAAE,MAAM,KAEjC,KAAK,GAAG,EACVC,GAAkBE,EAAU,QAAQ,QAAQL,EAAUC,CAAY,CACnE,CACD,CACkBE,GAAA,IACJP,GAAAO,CAAA,SACJL,aAAgB,iBAAkB,CAC9BF,GAAA,cAAcE,EAAK,IAAI,KACrC,QAASM,EAAI,EAAGA,EAAIN,EAAK,SAAS,OAAQM,IAAK,CACxC,MAAAC,EAAYP,EAAK,SAASM,CAAC,EAC7BC,aAAqB,kBACxBT,GAAc,GAAGS,EAAU,OAAO,MAAMA,EAAU,MAAM,OAAO,KAEjE,CACcT,GAAA,SACJE,aAAgB,kBACZF,GAAA,gBAAgBE,EAAK,MAAM,OAAO,KAElD,CACA,OAAAF,EAAaF,EAAeE,EAC5BJ,EAAc,YAAcI,EAEnB,cAAK,YAAYJ,CAAa,EAChCA,CACR,CCnGA,MAAMc,GAAY,gBAElB,IAAIC,GAEJA,GAAQ,gBAER,IAAIC,GACAC,GACAC,GAAU,IAAI,QAASrS,GAAQ,CAC3BoS,GAAApS,CACR,CAAC,EACD,eAAesS,IAA2B,CACvBH,IAAA,YAAM,OAAO,qBAAgB,EAAG,KAAAvK,KAAA,oDAC7CwK,IACN,CAEA,SAASG,IAA8B,CACtC,MAAMC,EAAI,CACT,gBAAiBC,GAAO,iBAEzB,UAAWlX,KAAOkX,GACblX,IAAQ,oBACRA,IAAQ,qBAETiX,EAAAjX,CAAG,EAAIiX,EAAE,gBAGTA,EAAAjX,CAAG,EAAIkX,GAAOlX,CAAG,GAIrB,OAAO,2BAA6BiX,EACpC,MAAME,UAAkB,WAAY,CAgBnC,aAAc,CACP,QACD,UAAO,KAAK,aAAa,MAAM,EAC/B,WAAQ,KAAK,aAAa,OAAO,EACjC,SAAM,KAAK,aAAa,KAAK,EAE7B,wBAAqB,KAAK,aAAa,oBAAoB,EAChE,KAAK,eAAiB,KAAK,aAAa,gBAAgB,GAAK,QAC7D,KAAK,SAAW,KAAK,aAAa,OAAO,GAAK,OAC9C,KAAK,UAAY,KAAK,aAAa,WAAW,GAAK,OACnD,KAAK,KAAO,KAAK,aAAa,MAAM,GAAK,GACpC,gBAAa,KAAK,aAAa,YAAY,EAC3C,WAAQ,KAAK,aAAa,OAAO,EACjC,gBAAa,KAAK,aAAa,YAAY,EAChD,KAAK,SAAW,GAChB,KAAK,QAAU,EAChB,CAEA,MAAM,mBAAmC,CACxC,MAAMJ,GAAU,EAChB,KAAK,QAAU,GAEX,KAAK,KACR,KAAK,IAAI,WAGN,OAAOJ,IAAU,UACpBA,GAAM,QAAS1N,GAAMmM,GAAUnM,EAAG,SAAS,IAAI,CAAC,EAG3C,MAAAmM,GAAUsB,GAAW,SAAS,IAAI,EAElC,MAAA5G,EAAQ,IAAI,YAAY,YAAa,CAC1C,QAAS,GACT,WAAY,GACZ,SAAU,GACV,EAEgB,IAAI,iBAAkBsH,GAAc,CACpD,KAAK,cAActH,CAAK,EACxB,EAEQ,QAAQ,KAAM,CAAE,UAAW,EAAM,GAErC,SAAM,IAAI8G,GAAe,CAC7B,OAAQ,KACR,MAAO,CAEN,MAAO,KAAK,MAAQ,KAAK,MAAM,OAAS,KAAK,MAC7C,IAAK,KAAK,IAAM,KAAK,IAAI,OAAS,KAAK,IACvC,KAAM,KAAK,KAAO,KAAK,KAAK,OAAS,KAAK,KAE1C,KAAM,KAAK,OAAS,QACpB,UAAW,KAAK,YAAc,QAC9B,SAAU,KAAK,WAAa,QAC5B,eAAgB,KAAK,eACrB,MAAO,KAAK,QAAU,OAEtB,QAAS,SACT,WAAY,KAAK,WAEjB,WAAY,KAAK,aAAe,OAChC,mBAAoB,KAAK,qBAAuB,OAEhD,OAAApI,GAGA,SAAU,OAAO,kBAAoB,KACtC,EACA,EAEG,KAAK,UACR,KAAK,aAAa,KAAK,SAAS,KAAM,KAAK,SAAS,KAAK,EAG1D,KAAK,QAAU,EAChB,CAEA,WAAW,oBAA+C,CAClD,OAAC,MAAO,QAAS,MAAM,CAC/B,CAEA,MAAM,yBACL5C,EACAyL,EACAC,EACgB,CAEhB,GADM,MAAAR,IAEJlL,IAAS,QAAUA,IAAS,SAAWA,IAAS,QACjD0L,IAAYD,EACX,CAED,GADA,KAAK,SAAW,CAAE,KAAAzL,EAAM,MAAO0L,CAAQ,EACnC,KAAK,QAAS,OAEd,KAAK,KACR,KAAK,IAAI,WAGV,KAAK,MAAQ,KACb,KAAK,KAAO,KACZ,KAAK,IAAM,KAEP1L,IAAS,OACZ,KAAK,KAAO0L,EACF1L,IAAS,QACnB,KAAK,MAAQ0L,EACH1L,IAAS,QACnB,KAAK,IAAM0L,GAGP,SAAM,IAAIV,GAAe,CAC7B,OAAQ,KACR,MAAO,CAEN,MAAO,KAAK,MAAQ,KAAK,MAAM,OAAS,KAAK,MAC7C,IAAK,KAAK,IAAM,KAAK,IAAI,OAAS,KAAK,IACvC,KAAM,KAAK,KAAO,KAAK,KAAK,OAAS,KAAK,KAE1C,KAAM,KAAK,OAAS,QACpB,UAAW,KAAK,YAAc,QAC9B,SAAU,KAAK,WAAa,QAC5B,eAAgB,KAAK,eACrB,MAAO,KAAK,QAAU,OAEtB,QAAS,SACT,WAAY,KAAK,WAEjB,WAAY,KAAK,aAAe,OAChC,mBACC,KAAK,qBAAuB,OAE7B,OAAApI,GAGA,SAAU,OAAO,kBAAoB,KACtC,EACA,EAED,KAAK,SAAW,EACjB,CACD,CACD,CACK,eAAe,IAAI,YAAY,GACpB,sBAAO,aAAc2I,CAAS,CAC/C,CAEAH,GAAsB", "names": ["__defProp", "__defNormalProp", "obj", "key", "value", "__publicField", "__access<PERSON>heck", "member", "msg", "__privateGet", "getter", "__privateAdd", "__privateSet", "setter", "_currentLine", "fn", "semiver", "a", "b", "bool", "HOST_URL", "UPLOAD_URL", "LOGIN_URL", "CONFIG_URL", "API_INFO_URL", "RUNTIME_URL", "SLEEPTIME_URL", "SPACE_FETCHER_URL", "QUEUE_FULL_MSG", "BROKEN_CONNECTION_MSG", "CONFIG_ERROR_MSG", "SPACE_STATUS_ERROR_MSG", "API_INFO_ERROR_MSG", "SPACE_METADATA_ERROR_MSG", "INVALID_URL_MSG", "UNAUTHORIZED_MSG", "INVALID_CREDENTIALS_MSG", "MISSING_CREDENTIALS_MSG", "NODEJS_FS_ERROR_MSG", "ROOT_URL_ERROR_MSG", "FILE_PROCESSING_ERROR_MSG", "resolve_root", "base_url", "root_path", "prioritize_base", "get_jwt", "space", "token", "cookies", "map_names_to_ids", "fns", "apis", "api_name", "id", "resolve_config", "endpoint", "_a", "headers", "path", "config", "config_root", "config_url", "join_urls", "response", "dep", "i", "resolve_cookies", "http_protocol", "host", "process_endpoint", "cookie_header", "get_cookie_header", "auth", "_fetch", "hf_token", "formData", "res", "determine_protocol", "protocol", "pathname", "parse_and_set_cookies", "cookie", "cookie_name", "cookie_value", "RE_SPACE_NAME", "RE_SPACE_DOMAIN", "app_reference", "_app_reference", "_host", "ws_protocol", "urls", "part", "transform_api_info", "api_info", "api_map", "transformed_info", "category", "parameters", "returns", "_b", "_c", "_d", "dependencyIndex", "dependencyTypes", "components", "input", "_a2", "c", "comp", "idx", "new_param", "e", "transform_type", "data", "component", "serializer", "signature_type", "get_description", "get_type", "p", "r", "type", "handle_message", "last_status", "map_data_to_params", "values", "resolved_data", "provided_keys", "param", "index", "view_api", "url", "upload_files", "root_url", "files", "upload_id", "chunkSize", "uploadResponses", "chunk", "file", "upload_url", "error_text", "output", "upload", "file_data", "max_file_size", "file_data2", "oversized_files", "f", "FileData", "prepare_files", "is_stream", "orig_name", "size", "blob", "mime_type", "alt_text", "Command", "command", "meta", "update_object", "object", "newValue", "stack", "key2", "walk_and_store_blobs", "root", "endpoint_info", "blob_refs", "_", "new_path", "array_refs", "skip_queue", "fn_queue", "post_message", "message", "origin", "_rej", "channel", "handle_payload", "resolved_payload", "dependency", "with_null_state", "updated_payload", "payload_index", "input_id", "handle_blob", "self", "process_local_file_commands", "blobRefs", "file_url", "name", "client2", "recursively_process_commands", "process_single_command", "cmd_item", "fileBuffer", "fullPath", "fs", "__vitePreload", "n", "fileData", "error", "post_data", "body", "additional_headers", "status", "predict", "data_returned", "status_complete", "trimmed_endpoint", "resolve", "reject", "app", "result", "check_space_status", "status_callback", "_status", "stage", "space_name", "discussions_enabled", "RE_DISABLED_DISCUSSION", "space_id", "get_space_hardware", "hardware", "set_space_timeout", "timeout", "hardware_types", "duplicate", "options", "_private", "v", "user", "original_hardware", "requested_hardware", "Client", "duplicated_space", "get_space_reference", "regex", "match", "TextLineStream", "chars", "controller", "lfIndex", "crIndex", "endIndex", "currentLine", "stream$1", "decoder", "split2", "split", "fallback", "events", "signal", "iter", "line", "reader", "event", "field", "stream", "init", "req", "open_stream", "event_callbacks", "unclosed_events", "pending_stream_messages", "stream_status", "jwt", "that", "stream2", "params", "_data", "close_stream", "event_id", "event_id2", "fn2", "abort_controller", "apply_diff_stream", "pending_diff_streams", "new_data", "apply_diff", "diff", "action", "apply_edit", "target", "current", "last_path", "readable_stream", "instance", "submit", "event_data", "trigger_id", "all_events", "fire_event", "events_to_publish", "push_event", "close", "done", "resolvers", "push", "data2", "push_error", "thenable_reject", "next", "fetch2", "session_hash", "post_data2", "fn_index", "get_endpoint_info", "websocket", "_endpoint", "payload", "complete", "url_params", "acc", "cancel", "reset_request", "cancel_request", "resolve_heartbeat", "config2", "handle_render_config", "render_config", "render_id", "d", "any_state", "any_unload", "t", "_payload", "status_code", "evt", "status2", "hostname", "is_iframe", "is_zerogpu_space", "callback", "iterator", "ws", "_config", "heartbeat_url", "component_id", "fn_name", "raw_cookies", "supports_adopted_stylesheets", "mount_css", "base", "_url", "link", "rej", "prefix_css", "string", "version", "style_element", "stylesheet", "importString", "rules", "css_string", "gradio_css_infix", "rule", "is_dark_rule", "selector", "new_selector", "s", "mediaCssString", "j", "innerRule", "ENTRY_CSS", "FONTS", "IndexComponent", "_res", "pending", "get_index", "create_custom_element", "o", "svelte", "GradioApp", "mutations", "old_val", "new_val"], "ignoreList": [], "sources": ["../../../../client/js/dist/index.js", "../../../../js/app/src/css.ts", "../../../../js/app/src/main.ts"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar __accessCheck = (obj, member, msg) => {\n  if (!member.has(obj))\n    throw TypeError(\"Cannot \" + msg);\n};\nvar __privateGet = (obj, member, getter) => {\n  __accessCheck(obj, member, \"read from private field\");\n  return getter ? getter.call(obj) : member.get(obj);\n};\nvar __privateAdd = (obj, member, value) => {\n  if (member.has(obj))\n    throw TypeError(\"Cannot add the same private member more than once\");\n  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\n};\nvar __privateSet = (obj, member, value, setter) => {\n  __accessCheck(obj, member, \"write to private field\");\n  setter ? setter.call(obj, value) : member.set(obj, value);\n  return value;\n};\nvar _currentLine;\nvar fn = new Intl.Collator(0, { numeric: 1 }).compare;\nfunction semiver(a, b, bool) {\n  a = a.split(\".\");\n  b = b.split(\".\");\n  return fn(a[0], b[0]) || fn(a[1], b[1]) || (b[2] = b.slice(2).join(\".\"), bool = /[.-]/.test(a[2] = a.slice(2).join(\".\")), bool == /[.-]/.test(b[2]) ? fn(a[2], b[2]) : bool ? -1 : 1);\n}\nconst HOST_URL = \"host\";\nconst UPLOAD_URL = \"upload\";\nconst LOGIN_URL = \"login\";\nconst CONFIG_URL = \"config\";\nconst API_INFO_URL = \"info\";\nconst RUNTIME_URL = \"runtime\";\nconst SLEEPTIME_URL = \"sleeptime\";\nconst SPACE_FETCHER_URL = \"https://gradio-space-api-fetcher-v2.hf.space/api\";\nconst QUEUE_FULL_MSG = \"This application is currently busy. Please try again. \";\nconst BROKEN_CONNECTION_MSG = \"Connection errored out. \";\nconst CONFIG_ERROR_MSG = \"Could not resolve app config. \";\nconst SPACE_STATUS_ERROR_MSG = \"Could not get space status. \";\nconst API_INFO_ERROR_MSG = \"Could not get API info. \";\nconst SPACE_METADATA_ERROR_MSG = \"Space metadata could not be loaded. \";\nconst INVALID_URL_MSG = \"Invalid URL. A full URL path is required.\";\nconst UNAUTHORIZED_MSG = \"Not authorized to access this space. \";\nconst INVALID_CREDENTIALS_MSG = \"Invalid credentials. Could not login. \";\nconst MISSING_CREDENTIALS_MSG = \"Login credentials are required to access this space.\";\nconst NODEJS_FS_ERROR_MSG = \"File system access is only available in Node.js environments\";\nconst ROOT_URL_ERROR_MSG = \"Root URL not found in client config\";\nconst FILE_PROCESSING_ERROR_MSG = \"Error uploading file\";\nfunction resolve_root(base_url, root_path, prioritize_base) {\n  if (root_path.startsWith(\"http://\") || root_path.startsWith(\"https://\")) {\n    return prioritize_base ? base_url : root_path;\n  }\n  return base_url + root_path;\n}\nasync function get_jwt(space, token, cookies) {\n  try {\n    const r = await fetch(`https://huggingface.co/api/spaces/${space}/jwt`, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n        ...cookies ? { Cookie: cookies } : {}\n      }\n    });\n    const jwt = (await r.json()).token;\n    return jwt || false;\n  } catch (e) {\n    return false;\n  }\n}\nfunction map_names_to_ids(fns) {\n  let apis = {};\n  fns.forEach(({ api_name, id }) => {\n    if (api_name)\n      apis[api_name] = id;\n  });\n  return apis;\n}\nasync function resolve_config(endpoint) {\n  var _a;\n  const headers = this.options.hf_token ? { Authorization: `Bearer ${this.options.hf_token}` } : {};\n  headers[\"Content-Type\"] = \"application/json\";\n  if (typeof window !== \"undefined\" && window.gradio_config && location.origin !== \"http://localhost:9876\" && !window.gradio_config.dev_mode) {\n    const path = window.gradio_config.root;\n    const config = window.gradio_config;\n    let config_root = resolve_root(endpoint, config.root, false);\n    config.root = config_root;\n    return { ...config, path };\n  } else if (endpoint) {\n    const config_url = join_urls(endpoint, CONFIG_URL);\n    const response = await this.fetch(config_url, {\n      headers,\n      credentials: \"include\"\n    });\n    if ((response == null ? void 0 : response.status) === 401 && !this.options.auth) {\n      throw new Error(MISSING_CREDENTIALS_MSG);\n    } else if ((response == null ? void 0 : response.status) === 401 && this.options.auth) {\n      throw new Error(INVALID_CREDENTIALS_MSG);\n    }\n    if ((response == null ? void 0 : response.status) === 200) {\n      let config = await response.json();\n      config.path = config.path ?? \"\";\n      config.root = endpoint;\n      (_a = config.dependencies) == null ? void 0 : _a.forEach((dep, i) => {\n        if (dep.id === void 0) {\n          dep.id = i;\n        }\n      });\n      return config;\n    } else if ((response == null ? void 0 : response.status) === 401) {\n      throw new Error(UNAUTHORIZED_MSG);\n    }\n    throw new Error(CONFIG_ERROR_MSG);\n  }\n  throw new Error(CONFIG_ERROR_MSG);\n}\nasync function resolve_cookies() {\n  const { http_protocol, host } = await process_endpoint(\n    this.app_reference,\n    this.options.hf_token\n  );\n  try {\n    if (this.options.auth) {\n      const cookie_header = await get_cookie_header(\n        http_protocol,\n        host,\n        this.options.auth,\n        this.fetch,\n        this.options.hf_token\n      );\n      if (cookie_header)\n        this.set_cookies(cookie_header);\n    }\n  } catch (e) {\n    throw Error(e.message);\n  }\n}\nasync function get_cookie_header(http_protocol, host, auth, _fetch, hf_token) {\n  const formData = new FormData();\n  formData.append(\"username\", auth == null ? void 0 : auth[0]);\n  formData.append(\"password\", auth == null ? void 0 : auth[1]);\n  let headers = {};\n  if (hf_token) {\n    headers.Authorization = `Bearer ${hf_token}`;\n  }\n  const res = await _fetch(`${http_protocol}//${host}/${LOGIN_URL}`, {\n    headers,\n    method: \"POST\",\n    body: formData,\n    credentials: \"include\"\n  });\n  if (res.status === 200) {\n    return res.headers.get(\"set-cookie\");\n  } else if (res.status === 401) {\n    throw new Error(INVALID_CREDENTIALS_MSG);\n  } else {\n    throw new Error(SPACE_METADATA_ERROR_MSG);\n  }\n}\nfunction determine_protocol(endpoint) {\n  if (endpoint.startsWith(\"http\")) {\n    const { protocol, host, pathname } = new URL(endpoint);\n    if (host.endsWith(\"hf.space\")) {\n      return {\n        ws_protocol: \"wss\",\n        host,\n        http_protocol: protocol\n      };\n    }\n    return {\n      ws_protocol: protocol === \"https:\" ? \"wss\" : \"ws\",\n      http_protocol: protocol,\n      host: host + (pathname !== \"/\" ? pathname : \"\")\n    };\n  } else if (endpoint.startsWith(\"file:\")) {\n    return {\n      ws_protocol: \"ws\",\n      http_protocol: \"http:\",\n      host: \"lite.local\"\n      // Special fake hostname only used for this case. This matches the hostname allowed in `is_self_host()` in `js/wasm/network/host.ts`.\n    };\n  }\n  return {\n    ws_protocol: \"wss\",\n    http_protocol: \"https:\",\n    host: endpoint\n  };\n}\nconst parse_and_set_cookies = (cookie_header) => {\n  let cookies = [];\n  const parts = cookie_header.split(/,(?=\\s*[^\\s=;]+=[^\\s=;]+)/);\n  parts.forEach((cookie) => {\n    const [cookie_name, cookie_value] = cookie.split(\";\")[0].split(\"=\");\n    if (cookie_name && cookie_value) {\n      cookies.push(`${cookie_name.trim()}=${cookie_value.trim()}`);\n    }\n  });\n  return cookies;\n};\nconst RE_SPACE_NAME = /^[a-zA-Z0-9_\\-\\.]+\\/[a-zA-Z0-9_\\-\\.]+$/;\nconst RE_SPACE_DOMAIN = /.*hf\\.space\\/{0,1}$/;\nasync function process_endpoint(app_reference, hf_token) {\n  const headers = {};\n  if (hf_token) {\n    headers.Authorization = `Bearer ${hf_token}`;\n  }\n  const _app_reference = app_reference.trim().replace(/\\/$/, \"\");\n  if (RE_SPACE_NAME.test(_app_reference)) {\n    try {\n      const res = await fetch(\n        `https://huggingface.co/api/spaces/${_app_reference}/${HOST_URL}`,\n        { headers }\n      );\n      const _host = (await res.json()).host;\n      return {\n        space_id: app_reference,\n        ...determine_protocol(_host)\n      };\n    } catch (e) {\n      throw new Error(SPACE_METADATA_ERROR_MSG);\n    }\n  }\n  if (RE_SPACE_DOMAIN.test(_app_reference)) {\n    const { ws_protocol, http_protocol, host } = determine_protocol(_app_reference);\n    return {\n      space_id: host.replace(\".hf.space\", \"\"),\n      ws_protocol,\n      http_protocol,\n      host\n    };\n  }\n  return {\n    space_id: false,\n    ...determine_protocol(_app_reference)\n  };\n}\nconst join_urls = (...urls) => {\n  try {\n    return urls.reduce((base_url, part) => {\n      base_url = base_url.replace(/\\/+$/, \"\");\n      part = part.replace(/^\\/+/, \"\");\n      return new URL(part, base_url + \"/\").toString();\n    });\n  } catch (e) {\n    throw new Error(INVALID_URL_MSG);\n  }\n};\nfunction transform_api_info(api_info, config, api_map) {\n  const transformed_info = {\n    named_endpoints: {},\n    unnamed_endpoints: {}\n  };\n  Object.keys(api_info).forEach((category) => {\n    if (category === \"named_endpoints\" || category === \"unnamed_endpoints\") {\n      transformed_info[category] = {};\n      Object.entries(api_info[category]).forEach(\n        ([endpoint, { parameters, returns }]) => {\n          var _a, _b, _c, _d;\n          const dependencyIndex = ((_a = config.dependencies.find(\n            (dep) => dep.api_name === endpoint || dep.api_name === endpoint.replace(\"/\", \"\")\n          )) == null ? void 0 : _a.id) || api_map[endpoint.replace(\"/\", \"\")] || -1;\n          const dependencyTypes = dependencyIndex !== -1 ? (_b = config.dependencies.find((dep) => dep.id == dependencyIndex)) == null ? void 0 : _b.types : { continuous: false, generator: false, cancel: false };\n          if (dependencyIndex !== -1 && ((_d = (_c = config.dependencies.find((dep) => dep.id == dependencyIndex)) == null ? void 0 : _c.inputs) == null ? void 0 : _d.length) !== parameters.length) {\n            const components = config.dependencies.find((dep) => dep.id == dependencyIndex).inputs.map(\n              (input) => {\n                var _a2;\n                return (_a2 = config.components.find((c) => c.id === input)) == null ? void 0 : _a2.type;\n              }\n            );\n            try {\n              components.forEach((comp, idx) => {\n                if (comp === \"state\") {\n                  const new_param = {\n                    component: \"state\",\n                    example: null,\n                    parameter_default: null,\n                    parameter_has_default: true,\n                    parameter_name: null,\n                    hidden: true\n                  };\n                  parameters.splice(idx, 0, new_param);\n                }\n              });\n            } catch (e) {\n              console.error(e);\n            }\n          }\n          const transform_type = (data, component, serializer, signature_type) => ({\n            ...data,\n            description: get_description(data == null ? void 0 : data.type, serializer),\n            type: get_type(data == null ? void 0 : data.type, component, serializer, signature_type) || \"\"\n          });\n          transformed_info[category][endpoint] = {\n            parameters: parameters.map(\n              (p) => transform_type(p, p == null ? void 0 : p.component, p == null ? void 0 : p.serializer, \"parameter\")\n            ),\n            returns: returns.map(\n              (r) => transform_type(r, r == null ? void 0 : r.component, r == null ? void 0 : r.serializer, \"return\")\n            ),\n            type: dependencyTypes\n          };\n        }\n      );\n    }\n  });\n  return transformed_info;\n}\nfunction get_type(type, component, serializer, signature_type) {\n  switch (type == null ? void 0 : type.type) {\n    case \"string\":\n      return \"string\";\n    case \"boolean\":\n      return \"boolean\";\n    case \"number\":\n      return \"number\";\n  }\n  if (serializer === \"JSONSerializable\" || serializer === \"StringSerializable\") {\n    return \"any\";\n  } else if (serializer === \"ListStringSerializable\") {\n    return \"string[]\";\n  } else if (component === \"Image\") {\n    return signature_type === \"parameter\" ? \"Blob | File | Buffer\" : \"string\";\n  } else if (serializer === \"FileSerializable\") {\n    if ((type == null ? void 0 : type.type) === \"array\") {\n      return signature_type === \"parameter\" ? \"(Blob | File | Buffer)[]\" : `{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]`;\n    }\n    return signature_type === \"parameter\" ? \"Blob | File | Buffer\" : `{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}`;\n  } else if (serializer === \"GallerySerializable\") {\n    return signature_type === \"parameter\" ? \"[(Blob | File | Buffer), (string | null)][]\" : `[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]`;\n  }\n}\nfunction get_description(type, serializer) {\n  if (serializer === \"GallerySerializable\") {\n    return \"array of [file, label] tuples\";\n  } else if (serializer === \"ListStringSerializable\") {\n    return \"array of strings\";\n  } else if (serializer === \"FileSerializable\") {\n    return \"array of files or single file\";\n  }\n  return type == null ? void 0 : type.description;\n}\nfunction handle_message(data, last_status) {\n  const queue = true;\n  switch (data.msg) {\n    case \"send_data\":\n      return { type: \"data\" };\n    case \"send_hash\":\n      return { type: \"hash\" };\n    case \"queue_full\":\n      return {\n        type: \"update\",\n        status: {\n          queue,\n          message: QUEUE_FULL_MSG,\n          stage: \"error\",\n          code: data.code,\n          success: data.success\n        }\n      };\n    case \"heartbeat\":\n      return {\n        type: \"heartbeat\"\n      };\n    case \"unexpected_error\":\n      return {\n        type: \"unexpected_error\",\n        status: {\n          queue,\n          message: data.message,\n          stage: \"error\",\n          success: false\n        }\n      };\n    case \"estimation\":\n      return {\n        type: \"update\",\n        status: {\n          queue,\n          stage: last_status || \"pending\",\n          code: data.code,\n          size: data.queue_size,\n          position: data.rank,\n          eta: data.rank_eta,\n          success: data.success\n        }\n      };\n    case \"progress\":\n      return {\n        type: \"update\",\n        status: {\n          queue,\n          stage: \"pending\",\n          code: data.code,\n          progress_data: data.progress_data,\n          success: data.success\n        }\n      };\n    case \"log\":\n      return { type: \"log\", data };\n    case \"process_generating\":\n      return {\n        type: \"generating\",\n        status: {\n          queue,\n          message: !data.success ? data.output.error : null,\n          stage: data.success ? \"generating\" : \"error\",\n          code: data.code,\n          progress_data: data.progress_data,\n          eta: data.average_duration\n        },\n        data: data.success ? data.output : null\n      };\n    case \"process_completed\":\n      if (\"error\" in data.output) {\n        return {\n          type: \"update\",\n          status: {\n            queue,\n            message: data.output.error,\n            stage: \"error\",\n            code: data.code,\n            success: data.success\n          }\n        };\n      }\n      return {\n        type: \"complete\",\n        status: {\n          queue,\n          message: !data.success ? data.output.error : void 0,\n          stage: data.success ? \"complete\" : \"error\",\n          code: data.code,\n          progress_data: data.progress_data,\n          changed_state_ids: data.success ? data.output.changed_state_ids : void 0\n        },\n        data: data.success ? data.output : null\n      };\n    case \"process_starts\":\n      return {\n        type: \"update\",\n        status: {\n          queue,\n          stage: \"pending\",\n          code: data.code,\n          size: data.rank,\n          position: 0,\n          success: data.success,\n          eta: data.eta\n        }\n      };\n  }\n  return { type: \"none\", status: { stage: \"error\", queue } };\n}\nconst map_data_to_params = (data, api_info) => {\n  const parameters = Object.values(api_info.named_endpoints).flatMap(\n    (values) => values.parameters\n  );\n  if (Array.isArray(data)) {\n    if (data.length > parameters.length) {\n      console.warn(\"Too many arguments provided for the endpoint.\");\n    }\n    return data;\n  }\n  const resolved_data = [];\n  const provided_keys = Object.keys(data);\n  parameters.forEach((param, index) => {\n    if (data.hasOwnProperty(param.parameter_name)) {\n      resolved_data[index] = data[param.parameter_name];\n    } else if (param.parameter_has_default) {\n      resolved_data[index] = param.parameter_default;\n    } else {\n      throw new Error(\n        `No value provided for required parameter: ${param.parameter_name}`\n      );\n    }\n  });\n  provided_keys.forEach((key) => {\n    if (!parameters.some((param) => param.parameter_name === key)) {\n      throw new Error(\n        `Parameter \\`${key}\\` is not a valid keyword argument. Please refer to the API for usage.`\n      );\n    }\n  });\n  resolved_data.forEach((value, idx) => {\n    if (value === void 0 && !parameters[idx].parameter_has_default) {\n      throw new Error(\n        `No value provided for required parameter: ${parameters[idx].parameter_name}`\n      );\n    }\n  });\n  return resolved_data;\n};\nasync function view_api() {\n  if (this.api_info)\n    return this.api_info;\n  const { hf_token } = this.options;\n  const { config } = this;\n  const headers = { \"Content-Type\": \"application/json\" };\n  if (hf_token) {\n    headers.Authorization = `Bearer ${hf_token}`;\n  }\n  if (!config) {\n    return;\n  }\n  try {\n    let response;\n    if (semiver((config == null ? void 0 : config.version) || \"2.0.0\", \"3.30\") < 0) {\n      response = await this.fetch(SPACE_FETCHER_URL, {\n        method: \"POST\",\n        body: JSON.stringify({\n          serialize: false,\n          config: JSON.stringify(config)\n        }),\n        headers,\n        credentials: \"include\"\n      });\n    } else {\n      const url = join_urls(config.root, API_INFO_URL);\n      response = await this.fetch(url, {\n        headers,\n        credentials: \"include\"\n      });\n    }\n    if (!response.ok) {\n      throw new Error(BROKEN_CONNECTION_MSG);\n    }\n    let api_info = await response.json();\n    if (\"api\" in api_info) {\n      api_info = api_info.api;\n    }\n    if (api_info.named_endpoints[\"/predict\"] && !api_info.unnamed_endpoints[\"0\"]) {\n      api_info.unnamed_endpoints[0] = api_info.named_endpoints[\"/predict\"];\n    }\n    return transform_api_info(api_info, config, this.api_map);\n  } catch (e) {\n    \"Could not get API info. \" + e.message;\n  }\n}\nasync function upload_files(root_url, files, upload_id) {\n  var _a;\n  const headers = {};\n  if ((_a = this == null ? void 0 : this.options) == null ? void 0 : _a.hf_token) {\n    headers.Authorization = `Bearer ${this.options.hf_token}`;\n  }\n  const chunkSize = 1e3;\n  const uploadResponses = [];\n  let response;\n  for (let i = 0; i < files.length; i += chunkSize) {\n    const chunk = files.slice(i, i + chunkSize);\n    const formData = new FormData();\n    chunk.forEach((file) => {\n      formData.append(\"files\", file);\n    });\n    try {\n      const upload_url = upload_id ? `${root_url}/${UPLOAD_URL}?upload_id=${upload_id}` : `${root_url}/${UPLOAD_URL}`;\n      response = await this.fetch(upload_url, {\n        method: \"POST\",\n        body: formData,\n        headers,\n        credentials: \"include\"\n      });\n    } catch (e) {\n      throw new Error(BROKEN_CONNECTION_MSG + e.message);\n    }\n    if (!response.ok) {\n      const error_text = await response.text();\n      return { error: `HTTP ${response.status}: ${error_text}` };\n    }\n    const output = await response.json();\n    if (output) {\n      uploadResponses.push(...output);\n    }\n  }\n  return { files: uploadResponses };\n}\nasync function upload(file_data, root_url, upload_id, max_file_size) {\n  let files = (Array.isArray(file_data) ? file_data : [file_data]).map(\n    (file_data2) => file_data2.blob\n  );\n  const oversized_files = files.filter(\n    (f) => f.size > (max_file_size ?? Infinity)\n  );\n  if (oversized_files.length) {\n    throw new Error(\n      `File size exceeds the maximum allowed size of ${max_file_size} bytes: ${oversized_files.map((f) => f.name).join(\", \")}`\n    );\n  }\n  return await Promise.all(\n    await this.upload_files(root_url, files, upload_id).then(\n      async (response) => {\n        if (response.error) {\n          throw new Error(response.error);\n        } else {\n          if (response.files) {\n            return response.files.map((f, i) => {\n              const file = new FileData({\n                ...file_data[i],\n                path: f,\n                url: root_url + \"/file=\" + f\n              });\n              return file;\n            });\n          }\n          return [];\n        }\n      }\n    )\n  );\n}\nasync function prepare_files(files, is_stream) {\n  return files.map(\n    (f) => new FileData({\n      path: f.name,\n      orig_name: f.name,\n      blob: f,\n      size: f.size,\n      mime_type: f.type,\n      is_stream\n    })\n  );\n}\nclass FileData {\n  constructor({\n    path,\n    url,\n    orig_name,\n    size,\n    blob,\n    is_stream,\n    mime_type,\n    alt_text\n  }) {\n    __publicField(this, \"path\");\n    __publicField(this, \"url\");\n    __publicField(this, \"orig_name\");\n    __publicField(this, \"size\");\n    __publicField(this, \"blob\");\n    __publicField(this, \"is_stream\");\n    __publicField(this, \"mime_type\");\n    __publicField(this, \"alt_text\");\n    __publicField(this, \"meta\", { _type: \"gradio.FileData\" });\n    this.path = path;\n    this.url = url;\n    this.orig_name = orig_name;\n    this.size = size;\n    this.blob = url ? void 0 : blob;\n    this.is_stream = is_stream;\n    this.mime_type = mime_type;\n    this.alt_text = alt_text;\n  }\n}\nclass Command {\n  constructor(command, meta) {\n    __publicField(this, \"type\");\n    __publicField(this, \"command\");\n    __publicField(this, \"meta\");\n    __publicField(this, \"fileData\");\n    this.type = \"command\";\n    this.command = command;\n    this.meta = meta;\n  }\n}\nconst is_node = typeof process !== \"undefined\" && process.versions && process.versions.node;\nfunction update_object(object, newValue, stack) {\n  while (stack.length > 1) {\n    const key2 = stack.shift();\n    if (typeof key2 === \"string\" || typeof key2 === \"number\") {\n      object = object[key2];\n    } else {\n      throw new Error(\"Invalid key type\");\n    }\n  }\n  const key = stack.shift();\n  if (typeof key === \"string\" || typeof key === \"number\") {\n    object[key] = newValue;\n  } else {\n    throw new Error(\"Invalid key type\");\n  }\n}\nasync function walk_and_store_blobs(data, type = void 0, path = [], root = false, endpoint_info = void 0) {\n  if (Array.isArray(data)) {\n    let blob_refs = [];\n    await Promise.all(\n      data.map(async (_, index) => {\n        var _a;\n        let new_path = path.slice();\n        new_path.push(String(index));\n        const array_refs = await walk_and_store_blobs(\n          data[index],\n          root ? ((_a = endpoint_info == null ? void 0 : endpoint_info.parameters[index]) == null ? void 0 : _a.component) || void 0 : type,\n          new_path,\n          false,\n          endpoint_info\n        );\n        blob_refs = blob_refs.concat(array_refs);\n      })\n    );\n    return blob_refs;\n  } else if (globalThis.Buffer && data instanceof globalThis.Buffer || data instanceof Blob) {\n    return [\n      {\n        path,\n        blob: new Blob([data]),\n        type\n      }\n    ];\n  } else if (typeof data === \"object\" && data !== null) {\n    let blob_refs = [];\n    for (const key of Object.keys(data)) {\n      const new_path = [...path, key];\n      const value = data[key];\n      blob_refs = blob_refs.concat(\n        await walk_and_store_blobs(\n          value,\n          void 0,\n          new_path,\n          false,\n          endpoint_info\n        )\n      );\n    }\n    return blob_refs;\n  }\n  return [];\n}\nfunction skip_queue(id, config) {\n  var _a, _b;\n  let fn_queue = (_b = (_a = config == null ? void 0 : config.dependencies) == null ? void 0 : _a.find((dep) => dep.id == id)) == null ? void 0 : _b.queue;\n  if (fn_queue != null) {\n    return !fn_queue;\n  }\n  return !config.enable_queue;\n}\nfunction post_message(message, origin) {\n  return new Promise((res, _rej) => {\n    const channel = new MessageChannel();\n    channel.port1.onmessage = ({ data }) => {\n      channel.port1.close();\n      res(data);\n    };\n    window.parent.postMessage(message, origin, [channel.port2]);\n  });\n}\nfunction handle_file(file_or_url) {\n  if (typeof file_or_url === \"string\") {\n    if (file_or_url.startsWith(\"http://\") || file_or_url.startsWith(\"https://\")) {\n      return {\n        path: file_or_url,\n        url: file_or_url,\n        orig_name: file_or_url.split(\"/\").pop() ?? \"unknown\",\n        meta: { _type: \"gradio.FileData\" }\n      };\n    }\n    if (is_node) {\n      return new Command(\"upload_file\", {\n        path: file_or_url,\n        name: file_or_url,\n        orig_path: file_or_url\n      });\n    }\n  } else if (typeof File !== \"undefined\" && file_or_url instanceof File) {\n    return {\n      path: file_or_url instanceof File ? file_or_url.name : \"blob\",\n      orig_name: file_or_url instanceof File ? file_or_url.name : \"unknown\",\n      // @ts-ignore\n      blob: file_or_url instanceof File ? file_or_url : new Blob([file_or_url]),\n      size: file_or_url instanceof Blob ? file_or_url.size : Buffer.byteLength(file_or_url),\n      mime_type: file_or_url instanceof File ? file_or_url.type : \"application/octet-stream\",\n      // Default MIME type for buffers\n      meta: { _type: \"gradio.FileData\" }\n    };\n  } else if (file_or_url instanceof Buffer) {\n    return new Blob([file_or_url]);\n  } else if (file_or_url instanceof Blob) {\n    return file_or_url;\n  }\n  throw new Error(\n    \"Invalid input: must be a URL, File, Blob, or Buffer object.\"\n  );\n}\nfunction handle_payload(resolved_payload, dependency, components, type, with_null_state = false) {\n  if (type === \"input\" && !with_null_state) {\n    throw new Error(\"Invalid code path. Cannot skip state inputs for input.\");\n  }\n  if (type === \"output\" && with_null_state) {\n    return resolved_payload;\n  }\n  let updated_payload = [];\n  let payload_index = 0;\n  for (let i = 0; i < dependency.inputs.length; i++) {\n    const input_id = dependency.inputs[i];\n    const component = components.find((c) => c.id === input_id);\n    if ((component == null ? void 0 : component.type) === \"state\") {\n      if (with_null_state) {\n        if (resolved_payload.length === dependency.inputs.length) {\n          const value = resolved_payload[payload_index];\n          updated_payload.push(value);\n          payload_index++;\n        } else {\n          updated_payload.push(null);\n        }\n      } else {\n        payload_index++;\n        continue;\n      }\n      continue;\n    } else {\n      const value = resolved_payload[payload_index];\n      updated_payload.push(value);\n      payload_index++;\n    }\n  }\n  return updated_payload;\n}\nasync function handle_blob(endpoint, data, api_info) {\n  const self = this;\n  await process_local_file_commands(self, data);\n  const blobRefs = await walk_and_store_blobs(\n    data,\n    void 0,\n    [],\n    true,\n    api_info\n  );\n  const results = await Promise.all(\n    blobRefs.map(async ({ path, blob, type }) => {\n      if (!blob)\n        return { path, type };\n      const response = await self.upload_files(endpoint, [blob]);\n      const file_url = response.files && response.files[0];\n      return {\n        path,\n        file_url,\n        type,\n        name: blob instanceof File ? blob == null ? void 0 : blob.name : void 0\n      };\n    })\n  );\n  results.forEach(({ path, file_url, type, name }) => {\n    if (type === \"Gallery\") {\n      update_object(data, file_url, path);\n    } else if (file_url) {\n      const file = new FileData({ path: file_url, orig_name: name });\n      update_object(data, file, path);\n    }\n  });\n  return data;\n}\nasync function process_local_file_commands(client2, data) {\n  var _a, _b;\n  const root = ((_a = client2.config) == null ? void 0 : _a.root) || ((_b = client2.config) == null ? void 0 : _b.root_url);\n  if (!root) {\n    throw new Error(ROOT_URL_ERROR_MSG);\n  }\n  await recursively_process_commands(client2, data);\n}\nasync function recursively_process_commands(client2, data, path = []) {\n  for (const key in data) {\n    if (data[key] instanceof Command) {\n      await process_single_command(client2, data, key);\n    } else if (typeof data[key] === \"object\" && data[key] !== null) {\n      await recursively_process_commands(client2, data[key], [...path, key]);\n    }\n  }\n}\nasync function process_single_command(client2, data, key) {\n  var _a, _b;\n  let cmd_item = data[key];\n  const root = ((_a = client2.config) == null ? void 0 : _a.root) || ((_b = client2.config) == null ? void 0 : _b.root_url);\n  if (!root) {\n    throw new Error(ROOT_URL_ERROR_MSG);\n  }\n  try {\n    let fileBuffer;\n    let fullPath;\n    if (typeof process !== \"undefined\" && process.versions && process.versions.node) {\n      const fs = await import(\"fs/promises\");\n      const path = await import(\"path\");\n      fullPath = path.resolve(process.cwd(), cmd_item.meta.path);\n      fileBuffer = await fs.readFile(fullPath);\n    } else {\n      throw new Error(NODEJS_FS_ERROR_MSG);\n    }\n    const file = new Blob([fileBuffer], { type: \"application/octet-stream\" });\n    const response = await client2.upload_files(root, [file]);\n    const file_url = response.files && response.files[0];\n    if (file_url) {\n      const fileData = new FileData({\n        path: file_url,\n        orig_name: cmd_item.meta.name || \"\"\n      });\n      data[key] = fileData;\n    }\n  } catch (error) {\n    console.error(FILE_PROCESSING_ERROR_MSG, error);\n  }\n}\nasync function post_data(url, body, additional_headers) {\n  const headers = { \"Content-Type\": \"application/json\" };\n  if (this.options.hf_token) {\n    headers.Authorization = `Bearer ${this.options.hf_token}`;\n  }\n  try {\n    var response = await this.fetch(url, {\n      method: \"POST\",\n      body: JSON.stringify(body),\n      headers: { ...headers, ...additional_headers },\n      credentials: \"include\"\n    });\n  } catch (e) {\n    return [{ error: BROKEN_CONNECTION_MSG }, 500];\n  }\n  let output;\n  let status;\n  try {\n    output = await response.json();\n    status = response.status;\n  } catch (e) {\n    output = { error: `Could not parse server response: ${e}` };\n    status = 500;\n  }\n  return [output, status];\n}\nasync function predict(endpoint, data) {\n  let data_returned = false;\n  let status_complete = false;\n  let dependency;\n  if (!this.config) {\n    throw new Error(\"Could not resolve app config\");\n  }\n  if (typeof endpoint === \"number\") {\n    dependency = this.config.dependencies.find((dep) => dep.id == endpoint);\n  } else {\n    const trimmed_endpoint = endpoint.replace(/^\\//, \"\");\n    dependency = this.config.dependencies.find(\n      (dep) => dep.id == this.api_map[trimmed_endpoint]\n    );\n  }\n  if (dependency == null ? void 0 : dependency.types.continuous) {\n    throw new Error(\n      \"Cannot call predict on this function as it may run forever. Use submit instead\"\n    );\n  }\n  return new Promise(async (resolve, reject) => {\n    const app = this.submit(endpoint, data, null, null, true);\n    let result;\n    for await (const message of app) {\n      if (message.type === \"data\") {\n        if (status_complete) {\n          resolve(result);\n        }\n        data_returned = true;\n        result = message;\n      }\n      if (message.type === \"status\") {\n        if (message.stage === \"error\")\n          reject(message);\n        if (message.stage === \"complete\") {\n          status_complete = true;\n          if (data_returned) {\n            resolve(result);\n          }\n        }\n      }\n    }\n  });\n}\nasync function check_space_status(id, type, status_callback) {\n  let endpoint = type === \"subdomain\" ? `https://huggingface.co/api/spaces/by-subdomain/${id}` : `https://huggingface.co/api/spaces/${id}`;\n  let response;\n  let _status;\n  try {\n    response = await fetch(endpoint);\n    _status = response.status;\n    if (_status !== 200) {\n      throw new Error();\n    }\n    response = await response.json();\n  } catch (e) {\n    status_callback({\n      status: \"error\",\n      load_status: \"error\",\n      message: SPACE_STATUS_ERROR_MSG,\n      detail: \"NOT_FOUND\"\n    });\n    return;\n  }\n  if (!response || _status !== 200)\n    return;\n  const {\n    runtime: { stage },\n    id: space_name\n  } = response;\n  switch (stage) {\n    case \"STOPPED\":\n    case \"SLEEPING\":\n      status_callback({\n        status: \"sleeping\",\n        load_status: \"pending\",\n        message: \"Space is asleep. Waking it up...\",\n        detail: stage\n      });\n      setTimeout(() => {\n        check_space_status(id, type, status_callback);\n      }, 1e3);\n      break;\n    case \"PAUSED\":\n      status_callback({\n        status: \"paused\",\n        load_status: \"error\",\n        message: \"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.\",\n        detail: stage,\n        discussions_enabled: await discussions_enabled(space_name)\n      });\n      break;\n    case \"RUNNING\":\n    case \"RUNNING_BUILDING\":\n      status_callback({\n        status: \"running\",\n        load_status: \"complete\",\n        message: \"\",\n        detail: stage\n      });\n      break;\n    case \"BUILDING\":\n      status_callback({\n        status: \"building\",\n        load_status: \"pending\",\n        message: \"Space is building...\",\n        detail: stage\n      });\n      setTimeout(() => {\n        check_space_status(id, type, status_callback);\n      }, 1e3);\n      break;\n    default:\n      status_callback({\n        status: \"space_error\",\n        load_status: \"error\",\n        message: \"This space is experiencing an issue.\",\n        detail: stage,\n        discussions_enabled: await discussions_enabled(space_name)\n      });\n      break;\n  }\n}\nconst RE_DISABLED_DISCUSSION = /^(?=[^]*\\b[dD]iscussions{0,1}\\b)(?=[^]*\\b[dD]isabled\\b)[^]*$/;\nasync function discussions_enabled(space_id) {\n  try {\n    const r = await fetch(\n      `https://huggingface.co/api/spaces/${space_id}/discussions`,\n      {\n        method: \"HEAD\"\n      }\n    );\n    const error = r.headers.get(\"x-error-message\");\n    if (!r.ok || error && RE_DISABLED_DISCUSSION.test(error))\n      return false;\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nasync function get_space_hardware(space_id, hf_token) {\n  const headers = {};\n  if (hf_token) {\n    headers.Authorization = `Bearer ${hf_token}`;\n  }\n  try {\n    const res = await fetch(\n      `https://huggingface.co/api/spaces/${space_id}/${RUNTIME_URL}`,\n      { headers }\n    );\n    if (res.status !== 200)\n      throw new Error(\"Space hardware could not be obtained.\");\n    const { hardware } = await res.json();\n    return hardware.current;\n  } catch (e) {\n    throw new Error(e.message);\n  }\n}\nasync function set_space_timeout(space_id, timeout, hf_token) {\n  const headers = {};\n  if (hf_token) {\n    headers.Authorization = `Bearer ${hf_token}`;\n  }\n  const body = {\n    seconds: timeout\n  };\n  try {\n    const res = await fetch(\n      `https://huggingface.co/api/spaces/${space_id}/${SLEEPTIME_URL}`,\n      {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\", ...headers },\n        body: JSON.stringify(body)\n      }\n    );\n    if (res.status !== 200) {\n      throw new Error(\n        \"Could not set sleep timeout on duplicated Space. Please visit *ADD HF LINK TO SETTINGS* to set a timeout manually to reduce billing charges.\"\n      );\n    }\n    const response = await res.json();\n    return response;\n  } catch (e) {\n    throw new Error(e.message);\n  }\n}\nconst hardware_types = [\n  \"cpu-basic\",\n  \"cpu-upgrade\",\n  \"cpu-xl\",\n  \"t4-small\",\n  \"t4-medium\",\n  \"a10g-small\",\n  \"a10g-large\",\n  \"a10g-largex2\",\n  \"a10g-largex4\",\n  \"a100-large\",\n  \"zero-a10g\",\n  \"h100\",\n  \"h100x8\"\n];\nasync function duplicate(app_reference, options) {\n  const { hf_token, private: _private, hardware, timeout, auth } = options;\n  if (hardware && !hardware_types.includes(hardware)) {\n    throw new Error(\n      `Invalid hardware type provided. Valid types are: ${hardware_types.map((v) => `\"${v}\"`).join(\",\")}.`\n    );\n  }\n  const { http_protocol, host } = await process_endpoint(\n    app_reference,\n    hf_token\n  );\n  let cookies = null;\n  if (auth) {\n    const cookie_header = await get_cookie_header(\n      http_protocol,\n      host,\n      auth,\n      fetch\n    );\n    if (cookie_header)\n      cookies = parse_and_set_cookies(cookie_header);\n  }\n  const headers = {\n    Authorization: `Bearer ${hf_token}`,\n    \"Content-Type\": \"application/json\",\n    ...cookies ? { Cookie: cookies.join(\"; \") } : {}\n  };\n  const user = (await (await fetch(`https://huggingface.co/api/whoami-v2`, {\n    headers\n  })).json()).name;\n  const space_name = app_reference.split(\"/\")[1];\n  const body = {\n    repository: `${user}/${space_name}`\n  };\n  if (_private) {\n    body.private = true;\n  }\n  let original_hardware;\n  try {\n    if (!hardware) {\n      original_hardware = await get_space_hardware(app_reference, hf_token);\n    }\n  } catch (e) {\n    throw Error(SPACE_METADATA_ERROR_MSG + e.message);\n  }\n  const requested_hardware = hardware || original_hardware || \"cpu-basic\";\n  body.hardware = requested_hardware;\n  try {\n    const response = await fetch(\n      `https://huggingface.co/api/spaces/${app_reference}/duplicate`,\n      {\n        method: \"POST\",\n        headers,\n        body: JSON.stringify(body)\n      }\n    );\n    if (response.status === 409) {\n      try {\n        const client2 = await Client.connect(`${user}/${space_name}`, options);\n        return client2;\n      } catch (error) {\n        console.error(\"Failed to connect Client instance:\", error);\n        throw error;\n      }\n    } else if (response.status !== 200) {\n      throw new Error(response.statusText);\n    }\n    const duplicated_space = await response.json();\n    await set_space_timeout(`${user}/${space_name}`, timeout || 300, hf_token);\n    return await Client.connect(\n      get_space_reference(duplicated_space.url),\n      options\n    );\n  } catch (e) {\n    throw new Error(e);\n  }\n}\nfunction get_space_reference(url) {\n  const regex = /https:\\/\\/huggingface.co\\/spaces\\/([^/]+\\/[^/]+)/;\n  const match = url.match(regex);\n  if (match) {\n    return match[1];\n  }\n}\nclass TextLineStream extends TransformStream {\n  /** Constructs a new instance. */\n  constructor(options = { allowCR: false }) {\n    super({\n      transform: (chars, controller) => {\n        chars = __privateGet(this, _currentLine) + chars;\n        while (true) {\n          const lfIndex = chars.indexOf(\"\\n\");\n          const crIndex = options.allowCR ? chars.indexOf(\"\\r\") : -1;\n          if (crIndex !== -1 && crIndex !== chars.length - 1 && (lfIndex === -1 || lfIndex - 1 > crIndex)) {\n            controller.enqueue(chars.slice(0, crIndex));\n            chars = chars.slice(crIndex + 1);\n            continue;\n          }\n          if (lfIndex === -1)\n            break;\n          const endIndex = chars[lfIndex - 1] === \"\\r\" ? lfIndex - 1 : lfIndex;\n          controller.enqueue(chars.slice(0, endIndex));\n          chars = chars.slice(lfIndex + 1);\n        }\n        __privateSet(this, _currentLine, chars);\n      },\n      flush: (controller) => {\n        if (__privateGet(this, _currentLine) === \"\")\n          return;\n        const currentLine = options.allowCR && __privateGet(this, _currentLine).endsWith(\"\\r\") ? __privateGet(this, _currentLine).slice(0, -1) : __privateGet(this, _currentLine);\n        controller.enqueue(currentLine);\n      }\n    });\n    __privateAdd(this, _currentLine, \"\");\n  }\n}\n_currentLine = new WeakMap();\nfunction stream$1(input) {\n  let decoder = new TextDecoderStream();\n  let split2 = new TextLineStream({ allowCR: true });\n  return input.pipeThrough(decoder).pipeThrough(split2);\n}\nfunction split(input) {\n  let rgx = /[:]\\s*/;\n  let match = rgx.exec(input);\n  let idx = match && match.index;\n  if (idx) {\n    return [\n      input.substring(0, idx),\n      input.substring(idx + match[0].length)\n    ];\n  }\n}\nfunction fallback(headers, key, value) {\n  let tmp = headers.get(key);\n  if (!tmp)\n    headers.set(key, value);\n}\nasync function* events(res, signal) {\n  if (!res.body)\n    return;\n  let iter = stream$1(res.body);\n  let line, reader = iter.getReader();\n  let event;\n  for (; ; ) {\n    if (signal && signal.aborted) {\n      return reader.cancel();\n    }\n    line = await reader.read();\n    if (line.done)\n      return;\n    if (!line.value) {\n      if (event)\n        yield event;\n      event = void 0;\n      continue;\n    }\n    let [field, value] = split(line.value) || [];\n    if (!field)\n      continue;\n    if (field === \"data\") {\n      event || (event = {});\n      event[field] = event[field] ? event[field] + \"\\n\" + value : value;\n    } else if (field === \"event\") {\n      event || (event = {});\n      event[field] = value;\n    } else if (field === \"id\") {\n      event || (event = {});\n      event[field] = +value || value;\n    } else if (field === \"retry\") {\n      event || (event = {});\n      event[field] = +value || void 0;\n    }\n  }\n}\nasync function stream(input, init) {\n  let req = new Request(input, init);\n  fallback(req.headers, \"Accept\", \"text/event-stream\");\n  fallback(req.headers, \"Content-Type\", \"application/json\");\n  let r = await fetch(req);\n  if (!r.ok)\n    throw r;\n  return events(r, req.signal);\n}\nasync function open_stream() {\n  let {\n    event_callbacks,\n    unclosed_events,\n    pending_stream_messages,\n    stream_status,\n    config,\n    jwt\n  } = this;\n  const that = this;\n  if (!config) {\n    throw new Error(\"Could not resolve app config\");\n  }\n  stream_status.open = true;\n  let stream2 = null;\n  let params = new URLSearchParams({\n    session_hash: this.session_hash\n  }).toString();\n  let url = new URL(`${config.root}/queue/data?${params}`);\n  if (jwt) {\n    url.searchParams.set(\"__sign\", jwt);\n  }\n  stream2 = this.stream(url);\n  if (!stream2) {\n    console.warn(\"Cannot connect to SSE endpoint: \" + url.toString());\n    return;\n  }\n  stream2.onmessage = async function(event) {\n    let _data = JSON.parse(event.data);\n    if (_data.msg === \"close_stream\") {\n      close_stream(stream_status, that.abort_controller);\n      return;\n    }\n    const event_id = _data.event_id;\n    if (!event_id) {\n      await Promise.all(\n        Object.keys(event_callbacks).map(\n          (event_id2) => event_callbacks[event_id2](_data)\n        )\n      );\n    } else if (event_callbacks[event_id] && config) {\n      if (_data.msg === \"process_completed\" && [\"sse\", \"sse_v1\", \"sse_v2\", \"sse_v2.1\", \"sse_v3\"].includes(\n        config.protocol\n      )) {\n        unclosed_events.delete(event_id);\n      }\n      let fn2 = event_callbacks[event_id];\n      if (typeof window !== \"undefined\" && typeof document !== \"undefined\") {\n        setTimeout(fn2, 0, _data);\n      } else {\n        fn2(_data);\n      }\n    } else {\n      if (!pending_stream_messages[event_id]) {\n        pending_stream_messages[event_id] = [];\n      }\n      pending_stream_messages[event_id].push(_data);\n    }\n  };\n  stream2.onerror = async function() {\n    await Promise.all(\n      Object.keys(event_callbacks).map(\n        (event_id) => event_callbacks[event_id]({\n          msg: \"unexpected_error\",\n          message: BROKEN_CONNECTION_MSG\n        })\n      )\n    );\n  };\n}\nfunction close_stream(stream_status, abort_controller) {\n  if (stream_status) {\n    stream_status.open = false;\n    abort_controller == null ? void 0 : abort_controller.abort();\n  }\n}\nfunction apply_diff_stream(pending_diff_streams, event_id, data) {\n  let is_first_generation = !pending_diff_streams[event_id];\n  if (is_first_generation) {\n    pending_diff_streams[event_id] = [];\n    data.data.forEach((value, i) => {\n      pending_diff_streams[event_id][i] = value;\n    });\n  } else {\n    data.data.forEach((value, i) => {\n      let new_data = apply_diff(pending_diff_streams[event_id][i], value);\n      pending_diff_streams[event_id][i] = new_data;\n      data.data[i] = new_data;\n    });\n  }\n}\nfunction apply_diff(obj, diff) {\n  diff.forEach(([action, path, value]) => {\n    obj = apply_edit(obj, path, action, value);\n  });\n  return obj;\n}\nfunction apply_edit(target, path, action, value) {\n  if (path.length === 0) {\n    if (action === \"replace\") {\n      return value;\n    } else if (action === \"append\") {\n      return target + value;\n    }\n    throw new Error(`Unsupported action: ${action}`);\n  }\n  let current = target;\n  for (let i = 0; i < path.length - 1; i++) {\n    current = current[path[i]];\n  }\n  const last_path = path[path.length - 1];\n  switch (action) {\n    case \"replace\":\n      current[last_path] = value;\n      break;\n    case \"append\":\n      current[last_path] += value;\n      break;\n    case \"add\":\n      if (Array.isArray(current)) {\n        current.splice(Number(last_path), 0, value);\n      } else {\n        current[last_path] = value;\n      }\n      break;\n    case \"delete\":\n      if (Array.isArray(current)) {\n        current.splice(Number(last_path), 1);\n      } else {\n        delete current[last_path];\n      }\n      break;\n    default:\n      throw new Error(`Unknown action: ${action}`);\n  }\n  return target;\n}\nfunction readable_stream(input, init = {}) {\n  const instance = {\n    close: () => {\n      throw new Error(\"Method not implemented.\");\n    },\n    onerror: null,\n    onmessage: null,\n    onopen: null,\n    readyState: 0,\n    url: input.toString(),\n    withCredentials: false,\n    CONNECTING: 0,\n    OPEN: 1,\n    CLOSED: 2,\n    addEventListener: () => {\n      throw new Error(\"Method not implemented.\");\n    },\n    dispatchEvent: () => {\n      throw new Error(\"Method not implemented.\");\n    },\n    removeEventListener: () => {\n      throw new Error(\"Method not implemented.\");\n    }\n  };\n  stream(input, init).then(async (res) => {\n    instance.readyState = instance.OPEN;\n    try {\n      for await (const chunk of res) {\n        instance.onmessage && instance.onmessage(chunk);\n      }\n      instance.readyState = instance.CLOSED;\n    } catch (e) {\n      instance.onerror && instance.onerror(e);\n      instance.readyState = instance.CLOSED;\n    }\n  }).catch((e) => {\n    console.error(e);\n    instance.onerror && instance.onerror(e);\n    instance.readyState = instance.CLOSED;\n  });\n  return instance;\n}\nfunction submit(endpoint, data, event_data, trigger_id, all_events) {\n  var _a;\n  try {\n    let fire_event = function(event) {\n      if (all_events || events_to_publish[event.type]) {\n        push_event(event);\n      }\n    }, close = function() {\n      done = true;\n      while (resolvers.length > 0)\n        resolvers.shift()({\n          value: void 0,\n          done: true\n        });\n    }, push = function(data2) {\n      if (done)\n        return;\n      if (resolvers.length > 0) {\n        resolvers.shift()(data2);\n      } else {\n        values.push(data2);\n      }\n    }, push_error = function(error) {\n      push(thenable_reject(error));\n      close();\n    }, push_event = function(event) {\n      push({ value: event, done: false });\n    }, next = function() {\n      if (values.length > 0)\n        return Promise.resolve(values.shift());\n      if (done)\n        return Promise.resolve({ value: void 0, done: true });\n      return new Promise((resolve) => resolvers.push(resolve));\n    };\n    const { hf_token } = this.options;\n    const {\n      fetch: fetch2,\n      app_reference,\n      config,\n      session_hash,\n      api_info,\n      api_map,\n      stream_status,\n      pending_stream_messages,\n      pending_diff_streams,\n      event_callbacks,\n      unclosed_events,\n      post_data: post_data2,\n      options\n    } = this;\n    const that = this;\n    if (!api_info)\n      throw new Error(\"No API found\");\n    if (!config)\n      throw new Error(\"Could not resolve app config\");\n    let { fn_index, endpoint_info, dependency } = get_endpoint_info(\n      api_info,\n      endpoint,\n      api_map,\n      config\n    );\n    let resolved_data = map_data_to_params(data, api_info);\n    let websocket;\n    let stream2;\n    let protocol = config.protocol ?? \"ws\";\n    const _endpoint = typeof endpoint === \"number\" ? \"/predict\" : endpoint;\n    let payload;\n    let event_id = null;\n    let complete = false;\n    let last_status = {};\n    let url_params = typeof window !== \"undefined\" && typeof document !== \"undefined\" ? new URLSearchParams(window.location.search).toString() : \"\";\n    const events_to_publish = ((_a = options == null ? void 0 : options.events) == null ? void 0 : _a.reduce(\n      (acc, event) => {\n        acc[event] = true;\n        return acc;\n      },\n      {}\n    )) || {};\n    async function cancel() {\n      const _status = {\n        stage: \"complete\",\n        queue: false,\n        time: /* @__PURE__ */ new Date()\n      };\n      complete = _status;\n      fire_event({\n        ..._status,\n        type: \"status\",\n        endpoint: _endpoint,\n        fn_index\n      });\n      let reset_request = {};\n      let cancel_request = {};\n      if (protocol === \"ws\") {\n        if (websocket && websocket.readyState === 0) {\n          websocket.addEventListener(\"open\", () => {\n            websocket.close();\n          });\n        } else {\n          websocket.close();\n        }\n        reset_request = { fn_index, session_hash };\n      } else {\n        close_stream(stream_status, that.abort_controller);\n        close();\n        reset_request = { event_id };\n        cancel_request = { event_id, session_hash, fn_index };\n      }\n      try {\n        if (!config) {\n          throw new Error(\"Could not resolve app config\");\n        }\n        if (\"event_id\" in cancel_request) {\n          await fetch2(`${config.root}/cancel`, {\n            headers: { \"Content-Type\": \"application/json\" },\n            method: \"POST\",\n            body: JSON.stringify(cancel_request)\n          });\n        }\n        await fetch2(`${config.root}/reset`, {\n          headers: { \"Content-Type\": \"application/json\" },\n          method: \"POST\",\n          body: JSON.stringify(reset_request)\n        });\n      } catch (e) {\n        console.warn(\n          \"The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.\"\n        );\n      }\n    }\n    const resolve_heartbeat = async (config2) => {\n      await this._resolve_hearbeat(config2);\n    };\n    async function handle_render_config(render_config) {\n      if (!config)\n        return;\n      let render_id = render_config.render_id;\n      config.components = [\n        ...config.components.filter((c) => c.props.rendered_in !== render_id),\n        ...render_config.components\n      ];\n      config.dependencies = [\n        ...config.dependencies.filter((d) => d.rendered_in !== render_id),\n        ...render_config.dependencies\n      ];\n      const any_state = config.components.some((c) => c.type === \"state\");\n      const any_unload = config.dependencies.some(\n        (d) => d.targets.some((t) => t[1] === \"unload\")\n      );\n      config.connect_heartbeat = any_state || any_unload;\n      await resolve_heartbeat(config);\n      fire_event({\n        type: \"render\",\n        data: render_config,\n        endpoint: _endpoint,\n        fn_index\n      });\n    }\n    this.handle_blob(config.root, resolved_data, endpoint_info).then(\n      async (_payload) => {\n        var _a2;\n        let input_data = handle_payload(\n          _payload,\n          dependency,\n          config.components,\n          \"input\",\n          true\n        );\n        payload = {\n          data: input_data || [],\n          event_data,\n          fn_index,\n          trigger_id\n        };\n        if (skip_queue(fn_index, config)) {\n          fire_event({\n            type: \"status\",\n            endpoint: _endpoint,\n            stage: \"pending\",\n            queue: false,\n            fn_index,\n            time: /* @__PURE__ */ new Date()\n          });\n          post_data2(\n            `${config.root}/run${_endpoint.startsWith(\"/\") ? _endpoint : `/${_endpoint}`}${url_params ? \"?\" + url_params : \"\"}`,\n            {\n              ...payload,\n              session_hash\n            }\n          ).then(([output, status_code]) => {\n            const data2 = output.data;\n            if (status_code == 200) {\n              fire_event({\n                type: \"data\",\n                endpoint: _endpoint,\n                fn_index,\n                data: handle_payload(\n                  data2,\n                  dependency,\n                  config.components,\n                  \"output\",\n                  options.with_null_state\n                ),\n                time: /* @__PURE__ */ new Date(),\n                event_data,\n                trigger_id\n              });\n              if (output.render_config) {\n                handle_render_config(output.render_config);\n              }\n              fire_event({\n                type: \"status\",\n                endpoint: _endpoint,\n                fn_index,\n                stage: \"complete\",\n                eta: output.average_duration,\n                queue: false,\n                time: /* @__PURE__ */ new Date()\n              });\n            } else {\n              fire_event({\n                type: \"status\",\n                stage: \"error\",\n                endpoint: _endpoint,\n                fn_index,\n                message: output.error,\n                queue: false,\n                time: /* @__PURE__ */ new Date()\n              });\n            }\n          }).catch((e) => {\n            fire_event({\n              type: \"status\",\n              stage: \"error\",\n              message: e.message,\n              endpoint: _endpoint,\n              fn_index,\n              queue: false,\n              time: /* @__PURE__ */ new Date()\n            });\n          });\n        } else if (protocol == \"ws\") {\n          const { ws_protocol, host } = await process_endpoint(\n            app_reference,\n            hf_token\n          );\n          fire_event({\n            type: \"status\",\n            stage: \"pending\",\n            queue: true,\n            endpoint: _endpoint,\n            fn_index,\n            time: /* @__PURE__ */ new Date()\n          });\n          let url = new URL(\n            `${ws_protocol}://${resolve_root(\n              host,\n              config.path,\n              true\n            )}/queue/join${url_params ? \"?\" + url_params : \"\"}`\n          );\n          if (this.jwt) {\n            url.searchParams.set(\"__sign\", this.jwt);\n          }\n          websocket = new WebSocket(url);\n          websocket.onclose = (evt) => {\n            if (!evt.wasClean) {\n              fire_event({\n                type: \"status\",\n                stage: \"error\",\n                broken: true,\n                message: BROKEN_CONNECTION_MSG,\n                queue: true,\n                endpoint: _endpoint,\n                fn_index,\n                time: /* @__PURE__ */ new Date()\n              });\n            }\n          };\n          websocket.onmessage = function(event) {\n            const _data = JSON.parse(event.data);\n            const { type, status, data: data2 } = handle_message(\n              _data,\n              last_status[fn_index]\n            );\n            if (type === \"update\" && status && !complete) {\n              fire_event({\n                type: \"status\",\n                endpoint: _endpoint,\n                fn_index,\n                time: /* @__PURE__ */ new Date(),\n                ...status\n              });\n              if (status.stage === \"error\") {\n                websocket.close();\n              }\n            } else if (type === \"hash\") {\n              websocket.send(JSON.stringify({ fn_index, session_hash }));\n              return;\n            } else if (type === \"data\") {\n              websocket.send(JSON.stringify({ ...payload, session_hash }));\n            } else if (type === \"complete\") {\n              complete = status;\n            } else if (type === \"log\") {\n              fire_event({\n                type: \"log\",\n                log: data2.log,\n                level: data2.level,\n                endpoint: _endpoint,\n                fn_index\n              });\n            } else if (type === \"generating\") {\n              fire_event({\n                type: \"status\",\n                time: /* @__PURE__ */ new Date(),\n                ...status,\n                stage: status == null ? void 0 : status.stage,\n                queue: true,\n                endpoint: _endpoint,\n                fn_index\n              });\n            }\n            if (data2) {\n              fire_event({\n                type: \"data\",\n                time: /* @__PURE__ */ new Date(),\n                data: handle_payload(\n                  data2.data,\n                  dependency,\n                  config.components,\n                  \"output\",\n                  options.with_null_state\n                ),\n                endpoint: _endpoint,\n                fn_index,\n                event_data,\n                trigger_id\n              });\n              if (complete) {\n                fire_event({\n                  type: \"status\",\n                  time: /* @__PURE__ */ new Date(),\n                  ...complete,\n                  stage: status == null ? void 0 : status.stage,\n                  queue: true,\n                  endpoint: _endpoint,\n                  fn_index\n                });\n                websocket.close();\n              }\n            }\n          };\n          if (semiver(config.version || \"2.0.0\", \"3.6\") < 0) {\n            addEventListener(\n              \"open\",\n              () => websocket.send(JSON.stringify({ hash: session_hash }))\n            );\n          }\n        } else if (protocol == \"sse\") {\n          fire_event({\n            type: \"status\",\n            stage: \"pending\",\n            queue: true,\n            endpoint: _endpoint,\n            fn_index,\n            time: /* @__PURE__ */ new Date()\n          });\n          var params = new URLSearchParams({\n            fn_index: fn_index.toString(),\n            session_hash\n          }).toString();\n          let url = new URL(\n            `${config.root}/queue/join?${url_params ? url_params + \"&\" : \"\"}${params}`\n          );\n          if (this.jwt) {\n            url.searchParams.set(\"__sign\", this.jwt);\n          }\n          stream2 = this.stream(url);\n          if (!stream2) {\n            return Promise.reject(\n              new Error(\"Cannot connect to SSE endpoint: \" + url.toString())\n            );\n          }\n          stream2.onmessage = async function(event) {\n            const _data = JSON.parse(event.data);\n            const { type, status, data: data2 } = handle_message(\n              _data,\n              last_status[fn_index]\n            );\n            if (type === \"update\" && status && !complete) {\n              fire_event({\n                type: \"status\",\n                endpoint: _endpoint,\n                fn_index,\n                time: /* @__PURE__ */ new Date(),\n                ...status\n              });\n              if (status.stage === \"error\") {\n                stream2 == null ? void 0 : stream2.close();\n                close();\n              }\n            } else if (type === \"data\") {\n              event_id = _data.event_id;\n              let [_, status2] = await post_data2(`${config.root}/queue/data`, {\n                ...payload,\n                session_hash,\n                event_id\n              });\n              if (status2 !== 200) {\n                fire_event({\n                  type: \"status\",\n                  stage: \"error\",\n                  message: BROKEN_CONNECTION_MSG,\n                  queue: true,\n                  endpoint: _endpoint,\n                  fn_index,\n                  time: /* @__PURE__ */ new Date()\n                });\n                stream2 == null ? void 0 : stream2.close();\n                close();\n              }\n            } else if (type === \"complete\") {\n              complete = status;\n            } else if (type === \"log\") {\n              fire_event({\n                type: \"log\",\n                log: data2.log,\n                level: data2.level,\n                endpoint: _endpoint,\n                fn_index\n              });\n            } else if (type === \"generating\") {\n              fire_event({\n                type: \"status\",\n                time: /* @__PURE__ */ new Date(),\n                ...status,\n                stage: status == null ? void 0 : status.stage,\n                queue: true,\n                endpoint: _endpoint,\n                fn_index\n              });\n            }\n            if (data2) {\n              fire_event({\n                type: \"data\",\n                time: /* @__PURE__ */ new Date(),\n                data: handle_payload(\n                  data2.data,\n                  dependency,\n                  config.components,\n                  \"output\",\n                  options.with_null_state\n                ),\n                endpoint: _endpoint,\n                fn_index,\n                event_data,\n                trigger_id\n              });\n              if (complete) {\n                fire_event({\n                  type: \"status\",\n                  time: /* @__PURE__ */ new Date(),\n                  ...complete,\n                  stage: status == null ? void 0 : status.stage,\n                  queue: true,\n                  endpoint: _endpoint,\n                  fn_index\n                });\n                stream2 == null ? void 0 : stream2.close();\n                close();\n              }\n            }\n          };\n        } else if (protocol == \"sse_v1\" || protocol == \"sse_v2\" || protocol == \"sse_v2.1\" || protocol == \"sse_v3\") {\n          fire_event({\n            type: \"status\",\n            stage: \"pending\",\n            queue: true,\n            endpoint: _endpoint,\n            fn_index,\n            time: /* @__PURE__ */ new Date()\n          });\n          let hostname = \"\";\n          if (typeof window !== \"undefined\" && typeof document !== \"undefined\") {\n            hostname = (_a2 = window == null ? void 0 : window.location) == null ? void 0 : _a2.hostname;\n          }\n          let hfhubdev = \"dev.spaces.huggingface.tech\";\n          const origin = hostname.includes(\".dev.\") ? `https://moon-${hostname.split(\".\")[1]}.${hfhubdev}` : `https://huggingface.co`;\n          const is_iframe = typeof window !== \"undefined\" && typeof document !== \"undefined\" && window.parent != window;\n          const is_zerogpu_space = dependency.zerogpu && config.space_id;\n          const zerogpu_auth_promise = is_iframe && is_zerogpu_space ? post_message(\"zerogpu-headers\", origin) : Promise.resolve(null);\n          const post_data_promise = zerogpu_auth_promise.then((headers) => {\n            return post_data2(\n              `${config.root}/queue/join?${url_params}`,\n              {\n                ...payload,\n                session_hash\n              },\n              headers\n            );\n          });\n          post_data_promise.then(async ([response, status]) => {\n            if (status === 503) {\n              fire_event({\n                type: \"status\",\n                stage: \"error\",\n                message: QUEUE_FULL_MSG,\n                queue: true,\n                endpoint: _endpoint,\n                fn_index,\n                time: /* @__PURE__ */ new Date()\n              });\n            } else if (status !== 200) {\n              fire_event({\n                type: \"status\",\n                stage: \"error\",\n                message: BROKEN_CONNECTION_MSG,\n                queue: true,\n                endpoint: _endpoint,\n                fn_index,\n                time: /* @__PURE__ */ new Date()\n              });\n            } else {\n              event_id = response.event_id;\n              let callback = async function(_data) {\n                try {\n                  const { type, status: status2, data: data2 } = handle_message(\n                    _data,\n                    last_status[fn_index]\n                  );\n                  if (type == \"heartbeat\") {\n                    return;\n                  }\n                  if (type === \"update\" && status2 && !complete) {\n                    fire_event({\n                      type: \"status\",\n                      endpoint: _endpoint,\n                      fn_index,\n                      time: /* @__PURE__ */ new Date(),\n                      ...status2\n                    });\n                  } else if (type === \"complete\") {\n                    complete = status2;\n                  } else if (type == \"unexpected_error\") {\n                    console.error(\"Unexpected error\", status2 == null ? void 0 : status2.message);\n                    fire_event({\n                      type: \"status\",\n                      stage: \"error\",\n                      message: (status2 == null ? void 0 : status2.message) || \"An Unexpected Error Occurred!\",\n                      queue: true,\n                      endpoint: _endpoint,\n                      fn_index,\n                      time: /* @__PURE__ */ new Date()\n                    });\n                  } else if (type === \"log\") {\n                    fire_event({\n                      type: \"log\",\n                      log: data2.log,\n                      level: data2.level,\n                      endpoint: _endpoint,\n                      fn_index\n                    });\n                    return;\n                  } else if (type === \"generating\") {\n                    fire_event({\n                      type: \"status\",\n                      time: /* @__PURE__ */ new Date(),\n                      ...status2,\n                      stage: status2 == null ? void 0 : status2.stage,\n                      queue: true,\n                      endpoint: _endpoint,\n                      fn_index\n                    });\n                    if (data2 && [\"sse_v2\", \"sse_v2.1\", \"sse_v3\"].includes(protocol)) {\n                      apply_diff_stream(pending_diff_streams, event_id, data2);\n                    }\n                  }\n                  if (data2) {\n                    fire_event({\n                      type: \"data\",\n                      time: /* @__PURE__ */ new Date(),\n                      data: handle_payload(\n                        data2.data,\n                        dependency,\n                        config.components,\n                        \"output\",\n                        options.with_null_state\n                      ),\n                      endpoint: _endpoint,\n                      fn_index\n                    });\n                    if (data2.render_config) {\n                      await handle_render_config(data2.render_config);\n                    }\n                    if (complete) {\n                      fire_event({\n                        type: \"status\",\n                        time: /* @__PURE__ */ new Date(),\n                        ...complete,\n                        stage: status2 == null ? void 0 : status2.stage,\n                        queue: true,\n                        endpoint: _endpoint,\n                        fn_index\n                      });\n                    }\n                  }\n                  if ((status2 == null ? void 0 : status2.stage) === \"complete\" || (status2 == null ? void 0 : status2.stage) === \"error\") {\n                    if (event_callbacks[event_id]) {\n                      delete event_callbacks[event_id];\n                    }\n                    if (event_id in pending_diff_streams) {\n                      delete pending_diff_streams[event_id];\n                    }\n                  }\n                } catch (e) {\n                  console.error(\"Unexpected client exception\", e);\n                  fire_event({\n                    type: \"status\",\n                    stage: \"error\",\n                    message: \"An Unexpected Error Occurred!\",\n                    queue: true,\n                    endpoint: _endpoint,\n                    fn_index,\n                    time: /* @__PURE__ */ new Date()\n                  });\n                  if ([\"sse_v2\", \"sse_v2.1\", \"sse_v3\"].includes(protocol)) {\n                    close_stream(stream_status, that.abort_controller);\n                    stream_status.open = false;\n                    close();\n                  }\n                }\n              };\n              if (event_id in pending_stream_messages) {\n                pending_stream_messages[event_id].forEach(\n                  (msg) => callback(msg)\n                );\n                delete pending_stream_messages[event_id];\n              }\n              event_callbacks[event_id] = callback;\n              unclosed_events.add(event_id);\n              if (!stream_status.open) {\n                await this.open_stream();\n              }\n            }\n          });\n        }\n      }\n    );\n    let done = false;\n    const values = [];\n    const resolvers = [];\n    const iterator = {\n      [Symbol.asyncIterator]: () => iterator,\n      next,\n      throw: async (value) => {\n        push_error(value);\n        return next();\n      },\n      return: async () => {\n        close();\n        return next();\n      },\n      cancel\n    };\n    return iterator;\n  } catch (error) {\n    console.error(\"Submit function encountered an error:\", error);\n    throw error;\n  }\n}\nfunction thenable_reject(error) {\n  return {\n    then: (resolve, reject) => reject(error)\n  };\n}\nfunction get_endpoint_info(api_info, endpoint, api_map, config) {\n  let fn_index;\n  let endpoint_info;\n  let dependency;\n  if (typeof endpoint === \"number\") {\n    fn_index = endpoint;\n    endpoint_info = api_info.unnamed_endpoints[fn_index];\n    dependency = config.dependencies.find((dep) => dep.id == endpoint);\n  } else {\n    const trimmed_endpoint = endpoint.replace(/^\\//, \"\");\n    fn_index = api_map[trimmed_endpoint];\n    endpoint_info = api_info.named_endpoints[endpoint.trim()];\n    dependency = config.dependencies.find(\n      (dep) => dep.id == api_map[trimmed_endpoint]\n    );\n  }\n  if (typeof fn_index !== \"number\") {\n    throw new Error(\n      \"There is no endpoint matching that name of fn_index matching that number.\"\n    );\n  }\n  return { fn_index, endpoint_info, dependency };\n}\nclass Client {\n  constructor(app_reference, options = { events: [\"data\"] }) {\n    __publicField(this, \"app_reference\");\n    __publicField(this, \"options\");\n    __publicField(this, \"config\");\n    __publicField(this, \"api_info\");\n    __publicField(this, \"api_map\", {});\n    __publicField(this, \"session_hash\", Math.random().toString(36).substring(2));\n    __publicField(this, \"jwt\", false);\n    __publicField(this, \"last_status\", {});\n    __publicField(this, \"cookies\", null);\n    // streaming\n    __publicField(this, \"stream_status\", { open: false });\n    __publicField(this, \"pending_stream_messages\", {});\n    __publicField(this, \"pending_diff_streams\", {});\n    __publicField(this, \"event_callbacks\", {});\n    __publicField(this, \"unclosed_events\", /* @__PURE__ */ new Set());\n    __publicField(this, \"heartbeat_event\", null);\n    __publicField(this, \"abort_controller\", null);\n    __publicField(this, \"stream_instance\", null);\n    __publicField(this, \"view_api\");\n    __publicField(this, \"upload_files\");\n    __publicField(this, \"upload\");\n    __publicField(this, \"handle_blob\");\n    __publicField(this, \"post_data\");\n    __publicField(this, \"submit\");\n    __publicField(this, \"predict\");\n    __publicField(this, \"open_stream\");\n    __publicField(this, \"resolve_config\");\n    __publicField(this, \"resolve_cookies\");\n    this.app_reference = app_reference;\n    if (!options.events) {\n      options.events = [\"data\"];\n    }\n    this.options = options;\n    this.view_api = view_api.bind(this);\n    this.upload_files = upload_files.bind(this);\n    this.handle_blob = handle_blob.bind(this);\n    this.post_data = post_data.bind(this);\n    this.submit = submit.bind(this);\n    this.predict = predict.bind(this);\n    this.open_stream = open_stream.bind(this);\n    this.resolve_config = resolve_config.bind(this);\n    this.resolve_cookies = resolve_cookies.bind(this);\n    this.upload = upload.bind(this);\n  }\n  fetch(input, init) {\n    const headers = new Headers((init == null ? void 0 : init.headers) || {});\n    if (this && this.cookies) {\n      headers.append(\"Cookie\", this.cookies);\n    }\n    return fetch(input, { ...init, headers });\n  }\n  stream(url) {\n    this.abort_controller = new AbortController();\n    this.stream_instance = readable_stream(url.toString(), {\n      signal: this.abort_controller.signal\n    });\n    return this.stream_instance;\n  }\n  async init() {\n    var _a;\n    if ((typeof window === \"undefined\" || !(\"WebSocket\" in window)) && !global.WebSocket) {\n      const ws = await import(\"./wrapper-CviSselG.js\");\n      global.WebSocket = ws.WebSocket;\n    }\n    try {\n      if (this.options.auth) {\n        await this.resolve_cookies();\n      }\n      await this._resolve_config().then(\n        ({ config }) => this._resolve_hearbeat(config)\n      );\n    } catch (e) {\n      throw Error(e);\n    }\n    this.api_info = await this.view_api();\n    this.api_map = map_names_to_ids(((_a = this.config) == null ? void 0 : _a.dependencies) || []);\n  }\n  async _resolve_hearbeat(_config) {\n    if (_config) {\n      this.config = _config;\n      if (this.config && this.config.connect_heartbeat) {\n        if (this.config.space_id && this.options.hf_token) {\n          this.jwt = await get_jwt(\n            this.config.space_id,\n            this.options.hf_token,\n            this.cookies\n          );\n        }\n      }\n    }\n    if (_config.space_id && this.options.hf_token) {\n      this.jwt = await get_jwt(_config.space_id, this.options.hf_token);\n    }\n    if (this.config && this.config.connect_heartbeat) {\n      const heartbeat_url = new URL(\n        `${this.config.root}/heartbeat/${this.session_hash}`\n      );\n      if (this.jwt) {\n        heartbeat_url.searchParams.set(\"__sign\", this.jwt);\n      }\n      if (!this.heartbeat_event) {\n        this.heartbeat_event = this.stream(heartbeat_url);\n      }\n    }\n  }\n  static async connect(app_reference, options = {\n    events: [\"data\"]\n  }) {\n    const client2 = new this(app_reference, options);\n    await client2.init();\n    return client2;\n  }\n  close() {\n    var _a;\n    (_a = this.heartbeat_event) == null ? void 0 : _a.close();\n  }\n  static async duplicate(app_reference, options = {\n    events: [\"data\"]\n  }) {\n    return duplicate(app_reference, options);\n  }\n  async _resolve_config() {\n    const { http_protocol, host, space_id } = await process_endpoint(\n      this.app_reference,\n      this.options.hf_token\n    );\n    const { status_callback } = this.options;\n    let config;\n    try {\n      config = await this.resolve_config(`${http_protocol}//${host}`);\n      if (!config) {\n        throw new Error(CONFIG_ERROR_MSG);\n      }\n      return this.config_success(config);\n    } catch (e) {\n      if (space_id && status_callback) {\n        check_space_status(\n          space_id,\n          RE_SPACE_NAME.test(space_id) ? \"space_name\" : \"subdomain\",\n          this.handle_space_success\n        );\n      } else {\n        if (status_callback)\n          status_callback({\n            status: \"error\",\n            message: \"Could not load this space.\",\n            load_status: \"error\",\n            detail: \"NOT_FOUND\"\n          });\n        throw Error(e);\n      }\n    }\n  }\n  async config_success(_config) {\n    this.config = _config;\n    if (typeof window !== \"undefined\" && typeof document !== \"undefined\") {\n      if (window.location.protocol === \"https:\") {\n        this.config.root = this.config.root.replace(\"http://\", \"https://\");\n      }\n    }\n    if (this.config.auth_required) {\n      return this.prepare_return_obj();\n    }\n    try {\n      this.api_info = await this.view_api();\n    } catch (e) {\n      console.error(API_INFO_ERROR_MSG + e.message);\n    }\n    return this.prepare_return_obj();\n  }\n  async handle_space_success(status) {\n    if (!this) {\n      throw new Error(CONFIG_ERROR_MSG);\n    }\n    const { status_callback } = this.options;\n    if (status_callback)\n      status_callback(status);\n    if (status.status === \"running\") {\n      try {\n        this.config = await this._resolve_config();\n        if (!this.config) {\n          throw new Error(CONFIG_ERROR_MSG);\n        }\n        const _config = await this.config_success(this.config);\n        return _config;\n      } catch (e) {\n        if (status_callback) {\n          status_callback({\n            status: \"error\",\n            message: \"Could not load this space.\",\n            load_status: \"error\",\n            detail: \"NOT_FOUND\"\n          });\n        }\n        throw e;\n      }\n    }\n  }\n  async component_server(component_id, fn_name, data) {\n    var _a;\n    if (!this.config) {\n      throw new Error(CONFIG_ERROR_MSG);\n    }\n    const headers = {};\n    const { hf_token } = this.options;\n    const { session_hash } = this;\n    if (hf_token) {\n      headers.Authorization = `Bearer ${this.options.hf_token}`;\n    }\n    let root_url;\n    let component = this.config.components.find(\n      (comp) => comp.id === component_id\n    );\n    if ((_a = component == null ? void 0 : component.props) == null ? void 0 : _a.root_url) {\n      root_url = component.props.root_url;\n    } else {\n      root_url = this.config.root;\n    }\n    let body;\n    if (\"binary\" in data) {\n      body = new FormData();\n      for (const key in data.data) {\n        if (key === \"binary\")\n          continue;\n        body.append(key, data.data[key]);\n      }\n      body.set(\"component_id\", component_id.toString());\n      body.set(\"fn_name\", fn_name);\n      body.set(\"session_hash\", session_hash);\n    } else {\n      body = JSON.stringify({\n        data,\n        component_id,\n        fn_name,\n        session_hash\n      });\n      headers[\"Content-Type\"] = \"application/json\";\n    }\n    if (hf_token) {\n      headers.Authorization = `Bearer ${hf_token}`;\n    }\n    try {\n      const response = await this.fetch(`${root_url}/component_server/`, {\n        method: \"POST\",\n        body,\n        headers,\n        credentials: \"include\"\n      });\n      if (!response.ok) {\n        throw new Error(\n          \"Could not connect to component server: \" + response.statusText\n        );\n      }\n      const output = await response.json();\n      return output;\n    } catch (e) {\n      console.warn(e);\n    }\n  }\n  set_cookies(raw_cookies) {\n    this.cookies = parse_and_set_cookies(raw_cookies).join(\"; \");\n  }\n  prepare_return_obj() {\n    return {\n      config: this.config,\n      predict: this.predict,\n      submit: this.submit,\n      view_api: this.view_api,\n      component_server: this.component_server\n    };\n  }\n}\nasync function client(app_reference, options = {\n  events: [\"data\"]\n}) {\n  return await Client.connect(app_reference, options);\n}\nasync function duplicate_space(app_reference, options) {\n  return await Client.duplicate(app_reference, options);\n}\nexport {\n  Client,\n  FileData,\n  client,\n  duplicate_space as duplicate,\n  handle_file,\n  predict,\n  prepare_files,\n  submit,\n  upload,\n  upload_files\n};\n", "let supports_adopted_stylesheets = false;\n\nif (\n\t\"attachShadow\" in Element.prototype &&\n\t\"adoptedStyleSheets\" in Document.prototype\n) {\n\t// Both Shadow DOM and adoptedStyleSheets are supported\n\tconst shadow_root_test = document\n\t\t.createElement(\"div\")\n\t\t.attachShadow({ mode: \"open\" });\n\tsupports_adopted_stylesheets = \"adoptedStyleSheets\" in shadow_root_test;\n}\n\nexport function mount_css(url: string, target: HTMLElement): Promise<void> {\n\tconst base = new URL(import.meta.url).origin;\n\tconst _url = new URL(url, base).href;\n\tconst existing_link = document.querySelector(`link[href='${_url}']`);\n\n\tif (existing_link) return Promise.resolve();\n\n\tconst link = document.createElement(\"link\");\n\tlink.rel = \"stylesheet\";\n\tlink.href = _url;\n\n\treturn new Promise((res, rej) => {\n\t\tlink.addEventListener(\"load\", () => res());\n\t\tlink.addEventListener(\"error\", () => {\n\t\t\tconsole.error(`Unable to preload CSS for ${_url}`);\n\t\t\tres();\n\t\t});\n\t\ttarget.appendChild(link);\n\t});\n}\n\nexport function prefix_css(\n\tstring: string,\n\tversion: string,\n\tstyle_element = document.createElement(\"style\")\n): HTMLStyleElement | null {\n\tif (!supports_adopted_stylesheets) return null;\n\tstyle_element.remove();\n\n\tconst stylesheet = new CSSStyleSheet();\n\tstylesheet.replaceSync(string);\n\n\tlet importString = \"\";\n\tstring = string.replace(/@import\\s+url\\((.*?)\\);\\s*/g, (match, url) => {\n\t\timportString += `@import url(${url});\\n`;\n\t\treturn \"\"; // remove and store any @import statements from the CSS\n\t});\n\n\tconst rules = stylesheet.cssRules;\n\n\tlet css_string = \"\";\n\tlet gradio_css_infix = `gradio-app .gradio-container.gradio-container-${version} .contain `;\n\n\tfor (let i = 0; i < rules.length; i++) {\n\t\tconst rule = rules[i];\n\n\t\tlet is_dark_rule = rule.cssText.includes(\".dark\");\n\t\tif (rule instanceof CSSStyleRule) {\n\t\t\tconst selector = rule.selectorText;\n\t\t\tif (selector) {\n\t\t\t\tconst new_selector = selector\n\t\t\t\t\t.replace(\".dark\", \"\")\n\t\t\t\t\t.split(\",\")\n\t\t\t\t\t.map(\n\t\t\t\t\t\t(s) =>\n\t\t\t\t\t\t\t`${is_dark_rule ? \".dark\" : \"\"} ${gradio_css_infix} ${s.trim()} `\n\t\t\t\t\t)\n\t\t\t\t\t.join(\",\");\n\n\t\t\t\tcss_string += rule.cssText;\n\t\t\t\tcss_string += rule.cssText.replace(selector, new_selector);\n\t\t\t}\n\t\t} else if (rule instanceof CSSMediaRule) {\n\t\t\tlet mediaCssString = `@media ${rule.media.mediaText} {`;\n\t\t\tfor (let j = 0; j < rule.cssRules.length; j++) {\n\t\t\t\tconst innerRule = rule.cssRules[j];\n\t\t\t\tif (innerRule instanceof CSSStyleRule) {\n\t\t\t\t\tlet is_dark_rule = innerRule.cssText.includes(\".dark \");\n\t\t\t\t\tconst selector = innerRule.selectorText;\n\t\t\t\t\tconst new_selector = selector\n\t\t\t\t\t\t.replace(\".dark\", \"\")\n\t\t\t\t\t\t.split(\",\")\n\t\t\t\t\t\t.map(\n\t\t\t\t\t\t\t(s) =>\n\t\t\t\t\t\t\t\t`${\n\t\t\t\t\t\t\t\t\tis_dark_rule ? \".dark\" : \"\"\n\t\t\t\t\t\t\t\t} ${gradio_css_infix} ${s.trim()} `\n\t\t\t\t\t\t)\n\t\t\t\t\t\t.join(\",\");\n\t\t\t\t\tmediaCssString += innerRule.cssText.replace(selector, new_selector);\n\t\t\t\t}\n\t\t\t}\n\t\t\tmediaCssString += \"}\";\n\t\t\tcss_string += mediaCssString;\n\t\t} else if (rule instanceof CSSKeyframesRule) {\n\t\t\tcss_string += `@keyframes ${rule.name} {`;\n\t\t\tfor (let j = 0; j < rule.cssRules.length; j++) {\n\t\t\t\tconst innerRule = rule.cssRules[j];\n\t\t\t\tif (innerRule instanceof CSSKeyframeRule) {\n\t\t\t\t\tcss_string += `${innerRule.keyText} { ${innerRule.style.cssText} }`;\n\t\t\t\t}\n\t\t\t}\n\t\t\tcss_string += \"}\";\n\t\t} else if (rule instanceof CSSFontFaceRule) {\n\t\t\tcss_string += `@font-face { ${rule.style.cssText} }`;\n\t\t}\n\t}\n\tcss_string = importString + css_string;\n\tstyle_element.textContent = css_string;\n\n\tdocument.head.appendChild(style_element);\n\treturn style_element;\n}\n", "import \"@gradio/theme/src/reset.css\";\nimport \"@gradio/theme/src/global.css\";\nimport \"@gradio/theme/src/pollen.css\";\nimport \"@gradio/theme/src/typography.css\";\nimport { Client } from \"@gradio/client\";\nimport { mount_css } from \"./css\";\nimport type Index from \"./Index.svelte\";\n\nimport type { ThemeMode } from \"./types\";\n\n//@ts-ignore\nimport * as svelte from \"./svelte/svelte.js\";\n\ndeclare let BUILD_MODE: string;\ndeclare let GRADIO_VERSION: string;\n\nconst ENTRY_CSS = \"__ENTRY_CSS__\";\n\nlet FONTS: string | [];\n\nFONTS = \"__FONTS_CSS__\";\n\nlet IndexComponent: typeof Index;\nlet _res: (value?: unknown) => void;\nlet pending = new Promise((res) => {\n\t_res = res;\n});\nasync function get_index(): Promise<void> {\n\tIndexComponent = (await import(\"./Index.svelte\")).default;\n\t_res();\n}\n\nfunction create_custom_element(): void {\n\tconst o = {\n\t\tSvelteComponent: svelte.SvelteComponent\n\t};\n\tfor (const key in svelte) {\n\t\tif (key === \"SvelteComponent\") continue;\n\t\tif (key === \"SvelteComponentDev\") {\n\t\t\t//@ts-ignore\n\t\t\to[key] = o[\"SvelteComponent\"];\n\t\t} else {\n\t\t\t//@ts-ignore\n\t\t\to[key] = svelte[key];\n\t\t}\n\t}\n\t//@ts-ignore\n\twindow.__gradio__svelte__internal = o;\n\tclass GradioApp extends HTMLElement {\n\t\tcontrol_page_title: string | null;\n\t\tinitial_height: string;\n\t\tis_embed: string;\n\t\tcontainer: string;\n\t\tinfo: string | true;\n\t\tautoscroll: string | null;\n\t\teager: string | null;\n\t\ttheme_mode: ThemeMode | null;\n\t\thost: string | null;\n\t\tspace: string | null;\n\t\tsrc: string | null;\n\t\tapp?: Index;\n\t\tloading: boolean;\n\t\tupdating: { name: string; value: string } | false;\n\n\t\tconstructor() {\n\t\t\tsuper();\n\t\t\tthis.host = this.getAttribute(\"host\");\n\t\t\tthis.space = this.getAttribute(\"space\");\n\t\t\tthis.src = this.getAttribute(\"src\");\n\n\t\t\tthis.control_page_title = this.getAttribute(\"control_page_title\");\n\t\t\tthis.initial_height = this.getAttribute(\"initial_height\") ?? \"300px\"; // default: 300px\n\t\t\tthis.is_embed = this.getAttribute(\"embed\") ?? \"true\"; // default: true\n\t\t\tthis.container = this.getAttribute(\"container\") ?? \"true\"; // default: true\n\t\t\tthis.info = this.getAttribute(\"info\") ?? true; // default: true\n\t\t\tthis.autoscroll = this.getAttribute(\"autoscroll\");\n\t\t\tthis.eager = this.getAttribute(\"eager\");\n\t\t\tthis.theme_mode = this.getAttribute(\"theme_mode\") as ThemeMode | null;\n\t\t\tthis.updating = false;\n\t\t\tthis.loading = false;\n\t\t}\n\n\t\tasync connectedCallback(): Promise<void> {\n\t\t\tawait get_index();\n\t\t\tthis.loading = true;\n\n\t\t\tif (this.app) {\n\t\t\t\tthis.app.$destroy();\n\t\t\t}\n\n\t\t\tif (typeof FONTS !== \"string\") {\n\t\t\t\tFONTS.forEach((f) => mount_css(f, document.head));\n\t\t\t}\n\n\t\t\tawait mount_css(ENTRY_CSS, document.head);\n\n\t\t\tconst event = new CustomEvent(\"domchange\", {\n\t\t\t\tbubbles: true,\n\t\t\t\tcancelable: false,\n\t\t\t\tcomposed: true\n\t\t\t});\n\n\t\t\tconst observer = new MutationObserver((mutations) => {\n\t\t\t\tthis.dispatchEvent(event);\n\t\t\t});\n\n\t\t\tobserver.observe(this, { childList: true });\n\n\t\t\tthis.app = new IndexComponent({\n\t\t\t\ttarget: this,\n\t\t\t\tprops: {\n\t\t\t\t\t// embed source\n\t\t\t\t\tspace: this.space ? this.space.trim() : this.space,\n\t\t\t\t\tsrc: this.src ? this.src.trim() : this.src,\n\t\t\t\t\thost: this.host ? this.host.trim() : this.host,\n\t\t\t\t\t// embed info\n\t\t\t\t\tinfo: this.info === \"false\" ? false : true,\n\t\t\t\t\tcontainer: this.container === \"false\" ? false : true,\n\t\t\t\t\tis_embed: this.is_embed === \"false\" ? false : true,\n\t\t\t\t\tinitial_height: this.initial_height,\n\t\t\t\t\teager: this.eager === \"true\" ? true : false,\n\t\t\t\t\t// gradio meta info\n\t\t\t\t\tversion: GRADIO_VERSION,\n\t\t\t\t\ttheme_mode: this.theme_mode,\n\t\t\t\t\t// misc global behaviour\n\t\t\t\t\tautoscroll: this.autoscroll === \"true\" ? true : false,\n\t\t\t\t\tcontrol_page_title: this.control_page_title === \"true\" ? true : false,\n\t\t\t\t\t// injectables\n\t\t\t\t\tClient,\n\t\t\t\t\t// for gradio docs\n\t\t\t\t\t// TODO: Remove -- i think this is just for autoscroll behavhiour, app vs embeds\n\t\t\t\t\tapp_mode: window.__gradio_mode__ === \"app\"\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tif (this.updating) {\n\t\t\t\tthis.setAttribute(this.updating.name, this.updating.value);\n\t\t\t}\n\n\t\t\tthis.loading = false;\n\t\t}\n\n\t\tstatic get observedAttributes(): [\"src\", \"space\", \"host\"] {\n\t\t\treturn [\"src\", \"space\", \"host\"];\n\t\t}\n\n\t\tasync attributeChangedCallback(\n\t\t\tname: string,\n\t\t\told_val: string,\n\t\t\tnew_val: string\n\t\t): Promise<void> {\n\t\t\tawait pending;\n\t\t\tif (\n\t\t\t\t(name === \"host\" || name === \"space\" || name === \"src\") &&\n\t\t\t\tnew_val !== old_val\n\t\t\t) {\n\t\t\t\tthis.updating = { name, value: new_val };\n\t\t\t\tif (this.loading) return;\n\n\t\t\t\tif (this.app) {\n\t\t\t\t\tthis.app.$destroy();\n\t\t\t\t}\n\n\t\t\t\tthis.space = null;\n\t\t\t\tthis.host = null;\n\t\t\t\tthis.src = null;\n\n\t\t\t\tif (name === \"host\") {\n\t\t\t\t\tthis.host = new_val;\n\t\t\t\t} else if (name === \"space\") {\n\t\t\t\t\tthis.space = new_val;\n\t\t\t\t} else if (name === \"src\") {\n\t\t\t\t\tthis.src = new_val;\n\t\t\t\t}\n\n\t\t\t\tthis.app = new IndexComponent({\n\t\t\t\t\ttarget: this,\n\t\t\t\t\tprops: {\n\t\t\t\t\t\t// embed source\n\t\t\t\t\t\tspace: this.space ? this.space.trim() : this.space,\n\t\t\t\t\t\tsrc: this.src ? this.src.trim() : this.src,\n\t\t\t\t\t\thost: this.host ? this.host.trim() : this.host,\n\t\t\t\t\t\t// embed info\n\t\t\t\t\t\tinfo: this.info === \"false\" ? false : true,\n\t\t\t\t\t\tcontainer: this.container === \"false\" ? false : true,\n\t\t\t\t\t\tis_embed: this.is_embed === \"false\" ? false : true,\n\t\t\t\t\t\tinitial_height: this.initial_height,\n\t\t\t\t\t\teager: this.eager === \"true\" ? true : false,\n\t\t\t\t\t\t// gradio meta info\n\t\t\t\t\t\tversion: GRADIO_VERSION,\n\t\t\t\t\t\ttheme_mode: this.theme_mode,\n\t\t\t\t\t\t// misc global behaviour\n\t\t\t\t\t\tautoscroll: this.autoscroll === \"true\" ? true : false,\n\t\t\t\t\t\tcontrol_page_title:\n\t\t\t\t\t\t\tthis.control_page_title === \"true\" ? true : false,\n\t\t\t\t\t\t// injectables\n\t\t\t\t\t\tClient,\n\t\t\t\t\t\t// for gradio docs\n\t\t\t\t\t\t// TODO: Remove -- i think this is just for autoscroll behavhiour, app vs embeds\n\t\t\t\t\t\tapp_mode: window.__gradio_mode__ === \"app\"\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tthis.updating = false;\n\t\t\t}\n\t\t}\n\t}\n\tif (!customElements.get(\"gradio-app\"))\n\t\tcustomElements.define(\"gradio-app\", GradioApp);\n}\n\ncreate_custom_element();\n"], "file": "assets/index-COY1HN2y.js"}