{"version": 3, "mappings": ";uuDAAA,IAAIA,GAAK,IAAI,KAAK,SAAS,EAAG,CAAE,QAAQ,EAAG,EAAE,QAE9B,SAAAC,GAAUC,EAAGC,EAAGC,EAAM,CACpC,OAAAF,EAAIA,EAAE,MAAM,GAAG,EACfC,EAAIA,EAAE,MAAM,GAAG,EAERH,GAAGE,EAAE,CAAC,EAAGC,EAAE,CAAC,CAAC,GAAKH,GAAGE,EAAE,CAAC,EAAGC,EAAE,CAAC,CAAC,IACrCA,EAAE,CAAC,EAAIA,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,EAC1BC,EAAO,OAAO,KAAKF,EAAE,CAAC,EAAIA,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC,EAC9CE,GAAQ,OAAO,KAAKD,EAAE,CAAC,CAAC,EAAIH,GAAGE,EAAE,CAAC,EAAGC,EAAE,CAAC,CAAC,EAAIC,EAAO,GAAK,EAE3D,CCVO,MAAMC,GAAW,OAMXC,GAAa,SACbC,GAAY,QACZC,GAAa,SACbC,GAAe,OACfC,GAAc,UACdC,GAAgB,YAEhBC,GACZ,mDAKYC,GACZ,yDACYC,EAAwB,2BACxBC,GAAmB,iCACnBC,GAAyB,+BACzBC,GAAqB,2BACrBC,GAA2B,uCAC3BC,GAAkB,4CAClBC,GAAmB,wCACnBC,GAA0B,yCAC1BC,GACZ,uDACYC,GACZ,+DACYC,GAAqB,sCACrBC,GAA4B,uBCZzB,SAAAC,GACfC,EACAC,EACAC,EACS,CACT,OAAID,EAAU,WAAW,SAAS,GAAKA,EAAU,WAAW,UAAU,EAC9DC,EAAkBF,EAAWC,EAE9BD,EAAWC,CACnB,CAEsB,eAAAE,GACrBC,EACAC,EACAC,EAC0B,CACtB,IAUH,OAFa,MAPH,MAAM,MAAM,qCAAqCF,CAAK,OAAQ,CACvE,QAAS,CACR,cAAe,UAAUC,CAAK,GAC9B,GAAIC,EAAU,CAAE,OAAQA,GAAY,CAAC,CACtC,EACA,GAEoB,QAAQ,OAEf,QACH,CACJ,QACR,CACD,CAEO,SAASC,GACfC,EACyB,CACzB,IAAIC,EAA+B,GAEnC,OAAAD,EAAI,QAAQ,CAAC,CAAE,SAAAE,EAAU,GAAAC,KAAS,CAC7BD,IAAUD,EAAKC,CAAQ,EAAIC,EAAA,CAC/B,EACMF,CACR,CAEA,eAAsBG,GAErBC,EAC8B,CAC9B,MAAMC,EAAkC,KAAK,QAAQ,SAClD,CAAE,cAAe,UAAU,KAAK,QAAQ,QAAQ,EAAG,EACnD,GAKF,GAHDA,EAAQ,cAAc,EAAI,mBAGzB,OAAO,OAAW,KAClB,OAAO,eACP,SAAS,SAAW,yBACpB,CAAC,OAAO,cAAc,SACrB,CACK,MAAAC,EAAO,OAAO,cAAc,KAC5BC,EAAS,OAAO,cACtB,IAAIC,EAAclB,GAAac,EAAUG,EAAO,KAAM,EAAK,EAC3D,OAAAA,EAAO,KAAOC,EACP,CAAE,GAAGD,EAAQ,KAAAD,WACVF,EAAU,CACd,MAAAK,EAAaC,GAAUN,EAAUhC,EAAU,EAC3CuC,EAAW,MAAM,KAAK,MAAMF,EAAY,CAC7C,QAAAJ,EACA,YAAa,UACb,EAED,GAAIM,GAAU,SAAW,KAAO,CAAC,KAAK,QAAQ,KACvC,UAAI,MAAMzB,EAAuB,KAC7ByB,GAAU,SAAW,KAAO,KAAK,QAAQ,KAC7C,UAAI,MAAM1B,EAAuB,EAEpC,GAAA0B,GAAU,SAAW,IAAK,CACzB,IAAAJ,EAAS,MAAMI,EAAS,OACrB,OAAAJ,EAAA,KAAOA,EAAO,MAAQ,GAC7BA,EAAO,KAAOH,EACdG,EAAO,cAAc,QAAQ,CAACK,EAAUC,IAAc,CACjDD,EAAI,KAAO,SACdA,EAAI,GAAKC,EACV,CACA,EACMN,CAAA,SACGI,GAAU,SAAW,IACzB,UAAI,MAAM3B,EAAgB,EAE3B,UAAI,MAAML,EAAgB,CACjC,CAEM,UAAI,MAAMA,EAAgB,CACjC,CAEA,eAAsBmC,IAA6C,CAClE,KAAM,CAAE,cAAAC,EAAe,KAAAC,CAAK,EAAI,MAAMC,GACrC,KAAK,cACL,KAAK,QAAQ,UAGV,IACC,QAAK,QAAQ,KAAM,CACtB,MAAMC,EAAgB,MAAMC,GAC3BJ,EACAC,EACA,KAAK,QAAQ,KACb,KAAK,MACL,KAAK,QAAQ,UAGVE,GAAe,KAAK,YAAYA,CAAa,CAClD,QACQE,EAAY,CACd,YAAOA,EAAY,OAAO,CACjC,CACD,CAGA,eAAsBD,GACrBJ,EACAC,EACAK,EACAC,EACAC,EACyB,CACnB,MAAAC,EAAW,IAAI,SACrBA,EAAS,OAAO,WAAYH,IAAO,CAAC,CAAC,EACrCG,EAAS,OAAO,WAAYH,IAAO,CAAC,CAAC,EAErC,IAAIhB,EAAsC,GAEtCkB,IACKlB,EAAA,cAAgB,UAAUkB,CAAQ,IAGrC,MAAAE,EAAM,MAAMH,EAAO,GAAGP,CAAa,KAAKC,CAAI,IAAI7C,EAAS,GAAI,CAClE,QAAAkC,EACA,OAAQ,OACR,KAAMmB,EACN,YAAa,UACb,EAEG,GAAAC,EAAI,SAAW,IACX,OAAAA,EAAI,QAAQ,IAAI,YAAY,EACpC,MAAWA,EAAI,SAAW,IACnB,IAAI,MAAMxC,EAAuB,EAEjC,IAAI,MAAMH,EAAwB,CAE1C,CAEO,SAAS4C,GAAmBtB,EAIjC,CACG,GAAAA,EAAS,WAAW,MAAM,EAAG,CAChC,KAAM,CAAE,SAAAuB,EAAU,KAAAX,EAAM,SAAAY,CAAa,MAAI,IAAIxB,CAAQ,EAEjD,OAAAY,EAAK,SAAS,UAAU,EACpB,CACN,YAAa,MACb,KAAAA,EACA,cAAeW,CAAA,EAGV,CACN,YAAaA,IAAa,SAAW,MAAQ,KAC7C,cAAeA,EACf,KAAMX,GAAQY,IAAa,IAAMA,EAAW,IAEnC,SAAAxB,EAAS,WAAW,OAAO,EAG9B,OACN,YAAa,KACb,cAAe,QACf,KAAM,cAKD,OACN,YAAa,MACb,cAAe,SACf,KAAMA,CAAA,CAER,CAEa,MAAAyB,GAAyBX,GAAoC,CACzE,IAAIrB,EAAoB,GAElB,OADQqB,EAAc,MAAM,2BAA2B,EACvD,QAASY,GAAW,CACnB,MAACC,EAAaC,CAAY,EAAIF,EAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAC9DC,GAAeC,GACVnC,EAAA,KAAK,GAAGkC,EAAY,MAAM,IAAIC,EAAa,MAAM,EAAE,CAC5D,CACA,EACMnC,CACR,EC/MaoC,GAAgB,yCAChBC,GAAkB,sBAET,eAAAjB,GACrBkB,EACAZ,EAME,CACF,MAAMlB,EAAsC,GACxCkB,IACKlB,EAAA,cAAgB,UAAUkB,CAAQ,IAG3C,MAAMa,EAAiBD,EAAc,OAAO,QAAQ,MAAO,EAAE,EAEzD,GAAAF,GAAc,KAAKG,CAAc,EAEhC,IAMH,MAAMC,GAAS,MALH,MAAM,MACjB,qCAAqCD,CAAc,IAAInE,EAAQ,GAC/D,CAAE,QAAAoC,CAAQ,IAGc,QAAQ,KAE1B,OACN,SAAU8B,EACV,GAAGT,GAAmBW,CAAK,QAEjB,CACL,UAAI,MAAMvD,EAAwB,CACzC,CAGG,GAAAoD,GAAgB,KAAKE,CAAc,EAAG,CAEzC,KAAM,CAAE,YAAAE,EAAa,cAAAvB,EAAe,KAAAC,CAAK,EACxCU,GAAmBU,CAAc,EAE3B,OACN,SAAUpB,EAAK,QAAQ,YAAa,EAAE,EACtC,YAAAsB,EACA,cAAAvB,EACA,KAAAC,CAAA,CAEF,CAEO,OACN,SAAU,GACV,GAAGU,GAAmBU,CAAc,EAEtC,CAEa,MAAA1B,GAAY,IAAI6B,IAA2B,CACnD,IACH,OAAOA,EAAK,OAAO,CAAChD,EAAkBiD,KAC1BjD,IAAS,QAAQ,OAAQ,EAAE,EAC/BiD,IAAK,QAAQ,OAAQ,EAAE,EACvB,IAAI,IAAIA,EAAMjD,EAAW,GAAG,EAAE,WACrC,OACU,CACL,UAAI,MAAMR,EAAe,CAChC,CACD,EAEgB,SAAA0D,GACfC,EACAnC,EACAoC,EACqB,CACrB,MAAMC,EAAuC,CAC5C,gBAAiB,CAAC,EAClB,kBAAmB,CAAC,GAGrB,cAAO,KAAKF,CAAQ,EAAE,QAASG,GAAa,EACvCA,IAAa,mBAAqBA,IAAa,uBACjCD,EAAAC,CAAQ,EAAI,GAE7B,OAAO,QAAQH,EAASG,CAAQ,CAAC,EAAE,QAClC,CAAC,CAACzC,EAAU,CAAE,WAAA0C,EAAY,QAAAC,CAAS,KAAM,CAClC,MAAAC,EACLzC,EAAO,aAAa,KAClBK,GACAA,EAAI,WAAaR,GACjBQ,EAAI,WAAaR,EAAS,QAAQ,IAAK,EAAE,IACxC,IACHuC,EAAQvC,EAAS,QAAQ,IAAK,EAAE,CAAC,GACjC,GAEK6C,EACLD,IAAoB,GACjBzC,EAAO,aAAa,KAAMK,GAAQA,EAAI,IAAMoC,CAAe,GACzD,MACF,CAAE,UAAW,GAAO,OAAQ,IAEhC,GACCA,IAAoB,IACpBzC,EAAO,aAAa,KAAMK,GAAQA,EAAI,IAAMoC,CAAe,GAAG,QAC3D,SAAWF,EAAW,OACxB,CACK,MAAAI,EAAa3C,EAAO,aACxB,KAAMK,GAAQA,EAAI,IAAMoC,CAAe,EACvC,OAAO,IACNG,GAAU5C,EAAO,WAAW,KAAM6C,GAAMA,EAAE,KAAOD,CAAK,GAAG,MAGxD,IACQD,EAAA,QAAQ,CAACG,EAAMC,IAAQ,CACjC,GAAID,IAAS,QAAS,CACrB,MAAME,EAAY,CACjB,UAAW,QACX,QAAS,KACT,kBAAmB,KACnB,sBAAuB,GACvB,eAAgB,KAChB,OAAQ,IAIET,EAAA,OAAOQ,EAAK,EAAGC,CAAS,CACpC,EACA,QACOnC,EAAG,CACX,QAAQ,MAAMA,CAAC,CAChB,CACD,CAEA,MAAMoC,EAAiB,CACtBC,EACAC,EACAC,EACAC,KACgB,CAChB,GAAGH,EACH,YAAaI,GAAgBJ,GAAM,KAAME,CAAU,EACnD,KACCG,GAASL,GAAM,KAAMC,EAAWC,EAAYC,CAAc,GAAK,KAGhDhB,EAAAC,CAAQ,EAAEzC,CAAQ,EAAI,CACtC,WAAY0C,EAAW,IAAKiB,GAC3BP,EAAeO,EAAGA,GAAG,UAAWA,GAAG,WAAY,WAAW,CAC3D,EACA,QAAShB,EAAQ,IAAKiB,GACrBR,EAAeQ,EAAGA,GAAG,UAAWA,GAAG,WAAY,QAAQ,CACxD,EACA,KAAMf,CAAA,CAER,GAEF,CACA,EAEML,CACR,CAEO,SAASkB,GACfG,EACAP,EACAC,EACAC,EACqB,CACrB,OAAQK,GAAM,KAAM,CACnB,IAAK,SACG,eACR,IAAK,UACG,gBACR,IAAK,SACG,cACT,CAGC,GAAAN,IAAe,oBACfA,IAAe,qBAER,YACR,GAAWA,IAAe,yBAClB,iBACR,GAAWD,IAAc,QACjB,OAAAE,IAAmB,YAAc,uBAAyB,SAClE,GAAWD,IAAe,mBACrB,OAAAM,GAAM,OAAS,QACXL,IAAmB,YACvB,2BACA,wFAEGA,IAAmB,YACvB,uBACA,sFACJ,GAAWD,IAAe,sBAClB,OAAAC,IAAmB,YACvB,8CACA,2GAEL,CAEgB,SAAAC,GACfI,EACAN,EACS,CACT,OAAIA,IAAe,sBACX,gCACGA,IAAe,yBAClB,mBACGA,IAAe,mBAClB,gCAEDM,GAAM,WACd,CAGgB,SAAAC,GACfT,EACAU,EAcC,CAED,OAAQV,EAAK,IAAK,CACjB,IAAK,YACG,OAAE,KAAM,QAChB,IAAK,YACG,OAAE,KAAM,QAChB,IAAK,aACG,OACN,KAAM,SACN,OAAQ,CACP,SACA,QAAShF,GACT,MAAO,QACP,KAAMgF,EAAK,KACX,QAASA,EAAK,OACf,GAEF,IAAK,YACG,OACN,KAAM,aAER,IAAK,mBACG,OACN,KAAM,mBACN,OAAQ,CACP,SACA,QAASA,EAAK,QACd,MAAO,QACP,QAAS,EACV,GAEF,IAAK,aACG,OACN,KAAM,SACN,OAAQ,CACP,SACA,MAAOU,GAAe,UACtB,KAAMV,EAAK,KACX,KAAMA,EAAK,WACX,SAAUA,EAAK,KACf,IAAKA,EAAK,SACV,QAASA,EAAK,OACf,GAEF,IAAK,WACG,OACN,KAAM,SACN,OAAQ,CACP,SACA,MAAO,UACP,KAAMA,EAAK,KACX,cAAeA,EAAK,cACpB,QAASA,EAAK,OACf,GAEF,IAAK,MACG,OAAE,KAAM,MAAO,KAAAA,GACvB,IAAK,qBACG,OACN,KAAM,aACN,OAAQ,CACP,SACA,QAAUA,EAAK,QAA8B,KAApBA,EAAK,OAAO,MACrC,MAAOA,EAAK,QAAU,aAAe,QACrC,KAAMA,EAAK,KACX,cAAeA,EAAK,cACpB,IAAKA,EAAK,gBACX,EACA,KAAMA,EAAK,QAAUA,EAAK,OAAS,MAErC,IAAK,oBACA,gBAAWA,EAAK,OACZ,CACN,KAAM,SACN,OAAQ,CACP,SACA,QAASA,EAAK,OAAO,MACrB,QAASA,EAAK,OAAO,QACrB,SAAUA,EAAK,OAAO,SACtB,MAAO,QACP,KAAMA,EAAK,KACX,QAASA,EAAK,OACf,GAGK,CACN,KAAM,WACN,OAAQ,CACP,SACA,QAAUA,EAAK,QAA8B,OAApBA,EAAK,OAAO,MACrC,MAAOA,EAAK,QAAU,WAAa,QACnC,KAAMA,EAAK,KACX,cAAeA,EAAK,cACpB,kBAAmBA,EAAK,QACrBA,EAAK,OAAO,kBACZ,MACJ,EACA,KAAMA,EAAK,QAAUA,EAAK,OAAS,MAGrC,IAAK,iBACG,OACN,KAAM,SACN,OAAQ,CACP,SACA,MAAO,UACP,KAAMA,EAAK,KACX,KAAMA,EAAK,KACX,SAAU,EACV,QAASA,EAAK,QACd,IAAKA,EAAK,GACX,EAEH,CAEO,OAAE,KAAM,OAAQ,OAAQ,CAAE,MAAO,QAAS,UAClD,CAyBO,MAAMW,GAAqB,CACjCX,EAA4C,GAC5CY,IACe,CAGf,MAAMvB,EAAauB,EAAgBA,EAAc,WAAa,GAE1D,SAAM,QAAQZ,CAAI,EACjB,OAAAA,EAAK,OAASX,EAAW,QAC5B,QAAQ,KAAK,+CAA+C,EAEtDW,EAGR,MAAMa,EAA2B,GAC3BC,EAAgB,OAAO,KAAKd,CAAI,EAE3B,OAAAX,EAAA,QAAQ,CAAC0B,EAAOC,IAAU,CACpC,GAAIhB,EAAK,eAAee,EAAM,cAAc,EAC3CF,EAAcG,CAAK,EAAIhB,EAAKe,EAAM,cAAc,UACtCA,EAAM,sBACFF,EAAAG,CAAK,EAAID,EAAM,sBAE7B,OAAM,IAAI,MACT,6CAA6CA,EAAM,cAAc,GAEnE,CACA,EAEaD,EAAA,QAASG,GAAQ,CAC1B,IAAC5B,EAAW,KAAM0B,GAAUA,EAAM,iBAAmBE,CAAG,EAC3D,MAAM,IAAI,MACT,eAAeA,CAAG,yEAEpB,CACA,EAEaJ,EAAA,QAAQ,CAACK,EAAOrB,IAAQ,CACrC,GAAIqB,IAAU,QAAa,CAAC7B,EAAWQ,CAAG,EAAE,sBAC3C,MAAM,IAAI,MACT,6CAA6CR,EAAWQ,CAAG,EAAE,cAAc,GAE7E,CACA,EAEMgB,CACR,EC9aA,eAAsBM,IAAqC,CAC1D,GAAI,KAAK,SAAU,OAAO,KAAK,SAEzB,MAAE,SAAArD,CAAS,EAAI,KAAK,QACpB,CAAE,OAAAhB,CAAW,OAEbF,EAGF,CAAE,eAAgB,oBAMtB,GAJIkB,IACKlB,EAAA,cAAgB,UAAUkB,CAAQ,IAGvC,EAAChB,EAID,IACC,IAAAI,EACA+B,EACJ,GAAI,OAAO,OAAW,KAAe,OAAO,gBAC3CA,EAAW,OAAO,oBACZ,CACN,GAAI7E,GAAQ0C,GAAQ,SAAW,QAAS,MAAM,EAAI,EACtCI,EAAA,MAAM,KAAK,MAAMnC,GAAmB,CAC9C,OAAQ,OACR,KAAM,KAAK,UAAU,CACpB,UAAW,GACX,OAAQ,KAAK,UAAU+B,CAAM,EAC7B,EACD,QAAAF,EACA,YAAa,UACb,MACK,CACN,MAAMwE,EAAMnE,GAAUH,EAAO,KAAMlC,EAAY,EACpCsC,EAAA,MAAM,KAAK,MAAMkE,EAAK,CAChC,QAAAxE,EACA,YAAa,UACb,CACF,CAEI,IAACM,EAAS,GACP,UAAI,MAAMjC,CAAqB,EAE3BgE,EAAA,MAAM/B,EAAS,MAC3B,CACA,MAAI,QAAS+B,IACZA,EAAWA,EAAS,KAIpBA,EAAS,gBAAgB,UAAU,GACnC,CAACA,EAAS,kBAAkB,CAAG,IAE/BA,EAAS,kBAAkB,CAAC,EAAIA,EAAS,gBAAgB,UAAU,GAG7DD,GAAmBC,EAAUnC,EAAQ,KAAK,OAAO,QAChDa,EAAG,CACX,GAA8BA,EAAY,OAC3C,CACD,CClEsB,eAAA0D,GAErBC,EACAC,EACAC,EAC0B,CAC1B,MAAM5E,EAEF,GACA,MAAM,SAAS,WAClBA,EAAQ,cAAgB,UAAU,KAAK,QAAQ,QAAQ,IAGxD,MAAM6E,EAAY,IACZC,EAAkB,GACpB,IAAAxE,EAEJ,QAASE,EAAI,EAAGA,EAAImE,EAAM,OAAQnE,GAAKqE,EAAW,CACjD,MAAME,EAAQJ,EAAM,MAAMnE,EAAGA,EAAIqE,CAAS,EACpC1D,EAAW,IAAI,SACf4D,EAAA,QAASC,GAAS,CACd7D,EAAA,OAAO,QAAS6D,CAAI,EAC7B,EACG,IACH,MAAMC,EAAaL,EAChB,GAAGF,CAAQ,IAAI7G,EAAU,cAAc+G,CAAS,GAChD,GAAGF,CAAQ,IAAI7G,EAAU,GAEjByC,EAAA,MAAM,KAAK,MAAM2E,EAAY,CACvC,OAAQ,OACR,KAAM9D,EACN,QAAAnB,EACA,YAAa,UACb,QACOe,EAAG,CACX,MAAM,IAAI,MAAM1C,EAAyB0C,EAAY,OAAO,CAC7D,CACI,IAACT,EAAS,GAAI,CACX,MAAA4E,EAAa,MAAM5E,EAAS,OAClC,MAAO,CAAE,MAAO,QAAQA,EAAS,MAAM,KAAK4E,CAAU,GACvD,CACM,MAAAC,EAAkC,MAAM7E,EAAS,OACnD6E,GACaL,EAAA,KAAK,GAAGK,CAAM,CAEhC,CACO,OAAE,MAAOL,EACjB,CChDA,eAAsBM,GAErBC,EACAX,EACAE,EACAU,EACsC,CAClC,IAAAX,GAAS,MAAM,QAAQU,CAAS,EAAIA,EAAY,CAACA,CAAS,GAAG,IAC/DA,GAAcA,EAAU,MAG1B,MAAME,EAAkBZ,EAAM,OAC5Ba,GAAMA,EAAE,MAAQF,GAAiB,MAEnC,GAAIC,EAAgB,OACnB,MAAM,IAAI,MACT,iDAAiDD,CAAa,WAAWC,EACvE,IAAKC,GAAMA,EAAE,IAAI,EACjB,KAAK,IAAI,CAAC,IAId,OAAO,MAAM,QAAQ,IACpB,MAAM,KAAK,aAAad,EAAUC,EAAOC,CAAS,EAAE,KACnD,MAAOtE,GAAmD,CACzD,GAAIA,EAAS,MACN,UAAI,MAAMA,EAAS,KAAK,EAE9B,OAAIA,EAAS,MACLA,EAAS,MAAM,IAAI,CAACkF,EAAGhF,IAChB,IAAIiF,GAAS,CACzB,GAAGJ,EAAU7E,CAAC,EACd,KAAMgF,EACN,IAAKd,EAAW,SAAWc,CAAA,CAC3B,CAED,EAGK,EAET,CACD,EAEF,CAEsB,eAAAE,GACrBf,EACAgB,EACsB,CACtB,OAAOhB,EAAM,IACXa,GACA,IAAIC,GAAS,CACZ,KAAMD,EAAE,KACR,UAAWA,EAAE,KACb,KAAMA,EACN,KAAMA,EAAE,KACR,UAAWA,EAAE,KACb,UAAAG,CAAA,CACA,EAEJ,CAEO,MAAMF,EAAS,CACrB,KACA,IACA,UACA,KACA,KACA,UACA,UACA,SACS,KAAO,CAAE,MAAO,mBAEzB,YAAY,CACX,KAAAxF,EACA,IAAAuE,EACA,UAAAoB,EACA,KAAAC,EACA,KAAAC,EACA,UAAAH,EACA,UAAAI,EACA,SAAAC,CAAA,EAUE,CACF,KAAK,KAAO/F,EACZ,KAAK,IAAMuE,EACX,KAAK,UAAYoB,EACjB,KAAK,KAAOC,EACP,UAAOrB,EAAM,OAAYsB,EAC9B,KAAK,UAAYH,EACjB,KAAK,UAAYI,EACjB,KAAK,SAAWC,CACjB,CACD,CCnDO,MAAMC,EAAQ,CACpB,KACA,QACA,KAKA,SAEA,YACCC,EACAC,EACC,CACD,KAAK,KAAO,UACZ,KAAK,QAAUD,EACf,KAAK,KAAOC,CACb,CACD,CC1DC,OAAO,QAAY,KAAe,QAAQ,UAAY,QAAQ,SAAS,KAExD,SAAAC,GACfC,EACAC,EACAC,EACO,CACA,KAAAA,EAAM,OAAS,GAAG,CAClBlC,QAAMkC,EAAM,QAClB,GAAI,OAAOlC,GAAQ,UAAY,OAAOA,GAAQ,SAC7CgC,EAASA,EAAOhC,CAAG,MAEb,WAAI,MAAM,kBAAkB,CAEpC,CAEM,MAAAA,EAAMkC,EAAM,QAClB,GAAI,OAAOlC,GAAQ,UAAY,OAAOA,GAAQ,SAC7CgC,EAAOhC,CAAG,EAAIiC,MAER,WAAI,MAAM,kBAAkB,CAEpC,CAEsB,eAAAE,GACrBpD,EACAQ,EAA2B,OAC3B3D,EAAiB,CACjB,EAAAwG,EAAO,GACPzC,EAA+D,OAC1C,CACjB,SAAM,QAAQZ,CAAI,EAAG,CACxB,IAAIsD,EAAuB,GAE3B,aAAM,QAAQ,IACbtD,EAAK,IAAI,MAAOuD,EAAGvC,IAAU,CACxB,IAAAwC,EAAW3G,EAAK,QACX2G,EAAA,KAAK,OAAOxC,CAAK,CAAC,EAE3B,MAAMyC,EAAa,MAAML,GACxBpD,EAAKgB,CAAK,EACVqC,EACGzC,GAAe,WAAWI,CAAK,GAAG,WAAa,OAC/CR,EACHgD,EACA,GACA5C,CAAA,EAGW0C,IAAU,OAAOG,CAAU,EACvC,GAGKH,CAAA,SAEN,WAAW,QAAUtD,aAAgB,WAAW,QACjDA,aAAgB,KAET,OACN,CACC,KAAAnD,EACA,KAAM,IAAI,KAAK,CAACmD,CAAI,CAAC,EACrB,KAAAQ,CACD,GAES,UAAOR,GAAS,UAAYA,IAAS,KAAM,CACrD,IAAIsD,EAAuB,GAC3B,UAAWrC,KAAO,OAAO,KAAKjB,CAAI,EAA4B,CAC7D,MAAMwD,EAAW,CAAC,GAAG3G,EAAMoE,CAAG,EACxBC,EAAQlB,EAAKiB,CAAG,EAEtBqC,EAAYA,EAAU,OACrB,MAAMF,GACLlC,EACA,OACAsC,EACA,GACA5C,CACD,EAEF,CAEO,OAAA0C,CACR,EAEA,MAAO,EACR,CAEgB,SAAAI,GAAWjH,EAAYK,EAAyB,CAC3D,IAAA6G,EAAW7G,GAAQ,cAAc,KAAMK,GAAQA,EAAI,IAAMV,CAAE,GAAG,MAClE,OAAIkH,GAAY,KACR,CAACA,EAEF,CAAC7G,EAAO,YAChB,CAIgB,SAAA8G,GACfC,EACAC,EACe,CACf,OAAO,IAAI,QAAQ,CAAC9F,EAAK+F,IAAS,CAC3B,MAAAC,EAAU,IAAI,eACpBA,EAAQ,MAAM,UAAa,CAAC,CAAE,KAAAhE,KAAW,CACxCgE,EAAQ,MAAM,QACdhG,EAAIgC,CAAI,GAET,OAAO,OAAO,YAAY6D,EAASC,EAAQ,CAACE,EAAQ,KAAK,CAAC,EAC1D,CACF,CAgDO,SAASC,GACfC,EACAC,EACA1E,EACAe,EACA4D,EAAkB,GACN,CACR,GAAA5D,IAAS,SAAW,CAAC4D,EAClB,UAAI,MAAM,wDAAwD,EAGrE,GAAA5D,IAAS,UAAY4D,EACjB,OAAAF,EAGR,IAAIG,EAA6B,GAC7BC,EAAgB,EACpB,MAAMC,EAAO/D,IAAS,QAAU2D,EAAW,OAASA,EAAW,QAC/D,QAAS/G,EAAI,EAAGA,EAAImH,EAAK,OAAQnH,IAAK,CAC/B,MAAAoH,EAAWD,EAAKnH,CAAC,EAGnB,GAFcqC,EAAW,KAAME,GAAMA,EAAE,KAAO6E,CAAQ,GAE3C,OAAS,QAAS,CAEhC,GAAIJ,EACC,GAAAF,EAAiB,SAAWK,EAAK,OAAQ,CACtC,MAAArD,EAAQgD,EAAiBI,CAAa,EAC5CD,EAAgB,KAAKnD,CAAK,EAC1BoD,GAAA,MAEAD,EAAgB,KAAK,IAAI,MAEpB,CAGNC,IACA,QACD,CAEA,aACM,CACA,MAAApD,EAAQgD,EAAiBI,CAAa,EAC5CD,EAAgB,KAAKnD,CAAK,EAC1BoD,GACD,CACD,CAEO,OAAAD,CACR,CC7MsB,eAAAI,GAErB9H,EACAqD,EACAf,EACqB,CACrB,MAAMyF,EAAO,KAEP,MAAAC,GAA4BD,EAAM1E,CAAI,EAE5C,MAAM4E,EAAW,MAAMxB,GACtBpD,EACA,OACA,CAAC,EACD,GACAf,CAAA,EAqBD,OAlBgB,MAAM,QAAQ,IAC7B2F,EAAS,IAAI,MAAO,CAAE,KAAA/H,EAAM,KAAA6F,EAAM,KAAAlC,KAAW,CAC5C,GAAI,CAACkC,EAAa,OAAE,KAAA7F,EAAM,KAAA2D,GAE1B,MAAMtD,EAAW,MAAMwH,EAAK,aAAa/H,EAAU,CAAC+F,CAAI,CAAC,EACnDmC,EAAW3H,EAAS,OAASA,EAAS,MAAM,CAAC,EAC5C,OACN,KAAAL,EACA,SAAAgI,EACA,KAAArE,EACA,KACC,OAAO,KAAS,KAAekC,aAAgB,KAC5CA,GAAM,KACN,OACL,CACA,IAGM,QAAQ,CAAC,CAAE,KAAA7F,EAAM,SAAAgI,EAAU,KAAArE,EAAM,KAAAsE,KAAW,CACnD,GAAItE,IAAS,UACEwC,GAAAhD,EAAM6E,EAAUhI,CAAI,UACxBgI,EAAU,CACd,MAAAjD,EAAO,IAAIS,GAAS,CAAE,KAAMwC,EAAU,UAAWC,EAAM,EAC/C9B,GAAAhD,EAAM4B,EAAM/E,CAAI,CAC/B,EACA,EAEMmD,CACR,CAEsB,eAAA2E,GACrBI,EACA/E,EACgB,CAGhB,GAAI,EAFS+E,EAAO,QAAQ,MAAQA,EAAO,QAAQ,UAG5C,UAAI,MAAMpJ,EAAkB,EAG7B,MAAAqJ,GAA6BD,EAAQ/E,CAAI,CAChD,CAEA,eAAegF,GACdD,EACA/E,EACAnD,EAAiB,GACD,CAChB,UAAWoE,KAAOjB,EACbA,EAAKiB,CAAG,YAAa4B,GAClB,MAAAoC,GAAuBF,EAAQ/E,EAAMiB,CAAG,EACpC,OAAOjB,EAAKiB,CAAG,GAAM,UAAYjB,EAAKiB,CAAG,IAAM,MACnD,MAAA+D,GAA6BD,EAAQ/E,EAAKiB,CAAG,EAAG,CAAC,GAAGpE,EAAMoE,CAAG,CAAC,CAGvE,CAEA,eAAegE,GACdF,EACA/E,EACAiB,EACgB,CACZ,IAAAiE,EAAWlF,EAAKiB,CAAG,EACvB,MAAMoC,EAAO0B,EAAO,QAAQ,MAAQA,EAAO,QAAQ,SAEnD,GAAI,CAAC1B,EACE,UAAI,MAAM1H,EAAkB,EAG/B,IACC,IAAAwJ,EACAC,EAGJ,GACC,OAAO,QAAY,KACnB,QAAQ,UACR,QAAQ,SAAS,KAChB,CACK,MAAAC,EAAK,MAAMC,GAAA,WAAO,uCAAa,OAAAC,KAAA,uBAGrCH,GAFa,aAAM,OAAO,uCAAM,OAAAG,KAAA,wBAEhB,QAAQ,QAAQ,MAAOL,EAAS,KAAK,IAAI,EAC5CC,EAAA,MAAME,EAAG,SAASD,CAAQ,MAEjC,WAAI,MAAM1J,EAAmB,EAG9B,MAAAkG,EAAO,IAAI,KAAK,CAACuD,CAAU,EAAG,CAAE,KAAM,2BAA4B,EAElEjI,EAAW,MAAM6H,EAAO,aAAa1B,EAAM,CAACzB,CAAI,CAAC,EAEjDiD,EAAW3H,EAAS,OAASA,EAAS,MAAM,CAAC,EAEnD,GAAI2H,EAAU,CACP,MAAAW,EAAW,IAAInD,GAAS,CAC7B,KAAMwC,EACN,UAAWK,EAAS,KAAK,MAAQ,GACjC,EAGDlF,EAAKiB,CAAG,EAAIuE,CACb,QACQC,EAAO,CACP,cAAM7J,GAA2B6J,CAAK,CAC/C,CACD,CCvIsB,eAAAC,GAErBtE,EACAuE,EACAC,EACkC,CAC5B,MAAAhJ,EAGF,CAAE,eAAgB,oBAClB,KAAK,QAAQ,WAChBA,EAAQ,cAAgB,UAAU,KAAK,QAAQ,QAAQ,IAEpD,IACH,IAAIM,EAAW,MAAM,KAAK,MAAMkE,EAAK,CACpC,OAAQ,OACR,KAAM,KAAK,UAAUuE,CAAI,EACzB,QAAS,CAAE,GAAG/I,EAAS,GAAGgJ,CAAmB,EAC7C,YAAa,UACb,OACU,CACX,MAAO,CAAC,CAAE,MAAO3K,GAAyB,GAAG,CAC9C,CACI,IAAA8G,EACA8D,EACA,IACM9D,EAAA,MAAM7E,EAAS,OACxB2I,EAAS3I,EAAS,aACVS,EAAG,CACXoE,EAAS,CAAE,MAAO,oCAAoCpE,CAAC,EAAG,EACjDkI,EAAA,GACV,CACO,OAAC9D,EAAQ8D,CAAM,CACvB,CClCA,eAAsBC,GAErBnJ,EACAqD,EAA4C,GACnB,CACzB,IAAI+F,EAAgB,GAChBC,EAAkB,GAGlB,IAAC,KAAK,OACH,UAAI,MAAM,8BAA8B,EAG3C,UAAOrJ,GAAa,SACV,KAAK,OAAO,aAAa,KAAMQ,GAAQA,EAAI,IAAMR,CAAQ,MAChE,CACN,MAAMsJ,EAAmBtJ,EAAS,QAAQ,MAAO,EAAE,EACtC,KAAK,OAAO,aAAa,KACpCQ,GAAQA,EAAI,IAAM,KAAK,QAAQ8I,CAAgB,EAElD,CAEA,OAAO,IAAI,QAAQ,MAAOC,EAASC,IAAW,CAC7C,MAAMC,EAAM,KAAK,OAAOzJ,EAAUqD,EAAM,KAAM,KAAM,EAAI,EACpD,IAAAqG,EAEJ,gBAAiBxC,KAAWuC,EACvBvC,EAAQ,OAAS,SAChBmC,GACHE,EAAQG,CAAuB,EAEhBN,EAAA,GACPM,EAAAxC,GAGNA,EAAQ,OAAS,WAChBA,EAAQ,QAAU,SAASsC,EAAOtC,CAAO,EACzCA,EAAQ,QAAU,aACHmC,EAAA,GAEdD,GACHG,EAAQG,CAAuB,GAInC,CACA,CACF,CC1CsB,eAAAC,GACrB7J,EACA+D,EACA+F,EACgB,CAChB,IAAI5J,EACH6D,IAAS,YACN,kDAAkD/D,CAAE,GACpD,qCAAqCA,CAAE,GACvCS,EACAsJ,EACA,IAGH,GAFWtJ,EAAA,MAAM,MAAMP,CAAQ,EAC/B6J,EAAUtJ,EAAS,OACfsJ,IAAY,IACf,MAAM,IAAI,MAEAtJ,EAAA,MAAMA,EAAS,YACf,CACKqJ,EAAA,CACf,OAAQ,QACR,YAAa,QACb,QAASpL,GACT,OAAQ,YACR,EACD,MACD,CAEI,IAAC+B,GAAYsJ,IAAY,IAAK,OAC5B,MACL,QAAS,CAAE,MAAAC,CAAM,EACjB,GAAIC,CACD,EAAAxJ,EAEJ,OAAQuJ,EAAO,CACd,IAAK,UACL,IAAK,WACYF,EAAA,CACf,OAAQ,WACR,YAAa,UACb,QAAS,mCACT,OAAQE,CAAA,CACR,EAED,WAAW,IAAM,CACGH,GAAA7J,EAAI+D,EAAM+F,CAAe,GAC1C,GAAI,EACP,MACD,IAAK,SACYA,EAAA,CACf,OAAQ,SACR,YAAa,QACb,QACC,gHACD,OAAQE,EACR,oBAAqB,MAAME,GAAoBD,CAAU,EACzD,EACD,MACD,IAAK,UACL,IAAK,mBACYH,EAAA,CACf,OAAQ,UACR,YAAa,WACb,QAAS,oBACT,OAAQE,CAAA,CACR,EACD,MACD,IAAK,WACYF,EAAA,CACf,OAAQ,WACR,YAAa,UACb,QAAS,uBACT,OAAQE,CAAA,CACR,EAED,WAAW,IAAM,CACGH,GAAA7J,EAAI+D,EAAM+F,CAAe,GAC1C,GAAI,EACP,MACD,IAAK,eACYA,EAAA,CACf,OAAQ,WACR,YAAa,UACb,QAAS,uBACT,OAAQE,CAAA,CACR,EAED,WAAW,IAAM,CACGH,GAAA7J,EAAI+D,EAAM+F,CAAe,GAC1C,GAAI,EACP,MACD,QACiBA,EAAA,CACf,OAAQ,cACR,YAAa,QACb,QAAS,uCACT,OAAQE,EACR,oBAAqB,MAAME,GAAoBD,CAAU,EACzD,EACD,KACF,CACD,CAEa,MAAAE,GAAuB,MACnCC,EACAN,IACmB,CACnB,IAAIO,EAAU,EACd,MAAMC,EAAc,GACdC,EAAiB,IAEhB,WAAI,QAASd,GAAY,CAC/BI,GACCO,EACArI,GAAc,KAAKqI,CAAQ,EAAI,aAAe,YAC7ChB,GAAW,CACXU,EAAgBV,CAAM,EAElBA,EAAO,SAAW,WAGrBA,EAAO,SAAW,SAClBA,EAAO,SAAW,UAClBA,EAAO,SAAW,cAJVK,KAQRL,EAAO,SAAW,YAClBA,EAAO,SAAW,cAEdiB,EAAUC,GACbD,IACA,WAAW,IAAM,CAChBF,GAAqBC,EAAUN,CAAe,EAAE,KAAKL,CAAO,GAC1Dc,CAAc,GAETd,IAGX,EACD,CACA,CACF,EAEMe,GACL,+DACD,eAAsBN,GAAoBE,EAAoC,CACzE,IACH,MAAMtG,EAAI,MAAM,MACf,qCAAqCsG,CAAQ,eAC7C,CACC,OAAQ,MACT,GAGKpB,EAAQlF,EAAE,QAAQ,IAAI,iBAAiB,EAE7C,MAAI,GAACA,EAAE,IAAOkF,GAASwB,GAAuB,KAAKxB,CAAK,QAE7C,CACJ,QACR,CACD,CAEsB,eAAAyB,GACrBL,EACA/I,EAC2C,CAC3C,MAAMlB,EAAsC,GACxCkB,IACKlB,EAAA,cAAgB,UAAUkB,CAAQ,IAGvC,IACH,MAAME,EAAM,MAAM,MACjB,qCAAqC6I,CAAQ,IAAIhM,EAAW,GAC5D,CAAE,QAAA+B,CAAQ,GAGX,GAAIoB,EAAI,SAAW,IACZ,UAAI,MAAM,uCAAuC,EAExD,KAAM,CAAE,SAAAmJ,CAAa,QAAMnJ,EAAI,KAAK,EAEpC,OAAOmJ,EAAS,cACRxJ,EAAQ,CACV,UAAI,MAAMA,EAAE,OAAO,CAC1B,CACD,CAEsB,eAAAyJ,GACrBP,EACAQ,EACAvJ,EACe,CACf,MAAMlB,EAAsC,GACxCkB,IACKlB,EAAA,cAAgB,UAAUkB,CAAQ,IAG3C,MAAM6H,EAEF,CACH,QAAS0B,CAAA,EAGN,IACH,MAAMrJ,EAAM,MAAM,MACjB,qCAAqC6I,CAAQ,IAAI/L,EAAa,GAC9D,CACC,OAAQ,OACR,QAAS,CAAE,eAAgB,mBAAoB,GAAG8B,CAAQ,EAC1D,KAAM,KAAK,UAAU+I,CAAI,CAC1B,GAGG,GAAA3H,EAAI,SAAW,IAClB,MAAM,IAAI,MACT,gJAKK,OADU,MAAMA,EAAI,aAEnBL,EAAQ,CACV,UAAI,MAAMA,EAAE,OAAO,CAC1B,CACD,CAEO,MAAM2J,GAAiB,CAC7B,YACA,cACA,SACA,WACA,YACA,aACA,aACA,eACA,eACA,aACA,YACA,OACA,QACD,EC7OsB,eAAAC,GACrB7I,EACA8I,EACkB,CAClB,KAAM,CAAE,SAAA1J,EAAU,QAAS2J,EAAU,SAAAN,EAAU,QAAAE,EAAS,KAAAzJ,CAAS,EAAA4J,EAEjE,GAAIL,GAAY,CAACG,GAAe,SAASH,CAAQ,EAChD,MAAM,IAAI,MACT,oDAAoDG,GAClD,IAAKI,GAAM,IAAIA,CAAC,GAAG,EACnB,KAAK,GAAG,CAAC,KAIb,KAAM,CAAE,cAAApK,EAAe,KAAAC,CAAK,EAAI,MAAMC,GACrCkB,EACAZ,CAAA,EAGD,IAAI1B,EAA2B,KAE/B,GAAIwB,EAAM,CACT,MAAMH,EAAgB,MAAMC,GAC3BJ,EACAC,EACAK,EACA,OAGGH,IAAerB,EAAUgC,GAAsBX,CAAa,EACjE,CAEA,MAAMb,EAAU,CACf,cAAe,UAAUkB,CAAQ,GACjC,eAAgB,mBAChB,GAAI1B,EAAU,CAAE,OAAQA,EAAQ,KAAK,IAAI,CAAE,EAAI,CAAC,GAG3CuL,GACL,MACC,MAAM,MAAM,uCAAwC,CACnD,QAAA/K,CAAA,CACA,GACA,KACD,QAEI8J,EAAahI,EAAc,MAAM,GAAG,EAAE,CAAC,EACvCiH,EAIF,CACH,WAAY,GAAGgC,CAAI,IAAIjB,CAAU,IAG9Be,IACH9B,EAAK,QAAU,IAGZ,IAAAiC,EAEA,IACET,IACgBS,EAAA,MAAMV,GAAmBxI,EAAeZ,CAAQ,SAE7DH,EAAG,CACL,YAAMtC,GAA4BsC,EAAY,OAAO,CAC5D,CAEM,MAAAkK,EAAqBV,GAAYS,GAAqB,YAE5DjC,EAAK,SAAWkC,EAEZ,IACH,MAAM3K,EAAW,MAAM,MACtB,qCAAqCwB,CAAa,aAClD,CACC,OAAQ,OACR,QAAA9B,EACA,KAAM,KAAK,UAAU+I,CAAI,CAC1B,GAGG,GAAAzI,EAAS,SAAW,IACnB,IAEI,OADQ,MAAM4K,GAAO,QAAQ,GAAGH,CAAI,IAAIjB,CAAU,GAAIc,CAAO,QAE5D/B,EAAO,CACP,oBAAM,qCAAsCA,CAAK,EACnDA,CACP,SACUvI,EAAS,SAAW,IACxB,UAAI,MAAMA,EAAS,UAAU,EAG9B,MAAA6K,EAAmB,MAAM7K,EAAS,OAElC,aAAAkK,GAAkB,GAAGO,CAAI,IAAIjB,CAAU,GAAIW,GAAW,IAAKvJ,CAAQ,EAElE,MAAMgK,GAAO,QACnBE,GAAoBD,EAAiB,GAAG,EACxCP,CAAA,QAEO7J,EAAQ,CACV,UAAI,MAAMA,CAAC,CAClB,CACD,CAEA,SAASqK,GAAoB5G,EAAkB,CAC9C,MAAM6G,EAAQ,mDACRC,EAAQ9G,EAAI,MAAM6G,CAAK,EAC7B,GAAIC,EACH,OAAOA,EAAM,CAAC,CAEhB,CChHO,MAAMC,WAAuB,eAAgB,CAChDC,GAAe,GAEf,YAAYZ,EAAU,CAAE,QAAS,EAAK,EAAI,CACtC,MAAM,CACF,UAAW,CAACa,EAAOC,IAAe,CAE9B,IADAD,EAAQ,KAAKD,GAAeC,IACf,CACT,MAAME,EAAUF,EAAM,QAAQ;AAAA,CAAI,EAC5BG,EAAUhB,EAAQ,QAAUa,EAAM,QAAQ,IAAI,EAAI,GACxD,GAAIG,IAAY,IAAMA,IAAaH,EAAM,OAAS,IAC7CE,IAAY,IAAOA,EAAU,EAAKC,GAAU,CAC7CF,EAAW,QAAQD,EAAM,MAAM,EAAGG,CAAO,CAAC,EAC1CH,EAAQA,EAAM,MAAMG,EAAU,CAAC,EAC/B,QACH,CACD,GAAID,IAAY,GACZ,MACJ,MAAME,EAAWJ,EAAME,EAAU,CAAC,IAAM,KAAOA,EAAU,EAAIA,EAC7DD,EAAW,QAAQD,EAAM,MAAM,EAAGI,CAAQ,CAAC,EAC3CJ,EAAQA,EAAM,MAAME,EAAU,CAAC,CAClC,CACD,KAAKH,GAAeC,CACvB,EACD,MAAQC,GAAe,CACnB,GAAI,KAAKF,KAAiB,GACtB,OACJ,MAAMM,EAAclB,EAAQ,SAAW,KAAKY,GAAa,SAAS,IAAI,EAChE,KAAKA,GAAa,MAAM,EAAG,EAAE,EAC7B,KAAKA,GACXE,EAAW,QAAQI,CAAW,CACjC,CACb,CAAS,CACJ,CACL,CCjDO,SAASC,GAAOjJ,EAAO,CAC1B,IAAIkJ,EAAU,IAAI,kBACdC,EAAQ,IAAIV,GAAe,CAAE,QAAS,EAAM,GAChD,OAAOzI,EAAM,YAAYkJ,CAAO,EAAE,YAAYC,CAAK,CACvD,CACO,SAASA,GAAMnJ,EAAO,CAEzB,IAAIwI,EADM,SACM,KAAKxI,CAAK,EAEtBG,EAAMqI,GAASA,EAAM,MACzB,GAAIrI,EACA,MAAO,CACHH,EAAM,UAAU,EAAGG,CAAG,EACtBH,EAAM,UAAUG,EAAMqI,EAAM,CAAC,EAAE,MAAM,CACjD,CAEA,CACO,SAASY,GAASlM,EAASqE,EAAKC,EAAO,CAChCtE,EAAQ,IAAIqE,CAAG,GAErBrE,EAAQ,IAAIqE,EAAKC,CAAK,CAC9B,CCWO,eAAgB6H,GAAO/K,EAAKgL,EAAQ,CAEvC,GAAI,CAAChL,EAAI,KACL,OACJ,IAAIiL,EAAOC,GAAalL,EAAI,IAAI,EAC5BmL,EAAMC,EAASH,EAAK,UAAS,EAC7BI,EACJ,OAAS,CACL,GAAIL,GAAUA,EAAO,QACjB,OAAOI,EAAO,SAGlB,GADAD,EAAO,MAAMC,EAAO,OAChBD,EAAK,KACL,OACJ,GAAI,CAACA,EAAK,MAAO,CACTE,IACA,MAAMA,GACVA,EAAQ,OACR,QACH,CACD,GAAI,CAACC,EAAOpI,CAAK,EAAIqI,GAAYJ,EAAK,KAAK,GAAK,GAC3CG,IAEDA,IAAU,QACVD,IAAU,GACVA,EAAMC,CAAK,EAAID,EAAMC,CAAK,EAAKD,EAAMC,CAAK,EAAI;AAAA,EAAOpI,EAASA,GAEzDoI,IAAU,SACfD,IAAU,GACVA,EAAMC,CAAK,EAAIpI,GAEVoI,IAAU,MACfD,IAAU,GACVA,EAAMC,CAAK,EAAI,CAACpI,GAASA,GAEpBoI,IAAU,UACfD,IAAU,GACVA,EAAMC,CAAK,EAAI,CAACpI,GAAS,QAEhC,CACL,CA0BO,eAAeyH,GAAOjJ,EAAO8J,EAAM,CACtC,IAAIC,EAAM,IAAI,QAAQ/J,EAAO8J,CAAI,EACjCE,GAAeD,EAAI,QAAS,SAAU,mBAAmB,EACzDC,GAAeD,EAAI,QAAS,eAAgB,kBAAkB,EAC9D,IAAIlJ,EAAI,MAAM,MAAMkJ,CAAG,EACvB,GAAI,CAAClJ,EAAE,GACH,MAAMA,EACV,OAAOwI,GAAOxI,EAAGkJ,EAAI,MAAM,CAC/B,CCvGA,eAAsBE,IAAyC,CAC1D,IACH,gBAAAC,EACA,gBAAAC,EACA,wBAAAC,EACA,cAAAC,EACA,OAAAjN,EACA,IAAAkN,CACG,OAEJ,MAAMC,EAAO,KAEb,GAAI,CAACnN,EACE,UAAI,MAAM,8BAA8B,EAG/CiN,EAAc,KAAO,GAErB,IAAIpB,EAA6B,KAC7BuB,EAAS,IAAI,gBAAgB,CAChC,aAAc,KAAK,aACnB,EAAE,SAAS,EAER9I,EAAM,IAAI,IAAI,GAAGtE,EAAO,IAAI,eAAeoN,CAAM,EAAE,EAQvD,GANIF,GACC5I,EAAA,aAAa,IAAI,SAAU4I,CAAG,EAGnCrB,EAAS,KAAK,OAAOvH,CAAG,EAEpB,CAACuH,EAAQ,CACZ,QAAQ,KAAK,mCAAqCvH,EAAI,SAAU,GAChE,MACD,CAEAuH,EAAO,UAAY,eAAgBU,EAAqB,CACvD,IAAIc,EAAQ,KAAK,MAAMd,EAAM,IAAI,EAC7B,GAAAc,EAAM,MAAQ,eAAgB,CACpBC,GAAAL,EAAeE,EAAK,gBAAgB,EACjD,MACD,CACA,MAAMI,EAAWF,EAAM,SACvB,GAAI,CAACE,EACJ,MAAM,QAAQ,IACb,OAAO,KAAKT,CAAe,EAAE,IAAKS,GACjCT,EAAgBS,CAAQ,EAAEF,CAAK,CAChC,WAESP,EAAgBS,CAAQ,GAAKvN,EAAQ,CAE9CqN,EAAM,MAAQ,qBACd,CAAC,MAAO,SAAU,SAAU,WAAY,QAAQ,EAAE,SACjDrN,EAAO,WAGR+M,EAAgB,OAAOQ,CAAQ,EAE5B,IAAAlQ,EAA0ByP,EAAgBS,CAAQ,EAElD,OAAO,OAAW,KAAe,OAAO,SAAa,IAE7C,WAAAlQ,EAAI,EAAGgQ,CAAK,EAEvBhQ,EAAGgQ,CAAK,CACT,MAEKL,EAAwBO,CAAQ,IACZP,EAAAO,CAAQ,EAAI,IAEbP,EAAAO,CAAQ,EAAE,KAAKF,CAAK,CAC7C,EAEDxB,EAAO,QAAU,gBAAkB,CAClC,MAAM,QAAQ,IACb,OAAO,KAAKiB,CAAe,EAAE,IAAKS,GACjCT,EAAgBS,CAAQ,EAAE,CACzB,IAAK,mBACL,QAASpP,CAAA,CACT,CACF,EACD,CAEF,CAEgB,SAAAmP,GACfL,EACAO,EACO,CACHP,IACHA,EAAc,KAAO,GACrBO,GAAkB,MAAM,EAE1B,CAEgB,SAAAC,GACfC,EACAH,EACArK,EACO,CACmB,CAACwK,EAAqBH,CAAQ,GAElCG,EAAAH,CAAQ,EAAI,GACjCrK,EAAK,KAAK,QAAQ,CAACkB,EAAY,IAAc,CACvBsJ,EAAAH,CAAQ,EAAE,CAAC,EAAInJ,CAAA,CACpC,GAEDlB,EAAK,KAAK,QAAQ,CAACkB,EAAY,IAAc,CAC5C,IAAIuJ,EAAWC,GAAWF,EAAqBH,CAAQ,EAAE,CAAC,EAAGnJ,CAAK,EAC7CsJ,EAAAH,CAAQ,EAAE,CAAC,EAAII,EAC/BzK,EAAA,KAAK,CAAC,EAAIyK,CAAA,CACf,CAEH,CAEgB,SAAAC,GACfC,EACAC,EACM,CACN,OAAAA,EAAK,QAAQ,CAAC,CAACC,EAAQhO,EAAMqE,CAAK,IAAM,CACvCyJ,EAAMG,GAAWH,EAAK9N,EAAMgO,EAAQ3J,CAAK,EACzC,EAEMyJ,CACR,CAEA,SAASG,GACRC,EACAlO,EACAgO,EACA3J,EACM,CACF,GAAArE,EAAK,SAAW,EAAG,CACtB,GAAIgO,IAAW,UACP,OAAA3J,EACR,GAAW2J,IAAW,SACrB,OAAOE,EAAS7J,EAEjB,MAAM,IAAI,MAAM,uBAAuB2J,CAAM,EAAE,CAChD,CAEA,IAAIG,EAAUD,EACd,QAAS3N,EAAI,EAAGA,EAAIP,EAAK,OAAS,EAAGO,IAC1B4N,IAAQnO,EAAKO,CAAC,CAAC,EAG1B,MAAM6N,EAAYpO,EAAKA,EAAK,OAAS,CAAC,EACtC,OAAQgO,EAAQ,CACf,IAAK,UACJG,EAAQC,CAAS,EAAI/J,EACrB,MACD,IAAK,SACJ8J,EAAQC,CAAS,GAAK/J,EACtB,MACD,IAAK,MACA,MAAM,QAAQ8J,CAAO,EACxBA,EAAQ,OAAO,OAAOC,CAAS,EAAG,EAAG/J,CAAK,EAE1C8J,EAAQC,CAAS,EAAI/J,EAEtB,MACD,IAAK,SACA,MAAM,QAAQ8J,CAAO,EACxBA,EAAQ,OAAO,OAAOC,CAAS,EAAG,CAAC,EAEnC,OAAOD,EAAQC,CAAS,EAEzB,MACD,QACC,MAAM,IAAI,MAAM,mBAAmBJ,CAAM,EAAE,CAC7C,CACO,OAAAE,CACR,CAEO,SAASG,GACfxL,EACA8J,EAAoB,GACN,CACd,MAAM2B,EAAiD,CACtD,MAAO,IAAM,CACZ,QAAQ,KAAK,yBAAyB,CACvC,EACA,QAAS,KACT,UAAW,KACX,OAAQ,KACR,WAAY,EACZ,IAAKzL,EAAM,SAAS,EACpB,gBAAiB,GACjB,WAAY,EACZ,KAAM,EACN,OAAQ,EACR,iBAAkB,IAAM,CACjB,UAAI,MAAM,yBAAyB,CAC1C,EACA,cAAe,IAAM,CACd,UAAI,MAAM,yBAAyB,CAC1C,EACA,oBAAqB,IAAM,CACpB,UAAI,MAAM,yBAAyB,CAC1C,GAGD,OAAAiJ,GAAOjJ,EAAO8J,CAAI,EAChB,KAAK,MAAOxL,GAAQ,CACpBmN,EAAS,WAAaA,EAAS,KAC3B,IACH,gBAAiBxJ,KAAS3D,EAEhBmN,EAAA,WAAaA,EAAS,UAAUxJ,CAAK,EAE/CwJ,EAAS,WAAaA,EAAS,aACvBxN,EAAG,CACFwN,EAAA,SAAWA,EAAS,QAAQxN,CAAU,EAC/CwN,EAAS,WAAaA,EAAS,MAChC,EACA,EACA,MAAOxN,GAAM,CACb,QAAQ,MAAMA,CAAC,EACNwN,EAAA,SAAWA,EAAS,QAAQxN,CAAU,EAC/CwN,EAAS,WAAaA,EAAS,OAC/B,EAEKA,CACR,CC1MO,SAASC,GAEfzO,EACAqD,EAA4C,CAC5C,EAAAqL,EACAC,EACAC,EAC8B,CAC1B,IAwDM,IAAAC,EAAT,SAAoBnC,EAA0B,EACzCkC,GAAcE,GAAkBpC,EAAM,IAAI,IAC7CqC,EAAWrC,CAAK,CAElB,EA4oBSsC,EAAT,UAAuB,CAEtB,IADOC,GAAA,GACAC,GAAU,OAAS,GACxBA,GAAU,QAAkC,CAC5C,MAAO,OACP,KAAM,GACN,GAGMC,EAAT,SACC9L,EACO,CACH4L,KACAC,GAAU,OAAS,EACrBA,GAAU,QAAkC7L,CAAI,EAEjD+L,GAAO,KAAK/L,CAAI,EACjB,EAGQgM,EAAT,SAAoBvG,EAAsB,CACpCqG,EAAAG,GAAgBxG,CAAK,CAAC,EACrBkG,GAAA,EAGED,EAAT,SAAoBrC,EAA0B,CAC7CyC,EAAK,CAAE,MAAOzC,EAAO,KAAM,EAAO,EACnC,EAES6C,EAAT,UAA+D,CAC9D,OAAIH,GAAO,OAAS,EACZ,QAAQ,QAAQA,GAAO,MAA6B,GACxDH,GAAa,QAAQ,QAAQ,CAAE,MAAO,OAAW,KAAM,GAAM,EAC1D,IAAI,QAAS1F,GAAY2F,GAAU,KAAK3F,CAAO,CAAC,GAxuBlD,MAAE,SAAApI,CAAS,EAAI,KAAK,QACpB,CACL,MAAAqO,EACA,cAAAzN,EACA,OAAA5B,EACA,aAAAsP,EACA,SAAAnN,EACA,QAAAC,EACA,cAAA6K,EACA,wBAAAD,EACA,qBAAAU,EACA,gBAAAZ,EACA,gBAAAC,EACA,UAAAnE,EACA,QAAA8B,EACG,OAEEyC,GAAO,KAEb,GAAI,CAAChL,EAAgB,UAAI,MAAM,cAAc,EAC7C,GAAI,CAACnC,EAAc,UAAI,MAAM,8BAA8B,EAE3D,GAAI,CAAE,SAAAuP,EAAU,cAAAzL,GAAe,WAAAuD,CAAe,EAAAmI,GAC7CrN,EACAtC,EACAuC,EACApC,CAAA,EAGG+D,GAAgBF,GAAmBX,EAAMY,EAAa,EAEtD2L,EACA5D,GACAzK,EAAWpB,EAAO,UAAY,KAElC,MAAM0P,EAAY,OAAO7P,GAAa,SAAW,WAAaA,EAC1D,IAAA8P,GACApC,EAA0B,KAC1BqC,EAAuC,GACvChM,GAA+C,GAC/CiM,EACH,OAAO,OAAW,KAAe,OAAO,SAAa,IAClD,IAAI,gBAAgB,OAAO,SAAS,MAAM,EAAE,WAC5C,GAEE,MAAAlB,GACLjE,IAAS,QAAQ,OAChB,CAACoF,EAAKvD,KACLuD,EAAIvD,CAAK,EAAI,GACNuD,GAER,CAAC,IACG,GASN,eAAeC,IAAwB,CACtC,MAAMrG,EAAkB,CACvB,MAAO,WACP,MAAO,GACP,SAAU,IAAK,EAELkG,EAAAlG,EACAgF,EAAA,CACV,GAAGhF,EACH,KAAM,SACN,SAAUgG,EACV,SAAAH,CAAA,CACA,EAED,IAAIS,EAAgB,GAChBC,EAAiB,GACjB7O,IAAa,MACZqO,GAAaA,EAAU,aAAe,EAC/BA,EAAA,iBAAiB,OAAQ,IAAM,CACxCA,EAAU,MAAM,EAChB,EAEDA,EAAU,MAAM,EAEDO,EAAA,CAAE,SAAAT,EAAU,aAAAD,KAEfhC,GAAAL,EAAeE,GAAK,gBAAgB,EAC3C0B,IACNmB,EAAgB,CAAE,SAAAzC,GACD0C,EAAA,CAAE,SAAA1C,EAAU,aAAA+B,EAAc,SAAAC,CAAS,GAGjD,IACH,GAAI,CAACvP,EACE,UAAI,MAAM,8BAA8B,EAG3C,aAAciQ,GACjB,MAAMZ,EAAM,GAAGrP,EAAO,IAAI,UAAW,CACpC,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,OAAQ,OACR,KAAM,KAAK,UAAUiQ,CAAc,EACnC,EAGF,MAAMZ,EAAM,GAAGrP,EAAO,IAAI,SAAU,CACnC,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,OAAQ,OACR,KAAM,KAAK,UAAUgQ,CAAa,EAClC,OACU,CACH,aACP,4FAEF,CACD,CAEM,MAAAE,GAAoB,MAAOlQ,GAAkC,CAC5D,WAAK,kBAAkBA,CAAM,GAGpC,eAAemQ,GAAqBC,EAAmC,CACtE,GAAI,CAACpQ,EAAQ,OACb,IAAIqQ,EAAoBD,EAAc,UACtCpQ,EAAO,WAAa,CACnB,GAAGA,EAAO,WAAW,OAAQ6C,GAAMA,EAAE,MAAM,cAAgBwN,CAAS,EACpE,GAAGD,EAAc,YAElBpQ,EAAO,aAAe,CACrB,GAAGA,EAAO,aAAa,OAAQsQ,GAAMA,EAAE,cAAgBD,CAAS,EAChE,GAAGD,EAAc,cAEZ,MAAAG,EAAYvQ,EAAO,WAAW,KAAM6C,GAAMA,EAAE,OAAS,OAAO,EAC5D2N,EAAaxQ,EAAO,aAAa,KAAMsQ,GAC5CA,EAAE,QAAQ,KAAMG,GAAMA,EAAE,CAAC,IAAM,QAAQ,GAExCzQ,EAAO,kBAAoBuQ,GAAaC,EACxC,MAAMN,GAAkBlQ,CAAM,EACnB0O,EAAA,CACV,KAAM,SACN,KAAM0B,EACN,SAAUV,EACV,SAAAH,CAAA,CACA,CACF,CAEA,KAAK,YAAYvP,EAAO,KAAM+D,GAAeD,EAAa,EAAE,KAC3D,MAAO4M,GAAa,CAcf,GANMf,GAAA,CACT,KARgBxI,GAChBuJ,EACArJ,EACArH,EAAO,WACP,QACA,KAGoB,CAAC,EACrB,WAAAuO,EACA,SAAAgB,EACA,WAAAf,CAAA,EAEG5H,GAAW2I,EAAUvP,CAAM,EACnB0O,EAAA,CACV,KAAM,SACN,SAAUgB,EACV,MAAO,UACP,MAAO,GACP,SAAAH,EACA,SAAU,IAAK,CACf,EAED3G,EACC,GAAG5I,EAAO,IAAI,OACb0P,EAAU,WAAW,GAAG,EAAIA,EAAY,IAAIA,CAAS,EACtD,GAAGG,EAAa,IAAMA,EAAa,EAAE,GACrC,CACC,GAAGF,GACH,aAAAL,CACD,GAEC,KAAK,CAAC,CAACrK,EAAQ0L,CAAW,IAAW,CACrC,MAAMzN,EAAO+B,EAAO,KAChB0L,GAAe,KACPjC,EAAA,CACV,KAAM,OACN,SAAUgB,EACV,SAAAH,EACA,KAAMpI,GACLjE,EACAmE,EACArH,EAAO,WACP,SACA0K,GAAQ,eACT,EACA,SAAU,KACV,WAAA6D,EACA,WAAAC,CAAA,CACA,EACGvJ,EAAO,eACVkL,GAAqBlL,EAAO,aAAa,EAG/ByJ,EAAA,CACV,KAAM,SACN,SAAUgB,EACV,SAAAH,EACA,MAAO,WACP,IAAKtK,EAAO,iBACZ,MAAO,GACP,SAAU,IAAK,CACf,GAEUyJ,EAAA,CACV,KAAM,SACN,MAAO,QACP,SAAUgB,EACV,SAAAH,EACA,QAAStK,EAAO,MAChB,MAAO,GACP,SAAU,IAAK,CACf,CACF,CACA,EACA,MAAOpE,GAAM,CACF6N,EAAA,CACV,KAAM,SACN,MAAO,QACP,QAAS7N,EAAE,QACX,SAAU6O,EACV,SAAAH,EACA,MAAO,GACP,SAAU,IAAK,CACf,EACD,UACQnO,GAAY,KAAM,CAC5B,KAAM,CAAE,YAAAW,EAAa,KAAAtB,CAAK,EAAI,MAAMC,GACnCkB,EACAZ,CAAA,EAGU0N,EAAA,CACV,KAAM,SACN,MAAO,UACP,MAAO,GACP,SAAUgB,EACV,SAAAH,EACA,SAAU,IAAK,CACf,EAED,IAAIjL,EAAM,IAAI,IACb,GAAGvC,CAAW,MAAMhD,GACnB0B,EACAT,EAAO,KACP,EACA,eAAc6P,EAAa,IAAMA,EAAa,EAAE,IAG9C,KAAK,KACRvL,EAAI,aAAa,IAAI,SAAU,KAAK,GAAG,EAG5BmL,EAAA,IAAI,UAAUnL,CAAG,EAEnBmL,EAAA,QAAWmB,GAAQ,CACvBA,EAAI,UACGlC,EAAA,CACV,KAAM,SACN,MAAO,QACP,OAAQ,GACR,QAASvQ,EACT,MAAO,GACP,SAAUuR,EACV,SAAAH,EACA,SAAU,IAAK,CACf,CACF,EAGSE,EAAA,UAAY,SAAUlD,EAAO,CACtC,MAAMc,EAAQ,KAAK,MAAMd,EAAM,IAAI,EAC7B,CAAE,KAAA7I,EAAM,OAAAqF,EAAQ,KAAA7F,CAAS,EAAAS,GAC9B0J,EACAzJ,GAAY2L,CAAQ,GAGrB,GAAI7L,IAAS,UAAYqF,GAAU,CAAC6G,EAExBlB,EAAA,CACV,KAAM,SACN,SAAUgB,EACV,SAAAH,EACA,SAAU,KACV,GAAGxG,CAAA,CACH,EACGA,EAAO,QAAU,SACpB0G,EAAU,MAAM,UAEP/L,IAAS,OAAQ,CAC3B+L,EAAU,KAAK,KAAK,UAAU,CAAE,SAAAF,EAAU,aAAAD,CAAc,EAAC,EACzD,YACU5L,IAAS,OACT+L,EAAA,KAAK,KAAK,UAAU,CAAE,GAAGE,GAAS,aAAAL,CAAc,EAAC,EACjD5L,IAAS,WACRkM,EAAA7G,EACDrF,IAAS,MACRgL,EAAA,CACV,KAAM,MACN,IAAKxL,EAAK,IACV,MAAOA,EAAK,MACZ,SAAUwM,EACV,SAAUxM,EAAK,SACf,QAASA,EAAK,QACd,SAAAqM,CAAA,CACA,EACS7L,IAAS,cACRgL,EAAA,CACV,KAAM,SACN,SAAU,KACV,GAAG3F,EACH,MAAOA,GAAQ,MACf,MAAO,GACP,SAAU2G,EACV,SAAAH,CAAA,CACA,EAEErM,IACQwL,EAAA,CACV,KAAM,OACN,SAAU,KACV,KAAMvH,GACLjE,EAAK,KACLmE,EACArH,EAAO,WACP,SACA0K,GAAQ,eACT,EACA,SAAUgF,EACV,SAAAH,EACA,WAAAhB,EACA,WAAAC,CAAA,CACA,EAEGoB,IACQlB,EAAA,CACV,KAAM,SACN,SAAU,KACV,GAAGkB,EACH,MAAO7G,GAAQ,MACf,MAAO,GACP,SAAU2G,EACV,SAAAH,CAAA,CACA,EACDE,EAAU,MAAM,GAElB,EAKGnS,GAAQ0C,EAAO,SAAW,QAAS,KAAK,EAAI,GAC/C,iBAAiB,OAAQ,IACxByP,EAAU,KAAK,KAAK,UAAU,CAAE,KAAMH,CAAa,CAAC,CAAC,EAEvD,SACUlO,GAAY,MAAO,CAClBsN,EAAA,CACV,KAAM,SACN,MAAO,UACP,MAAO,GACP,SAAUgB,EACV,SAAAH,EACA,SAAU,IAAK,CACf,EACG,IAAAnC,EAAS,IAAI,gBAAgB,CAChC,SAAUmC,EAAS,SAAS,EAC5B,aAAAD,CAAA,CACA,EAAE,SAAS,EACZ,IAAIhL,EAAM,IAAI,IACb,GAAGtE,EAAO,IAAI,eACb6P,EAAaA,EAAa,IAAM,EACjC,GAAGzC,CAAM,IASV,GANI,KAAK,KACR9I,EAAI,aAAa,IAAI,SAAU,KAAK,GAAG,EAG/BuH,GAAA,KAAK,OAAOvH,CAAG,EAEpB,CAACuH,GACJ,OAAO,QAAQ,OACd,IAAI,MAAM,mCAAqCvH,EAAI,UAAU,GAIxDuH,GAAA,UAAY,eAAgBU,EAAqB,CACvD,MAAMc,EAAQ,KAAK,MAAMd,EAAM,IAAI,EAC7B,CAAE,KAAA7I,EAAM,OAAAqF,EAAQ,KAAA7F,CAAS,EAAAS,GAC9B0J,EACAzJ,GAAY2L,CAAQ,GAGrB,GAAI7L,IAAS,UAAYqF,GAAU,CAAC6G,EAExBlB,EAAA,CACV,KAAM,SACN,SAAUgB,EACV,SAAAH,EACA,SAAU,KACV,GAAGxG,CAAA,CACH,EACGA,EAAO,QAAU,UACpB8C,IAAQ,MAAM,EACRgD,aAEGnL,IAAS,OAAQ,CAC3B6J,EAAWF,EAAM,SACb,IAAC5G,EAAGsC,CAAM,EAAI,MAAMH,EAAU,GAAG5I,EAAO,IAAI,cAAe,CAC9D,GAAG2P,GACH,aAAAL,EACA,SAAA/B,CAAA,CACA,EACGxE,IAAW,MACH2F,EAAA,CACV,KAAM,SACN,MAAO,QACP,QAASvQ,EACT,MAAO,GACP,SAAUuR,EACV,SAAAH,EACA,SAAU,IAAK,CACf,EACD1D,IAAQ,MAAM,EACRgD,IACP,MACUnL,IAAS,WACRkM,EAAA7G,EACDrF,IAAS,MACRgL,EAAA,CACV,KAAM,MACN,IAAKxL,EAAK,IACV,MAAOA,EAAK,MACZ,SAAUwM,EACV,SAAUxM,EAAK,SACf,QAASA,EAAK,QACd,SAAAqM,CAAA,CACA,EACS7L,IAAS,cACRgL,EAAA,CACV,KAAM,SACN,SAAU,KACV,GAAG3F,EACH,MAAOA,GAAQ,MACf,MAAO,GACP,SAAU2G,EACV,SAAAH,CAAA,CACA,EAEErM,IACQwL,EAAA,CACV,KAAM,OACN,SAAU,KACV,KAAMvH,GACLjE,EAAK,KACLmE,EACArH,EAAO,WACP,SACA0K,GAAQ,eACT,EACA,SAAUgF,EACV,SAAAH,EACA,WAAAhB,EACA,WAAAC,CAAA,CACA,EAEGoB,IACQlB,EAAA,CACV,KAAM,SACN,SAAU,KACV,GAAGkB,EACH,MAAO7G,GAAQ,MACf,MAAO,GACP,SAAU2G,EACV,SAAAH,CAAA,CACA,EACD1D,IAAQ,MAAM,EACRgD,KAER,CACD,SAEAzN,GAAY,UACZA,GAAY,UACZA,GAAY,YACZA,GAAY,SACX,CAGUsN,EAAA,CACV,KAAM,SACN,MAAO,UACP,MAAO,GACP,SAAUgB,EACV,SAAAH,EACA,SAAU,IAAK,CACf,EACD,IAAIsB,EAAW,GAEd,OAAO,OAAW,KAClB,OAAO,SAAa,MAEpBA,EAAW,QAAQ,UAAU,UAI9B,MAAM7J,EAAS6J,EAAS,SAAS,OAAO,EACrC,gBAAgBA,EAAS,MAAM,GAAG,EAAE,CAAC,CAAC,+BACtC,yBAEGC,EACL,OAAO,OAAW,KAClB,OAAO,SAAa,KACpB,OAAO,QAAU,OACZC,EAAmB1J,EAAW,SAAWrH,EAAO,UAErD8Q,GAAaC,EACVjK,GAAsB,kBAAmBE,CAAM,EAC/C,QAAQ,QAAQ,IAAI,GACuB,KAAMlH,GAC7C8I,EACN,GAAG5I,EAAO,IAAI,eAAe6P,CAAU,GACvC,CACC,GAAGF,GACH,aAAAL,CACD,EACAxP,CAAA,CAED,EACiB,KAAK,MAAO,CAACM,EAAU2I,EAAM,IAAW,CACzD,GAAIA,KAAW,IACH2F,EAAA,CACV,KAAM,SACN,MAAO,QACP,QAASxQ,GACT,MAAO,GACP,SAAUwR,EACV,SAAAH,EACA,SAAU,IAAK,CACf,UACSxG,KAAW,IACV2F,EAAA,CACV,KAAM,SACN,MAAO,QACP,QAASvQ,EACT,MAAO,GACP,SAAUuR,EACV,SAAAH,EACA,SAAU,IAAK,CACf,MACK,CACNhC,EAAWnN,EAAS,SAChB,IAAA4Q,GAAW,eAAgB3D,GAA8B,CACxD,IACH,KAAM,CAAE,KAAA3J,EAAM,OAAAqF,EAAQ,KAAA7F,GAASS,GAC9B0J,GACAzJ,GAAY2L,CAAQ,GAGrB,GAAI7L,GAAQ,YACX,OAGD,GAAIA,IAAS,UAAYqF,GAAU,CAAC6G,EAExBlB,EAAA,CACV,KAAM,SACN,SAAUgB,EACV,SAAAH,EACA,SAAU,KACV,GAAGxG,CAAA,CACH,UACSrF,IAAS,WACRqF,YACDrF,GAAQ,mBACV,cAAM,mBAAoBqF,GAAQ,OAAO,EACtC2F,EAAA,CACV,KAAM,SACN,MAAO,QACP,QACC3F,GAAQ,SAAW,gCACpB,MAAO,GACP,SAAU2G,EACV,SAAAH,EACA,SAAU,IAAK,CACf,UACS7L,IAAS,MAAO,CACfgL,EAAA,CACV,KAAM,MACN,IAAKxL,EAAK,IACV,MAAOA,EAAK,MACZ,SAAUwM,EACV,SAAUxM,EAAK,SACf,QAASA,EAAK,QACd,SAAAqM,CAAA,CACA,EACD,YACU7L,IAAS,eACRgL,EAAA,CACV,KAAM,SACN,SAAU,KACV,GAAG3F,EACH,MAAOA,GAAQ,MACf,MAAO,GACP,SAAU2G,EACV,SAAAH,CAAA,CACA,EAEArM,GACA,CAAC,SAAU,WAAY,QAAQ,EAAE,SAAS9B,CAAQ,GAEhCqM,GAAAC,EAAsBH,EAAWrK,CAAI,GAGrDA,IACQwL,EAAA,CACV,KAAM,OACN,SAAU,KACV,KAAMvH,GACLjE,EAAK,KACLmE,EACArH,EAAO,WACP,SACA0K,GAAQ,eACT,EACA,SAAUgF,EACV,SAAAH,CAAA,CACA,EACGrM,EAAK,eACF,MAAAiN,GAAqBjN,EAAK,aAAa,EAG1C0M,IACQlB,EAAA,CACV,KAAM,SACN,SAAU,KACV,GAAGkB,EACH,MAAO7G,GAAQ,MACf,MAAO,GACP,SAAU2G,EACV,SAAAH,CAAA,CACA,EAEKV,OAKP9F,GAAQ,QAAU,YAClBA,GAAQ,QAAU,WAEd+D,EAAgBS,CAAS,GAC5B,OAAOT,EAAgBS,CAAS,EAE7BA,KAAaG,GAChB,OAAOA,EAAqBH,CAAS,SAG/B1M,EAAG,CACH,cAAM,8BAA+BA,CAAC,EACnC6N,EAAA,CACV,KAAM,SACN,MAAO,QACP,QAAS,gCACT,MAAO,GACP,SAAUgB,EACV,SAAAH,EACA,SAAU,IAAK,CACf,EACG,CAAC,SAAU,WAAY,QAAQ,EAAE,SAASnO,CAAQ,IACxCkM,GAAAL,EAAeE,GAAK,gBAAgB,EACjDF,EAAc,KAAO,GACf4B,IAER,GAGGtB,KAAYP,IACfA,EAAwBO,CAAQ,EAAE,QAAS0D,IAC1CD,GAASC,EAAG,GAEb,OAAOjE,EAAwBO,CAAQ,GAGxCT,EAAgBS,CAAQ,EAAIyD,GAC5BjE,EAAgB,IAAIQ,CAAQ,EACvBN,EAAc,MAClB,MAAM,KAAK,aAEb,EACA,CACF,CACD,GAGD,IAAI6B,GAAO,GACX,MAAMG,GAA+D,GAC/DF,GAES,GAsCTmC,GAAW,CAChB,CAAC,OAAO,aAAa,EAAG,IAAMA,GAC9B,KAAA9B,EACA,MAAO,MAAOhL,IACb8K,EAAW9K,CAAK,EACTgL,EAAK,GAEb,OAAQ,UACDP,IACCO,EAAK,GAEb,OAAAW,EAAA,EAGM,OAAAmB,SACCvI,EAAO,CACP,oBAAM,wCAAyCA,CAAK,EACtDA,CACP,CACD,CAEA,SAASwG,GAAmBxG,EAA8B,CAClD,OACN,KAAM,CACLS,EACAC,IACIA,EAAOV,CAAK,EAEnB,CAEA,SAAS6G,GACRrN,EACAtC,EACAuC,EACApC,EAKC,CACG,IAAAuP,EACAzL,EACAuD,EAEA,UAAOxH,GAAa,SACZ0P,EAAA1P,EACKiE,EAAA3B,EAAS,kBAAkBoN,CAAQ,EACnDlI,EAAarH,EAAO,aAAa,KAAMK,GAAQA,EAAI,IAAMR,CAAQ,MAC3D,CACN,MAAMsJ,EAAmBtJ,EAAS,QAAQ,MAAO,EAAE,EAEnD0P,EAAWnN,EAAQ+G,CAAgB,EACnCrF,EAAgB3B,EAAS,gBAAgBtC,EAAS,KAAM,GACxDwH,EAAarH,EAAO,aAAa,KAC/BK,GAAQA,EAAI,IAAM+B,EAAQ+G,CAAgB,EAE7C,CAEI,UAAOoG,GAAa,SACvB,MAAM,IAAI,MACT,6EAGK,OAAE,SAAAA,EAAU,cAAAzL,EAAe,WAAAuD,EACnC,CCzyBO,MAAM2D,EAAO,CACnB,cACA,QAEA,OACA,SACA,QAAkC,GAClC,aAAuB,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,CAAC,EAC7D,IAAsB,GACtB,YAA+C,GAEvC,QAAyB,KAGjC,cAAgB,CAAE,KAAM,IACxB,wBAAmD,GACnD,qBAAgD,GAChD,gBAAqE,GACrE,oBAAmC,IACnC,gBAAsC,KACtC,iBAA2C,KAC3C,gBAAsC,KAEtC,MAAMpI,EAA0B8J,EAAuC,CACtE,MAAM5M,EAAU,IAAI,QAAQ4M,GAAM,SAAW,CAAE,GAC3C,aAAQ,KAAK,SACR5M,EAAA,OAAO,SAAU,KAAK,OAAO,EAG/B,MAAM8C,EAAO,CAAE,GAAG8J,EAAM,QAAA5M,CAAS,EACzC,CAEA,OAAOwE,EAAuB,CACvB,MAAAxE,EAAU,IAAI,QAChB,aAAQ,KAAK,SACRA,EAAA,OAAO,SAAU,KAAK,OAAO,EAGjC,sBAAmB,IAAI,gBAE5B,KAAK,gBAAkBsO,GAAgB9J,EAAI,WAAY,CACtD,YAAa,UACb,QAAAxE,EACA,OAAQ,KAAK,iBAAiB,OAC9B,EAEM,KAAK,eACb,CAEA,SACA,aAKA,OAMA,YAKA,UAKA,OAOA,QAKA,YACQ,eACA,gBACR,YACC8B,EACA8I,EAAyB,CAAE,OAAQ,CAAC,MAAM,GACzC,CACD,KAAK,cAAgB9I,EAChB8I,EAAQ,SACJA,EAAA,OAAS,CAAC,MAAM,GAGzB,KAAK,QAAUA,EAEV,cAAWrG,GAAS,KAAK,IAAI,EAC7B,kBAAeE,GAAa,KAAK,IAAI,EACrC,iBAAcoD,GAAY,KAAK,IAAI,EACnC,eAAYiB,GAAU,KAAK,IAAI,EAC/B,YAAS0F,GAAO,KAAK,IAAI,EACzB,aAAUtF,GAAQ,KAAK,IAAI,EAC3B,iBAAc6D,GAAY,KAAK,IAAI,EACnC,oBAAiBjN,GAAe,KAAK,IAAI,EACzC,qBAAkBW,GAAgB,KAAK,IAAI,EAC3C,YAAS2E,GAAO,KAAK,IAAI,EAC9B,KAAK,MAAQ,KAAK,MAAM,KAAK,IAAI,EACjC,KAAK,qBAAuB,KAAK,qBAAqB,KAAK,IAAI,EAC/D,KAAK,OAAS,KAAK,OAAO,KAAK,IAAI,CACpC,CAEA,MAAc,MAAsB,CAEjC,WAAO,OAAW,KAAe,EAAE,cAAe,UACnD,CAAC,OAAO,UACP,CACK,MAAAiM,EAAK,aAAM,OAAO,uBAAI,OAAA1I,KAAA,uBAC5B,OAAO,UAAY0I,EAAG,SACvB,CAEI,IACC,KAAK,QAAQ,MAChB,MAAM,KAAK,kBAGN,WAAK,kBAAkB,KAAK,CAAC,CAAE,OAAAnR,CACpC,SAAK,kBAAkBA,CAAM,SAEtBa,EAAQ,CAChB,MAAM,MAAMA,CAAC,CACd,CAEK,cAAW,MAAM,KAAK,SAAS,EACpC,KAAK,QAAUtB,GAAiB,KAAK,QAAQ,cAAgB,EAAE,CAChE,CAEA,MAAM,kBAAkB6R,EAAgC,CAkBvD,GAjBIA,IACH,KAAK,OAASA,EACV,KAAK,QAAU,KAAK,OAAO,mBAC1B,KAAK,OAAO,UAAY,KAAK,QAAQ,WACxC,KAAK,IAAM,MAAMjS,GAChB,KAAK,OAAO,SACZ,KAAK,QAAQ,SACb,KAAK,WAMLiS,EAAQ,UAAY,KAAK,QAAQ,WACpC,KAAK,IAAM,MAAMjS,GAAQiS,EAAQ,SAAU,KAAK,QAAQ,QAAQ,GAG7D,KAAK,QAAU,KAAK,OAAO,kBAAmB,CAEjD,MAAMC,EAAgB,IAAI,IACzB,GAAG,KAAK,OAAO,IAAI,cAAc,KAAK,YAAY,IAI/C,KAAK,KACRA,EAAc,aAAa,IAAI,SAAU,KAAK,GAAG,EAI7C,KAAK,kBACJ,qBAAkB,KAAK,OAAOA,CAAa,EAElD,CACD,CAEA,aAAa,QACZzP,EACA8I,EAAyB,CACxB,OAAQ,CAAC,MAAM,GAEE,CAClB,MAAMzC,EAAS,IAAI,KAAKrG,EAAe8I,CAAO,EAC9C,aAAMzC,EAAO,OACNA,CACR,CAEA,OAAc,CACAqF,GAAA,KAAK,cAAe,KAAK,gBAAgB,CACvD,CAEA,aAAa,UACZ1L,EACA8I,EAA4B,CAC3B,OAAQ,CAAC,MAAM,GAEE,CACX,OAAAD,GAAU7I,EAAe8I,CAAO,CACxC,CAEA,MAAc,iBAAgC,CAC7C,KAAM,CAAE,cAAAlK,EAAe,KAAAC,EAAM,SAAAsJ,CAAA,EAAa,MAAMrJ,GAC/C,KAAK,cACL,KAAK,QAAQ,UAGR,CAAE,gBAAA+I,CAAgB,EAAI,KAAK,QAE7BM,GAAYN,GACT,MAAAK,GAAqBC,EAAUN,CAAe,EAGjD,IAAAzJ,EAEA,IAGH,GAFAA,EAAS,MAAM,KAAK,eAAe,GAAGQ,CAAa,KAAKC,CAAI,EAAE,EAE1D,CAACT,EACE,UAAI,MAAM5B,EAAgB,EAG1B,YAAK,eAAe4B,CAAM,QACzBa,EAAQ,CAChB,GAAIkJ,GAAYN,EACfD,GACCO,EACArI,GAAc,KAAKqI,CAAQ,EAAI,aAAe,YAC9C,KAAK,0BAGF,OAAAN,GACaA,EAAA,CACf,OAAQ,QACR,QAAS,6BACT,YAAa,QACb,OAAQ,YACR,EACI,MAAM5I,CAAC,CAEf,CACD,CAEA,MAAc,eACbuQ,EACkC,CAS9B,GARJ,KAAK,OAASA,EAEV,OAAO,OAAW,KAAe,OAAO,SAAa,KACpD,OAAO,SAAS,WAAa,WAChC,KAAK,OAAO,KAAO,KAAK,OAAO,KAAK,QAAQ,UAAW,UAAU,GAI/D,KAAK,OAAO,cACf,OAAO,KAAK,qBAGT,IACE,cAAW,MAAM,KAAK,SAAS,QAC5BvQ,EAAG,CACH,cAAMvC,GAAsBuC,EAAY,OAAO,CACxD,CAEA,OAAO,KAAK,oBACb,CAEA,MAAM,qBAAqBkI,EAA6C,CACvE,GAAI,CAAC,KACE,UAAI,MAAM3K,EAAgB,EAE3B,MAAE,gBAAAqL,CAAgB,EAAI,KAAK,QAE7B,GADAA,GAAiBA,EAAgBV,CAAM,EACvCA,EAAO,SAAW,UACjB,IAEC,GADC,YAAS,MAAM,KAAK,gBAAgB,EACrC,CAAC,KAAK,OACH,UAAI,MAAM3K,EAAgB,EAK1B,OAFS,MAAM,KAAK,eAAe,KAAK,MAAM,QAG7CyC,EAAG,CACX,MAAI4I,GACaA,EAAA,CACf,OAAQ,QACR,QAAS,6BACT,YAAa,QACb,OAAQ,YACR,EAEI5I,CACP,CAEF,CAEA,MAAa,iBACZyQ,EACAC,EACArO,EACmB,CACf,IAAC,KAAK,OACH,UAAI,MAAM9E,EAAgB,EAGjC,MAAM0B,EAGF,GAEE,CAAE,SAAAkB,CAAS,EAAI,KAAK,QACpB,CAAE,aAAAsO,CAAiB,OAErBtO,IACHlB,EAAQ,cAAgB,UAAU,KAAK,QAAQ,QAAQ,IAGpD,IAAA0E,EACArB,EAAY,KAAK,OAAO,WAAW,KACrCL,GAASA,EAAK,KAAOwO,CAAA,EAEnBnO,GAAW,OAAO,SACrBqB,EAAWrB,EAAU,MAAM,SAE3BqB,EAAW,KAAK,OAAO,KAGpB,IAAAqE,EAEJ,GAAI,WAAY3F,EAAM,CACrB2F,EAAO,IAAI,SACA,UAAA1E,KAAOjB,EAAK,KAClBiB,IAAQ,UACZ0E,EAAK,OAAO1E,EAAKjB,EAAK,KAAKiB,CAAG,CAAC,EAEhC0E,EAAK,IAAI,eAAgByI,EAAa,SAAU,GAC3CzI,EAAA,IAAI,UAAW0I,CAAO,EACtB1I,EAAA,IAAI,eAAgByG,CAAY,OAErCzG,EAAO,KAAK,UAAU,CACrB,KAAA3F,EACA,aAAAoO,EACA,QAAAC,EACA,aAAAjC,CAAA,CACA,EAEDxP,EAAQ,cAAc,EAAI,mBAGvBkB,IACKlB,EAAA,cAAgB,UAAUkB,CAAQ,IAGvC,IACH,MAAMZ,EAAW,MAAM,KAAK,MAAM,GAAGoE,CAAQ,qBAAsB,CAClE,OAAQ,OACR,KAAAqE,EACA,QAAA/I,EACA,YAAa,UACb,EAEG,IAACM,EAAS,GACb,MAAM,IAAI,MACT,0CAA4CA,EAAS,YAKhD,OADQ,MAAMA,EAAS,aAEtBS,EAAG,CACX,QAAQ,KAAKA,CAAC,CACf,CACD,CAEO,YAAY2Q,EAA2B,CAC7C,KAAK,QAAUlQ,GAAsBkQ,CAAW,EAAE,KAAK,IAAI,CAC5D,CAEQ,oBAAoC,CACpC,OACN,OAAQ,KAAK,OACb,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,SAAU,KAAK,SACf,iBAAkB,KAAK,iBAEzB,CACD,CCnaO,SAASC,IAAO,CAAE,CAEb,MAACC,GAAYC,GAAMA,EAoCxB,SAASC,GAAIvU,EAAI,CACvB,OAAOA,EAAE,CACV,CAUO,SAASwU,GAAQrS,EAAK,CAC5BA,EAAI,QAAQoS,EAAG,CAChB,CAMO,SAASE,GAAYC,EAAO,CAClC,OAAO,OAAOA,GAAU,UACzB,CAGO,SAASC,GAAezU,EAAGC,EAAG,CACpC,OAAOD,GAAKA,EAAIC,GAAKA,EAAID,IAAMC,GAAMD,GAAK,OAAOA,GAAM,UAAa,OAAOA,GAAM,UAClF,CAiEO,SAAS0U,GAAUC,KAAUC,EAAW,CAC9C,GAAID,GAAS,KAAM,CAClB,UAAWlB,KAAYmB,EACtBnB,EAAS,MAAS,EAEnB,OAAOS,EACP,CACD,MAAMW,EAAQF,EAAM,UAAU,GAAGC,CAAS,EAC1C,OAAOC,EAAM,YAAc,IAAMA,EAAM,YAAW,EAAKA,CACxD,CAUO,SAASC,GAAgBH,EAAO,CACtC,IAAI9N,EACJ,OAAA6N,GAAUC,EAAQzL,GAAOrC,EAAQqC,CAAE,IAC5BrC,CACR,CAmIO,SAASkO,GAAelO,EAAO,CACrC,MAAM2H,EAAQ,OAAO3H,GAAU,UAAYA,EAAM,MAAM,4BAA4B,EACnF,OAAO2H,EAAQ,CAAC,WAAWA,EAAM,CAAC,CAAC,EAAGA,EAAM,CAAC,GAAK,IAAI,EAAI,CAAwB3H,EAAQ,IAAI,CAC/F,CCvRA,MAAMmO,GAAmB,GAWlB,SAASC,GAASpO,EAAOqO,EAAO,CACtC,MAAO,CACN,UAAWC,GAAStO,EAAOqO,CAAK,EAAE,SACpC,CACA,CAWO,SAASC,GAAStO,EAAOqO,EAAQhB,GAAM,CAE7C,IAAIkB,EAEJ,MAAMC,EAAc,IAAI,IAIxB,SAASC,EAAIC,EAAW,CACvB,GAAId,GAAe5N,EAAO0O,CAAS,IAClC1O,EAAQ0O,EACJH,GAAM,CAET,MAAMI,EAAY,CAACR,GAAiB,OACpC,UAAWS,KAAcJ,EACxBI,EAAW,CAAC,IACZT,GAAiB,KAAKS,EAAY5O,CAAK,EAExC,GAAI2O,EAAW,CACd,QAASzS,EAAI,EAAGA,EAAIiS,GAAiB,OAAQjS,GAAK,EACjDiS,GAAiBjS,CAAC,EAAE,CAAC,EAAEiS,GAAiBjS,EAAI,CAAC,CAAC,EAE/CiS,GAAiB,OAAS,CAC1B,CACD,CAEF,CAMD,SAASU,EAAO5V,EAAI,CACnBwV,EAAIxV,EAAG+G,CAAK,CAAC,CACb,CAOD,SAAS6N,EAAUL,EAAKsB,EAAazB,GAAM,CAE1C,MAAMuB,EAAa,CAACpB,EAAKsB,CAAU,EACnC,OAAAN,EAAY,IAAII,CAAU,EACtBJ,EAAY,OAAS,IACxBD,EAAOF,EAAMI,EAAKI,CAAM,GAAKxB,IAE9BG,EAAIxN,CAAK,EACF,IAAM,CACZwO,EAAY,OAAOI,CAAU,EACzBJ,EAAY,OAAS,GAAKD,IAC7BA,IACAA,EAAO,KAEX,CACE,CACD,MAAO,CAAE,IAAAE,EAAK,OAAAI,EAAQ,UAAAhB,EACvB,CAsCO,SAASkB,GAAQC,EAAQ/V,EAAIgW,EAAe,CAClD,MAAMC,EAAS,CAAC,MAAM,QAAQF,CAAM,EAE9BG,EAAeD,EAAS,CAACF,CAAM,EAAIA,EACzC,GAAI,CAACG,EAAa,MAAM,OAAO,EAC9B,MAAM,IAAI,MAAM,sDAAsD,EAEvE,MAAMC,EAAOnW,EAAG,OAAS,EACzB,OAAOmV,GAASa,EAAe,CAACR,EAAKI,IAAW,CAC/C,IAAIQ,EAAU,GACd,MAAMxE,EAAS,GACf,IAAIyE,EAAU,EACVC,EAAUlC,GACd,MAAMmC,EAAO,IAAM,CAClB,GAAIF,EACH,OAEDC,IACA,MAAMpK,EAASlM,EAAGiW,EAASrE,EAAO,CAAC,EAAIA,EAAQ4D,EAAKI,CAAM,EACtDO,EACHX,EAAItJ,CAAM,EAEVoK,EAAU7B,GAAYvI,CAAM,EAAIA,EAASkI,EAE7C,EACQoC,EAAgBN,EAAa,IAAI,CAACrB,EAAO5R,IAC9C2R,GACCC,EACC9N,GAAU,CACV6K,EAAO3O,CAAC,EAAI8D,EACZsP,GAAW,EAAE,GAAKpT,GACdmT,GACHG,GAED,EACD,IAAM,CACLF,GAAW,GAAKpT,CAChB,CACD,CACJ,EACE,OAAAmT,EAAU,GACVG,IACO,UAAgB,CACtB/B,GAAQgC,CAAa,EACrBF,IAIAF,EAAU,EACb,CACA,CAAE,CACF,ypBCpLA,IAAIK,GAAoB,SAA2B1P,EAAO,CACzD,OAAO2P,GAAgB3P,CAAK,GACxB,CAAC4P,GAAU5P,CAAK,CACrB,EAEA,SAAS2P,GAAgB3P,EAAO,CAC/B,MAAO,CAAC,CAACA,GAAS,OAAOA,GAAU,QACpC,CAEA,SAAS4P,GAAU5P,EAAO,CACzB,IAAI6P,EAAc,OAAO,UAAU,SAAS,KAAK7P,CAAK,EAEtD,OAAO6P,IAAgB,mBACnBA,IAAgB,iBAChBC,GAAe9P,CAAK,CACzB,CAGA,IAAI+P,GAAe,OAAO,QAAW,YAAc,OAAO,IACtDC,GAAqBD,GAAe,OAAO,IAAI,eAAe,EAAI,MAEtE,SAASD,GAAe9P,EAAO,CAC9B,OAAOA,EAAM,WAAagQ,EAC3B,CAEA,SAASC,GAAYC,EAAK,CACzB,OAAO,MAAM,QAAQA,CAAG,EAAI,CAAE,EAAG,CAAE,CACpC,CAEA,SAASC,GAA8BnQ,EAAOsG,EAAS,CACtD,OAAQA,EAAQ,QAAU,IAASA,EAAQ,kBAAkBtG,CAAK,EAC/DoQ,GAAUH,GAAYjQ,CAAK,EAAGA,EAAOsG,CAAO,EAC5CtG,CACJ,CAEA,SAASqQ,GAAkBxG,EAAQyG,EAAQhK,EAAS,CACnD,OAAOuD,EAAO,OAAOyG,CAAM,EAAE,IAAI,SAASC,EAAS,CAClD,OAAOJ,GAA8BI,EAASjK,CAAO,CACvD,CAAE,CACF,CAEA,SAASkK,GAAiBzQ,EAAKuG,EAAS,CACvC,GAAI,CAACA,EAAQ,YACZ,OAAO8J,GAER,IAAIK,EAAcnK,EAAQ,YAAYvG,CAAG,EACzC,OAAO,OAAO0Q,GAAgB,WAAaA,EAAcL,EAC1D,CAEA,SAASM,GAAgC7G,EAAQ,CAChD,OAAO,OAAO,sBACX,OAAO,sBAAsBA,CAAM,EAAE,OAAO,SAAS8G,EAAQ,CAC9D,OAAO,OAAO,qBAAqB,KAAK9G,EAAQ8G,CAAM,CACzD,CAAG,EACC,CAAE,CACN,CAEA,SAASC,GAAQ/G,EAAQ,CACxB,OAAO,OAAO,KAAKA,CAAM,EAAE,OAAO6G,GAAgC7G,CAAM,CAAC,CAC1E,CAEA,SAASgH,GAAmB9O,EAAQ+O,EAAU,CAC7C,GAAI,CACH,OAAOA,KAAY/O,CACnB,MAAU,CACV,MAAO,EACP,CACF,CAGA,SAASgP,GAAiBlH,EAAQ9J,EAAK,CACtC,OAAO8Q,GAAmBhH,EAAQ9J,CAAG,GACjC,EAAE,OAAO,eAAe,KAAK8J,EAAQ9J,CAAG,GACvC,OAAO,qBAAqB,KAAK8J,EAAQ9J,CAAG,EAClD,CAEA,SAASiR,GAAYnH,EAAQyG,EAAQhK,EAAS,CAC7C,IAAI2K,EAAc,GAClB,OAAI3K,EAAQ,kBAAkBuD,CAAM,GACnC+G,GAAQ/G,CAAM,EAAE,QAAQ,SAAS9J,EAAK,CACrCkR,EAAYlR,CAAG,EAAIoQ,GAA8BtG,EAAO9J,CAAG,EAAGuG,CAAO,CACxE,CAAG,EAEFsK,GAAQN,CAAM,EAAE,QAAQ,SAASvQ,EAAK,CACjCgR,GAAiBlH,EAAQ9J,CAAG,IAI5B8Q,GAAmBhH,EAAQ9J,CAAG,GAAKuG,EAAQ,kBAAkBgK,EAAOvQ,CAAG,CAAC,EAC3EkR,EAAYlR,CAAG,EAAIyQ,GAAiBzQ,EAAKuG,CAAO,EAAEuD,EAAO9J,CAAG,EAAGuQ,EAAOvQ,CAAG,EAAGuG,CAAO,EAEnF2K,EAAYlR,CAAG,EAAIoQ,GAA8BG,EAAOvQ,CAAG,EAAGuG,CAAO,EAExE,CAAE,EACM2K,CACR,CAEA,SAASb,GAAUvG,EAAQyG,EAAQhK,EAAS,CAC3CA,EAAUA,GAAW,GACrBA,EAAQ,WAAaA,EAAQ,YAAc+J,GAC3C/J,EAAQ,kBAAoBA,EAAQ,mBAAqBoJ,GAGzDpJ,EAAQ,8BAAgC6J,GAExC,IAAIe,EAAgB,MAAM,QAAQZ,CAAM,EACpCa,EAAgB,MAAM,QAAQtH,CAAM,EACpCuH,EAA4BF,IAAkBC,EAElD,OAAKC,EAEMF,EACH5K,EAAQ,WAAWuD,EAAQyG,EAAQhK,CAAO,EAE1C0K,GAAYnH,EAAQyG,EAAQhK,CAAO,EAJnC6J,GAA8BG,EAAQhK,CAAO,CAMtD,CAEA8J,GAAU,IAAM,SAAsBiB,EAAO/K,EAAS,CACrD,GAAI,CAAC,MAAM,QAAQ+K,CAAK,EACvB,MAAM,IAAI,MAAM,mCAAmC,EAGpD,OAAOA,EAAM,OAAO,SAASC,EAAMtG,EAAM,CACxC,OAAOoF,GAAUkB,EAAMtG,EAAM1E,CAAO,CACpC,EAAE,EAAE,CACN,EAEA,IAAIiL,GAAcnB,GAElBoB,GAAiBD,mBCpHjB,IAAIE,GAAgB,SAASvF,EAAG9S,EAAG,CACjC,OAAAqY,GAAgB,OAAO,gBAClB,CAAE,UAAW,cAAgB,OAAS,SAAUvF,EAAG9S,EAAG,CAAE8S,EAAE,UAAY9S,CAAE,GACzE,SAAU8S,EAAG9S,EAAG,CAAE,QAASgG,KAAKhG,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGgG,CAAC,IAAG8M,EAAE9M,CAAC,EAAIhG,EAAEgG,CAAC,IAC1FqS,GAAcvF,EAAG9S,CAAC,CAC3B,EAEO,SAASsY,GAAUxF,EAAG9S,EAAG,CAC9B,GAAI,OAAOA,GAAM,YAAcA,IAAM,KACjC,MAAM,IAAI,UAAU,uBAAyB,OAAOA,CAAC,EAAI,+BAA+B,EAC5FqY,GAAcvF,EAAG9S,CAAC,EAClB,SAASuY,GAAK,CAAE,KAAK,YAAczF,CAAI,CACvCA,EAAE,UAAY9S,IAAM,KAAO,OAAO,OAAOA,CAAC,GAAKuY,EAAG,UAAYvY,EAAE,UAAW,IAAIuY,EACjF,CAEO,IAAIC,EAAW,UAAW,CAC/B,OAAAA,EAAW,OAAO,QAAU,SAAkB,EAAG,CAC7C,QAASC,EAAG3V,EAAI,EAAG,EAAI,UAAU,OAAQA,EAAI,EAAGA,IAAK,CACjD2V,EAAI,UAAU3V,CAAC,EACf,QAASkD,KAAKyS,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGzS,CAAC,IAAG,EAAEA,CAAC,EAAIyS,EAAEzS,CAAC,EAC9E,CACD,OAAO,CACV,EACMwS,EAAS,MAAM,KAAM,SAAS,CACvC,EAEO,SAASE,GAAOD,EAAGpV,EAAG,CAC3B,IAAI4P,EAAI,GACR,QAASjN,KAAKyS,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGzS,CAAC,GAAK3C,EAAE,QAAQ2C,CAAC,EAAI,IAC9EiN,EAAEjN,CAAC,EAAIyS,EAAEzS,CAAC,GACd,GAAIyS,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WACrD,QAAS3V,EAAI,EAAGkD,EAAI,OAAO,sBAAsByS,CAAC,EAAG3V,EAAIkD,EAAE,OAAQlD,IAC3DO,EAAE,QAAQ2C,EAAElD,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAK2V,EAAGzS,EAAElD,CAAC,CAAC,IACzEmQ,EAAEjN,EAAElD,CAAC,CAAC,EAAI2V,EAAEzS,EAAElD,CAAC,CAAC,GAE5B,OAAOmQ,CACT,CAiKO,SAAS0F,GAAcC,EAAIC,EAAMC,EAAM,CAC5C,GAAIA,GAAQ,UAAU,SAAW,EAAG,QAAShW,EAAI,EAAGiW,EAAIF,EAAK,OAAQG,EAAIlW,EAAIiW,EAAGjW,KACxEkW,GAAM,EAAElW,KAAK+V,MACRG,IAAIA,EAAK,MAAM,UAAU,MAAM,KAAKH,EAAM,EAAG/V,CAAC,GACnDkW,EAAGlW,CAAC,EAAI+V,EAAK/V,CAAC,GAGtB,OAAO8V,EAAG,OAAOI,GAAM,MAAM,UAAU,MAAM,KAAKH,CAAI,CAAC,CACzD,CC7NO,IAAII,GACV,SAAUA,EAAW,CAElBA,EAAUA,EAAU,8BAAmC,CAAC,EAAI,gCAE5DA,EAAUA,EAAU,eAAoB,CAAC,EAAI,iBAE7CA,EAAUA,EAAU,mBAAwB,CAAC,EAAI,qBAEjDA,EAAUA,EAAU,qBAA0B,CAAC,EAAI,uBAEnDA,EAAUA,EAAU,sBAA2B,CAAC,EAAI,wBAEpDA,EAAUA,EAAU,sBAA2B,CAAC,EAAI,wBAEpDA,EAAUA,EAAU,wBAA6B,CAAC,EAAI,0BAEtDA,EAAUA,EAAU,2BAAgC,CAAC,EAAI,6BAEzDA,EAAUA,EAAU,uBAA4B,CAAC,EAAI,yBAErDA,EAAUA,EAAU,0BAA+B,EAAE,EAAI,4BAEzDA,EAAUA,EAAU,iCAAsC,EAAE,EAAI,mCAEhEA,EAAUA,EAAU,+BAAoC,EAAE,EAAI,iCAE9DA,EAAUA,EAAU,oCAAyC,EAAE,EAAI,sCAEnEA,EAAUA,EAAU,qCAA0C,EAAE,EAAI,uCAEpEA,EAAUA,EAAU,gCAAqC,EAAE,EAAI,kCAE/DA,EAAUA,EAAU,gCAAqC,EAAE,EAAI,kCAE/DA,EAAUA,EAAU,yCAA8C,EAAE,EAAI,2CAKxEA,EAAUA,EAAU,yCAA8C,EAAE,EAAI,2CAExEA,EAAUA,EAAU,iCAAsC,EAAE,EAAI,mCAKhEA,EAAUA,EAAU,mCAAwC,EAAE,EAAI,qCAIlEA,EAAUA,EAAU,mCAAwC,EAAE,EAAI,qCAElEA,EAAUA,EAAU,qBAA0B,EAAE,EAAI,uBAEpDA,EAAUA,EAAU,YAAiB,EAAE,EAAI,cAE3CA,EAAUA,EAAU,iBAAsB,EAAE,EAAI,mBAEhDA,EAAUA,EAAU,sBAA2B,EAAE,EAAI,wBAErDA,EAAUA,EAAU,aAAkB,EAAE,EAAI,cAChD,GAAGA,IAAcA,EAAY,GAAG,EC9DzB,IAAIC,GACV,SAAUA,EAAM,CAIbA,EAAKA,EAAK,QAAa,CAAC,EAAI,UAI5BA,EAAKA,EAAK,SAAc,CAAC,EAAI,WAI7BA,EAAKA,EAAK,OAAY,CAAC,EAAI,SAI3BA,EAAKA,EAAK,KAAU,CAAC,EAAI,OAIzBA,EAAKA,EAAK,KAAU,CAAC,EAAI,OAIzBA,EAAKA,EAAK,OAAY,CAAC,EAAI,SAI3BA,EAAKA,EAAK,OAAY,CAAC,EAAI,SAK3BA,EAAKA,EAAK,MAAW,CAAC,EAAI,QAI1BA,EAAKA,EAAK,IAAS,CAAC,EAAI,KAC5B,GAAGA,IAASA,EAAO,CAAE,EAAC,EACf,IAAIC,IACV,SAAUA,EAAe,CACtBA,EAAcA,EAAc,OAAY,CAAC,EAAI,SAC7CA,EAAcA,EAAc,SAAc,CAAC,EAAI,UACnD,GAAGA,KAAkBA,GAAgB,CAAE,EAAC,EAIjC,SAASC,GAAiBC,EAAI,CACjC,OAAOA,EAAG,OAASH,EAAK,OAC5B,CACO,SAASI,GAAkBD,EAAI,CAClC,OAAOA,EAAG,OAASH,EAAK,QAC5B,CACO,SAASK,GAAgBF,EAAI,CAChC,OAAOA,EAAG,OAASH,EAAK,MAC5B,CACO,SAASM,GAAcH,EAAI,CAC9B,OAAOA,EAAG,OAASH,EAAK,IAC5B,CACO,SAASO,GAAcJ,EAAI,CAC9B,OAAOA,EAAG,OAASH,EAAK,IAC5B,CACO,SAASQ,GAAgBL,EAAI,CAChC,OAAOA,EAAG,OAASH,EAAK,MAC5B,CACO,SAASS,GAAgBN,EAAI,CAChC,OAAOA,EAAG,OAASH,EAAK,MAC5B,CACO,SAASU,GAAeP,EAAI,CAC/B,OAAOA,EAAG,OAASH,EAAK,KAC5B,CACO,SAASW,GAAaR,EAAI,CAC7B,OAAOA,EAAG,OAASH,EAAK,GAC5B,CACO,SAASY,GAAiBT,EAAI,CACjC,MAAO,CAAC,EAAEA,GAAM,OAAOA,GAAO,UAAYA,EAAG,OAASF,GAAc,OACxE,CACO,SAASY,GAAmBV,EAAI,CACnC,MAAO,CAAC,EAAEA,GAAM,OAAOA,GAAO,UAAYA,EAAG,OAASF,GAAc,SACxE,CC/EO,IAAIa,GAAwB,+CCI/BC,GAAkB,4KAOf,SAASC,GAAsBC,EAAU,CAC5C,IAAIpO,EAAS,GACb,OAAAoO,EAAS,QAAQF,GAAiB,SAAUrM,EAAO,CAC/C,IAAIwM,EAAMxM,EAAM,OAChB,OAAQA,EAAM,CAAC,EAAC,CAEZ,IAAK,IACD7B,EAAO,IAAMqO,IAAQ,EAAI,OAASA,IAAQ,EAAI,SAAW,QACzD,MAEJ,IAAK,IACDrO,EAAO,KAAOqO,IAAQ,EAAI,UAAY,UACtC,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,8DAA8D,EAEvF,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,4CAA4C,EAErE,IAAK,IACL,IAAK,IACDrO,EAAO,MAAQ,CAAC,UAAW,UAAW,QAAS,OAAQ,QAAQ,EAAEqO,EAAM,CAAC,EACxE,MAEJ,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,yCAAyC,EAClE,IAAK,IACDrO,EAAO,IAAM,CAAC,UAAW,SAAS,EAAEqO,EAAM,CAAC,EAC3C,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,2DAA2D,EAEpF,IAAK,IACDrO,EAAO,QAAUqO,IAAQ,EAAI,OAASA,IAAQ,EAAI,SAAW,QAC7D,MACJ,IAAK,IACD,GAAIA,EAAM,EACN,MAAM,IAAI,WAAW,+CAA+C,EAExErO,EAAO,QAAU,CAAC,QAAS,OAAQ,SAAU,OAAO,EAAEqO,EAAM,CAAC,EAC7D,MACJ,IAAK,IACD,GAAIA,EAAM,EACN,MAAM,IAAI,WAAW,+CAA+C,EAExErO,EAAO,QAAU,CAAC,QAAS,OAAQ,SAAU,OAAO,EAAEqO,EAAM,CAAC,EAC7D,MAEJ,IAAK,IACDrO,EAAO,OAAS,GAChB,MACJ,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,4DAA4D,EAErF,IAAK,IACDA,EAAO,UAAY,MACnBA,EAAO,KAAO,CAAC,UAAW,SAAS,EAAEqO,EAAM,CAAC,EAC5C,MACJ,IAAK,IACDrO,EAAO,UAAY,MACnBA,EAAO,KAAO,CAAC,UAAW,SAAS,EAAEqO,EAAM,CAAC,EAC5C,MACJ,IAAK,IACDrO,EAAO,UAAY,MACnBA,EAAO,KAAO,CAAC,UAAW,SAAS,EAAEqO,EAAM,CAAC,EAC5C,MACJ,IAAK,IACDrO,EAAO,UAAY,MACnBA,EAAO,KAAO,CAAC,UAAW,SAAS,EAAEqO,EAAM,CAAC,EAC5C,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,kEAAkE,EAE3F,IAAK,IACDrO,EAAO,OAAS,CAAC,UAAW,SAAS,EAAEqO,EAAM,CAAC,EAC9C,MAEJ,IAAK,IACDrO,EAAO,OAAS,CAAC,UAAW,SAAS,EAAEqO,EAAM,CAAC,EAC9C,MACJ,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,4DAA4D,EAErF,IAAK,IACDrO,EAAO,aAAeqO,EAAM,EAAI,QAAU,OAC1C,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,sEAAsE,CAClG,CACD,MAAO,EACf,CAAK,EACMrO,CACX,CCvHO,IAAIsO,GAAoB,wCCCxB,SAASC,GAA8BH,EAAU,CACpD,GAAIA,EAAS,SAAW,EACpB,MAAM,IAAI,MAAM,iCAAiC,EAOrD,QAJII,EAAeJ,EACd,MAAME,EAAiB,EACvB,OAAO,SAAUlG,EAAG,CAAE,OAAOA,EAAE,OAAS,CAAE,CAAE,EAC7CqG,EAAS,GACJC,EAAK,EAAGC,EAAiBH,EAAcE,EAAKC,EAAe,OAAQD,IAAM,CAC9E,IAAIE,EAAcD,EAAeD,CAAE,EAC/BG,EAAiBD,EAAY,MAAM,GAAG,EAC1C,GAAIC,EAAe,SAAW,EAC1B,MAAM,IAAI,MAAM,yBAAyB,EAG7C,QADIC,EAAOD,EAAe,CAAC,EAAG1N,EAAU0N,EAAe,MAAM,CAAC,EACrDE,EAAK,EAAGC,EAAY7N,EAAS4N,EAAKC,EAAU,OAAQD,IAAM,CAC/D,IAAIE,EAASD,EAAUD,CAAE,EACzB,GAAIE,EAAO,SAAW,EAClB,MAAM,IAAI,MAAM,yBAAyB,CAEhD,CACDR,EAAO,KAAK,CAAE,KAAMK,EAAM,QAAS3N,CAAO,CAAE,CAC/C,CACD,OAAOsN,CACX,CACA,SAASS,GAAcC,EAAM,CACzB,OAAOA,EAAK,QAAQ,UAAW,EAAE,CACrC,CACA,IAAIC,GAA2B,mCAC3BC,GAA8B,wBAC9BC,GAAsB,0BACtBC,GAA8B,SAClC,SAASC,GAA0BC,EAAK,CACpC,IAAIzP,EAAS,GACb,OAAIyP,EAAIA,EAAI,OAAS,CAAC,IAAM,IACxBzP,EAAO,iBAAmB,gBAErByP,EAAIA,EAAI,OAAS,CAAC,IAAM,MAC7BzP,EAAO,iBAAmB,iBAE9ByP,EAAI,QAAQJ,GAA6B,SAAUnS,EAAGwS,EAAIC,EAAI,CAE1D,OAAI,OAAOA,GAAO,UACd3P,EAAO,yBAA2B0P,EAAG,OACrC1P,EAAO,yBAA2B0P,EAAG,QAGhCC,IAAO,IACZ3P,EAAO,yBAA2B0P,EAAG,OAGhCA,EAAG,CAAC,IAAM,IACf1P,EAAO,yBAA2B0P,EAAG,QAIrC1P,EAAO,yBAA2B0P,EAAG,OACrC1P,EAAO,yBACH0P,EAAG,QAAU,OAAOC,GAAO,SAAWA,EAAG,OAAS,IAEnD,EACf,CAAK,EACM3P,CACX,CACA,SAAS4P,GAAUH,EAAK,CACpB,OAAQA,EAAG,CACP,IAAK,YACD,MAAO,CACH,YAAa,MAC7B,EACQ,IAAK,kBACL,IAAK,KACD,MAAO,CACH,aAAc,YAC9B,EACQ,IAAK,cACL,IAAK,KACD,MAAO,CACH,YAAa,QAC7B,EACQ,IAAK,yBACL,IAAK,MACD,MAAO,CACH,YAAa,SACb,aAAc,YAC9B,EACQ,IAAK,mBACL,IAAK,KACD,MAAO,CACH,YAAa,YAC7B,EACQ,IAAK,8BACL,IAAK,MACD,MAAO,CACH,YAAa,aACb,aAAc,YAC9B,EACQ,IAAK,aACL,IAAK,KACD,MAAO,CACH,YAAa,OAC7B,CACK,CACL,CACA,SAASI,GAAyCf,EAAM,CAEpD,IAAI9O,EAaJ,GAZI8O,EAAK,CAAC,IAAM,KAAOA,EAAK,CAAC,IAAM,KAC/B9O,EAAS,CACL,SAAU,aACtB,EACQ8O,EAAOA,EAAK,MAAM,CAAC,GAEdA,EAAK,CAAC,IAAM,MACjB9O,EAAS,CACL,SAAU,YACtB,EACQ8O,EAAOA,EAAK,MAAM,CAAC,GAEnB9O,EAAQ,CACR,IAAI8P,EAAchB,EAAK,MAAM,EAAG,CAAC,EASjC,GARIgB,IAAgB,MAChB9P,EAAO,YAAc,SACrB8O,EAAOA,EAAK,MAAM,CAAC,GAEdgB,IAAgB,OACrB9P,EAAO,YAAc,aACrB8O,EAAOA,EAAK,MAAM,CAAC,GAEnB,CAACS,GAA4B,KAAKT,CAAI,EACtC,MAAM,IAAI,MAAM,2CAA2C,EAE/D9O,EAAO,qBAAuB8O,EAAK,MACtC,CACD,OAAO9O,CACX,CACA,SAAS+P,GAAqBC,EAAK,CAC/B,IAAIhQ,EAAS,GACTiQ,EAAWL,GAAUI,CAAG,EAC5B,OAAIC,GAGGjQ,CACX,CAIO,SAASkQ,GAAoBzB,EAAQ,CAExC,QADIzO,EAAS,GACJ0O,EAAK,EAAGyB,EAAW1B,EAAQC,EAAKyB,EAAS,OAAQzB,IAAM,CAC5D,IAAI5Y,EAAQqa,EAASzB,CAAE,EACvB,OAAQ5Y,EAAM,KAAI,CACd,IAAK,UACL,IAAK,IACDkK,EAAO,MAAQ,UACf,SACJ,IAAK,QACDA,EAAO,MAAQ,UACfA,EAAO,MAAQ,IACf,SACJ,IAAK,WACDA,EAAO,MAAQ,WACfA,EAAO,SAAWlK,EAAM,QAAQ,CAAC,EACjC,SACJ,IAAK,YACL,IAAK,KACDkK,EAAO,YAAc,GACrB,SACJ,IAAK,oBACL,IAAK,IACDA,EAAO,sBAAwB,EAC/B,SACJ,IAAK,eACL,IAAK,OACDA,EAAO,MAAQ,OACfA,EAAO,KAAOkP,GAAcpZ,EAAM,QAAQ,CAAC,CAAC,EAC5C,SACJ,IAAK,gBACL,IAAK,IACDkK,EAAO,SAAW,UAClBA,EAAO,eAAiB,QACxB,SACJ,IAAK,eACL,IAAK,KACDA,EAAO,SAAW,UAClBA,EAAO,eAAiB,OACxB,SACJ,IAAK,aACDA,EAASyM,EAASA,EAASA,EAAS,CAAE,EAAEzM,CAAM,EAAG,CAAE,SAAU,YAAc,GAAGlK,EAAM,QAAQ,OAAO,SAAUsa,EAAKJ,EAAK,CAAE,OAAQvD,EAASA,EAAS,GAAI2D,CAAG,EAAGL,GAAqBC,CAAG,CAAC,CAAG,EAAI,EAAE,CAAC,EAChM,SACJ,IAAK,cACDhQ,EAASyM,EAASA,EAASA,EAAS,CAAE,EAAEzM,CAAM,EAAG,CAAE,SAAU,aAAe,GAAGlK,EAAM,QAAQ,OAAO,SAAUsa,EAAKJ,EAAK,CAAE,OAAQvD,EAASA,EAAS,GAAI2D,CAAG,EAAGL,GAAqBC,CAAG,CAAC,CAAG,EAAI,EAAE,CAAC,EACjM,SACJ,IAAK,kBACDhQ,EAAO,SAAW,WAClB,SAEJ,IAAK,oBACDA,EAAO,gBAAkB,eACzBA,EAAO,YAAc,SACrB,SACJ,IAAK,mBACDA,EAAO,gBAAkB,OACzBA,EAAO,YAAc,QACrB,SACJ,IAAK,uBACDA,EAAO,gBAAkB,OACzBA,EAAO,YAAc,OACrB,SACJ,IAAK,sBACDA,EAAO,gBAAkB,SACzB,SACJ,IAAK,QACDA,EAAO,MAAQ,WAAWlK,EAAM,QAAQ,CAAC,CAAC,EAC1C,SACJ,IAAK,sBACDkK,EAAO,aAAe,QACtB,SACJ,IAAK,wBACDA,EAAO,aAAe,OACtB,SACJ,IAAK,qBACDA,EAAO,aAAe,QACtB,SACJ,IAAK,mBACDA,EAAO,aAAe,SACtB,SACJ,IAAK,0BACDA,EAAO,aAAe,WACtB,SACJ,IAAK,0BACDA,EAAO,aAAe,YACtB,SACJ,IAAK,wBACDA,EAAO,aAAe,aACtB,SAEJ,IAAK,gBACD,GAAIlK,EAAM,QAAQ,OAAS,EACvB,MAAM,IAAI,WAAW,0DAA0D,EAEnFA,EAAM,QAAQ,CAAC,EAAE,QAAQwZ,GAAqB,SAAUpS,EAAGwS,EAAIC,EAAIU,EAAIC,EAAIC,EAAI,CAC3E,GAAIb,EACA1P,EAAO,qBAAuB2P,EAAG,WAEhC,IAAIU,GAAMC,EACX,MAAM,IAAI,MAAM,oDAAoD,EAEnE,GAAIC,EACL,MAAM,IAAI,MAAM,kDAAkD,EAEtE,MAAO,EAC3B,CAAiB,EACD,QACP,CAED,GAAIhB,GAA4B,KAAKzZ,EAAM,IAAI,EAAG,CAC9CkK,EAAO,qBAAuBlK,EAAM,KAAK,OACzC,QACH,CACD,GAAIsZ,GAAyB,KAAKtZ,EAAM,IAAI,EAAG,CAI3C,GAAIA,EAAM,QAAQ,OAAS,EACvB,MAAM,IAAI,WAAW,+DAA+D,EAExFA,EAAM,KAAK,QAAQsZ,GAA0B,SAAUlS,EAAGwS,EAAIC,EAAIU,EAAIC,EAAIC,EAAI,CAE1E,OAAIZ,IAAO,IACP3P,EAAO,sBAAwB0P,EAAG,OAG7BW,GAAMA,EAAG,CAAC,IAAM,IACrBrQ,EAAO,sBAAwBqQ,EAAG,OAG7BC,GAAMC,GACXvQ,EAAO,sBAAwBsQ,EAAG,OAClCtQ,EAAO,sBAAwBsQ,EAAG,OAASC,EAAG,SAG9CvQ,EAAO,sBAAwB0P,EAAG,OAClC1P,EAAO,sBAAwB0P,EAAG,QAE/B,EACvB,CAAa,EACD,IAAIM,EAAMla,EAAM,QAAQ,CAAC,EAErBka,IAAQ,IACRhQ,EAASyM,EAASA,EAAS,CAAE,EAAEzM,CAAM,EAAG,CAAE,oBAAqB,gBAAgB,CAAE,EAE5EgQ,IACLhQ,EAASyM,EAASA,EAAS,CAAE,EAAEzM,CAAM,EAAGwP,GAA0BQ,CAAG,CAAC,GAE1E,QACH,CAED,GAAIX,GAA4B,KAAKvZ,EAAM,IAAI,EAAG,CAC9CkK,EAASyM,EAASA,EAAS,GAAIzM,CAAM,EAAGwP,GAA0B1Z,EAAM,IAAI,CAAC,EAC7E,QACH,CACD,IAAIma,EAAWL,GAAU9Z,EAAM,IAAI,EAC/Bma,IACAjQ,EAASyM,EAASA,EAAS,CAAE,EAAEzM,CAAM,EAAGiQ,CAAQ,GAEpD,IAAIO,EAAsCX,GAAyC/Z,EAAM,IAAI,EACzF0a,IACAxQ,EAASyM,EAASA,EAAS,CAAE,EAAEzM,CAAM,EAAGwQ,CAAmC,EAElF,CACD,OAAOxQ,CACX,CCzTO,IAAIyQ,GAAW,CAClB,MAAO,CACH,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,KACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,GACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,KACA,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,SAAU,CACN,IACA,KACA,KACA,GACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,SAAU,CACN,IACA,KACA,IACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,QAAS,CACL,KACA,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,IACA,GACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,QAAS,CACL,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,IACA,KACA,GACH,EACD,QAAS,CACL,KACA,IACA,GACH,EACD,QAAS,CACL,IACA,KACA,KACA,GACH,CACL,EC13CO,SAASC,GAAetC,EAAUuC,EAAQ,CAE7C,QADIC,EAAe,GACVC,EAAa,EAAGA,EAAazC,EAAS,OAAQyC,IAAc,CACjE,IAAIC,EAAc1C,EAAS,OAAOyC,CAAU,EAC5C,GAAIC,IAAgB,IAAK,CAErB,QADIC,EAAc,EACXF,EAAa,EAAIzC,EAAS,QAC7BA,EAAS,OAAOyC,EAAa,CAAC,IAAMC,GACpCC,IACAF,IAEJ,IAAIG,EAAU,GAAKD,EAAc,GAC7BE,EAAeF,EAAc,EAAI,EAAI,GAAKA,GAAe,GACzDG,EAAgB,IAChBC,EAAWC,GAA+BT,CAAM,EAIpD,KAHIQ,GAAY,KAAOA,GAAY,OAC/BF,EAAe,GAEZA,KAAiB,GACpBL,GAAgBM,EAEpB,KAAOF,KAAY,GACfJ,EAAeO,EAAWP,CAEjC,MACQE,IAAgB,IACrBF,GAAgB,IAGhBA,GAAgBE,CAEvB,CACD,OAAOF,CACX,CAMA,SAASQ,GAA+BT,EAAQ,CAC5C,IAAIU,EAAYV,EAAO,UASvB,GARIU,IAAc,QAEdV,EAAO,YAEPA,EAAO,WAAW,SAElBU,EAAYV,EAAO,WAAW,CAAC,GAE/BU,EACA,OAAQA,EAAS,CACb,IAAK,MACD,MAAO,IACX,IAAK,MACD,MAAO,IACX,IAAK,MACD,MAAO,IACX,IAAK,MACD,MAAO,IACX,QACI,MAAM,IAAI,MAAM,mBAAmB,CAC1C,CAGL,IAAIC,EAAcX,EAAO,SACrBY,EACAD,IAAgB,SAChBC,EAAYZ,EAAO,SAAU,EAAC,QAElC,IAAIa,EAAaf,GAASc,GAAa,EAAE,GACrCd,GAASa,GAAe,EAAE,GAC1Bb,GAAS,GAAG,OAAOa,EAAa,MAAM,CAAC,GACvCb,GAAS,KAAK,EAClB,OAAOe,EAAW,CAAC,CACvB,CClFA,IAAIzC,GAOA0C,GAA8B,IAAI,OAAO,IAAI,OAAOxD,GAAsB,OAAQ,GAAG,CAAC,EACtFyD,GAA4B,IAAI,OAAO,GAAG,OAAOzD,GAAsB,OAAQ,IAAI,CAAC,EACxF,SAAS0D,EAAezI,EAAO0I,EAAK,CAChC,MAAO,CAAE,MAAO1I,EAAO,IAAK0I,CAAG,CACnC,CAGA,IAAIC,GAAsB,CAAC,CAAC,OAAO,UAAU,YAAc,KAAK,WAAW,IAAK,CAAC,EAC7EC,GAAyB,CAAC,CAAC,OAAO,cAClCC,GAAuB,CAAC,CAAC,OAAO,YAChCC,GAAuB,CAAC,CAAC,OAAO,UAAU,YAC1CC,GAAe,CAAC,CAAC,OAAO,UAAU,UAClCC,GAAa,CAAC,CAAC,OAAO,UAAU,QAChCC,GAAyB,CAAC,CAAC,OAAO,cAClCC,GAAgBD,GACd,OAAO,cACP,SAAUjT,EAAG,CACX,OAAQ,OAAOA,GAAM,UACjB,SAASA,CAAC,GACV,KAAK,MAAMA,CAAC,IAAMA,GAClB,KAAK,IAAIA,CAAC,GAAK,gBAC3B,EAEImT,GAAyB,GAC7B,GAAI,CACA,IAAIC,GAAKC,GAAG,4CAA6C,IAAI,EAO7DF,KAA2BtD,GAAKuD,GAAG,KAAK,GAAG,KAAO,MAAQvD,KAAO,OAAS,OAASA,GAAG,CAAC,KAAO,GAClG,MACU,CACNsD,GAAyB,EAC7B,CACA,IAAIG,GAAaX,GAET,SAAoBnF,EAAG+F,EAAQC,EAAU,CACrC,OAAOhG,EAAE,WAAW+F,EAAQC,CAAQ,CACvC,EAED,SAAoBhG,EAAG+F,EAAQC,EAAU,CACrC,OAAOhG,EAAE,MAAMgG,EAAUA,EAAWD,EAAO,MAAM,IAAMA,CACnE,EACIE,GAAgBb,GACd,OAAO,cAEL,UAAyB,CAErB,QADIc,EAAa,GACRlE,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpCkE,EAAWlE,CAAE,EAAI,UAAUA,CAAE,EAMjC,QAJImE,EAAW,GACXC,EAASF,EAAW,OACpB,EAAI,EACJG,EACGD,EAAS,GAAG,CAEf,GADAC,EAAOH,EAAW,GAAG,EACjBG,EAAO,QACP,MAAM,WAAWA,EAAO,4BAA4B,EACxDF,GACIE,EAAO,MACD,OAAO,aAAaA,CAAI,EACxB,OAAO,eAAeA,GAAQ,QAAY,IAAM,MAASA,EAAO,KAAS,KAAM,CAC5F,CACD,OAAOF,CACnB,EACIG,GAEJjB,GACM,OAAO,YAEL,SAAqBkB,EAAS,CAE1B,QADI3O,EAAM,GACDoK,EAAK,EAAGwE,EAAYD,EAASvE,EAAKwE,EAAU,OAAQxE,IAAM,CAC/D,IAAIK,EAAKmE,EAAUxE,CAAE,EAAGyE,EAAIpE,EAAG,CAAC,EAAG1N,EAAI0N,EAAG,CAAC,EAC3CzK,EAAI6O,CAAC,EAAI9R,CACZ,CACD,OAAOiD,CACnB,EACI8O,GAAcpB,GAEV,SAAqBtF,EAAG/R,EAAO,CAC3B,OAAO+R,EAAE,YAAY/R,CAAK,CAC7B,EAED,SAAqB+R,EAAG/R,EAAO,CAC3B,IAAIyB,EAAOsQ,EAAE,OACb,GAAI,EAAA/R,EAAQ,GAAKA,GAASyB,GAG1B,KAAIiX,EAAQ3G,EAAE,WAAW/R,CAAK,EAC1B2Y,EACJ,OAAOD,EAAQ,OACXA,EAAQ,OACR1Y,EAAQ,IAAMyB,IACbkX,EAAS5G,EAAE,WAAW/R,EAAQ,CAAC,GAAK,OACrC2Y,EAAS,MACPD,GACEA,EAAQ,OAAW,KAAOC,EAAS,OAAU,MACjE,EACIC,GAAYtB,GAER,SAAmBvF,EAAG,CAClB,OAAOA,EAAE,WACZ,EAED,SAAmBA,EAAG,CAClB,OAAOA,EAAE,QAAQ+E,GAA6B,EAAE,CAC5D,EACI+B,GAAUtB,GAEN,SAAiBxF,EAAG,CAChB,OAAOA,EAAE,SACZ,EAED,SAAiBA,EAAG,CAChB,OAAOA,EAAE,QAAQgF,GAA2B,EAAE,CAC1D,EAEA,SAASa,GAAG7F,EAAG+G,EAAM,CACjB,OAAO,IAAI,OAAO/G,EAAG+G,CAAI,CAC7B,CAEA,IAAIC,GACJ,GAAIrB,GAAwB,CAExB,IAAIsB,GAAyBpB,GAAG,4CAA6C,IAAI,EACjFmB,GAAyB,SAAgChH,EAAG/R,EAAO,CAC/D,IAAIoU,EACJ4E,GAAuB,UAAYhZ,EACnC,IAAIkH,EAAQ8R,GAAuB,KAAKjH,CAAC,EACzC,OAAQqC,EAAKlN,EAAM,CAAC,KAAO,MAAQkN,IAAO,OAASA,EAAK,EAChE,CACA,MAGI2E,GAAyB,SAAgChH,EAAG/R,EAAO,CAE/D,QADIkH,EAAQ,KACC,CACT,IAAIvI,EAAI8Z,GAAY1G,EAAG/R,CAAK,EAC5B,GAAIrB,IAAM,QAAasa,GAActa,CAAC,GAAKua,GAAiBva,CAAC,EACzD,MAEJuI,EAAM,KAAKvI,CAAC,EACZqB,GAASrB,GAAK,MAAU,EAAI,CAC/B,CACD,OAAOqZ,GAAc,MAAM,OAAQ9Q,CAAK,CAChD,EAEA,IAAIiS,GAAwB,UAAY,CACpC,SAASA,EAAOtW,EAAS2D,EAAS,CAC1BA,IAAY,SAAUA,EAAU,CAAE,GACtC,KAAK,QAAU3D,EACf,KAAK,SAAW,CAAE,OAAQ,EAAG,KAAM,EAAG,OAAQ,GAC9C,KAAK,UAAY,CAAC,CAAC2D,EAAQ,UAC3B,KAAK,OAASA,EAAQ,OACtB,KAAK,oBAAsB,CAAC,CAACA,EAAQ,oBACrC,KAAK,qBAAuB,CAAC,CAACA,EAAQ,oBACzC,CACD,OAAA2S,EAAO,UAAU,MAAQ,UAAY,CACjC,GAAI,KAAK,OAAQ,IAAK,EAClB,MAAM,MAAM,8BAA8B,EAE9C,OAAO,KAAK,aAAa,EAAG,GAAI,EAAK,CAC7C,EACIA,EAAO,UAAU,aAAe,SAAUC,EAAcC,EAAeC,EAAmB,CAEtF,QADIpB,EAAW,GACR,CAAC,KAAK,SAAS,CAClB,IAAIqB,EAAO,KAAK,OAChB,GAAIA,IAAS,IAAe,CACxB,IAAIlU,EAAS,KAAK,cAAc+T,EAAcE,CAAiB,EAC/D,GAAIjU,EAAO,IACP,OAAOA,EAEX6S,EAAS,KAAK7S,EAAO,GAAG,CAC3B,KACI,IAAIkU,IAAS,KAAiBH,EAAe,EAC9C,MAEC,GAAIG,IAAS,KACbF,IAAkB,UAAYA,IAAkB,iBAAkB,CACnE,IAAItB,EAAW,KAAK,gBACpB,KAAK,KAAI,EACTG,EAAS,KAAK,CACV,KAAM1F,EAAK,MACX,SAAUwE,EAAee,EAAU,KAAK,cAAa,CAAE,CAC3E,CAAiB,CACJ,SACQwB,IAAS,IACd,CAAC,KAAK,WACN,KAAK,KAAI,IAAO,GAClB,CACE,GAAID,EACA,MAGA,OAAO,KAAK,MAAM/G,EAAU,sBAAuByE,EAAe,KAAK,gBAAiB,KAAK,cAAa,CAAE,CAAC,CAEpH,SACQuC,IAAS,IACd,CAAC,KAAK,WACNC,GAAS,KAAK,KAAM,GAAI,CAAC,EAAG,CAC5B,IAAInU,EAAS,KAAK,SAAS+T,EAAcC,CAAa,EACtD,GAAIhU,EAAO,IACP,OAAOA,EAEX6S,EAAS,KAAK7S,EAAO,GAAG,CAC3B,KACI,CACD,IAAIA,EAAS,KAAK,aAAa+T,EAAcC,CAAa,EAC1D,GAAIhU,EAAO,IACP,OAAOA,EAEX6S,EAAS,KAAK7S,EAAO,GAAG,CAC3B,EACJ,CACD,MAAO,CAAE,IAAK6S,EAAU,IAAK,IAAI,CACzC,EAmBIiB,EAAO,UAAU,SAAW,SAAUC,EAAcC,EAAe,CAC/D,IAAII,EAAgB,KAAK,gBACzB,KAAK,KAAI,EACT,IAAIC,EAAU,KAAK,eAEnB,GADA,KAAK,UAAS,EACV,KAAK,OAAO,IAAI,EAEhB,MAAO,CACH,IAAK,CACD,KAAMlH,EAAK,QACX,MAAO,IAAI,OAAOkH,EAAS,IAAI,EAC/B,SAAU1C,EAAeyC,EAAe,KAAK,cAAa,CAAE,CAC/D,EACD,IAAK,IACrB,EAEa,GAAI,KAAK,OAAO,GAAG,EAAG,CACvB,IAAIE,EAAiB,KAAK,aAAaP,EAAe,EAAGC,EAAe,EAAI,EAC5E,GAAIM,EAAe,IACf,OAAOA,EAEX,IAAIC,EAAWD,EAAe,IAE1BE,EAAsB,KAAK,gBAC/B,GAAI,KAAK,OAAO,IAAI,EAAG,CACnB,GAAI,KAAK,SAAW,CAACL,GAAS,KAAK,KAAI,CAAE,EACrC,OAAO,KAAK,MAAMjH,EAAU,YAAayE,EAAe6C,EAAqB,KAAK,cAAe,EAAC,EAEtG,IAAIC,EAA8B,KAAK,gBACnCC,EAAiB,KAAK,eAC1B,OAAIL,IAAYK,EACL,KAAK,MAAMxH,EAAU,sBAAuByE,EAAe8C,EAA6B,KAAK,cAAe,EAAC,GAExH,KAAK,UAAS,EACT,KAAK,OAAO,GAAG,EAGb,CACH,IAAK,CACD,KAAMtH,EAAK,IACX,MAAOkH,EACP,SAAUE,EACV,SAAU5C,EAAeyC,EAAe,KAAK,cAAa,CAAE,CAC/D,EACD,IAAK,IACzB,EAV2B,KAAK,MAAMlH,EAAU,YAAayE,EAAe6C,EAAqB,KAAK,cAAe,EAAC,EAWzG,KAEG,QAAO,KAAK,MAAMtH,EAAU,aAAcyE,EAAeyC,EAAe,KAAK,cAAe,EAAC,CAEpG,KAEG,QAAO,KAAK,MAAMlH,EAAU,YAAayE,EAAeyC,EAAe,KAAK,cAAe,EAAC,CAExG,EAIIN,EAAO,UAAU,aAAe,UAAY,CACxC,IAAIa,EAAc,KAAK,SAEvB,IADA,KAAK,KAAI,EACF,CAAC,KAAK,MAAO,GAAIC,GAA4B,KAAK,KAAI,CAAE,GAC3D,KAAK,KAAI,EAEb,OAAO,KAAK,QAAQ,MAAMD,EAAa,KAAK,OAAM,CAAE,CAC5D,EACIb,EAAO,UAAU,aAAe,SAAUC,EAAcC,EAAe,CAGnE,QAFI9K,EAAQ,KAAK,gBACbrO,EAAQ,KACC,CACT,IAAIga,EAAmB,KAAK,cAAcb,CAAa,EACvD,GAAIa,EAAkB,CAClBha,GAASga,EACT,QACH,CACD,IAAIC,EAAsB,KAAK,iBAAiBf,EAAcC,CAAa,EAC3E,GAAIc,EAAqB,CACrBja,GAASia,EACT,QACH,CACD,IAAIC,EAAuB,KAAK,2BAChC,GAAIA,EAAsB,CACtBla,GAASka,EACT,QACH,CACD,KACH,CACD,IAAIC,EAAWrD,EAAezI,EAAO,KAAK,cAAe,GACzD,MAAO,CACH,IAAK,CAAE,KAAMiE,EAAK,QAAS,MAAOtS,EAAO,SAAUma,CAAU,EAC7D,IAAK,IACjB,CACA,EACIlB,EAAO,UAAU,yBAA2B,UAAY,CACpD,MAAI,CAAC,KAAK,MAAO,GACb,KAAK,KAAI,IAAO,KACf,KAAK,WAEF,CAACmB,GAAgB,KAAK,KAAI,GAAM,CAAC,IACrC,KAAK,KAAI,EACF,KAEJ,IACf,EAMInB,EAAO,UAAU,cAAgB,SAAUE,EAAe,CACtD,GAAI,KAAK,MAAO,GAAI,KAAK,KAAI,IAAO,GAChC,OAAO,KAIX,OAAQ,KAAK,KAAM,GACf,IAAK,IAED,YAAK,KAAI,EACT,KAAK,KAAI,EACF,IAEX,IAAK,KACL,IAAK,IACL,IAAK,IACL,IAAK,KACD,MACJ,IAAK,IACD,GAAIA,IAAkB,UAAYA,IAAkB,gBAChD,MAEJ,OAAO,KACX,QACI,OAAO,IACd,CACD,KAAK,KAAI,EACT,IAAIpB,EAAa,CAAC,KAAK,KAAM,GAG7B,IAFA,KAAK,KAAI,EAEF,CAAC,KAAK,SAAS,CAClB,IAAIsC,EAAK,KAAK,OACd,GAAIA,IAAO,GACP,GAAI,KAAK,KAAM,IAAK,GAChBtC,EAAW,KAAK,EAAE,EAElB,KAAK,KAAI,MAER,CAED,KAAK,KAAI,EACT,KACH,MAGDA,EAAW,KAAKsC,CAAE,EAEtB,KAAK,KAAI,CACZ,CACD,OAAOvC,GAAc,MAAM,OAAQC,CAAU,CACrD,EACIkB,EAAO,UAAU,iBAAmB,SAAUC,EAAcC,EAAe,CACvE,GAAI,KAAK,QACL,OAAO,KAEX,IAAIkB,EAAK,KAAK,OACd,OAAIA,IAAO,IACPA,IAAO,KACNA,IAAO,KACHlB,IAAkB,UAAYA,IAAkB,kBACpDkB,IAAO,KAAiBnB,EAAe,EACjC,MAGP,KAAK,KAAI,EACFpB,GAAcuC,CAAE,EAEnC,EACIpB,EAAO,UAAU,cAAgB,SAAUC,EAAcE,EAAmB,CACxE,IAAIkB,EAAuB,KAAK,gBAGhC,GAFA,KAAK,KAAI,EACT,KAAK,UAAS,EACV,KAAK,QACL,OAAO,KAAK,MAAMjI,EAAU,8BAA+ByE,EAAewD,EAAsB,KAAK,cAAe,EAAC,EAEzH,GAAI,KAAK,KAAM,IAAK,IAChB,YAAK,KAAI,EACF,KAAK,MAAMjI,EAAU,eAAgByE,EAAewD,EAAsB,KAAK,cAAe,EAAC,EAG1G,IAAIta,EAAQ,KAAK,0BAAyB,EAAG,MAC7C,GAAI,CAACA,EACD,OAAO,KAAK,MAAMqS,EAAU,mBAAoByE,EAAewD,EAAsB,KAAK,cAAe,EAAC,EAG9G,GADA,KAAK,UAAS,EACV,KAAK,QACL,OAAO,KAAK,MAAMjI,EAAU,8BAA+ByE,EAAewD,EAAsB,KAAK,cAAe,EAAC,EAEzH,OAAQ,KAAK,KAAM,GAEf,IAAK,KACD,YAAK,KAAI,EACF,CACH,IAAK,CACD,KAAMhI,EAAK,SAEX,MAAOtS,EACP,SAAU8W,EAAewD,EAAsB,KAAK,cAAa,CAAE,CACtE,EACD,IAAK,IACzB,EAGY,IAAK,IAGD,OAFA,KAAK,KAAI,EACT,KAAK,UAAS,EACV,KAAK,QACE,KAAK,MAAMjI,EAAU,8BAA+ByE,EAAewD,EAAsB,KAAK,cAAe,EAAC,EAElH,KAAK,qBAAqBpB,EAAcE,EAAmBpZ,EAAOsa,CAAoB,EAEjG,QACI,OAAO,KAAK,MAAMjI,EAAU,mBAAoByE,EAAewD,EAAsB,KAAK,cAAe,EAAC,CACjH,CACT,EAKIrB,EAAO,UAAU,0BAA4B,UAAY,CACrD,IAAIsB,EAAmB,KAAK,gBACxBT,EAAc,KAAK,SACnB9Z,EAAQ6Y,GAAuB,KAAK,QAASiB,CAAW,EACxDU,EAAYV,EAAc9Z,EAAM,OACpC,KAAK,OAAOwa,CAAS,EACrB,IAAIC,EAAc,KAAK,gBACnBN,EAAWrD,EAAeyD,EAAkBE,CAAW,EAC3D,MAAO,CAAE,MAAOza,EAAO,SAAUma,CAAQ,CACjD,EACIlB,EAAO,UAAU,qBAAuB,SAAUC,EAAcE,EAAmBpZ,EAAOsa,EAAsB,CAC5G,IAAIpG,EAIAwG,EAAoB,KAAK,gBACzBC,EAAU,KAAK,0BAAyB,EAAG,MAC3CC,EAAkB,KAAK,gBAC3B,OAAQD,EAAO,CACX,IAAK,GAED,OAAO,KAAK,MAAMtI,EAAU,qBAAsByE,EAAe4D,EAAmBE,CAAe,CAAC,EACxG,IAAK,SACL,IAAK,OACL,IAAK,OAAQ,CAIT,KAAK,UAAS,EACd,IAAIC,EAAmB,KACvB,GAAI,KAAK,OAAO,GAAG,EAAG,CAClB,KAAK,UAAS,EACd,IAAIC,EAAqB,KAAK,gBAC1B3V,EAAS,KAAK,gCAClB,GAAIA,EAAO,IACP,OAAOA,EAEX,IAAI4V,EAAQpC,GAAQxT,EAAO,GAAG,EAC9B,GAAI4V,EAAM,SAAW,EACjB,OAAO,KAAK,MAAM1I,EAAU,sBAAuByE,EAAe,KAAK,gBAAiB,KAAK,cAAa,CAAE,CAAC,EAEjH,IAAIkE,EAAgBlE,EAAegE,EAAoB,KAAK,cAAe,GAC3ED,EAAmB,CAAE,MAAOE,EAAO,cAAeC,CAAa,CAClE,CACD,IAAIC,EAAiB,KAAK,sBAAsBX,CAAoB,EACpE,GAAIW,EAAe,IACf,OAAOA,EAEX,IAAIC,EAAapE,EAAewD,EAAsB,KAAK,cAAe,GAE1E,GAAIO,GAAoBlD,GAA+EkD,GAAiB,MAAO,KAAM,CAAC,EAAG,CAErI,IAAItH,EAAWmF,GAAUmC,EAAiB,MAAM,MAAM,CAAC,CAAC,EACxD,GAAIF,IAAY,SAAU,CACtB,IAAIxV,EAAS,KAAK,8BAA8BoO,EAAUsH,EAAiB,aAAa,EACxF,OAAI1V,EAAO,IACAA,EAEJ,CACH,IAAK,CAAE,KAAMmN,EAAK,OAAQ,MAAOtS,EAAO,SAAUkb,EAAY,MAAO/V,EAAO,GAAK,EACjF,IAAK,IACjC,CACqB,KACI,CACD,GAAIoO,EAAS,SAAW,EACpB,OAAO,KAAK,MAAMlB,EAAU,0BAA2B6I,CAAU,EAErE,IAAIC,EAAkB5H,EAIlB,KAAK,SACL4H,EAAkBtF,GAAetC,EAAU,KAAK,MAAM,GAE1D,IAAIwH,EAAQ,CACR,KAAMxI,GAAc,SACpB,QAAS4I,EACT,SAAUN,EAAiB,cAC3B,cAAe,KAAK,qBACdvH,GAAsB6H,CAAe,EACrC,CAAE,CACpC,EAC4B7b,EAAOqb,IAAY,OAASrI,EAAK,KAAOA,EAAK,KACjD,MAAO,CACH,IAAK,CAAE,KAAMhT,EAAM,MAAOU,EAAO,SAAUkb,EAAY,MAAOH,CAAO,EACrE,IAAK,IACjC,CACqB,CACJ,CAED,MAAO,CACH,IAAK,CACD,KAAMJ,IAAY,SACZrI,EAAK,OACLqI,IAAY,OACRrI,EAAK,KACLA,EAAK,KACf,MAAOtS,EACP,SAAUkb,EACV,OAAQhH,EAAyE2G,GAAiB,SAAW,MAAQ3G,IAAO,OAASA,EAAK,IAC7I,EACD,IAAK,IACzB,CACa,CACD,IAAK,SACL,IAAK,gBACL,IAAK,SAAU,CAIX,IAAIkH,EAAoB,KAAK,gBAE7B,GADA,KAAK,UAAS,EACV,CAAC,KAAK,OAAO,GAAG,EAChB,OAAO,KAAK,MAAM/I,EAAU,+BAAgCyE,EAAesE,EAAmBxJ,EAAS,GAAIwJ,CAAiB,CAAC,CAAC,EAElI,KAAK,UAAS,EASd,IAAIC,EAAwB,KAAK,4BAC7BC,EAAe,EACnB,GAAIX,IAAY,UAAYU,EAAsB,QAAU,SAAU,CAClE,GAAI,CAAC,KAAK,OAAO,GAAG,EAChB,OAAO,KAAK,MAAMhJ,EAAU,oCAAqCyE,EAAe,KAAK,gBAAiB,KAAK,cAAa,CAAE,CAAC,EAE/H,KAAK,UAAS,EACd,IAAI3R,EAAS,KAAK,uBAAuBkN,EAAU,oCAAqCA,EAAU,oCAAoC,EACtI,GAAIlN,EAAO,IACP,OAAOA,EAGX,KAAK,UAAS,EACdkW,EAAwB,KAAK,4BAC7BC,EAAenW,EAAO,GACzB,CACD,IAAIoW,EAAgB,KAAK,8BAA8BrC,EAAcyB,EAASvB,EAAmBiC,CAAqB,EACtH,GAAIE,EAAc,IACd,OAAOA,EAEX,IAAIN,EAAiB,KAAK,sBAAsBX,CAAoB,EACpE,GAAIW,EAAe,IACf,OAAOA,EAEX,IAAIO,EAAa1E,EAAewD,EAAsB,KAAK,cAAe,GAC1E,OAAIK,IAAY,SACL,CACH,IAAK,CACD,KAAMrI,EAAK,OACX,MAAOtS,EACP,QAASmY,GAAYoD,EAAc,GAAG,EACtC,SAAUC,CACb,EACD,IAAK,IAC7B,EAG2B,CACH,IAAK,CACD,KAAMlJ,EAAK,OACX,MAAOtS,EACP,QAASmY,GAAYoD,EAAc,GAAG,EACtC,OAAQD,EACR,WAAYX,IAAY,SAAW,WAAa,UAChD,SAAUa,CACb,EACD,IAAK,IAC7B,CAEa,CACD,QACI,OAAO,KAAK,MAAMnJ,EAAU,sBAAuByE,EAAe4D,EAAmBE,CAAe,CAAC,CAC5G,CACT,EACI3B,EAAO,UAAU,sBAAwB,SAAUqB,EAAsB,CAGrE,OAAI,KAAK,MAAO,GAAI,KAAK,KAAI,IAAO,IACzB,KAAK,MAAMjI,EAAU,8BAA+ByE,EAAewD,EAAsB,KAAK,cAAe,EAAC,GAEzH,KAAK,KAAI,EACF,CAAE,IAAK,GAAM,IAAK,IAAI,EACrC,EAIIrB,EAAO,UAAU,8BAAgC,UAAY,CAGzD,QAFIwC,EAAe,EACflC,EAAgB,KAAK,gBAClB,CAAC,KAAK,SAAS,CAClB,IAAIc,EAAK,KAAK,OACd,OAAQA,EAAE,CACN,IAAK,IAAc,CAGf,KAAK,KAAI,EACT,IAAIqB,EAAqB,KAAK,gBAC9B,GAAI,CAAC,KAAK,UAAU,GAAG,EACnB,OAAO,KAAK,MAAMrJ,EAAU,iCAAkCyE,EAAe4E,EAAoB,KAAK,cAAe,EAAC,EAE1H,KAAK,KAAI,EACT,KACH,CACD,IAAK,KAAe,CAChBD,GAAgB,EAChB,KAAK,KAAI,EACT,KACH,CACD,IAAK,KAAe,CAChB,GAAIA,EAAe,EACfA,GAAgB,MAGhB,OAAO,CACH,IAAK,KAAK,QAAQ,MAAMlC,EAAc,OAAQ,KAAK,QAAQ,EAC3D,IAAK,IACjC,EAEoB,KACH,CACD,QACI,KAAK,KAAI,EACT,KACP,CACJ,CACD,MAAO,CACH,IAAK,KAAK,QAAQ,MAAMA,EAAc,OAAQ,KAAK,QAAQ,EAC3D,IAAK,IACjB,CACA,EACIN,EAAO,UAAU,8BAAgC,SAAU1F,EAAU4G,EAAU,CAC3E,IAAIvG,EAAS,GACb,GAAI,CACAA,EAASF,GAA8BH,CAAQ,CAClD,MACS,CACN,OAAO,KAAK,MAAMlB,EAAU,wBAAyB8H,CAAQ,CAChE,CACD,MAAO,CACH,IAAK,CACD,KAAM5H,GAAc,OACpB,OAAQqB,EACR,SAAUuG,EACV,cAAe,KAAK,qBACd9E,GAAoBzB,CAAM,EAC1B,CAAE,CACX,EACD,IAAK,IACjB,CACA,EAWIqF,EAAO,UAAU,8BAAgC,SAAUC,EAAcC,EAAewC,EAAgBC,EAAuB,CAS3H,QARI1H,EACA2H,EAAiB,GACjBvV,EAAU,GACVwV,EAAkB,IAAI,IACtBC,EAAWH,EAAsB,MAAOI,EAAmBJ,EAAsB,WAIxE,CACT,GAAIG,EAAS,SAAW,EAAG,CACvB,IAAIxC,EAAgB,KAAK,gBACzB,GAAIJ,IAAkB,UAAY,KAAK,OAAO,GAAG,EAAG,CAEhD,IAAIhU,EAAS,KAAK,uBAAuBkN,EAAU,gCAAiCA,EAAU,gCAAgC,EAC9H,GAAIlN,EAAO,IACP,OAAOA,EAEX6W,EAAmBlF,EAAeyC,EAAe,KAAK,cAAe,GACrEwC,EAAW,KAAK,QAAQ,MAAMxC,EAAc,OAAQ,KAAK,OAAM,CAAE,CACpE,KAEG,MAEP,CAED,GAAIuC,EAAgB,IAAIC,CAAQ,EAC5B,OAAO,KAAK,MAAM5C,IAAkB,SAC9B9G,EAAU,mCACVA,EAAU,mCAAoC2J,CAAgB,EAEpED,IAAa,UACbF,EAAiB,IAKrB,KAAK,UAAS,EACd,IAAIvB,EAAuB,KAAK,gBAChC,GAAI,CAAC,KAAK,OAAO,GAAG,EAChB,OAAO,KAAK,MAAMnB,IAAkB,SAC9B9G,EAAU,yCACVA,EAAU,yCAA0CyE,EAAe,KAAK,cAAa,EAAI,KAAK,cAAe,EAAC,EAExH,IAAImF,EAAiB,KAAK,aAAa/C,EAAe,EAAGC,EAAewC,CAAc,EACtF,GAAIM,EAAe,IACf,OAAOA,EAEX,IAAIhB,EAAiB,KAAK,sBAAsBX,CAAoB,EACpE,GAAIW,EAAe,IACf,OAAOA,EAEX3U,EAAQ,KAAK,CACTyV,EACA,CACI,MAAOE,EAAe,IACtB,SAAUnF,EAAewD,EAAsB,KAAK,cAAa,CAAE,CACtE,CACjB,CAAa,EAEDwB,EAAgB,IAAIC,CAAQ,EAE5B,KAAK,UAAS,EACb7H,EAAK,KAAK,4BAA6B6H,EAAW7H,EAAG,MAAO8H,EAAmB9H,EAAG,QACtF,CACD,OAAI5N,EAAQ,SAAW,EACZ,KAAK,MAAM6S,IAAkB,SAC9B9G,EAAU,gCACVA,EAAU,gCAAiCyE,EAAe,KAAK,cAAa,EAAI,KAAK,cAAe,EAAC,EAE3G,KAAK,qBAAuB,CAAC+E,EACtB,KAAK,MAAMxJ,EAAU,qBAAsByE,EAAe,KAAK,gBAAiB,KAAK,cAAa,CAAE,CAAC,EAEzG,CAAE,IAAKxQ,EAAS,IAAK,IAAI,CACxC,EACI2S,EAAO,UAAU,uBAAyB,SAAUiD,EAAmBC,EAAoB,CACvF,IAAIC,EAAO,EACP7B,EAAmB,KAAK,gBACxB,KAAK,OAAO,GAAG,GAEV,KAAK,OAAO,GAAG,IACpB6B,EAAO,IAIX,QAFIC,EAAY,GACZC,EAAU,EACP,CAAC,KAAK,SAAS,CAClB,IAAIjC,EAAK,KAAK,OACd,GAAIA,GAAM,IAAgBA,GAAM,GAC5BgC,EAAY,GACZC,EAAUA,EAAU,IAAMjC,EAAK,IAC/B,KAAK,KAAI,MAGT,MAEP,CACD,IAAIF,EAAWrD,EAAeyD,EAAkB,KAAK,cAAe,GACpE,OAAK8B,GAGLC,GAAWF,EACN7E,GAAc+E,CAAO,EAGnB,CAAE,IAAKA,EAAS,IAAK,IAAI,EAFrB,KAAK,MAAMH,EAAoBhC,CAAQ,GAJvC,KAAK,MAAM+B,EAAmB/B,CAAQ,CAOzD,EACIlB,EAAO,UAAU,OAAS,UAAY,CAClC,OAAO,KAAK,SAAS,MAC7B,EACIA,EAAO,UAAU,MAAQ,UAAY,CACjC,OAAO,KAAK,OAAM,IAAO,KAAK,QAAQ,MAC9C,EACIA,EAAO,UAAU,cAAgB,UAAY,CAEzC,MAAO,CACH,OAAQ,KAAK,SAAS,OACtB,KAAM,KAAK,SAAS,KACpB,OAAQ,KAAK,SAAS,MAClC,CACA,EAKIA,EAAO,UAAU,KAAO,UAAY,CAChC,IAAIsD,EAAS,KAAK,SAAS,OAC3B,GAAIA,GAAU,KAAK,QAAQ,OACvB,MAAM,MAAM,cAAc,EAE9B,IAAIrE,EAAOK,GAAY,KAAK,QAASgE,CAAM,EAC3C,GAAIrE,IAAS,OACT,MAAM,MAAM,UAAU,OAAOqE,EAAQ,0CAA0C,CAAC,EAEpF,OAAOrE,CACf,EACIe,EAAO,UAAU,MAAQ,SAAUuD,EAAMrC,EAAU,CAC/C,MAAO,CACH,IAAK,KACL,IAAK,CACD,KAAMqC,EACN,QAAS,KAAK,QACd,SAAUrC,CACb,CACb,CACA,EAEIlB,EAAO,UAAU,KAAO,UAAY,CAChC,GAAI,MAAK,QAGT,KAAIf,EAAO,KAAK,OACZA,IAAS,IACT,KAAK,SAAS,MAAQ,EACtB,KAAK,SAAS,OAAS,EACvB,KAAK,SAAS,QAAU,IAGxB,KAAK,SAAS,QAAU,EAExB,KAAK,SAAS,QAAUA,EAAO,MAAU,EAAI,GAEzD,EAOIe,EAAO,UAAU,OAAS,SAAUwD,EAAQ,CACxC,GAAI9E,GAAW,KAAK,QAAS8E,EAAQ,KAAK,OAAM,CAAE,EAAG,CACjD,QAASvgB,EAAI,EAAGA,EAAIugB,EAAO,OAAQvgB,IAC/B,KAAK,KAAI,EAEb,MAAO,EACV,CACD,MAAO,EACf,EAKI+c,EAAO,UAAU,UAAY,SAAUyD,EAAS,CAC5C,IAAIC,EAAgB,KAAK,SACrB7c,EAAQ,KAAK,QAAQ,QAAQ4c,EAASC,CAAa,EACvD,OAAI7c,GAAS,GACT,KAAK,OAAOA,CAAK,EACV,KAGP,KAAK,OAAO,KAAK,QAAQ,MAAM,EACxB,GAEnB,EAKImZ,EAAO,UAAU,OAAS,SAAU2D,EAAc,CAC9C,GAAI,KAAK,OAAQ,EAAGA,EAChB,MAAM,MAAM,gBAAgB,OAAOA,EAAc,uDAAuD,EAAE,OAAO,KAAK,OAAQ,EAAC,EAGnI,IADAA,EAAe,KAAK,IAAIA,EAAc,KAAK,QAAQ,MAAM,IAC5C,CACT,IAAIL,EAAS,KAAK,SAClB,GAAIA,IAAWK,EACX,MAEJ,GAAIL,EAASK,EACT,MAAM,MAAM,gBAAgB,OAAOA,EAAc,0CAA0C,CAAC,EAGhG,GADA,KAAK,KAAI,EACL,KAAK,QACL,KAEP,CACT,EAEI3D,EAAO,UAAU,UAAY,UAAY,CACrC,KAAO,CAAC,KAAK,MAAO,GAAIF,GAAc,KAAK,KAAI,CAAE,GAC7C,KAAK,KAAI,CAErB,EAKIE,EAAO,UAAU,KAAO,UAAY,CAChC,GAAI,KAAK,QACL,OAAO,KAEX,IAAIf,EAAO,KAAK,OACZqE,EAAS,KAAK,SACdM,EAAW,KAAK,QAAQ,WAAWN,GAAUrE,GAAQ,MAAU,EAAI,EAAE,EACzE,OAAO2E,GAAsD,IACrE,EACW5D,CACX,EAAC,EAOD,SAASK,GAASwD,EAAW,CACzB,OAASA,GAAa,IAAMA,GAAa,KACpCA,GAAa,IAAMA,GAAa,EACzC,CACA,SAAS1C,GAAgB0C,EAAW,CAChC,OAAOxD,GAASwD,CAAS,GAAKA,IAAc,EAChD,CAEA,SAAS/C,GAA4Btb,EAAG,CACpC,OAAQA,IAAM,IACVA,IAAM,IACLA,GAAK,IAAMA,GAAK,IACjBA,IAAM,IACLA,GAAK,IAAMA,GAAK,KAChBA,GAAK,IAAMA,GAAK,IACjBA,GAAK,KACJA,GAAK,KAAQA,GAAK,KAClBA,GAAK,KAAQA,GAAK,KAClBA,GAAK,KAAQA,GAAK,KAClBA,GAAK,KAASA,GAAK,MACnBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAWA,GAAK,MAC9B,CAKA,SAASsa,GAActa,EAAG,CACtB,OAASA,GAAK,GAAUA,GAAK,IACzBA,IAAM,IACNA,IAAM,KACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,IACd,CAKA,SAASua,GAAiBva,EAAG,CACzB,OAASA,GAAK,IAAUA,GAAK,IACzBA,IAAM,IACLA,GAAK,IAAUA,GAAK,IACrBA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACLA,GAAK,IAAUA,GAAK,IACpBA,GAAK,IAAUA,GAAK,IACpBA,GAAK,IAAUA,GAAK,IACpBA,GAAK,IAAUA,GAAK,IACrBA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACLA,GAAK,KAAUA,GAAK,KACrBA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,KACrBA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,KAC7B,CCvvCA,SAASse,GAAcC,EAAK,CACxBA,EAAI,QAAQ,SAAUvK,EAAI,CAEtB,GADA,OAAOA,EAAG,SACNK,GAAgBL,CAAE,GAAKM,GAAgBN,CAAE,EACzC,QAAS6F,KAAK7F,EAAG,QACb,OAAOA,EAAG,QAAQ6F,CAAC,EAAE,SACrByE,GAActK,EAAG,QAAQ6F,CAAC,EAAE,KAAK,OAGhC3F,GAAgBF,CAAE,GAAKS,GAAiBT,EAAG,KAAK,IAG/CG,GAAcH,CAAE,GAAKI,GAAcJ,CAAE,IAC3CU,GAAmBV,EAAG,KAAK,EAH3B,OAAOA,EAAG,MAAM,SAMXQ,GAAaR,CAAE,GACpBsK,GAActK,EAAG,QAAQ,CAErC,CAAK,CACL,CACO,SAASwK,GAAMta,EAASua,EAAM,CAC7BA,IAAS,SAAUA,EAAO,CAAE,GAChCA,EAAOtL,EAAS,CAAE,qBAAsB,GAAM,oBAAqB,EAAI,EAAIsL,CAAI,EAC/E,IAAI/X,EAAS,IAAI8T,GAAOtW,EAASua,CAAI,EAAE,QACvC,GAAI/X,EAAO,IAAK,CACZ,IAAIZ,EAAQ,YAAY8N,EAAUlN,EAAO,IAAI,IAAI,CAAC,EAElD,MAAAZ,EAAM,SAAWY,EAAO,IAAI,SAE5BZ,EAAM,gBAAkBY,EAAO,IAAI,QAC7BZ,CACT,CACD,OAAkD2Y,GAAK,iBACnDH,GAAc5X,EAAO,GAAG,EAErBA,EAAO,GAClB,CCtCO,SAASgY,GAAQlkB,EAAIqN,EAAS,CACjC,IAAI8W,EAAQ9W,GAAWA,EAAQ,MAAQA,EAAQ,MAAQ+W,GACnDre,EAAasH,GAAWA,EAAQ,WAAaA,EAAQ,WAAagX,GAClEC,EAAWjX,GAAWA,EAAQ,SAAWA,EAAQ,SAAWkX,GAChE,OAAOD,EAAStkB,EAAI,CAChB,MAAOmkB,EACP,WAAYpe,CACpB,CAAK,CACL,CAIA,SAASye,GAAYzd,EAAO,CACxB,OAAQA,GAAS,MAAQ,OAAOA,GAAU,UAAY,OAAOA,GAAU,SAC3E,CACA,SAAS0d,GAAQzkB,EAAImkB,EAAOpe,EAAY2e,EAAK,CACzC,IAAIC,EAAWH,GAAYE,CAAG,EAAIA,EAAM3e,EAAW2e,CAAG,EAClDE,EAAgBT,EAAM,IAAIQ,CAAQ,EACtC,OAAI,OAAOC,EAAkB,MACzBA,EAAgB5kB,EAAG,KAAK,KAAM0kB,CAAG,EACjCP,EAAM,IAAIQ,EAAUC,CAAa,GAE9BA,CACX,CACA,SAASC,GAAS7kB,EAAImkB,EAAOpe,EAAY,CACrC,IAAI+e,EAAO,MAAM,UAAU,MAAM,KAAK,UAAW,CAAC,EAC9CH,EAAW5e,EAAW+e,CAAI,EAC1BF,EAAgBT,EAAM,IAAIQ,CAAQ,EACtC,OAAI,OAAOC,EAAkB,MACzBA,EAAgB5kB,EAAG,MAAM,KAAM8kB,CAAI,EACnCX,EAAM,IAAIQ,EAAUC,CAAa,GAE9BA,CACX,CACA,SAASG,GAAS/kB,EAAIglB,EAASV,EAAUH,EAAOc,EAAW,CACvD,OAAOX,EAAS,KAAKU,EAAShlB,EAAImkB,EAAOc,CAAS,CACtD,CACA,SAASV,GAAgBvkB,EAAIqN,EAAS,CAClC,IAAIiX,EAAWtkB,EAAG,SAAW,EAAIykB,GAAUI,GAC3C,OAAOE,GAAS/kB,EAAI,KAAMskB,EAAUjX,EAAQ,MAAM,OAAQ,EAAEA,EAAQ,UAAU,CAClF,CACA,SAAS6X,GAAiBllB,EAAIqN,EAAS,CACnC,OAAO0X,GAAS/kB,EAAI,KAAM6kB,GAAUxX,EAAQ,MAAM,OAAQ,EAAEA,EAAQ,UAAU,CAClF,CACA,SAAS8X,GAAgBnlB,EAAIqN,EAAS,CAClC,OAAO0X,GAAS/kB,EAAI,KAAMykB,GAASpX,EAAQ,MAAM,OAAQ,EAAEA,EAAQ,UAAU,CACjF,CAIA,IAAIgX,GAAoB,UAAY,CAChC,OAAO,KAAK,UAAU,SAAS,CACnC,EAIA,SAASe,IAA8B,CACnC,KAAK,MAAQ,OAAO,OAAO,IAAI,CACnC,CACAA,GAA4B,UAAU,IAAM,SAAUte,EAAK,CACvD,OAAO,KAAK,MAAMA,CAAG,CACzB,EACAse,GAA4B,UAAU,IAAM,SAAUte,EAAKC,EAAO,CAC9D,KAAK,MAAMD,CAAG,EAAIC,CACtB,EACA,IAAIqd,GAAe,CACf,OAAQ,UAAkB,CAEtB,OAAO,IAAIgB,EACd,CACL,EACWC,GAAa,CACpB,SAAUH,GACV,QAASC,EACb,EC5EWG,IACV,SAAUA,EAAW,CAElBA,EAAU,cAAmB,gBAE7BA,EAAU,cAAmB,gBAE7BA,EAAU,iBAAsB,kBACpC,GAAGA,KAAcA,GAAY,CAAE,EAAC,EAChC,IAAIC,GAA6B,SAAUC,EAAQ,CAC/C/M,GAAU8M,EAAaC,CAAM,EAC7B,SAASD,EAAY3R,EAAKqL,EAAMwG,EAAiB,CAC7C,IAAIC,EAAQF,EAAO,KAAK,KAAM5R,CAAG,GAAK,KACtC,OAAA8R,EAAM,KAAOzG,EACbyG,EAAM,gBAAkBD,EACjBC,CACV,CACD,OAAAH,EAAY,UAAU,SAAW,UAAY,CACzC,MAAO,oBAAoB,OAAO,KAAK,KAAM,IAAI,EAAE,OAAO,KAAK,OAAO,CAC9E,EACWA,CACX,EAAE,KAAK,EAEHI,GAAmC,SAAUH,EAAQ,CACrD/M,GAAUkN,EAAmBH,CAAM,EACnC,SAASG,EAAkBC,EAAY7e,EAAOsG,EAASoY,EAAiB,CACpE,OAAOD,EAAO,KAAK,KAAM,uBAAwB,OAAOI,EAAY,MAAQ,EAAE,OAAO7e,EAAO,kBAAoB,EAAE,OAAO,OAAO,KAAKsG,CAAO,EAAE,KAAK,MAAM,EAAG,GAAI,EAAGiY,GAAU,cAAeG,CAAe,GAAK,IACnN,CACD,OAAOE,CACX,EAAEJ,EAAW,EAETM,GAAuC,SAAUL,EAAQ,CACzD/M,GAAUoN,EAAuBL,CAAM,EACvC,SAASK,EAAsB9e,EAAOV,EAAMof,EAAiB,CACzD,OAAOD,EAAO,KAAK,KAAM,cAAe,OAAOze,EAAO,oBAAqB,EAAE,OAAOV,CAAI,EAAGif,GAAU,cAAeG,CAAe,GAAK,IAC3I,CACD,OAAOI,CACX,EAAEN,EAAW,EAETO,GAAmC,SAAUN,EAAQ,CACrD/M,GAAUqN,EAAmBN,CAAM,EACnC,SAASM,EAAkBF,EAAYH,EAAiB,CACpD,OAAOD,EAAO,KAAK,KAAM,qCAAsC,OAAOI,EAAY,oCAAsC,EAAE,OAAOH,EAAiB,GAAI,EAAGH,GAAU,cAAeG,CAAe,GAAK,IACzM,CACD,OAAOK,CACX,EAAEP,EAAW,EC5CFQ,GACV,SAAUA,EAAW,CAClBA,EAAUA,EAAU,QAAa,CAAC,EAAI,UACtCA,EAAUA,EAAU,OAAY,CAAC,EAAI,QACzC,GAAGA,IAAcA,EAAY,CAAE,EAAC,EAChC,SAASC,GAAaC,EAAO,CACzB,OAAIA,EAAM,OAAS,EACRA,EAEJA,EAAM,OAAO,SAAU3J,EAAK1X,EAAM,CACrC,IAAIshB,EAAW5J,EAAIA,EAAI,OAAS,CAAC,EACjC,MAAI,CAAC4J,GACDA,EAAS,OAASH,EAAU,SAC5BnhB,EAAK,OAASmhB,EAAU,QACxBzJ,EAAI,KAAK1X,CAAI,EAGbshB,EAAS,OAASthB,EAAK,MAEpB0X,CACV,EAAE,CAAE,EACT,CACO,SAAS6J,GAAqB3M,EAAI,CACrC,OAAO,OAAOA,GAAO,UACzB,CAEO,SAAS4M,GAAcrC,EAAKsC,EAASC,EAAYC,EAAS3U,EAAQ4U,EAEzEf,EAAiB,CAEb,GAAI1B,EAAI,SAAW,GAAKxK,GAAiBwK,EAAI,CAAC,CAAC,EAC3C,MAAO,CACH,CACI,KAAMgC,EAAU,QAChB,MAAOhC,EAAI,CAAC,EAAE,KACjB,CACb,EAGI,QADI7X,EAAS,GACJ0O,EAAK,EAAG6L,EAAQ1C,EAAKnJ,EAAK6L,EAAM,OAAQ7L,IAAM,CACnD,IAAIpB,EAAKiN,EAAM7L,CAAE,EAEjB,GAAIrB,GAAiBC,CAAE,EAAG,CACtBtN,EAAO,KAAK,CACR,KAAM6Z,EAAU,QAChB,MAAOvM,EAAG,KAC1B,CAAa,EACD,QACH,CAGD,GAAIO,GAAeP,CAAE,EAAG,CAChB,OAAOgN,GAAuB,UAC9Bta,EAAO,KAAK,CACR,KAAM6Z,EAAU,QAChB,MAAOO,EAAW,gBAAgBD,CAAO,EAAE,OAAOG,CAAkB,CACxF,CAAiB,EAEL,QACH,CACD,IAAIE,EAAUlN,EAAG,MAEjB,GAAI,EAAE5H,GAAU8U,KAAW9U,GACvB,MAAM,IAAIkU,GAAkBY,EAASjB,CAAe,EAExD,IAAI1e,EAAQ6K,EAAO8U,CAAO,EAC1B,GAAIjN,GAAkBD,CAAE,EAAG,EACnB,CAACzS,GAAS,OAAOA,GAAU,UAAY,OAAOA,GAAU,YACxDA,EACI,OAAOA,GAAU,UAAY,OAAOA,GAAU,SACxC,OAAOA,CAAK,EACZ,IAEdmF,EAAO,KAAK,CACR,KAAM,OAAOnF,GAAU,SAAWgf,EAAU,QAAUA,EAAU,OAChE,MAAOhf,CACvB,CAAa,EACD,QACH,CAID,GAAI4S,GAAcH,CAAE,EAAG,CACnB,IAAIsI,EAAQ,OAAOtI,EAAG,OAAU,SAC1B+M,EAAQ,KAAK/M,EAAG,KAAK,EACrBU,GAAmBV,EAAG,KAAK,EACvBA,EAAG,MAAM,cACT,OACVtN,EAAO,KAAK,CACR,KAAM6Z,EAAU,QAChB,MAAOO,EACF,kBAAkBD,EAASvE,CAAK,EAChC,OAAO/a,CAAK,CACjC,CAAa,EACD,QACH,CACD,GAAI6S,GAAcJ,CAAE,EAAG,CACnB,IAAIsI,EAAQ,OAAOtI,EAAG,OAAU,SAC1B+M,EAAQ,KAAK/M,EAAG,KAAK,EACrBU,GAAmBV,EAAG,KAAK,EACvBA,EAAG,MAAM,cACT+M,EAAQ,KAAK,OACvBra,EAAO,KAAK,CACR,KAAM6Z,EAAU,QAChB,MAAOO,EACF,kBAAkBD,EAASvE,CAAK,EAChC,OAAO/a,CAAK,CACjC,CAAa,EACD,QACH,CACD,GAAI2S,GAAgBF,CAAE,EAAG,CACrB,IAAIsI,EAAQ,OAAOtI,EAAG,OAAU,SAC1B+M,EAAQ,OAAO/M,EAAG,KAAK,EACvBS,GAAiBT,EAAG,KAAK,EACrBA,EAAG,MAAM,cACT,OACNsI,GAASA,EAAM,QACf/a,EACIA,GACK+a,EAAM,OAAS,IAE5B5V,EAAO,KAAK,CACR,KAAM6Z,EAAU,QAChB,MAAOO,EACF,gBAAgBD,EAASvE,CAAK,EAC9B,OAAO/a,CAAK,CACjC,CAAa,EACD,QACH,CACD,GAAIiT,GAAaR,CAAE,EAAG,CAClB,IAAIiH,EAAWjH,EAAG,SAAUmN,EAAUnN,EAAG,MACrCoN,EAAWhV,EAAO+U,CAAO,EAC7B,GAAI,CAACR,GAAqBS,CAAQ,EAC9B,MAAM,IAAIf,GAAsBc,EAAS,WAAYlB,CAAe,EAExE,IAAIQ,EAAQG,GAAc3F,EAAU4F,EAASC,EAAYC,EAAS3U,EAAQ4U,CAAkB,EACxFK,EAASD,EAASX,EAAM,IAAI,SAAU9f,EAAG,CAAE,OAAOA,EAAE,KAAM,CAAE,CAAC,EAC5D,MAAM,QAAQ0gB,CAAM,IACrBA,EAAS,CAACA,CAAM,GAEpB3a,EAAO,KAAK,MAAMA,EAAQ2a,EAAO,IAAI,SAAUrhB,EAAG,CAC9C,MAAO,CACH,KAAM,OAAOA,GAAM,SAAWugB,EAAU,QAAUA,EAAU,OAC5D,MAAOvgB,CAC3B,CACa,EAAC,CACL,CACD,GAAIqU,GAAgBL,CAAE,EAAG,CACrB,IAAI0C,EAAM1C,EAAG,QAAQzS,CAAK,GAAKyS,EAAG,QAAQ,MAC1C,GAAI,CAAC0C,EACD,MAAM,IAAIyJ,GAAkBnM,EAAG,MAAOzS,EAAO,OAAO,KAAKyS,EAAG,OAAO,EAAGiM,CAAe,EAEzFvZ,EAAO,KAAK,MAAMA,EAAQka,GAAclK,EAAI,MAAOmK,EAASC,EAAYC,EAAS3U,CAAM,CAAC,EACxF,QACH,CACD,GAAIkI,GAAgBN,CAAE,EAAG,CACrB,IAAI0C,EAAM1C,EAAG,QAAQ,IAAI,OAAOzS,CAAK,CAAC,EACtC,GAAI,CAACmV,EAAK,CACN,GAAI,CAAC,KAAK,YACN,MAAM,IAAIqJ,GAAY;AAAA;AAAA,EAAqHD,GAAU,iBAAkBG,CAAe,EAE1L,IAAIqB,EAAOR,EACN,eAAeD,EAAS,CAAE,KAAM7M,EAAG,UAAU,CAAE,EAC/C,OAAOzS,GAASyS,EAAG,QAAU,EAAE,EACpC0C,EAAM1C,EAAG,QAAQsN,CAAI,GAAKtN,EAAG,QAAQ,KACxC,CACD,GAAI,CAAC0C,EACD,MAAM,IAAIyJ,GAAkBnM,EAAG,MAAOzS,EAAO,OAAO,KAAKyS,EAAG,OAAO,EAAGiM,CAAe,EAEzFvZ,EAAO,KAAK,MAAMA,EAAQka,GAAclK,EAAI,MAAOmK,EAASC,EAAYC,EAAS3U,EAAQ7K,GAASyS,EAAG,QAAU,EAAE,CAAC,EAClH,QACH,CACJ,CACD,OAAOwM,GAAa9Z,CAAM,CAC9B,CCtKA,SAAS6a,GAAYC,EAAIC,EAAI,CACzB,OAAKA,EAGEtO,EAASA,EAASA,EAAS,CAAE,EAAGqO,GAAM,CAAE,GAAKC,GAAM,CAAE,GAAI,OAAO,KAAKD,CAAE,EAAE,OAAO,SAAU1K,EAAK+C,EAAG,CACrG,OAAA/C,EAAI+C,CAAC,EAAI1G,EAASA,EAAS,GAAIqO,EAAG3H,CAAC,CAAC,EAAI4H,EAAG5H,CAAC,GAAK,CAAE,GAC5C/C,CACf,EAAO,EAAE,CAAC,EALK0K,CAMf,CACA,SAASE,GAAaC,EAAeC,EAAS,CAC1C,OAAKA,EAGE,OAAO,KAAKD,CAAa,EAAE,OAAO,SAAU7K,EAAK+C,EAAG,CACvD,OAAA/C,EAAI+C,CAAC,EAAI0H,GAAYI,EAAc9H,CAAC,EAAG+H,EAAQ/H,CAAC,CAAC,EAC1C/C,CACV,EAAE3D,EAAS,GAAIwO,CAAa,CAAC,EALnBA,CAMf,CACA,SAASE,GAAuBxS,EAAO,CACnC,MAAO,CACH,OAAQ,UAAY,CAChB,MAAO,CACH,IAAK,SAAU/N,EAAK,CAChB,OAAO+N,EAAM/N,CAAG,CACnB,EACD,IAAK,SAAUA,EAAKC,EAAO,CACvB8N,EAAM/N,CAAG,EAAIC,CAChB,CACjB,CACS,CACT,CACA,CACA,SAASugB,GAAwBnD,EAAO,CACpC,OAAIA,IAAU,SAAUA,EAAQ,CAC5B,OAAQ,CAAE,EACV,SAAU,CAAE,EACZ,YAAa,CAAE,CACvB,GACW,CACH,gBAAiBD,GAAQ,UAAY,CAGjC,QAFIjJ,EACA6J,EAAO,GACFlK,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpCkK,EAAKlK,CAAE,EAAI,UAAUA,CAAE,EAE3B,OAAO,KAAMK,EAAK,KAAK,cAAc,KAAK,MAAMA,EAAInC,GAAc,CAAC,MAAM,EAAGgM,EAAM,EAAK,CAAC,EACpG,EAAW,CACC,MAAOuC,GAAuBlD,EAAM,MAAM,EAC1C,SAAUkB,GAAW,QACjC,CAAS,EACD,kBAAmBnB,GAAQ,UAAY,CAGnC,QAFIjJ,EACA6J,EAAO,GACFlK,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpCkK,EAAKlK,CAAE,EAAI,UAAUA,CAAE,EAE3B,OAAO,KAAMK,EAAK,KAAK,gBAAgB,KAAK,MAAMA,EAAInC,GAAc,CAAC,MAAM,EAAGgM,EAAM,EAAK,CAAC,EACtG,EAAW,CACC,MAAOuC,GAAuBlD,EAAM,QAAQ,EAC5C,SAAUkB,GAAW,QACjC,CAAS,EACD,eAAgBnB,GAAQ,UAAY,CAGhC,QAFIjJ,EACA6J,EAAO,GACFlK,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpCkK,EAAKlK,CAAE,EAAI,UAAUA,CAAE,EAE3B,OAAO,KAAMK,EAAK,KAAK,aAAa,KAAK,MAAMA,EAAInC,GAAc,CAAC,MAAM,EAAGgM,EAAM,EAAK,CAAC,EACnG,EAAW,CACC,MAAOuC,GAAuBlD,EAAM,WAAW,EAC/C,SAAUkB,GAAW,QACjC,CAAS,CACT,CACA,CACA,IAAIkC,GAAmC,UAAY,CAC/C,SAASA,EAAkB7d,EAAS2c,EAASmB,EAAiBvD,EAAM,CAChE,IAAIyB,EAAQ,KA2CZ,GA1CIW,IAAY,SAAUA,EAAUkB,EAAkB,eACtD,KAAK,eAAiB,CAClB,OAAQ,CAAE,EACV,SAAU,CAAE,EACZ,YAAa,CAAE,CAC3B,EACQ,KAAK,OAAS,SAAU3V,EAAQ,CAC5B,IAAIqU,EAAQP,EAAM,cAAc9T,CAAM,EAEtC,GAAIqU,EAAM,SAAW,EACjB,OAAOA,EAAM,CAAC,EAAE,MAEpB,IAAI/Z,EAAS+Z,EAAM,OAAO,SAAU3J,EAAK1X,EAAM,CAC3C,MAAI,CAAC0X,EAAI,QACL1X,EAAK,OAASmhB,EAAU,SACxB,OAAOzJ,EAAIA,EAAI,OAAS,CAAC,GAAM,SAC/BA,EAAI,KAAK1X,EAAK,KAAK,EAGnB0X,EAAIA,EAAI,OAAS,CAAC,GAAK1X,EAAK,MAEzB0X,CACV,EAAE,CAAE,GACL,OAAIpQ,EAAO,QAAU,EACVA,EAAO,CAAC,GAAK,GAEjBA,CACnB,EACQ,KAAK,cAAgB,SAAU0F,EAAQ,CACnC,OAAOwU,GAAcV,EAAM,IAAKA,EAAM,QAASA,EAAM,WAAYA,EAAM,QAAS9T,EAAQ,OAAW8T,EAAM,OAAO,CAC5H,EACQ,KAAK,gBAAkB,UAAY,CAC/B,IAAIzK,EACJ,MAAQ,CACJ,SAAUA,EAAKyK,EAAM,kBAAoB,MAAQzK,IAAO,OAAS,OAASA,EAAG,SAAU,IACnF,KAAK,aAAa,mBAAmByK,EAAM,OAAO,EAAE,CAAC,CACzE,CACA,EACQ,KAAK,OAAS,UAAY,CAAE,OAAOA,EAAM,GAAI,EAE7C,KAAK,QAAUW,EACf,KAAK,eAAiBkB,EAAkB,cAAclB,CAAO,EACzD,OAAO3c,GAAY,SAAU,CAE7B,GADA,KAAK,QAAUA,EACX,CAAC6d,EAAkB,QACnB,MAAM,IAAI,UAAU,6EAA6E,EAElG,IAACtM,EAAKgJ,GAAQ,CAAE,EAAehJ,EAAG,WAAY,IAAAwM,EAAY5O,GAAOoC,EAAI,CAAC,YAAY,CAAC,EAEtF,KAAK,IAAMsM,EAAkB,QAAQ7d,EAASiP,EAASA,EAAS,CAAE,EAAE8O,CAAS,EAAG,CAAE,OAAQ,KAAK,cAAc,CAAE,CAAC,CACnH,MAEG,KAAK,IAAM/d,EAEf,GAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,EACvB,MAAM,IAAI,UAAU,gDAAgD,EAIxE,KAAK,QAAUwd,GAAaK,EAAkB,QAASC,CAAe,EACtE,KAAK,WACAvD,GAAQA,EAAK,YAAeqD,GAAwB,KAAK,cAAc,CAC/E,CACD,cAAO,eAAeC,EAAmB,gBAAiB,CACtD,IAAK,UAAY,CACb,OAAKA,EAAkB,wBACnBA,EAAkB,sBACd,IAAI,KAAK,aAAY,EAAG,gBAAe,EAAG,QAE3CA,EAAkB,qBAC5B,EACD,WAAY,GACZ,aAAc,EACtB,CAAK,EACDA,EAAkB,sBAAwB,KAC1CA,EAAkB,cAAgB,SAAUlB,EAAS,CACjD,GAAI,SAAO,KAAK,OAAW,KAG3B,KAAIqB,EAAmB,KAAK,aAAa,mBAAmBrB,CAAO,EACnE,OAAIqB,EAAiB,OAAS,EACnB,IAAI,KAAK,OAAOA,EAAiB,CAAC,CAAC,EAEvC,IAAI,KAAK,OAAO,OAAOrB,GAAY,SAAWA,EAAUA,EAAQ,CAAC,CAAC,EACjF,EACIkB,EAAkB,QAAUvD,GAI5BuD,EAAkB,QAAU,CACxB,OAAQ,CACJ,QAAS,CACL,sBAAuB,CAC1B,EACD,SAAU,CACN,MAAO,UACV,EACD,QAAS,CACL,MAAO,SACV,CACJ,EACD,KAAM,CACF,MAAO,CACH,MAAO,UACP,IAAK,UACL,KAAM,SACT,EACD,OAAQ,CACJ,MAAO,QACP,IAAK,UACL,KAAM,SACT,EACD,KAAM,CACF,MAAO,OACP,IAAK,UACL,KAAM,SACT,EACD,KAAM,CACF,QAAS,OACT,MAAO,OACP,IAAK,UACL,KAAM,SACT,CACJ,EACD,KAAM,CACF,MAAO,CACH,KAAM,UACN,OAAQ,SACX,EACD,OAAQ,CACJ,KAAM,UACN,OAAQ,UACR,OAAQ,SACX,EACD,KAAM,CACF,KAAM,UACN,OAAQ,UACR,OAAQ,UACR,aAAc,OACjB,EACD,KAAM,CACF,KAAM,UACN,OAAQ,UACR,OAAQ,UACR,aAAc,OACjB,CACJ,CACT,EACWA,CACX,ICxOA,SAASI,GAAMnX,EAAKoX,EAAS,CAC3B,GAAIA,GAAW,KACb,OACF,GAAIA,KAAWpX,EACb,OAAOA,EAAIoX,CAAO,EAEpB,MAAMC,EAAOD,EAAQ,MAAM,GAAG,EAC9B,IAAI1b,EAASsE,EACb,QAASrK,EAAI,EAAGA,EAAI0hB,EAAK,OAAQ1hB,IAC/B,GAAI,OAAO+F,GAAW,SAAU,CAC9B,GAAI/F,EAAI,EAAG,CACT,MAAM2hB,EAAaD,EAAK,MAAM1hB,EAAG0hB,EAAK,MAAM,EAAE,KAAK,GAAG,EACtD,GAAIC,KAAc5b,EAAQ,CACxBA,EAASA,EAAO4b,CAAU,EAC1B,KACD,CACF,CACD5b,EAASA,EAAO2b,EAAK1hB,CAAC,CAAC,CAC7B,MACM+F,EAAS,OAGb,OAAOA,CACT,CAEA,MAAM6b,EAAc,GACdC,GAAa,CAACtlB,EAAMma,EAAQnT,IAC3BA,IAECmT,KAAUkL,IACdA,EAAYlL,CAAM,EAAI,IAClBna,KAAQqlB,EAAYlL,CAAM,IAC9BkL,EAAYlL,CAAM,EAAEna,CAAI,EAAIgH,GACvBA,GAEHue,GAAS,CAACvlB,EAAMwlB,IAAc,CAClC,GAAIA,GAAa,KACf,OACF,GAAIA,KAAaH,GAAerlB,KAAQqlB,EAAYG,CAAS,EAC3D,OAAOH,EAAYG,CAAS,EAAExlB,CAAI,EAEpC,MAAM2jB,EAAU8B,GAAmBD,CAAS,EAC5C,QAASjlB,EAAI,EAAGA,EAAIojB,EAAQ,OAAQpjB,IAAK,CACvC,MAAM4Z,EAASwJ,EAAQpjB,CAAC,EAClByG,EAAU0e,GAAyBvL,EAAQna,CAAI,EACrD,GAAIgH,EACF,OAAOse,GAAWtlB,EAAMwlB,EAAWxe,CAAO,CAE7C,CAEH,EAEA,IAAI2e,GACJ,MAAMC,GAAcjT,GAAS,EAAE,EAC/B,SAASkT,GAAoB1L,EAAQ,CACnC,OAAOwL,GAAWxL,CAAM,GAAK,IAC/B,CACA,SAAS2L,GAAoB3L,EAAQ,CACnC,OAAOA,KAAUwL,EACnB,CACA,SAASD,GAAyBvL,EAAQva,EAAI,CAC5C,GAAI,CAACkmB,GAAoB3L,CAAM,EAC7B,OAAO,KAET,MAAM4L,EAAmBF,GAAoB1L,CAAM,EAEnD,OADc8K,GAAMc,EAAkBnmB,CAAE,CAE1C,CACA,SAASomB,GAA0BR,EAAW,CAC5C,GAAIA,GAAa,KACf,OACF,MAAMS,EAAiBR,GAAmBD,CAAS,EACnD,QAASjlB,EAAI,EAAGA,EAAI0lB,EAAe,OAAQ1lB,IAAK,CAC9C,MAAM4Z,EAAS8L,EAAe1lB,CAAC,EAC/B,GAAIulB,GAAoB3L,CAAM,EAC5B,OAAOA,CAEV,CAEH,CACA,SAAS+L,GAAY/L,KAAWgM,EAAU,CACxC,OAAOd,EAAYlL,CAAM,EACzByL,GAAY,OAAQrV,IAClBA,EAAE4J,CAAM,EAAI1F,GAAU,IAAI,CAAClE,EAAE4J,CAAM,GAAK,GAAI,GAAGgM,CAAQ,CAAC,EACjD5V,EACR,CACH,CACiB6C,GACf,CAACwS,EAAW,EACZ,CAAC,CAACQ,CAAW,IAAM,OAAO,KAAKA,CAAW,CAC5C,EACAR,GAAY,UAAWS,GAAkBV,GAAaU,CAAa,EAEnE,MAAMC,GAAQ,GAId,SAASC,GAAsBpM,EAAQqM,EAAQ,CAC7CF,GAAMnM,CAAM,EAAE,OAAOqM,CAAM,EACvBF,GAAMnM,CAAM,EAAE,OAAS,GACzB,OAAOmM,GAAMnM,CAAM,CAEvB,CACA,SAASsM,GAAetM,EAAQ,CAC9B,OAAOmM,GAAMnM,CAAM,CACrB,CACA,SAASuM,GAAiBvM,EAAQ,CAChC,OAAOsL,GAAmBtL,CAAM,EAAE,IAAKwM,GAAe,CACpD,MAAMC,EAAcH,GAAeE,CAAU,EAC7C,MAAO,CAACA,EAAYC,EAAc,CAAC,GAAGA,CAAW,EAAI,EAAE,CAC3D,CAAG,EAAE,OAAO,CAAC,CAAG,CAAAA,CAAW,IAAMA,EAAY,OAAS,CAAC,CACvD,CACA,SAASC,GAAe1M,EAAQ,CAC9B,OAAIA,GAAU,KACL,GACFsL,GAAmBtL,CAAM,EAAE,KAC/ByM,GAAgB,CACf,IAAIrO,EACJ,OAAQA,EAAKkO,GAAeG,CAAW,IAAM,KAAO,OAASrO,EAAG,IACjE,CACL,CACA,CACA,SAASuO,GAAgB3M,EAAQyM,EAAa,CAO5C,OAN0B,QAAQ,IAChCA,EAAY,IAAKJ,IACfD,GAAsBpM,EAAQqM,CAAM,EAC7BA,EAAQ,EAAC,KAAMO,GAAYA,EAAQ,SAAWA,CAAO,EAC7D,CACL,EAC2B,KAAMZ,GAAaD,GAAY/L,EAAQ,GAAGgM,CAAQ,CAAC,CAC9E,CACA,MAAMa,GAAgB,GACtB,SAASC,GAAM9M,EAAQ,CACrB,GAAI,CAAC0M,GAAe1M,CAAM,EACxB,OAAIA,KAAU6M,GACLA,GAAc7M,CAAM,EAEtB,QAAQ,UAEjB,MAAM+M,EAASR,GAAiBvM,CAAM,EACtC,OAAA6M,GAAc7M,CAAM,EAAI,QAAQ,IAC9B+M,EAAO,IACL,CAAC,CAACC,EAAYP,CAAW,IAAME,GAAgBK,EAAYP,CAAW,CACvE,CACF,EAAC,KAAK,IAAM,CACX,GAAIC,GAAe1M,CAAM,EACvB,OAAO8M,GAAM9M,CAAM,EAErB,OAAO6M,GAAc7M,CAAM,CAC/B,CAAG,EACM6M,GAAc7M,CAAM,CAC7B,CAgBA,IAAIiN,GAAwB,OAAO,sBAC/BC,GAAiB,OAAO,UAAU,eAClCC,GAAiB,OAAO,UAAU,qBAClCC,GAAc,CAAC5S,EAAQ6S,IAAY,CACrC,IAAItZ,EAAS,GACb,QAASuZ,KAAQ9S,EACX0S,GAAe,KAAK1S,EAAQ8S,CAAI,GAAKD,EAAQ,QAAQC,CAAI,EAAI,IAC/DvZ,EAAOuZ,CAAI,EAAI9S,EAAO8S,CAAI,GAC9B,GAAI9S,GAAU,MAAQyS,GACpB,QAASK,KAAQL,GAAsBzS,CAAM,EACvC6S,EAAQ,QAAQC,CAAI,EAAI,GAAKH,GAAe,KAAK3S,EAAQ8S,CAAI,IAC/DvZ,EAAOuZ,CAAI,EAAI9S,EAAO8S,CAAI,GAEhC,OAAOvZ,CACT,EACA,MAAMwZ,GAAiB,CACrB,OAAQ,CACN,WAAY,CAAE,SAAU,YAAc,EACtC,YAAa,CAAE,SAAU,aAAe,EACxC,YAAa,CAAE,SAAU,UAAW,eAAgB,MAAQ,EAC5D,aAAc,CAAE,SAAU,UAAW,eAAgB,OAAS,CAC/D,EACD,KAAM,CACJ,MAAO,CAAE,MAAO,UAAW,IAAK,UAAW,KAAM,SAAW,EAC5D,OAAQ,CAAE,MAAO,QAAS,IAAK,UAAW,KAAM,SAAW,EAC3D,KAAM,CAAE,MAAO,OAAQ,IAAK,UAAW,KAAM,SAAW,EACxD,KAAM,CAAE,QAAS,OAAQ,MAAO,OAAQ,IAAK,UAAW,KAAM,SAAW,CAC1E,EACD,KAAM,CACJ,MAAO,CAAE,KAAM,UAAW,OAAQ,SAAW,EAC7C,OAAQ,CAAE,KAAM,UAAW,OAAQ,UAAW,OAAQ,SAAW,EACjE,KAAM,CACJ,KAAM,UACN,OAAQ,UACR,OAAQ,UACR,aAAc,OACf,EACD,KAAM,CACJ,KAAM,UACN,OAAQ,UACR,OAAQ,UACR,aAAc,OACf,CACF,CACH,EACA,SAASC,GAAyB,CAAE,OAAAxN,EAAQ,GAAAva,GAAM,CAChD,QAAQ,KACN,8BAA8BA,CAAE,uBAAuB6lB,GACrDtL,CACN,EAAM,KAAK,MAAM,CAAC,KAAK0M,GAAee,EAAkB,GAAI;AAAA;AAAA,2FAEiC,EAAE,EAC/F,CACA,CACA,MAAMC,GAAiB,CACrB,eAAgB,KAChB,aAAc,IACd,QAASH,GACT,sBAAuB,GACvB,qBAAsB,OACtB,UAAW,EACb,EACM/c,GAAUkd,GAChB,SAASC,IAAa,CACpB,OAAOnd,EACT,CACA,SAASgC,GAAK4U,EAAM,CAClB,MAAMhJ,EAAKgJ,EAAM,CAAE,QAAAsC,GAAYtL,EAAIwP,EAAOR,GAAYhP,EAAI,CAAC,SAAS,CAAC,EACrE,IAAIyP,EAAgBzG,EAAK,eACzB,GAAIA,EAAK,cACP,GAAI,CACEsD,GAAkB,cAActD,EAAK,aAAa,IACpDyG,EAAgBzG,EAAK,cAExB,MAAW,CACV,QAAQ,KACN,qCAAqCA,EAAK,aAAa,0BAC/D,CACK,CAEH,OAAIwG,EAAK,wBACP,OAAOA,EAAK,sBACRA,EAAK,sBAAwB,KAC/BA,EAAK,qBAAuBJ,GAE5B,QAAQ,KACN,uHACR,GAGE,OAAO,OAAOhd,GAASod,EAAM,CAAE,cAAAC,CAAe,GAC1CnE,IACE,WAAYA,GACd,OAAO,OAAOlZ,GAAQ,QAAQ,OAAQkZ,EAAQ,MAAM,EAElD,SAAUA,GACZ,OAAO,OAAOlZ,GAAQ,QAAQ,KAAMkZ,EAAQ,IAAI,EAE9C,SAAUA,GACZ,OAAO,OAAOlZ,GAAQ,QAAQ,KAAMkZ,EAAQ,IAAI,GAG7CoE,GAAQ,IAAID,CAAa,CAClC,CAEA,MAAME,GAAavV,GAAS,EAAK,EAEjC,IAAIwV,GAAc,OAAO,eACrBC,GAAa,OAAO,iBACpBC,GAAoB,OAAO,0BAC3BC,GAAwB,OAAO,sBAC/BC,GAAiB,OAAO,UAAU,eAClCC,GAAiB,OAAO,UAAU,qBAClCC,GAAoB,CAAC3a,EAAK1J,EAAKC,IAAUD,KAAO0J,EAAMqa,GAAYra,EAAK1J,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAO,GAAIyJ,EAAI1J,CAAG,EAAIC,EAC1JqkB,GAAmB,CAAClrB,EAAGC,IAAM,CAC/B,QAASgqB,KAAQhqB,IAAMA,EAAI,IACrB8qB,GAAe,KAAK9qB,EAAGgqB,CAAI,GAC7BgB,GAAkBjrB,EAAGiqB,EAAMhqB,EAAEgqB,CAAI,CAAC,EACtC,GAAIa,GACF,QAASb,KAAQa,GAAsB7qB,CAAC,EAClC+qB,GAAe,KAAK/qB,EAAGgqB,CAAI,GAC7BgB,GAAkBjrB,EAAGiqB,EAAMhqB,EAAEgqB,CAAI,CAAC,EAExC,OAAOjqB,CACT,EACImrB,GAAgB,CAACnrB,EAAGC,IAAM2qB,GAAW5qB,EAAG6qB,GAAkB5qB,CAAC,CAAC,EAChE,IAAI0Q,GACJ,MAAMya,GAAiBjW,GAAS,IAAI,EACpC,SAASkW,GAAcrD,EAAW,CAChC,OAAOA,EAAU,MAAM,GAAG,EAAE,IAAI,CAAC9e,EAAGnG,EAAGuoB,IAAQA,EAAI,MAAM,EAAGvoB,EAAI,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,SAChF,CACA,SAASklB,GAAmBD,EAAWuD,EAAiBjB,GAAU,EAAG,eAAgB,CACnF,MAAMnE,EAAUkF,GAAcrD,CAAS,EACvC,OAAIuD,EACK,CAAC,GAAmB,IAAI,IAAI,CAAC,GAAGpF,EAAS,GAAGkF,GAAcE,CAAc,CAAC,CAAC,CAAC,EAE7EpF,CACT,CACA,SAASiE,GAAmB,CAC1B,OAAOzZ,IAA4B,MACrC,CACAya,GAAe,UAAWI,GAAc,CACtC7a,GAAU6a,GAAgC,OACtC,OAAO,OAAW,KAAeA,GAAa,MAChD,SAAS,gBAAgB,aAAa,OAAQA,CAAS,CAE3D,CAAC,EACD,MAAMlW,GAAOkW,GAAc,CACzB,GAAIA,GAAahD,GAA0BgD,CAAS,GAAKnC,GAAemC,CAAS,EAAG,CAClF,KAAM,CAAE,aAAAC,GAAiBnB,KACzB,IAAIoB,EACJ,OAAI,OAAO,OAAW,KAAetB,EAAgB,GAAM,MAAQqB,EACjEC,EAAe,OAAO,WACpB,IAAMhB,GAAW,IAAI,EAAI,EACzBe,CACR,EAEMf,GAAW,IAAI,EAAI,EAEdjB,GAAM+B,CAAS,EAAE,KAAK,IAAM,CACjCJ,GAAe,IAAII,CAAS,CAClC,CAAK,EAAE,QAAQ,IAAM,CACf,aAAaE,CAAY,EACzBhB,GAAW,IAAI,EAAK,CAC1B,CAAK,CACF,CACD,OAAOU,GAAe,IAAII,CAAS,CACrC,EACMf,GAAUU,GAAcD,GAAiB,CAAE,EAAEE,EAAc,EAAG,CAClE,IAAA9V,EACF,CAAC,EAyBKqW,GAAyB,IACzB,OAAO,OAAW,IACb,KACF,OAAO,UAAU,UAAY,OAAO,UAAU,UAAU,CAAC,EAa5DC,GAAkB9rB,GAAO,CAC7B,MAAMmkB,EAAwB,OAAO,OAAO,IAAI,EAQhD,OAPoBO,GAAQ,CAC1B,MAAMC,EAAW,KAAK,UAAUD,CAAG,EACnC,OAAIC,KAAYR,EACPA,EAAMQ,CAAQ,EAEhBR,EAAMQ,CAAQ,EAAI3kB,EAAG0kB,CAAG,CACnC,CAEA,EAEA,IAAIqH,GAAY,OAAO,eACnBC,GAAsB,OAAO,sBAC7BC,GAAe,OAAO,UAAU,eAChCC,GAAe,OAAO,UAAU,qBAChCC,GAAkB,CAAC3b,EAAK1J,EAAKC,IAAUD,KAAO0J,EAAMub,GAAUvb,EAAK1J,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAO,GAAIyJ,EAAI1J,CAAG,EAAIC,EACtJqlB,GAAiB,CAAClsB,EAAGC,IAAM,CAC7B,QAASgqB,KAAQhqB,IAAMA,EAAI,IACrB8rB,GAAa,KAAK9rB,EAAGgqB,CAAI,GAC3BgC,GAAgBjsB,EAAGiqB,EAAMhqB,EAAEgqB,CAAI,CAAC,EACpC,GAAI6B,GACF,QAAS7B,KAAQ6B,GAAoB7rB,CAAC,EAChC+rB,GAAa,KAAK/rB,EAAGgqB,CAAI,GAC3BgC,GAAgBjsB,EAAGiqB,EAAMhqB,EAAEgqB,CAAI,CAAC,EAEtC,OAAOjqB,CACT,EACImsB,GAAY,CAAChV,EAAQ6S,IAAY,CACnC,IAAItZ,EAAS,GACb,QAASuZ,KAAQ9S,EACX4U,GAAa,KAAK5U,EAAQ8S,CAAI,GAAKD,EAAQ,QAAQC,CAAI,EAAI,IAC7DvZ,EAAOuZ,CAAI,EAAI9S,EAAO8S,CAAI,GAC9B,GAAI9S,GAAU,MAAQ2U,GACpB,QAAS7B,KAAQ6B,GAAoB3U,CAAM,EACrC6S,EAAQ,QAAQC,CAAI,EAAI,GAAK+B,GAAa,KAAK7U,EAAQ8S,CAAI,IAC7DvZ,EAAOuZ,CAAI,EAAI9S,EAAO8S,CAAI,GAEhC,OAAOvZ,CACT,EACA,MAAM0b,GAA0B,CAACjmB,EAAMsE,IAAS,CAC9C,KAAM,CAAE,QAAA4b,GAAYiE,KACpB,GAAInkB,KAAQkgB,GAAW5b,KAAQ4b,EAAQlgB,CAAI,EACzC,OAAOkgB,EAAQlgB,CAAI,EAAEsE,CAAI,EAE3B,MAAM,IAAI,MAAM,0BAA0BA,CAAI,KAAKtE,CAAI,UAAU,CACnE,EACMkmB,GAAwBT,GAC3B7Q,GAAO,CACN,IAAIuR,EAAKvR,EAAI,CAAE,OAAA4B,EAAQ,OAAA4P,CAAM,EAAKD,EAAInf,EAAUgf,GAAUG,EAAI,CAAC,SAAU,QAAQ,CAAC,EAClF,GAAI3P,GAAU,KACZ,MAAM,IAAI,MAAM,wDAAwD,EAE1E,OAAI4P,IACFpf,EAAUif,GAAwB,SAAUG,CAAM,GAE7C,IAAI,KAAK,aAAa5P,EAAQxP,CAAO,CAC7C,CACH,EACMqf,GAAsBZ,GACzBa,GAAO,CACN,IAAIC,EAAKD,EAAI,CAAE,OAAA9P,EAAQ,OAAA4P,CAAM,EAAKG,EAAIvf,EAAUgf,GAAUO,EAAI,CAAC,SAAU,QAAQ,CAAC,EAClF,GAAI/P,GAAU,KACZ,MAAM,IAAI,MAAM,sDAAsD,EAExE,OAAI4P,EACFpf,EAAUif,GAAwB,OAAQG,CAAM,EACvC,OAAO,KAAKpf,CAAO,EAAE,SAAW,IACzCA,EAAUif,GAAwB,OAAQ,OAAO,GAE5C,IAAI,KAAK,eAAezP,EAAQxP,CAAO,CAC/C,CACH,EACMwf,GAAsBf,GACzBgB,GAAO,CACN,IAAIC,EAAKD,EAAI,CAAE,OAAAjQ,EAAQ,OAAA4P,CAAM,EAAKM,EAAI1f,EAAUgf,GAAUU,EAAI,CAAC,SAAU,QAAQ,CAAC,EAClF,GAAIlQ,GAAU,KACZ,MAAM,IAAI,MACR,4DACR,EAEI,OAAI4P,EACFpf,EAAUif,GAAwB,OAAQG,CAAM,EACvC,OAAO,KAAKpf,CAAO,EAAE,SAAW,IACzCA,EAAUif,GAAwB,OAAQ,OAAO,GAE5C,IAAI,KAAK,eAAezP,EAAQxP,CAAO,CAC/C,CACH,EACM2f,GAAqB,CAACC,EAAK,KAAO,CACtC,IAAIC,EAAKD,EAAI,CACX,OAAApQ,EAASyN,EAAkB,CAC5B,EAAG4C,EAAIpI,EAAOuH,GAAUa,EAAI,CAC3B,QACJ,CAAG,EACD,OAAOX,GAAsBH,GAAe,CAAE,OAAAvP,CAAM,EAAIiI,CAAI,CAAC,CAC/D,EACMqI,GAAmB,CAACvS,EAAK,KAAO,CACpC,IAAIwS,EAAKxS,EAAI,CACX,OAAAiC,EAASyN,EAAkB,CAC5B,EAAG8C,EAAItI,EAAOuH,GAAUe,EAAI,CAC3B,QACJ,CAAG,EACD,OAAOV,GAAoBN,GAAe,CAAE,OAAAvP,CAAM,EAAIiI,CAAI,CAAC,CAC7D,EACMuI,GAAmB,CAACC,EAAK,KAAO,CACpC,IAAIC,EAAKD,EAAI,CACX,OAAAzQ,EAASyN,EAAkB,CAC5B,EAAGiD,EAAIzI,EAAOuH,GAAUkB,EAAI,CAC3B,QACJ,CAAG,EACD,OAAOV,GAAoBT,GAAe,CAAE,OAAAvP,CAAM,EAAIiI,CAAI,CAAC,CAC7D,EACM0I,GAAsB1B,GAE1B,CAACpiB,EAASmT,EAASyN,MAAuB,IAAI/C,GAAkB7d,EAASmT,EAAQ2N,GAAY,EAAC,QAAS,CACrG,UAAWA,GAAU,EAAG,SAC5B,CAAG,CACH,EAEMiD,GAAgB,CAACnrB,EAAI+K,EAAU,KAAO,CAC1C,IAAI4N,EAAIuR,EAAIG,EAAIC,EAChB,IAAIc,EAAargB,EACb,OAAO/K,GAAO,WAChBorB,EAAaprB,EACbA,EAAKorB,EAAW,IAElB,KAAM,CACJ,OAAA9b,EACA,OAAAiL,EAASyN,EAAkB,EAC3B,QAASqD,CACV,EAAGD,EACJ,GAAI7Q,GAAU,KACZ,MAAM,IAAI,MACR,iFACN,EAEE,IAAInT,EAAUue,GAAO3lB,EAAIua,CAAM,EAC/B,GAAI,CAACnT,EACHA,GAAWkjB,GAAMD,GAAMH,GAAMvR,EAAKuP,GAAU,GAAI,uBAAyB,KAAO,OAASgC,EAAG,KAAKvR,EAAI,CAAE,OAAA4B,EAAQ,GAAAva,EAAI,aAAAqrB,CAAc,KAAM,KAAOhB,EAAKgB,IAAiB,KAAOf,EAAKtqB,UACvK,OAAOoH,GAAY,SAC5B,eAAQ,KACN,kCAAkCpH,CAAE,uCAAuC,OAAOoH,CAAO,gGAC/F,EACWA,EAET,GAAI,CAACkI,EACH,OAAOlI,EAET,IAAIwC,EAASxC,EACb,GAAI,CACFwC,EAASshB,GAAoB9jB,EAASmT,CAAM,EAAE,OAAOjL,CAAM,CAC5D,OAAQpO,EAAG,CACNA,aAAa,OACf,QAAQ,KACN,0BAA0BlB,CAAE,sBAC5BkB,EAAE,OACV,CAEG,CACD,OAAO0I,CACT,EACM0hB,GAAa,CAACxa,EAAG/F,IACdggB,GAAiBhgB,CAAO,EAAE,OAAO+F,CAAC,EAErCya,GAAa,CAAC5a,EAAG5F,IACd8f,GAAiB9f,CAAO,EAAE,OAAO4F,CAAC,EAErC6a,GAAe,CAAC1iB,EAAGiC,IAChB2f,GAAmB3f,CAAO,EAAE,OAAOjC,CAAC,EAEvC2iB,GAAU,CAACzrB,EAAIua,EAASyN,EAAgB,IACrCrC,GAAO3lB,EAAIua,CAAM,EAEpBmR,GAAUlY,GAAQ,CAAC6U,GAASrC,EAAW,EAAG,IAAMmF,EAAa,EAC/C3X,GAAQ,CAAC6U,EAAO,EAAG,IAAMiD,EAAU,EACnC9X,GAAQ,CAAC6U,EAAO,EAAG,IAAMkD,EAAU,EACjC/X,GAAQ,CAAC6U,EAAO,EAAG,IAAMmD,EAAY,EAC1ChY,GAAQ,CAAC6U,GAASrC,EAAW,EAAG,IAAMyF,EAAO,EChjB9D,IAAIE,GAA+B,GAGlC,iBAAkB,QAAQ,WAC1B,uBAAwB,SAAS,YAMjCA,GAA+B,uBAHN,SACvB,cAAc,KAAK,EACnB,aAAa,CAAE,KAAM,OAAQ,GAIhB,SAAAC,GAAUjnB,EAAa2J,EAAoC,CAC1E,MAAMud,EAAO,IAAI,IAAI,YAAY,GAAG,EAAE,OAChCC,EAAO,IAAI,IAAInnB,EAAKknB,CAAI,EAAE,KAG5B,GAFkB,SAAS,cAAc,cAAcC,CAAI,IAAI,EAEhD,OAAO,QAAQ,UAE5B,MAAAC,EAAO,SAAS,cAAc,MAAM,EAC1C,OAAAA,EAAK,IAAM,aACXA,EAAK,KAAOD,EAEL,IAAI,QAAQ,CAACvqB,EAAKyqB,IAAQ,CAChCD,EAAK,iBAAiB,OAAQ,IAAMxqB,EAAK,GACpCwqB,EAAA,iBAAiB,QAAS,IAAM,CAC5B,cAAM,6BAA6BD,CAAI,EAAE,EAC7CvqB,GAAA,CACJ,EACD+M,EAAO,YAAYyd,CAAI,EACvB,CACF,CAEO,SAASE,GACfC,EACAC,EACAC,EAAgB,SAAS,cAAc,OAAO,EACpB,CAC1B,GAAI,CAACT,GAAqC,YAC1CS,EAAc,OAAO,EAEf,MAAAC,EAAa,IAAI,cACvBA,EAAW,YAAYH,CAAM,EAE7B,IAAII,EAAe,GACnBJ,EAASA,EAAO,QAAQ,8BAA+B,CAACzgB,EAAO9G,KAC9D2nB,GAAgB,eAAe3nB,CAAG;AAAA,EAC3B,GACP,EAED,MAAM4nB,EAAQF,EAAW,SAEzB,IAAIG,EAAa,GACbC,EAAmB,iDAAiDN,CAAO,aAE/E,QAASxrB,EAAI,EAAGA,EAAI4rB,EAAM,OAAQ5rB,IAAK,CAChC,MAAA6jB,EAAO+H,EAAM5rB,CAAC,EAEpB,IAAI+rB,EAAelI,EAAK,QAAQ,SAAS,OAAO,EAChD,GAAIA,aAAgB,aAAc,CACjC,MAAMhE,EAAWgE,EAAK,aACtB,GAAIhE,EAAU,CACP,MAAAmM,EAAenM,EACnB,QAAQ,QAAS,EAAE,EACnB,MAAM,GAAG,EACT,IACClK,GACA,GAAGoW,EAAe,QAAU,EAAE,IAAID,CAAgB,IAAInW,EAAE,MAAM,KAE/D,KAAK,GAAG,EAEVkW,GAAchI,EAAK,QACnBgI,GAAchI,EAAK,QAAQ,QAAQhE,EAAUmM,CAAY,CAC1D,UACUnI,aAAgB,aAAc,CACxC,IAAIoI,EAAiB,UAAUpI,EAAK,MAAM,SAAS,KACnD,QAASqI,EAAI,EAAGA,EAAIrI,EAAK,SAAS,OAAQqI,IAAK,CACxC,MAAAC,EAAYtI,EAAK,SAASqI,CAAC,EACjC,GAAIC,aAAqB,aAAc,CACtC,IAAIJ,EAAeI,EAAU,QAAQ,SAAS,QAAQ,EACtD,MAAMtM,EAAWsM,EAAU,aACrBH,EAAenM,EACnB,QAAQ,QAAS,EAAE,EACnB,MAAM,GAAG,EACT,IACClK,GACA,GACCoW,EAAe,QAAU,EAC1B,IAAID,CAAgB,IAAInW,EAAE,MAAM,KAEjC,KAAK,GAAG,EACVsW,GAAkBE,EAAU,QAAQ,QAAQtM,EAAUmM,CAAY,CACnE,CACD,CACkBC,GAAA,IACJJ,GAAAI,CAAA,SACJpI,aAAgB,iBAAkB,CAC9BgI,GAAA,cAAchI,EAAK,IAAI,KACrC,QAASqI,EAAI,EAAGA,EAAIrI,EAAK,SAAS,OAAQqI,IAAK,CACxC,MAAAC,EAAYtI,EAAK,SAASqI,CAAC,EAC7BC,aAAqB,kBACxBN,GAAc,GAAGM,EAAU,OAAO,MAAMA,EAAU,MAAM,OAAO,KAEjE,CACcN,GAAA,SACJhI,aAAgB,kBACZgI,GAAA,gBAAgBhI,EAAK,MAAM,OAAO,KAElD,CACA,OAAAgI,EAAaF,EAAeE,EAC5BJ,EAAc,YAAcI,EAEnB,cAAK,YAAYJ,CAAa,EAChCA,CACR,u3qBCjHMW,GAAQ,gCAAAC,GAAA,iBAAAC,GAAA,kBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,oBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,oBAAAC,GAAA,oBAAAC,EAAA,GAWP,SAASC,IAA6B,CAC5C,IAAIC,EAAsB,GAE1B,UAAWC,KAAQ5B,GAAO,CACnB,MAAApQ,EAAQgS,EAAK,MAAM,GAAG,EAAE,IAAiB,QAAM,GAAG,EAAE,QAC1DD,EAAO/R,CAAI,EAAKoQ,GAAM4B,CAAI,EAA0B,OACrD,CAEO,OAAAD,CACR,CAEA,MAAME,GAAkBH,GAAc,EAEtC,UAAWE,KAAQC,GACNtI,GAAAqI,EAAMC,GAAgBD,CAAI,CAAC,EAGxC,eAAsBE,IAA2B,CAChD,MAAM9hB,GAAK,CACV,eAAgB,KAChB,cAAewc,GAAuB,EACtC,CACF,CCnBA,MAAMuF,GAAY,gBAElB,IAAIC,GAEJA,GAAQ,gBAER,IAAIC,GACAC,GACAlb,GAAU,IAAI,QAASxS,GAAQ,CAC3B0tB,GAAA1tB,CACR,CAAC,EACD,eAAe2tB,IAA2B,CACvBF,IAAA,aAAM,OAAO,qBAAgB,EAAG,KAAAlmB,KAAA,oDAC7CmmB,IACN,CAEA,SAASE,IAA8B,CACtC,MAAMC,EAAI,CACT,gBAAiBC,GAAO,iBAEzB,UAAW7qB,KAAO6qB,GACb7qB,IAAQ,oBACRA,IAAQ,qBAET4qB,EAAA5qB,CAAG,EAAI4qB,EAAE,gBAGTA,EAAA5qB,CAAG,EAAI6qB,GAAO7qB,CAAG,GAIrB,OAAO,2BAA6B4qB,EACpC,MAAME,UAAkB,WAAY,CAgBnC,aAAc,CACP,QACD,UAAO,KAAK,aAAa,MAAM,EAC/B,WAAQ,KAAK,aAAa,OAAO,EACjC,SAAM,KAAK,aAAa,KAAK,EAE7B,wBAAqB,KAAK,aAAa,oBAAoB,EAChE,KAAK,eAAiB,KAAK,aAAa,gBAAgB,GAAK,QAC7D,KAAK,SAAW,KAAK,aAAa,OAAO,GAAK,OAC9C,KAAK,UAAY,KAAK,aAAa,WAAW,GAAK,OACnD,KAAK,KAAO,KAAK,aAAa,MAAM,GAAK,GACpC,gBAAa,KAAK,aAAa,YAAY,EAC3C,WAAQ,KAAK,aAAa,OAAO,EACjC,gBAAa,KAAK,aAAa,YAAY,EAChD,KAAK,SAAW,GAChB,KAAK,QAAU,EAChB,CAEA,MAAM,mBAAmC,CACxC,MAAMJ,GAAU,EAChB,KAAK,QAAU,GAEX,KAAK,KACR,KAAK,IAAI,WAGN,OAAOH,IAAU,UACpBA,GAAM,QAASppB,GAAMimB,GAAUjmB,EAAG,SAAS,IAAI,CAAC,EAG3C,MAAAimB,GAAUkD,GAAW,SAAS,IAAI,EAElC,MAAAliB,EAAQ,IAAI,YAAY,YAAa,CAC1C,QAAS,GACT,WAAY,GACZ,SAAU,GACV,EAEgB,IAAI,iBAAkB2iB,GAAc,CACpD,KAAK,cAAc3iB,CAAK,EACxB,EAEQ,QAAQ,KAAM,CAAE,UAAW,EAAM,GAErC,SAAM,IAAIoiB,GAAe,CAC7B,OAAQ,KACR,MAAO,CAEN,MAAO,KAAK,MAAQ,KAAK,MAAM,OAAS,KAAK,MAC7C,IAAK,KAAK,IAAM,KAAK,IAAI,OAAS,KAAK,IACvC,KAAM,KAAK,KAAO,KAAK,KAAK,OAAS,KAAK,KAE1C,KAAM,KAAK,OAAS,QACpB,UAAW,KAAK,YAAc,QAC9B,SAAU,KAAK,WAAa,QAC5B,eAAgB,KAAK,eACrB,MAAO,KAAK,QAAU,OAEtB,QAAS,SACT,WAAY,KAAK,WAEjB,WAAY,KAAK,aAAe,OAChC,mBAAoB,KAAK,qBAAuB,OAEhD,OAAA3jB,GAGA,SAAU,OAAO,kBAAoB,KACtC,EACA,EAEG,KAAK,UACR,KAAK,aAAa,KAAK,SAAS,KAAM,KAAK,SAAS,KAAK,EAG1D,KAAK,QAAU,EAChB,CAEA,WAAW,oBAA+C,CAClD,OAAC,MAAO,QAAS,MAAM,CAC/B,CAEA,MAAM,yBACLhD,EACAmnB,EACAC,EACgB,CAEhB,GADM,MAAA1b,IAEJ1L,IAAS,QAAUA,IAAS,SAAWA,IAAS,QACjDonB,IAAYD,EACX,CAED,GADA,KAAK,SAAW,CAAE,KAAAnnB,EAAM,MAAOonB,CAAQ,EACnC,KAAK,QAAS,OAEd,KAAK,KACR,KAAK,IAAI,WAGV,KAAK,MAAQ,KACb,KAAK,KAAO,KACZ,KAAK,IAAM,KAEPpnB,IAAS,OACZ,KAAK,KAAOonB,EACFpnB,IAAS,QACnB,KAAK,MAAQonB,EACHpnB,IAAS,QACnB,KAAK,IAAMonB,GAGP,SAAM,IAAIT,GAAe,CAC7B,OAAQ,KACR,MAAO,CAEN,MAAO,KAAK,MAAQ,KAAK,MAAM,OAAS,KAAK,MAC7C,IAAK,KAAK,IAAM,KAAK,IAAI,OAAS,KAAK,IACvC,KAAM,KAAK,KAAO,KAAK,KAAK,OAAS,KAAK,KAE1C,KAAM,KAAK,OAAS,QACpB,UAAW,KAAK,YAAc,QAC9B,SAAU,KAAK,WAAa,QAC5B,eAAgB,KAAK,eACrB,MAAO,KAAK,QAAU,OAEtB,QAAS,SACT,WAAY,KAAK,WAEjB,WAAY,KAAK,aAAe,OAChC,mBACC,KAAK,qBAAuB,OAE7B,OAAA3jB,GAGA,SAAU,OAAO,kBAAoB,KACtC,EACA,EAED,KAAK,SAAW,EACjB,CACD,CACD,CACK,eAAe,IAAI,YAAY,GACpB,sBAAO,aAAcikB,CAAS,CAC/C,CAEAH,GAAsB", "names": ["fn", "semiver", "a", "b", "bool", "HOST_URL", "UPLOAD_URL", "LOGIN_URL", "CONFIG_URL", "API_INFO_URL", "RUNTIME_URL", "SLEEPTIME_URL", "SPACE_FETCHER_URL", "QUEUE_FULL_MSG", "BROKEN_CONNECTION_MSG", "CONFIG_ERROR_MSG", "SPACE_STATUS_ERROR_MSG", "API_INFO_ERROR_MSG", "SPACE_METADATA_ERROR_MSG", "INVALID_URL_MSG", "UNAUTHORIZED_MSG", "INVALID_CREDENTIALS_MSG", "MISSING_CREDENTIALS_MSG", "NODEJS_FS_ERROR_MSG", "ROOT_URL_ERROR_MSG", "FILE_PROCESSING_ERROR_MSG", "resolve_root", "base_url", "root_path", "prioritize_base", "get_jwt", "space", "token", "cookies", "map_names_to_ids", "fns", "apis", "api_name", "id", "resolve_config", "endpoint", "headers", "path", "config", "config_root", "config_url", "join_urls", "response", "dep", "i", "resolve_cookies", "http_protocol", "host", "process_endpoint", "cookie_header", "get_cookie_header", "e", "auth", "_fetch", "hf_token", "formData", "res", "determine_protocol", "protocol", "pathname", "parse_and_set_cookies", "cookie", "cookie_name", "cookie_value", "RE_SPACE_NAME", "RE_SPACE_DOMAIN", "app_reference", "_app_reference", "_host", "ws_protocol", "urls", "part", "transform_api_info", "api_info", "api_map", "transformed_info", "category", "parameters", "returns", "dependencyIndex", "dependencyTypes", "components", "input", "c", "comp", "idx", "new_param", "transform_type", "data", "component", "serializer", "signature_type", "get_description", "get_type", "p", "r", "type", "handle_message", "last_status", "map_data_to_params", "endpoint_info", "resolved_data", "provided_keys", "param", "index", "key", "value", "view_api", "url", "upload_files", "root_url", "files", "upload_id", "chunkSize", "uploadResponses", "chunk", "file", "upload_url", "error_text", "output", "upload", "file_data", "max_file_size", "oversized_files", "f", "FileData", "prepare_files", "is_stream", "orig_name", "size", "blob", "mime_type", "alt_text", "Command", "command", "meta", "update_object", "object", "newValue", "stack", "walk_and_store_blobs", "root", "blob_refs", "_", "new_path", "array_refs", "skip_queue", "fn_queue", "post_message", "message", "origin", "_rej", "channel", "handle_payload", "resolved_payload", "dependency", "with_null_state", "updated_payload", "payload_index", "deps", "input_id", "handle_blob", "self", "process_local_file_commands", "blobRefs", "file_url", "name", "client", "recursively_process_commands", "process_single_command", "cmd_item", "fileBuffer", "fullPath", "fs", "__vitePreload", "n", "fileData", "error", "post_data", "body", "additional_headers", "status", "predict", "data_returned", "status_complete", "trimmed_endpoint", "resolve", "reject", "app", "result", "check_space_status", "status_callback", "_status", "stage", "space_name", "discussions_enabled", "check_and_wake_space", "space_id", "retries", "max_retries", "check_interval", "RE_DISABLED_DISCUSSION", "get_space_hardware", "hardware", "set_space_timeout", "timeout", "hardware_types", "duplicate", "options", "_private", "v", "user", "original_hardware", "requested_hardware", "Client", "duplicated_space", "get_space_reference", "regex", "match", "TextLineStream", "#currentLine", "chars", "controller", "lfIndex", "crIndex", "endIndex", "currentLine", "stream", "decoder", "split", "fallback", "events", "signal", "iter", "utils.stream", "line", "reader", "event", "field", "utils.split", "init", "req", "utils.fallback", "open_stream", "event_callbacks", "unclosed_events", "pending_stream_messages", "stream_status", "jwt", "that", "params", "_data", "close_stream", "event_id", "abort_controller", "apply_diff_stream", "pending_diff_streams", "new_data", "apply_diff", "obj", "diff", "action", "apply_edit", "target", "current", "last_path", "readable_stream", "instance", "submit", "event_data", "trigger_id", "all_events", "fire_event", "events_to_publish", "push_event", "close", "done", "resolvers", "push", "values", "push_error", "thenable_reject", "next", "fetch", "session_hash", "fn_index", "get_endpoint_info", "websocket", "_endpoint", "payload", "complete", "url_params", "acc", "cancel", "reset_request", "cancel_request", "resolve_heartbeat", "handle_render_config", "render_config", "render_id", "d", "any_state", "any_unload", "t", "_payload", "status_code", "evt", "hostname", "is_iframe", "is_zerogpu_space", "callback", "msg", "iterator", "ws", "_config", "heartbeat_url", "component_id", "fn_name", "raw_cookies", "noop", "identity", "x", "run", "run_all", "is_function", "thing", "safe_not_equal", "subscribe", "store", "callbacks", "unsub", "get_store_value", "split_css_unit", "subscriber_queue", "readable", "start", "writable", "stop", "subscribers", "set", "new_value", "run_queue", "subscriber", "update", "invalidate", "derived", "stores", "initial_value", "single", "stores_array", "auto", "started", "pending", "cleanup", "sync", "unsubscribers", "isMergeableObject", "isNonNullObject", "isSpecial", "stringValue", "isReactElement", "canUseSymbol", "REACT_ELEMENT_TYPE", "emptyTarget", "val", "cloneUnlessOtherwiseSpecified", "deepmerge", "defaultArrayMerge", "source", "element", "getMergeFunction", "customMerge", "getEnumerableOwnPropertySymbols", "symbol", "get<PERSON><PERSON><PERSON>", "propertyIsOnObject", "property", "propertyIsUnsafe", "mergeObject", "destination", "sourceIsArray", "targetIsArray", "sourceAndTargetTypesMatch", "array", "prev", "deepmerge_1", "cjs", "extendStatics", "__extends", "__", "__assign", "s", "__rest", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "ar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TYPE", "SKELETON_TYPE", "isLiteralElement", "el", "isArgumentElement", "isNumberElement", "isDateElement", "isTimeElement", "isSelectElement", "isPluralElement", "isPoundElement", "isTagElement", "isNumberSkeleton", "isDateTimeSkeleton", "SPACE_SEPARATOR_REGEX", "DATE_TIME_REGEX", "parseDateTimeSkeleton", "skeleton", "len", "WHITE_SPACE_REGEX", "parseNumberSkeletonFromString", "stringTokens", "tokens", "_i", "stringTokens_1", "stringToken", "stemAndOptions", "stem", "_a", "options_1", "option", "icuUnitToEcma", "unit", "FRACTION_PRECISION_REGEX", "SIGNIFICANT_PRECISION_REGEX", "INTEGER_WIDTH_REGEX", "CONCISE_INTEGER_WIDTH_REGEX", "parseSignificantPrecision", "str", "g1", "g2", "parseSign", "parseConciseScientificAndEngineeringStem", "signDisplay", "parseNotationOptions", "opt", "signOpts", "parseNumberSkeleton", "tokens_1", "all", "g3", "g4", "g5", "conciseScientificAndEngineeringOpts", "timeData", "getBestPattern", "locale", "skeletonCopy", "patternPos", "patternChar", "extraLength", "hourLen", "dayPeriodLen", "dayPeriodChar", "hourChar", "getDefaultHourSymbolFromLocale", "hourCycle", "languageTag", "regionTag", "hourCycles", "SPACE_SEPARATOR_START_REGEX", "SPACE_SEPARATOR_END_REGEX", "createLocation", "end", "hasNativeStartsWith", "hasNativeFromCodePoint", "hasNativeFromEntries", "hasNativeCodePointAt", "hasTrimStart", "hasTrimEnd", "hasNativeIsSafeInteger", "isSafeInteger", "REGEX_SUPPORTS_U_AND_Y", "re", "RE", "startsWith", "search", "position", "fromCodePoint", "codePoints", "elements", "length", "code", "fromEntries", "entries", "entries_1", "k", "codePointAt", "first", "second", "trimStart", "trimEnd", "flag", "matchIdentifierAtIndex", "IDENTIFIER_PREFIX_RE_1", "_isWhiteSpace", "_isPatternSyntax", "<PERSON><PERSON><PERSON>", "nestingLevel", "parentArgType", "expectingCloseTag", "char", "_isAlpha", "startPosition", "tagName", "childrenResult", "children", "endTagStartPosition", "closingTagNameStartPosition", "closingTagName", "startOffset", "_isPotentialElementNameChar", "parseQuoteResult", "parseUnquotedResult", "parseLeftAngleResult", "location", "_isAlphaOrSlash", "ch", "openingBracePosition", "startingPosition", "endOffset", "endPosition", "typeStartPosition", "argType", "typeEndPosition", "styleAndLocation", "styleStartPosition", "style", "styleLocation", "argCloseResult", "location_1", "dateTimePattern", "typeEndPosition_1", "identifierAndLocation", "pluralOffset", "optionsResult", "location_2", "nestedBraces", "apostrophePosition", "expectCloseTag", "parsedFirstIdentifier", "has<PERSON>ther<PERSON><PERSON><PERSON>", "parsedSelectors", "selector", "selectorLocation", "fragmentResult", "expectNumberError", "invalidNumberError", "sign", "hasDigits", "decimal", "offset", "kind", "prefix", "pattern", "currentOffset", "targetOffset", "nextCode", "codepoint", "pruneLocation", "els", "parse", "opts", "memoize", "cache", "cacheDefault", "serializerDefault", "strategy", "strategyDefault", "isPrimitive", "monadic", "arg", "cache<PERSON>ey", "computedValue", "variadic", "args", "assemble", "context", "serialize", "strategyVariadic", "strategyMonadic", "ObjectWithoutPrototypeCache", "strategies", "ErrorCode", "FormatError", "_super", "originalMessage", "_this", "InvalidValueError", "variableId", "InvalidValueTypeError", "Missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PART_TYPE", "mergeLiteral", "parts", "lastPart", "isFormatXMLElementFn", "formatToParts", "locales", "formatters", "formats", "currentPluralValue", "els_1", "varName", "value_1", "formatFn", "chunks", "rule", "mergeConfig", "c1", "c2", "mergeConfigs", "defaultConfig", "configs", "createFastMemoizeCache", "createDefaultFormatters", "IntlMessageFormat", "overrideFormats", "parseOpts", "supportedLocales", "delve", "<PERSON><PERSON><PERSON>", "keys", "<PERSON><PERSON><PERSON>", "lookup<PERSON>ache", "addToCache", "lookup", "refLocale", "getPossibleLocales", "getMessageFromDictionary", "dictionary", "$dictionary", "getLocaleDictionary", "hasLocaleDictionary", "localeDictionary", "getClosestAvailableLocale", "relatedLocales", "addMessages", "partials", "dictionary2", "newDictionary", "queue", "removeLoaderFromQ<PERSON>ue", "loader", "getLocaleQueue", "getLocalesQueues", "localeItem", "localeQueue", "hasLocaleQueue", "loadLocaleQueue", "partial", "activeFlushes", "flush", "queues", "localeName", "__getOwnPropSymbols$2", "__hasOwnProp$2", "__propIsEnum$2", "__objRest$1", "exclude", "prop", "defaultFormats", "defaultMissingKeyHandler", "getCurrentLocale", "defaultOptions", "getOptions", "rest", "initialLocale", "$locale", "$isLoading", "__defProp$1", "__defProps", "__getOwnPropDescs", "__getOwnPropSymbols$1", "__hasOwnProp$1", "__propIsEnum$1", "__defNormalProp$1", "__spreadValues$1", "__spreadProps", "internalLocale", "getSubLocales", "arr", "fallback<PERSON><PERSON><PERSON>", "newLocale", "loadingDelay", "loadingTimer", "getLocaleFromNavigator", "monadicMemoize", "__defProp", "__getOwnPropSymbols", "__hasOwnProp", "__propIsEnum", "__defNormalProp", "__spreadValues", "__objRest", "getIntlFormatterOptions", "createNumberFormatter", "_b", "format", "createDateFormatter", "_c", "_d", "createTimeFormatter", "_e", "_f", "getNumberFormatter", "_g", "_h", "getDate<PERSON><PERSON><PERSON><PERSON>", "_j", "getTimeFormatter", "_k", "_l", "getMessageFormatter", "formatMessage", "messageObj", "defaultValue", "formatTime", "formatDate", "formatNumber", "getJSON", "$format", "supports_adopted_stylesheets", "mount_css", "base", "_url", "link", "rej", "prefix_css", "string", "version", "style_element", "stylesheet", "importString", "rules", "css_string", "gradio_css_infix", "is_dark_rule", "new_selector", "mediaCssString", "j", "innerRule", "langs", "__vite_glob_0_0", "__vite_glob_0_1", "__vite_glob_0_2", "__vite_glob_0_3", "__vite_glob_0_4", "__vite_glob_0_5", "__vite_glob_0_6", "__vite_glob_0_7", "__vite_glob_0_8", "__vite_glob_0_9", "__vite_glob_0_10", "__vite_glob_0_11", "__vite_glob_0_12", "__vite_glob_0_13", "__vite_glob_0_14", "__vite_glob_0_15", "__vite_glob_0_16", "__vite_glob_0_17", "__vite_glob_0_18", "__vite_glob_0_19", "__vite_glob_0_20", "__vite_glob_0_21", "__vite_glob_0_22", "__vite_glob_0_23", "__vite_glob_0_24", "process_langs", "_langs", "lang", "processed_langs", "setupi18n", "ENTRY_CSS", "FONTS", "IndexComponent", "_res", "get_index", "create_custom_element", "o", "svelte", "GradioApp", "mutations", "old_val", "new_val"], "ignoreList": [0, 14, 15, 16, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38], "sources": ["../../../../node_modules/.pnpm/semiver@1.1.0/node_modules/semiver/dist/semiver.mjs", "../../../../client/js/src/constants.ts", "../../../../client/js/src/helpers/init_helpers.ts", "../../../../client/js/src/helpers/api_info.ts", "../../../../client/js/src/utils/view_api.ts", "../../../../client/js/src/utils/upload_files.ts", "../../../../client/js/src/upload.ts", "../../../../client/js/src/types.ts", "../../../../client/js/src/helpers/data.ts", "../../../../client/js/src/utils/handle_blob.ts", "../../../../client/js/src/utils/post_data.ts", "../../../../client/js/src/utils/predict.ts", "../../../../client/js/src/helpers/spaces.ts", "../../../../client/js/src/utils/duplicate.ts", "../../../../node_modules/.pnpm/fetch-event-stream@0.1.5/node_modules/fetch-event-stream/esm/deps/jsr.io/@std/streams/0.221.0/text_line_stream.js", "../../../../node_modules/.pnpm/fetch-event-stream@0.1.5/node_modules/fetch-event-stream/esm/utils.js", "../../../../node_modules/.pnpm/fetch-event-stream@0.1.5/node_modules/fetch-event-stream/esm/mod.js", "../../../../client/js/src/utils/stream.ts", "../../../../client/js/src/utils/submit.ts", "../../../../client/js/src/client.ts", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/utils.js", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/store/index.js", "../../../../node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge/dist/cjs.js", "../../../../node_modules/.pnpm/tslib@2.6.2/node_modules/tslib/tslib.es6.mjs", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.6/node_modules/@formatjs/icu-messageformat-parser/lib/error.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.6/node_modules/@formatjs/icu-messageformat-parser/lib/types.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.6/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js", "../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.0/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js", "../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.0/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js", "../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.0/node_modules/@formatjs/icu-skeleton-parser/lib/number.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.6/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.6/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.6/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.6/node_modules/@formatjs/icu-messageformat-parser/lib/index.js", "../../../../node_modules/.pnpm/@formatjs+fast-memoize@2.2.0/node_modules/@formatjs/fast-memoize/lib/index.js", "../../../../node_modules/.pnpm/intl-messageformat@10.5.11/node_modules/intl-messageformat/lib/src/error.js", "../../../../node_modules/.pnpm/intl-messageformat@10.5.11/node_modules/intl-messageformat/lib/src/formatters.js", "../../../../node_modules/.pnpm/intl-messageformat@10.5.11/node_modules/intl-messageformat/lib/src/core.js", "../../../../node_modules/.pnpm/svelte-i18n@4.0.0_svelte@4.2.15/node_modules/svelte-i18n/dist/runtime.js", "../../../../js/core/src/css.ts", "../../../../js/core/src/i18n.ts", "../../../../js/spa/src/main.ts"], "sourcesContent": ["var fn = new Intl.Collator(0, { numeric:1 }).compare;\n\nexport default function (a, b, bool) {\n\ta = a.split('.');\n\tb = b.split('.');\n\n\treturn fn(a[0], b[0]) || fn(a[1], b[1]) || (\n\t\tb[2] = b.slice(2).join('.'),\n\t\tbool = /[.-]/.test(a[2] = a.slice(2).join('.')),\n\t\tbool == /[.-]/.test(b[2]) ? fn(a[2], b[2]) : bool ? -1 : 1\n\t);\n}\n", "// endpoints\nexport const HOST_URL = \"host\";\nexport const API_URL = \"api/predict/\";\nexport const SSE_URL_V0 = \"queue/join\";\nexport const SSE_DATA_URL_V0 = \"queue/data\";\nexport const SSE_URL = \"queue/data\";\nexport const SSE_DATA_URL = \"queue/join\";\nexport const UPLOAD_URL = \"upload\";\nexport const LOGIN_URL = \"login\";\nexport const CONFIG_URL = \"config\";\nexport const API_INFO_URL = \"info\";\nexport const RUNTIME_URL = \"runtime\";\nexport const SLEEPTIME_URL = \"sleeptime\";\nexport const RAW_API_INFO_URL = \"info?serialize=False\";\nexport const SPACE_FETCHER_URL =\n\t\"https://gradio-space-api-fetcher-v2.hf.space/api\";\nexport const RESET_URL = \"reset\";\nexport const SPACE_URL = \"https://hf.space/{}\";\n\n// messages\nexport const QUEUE_FULL_MSG =\n\t\"This application is currently busy. Please try again. \";\nexport const BROKEN_CONNECTION_MSG = \"Connection errored out. \";\nexport const CONFIG_ERROR_MSG = \"Could not resolve app config. \";\nexport const SPACE_STATUS_ERROR_MSG = \"Could not get space status. \";\nexport const API_INFO_ERROR_MSG = \"Could not get API info. \";\nexport const SPACE_METADATA_ERROR_MSG = \"Space metadata could not be loaded. \";\nexport const INVALID_URL_MSG = \"Invalid URL. A full URL path is required.\";\nexport const UNAUTHORIZED_MSG = \"Not authorized to access this space. \";\nexport const INVALID_CREDENTIALS_MSG = \"Invalid credentials. Could not login. \";\nexport const MISSING_CREDENTIALS_MSG =\n\t\"Login credentials are required to access this space.\";\nexport const NODEJS_FS_ERROR_MSG =\n\t\"File system access is only available in Node.js environments\";\nexport const ROOT_URL_ERROR_MSG = \"Root URL not found in client config\";\nexport const FILE_PROCESSING_ERROR_MSG = \"Error uploading file\";\n", "import type { Config } from \"../types\";\nimport {\n\tCONFIG_ERROR_MSG,\n\tCONFIG_URL,\n\tINVALID_CREDENTIALS_MSG,\n\tLOGIN_URL,\n\tMISSING_CREDENTIALS_MSG,\n\tSPACE_METADATA_ERROR_MSG,\n\tUNAUTHORIZED_MSG\n} from \"../constants\";\nimport { Client } from \"..\";\nimport { join_urls, process_endpoint } from \"./api_info\";\n\n/**\n * This function is used to resolve the URL for making requests when the app has a root path.\n * The root path could be a path suffix like \"/app\" which is appended to the end of the base URL. Or\n * it could be a full URL like \"https://abidlabs-test-client-replica--gqf2x.hf.space\" which is used when hosting\n * Gradio apps on Hugging Face Spaces.\n * @param {string} base_url The base URL at which the Gradio server is hosted\n * @param {string} root_path The root path, which could be a path suffix (e.g. mounted in FastAPI app) or a full URL (e.g. hosted on Hugging Face Spaces)\n * @param {boolean} prioritize_base Whether to prioritize the base URL over the root path. This is used when both the base path and root paths are full URLs. For example, for fetching files the root path should be prioritized, but for making requests, the base URL should be prioritized.\n * @returns {string} the resolved URL\n */\nexport function resolve_root(\n\tbase_url: string,\n\troot_path: string,\n\tprioritize_base: boolean\n): string {\n\tif (root_path.startsWith(\"http://\") || root_path.startsWith(\"https://\")) {\n\t\treturn prioritize_base ? base_url : root_path;\n\t}\n\treturn base_url + root_path;\n}\n\nexport async function get_jwt(\n\tspace: string,\n\ttoken: `hf_${string}`,\n\tcookies?: string | null\n): Promise<string | false> {\n\ttry {\n\t\tconst r = await fetch(`https://huggingface.co/api/spaces/${space}/jwt`, {\n\t\t\theaders: {\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t\t...(cookies ? { Cookie: cookies } : {})\n\t\t\t}\n\t\t});\n\n\t\tconst jwt = (await r.json()).token;\n\n\t\treturn jwt || false;\n\t} catch (e) {\n\t\treturn false;\n\t}\n}\n\nexport function map_names_to_ids(\n\tfns: Config[\"dependencies\"]\n): Record<string, number> {\n\tlet apis: Record<string, number> = {};\n\n\tfns.forEach(({ api_name, id }) => {\n\t\tif (api_name) apis[api_name] = id;\n\t});\n\treturn apis;\n}\n\nexport async function resolve_config(\n\tthis: Client,\n\tendpoint: string\n): Promise<Config | undefined> {\n\tconst headers: Record<string, string> = this.options.hf_token\n\t\t? { Authorization: `Bearer ${this.options.hf_token}` }\n\t\t: {};\n\n\theaders[\"Content-Type\"] = \"application/json\";\n\n\tif (\n\t\ttypeof window !== \"undefined\" &&\n\t\twindow.gradio_config &&\n\t\tlocation.origin !== \"http://localhost:9876\" &&\n\t\t!window.gradio_config.dev_mode\n\t) {\n\t\tconst path = window.gradio_config.root;\n\t\tconst config = window.gradio_config;\n\t\tlet config_root = resolve_root(endpoint, config.root, false);\n\t\tconfig.root = config_root;\n\t\treturn { ...config, path } as Config;\n\t} else if (endpoint) {\n\t\tconst config_url = join_urls(endpoint, CONFIG_URL);\n\t\tconst response = await this.fetch(config_url, {\n\t\t\theaders,\n\t\t\tcredentials: \"include\"\n\t\t});\n\n\t\tif (response?.status === 401 && !this.options.auth) {\n\t\t\tthrow new Error(MISSING_CREDENTIALS_MSG);\n\t\t} else if (response?.status === 401 && this.options.auth) {\n\t\t\tthrow new Error(INVALID_CREDENTIALS_MSG);\n\t\t}\n\t\tif (response?.status === 200) {\n\t\t\tlet config = await response.json();\n\t\t\tconfig.path = config.path ?? \"\";\n\t\t\tconfig.root = endpoint;\n\t\t\tconfig.dependencies?.forEach((dep: any, i: number) => {\n\t\t\t\tif (dep.id === undefined) {\n\t\t\t\t\tdep.id = i;\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn config;\n\t\t} else if (response?.status === 401) {\n\t\t\tthrow new Error(UNAUTHORIZED_MSG);\n\t\t}\n\t\tthrow new Error(CONFIG_ERROR_MSG);\n\t}\n\n\tthrow new Error(CONFIG_ERROR_MSG);\n}\n\nexport async function resolve_cookies(this: Client): Promise<void> {\n\tconst { http_protocol, host } = await process_endpoint(\n\t\tthis.app_reference,\n\t\tthis.options.hf_token\n\t);\n\n\ttry {\n\t\tif (this.options.auth) {\n\t\t\tconst cookie_header = await get_cookie_header(\n\t\t\t\thttp_protocol,\n\t\t\t\thost,\n\t\t\t\tthis.options.auth,\n\t\t\t\tthis.fetch,\n\t\t\t\tthis.options.hf_token\n\t\t\t);\n\n\t\t\tif (cookie_header) this.set_cookies(cookie_header);\n\t\t}\n\t} catch (e: unknown) {\n\t\tthrow Error((e as Error).message);\n\t}\n}\n\n// separating this from client-bound resolve_cookies so that it can be used in duplicate\nexport async function get_cookie_header(\n\thttp_protocol: string,\n\thost: string,\n\tauth: [string, string],\n\t_fetch: typeof fetch,\n\thf_token?: `hf_${string}`\n): Promise<string | null> {\n\tconst formData = new FormData();\n\tformData.append(\"username\", auth?.[0]);\n\tformData.append(\"password\", auth?.[1]);\n\n\tlet headers: { Authorization?: string } = {};\n\n\tif (hf_token) {\n\t\theaders.Authorization = `Bearer ${hf_token}`;\n\t}\n\n\tconst res = await _fetch(`${http_protocol}//${host}/${LOGIN_URL}`, {\n\t\theaders,\n\t\tmethod: \"POST\",\n\t\tbody: formData,\n\t\tcredentials: \"include\"\n\t});\n\n\tif (res.status === 200) {\n\t\treturn res.headers.get(\"set-cookie\");\n\t} else if (res.status === 401) {\n\t\tthrow new Error(INVALID_CREDENTIALS_MSG);\n\t} else {\n\t\tthrow new Error(SPACE_METADATA_ERROR_MSG);\n\t}\n}\n\nexport function determine_protocol(endpoint: string): {\n\tws_protocol: \"ws\" | \"wss\";\n\thttp_protocol: \"http:\" | \"https:\";\n\thost: string;\n} {\n\tif (endpoint.startsWith(\"http\")) {\n\t\tconst { protocol, host, pathname } = new URL(endpoint);\n\n\t\tif (host.endsWith(\"hf.space\")) {\n\t\t\treturn {\n\t\t\t\tws_protocol: \"wss\",\n\t\t\t\thost: host,\n\t\t\t\thttp_protocol: protocol as \"http:\" | \"https:\"\n\t\t\t};\n\t\t}\n\t\treturn {\n\t\t\tws_protocol: protocol === \"https:\" ? \"wss\" : \"ws\",\n\t\t\thttp_protocol: protocol as \"http:\" | \"https:\",\n\t\t\thost: host + (pathname !== \"/\" ? pathname : \"\")\n\t\t};\n\t} else if (endpoint.startsWith(\"file:\")) {\n\t\t// This case is only expected to be used for the Wasm mode (Gradio-lite),\n\t\t// where users can create a local HTML file using it and open the page in a browser directly via the `file:` protocol.\n\t\treturn {\n\t\t\tws_protocol: \"ws\",\n\t\t\thttp_protocol: \"http:\",\n\t\t\thost: \"lite.local\" // Special fake hostname only used for this case. This matches the hostname allowed in `is_self_host()` in `js/wasm/network/host.ts`.\n\t\t};\n\t}\n\n\t// default to secure if no protocol is provided\n\treturn {\n\t\tws_protocol: \"wss\",\n\t\thttp_protocol: \"https:\",\n\t\thost: endpoint\n\t};\n}\n\nexport const parse_and_set_cookies = (cookie_header: string): string[] => {\n\tlet cookies: string[] = [];\n\tconst parts = cookie_header.split(/,(?=\\s*[^\\s=;]+=[^\\s=;]+)/);\n\tparts.forEach((cookie) => {\n\t\tconst [cookie_name, cookie_value] = cookie.split(\";\")[0].split(\"=\");\n\t\tif (cookie_name && cookie_value) {\n\t\t\tcookies.push(`${cookie_name.trim()}=${cookie_value.trim()}`);\n\t\t}\n\t});\n\treturn cookies;\n};\n", "import {\n\tHOST_URL,\n\tINVALID_URL_MSG,\n\tQUEUE_FULL_MSG,\n\tSPACE_METADATA_ERROR_MSG\n} from \"../constants\";\nimport type {\n\tApiData,\n\tApiInfo,\n\tConfig,\n\tJsApiData,\n\tEndpointInfo,\n\tStatus\n} from \"../types\";\nimport { determine_protocol } from \"./init_helpers\";\n\nexport const RE_SPACE_NAME = /^[a-zA-Z0-9_\\-\\.]+\\/[a-zA-Z0-9_\\-\\.]+$/;\nexport const RE_SPACE_DOMAIN = /.*hf\\.space\\/{0,1}$/;\n\nexport async function process_endpoint(\n\tapp_reference: string,\n\thf_token?: `hf_${string}`\n): Promise<{\n\tspace_id: string | false;\n\thost: string;\n\tws_protocol: \"ws\" | \"wss\";\n\thttp_protocol: \"http:\" | \"https:\";\n}> {\n\tconst headers: { Authorization?: string } = {};\n\tif (hf_token) {\n\t\theaders.Authorization = `Bearer ${hf_token}`;\n\t}\n\n\tconst _app_reference = app_reference.trim().replace(/\\/$/, \"\");\n\n\tif (RE_SPACE_NAME.test(_app_reference)) {\n\t\t// app_reference is a HF space name\n\t\ttry {\n\t\t\tconst res = await fetch(\n\t\t\t\t`https://huggingface.co/api/spaces/${_app_reference}/${HOST_URL}`,\n\t\t\t\t{ headers }\n\t\t\t);\n\n\t\t\tconst _host = (await res.json()).host;\n\n\t\t\treturn {\n\t\t\t\tspace_id: app_reference,\n\t\t\t\t...determine_protocol(_host)\n\t\t\t};\n\t\t} catch (e) {\n\t\t\tthrow new Error(SPACE_METADATA_ERROR_MSG);\n\t\t}\n\t}\n\n\tif (RE_SPACE_DOMAIN.test(_app_reference)) {\n\t\t// app_reference is a direct HF space domain\n\t\tconst { ws_protocol, http_protocol, host } =\n\t\t\tdetermine_protocol(_app_reference);\n\n\t\treturn {\n\t\t\tspace_id: host.replace(\".hf.space\", \"\"),\n\t\t\tws_protocol,\n\t\t\thttp_protocol,\n\t\t\thost\n\t\t};\n\t}\n\n\treturn {\n\t\tspace_id: false,\n\t\t...determine_protocol(_app_reference)\n\t};\n}\n\nexport const join_urls = (...urls: string[]): string => {\n\ttry {\n\t\treturn urls.reduce((base_url: string, part: string) => {\n\t\t\tbase_url = base_url.replace(/\\/+$/, \"\");\n\t\t\tpart = part.replace(/^\\/+/, \"\");\n\t\t\treturn new URL(part, base_url + \"/\").toString();\n\t\t});\n\t} catch (e) {\n\t\tthrow new Error(INVALID_URL_MSG);\n\t}\n};\n\nexport function transform_api_info(\n\tapi_info: ApiInfo<ApiData>,\n\tconfig: Config,\n\tapi_map: Record<string, number>\n): ApiInfo<JsApiData> {\n\tconst transformed_info: ApiInfo<JsApiData> = {\n\t\tnamed_endpoints: {},\n\t\tunnamed_endpoints: {}\n\t};\n\n\tObject.keys(api_info).forEach((category) => {\n\t\tif (category === \"named_endpoints\" || category === \"unnamed_endpoints\") {\n\t\t\ttransformed_info[category] = {};\n\n\t\t\tObject.entries(api_info[category]).forEach(\n\t\t\t\t([endpoint, { parameters, returns }]) => {\n\t\t\t\t\tconst dependencyIndex =\n\t\t\t\t\t\tconfig.dependencies.find(\n\t\t\t\t\t\t\t(dep) =>\n\t\t\t\t\t\t\t\tdep.api_name === endpoint ||\n\t\t\t\t\t\t\t\tdep.api_name === endpoint.replace(\"/\", \"\")\n\t\t\t\t\t\t)?.id ||\n\t\t\t\t\t\tapi_map[endpoint.replace(\"/\", \"\")] ||\n\t\t\t\t\t\t-1;\n\n\t\t\t\t\tconst dependencyTypes =\n\t\t\t\t\t\tdependencyIndex !== -1\n\t\t\t\t\t\t\t? config.dependencies.find((dep) => dep.id == dependencyIndex)\n\t\t\t\t\t\t\t\t\t?.types\n\t\t\t\t\t\t\t: { generator: false, cancel: false };\n\n\t\t\t\t\tif (\n\t\t\t\t\t\tdependencyIndex !== -1 &&\n\t\t\t\t\t\tconfig.dependencies.find((dep) => dep.id == dependencyIndex)?.inputs\n\t\t\t\t\t\t\t?.length !== parameters.length\n\t\t\t\t\t) {\n\t\t\t\t\t\tconst components = config.dependencies\n\t\t\t\t\t\t\t.find((dep) => dep.id == dependencyIndex)!\n\t\t\t\t\t\t\t.inputs.map(\n\t\t\t\t\t\t\t\t(input) => config.components.find((c) => c.id === input)?.type\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tcomponents.forEach((comp, idx) => {\n\t\t\t\t\t\t\t\tif (comp === \"state\") {\n\t\t\t\t\t\t\t\t\tconst new_param = {\n\t\t\t\t\t\t\t\t\t\tcomponent: \"state\",\n\t\t\t\t\t\t\t\t\t\texample: null,\n\t\t\t\t\t\t\t\t\t\tparameter_default: null,\n\t\t\t\t\t\t\t\t\t\tparameter_has_default: true,\n\t\t\t\t\t\t\t\t\t\tparameter_name: null,\n\t\t\t\t\t\t\t\t\t\thidden: true\n\t\t\t\t\t\t\t\t\t};\n\n\t\t\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\t\t\tparameters.splice(idx, 0, new_param);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tconst transform_type = (\n\t\t\t\t\t\tdata: ApiData,\n\t\t\t\t\t\tcomponent: string,\n\t\t\t\t\t\tserializer: string,\n\t\t\t\t\t\tsignature_type: \"return\" | \"parameter\"\n\t\t\t\t\t): JsApiData => ({\n\t\t\t\t\t\t...data,\n\t\t\t\t\t\tdescription: get_description(data?.type, serializer),\n\t\t\t\t\t\ttype:\n\t\t\t\t\t\t\tget_type(data?.type, component, serializer, signature_type) || \"\"\n\t\t\t\t\t});\n\n\t\t\t\t\ttransformed_info[category][endpoint] = {\n\t\t\t\t\t\tparameters: parameters.map((p: ApiData) =>\n\t\t\t\t\t\t\ttransform_type(p, p?.component, p?.serializer, \"parameter\")\n\t\t\t\t\t\t),\n\t\t\t\t\t\treturns: returns.map((r: ApiData) =>\n\t\t\t\t\t\t\ttransform_type(r, r?.component, r?.serializer, \"return\")\n\t\t\t\t\t\t),\n\t\t\t\t\t\ttype: dependencyTypes\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\t});\n\n\treturn transformed_info;\n}\n\nexport function get_type(\n\ttype: { type: any; description: string },\n\tcomponent: string,\n\tserializer: string,\n\tsignature_type: \"return\" | \"parameter\"\n): string | undefined {\n\tswitch (type?.type) {\n\t\tcase \"string\":\n\t\t\treturn \"string\";\n\t\tcase \"boolean\":\n\t\t\treturn \"boolean\";\n\t\tcase \"number\":\n\t\t\treturn \"number\";\n\t}\n\n\tif (\n\t\tserializer === \"JSONSerializable\" ||\n\t\tserializer === \"StringSerializable\"\n\t) {\n\t\treturn \"any\";\n\t} else if (serializer === \"ListStringSerializable\") {\n\t\treturn \"string[]\";\n\t} else if (component === \"Image\") {\n\t\treturn signature_type === \"parameter\" ? \"Blob | File | Buffer\" : \"string\";\n\t} else if (serializer === \"FileSerializable\") {\n\t\tif (type?.type === \"array\") {\n\t\t\treturn signature_type === \"parameter\"\n\t\t\t\t? \"(Blob | File | Buffer)[]\"\n\t\t\t\t: `{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]`;\n\t\t}\n\t\treturn signature_type === \"parameter\"\n\t\t\t? \"Blob | File | Buffer\"\n\t\t\t: `{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}`;\n\t} else if (serializer === \"GallerySerializable\") {\n\t\treturn signature_type === \"parameter\"\n\t\t\t? \"[(Blob | File | Buffer), (string | null)][]\"\n\t\t\t: `[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]`;\n\t}\n}\n\nexport function get_description(\n\ttype: { type: any; description: string },\n\tserializer: string\n): string {\n\tif (serializer === \"GallerySerializable\") {\n\t\treturn \"array of [file, label] tuples\";\n\t} else if (serializer === \"ListStringSerializable\") {\n\t\treturn \"array of strings\";\n\t} else if (serializer === \"FileSerializable\") {\n\t\treturn \"array of files or single file\";\n\t}\n\treturn type?.description;\n}\n\n/* eslint-disable complexity */\nexport function handle_message(\n\tdata: any,\n\tlast_status: Status[\"stage\"]\n): {\n\ttype:\n\t\t| \"hash\"\n\t\t| \"data\"\n\t\t| \"update\"\n\t\t| \"complete\"\n\t\t| \"generating\"\n\t\t| \"log\"\n\t\t| \"none\"\n\t\t| \"heartbeat\"\n\t\t| \"unexpected_error\";\n\tdata?: any;\n\tstatus?: Status;\n} {\n\tconst queue = true;\n\tswitch (data.msg) {\n\t\tcase \"send_data\":\n\t\t\treturn { type: \"data\" };\n\t\tcase \"send_hash\":\n\t\t\treturn { type: \"hash\" };\n\t\tcase \"queue_full\":\n\t\t\treturn {\n\t\t\t\ttype: \"update\",\n\t\t\t\tstatus: {\n\t\t\t\t\tqueue,\n\t\t\t\t\tmessage: QUEUE_FULL_MSG,\n\t\t\t\t\tstage: \"error\",\n\t\t\t\t\tcode: data.code,\n\t\t\t\t\tsuccess: data.success\n\t\t\t\t}\n\t\t\t};\n\t\tcase \"heartbeat\":\n\t\t\treturn {\n\t\t\t\ttype: \"heartbeat\"\n\t\t\t};\n\t\tcase \"unexpected_error\":\n\t\t\treturn {\n\t\t\t\ttype: \"unexpected_error\",\n\t\t\t\tstatus: {\n\t\t\t\t\tqueue,\n\t\t\t\t\tmessage: data.message,\n\t\t\t\t\tstage: \"error\",\n\t\t\t\t\tsuccess: false\n\t\t\t\t}\n\t\t\t};\n\t\tcase \"estimation\":\n\t\t\treturn {\n\t\t\t\ttype: \"update\",\n\t\t\t\tstatus: {\n\t\t\t\t\tqueue,\n\t\t\t\t\tstage: last_status || \"pending\",\n\t\t\t\t\tcode: data.code,\n\t\t\t\t\tsize: data.queue_size,\n\t\t\t\t\tposition: data.rank,\n\t\t\t\t\teta: data.rank_eta,\n\t\t\t\t\tsuccess: data.success\n\t\t\t\t}\n\t\t\t};\n\t\tcase \"progress\":\n\t\t\treturn {\n\t\t\t\ttype: \"update\",\n\t\t\t\tstatus: {\n\t\t\t\t\tqueue,\n\t\t\t\t\tstage: \"pending\",\n\t\t\t\t\tcode: data.code,\n\t\t\t\t\tprogress_data: data.progress_data,\n\t\t\t\t\tsuccess: data.success\n\t\t\t\t}\n\t\t\t};\n\t\tcase \"log\":\n\t\t\treturn { type: \"log\", data: data };\n\t\tcase \"process_generating\":\n\t\t\treturn {\n\t\t\t\ttype: \"generating\",\n\t\t\t\tstatus: {\n\t\t\t\t\tqueue,\n\t\t\t\t\tmessage: !data.success ? data.output.error : null,\n\t\t\t\t\tstage: data.success ? \"generating\" : \"error\",\n\t\t\t\t\tcode: data.code,\n\t\t\t\t\tprogress_data: data.progress_data,\n\t\t\t\t\teta: data.average_duration\n\t\t\t\t},\n\t\t\t\tdata: data.success ? data.output : null\n\t\t\t};\n\t\tcase \"process_completed\":\n\t\t\tif (\"error\" in data.output) {\n\t\t\t\treturn {\n\t\t\t\t\ttype: \"update\",\n\t\t\t\t\tstatus: {\n\t\t\t\t\t\tqueue,\n\t\t\t\t\t\tmessage: data.output.error as string,\n\t\t\t\t\t\tvisible: data.output.visible as boolean,\n\t\t\t\t\t\tduration: data.output.duration as number,\n\t\t\t\t\t\tstage: \"error\",\n\t\t\t\t\t\tcode: data.code,\n\t\t\t\t\t\tsuccess: data.success\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\t\t\treturn {\n\t\t\t\ttype: \"complete\",\n\t\t\t\tstatus: {\n\t\t\t\t\tqueue,\n\t\t\t\t\tmessage: !data.success ? data.output.error : undefined,\n\t\t\t\t\tstage: data.success ? \"complete\" : \"error\",\n\t\t\t\t\tcode: data.code,\n\t\t\t\t\tprogress_data: data.progress_data,\n\t\t\t\t\tchanged_state_ids: data.success\n\t\t\t\t\t\t? data.output.changed_state_ids\n\t\t\t\t\t\t: undefined\n\t\t\t\t},\n\t\t\t\tdata: data.success ? data.output : null\n\t\t\t};\n\n\t\tcase \"process_starts\":\n\t\t\treturn {\n\t\t\t\ttype: \"update\",\n\t\t\t\tstatus: {\n\t\t\t\t\tqueue,\n\t\t\t\t\tstage: \"pending\",\n\t\t\t\t\tcode: data.code,\n\t\t\t\t\tsize: data.rank,\n\t\t\t\t\tposition: 0,\n\t\t\t\t\tsuccess: data.success,\n\t\t\t\t\teta: data.eta\n\t\t\t\t}\n\t\t\t};\n\t}\n\n\treturn { type: \"none\", status: { stage: \"error\", queue } };\n}\n/* eslint-enable complexity */\n\n/**\n * Maps the provided `data` to the parameters defined by the `/info` endpoint response.\n * This allows us to support both positional and keyword arguments passed to the client\n * and ensures that all parameters are either directly provided or have default values assigned.\n *\n * @param {unknown[] | Record<string, unknown>} data - The input data for the function,\n *        which can be either an array of values for positional arguments or an object\n *        with key-value pairs for keyword arguments.\n * @param {JsApiData[]} parameters - Array of parameter descriptions retrieved from the\n *        `/info` endpoint.\n *\n * @returns {unknown[]} - Returns an array of resolved data where each element corresponds\n *         to the expected parameter from the API. The `parameter_default` value is used where\n *         a value is not provided for a parameter, and optional parameters without defaults are\n * \t\t   set to `undefined`.\n *\n * @throws {Error} - Throws an error:\n *         - If more arguments are provided than are defined in the parameters.\n *  *      - If no parameter value is provided for a required parameter and no default value is defined.\n *         - If an argument is provided that does not match any defined parameter.\n */\n\nexport const map_data_to_params = (\n\tdata: unknown[] | Record<string, unknown> = [],\n\tendpoint_info: EndpointInfo<JsApiData | ApiData>\n): unknown[] => {\n\t// Workaround for the case where the endpoint_info is undefined\n\t// See https://github.com/gradio-app/gradio/pull/8820#issuecomment-2237381761\n\tconst parameters = endpoint_info ? endpoint_info.parameters : [];\n\n\tif (Array.isArray(data)) {\n\t\tif (data.length > parameters.length) {\n\t\t\tconsole.warn(\"Too many arguments provided for the endpoint.\");\n\t\t}\n\t\treturn data;\n\t}\n\n\tconst resolved_data: unknown[] = [];\n\tconst provided_keys = Object.keys(data);\n\n\tparameters.forEach((param, index) => {\n\t\tif (data.hasOwnProperty(param.parameter_name)) {\n\t\t\tresolved_data[index] = data[param.parameter_name];\n\t\t} else if (param.parameter_has_default) {\n\t\t\tresolved_data[index] = param.parameter_default;\n\t\t} else {\n\t\t\tthrow new Error(\n\t\t\t\t`No value provided for required parameter: ${param.parameter_name}`\n\t\t\t);\n\t\t}\n\t});\n\n\tprovided_keys.forEach((key) => {\n\t\tif (!parameters.some((param) => param.parameter_name === key)) {\n\t\t\tthrow new Error(\n\t\t\t\t`Parameter \\`${key}\\` is not a valid keyword argument. Please refer to the API for usage.`\n\t\t\t);\n\t\t}\n\t});\n\n\tresolved_data.forEach((value, idx) => {\n\t\tif (value === undefined && !parameters[idx].parameter_has_default) {\n\t\t\tthrow new Error(\n\t\t\t\t`No value provided for required parameter: ${parameters[idx].parameter_name}`\n\t\t\t);\n\t\t}\n\t});\n\n\treturn resolved_data;\n};\n", "import type { ApiInfo, ApiData } from \"../types\";\nimport semiver from \"semiver\";\nimport { API_INFO_URL, BROKEN_CONNECTION_MSG } from \"../constants\";\nimport { Client } from \"../client\";\nimport { SPACE_FETCHER_URL } from \"../constants\";\nimport { join_urls, transform_api_info } from \"../helpers/api_info\";\n\nexport async function view_api(this: Client): Promise<any> {\n\tif (this.api_info) return this.api_info;\n\n\tconst { hf_token } = this.options;\n\tconst { config } = this;\n\n\tconst headers: {\n\t\tAuthorization?: string;\n\t\t\"Content-Type\": \"application/json\";\n\t} = { \"Content-Type\": \"application/json\" };\n\n\tif (hf_token) {\n\t\theaders.Authorization = `Bearer ${hf_token}`;\n\t}\n\n\tif (!config) {\n\t\treturn;\n\t}\n\n\ttry {\n\t\tlet response: Response;\n\t\tlet api_info: ApiInfo<ApiData> | { api: ApiInfo<ApiData> };\n\t\tif (typeof window !== \"undefined\" && window.gradio_api_info) {\n\t\t\tapi_info = window.gradio_api_info;\n\t\t} else {\n\t\t\tif (semiver(config?.version || \"2.0.0\", \"3.30\") < 0) {\n\t\t\t\tresponse = await this.fetch(SPACE_FETCHER_URL, {\n\t\t\t\t\tmethod: \"POST\",\n\t\t\t\t\tbody: JSON.stringify({\n\t\t\t\t\t\tserialize: false,\n\t\t\t\t\t\tconfig: JSON.stringify(config)\n\t\t\t\t\t}),\n\t\t\t\t\theaders,\n\t\t\t\t\tcredentials: \"include\"\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tconst url = join_urls(config.root, API_INFO_URL);\n\t\t\t\tresponse = await this.fetch(url, {\n\t\t\t\t\theaders,\n\t\t\t\t\tcredentials: \"include\"\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (!response.ok) {\n\t\t\t\tthrow new Error(BROKEN_CONNECTION_MSG);\n\t\t\t}\n\t\t\tapi_info = await response.json();\n\t\t}\n\t\tif (\"api\" in api_info) {\n\t\t\tapi_info = api_info.api;\n\t\t}\n\n\t\tif (\n\t\t\tapi_info.named_endpoints[\"/predict\"] &&\n\t\t\t!api_info.unnamed_endpoints[\"0\"]\n\t\t) {\n\t\t\tapi_info.unnamed_endpoints[0] = api_info.named_endpoints[\"/predict\"];\n\t\t}\n\n\t\treturn transform_api_info(api_info, config, this.api_map);\n\t} catch (e) {\n\t\t\"Could not get API info. \" + (e as Error).message;\n\t}\n}\n", "import type { Client } from \"..\";\nimport { BROKEN_CONNECTION_MSG, UPLOAD_URL } from \"../constants\";\nimport type { UploadResponse } from \"../types\";\n\nexport async function upload_files(\n\tthis: Client,\n\troot_url: string,\n\tfiles: (Blob | File)[],\n\tupload_id?: string\n): Promise<UploadResponse> {\n\tconst headers: {\n\t\tAuthorization?: string;\n\t} = {};\n\tif (this?.options?.hf_token) {\n\t\theaders.Authorization = `Bearer ${this.options.hf_token}`;\n\t}\n\n\tconst chunkSize = 1000;\n\tconst uploadResponses = [];\n\tlet response: Response;\n\n\tfor (let i = 0; i < files.length; i += chunkSize) {\n\t\tconst chunk = files.slice(i, i + chunkSize);\n\t\tconst formData = new FormData();\n\t\tchunk.forEach((file) => {\n\t\t\tformData.append(\"files\", file);\n\t\t});\n\t\ttry {\n\t\t\tconst upload_url = upload_id\n\t\t\t\t? `${root_url}/${UPLOAD_URL}?upload_id=${upload_id}`\n\t\t\t\t: `${root_url}/${UPLOAD_URL}`;\n\n\t\t\tresponse = await this.fetch(upload_url, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: formData,\n\t\t\t\theaders,\n\t\t\t\tcredentials: \"include\"\n\t\t\t});\n\t\t} catch (e) {\n\t\t\tthrow new Error(BROKEN_CONNECTION_MSG + (e as Error).message);\n\t\t}\n\t\tif (!response.ok) {\n\t\t\tconst error_text = await response.text();\n\t\t\treturn { error: `HTTP ${response.status}: ${error_text}` };\n\t\t}\n\t\tconst output: UploadResponse[\"files\"] = await response.json();\n\t\tif (output) {\n\t\t\tuploadResponses.push(...output);\n\t\t}\n\t}\n\treturn { files: uploadResponses };\n}\n", "import type { UploadResponse } from \"./types\";\nimport type { Client } from \"./client\";\n\nexport async function upload(\n\tthis: Client,\n\tfile_data: FileData[],\n\troot_url: string,\n\tupload_id?: string,\n\tmax_file_size?: number\n): Promise<(FileData | null)[] | null> {\n\tlet files = (Array.isArray(file_data) ? file_data : [file_data]).map(\n\t\t(file_data) => file_data.blob!\n\t);\n\n\tconst oversized_files = files.filter(\n\t\t(f) => f.size > (max_file_size ?? Infinity)\n\t);\n\tif (oversized_files.length) {\n\t\tthrow new Error(\n\t\t\t`File size exceeds the maximum allowed size of ${max_file_size} bytes: ${oversized_files\n\t\t\t\t.map((f) => f.name)\n\t\t\t\t.join(\", \")}`\n\t\t);\n\t}\n\n\treturn await Promise.all(\n\t\tawait this.upload_files(root_url, files, upload_id).then(\n\t\t\tasync (response: { files?: string[]; error?: string }) => {\n\t\t\t\tif (response.error) {\n\t\t\t\t\tthrow new Error(response.error);\n\t\t\t\t} else {\n\t\t\t\t\tif (response.files) {\n\t\t\t\t\t\treturn response.files.map((f, i) => {\n\t\t\t\t\t\t\tconst file = new FileData({\n\t\t\t\t\t\t\t\t...file_data[i],\n\t\t\t\t\t\t\t\tpath: f,\n\t\t\t\t\t\t\t\turl: root_url + \"/file=\" + f\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn file;\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\t\t\t\t\treturn [];\n\t\t\t\t}\n\t\t\t}\n\t\t)\n\t);\n}\n\nexport async function prepare_files(\n\tfiles: File[],\n\tis_stream?: boolean\n): Promise<FileData[]> {\n\treturn files.map(\n\t\t(f) =>\n\t\t\tnew FileData({\n\t\t\t\tpath: f.name,\n\t\t\t\torig_name: f.name,\n\t\t\t\tblob: f,\n\t\t\t\tsize: f.size,\n\t\t\t\tmime_type: f.type,\n\t\t\t\tis_stream\n\t\t\t})\n\t);\n}\n\nexport class FileData {\n\tpath: string;\n\turl?: string;\n\torig_name?: string;\n\tsize?: number;\n\tblob?: File;\n\tis_stream?: boolean;\n\tmime_type?: string;\n\talt_text?: string;\n\treadonly meta = { _type: \"gradio.FileData\" };\n\n\tconstructor({\n\t\tpath,\n\t\turl,\n\t\torig_name,\n\t\tsize,\n\t\tblob,\n\t\tis_stream,\n\t\tmime_type,\n\t\talt_text\n\t}: {\n\t\tpath: string;\n\t\turl?: string;\n\t\torig_name?: string;\n\t\tsize?: number;\n\t\tblob?: File;\n\t\tis_stream?: boolean;\n\t\tmime_type?: string;\n\t\talt_text?: string;\n\t}) {\n\t\tthis.path = path;\n\t\tthis.url = url;\n\t\tthis.orig_name = orig_name;\n\t\tthis.size = size;\n\t\tthis.blob = url ? undefined : blob;\n\t\tthis.is_stream = is_stream;\n\t\tthis.mime_type = mime_type;\n\t\tthis.alt_text = alt_text;\n\t}\n}\n", "// API Data Types\n\nimport { hardware_types } from \"./helpers/spaces\";\nimport type { SvelteComponent } from \"svelte\";\nimport type { ComponentType } from \"svelte\";\n\nexport interface ApiData {\n\tlabel: string;\n\tparameter_name: string;\n\tparameter_default?: any;\n\tparameter_has_default?: boolean;\n\ttype: {\n\t\ttype: any;\n\t\tdescription: string;\n\t};\n\tcomponent: string;\n\texample_input?: any;\n\tpython_type: { type: string; description: string };\n\tserializer: string;\n}\n\nexport interface JsApiData {\n\tlabel: string;\n\tparameter_name: string;\n\tparameter_default?: any;\n\tparameter_has_default?: boolean;\n\ttype: string;\n\tdescription: string;\n\tcomponent: string;\n\texample_input?: any;\n\tserializer: string;\n\tpython_type: { type: string; description: string };\n}\n\nexport interface EndpointInfo<T extends ApiData | JsApiData> {\n\tparameters: T[];\n\treturns: T[];\n\ttype?: DependencyTypes;\n}\n\nexport interface ApiInfo<T extends ApiData | JsApiData> {\n\tnamed_endpoints: Record<string, EndpointInfo<T>>;\n\tunnamed_endpoints: Record<string, EndpointInfo<T>>;\n}\n\nexport interface BlobRef {\n\tpath: string[];\n\ttype: string | undefined;\n\tblob: Blob | File | false;\n}\n\nexport type DataType = string | Buffer | Record<string, any> | any[];\n\n// custom class used for uploading local files\nexport class Command {\n\ttype: string;\n\tcommand: string;\n\tmeta: {\n\t\tpath: string;\n\t\tname: string;\n\t\torig_path: string;\n\t};\n\tfileData?: FileData;\n\n\tconstructor(\n\t\tcommand: string,\n\t\tmeta: { path: string; name: string; orig_path: string }\n\t) {\n\t\tthis.type = \"command\";\n\t\tthis.command = command;\n\t\tthis.meta = meta;\n\t}\n}\n\n// Function Signature Types\n\nexport type SubmitFunction = (\n\tendpoint: string | number,\n\tdata?: unknown[] | Record<string, unknown>,\n\tevent_data?: unknown,\n\ttrigger_id?: number | null\n) => SubmitIterable<GradioEvent>;\n\nexport type PredictFunction = (\n\tendpoint: string | number,\n\tdata?: unknown[] | Record<string, unknown>,\n\tevent_data?: unknown\n) => Promise<PredictReturn>;\n\nexport type client_return = {\n\tconfig: Config | undefined;\n\tpredict: PredictFunction;\n\tsubmit: SubmitFunction;\n\tcomponent_server: (\n\t\tcomponent_id: number,\n\t\tfn_name: string,\n\t\tdata: unknown[]\n\t) => any;\n\tview_api: (_fetch: typeof fetch) => Promise<ApiInfo<JsApiData>>;\n};\n\nexport interface SubmitIterable<T> extends AsyncIterable<T> {\n\t[Symbol.asyncIterator](): AsyncIterator<T>;\n\tcancel: () => Promise<void>;\n}\n\nexport type PredictReturn = {\n\ttype: EventType;\n\ttime: Date;\n\tdata: unknown;\n\tendpoint: string;\n\tfn_index: number;\n};\n\n// Space Status Types\n\nexport type SpaceStatus = SpaceStatusNormal | SpaceStatusError;\n\nexport interface SpaceStatusNormal {\n\tstatus:\n\t\t| \"sleeping\"\n\t\t| \"running\"\n\t\t| \"building\"\n\t\t| \"error\"\n\t\t| \"stopped\"\n\t\t| \"starting\";\n\tdetail:\n\t\t| \"SLEEPING\"\n\t\t| \"RUNNING\"\n\t\t| \"RUNNING_BUILDING\"\n\t\t| \"BUILDING\"\n\t\t| \"APP_STARTING\"\n\t\t| \"NOT_FOUND\";\n\tload_status: \"pending\" | \"error\" | \"complete\" | \"generating\";\n\tmessage: string;\n}\n\nexport interface SpaceStatusError {\n\tstatus: \"space_error\" | \"paused\";\n\tdetail:\n\t\t| \"NO_APP_FILE\"\n\t\t| \"CONFIG_ERROR\"\n\t\t| \"BUILD_ERROR\"\n\t\t| \"RUNTIME_ERROR\"\n\t\t| \"PAUSED\";\n\tload_status: \"error\";\n\tmessage: string;\n\tdiscussions_enabled: boolean;\n}\n\nexport type SpaceStatusCallback = (a: SpaceStatus) => void;\n\n// Configuration and Response Types\n// --------------------------------\nexport interface Config {\n\tauth_required?: true;\n\tanalytics_enabled: boolean;\n\tconnect_heartbeat: boolean;\n\tauth_message: string;\n\tcomponents: ComponentMeta[];\n\tcss: string | null;\n\tjs: string | null;\n\thead: string | null;\n\tdependencies: Dependency[];\n\tdev_mode: boolean;\n\tenable_queue: boolean;\n\tshow_error: boolean;\n\tlayout: any;\n\tmode: \"blocks\" | \"interface\";\n\troot: string;\n\troot_url?: string;\n\ttheme: string;\n\ttitle: string;\n\tversion: string;\n\tspace_id: string | null;\n\tis_space: boolean;\n\tis_colab: boolean;\n\tshow_api: boolean;\n\tstylesheets: string[];\n\tpath: string;\n\tprotocol: \"sse_v3\" | \"sse_v2.1\" | \"sse_v2\" | \"sse_v1\" | \"sse\" | \"ws\";\n\tmax_file_size?: number;\n\ttheme_hash?: number;\n\tusername: string | null;\n}\n\n// todo: DRY up types\nexport interface ComponentMeta {\n\ttype: string;\n\tid: number;\n\thas_modes: boolean;\n\tprops: SharedProps;\n\tinstance: SvelteComponent;\n\tcomponent: ComponentType<SvelteComponent>;\n\tdocumentation?: Documentation;\n\tchildren?: ComponentMeta[];\n\tparent?: ComponentMeta;\n\tvalue?: any;\n\tcomponent_class_id: string;\n\tkey: string | number | null;\n\trendered_in?: number;\n}\n\ninterface SharedProps {\n\telem_id?: string;\n\telem_classes?: string[];\n\tcomponents?: string[];\n\tserver_fns?: string[];\n\tinteractive: boolean;\n\t[key: string]: unknown;\n\troot_url?: string;\n}\n\nexport interface Documentation {\n\ttype?: TypeDescription;\n\tdescription?: TypeDescription;\n\texample_data?: string;\n}\n\ninterface TypeDescription {\n\tinput_payload?: string;\n\tresponse_object?: string;\n\tpayload?: string;\n}\n\nexport interface Dependency {\n\tid: number;\n\ttargets: [number, string][];\n\tinputs: number[];\n\toutputs: number[];\n\tbackend_fn: boolean;\n\tjs: string | null;\n\tscroll_to_output: boolean;\n\ttrigger: \"click\" | \"load\" | string;\n\tmax_batch_size: number;\n\tshow_progress: \"full\" | \"minimal\" | \"hidden\";\n\tfrontend_fn: ((...args: unknown[]) => Promise<unknown[]>) | null;\n\tstatus?: string;\n\tqueue: boolean | null;\n\tevery: number | null;\n\tbatch: boolean;\n\tapi_name: string | null;\n\tcancels: number[];\n\ttypes: DependencyTypes;\n\tcollects_event_data: boolean;\n\tpending_request?: boolean;\n\ttrigger_after?: number;\n\ttrigger_only_on_success?: boolean;\n\ttrigger_mode: \"once\" | \"multiple\" | \"always_last\";\n\tfinal_event: Payload | null;\n\tshow_api: boolean;\n\tzerogpu?: boolean;\n\trendered_in: number | null;\n}\n\nexport interface DependencyTypes {\n\tgenerator: boolean;\n\tcancel: boolean;\n}\n\nexport interface Payload {\n\tfn_index: number;\n\tdata: unknown[];\n\ttime?: Date;\n\tevent_data?: unknown;\n\ttrigger_id?: number | null;\n}\n\nexport interface PostResponse {\n\terror?: string;\n\t[x: string]: any;\n}\n\nexport interface UploadResponse {\n\terror?: string;\n\tfiles?: string[];\n}\n\n// Client and File Handling Types\n\nexport interface DuplicateOptions extends ClientOptions {\n\tprivate?: boolean;\n\thardware?: (typeof hardware_types)[number];\n\ttimeout?: number;\n}\n\nexport interface ClientOptions {\n\thf_token?: `hf_${string}`;\n\tstatus_callback?: SpaceStatusCallback | null;\n\tauth?: [string, string] | null;\n\twith_null_state?: boolean;\n\tevents?: EventType[];\n}\n\nexport interface FileData {\n\tname: string;\n\torig_name?: string;\n\tsize?: number;\n\tdata: string;\n\tblob?: File;\n\tis_file?: boolean;\n\tmime_type?: string;\n\talt_text?: string;\n}\n\n// Event and Listener Types\n\nexport type EventType = \"data\" | \"status\" | \"log\" | \"render\";\n\nexport interface EventMap {\n\tdata: PayloadMessage;\n\tstatus: StatusMessage;\n\tlog: LogMessage;\n\trender: RenderMessage;\n}\n\nexport type GradioEvent = {\n\t[P in EventType]: EventMap[P];\n}[EventType];\n\nexport interface Log {\n\tlog: string;\n\tlevel: \"warning\" | \"info\";\n}\nexport interface Render {\n\tdata: {\n\t\tcomponents: any[];\n\t\tlayout: any;\n\t\tdependencies: Dependency[];\n\t\trender_id: number;\n\t};\n}\n\nexport interface Status {\n\tqueue: boolean;\n\tcode?: string;\n\tsuccess?: boolean;\n\tstage: \"pending\" | \"error\" | \"complete\" | \"generating\";\n\tduration?: number;\n\tvisible?: boolean;\n\tbroken?: boolean;\n\tsize?: number;\n\tposition?: number;\n\teta?: number;\n\tmessage?: string;\n\tprogress_data?: {\n\t\tprogress: number | null;\n\t\tindex: number | null;\n\t\tlength: number | null;\n\t\tunit: string | null;\n\t\tdesc: string | null;\n\t}[];\n\ttime?: Date;\n\tchanged_state_ids?: number[];\n}\n\nexport interface StatusMessage extends Status {\n\ttype: \"status\";\n\tendpoint: string;\n\tfn_index: number;\n}\n\nexport interface PayloadMessage extends Payload {\n\ttype: \"data\";\n\tendpoint: string;\n\tfn_index: number;\n}\n\nexport interface LogMessage extends Log {\n\ttype: \"log\";\n\tendpoint: string;\n\tfn_index: number;\n\tduration: number | null;\n\tvisible: boolean;\n}\n\nexport interface RenderMessage extends Render {\n\ttype: \"render\";\n\tendpoint: string;\n\tfn_index: number;\n}\n", "import {\n\ttype ApiData,\n\ttype BlobRef,\n\ttype Config,\n\ttype EndpointInfo,\n\ttype JsApiData,\n\ttype DataType,\n\tCommand,\n\ttype Dependency,\n\ttype ComponentMeta\n} from \"../types\";\nimport { FileData } from \"../upload\";\n\nconst is_node =\n\ttypeof process !== \"undefined\" && process.versions && process.versions.node;\n\nexport function update_object(\n\tobject: { [x: string]: any },\n\tnewValue: any,\n\tstack: (string | number)[]\n): void {\n\twhile (stack.length > 1) {\n\t\tconst key = stack.shift();\n\t\tif (typeof key === \"string\" || typeof key === \"number\") {\n\t\t\tobject = object[key];\n\t\t} else {\n\t\t\tthrow new Error(\"Invalid key type\");\n\t\t}\n\t}\n\n\tconst key = stack.shift();\n\tif (typeof key === \"string\" || typeof key === \"number\") {\n\t\tobject[key] = newValue;\n\t} else {\n\t\tthrow new Error(\"Invalid key type\");\n\t}\n}\n\nexport async function walk_and_store_blobs(\n\tdata: DataType,\n\ttype: string | undefined = undefined,\n\tpath: string[] = [],\n\troot = false,\n\tendpoint_info: EndpointInfo<ApiData | JsApiData> | undefined = undefined\n): Promise<BlobRef[]> {\n\tif (Array.isArray(data)) {\n\t\tlet blob_refs: BlobRef[] = [];\n\n\t\tawait Promise.all(\n\t\t\tdata.map(async (_, index) => {\n\t\t\t\tlet new_path = path.slice();\n\t\t\t\tnew_path.push(String(index));\n\n\t\t\t\tconst array_refs = await walk_and_store_blobs(\n\t\t\t\t\tdata[index],\n\t\t\t\t\troot\n\t\t\t\t\t\t? endpoint_info?.parameters[index]?.component || undefined\n\t\t\t\t\t\t: type,\n\t\t\t\t\tnew_path,\n\t\t\t\t\tfalse,\n\t\t\t\t\tendpoint_info\n\t\t\t\t);\n\n\t\t\t\tblob_refs = blob_refs.concat(array_refs);\n\t\t\t})\n\t\t);\n\n\t\treturn blob_refs;\n\t} else if (\n\t\t(globalThis.Buffer && data instanceof globalThis.Buffer) ||\n\t\tdata instanceof Blob\n\t) {\n\t\treturn [\n\t\t\t{\n\t\t\t\tpath: path,\n\t\t\t\tblob: new Blob([data]),\n\t\t\t\ttype\n\t\t\t}\n\t\t];\n\t} else if (typeof data === \"object\" && data !== null) {\n\t\tlet blob_refs: BlobRef[] = [];\n\t\tfor (const key of Object.keys(data) as (keyof typeof data)[]) {\n\t\t\tconst new_path = [...path, key];\n\t\t\tconst value = data[key];\n\n\t\t\tblob_refs = blob_refs.concat(\n\t\t\t\tawait walk_and_store_blobs(\n\t\t\t\t\tvalue,\n\t\t\t\t\tundefined,\n\t\t\t\t\tnew_path,\n\t\t\t\t\tfalse,\n\t\t\t\t\tendpoint_info\n\t\t\t\t)\n\t\t\t);\n\t\t}\n\n\t\treturn blob_refs;\n\t}\n\n\treturn [];\n}\n\nexport function skip_queue(id: number, config: Config): boolean {\n\tlet fn_queue = config?.dependencies?.find((dep) => dep.id == id)?.queue;\n\tif (fn_queue != null) {\n\t\treturn !fn_queue;\n\t}\n\treturn !config.enable_queue;\n}\n\n// todo: add jsdoc for this function\n\nexport function post_message<Res = any>(\n\tmessage: any,\n\torigin: string\n): Promise<Res> {\n\treturn new Promise((res, _rej) => {\n\t\tconst channel = new MessageChannel();\n\t\tchannel.port1.onmessage = (({ data }) => {\n\t\t\tchannel.port1.close();\n\t\t\tres(data);\n\t\t}) as (ev: MessageEvent<Res>) => void;\n\t\twindow.parent.postMessage(message, origin, [channel.port2]);\n\t});\n}\n\nexport function handle_file(\n\tfile_or_url: File | string | Blob | Buffer\n): FileData | Blob | Command {\n\tif (typeof file_or_url === \"string\") {\n\t\tif (\n\t\t\tfile_or_url.startsWith(\"http://\") ||\n\t\t\tfile_or_url.startsWith(\"https://\")\n\t\t) {\n\t\t\treturn {\n\t\t\t\tpath: file_or_url,\n\t\t\t\turl: file_or_url,\n\t\t\t\torig_name: file_or_url.split(\"/\").pop() ?? \"unknown\",\n\t\t\t\tmeta: { _type: \"gradio.FileData\" }\n\t\t\t};\n\t\t}\n\n\t\tif (is_node) {\n\t\t\t// Handle local file paths\n\t\t\treturn new Command(\"upload_file\", {\n\t\t\t\tpath: file_or_url,\n\t\t\t\tname: file_or_url,\n\t\t\t\torig_path: file_or_url\n\t\t\t});\n\t\t}\n\t} else if (typeof File !== \"undefined\" && file_or_url instanceof File) {\n\t\treturn new Blob([file_or_url]);\n\t} else if (file_or_url instanceof Buffer) {\n\t\treturn new Blob([file_or_url]);\n\t} else if (file_or_url instanceof Blob) {\n\t\treturn file_or_url;\n\t}\n\tthrow new Error(\n\t\t\"Invalid input: must be a URL, File, Blob, or Buffer object.\"\n\t);\n}\n\n/**\n * Handles the payload by filtering out state inputs and returning an array of resolved payload values.\n * We send null values for state inputs to the server, but we don't want to include them in the resolved payload.\n *\n * @param resolved_payload - The resolved payload values received from the client or the server\n * @param dependency - The dependency object.\n * @param components - The array of component metadata.\n * @param with_null_state - Optional. Specifies whether to include null values for state inputs. Default is false.\n * @returns An array of resolved payload values, filtered based on the dependency and component metadata.\n */\nexport function handle_payload(\n\tresolved_payload: unknown[],\n\tdependency: Dependency,\n\tcomponents: ComponentMeta[],\n\ttype: \"input\" | \"output\",\n\twith_null_state = false\n): unknown[] {\n\tif (type === \"input\" && !with_null_state) {\n\t\tthrow new Error(\"Invalid code path. Cannot skip state inputs for input.\");\n\t}\n\t// data comes from the server with null state values so we skip\n\tif (type === \"output\" && with_null_state) {\n\t\treturn resolved_payload;\n\t}\n\n\tlet updated_payload: unknown[] = [];\n\tlet payload_index = 0;\n\tconst deps = type === \"input\" ? dependency.inputs : dependency.outputs;\n\tfor (let i = 0; i < deps.length; i++) {\n\t\tconst input_id = deps[i];\n\t\tconst component = components.find((c) => c.id === input_id);\n\n\t\tif (component?.type === \"state\") {\n\t\t\t// input + with_null_state needs us to fill state with null values\n\t\t\tif (with_null_state) {\n\t\t\t\tif (resolved_payload.length === deps.length) {\n\t\t\t\t\tconst value = resolved_payload[payload_index];\n\t\t\t\t\tupdated_payload.push(value);\n\t\t\t\t\tpayload_index++;\n\t\t\t\t} else {\n\t\t\t\t\tupdated_payload.push(null);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// this is output & !with_null_state, we skip state inputs\n\t\t\t\t// the server payload always comes with null state values so we move along the payload index\n\t\t\t\tpayload_index++;\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\t// input & !with_null_state isn't a case we care about, server needs null\n\t\t\tcontinue;\n\t\t} else {\n\t\t\tconst value = resolved_payload[payload_index];\n\t\t\tupdated_payload.push(value);\n\t\t\tpayload_index++;\n\t\t}\n\t}\n\n\treturn updated_payload;\n}\n", "import { update_object, walk_and_store_blobs } from \"../helpers/data\";\nimport {\n\tCommand,\n\ttype ApiData,\n\ttype EndpointInfo,\n\ttype JsApiData\n} from \"../types\";\nimport { FileData } from \"../upload\";\nimport type { Client } from \"..\";\nimport {\n\tFILE_PROCESSING_ERROR_MSG,\n\tNODEJS_FS_ERROR_MSG,\n\tROOT_URL_ERROR_MSG\n} from \"../constants\";\n\nexport async function handle_blob(\n\tthis: Client,\n\tendpoint: string,\n\tdata: unknown[],\n\tapi_info: EndpointInfo<JsApiData | ApiData>\n): Promise<unknown[]> {\n\tconst self = this;\n\n\tawait process_local_file_commands(self, data);\n\n\tconst blobRefs = await walk_and_store_blobs(\n\t\tdata,\n\t\tundefined,\n\t\t[],\n\t\ttrue,\n\t\tapi_info\n\t);\n\n\tconst results = await Promise.all(\n\t\tblobRefs.map(async ({ path, blob, type }) => {\n\t\t\tif (!blob) return { path, type };\n\n\t\t\tconst response = await self.upload_files(endpoint, [blob]);\n\t\t\tconst file_url = response.files && response.files[0];\n\t\t\treturn {\n\t\t\t\tpath,\n\t\t\t\tfile_url,\n\t\t\t\ttype,\n\t\t\t\tname:\n\t\t\t\t\ttypeof File !== \"undefined\" && blob instanceof File\n\t\t\t\t\t\t? blob?.name\n\t\t\t\t\t\t: undefined\n\t\t\t};\n\t\t})\n\t);\n\n\tresults.forEach(({ path, file_url, type, name }) => {\n\t\tif (type === \"Gallery\") {\n\t\t\tupdate_object(data, file_url, path);\n\t\t} else if (file_url) {\n\t\t\tconst file = new FileData({ path: file_url, orig_name: name });\n\t\t\tupdate_object(data, file, path);\n\t\t}\n\t});\n\n\treturn data;\n}\n\nexport async function process_local_file_commands(\n\tclient: Client,\n\tdata: unknown[]\n): Promise<void> {\n\tconst root = client.config?.root || client.config?.root_url;\n\n\tif (!root) {\n\t\tthrow new Error(ROOT_URL_ERROR_MSG);\n\t}\n\n\tawait recursively_process_commands(client, data);\n}\n\nasync function recursively_process_commands(\n\tclient: Client,\n\tdata: any,\n\tpath: string[] = []\n): Promise<void> {\n\tfor (const key in data) {\n\t\tif (data[key] instanceof Command) {\n\t\t\tawait process_single_command(client, data, key);\n\t\t} else if (typeof data[key] === \"object\" && data[key] !== null) {\n\t\t\tawait recursively_process_commands(client, data[key], [...path, key]);\n\t\t}\n\t}\n}\n\nasync function process_single_command(\n\tclient: Client,\n\tdata: any,\n\tkey: string\n): Promise<void> {\n\tlet cmd_item = data[key] as Command;\n\tconst root = client.config?.root || client.config?.root_url;\n\n\tif (!root) {\n\t\tthrow new Error(ROOT_URL_ERROR_MSG);\n\t}\n\n\ttry {\n\t\tlet fileBuffer: Buffer;\n\t\tlet fullPath: string;\n\n\t\t// check if running in a Node.js environment\n\t\tif (\n\t\t\ttypeof process !== \"undefined\" &&\n\t\t\tprocess.versions &&\n\t\t\tprocess.versions.node\n\t\t) {\n\t\t\tconst fs = await import(\"fs/promises\");\n\t\t\tconst path = await import(\"path\");\n\n\t\t\tfullPath = path.resolve(process.cwd(), cmd_item.meta.path);\n\t\t\tfileBuffer = await fs.readFile(fullPath); // Read file from disk\n\t\t} else {\n\t\t\tthrow new Error(NODEJS_FS_ERROR_MSG);\n\t\t}\n\n\t\tconst file = new Blob([fileBuffer], { type: \"application/octet-stream\" });\n\n\t\tconst response = await client.upload_files(root, [file]);\n\n\t\tconst file_url = response.files && response.files[0];\n\n\t\tif (file_url) {\n\t\t\tconst fileData = new FileData({\n\t\t\t\tpath: file_url,\n\t\t\t\torig_name: cmd_item.meta.name || \"\"\n\t\t\t});\n\n\t\t\t// replace the command object with the fileData object\n\t\t\tdata[key] = fileData;\n\t\t}\n\t} catch (error) {\n\t\tconsole.error(FILE_PROCESSING_ERROR_MSG, error);\n\t}\n}\n", "import { BROKEN_CONNECTION_MSG } from \"../constants\";\nimport type { PostResponse } from \"../types\";\nimport { Client } from \"..\";\n\nexport async function post_data(\n\tthis: Client,\n\turl: string,\n\tbody: unknown,\n\tadditional_headers?: any\n): Promise<[PostResponse, number]> {\n\tconst headers: {\n\t\tAuthorization?: string;\n\t\t\"Content-Type\": \"application/json\";\n\t} = { \"Content-Type\": \"application/json\" };\n\tif (this.options.hf_token) {\n\t\theaders.Authorization = `Bearer ${this.options.hf_token}`;\n\t}\n\ttry {\n\t\tvar response = await this.fetch(url, {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(body),\n\t\t\theaders: { ...headers, ...additional_headers },\n\t\t\tcredentials: \"include\"\n\t\t});\n\t} catch (e) {\n\t\treturn [{ error: BROKEN_CONNECTION_MSG }, 500];\n\t}\n\tlet output: PostResponse;\n\tlet status: number;\n\ttry {\n\t\toutput = await response.json();\n\t\tstatus = response.status;\n\t} catch (e) {\n\t\toutput = { error: `Could not parse server response: ${e}` };\n\t\tstatus = 500;\n\t}\n\treturn [output, status];\n}\n", "import { Client } from \"../client\";\nimport type { Dependency, PredictReturn } from \"../types\";\n\nexport async function predict(\n\tthis: Client,\n\tendpoint: string | number,\n\tdata: unknown[] | Record<string, unknown> = {}\n): Promise<PredictReturn> {\n\tlet data_returned = false;\n\tlet status_complete = false;\n\tlet dependency: Dependency;\n\n\tif (!this.config) {\n\t\tthrow new Error(\"Could not resolve app config\");\n\t}\n\n\tif (typeof endpoint === \"number\") {\n\t\tdependency = this.config.dependencies.find((dep) => dep.id == endpoint)!;\n\t} else {\n\t\tconst trimmed_endpoint = endpoint.replace(/^\\//, \"\");\n\t\tdependency = this.config.dependencies.find(\n\t\t\t(dep) => dep.id == this.api_map[trimmed_endpoint]\n\t\t)!;\n\t}\n\n\treturn new Promise(async (resolve, reject) => {\n\t\tconst app = this.submit(endpoint, data, null, null, true);\n\t\tlet result: unknown;\n\n\t\tfor await (const message of app) {\n\t\t\tif (message.type === \"data\") {\n\t\t\t\tif (status_complete) {\n\t\t\t\t\tresolve(result as PredictReturn);\n\t\t\t\t}\n\t\t\t\tdata_returned = true;\n\t\t\t\tresult = message;\n\t\t\t}\n\n\t\t\tif (message.type === \"status\") {\n\t\t\t\tif (message.stage === \"error\") reject(message);\n\t\t\t\tif (message.stage === \"complete\") {\n\t\t\t\t\tstatus_complete = true;\n\t\t\t\t\t// if complete message comes after data, resolve here\n\t\t\t\t\tif (data_returned) {\n\t\t\t\t\t\tresolve(result as PredictReturn);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n}\n", "import {\n\tRUNTIME_URL,\n\tSLEEPTIME_URL,\n\tSPACE_STATUS_ERROR_MSG\n} from \"../constants\";\nimport { RE_SPACE_NAME } from \"./api_info\";\nimport type { SpaceStatusCallback } from \"../types\";\n\nexport async function check_space_status(\n\tid: string,\n\ttype: \"subdomain\" | \"space_name\",\n\tstatus_callback: SpaceStatusCallback\n): Promise<void> {\n\tlet endpoint =\n\t\ttype === \"subdomain\"\n\t\t\t? `https://huggingface.co/api/spaces/by-subdomain/${id}`\n\t\t\t: `https://huggingface.co/api/spaces/${id}`;\n\tlet response;\n\tlet _status;\n\ttry {\n\t\tresponse = await fetch(endpoint);\n\t\t_status = response.status;\n\t\tif (_status !== 200) {\n\t\t\tthrow new Error();\n\t\t}\n\t\tresponse = await response.json();\n\t} catch (e) {\n\t\tstatus_callback({\n\t\t\tstatus: \"error\",\n\t\t\tload_status: \"error\",\n\t\t\tmessage: SPACE_STATUS_ERROR_MSG,\n\t\t\tdetail: \"NOT_FOUND\"\n\t\t});\n\t\treturn;\n\t}\n\n\tif (!response || _status !== 200) return;\n\tconst {\n\t\truntime: { stage },\n\t\tid: space_name\n\t} = response;\n\n\tswitch (stage) {\n\t\tcase \"STOPPED\":\n\t\tcase \"SLEEPING\":\n\t\t\tstatus_callback({\n\t\t\t\tstatus: \"sleeping\",\n\t\t\t\tload_status: \"pending\",\n\t\t\t\tmessage: \"Space is asleep. Waking it up...\",\n\t\t\t\tdetail: stage\n\t\t\t});\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tcheck_space_status(id, type, status_callback);\n\t\t\t}, 1000); // poll for status\n\t\t\tbreak;\n\t\tcase \"PAUSED\":\n\t\t\tstatus_callback({\n\t\t\t\tstatus: \"paused\",\n\t\t\t\tload_status: \"error\",\n\t\t\t\tmessage:\n\t\t\t\t\t\"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.\",\n\t\t\t\tdetail: stage,\n\t\t\t\tdiscussions_enabled: await discussions_enabled(space_name)\n\t\t\t});\n\t\t\tbreak;\n\t\tcase \"RUNNING\":\n\t\tcase \"RUNNING_BUILDING\":\n\t\t\tstatus_callback({\n\t\t\t\tstatus: \"running\",\n\t\t\t\tload_status: \"complete\",\n\t\t\t\tmessage: \"Space is running.\",\n\t\t\t\tdetail: stage\n\t\t\t});\n\t\t\tbreak;\n\t\tcase \"BUILDING\":\n\t\t\tstatus_callback({\n\t\t\t\tstatus: \"building\",\n\t\t\t\tload_status: \"pending\",\n\t\t\t\tmessage: \"Space is building...\",\n\t\t\t\tdetail: stage\n\t\t\t});\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tcheck_space_status(id, type, status_callback);\n\t\t\t}, 1000);\n\t\t\tbreak;\n\t\tcase \"APP_STARTING\":\n\t\t\tstatus_callback({\n\t\t\t\tstatus: \"starting\",\n\t\t\t\tload_status: \"pending\",\n\t\t\t\tmessage: \"Space is starting...\",\n\t\t\t\tdetail: stage\n\t\t\t});\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tcheck_space_status(id, type, status_callback);\n\t\t\t}, 1000);\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tstatus_callback({\n\t\t\t\tstatus: \"space_error\",\n\t\t\t\tload_status: \"error\",\n\t\t\t\tmessage: \"This space is experiencing an issue.\",\n\t\t\t\tdetail: stage,\n\t\t\t\tdiscussions_enabled: await discussions_enabled(space_name)\n\t\t\t});\n\t\t\tbreak;\n\t}\n}\n\nexport const check_and_wake_space = async (\n\tspace_id: string,\n\tstatus_callback: SpaceStatusCallback\n): Promise<void> => {\n\tlet retries = 0;\n\tconst max_retries = 12;\n\tconst check_interval = 5000;\n\n\treturn new Promise((resolve) => {\n\t\tcheck_space_status(\n\t\t\tspace_id,\n\t\t\tRE_SPACE_NAME.test(space_id) ? \"space_name\" : \"subdomain\",\n\t\t\t(status) => {\n\t\t\t\tstatus_callback(status);\n\n\t\t\t\tif (status.status === \"running\") {\n\t\t\t\t\tresolve();\n\t\t\t\t} else if (\n\t\t\t\t\tstatus.status === \"error\" ||\n\t\t\t\t\tstatus.status === \"paused\" ||\n\t\t\t\t\tstatus.status === \"space_error\"\n\t\t\t\t) {\n\t\t\t\t\tresolve();\n\t\t\t\t} else if (\n\t\t\t\t\tstatus.status === \"sleeping\" ||\n\t\t\t\t\tstatus.status === \"building\"\n\t\t\t\t) {\n\t\t\t\t\tif (retries < max_retries) {\n\t\t\t\t\t\tretries++;\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tcheck_and_wake_space(space_id, status_callback).then(resolve);\n\t\t\t\t\t\t}, check_interval);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresolve();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t});\n};\n\nconst RE_DISABLED_DISCUSSION =\n\t/^(?=[^]*\\b[dD]iscussions{0,1}\\b)(?=[^]*\\b[dD]isabled\\b)[^]*$/;\nexport async function discussions_enabled(space_id: string): Promise<boolean> {\n\ttry {\n\t\tconst r = await fetch(\n\t\t\t`https://huggingface.co/api/spaces/${space_id}/discussions`,\n\t\t\t{\n\t\t\t\tmethod: \"HEAD\"\n\t\t\t}\n\t\t);\n\n\t\tconst error = r.headers.get(\"x-error-message\");\n\n\t\tif (!r.ok || (error && RE_DISABLED_DISCUSSION.test(error))) return false;\n\t\treturn true;\n\t} catch (e) {\n\t\treturn false;\n\t}\n}\n\nexport async function get_space_hardware(\n\tspace_id: string,\n\thf_token?: `hf_${string}` | undefined\n): Promise<(typeof hardware_types)[number]> {\n\tconst headers: { Authorization?: string } = {};\n\tif (hf_token) {\n\t\theaders.Authorization = `Bearer ${hf_token}`;\n\t}\n\n\ttry {\n\t\tconst res = await fetch(\n\t\t\t`https://huggingface.co/api/spaces/${space_id}/${RUNTIME_URL}`,\n\t\t\t{ headers }\n\t\t);\n\n\t\tif (res.status !== 200)\n\t\t\tthrow new Error(\"Space hardware could not be obtained.\");\n\n\t\tconst { hardware } = await res.json();\n\n\t\treturn hardware.current;\n\t} catch (e: any) {\n\t\tthrow new Error(e.message);\n\t}\n}\n\nexport async function set_space_timeout(\n\tspace_id: string,\n\ttimeout: number,\n\thf_token?: `hf_${string}`\n): Promise<any> {\n\tconst headers: { Authorization?: string } = {};\n\tif (hf_token) {\n\t\theaders.Authorization = `Bearer ${hf_token}`;\n\t}\n\n\tconst body: {\n\t\tseconds?: number;\n\t} = {\n\t\tseconds: timeout\n\t};\n\n\ttry {\n\t\tconst res = await fetch(\n\t\t\t`https://huggingface.co/api/spaces/${space_id}/${SLEEPTIME_URL}`,\n\t\t\t{\n\t\t\t\tmethod: \"POST\",\n\t\t\t\theaders: { \"Content-Type\": \"application/json\", ...headers },\n\t\t\t\tbody: JSON.stringify(body)\n\t\t\t}\n\t\t);\n\n\t\tif (res.status !== 200) {\n\t\t\tthrow new Error(\n\t\t\t\t\"Could not set sleep timeout on duplicated Space. Please visit *ADD HF LINK TO SETTINGS* to set a timeout manually to reduce billing charges.\"\n\t\t\t);\n\t\t}\n\n\t\tconst response = await res.json();\n\t\treturn response;\n\t} catch (e: any) {\n\t\tthrow new Error(e.message);\n\t}\n}\n\nexport const hardware_types = [\n\t\"cpu-basic\",\n\t\"cpu-upgrade\",\n\t\"cpu-xl\",\n\t\"t4-small\",\n\t\"t4-medium\",\n\t\"a10g-small\",\n\t\"a10g-large\",\n\t\"a10g-largex2\",\n\t\"a10g-largex4\",\n\t\"a100-large\",\n\t\"zero-a10g\",\n\t\"h100\",\n\t\"h100x8\"\n] as const;\n", "import {\n\tget_space_hardware,\n\thardware_types,\n\tset_space_timeout\n} from \"../helpers/spaces\";\nimport type { DuplicateOptions } from \"../types\";\nimport { Client } from \"../client\";\nimport { SPACE_METADATA_ERROR_MSG } from \"../constants\";\nimport {\n\tget_cookie_header,\n\tparse_and_set_cookies\n} from \"../helpers/init_helpers\";\nimport { process_endpoint } from \"../helpers/api_info\";\n\nexport async function duplicate(\n\tapp_reference: string,\n\toptions: DuplicateOptions\n): Promise<Client> {\n\tconst { hf_token, private: _private, hardware, timeout, auth } = options;\n\n\tif (hardware && !hardware_types.includes(hardware)) {\n\t\tthrow new Error(\n\t\t\t`Invalid hardware type provided. Valid types are: ${hardware_types\n\t\t\t\t.map((v) => `\"${v}\"`)\n\t\t\t\t.join(\",\")}.`\n\t\t);\n\t}\n\n\tconst { http_protocol, host } = await process_endpoint(\n\t\tapp_reference,\n\t\thf_token\n\t);\n\n\tlet cookies: string[] | null = null;\n\n\tif (auth) {\n\t\tconst cookie_header = await get_cookie_header(\n\t\t\thttp_protocol,\n\t\t\thost,\n\t\t\tauth,\n\t\t\tfetch\n\t\t);\n\n\t\tif (cookie_header) cookies = parse_and_set_cookies(cookie_header);\n\t}\n\n\tconst headers = {\n\t\tAuthorization: `Bearer ${hf_token}`,\n\t\t\"Content-Type\": \"application/json\",\n\t\t...(cookies ? { Cookie: cookies.join(\"; \") } : {})\n\t};\n\n\tconst user = (\n\t\tawait (\n\t\t\tawait fetch(`https://huggingface.co/api/whoami-v2`, {\n\t\t\t\theaders\n\t\t\t})\n\t\t).json()\n\t).name;\n\n\tconst space_name = app_reference.split(\"/\")[1];\n\tconst body: {\n\t\trepository: string;\n\t\tprivate?: boolean;\n\t\thardware?: string;\n\t} = {\n\t\trepository: `${user}/${space_name}`\n\t};\n\n\tif (_private) {\n\t\tbody.private = true;\n\t}\n\n\tlet original_hardware;\n\n\ttry {\n\t\tif (!hardware) {\n\t\t\toriginal_hardware = await get_space_hardware(app_reference, hf_token);\n\t\t}\n\t} catch (e) {\n\t\tthrow Error(SPACE_METADATA_ERROR_MSG + (e as Error).message);\n\t}\n\n\tconst requested_hardware = hardware || original_hardware || \"cpu-basic\";\n\n\tbody.hardware = requested_hardware;\n\n\ttry {\n\t\tconst response = await fetch(\n\t\t\t`https://huggingface.co/api/spaces/${app_reference}/duplicate`,\n\t\t\t{\n\t\t\t\tmethod: \"POST\",\n\t\t\t\theaders,\n\t\t\t\tbody: JSON.stringify(body)\n\t\t\t}\n\t\t);\n\n\t\tif (response.status === 409) {\n\t\t\ttry {\n\t\t\t\tconst client = await Client.connect(`${user}/${space_name}`, options);\n\t\t\t\treturn client;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Failed to connect Client instance:\", error);\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t} else if (response.status !== 200) {\n\t\t\tthrow new Error(response.statusText);\n\t\t}\n\n\t\tconst duplicated_space = await response.json();\n\n\t\tawait set_space_timeout(`${user}/${space_name}`, timeout || 300, hf_token);\n\n\t\treturn await Client.connect(\n\t\t\tget_space_reference(duplicated_space.url),\n\t\t\toptions\n\t\t);\n\t} catch (e: any) {\n\t\tthrow new Error(e);\n\t}\n}\n\nfunction get_space_reference(url: string): any {\n\tconst regex = /https:\\/\\/huggingface.co\\/spaces\\/([^/]+\\/[^/]+)/;\n\tconst match = url.match(regex);\n\tif (match) {\n\t\treturn match[1];\n\t}\n}\n", "// Copyright 2018-2024 the Deno authors. All rights reserved. MIT license.\n// This module is browser compatible.\n/**\n * Transform a stream into a stream where each chunk is divided by a newline,\n * be it `\\n` or `\\r\\n`. `\\r` can be enabled via the `allowCR` option.\n *\n * @example\n * ```ts\n * import { TextLineStream } from \"@std/streams/text-line-stream\";\n *\n * const res = await fetch(\"https://example.com\");\n * const lines = res.body!\n *   .pipeThrough(new TextDecoderStream())\n *   .pipeThrough(new TextLineStream());\n * ```\n */\nexport class TextLineStream extends TransformStream {\n    #currentLine = \"\";\n    /** Constructs a new instance. */\n    constructor(options = { allowCR: false }) {\n        super({\n            transform: (chars, controller) => {\n                chars = this.#currentLine + chars;\n                while (true) {\n                    const lfIndex = chars.indexOf(\"\\n\");\n                    const crIndex = options.allowCR ? chars.indexOf(\"\\r\") : -1;\n                    if (crIndex !== -1 && crIndex !== (chars.length - 1) &&\n                        (lfIndex === -1 || (lfIndex - 1) > crIndex)) {\n                        controller.enqueue(chars.slice(0, crIndex));\n                        chars = chars.slice(crIndex + 1);\n                        continue;\n                    }\n                    if (lfIndex === -1)\n                        break;\n                    const endIndex = chars[lfIndex - 1] === \"\\r\" ? lfIndex - 1 : lfIndex;\n                    controller.enqueue(chars.slice(0, endIndex));\n                    chars = chars.slice(lfIndex + 1);\n                }\n                this.#currentLine = chars;\n            },\n            flush: (controller) => {\n                if (this.#currentLine === \"\")\n                    return;\n                const currentLine = options.allowCR && this.#currentLine.endsWith(\"\\r\")\n                    ? this.#currentLine.slice(0, -1)\n                    : this.#currentLine;\n                controller.enqueue(currentLine);\n            },\n        });\n    }\n}\n", "import { TextLineStream } from './deps/jsr.io/@std/streams/0.221.0/text_line_stream.js';\nexport function stream(input) {\n    let decoder = new TextDecoderStream();\n    let split = new TextLineStream({ allowCR: true });\n    return input.pipeThrough(decoder).pipeThrough(split);\n}\nexport function split(input) {\n    let rgx = /[:]\\s*/;\n    let match = rgx.exec(input);\n    // \": comment\" -> index=0 -> ignore\n    let idx = match && match.index;\n    if (idx) {\n        return [\n            input.substring(0, idx),\n            input.substring(idx + match[0].length),\n        ];\n    }\n}\nexport function fallback(headers, key, value) {\n    let tmp = headers.get(key);\n    if (!tmp)\n        headers.set(key, value);\n}\n", "import * as utils from './utils.js';\n/**\n * Convert a `Response` body containing Server Sent Events (SSE) into an Async Iterator that yields {@linkcode ServerSentEventMessage} objects.\n *\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events}\n *\n * @example\n * ```js\n * // Optional\n * let abort = new AbortController;\n *\n * // Manually fetch a Response\n * let res = await fetch('https://...', {\n *   method: 'POST',\n *   signal: abort.signal,\n *   headers: {\n *     'api-key': 'token <value>',\n *     'content-type': 'application/json',\n *   },\n *   body: JSON.stringify({\n *     stream: true, // <- hypothetical\n *     // ...\n *   })\n * });\n *\n * if (res.ok) {\n *   let stream = events(res, abort.signal);\n *   for await (let event of stream) {\n *     console.log('<<', event.data);\n *   }\n * }\n * ```\n */\nexport async function* events(res, signal) {\n    // TODO: throw error?\n    if (!res.body)\n        return;\n    let iter = utils.stream(res.body);\n    let line, reader = iter.getReader();\n    let event;\n    for (;;) {\n        if (signal && signal.aborted) {\n            return reader.cancel();\n        }\n        line = await reader.read();\n        if (line.done)\n            return;\n        if (!line.value) {\n            if (event)\n                yield event;\n            event = undefined;\n            continue;\n        }\n        let [field, value] = utils.split(line.value) || [];\n        if (!field)\n            continue; // comment or invalid\n        if (field === 'data') {\n            event ||= {};\n            event[field] = event[field] ? (event[field] + '\\n' + value) : value;\n        }\n        else if (field === 'event') {\n            event ||= {};\n            event[field] = value;\n        }\n        else if (field === 'id') {\n            event ||= {};\n            event[field] = +value || value;\n        }\n        else if (field === 'retry') {\n            event ||= {};\n            event[field] = +value || undefined;\n        }\n    }\n}\n/**\n * Convenience function that will `fetch` with the given arguments and, if ok, will return the {@linkcode events} async iterator.\n *\n * If the response is not ok (status 200-299), the `Response` is thrown.\n *\n * @example\n * ```js\n * // NOTE: throws `Response` if not 2xx status\n * let events = await stream('https://api.openai.com/...', {\n *   method: 'POST',\n *   headers: {\n *     'Authorization': 'Bearer <token>',\n *     'Content-Type': 'application/json',\n *   },\n *   body: JSON.stringify({\n *     stream: true,\n *     // ...\n *   })\n * });\n *\n * for await (let event of events) {\n *   console.log('<<', JSON.parse(event.data));\n * }\n * ```\n */\nexport async function stream(input, init) {\n    let req = new Request(input, init);\n    utils.fallback(req.headers, 'Accept', 'text/event-stream');\n    utils.fallback(req.headers, 'Content-Type', 'application/json');\n    let r = await fetch(req);\n    if (!r.ok)\n        throw r;\n    return events(r, req.signal);\n}\n", "import { BROKEN_CONNECTION_MSG } from \"../constants\";\nimport type { Client } from \"../client\";\nimport { stream } from \"fetch-event-stream\";\n\nexport async function open_stream(this: Client): Promise<void> {\n\tlet {\n\t\tevent_callbacks,\n\t\tunclosed_events,\n\t\tpending_stream_messages,\n\t\tstream_status,\n\t\tconfig,\n\t\tjwt\n\t} = this;\n\n\tconst that = this;\n\n\tif (!config) {\n\t\tthrow new Error(\"Could not resolve app config\");\n\t}\n\n\tstream_status.open = true;\n\n\tlet stream: EventSource | null = null;\n\tlet params = new URLSearchParams({\n\t\tsession_hash: this.session_hash\n\t}).toString();\n\n\tlet url = new URL(`${config.root}/queue/data?${params}`);\n\n\tif (jwt) {\n\t\turl.searchParams.set(\"__sign\", jwt);\n\t}\n\n\tstream = this.stream(url);\n\n\tif (!stream) {\n\t\tconsole.warn(\"Cannot connect to SSE endpoint: \" + url.toString());\n\t\treturn;\n\t}\n\n\tstream.onmessage = async function (event: MessageEvent) {\n\t\tlet _data = JSON.parse(event.data);\n\t\tif (_data.msg === \"close_stream\") {\n\t\t\tclose_stream(stream_status, that.abort_controller);\n\t\t\treturn;\n\t\t}\n\t\tconst event_id = _data.event_id;\n\t\tif (!event_id) {\n\t\t\tawait Promise.all(\n\t\t\t\tObject.keys(event_callbacks).map((event_id) =>\n\t\t\t\t\tevent_callbacks[event_id](_data)\n\t\t\t\t)\n\t\t\t);\n\t\t} else if (event_callbacks[event_id] && config) {\n\t\t\tif (\n\t\t\t\t_data.msg === \"process_completed\" &&\n\t\t\t\t[\"sse\", \"sse_v1\", \"sse_v2\", \"sse_v2.1\", \"sse_v3\"].includes(\n\t\t\t\t\tconfig.protocol\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\tunclosed_events.delete(event_id);\n\t\t\t}\n\t\t\tlet fn: (data: any) => void = event_callbacks[event_id];\n\n\t\t\tif (typeof window !== \"undefined\" && typeof document !== \"undefined\") {\n\t\t\t\t// fn(_data); // need to do this to put the event on the end of the event loop, so the browser can refresh between callbacks and not freeze in case of quick generations. See\n\t\t\t\tsetTimeout(fn, 0, _data); // need to do this to put the event on the end of the event loop, so the browser can refresh between callbacks and not freeze in case of quick generations. See https://github.com/gradio-app/gradio/pull/7055\n\t\t\t} else {\n\t\t\t\tfn(_data);\n\t\t\t}\n\t\t} else {\n\t\t\tif (!pending_stream_messages[event_id]) {\n\t\t\t\tpending_stream_messages[event_id] = [];\n\t\t\t}\n\t\t\tpending_stream_messages[event_id].push(_data);\n\t\t}\n\t};\n\tstream.onerror = async function () {\n\t\tawait Promise.all(\n\t\t\tObject.keys(event_callbacks).map((event_id) =>\n\t\t\t\tevent_callbacks[event_id]({\n\t\t\t\t\tmsg: \"unexpected_error\",\n\t\t\t\t\tmessage: BROKEN_CONNECTION_MSG\n\t\t\t\t})\n\t\t\t)\n\t\t);\n\t};\n}\n\nexport function close_stream(\n\tstream_status: { open: boolean },\n\tabort_controller: AbortController | null\n): void {\n\tif (stream_status) {\n\t\tstream_status.open = false;\n\t\tabort_controller?.abort();\n\t}\n}\n\nexport function apply_diff_stream(\n\tpending_diff_streams: Record<string, any[][]>,\n\tevent_id: string,\n\tdata: any\n): void {\n\tlet is_first_generation = !pending_diff_streams[event_id];\n\tif (is_first_generation) {\n\t\tpending_diff_streams[event_id] = [];\n\t\tdata.data.forEach((value: any, i: number) => {\n\t\t\tpending_diff_streams[event_id][i] = value;\n\t\t});\n\t} else {\n\t\tdata.data.forEach((value: any, i: number) => {\n\t\t\tlet new_data = apply_diff(pending_diff_streams[event_id][i], value);\n\t\t\tpending_diff_streams[event_id][i] = new_data;\n\t\t\tdata.data[i] = new_data;\n\t\t});\n\t}\n}\n\nexport function apply_diff(\n\tobj: any,\n\tdiff: [string, (number | string)[], any][]\n): any {\n\tdiff.forEach(([action, path, value]) => {\n\t\tobj = apply_edit(obj, path, action, value);\n\t});\n\n\treturn obj;\n}\n\nfunction apply_edit(\n\ttarget: any,\n\tpath: (number | string)[],\n\taction: string,\n\tvalue: any\n): any {\n\tif (path.length === 0) {\n\t\tif (action === \"replace\") {\n\t\t\treturn value;\n\t\t} else if (action === \"append\") {\n\t\t\treturn target + value;\n\t\t}\n\t\tthrow new Error(`Unsupported action: ${action}`);\n\t}\n\n\tlet current = target;\n\tfor (let i = 0; i < path.length - 1; i++) {\n\t\tcurrent = current[path[i]];\n\t}\n\n\tconst last_path = path[path.length - 1];\n\tswitch (action) {\n\t\tcase \"replace\":\n\t\t\tcurrent[last_path] = value;\n\t\t\tbreak;\n\t\tcase \"append\":\n\t\t\tcurrent[last_path] += value;\n\t\t\tbreak;\n\t\tcase \"add\":\n\t\t\tif (Array.isArray(current)) {\n\t\t\t\tcurrent.splice(Number(last_path), 0, value);\n\t\t\t} else {\n\t\t\t\tcurrent[last_path] = value;\n\t\t\t}\n\t\t\tbreak;\n\t\tcase \"delete\":\n\t\t\tif (Array.isArray(current)) {\n\t\t\t\tcurrent.splice(Number(last_path), 1);\n\t\t\t} else {\n\t\t\t\tdelete current[last_path];\n\t\t\t}\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tthrow new Error(`Unknown action: ${action}`);\n\t}\n\treturn target;\n}\n\nexport function readable_stream(\n\tinput: RequestInfo | URL,\n\tinit: RequestInit = {}\n): EventSource {\n\tconst instance: EventSource & { readyState: number } = {\n\t\tclose: () => {\n\t\t\tconsole.warn(\"Method not implemented.\");\n\t\t},\n\t\tonerror: null,\n\t\tonmessage: null,\n\t\tonopen: null,\n\t\treadyState: 0,\n\t\turl: input.toString(),\n\t\twithCredentials: false,\n\t\tCONNECTING: 0,\n\t\tOPEN: 1,\n\t\tCLOSED: 2,\n\t\taddEventListener: () => {\n\t\t\tthrow new Error(\"Method not implemented.\");\n\t\t},\n\t\tdispatchEvent: () => {\n\t\t\tthrow new Error(\"Method not implemented.\");\n\t\t},\n\t\tremoveEventListener: () => {\n\t\t\tthrow new Error(\"Method not implemented.\");\n\t\t}\n\t};\n\n\tstream(input, init)\n\t\t.then(async (res) => {\n\t\t\tinstance.readyState = instance.OPEN;\n\t\t\ttry {\n\t\t\t\tfor await (const chunk of res) {\n\t\t\t\t\t//@ts-ignore\n\t\t\t\t\tinstance.onmessage && instance.onmessage(chunk);\n\t\t\t\t}\n\t\t\t\tinstance.readyState = instance.CLOSED;\n\t\t\t} catch (e) {\n\t\t\t\tinstance.onerror && instance.onerror(e as Event);\n\t\t\t\tinstance.readyState = instance.CLOSED;\n\t\t\t}\n\t\t})\n\t\t.catch((e) => {\n\t\t\tconsole.error(e);\n\t\t\tinstance.onerror && instance.onerror(e as Event);\n\t\t\tinstance.readyState = instance.CLOSED;\n\t\t});\n\n\treturn instance as EventSource;\n}\n", "/* eslint-disable complexity */\nimport type {\n\tStatus,\n\tPayload,\n\tGradioEvent,\n\tJsApiData,\n\tEndpointInfo,\n\tApiInfo,\n\tConfig,\n\tDependency,\n\tSubmitIterable\n} from \"../types\";\n\nimport { skip_queue, post_message, handle_payload } from \"../helpers/data\";\nimport { resolve_root } from \"../helpers/init_helpers\";\nimport {\n\thandle_message,\n\tmap_data_to_params,\n\tprocess_endpoint\n} from \"../helpers/api_info\";\nimport semiver from \"semiver\";\nimport { BROKEN_CONNECTION_MSG, QUEUE_FULL_MSG } from \"../constants\";\nimport { apply_diff_stream, close_stream } from \"./stream\";\nimport { Client } from \"../client\";\n\nexport function submit(\n\tthis: Client,\n\tendpoint: string | number,\n\tdata: unknown[] | Record<string, unknown> = {},\n\tevent_data?: unknown,\n\ttrigger_id?: number | null,\n\tall_events?: boolean\n): SubmitIterable<GradioEvent> {\n\ttry {\n\t\tconst { hf_token } = this.options;\n\t\tconst {\n\t\t\tfetch,\n\t\t\tapp_reference,\n\t\t\tconfig,\n\t\t\tsession_hash,\n\t\t\tapi_info,\n\t\t\tapi_map,\n\t\t\tstream_status,\n\t\t\tpending_stream_messages,\n\t\t\tpending_diff_streams,\n\t\t\tevent_callbacks,\n\t\t\tunclosed_events,\n\t\t\tpost_data,\n\t\t\toptions\n\t\t} = this;\n\n\t\tconst that = this;\n\n\t\tif (!api_info) throw new Error(\"No API found\");\n\t\tif (!config) throw new Error(\"Could not resolve app config\");\n\n\t\tlet { fn_index, endpoint_info, dependency } = get_endpoint_info(\n\t\t\tapi_info,\n\t\t\tendpoint,\n\t\t\tapi_map,\n\t\t\tconfig\n\t\t);\n\n\t\tlet resolved_data = map_data_to_params(data, endpoint_info);\n\n\t\tlet websocket: WebSocket;\n\t\tlet stream: EventSource | null;\n\t\tlet protocol = config.protocol ?? \"ws\";\n\n\t\tconst _endpoint = typeof endpoint === \"number\" ? \"/predict\" : endpoint;\n\t\tlet payload: Payload;\n\t\tlet event_id: string | null = null;\n\t\tlet complete: Status | undefined | false = false;\n\t\tlet last_status: Record<string, Status[\"stage\"]> = {};\n\t\tlet url_params =\n\t\t\ttypeof window !== \"undefined\" && typeof document !== \"undefined\"\n\t\t\t\t? new URLSearchParams(window.location.search).toString()\n\t\t\t\t: \"\";\n\n\t\tconst events_to_publish =\n\t\t\toptions?.events?.reduce(\n\t\t\t\t(acc, event) => {\n\t\t\t\t\tacc[event] = true;\n\t\t\t\t\treturn acc;\n\t\t\t\t},\n\t\t\t\t{} as Record<string, boolean>\n\t\t\t) || {};\n\n\t\t// event subscription methods\n\t\tfunction fire_event(event: GradioEvent): void {\n\t\t\tif (all_events || events_to_publish[event.type]) {\n\t\t\t\tpush_event(event);\n\t\t\t}\n\t\t}\n\n\t\tasync function cancel(): Promise<void> {\n\t\t\tconst _status: Status = {\n\t\t\t\tstage: \"complete\",\n\t\t\t\tqueue: false,\n\t\t\t\ttime: new Date()\n\t\t\t};\n\t\t\tcomplete = _status;\n\t\t\tfire_event({\n\t\t\t\t..._status,\n\t\t\t\ttype: \"status\",\n\t\t\t\tendpoint: _endpoint,\n\t\t\t\tfn_index: fn_index\n\t\t\t});\n\n\t\t\tlet reset_request = {};\n\t\t\tlet cancel_request = {};\n\t\t\tif (protocol === \"ws\") {\n\t\t\t\tif (websocket && websocket.readyState === 0) {\n\t\t\t\t\twebsocket.addEventListener(\"open\", () => {\n\t\t\t\t\t\twebsocket.close();\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\twebsocket.close();\n\t\t\t\t}\n\t\t\t\treset_request = { fn_index, session_hash };\n\t\t\t} else {\n\t\t\t\tclose_stream(stream_status, that.abort_controller);\n\t\t\t\tclose();\n\t\t\t\treset_request = { event_id };\n\t\t\t\tcancel_request = { event_id, session_hash, fn_index };\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tif (!config) {\n\t\t\t\t\tthrow new Error(\"Could not resolve app config\");\n\t\t\t\t}\n\n\t\t\t\tif (\"event_id\" in cancel_request) {\n\t\t\t\t\tawait fetch(`${config.root}/cancel`, {\n\t\t\t\t\t\theaders: { \"Content-Type\": \"application/json\" },\n\t\t\t\t\t\tmethod: \"POST\",\n\t\t\t\t\t\tbody: JSON.stringify(cancel_request)\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tawait fetch(`${config.root}/reset`, {\n\t\t\t\t\theaders: { \"Content-Type\": \"application/json\" },\n\t\t\t\t\tmethod: \"POST\",\n\t\t\t\t\tbody: JSON.stringify(reset_request)\n\t\t\t\t});\n\t\t\t} catch (e) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t\"The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.\"\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst resolve_heartbeat = async (config: Config): Promise<void> => {\n\t\t\tawait this._resolve_hearbeat(config);\n\t\t};\n\n\t\tasync function handle_render_config(render_config: any): Promise<void> {\n\t\t\tif (!config) return;\n\t\t\tlet render_id: number = render_config.render_id;\n\t\t\tconfig.components = [\n\t\t\t\t...config.components.filter((c) => c.props.rendered_in !== render_id),\n\t\t\t\t...render_config.components\n\t\t\t];\n\t\t\tconfig.dependencies = [\n\t\t\t\t...config.dependencies.filter((d) => d.rendered_in !== render_id),\n\t\t\t\t...render_config.dependencies\n\t\t\t];\n\t\t\tconst any_state = config.components.some((c) => c.type === \"state\");\n\t\t\tconst any_unload = config.dependencies.some((d) =>\n\t\t\t\td.targets.some((t) => t[1] === \"unload\")\n\t\t\t);\n\t\t\tconfig.connect_heartbeat = any_state || any_unload;\n\t\t\tawait resolve_heartbeat(config);\n\t\t\tfire_event({\n\t\t\t\ttype: \"render\",\n\t\t\t\tdata: render_config,\n\t\t\t\tendpoint: _endpoint,\n\t\t\t\tfn_index\n\t\t\t});\n\t\t}\n\n\t\tthis.handle_blob(config.root, resolved_data, endpoint_info).then(\n\t\t\tasync (_payload) => {\n\t\t\t\tlet input_data = handle_payload(\n\t\t\t\t\t_payload,\n\t\t\t\t\tdependency,\n\t\t\t\t\tconfig.components,\n\t\t\t\t\t\"input\",\n\t\t\t\t\ttrue\n\t\t\t\t);\n\t\t\t\tpayload = {\n\t\t\t\t\tdata: input_data || [],\n\t\t\t\t\tevent_data,\n\t\t\t\t\tfn_index,\n\t\t\t\t\ttrigger_id\n\t\t\t\t};\n\t\t\t\tif (skip_queue(fn_index, config)) {\n\t\t\t\t\tfire_event({\n\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\tstage: \"pending\",\n\t\t\t\t\t\tqueue: false,\n\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t});\n\n\t\t\t\t\tpost_data(\n\t\t\t\t\t\t`${config.root}/run${\n\t\t\t\t\t\t\t_endpoint.startsWith(\"/\") ? _endpoint : `/${_endpoint}`\n\t\t\t\t\t\t}${url_params ? \"?\" + url_params : \"\"}`,\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t...payload,\n\t\t\t\t\t\t\tsession_hash\n\t\t\t\t\t\t}\n\t\t\t\t\t)\n\t\t\t\t\t\t.then(([output, status_code]: any) => {\n\t\t\t\t\t\t\tconst data = output.data;\n\t\t\t\t\t\t\tif (status_code == 200) {\n\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\ttype: \"data\",\n\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\tdata: handle_payload(\n\t\t\t\t\t\t\t\t\t\tdata,\n\t\t\t\t\t\t\t\t\t\tdependency,\n\t\t\t\t\t\t\t\t\t\tconfig.components,\n\t\t\t\t\t\t\t\t\t\t\"output\",\n\t\t\t\t\t\t\t\t\t\toptions.with_null_state\n\t\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t\tevent_data,\n\t\t\t\t\t\t\t\t\ttrigger_id\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tif (output.render_config) {\n\t\t\t\t\t\t\t\t\thandle_render_config(output.render_config);\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\tstage: \"complete\",\n\t\t\t\t\t\t\t\t\teta: output.average_duration,\n\t\t\t\t\t\t\t\t\tqueue: false,\n\t\t\t\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\tstage: \"error\",\n\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\tmessage: output.error,\n\t\t\t\t\t\t\t\t\tqueue: false,\n\t\t\t\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch((e) => {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\tstage: \"error\",\n\t\t\t\t\t\t\t\tmessage: e.message,\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\tqueue: false,\n\t\t\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t} else if (protocol == \"ws\") {\n\t\t\t\t\tconst { ws_protocol, host } = await process_endpoint(\n\t\t\t\t\t\tapp_reference,\n\t\t\t\t\t\thf_token\n\t\t\t\t\t);\n\n\t\t\t\t\tfire_event({\n\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\tstage: \"pending\",\n\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t});\n\n\t\t\t\t\tlet url = new URL(\n\t\t\t\t\t\t`${ws_protocol}://${resolve_root(\n\t\t\t\t\t\t\thost,\n\t\t\t\t\t\t\tconfig.path as string,\n\t\t\t\t\t\t\ttrue\n\t\t\t\t\t\t)}/queue/join${url_params ? \"?\" + url_params : \"\"}`\n\t\t\t\t\t);\n\n\t\t\t\t\tif (this.jwt) {\n\t\t\t\t\t\turl.searchParams.set(\"__sign\", this.jwt);\n\t\t\t\t\t}\n\n\t\t\t\t\twebsocket = new WebSocket(url);\n\n\t\t\t\t\twebsocket.onclose = (evt) => {\n\t\t\t\t\t\tif (!evt.wasClean) {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\tstage: \"error\",\n\t\t\t\t\t\t\t\tbroken: true,\n\t\t\t\t\t\t\t\tmessage: BROKEN_CONNECTION_MSG,\n\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\n\t\t\t\t\twebsocket.onmessage = function (event) {\n\t\t\t\t\t\tconst _data = JSON.parse(event.data);\n\t\t\t\t\t\tconst { type, status, data } = handle_message(\n\t\t\t\t\t\t\t_data,\n\t\t\t\t\t\t\tlast_status[fn_index]\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tif (type === \"update\" && status && !complete) {\n\t\t\t\t\t\t\t// call 'status' listeners\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t...status\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tif (status.stage === \"error\") {\n\t\t\t\t\t\t\t\twebsocket.close();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (type === \"hash\") {\n\t\t\t\t\t\t\twebsocket.send(JSON.stringify({ fn_index, session_hash }));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t} else if (type === \"data\") {\n\t\t\t\t\t\t\twebsocket.send(JSON.stringify({ ...payload, session_hash }));\n\t\t\t\t\t\t} else if (type === \"complete\") {\n\t\t\t\t\t\t\tcomplete = status;\n\t\t\t\t\t\t} else if (type === \"log\") {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"log\",\n\t\t\t\t\t\t\t\tlog: data.log,\n\t\t\t\t\t\t\t\tlevel: data.level,\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tduration: data.duration,\n\t\t\t\t\t\t\t\tvisible: data.visible,\n\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (type === \"generating\") {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t...status,\n\t\t\t\t\t\t\t\tstage: status?.stage!,\n\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (data) {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"data\",\n\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\tdata: handle_payload(\n\t\t\t\t\t\t\t\t\tdata.data,\n\t\t\t\t\t\t\t\t\tdependency,\n\t\t\t\t\t\t\t\t\tconfig.components,\n\t\t\t\t\t\t\t\t\t\"output\",\n\t\t\t\t\t\t\t\t\toptions.with_null_state\n\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\tevent_data,\n\t\t\t\t\t\t\t\ttrigger_id\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tif (complete) {\n\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t\t...complete,\n\t\t\t\t\t\t\t\t\tstage: status?.stage!,\n\t\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\twebsocket.close();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\n\t\t\t\t\t// different ws contract for gradio versions older than 3.6.0\n\t\t\t\t\t//@ts-ignore\n\t\t\t\t\tif (semiver(config.version || \"2.0.0\", \"3.6\") < 0) {\n\t\t\t\t\t\taddEventListener(\"open\", () =>\n\t\t\t\t\t\t\twebsocket.send(JSON.stringify({ hash: session_hash }))\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else if (protocol == \"sse\") {\n\t\t\t\t\tfire_event({\n\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\tstage: \"pending\",\n\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t});\n\t\t\t\t\tvar params = new URLSearchParams({\n\t\t\t\t\t\tfn_index: fn_index.toString(),\n\t\t\t\t\t\tsession_hash: session_hash\n\t\t\t\t\t}).toString();\n\t\t\t\t\tlet url = new URL(\n\t\t\t\t\t\t`${config.root}/queue/join?${\n\t\t\t\t\t\t\turl_params ? url_params + \"&\" : \"\"\n\t\t\t\t\t\t}${params}`\n\t\t\t\t\t);\n\n\t\t\t\t\tif (this.jwt) {\n\t\t\t\t\t\turl.searchParams.set(\"__sign\", this.jwt);\n\t\t\t\t\t}\n\n\t\t\t\t\tstream = this.stream(url);\n\n\t\t\t\t\tif (!stream) {\n\t\t\t\t\t\treturn Promise.reject(\n\t\t\t\t\t\t\tnew Error(\"Cannot connect to SSE endpoint: \" + url.toString())\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tstream.onmessage = async function (event: MessageEvent) {\n\t\t\t\t\t\tconst _data = JSON.parse(event.data);\n\t\t\t\t\t\tconst { type, status, data } = handle_message(\n\t\t\t\t\t\t\t_data,\n\t\t\t\t\t\t\tlast_status[fn_index]\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\tif (type === \"update\" && status && !complete) {\n\t\t\t\t\t\t\t// call 'status' listeners\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t...status\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tif (status.stage === \"error\") {\n\t\t\t\t\t\t\t\tstream?.close();\n\t\t\t\t\t\t\t\tclose();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (type === \"data\") {\n\t\t\t\t\t\t\tevent_id = _data.event_id as string;\n\t\t\t\t\t\t\tlet [_, status] = await post_data(`${config.root}/queue/data`, {\n\t\t\t\t\t\t\t\t...payload,\n\t\t\t\t\t\t\t\tsession_hash,\n\t\t\t\t\t\t\t\tevent_id\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tif (status !== 200) {\n\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\tstage: \"error\",\n\t\t\t\t\t\t\t\t\tmessage: BROKEN_CONNECTION_MSG,\n\t\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tstream?.close();\n\t\t\t\t\t\t\t\tclose();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (type === \"complete\") {\n\t\t\t\t\t\t\tcomplete = status;\n\t\t\t\t\t\t} else if (type === \"log\") {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"log\",\n\t\t\t\t\t\t\t\tlog: data.log,\n\t\t\t\t\t\t\t\tlevel: data.level,\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tduration: data.duration,\n\t\t\t\t\t\t\t\tvisible: data.visible,\n\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (type === \"generating\") {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t...status,\n\t\t\t\t\t\t\t\tstage: status?.stage!,\n\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (data) {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"data\",\n\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\tdata: handle_payload(\n\t\t\t\t\t\t\t\t\tdata.data,\n\t\t\t\t\t\t\t\t\tdependency,\n\t\t\t\t\t\t\t\t\tconfig.components,\n\t\t\t\t\t\t\t\t\t\"output\",\n\t\t\t\t\t\t\t\t\toptions.with_null_state\n\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\tevent_data,\n\t\t\t\t\t\t\t\ttrigger_id\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tif (complete) {\n\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t\t...complete,\n\t\t\t\t\t\t\t\t\tstage: status?.stage!,\n\t\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tstream?.close();\n\t\t\t\t\t\t\t\tclose();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t} else if (\n\t\t\t\t\tprotocol == \"sse_v1\" ||\n\t\t\t\t\tprotocol == \"sse_v2\" ||\n\t\t\t\t\tprotocol == \"sse_v2.1\" ||\n\t\t\t\t\tprotocol == \"sse_v3\"\n\t\t\t\t) {\n\t\t\t\t\t// latest API format. v2 introduces sending diffs for intermediate outputs in generative functions, which makes payloads lighter.\n\t\t\t\t\t// v3 only closes the stream when the backend sends the close stream message.\n\t\t\t\t\tfire_event({\n\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\tstage: \"pending\",\n\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t});\n\t\t\t\t\tlet hostname = \"\";\n\t\t\t\t\tif (\n\t\t\t\t\t\ttypeof window !== \"undefined\" &&\n\t\t\t\t\t\ttypeof document !== \"undefined\"\n\t\t\t\t\t) {\n\t\t\t\t\t\thostname = window?.location?.hostname;\n\t\t\t\t\t}\n\n\t\t\t\t\tlet hfhubdev = \"dev.spaces.huggingface.tech\";\n\t\t\t\t\tconst origin = hostname.includes(\".dev.\")\n\t\t\t\t\t\t? `https://moon-${hostname.split(\".\")[1]}.${hfhubdev}`\n\t\t\t\t\t\t: `https://huggingface.co`;\n\n\t\t\t\t\tconst is_iframe =\n\t\t\t\t\t\ttypeof window !== \"undefined\" &&\n\t\t\t\t\t\ttypeof document !== \"undefined\" &&\n\t\t\t\t\t\twindow.parent != window;\n\t\t\t\t\tconst is_zerogpu_space = dependency.zerogpu && config.space_id;\n\t\t\t\t\tconst zerogpu_auth_promise =\n\t\t\t\t\t\tis_iframe && is_zerogpu_space\n\t\t\t\t\t\t\t? post_message<Headers>(\"zerogpu-headers\", origin)\n\t\t\t\t\t\t\t: Promise.resolve(null);\n\t\t\t\t\tconst post_data_promise = zerogpu_auth_promise.then((headers) => {\n\t\t\t\t\t\treturn post_data(\n\t\t\t\t\t\t\t`${config.root}/queue/join?${url_params}`,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t...payload,\n\t\t\t\t\t\t\t\tsession_hash\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\theaders\n\t\t\t\t\t\t);\n\t\t\t\t\t});\n\t\t\t\t\tpost_data_promise.then(async ([response, status]: any) => {\n\t\t\t\t\t\tif (status === 503) {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\tstage: \"error\",\n\t\t\t\t\t\t\t\tmessage: QUEUE_FULL_MSG,\n\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (status !== 200) {\n\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\tstage: \"error\",\n\t\t\t\t\t\t\t\tmessage: BROKEN_CONNECTION_MSG,\n\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tevent_id = response.event_id as string;\n\t\t\t\t\t\t\tlet callback = async function (_data: object): Promise<void> {\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tconst { type, status, data } = handle_message(\n\t\t\t\t\t\t\t\t\t\t_data,\n\t\t\t\t\t\t\t\t\t\tlast_status[fn_index]\n\t\t\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\t\t\tif (type == \"heartbeat\") {\n\t\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tif (type === \"update\" && status && !complete) {\n\t\t\t\t\t\t\t\t\t\t// call 'status' listeners\n\t\t\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t\t\t\t...status\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t} else if (type === \"complete\") {\n\t\t\t\t\t\t\t\t\t\tcomplete = status;\n\t\t\t\t\t\t\t\t\t} else if (type == \"unexpected_error\") {\n\t\t\t\t\t\t\t\t\t\tconsole.error(\"Unexpected error\", status?.message);\n\t\t\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\t\t\tstage: \"error\",\n\t\t\t\t\t\t\t\t\t\t\tmessage:\n\t\t\t\t\t\t\t\t\t\t\t\tstatus?.message || \"An Unexpected Error Occurred!\",\n\t\t\t\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t} else if (type === \"log\") {\n\t\t\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\t\t\ttype: \"log\",\n\t\t\t\t\t\t\t\t\t\t\tlog: data.log,\n\t\t\t\t\t\t\t\t\t\t\tlevel: data.level,\n\t\t\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\t\t\tduration: data.duration,\n\t\t\t\t\t\t\t\t\t\t\tvisible: data.visible,\n\t\t\t\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t\t} else if (type === \"generating\") {\n\t\t\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t\t\t\t...status,\n\t\t\t\t\t\t\t\t\t\t\tstage: status?.stage!,\n\t\t\t\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\t\t\tdata &&\n\t\t\t\t\t\t\t\t\t\t\t[\"sse_v2\", \"sse_v2.1\", \"sse_v3\"].includes(protocol)\n\t\t\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\t\t\tapply_diff_stream(pending_diff_streams, event_id!, data);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif (data) {\n\t\t\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\t\t\ttype: \"data\",\n\t\t\t\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t\t\t\tdata: handle_payload(\n\t\t\t\t\t\t\t\t\t\t\t\tdata.data,\n\t\t\t\t\t\t\t\t\t\t\t\tdependency,\n\t\t\t\t\t\t\t\t\t\t\t\tconfig.components,\n\t\t\t\t\t\t\t\t\t\t\t\t\"output\",\n\t\t\t\t\t\t\t\t\t\t\t\toptions.with_null_state\n\t\t\t\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\tif (data.render_config) {\n\t\t\t\t\t\t\t\t\t\t\tawait handle_render_config(data.render_config);\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tif (complete) {\n\t\t\t\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\t\t\t\ttime: new Date(),\n\t\t\t\t\t\t\t\t\t\t\t\t...complete,\n\t\t\t\t\t\t\t\t\t\t\t\tstage: status?.stage!,\n\t\t\t\t\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\t\t\t\tfn_index\n\t\t\t\t\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t\t\t\t\tclose();\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\t\tstatus?.stage === \"complete\" ||\n\t\t\t\t\t\t\t\t\t\tstatus?.stage === \"error\"\n\t\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\t\tif (event_callbacks[event_id!]) {\n\t\t\t\t\t\t\t\t\t\t\tdelete event_callbacks[event_id!];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif (event_id! in pending_diff_streams) {\n\t\t\t\t\t\t\t\t\t\t\tdelete pending_diff_streams[event_id!];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\tconsole.error(\"Unexpected client exception\", e);\n\t\t\t\t\t\t\t\t\tfire_event({\n\t\t\t\t\t\t\t\t\t\ttype: \"status\",\n\t\t\t\t\t\t\t\t\t\tstage: \"error\",\n\t\t\t\t\t\t\t\t\t\tmessage: \"An Unexpected Error Occurred!\",\n\t\t\t\t\t\t\t\t\t\tqueue: true,\n\t\t\t\t\t\t\t\t\t\tendpoint: _endpoint,\n\t\t\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\t\ttime: new Date()\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tif ([\"sse_v2\", \"sse_v2.1\", \"sse_v3\"].includes(protocol)) {\n\t\t\t\t\t\t\t\t\t\tclose_stream(stream_status, that.abort_controller);\n\t\t\t\t\t\t\t\t\t\tstream_status.open = false;\n\t\t\t\t\t\t\t\t\t\tclose();\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t};\n\n\t\t\t\t\t\t\tif (event_id in pending_stream_messages) {\n\t\t\t\t\t\t\t\tpending_stream_messages[event_id].forEach((msg) =>\n\t\t\t\t\t\t\t\t\tcallback(msg)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\tdelete pending_stream_messages[event_id];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\tevent_callbacks[event_id] = callback;\n\t\t\t\t\t\t\tunclosed_events.add(event_id);\n\t\t\t\t\t\t\tif (!stream_status.open) {\n\t\t\t\t\t\t\t\tawait this.open_stream();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\n\t\tlet done = false;\n\t\tconst values: (IteratorResult<GradioEvent> | PromiseLike<never>)[] = [];\n\t\tconst resolvers: ((\n\t\t\tvalue: IteratorResult<GradioEvent> | PromiseLike<never>\n\t\t) => void)[] = [];\n\n\t\tfunction close(): void {\n\t\t\tdone = true;\n\t\t\twhile (resolvers.length > 0)\n\t\t\t\t(resolvers.shift() as (typeof resolvers)[0])({\n\t\t\t\t\tvalue: undefined,\n\t\t\t\t\tdone: true\n\t\t\t\t});\n\t\t}\n\n\t\tfunction push(\n\t\t\tdata: { value: GradioEvent; done: boolean } | PromiseLike<never>\n\t\t): void {\n\t\t\tif (done) return;\n\t\t\tif (resolvers.length > 0) {\n\t\t\t\t(resolvers.shift() as (typeof resolvers)[0])(data);\n\t\t\t} else {\n\t\t\t\tvalues.push(data);\n\t\t\t}\n\t\t}\n\n\t\tfunction push_error(error: unknown): void {\n\t\t\tpush(thenable_reject(error));\n\t\t\tclose();\n\t\t}\n\n\t\tfunction push_event(event: GradioEvent): void {\n\t\t\tpush({ value: event, done: false });\n\t\t}\n\n\t\tfunction next(): Promise<IteratorResult<GradioEvent, unknown>> {\n\t\t\tif (values.length > 0)\n\t\t\t\treturn Promise.resolve(values.shift() as (typeof values)[0]);\n\t\t\tif (done) return Promise.resolve({ value: undefined, done: true });\n\t\t\treturn new Promise((resolve) => resolvers.push(resolve));\n\t\t}\n\n\t\tconst iterator = {\n\t\t\t[Symbol.asyncIterator]: () => iterator,\n\t\t\tnext,\n\t\t\tthrow: async (value: unknown) => {\n\t\t\t\tpush_error(value);\n\t\t\t\treturn next();\n\t\t\t},\n\t\t\treturn: async () => {\n\t\t\t\tclose();\n\t\t\t\treturn next();\n\t\t\t},\n\t\t\tcancel\n\t\t};\n\n\t\treturn iterator;\n\t} catch (error) {\n\t\tconsole.error(\"Submit function encountered an error:\", error);\n\t\tthrow error;\n\t}\n}\n\nfunction thenable_reject<T>(error: T): PromiseLike<never> {\n\treturn {\n\t\tthen: (\n\t\t\tresolve: (value: never) => PromiseLike<never>,\n\t\t\treject: (error: T) => PromiseLike<never>\n\t\t) => reject(error)\n\t};\n}\n\nfunction get_endpoint_info(\n\tapi_info: ApiInfo<JsApiData>,\n\tendpoint: string | number,\n\tapi_map: Record<string, number>,\n\tconfig: Config\n): {\n\tfn_index: number;\n\tendpoint_info: EndpointInfo<JsApiData>;\n\tdependency: Dependency;\n} {\n\tlet fn_index: number;\n\tlet endpoint_info: EndpointInfo<JsApiData>;\n\tlet dependency: Dependency;\n\n\tif (typeof endpoint === \"number\") {\n\t\tfn_index = endpoint;\n\t\tendpoint_info = api_info.unnamed_endpoints[fn_index];\n\t\tdependency = config.dependencies.find((dep) => dep.id == endpoint)!;\n\t} else {\n\t\tconst trimmed_endpoint = endpoint.replace(/^\\//, \"\");\n\n\t\tfn_index = api_map[trimmed_endpoint];\n\t\tendpoint_info = api_info.named_endpoints[endpoint.trim()];\n\t\tdependency = config.dependencies.find(\n\t\t\t(dep) => dep.id == api_map[trimmed_endpoint]\n\t\t)!;\n\t}\n\n\tif (typeof fn_index !== \"number\") {\n\t\tthrow new Error(\n\t\t\t\"There is no endpoint matching that name of fn_index matching that number.\"\n\t\t);\n\t}\n\treturn { fn_index, endpoint_info, dependency };\n}\n", "import type {\n\tApiData,\n\tApiInfo,\n\tClientOptions,\n\tConfig,\n\tDuplicateOptions,\n\tEndpointInfo,\n\tJsApiData,\n\tPredictReturn,\n\tSpaceStatus,\n\tStatus,\n\tUploadResponse,\n\tclient_return,\n\tSubmitIterable,\n\tGradioEvent\n} from \"./types\";\nimport { view_api } from \"./utils/view_api\";\nimport { upload_files } from \"./utils/upload_files\";\nimport { upload, FileData } from \"./upload\";\nimport { handle_blob } from \"./utils/handle_blob\";\nimport { post_data } from \"./utils/post_data\";\nimport { predict } from \"./utils/predict\";\nimport { duplicate } from \"./utils/duplicate\";\nimport { submit } from \"./utils/submit\";\nimport { RE_SPACE_NAME, process_endpoint } from \"./helpers/api_info\";\nimport {\n\tmap_names_to_ids,\n\tresolve_cookies,\n\tresolve_config,\n\tget_jwt,\n\tparse_and_set_cookies\n} from \"./helpers/init_helpers\";\nimport { check_and_wake_space, check_space_status } from \"./helpers/spaces\";\nimport { open_stream, readable_stream, close_stream } from \"./utils/stream\";\nimport { API_INFO_ERROR_MSG, CONFIG_ERROR_MSG } from \"./constants\";\n\nexport class Client {\n\tapp_reference: string;\n\toptions: ClientOptions;\n\n\tconfig: Config | undefined;\n\tapi_info: ApiInfo<JsApiData> | undefined;\n\tapi_map: Record<string, number> = {};\n\tsession_hash: string = Math.random().toString(36).substring(2);\n\tjwt: string | false = false;\n\tlast_status: Record<string, Status[\"stage\"]> = {};\n\n\tprivate cookies: string | null = null;\n\n\t// streaming\n\tstream_status = { open: false };\n\tpending_stream_messages: Record<string, any[][]> = {};\n\tpending_diff_streams: Record<string, any[][]> = {};\n\tevent_callbacks: Record<string, (data?: unknown) => Promise<void>> = {};\n\tunclosed_events: Set<string> = new Set();\n\theartbeat_event: EventSource | null = null;\n\tabort_controller: AbortController | null = null;\n\tstream_instance: EventSource | null = null;\n\n\tfetch(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {\n\t\tconst headers = new Headers(init?.headers || {});\n\t\tif (this && this.cookies) {\n\t\t\theaders.append(\"Cookie\", this.cookies);\n\t\t}\n\n\t\treturn fetch(input, { ...init, headers });\n\t}\n\n\tstream(url: URL): EventSource {\n\t\tconst headers = new Headers();\n\t\tif (this && this.cookies) {\n\t\t\theaders.append(\"Cookie\", this.cookies);\n\t\t}\n\n\t\tthis.abort_controller = new AbortController();\n\n\t\tthis.stream_instance = readable_stream(url.toString(), {\n\t\t\tcredentials: \"include\",\n\t\t\theaders: headers,\n\t\t\tsignal: this.abort_controller.signal\n\t\t});\n\n\t\treturn this.stream_instance;\n\t}\n\n\tview_api: () => Promise<ApiInfo<JsApiData>>;\n\tupload_files: (\n\t\troot_url: string,\n\t\tfiles: (Blob | File)[],\n\t\tupload_id?: string\n\t) => Promise<UploadResponse>;\n\tupload: (\n\t\tfile_data: FileData[],\n\t\troot_url: string,\n\t\tupload_id?: string,\n\t\tmax_file_size?: number\n\t) => Promise<(FileData | null)[] | null>;\n\thandle_blob: (\n\t\tendpoint: string,\n\t\tdata: unknown[],\n\t\tendpoint_info: EndpointInfo<ApiData | JsApiData>\n\t) => Promise<unknown[]>;\n\tpost_data: (\n\t\turl: string,\n\t\tbody: unknown,\n\t\tadditional_headers?: any\n\t) => Promise<unknown[]>;\n\tsubmit: (\n\t\tendpoint: string | number,\n\t\tdata: unknown[] | Record<string, unknown> | undefined,\n\t\tevent_data?: unknown,\n\t\ttrigger_id?: number | null,\n\t\tall_events?: boolean\n\t) => SubmitIterable<GradioEvent>;\n\tpredict: (\n\t\tendpoint: string | number,\n\t\tdata: unknown[] | Record<string, unknown> | undefined,\n\t\tevent_data?: unknown\n\t) => Promise<PredictReturn>;\n\topen_stream: () => Promise<void>;\n\tprivate resolve_config: (endpoint: string) => Promise<Config | undefined>;\n\tprivate resolve_cookies: () => Promise<void>;\n\tconstructor(\n\t\tapp_reference: string,\n\t\toptions: ClientOptions = { events: [\"data\"] }\n\t) {\n\t\tthis.app_reference = app_reference;\n\t\tif (!options.events) {\n\t\t\toptions.events = [\"data\"];\n\t\t}\n\n\t\tthis.options = options;\n\n\t\tthis.view_api = view_api.bind(this);\n\t\tthis.upload_files = upload_files.bind(this);\n\t\tthis.handle_blob = handle_blob.bind(this);\n\t\tthis.post_data = post_data.bind(this);\n\t\tthis.submit = submit.bind(this);\n\t\tthis.predict = predict.bind(this);\n\t\tthis.open_stream = open_stream.bind(this);\n\t\tthis.resolve_config = resolve_config.bind(this);\n\t\tthis.resolve_cookies = resolve_cookies.bind(this);\n\t\tthis.upload = upload.bind(this);\n\t\tthis.fetch = this.fetch.bind(this);\n\t\tthis.handle_space_success = this.handle_space_success.bind(this);\n\t\tthis.stream = this.stream.bind(this);\n\t}\n\n\tprivate async init(): Promise<void> {\n\t\tif (\n\t\t\t(typeof window === \"undefined\" || !(\"WebSocket\" in window)) &&\n\t\t\t!global.WebSocket\n\t\t) {\n\t\t\tconst ws = await import(\"ws\");\n\t\t\tglobal.WebSocket = ws.WebSocket as unknown as typeof WebSocket;\n\t\t}\n\n\t\ttry {\n\t\t\tif (this.options.auth) {\n\t\t\t\tawait this.resolve_cookies();\n\t\t\t}\n\n\t\t\tawait this._resolve_config().then(({ config }) =>\n\t\t\t\tthis._resolve_hearbeat(config)\n\t\t\t);\n\t\t} catch (e: any) {\n\t\t\tthrow Error(e);\n\t\t}\n\n\t\tthis.api_info = await this.view_api();\n\t\tthis.api_map = map_names_to_ids(this.config?.dependencies || []);\n\t}\n\n\tasync _resolve_hearbeat(_config: Config): Promise<void> {\n\t\tif (_config) {\n\t\t\tthis.config = _config;\n\t\t\tif (this.config && this.config.connect_heartbeat) {\n\t\t\t\tif (this.config.space_id && this.options.hf_token) {\n\t\t\t\t\tthis.jwt = await get_jwt(\n\t\t\t\t\t\tthis.config.space_id,\n\t\t\t\t\t\tthis.options.hf_token,\n\t\t\t\t\t\tthis.cookies\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (_config.space_id && this.options.hf_token) {\n\t\t\tthis.jwt = await get_jwt(_config.space_id, this.options.hf_token);\n\t\t}\n\n\t\tif (this.config && this.config.connect_heartbeat) {\n\t\t\t// connect to the heartbeat endpoint via GET request\n\t\t\tconst heartbeat_url = new URL(\n\t\t\t\t`${this.config.root}/heartbeat/${this.session_hash}`\n\t\t\t);\n\n\t\t\t// if the jwt is available, add it to the query params\n\t\t\tif (this.jwt) {\n\t\t\t\theartbeat_url.searchParams.set(\"__sign\", this.jwt);\n\t\t\t}\n\n\t\t\t// Just connect to the endpoint without parsing the response. Ref: https://github.com/gradio-app/gradio/pull/7974#discussion_r1557717540\n\t\t\tif (!this.heartbeat_event) {\n\t\t\t\tthis.heartbeat_event = this.stream(heartbeat_url);\n\t\t\t}\n\t\t}\n\t}\n\n\tstatic async connect(\n\t\tapp_reference: string,\n\t\toptions: ClientOptions = {\n\t\t\tevents: [\"data\"]\n\t\t}\n\t): Promise<Client> {\n\t\tconst client = new this(app_reference, options); // this refers to the class itself, not the instance\n\t\tawait client.init();\n\t\treturn client;\n\t}\n\n\tclose(): void {\n\t\tclose_stream(this.stream_status, this.abort_controller);\n\t}\n\n\tstatic async duplicate(\n\t\tapp_reference: string,\n\t\toptions: DuplicateOptions = {\n\t\t\tevents: [\"data\"]\n\t\t}\n\t): Promise<Client> {\n\t\treturn duplicate(app_reference, options);\n\t}\n\n\tprivate async _resolve_config(): Promise<any> {\n\t\tconst { http_protocol, host, space_id } = await process_endpoint(\n\t\t\tthis.app_reference,\n\t\t\tthis.options.hf_token\n\t\t);\n\n\t\tconst { status_callback } = this.options;\n\n\t\tif (space_id && status_callback) {\n\t\t\tawait check_and_wake_space(space_id, status_callback);\n\t\t}\n\n\t\tlet config: Config | undefined;\n\n\t\ttry {\n\t\t\tconfig = await this.resolve_config(`${http_protocol}//${host}`);\n\n\t\t\tif (!config) {\n\t\t\t\tthrow new Error(CONFIG_ERROR_MSG);\n\t\t\t}\n\n\t\t\treturn this.config_success(config);\n\t\t} catch (e: any) {\n\t\t\tif (space_id && status_callback) {\n\t\t\t\tcheck_space_status(\n\t\t\t\t\tspace_id,\n\t\t\t\t\tRE_SPACE_NAME.test(space_id) ? \"space_name\" : \"subdomain\",\n\t\t\t\t\tthis.handle_space_success\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tif (status_callback)\n\t\t\t\t\tstatus_callback({\n\t\t\t\t\t\tstatus: \"error\",\n\t\t\t\t\t\tmessage: \"Could not load this space.\",\n\t\t\t\t\t\tload_status: \"error\",\n\t\t\t\t\t\tdetail: \"NOT_FOUND\"\n\t\t\t\t\t});\n\t\t\t\tthrow Error(e);\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate async config_success(\n\t\t_config: Config\n\t): Promise<Config | client_return> {\n\t\tthis.config = _config;\n\n\t\tif (typeof window !== \"undefined\" && typeof document !== \"undefined\") {\n\t\t\tif (window.location.protocol === \"https:\") {\n\t\t\t\tthis.config.root = this.config.root.replace(\"http://\", \"https://\");\n\t\t\t}\n\t\t}\n\n\t\tif (this.config.auth_required) {\n\t\t\treturn this.prepare_return_obj();\n\t\t}\n\n\t\ttry {\n\t\t\tthis.api_info = await this.view_api();\n\t\t} catch (e) {\n\t\t\tconsole.error(API_INFO_ERROR_MSG + (e as Error).message);\n\t\t}\n\n\t\treturn this.prepare_return_obj();\n\t}\n\n\tasync handle_space_success(status: SpaceStatus): Promise<Config | void> {\n\t\tif (!this) {\n\t\t\tthrow new Error(CONFIG_ERROR_MSG);\n\t\t}\n\t\tconst { status_callback } = this.options;\n\t\tif (status_callback) status_callback(status);\n\t\tif (status.status === \"running\") {\n\t\t\ttry {\n\t\t\t\tthis.config = await this._resolve_config();\n\t\t\t\tif (!this.config) {\n\t\t\t\t\tthrow new Error(CONFIG_ERROR_MSG);\n\t\t\t\t}\n\n\t\t\t\tconst _config = await this.config_success(this.config);\n\n\t\t\t\treturn _config as Config;\n\t\t\t} catch (e) {\n\t\t\t\tif (status_callback) {\n\t\t\t\t\tstatus_callback({\n\t\t\t\t\t\tstatus: \"error\",\n\t\t\t\t\t\tmessage: \"Could not load this space.\",\n\t\t\t\t\t\tload_status: \"error\",\n\t\t\t\t\t\tdetail: \"NOT_FOUND\"\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthrow e;\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic async component_server(\n\t\tcomponent_id: number,\n\t\tfn_name: string,\n\t\tdata: unknown[] | { binary: boolean; data: Record<string, any> }\n\t): Promise<unknown> {\n\t\tif (!this.config) {\n\t\t\tthrow new Error(CONFIG_ERROR_MSG);\n\t\t}\n\n\t\tconst headers: {\n\t\t\tAuthorization?: string;\n\t\t\t\"Content-Type\"?: \"application/json\";\n\t\t} = {};\n\n\t\tconst { hf_token } = this.options;\n\t\tconst { session_hash } = this;\n\n\t\tif (hf_token) {\n\t\t\theaders.Authorization = `Bearer ${this.options.hf_token}`;\n\t\t}\n\n\t\tlet root_url: string;\n\t\tlet component = this.config.components.find(\n\t\t\t(comp) => comp.id === component_id\n\t\t);\n\t\tif (component?.props?.root_url) {\n\t\t\troot_url = component.props.root_url;\n\t\t} else {\n\t\t\troot_url = this.config.root;\n\t\t}\n\n\t\tlet body: FormData | string;\n\n\t\tif (\"binary\" in data) {\n\t\t\tbody = new FormData();\n\t\t\tfor (const key in data.data) {\n\t\t\t\tif (key === \"binary\") continue;\n\t\t\t\tbody.append(key, data.data[key]);\n\t\t\t}\n\t\t\tbody.set(\"component_id\", component_id.toString());\n\t\t\tbody.set(\"fn_name\", fn_name);\n\t\t\tbody.set(\"session_hash\", session_hash);\n\t\t} else {\n\t\t\tbody = JSON.stringify({\n\t\t\t\tdata: data,\n\t\t\t\tcomponent_id,\n\t\t\t\tfn_name,\n\t\t\t\tsession_hash\n\t\t\t});\n\n\t\t\theaders[\"Content-Type\"] = \"application/json\";\n\t\t}\n\n\t\tif (hf_token) {\n\t\t\theaders.Authorization = `Bearer ${hf_token}`;\n\t\t}\n\n\t\ttry {\n\t\t\tconst response = await this.fetch(`${root_url}/component_server/`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: body,\n\t\t\t\theaders,\n\t\t\t\tcredentials: \"include\"\n\t\t\t});\n\n\t\t\tif (!response.ok) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t\"Could not connect to component server: \" + response.statusText\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tconst output = await response.json();\n\t\t\treturn output;\n\t\t} catch (e) {\n\t\t\tconsole.warn(e);\n\t\t}\n\t}\n\n\tpublic set_cookies(raw_cookies: string): void {\n\t\tthis.cookies = parse_and_set_cookies(raw_cookies).join(\"; \");\n\t}\n\n\tprivate prepare_return_obj(): client_return {\n\t\treturn {\n\t\t\tconfig: this.config,\n\t\t\tpredict: this.predict,\n\t\t\tsubmit: this.submit,\n\t\t\tview_api: this.view_api,\n\t\t\tcomponent_server: this.component_server\n\t\t};\n\t}\n}\n\n/**\n * @deprecated This method will be removed in v1.0. Use `Client.connect()` instead.\n * Creates a client instance for interacting with Gradio apps.\n *\n * @param {string} app_reference - The reference or URL to a Gradio space or app.\n * @param {ClientOptions} options - Configuration options for the client.\n * @returns {Promise<Client>} A promise that resolves to a `Client` instance.\n */\nexport async function client(\n\tapp_reference: string,\n\toptions: ClientOptions = {\n\t\tevents: [\"data\"]\n\t}\n): Promise<Client> {\n\treturn await Client.connect(app_reference, options);\n}\n\n/**\n * @deprecated This method will be removed in v1.0. Use `Client.duplicate()` instead.\n * Creates a duplicate of a space and returns a client instance for the duplicated space.\n *\n * @param {string} app_reference - The reference or URL to a Gradio space or app to duplicate.\n * @param {DuplicateOptions} options - Configuration options for the client.\n * @returns {Promise<Client>} A promise that resolves to a `Client` instance.\n */\nexport async function duplicate_space(\n\tapp_reference: string,\n\toptions: DuplicateOptions\n): Promise<Client> {\n\treturn await Client.duplicate(app_reference, options);\n}\n\nexport type ClientInstance = Client;\n", "/** @returns {void} */\nexport function noop() {}\n\nexport const identity = (x) => x;\n\n/**\n * @template T\n * @template S\n * @param {T} tar\n * @param {S} src\n * @returns {T & S}\n */\nexport function assign(tar, src) {\n\t// @ts-ignore\n\tfor (const k in src) tar[k] = src[k];\n\treturn /** @type {T & S} */ (tar);\n}\n\n// Adapted from https://github.com/then/is-promise/blob/master/index.js\n// Distributed under MIT License https://github.com/then/is-promise/blob/master/LICENSE\n/**\n * @param {any} value\n * @returns {value is PromiseLike<any>}\n */\nexport function is_promise(value) {\n\treturn (\n\t\t!!value &&\n\t\t(typeof value === 'object' || typeof value === 'function') &&\n\t\ttypeof (/** @type {any} */ (value).then) === 'function'\n\t);\n}\n\n/** @returns {void} */\nexport function add_location(element, file, line, column, char) {\n\telement.__svelte_meta = {\n\t\tloc: { file, line, column, char }\n\t};\n}\n\nexport function run(fn) {\n\treturn fn();\n}\n\nexport function blank_object() {\n\treturn Object.create(null);\n}\n\n/**\n * @param {Function[]} fns\n * @returns {void}\n */\nexport function run_all(fns) {\n\tfns.forEach(run);\n}\n\n/**\n * @param {any} thing\n * @returns {thing is Function}\n */\nexport function is_function(thing) {\n\treturn typeof thing === 'function';\n}\n\n/** @returns {boolean} */\nexport function safe_not_equal(a, b) {\n\treturn a != a ? b == b : a !== b || (a && typeof a === 'object') || typeof a === 'function';\n}\n\nlet src_url_equal_anchor;\n\n/**\n * @param {string} element_src\n * @param {string} url\n * @returns {boolean}\n */\nexport function src_url_equal(element_src, url) {\n\tif (element_src === url) return true;\n\tif (!src_url_equal_anchor) {\n\t\tsrc_url_equal_anchor = document.createElement('a');\n\t}\n\t// This is actually faster than doing URL(..).href\n\tsrc_url_equal_anchor.href = url;\n\treturn element_src === src_url_equal_anchor.href;\n}\n\n/** @param {string} srcset */\nfunction split_srcset(srcset) {\n\treturn srcset.split(',').map((src) => src.trim().split(' ').filter(Boolean));\n}\n\n/**\n * @param {HTMLSourceElement | HTMLImageElement} element_srcset\n * @param {string | undefined | null} srcset\n * @returns {boolean}\n */\nexport function srcset_url_equal(element_srcset, srcset) {\n\tconst element_urls = split_srcset(element_srcset.srcset);\n\tconst urls = split_srcset(srcset || '');\n\n\treturn (\n\t\turls.length === element_urls.length &&\n\t\turls.every(\n\t\t\t([url, width], i) =>\n\t\t\t\twidth === element_urls[i][1] &&\n\t\t\t\t// We need to test both ways because Vite will create an a full URL with\n\t\t\t\t// `new URL(asset, import.meta.url).href` for the client when `base: './'`, and the\n\t\t\t\t// relative URLs inside srcset are not automatically resolved to absolute URLs by\n\t\t\t\t// browsers (in contrast to img.src). This means both SSR and DOM code could\n\t\t\t\t// contain relative or absolute URLs.\n\t\t\t\t(src_url_equal(element_urls[i][0], url) || src_url_equal(url, element_urls[i][0]))\n\t\t)\n\t);\n}\n\n/** @returns {boolean} */\nexport function not_equal(a, b) {\n\treturn a != a ? b == b : a !== b;\n}\n\n/** @returns {boolean} */\nexport function is_empty(obj) {\n\treturn Object.keys(obj).length === 0;\n}\n\n/** @returns {void} */\nexport function validate_store(store, name) {\n\tif (store != null && typeof store.subscribe !== 'function') {\n\t\tthrow new Error(`'${name}' is not a store with a 'subscribe' method`);\n\t}\n}\n\nexport function subscribe(store, ...callbacks) {\n\tif (store == null) {\n\t\tfor (const callback of callbacks) {\n\t\t\tcallback(undefined);\n\t\t}\n\t\treturn noop;\n\t}\n\tconst unsub = store.subscribe(...callbacks);\n\treturn unsub.unsubscribe ? () => unsub.unsubscribe() : unsub;\n}\n\n/**\n * Get the current value from a store by subscribing and immediately unsubscribing.\n *\n * https://svelte.dev/docs/svelte-store#get\n * @template T\n * @param {import('../store/public.js').Readable<T>} store\n * @returns {T}\n */\nexport function get_store_value(store) {\n\tlet value;\n\tsubscribe(store, (_) => (value = _))();\n\treturn value;\n}\n\n/** @returns {void} */\nexport function component_subscribe(component, store, callback) {\n\tcomponent.$$.on_destroy.push(subscribe(store, callback));\n}\n\nexport function create_slot(definition, ctx, $$scope, fn) {\n\tif (definition) {\n\t\tconst slot_ctx = get_slot_context(definition, ctx, $$scope, fn);\n\t\treturn definition[0](slot_ctx);\n\t}\n}\n\nfunction get_slot_context(definition, ctx, $$scope, fn) {\n\treturn definition[1] && fn ? assign($$scope.ctx.slice(), definition[1](fn(ctx))) : $$scope.ctx;\n}\n\nexport function get_slot_changes(definition, $$scope, dirty, fn) {\n\tif (definition[2] && fn) {\n\t\tconst lets = definition[2](fn(dirty));\n\t\tif ($$scope.dirty === undefined) {\n\t\t\treturn lets;\n\t\t}\n\t\tif (typeof lets === 'object') {\n\t\t\tconst merged = [];\n\t\t\tconst len = Math.max($$scope.dirty.length, lets.length);\n\t\t\tfor (let i = 0; i < len; i += 1) {\n\t\t\t\tmerged[i] = $$scope.dirty[i] | lets[i];\n\t\t\t}\n\t\t\treturn merged;\n\t\t}\n\t\treturn $$scope.dirty | lets;\n\t}\n\treturn $$scope.dirty;\n}\n\n/** @returns {void} */\nexport function update_slot_base(\n\tslot,\n\tslot_definition,\n\tctx,\n\t$$scope,\n\tslot_changes,\n\tget_slot_context_fn\n) {\n\tif (slot_changes) {\n\t\tconst slot_context = get_slot_context(slot_definition, ctx, $$scope, get_slot_context_fn);\n\t\tslot.p(slot_context, slot_changes);\n\t}\n}\n\n/** @returns {void} */\nexport function update_slot(\n\tslot,\n\tslot_definition,\n\tctx,\n\t$$scope,\n\tdirty,\n\tget_slot_changes_fn,\n\tget_slot_context_fn\n) {\n\tconst slot_changes = get_slot_changes(slot_definition, $$scope, dirty, get_slot_changes_fn);\n\tupdate_slot_base(slot, slot_definition, ctx, $$scope, slot_changes, get_slot_context_fn);\n}\n\n/** @returns {any[] | -1} */\nexport function get_all_dirty_from_scope($$scope) {\n\tif ($$scope.ctx.length > 32) {\n\t\tconst dirty = [];\n\t\tconst length = $$scope.ctx.length / 32;\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tdirty[i] = -1;\n\t\t}\n\t\treturn dirty;\n\t}\n\treturn -1;\n}\n\n/** @returns {{}} */\nexport function exclude_internal_props(props) {\n\tconst result = {};\n\tfor (const k in props) if (k[0] !== '$') result[k] = props[k];\n\treturn result;\n}\n\n/** @returns {{}} */\nexport function compute_rest_props(props, keys) {\n\tconst rest = {};\n\tkeys = new Set(keys);\n\tfor (const k in props) if (!keys.has(k) && k[0] !== '$') rest[k] = props[k];\n\treturn rest;\n}\n\n/** @returns {{}} */\nexport function compute_slots(slots) {\n\tconst result = {};\n\tfor (const key in slots) {\n\t\tresult[key] = true;\n\t}\n\treturn result;\n}\n\n/** @returns {(this: any, ...args: any[]) => void} */\nexport function once(fn) {\n\tlet ran = false;\n\treturn function (...args) {\n\t\tif (ran) return;\n\t\tran = true;\n\t\tfn.call(this, ...args);\n\t};\n}\n\nexport function null_to_empty(value) {\n\treturn value == null ? '' : value;\n}\n\nexport function set_store_value(store, ret, value) {\n\tstore.set(value);\n\treturn ret;\n}\n\nexport const has_prop = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);\n\nexport function action_destroyer(action_result) {\n\treturn action_result && is_function(action_result.destroy) ? action_result.destroy : noop;\n}\n\n/** @param {number | string} value\n * @returns {[number, string]}\n */\nexport function split_css_unit(value) {\n\tconst split = typeof value === 'string' && value.match(/^\\s*(-?[\\d.]+)([^\\s]*)\\s*$/);\n\treturn split ? [parseFloat(split[1]), split[2] || 'px'] : [/** @type {number} */ (value), 'px'];\n}\n\nexport const contenteditable_truthy_values = ['', true, 1, 'true', 'contenteditable'];\n", "import {\n\trun_all,\n\tsubscribe,\n\tnoop,\n\tsafe_not_equal,\n\tis_function,\n\tget_store_value\n} from '../internal/index.js';\n\nconst subscriber_queue = [];\n\n/**\n * Creates a `Readable` store that allows reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#readable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readable(value, start) {\n\treturn {\n\t\tsubscribe: writable(value, start).subscribe\n\t};\n}\n\n/**\n * Create a `Writable` store that allows both updating and reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#writable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Writable<T>}\n */\nexport function writable(value, start = noop) {\n\t/** @type {import('./public.js').Unsubscriber} */\n\tlet stop;\n\t/** @type {Set<import('./private.js').SubscribeInvalidateTuple<T>>} */\n\tconst subscribers = new Set();\n\t/** @param {T} new_value\n\t * @returns {void}\n\t */\n\tfunction set(new_value) {\n\t\tif (safe_not_equal(value, new_value)) {\n\t\t\tvalue = new_value;\n\t\t\tif (stop) {\n\t\t\t\t// store is ready\n\t\t\t\tconst run_queue = !subscriber_queue.length;\n\t\t\t\tfor (const subscriber of subscribers) {\n\t\t\t\t\tsubscriber[1]();\n\t\t\t\t\tsubscriber_queue.push(subscriber, value);\n\t\t\t\t}\n\t\t\t\tif (run_queue) {\n\t\t\t\t\tfor (let i = 0; i < subscriber_queue.length; i += 2) {\n\t\t\t\t\t\tsubscriber_queue[i][0](subscriber_queue[i + 1]);\n\t\t\t\t\t}\n\t\t\t\t\tsubscriber_queue.length = 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @param {import('./public.js').Updater<T>} fn\n\t * @returns {void}\n\t */\n\tfunction update(fn) {\n\t\tset(fn(value));\n\t}\n\n\t/**\n\t * @param {import('./public.js').Subscriber<T>} run\n\t * @param {import('./private.js').Invalidator<T>} [invalidate]\n\t * @returns {import('./public.js').Unsubscriber}\n\t */\n\tfunction subscribe(run, invalidate = noop) {\n\t\t/** @type {import('./private.js').SubscribeInvalidateTuple<T>} */\n\t\tconst subscriber = [run, invalidate];\n\t\tsubscribers.add(subscriber);\n\t\tif (subscribers.size === 1) {\n\t\t\tstop = start(set, update) || noop;\n\t\t}\n\t\trun(value);\n\t\treturn () => {\n\t\t\tsubscribers.delete(subscriber);\n\t\t\tif (subscribers.size === 0 && stop) {\n\t\t\t\tstop();\n\t\t\t\tstop = null;\n\t\t\t}\n\t\t};\n\t}\n\treturn { set, update, subscribe };\n}\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>, set: (value: T) => void, update: (fn: import('./public.js').Updater<T>) => void) => import('./public.js').Unsubscriber | void} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>) => T} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * @template {import('./private.js').Stores} S\n * @template T\n * @param {S} stores\n * @param {Function} fn\n * @param {T} [initial_value]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function derived(stores, fn, initial_value) {\n\tconst single = !Array.isArray(stores);\n\t/** @type {Array<import('./public.js').Readable<any>>} */\n\tconst stores_array = single ? [stores] : stores;\n\tif (!stores_array.every(Boolean)) {\n\t\tthrow new Error('derived() expects stores as input, got a falsy value');\n\t}\n\tconst auto = fn.length < 2;\n\treturn readable(initial_value, (set, update) => {\n\t\tlet started = false;\n\t\tconst values = [];\n\t\tlet pending = 0;\n\t\tlet cleanup = noop;\n\t\tconst sync = () => {\n\t\t\tif (pending) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tcleanup();\n\t\t\tconst result = fn(single ? values[0] : values, set, update);\n\t\t\tif (auto) {\n\t\t\t\tset(result);\n\t\t\t} else {\n\t\t\t\tcleanup = is_function(result) ? result : noop;\n\t\t\t}\n\t\t};\n\t\tconst unsubscribers = stores_array.map((store, i) =>\n\t\t\tsubscribe(\n\t\t\t\tstore,\n\t\t\t\t(value) => {\n\t\t\t\t\tvalues[i] = value;\n\t\t\t\t\tpending &= ~(1 << i);\n\t\t\t\t\tif (started) {\n\t\t\t\t\t\tsync();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t() => {\n\t\t\t\t\tpending |= 1 << i;\n\t\t\t\t}\n\t\t\t)\n\t\t);\n\t\tstarted = true;\n\t\tsync();\n\t\treturn function stop() {\n\t\t\trun_all(unsubscribers);\n\t\t\tcleanup();\n\t\t\t// We need to set this to false because callbacks can still happen despite having unsubscribed:\n\t\t\t// Callbacks might already be placed in the queue which doesn't know it should no longer\n\t\t\t// invoke this derived store.\n\t\t\tstarted = false;\n\t\t};\n\t});\n}\n\n/**\n * Takes a store and returns a new one derived from the old one that is readable.\n *\n * https://svelte.dev/docs/svelte-store#readonly\n * @template T\n * @param {import('./public.js').Readable<T>} store  - store to make readonly\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readonly(store) {\n\treturn {\n\t\tsubscribe: store.subscribe.bind(store)\n\t};\n}\n\nexport { get_store_value as get };\n", "'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n  return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose;\n    if (async) {\n        if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n        dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n        if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n        dispose = value[Symbol.dispose];\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  function next() {\n    while (env.stack.length) {\n      var rec = env.stack.pop();\n      try {\n        var result = rec.dispose && rec.dispose.call(rec.value);\n        if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n      }\n      catch (e) {\n          fail(e);\n      }\n    }\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n};\n", "export var ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n", "export var TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nexport var SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nexport function isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nexport function isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nexport function isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nexport function isDateElement(el) {\n    return el.type === TYPE.date;\n}\nexport function isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nexport function isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nexport function isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nexport function isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nexport function isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nexport function isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nexport function isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nexport function createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nexport function createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n", "// @generated from regex-gen.ts\nexport var SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n", "/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nexport function parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n", "// @generated from regex-gen.ts\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n", "import { __assign } from \"tslib\";\nimport { WHITE_SPACE_REGEX } from './regex.generated';\nexport function parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nexport function parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = __assign(__assign(__assign({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = __assign(__assign(__assign({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = __assign(__assign({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = __assign(__assign({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = __assign(__assign({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = __assign(__assign({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = __assign(__assign({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n", "// @generated from time-data-gen.ts\n// prettier-ignore  \nexport var timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BO\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-EC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-PE\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n", "import { timeData } from './time-data.generated';\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nexport function getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = timeData[regionTag || ''] ||\n        timeData[languageTag || ''] ||\n        timeData[\"\".concat(languageTag, \"-001\")] ||\n        timeData['001'];\n    return hourCycles[0];\n}\n", "var _a;\nimport { __assign } from \"tslib\";\nimport { <PERSON>rrorKind } from './error';\nimport { SKELETON_TYPE, TYPE, } from './types';\nimport { SPACE_SEPARATOR_REGEX } from './regex.generated';\nimport { parseNumberSkeleton, parseNumberSkeletonFromString, parseDateTimeSkeleton, } from '@formatjs/icu-skeleton-parser';\nimport { getBestPattern } from './date-time-pattern-generator';\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = getBestPattern(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? parseDateTimeSkeleton(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? TYPE.date : TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? TYPE.number\n                            : argType === 'date'\n                                ? TYPE.date\n                                : TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, __assign({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = parseNumberSkeletonFromString(skeleton);\n        }\n        catch (e) {\n            return this.error(ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? parseNumberSkeleton(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\nexport { Parser };\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n", "import { __assign } from \"tslib\";\nimport { ErrorKind } from './error';\nimport { Parser } from './parser';\nimport { isDateElement, isDateTimeSkeleton, isNumberElement, isNumberSkeleton, isPluralElement, isSelectElement, isTagElement, isTimeElement, } from './types';\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if (isSelectElement(el) || isPluralElement(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if (isNumberElement(el) && isNumberSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if ((isDateElement(el) || isTimeElement(el)) &&\n            isDateTimeSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if (isTagElement(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nexport function parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = __assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\nexport * from './types';\n// only for testing\nexport var _Parser = Parser;\n", "//\n// Main\n//\nexport function memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nfunction ObjectWithoutPrototypeCache() {\n    this.cache = Object.create(null);\n}\nObjectWithoutPrototypeCache.prototype.get = function (key) {\n    return this.cache[key];\n};\nObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n    this.cache[key] = value;\n};\nvar cacheDefault = {\n    create: function create() {\n        // @ts-ignore\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nexport var strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n", "import { __extends } from \"tslib\";\nexport var ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    __extends(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\nexport { FormatError };\nvar InvalidValueError = /** @class */ (function (_super) {\n    __extends(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\nexport { InvalidValueError };\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    __extends(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\nexport { InvalidValueTypeError };\nvar MissingValueError = /** @class */ (function (_super) {\n    __extends(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\nexport { MissingValueError };\n", "import { isArgumentElement, isDateElement, isDateTimeSkeleton, isLiteralElement, isNumberElement, isNumberSkeleton, isPluralElement, isPoundElement, isSelectElement, isTimeElement, isTagElement, } from '@formatjs/icu-messageformat-parser';\nimport { MissingValueError, InvalidValueError, ErrorCode, FormatError, InvalidValueTypeError, } from './error';\nexport var PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nexport function isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nexport function formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && isLiteralElement(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if (isLiteralElement(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if (isPoundElement(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if (isArgumentElement(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if (isDateElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTimeElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isNumberElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : isNumberSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTagElement(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if (isSelectElement(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if (isPluralElement(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n", "/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nimport { __assign, __rest, __spreadArray } from \"tslib\";\nimport { parse, } from '@formatjs/icu-messageformat-parser';\nimport { memoize, strategies } from '@formatjs/fast-memoize';\nimport { formatToParts, PART_TYPE, } from './formatters';\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return __assign(__assign(__assign({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = __assign(__assign({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, __assign({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: strategies.variadic,\n        }),\n        getDateTimeFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: strategies.variadic,\n        }),\n        getPluralRules: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        var _this = this;\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return formatToParts(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = __rest(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, __assign(__assign({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\nexport { IntlMessageFormat };\n", "import { writable, derived } from 'svelte/store';\nimport deepmerge from 'deepmerge';\nimport { IntlMessageFormat } from 'intl-messageformat';\n\nfunction delve(obj, fullKey) {\n  if (fullKey == null)\n    return void 0;\n  if (fullKey in obj) {\n    return obj[fullKey];\n  }\n  const keys = fullKey.split(\".\");\n  let result = obj;\n  for (let p = 0; p < keys.length; p++) {\n    if (typeof result === \"object\") {\n      if (p > 0) {\n        const partialKey = keys.slice(p, keys.length).join(\".\");\n        if (partialKey in result) {\n          result = result[partialKey];\n          break;\n        }\n      }\n      result = result[keys[p]];\n    } else {\n      result = void 0;\n    }\n  }\n  return result;\n}\n\nconst lookupCache = {};\nconst addToCache = (path, locale, message) => {\n  if (!message)\n    return message;\n  if (!(locale in lookupCache))\n    lookupCache[locale] = {};\n  if (!(path in lookupCache[locale]))\n    lookupCache[locale][path] = message;\n  return message;\n};\nconst lookup = (path, refLocale) => {\n  if (refLocale == null)\n    return void 0;\n  if (refLocale in lookupCache && path in lookupCache[refLocale]) {\n    return lookupCache[refLocale][path];\n  }\n  const locales = getPossibleLocales(refLocale);\n  for (let i = 0; i < locales.length; i++) {\n    const locale = locales[i];\n    const message = getMessageFromDictionary(locale, path);\n    if (message) {\n      return addToCache(path, refLocale, message);\n    }\n  }\n  return void 0;\n};\n\nlet dictionary;\nconst $dictionary = writable({});\nfunction getLocaleDictionary(locale) {\n  return dictionary[locale] || null;\n}\nfunction hasLocaleDictionary(locale) {\n  return locale in dictionary;\n}\nfunction getMessageFromDictionary(locale, id) {\n  if (!hasLocaleDictionary(locale)) {\n    return null;\n  }\n  const localeDictionary = getLocaleDictionary(locale);\n  const match = delve(localeDictionary, id);\n  return match;\n}\nfunction getClosestAvailableLocale(refLocale) {\n  if (refLocale == null)\n    return void 0;\n  const relatedLocales = getPossibleLocales(refLocale);\n  for (let i = 0; i < relatedLocales.length; i++) {\n    const locale = relatedLocales[i];\n    if (hasLocaleDictionary(locale)) {\n      return locale;\n    }\n  }\n  return void 0;\n}\nfunction addMessages(locale, ...partials) {\n  delete lookupCache[locale];\n  $dictionary.update((d) => {\n    d[locale] = deepmerge.all([d[locale] || {}, ...partials]);\n    return d;\n  });\n}\nconst $locales = derived(\n  [$dictionary],\n  ([dictionary2]) => Object.keys(dictionary2)\n);\n$dictionary.subscribe((newDictionary) => dictionary = newDictionary);\n\nconst queue = {};\nfunction createLocaleQueue(locale) {\n  queue[locale] = /* @__PURE__ */ new Set();\n}\nfunction removeLoaderFromQueue(locale, loader) {\n  queue[locale].delete(loader);\n  if (queue[locale].size === 0) {\n    delete queue[locale];\n  }\n}\nfunction getLocaleQueue(locale) {\n  return queue[locale];\n}\nfunction getLocalesQueues(locale) {\n  return getPossibleLocales(locale).map((localeItem) => {\n    const localeQueue = getLocaleQueue(localeItem);\n    return [localeItem, localeQueue ? [...localeQueue] : []];\n  }).filter(([, localeQueue]) => localeQueue.length > 0);\n}\nfunction hasLocaleQueue(locale) {\n  if (locale == null)\n    return false;\n  return getPossibleLocales(locale).some(\n    (localeQueue) => {\n      var _a;\n      return (_a = getLocaleQueue(localeQueue)) == null ? void 0 : _a.size;\n    }\n  );\n}\nfunction loadLocaleQueue(locale, localeQueue) {\n  const allLoadersPromise = Promise.all(\n    localeQueue.map((loader) => {\n      removeLoaderFromQueue(locale, loader);\n      return loader().then((partial) => partial.default || partial);\n    })\n  );\n  return allLoadersPromise.then((partials) => addMessages(locale, ...partials));\n}\nconst activeFlushes = {};\nfunction flush(locale) {\n  if (!hasLocaleQueue(locale)) {\n    if (locale in activeFlushes) {\n      return activeFlushes[locale];\n    }\n    return Promise.resolve();\n  }\n  const queues = getLocalesQueues(locale);\n  activeFlushes[locale] = Promise.all(\n    queues.map(\n      ([localeName, localeQueue]) => loadLocaleQueue(localeName, localeQueue)\n    )\n  ).then(() => {\n    if (hasLocaleQueue(locale)) {\n      return flush(locale);\n    }\n    delete activeFlushes[locale];\n  });\n  return activeFlushes[locale];\n}\nfunction registerLocaleLoader(locale, loader) {\n  if (!getLocaleQueue(locale))\n    createLocaleQueue(locale);\n  const localeQueue = getLocaleQueue(locale);\n  if (getLocaleQueue(locale).has(loader))\n    return;\n  if (!hasLocaleDictionary(locale)) {\n    $dictionary.update((d) => {\n      d[locale] = {};\n      return d;\n    });\n  }\n  localeQueue.add(loader);\n}\n\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __objRest$1 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$2.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$2.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nconst defaultFormats = {\n  number: {\n    scientific: { notation: \"scientific\" },\n    engineering: { notation: \"engineering\" },\n    compactLong: { notation: \"compact\", compactDisplay: \"long\" },\n    compactShort: { notation: \"compact\", compactDisplay: \"short\" }\n  },\n  date: {\n    short: { month: \"numeric\", day: \"numeric\", year: \"2-digit\" },\n    medium: { month: \"short\", day: \"numeric\", year: \"numeric\" },\n    long: { month: \"long\", day: \"numeric\", year: \"numeric\" },\n    full: { weekday: \"long\", month: \"long\", day: \"numeric\", year: \"numeric\" }\n  },\n  time: {\n    short: { hour: \"numeric\", minute: \"numeric\" },\n    medium: { hour: \"numeric\", minute: \"numeric\", second: \"numeric\" },\n    long: {\n      hour: \"numeric\",\n      minute: \"numeric\",\n      second: \"numeric\",\n      timeZoneName: \"short\"\n    },\n    full: {\n      hour: \"numeric\",\n      minute: \"numeric\",\n      second: \"numeric\",\n      timeZoneName: \"short\"\n    }\n  }\n};\nfunction defaultMissingKeyHandler({ locale, id }) {\n  console.warn(\n    `[svelte-i18n] The message \"${id}\" was not found in \"${getPossibleLocales(\n      locale\n    ).join('\", \"')}\".${hasLocaleQueue(getCurrentLocale()) ? `\n\nNote: there are at least one loader still registered to this locale that wasn't executed.` : \"\"}`\n  );\n}\nconst defaultOptions = {\n  fallbackLocale: null,\n  loadingDelay: 200,\n  formats: defaultFormats,\n  warnOnMissingMessages: true,\n  handleMissingMessage: void 0,\n  ignoreTag: true\n};\nconst options = defaultOptions;\nfunction getOptions() {\n  return options;\n}\nfunction init(opts) {\n  const _a = opts, { formats } = _a, rest = __objRest$1(_a, [\"formats\"]);\n  let initialLocale = opts.fallbackLocale;\n  if (opts.initialLocale) {\n    try {\n      if (IntlMessageFormat.resolveLocale(opts.initialLocale)) {\n        initialLocale = opts.initialLocale;\n      }\n    } catch (e) {\n      console.warn(\n        `[svelte-i18n] The initial locale \"${opts.initialLocale}\" is not a valid locale.`\n      );\n    }\n  }\n  if (rest.warnOnMissingMessages) {\n    delete rest.warnOnMissingMessages;\n    if (rest.handleMissingMessage == null) {\n      rest.handleMissingMessage = defaultMissingKeyHandler;\n    } else {\n      console.warn(\n        '[svelte-i18n] The \"warnOnMissingMessages\" option is deprecated. Please use the \"handleMissingMessage\" option instead.'\n      );\n    }\n  }\n  Object.assign(options, rest, { initialLocale });\n  if (formats) {\n    if (\"number\" in formats) {\n      Object.assign(options.formats.number, formats.number);\n    }\n    if (\"date\" in formats) {\n      Object.assign(options.formats.date, formats.date);\n    }\n    if (\"time\" in formats) {\n      Object.assign(options.formats.time, formats.time);\n    }\n  }\n  return $locale.set(initialLocale);\n}\n\nconst $isLoading = writable(false);\n\nvar __defProp$1 = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nlet current;\nconst internalLocale = writable(null);\nfunction getSubLocales(refLocale) {\n  return refLocale.split(\"-\").map((_, i, arr) => arr.slice(0, i + 1).join(\"-\")).reverse();\n}\nfunction getPossibleLocales(refLocale, fallbackLocale = getOptions().fallbackLocale) {\n  const locales = getSubLocales(refLocale);\n  if (fallbackLocale) {\n    return [.../* @__PURE__ */ new Set([...locales, ...getSubLocales(fallbackLocale)])];\n  }\n  return locales;\n}\nfunction getCurrentLocale() {\n  return current != null ? current : void 0;\n}\ninternalLocale.subscribe((newLocale) => {\n  current = newLocale != null ? newLocale : void 0;\n  if (typeof window !== \"undefined\" && newLocale != null) {\n    document.documentElement.setAttribute(\"lang\", newLocale);\n  }\n});\nconst set = (newLocale) => {\n  if (newLocale && getClosestAvailableLocale(newLocale) && hasLocaleQueue(newLocale)) {\n    const { loadingDelay } = getOptions();\n    let loadingTimer;\n    if (typeof window !== \"undefined\" && getCurrentLocale() != null && loadingDelay) {\n      loadingTimer = window.setTimeout(\n        () => $isLoading.set(true),\n        loadingDelay\n      );\n    } else {\n      $isLoading.set(true);\n    }\n    return flush(newLocale).then(() => {\n      internalLocale.set(newLocale);\n    }).finally(() => {\n      clearTimeout(loadingTimer);\n      $isLoading.set(false);\n    });\n  }\n  return internalLocale.set(newLocale);\n};\nconst $locale = __spreadProps(__spreadValues$1({}, internalLocale), {\n  set\n});\n\nconst getFromQueryString = (queryString, key) => {\n  const keyVal = queryString.split(\"&\").find((i) => i.indexOf(`${key}=`) === 0);\n  if (keyVal) {\n    return keyVal.split(\"=\").pop();\n  }\n  return null;\n};\nconst getFirstMatch = (base, pattern) => {\n  const match = pattern.exec(base);\n  if (!match)\n    return null;\n  return match[1] || null;\n};\nconst getLocaleFromHostname = (hostname) => {\n  if (typeof window === \"undefined\")\n    return null;\n  return getFirstMatch(window.location.hostname, hostname);\n};\nconst getLocaleFromPathname = (pathname) => {\n  if (typeof window === \"undefined\")\n    return null;\n  return getFirstMatch(window.location.pathname, pathname);\n};\nconst getLocaleFromNavigator = () => {\n  if (typeof window === \"undefined\")\n    return null;\n  return window.navigator.language || window.navigator.languages[0];\n};\nconst getLocaleFromQueryString = (search) => {\n  if (typeof window === \"undefined\")\n    return null;\n  return getFromQueryString(window.location.search.substr(1), search);\n};\nconst getLocaleFromHash = (hash) => {\n  if (typeof window === \"undefined\")\n    return null;\n  return getFromQueryString(window.location.hash.substr(1), hash);\n};\n\nconst monadicMemoize = (fn) => {\n  const cache = /* @__PURE__ */ Object.create(null);\n  const memoizedFn = (arg) => {\n    const cacheKey = JSON.stringify(arg);\n    if (cacheKey in cache) {\n      return cache[cacheKey];\n    }\n    return cache[cacheKey] = fn(arg);\n  };\n  return memoizedFn;\n};\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nconst getIntlFormatterOptions = (type, name) => {\n  const { formats } = getOptions();\n  if (type in formats && name in formats[type]) {\n    return formats[type][name];\n  }\n  throw new Error(`[svelte-i18n] Unknown \"${name}\" ${type} format.`);\n};\nconst createNumberFormatter = monadicMemoize(\n  (_a) => {\n    var _b = _a, { locale, format } = _b, options = __objRest(_b, [\"locale\", \"format\"]);\n    if (locale == null) {\n      throw new Error('[svelte-i18n] A \"locale\" must be set to format numbers');\n    }\n    if (format) {\n      options = getIntlFormatterOptions(\"number\", format);\n    }\n    return new Intl.NumberFormat(locale, options);\n  }\n);\nconst createDateFormatter = monadicMemoize(\n  (_c) => {\n    var _d = _c, { locale, format } = _d, options = __objRest(_d, [\"locale\", \"format\"]);\n    if (locale == null) {\n      throw new Error('[svelte-i18n] A \"locale\" must be set to format dates');\n    }\n    if (format) {\n      options = getIntlFormatterOptions(\"date\", format);\n    } else if (Object.keys(options).length === 0) {\n      options = getIntlFormatterOptions(\"date\", \"short\");\n    }\n    return new Intl.DateTimeFormat(locale, options);\n  }\n);\nconst createTimeFormatter = monadicMemoize(\n  (_e) => {\n    var _f = _e, { locale, format } = _f, options = __objRest(_f, [\"locale\", \"format\"]);\n    if (locale == null) {\n      throw new Error(\n        '[svelte-i18n] A \"locale\" must be set to format time values'\n      );\n    }\n    if (format) {\n      options = getIntlFormatterOptions(\"time\", format);\n    } else if (Object.keys(options).length === 0) {\n      options = getIntlFormatterOptions(\"time\", \"short\");\n    }\n    return new Intl.DateTimeFormat(locale, options);\n  }\n);\nconst getNumberFormatter = (_g = {}) => {\n  var _h = _g, {\n    locale = getCurrentLocale()\n  } = _h, args = __objRest(_h, [\n    \"locale\"\n  ]);\n  return createNumberFormatter(__spreadValues({ locale }, args));\n};\nconst getDateFormatter = (_i = {}) => {\n  var _j = _i, {\n    locale = getCurrentLocale()\n  } = _j, args = __objRest(_j, [\n    \"locale\"\n  ]);\n  return createDateFormatter(__spreadValues({ locale }, args));\n};\nconst getTimeFormatter = (_k = {}) => {\n  var _l = _k, {\n    locale = getCurrentLocale()\n  } = _l, args = __objRest(_l, [\n    \"locale\"\n  ]);\n  return createTimeFormatter(__spreadValues({ locale }, args));\n};\nconst getMessageFormatter = monadicMemoize(\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  (message, locale = getCurrentLocale()) => new IntlMessageFormat(message, locale, getOptions().formats, {\n    ignoreTag: getOptions().ignoreTag\n  })\n);\n\nconst formatMessage = (id, options = {}) => {\n  var _a, _b, _c, _d;\n  let messageObj = options;\n  if (typeof id === \"object\") {\n    messageObj = id;\n    id = messageObj.id;\n  }\n  const {\n    values,\n    locale = getCurrentLocale(),\n    default: defaultValue\n  } = messageObj;\n  if (locale == null) {\n    throw new Error(\n      \"[svelte-i18n] Cannot format a message without first setting the initial locale.\"\n    );\n  }\n  let message = lookup(id, locale);\n  if (!message) {\n    message = (_d = (_c = (_b = (_a = getOptions()).handleMissingMessage) == null ? void 0 : _b.call(_a, { locale, id, defaultValue })) != null ? _c : defaultValue) != null ? _d : id;\n  } else if (typeof message !== \"string\") {\n    console.warn(\n      `[svelte-i18n] Message with id \"${id}\" must be of type \"string\", found: \"${typeof message}\". Gettin its value through the \"$format\" method is deprecated; use the \"json\" method instead.`\n    );\n    return message;\n  }\n  if (!values) {\n    return message;\n  }\n  let result = message;\n  try {\n    result = getMessageFormatter(message, locale).format(values);\n  } catch (e) {\n    if (e instanceof Error) {\n      console.warn(\n        `[svelte-i18n] Message \"${id}\" has syntax error:`,\n        e.message\n      );\n    }\n  }\n  return result;\n};\nconst formatTime = (t, options) => {\n  return getTimeFormatter(options).format(t);\n};\nconst formatDate = (d, options) => {\n  return getDateFormatter(options).format(d);\n};\nconst formatNumber = (n, options) => {\n  return getNumberFormatter(options).format(n);\n};\nconst getJSON = (id, locale = getCurrentLocale()) => {\n  return lookup(id, locale);\n};\nconst $format = derived([$locale, $dictionary], () => formatMessage);\nconst $formatTime = derived([$locale], () => formatTime);\nconst $formatDate = derived([$locale], () => formatDate);\nconst $formatNumber = derived([$locale], () => formatNumber);\nconst $getJSON = derived([$locale, $dictionary], () => getJSON);\n\nfunction unwrapFunctionStore(store) {\n  let localReference;\n  const cancel = store.subscribe((value) => localReference = value);\n  const fn = (...args) => localReference(...args);\n  fn.freeze = cancel;\n  return fn;\n}\n\nfunction defineMessages(i) {\n  return i;\n}\nfunction waitLocale(locale) {\n  return flush(locale || getCurrentLocale() || getOptions().initialLocale);\n}\n\nexport { $format as _, addMessages, $formatDate as date, defineMessages, $dictionary as dictionary, $format as format, getDateFormatter, getLocaleFromHash, getLocaleFromHostname, getLocaleFromNavigator, getLocaleFromPathname, getLocaleFromQueryString, getMessageFormatter, getNumberFormatter, getTimeFormatter, init, $isLoading as isLoading, $getJSON as json, $locale as locale, $locales as locales, $formatNumber as number, registerLocaleLoader as register, $format as t, $formatTime as time, unwrapFunctionStore, waitLocale };\n", "let supports_adopted_stylesheets = false;\n\nif (\n\t\"attachShadow\" in Element.prototype &&\n\t\"adoptedStyleSheets\" in Document.prototype\n) {\n\t// Both Shadow DOM and adoptedStyleSheets are supported\n\tconst shadow_root_test = document\n\t\t.createElement(\"div\")\n\t\t.attachShadow({ mode: \"open\" });\n\tsupports_adopted_stylesheets = \"adoptedStyleSheets\" in shadow_root_test;\n}\n\nexport function mount_css(url: string, target: HTMLElement): Promise<void> {\n\tconst base = new URL(import.meta.url).origin;\n\tconst _url = new URL(url, base).href;\n\tconst existing_link = document.querySelector(`link[href='${_url}']`);\n\n\tif (existing_link) return Promise.resolve();\n\n\tconst link = document.createElement(\"link\");\n\tlink.rel = \"stylesheet\";\n\tlink.href = _url;\n\n\treturn new Promise((res, rej) => {\n\t\tlink.addEventListener(\"load\", () => res());\n\t\tlink.addEventListener(\"error\", () => {\n\t\t\tconsole.error(`Unable to preload CSS for ${_url}`);\n\t\t\tres();\n\t\t});\n\t\ttarget.appendChild(link);\n\t});\n}\n\nexport function prefix_css(\n\tstring: string,\n\tversion: string,\n\tstyle_element = document.createElement(\"style\")\n): HTMLStyleElement | null {\n\tif (!supports_adopted_stylesheets) return null;\n\tstyle_element.remove();\n\n\tconst stylesheet = new CSSStyleSheet();\n\tstylesheet.replaceSync(string);\n\n\tlet importString = \"\";\n\tstring = string.replace(/@import\\s+url\\((.*?)\\);\\s*/g, (match, url) => {\n\t\timportString += `@import url(${url});\\n`;\n\t\treturn \"\"; // remove and store any @import statements from the CSS\n\t});\n\n\tconst rules = stylesheet.cssRules;\n\n\tlet css_string = \"\";\n\tlet gradio_css_infix = `gradio-app .gradio-container.gradio-container-${version} .contain `;\n\n\tfor (let i = 0; i < rules.length; i++) {\n\t\tconst rule = rules[i];\n\n\t\tlet is_dark_rule = rule.cssText.includes(\".dark\");\n\t\tif (rule instanceof CSSStyleRule) {\n\t\t\tconst selector = rule.selectorText;\n\t\t\tif (selector) {\n\t\t\t\tconst new_selector = selector\n\t\t\t\t\t.replace(\".dark\", \"\")\n\t\t\t\t\t.split(\",\")\n\t\t\t\t\t.map(\n\t\t\t\t\t\t(s) =>\n\t\t\t\t\t\t\t`${is_dark_rule ? \".dark\" : \"\"} ${gradio_css_infix} ${s.trim()} `\n\t\t\t\t\t)\n\t\t\t\t\t.join(\",\");\n\n\t\t\t\tcss_string += rule.cssText;\n\t\t\t\tcss_string += rule.cssText.replace(selector, new_selector);\n\t\t\t}\n\t\t} else if (rule instanceof CSSMediaRule) {\n\t\t\tlet mediaCssString = `@media ${rule.media.mediaText} {`;\n\t\t\tfor (let j = 0; j < rule.cssRules.length; j++) {\n\t\t\t\tconst innerRule = rule.cssRules[j];\n\t\t\t\tif (innerRule instanceof CSSStyleRule) {\n\t\t\t\t\tlet is_dark_rule = innerRule.cssText.includes(\".dark \");\n\t\t\t\t\tconst selector = innerRule.selectorText;\n\t\t\t\t\tconst new_selector = selector\n\t\t\t\t\t\t.replace(\".dark\", \"\")\n\t\t\t\t\t\t.split(\",\")\n\t\t\t\t\t\t.map(\n\t\t\t\t\t\t\t(s) =>\n\t\t\t\t\t\t\t\t`${\n\t\t\t\t\t\t\t\t\tis_dark_rule ? \".dark\" : \"\"\n\t\t\t\t\t\t\t\t} ${gradio_css_infix} ${s.trim()} `\n\t\t\t\t\t\t)\n\t\t\t\t\t\t.join(\",\");\n\t\t\t\t\tmediaCssString += innerRule.cssText.replace(selector, new_selector);\n\t\t\t\t}\n\t\t\t}\n\t\t\tmediaCssString += \"}\";\n\t\t\tcss_string += mediaCssString;\n\t\t} else if (rule instanceof CSSKeyframesRule) {\n\t\t\tcss_string += `@keyframes ${rule.name} {`;\n\t\t\tfor (let j = 0; j < rule.cssRules.length; j++) {\n\t\t\t\tconst innerRule = rule.cssRules[j];\n\t\t\t\tif (innerRule instanceof CSSKeyframeRule) {\n\t\t\t\t\tcss_string += `${innerRule.keyText} { ${innerRule.style.cssText} }`;\n\t\t\t\t}\n\t\t\t}\n\t\t\tcss_string += \"}\";\n\t\t} else if (rule instanceof CSSFontFaceRule) {\n\t\t\tcss_string += `@font-face { ${rule.style.cssText} }`;\n\t\t}\n\t}\n\tcss_string = importString + css_string;\n\tstyle_element.textContent = css_string;\n\n\tdocument.head.appendChild(style_element);\n\treturn style_element;\n}\n", "import { addMessages, init, getLocaleFromNavigator } from \"svelte-i18n\";\n\nconst langs = import.meta.glob(\"./lang/*.json\", {\n\teager: true\n});\n\ntype LangsRecord = Record<\n\tstring,\n\t{\n\t\t[key: string]: any;\n\t}\n>;\n\nexport function process_langs(): LangsRecord {\n\tlet _langs: LangsRecord = {};\n\n\tfor (const lang in langs) {\n\t\tconst code = (lang.split(\"/\").pop() as string).split(\".\").shift() as string;\n\t\t_langs[code] = (langs[lang] as Record<string, any>).default;\n\t}\n\n\treturn _langs;\n}\n\nconst processed_langs = process_langs();\n\nfor (const lang in processed_langs) {\n\taddMessages(lang, processed_langs[lang]);\n}\n\nexport async function setupi18n(): Promise<void> {\n\tawait init({\n\t\tfallbackLocale: \"en\",\n\t\tinitialLocale: getLocaleFromNavigator()\n\t});\n}\n", "import \"@gradio/theme/reset.css\";\nimport \"@gradio/theme/global.css\";\nimport \"@gradio/theme/pollen.css\";\nimport \"@gradio/theme/typography.css\";\nimport { Client } from \"@gradio/client\";\nimport { mount_css } from \"@gradio/core\";\nimport type Index from \"./Index.svelte\";\n\nimport type { ThemeMode } from \"@gradio/core\";\n\n//@ts-ignore\nimport * as svelte from \"./svelte/svelte.js\";\n\ndeclare let BUILD_MODE: string;\ndeclare let GRADIO_VERSION: string;\n\nconst ENTRY_CSS = \"__ENTRY_CSS__\";\n\nlet FONTS: string | [];\n\nFONTS = \"__FONTS_CSS__\";\n\nlet IndexComponent: typeof Index;\nlet _res: (value?: unknown) => void;\nlet pending = new Promise((res) => {\n\t_res = res;\n});\nasync function get_index(): Promise<void> {\n\tIndexComponent = (await import(\"./Index.svelte\")).default;\n\t_res();\n}\n\nfunction create_custom_element(): void {\n\tconst o = {\n\t\tSvelteComponent: svelte.SvelteComponent\n\t};\n\tfor (const key in svelte) {\n\t\tif (key === \"SvelteComponent\") continue;\n\t\tif (key === \"SvelteComponentDev\") {\n\t\t\t//@ts-ignore\n\t\t\to[key] = o[\"SvelteComponent\"];\n\t\t} else {\n\t\t\t//@ts-ignore\n\t\t\to[key] = svelte[key];\n\t\t}\n\t}\n\t//@ts-ignore\n\twindow.__gradio__svelte__internal = o;\n\tclass GradioApp extends HTMLElement {\n\t\tcontrol_page_title: string | null;\n\t\tinitial_height: string;\n\t\tis_embed: string;\n\t\tcontainer: string;\n\t\tinfo: string | true;\n\t\tautoscroll: string | null;\n\t\teager: string | null;\n\t\ttheme_mode: ThemeMode | null;\n\t\thost: string | null;\n\t\tspace: string | null;\n\t\tsrc: string | null;\n\t\tapp?: Index;\n\t\tloading: boolean;\n\t\tupdating: { name: string; value: string } | false;\n\n\t\tconstructor() {\n\t\t\tsuper();\n\t\t\tthis.host = this.getAttribute(\"host\");\n\t\t\tthis.space = this.getAttribute(\"space\");\n\t\t\tthis.src = this.getAttribute(\"src\");\n\n\t\t\tthis.control_page_title = this.getAttribute(\"control_page_title\");\n\t\t\tthis.initial_height = this.getAttribute(\"initial_height\") ?? \"300px\"; // default: 300px\n\t\t\tthis.is_embed = this.getAttribute(\"embed\") ?? \"true\"; // default: true\n\t\t\tthis.container = this.getAttribute(\"container\") ?? \"true\"; // default: true\n\t\t\tthis.info = this.getAttribute(\"info\") ?? true; // default: true\n\t\t\tthis.autoscroll = this.getAttribute(\"autoscroll\");\n\t\t\tthis.eager = this.getAttribute(\"eager\");\n\t\t\tthis.theme_mode = this.getAttribute(\"theme_mode\") as ThemeMode | null;\n\t\t\tthis.updating = false;\n\t\t\tthis.loading = false;\n\t\t}\n\n\t\tasync connectedCallback(): Promise<void> {\n\t\t\tawait get_index();\n\t\t\tthis.loading = true;\n\n\t\t\tif (this.app) {\n\t\t\t\tthis.app.$destroy();\n\t\t\t}\n\n\t\t\tif (typeof FONTS !== \"string\") {\n\t\t\t\tFONTS.forEach((f) => mount_css(f, document.head));\n\t\t\t}\n\n\t\t\tawait mount_css(ENTRY_CSS, document.head);\n\n\t\t\tconst event = new CustomEvent(\"domchange\", {\n\t\t\t\tbubbles: true,\n\t\t\t\tcancelable: false,\n\t\t\t\tcomposed: true\n\t\t\t});\n\n\t\t\tconst observer = new MutationObserver((mutations) => {\n\t\t\t\tthis.dispatchEvent(event);\n\t\t\t});\n\n\t\t\tobserver.observe(this, { childList: true });\n\n\t\t\tthis.app = new IndexComponent({\n\t\t\t\ttarget: this,\n\t\t\t\tprops: {\n\t\t\t\t\t// embed source\n\t\t\t\t\tspace: this.space ? this.space.trim() : this.space,\n\t\t\t\t\tsrc: this.src ? this.src.trim() : this.src,\n\t\t\t\t\thost: this.host ? this.host.trim() : this.host,\n\t\t\t\t\t// embed info\n\t\t\t\t\tinfo: this.info === \"false\" ? false : true,\n\t\t\t\t\tcontainer: this.container === \"false\" ? false : true,\n\t\t\t\t\tis_embed: this.is_embed === \"false\" ? false : true,\n\t\t\t\t\tinitial_height: this.initial_height,\n\t\t\t\t\teager: this.eager === \"true\" ? true : false,\n\t\t\t\t\t// gradio meta info\n\t\t\t\t\tversion: GRADIO_VERSION,\n\t\t\t\t\ttheme_mode: this.theme_mode,\n\t\t\t\t\t// misc global behaviour\n\t\t\t\t\tautoscroll: this.autoscroll === \"true\" ? true : false,\n\t\t\t\t\tcontrol_page_title: this.control_page_title === \"true\" ? true : false,\n\t\t\t\t\t// injectables\n\t\t\t\t\tClient,\n\t\t\t\t\t// for gradio docs\n\t\t\t\t\t// TODO: Remove -- i think this is just for autoscroll behavhiour, app vs embeds\n\t\t\t\t\tapp_mode: window.__gradio_mode__ === \"app\"\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tif (this.updating) {\n\t\t\t\tthis.setAttribute(this.updating.name, this.updating.value);\n\t\t\t}\n\n\t\t\tthis.loading = false;\n\t\t}\n\n\t\tstatic get observedAttributes(): [\"src\", \"space\", \"host\"] {\n\t\t\treturn [\"src\", \"space\", \"host\"];\n\t\t}\n\n\t\tasync attributeChangedCallback(\n\t\t\tname: string,\n\t\t\told_val: string,\n\t\t\tnew_val: string\n\t\t): Promise<void> {\n\t\t\tawait pending;\n\t\t\tif (\n\t\t\t\t(name === \"host\" || name === \"space\" || name === \"src\") &&\n\t\t\t\tnew_val !== old_val\n\t\t\t) {\n\t\t\t\tthis.updating = { name, value: new_val };\n\t\t\t\tif (this.loading) return;\n\n\t\t\t\tif (this.app) {\n\t\t\t\t\tthis.app.$destroy();\n\t\t\t\t}\n\n\t\t\t\tthis.space = null;\n\t\t\t\tthis.host = null;\n\t\t\t\tthis.src = null;\n\n\t\t\t\tif (name === \"host\") {\n\t\t\t\t\tthis.host = new_val;\n\t\t\t\t} else if (name === \"space\") {\n\t\t\t\t\tthis.space = new_val;\n\t\t\t\t} else if (name === \"src\") {\n\t\t\t\t\tthis.src = new_val;\n\t\t\t\t}\n\n\t\t\t\tthis.app = new IndexComponent({\n\t\t\t\t\ttarget: this,\n\t\t\t\t\tprops: {\n\t\t\t\t\t\t// embed source\n\t\t\t\t\t\tspace: this.space ? this.space.trim() : this.space,\n\t\t\t\t\t\tsrc: this.src ? this.src.trim() : this.src,\n\t\t\t\t\t\thost: this.host ? this.host.trim() : this.host,\n\t\t\t\t\t\t// embed info\n\t\t\t\t\t\tinfo: this.info === \"false\" ? false : true,\n\t\t\t\t\t\tcontainer: this.container === \"false\" ? false : true,\n\t\t\t\t\t\tis_embed: this.is_embed === \"false\" ? false : true,\n\t\t\t\t\t\tinitial_height: this.initial_height,\n\t\t\t\t\t\teager: this.eager === \"true\" ? true : false,\n\t\t\t\t\t\t// gradio meta info\n\t\t\t\t\t\tversion: GRADIO_VERSION,\n\t\t\t\t\t\ttheme_mode: this.theme_mode,\n\t\t\t\t\t\t// misc global behaviour\n\t\t\t\t\t\tautoscroll: this.autoscroll === \"true\" ? true : false,\n\t\t\t\t\t\tcontrol_page_title:\n\t\t\t\t\t\t\tthis.control_page_title === \"true\" ? true : false,\n\t\t\t\t\t\t// injectables\n\t\t\t\t\t\tClient,\n\t\t\t\t\t\t// for gradio docs\n\t\t\t\t\t\t// TODO: Remove -- i think this is just for autoscroll behavhiour, app vs embeds\n\t\t\t\t\t\tapp_mode: window.__gradio_mode__ === \"app\"\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tthis.updating = false;\n\t\t\t}\n\t\t}\n\t}\n\tif (!customElements.get(\"gradio-app\"))\n\t\tcustomElements.define(\"gradio-app\", GradioApp);\n}\n\ncreate_custom_element();\n"], "file": "assets/index-D5ROCp7B.js"}