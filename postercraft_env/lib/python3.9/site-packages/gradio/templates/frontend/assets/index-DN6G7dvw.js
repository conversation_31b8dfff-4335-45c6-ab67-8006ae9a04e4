import{U as kn}from"./ModifyUpload.svelte_svelte_type_style_lang-DEZM0x56.js";import{M as $n}from"./ModifyUpload-DZAlpNPL.js";import{I as En,S as Yt}from"./Index-WGC0_FkS.js";import{B as Zt}from"./BlockLabel-CJsotHlk.js";import{V as pt}from"./Video-fsmLZWjA.js";import{S as Tn}from"./SelectSource-ghC4bkgc.js";import{W as yn}from"./ImageUploader-sLREcIL3.js";import"./Image-BZaARumT.js";/* empty css                                              */import{l as Sn,t as Vn,V as Dn,p as St,a as qn}from"./Video-CZt5m8l5.js";import{b as no}from"./Video-CZt5m8l5.js";import{T as Cn,P as Rn,a as zn}from"./Trim-UKwaW4UI.js";import{U as xt}from"./Undo-CpmTQw3B.js";import{f as Ce,u as Bn}from"./Blocks-aR9ucLZz.js";import{p as Ln}from"./index-COY1HN2y.js";import{E as Mn}from"./Empty-Vuj7-ssy.js";import{S as In}from"./ShareButton-Ds9bG3Tz.js";import{D as Pn,a as Nn}from"./DownloadLink-DYBmO3sz.js";import{default as io}from"./Example-CCJuO3xS.js";import{B as en}from"./Button-8nmImwVJ.js";import{U as Un}from"./UploadText-DlCTYTPP.js";import"./Upload-Cp8Go_XF.js";import"./Image-Bsh8Umrh.js";import"./DropdownArrow-AhwBZaFV.js";import"./file-url-Bf0nK4ai.js";import"./svelte/svelte.js";const{SvelteComponent:An,append:Hn,attr:x,detach:Xn,init:Fn,insert:jn,noop:ft,safe_not_equal:On,svg_element:Vt}=window.__gradio__svelte__internal;function Wn(l){let e,n;return{c(){e=Vt("svg"),n=Vt("path"),x(n,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24"),x(e,"fill","none"),x(e,"stroke","currentColor"),x(e,"stroke-width","1.5"),x(e,"stroke-linecap","round"),x(e,"stroke-linejoin","round")},m(t,i){jn(t,e,i),Hn(e,n)},p:ft,i:ft,o:ft,d(t){t&&Xn(e)}}}class Jn extends An{constructor(e){super(),Fn(this,e,null,Wn,On,{})}}const{SvelteComponent:Gn,append:Se,attr:U,destroy_block:Kn,detach:rt,element:ve,ensure_array_like:Dt,flush:Ue,init:Qn,insert:at,listen:Ve,noop:ht,run_all:Yn,safe_not_equal:Zn,set_style:he,space:ct,src_url_equal:qt,update_keyed_each:xn}=window.__gradio__svelte__internal,{onMount:Ct,onDestroy:el}=window.__gradio__svelte__internal;function Rt(l,e,n){const t=l.slice();return t[20]=e[n],t[22]=n,t}function tl(l){let e,n,t,i,o,s=[],a=new Map,r,_,u,c,d=Dt(l[1]);const h=g=>g[22];for(let g=0;g<d.length;g+=1){let m=Rt(l,d,g),k=h(m);a.set(k,s[g]=zt(k,m))}return{c(){e=ve("div"),n=ve("button"),t=ct(),i=ve("div"),o=ct();for(let g=0;g<s.length;g+=1)s[g].c();r=ct(),_=ve("button"),U(n,"aria-label","start drag handle for trimming video"),U(n,"class","handle left svelte-10c4beq"),he(n,"left",l[2]+"%"),U(i,"class","opaque-layer svelte-10c4beq"),he(i,"left",l[2]+"%"),he(i,"right",100-l[3]+"%"),U(_,"aria-label","end drag handle for trimming video"),U(_,"class","handle right svelte-10c4beq"),he(_,"left",l[3]+"%"),U(e,"id","timeline"),U(e,"class","thumbnail-wrapper svelte-10c4beq")},m(g,m){at(g,e,m),Se(e,n),Se(e,t),Se(e,i),Se(e,o);for(let k=0;k<s.length;k+=1)s[k]&&s[k].m(e,null);Se(e,r),Se(e,_),u||(c=[Ve(n,"mousedown",l[10]),Ve(n,"blur",l[5]),Ve(n,"keydown",l[11]),Ve(_,"mousedown",l[12]),Ve(_,"blur",l[5]),Ve(_,"keydown",l[13])],u=!0)},p(g,m){m&4&&he(n,"left",g[2]+"%"),m&4&&he(i,"left",g[2]+"%"),m&8&&he(i,"right",100-g[3]+"%"),m&2&&(d=Dt(g[1]),s=xn(s,m,h,1,g,d,a,e,Kn,zt,r,Rt)),m&8&&he(_,"left",g[3]+"%")},d(g){g&&rt(e);for(let m=0;m<s.length;m+=1)s[m].d();u=!1,Yn(c)}}}function nl(l){let e;return{c(){e=ve("div"),e.innerHTML='<span aria-label="loading timeline" class="loader svelte-10c4beq"></span>',U(e,"class","load-wrap svelte-10c4beq")},m(n,t){at(n,e,t)},p:ht,d(n){n&&rt(e)}}}function zt(l,e){let n,t,i;return{key:l,first:null,c(){n=ve("img"),qt(n.src,t=e[20])||U(n,"src",t),U(n,"alt",i=`frame-${e[22]}`),U(n,"draggable","false"),U(n,"class","svelte-10c4beq"),this.first=n},m(o,s){at(o,n,s)},p(o,s){e=o,s&2&&!qt(n.src,t=e[20])&&U(n,"src",t),s&2&&i!==(i=`frame-${e[22]}`)&&U(n,"alt",i)},d(o){o&&rt(n)}}}function ll(l){let e;function n(o,s){return o[0]?nl:tl}let t=n(l),i=t(l);return{c(){e=ve("div"),i.c(),U(e,"class","container svelte-10c4beq")},m(o,s){at(o,e,s),i.m(e,null)},p(o,[s]){t===(t=n(o))&&i?i.p(o,s):(i.d(1),i=t(o),i&&(i.c(),i.m(e,null)))},i:ht,o:ht,d(o){o&&rt(e),i.d()}}}let dt=10;function il(l,e,n){let{videoElement:t}=e,{trimmedDuration:i}=e,{dragStart:o}=e,{dragEnd:s}=e,{loadingTimeline:a}=e,r=[],_,u=0,c=100,d=null;const h=f=>{d=f},g=()=>{d=null},m=(f,y)=>{if(d){const M=document.getElementById("timeline");if(!M)return;const T=M.getBoundingClientRect();let S=(f.clientX-T.left)/T.width*100;if(y?S=d==="left"?u+y:c+y:S=(f.clientX-T.left)/T.width*100,S=Math.max(0,Math.min(S,100)),d==="left"){n(2,u=Math.min(S,c));const R=u/100*_;n(6,t.currentTime=R,t),n(8,o=R)}else if(d==="right"){n(3,c=Math.max(S,u));const R=c/100*_;n(6,t.currentTime=R,t),n(9,s=R)}const W=u/100*_,B=c/100*_;n(7,i=B-W),n(2,u),n(3,c)}},k=f=>{if(d){const y=1/_*100;f.key==="ArrowLeft"?m({clientX:0},-y):f.key==="ArrowRight"&&m({clientX:0},y)}},E=()=>{const f=document.createElement("canvas"),y=f.getContext("2d");if(!y)return;f.width=t.videoWidth,f.height=t.videoHeight,y.drawImage(t,0,0,f.width,f.height);const M=f.toDataURL("image/jpeg",.7);n(1,r=[...r,M])};Ct(()=>{const f=()=>{_=t.duration;const y=_/dt;let M=0;const T=()=>{E(),M++,M<dt?n(6,t.currentTime+=y,t):t.removeEventListener("seeked",T)};t.addEventListener("seeked",T),n(6,t.currentTime=0,t)};t.readyState>=1?f():t.addEventListener("loadedmetadata",f)}),el(()=>{window.removeEventListener("mousemove",m),window.removeEventListener("mouseup",g),window.removeEventListener("keydown",k)}),Ct(()=>{window.addEventListener("mousemove",m),window.addEventListener("mouseup",g),window.addEventListener("keydown",k)});const D=()=>h("left"),q=f=>{(f.key==="ArrowLeft"||f.key=="ArrowRight")&&h("left")},L=()=>h("right"),$=f=>{(f.key==="ArrowLeft"||f.key=="ArrowRight")&&h("right")};return l.$$set=f=>{"videoElement"in f&&n(6,t=f.videoElement),"trimmedDuration"in f&&n(7,i=f.trimmedDuration),"dragStart"in f&&n(8,o=f.dragStart),"dragEnd"in f&&n(9,s=f.dragEnd),"loadingTimeline"in f&&n(0,a=f.loadingTimeline)},l.$$.update=()=>{l.$$.dirty&2&&n(0,a=r.length!==dt)},[a,r,u,c,h,g,t,i,o,s,D,q,L,$]}class ol extends Gn{constructor(e){super(),Qn(this,e,il,ll,Zn,{videoElement:6,trimmedDuration:7,dragStart:8,dragEnd:9,loadingTimeline:0})}get videoElement(){return this.$$.ctx[6]}set videoElement(e){this.$$set({videoElement:e}),Ue()}get trimmedDuration(){return this.$$.ctx[7]}set trimmedDuration(e){this.$$set({trimmedDuration:e}),Ue()}get dragStart(){return this.$$.ctx[8]}set dragStart(e){this.$$set({dragStart:e}),Ue()}get dragEnd(){return this.$$.ctx[9]}set dragEnd(e){this.$$set({dragEnd:e}),Ue()}get loadingTimeline(){return this.$$.ctx[0]}set loadingTimeline(e){this.$$set({loadingTimeline:e}),Ue()}}const{SvelteComponent:sl,add_flush_callback:We,append:qe,attr:O,bind:Je,binding_callbacks:Ge,check_outros:Ke,create_component:kt,destroy_component:$t,detach:ne,element:le,empty:rl,flush:ge,group_outros:Qe,init:al,insert:ie,listen:nt,mount_component:Et,noop:gt,run_all:ul,safe_not_equal:_l,set_data:fl,space:Ye,text:cl,toggle_class:Re,transition_in:j,transition_out:Y}=window.__gradio__svelte__internal,{onMount:dl}=window.__gradio__svelte__internal;function Bt(l){let e,n,t,i,o,s,a;function r(h){l[13](h)}function _(h){l[14](h)}function u(h){l[15](h)}function c(h){l[16](h)}let d={videoElement:l[2]};return l[9]!==void 0&&(d.dragStart=l[9]),l[10]!==void 0&&(d.dragEnd=l[10]),l[7]!==void 0&&(d.trimmedDuration=l[7]),l[11]!==void 0&&(d.loadingTimeline=l[11]),n=new ol({props:d}),Ge.push(()=>Je(n,"dragStart",r)),Ge.push(()=>Je(n,"dragEnd",_)),Ge.push(()=>Je(n,"trimmedDuration",u)),Ge.push(()=>Je(n,"loadingTimeline",c)),{c(){e=le("div"),kt(n.$$.fragment),O(e,"class","timeline-wrapper svelte-sxyn79")},m(h,g){ie(h,e,g),Et(n,e,null),a=!0},p(h,g){const m={};g&4&&(m.videoElement=h[2]),!t&&g&512&&(t=!0,m.dragStart=h[9],We(()=>t=!1)),!i&&g&1024&&(i=!0,m.dragEnd=h[10],We(()=>i=!1)),!o&&g&128&&(o=!0,m.trimmedDuration=h[7],We(()=>o=!1)),!s&&g&2048&&(s=!0,m.loadingTimeline=h[11],We(()=>s=!1)),n.$set(m)},i(h){a||(j(n.$$.fragment,h),a=!0)},o(h){Y(n.$$.fragment,h),a=!1},d(h){h&&ne(e),$t(n)}}}function ml(l){let e;return{c(){e=le("div"),O(e,"class","svelte-sxyn79")},m(n,t){ie(n,e,t)},p:gt,d(n){n&&ne(e)}}}function hl(l){let e,n=Ce(l[7])+"",t;return{c(){e=le("time"),t=cl(n),O(e,"aria-label","duration of selected region in seconds"),O(e,"class","svelte-sxyn79"),Re(e,"hidden",l[11])},m(i,o){ie(i,e,o),qe(e,t)},p(i,o){o&128&&n!==(n=Ce(i[7])+"")&&fl(t,n),o&2048&&Re(e,"hidden",i[11])},d(i){i&&ne(e)}}}function Lt(l){let e,n,t,i,o;return n=new xt({}),{c(){e=le("button"),kt(n.$$.fragment),O(e,"class","action icon svelte-sxyn79"),e.disabled=l[1],O(e,"aria-label","Reset video to initial value")},m(s,a){ie(s,e,a),Et(n,e,null),t=!0,i||(o=nt(e,"click",l[17]),i=!0)},p(s,a){(!t||a&2)&&(e.disabled=s[1])},i(s){t||(j(n.$$.fragment,s),t=!0)},o(s){Y(n.$$.fragment,s),t=!1},d(s){s&&ne(e),$t(n),i=!1,o()}}}function Mt(l){let e,n,t,i;const o=[bl,gl],s=[];function a(r,_){return r[0]===""?0:1}return e=a(l),n=s[e]=o[e](l),{c(){n.c(),t=rl()},m(r,_){s[e].m(r,_),ie(r,t,_),i=!0},p(r,_){let u=e;e=a(r),e===u?s[e].p(r,_):(Qe(),Y(s[u],1,1,()=>{s[u]=null}),Ke(),n=s[e],n?n.p(r,_):(n=s[e]=o[e](r),n.c()),j(n,1),n.m(t.parentNode,t))},i(r){i||(j(n),i=!0)},o(r){Y(n),i=!1},d(r){r&&ne(t),s[e].d(r)}}}function gl(l){let e,n,t,i,o;return{c(){e=le("button"),e.textContent="Trim",n=Ye(),t=le("button"),t.textContent="Cancel",O(e,"class","text-button svelte-sxyn79"),Re(e,"hidden",l[11]),O(t,"class","text-button svelte-sxyn79"),Re(t,"hidden",l[11])},m(s,a){ie(s,e,a),ie(s,n,a),ie(s,t,a),i||(o=[nt(e,"click",l[18]),nt(t,"click",l[12])],i=!0)},p(s,a){a&2048&&Re(e,"hidden",s[11]),a&2048&&Re(t,"hidden",s[11])},i:gt,o:gt,d(s){s&&(ne(e),ne(n),ne(t)),i=!1,ul(o)}}}function bl(l){let e,n,t,i,o;return n=new Cn({}),{c(){e=le("button"),kt(n.$$.fragment),e.disabled=l[1],O(e,"class","action icon svelte-sxyn79"),O(e,"aria-label","Trim video to selection")},m(s,a){ie(s,e,a),Et(n,e,null),t=!0,i||(o=nt(e,"click",l[12]),i=!0)},p(s,a){(!t||a&2)&&(e.disabled=s[1])},i(s){t||(j(n.$$.fragment,s),t=!0)},o(s){Y(n.$$.fragment,s),t=!1},d(s){s&&ne(e),$t(n),i=!1,o()}}}function wl(l){let e,n,t,i,o,s,a,r=l[0]==="edit"&&Bt(l);function _(g,m){return g[0]==="edit"&&g[7]!==null?hl:ml}let u=_(l),c=u(l),d=l[3]&&l[0]===""&&Lt(l),h=l[4]&&Mt(l);return{c(){e=le("div"),r&&r.c(),n=Ye(),t=le("div"),c.c(),i=Ye(),o=le("div"),d&&d.c(),s=Ye(),h&&h.c(),O(o,"class","settings-wrapper svelte-sxyn79"),O(t,"class","controls svelte-sxyn79"),O(t,"data-testid","waveform-controls"),O(e,"class","container svelte-sxyn79")},m(g,m){ie(g,e,m),r&&r.m(e,null),qe(e,n),qe(e,t),c.m(t,null),qe(t,i),qe(t,o),d&&d.m(o,null),qe(o,s),h&&h.m(o,null),a=!0},p(g,[m]){g[0]==="edit"?r?(r.p(g,m),m&1&&j(r,1)):(r=Bt(g),r.c(),j(r,1),r.m(e,n)):r&&(Qe(),Y(r,1,1,()=>{r=null}),Ke()),u===(u=_(g))&&c?c.p(g,m):(c.d(1),c=u(g),c&&(c.c(),c.m(t,i))),g[3]&&g[0]===""?d?(d.p(g,m),m&9&&j(d,1)):(d=Lt(g),d.c(),j(d,1),d.m(o,s)):d&&(Qe(),Y(d,1,1,()=>{d=null}),Ke()),g[4]?h?(h.p(g,m),m&16&&j(h,1)):(h=Mt(g),h.c(),j(h,1),h.m(o,null)):h&&(Qe(),Y(h,1,1,()=>{h=null}),Ke())},i(g){a||(j(r),j(d),j(h),a=!0)},o(g){Y(r),Y(d),Y(h),a=!1},d(g){g&&ne(e),r&&r.d(),c.d(),d&&d.d(),h&&h.d()}}}function vl(l,e,n){let{videoElement:t}=e,{showRedo:i=!1}=e,{interactive:o=!0}=e,{mode:s=""}=e,{handle_reset_value:a}=e,{handle_trim_video:r}=e,{processingVideo:_=!1}=e,u;dl(async()=>{n(8,u=await Sn())});let c=null,d=0,h=0,g=!1;const m=()=>{s==="edit"?(n(0,s=""),n(7,c=t.duration)):n(0,s="edit")};function k(f){d=f,n(9,d)}function E(f){h=f,n(10,h)}function D(f){c=f,n(7,c),n(0,s),n(2,t)}function q(f){g=f,n(11,g)}const L=()=>{a(),n(0,s="")},$=()=>{n(0,s=""),n(1,_=!0),Vn(u,d,h,t).then(f=>{r(f)}).then(()=>{n(1,_=!1)})};return l.$$set=f=>{"videoElement"in f&&n(2,t=f.videoElement),"showRedo"in f&&n(3,i=f.showRedo),"interactive"in f&&n(4,o=f.interactive),"mode"in f&&n(0,s=f.mode),"handle_reset_value"in f&&n(5,a=f.handle_reset_value),"handle_trim_video"in f&&n(6,r=f.handle_trim_video),"processingVideo"in f&&n(1,_=f.processingVideo)},l.$$.update=()=>{l.$$.dirty&133&&s==="edit"&&c===null&&t&&n(7,c=t.duration)},[s,_,t,i,o,a,r,c,u,d,h,g,m,k,E,D,q,L,$]}class pl extends sl{constructor(e){super(),al(this,e,vl,wl,_l,{videoElement:2,showRedo:3,interactive:4,mode:0,handle_reset_value:5,handle_trim_video:6,processingVideo:1})}get videoElement(){return this.$$.ctx[2]}set videoElement(e){this.$$set({videoElement:e}),ge()}get showRedo(){return this.$$.ctx[3]}set showRedo(e){this.$$set({showRedo:e}),ge()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),ge()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),ge()}get handle_reset_value(){return this.$$.ctx[5]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),ge()}get handle_trim_video(){return this.$$.ctx[6]}set handle_trim_video(e){this.$$set({handle_trim_video:e}),ge()}get processingVideo(){return this.$$.ctx[1]}set processingVideo(e){this.$$set({processingVideo:e}),ge()}}const{SvelteComponent:kl,add_flush_callback:He,append:F,attr:I,bind:Xe,binding_callbacks:Fe,bubble:It,check_outros:Pt,create_component:ze,destroy_component:Be,detach:Ze,element:ue,empty:$l,flush:ee,group_outros:Nt,init:El,insert:xe,listen:be,mount_component:Le,prevent_default:Ut,run_all:Tl,safe_not_equal:yl,set_data:At,space:Ae,src_url_equal:Ht,stop_propagation:Sl,text:mt,toggle_class:Xt,transition_in:Q,transition_out:te}=window.__gradio__svelte__internal,{createEventDispatcher:Vl}=window.__gradio__svelte__internal;function Dl(l){let e,n;return{c(){e=ue("track"),I(e,"kind","captions"),Ht(e.src,n=l[1])||I(e,"src",n),e.default=!0},m(t,i){xe(t,e,i)},p(t,i){i&2&&!Ht(e.src,n=t[1])&&I(e,"src",n)},d(t){t&&Ze(e)}}}function ql(l){let e,n;return e=new Rn({}),{c(){ze(e.$$.fragment)},m(t,i){Le(e,t,i),n=!0},i(t){n||(Q(e.$$.fragment,t),n=!0)},o(t){te(e.$$.fragment,t),n=!1},d(t){Be(e,t)}}}function Cl(l){let e,n;return e=new zn({}),{c(){ze(e.$$.fragment)},m(t,i){Le(e,t,i),n=!0},i(t){n||(Q(e.$$.fragment,t),n=!0)},o(t){te(e.$$.fragment,t),n=!1},d(t){Be(e,t)}}}function Rl(l){let e,n;return e=new xt({}),{c(){ze(e.$$.fragment)},m(t,i){Le(e,t,i),n=!0},i(t){n||(Q(e.$$.fragment,t),n=!0)},o(t){te(e.$$.fragment,t),n=!1},d(t){Be(e,t)}}}function Ft(l){let e,n,t;function i(s){l[27](s)}let o={videoElement:l[10],showRedo:!0,handle_trim_video:l[16],handle_reset_value:l[6]};return l[11]!==void 0&&(o.processingVideo=l[11]),e=new pl({props:o}),Fe.push(()=>Xe(e,"processingVideo",i)),{c(){ze(e.$$.fragment)},m(s,a){Le(e,s,a),t=!0},p(s,a){const r={};a&1024&&(r.videoElement=s[10]),a&64&&(r.handle_reset_value=s[6]),!n&&a&2048&&(n=!0,r.processingVideo=s[11],He(()=>n=!1)),e.$set(r)},i(s){t||(Q(e.$$.fragment,s),t=!0)},o(s){te(e.$$.fragment,s),t=!1},d(s){Be(e,s)}}}function zl(l){let e,n,t,i,o,s,a,r,_,u,c,d,h,g,m,k=Ce(l[7])+"",E,D,q=Ce(l[8])+"",L,$,f,y,M,T,S,W,B,R,Z,me;function w(p){l[21](p)}function J(p){l[22](p)}function G(p){l[23](p)}function oe(p){l[24](p)}let se={src:l[0],preload:"auto",autoplay:l[3],"data-testid":`${l[4]}-player`,processingVideo:l[11],$$slots:{default:[Dl]},$$scope:{ctx:l}};l[7]!==void 0&&(se.currentTime=l[7]),l[8]!==void 0&&(se.duration=l[8]),l[9]!==void 0&&(se.paused=l[9]),l[10]!==void 0&&(se.node=l[10]),t=new Dn({props:se}),Fe.push(()=>Xe(t,"currentTime",w)),Fe.push(()=>Xe(t,"duration",J)),Fe.push(()=>Xe(t,"paused",G)),Fe.push(()=>Xe(t,"node",oe)),t.$on("click",l[13]),t.$on("play",l[25]),t.$on("pause",l[26]),t.$on("ended",l[15]);const b=[Rl,Cl,ql],re=[];function Oe(p,V){return p[7]===p[8]?0:p[9]?1:2}d=Oe(l),h=re[d]=b[d](l),S=new Jn({});let z=l[5]&&Ft(l);return{c(){e=ue("div"),n=ue("div"),ze(t.$$.fragment),r=Ae(),_=ue("div"),u=ue("div"),c=ue("span"),h.c(),g=Ae(),m=ue("span"),E=mt(k),D=mt(" / "),L=mt(q),$=Ae(),f=ue("progress"),M=Ae(),T=ue("div"),ze(S.$$.fragment),W=Ae(),z&&z.c(),B=$l(),I(n,"class","mirror-wrap svelte-euo1cw"),Xt(n,"mirror",l[2]),I(c,"role","button"),I(c,"tabindex","0"),I(c,"class","icon svelte-euo1cw"),I(c,"aria-label","play-pause-replay-button"),I(m,"class","time svelte-euo1cw"),f.value=y=l[7]/l[8]||0,I(f,"class","svelte-euo1cw"),I(T,"role","button"),I(T,"tabindex","0"),I(T,"class","icon svelte-euo1cw"),I(T,"aria-label","full-screen"),I(u,"class","inner svelte-euo1cw"),I(_,"class","controls svelte-euo1cw"),I(e,"class","wrap svelte-euo1cw")},m(p,V){xe(p,e,V),F(e,n),Le(t,n,null),F(e,r),F(e,_),F(_,u),F(u,c),re[d].m(c,null),F(u,g),F(u,m),F(m,E),F(m,D),F(m,L),F(u,$),F(u,f),F(u,M),F(u,T),Le(S,T,null),xe(p,W,V),z&&z.m(p,V),xe(p,B,V),R=!0,Z||(me=[be(c,"click",l[13]),be(c,"keydown",l[13]),be(f,"mousemove",l[12]),be(f,"touchmove",Ut(l[12])),be(f,"click",Sl(Ut(l[14]))),be(T,"click",l[17]),be(T,"keypress",l[17])],Z=!0)},p(p,[V]){const K={};V&1&&(K.src=p[0]),V&8&&(K.autoplay=p[3]),V&16&&(K["data-testid"]=`${p[4]}-player`),V&2048&&(K.processingVideo=p[11]),V&536870914&&(K.$$scope={dirty:V,ctx:p}),!i&&V&128&&(i=!0,K.currentTime=p[7],He(()=>i=!1)),!o&&V&256&&(o=!0,K.duration=p[8],He(()=>o=!1)),!s&&V&512&&(s=!0,K.paused=p[9],He(()=>s=!1)),!a&&V&1024&&(a=!0,K.node=p[10],He(()=>a=!1)),t.$set(K),(!R||V&4)&&Xt(n,"mirror",p[2]);let Ne=d;d=Oe(p),d!==Ne&&(Nt(),te(re[Ne],1,1,()=>{re[Ne]=null}),Pt(),h=re[d],h||(h=re[d]=b[d](p),h.c()),Q(h,1),h.m(c,null)),(!R||V&128)&&k!==(k=Ce(p[7])+"")&&At(E,k),(!R||V&256)&&q!==(q=Ce(p[8])+"")&&At(L,q),(!R||V&384&&y!==(y=p[7]/p[8]||0))&&(f.value=y),p[5]?z?(z.p(p,V),V&32&&Q(z,1)):(z=Ft(p),z.c(),Q(z,1),z.m(B.parentNode,B)):z&&(Nt(),te(z,1,1,()=>{z=null}),Pt())},i(p){R||(Q(t.$$.fragment,p),Q(h),Q(S.$$.fragment,p),Q(z),R=!0)},o(p){te(t.$$.fragment,p),te(h),te(S.$$.fragment,p),te(z),R=!1},d(p){p&&(Ze(e),Ze(W),Ze(B)),Be(t),re[d].d(),Be(S),z&&z.d(p),Z=!1,Tl(me)}}}function Bl(l,e,n){let{root:t=""}=e,{src:i}=e,{subtitle:o=null}=e,{mirror:s}=e,{autoplay:a}=e,{label:r="test"}=e,{interactive:_=!1}=e,{handle_change:u=()=>{}}=e,{handle_reset_value:c=()=>{}}=e,{upload:d}=e;const h=Vl();let g=0,m,k=!0,E,D=!1;function q(w){if(!m)return;if(w.type==="click"){$(w);return}if(w.type!=="touchmove"&&!(w.buttons&1))return;const J=w.type==="touchmove"?w.touches[0].clientX:w.clientX,{left:G,right:oe}=w.currentTarget.getBoundingClientRect();n(7,g=m*(J-G)/(oe-G))}async function L(){document.fullscreenElement!=E&&(E.currentTime>0&&!E.paused&&!E.ended&&E.readyState>E.HAVE_CURRENT_DATA?E.pause():await E.play())}function $(w){const{left:J,right:G}=w.currentTarget.getBoundingClientRect();n(7,g=m*(w.clientX-J)/(G-J))}function f(){h("stop"),h("end")}const y=async w=>{let J=new File([w],"video.mp4");const G=await Ln([J]);let oe=(await d(G,t))?.filter(Boolean)[0];u(oe)};function M(){E.requestFullscreen()}function T(w){g=w,n(7,g)}function S(w){m=w,n(8,m)}function W(w){k=w,n(9,k)}function B(w){E=w,n(10,E)}function R(w){It.call(this,l,w)}function Z(w){It.call(this,l,w)}function me(w){D=w,n(11,D)}return l.$$set=w=>{"root"in w&&n(18,t=w.root),"src"in w&&n(0,i=w.src),"subtitle"in w&&n(1,o=w.subtitle),"mirror"in w&&n(2,s=w.mirror),"autoplay"in w&&n(3,a=w.autoplay),"label"in w&&n(4,r=w.label),"interactive"in w&&n(5,_=w.interactive),"handle_change"in w&&n(19,u=w.handle_change),"handle_reset_value"in w&&n(6,c=w.handle_reset_value),"upload"in w&&n(20,d=w.upload)},[i,o,s,a,r,_,c,g,m,k,E,D,q,L,$,f,y,M,t,u,d,T,S,W,B,R,Z,me]}class Ll extends kl{constructor(e){super(),El(this,e,Bl,zl,yl,{root:18,src:0,subtitle:1,mirror:2,autoplay:3,label:4,interactive:5,handle_change:19,handle_reset_value:6,upload:20})}get root(){return this.$$.ctx[18]}set root(e){this.$$set({root:e}),ee()}get src(){return this.$$.ctx[0]}set src(e){this.$$set({src:e}),ee()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),ee()}get mirror(){return this.$$.ctx[2]}set mirror(e){this.$$set({mirror:e}),ee()}get autoplay(){return this.$$.ctx[3]}set autoplay(e){this.$$set({autoplay:e}),ee()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),ee()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),ee()}get handle_change(){return this.$$.ctx[19]}set handle_change(e){this.$$set({handle_change:e}),ee()}get handle_reset_value(){return this.$$.ctx[6]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),ee()}get upload(){return this.$$.ctx[20]}set upload(e){this.$$set({upload:e}),ee()}}const tn=Ll,{SvelteComponent:Ml,add_flush_callback:nn,append:bt,attr:je,bind:ln,binding_callbacks:on,bubble:we,check_outros:ut,create_component:Me,create_slot:Il,destroy_component:Ie,detach:_e,element:lt,empty:sn,flush:N,get_all_dirty_from_scope:Pl,get_slot_changes:Nl,group_outros:_t,init:Ul,insert:fe,mount_component:Pe,noop:wt,safe_not_equal:rn,set_data:jt,space:it,text:Ot,transition_in:H,transition_out:X,update_slot_base:Al}=window.__gradio__svelte__internal,{createEventDispatcher:Hl}=window.__gradio__svelte__internal;function Xl(l){let e,n,t,i,o,s,a;e=new $n({props:{i18n:l[11],download:l[5]?l[0].url:null}}),e.$on("clear",l[19]);const r=[Ol,jl],_=[];function u(c,d){return t==null&&(t=!!qn()),t?0:c[0].size?1:-1}return~(i=u(l))&&(o=_[i]=r[i](l)),{c(){Me(e.$$.fragment),n=it(),o&&o.c(),s=sn()},m(c,d){Pe(e,c,d),fe(c,n,d),~i&&_[i].m(c,d),fe(c,s,d),a=!0},p(c,d){const h={};d[0]&2048&&(h.i18n=c[11]),d[0]&33&&(h.download=c[5]?c[0].url:null),e.$set(h);let g=i;i=u(c),i===g?~i&&_[i].p(c,d):(o&&(_t(),X(_[g],1,1,()=>{_[g]=null}),ut()),~i?(o=_[i],o?o.p(c,d):(o=_[i]=r[i](c),o.c()),H(o,1),o.m(s.parentNode,s)):o=null)},i(c){a||(H(e.$$.fragment,c),H(o),a=!0)},o(c){X(e.$$.fragment,c),X(o),a=!1},d(c){c&&(_e(n),_e(s)),Ie(e,c),~i&&_[i].d(c)}}}function Fl(l){let e,n,t,i;const o=[Jl,Wl],s=[];function a(r,_){return r[1]==="upload"?0:r[1]==="webcam"?1:-1}return~(n=a(l))&&(t=s[n]=o[n](l)),{c(){e=lt("div"),t&&t.c(),je(e,"class","upload-container svelte-1cs6pot")},m(r,_){fe(r,e,_),~n&&s[n].m(e,null),i=!0},p(r,_){let u=n;n=a(r),n===u?~n&&s[n].p(r,_):(t&&(_t(),X(s[u],1,1,()=>{s[u]=null}),ut()),~n?(t=s[n],t?t.p(r,_):(t=s[n]=o[n](r),t.c()),H(t,1),t.m(e,null)):t=null)},i(r){i||(H(t),i=!0)},o(r){X(t),i=!1},d(r){r&&_e(e),~n&&s[n].d()}}}function jl(l){let e,n=(l[0].orig_name||l[0].url)+"",t,i,o,s=St(l[0].size)+"",a;return{c(){e=lt("div"),t=Ot(n),i=it(),o=lt("div"),a=Ot(s),je(e,"class","file-name svelte-1cs6pot"),je(o,"class","file-size svelte-1cs6pot")},m(r,_){fe(r,e,_),bt(e,t),fe(r,i,_),fe(r,o,_),bt(o,a)},p(r,_){_[0]&1&&n!==(n=(r[0].orig_name||r[0].url)+"")&&jt(t,n),_[0]&1&&s!==(s=St(r[0].size)+"")&&jt(a,s)},i:wt,o:wt,d(r){r&&(_e(e),_e(i),_e(o))}}}function Ol(l){let e=l[0]?.url,n,t,i=Wt(l);return{c(){i.c(),n=sn()},m(o,s){i.m(o,s),fe(o,n,s),t=!0},p(o,s){s[0]&1&&rn(e,e=o[0]?.url)?(_t(),X(i,1,1,wt),ut(),i=Wt(o),i.c(),H(i,1),i.m(n.parentNode,n)):i.p(o,s)},i(o){t||(H(i),t=!0)},o(o){X(i),t=!1},d(o){o&&_e(n),i.d(o)}}}function Wt(l){let e,n;return e=new tn({props:{upload:l[14],root:l[10],interactive:!0,autoplay:l[9],src:l[0].url,subtitle:l[2]?.url,mirror:l[7]&&l[1]==="webcam",label:l[4],handle_change:l[20],handle_reset_value:l[12]}}),e.$on("play",l[28]),e.$on("pause",l[29]),e.$on("stop",l[30]),e.$on("end",l[31]),{c(){Me(e.$$.fragment)},m(t,i){Pe(e,t,i),n=!0},p(t,i){const o={};i[0]&16384&&(o.upload=t[14]),i[0]&1024&&(o.root=t[10]),i[0]&512&&(o.autoplay=t[9]),i[0]&1&&(o.src=t[0].url),i[0]&4&&(o.subtitle=t[2]?.url),i[0]&130&&(o.mirror=t[7]&&t[1]==="webcam"),i[0]&16&&(o.label=t[4]),i[0]&4096&&(o.handle_reset_value=t[12]),e.$set(o)},i(t){n||(H(e.$$.fragment,t),n=!0)},o(t){X(e.$$.fragment,t),n=!1},d(t){Ie(e,t)}}}function Wl(l){let e,n;return e=new yn({props:{root:l[10],mirror_webcam:l[7],include_audio:l[8],mode:"video",i18n:l[11],upload:l[14]}}),e.$on("error",l[25]),e.$on("capture",l[21]),e.$on("start_recording",l[26]),e.$on("stop_recording",l[27]),{c(){Me(e.$$.fragment)},m(t,i){Pe(e,t,i),n=!0},p(t,i){const o={};i[0]&1024&&(o.root=t[10]),i[0]&128&&(o.mirror_webcam=t[7]),i[0]&256&&(o.include_audio=t[8]),i[0]&2048&&(o.i18n=t[11]),i[0]&16384&&(o.upload=t[14]),e.$set(o)},i(t){n||(H(e.$$.fragment,t),n=!0)},o(t){X(e.$$.fragment,t),n=!1},d(t){Ie(e,t)}}}function Jl(l){let e,n,t;function i(s){l[23](s)}let o={filetype:"video/x-m4v,video/*",max_file_size:l[13],root:l[10],upload:l[14],stream_handler:l[15],$$slots:{default:[Gl]},$$scope:{ctx:l}};return l[16]!==void 0&&(o.dragging=l[16]),e=new kn({props:o}),on.push(()=>ln(e,"dragging",i)),e.$on("load",l[18]),e.$on("error",l[24]),{c(){Me(e.$$.fragment)},m(s,a){Pe(e,s,a),t=!0},p(s,a){const r={};a[0]&8192&&(r.max_file_size=s[13]),a[0]&1024&&(r.root=s[10]),a[0]&16384&&(r.upload=s[14]),a[0]&32768&&(r.stream_handler=s[15]),a[1]&4&&(r.$$scope={dirty:a,ctx:s}),!n&&a[0]&65536&&(n=!0,r.dragging=s[16],nn(()=>n=!1)),e.$set(r)},i(s){t||(H(e.$$.fragment,s),t=!0)},o(s){X(e.$$.fragment,s),t=!1},d(s){Ie(e,s)}}}function Gl(l){let e;const n=l[22].default,t=Il(n,l,l[33],null);return{c(){t&&t.c()},m(i,o){t&&t.m(i,o),e=!0},p(i,o){t&&t.p&&(!e||o[1]&4)&&Al(t,n,i,i[33],e?Nl(n,i[33],o,null):Pl(i[33]),null)},i(i){e||(H(t,i),e=!0)},o(i){X(t,i),e=!1},d(i){t&&t.d(i)}}}function Kl(l){let e,n,t,i,o,s,a,r,_;e=new Zt({props:{show_label:l[6],Icon:pt,label:l[4]||"Video"}});const u=[Fl,Xl],c=[];function d(m,k){return m[0]===null||m[0].url===void 0?0:1}i=d(l),o=c[i]=u[i](l);function h(m){l[32](m)}let g={sources:l[3],handle_clear:l[19]};return l[1]!==void 0&&(g.active_source=l[1]),a=new Tn({props:g}),on.push(()=>ln(a,"active_source",h)),{c(){Me(e.$$.fragment),n=it(),t=lt("div"),o.c(),s=it(),Me(a.$$.fragment),je(t,"data-testid","video"),je(t,"class","video-container svelte-1cs6pot")},m(m,k){Pe(e,m,k),fe(m,n,k),fe(m,t,k),c[i].m(t,null),bt(t,s),Pe(a,t,null),_=!0},p(m,k){const E={};k[0]&64&&(E.show_label=m[6]),k[0]&16&&(E.label=m[4]||"Video"),e.$set(E);let D=i;i=d(m),i===D?c[i].p(m,k):(_t(),X(c[D],1,1,()=>{c[D]=null}),ut(),o=c[i],o?o.p(m,k):(o=c[i]=u[i](m),o.c()),H(o,1),o.m(t,s));const q={};k[0]&8&&(q.sources=m[3]),!r&&k[0]&2&&(r=!0,q.active_source=m[1],nn(()=>r=!1)),a.$set(q)},i(m){_||(H(e.$$.fragment,m),H(o),H(a.$$.fragment,m),_=!0)},o(m){X(e.$$.fragment,m),X(o),X(a.$$.fragment,m),_=!1},d(m){m&&(_e(n),_e(t)),Ie(e,m),c[i].d(),Ie(a)}}}function Ql(l,e,n){let{$$slots:t={},$$scope:i}=e,{value:o=null}=e,{subtitle:s=null}=e,{sources:a=["webcam","upload"]}=e,{label:r=void 0}=e,{show_download_button:_=!1}=e,{show_label:u=!0}=e,{mirror_webcam:c=!1}=e,{include_audio:d}=e,{autoplay:h}=e,{root:g}=e,{i18n:m}=e,{active_source:k="webcam"}=e,{handle_reset_value:E=()=>{}}=e,{max_file_size:D=null}=e,{upload:q}=e,{stream_handler:L}=e;const $=Hl();function f({detail:b}){n(0,o=b),$("change",b),$("upload",b)}function y(){n(0,o=null),$("change",null),$("clear")}function M(b){$("change",b)}function T({detail:b}){$("change",b)}let S=!1;function W(b){S=b,n(16,S)}const B=({detail:b})=>$("error",b);function R(b){we.call(this,l,b)}function Z(b){we.call(this,l,b)}function me(b){we.call(this,l,b)}function w(b){we.call(this,l,b)}function J(b){we.call(this,l,b)}function G(b){we.call(this,l,b)}function oe(b){we.call(this,l,b)}function se(b){k=b,n(1,k)}return l.$$set=b=>{"value"in b&&n(0,o=b.value),"subtitle"in b&&n(2,s=b.subtitle),"sources"in b&&n(3,a=b.sources),"label"in b&&n(4,r=b.label),"show_download_button"in b&&n(5,_=b.show_download_button),"show_label"in b&&n(6,u=b.show_label),"mirror_webcam"in b&&n(7,c=b.mirror_webcam),"include_audio"in b&&n(8,d=b.include_audio),"autoplay"in b&&n(9,h=b.autoplay),"root"in b&&n(10,g=b.root),"i18n"in b&&n(11,m=b.i18n),"active_source"in b&&n(1,k=b.active_source),"handle_reset_value"in b&&n(12,E=b.handle_reset_value),"max_file_size"in b&&n(13,D=b.max_file_size),"upload"in b&&n(14,q=b.upload),"stream_handler"in b&&n(15,L=b.stream_handler),"$$scope"in b&&n(33,i=b.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&65536&&$("drag",S)},[o,k,s,a,r,_,u,c,d,h,g,m,E,D,q,L,S,$,f,y,M,T,t,W,B,R,Z,me,w,J,G,oe,se,i]}class Yl extends Ml{constructor(e){super(),Ul(this,e,Ql,Kl,rn,{value:0,subtitle:2,sources:3,label:4,show_download_button:5,show_label:6,mirror_webcam:7,include_audio:8,autoplay:9,root:10,i18n:11,active_source:1,handle_reset_value:12,max_file_size:13,upload:14,stream_handler:15},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),N()}get subtitle(){return this.$$.ctx[2]}set subtitle(e){this.$$set({subtitle:e}),N()}get sources(){return this.$$.ctx[3]}set sources(e){this.$$set({sources:e}),N()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),N()}get show_download_button(){return this.$$.ctx[5]}set show_download_button(e){this.$$set({show_download_button:e}),N()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),N()}get mirror_webcam(){return this.$$.ctx[7]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),N()}get include_audio(){return this.$$.ctx[8]}set include_audio(e){this.$$set({include_audio:e}),N()}get autoplay(){return this.$$.ctx[9]}set autoplay(e){this.$$set({autoplay:e}),N()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),N()}get i18n(){return this.$$.ctx[11]}set i18n(e){this.$$set({i18n:e}),N()}get active_source(){return this.$$.ctx[1]}set active_source(e){this.$$set({active_source:e}),N()}get handle_reset_value(){return this.$$.ctx[12]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),N()}get max_file_size(){return this.$$.ctx[13]}set max_file_size(e){this.$$set({max_file_size:e}),N()}get upload(){return this.$$.ctx[14]}set upload(e){this.$$set({upload:e}),N()}get stream_handler(){return this.$$.ctx[15]}set stream_handler(e){this.$$set({stream_handler:e}),N()}}const Zl=Yl,{SvelteComponent:xl,append:ei,attr:Jt,bubble:De,check_outros:et,create_component:Ee,destroy_component:Te,detach:ot,element:ti,empty:ni,flush:ae,group_outros:tt,init:li,insert:st,mount_component:ye,noop:an,safe_not_equal:un,space:vt,transition_in:P,transition_out:A}=window.__gradio__svelte__internal,{createEventDispatcher:ii,afterUpdate:oi,tick:si}=window.__gradio__svelte__internal;function ri(l){let e=l[0].url,n,t,i,o,s=Gt(l),a=l[6]&&Kt(l),r=l[5]&&Qt(l);return{c(){s.c(),n=vt(),t=ti("div"),a&&a.c(),i=vt(),r&&r.c(),Jt(t,"class","icon-buttons svelte-rvdo70"),Jt(t,"data-testid","download-div")},m(_,u){s.m(_,u),st(_,n,u),st(_,t,u),a&&a.m(t,null),ei(t,i),r&&r.m(t,null),o=!0},p(_,u){u&1&&un(e,e=_[0].url)?(tt(),A(s,1,1,an),et(),s=Gt(_),s.c(),P(s,1),s.m(n.parentNode,n)):s.p(_,u),_[6]?a?(a.p(_,u),u&64&&P(a,1)):(a=Kt(_),a.c(),P(a,1),a.m(t,i)):a&&(tt(),A(a,1,1,()=>{a=null}),et()),_[5]?r?(r.p(_,u),u&32&&P(r,1)):(r=Qt(_),r.c(),P(r,1),r.m(t,null)):r&&(tt(),A(r,1,1,()=>{r=null}),et())},i(_){o||(P(s),P(a),P(r),o=!0)},o(_){A(s),A(a),A(r),o=!1},d(_){_&&(ot(n),ot(t)),s.d(_),a&&a.d(),r&&r.d()}}}function ai(l){let e,n;return e=new Mn({props:{unpadded_box:!0,size:"large",$$slots:{default:[_i]},$$scope:{ctx:l}}}),{c(){Ee(e.$$.fragment)},m(t,i){ye(e,t,i),n=!0},p(t,i){const o={};i&524288&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(P(e.$$.fragment,t),n=!0)},o(t){A(e.$$.fragment,t),n=!1},d(t){Te(e,t)}}}function Gt(l){let e,n;return e=new tn({props:{src:l[0].url,subtitle:l[1]?.url,autoplay:l[4],mirror:!1,label:l[2],interactive:!1,upload:l[8]}}),e.$on("play",l[9]),e.$on("pause",l[10]),e.$on("stop",l[11]),e.$on("end",l[12]),{c(){Ee(e.$$.fragment)},m(t,i){ye(e,t,i),n=!0},p(t,i){const o={};i&1&&(o.src=t[0].url),i&2&&(o.subtitle=t[1]?.url),i&16&&(o.autoplay=t[4]),i&4&&(o.label=t[2]),i&256&&(o.upload=t[8]),e.$set(o)},i(t){n||(P(e.$$.fragment,t),n=!0)},o(t){A(e.$$.fragment,t),n=!1},d(t){Te(e,t)}}}function Kt(l){let e,n;return e=new Pn({props:{href:l[0].url,download:l[0].orig_name||l[0].path,$$slots:{default:[ui]},$$scope:{ctx:l}}}),{c(){Ee(e.$$.fragment)},m(t,i){ye(e,t,i),n=!0},p(t,i){const o={};i&1&&(o.href=t[0].url),i&1&&(o.download=t[0].orig_name||t[0].path),i&524288&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(P(e.$$.fragment,t),n=!0)},o(t){A(e.$$.fragment,t),n=!1},d(t){Te(e,t)}}}function ui(l){let e,n;return e=new En({props:{Icon:Nn,label:"Download"}}),{c(){Ee(e.$$.fragment)},m(t,i){ye(e,t,i),n=!0},p:an,i(t){n||(P(e.$$.fragment,t),n=!0)},o(t){A(e.$$.fragment,t),n=!1},d(t){Te(e,t)}}}function Qt(l){let e,n;return e=new In({props:{i18n:l[7],value:l[0],formatter:l[13]}}),e.$on("error",l[14]),e.$on("share",l[15]),{c(){Ee(e.$$.fragment)},m(t,i){ye(e,t,i),n=!0},p(t,i){const o={};i&128&&(o.i18n=t[7]),i&1&&(o.value=t[0]),e.$set(o)},i(t){n||(P(e.$$.fragment,t),n=!0)},o(t){A(e.$$.fragment,t),n=!1},d(t){Te(e,t)}}}function _i(l){let e,n;return e=new pt({}),{c(){Ee(e.$$.fragment)},m(t,i){ye(e,t,i),n=!0},i(t){n||(P(e.$$.fragment,t),n=!0)},o(t){A(e.$$.fragment,t),n=!1},d(t){Te(e,t)}}}function fi(l){let e,n,t,i,o,s;e=new Zt({props:{show_label:l[3],Icon:pt,label:l[2]||"Video"}});const a=[ai,ri],r=[];function _(u,c){return u[0]===null||u[0].url===void 0?0:1}return t=_(l),i=r[t]=a[t](l),{c(){Ee(e.$$.fragment),n=vt(),i.c(),o=ni()},m(u,c){ye(e,u,c),st(u,n,c),r[t].m(u,c),st(u,o,c),s=!0},p(u,[c]){const d={};c&8&&(d.show_label=u[3]),c&4&&(d.label=u[2]||"Video"),e.$set(d);let h=t;t=_(u),t===h?r[t].p(u,c):(tt(),A(r[h],1,1,()=>{r[h]=null}),et(),i=r[t],i?i.p(u,c):(i=r[t]=a[t](u),i.c()),P(i,1),i.m(o.parentNode,o))},i(u){s||(P(e.$$.fragment,u),P(i),s=!0)},o(u){A(e.$$.fragment,u),A(i),s=!1},d(u){u&&(ot(n),ot(o)),Te(e,u),r[t].d(u)}}}function ci(l,e,n){let{value:t=null}=e,{subtitle:i=null}=e,{label:o=void 0}=e,{show_label:s=!0}=e,{autoplay:a}=e,{show_share_button:r=!0}=e,{show_download_button:_=!0}=e,{i18n:u}=e,{upload:c}=e,d=null,h=null;const g=ii();oi(async()=>{t!==d&&i!==h&&h!==null&&(d=t,n(0,t=null),await si(),n(0,t=d)),d=t,h=i});function m(f){De.call(this,l,f)}function k(f){De.call(this,l,f)}function E(f){De.call(this,l,f)}function D(f){De.call(this,l,f)}const q=async f=>f?await Bn(f.data,"url"):"";function L(f){De.call(this,l,f)}function $(f){De.call(this,l,f)}return l.$$set=f=>{"value"in f&&n(0,t=f.value),"subtitle"in f&&n(1,i=f.subtitle),"label"in f&&n(2,o=f.label),"show_label"in f&&n(3,s=f.show_label),"autoplay"in f&&n(4,a=f.autoplay),"show_share_button"in f&&n(5,r=f.show_share_button),"show_download_button"in f&&n(6,_=f.show_download_button),"i18n"in f&&n(7,u=f.i18n),"upload"in f&&n(8,c=f.upload)},l.$$.update=()=>{l.$$.dirty&1&&t&&g("change",t)},[t,i,o,s,a,r,_,u,c,m,k,E,D,q,L,$]}class di extends xl{constructor(e){super(),li(this,e,ci,fi,un,{value:0,subtitle:1,label:2,show_label:3,autoplay:4,show_share_button:5,show_download_button:6,i18n:7,upload:8})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),ae()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),ae()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),ae()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),ae()}get autoplay(){return this.$$.ctx[4]}set autoplay(e){this.$$set({autoplay:e}),ae()}get show_share_button(){return this.$$.ctx[5]}set show_share_button(e){this.$$set({show_share_button:e}),ae()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),ae()}get i18n(){return this.$$.ctx[7]}set i18n(e){this.$$set({i18n:e}),ae()}get upload(){return this.$$.ctx[8]}set upload(e){this.$$set({upload:e}),ae()}}const mi=di,{SvelteComponent:hi,assign:_n,check_outros:gi,create_component:pe,destroy_component:ke,detach:Tt,empty:bi,flush:C,get_spread_object:fn,get_spread_update:cn,group_outros:wi,init:vi,insert:yt,mount_component:$e,safe_not_equal:pi,space:dn,transition_in:ce,transition_out:de}=window.__gradio__svelte__internal;function ki(l){let e,n;return e=new en({props:{visible:l[4],variant:l[0]===null&&l[21]==="upload"?"dashed":"solid",border_mode:l[24]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[11],scale:l[12],min_width:l[13],allow_overflow:!1,$$slots:{default:[Ti]},$$scope:{ctx:l}}}),{c(){pe(e.$$.fragment)},m(t,i){$e(e,t,i),n=!0},p(t,i){const o={};i[0]&16&&(o.visible=t[4]),i[0]&2097153&&(o.variant=t[0]===null&&t[21]==="upload"?"dashed":"solid"),i[0]&16777216&&(o.border_mode=t[24]?"focus":"base"),i[0]&4&&(o.elem_id=t[2]),i[0]&8&&(o.elem_classes=t[3]),i[0]&512&&(o.height=t[9]),i[0]&1024&&(o.width=t[10]),i[0]&2048&&(o.container=t[11]),i[0]&4096&&(o.scale=t[12]),i[0]&8192&&(o.min_width=t[13]),i[0]&33243618|i[1]&65536&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(ce(e.$$.fragment,t),n=!0)},o(t){de(e.$$.fragment,t),n=!1},d(t){ke(e,t)}}}function $i(l){let e,n;return e=new en({props:{visible:l[4],variant:l[0]===null&&l[21]==="upload"?"dashed":"solid",border_mode:l[24]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[11],scale:l[12],min_width:l[13],allow_overflow:!1,$$slots:{default:[yi]},$$scope:{ctx:l}}}),{c(){pe(e.$$.fragment)},m(t,i){$e(e,t,i),n=!0},p(t,i){const o={};i[0]&16&&(o.visible=t[4]),i[0]&2097153&&(o.variant=t[0]===null&&t[21]==="upload"?"dashed":"solid"),i[0]&16777216&&(o.border_mode=t[24]?"focus":"base"),i[0]&4&&(o.elem_id=t[2]),i[0]&8&&(o.elem_classes=t[3]),i[0]&512&&(o.height=t[9]),i[0]&1024&&(o.width=t[10]),i[0]&2048&&(o.container=t[11]),i[0]&4096&&(o.scale=t[12]),i[0]&8192&&(o.min_width=t[13]),i[0]&12828962|i[1]&65536&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(ce(e.$$.fragment,t),n=!0)},o(t){de(e.$$.fragment,t),n=!1},d(t){ke(e,t)}}}function Ei(l){let e,n;return e=new Un({props:{i18n:l[17].i18n,type:"video"}}),{c(){pe(e.$$.fragment)},m(t,i){$e(e,t,i),n=!0},p(t,i){const o={};i[0]&131072&&(o.i18n=t[17].i18n),e.$set(o)},i(t){n||(ce(e.$$.fragment,t),n=!0)},o(t){de(e.$$.fragment,t),n=!1},d(t){ke(e,t)}}}function Ti(l){let e,n,t,i;const o=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[1]];let s={};for(let a=0;a<o.length;a+=1)s=_n(s,o[a]);return e=new Yt({props:s}),e.$on("clear_status",l[37]),t=new Zl({props:{value:l[22],subtitle:l[23],label:l[5],show_label:l[8],show_download_button:l[16],sources:l[6],active_source:l[21],mirror_webcam:l[19],include_audio:l[20],autoplay:l[14],root:l[7],handle_reset_value:l[25],i18n:l[17].i18n,max_file_size:l[17].max_file_size,upload:l[17].client.upload,stream_handler:l[17].client.stream,$$slots:{default:[Ei]},$$scope:{ctx:l}}}),t.$on("change",l[26]),t.$on("drag",l[38]),t.$on("error",l[27]),t.$on("clear",l[39]),t.$on("play",l[40]),t.$on("pause",l[41]),t.$on("upload",l[42]),t.$on("stop",l[43]),t.$on("end",l[44]),t.$on("start_recording",l[45]),t.$on("stop_recording",l[46]),{c(){pe(e.$$.fragment),n=dn(),pe(t.$$.fragment)},m(a,r){$e(e,a,r),yt(a,n,r),$e(t,a,r),i=!0},p(a,r){const _=r[0]&131074?cn(o,[r[0]&131072&&{autoscroll:a[17].autoscroll},r[0]&131072&&{i18n:a[17].i18n},r[0]&2&&fn(a[1])]):{};e.$set(_);const u={};r[0]&4194304&&(u.value=a[22]),r[0]&8388608&&(u.subtitle=a[23]),r[0]&32&&(u.label=a[5]),r[0]&256&&(u.show_label=a[8]),r[0]&65536&&(u.show_download_button=a[16]),r[0]&64&&(u.sources=a[6]),r[0]&2097152&&(u.active_source=a[21]),r[0]&524288&&(u.mirror_webcam=a[19]),r[0]&1048576&&(u.include_audio=a[20]),r[0]&16384&&(u.autoplay=a[14]),r[0]&128&&(u.root=a[7]),r[0]&131072&&(u.i18n=a[17].i18n),r[0]&131072&&(u.max_file_size=a[17].max_file_size),r[0]&131072&&(u.upload=a[17].client.upload),r[0]&131072&&(u.stream_handler=a[17].client.stream),r[0]&131072|r[1]&65536&&(u.$$scope={dirty:r,ctx:a}),t.$set(u)},i(a){i||(ce(e.$$.fragment,a),ce(t.$$.fragment,a),i=!0)},o(a){de(e.$$.fragment,a),de(t.$$.fragment,a),i=!1},d(a){a&&Tt(n),ke(e,a),ke(t,a)}}}function yi(l){let e,n,t,i;const o=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[1]];let s={};for(let a=0;a<o.length;a+=1)s=_n(s,o[a]);return e=new Yt({props:s}),e.$on("clear_status",l[30]),t=new mi({props:{value:l[22],subtitle:l[23],label:l[5],show_label:l[8],autoplay:l[14],show_share_button:l[15],show_download_button:l[16],i18n:l[17].i18n,upload:l[17].client.upload}}),t.$on("play",l[31]),t.$on("pause",l[32]),t.$on("stop",l[33]),t.$on("end",l[34]),t.$on("share",l[35]),t.$on("error",l[36]),{c(){pe(e.$$.fragment),n=dn(),pe(t.$$.fragment)},m(a,r){$e(e,a,r),yt(a,n,r),$e(t,a,r),i=!0},p(a,r){const _=r[0]&131074?cn(o,[r[0]&131072&&{autoscroll:a[17].autoscroll},r[0]&131072&&{i18n:a[17].i18n},r[0]&2&&fn(a[1])]):{};e.$set(_);const u={};r[0]&4194304&&(u.value=a[22]),r[0]&8388608&&(u.subtitle=a[23]),r[0]&32&&(u.label=a[5]),r[0]&256&&(u.show_label=a[8]),r[0]&16384&&(u.autoplay=a[14]),r[0]&32768&&(u.show_share_button=a[15]),r[0]&65536&&(u.show_download_button=a[16]),r[0]&131072&&(u.i18n=a[17].i18n),r[0]&131072&&(u.upload=a[17].client.upload),t.$set(u)},i(a){i||(ce(e.$$.fragment,a),ce(t.$$.fragment,a),i=!0)},o(a){de(e.$$.fragment,a),de(t.$$.fragment,a),i=!1},d(a){a&&Tt(n),ke(e,a),ke(t,a)}}}function Si(l){let e,n,t,i;const o=[$i,ki],s=[];function a(r,_){return r[18]?1:0}return e=a(l),n=s[e]=o[e](l),{c(){n.c(),t=bi()},m(r,_){s[e].m(r,_),yt(r,t,_),i=!0},p(r,_){let u=e;e=a(r),e===u?s[e].p(r,_):(wi(),de(s[u],1,1,()=>{s[u]=null}),gi(),n=s[e],n?n.p(r,_):(n=s[e]=o[e](r),n.c()),ce(n,1),n.m(t.parentNode,t))},i(r){i||(ce(n),i=!0)},o(r){de(n),i=!1},d(r){r&&Tt(t),s[e].d(r)}}}function Vi(l,e,n){let{elem_id:t=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{value:s=null}=e,a=null,{label:r}=e,{sources:_}=e,{root:u}=e,{show_label:c}=e,{loading_status:d}=e,{height:h}=e,{width:g}=e,{container:m=!1}=e,{scale:k=null}=e,{min_width:E=void 0}=e,{autoplay:D=!1}=e,{show_share_button:q=!0}=e,{show_download_button:L}=e,{gradio:$}=e,{interactive:f}=e,{mirror_webcam:y}=e,{include_audio:M}=e,T=null,S=null,W,B=s;const R=()=>{B===null||s===B||n(0,s=B)};let Z=!1;function me({detail:v}){v!=null?n(0,s={video:v,subtitles:null}):n(0,s=null)}function w({detail:v}){const[vn,pn]=v.includes("Invalid file type")?["warning","complete"]:["error","error"];n(1,d=d||{}),n(1,d.status=pn,d),n(1,d.message=v,d),$.dispatch(vn,v)}const J=()=>$.dispatch("clear_status",d),G=()=>$.dispatch("play"),oe=()=>$.dispatch("pause"),se=()=>$.dispatch("stop"),b=()=>$.dispatch("end"),re=({detail:v})=>$.dispatch("share",v),Oe=({detail:v})=>$.dispatch("error",v),z=()=>$.dispatch("clear_status",d),p=({detail:v})=>n(24,Z=v),V=()=>$.dispatch("clear"),K=()=>$.dispatch("play"),Ne=()=>$.dispatch("pause"),mn=()=>$.dispatch("upload"),hn=()=>$.dispatch("stop"),gn=()=>$.dispatch("end"),bn=()=>$.dispatch("start_recording"),wn=()=>$.dispatch("stop_recording");return l.$$set=v=>{"elem_id"in v&&n(2,t=v.elem_id),"elem_classes"in v&&n(3,i=v.elem_classes),"visible"in v&&n(4,o=v.visible),"value"in v&&n(0,s=v.value),"label"in v&&n(5,r=v.label),"sources"in v&&n(6,_=v.sources),"root"in v&&n(7,u=v.root),"show_label"in v&&n(8,c=v.show_label),"loading_status"in v&&n(1,d=v.loading_status),"height"in v&&n(9,h=v.height),"width"in v&&n(10,g=v.width),"container"in v&&n(11,m=v.container),"scale"in v&&n(12,k=v.scale),"min_width"in v&&n(13,E=v.min_width),"autoplay"in v&&n(14,D=v.autoplay),"show_share_button"in v&&n(15,q=v.show_share_button),"show_download_button"in v&&n(16,L=v.show_download_button),"gradio"in v&&n(17,$=v.gradio),"interactive"in v&&n(18,f=v.interactive),"mirror_webcam"in v&&n(19,y=v.mirror_webcam),"include_audio"in v&&n(20,M=v.include_audio)},l.$$.update=()=>{l.$$.dirty[0]&536870913&&s&&B===null&&n(29,B=s),l.$$.dirty[0]&2097216&&_&&!W&&n(21,W=_[0]),l.$$.dirty[0]&1&&(s!=null?(n(22,T=s.video),n(23,S=s.subtitles)):(n(22,T=null),n(23,S=null))),l.$$.dirty[0]&268566529&&JSON.stringify(s)!==JSON.stringify(a)&&(n(28,a=s),$.dispatch("change"))},[s,d,t,i,o,r,_,u,c,h,g,m,k,E,D,q,L,$,f,y,M,W,T,S,Z,R,me,w,a,B,J,G,oe,se,b,re,Oe,z,p,V,K,Ne,mn,hn,gn,bn,wn]}class Di extends hi{constructor(e){super(),vi(this,e,Vi,Si,pi,{elem_id:2,elem_classes:3,visible:4,value:0,label:5,sources:6,root:7,show_label:8,loading_status:1,height:9,width:10,container:11,scale:12,min_width:13,autoplay:14,show_share_button:15,show_download_button:16,gradio:17,interactive:18,mirror_webcam:19,include_audio:20},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),C()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),C()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),C()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),C()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),C()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),C()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),C()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),C()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),C()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),C()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),C()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),C()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),C()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),C()}get autoplay(){return this.$$.ctx[14]}set autoplay(e){this.$$set({autoplay:e}),C()}get show_share_button(){return this.$$.ctx[15]}set show_share_button(e){this.$$set({show_share_button:e}),C()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),C()}get gradio(){return this.$$.ctx[17]}set gradio(e){this.$$set({gradio:e}),C()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),C()}get mirror_webcam(){return this.$$.ctx[19]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),C()}get include_audio(){return this.$$.ctx[20]}set include_audio(e){this.$$set({include_audio:e}),C()}}const xi=Di;export{io as BaseExample,Zl as BaseInteractiveVideo,tn as BasePlayer,mi as BaseStaticVideo,xi as default,no as loaded,qn as playable,St as prettyBytes};
//# sourceMappingURL=index-DN6G7dvw.js.map
