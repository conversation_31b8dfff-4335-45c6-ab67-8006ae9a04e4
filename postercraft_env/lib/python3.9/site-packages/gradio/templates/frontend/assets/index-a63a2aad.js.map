{"version": 3, "file": "index-a63a2aad.js", "sources": ["../../../../js/fileexplorer/interactive/InteractiveFileExplorer.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport DirectoryExplorer from \"../shared/DirectoryExplorer.svelte\";\n\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string[][];\n\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let loading_status: LoadingStatus;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let height: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t}>;\n\texport let file_count: \"single\" | \"multiple\" = \"multiple\";\n\texport let server: {\n\t\tls: (path: string[]) => Promise<[string[], string[]]>;\n\t};\n\n\t// $: value && gradio.dispatch(\"change\");\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\t{height}\n>\n\t<BlockLabel\n\t\t{show_label}\n\t\tIcon={File}\n\t\tlabel={label || \"FileExplorer\"}\n\t\tfloat={false}\n\t/>\n\t<StatusTracker {...loading_status} />\n\n\t<DirectoryExplorer\n\t\tbind:value\n\t\t{file_count}\n\t\t{server}\n\t\tmode=\"interactive\"\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t/>\n</Block>\n"], "names": ["File", "ctx", "dirty", "blocklabel_changes", "elem_id", "$$props", "elem_classes", "visible", "value", "label", "show_label", "loading_status", "container", "scale", "min_width", "height", "gradio", "file_count", "server"], "mappings": "0bAiDQA,EACC,MAAAC,MAAS,qBACT,eAEWA,EAAc,CAAA,CAAA,0aAHzBC,EAAA,KAAAC,EAAA,MAAAF,MAAS,8CAGEA,EAAc,CAAA,CAAA,CAAA,CAAA,8XAdxB,6fA1BE,GAAA,CAAA,QAAAG,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAiB,EAAAH,GAEjB,MAAAI,CAAa,EAAAJ,GACb,WAAAK,CAAmB,EAAAL,GACnB,eAAAM,CAA6B,EAAAN,EAC7B,CAAA,UAAAO,EAAY,EAAI,EAAAP,EAChB,CAAA,MAAAQ,EAAuB,IAAI,EAAAR,EAC3B,CAAA,UAAAS,EAAgC,MAAS,EAAAT,EACzC,CAAA,OAAAU,EAA6B,MAAS,EAAAV,GACtC,OAAAW,CAGT,EAAAX,EACS,CAAA,WAAAY,EAAoC,UAAU,EAAAZ,GAC9C,OAAAa,CAEV,EAAAb,uCA4BiBW,EAAO,SAAS,QAAQ"}