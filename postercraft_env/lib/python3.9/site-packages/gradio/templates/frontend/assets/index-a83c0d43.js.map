{"version": 3, "file": "index-a83c0d43.js", "sources": ["../../../../js/video/interactive/Video.svelte", "../../../../js/video/interactive/InteractiveVideo.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Webcam } from \"@gradio/image/interactive\";\n\timport { Video } from \"@gradio/icons\";\n\n\timport { prettyBytes, playable } from \"../shared/utils\";\n\timport Player from \"../shared/Player.svelte\";\n\n\texport let value: FileData | null = null;\n\texport let subtitle: FileData | null = null;\n\texport let source: string;\n\texport let label: string | undefined = undefined;\n\texport let show_label = true;\n\texport let mirror_webcam = false;\n\texport let include_audio: boolean;\n\texport let autoplay: boolean;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: any;\n\t\tclear: undefined;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tdrag: boolean;\n\t\terror: string;\n\t\tupload: FileData;\n\t\tstart_recording: undefined;\n\t\tstop_recording: undefined;\n\t}>();\n\n\tfunction handle_load({ detail }: CustomEvent<FileData | null>): void {\n\t\tdispatch(\"change\", detail);\n\t\tdispatch(\"upload\", detail!);\n\t\tvalue = detail;\n\t}\n\n\tfunction handle_clear({ detail }: CustomEvent<FileData | null>): void {\n\t\tvalue = null;\n\t\tdispatch(\"change\", detail);\n\t\tdispatch(\"clear\");\n\t}\n\n\tlet dragging = false;\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel {show_label} Icon={Video} label={label || \"Video\"} />\n{#if value === null}\n\t{#if source === \"upload\"}\n\t\t<Upload bind:dragging filetype=\"video/x-m4v,video/*\" on:load={handle_load}>\n\t\t\t<slot />\n\t\t</Upload>\n\t{:else if source === \"webcam\"}\n\t\t<Webcam\n\t\t\t{mirror_webcam}\n\t\t\t{include_audio}\n\t\t\tmode=\"video\"\n\t\t\ton:error\n\t\t\ton:capture={({ detail }) => dispatch(\"change\", detail)}\n\t\t\ton:start_recording\n\t\t\ton:stop_recording\n\t\t/>\n\t{/if}\n{:else}\n\t<ModifyUpload on:clear={handle_clear} />\n\t{#if playable()}\n\t\t{#key value?.data}\n\t\t\t<Player\n\t\t\t\t{autoplay}\n\t\t\t\tsrc={value.data}\n\t\t\t\tsubtitle={subtitle?.data}\n\t\t\t\ton:play\n\t\t\t\ton:pause\n\t\t\t\ton:stop\n\t\t\t\ton:end\n\t\t\t\tmirror={mirror_webcam && source === \"webcam\"}\n\t\t\t\t{label}\n\t\t\t/>\n\t\t{/key}\n\t{:else if value.size}\n\t\t<div class=\"file-name\">{value.name}</div>\n\t\t<div class=\"file-size\">\n\t\t\t{prettyBytes(value.size)}\n\t\t</div>\n\t{/if}\n{/if}\n\n<style>\n\t.file-name {\n\t\tpadding: var(--size-6);\n\t\tfont-size: var(--text-xxl);\n\t\tword-break: break-all;\n\t}\n\n\t.file-size {\n\t\tpadding: var(--size-2);\n\t\tfont-size: var(--text-xl);\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Video from \"./Video.svelte\";\n\timport { UploadText } from \"@gradio/atoms\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [FileData, FileData | null] | null = null;\n\tlet old_value: [FileData, FileData | null] | null = null;\n\n\texport let label: string;\n\texport let source: \"upload\" | \"webcam\";\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let show_label: boolean;\n\texport let loading_status: LoadingStatus;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\texport let mirror_webcam: boolean;\n\texport let include_audio: boolean;\n\texport let container = false;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let autoplay = false;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tupload: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tstart_recording: never;\n\t\tstop_recording: never;\n\t}>;\n\n\tlet _video: FileData | null = null;\n\tlet _subtitle: FileData | null = null;\n\n\t$: {\n\t\tif (value != null) {\n\t\t\t_video = normalise_file(value[0], root, root_url);\n\t\t\t_subtitle = normalise_file(value[1], root, root_url);\n\t\t} else {\n\t\t\t_video = null;\n\t\t\t_subtitle = null;\n\t\t}\n\t}\n\n\tlet dragging = false;\n\n\tfunction handle_change({ detail }: CustomEvent<FileData | null>): void {\n\t\tif (detail != null) {\n\t\t\tvalue = [detail, null] as [FileData, FileData | null];\n\t\t} else {\n\t\t\tvalue = null;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null && source === \"upload\" ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{height}\n\t{width}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker {...loading_status} />\n\n\t<Video\n\t\tvalue={_video}\n\t\tsubtitle={_subtitle}\n\t\ton:change={handle_change}\n\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\ton:error={({ detail }) => {\n\t\t\tloading_status = loading_status || {};\n\t\t\tloading_status.status = \"error\";\n\t\t\tloading_status.message = detail;\n\t\t}}\n\t\t{label}\n\t\t{show_label}\n\t\t{source}\n\t\t{mirror_webcam}\n\t\t{include_audio}\n\t\t{autoplay}\n\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\ton:end={() => gradio.dispatch(\"end\")}\n\t\ton:start_recording={() => gradio.dispatch(\"start_recording\")}\n\t\ton:stop_recording={() => gradio.dispatch(\"stop_recording\")}\n\t>\n\t\t<UploadText type=\"video\" />\n\t</Video>\n</Block>\n"], "names": ["ctx", "playable", "t0_value", "prettyBytes", "insert", "target", "div0", "anchor", "div1", "dirty", "set_data", "t0", "t2", "t2_value", "previous_key", "safe_not_equal", "player_changes", "Video", "blocklabel_changes", "value", "$$props", "subtitle", "source", "label", "show_label", "mirror_webcam", "include_audio", "autoplay", "dispatch", "createEventDispatcher", "handle_load", "detail", "$$invalidate", "handle_clear", "dragging", "capture_handler", "block_changes", "elem_id", "elem_classes", "visible", "old_value", "root", "root_url", "loading_status", "height", "width", "container", "scale", "min_width", "gradio", "_video", "_subtitle", "handle_change", "normalise_file"], "mappings": "u4BAmEyBA,EAAY,EAAA,CAAA,4DAC/BC,GAAQ,OAcHD,KAAM,KAAI,yeA/Bf,OAAAA,OAAW,SAAQ,EAIdA,OAAW,SAAQ,iWA4BJE,EAAAF,KAAM,KAAI,WAEhCG,EAAYH,EAAK,CAAA,EAAC,IAAI,EAAA,gJAFxBI,EAAwCC,EAAAC,EAAAC,CAAA,kBACxCH,EAEKC,EAAAG,EAAAD,CAAA,iBAHmBE,EAAA,GAAAP,KAAAA,EAAAF,KAAM,KAAI,KAAAU,EAAAC,EAAAT,CAAA,cAEhCC,EAAYH,EAAK,CAAA,EAAC,IAAI,EAAA,KAAAU,EAAAE,EAAAC,CAAA,qDAhBlB,IAAAC,EAAAd,MAAO,8EAAPS,EAAA,GAAAM,EAAAD,EAAAA,EAAAd,MAAO,IAAI,gMAGV,IAAAA,KAAM,KACD,SAAAA,MAAU,YAKZA,EAAa,CAAA,GAAIA,EAAM,CAAA,IAAK,+LAN/BS,EAAA,IAAAO,EAAA,IAAAhB,KAAM,MACDS,EAAA,IAAAO,EAAA,SAAAhB,MAAU,sBAKZA,EAAa,CAAA,GAAIA,EAAM,CAAA,IAAK,gwBA1BwBA,EAAW,EAAA,CAAA,mjBAH5CiB,GAAc,MAAAjB,MAAS,gDACjD,OAAAA,OAAU,KAAI,4KAD0BS,EAAA,IAAAS,EAAA,MAAAlB,MAAS,kUAtC1C,CAAA,MAAAmB,EAAyB,IAAI,EAAAC,EAC7B,CAAA,SAAAC,EAA4B,IAAI,EAAAD,GAChC,OAAAE,CAAc,EAAAF,EACd,CAAA,MAAAG,EAA4B,MAAS,EAAAH,EACrC,CAAA,WAAAI,EAAa,EAAI,EAAAJ,EACjB,CAAA,cAAAK,EAAgB,EAAK,EAAAL,GACrB,cAAAM,CAAsB,EAAAN,GACtB,SAAAO,CAAiB,EAAAP,EAEtB,MAAAQ,EAAWC,KAaR,SAAAC,GAAc,OAAAC,GAAM,CAC5BH,EAAS,SAAUG,CAAM,EACzBH,EAAS,SAAUG,CAAO,EAC1BC,EAAA,EAAAb,EAAQY,CAAM,EAGN,SAAAE,GAAe,OAAAF,GAAM,CAC7BC,EAAA,EAAAb,EAAQ,IAAI,EACZS,EAAS,SAAUG,CAAM,EACzBH,EAAS,OAAO,EAGb,IAAAM,EAAW,2DAgBE,MAAAC,EAAA,CAAA,CAAA,OAAAJ,CAAM,IAAOH,EAAS,SAAUG,CAAM,0iBAfpDH,EAAS,OAAQM,CAAQ,ibC6CTlC,EAAc,CAAA,CAAA,sGAGzBA,EAAM,EAAA,WACHA,EAAS,EAAA,yJACRA,EAAa,EAAA,CAAA,wVALNA,EAAc,CAAA,CAAA,CAAA,CAAA,gDAGzBA,EAAM,EAAA,4BACHA,EAAS,EAAA,saAhBX,QAAAA,EAAU,CAAA,IAAA,MAAQA,OAAW,SAAW,SAAW,oBAC/CA,EAAQ,EAAA,EAAG,QAAU,eACzB,oHAQO,sIAVPS,EAAA,CAAA,EAAA,KAAA2B,EAAA,QAAApC,EAAU,CAAA,IAAA,MAAQA,OAAW,SAAW,SAAW,qCAC/CA,EAAQ,EAAA,EAAG,QAAU,0WAlEvB,GAAA,CAAA,QAAAqC,EAAU,EAAE,EAAAjB,GACZ,aAAAkB,EAAY,EAAA,EAAAlB,EACZ,CAAA,QAAAmB,EAAU,EAAI,EAAAnB,EACd,CAAA,MAAAD,EAA4C,IAAI,EAAAC,EACvDoB,EAAgD,MAEzC,MAAAjB,CAAa,EAAAH,GACb,OAAAE,CAA2B,EAAAF,GAC3B,KAAAqB,CAAY,EAAArB,GACZ,SAAAsB,CAAuB,EAAAtB,GACvB,WAAAI,CAAmB,EAAAJ,GACnB,eAAAuB,CAA6B,EAAAvB,GAC7B,OAAAwB,CAA0B,EAAAxB,GAC1B,MAAAyB,CAAyB,EAAAzB,GACzB,cAAAK,CAAsB,EAAAL,GACtB,cAAAM,CAAsB,EAAAN,EACtB,CAAA,UAAA0B,EAAY,EAAK,EAAA1B,EACjB,CAAA,MAAA2B,EAAuB,IAAI,EAAA3B,EAC3B,CAAA,UAAA4B,EAAgC,MAAS,EAAA5B,EACzC,CAAA,SAAAO,EAAW,EAAK,EAAAP,GAChB,OAAA6B,CAUT,EAAA7B,EAEE8B,EAA0B,KAC1BC,EAA6B,KAY7BjB,EAAW,GAEN,SAAAkB,GAAgB,OAAArB,GAAM,CAC1BA,GAAU,SACbZ,EAAK,CAAIY,EAAQ,IAAI,CAAA,EAErBC,EAAA,EAAAb,EAAQ,IAAI,YAgCD,OAAAY,CAAM,IAAAC,EAAA,GAAQE,EAAWH,CAAM,OAC9B,OAAAA,KAAM,CAClBC,EAAA,EAAAW,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,MAC/BA,EAAe,QAAUZ,EAAMY,CAAA,UAQhBM,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,SACtBA,EAAO,SAAS,QAAQ,SAC1BA,EAAO,SAAS,MAAM,SACvBA,EAAO,SAAS,KAAK,SACTA,EAAO,SAAS,iBAAiB,SAClCA,EAAO,SAAS,gBAAgB,8sBAlErD9B,GAAS,WACZ+B,EAASG,EAAelC,EAAM,CAAC,EAAGsB,EAAMC,CAAQ,CAAA,OAChDS,EAAYE,EAAelC,EAAM,CAAC,EAAGsB,EAAMC,CAAQ,CAAA,IAEnDV,EAAA,GAAAkB,EAAS,IAAI,EACblB,EAAA,GAAAmB,EAAY,IAAI,2BAeb,KAAK,UAAUhC,CAAK,IAAM,KAAK,UAAUqB,CAAS,IACrDR,EAAA,GAAAQ,EAAYrB,CAAK,EACjB8B,EAAO,SAAS,QAAQ"}