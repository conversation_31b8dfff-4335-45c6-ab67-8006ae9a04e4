{"version": 3, "file": "index-f0ff32e9.js", "sources": ["../../../../js/highlightedtext/interactive/LabelInput.svelte", "../../../../js/highlightedtext/interactive/Highlightedtext.svelte", "../../../../js/highlightedtext/interactive/InteractiveHighlightedText.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\ttype HighlightedTextType = [string, string | number | null, symbol?];\n\n\texport let value: HighlightedTextType[];\n\texport let category: string | number | null;\n\texport let active: string;\n\texport let labelToEdit: number;\n\texport let indexOfLabel: number;\n\texport let text: string;\n\texport let handleValueChange: () => void;\n\texport let isScoresMode = false;\n\texport let _color_map: Record<string, { primary: string; secondary: string }>;\n\n\tlet _input_value = category;\n\n\tfunction handleInput(e: Event): void {\n\t\tlet target = e.target as HTMLInputElement;\n\t\tif (target) {\n\t\t\t_input_value = target.value;\n\t\t}\n\t}\n\n\tfunction updateLabelValue(\n\t\te: Event,\n\t\telementIndex: number,\n\t\ttext: string\n\t): void {\n\t\tlet target = e.target as HTMLInputElement;\n\t\tvalue = [\n\t\t\t...value.slice(0, elementIndex),\n\t\t\t[\n\t\t\t\ttext,\n\t\t\t\ttarget.value === \"\"\n\t\t\t\t\t? null\n\t\t\t\t\t: isScoresMode\n\t\t\t\t\t? Number(target.value)\n\t\t\t\t\t: target.value,\n\t\t\t],\n\t\t\t...value.slice(elementIndex + 1),\n\t\t];\n\n\t\thandleValueChange();\n\t}\n\n\tfunction clearPlaceHolderOnFocus(e: FocusEvent): void {\n\t\tlet target = e.target as HTMLInputElement;\n\t\tif (target && target.placeholder) target.placeholder = \"\";\n\t}\n</script>\n\n<!-- svelte-ignore a11y-autofocus -->\n<!-- autofocus should not be disorienting for a screen reader users\nas input is only rendered once a new label is created -->\n{#if !isScoresMode}\n\t<input\n\t\tclass=\"label-input\"\n\t\tautofocus\n\t\tid={`label-input-${indexOfLabel}`}\n\t\ttype=\"text\"\n\t\tplaceholder=\"label\"\n\t\tvalue={category}\n\t\tstyle:background-color={category === null || (active && active !== category)\n\t\t\t? \"\"\n\t\t\t: _color_map[category].primary}\n\t\tstyle:width={_input_value\n\t\t\t? _input_value.toString()?.length + 4 + \"ch\"\n\t\t\t: \"8ch\"}\n\t\ton:input={handleInput}\n\t\ton:blur={(e) => updateLabelValue(e, indexOfLabel, text)}\n\t\ton:keydown={(e) => {\n\t\t\tif (e.key === \"Enter\") {\n\t\t\t\tupdateLabelValue(e, indexOfLabel, text);\n\t\t\t\tlabelToEdit = -1;\n\t\t\t}\n\t\t}}\n\t\ton:focus={clearPlaceHolderOnFocus}\n\t/>\n{:else}\n\t<input\n\t\tclass=\"label-input\"\n\t\tautofocus\n\t\ttype=\"number\"\n\t\tstep=\"0.1\"\n\t\tstyle={\"background-color: rgba(\" +\n\t\t\t(typeof category === \"number\" && category < 0\n\t\t\t\t? \"128, 90, 213,\" + -category\n\t\t\t\t: \"239, 68, 60,\" + category) +\n\t\t\t\")\"}\n\t\tvalue={category}\n\t\tstyle:width=\"7ch\"\n\t\ton:input={handleInput}\n\t\ton:blur={(e) => updateLabelValue(e, indexOfLabel, text)}\n\t\ton:keydown={(e) => {\n\t\t\tif (e.key === \"Enter\") {\n\t\t\t\tupdateLabelValue(e, indexOfLabel, text);\n\t\t\t\tlabelToEdit = -1;\n\t\t\t}\n\t\t}}\n\t/>\n{/if}\n\n<style>\n\t.label-input {\n\t\ttransition: 150ms;\n\t\tmargin-top: 1px;\n\t\tmargin-right: calc(var(--size-1));\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding: 1px 5px;\n\t\tcolor: black;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-sm);\n\t\ttext-transform: uppercase;\n\t\tline-height: 1;\n\t\tcolor: white;\n\t}\n\n\t.label-input::placeholder {\n\t\tcolor: rgba(1, 1, 1, 0.5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\tconst browser = typeof document !== \"undefined\";\n\timport { get_next_color } from \"@gradio/utils\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { correct_color_map, merge_elements } from \"../utils\";\n\timport LabelInput from \"./LabelInput.svelte\";\n\n\texport let value: [string, string | number | null][] = [];\n\texport let show_legend = false;\n\texport let color_map: Record<string, string> = {};\n\texport let selectable = false;\n\n\tlet activeElementIndex = -1;\n\tlet ctx: CanvasRenderingContext2D;\n\tlet _color_map: Record<string, { primary: string; secondary: string }> = {};\n\tlet active = \"\";\n\tlet selection: Selection | null;\n\tlet labelToEdit = -1;\n\n\tonMount(() => {\n\t\tconst mouseUpHandler = (): void => {\n\t\t\tselection = window.getSelection();\n\t\t\thandleSelectionComplete();\n\t\t\twindow.removeEventListener(\"mouseup\", mouseUpHandler);\n\t\t};\n\n\t\twindow.addEventListener(\"mousedown\", () => {\n\t\t\twindow.addEventListener(\"mouseup\", mouseUpHandler);\n\t\t});\n\t});\n\n\tasync function handleTextSelected(\n\t\tstartIndex: number,\n\t\tendIndex: number\n\t): Promise<void> {\n\t\tif (\n\t\t\tselection?.toString() &&\n\t\t\tactiveElementIndex !== -1 &&\n\t\t\tvalue[activeElementIndex][0].toString().includes(selection.toString())\n\t\t) {\n\t\t\tconst tempFlag = Symbol();\n\n\t\t\tconst str = value[activeElementIndex][0];\n\t\t\tconst [before, selected, after] = [\n\t\t\t\tstr.substring(0, startIndex),\n\t\t\t\tstr.substring(startIndex, endIndex),\n\t\t\t\tstr.substring(endIndex),\n\t\t\t];\n\n\t\t\tlet tempValue: [string, string | number | null, symbol?][] = [\n\t\t\t\t...value.slice(0, activeElementIndex),\n\t\t\t\t[before, null],\n\t\t\t\t[selected, mode === \"scores\" ? 1 : \"label\", tempFlag], // add a temp flag to the new highlighted text element\n\t\t\t\t[after, null],\n\t\t\t\t...value.slice(activeElementIndex + 1),\n\t\t\t];\n\n\t\t\t// store the index of the new highlighted text element and remove the flag\n\t\t\tlabelToEdit = tempValue.findIndex(([_, __, flag]) => flag === tempFlag);\n\t\t\ttempValue[labelToEdit].pop();\n\n\t\t\t// remove elements with empty labels\n\t\t\ttempValue = tempValue.filter((item) => item[0].trim() !== \"\");\n\t\t\tvalue = tempValue as [string, string | number | null][];\n\n\t\t\thandleValueChange();\n\t\t\tdocument.getElementById(`label-input-${labelToEdit}`)?.focus();\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tselect: SelectData;\n\t\tchange: typeof value;\n\t\tinput: never;\n\t}>();\n\n\tfunction splitTextByNewline(text: string): string[] {\n\t\treturn text.split(\"\\n\");\n\t}\n\n\tfunction removeHighlightedText(index: number): void {\n\t\tif (index < 0 || index >= value.length) return;\n\t\tvalue[index][1] = null;\n\t\tvalue = merge_elements(value, \"equal\");\n\t\thandleValueChange();\n\t\twindow.getSelection()?.empty();\n\t}\n\n\tfunction handleValueChange(): void {\n\t\tdispatch(\"change\", value);\n\t\tlabelToEdit = -1;\n\n\t\t// reset legend color maps\n\t\tif (show_legend) {\n\t\t\tcolor_map = {};\n\t\t\t_color_map = {};\n\t\t}\n\t}\n\n\tlet mode: \"categories\" | \"scores\";\n\n\t$: {\n\t\tif (!color_map) {\n\t\t\tcolor_map = {};\n\t\t}\n\t\tif (value.length > 0) {\n\t\t\tfor (let [_, label] of value) {\n\t\t\t\tif (label !== null) {\n\t\t\t\t\tif (typeof label === \"string\") {\n\t\t\t\t\t\tmode = \"categories\";\n\t\t\t\t\t\tif (!(label in color_map)) {\n\t\t\t\t\t\t\tlet color = get_next_color(Object.keys(color_map).length);\n\t\t\t\t\t\t\tcolor_map[label] = color;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmode = \"scores\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tcorrect_color_map(color_map, _color_map, browser, ctx);\n\t}\n\n\tfunction handle_mouseover(label: string): void {\n\t\tactive = label;\n\t}\n\tfunction handle_mouseout(): void {\n\t\tactive = \"\";\n\t}\n\n\tasync function handleKeydownSelection(event: KeyboardEvent): Promise<void> {\n\t\tselection = window.getSelection();\n\n\t\tif (event.key === \"Enter\") {\n\t\t\thandleSelectionComplete();\n\t\t}\n\t}\n\n\tfunction handleSelectionComplete(): void {\n\t\tif (selection && selection?.toString().trim() !== \"\") {\n\t\t\tconst textBeginningIndex = selection.getRangeAt(0).startOffset;\n\t\t\tconst textEndIndex = selection.getRangeAt(0).endOffset;\n\t\t\thandleTextSelected(textBeginningIndex, textEndIndex);\n\t\t}\n\t}\n\n\tfunction handleSelect(\n\t\ti: number,\n\t\ttext: string,\n\t\tcategory: string | number | null\n\t): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: i,\n\t\t\tvalue: [text, category],\n\t\t});\n\t}\n</script>\n\n<div class=\"container\">\n\t{#if mode === \"categories\"}\n\t\t{#if show_legend}\n\t\t\t<div\n\t\t\t\tclass=\"category-legend\"\n\t\t\t\tdata-testid=\"highlighted-text:category-legend\"\n\t\t\t>\n\t\t\t\t{#if _color_map}\n\t\t\t\t\t{#each Object.entries(_color_map) as [category, color], i}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\taria-roledescription=\"Categories of highlighted text. Hover to see text with this category highlighted.\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ton:mouseover={() => handle_mouseover(category)}\n\t\t\t\t\t\t\ton:focus={() => handle_mouseover(category)}\n\t\t\t\t\t\t\ton:mouseout={() => handle_mouseout()}\n\t\t\t\t\t\t\ton:blur={() => handle_mouseout()}\n\t\t\t\t\t\t\tclass=\"category-label\"\n\t\t\t\t\t\t\tstyle={\"background-color:\" + color.secondary}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{category}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t{/if}\n\n\t\t<div class=\"textfield\">\n\t\t\t{#each value as [text, category], i}\n\t\t\t\t{#each splitTextByNewline(text) as line, j}\n\t\t\t\t\t{#if line.trim() !== \"\"}\n\t\t\t\t\t\t<span class=\"text-category-container\">\n\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\t\tclass=\"textspan\"\n\t\t\t\t\t\t\t\tstyle:background-color={category === null ||\n\t\t\t\t\t\t\t\t(active && active !== category)\n\t\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t\t: category && _color_map[category]\n\t\t\t\t\t\t\t\t\t? _color_map[category].secondary\n\t\t\t\t\t\t\t\t\t: \"\"}\n\t\t\t\t\t\t\t\tclass:no-cat={category === null ||\n\t\t\t\t\t\t\t\t\t(active && active !== category)}\n\t\t\t\t\t\t\t\tclass:hl={category !== null}\n\t\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\t\tif (category !== null) {\n\t\t\t\t\t\t\t\t\t\thandleSelect(i, text, category);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\t\t\tif (category !== null) {\n\t\t\t\t\t\t\t\t\t\tlabelToEdit = i;\n\t\t\t\t\t\t\t\t\t\thandleSelect(i, text, category);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\thandleKeydownSelection(e);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\ton:focus={() => (activeElementIndex = i)}\n\t\t\t\t\t\t\t\ton:mouseover={() => (activeElementIndex = i)}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass:no-label={category === null}\n\t\t\t\t\t\t\t\t\tclass=\"text\"\n\t\t\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\t\t\ton:keydown={(e) => handleKeydownSelection(e)}\n\t\t\t\t\t\t\t\t\ton:focus={() => (activeElementIndex = i)}\n\t\t\t\t\t\t\t\t\ton:mouseover={() => (activeElementIndex = i)}\n\t\t\t\t\t\t\t\t\ton:click={() => (labelToEdit = i)}\n\t\t\t\t\t\t\t\t\ttabindex=\"0\">{line}</span\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{#if !show_legend && category !== null && labelToEdit !== i}\n\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\tid={`label-tag-${i}`}\n\t\t\t\t\t\t\t\t\t\tclass=\"label\"\n\t\t\t\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\t\t\t\tstyle:background-color={category === null ||\n\t\t\t\t\t\t\t\t\t\t(active && active !== category)\n\t\t\t\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t\t\t\t: _color_map[category].primary}\n\t\t\t\t\t\t\t\t\t\ton:click={() => (labelToEdit = i)}\n\t\t\t\t\t\t\t\t\t\ton:keydown={() => (labelToEdit = i)}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t{category}\n\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if labelToEdit === i && category !== null}\n\t\t\t\t\t\t\t\t\t&nbsp;\n\t\t\t\t\t\t\t\t\t<LabelInput\n\t\t\t\t\t\t\t\t\t\tbind:value\n\t\t\t\t\t\t\t\t\t\t{labelToEdit}\n\t\t\t\t\t\t\t\t\t\t{category}\n\t\t\t\t\t\t\t\t\t\t{active}\n\t\t\t\t\t\t\t\t\t\t{_color_map}\n\t\t\t\t\t\t\t\t\t\tindexOfLabel={i}\n\t\t\t\t\t\t\t\t\t\t{text}\n\t\t\t\t\t\t\t\t\t\t{handleValueChange}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t{#if category !== null}\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"label-clear-button\"\n\t\t\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\t\t\taria-roledescription=\"Remove label from text\"\n\t\t\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\t\t\ton:click={() => removeHighlightedText(i)}\n\t\t\t\t\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\t\t\tremoveHighlightedText(i);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t>×\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#if j < splitTextByNewline(text).length - 1}\n\t\t\t\t\t\t<br />\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{/each}\n\t\t</div>\n\t{:else}\n\t\t{#if show_legend}\n\t\t\t<div class=\"color-legend\" data-testid=\"highlighted-text:color-legend\">\n\t\t\t\t<span>-1</span>\n\t\t\t\t<span>0</span>\n\t\t\t\t<span>+1</span>\n\t\t\t</div>\n\t\t{/if}\n\n\t\t<div class=\"textfield\" data-testid=\"highlighted-text:textfield\">\n\t\t\t{#each value as [text, _score], i}\n\t\t\t\t{@const score = typeof _score === \"string\" ? parseInt(_score) : _score}\n\t\t\t\t<span class=\"score-text-container\">\n\t\t\t\t\t<span\n\t\t\t\t\t\tclass=\"textspan score-text\"\n\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\tclass:no-cat={_score === null || (active && active !== _score)}\n\t\t\t\t\t\tclass:hl={_score !== null}\n\t\t\t\t\t\ton:mouseover={() => (activeElementIndex = i)}\n\t\t\t\t\t\ton:focus={() => (activeElementIndex = i)}\n\t\t\t\t\t\ton:click={() => (labelToEdit = i)}\n\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\tif (e.key === \"Enter\") {\n\t\t\t\t\t\t\t\tlabelToEdit = i;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}}\n\t\t\t\t\t\tstyle={\"background-color: rgba(\" +\n\t\t\t\t\t\t\t(score && score < 0\n\t\t\t\t\t\t\t\t? \"128, 90, 213,\" + -score\n\t\t\t\t\t\t\t\t: \"239, 68, 60,\" + score) +\n\t\t\t\t\t\t\t\")\"}\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"text\">{text}</span>\n\t\t\t\t\t\t{#if _score && labelToEdit === i}\n\t\t\t\t\t\t\t<LabelInput\n\t\t\t\t\t\t\t\tbind:value\n\t\t\t\t\t\t\t\t{labelToEdit}\n\t\t\t\t\t\t\t\t{_color_map}\n\t\t\t\t\t\t\t\tcategory={_score}\n\t\t\t\t\t\t\t\t{active}\n\t\t\t\t\t\t\t\tindexOfLabel={i}\n\t\t\t\t\t\t\t\t{text}\n\t\t\t\t\t\t\t\t{handleValueChange}\n\t\t\t\t\t\t\t\tisScoresMode\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</span>\n\t\t\t\t\t{#if _score && activeElementIndex === i}\n\t\t\t\t\t\t<span\n\t\t\t\t\t\t\tclass=\"label-clear-button\"\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\taria-roledescription=\"Remove label from text\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ton:click={() => removeHighlightedText(i)}\n\t\t\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremoveHighlightedText(i);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t>×\n\t\t\t\t\t\t</span>\n\t\t\t\t\t{/if}\n\t\t\t\t</span>\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.label-clear-button {\n\t\tdisplay: none;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-top: 2.5px;\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: 3.5px;\n\t\tpadding-left: var(--size-1);\n\t\tcolor: black;\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tuser-select: none;\n\t\tposition: relative;\n\t\tleft: -3px;\n\t\tborder-radius: 0 var(--radius-xs) var(--radius-xs) 0;\n\t\tcolor: var(--block-label-text-color);\n\t}\n\n\t.text-category-container:hover .label-clear-button,\n\t.text-category-container:focus-within .label-clear-button,\n\t.score-text-container:hover .label-clear-button,\n\t.score-text-container:focus-within .label-clear-button {\n\t\tdisplay: inline;\n\t}\n\n\t.text-category-container:hover .textspan.hl,\n\t.text-category-container:focus-within .textspan.hl,\n\t.score-text:hover {\n\t\tborder-radius: var(--radius-xs) 0 0 var(--radius-xs);\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-sm);\n\t\tpadding: var(--block-padding);\n\t}\n\n\t.hl {\n\t\tmargin-left: var(--size-1);\n\t\ttransition: background-color 0.3s;\n\t\tuser-select: none;\n\t}\n\n\t.textspan:last-child > .label {\n\t\tmargin-right: 0;\n\t}\n\n\t.category-legend {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--spacing-sm);\n\t\tcolor: black;\n\t}\n\n\t.category-label {\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-right: var(--size-2);\n\t\tpadding-left: var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.color-legend {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: linear-gradient(\n\t\t\tto right,\n\t\t\tvar(--color-purple),\n\t\t\trgba(255, 255, 255, 0),\n\t\t\tvar(--color-red)\n\t\t);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.textfield {\n\t\tbox-sizing: border-box;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: var(--background-fill-primary);\n\t\tbackground-color: transparent;\n\t\tmax-width: var(--size-full);\n\t\tline-height: var(--scale-4);\n\t\tword-break: break-all;\n\t}\n\n\t.textspan {\n\t\ttransition: 150ms;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-top: 2.5px;\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: 3.5px;\n\t\tpadding-left: var(--size-1);\n\t\tcolor: black;\n\t\tcursor: text;\n\t}\n\n\t.label {\n\t\ttransition: 150ms;\n\t\tmargin-top: 1px;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding: 1px 5px;\n\t\tcolor: var(--body-text-color);\n\t\tcolor: white;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-sm);\n\t\ttext-transform: uppercase;\n\t\tuser-select: none;\n\t}\n\n\t.text {\n\t\tcolor: black;\n\t\twhite-space: pre-wrap;\n\t}\n\n\t.textspan.hl {\n\t\tuser-select: none;\n\t}\n\n\t.score-text-container {\n\t\tmargin-right: var(--size-1);\n\t}\n\n\t.score-text .text {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.no-cat {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.no-label {\n\t\tcolor: var(--body-text-color);\n\t\tuser-select: text;\n\t}\n\n\t.selectable {\n\t\tcursor: text;\n\t\tuser-select: text;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport HighlightedText from \"./Highlightedtext.svelte\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { TextHighlight } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\timport { merge_elements } from \"../utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [string, string | number | null][];\n\texport let mode: \"static\" | \"interactive\";\n\texport let show_legend: boolean;\n\texport let color_map: Record<string, string> = {};\n\texport let label = $_(\"highlighted_text.highlighted_text\");\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let selectable = false;\n\texport let combine_adjacent = false;\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t\tchange: typeof value;\n\t\tinput: never;\n\t}>;\n\n\t$: if (!color_map && Object.keys(color_map).length) {\n\t\tcolor_map = color_map;\n\t}\n\n\texport let loading_status: LoadingStatus;\n\n\t$: if (value && combine_adjacent) {\n\t\tvalue = merge_elements(value, \"equal\");\n\t}\n</script>\n\n<Block\n\tvariant={mode === \"interactive\" ? \"dashed\" : \"solid\"}\n\ttest_id=\"highlighted-text\"\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={false}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\t{#if label}\n\t\t<BlockLabel\n\t\t\tIcon={TextHighlight}\n\t\t\t{label}\n\t\t\tfloat={false}\n\t\t\tdisable={container === false}\n\t\t/>\n\t{/if}\n\n\t{#if value}\n\t\t<HighlightedText\n\t\t\tbind:value\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\t{selectable}\n\t\t\t{show_legend}\n\t\t\t{color_map}\n\t\t/>\n\t{:else}\n\t\t<Empty>\n\t\t\t<TextHighlight />\n\t\t</Empty>\n\t{/if}\n</Block>\n"], "names": ["attr", "input", "input_style_value", "ctx", "insert", "target", "anchor", "dirty", "set_style", "clearPlaceHolderOnFocus", "create_if_block", "value", "$$props", "category", "active", "labelToEdit", "indexOfLabel", "text", "handleValueChange", "isScoresMode", "_color_map", "_input_value", "handleInput", "e", "updateLabelValue", "elementIndex", "blur_handler", "$$invalidate", "blur_handler_1", "constants_0", "child_ctx", "create_if_block_10", "i", "div", "each_blocks", "create_if_block_6", "span", "create_if_block_9", "create_if_block_8", "span1", "span1_style_value", "toggle_class", "span2", "append", "span0", "set_data", "t0", "t0_value", "current", "create_if_block_7", "if_block0", "create_if_block_5", "if_block1", "create_if_block_4", "if_block2", "create_if_block_3", "t", "t_value", "br", "show_if", "splitTextByNewline", "each_value_1", "ensure_array_like", "browser", "show_legend", "color_map", "selectable", "activeElementIndex", "selection", "onMount", "mouseUpHandler", "handleSelectionComplete", "handleTextSelected", "startIndex", "endIndex", "tempFlag", "str", "before", "selected", "after", "tempValue", "mode", "_", "__", "flag", "item", "dispatch", "createEventDispatcher", "removeHighlightedText", "index", "merge_elements", "handle_mouseover", "label", "handle_mouseout", "handleKeydownSelection", "event", "textBeginningIndex", "textEndIndex", "handleSelect", "mouseover_handler", "focus_handler", "focus_handler_1", "mouseover_handler_1", "click_handler", "click_handler_1", "keydown_handler_1", "focus_handler_2", "mouseover_handler_2", "click_handler_3", "mouseover_handler_3", "focus_handler_3", "click_handler_4", "click_handler_5", "color", "get_next_color", "correct_color_map", "TextHighlight", "blocklabel_changes", "create_if_block_1", "block_changes", "elem_id", "elem_classes", "visible", "$_", "container", "scale", "min_width", "combine_adjacent", "gradio", "loading_status"], "mappings": "qqBAmFSA,EAAAC,EAAA,QAAAC,EAAA,kCACEC,EAAQ,CAAA,GAAK,UAAYA,EAAW,CAAA,EAAA,EACzC,gBAAmB,CAAAA,EAAA,CAAA,EACnB,eAAiBA,EAAQ,CAAA,GAC5B,GAAG,UACGA,EAAQ,CAAA,6BAVhBC,EAoBCC,EAAAJ,EAAAK,CAAA,+BARUH,EAAW,CAAA,CAAA,wDAPdI,EAAA,GAAAL,KAAAA,EAAA,kCACEC,EAAQ,CAAA,GAAK,UAAYA,EAAW,CAAA,EAAA,EACzC,gBAAmB,CAAAA,EAAA,CAAA,EACnB,eAAiBA,EAAQ,CAAA,GAC5B,oCACMA,EAAQ,CAAA,YAARA,EAAQ,CAAA,2LA/BIA,EAAY,CAAA,GAAA,wDAGxBA,EAAQ,CAAA,EACSK,EAAAP,EAAA,mBAAAE,OAAa,MAASA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,CAAA,EACxE,GACAA,EAAU,CAAA,EAACA,EAAQ,CAAA,CAAA,EAAE,OAAO,EAClBK,EAAAP,EAAA,QAAAE,EAAA,CAAA,EACVA,KAAa,SAAQ,GAAI,OAAS,EAAI,KACtC,KAAK,UAZTC,EAsBCC,EAAAJ,EAAAK,CAAA,+BATUH,EAAW,CAAA,CAAA,qDAQXM,EAAuB,4CAlBdN,EAAY,CAAA,iCAGxBA,EAAQ,CAAA,YAARA,EAAQ,CAAA,SACSK,EAAAP,EAAA,mBAAAE,OAAa,MAASA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,CAAA,EACxE,GACAA,EAAU,CAAA,EAACA,EAAQ,CAAA,CAAA,EAAE,OAAO,SAClBK,EAAAP,EAAA,QAAAE,EAAA,CAAA,EACVA,KAAa,SAAQ,GAAI,OAAS,EAAI,KACtC,KAAK,wEAbJA,EAAY,CAAA,KAAAO,yLATR,SAAAD,GAAwB,EAAa,KACzCJ,EAAS,EAAE,OACXA,GAAUA,EAAO,cAAaA,EAAO,YAAc,2BA3C7C,MAAAM,CAA4B,EAAAC,GAC5B,SAAAC,CAAgC,EAAAD,GAChC,OAAAE,CAAc,EAAAF,GACd,YAAAG,CAAmB,EAAAH,GACnB,aAAAI,CAAoB,EAAAJ,GACpB,KAAAK,CAAY,EAAAL,GACZ,kBAAAM,CAA6B,EAAAN,EAC7B,CAAA,aAAAO,EAAe,EAAK,EAAAP,GACpB,WAAAQ,CAAkE,EAAAR,EAEzES,EAAeR,EAEV,SAAAS,EAAYC,EAAQ,KACxBlB,EAASkB,EAAE,OACXlB,OACHgB,EAAehB,EAAO,KAAK,EAIpB,SAAAmB,EACRD,EACAE,EACAR,EAAY,KAERZ,EAASkB,EAAE,YACfZ,EAAK,CACD,GAAAA,EAAM,MAAM,EAAGc,CAAY,GAE7BR,EACAZ,EAAO,QAAU,GACd,KACAc,EACA,OAAOd,EAAO,KAAK,EACnBA,EAAO,OAER,GAAAM,EAAM,MAAMc,EAAe,CAAC,IAGhCP,IA2BU,MAAAQ,EAAAH,GAAMC,EAAiBD,EAAGP,EAAcC,CAAI,IACzCM,GAAC,CACTA,EAAE,MAAQ,UACbC,EAAiBD,EAAGP,EAAcC,CAAI,EACtCU,EAAA,EAAAZ,IAAgB,IAmBRa,EAAAL,GAAMC,EAAiBD,EAAGP,EAAcC,CAAI,IACzCM,GAAC,CACTA,EAAE,MAAQ,UACbC,EAAiBD,EAAGP,EAAcC,CAAI,EACtCU,EAAA,EAAAZ,IAAgB,moBCyMO,MAAAc,EAAA,OAAAC,OAAW,SAAW,SAASA,EAAM,EAAA,CAAA,EAAIA,EAAM,EAAA,mRAVnE3B,EAAW,CAAA,GAAA4B,GAAA,MASR5B,EAAK,CAAA,CAAA,uBAAV,OAAI6B,GAAA,2PADP5B,EAwDKC,EAAA4B,EAAA3B,CAAA,mEAhEAH,EAAW,CAAA,yEASRA,EAAK,CAAA,CAAA,oBAAV,OAAI6B,GAAA,EAAA,2GAAJ,OAAIA,EAAAE,EAAA,OAAAF,GAAA,yCAAJ,OAAIA,GAAA,sJArIF7B,EAAW,CAAA,GAAAgC,GAAAhC,CAAA,MA0BRA,EAAK,CAAA,CAAA,uBAAV,OAAI6B,GAAA,2MADP5B,EAiGKC,EAAA4B,EAAA3B,CAAA,mEA1HAH,EAAW,CAAA,oFA0BRA,EAAK,CAAA,CAAA,oBAAV,OAAI6B,GAAA,EAAA,2GAAJ,OAAIA,EAAAE,EAAA,OAAAF,GAAA,yCAAJ,OAAIA,GAAA,yUAmGN5B,EAIKC,EAAA4B,EAAA3B,CAAA,mHAiCUH,EAAM,EAAA,2BAEFA,EAAC,EAAA,wRAFLA,EAAM,EAAA,8dAUlBC,EAYMC,EAAA+B,EAAA9B,CAAA,iHA5BcH,EAAI,EAAA,EAAA,qBACnBA,EAAM,EAAA,GAAIA,EAAW,CAAA,IAAKA,EAAC,EAAA,GAAAkC,GAAAlC,CAAA,qJAc5BA,EAAM,EAAA,GAAIA,EAAkB,CAAA,IAAKA,EAAC,EAAA,GAAAmC,GAAAnC,CAAA,0NArB/BH,EAAAuC,EAAA,QAAAC,EAAA,2BACLrC,EAAS,EAAA,GAAAA,EAAQ,EAAA,EAAA,EACf,gBAAmB,CAAAA,EAAA,EAAA,EACnB,eAAiBA,EAAK,EAAA,GACzB,GAAG,EAdUsC,EAAAF,EAAA,SAAApC,QAAW,MAASA,EAAU,CAAA,GAAAA,OAAWA,EAAM,EAAA,CAAA,EACnDsC,EAAAF,EAAA,KAAApC,QAAW,IAAI,6DAN3BC,EAmDMC,EAAAqC,EAAApC,CAAA,EAlDLqC,EAkCMD,EAAAH,CAAA,EAdLI,EAA+BJ,EAAAK,CAAA,kLAAXzC,EAAI,EAAA,EAAA,KAAA0C,EAAAC,EAAAC,CAAA,EACnB5C,EAAM,EAAA,GAAIA,EAAW,CAAA,IAAKA,EAAC,EAAA,sGAPzB,CAAA6C,GAAAzC,EAAA,CAAA,EAAA,GAAAiC,KAAAA,EAAA,2BACLrC,EAAS,EAAA,GAAAA,EAAQ,EAAA,EAAA,EACf,gBAAmB,CAAAA,EAAA,EAAA,EACnB,eAAiBA,EAAK,EAAA,GACzB,qCAdasC,EAAAF,EAAA,SAAApC,QAAW,MAASA,EAAU,CAAA,GAAAA,OAAWA,EAAM,EAAA,CAAA,gBACnDsC,EAAAF,EAAA,KAAApC,QAAW,IAAI,EA8BrBA,EAAM,EAAA,GAAIA,EAAkB,CAAA,IAAKA,EAAC,EAAA,iKAtKnCA,EAAU,CAAA,GAAA8C,GAAA9C,CAAA,6IAJhBC,EAqBKC,EAAA4B,EAAA3B,CAAA,yBAjBCH,EAAU,CAAA,8GACP,OAAO,QAAQA,EAAU,CAAA,CAAA,CAAA,uBAA9B,OAAI,GAAA,oKAAC,OAAO,QAAQA,EAAU,CAAA,CAAA,CAAA,oBAA9B,OAAI6B,GAAA,EAAA,2HAAJ,sDAYC7B,EAAQ,EAAA,EAAA,sUAFF,oBAAsBA,EAAK,EAAA,EAAC,SAAS,UAT7CC,EAYKC,EAAA4B,EAAA3B,CAAA,mIADHH,EAAQ,EAAA,EAAA,KAAA0C,EAAAC,EAAAC,CAAA,iBAFF,oBAAsB5C,EAAK,EAAA,EAAC,gFAoDnBA,EAAI,EAAA,EAAA,oHAEb,IAAA+C,EAAA,CAAA/C,MAAeA,EAAQ,EAAA,IAAK,MAAQA,OAAgBA,EAAC,EAAA,GAAAgD,GAAAhD,CAAA,EAgBtDiD,EAAAjD,EAAgB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAa,MAAIkD,GAAAlD,CAAA,uKAcvC,IAAAmD,EAAAnD,QAAa,MAAIoD,GAAApD,CAAA,iLAvCJsC,EAAAG,EAAA,WAAAzC,QAAa,IAAI,kFArBpBsC,EAAAF,EAAA,SAAApC,QAAa,MACzBA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,CAAA,EACrBsC,EAAAF,EAAA,KAAApC,QAAa,IAAI,yBARHK,EAAA+B,EAAA,mBAAApC,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAQ,EAAA,GAAIA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAC/BA,EAAU,CAAA,EAACA,EAAU,EAAA,CAAA,EAAA,UACrB,EAAE,gEAVPC,EAsFMC,EAAAqC,EAAApC,CAAA,EArFLqC,EAqEMD,EAAAH,CAAA,EAvCLI,EASAJ,EAAAK,CAAA,uQADezC,EAAI,EAAA,EAAA,KAAA0C,EAAAC,EAAAC,CAAA,gBAPFN,EAAAG,EAAA,WAAAzC,QAAa,IAAI,EAS5B,CAAAA,MAAeA,EAAQ,EAAA,IAAK,MAAQA,OAAgBA,EAAC,EAAA,yDAgBtDA,EAAgB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAa,uHA9CzBsC,EAAAF,EAAA,SAAApC,QAAa,MACzBA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,CAAA,gBACrBsC,EAAAF,EAAA,KAAApC,QAAa,IAAI,gDARHK,EAAA+B,EAAA,mBAAApC,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAQ,EAAA,GAAIA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAC/BA,EAAU,CAAA,EAACA,EAAU,EAAA,CAAA,EAAA,UACrB,EAAE,EA6DDA,QAAa,gLAjBdA,EAAQ,EAAA,EAAA,+HAXQA,EAAC,EAAA,GAAA,+EAIMK,EAAA4B,EAAA,mBAAAjC,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,OAAO,UARhCC,EAaMC,EAAA+B,EAAA9B,CAAA,kFADJH,EAAQ,EAAA,EAAA,KAAA0C,EAAAW,EAAAC,CAAA,WAPejD,EAAA4B,EAAA,mBAAAjC,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,OAAO,8JAejBA,EAAC,EAAA,oIAR0B;AAAA,UAE1C,6nBAaDC,EAYMC,EAAA+B,EAAA9B,CAAA,wIAKRF,EAAKC,EAAAqD,EAAApD,CAAA,uCA1FDH,EAAI,EAAA,EAAC,KAAI,IAAO,KAyFhBwD,EAAAxD,MAAIyD,EAAmBzD,EAAM,EAAA,CAAA,EAAA,OAAS,8IAzFtCA,EAAI,EAAA,EAAC,KAAI,IAAO,gHAyFhBI,EAAA,CAAA,EAAA,IAAAoD,EAAAxD,MAAIyD,EAAmBzD,EAAM,EAAA,CAAA,EAAA,OAAS,qKA1FrC0D,EAAAC,EAAAF,EAAmBzD,EAAI,EAAA,CAAA,CAAA,uBAA5B,OAAI6B,GAAA,6MAAC6B,EAAAC,EAAAF,EAAmBzD,EAAI,EAAA,CAAA,CAAA,oBAA5B,OAAI6B,GAAA,EAAA,mHAAJ,OAAIA,EAAAE,EAAA,OAAAF,GAAA,yCAAJ,OAAIA,GAAA,0KA5BJ,OAAA7B,OAAS,aAAY,uGAD3BC,EAgMKC,EAAA4B,EAAA3B,CAAA,+NAnRK,SAAAsD,EAAmB3C,EAAY,QAChCA,EAAK,MAAM;AAAA,CAAI,2BA7EjB8C,EAAO,OAAU,SAAa,QAOzB,MAAApD,EAAK,EAAA,EAAAC,EACL,CAAA,YAAAoD,EAAc,EAAK,EAAApD,GACnB,UAAAqD,EAAS,EAAA,EAAArD,EACT,CAAA,WAAAsD,EAAa,EAAK,EAAAtD,EAEzBuD,KACAhE,EACAiB,EAAU,CAAA,EACVN,EAAS,GACTsD,EACArD,KAEJsD,GAAO,IAAA,OACAC,EAAc,IAAA,CACnBF,EAAY,OAAO,eACnBG,IACA,OAAO,oBAAoB,UAAWD,CAAc,GAGrD,OAAO,iBAAiB,YAAW,IAAA,CAClC,OAAO,iBAAiB,UAAWA,CAAc,qBAIpCE,EACdC,EACAC,EAAgB,CAGf,GAAAN,GAAW,SAAQ,GACnBD,IAAwB,IACxBxD,EAAMwD,CAAkB,EAAE,CAAC,EAAE,SAAQ,EAAG,SAASC,EAAU,SAAQ,CAAA,EAAA,CAE7D,MAAAO,EAAW,SAEXC,EAAMjE,EAAMwD,CAAkB,EAAE,CAAC,GAChCU,GAAQC,GAAUC,EAAK,EAAA,CAC7BH,EAAI,UAAU,EAAGH,CAAU,EAC3BG,EAAI,UAAUH,EAAYC,CAAQ,EAClCE,EAAI,UAAUF,CAAQ,OAGnBM,EAAS,CACT,GAAArE,EAAM,MAAM,EAAGwD,CAAkB,EACnC,CAAAU,GAAQ,IAAI,GACZC,GAAUG,IAAS,SAAW,EAAI,QAASN,CAAQ,EACnD,CAAAI,GAAO,IAAI,EACT,GAAApE,EAAM,MAAMwD,EAAqB,CAAC,GAItCxC,EAAA,EAAAZ,EAAciE,EAAU,UAAY,CAAA,CAAAE,GAAGC,GAAIC,EAAI,IAAMA,KAAST,CAAQ,CAAA,EACtEK,EAAUjE,CAAW,EAAE,MAGvBiE,EAAYA,EAAU,OAAQK,IAASA,GAAK,CAAC,EAAE,SAAW,EAAE,EAC5D1D,EAAA,EAAAhB,EAAQqE,CAA+C,EAEvD9D,IACA,SAAS,eAA8B,eAAAH,MAAgB,SAInD,MAAAuE,EAAWC,KAUR,SAAAC,EAAsBC,EAAa,CACvCA,EAAQ,GAAKA,GAAS9E,EAAM,SAChCgB,EAAA,EAAAhB,EAAM8E,CAAK,EAAE,CAAC,EAAI,KAAI9E,CAAA,EACtBgB,EAAA,EAAAhB,EAAQ+E,GAAe/E,EAAO,OAAO,CAAA,EACrCO,IACA,OAAO,gBAAgB,kBAGfA,GAAiB,CACzBoE,EAAS,SAAU3E,CAAK,EACxBgB,EAAA,EAAAZ,IAAgB,EAGZiD,SACHC,EAAS,CAAA,CAAA,MACT7C,EAAU,CAAA,CAAA,OAIR6D,EAyBK,SAAAU,EAAiBC,EAAa,CACtCjE,EAAA,EAAAb,EAAS8E,CAAK,WAENC,GAAe,CACvBlE,EAAA,EAAAb,EAAS,EAAE,EAGG,eAAAgF,EAAuBC,EAAoB,CACzD3B,EAAY,OAAO,eAEf2B,EAAM,MAAQ,SACjBxB,aAIOA,GAAuB,CAC3B,GAAAH,GAAaA,GAAW,SAAW,EAAA,KAAI,IAAO,GAAE,CAC7C,MAAA4B,EAAqB5B,EAAU,WAAW,CAAC,EAAE,YAC7C6B,EAAe7B,EAAU,WAAW,CAAC,EAAE,UAC7CI,EAAmBwB,EAAoBC,CAAY,GAI5C,SAAAC,EACRlE,EACAf,EACAJ,EAAgC,CAEhCyE,EAAS,SAAQ,CAChB,MAAOtD,EACP,MAAK,CAAGf,EAAMJ,CAAQ,CAAA,CAAA,EAkBE,MAAAsF,GAAAtF,GAAA8E,EAAiB9E,CAAQ,EAC7BuF,GAAAvF,GAAA8E,EAAiB9E,CAAQ,SACtBgF,WACJA,OAkDAtE,GAAMuE,EAAuBvE,CAAC,EAC1B8E,GAAArE,GAAAL,EAAA,EAAAwC,EAAqBnC,CAAC,EAClBsE,GAAAtE,GAAAL,EAAA,EAAAwC,EAAqBnC,CAAC,EAC1BuE,GAAAvE,GAAAL,EAAA,EAAAZ,EAAciB,CAAC,EAadwE,GAAAxE,GAAAL,EAAA,EAAAZ,EAAciB,CAAC,EACbyE,GAAAzE,GAAAL,EAAA,EAAAZ,EAAciB,CAAC,+CApC/BnB,IAAa,MAChBqF,EAAalE,EAAGf,EAAMJ,CAAQ,aAGnBU,IAAC,CACTV,IAAa,MAChBc,EAAA,EAAAZ,EAAciB,CAAC,EACfkE,EAAalE,EAAGf,EAAMJ,CAAQ,GAE9BiF,EAAuBvE,CAAC,GAGTmF,GAAA1E,GAAAL,EAAA,EAAAwC,EAAqBnC,CAAC,EAClB2E,GAAA3E,GAAAL,EAAA,EAAAwC,EAAqBnC,CAAC,EAgD1B4E,GAAA5E,GAAAwD,EAAsBxD,CAAC,QAC1B+D,IAAK,CACbA,EAAM,MAAQ,SACjBP,EAAsBxD,CAAC,6BAiCP,MAAA6E,GAAA7E,GAAAL,EAAA,EAAAwC,EAAqBnC,CAAC,EAC1B8E,GAAA9E,GAAAL,EAAA,EAAAwC,EAAqBnC,CAAC,EACtB+E,GAAA/E,GAAAL,EAAA,EAAAZ,EAAciB,CAAC,QACnBT,IAAC,CACTA,EAAE,MAAQ,SACbI,EAAA,EAAAZ,EAAciB,CAAC,GA8BAgF,GAAAhF,GAAAwD,EAAsBxD,CAAC,QAC1B+D,IAAK,CACbA,EAAM,MAAQ,SACjBP,EAAsBxD,CAAC,8MAhP9B,IACKiC,QACJA,EAAS,CAAA,CAAA,EAENtD,EAAM,OAAS,WACR,EAAGiF,CAAK,IAAKjF,EAClB,GAAAiF,IAAU,KACF,GAAA,OAAAA,GAAU,UAEd,GADNjE,EAAA,EAAAsD,EAAO,YAAY,EACb,EAAAW,KAAS3B,GAAS,KACnBgD,EAAQC,GAAe,OAAO,KAAKjD,CAAS,EAAE,MAAM,OACxDA,EAAU2B,CAAK,EAAIqB,EAAKhD,CAAA,QAGzBtC,EAAA,EAAAsD,EAAO,QAAQ,EAMnBkC,GAAkBlD,EAAW7C,EAAY2C,EAAS5D,CAAG,6RCpE9CiH,oBAEC,GACE,QAAAjH,OAAc,2FAAdI,EAAA,MAAA8G,EAAA,QAAAlH,OAAc,+iCANNA,EAAc,EAAA,CAAA,8EAC5BA,EAAK,CAAA,GAAAmH,GAAAnH,CAAA,8CASLA,EAAK,CAAA,EAAA,iMAVSA,EAAc,EAAA,CAAA,CAAA,CAAA,eAC5BA,EAAK,CAAA,ibAXD,QAAAA,EAAS,CAAA,IAAA,cAAgB,SAAW,uFAKpC,wJALAI,EAAA,KAAAgH,EAAA,QAAApH,EAAS,CAAA,IAAA,cAAgB,SAAW,4UA/BlC,GAAA,CAAA,QAAAqH,EAAU,EAAE,EAAA5G,GACZ,aAAA6G,EAAY,EAAA,EAAA7G,EACZ,CAAA,QAAA8G,EAAU,EAAI,EAAA9G,GACd,MAAAD,CAAyC,EAAAC,GACzC,KAAAqE,CAA8B,EAAArE,GAC9B,YAAAoD,CAAoB,EAAApD,GACpB,UAAAqD,EAAS,EAAA,EAAArD,GACT,MAAAgF,EAAQ+B,EAAG,mCAAmC,CAAA,EAAA/G,EAC9C,CAAA,UAAAgH,EAAY,EAAI,EAAAhH,EAChB,CAAA,MAAAiH,EAAuB,IAAI,EAAAjH,EAC3B,CAAA,UAAAkH,EAAgC,MAAS,EAAAlH,EACzC,CAAA,WAAAsD,EAAa,EAAK,EAAAtD,EAClB,CAAA,iBAAAmH,EAAmB,EAAK,EAAAnH,GACxB,OAAAoH,CAIT,EAAApH,GAMS,eAAAqH,CAA6B,EAAArH,+CA+BrBoH,EAAO,SAAS,QAAQ,0kBAnC1C,CAAO/D,GAAa,OAAO,KAAKA,CAAS,EAAE,iCAMrCtD,GAASoH,GACfpG,EAAA,EAAAhB,EAAQ+E,GAAe/E,EAAO,OAAO,CAAA"}