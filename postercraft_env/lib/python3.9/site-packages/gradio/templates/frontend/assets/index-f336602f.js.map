{"version": 3, "file": "index-f336602f.js", "sources": ["../../../../js/dataframe/static/StaticDataframe.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { afterUpdate } from \"svelte\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Table from \"../shared\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Headers, Data, Metadata, Datatype } from \"../shared/utils\";\n\texport let headers: Headers = [];\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { data: Data; headers: Headers; metadata: Metadata } = {\n\t\tdata: [[\"\", \"\", \"\"]],\n\t\theaders: [\"1\", \"2\", \"3\"],\n\t\tmetadata: null\n\t};\n\tlet old_value: string = JSON.stringify(value);\n\texport let value_is_output = false;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let label: string | null = null;\n\texport let wrap: boolean;\n\texport let datatype: Datatype | Datatype[];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t}>;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let height: number | undefined = undefined;\n\n\texport let loading_status: LoadingStatus;\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\");\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: {\n\t\tif (JSON.stringify(value) !== old_value) {\n\t\t\told_value = JSON.stringify(value);\n\t\t\thandle_change();\n\t\t}\n\t}\n\tif (\n\t\t(Array.isArray(value) && value?.[0]?.length === 0) ||\n\t\tvalue.data?.[0]?.length === 0\n\t) {\n\t\tvalue = {\n\t\t\tdata: [Array(col_count?.[0] || 3).fill(\"\")],\n\t\t\theaders: Array(col_count?.[0] || 3)\n\t\t\t\t.fill(\"\")\n\t\t\t\t.map((_, i) => `${i + 1}`),\n\t\t\tmetadata: null\n\t\t};\n\t}\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\tcontainer={false}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker {...loading_status} border={true} />\n\t<Table\n\t\t{label}\n\t\t{row_count}\n\t\t{col_count}\n\t\t{value}\n\t\t{headers}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t{wrap}\n\t\t{datatype}\n\t\t{latex_delimiters}\n\t\teditable={false}\n\t\t{height}\n\t\t{line_breaks}\n\t\t{column_widths}\n\t/>\n</Block>\n"], "names": ["statustracker_spread_levels", "ctx", "headers", "$$props", "elem_id", "elem_classes", "visible", "value", "old_value", "value_is_output", "col_count", "row_count", "label", "wrap", "datatype", "scale", "min_width", "line_breaks", "column_widths", "gradio", "latex_delimiters", "height", "loading_status", "handle_change", "afterUpdate", "$$invalidate", "_", "i"], "mappings": "kZAiFoB,MAAAA,EAAA,CAAAC,cAAwB,EAAI,CAAA,sNAWpC,oMAXQA,EAAc,EAAA,CAAA,8gBARxB,4CAGE,8CAGK,gYAvEL,QAAAC,EAAO,EAAA,EAAAC,EACP,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,GACd,MAAAI,EAAK,CACf,KAAQ,CAAA,CAAA,GAAI,GAAI,EAAE,CAAA,EAClB,QAAU,CAAA,IAAK,IAAK,GAAG,EACvB,SAAU,SAEPC,EAAoB,KAAK,UAAUD,CAAK,EACjC,CAAA,gBAAAE,EAAkB,EAAK,EAAAN,GACvB,UAAAO,CAAwC,EAAAP,GACxC,UAAAQ,CAAwC,EAAAR,EACxC,CAAA,MAAAS,EAAuB,IAAI,EAAAT,GAC3B,KAAAU,CAAa,EAAAV,GACb,SAAAW,CAA+B,EAAAX,EAC/B,CAAA,MAAAY,EAAuB,IAAI,EAAAZ,EAC3B,CAAA,UAAAa,EAAgC,MAAS,EAAAb,EACzC,CAAA,YAAAc,EAAc,EAAI,EAAAd,GAClB,cAAAe,EAAa,EAAA,EAAAf,GACb,OAAAgB,CAIT,EAAAhB,GACS,iBAAAiB,CAIR,EAAAjB,EACQ,CAAA,OAAAkB,EAA6B,MAAS,EAAAlB,GAEtC,eAAAmB,CAA6B,EAAAnB,WAE/BoB,GAAa,CACrBJ,EAAO,SAAS,QAAQ,EACnBV,GACJU,EAAO,SAAS,OAAO,EAGzBK,EAAW,IAAA,CACVC,EAAA,GAAAhB,EAAkB,EAAK,KAStB,MAAM,QAAQF,CAAK,GAAKA,IAAQ,CAAC,GAAG,SAAW,GAChDA,EAAM,OAAO,CAAC,GAAG,SAAW,KAE5BA,EAAK,CACJ,KAAI,CAAG,MAAMG,IAAY,CAAC,GAAK,CAAC,EAAE,KAAK,EAAE,CAAA,EACzC,QAAS,MAAMA,IAAY,CAAC,GAAK,CAAC,EAChC,KAAK,EAAE,EACP,IAAK,CAAAgB,EAAGC,IAAC,GAAQA,EAAI,GAAC,EACxB,SAAU,eAsBC,GAAMR,EAAO,SAAS,SAAU,EAAE,MAAM,yuBApChD,KAAK,UAAUZ,CAAK,IAAMC,IAC7BiB,EAAA,GAAAjB,EAAY,KAAK,UAAUD,CAAK,CAAA,EAChCgB"}