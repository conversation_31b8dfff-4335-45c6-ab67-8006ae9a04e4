{"version": 3, "file": "index-fa989a30.js", "sources": ["../../../../js/highlightedtext/static/Highlightedtext.svelte", "../../../../js/highlightedtext/static/StaticHighlightedtext.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\tconst browser = typeof document !== \"undefined\";\n\timport { get_next_color } from \"@gradio/utils\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { correct_color_map } from \"../utils\";\n\n\texport let value: [string, string | number | null][] = [];\n\texport let show_legend = false;\n\texport let color_map: Record<string, string> = {};\n\texport let selectable = false;\n\n\tlet ctx: CanvasRenderingContext2D;\n\tlet _color_map: Record<string, { primary: string; secondary: string }> = {};\n\tlet active = \"\";\n\n\tfunction splitTextByNewline(text: string): string[] {\n\t\treturn text.split(\"\\n\");\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tselect: SelectData;\n\t}>();\n\n\tlet mode: \"categories\" | \"scores\";\n\n\t$: {\n\t\tif (!color_map) {\n\t\t\tcolor_map = {};\n\t\t}\n\t\tif (value.length > 0) {\n\t\t\tfor (let [_, label] of value) {\n\t\t\t\tif (label !== null) {\n\t\t\t\t\tif (typeof label === \"string\") {\n\t\t\t\t\t\tmode = \"categories\";\n\t\t\t\t\t\tif (!(label in color_map)) {\n\t\t\t\t\t\t\tlet color = get_next_color(Object.keys(color_map).length);\n\t\t\t\t\t\t\tcolor_map[label] = color;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmode = \"scores\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tcorrect_color_map(color_map, _color_map, browser, ctx);\n\t}\n\n\tfunction handle_mouseover(label: string): void {\n\t\tactive = label;\n\t}\n\tfunction handle_mouseout(): void {\n\t\tactive = \"\";\n\t}\n</script>\n\n<!-- \n\t@todo victor: try reimplementing without flex (negative margins on container to avoid left margin on linebreak). \n\tIf not possible hijack the copy execution like this:\n\n<svelte:window\n\ton:copy|preventDefault={() => {\n\t\tconst selection =.getSelection()?.toString();\n\t\tconsole.log(selection?.replaceAll(\"\\n\", \" \"));\n\t}}\n/>\n-->\n\n<div class=\"container\">\n\t{#if mode === \"categories\"}\n\t\t{#if show_legend}\n\t\t\t<div\n\t\t\t\tclass=\"category-legend\"\n\t\t\t\tdata-testid=\"highlighted-text:category-legend\"\n\t\t\t>\n\t\t\t\t{#each Object.entries(_color_map) as [category, color], i}\n\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t\t\t\t<div\n\t\t\t\t\t\ton:mouseover={() => handle_mouseover(category)}\n\t\t\t\t\t\ton:focus={() => handle_mouseover(category)}\n\t\t\t\t\t\ton:mouseout={() => handle_mouseout()}\n\t\t\t\t\t\ton:blur={() => handle_mouseout()}\n\t\t\t\t\t\tclass=\"category-label\"\n\t\t\t\t\t\tstyle={\"background-color:\" + color.secondary}\n\t\t\t\t\t>\n\t\t\t\t\t\t{category}\n\t\t\t\t\t</div>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t{/if}\n\t\t<div class=\"textfield\">\n\t\t\t{#each value as [text, category], i}\n\t\t\t\t{#each splitTextByNewline(text) as line, j}\n\t\t\t\t\t{#if line.trim() !== \"\"}\n\t\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t\t\t\t\t<span\n\t\t\t\t\t\t\tclass=\"textspan\"\n\t\t\t\t\t\t\tstyle:background-color={category === null ||\n\t\t\t\t\t\t\t(active && active !== category)\n\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t: _color_map[category].secondary}\n\t\t\t\t\t\t\tclass:no-cat={category === null ||\n\t\t\t\t\t\t\t\t(active && active !== category)}\n\t\t\t\t\t\t\tclass:hl={category !== null}\n\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\t\t\tvalue: [text, category],\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\tclass:no-label={category && !_color_map[category]}\n\t\t\t\t\t\t\t\tclass=\"text\">{line}</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#if !show_legend && category !== null}\n\t\t\t\t\t\t\t\t&nbsp;\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"label\"\n\t\t\t\t\t\t\t\t\tstyle:background-color={category === null ||\n\t\t\t\t\t\t\t\t\t(active && active !== category)\n\t\t\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t\t\t: _color_map[category].primary}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{category}\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#if j < splitTextByNewline(text).length - 1}\n\t\t\t\t\t\t<br />\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{/each}\n\t\t</div>\n\t{:else}\n\t\t{#if show_legend}\n\t\t\t<div class=\"color-legend\" data-testid=\"highlighted-text:color-legend\">\n\t\t\t\t<span>-1</span>\n\t\t\t\t<span>0</span>\n\t\t\t\t<span>+1</span>\n\t\t\t</div>\n\t\t{/if}\n\t\t<div class=\"textfield\" data-testid=\"highlighted-text:textfield\">\n\t\t\t{#each value as [text, _score]}\n\t\t\t\t{@const score = typeof _score === \"string\" ? parseInt(_score) : _score}\n\t\t\t\t<span\n\t\t\t\t\tclass=\"textspan score-text\"\n\t\t\t\t\tstyle={\"background-color: rgba(\" +\n\t\t\t\t\t\t(score && score < 0\n\t\t\t\t\t\t\t? \"128, 90, 213,\" + -score\n\t\t\t\t\t\t\t: \"239, 68, 60,\" + score) +\n\t\t\t\t\t\t\")\"}\n\t\t\t\t>\n\t\t\t\t\t<span class=\"text\">{text}</span>\n\t\t\t\t</span>\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-sm);\n\t\tpadding: var(--block-padding);\n\t}\n\t.hl + .hl {\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.textspan:last-child > .label {\n\t\tmargin-right: 0;\n\t}\n\n\t.category-legend {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--spacing-sm);\n\t\tcolor: black;\n\t}\n\n\t.category-label {\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-right: var(--size-2);\n\t\tpadding-left: var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.color-legend {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: linear-gradient(\n\t\t\tto right,\n\t\t\tvar(--color-purple),\n\t\t\trgba(255, 255, 255, 0),\n\t\t\tvar(--color-red)\n\t\t);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.textfield {\n\t\tbox-sizing: border-box;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: var(--background-fill-primary);\n\t\tbackground-color: transparent;\n\t\tmax-width: var(--size-full);\n\t\tline-height: var(--scale-4);\n\t\tword-break: break-all;\n\t}\n\n\t.textspan {\n\t\ttransition: 150ms;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-top: 2.5px;\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: 3.5px;\n\t\tpadding-left: var(--size-1);\n\t\tcolor: black;\n\t}\n\n\t.label {\n\t\ttransition: 150ms;\n\t\tmargin-top: 1px;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding: 1px 5px;\n\t\tcolor: var(--body-text-color);\n\t\tcolor: white;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-sm);\n\t\ttext-transform: uppercase;\n\t}\n\n\t.text {\n\t\tcolor: black;\n\t\twhite-space: pre-wrap;\n\t}\n\n\t.score-text .text {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.score-text {\n\t\tmargin-right: var(--size-1);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.no-cat {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.no-label {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport HighlightedText from \"./Highlightedtext.svelte\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { TextHighlight } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\timport { merge_elements } from \"../utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [string, string | number | null][];\n\tlet old_value: [string, string | number | null][];\n\texport let mode: \"static\" | \"interactive\";\n\texport let show_legend: boolean;\n\texport let color_map: Record<string, string> = {};\n\texport let label = $_(\"highlighted_text.highlighted_text\");\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let selectable = false;\n\texport let combine_adjacent = false;\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t\tchange: never;\n\t}>;\n\n\t$: if (!color_map && Object.keys(color_map).length) {\n\t\tcolor_map = color_map;\n\t}\n\n\texport let loading_status: LoadingStatus;\n\n\t$: {\n\t\tif (value !== old_value) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\tif (value && combine_adjacent) {\n\t\tvalue = merge_elements(value, \"equal\");\n\t}\n</script>\n\n<Block\n\tvariant={mode === \"interactive\" ? \"dashed\" : \"solid\"}\n\ttest_id=\"highlighted-text\"\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={false}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\t{#if label}\n\t\t<BlockLabel\n\t\t\tIcon={TextHighlight}\n\t\t\t{label}\n\t\t\tfloat={false}\n\t\t\tdisable={container === false}\n\t\t/>\n\t{/if}\n\n\t{#if value}\n\t\t<HighlightedText\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\t{selectable}\n\t\t\t{value}\n\t\t\t{show_legend}\n\t\t\t{color_map}\n\t\t/>\n\t{:else}\n\t\t<Empty>\n\t\t\t<TextHighlight />\n\t\t</Empty>\n\t{/if}\n</Block>\n"], "names": ["constants_0", "child_ctx", "ctx", "create_if_block_5", "insert", "target", "div", "anchor", "i", "create_if_block_4", "attr", "span1", "span1_style_value", "append", "span0", "set_data", "t0", "t0_value", "dirty", "create_if_block_3", "toggle_class", "set_style", "span", "t1", "t1_value", "br", "show_if", "splitTextByNewline", "each_value_1", "ensure_array_like", "create_if_block", "text", "browser", "value", "$$props", "show_legend", "color_map", "selectable", "_color_map", "active", "dispatch", "createEventDispatcher", "mode", "handle_mouseover", "label", "$$invalidate", "handle_mouseout", "mouseover_handler", "category", "focus_handler", "_", "color", "get_next_color", "correct_color_map", "TextHighlight", "blocklabel_changes", "create_if_block_1", "block_changes", "elem_id", "elem_classes", "visible", "old_value", "$_", "container", "scale", "min_width", "combine_adjacent", "gradio", "loading_status", "merge_elements", "select_handler", "detail"], "mappings": "yjBAsJ2B,MAAAA,EAAA,OAAAC,OAAW,SAAW,SAASA,EAAM,EAAA,CAAA,EAAIA,EAAM,EAAA,8QATnEC,EAAW,CAAA,GAAAC,EAAA,MAQRD,EAAK,CAAA,CAAA,uBAAV,OAAI,GAAA,iNADPE,EAcKC,EAAAC,EAAAC,CAAA,8DArBAL,EAAW,CAAA,mEAQRA,EAAK,CAAA,CAAA,oBAAV,OAAIM,GAAA,EAAA,iHAAJ,yEA9EEN,EAAW,CAAA,GAAAO,EAAAP,CAAA,MAsBRA,EAAK,CAAA,CAAA,uBAAV,OAAI,GAAA,kKADPE,EA+CKC,EAAAC,EAAAC,CAAA,8DApEAL,EAAW,CAAA,8EAsBRA,EAAK,CAAA,CAAA,oBAAV,OAAIM,GAAA,EAAA,kHAAJ,4PAiDFJ,EAIKC,EAAAC,EAAAC,CAAA,0CAaiBL,EAAI,EAAA,EAAA,4IANjBQ,EAAAC,EAAA,QAAAC,EAAA,2BACLV,EAAS,EAAA,GAAAA,EAAQ,EAAA,EAAA,EACf,gBAAmB,CAAAA,EAAA,EAAA,EACnB,eAAiBA,EAAK,EAAA,GACzB,GAAG,UANLE,EASMC,EAAAM,EAAAJ,CAAA,EADLM,EAA+BF,EAAAG,CAAA,oCAAXZ,EAAI,EAAA,EAAA,KAAAa,EAAAC,EAAAC,CAAA,EANjBC,EAAA,GAAAN,KAAAA,EAAA,2BACLV,EAAS,EAAA,GAAAA,EAAQ,EAAA,EAAA,EACf,gBAAmB,CAAAA,EAAA,EAAA,EACnB,eAAiBA,EAAK,EAAA,GACzB,6DAjFK,OAAO,QAAQA,EAAU,CAAA,CAAA,CAAA,uBAA9B,OAAIM,GAAA,yLAJPJ,EAkBKC,EAAAC,EAAAC,CAAA,yEAdG,OAAO,QAAQL,EAAU,CAAA,CAAA,CAAA,oBAA9B,OAAI,GAAA,EAAA,iHAAJ,qDAWCA,EAAQ,EAAA,EAAA,wKAFF,oBAAsBA,EAAK,EAAA,EAAC,SAAS,UAN7CE,EASKC,EAAAC,EAAAC,CAAA,wKA8BYL,EAAI,EAAA,EAAA,cAEbA,EAAW,CAAA,GAAIA,EAAQ,EAAA,IAAK,MAAIiB,GAAAjB,CAAA,yJAHrBA,EAAQ,EAAA,GAAA,CAAKA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,CAAA,wCAZnCkB,EAAAT,EAAA,SAAAT,QAAa,MACzBA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,CAAA,EACrBkB,EAAAT,EAAA,KAAAT,QAAa,IAAI,yBANHmB,EAAAV,EAAA,mBAAAT,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,SAAS,UALlCE,EAiCMC,EAAAM,EAAAJ,CAAA,EAhBLM,EAGAF,EAAAG,CAAA,kFADeZ,EAAI,EAAA,EAAA,KAAAa,EAAAC,EAAAC,CAAA,uBADFf,EAAQ,EAAA,GAAA,CAAKA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,CAAA,GAG3CA,EAAW,CAAA,GAAIA,EAAQ,EAAA,IAAK,oEAfpBkB,EAAAT,EAAA,SAAAT,QAAa,MACzBA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,CAAA,OACrBkB,EAAAT,EAAA,KAAAT,QAAa,IAAI,mCANHmB,EAAAV,EAAA,mBAAAT,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,SAAS,6DAyB9BA,EAAQ,EAAA,EAAA,oBAT2B;AAAA,SAErC,wDAEyBmB,EAAAC,EAAA,mBAAApB,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,OAAO,mBALhCE,EAQMC,EAAAiB,EAAAf,CAAA,6BADJL,EAAQ,EAAA,EAAA,KAAAa,EAAAQ,EAAAC,CAAA,OALeH,EAAAC,EAAA,mBAAApB,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,OAAO,2EAQlCE,EAAKC,EAAAoB,EAAAlB,CAAA,uCAxCDL,EAAI,EAAA,EAAC,KAAI,IAAO,KAuChBwB,EAAAxB,MAAIyB,EAAmBzB,EAAM,EAAA,CAAA,EAAA,OAAS,oIAvCtCA,EAAI,EAAA,EAAC,KAAI,IAAO,wEAuChBgB,EAAA,IAAAQ,EAAAxB,MAAIyB,EAAmBzB,EAAM,EAAA,CAAA,EAAA,OAAS,8HAxCrC0B,EAAAC,EAAAF,EAAmBzB,EAAI,EAAA,CAAA,CAAA,uBAA5B,OAAIM,GAAA,2JAACoB,EAAAC,EAAAF,EAAmBzB,EAAI,EAAA,CAAA,CAAA,oBAA5B,OAAI,GAAA,EAAA,0HAAJ,oEAxBA,OAAAA,OAAS,aAAY4B,mGAD3B1B,EA+FKC,EAAAC,EAAAC,CAAA,sHApJK,SAAAoB,EAAmBI,EAAY,QAChCA,EAAK,MAAM;AAAA,CAAI,2BAhBjBC,EAAO,OAAU,SAAa,QAMzB,MAAAC,EAAK,EAAA,EAAAC,EACL,CAAA,YAAAC,EAAc,EAAK,EAAAD,GACnB,UAAAE,EAAS,EAAA,EAAAF,EACT,CAAA,WAAAG,EAAa,EAAK,EAAAH,EAEzBhC,EACAoC,EAAU,CAAA,EACVC,EAAS,GAMP,MAAAC,EAAWC,SAIbC,EAyBK,SAAAC,EAAiBC,EAAa,CACtCC,EAAA,EAAAN,EAASK,CAAK,WAENE,GAAe,CACvBD,EAAA,EAAAN,EAAS,EAAE,EA2Ba,MAAAQ,EAAAC,GAAAL,EAAiBK,CAAQ,EAC7BC,EAAAD,GAAAL,EAAiBK,CAAQ,QACtBF,UACJA,gBA2BbN,EAAS,SAAQ,CAChB,MAAOhC,EACP,MAAK,CAAGuB,EAAMiB,CAAQ,CAAA,CAAA,wMAtF7B,IACKZ,OACJA,EAAS,CAAA,CAAA,EAENH,EAAM,OAAS,WACRiB,EAAGN,CAAK,IAAKX,EAClB,GAAAW,IAAU,KACF,GAAA,OAAAA,GAAU,UAEd,GADNC,EAAA,EAAAH,EAAO,YAAY,EACb,EAAAE,KAASR,GAAS,KACnBe,EAAQC,GAAe,OAAO,KAAKhB,CAAS,EAAE,MAAM,MACxDA,EAAUQ,CAAK,EAAIO,EAAKf,CAAA,QAGzBS,EAAA,EAAAH,EAAO,QAAQ,EAMnBW,GAAkBjB,EAAWE,EAAYN,EAAS9B,CAAG,yMCe9CoD,oBAEC,GACE,QAAApD,OAAc,2FAAdgB,EAAA,MAAAqC,EAAA,QAAArD,OAAc,w8BANNA,EAAc,EAAA,CAAA,8EAC5BA,EAAK,CAAA,GAAAsD,GAAAtD,CAAA,8CASLA,EAAK,CAAA,EAAA,iMAVSA,EAAc,EAAA,CAAA,CAAA,CAAA,eAC5BA,EAAK,CAAA,ibAXD,QAAAA,EAAS,CAAA,IAAA,cAAgB,SAAW,uFAKpC,wJALAgB,EAAA,KAAAuC,EAAA,QAAAvD,EAAS,CAAA,IAAA,cAAgB,SAAW,4UAtClC,GAAA,CAAA,QAAAwD,EAAU,EAAE,EAAAxB,GACZ,aAAAyB,EAAY,EAAA,EAAAzB,EACZ,CAAA,QAAA0B,EAAU,EAAI,EAAA1B,GACd,MAAAD,CAAyC,EAAAC,EAChD2B,GACO,KAAAnB,CAA8B,EAAAR,GAC9B,YAAAC,CAAoB,EAAAD,GACpB,UAAAE,EAAS,EAAA,EAAAF,GACT,MAAAU,EAAQkB,EAAG,mCAAmC,CAAA,EAAA5B,EAC9C,CAAA,UAAA6B,EAAY,EAAI,EAAA7B,EAChB,CAAA,MAAA8B,EAAuB,IAAI,EAAA9B,EAC3B,CAAA,UAAA+B,EAAgC,MAAS,EAAA/B,EACzC,CAAA,WAAAG,EAAa,EAAK,EAAAH,EAClB,CAAA,iBAAAgC,EAAmB,EAAK,EAAAhC,GACxB,OAAAiC,CAGT,EAAAjC,GAMS,eAAAkC,CAA6B,EAAAlC,EASpCD,GAASiC,IACZjC,EAAQoC,GAAepC,EAAO,OAAO,GA2BtB,MAAAqC,EAAA,CAAA,CAAA,OAAAC,KAAaJ,EAAO,SAAS,SAAUI,CAAM,0kBAzC5D,CAAOnC,GAAa,OAAO,KAAKA,CAAS,EAAE,iCAOvCH,IAAU4B,IACbhB,EAAA,GAAAgB,EAAY5B,CAAK,EACjBkC,EAAO,SAAS,QAAQ"}