{"version": 3, "file": "module-1791af61.js", "sources": ["../../../../node_modules/.pnpm/fast-unique-numbers@6.0.21/node_modules/fast-unique-numbers/build/es2019/factories/cache.js", "../../../../node_modules/.pnpm/fast-unique-numbers@6.0.21/node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js", "../../../../node_modules/.pnpm/fast-unique-numbers@6.0.21/node_modules/fast-unique-numbers/build/es2019/module.js", "../../../../node_modules/.pnpm/broker-factory@3.0.68/node_modules/broker-factory/build/es2019/guards/message-port.js", "../../../../node_modules/.pnpm/broker-factory@3.0.68/node_modules/broker-factory/build/es2019/helpers/port-map.js", "../../../../node_modules/.pnpm/broker-factory@3.0.68/node_modules/broker-factory/build/es2019/helpers/extend-broker-implementation.js", "../../../../node_modules/.pnpm/broker-factory@3.0.68/node_modules/broker-factory/build/es2019/module.js", "../../../../node_modules/.pnpm/extendable-media-recorder-wav-encoder-broker@7.0.70/node_modules/extendable-media-recorder-wav-encoder-broker/build/es2019/module.js", "../../../../node_modules/.pnpm/extendable-media-recorder-wav-encoder@7.0.76/node_modules/extendable-media-recorder-wav-encoder/build/es2019/worker/worker.js", "../../../../node_modules/.pnpm/extendable-media-recorder-wav-encoder@7.0.76/node_modules/extendable-media-recorder-wav-encoder/build/es2019/module.js"], "sourcesContent": ["export const createCache = (lastNumberWeakMap) => {\n    return (collection, nextNumber) => {\n        lastNumberWeakMap.set(collection, nextNumber);\n        return nextNumber;\n    };\n};\n//# sourceMappingURL=cache.js.map", "/*\n * The value of the constant Number.MAX_SAFE_INTEGER equals (2 ** 53 - 1) but it\n * is fairly new.\n */\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER === undefined ? 9007199254740991 : Number.MAX_SAFE_INTEGER;\nconst TWO_TO_THE_POWER_OF_TWENTY_NINE = 536870912;\nconst TWO_TO_THE_POWER_OF_THIRTY = TWO_TO_THE_POWER_OF_TWENTY_NINE * 2;\nexport const createGenerateUniqueNumber = (cache, lastNumberWeakMap) => {\n    return (collection) => {\n        const lastNumber = lastNumberWeakMap.get(collection);\n        /*\n         * Let's try the cheapest algorithm first. It might fail to produce a new\n         * number, but it is so cheap that it is okay to take the risk. Just\n         * increase the last number by one or reset it to 0 if we reached the upper\n         * bound of SMIs (which stands for small integers). When the last number is\n         * unknown it is assumed that the collection contains zero based consecutive\n         * numbers.\n         */\n        let nextNumber = lastNumber === undefined ? collection.size : lastNumber < TWO_TO_THE_POWER_OF_THIRTY ? lastNumber + 1 : 0;\n        if (!collection.has(nextNumber)) {\n            return cache(collection, nextNumber);\n        }\n        /*\n         * If there are less than half of 2 ** 30 numbers stored in the collection,\n         * the chance to generate a new random number in the range from 0 to 2 ** 30\n         * is at least 50%. It's benifitial to use only SMIs because they perform\n         * much better in any environment based on V8.\n         */\n        if (collection.size < TWO_TO_THE_POWER_OF_TWENTY_NINE) {\n            while (collection.has(nextNumber)) {\n                nextNumber = Math.floor(Math.random() * TWO_TO_THE_POWER_OF_THIRTY);\n            }\n            return cache(collection, nextNumber);\n        }\n        // Quickly check if there is a theoretical chance to generate a new number.\n        if (collection.size > MAX_SAFE_INTEGER) {\n            throw new Error('Congratulations, you created a collection of unique numbers which uses all available integers!');\n        }\n        // Otherwise use the full scale of safely usable integers.\n        while (collection.has(nextNumber)) {\n            nextNumber = Math.floor(Math.random() * MAX_SAFE_INTEGER);\n        }\n        return cache(collection, nextNumber);\n    };\n};\n//# sourceMappingURL=generate-unique-number.js.map", "import { createAddUniqueNumber } from './factories/add-unique-number';\nimport { createCache } from './factories/cache';\nimport { createGenerateUniqueNumber } from './factories/generate-unique-number';\n/*\n * @todo Explicitly referencing the barrel file seems to be necessary when enabling the\n * isolatedModules compiler option.\n */\nexport * from './types/index';\nconst LAST_NUMBER_WEAK_MAP = new WeakMap();\nconst cache = createCache(LAST_NUMBER_WEAK_MAP);\nconst generateUniqueNumber = createGenerateUniqueNumber(cache, LAST_NUMBER_WEAK_MAP);\nconst addUniqueNumber = createAddUniqueNumber(generateUniqueNumber);\nexport { addUniqueNumber, generateUniqueNumber };\n//# sourceMappingURL=module.js.map", "export const isMessagePort = (sender) => {\n    return typeof sender.start === 'function';\n};\n//# sourceMappingURL=message-port.js.map", "export const PORT_MAP = new WeakMap();\n//# sourceMappingURL=port-map.js.map", "import { PORT_MAP } from './port-map';\nexport const extendBrokerImplementation = (partialBrokerImplementation) => ({\n    ...partialBrokerImplementation,\n    connect: ({ call }) => {\n        return async () => {\n            const { port1, port2 } = new MessageChannel();\n            const portId = await call('connect', { port: port1 }, [port1]);\n            PORT_MAP.set(port2, portId);\n            return port2;\n        };\n    },\n    disconnect: ({ call }) => {\n        return async (port) => {\n            const portId = PORT_MAP.get(port);\n            if (portId === undefined) {\n                throw new Error('The given port is not connected.');\n            }\n            await call('disconnect', { portId });\n        };\n    },\n    isSupported: ({ call }) => {\n        return () => call('isSupported');\n    }\n});\n//# sourceMappingURL=extend-broker-implementation.js.map", "import { generateUniqueNumber } from 'fast-unique-numbers';\nimport { isMessagePort } from './guards/message-port';\nimport { extendBrokerImplementation } from './helpers/extend-broker-implementation';\n/*\n * @todo Explicitly referencing the barrel file seems to be necessary when enabling the\n * isolatedModules compiler option.\n */\nexport * from './interfaces/index';\nexport * from './types/index';\nconst ONGOING_REQUESTS = new WeakMap();\nconst createOrGetOngoingRequests = (sender) => {\n    if (ONGOING_REQUESTS.has(sender)) {\n        // @todo TypeScript needs to be convinced that has() works as expected.\n        return ONGOING_REQUESTS.get(sender);\n    }\n    const ongoingRequests = new Map();\n    ONGOING_REQUESTS.set(sender, ongoingRequests);\n    return ongoingRequests;\n};\nexport const createBroker = (brokerImplementation) => {\n    const fullBrokerImplementation = extendBrokerImplementation(brokerImplementation);\n    return (sender) => {\n        const ongoingRequests = createOrGetOngoingRequests(sender);\n        sender.addEventListener('message', (({ data: message }) => {\n            const { id } = message;\n            if (id !== null && ongoingRequests.has(id)) {\n                const { reject, resolve } = ongoingRequests.get(id);\n                ongoingRequests.delete(id);\n                if (message.error === undefined) {\n                    resolve(message.result);\n                }\n                else {\n                    reject(new Error(message.error.message));\n                }\n            }\n        }));\n        if (isMessagePort(sender)) {\n            sender.start();\n        }\n        const call = (method, params = null, transferables = []) => {\n            return new Promise((resolve, reject) => {\n                const id = generateUniqueNumber(ongoingRequests);\n                ongoingRequests.set(id, { reject, resolve });\n                if (params === null) {\n                    sender.postMessage({ id, method }, transferables);\n                }\n                else {\n                    sender.postMessage({ id, method, params }, transferables);\n                }\n            });\n        };\n        const notify = (method, params, transferables = []) => {\n            sender.postMessage({ id: null, method, params }, transferables);\n        };\n        let functions = {};\n        for (const [key, handler] of Object.entries(fullBrokerImplementation)) {\n            functions = { ...functions, [key]: handler({ call, notify }) };\n        }\n        return { ...functions };\n    };\n};\n//# sourceMappingURL=module.js.map", "import { createBroker } from 'broker-factory';\n/*\n * @todo Explicitly referencing the barrel file seems to be necessary when enabling the\n * isolatedModules compiler option.\n */\nexport * from './interfaces/index';\nexport * from './types/index';\nexport const wrap = createBroker({\n    characterize: ({ call }) => {\n        return () => call('characterize');\n    },\n    encode: ({ call }) => {\n        return (recordingId, timeslice) => {\n            return call('encode', { recordingId, timeslice });\n        };\n    },\n    record: ({ call }) => {\n        return async (recordingId, sampleRate, typedArrays) => {\n            await call('record', { recordingId, sampleRate, typedArrays }, typedArrays.map(({ buffer }) => buffer));\n        };\n    }\n});\nexport const load = (url) => {\n    const worker = new Worker(url);\n    return wrap(worker);\n};\n//# sourceMappingURL=module.js.map", "// This is the minified and stringified code of the extendable-media-recorder-wav-encoder-worker package.\nexport const worker = `(()=>{var e={775:function(e,t,r){!function(e,t,r,n){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var s=o(t),a=o(r),i=o(n),u=function(e,t){return void 0===t?e:t.reduce((function(e,t){if(\"capitalize\"===t){var r=e.charAt(0).toUpperCase(),n=e.slice(1);return\"\".concat(r).concat(n)}return\"dashify\"===t?a.default(e):\"prependIndefiniteArticle\"===t?\"\".concat(i.default(e),\" \").concat(e):e}),e)},c=function(e){var t=e.name+e.modifiers.map((function(e){return\"\\\\\\\\.\".concat(e,\"\\\\\\\\(\\\\\\\\)\")})).join(\"\");return new RegExp(\"\\\\\\\\$\\\\\\\\{\".concat(t,\"}\"),\"g\")},l=function(e,t){for(var r=/\\\\\\${([^.}]+)((\\\\.[^(]+\\\\(\\\\))*)}/g,n=[],o=r.exec(e);null!==o;){var a={modifiers:[],name:o[1]};if(void 0!==o[3])for(var i=/\\\\.[^(]+\\\\(\\\\)/g,l=i.exec(o[2]);null!==l;)a.modifiers.push(l[0].slice(1,-2)),l=i.exec(o[2]);n.push(a),o=r.exec(e)}var d=n.reduce((function(e,r){return e.map((function(e){return\"string\"==typeof e?e.split(c(r)).reduce((function(e,n,o){return 0===o?[n]:r.name in t?[].concat(s.default(e),[u(t[r.name],r.modifiers),n]):[].concat(s.default(e),[function(e){return u(e[r.name],r.modifiers)},n])}),[]):[e]})).reduce((function(e,t){return[].concat(s.default(e),s.default(t))}),[])}),[e]);return function(e){return d.reduce((function(t,r){return[].concat(s.default(t),\"string\"==typeof r?[r]:[r(e)])}),[]).join(\"\")}},d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=void 0===e.code?void 0:l(e.code,t),n=void 0===e.message?void 0:l(e.message,t);function o(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0,s=void 0===o&&(t instanceof Error||void 0!==t.code&&\"Exception\"===t.code.slice(-9))?{cause:t,missingParameters:{}}:{cause:o,missingParameters:t},a=s.cause,i=s.missingParameters,u=void 0===n?new Error:new Error(n(i));return null!==a&&(u.cause=a),void 0!==r&&(u.code=r(i)),void 0!==e.status&&(u.status=e.status),u}return o};e.compile=d,Object.defineProperty(e,\"__esModule\",{value:!0})}(t,r(106),r(881),r(507))},881:e=>{\"use strict\";e.exports=(e,t)=>{if(\"string\"!=typeof e)throw new TypeError(\"expected a string\");return e.trim().replace(/([a-z])([A-Z])/g,\"$1-$2\").replace(/\\\\W/g,(e=>/[À-ž]/.test(e)?e:\"-\")).replace(/^-+|-+$/g,\"\").replace(/-{2,}/g,(e=>t&&t.condense?\"-\":e)).toLowerCase()}},107:function(e,t){!function(e){\"use strict\";var t=function(e){return function(t){var r=e(t);return t.add(r),r}},r=function(e){return function(t,r){return e.set(t,r),r}},n=void 0===Number.MAX_SAFE_INTEGER?9007199254740991:Number.MAX_SAFE_INTEGER,o=536870912,s=2*o,a=function(e,t){return function(r){var a=t.get(r),i=void 0===a?r.size:a<s?a+1:0;if(!r.has(i))return e(r,i);if(r.size<o){for(;r.has(i);)i=Math.floor(Math.random()*s);return e(r,i)}if(r.size>n)throw new Error(\"Congratulations, you created a collection of unique numbers which uses all available integers!\");for(;r.has(i);)i=Math.floor(Math.random()*n);return e(r,i)}},i=new WeakMap,u=r(i),c=a(u,i),l=t(c);e.addUniqueNumber=l,e.generateUniqueNumber=c,Object.defineProperty(e,\"__esModule\",{value:!0})}(t)},507:e=>{var t=function(e){var t,r,n=/\\\\w+/.exec(e);if(!n)return\"an\";var o=(r=n[0]).toLowerCase(),s=[\"honest\",\"hour\",\"hono\"];for(t in s)if(0==o.indexOf(s[t]))return\"an\";if(1==o.length)return\"aedhilmnorsx\".indexOf(o)>=0?\"an\":\"a\";if(r.match(/(?!FJO|[HLMNS]Y.|RY[EO]|SQU|(F[LR]?|[HL]|MN?|N|RH?|S[CHKLMNPTVW]?|X(YL)?)[AEIOU])[FHLMNRSX][A-Z]/))return\"an\";var a=[/^e[uw]/,/^onc?e\\\\b/,/^uni([^nmd]|mo)/,/^u[bcfhjkqrst][aeiou]/];for(t=0;t<a.length;t++)if(o.match(a[t]))return\"a\";return r.match(/^U[NK][AIEO]/)?\"a\":r==r.toUpperCase()?\"aedhilmnorsx\".indexOf(o[0])>=0?\"an\":\"a\":\"aeiou\".indexOf(o[0])>=0||o.match(/^y(b[lor]|cl[ea]|fere|gg|p[ios]|rou|tt)/)?\"an\":\"a\"};void 0!==e.exports?e.exports=t:window.indefiniteArticle=t},768:e=>{e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},907:(e,t,r)=>{var n=r(768);e.exports=function(e){if(Array.isArray(e))return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},642:e=>{e.exports=function(e){if(\"undefined\"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e[\"@@iterator\"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},344:e=>{e.exports=function(){throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")},e.exports.__esModule=!0,e.exports.default=e.exports},106:(e,t,r)=>{var n=r(907),o=r(642),s=r(906),a=r(344);e.exports=function(e){return n(e)||o(e)||s(e)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},906:(e,t,r)=>{var n=r(768);e.exports=function(e,t){if(e){if(\"string\"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return\"Object\"===r&&e.constructor&&(r=e.constructor.name),\"Map\"===r||\"Set\"===r?Array.from(e):\"Arguments\"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n].call(s.exports,s,s.exports,r),s.exports}(()=>{\"use strict\";var e=r(775);const t=-32603,n=-32602,o=-32601,s=(0,e.compile)({message:'The requested method called \"\\${method}\" is not supported.',status:o}),a=(0,e.compile)({message:'The handler of the method called \"\\${method}\" returned no required result.',status:t}),i=(0,e.compile)({message:'The handler of the method called \"\\${method}\" returned an unexpected result.',status:t}),u=(0,e.compile)({message:'The specified parameter called \"portId\" with the given value \"\\${portId}\" does not identify a port connected to this worker.',status:n}),c=(e,t)=>async r=>{let{data:{id:n,method:o,params:u}}=r;const c=t[o];try{if(void 0===c)throw s({method:o});const t=void 0===u?c():c(u);if(void 0===t)throw a({method:o});const r=t instanceof Promise?await t:t;if(null===n){if(void 0!==r.result)throw i({method:o})}else{if(void 0===r.result)throw i({method:o});const{result:t,transferables:s=[]}=r;e.postMessage({id:n,result:t},s)}}catch(t){const{message:r,status:o=-32603}=t;e.postMessage({error:{code:o,message:r},id:n})}};var l=r(107);const d=new Map,f=(e,t,r)=>({...t,connect:r=>{let{port:n}=r;n.start();const o=e(n,t),s=(0,l.generateUniqueNumber)(d);return d.set(s,(()=>{o(),n.close(),d.delete(s)})),{result:s}},disconnect:e=>{let{portId:t}=e;const r=d.get(t);if(void 0===r)throw u({portId:t.toString()});return r(),{result:null}},isSupported:async()=>{if(await new Promise((e=>{const t=new ArrayBuffer(0),{port1:r,port2:n}=new MessageChannel;r.onmessage=t=>{let{data:r}=t;return e(null!==r)},n.postMessage(t,[t])}))){const e=r();return{result:e instanceof Promise?await e:e}}return{result:!1}}}),p=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>!0;const n=f(p,t,r),o=c(e,n);return e.addEventListener(\"message\",o),()=>e.removeEventListener(\"message\",o)},m=e=>e.reduce(((e,t)=>e+t.length),0),h=(e,t)=>{const r=[];let n=0;e:for(;n<t;){const t=e.length;for(let o=0;o<t;o+=1){const t=e[o];void 0===r[o]&&(r[o]=[]);const s=t.shift();if(void 0===s)break e;r[o].push(s),0===o&&(n+=s.length)}}if(n>t){const o=n-t;r.forEach(((t,r)=>{const n=t.pop(),s=n.length-o;t.push(n.subarray(0,s)),e[r].unshift(n.subarray(s))}))}return r},v=new Map,g=(e=>(t,r,n)=>{const o=e.get(t);if(void 0===o){const o={channelDataArrays:n.map((e=>[e])),isComplete:!0,sampleRate:r};return e.set(t,o),o}return o.channelDataArrays.forEach(((e,t)=>e.push(n[t]))),o})(v),x=((e,t)=>(r,n,o,s)=>{const a=o>>3,i=\"subsequent\"===n?0:44,u=r.length,c=e(r[0]),l=new ArrayBuffer(c*u*a+i),d=new DataView(l);return\"subsequent\"!==n&&t(d,o,u,\"complete\"===n?c:Number.POSITIVE_INFINITY,s),r.forEach(((e,t)=>{let r=i+t*a;e.forEach((e=>{const t=e.length;for(let n=0;n<t;n+=1){const t=e[n];d.setInt16(r,t<0?32768*Math.max(-1,t):32767*Math.min(1,t),!0),r+=u*a}}))})),[l]})(m,((e,t,r,n,o)=>{const s=t>>3,a=Math.min(n*r*s,4294967251);e.setUint32(0,1380533830),e.setUint32(4,a+36,!0),e.setUint32(8,1463899717),e.setUint32(12,1718449184),e.setUint32(16,16,!0),e.setUint16(20,1,!0),e.setUint16(22,r,!0),e.setUint32(24,o,!0),e.setUint32(28,o*r*s,!0),e.setUint16(32,r*s,!0),e.setUint16(34,t,!0),e.setUint32(36,1684108385),e.setUint32(40,a,!0)})),w=new Map;p(self,{characterize:()=>({result:/^audio\\\\/wav$/}),encode:e=>{let{recordingId:t,timeslice:r}=e;const n=w.get(t);void 0!==n&&(w.delete(t),n.reject(new Error(\"Another request was made to initiate an encoding.\")));const o=v.get(t);if(null!==r){if(void 0===o||m(o.channelDataArrays[0])*(1e3/o.sampleRate)<r)return new Promise(((e,n)=>{w.set(t,{reject:n,resolve:e,timeslice:r})}));const e=h(o.channelDataArrays,Math.ceil(r*(o.sampleRate/1e3))),n=x(e,o.isComplete?\"initial\":\"subsequent\",16,o.sampleRate);return o.isComplete=!1,{result:n,transferables:n}}if(void 0!==o){const e=x(o.channelDataArrays,o.isComplete?\"complete\":\"subsequent\",16,o.sampleRate);return v.delete(t),{result:e,transferables:e}}return{result:[],transferables:[]}},record:e=>{let{recordingId:t,sampleRate:r,typedArrays:n}=e;const o=g(t,r,n),s=w.get(t);if(void 0!==s&&m(o.channelDataArrays[0])*(1e3/r)>=s.timeslice){const e=h(o.channelDataArrays,Math.ceil(s.timeslice*(r/1e3))),n=x(e,o.isComplete?\"initial\":\"subsequent\",16,r);o.isComplete=!1,w.delete(t),s.resolve({result:n,transferables:n})}return{result:null}}})})()})();`; // tslint:disable-line:max-line-length\n//# sourceMappingURL=worker.js.map", "import { load } from 'extendable-media-recorder-wav-encoder-broker';\nimport { worker } from './worker/worker';\nconst blob = new Blob([worker], { type: 'application/javascript; charset=utf-8' });\nconst url = URL.createObjectURL(blob);\nconst extendableMediaRecorderWavEncoder = load(url);\nexport const characterize = extendableMediaRecorderWavEncoder.characterize;\nexport const connect = extendableMediaRecorderWavEncoder.connect;\nexport const disconnect = extendableMediaRecorderWavEncoder.disconnect;\nexport const encode = extendableMediaRecorderWavEncoder.encode;\nexport const isSupported = extendableMediaRecorderWavEncoder.isSupported;\nexport const record = extendableMediaRecorderWavEncoder.record;\nURL.revokeObjectURL(url);\n//# sourceMappingURL=module.js.map"], "names": ["createCache", "lastNumberWeakMap", "collection", "nextNumber", "MAX_SAFE_INTEGER", "TWO_TO_THE_POWER_OF_TWENTY_NINE", "TWO_TO_THE_POWER_OF_THIRTY", "createGenerateUniqueNumber", "cache", "lastNumber", "LAST_NUMBER_WEAK_MAP", "generateUniqueNumber", "isMessagePort", "sender", "PORT_MAP", "extendBrokerImplementation", "partialBrokerImplementation", "call", "port1", "port2", "portId", "port", "ONGOING_REQUESTS", "createOrGetOngoingRequests", "ongoingRequests", "createBroker", "brokerImplementation", "fullBrokerImplementation", "message", "id", "reject", "resolve", "method", "params", "transferables", "notify", "functions", "key", "handler", "wrap", "recordingId", "timeslice", "sampleRate", "typedArrays", "buffer", "load", "url", "worker", "blob", "extendableMediaRecorderWavEncoder", "characterize", "connect", "disconnect", "encode", "isSupported", "record"], "mappings": "AAAO,MAAMA,EAAeC,GACjB,CAACC,EAAYC,KAChBF,EAAkB,IAAIC,EAAYC,CAAU,EACrCA,GCCTC,EAAmB,OAAO,mBAAqB,OAAY,iBAAmB,OAAO,iBACrFC,EAAkC,UAClCC,EAA6BD,EAAkC,EACxDE,EAA6B,CAACC,EAAOP,IACtCC,GAAe,CACnB,MAAMO,EAAaR,EAAkB,IAAIC,CAAU,EASnD,IAAIC,EAAaM,IAAe,OAAYP,EAAW,KAAOO,EAAaH,EAA6BG,EAAa,EAAI,EACzH,GAAI,CAACP,EAAW,IAAIC,CAAU,EAC1B,OAAOK,EAAMN,EAAYC,CAAU,EAQvC,GAAID,EAAW,KAAOG,EAAiC,CACnD,KAAOH,EAAW,IAAIC,CAAU,GAC5BA,EAAa,KAAK,MAAM,KAAK,OAAM,EAAKG,CAA0B,EAEtE,OAAOE,EAAMN,EAAYC,CAAU,EAGvC,GAAID,EAAW,KAAOE,EAClB,MAAM,IAAI,MAAM,gGAAgG,EAGpH,KAAOF,EAAW,IAAIC,CAAU,GAC5BA,EAAa,KAAK,MAAM,KAAK,OAAM,EAAKC,CAAgB,EAE5D,OAAOI,EAAMN,EAAYC,CAAU,CAC3C,ECnCMO,EAAuB,IAAI,QAC3BF,EAAQR,EAAYU,CAAoB,EACxCC,EAAuBJ,EAA2BC,EAAOE,CAAoB,ECVtEE,EAAiBC,GACnB,OAAOA,EAAO,OAAU,WCDtBC,EAAW,IAAI,QCCfC,EAA8BC,IAAiC,CACxE,GAAGA,EACH,QAAS,CAAC,CAAE,KAAAC,KACD,SAAY,CACf,KAAM,CAAE,MAAAC,EAAO,MAAAC,GAAU,IAAI,eACvBC,EAAS,MAAMH,EAAK,UAAW,CAAE,KAAMC,CAAO,EAAE,CAACA,CAAK,CAAC,EAC7D,OAAAJ,EAAS,IAAIK,EAAOC,CAAM,EACnBD,CACnB,EAEI,WAAY,CAAC,CAAE,KAAAF,KACJ,MAAOI,GAAS,CACnB,MAAMD,EAASN,EAAS,IAAIO,CAAI,EAChC,GAAID,IAAW,OACX,MAAM,IAAI,MAAM,kCAAkC,EAEtD,MAAMH,EAAK,aAAc,CAAE,OAAAG,CAAQ,CAAA,CAC/C,EAEI,YAAa,CAAC,CAAE,KAAAH,KACL,IAAMA,EAAK,aAAa,CAEvC,GCdMK,EAAmB,IAAI,QACvBC,EAA8BV,GAAW,CAC3C,GAAIS,EAAiB,IAAIT,CAAM,EAE3B,OAAOS,EAAiB,IAAIT,CAAM,EAEtC,MAAMW,EAAkB,IAAI,IAC5B,OAAAF,EAAiB,IAAIT,EAAQW,CAAe,EACrCA,CACX,EACaC,EAAgBC,GAAyB,CAClD,MAAMC,EAA2BZ,EAA2BW,CAAoB,EAChF,OAAQb,GAAW,CACf,MAAMW,EAAkBD,EAA2BV,CAAM,EACzDA,EAAO,iBAAiB,UAAY,CAAC,CAAE,KAAMe,CAAO,IAAO,CACvD,KAAM,CAAE,GAAAC,CAAI,EAAGD,EACf,GAAIC,IAAO,MAAQL,EAAgB,IAAIK,CAAE,EAAG,CACxC,KAAM,CAAE,OAAAC,EAAQ,QAAAC,CAAO,EAAKP,EAAgB,IAAIK,CAAE,EAClDL,EAAgB,OAAOK,CAAE,EACrBD,EAAQ,QAAU,OAClBG,EAAQH,EAAQ,MAAM,EAGtBE,EAAO,IAAI,MAAMF,EAAQ,MAAM,OAAO,CAAC,EAG3D,GACYhB,EAAcC,CAAM,GACpBA,EAAO,MAAK,EAEhB,MAAMI,EAAO,CAACe,EAAQC,EAAS,KAAMC,EAAgB,KAC1C,IAAI,QAAQ,CAACH,EAASD,IAAW,CACpC,MAAMD,EAAKlB,EAAqBa,CAAe,EAC/CA,EAAgB,IAAIK,EAAI,CAAE,OAAAC,EAAQ,QAAAC,CAAS,CAAA,EACvCE,IAAW,KACXpB,EAAO,YAAY,CAAE,GAAAgB,EAAI,OAAAG,CAAQ,EAAEE,CAAa,EAGhDrB,EAAO,YAAY,CAAE,GAAAgB,EAAI,OAAAG,EAAQ,OAAAC,CAAM,EAAIC,CAAa,CAE5E,CAAa,EAECC,EAAS,CAACH,EAAQC,EAAQC,EAAgB,CAAA,IAAO,CACnDrB,EAAO,YAAY,CAAE,GAAI,KAAM,OAAAmB,EAAQ,OAAAC,CAAM,EAAIC,CAAa,CAC1E,EACQ,IAAIE,EAAY,CAAA,EAChB,SAAW,CAACC,EAAKC,CAAO,IAAK,OAAO,QAAQX,CAAwB,EAChES,EAAY,CAAE,GAAGA,EAAW,CAACC,CAAG,EAAGC,EAAQ,CAAE,KAAArB,EAAM,OAAAkB,CAAQ,CAAA,GAE/D,MAAO,CAAE,GAAGC,EACpB,CACA,ECrDaG,EAAOd,EAAa,CAC7B,aAAc,CAAC,CAAE,KAAAR,KACN,IAAMA,EAAK,cAAc,EAEpC,OAAQ,CAAC,CAAE,KAAAA,KACA,CAACuB,EAAaC,IACVxB,EAAK,SAAU,CAAE,YAAAuB,EAAa,UAAAC,CAAW,CAAA,EAGxD,OAAQ,CAAC,CAAE,KAAAxB,KACA,MAAOuB,EAAaE,EAAYC,IAAgB,CACnD,MAAM1B,EAAK,SAAU,CAAE,YAAAuB,EAAa,WAAAE,EAAY,YAAAC,CAAW,EAAIA,EAAY,IAAI,CAAC,CAAE,OAAAC,CAAM,IAAOA,CAAM,CAAC,CAClH,CAEA,CAAC,EACYC,EAAQC,GAAQ,CACzB,MAAMC,EAAS,IAAI,OAAOD,CAAG,EAC7B,OAAOP,EAAKQ,CAAM,CACtB,ECxBaA,EAAS,0rSCChBC,EAAO,IAAI,KAAK,CAACD,CAAM,EAAG,CAAE,KAAM,uCAAuC,CAAE,EAC3ED,EAAM,IAAI,gBAAgBE,CAAI,EAC9BC,EAAoCJ,EAAKC,CAAG,EACrCI,EAAeD,EAAkC,aACjDE,EAAUF,EAAkC,QAC5CG,EAAaH,EAAkC,WAC/CI,EAASJ,EAAkC,OAC3CK,EAAcL,EAAkC,YAChDM,EAASN,EAAkC,OACxD,IAAI,gBAAgBH,CAAG", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}