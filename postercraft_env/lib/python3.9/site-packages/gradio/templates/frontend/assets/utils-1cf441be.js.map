{"version": 3, "file": "utils-1cf441be.js", "sources": ["../../../../js/icons/src/TextHighlight.svelte", "../../../../js/highlightedtext/utils.ts"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z\"\n\t/>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z\"\n\t/>\n</svg>\n", "import { colors } from \"@gradio/theme\";\n\ntype HighlightValueType = [string, string | number | null];\n\nexport function name_to_rgba(\n\tname: string,\n\ta: number,\n\tctx: CanvasRenderingContext2D | null\n): string {\n\tif (!ctx) {\n\t\tvar canvas = document.createElement(\"canvas\");\n\t\tctx = canvas.getContext(\"2d\")!;\n\t}\n\tctx.fillStyle = name;\n\tctx.fillRect(0, 0, 1, 1);\n\tconst [r, g, b] = ctx.getImageData(0, 0, 1, 1).data;\n\tctx.clearRect(0, 0, 1, 1);\n\treturn `rgba(${r}, ${g}, ${b}, ${255 / a})`;\n}\n\nexport function correct_color_map(\n\tcolor_map: Record<string, string>,\n\t_color_map: Record<string, { primary: string; secondary: string }>,\n\tbrowser: any,\n\tctx: CanvasRenderingContext2D | null\n): void {\n\tfor (const col in color_map) {\n\t\tconst _c = color_map[col].trim();\n\n\t\tif (_c in colors) {\n\t\t\t_color_map[col] = colors[_c as keyof typeof colors];\n\t\t} else {\n\t\t\t_color_map[col] = {\n\t\t\t\tprimary: browser\n\t\t\t\t\t? name_to_rgba(color_map[col], 1, ctx)\n\t\t\t\t\t: color_map[col],\n\t\t\t\tsecondary: browser\n\t\t\t\t\t? name_to_rgba(color_map[col], 0.5, ctx)\n\t\t\t\t\t: color_map[col]\n\t\t\t};\n\t\t}\n\t}\n}\n\nexport function merge_elements(\n\tvalue: HighlightValueType[],\n\tmergeMode: \"empty\" | \"equal\"\n): HighlightValueType[] {\n\tlet result: HighlightValueType[] = [];\n\tlet tempStr: string | null = null;\n\tlet tempVal: string | number | null = null;\n\n\tfor (const [str, val] of value) {\n\t\tif (\n\t\t\t(mergeMode === \"empty\" && val === null) ||\n\t\t\t(mergeMode === \"equal\" && tempVal === val)\n\t\t) {\n\t\t\ttempStr = tempStr ? tempStr + str : str;\n\t\t} else {\n\t\t\tif (tempStr !== null) {\n\t\t\t\tresult.push([tempStr, tempVal as string | number]);\n\t\t\t}\n\t\t\ttempStr = str;\n\t\t\ttempVal = val;\n\t\t}\n\t}\n\n\tif (tempStr !== null) {\n\t\tresult.push([tempStr, tempVal as string | number]);\n\t}\n\n\treturn result;\n}\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "name_to_rgba", "name", "a", "ctx", "canvas", "r", "g", "b", "correct_color_map", "color_map", "_color_map", "browser", "col", "_c", "colors", "merge_elements", "value", "mergeMode", "result", "tempStr", "tempVal", "str", "val"], "mappings": "gyBAAAA,EAmBKC,EAAAC,EAAAC,CAAA,EARJC,EAGCF,EAAAG,CAAA,EACDD,EAGCF,EAAAI,CAAA,8FCdc,SAAAC,EACfC,EACAC,EACAC,EACS,CACT,GAAI,CAACA,EAAK,CACL,IAAAC,EAAS,SAAS,cAAc,QAAQ,EACtCD,EAAAC,EAAO,WAAW,IAAI,EAE7BD,EAAI,UAAYF,EAChBE,EAAI,SAAS,EAAG,EAAG,EAAG,CAAC,EACjB,KAAA,CAACE,EAAGC,EAAGC,CAAC,EAAIJ,EAAI,aAAa,EAAG,EAAG,EAAG,CAAC,EAAE,KAC/C,OAAAA,EAAI,UAAU,EAAG,EAAG,EAAG,CAAC,EACjB,QAAQE,MAAMC,MAAMC,MAAM,IAAML,IACxC,CAEO,SAASM,EACfC,EACAC,EACAC,EACAR,EACO,CACP,UAAWS,KAAOH,EAAW,CAC5B,MAAMI,EAAKJ,EAAUG,CAAG,EAAE,KAAK,EAE3BC,KAAMC,EACEJ,EAAAE,CAAG,EAAIE,EAAOD,CAAyB,EAElDH,EAAWE,CAAG,EAAI,CACjB,QAASD,EACNX,EAAaS,EAAUG,CAAG,EAAG,EAAGT,CAAG,EACnCM,EAAUG,CAAG,EAChB,UAAWD,EACRX,EAAaS,EAAUG,CAAG,EAAG,GAAKT,CAAG,EACrCM,EAAUG,CAAG,CAAA,EAIpB,CAEgB,SAAAG,EACfC,EACAC,EACuB,CACvB,IAAIC,EAA+B,CAAA,EAC/BC,EAAyB,KACzBC,EAAkC,KAEtC,SAAW,CAACC,EAAKC,CAAG,IAAKN,EAEtBC,IAAc,SAAWK,IAAQ,MACjCL,IAAc,SAAWG,IAAYE,EAE5BH,EAAAA,EAAUA,EAAUE,EAAMA,GAEhCF,IAAY,MACfD,EAAO,KAAK,CAACC,EAASC,CAA0B,CAAC,EAExCD,EAAAE,EACAD,EAAAE,GAIZ,OAAIH,IAAY,MACfD,EAAO,KAAK,CAACC,EAASC,CAA0B,CAAC,EAG3CF,CACR"}