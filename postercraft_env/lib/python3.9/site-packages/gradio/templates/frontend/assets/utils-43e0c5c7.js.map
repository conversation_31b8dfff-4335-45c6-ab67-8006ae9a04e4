{"version": 3, "file": "utils-43e0c5c7.js", "sources": ["../../../../js/icons/src/Music.svelte", "../../../../js/audio/shared/utils.ts"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-music\"\n>\n\t<path d=\"M9 18V5l12-2v13\" />\n\t<circle cx=\"6\" cy=\"18\" r=\"3\" />\n\t<circle cx=\"18\" cy=\"16\" r=\"3\" />\n</svg>\n", "import type { ActionReturn } from \"svelte/action\";\n\nexport interface LoadedParams {\n\tcrop_values?: [number, number];\n\tautoplay?: boolean;\n}\n\nexport function loaded(\n\tnode: HTMLAudioElement,\n\t{ crop_values, autoplay }: LoadedParams = {}\n): ActionReturn {\n\tfunction clamp_playback(): void {\n\t\tif (crop_values === undefined) return;\n\n\t\tconst start_time = (crop_values[0] / 100) * node.duration;\n\t\tconst end_time = (crop_values[1] / 100) * node.duration;\n\n\t\tif (node.currentTime < start_time) {\n\t\t\tnode.currentTime = start_time;\n\t\t}\n\n\t\tif (node.currentTime > end_time) {\n\t\t\tnode.currentTime = start_time;\n\t\t\tnode.pause();\n\t\t}\n\t}\n\n\tasync function handle_playback(): Promise<void> {\n\t\tif (!autoplay) return;\n\n\t\tnode.pause();\n\t\tawait node.play();\n\t}\n\n\tnode.addEventListener(\"loadeddata\", handle_playback);\n\tnode.addEventListener(\"timeupdate\", clamp_playback);\n\n\treturn {\n\t\tdestroy(): void {\n\t\t\tnode.removeEventListener(\"loadeddata\", handle_playback);\n\t\t\tnode.removeEventListener(\"timeupdate\", clamp_playback);\n\t\t}\n\t};\n}\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "circle0", "circle1", "loaded", "node", "crop_values", "autoplay", "clamp_playback", "start_time", "end_time", "handle_playback"], "mappings": "0kBAAAA,EAeKC,EAAAC,EAAAC,CAAA,EAHJC,EAA2BF,EAAAG,CAAA,EAC3BD,EAA8BF,EAAAI,CAAA,EAC9BF,EAA+BF,EAAAK,CAAA,4FCPzB,SAASC,EACfC,EACA,CAAE,YAAAC,EAAa,SAAAC,CAAS,EAAkB,CAAA,EAC3B,CACf,SAASC,GAAuB,CAC/B,GAAIF,IAAgB,OAAW,OAE/B,MAAMG,EAAcH,EAAY,CAAC,EAAI,IAAOD,EAAK,SAC3CK,EAAYJ,EAAY,CAAC,EAAI,IAAOD,EAAK,SAE3CA,EAAK,YAAcI,IACtBJ,EAAK,YAAcI,GAGhBJ,EAAK,YAAcK,IACtBL,EAAK,YAAcI,EACnBJ,EAAK,MAAM,EAEb,CAEA,eAAeM,GAAiC,CAC1CJ,IAELF,EAAK,MAAM,EACX,MAAMA,EAAK,OACZ,CAEK,OAAAA,EAAA,iBAAiB,aAAcM,CAAe,EAC9CN,EAAA,iBAAiB,aAAcG,CAAc,EAE3C,CACN,SAAgB,CACVH,EAAA,oBAAoB,aAAcM,CAAe,EACjDN,EAAA,oBAAoB,aAAcG,CAAc,CACtD,CAAA,CAEF"}