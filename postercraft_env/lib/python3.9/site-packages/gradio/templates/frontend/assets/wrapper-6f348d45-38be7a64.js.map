{"version": 3, "file": "wrapper-6f348d45-38be7a64.js", "sources": ["../../../../client/js/dist/wrapper-6f348d45.js"], "sourcesContent": ["import require$$0 from \"stream\";\nimport require$$0$1 from \"zlib\";\nimport require$$0$2 from \"buffer\";\nimport require$$3 from \"net\";\nimport require$$4 from \"tls\";\nimport require$$5 from \"crypto\";\nimport require$$0$3 from \"events\";\nimport require$$1$1 from \"https\";\nimport require$$2 from \"http\";\nimport require$$7 from \"url\";\nfunction getDefaultExportFromCjs(x) {\n  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, \"default\") ? x[\"default\"] : x;\n}\nfunction getAugmentedNamespace(n) {\n  if (n.__esModule)\n    return n;\n  var f = n.default;\n  if (typeof f == \"function\") {\n    var a = function a2() {\n      if (this instanceof a2) {\n        var args = [null];\n        args.push.apply(args, arguments);\n        var Ctor = Function.bind.apply(f, args);\n        return new Ctor();\n      }\n      return f.apply(this, arguments);\n    };\n    a.prototype = f.prototype;\n  } else\n    a = {};\n  Object.defineProperty(a, \"__esModule\", { value: true });\n  Object.keys(n).forEach(function(k) {\n    var d = Object.getOwnPropertyDescriptor(n, k);\n    Object.defineProperty(a, k, d.get ? d : {\n      enumerable: true,\n      get: function() {\n        return n[k];\n      }\n    });\n  });\n  return a;\n}\nconst { Duplex } = require$$0;\nfunction emitClose$1(stream2) {\n  stream2.emit(\"close\");\n}\nfunction duplexOnEnd() {\n  if (!this.destroyed && this._writableState.finished) {\n    this.destroy();\n  }\n}\nfunction duplexOnError(err) {\n  this.removeListener(\"error\", duplexOnError);\n  this.destroy();\n  if (this.listenerCount(\"error\") === 0) {\n    this.emit(\"error\", err);\n  }\n}\nfunction createWebSocketStream(ws, options) {\n  let terminateOnDestroy = true;\n  const duplex = new Duplex({\n    ...options,\n    autoDestroy: false,\n    emitClose: false,\n    objectMode: false,\n    writableObjectMode: false\n  });\n  ws.on(\"message\", function message(msg, isBinary) {\n    const data = !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;\n    if (!duplex.push(data))\n      ws.pause();\n  });\n  ws.once(\"error\", function error2(err) {\n    if (duplex.destroyed)\n      return;\n    terminateOnDestroy = false;\n    duplex.destroy(err);\n  });\n  ws.once(\"close\", function close() {\n    if (duplex.destroyed)\n      return;\n    duplex.push(null);\n  });\n  duplex._destroy = function(err, callback) {\n    if (ws.readyState === ws.CLOSED) {\n      callback(err);\n      process.nextTick(emitClose$1, duplex);\n      return;\n    }\n    let called = false;\n    ws.once(\"error\", function error2(err2) {\n      called = true;\n      callback(err2);\n    });\n    ws.once(\"close\", function close() {\n      if (!called)\n        callback(err);\n      process.nextTick(emitClose$1, duplex);\n    });\n    if (terminateOnDestroy)\n      ws.terminate();\n  };\n  duplex._final = function(callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once(\"open\", function open() {\n        duplex._final(callback);\n      });\n      return;\n    }\n    if (ws._socket === null)\n      return;\n    if (ws._socket._writableState.finished) {\n      callback();\n      if (duplex._readableState.endEmitted)\n        duplex.destroy();\n    } else {\n      ws._socket.once(\"finish\", function finish() {\n        callback();\n      });\n      ws.close();\n    }\n  };\n  duplex._read = function() {\n    if (ws.isPaused)\n      ws.resume();\n  };\n  duplex._write = function(chunk, encoding, callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once(\"open\", function open() {\n        duplex._write(chunk, encoding, callback);\n      });\n      return;\n    }\n    ws.send(chunk, callback);\n  };\n  duplex.on(\"end\", duplexOnEnd);\n  duplex.on(\"error\", duplexOnError);\n  return duplex;\n}\nvar stream = createWebSocketStream;\nconst stream$1 = /* @__PURE__ */ getDefaultExportFromCjs(stream);\nvar bufferUtil$1 = { exports: {} };\nvar constants = {\n  BINARY_TYPES: [\"nodebuffer\", \"arraybuffer\", \"fragments\"],\n  EMPTY_BUFFER: Buffer.alloc(0),\n  GUID: \"258EAFA5-E914-47DA-95CA-C5AB0DC85B11\",\n  kForOnEventAttribute: Symbol(\"kIsForOnEventAttribute\"),\n  kListener: Symbol(\"kListener\"),\n  kStatusCode: Symbol(\"status-code\"),\n  kWebSocket: Symbol(\"websocket\"),\n  NOOP: () => {\n  }\n};\nvar unmask$1;\nvar mask;\nconst { EMPTY_BUFFER: EMPTY_BUFFER$3 } = constants;\nconst FastBuffer$2 = Buffer[Symbol.species];\nfunction concat$1(list, totalLength) {\n  if (list.length === 0)\n    return EMPTY_BUFFER$3;\n  if (list.length === 1)\n    return list[0];\n  const target = Buffer.allocUnsafe(totalLength);\n  let offset = 0;\n  for (let i = 0; i < list.length; i++) {\n    const buf = list[i];\n    target.set(buf, offset);\n    offset += buf.length;\n  }\n  if (offset < totalLength) {\n    return new FastBuffer$2(target.buffer, target.byteOffset, offset);\n  }\n  return target;\n}\nfunction _mask(source, mask2, output, offset, length) {\n  for (let i = 0; i < length; i++) {\n    output[offset + i] = source[i] ^ mask2[i & 3];\n  }\n}\nfunction _unmask(buffer, mask2) {\n  for (let i = 0; i < buffer.length; i++) {\n    buffer[i] ^= mask2[i & 3];\n  }\n}\nfunction toArrayBuffer$1(buf) {\n  if (buf.length === buf.buffer.byteLength) {\n    return buf.buffer;\n  }\n  return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\nfunction toBuffer$2(data) {\n  toBuffer$2.readOnly = true;\n  if (Buffer.isBuffer(data))\n    return data;\n  let buf;\n  if (data instanceof ArrayBuffer) {\n    buf = new FastBuffer$2(data);\n  } else if (ArrayBuffer.isView(data)) {\n    buf = new FastBuffer$2(data.buffer, data.byteOffset, data.byteLength);\n  } else {\n    buf = Buffer.from(data);\n    toBuffer$2.readOnly = false;\n  }\n  return buf;\n}\nbufferUtil$1.exports = {\n  concat: concat$1,\n  mask: _mask,\n  toArrayBuffer: toArrayBuffer$1,\n  toBuffer: toBuffer$2,\n  unmask: _unmask\n};\nif (!process.env.WS_NO_BUFFER_UTIL) {\n  try {\n    const bufferUtil2 = require(\"bufferutil\");\n    mask = bufferUtil$1.exports.mask = function(source, mask2, output, offset, length) {\n      if (length < 48)\n        _mask(source, mask2, output, offset, length);\n      else\n        bufferUtil2.mask(source, mask2, output, offset, length);\n    };\n    unmask$1 = bufferUtil$1.exports.unmask = function(buffer, mask2) {\n      if (buffer.length < 32)\n        _unmask(buffer, mask2);\n      else\n        bufferUtil2.unmask(buffer, mask2);\n    };\n  } catch (e) {\n  }\n}\nvar bufferUtilExports = bufferUtil$1.exports;\nconst kDone = Symbol(\"kDone\");\nconst kRun = Symbol(\"kRun\");\nlet Limiter$1 = class Limiter {\n  /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */\n  constructor(concurrency) {\n    this[kDone] = () => {\n      this.pending--;\n      this[kRun]();\n    };\n    this.concurrency = concurrency || Infinity;\n    this.jobs = [];\n    this.pending = 0;\n  }\n  /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */\n  add(job) {\n    this.jobs.push(job);\n    this[kRun]();\n  }\n  /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */\n  [kRun]() {\n    if (this.pending === this.concurrency)\n      return;\n    if (this.jobs.length) {\n      const job = this.jobs.shift();\n      this.pending++;\n      job(this[kDone]);\n    }\n  }\n};\nvar limiter = Limiter$1;\nconst zlib = require$$0$1;\nconst bufferUtil = bufferUtilExports;\nconst Limiter2 = limiter;\nconst { kStatusCode: kStatusCode$2 } = constants;\nconst FastBuffer$1 = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([0, 0, 255, 255]);\nconst kPerMessageDeflate = Symbol(\"permessage-deflate\");\nconst kTotalLength = Symbol(\"total-length\");\nconst kCallback = Symbol(\"callback\");\nconst kBuffers = Symbol(\"buffers\");\nconst kError$1 = Symbol(\"error\");\nlet zlibLimiter;\nlet PerMessageDeflate$4 = class PerMessageDeflate {\n  /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */\n  constructor(options, isServer, maxPayload) {\n    this._maxPayload = maxPayload | 0;\n    this._options = options || {};\n    this._threshold = this._options.threshold !== void 0 ? this._options.threshold : 1024;\n    this._isServer = !!isServer;\n    this._deflate = null;\n    this._inflate = null;\n    this.params = null;\n    if (!zlibLimiter) {\n      const concurrency = this._options.concurrencyLimit !== void 0 ? this._options.concurrencyLimit : 10;\n      zlibLimiter = new Limiter2(concurrency);\n    }\n  }\n  /**\n   * @type {String}\n   */\n  static get extensionName() {\n    return \"permessage-deflate\";\n  }\n  /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */\n  offer() {\n    const params = {};\n    if (this._options.serverNoContextTakeover) {\n      params.server_no_context_takeover = true;\n    }\n    if (this._options.clientNoContextTakeover) {\n      params.client_no_context_takeover = true;\n    }\n    if (this._options.serverMaxWindowBits) {\n      params.server_max_window_bits = this._options.serverMaxWindowBits;\n    }\n    if (this._options.clientMaxWindowBits) {\n      params.client_max_window_bits = this._options.clientMaxWindowBits;\n    } else if (this._options.clientMaxWindowBits == null) {\n      params.client_max_window_bits = true;\n    }\n    return params;\n  }\n  /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */\n  accept(configurations) {\n    configurations = this.normalizeParams(configurations);\n    this.params = this._isServer ? this.acceptAsServer(configurations) : this.acceptAsClient(configurations);\n    return this.params;\n  }\n  /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */\n  cleanup() {\n    if (this._inflate) {\n      this._inflate.close();\n      this._inflate = null;\n    }\n    if (this._deflate) {\n      const callback = this._deflate[kCallback];\n      this._deflate.close();\n      this._deflate = null;\n      if (callback) {\n        callback(\n          new Error(\n            \"The deflate stream was closed while data was being processed\"\n          )\n        );\n      }\n    }\n  }\n  /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsServer(offers) {\n    const opts = this._options;\n    const accepted = offers.find((params) => {\n      if (opts.serverNoContextTakeover === false && params.server_no_context_takeover || params.server_max_window_bits && (opts.serverMaxWindowBits === false || typeof opts.serverMaxWindowBits === \"number\" && opts.serverMaxWindowBits > params.server_max_window_bits) || typeof opts.clientMaxWindowBits === \"number\" && !params.client_max_window_bits) {\n        return false;\n      }\n      return true;\n    });\n    if (!accepted) {\n      throw new Error(\"None of the extension offers can be accepted\");\n    }\n    if (opts.serverNoContextTakeover) {\n      accepted.server_no_context_takeover = true;\n    }\n    if (opts.clientNoContextTakeover) {\n      accepted.client_no_context_takeover = true;\n    }\n    if (typeof opts.serverMaxWindowBits === \"number\") {\n      accepted.server_max_window_bits = opts.serverMaxWindowBits;\n    }\n    if (typeof opts.clientMaxWindowBits === \"number\") {\n      accepted.client_max_window_bits = opts.clientMaxWindowBits;\n    } else if (accepted.client_max_window_bits === true || opts.clientMaxWindowBits === false) {\n      delete accepted.client_max_window_bits;\n    }\n    return accepted;\n  }\n  /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsClient(response) {\n    const params = response[0];\n    if (this._options.clientNoContextTakeover === false && params.client_no_context_takeover) {\n      throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n    }\n    if (!params.client_max_window_bits) {\n      if (typeof this._options.clientMaxWindowBits === \"number\") {\n        params.client_max_window_bits = this._options.clientMaxWindowBits;\n      }\n    } else if (this._options.clientMaxWindowBits === false || typeof this._options.clientMaxWindowBits === \"number\" && params.client_max_window_bits > this._options.clientMaxWindowBits) {\n      throw new Error(\n        'Unexpected or invalid parameter \"client_max_window_bits\"'\n      );\n    }\n    return params;\n  }\n  /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */\n  normalizeParams(configurations) {\n    configurations.forEach((params) => {\n      Object.keys(params).forEach((key) => {\n        let value = params[key];\n        if (value.length > 1) {\n          throw new Error(`Parameter \"${key}\" must have only a single value`);\n        }\n        value = value[0];\n        if (key === \"client_max_window_bits\") {\n          if (value !== true) {\n            const num = +value;\n            if (!Number.isInteger(num) || num < 8 || num > 15) {\n              throw new TypeError(\n                `Invalid value for parameter \"${key}\": ${value}`\n              );\n            }\n            value = num;\n          } else if (!this._isServer) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else if (key === \"server_max_window_bits\") {\n          const num = +value;\n          if (!Number.isInteger(num) || num < 8 || num > 15) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n          value = num;\n        } else if (key === \"client_no_context_takeover\" || key === \"server_no_context_takeover\") {\n          if (value !== true) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else {\n          throw new Error(`Unknown parameter \"${key}\"`);\n        }\n        params[key] = value;\n      });\n    });\n    return configurations;\n  }\n  /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  decompress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._decompress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n  /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  compress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._compress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n  /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _decompress(data, fin, callback) {\n    const endpoint = this._isServer ? \"client\" : \"server\";\n    if (!this._inflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n      this._inflate = zlib.createInflateRaw({\n        ...this._options.zlibInflateOptions,\n        windowBits\n      });\n      this._inflate[kPerMessageDeflate] = this;\n      this._inflate[kTotalLength] = 0;\n      this._inflate[kBuffers] = [];\n      this._inflate.on(\"error\", inflateOnError);\n      this._inflate.on(\"data\", inflateOnData);\n    }\n    this._inflate[kCallback] = callback;\n    this._inflate.write(data);\n    if (fin)\n      this._inflate.write(TRAILER);\n    this._inflate.flush(() => {\n      const err = this._inflate[kError$1];\n      if (err) {\n        this._inflate.close();\n        this._inflate = null;\n        callback(err);\n        return;\n      }\n      const data2 = bufferUtil.concat(\n        this._inflate[kBuffers],\n        this._inflate[kTotalLength]\n      );\n      if (this._inflate._readableState.endEmitted) {\n        this._inflate.close();\n        this._inflate = null;\n      } else {\n        this._inflate[kTotalLength] = 0;\n        this._inflate[kBuffers] = [];\n        if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n          this._inflate.reset();\n        }\n      }\n      callback(null, data2);\n    });\n  }\n  /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _compress(data, fin, callback) {\n    const endpoint = this._isServer ? \"server\" : \"client\";\n    if (!this._deflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n      this._deflate = zlib.createDeflateRaw({\n        ...this._options.zlibDeflateOptions,\n        windowBits\n      });\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n      this._deflate.on(\"data\", deflateOnData);\n    }\n    this._deflate[kCallback] = callback;\n    this._deflate.write(data);\n    this._deflate.flush(zlib.Z_SYNC_FLUSH, () => {\n      if (!this._deflate) {\n        return;\n      }\n      let data2 = bufferUtil.concat(\n        this._deflate[kBuffers],\n        this._deflate[kTotalLength]\n      );\n      if (fin) {\n        data2 = new FastBuffer$1(data2.buffer, data2.byteOffset, data2.length - 4);\n      }\n      this._deflate[kCallback] = null;\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n      if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n        this._deflate.reset();\n      }\n      callback(null, data2);\n    });\n  }\n};\nvar permessageDeflate = PerMessageDeflate$4;\nfunction deflateOnData(chunk) {\n  this[kBuffers].push(chunk);\n  this[kTotalLength] += chunk.length;\n}\nfunction inflateOnData(chunk) {\n  this[kTotalLength] += chunk.length;\n  if (this[kPerMessageDeflate]._maxPayload < 1 || this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload) {\n    this[kBuffers].push(chunk);\n    return;\n  }\n  this[kError$1] = new RangeError(\"Max payload size exceeded\");\n  this[kError$1].code = \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\";\n  this[kError$1][kStatusCode$2] = 1009;\n  this.removeListener(\"data\", inflateOnData);\n  this.reset();\n}\nfunction inflateOnError(err) {\n  this[kPerMessageDeflate]._inflate = null;\n  err[kStatusCode$2] = 1007;\n  this[kCallback](err);\n}\nvar validation = { exports: {} };\nconst __viteOptionalPeerDep_utf8Validate_ws = {};\nconst __viteOptionalPeerDep_utf8Validate_ws$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  default: __viteOptionalPeerDep_utf8Validate_ws\n}, Symbol.toStringTag, { value: \"Module\" }));\nconst require$$1 = /* @__PURE__ */ getAugmentedNamespace(__viteOptionalPeerDep_utf8Validate_ws$1);\nvar isValidUTF8_1;\nconst { isUtf8 } = require$$0$2;\nconst tokenChars$2 = [\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  // 0 - 15\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  // 16 - 31\n  0,\n  1,\n  0,\n  1,\n  1,\n  1,\n  1,\n  1,\n  0,\n  0,\n  1,\n  1,\n  0,\n  1,\n  1,\n  0,\n  // 32 - 47\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  // 48 - 63\n  0,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  // 64 - 79\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  0,\n  0,\n  0,\n  1,\n  1,\n  // 80 - 95\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  // 96 - 111\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  0,\n  1,\n  0,\n  1,\n  0\n  // 112 - 127\n];\nfunction isValidStatusCode$2(code) {\n  return code >= 1e3 && code <= 1014 && code !== 1004 && code !== 1005 && code !== 1006 || code >= 3e3 && code <= 4999;\n}\nfunction _isValidUTF8(buf) {\n  const len = buf.length;\n  let i = 0;\n  while (i < len) {\n    if ((buf[i] & 128) === 0) {\n      i++;\n    } else if ((buf[i] & 224) === 192) {\n      if (i + 1 === len || (buf[i + 1] & 192) !== 128 || (buf[i] & 254) === 192) {\n        return false;\n      }\n      i += 2;\n    } else if ((buf[i] & 240) === 224) {\n      if (i + 2 >= len || (buf[i + 1] & 192) !== 128 || (buf[i + 2] & 192) !== 128 || buf[i] === 224 && (buf[i + 1] & 224) === 128 || // Overlong\n      buf[i] === 237 && (buf[i + 1] & 224) === 160) {\n        return false;\n      }\n      i += 3;\n    } else if ((buf[i] & 248) === 240) {\n      if (i + 3 >= len || (buf[i + 1] & 192) !== 128 || (buf[i + 2] & 192) !== 128 || (buf[i + 3] & 192) !== 128 || buf[i] === 240 && (buf[i + 1] & 240) === 128 || // Overlong\n      buf[i] === 244 && buf[i + 1] > 143 || buf[i] > 244) {\n        return false;\n      }\n      i += 4;\n    } else {\n      return false;\n    }\n  }\n  return true;\n}\nvalidation.exports = {\n  isValidStatusCode: isValidStatusCode$2,\n  isValidUTF8: _isValidUTF8,\n  tokenChars: tokenChars$2\n};\nif (isUtf8) {\n  isValidUTF8_1 = validation.exports.isValidUTF8 = function(buf) {\n    return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n  };\n} else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n  try {\n    const isValidUTF82 = require$$1;\n    isValidUTF8_1 = validation.exports.isValidUTF8 = function(buf) {\n      return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF82(buf);\n    };\n  } catch (e) {\n  }\n}\nvar validationExports = validation.exports;\nconst { Writable } = require$$0;\nconst PerMessageDeflate$3 = permessageDeflate;\nconst {\n  BINARY_TYPES: BINARY_TYPES$1,\n  EMPTY_BUFFER: EMPTY_BUFFER$2,\n  kStatusCode: kStatusCode$1,\n  kWebSocket: kWebSocket$2\n} = constants;\nconst { concat, toArrayBuffer, unmask } = bufferUtilExports;\nconst { isValidStatusCode: isValidStatusCode$1, isValidUTF8 } = validationExports;\nconst FastBuffer = Buffer[Symbol.species];\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nlet Receiver$1 = class Receiver extends Writable {\n  /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */\n  constructor(options = {}) {\n    super();\n    this._binaryType = options.binaryType || BINARY_TYPES$1[0];\n    this._extensions = options.extensions || {};\n    this._isServer = !!options.isServer;\n    this._maxPayload = options.maxPayload | 0;\n    this._skipUTF8Validation = !!options.skipUTF8Validation;\n    this[kWebSocket$2] = void 0;\n    this._bufferedBytes = 0;\n    this._buffers = [];\n    this._compressed = false;\n    this._payloadLength = 0;\n    this._mask = void 0;\n    this._fragmented = 0;\n    this._masked = false;\n    this._fin = false;\n    this._opcode = 0;\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragments = [];\n    this._state = GET_INFO;\n    this._loop = false;\n  }\n  /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */\n  _write(chunk, encoding, cb) {\n    if (this._opcode === 8 && this._state == GET_INFO)\n      return cb();\n    this._bufferedBytes += chunk.length;\n    this._buffers.push(chunk);\n    this.startLoop(cb);\n  }\n  /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */\n  consume(n) {\n    this._bufferedBytes -= n;\n    if (n === this._buffers[0].length)\n      return this._buffers.shift();\n    if (n < this._buffers[0].length) {\n      const buf = this._buffers[0];\n      this._buffers[0] = new FastBuffer(\n        buf.buffer,\n        buf.byteOffset + n,\n        buf.length - n\n      );\n      return new FastBuffer(buf.buffer, buf.byteOffset, n);\n    }\n    const dst = Buffer.allocUnsafe(n);\n    do {\n      const buf = this._buffers[0];\n      const offset = dst.length - n;\n      if (n >= buf.length) {\n        dst.set(this._buffers.shift(), offset);\n      } else {\n        dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n        this._buffers[0] = new FastBuffer(\n          buf.buffer,\n          buf.byteOffset + n,\n          buf.length - n\n        );\n      }\n      n -= buf.length;\n    } while (n > 0);\n    return dst;\n  }\n  /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  startLoop(cb) {\n    let err;\n    this._loop = true;\n    do {\n      switch (this._state) {\n        case GET_INFO:\n          err = this.getInfo();\n          break;\n        case GET_PAYLOAD_LENGTH_16:\n          err = this.getPayloadLength16();\n          break;\n        case GET_PAYLOAD_LENGTH_64:\n          err = this.getPayloadLength64();\n          break;\n        case GET_MASK:\n          this.getMask();\n          break;\n        case GET_DATA:\n          err = this.getData(cb);\n          break;\n        default:\n          this._loop = false;\n          return;\n      }\n    } while (this._loop);\n    cb(err);\n  }\n  /**\n   * Reads the first two bytes of a frame.\n   *\n   * @return {(RangeError|undefined)} A possible error\n   * @private\n   */\n  getInfo() {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n    const buf = this.consume(2);\n    if ((buf[0] & 48) !== 0) {\n      this._loop = false;\n      return error(\n        RangeError,\n        \"RSV2 and RSV3 must be clear\",\n        true,\n        1002,\n        \"WS_ERR_UNEXPECTED_RSV_2_3\"\n      );\n    }\n    const compressed = (buf[0] & 64) === 64;\n    if (compressed && !this._extensions[PerMessageDeflate$3.extensionName]) {\n      this._loop = false;\n      return error(\n        RangeError,\n        \"RSV1 must be clear\",\n        true,\n        1002,\n        \"WS_ERR_UNEXPECTED_RSV_1\"\n      );\n    }\n    this._fin = (buf[0] & 128) === 128;\n    this._opcode = buf[0] & 15;\n    this._payloadLength = buf[1] & 127;\n    if (this._opcode === 0) {\n      if (compressed) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"RSV1 must be clear\",\n          true,\n          1002,\n          \"WS_ERR_UNEXPECTED_RSV_1\"\n        );\n      }\n      if (!this._fragmented) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"invalid opcode 0\",\n          true,\n          1002,\n          \"WS_ERR_INVALID_OPCODE\"\n        );\n      }\n      this._opcode = this._fragmented;\n    } else if (this._opcode === 1 || this._opcode === 2) {\n      if (this._fragmented) {\n        this._loop = false;\n        return error(\n          RangeError,\n          `invalid opcode ${this._opcode}`,\n          true,\n          1002,\n          \"WS_ERR_INVALID_OPCODE\"\n        );\n      }\n      this._compressed = compressed;\n    } else if (this._opcode > 7 && this._opcode < 11) {\n      if (!this._fin) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"FIN must be set\",\n          true,\n          1002,\n          \"WS_ERR_EXPECTED_FIN\"\n        );\n      }\n      if (compressed) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"RSV1 must be clear\",\n          true,\n          1002,\n          \"WS_ERR_UNEXPECTED_RSV_1\"\n        );\n      }\n      if (this._payloadLength > 125 || this._opcode === 8 && this._payloadLength === 1) {\n        this._loop = false;\n        return error(\n          RangeError,\n          `invalid payload length ${this._payloadLength}`,\n          true,\n          1002,\n          \"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH\"\n        );\n      }\n    } else {\n      this._loop = false;\n      return error(\n        RangeError,\n        `invalid opcode ${this._opcode}`,\n        true,\n        1002,\n        \"WS_ERR_INVALID_OPCODE\"\n      );\n    }\n    if (!this._fin && !this._fragmented)\n      this._fragmented = this._opcode;\n    this._masked = (buf[1] & 128) === 128;\n    if (this._isServer) {\n      if (!this._masked) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"MASK must be set\",\n          true,\n          1002,\n          \"WS_ERR_EXPECTED_MASK\"\n        );\n      }\n    } else if (this._masked) {\n      this._loop = false;\n      return error(\n        RangeError,\n        \"MASK must be clear\",\n        true,\n        1002,\n        \"WS_ERR_UNEXPECTED_MASK\"\n      );\n    }\n    if (this._payloadLength === 126)\n      this._state = GET_PAYLOAD_LENGTH_16;\n    else if (this._payloadLength === 127)\n      this._state = GET_PAYLOAD_LENGTH_64;\n    else\n      return this.haveLength();\n  }\n  /**\n   * Gets extended payload length (7+16).\n   *\n   * @return {(RangeError|undefined)} A possible error\n   * @private\n   */\n  getPayloadLength16() {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n    this._payloadLength = this.consume(2).readUInt16BE(0);\n    return this.haveLength();\n  }\n  /**\n   * Gets extended payload length (7+64).\n   *\n   * @return {(RangeError|undefined)} A possible error\n   * @private\n   */\n  getPayloadLength64() {\n    if (this._bufferedBytes < 8) {\n      this._loop = false;\n      return;\n    }\n    const buf = this.consume(8);\n    const num = buf.readUInt32BE(0);\n    if (num > Math.pow(2, 53 - 32) - 1) {\n      this._loop = false;\n      return error(\n        RangeError,\n        \"Unsupported WebSocket frame: payload length > 2^53 - 1\",\n        false,\n        1009,\n        \"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH\"\n      );\n    }\n    this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n    return this.haveLength();\n  }\n  /**\n   * Payload length has been read.\n   *\n   * @return {(RangeError|undefined)} A possible error\n   * @private\n   */\n  haveLength() {\n    if (this._payloadLength && this._opcode < 8) {\n      this._totalPayloadLength += this._payloadLength;\n      if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"Max payload size exceeded\",\n          false,\n          1009,\n          \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\"\n        );\n      }\n    }\n    if (this._masked)\n      this._state = GET_MASK;\n    else\n      this._state = GET_DATA;\n  }\n  /**\n   * Reads mask bytes.\n   *\n   * @private\n   */\n  getMask() {\n    if (this._bufferedBytes < 4) {\n      this._loop = false;\n      return;\n    }\n    this._mask = this.consume(4);\n    this._state = GET_DATA;\n  }\n  /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */\n  getData(cb) {\n    let data = EMPTY_BUFFER$2;\n    if (this._payloadLength) {\n      if (this._bufferedBytes < this._payloadLength) {\n        this._loop = false;\n        return;\n      }\n      data = this.consume(this._payloadLength);\n      if (this._masked && (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0) {\n        unmask(data, this._mask);\n      }\n    }\n    if (this._opcode > 7)\n      return this.controlMessage(data);\n    if (this._compressed) {\n      this._state = INFLATING;\n      this.decompress(data, cb);\n      return;\n    }\n    if (data.length) {\n      this._messageLength = this._totalPayloadLength;\n      this._fragments.push(data);\n    }\n    return this.dataMessage();\n  }\n  /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */\n  decompress(data, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate$3.extensionName];\n    perMessageDeflate.decompress(data, this._fin, (err, buf) => {\n      if (err)\n        return cb(err);\n      if (buf.length) {\n        this._messageLength += buf.length;\n        if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n          return cb(\n            error(\n              RangeError,\n              \"Max payload size exceeded\",\n              false,\n              1009,\n              \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\"\n            )\n          );\n        }\n        this._fragments.push(buf);\n      }\n      const er = this.dataMessage();\n      if (er)\n        return cb(er);\n      this.startLoop(cb);\n    });\n  }\n  /**\n   * Handles a data message.\n   *\n   * @return {(Error|undefined)} A possible error\n   * @private\n   */\n  dataMessage() {\n    if (this._fin) {\n      const messageLength = this._messageLength;\n      const fragments = this._fragments;\n      this._totalPayloadLength = 0;\n      this._messageLength = 0;\n      this._fragmented = 0;\n      this._fragments = [];\n      if (this._opcode === 2) {\n        let data;\n        if (this._binaryType === \"nodebuffer\") {\n          data = concat(fragments, messageLength);\n        } else if (this._binaryType === \"arraybuffer\") {\n          data = toArrayBuffer(concat(fragments, messageLength));\n        } else {\n          data = fragments;\n        }\n        this.emit(\"message\", data, true);\n      } else {\n        const buf = concat(fragments, messageLength);\n        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n          this._loop = false;\n          return error(\n            Error,\n            \"invalid UTF-8 sequence\",\n            true,\n            1007,\n            \"WS_ERR_INVALID_UTF8\"\n          );\n        }\n        this.emit(\"message\", buf, false);\n      }\n    }\n    this._state = GET_INFO;\n  }\n  /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */\n  controlMessage(data) {\n    if (this._opcode === 8) {\n      this._loop = false;\n      if (data.length === 0) {\n        this.emit(\"conclude\", 1005, EMPTY_BUFFER$2);\n        this.end();\n      } else {\n        const code = data.readUInt16BE(0);\n        if (!isValidStatusCode$1(code)) {\n          return error(\n            RangeError,\n            `invalid status code ${code}`,\n            true,\n            1002,\n            \"WS_ERR_INVALID_CLOSE_CODE\"\n          );\n        }\n        const buf = new FastBuffer(\n          data.buffer,\n          data.byteOffset + 2,\n          data.length - 2\n        );\n        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n          return error(\n            Error,\n            \"invalid UTF-8 sequence\",\n            true,\n            1007,\n            \"WS_ERR_INVALID_UTF8\"\n          );\n        }\n        this.emit(\"conclude\", code, buf);\n        this.end();\n      }\n    } else if (this._opcode === 9) {\n      this.emit(\"ping\", data);\n    } else {\n      this.emit(\"pong\", data);\n    }\n    this._state = GET_INFO;\n  }\n};\nvar receiver = Receiver$1;\nfunction error(ErrorCtor, message, prefix, statusCode, errorCode) {\n  const err = new ErrorCtor(\n    prefix ? `Invalid WebSocket frame: ${message}` : message\n  );\n  Error.captureStackTrace(err, error);\n  err.code = errorCode;\n  err[kStatusCode$1] = statusCode;\n  return err;\n}\nconst receiver$1 = /* @__PURE__ */ getDefaultExportFromCjs(receiver);\nconst { randomFillSync } = require$$5;\nconst PerMessageDeflate$2 = permessageDeflate;\nconst { EMPTY_BUFFER: EMPTY_BUFFER$1 } = constants;\nconst { isValidStatusCode } = validationExports;\nconst { mask: applyMask, toBuffer: toBuffer$1 } = bufferUtilExports;\nconst kByteLength = Symbol(\"kByteLength\");\nconst maskBuffer = Buffer.alloc(4);\nlet Sender$1 = class Sender {\n  /**\n   * Creates a Sender instance.\n   *\n   * @param {(net.Socket|tls.Socket)} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */\n  constructor(socket, extensions, generateMask) {\n    this._extensions = extensions || {};\n    if (generateMask) {\n      this._generateMask = generateMask;\n      this._maskBuffer = Buffer.alloc(4);\n    }\n    this._socket = socket;\n    this._firstFragment = true;\n    this._compress = false;\n    this._bufferedBytes = 0;\n    this._deflating = false;\n    this._queue = [];\n  }\n  /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */\n  static frame(data, options) {\n    let mask2;\n    let merge = false;\n    let offset = 2;\n    let skipMasking = false;\n    if (options.mask) {\n      mask2 = options.maskBuffer || maskBuffer;\n      if (options.generateMask) {\n        options.generateMask(mask2);\n      } else {\n        randomFillSync(mask2, 0, 4);\n      }\n      skipMasking = (mask2[0] | mask2[1] | mask2[2] | mask2[3]) === 0;\n      offset = 6;\n    }\n    let dataLength;\n    if (typeof data === \"string\") {\n      if ((!options.mask || skipMasking) && options[kByteLength] !== void 0) {\n        dataLength = options[kByteLength];\n      } else {\n        data = Buffer.from(data);\n        dataLength = data.length;\n      }\n    } else {\n      dataLength = data.length;\n      merge = options.mask && options.readOnly && !skipMasking;\n    }\n    let payloadLength = dataLength;\n    if (dataLength >= 65536) {\n      offset += 8;\n      payloadLength = 127;\n    } else if (dataLength > 125) {\n      offset += 2;\n      payloadLength = 126;\n    }\n    const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n    target[0] = options.fin ? options.opcode | 128 : options.opcode;\n    if (options.rsv1)\n      target[0] |= 64;\n    target[1] = payloadLength;\n    if (payloadLength === 126) {\n      target.writeUInt16BE(dataLength, 2);\n    } else if (payloadLength === 127) {\n      target[2] = target[3] = 0;\n      target.writeUIntBE(dataLength, 4, 6);\n    }\n    if (!options.mask)\n      return [target, data];\n    target[1] |= 128;\n    target[offset - 4] = mask2[0];\n    target[offset - 3] = mask2[1];\n    target[offset - 2] = mask2[2];\n    target[offset - 1] = mask2[3];\n    if (skipMasking)\n      return [target, data];\n    if (merge) {\n      applyMask(data, mask2, target, offset, dataLength);\n      return [target];\n    }\n    applyMask(data, mask2, data, 0, dataLength);\n    return [target, data];\n  }\n  /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  close(code, data, mask2, cb) {\n    let buf;\n    if (code === void 0) {\n      buf = EMPTY_BUFFER$1;\n    } else if (typeof code !== \"number\" || !isValidStatusCode(code)) {\n      throw new TypeError(\"First argument must be a valid error code number\");\n    } else if (data === void 0 || !data.length) {\n      buf = Buffer.allocUnsafe(2);\n      buf.writeUInt16BE(code, 0);\n    } else {\n      const length = Buffer.byteLength(data);\n      if (length > 123) {\n        throw new RangeError(\"The message must not be greater than 123 bytes\");\n      }\n      buf = Buffer.allocUnsafe(2 + length);\n      buf.writeUInt16BE(code, 0);\n      if (typeof data === \"string\") {\n        buf.write(data, 2);\n      } else {\n        buf.set(data, 2);\n      }\n    }\n    const options = {\n      [kByteLength]: buf.length,\n      fin: true,\n      generateMask: this._generateMask,\n      mask: mask2,\n      maskBuffer: this._maskBuffer,\n      opcode: 8,\n      readOnly: false,\n      rsv1: false\n    };\n    if (this._deflating) {\n      this.enqueue([this.dispatch, buf, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(buf, options), cb);\n    }\n  }\n  /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  ping(data, mask2, cb) {\n    let byteLength;\n    let readOnly;\n    if (typeof data === \"string\") {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer$1(data);\n      byteLength = data.length;\n      readOnly = toBuffer$1.readOnly;\n    }\n    if (byteLength > 125) {\n      throw new RangeError(\"The data size must not be greater than 125 bytes\");\n    }\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask: mask2,\n      maskBuffer: this._maskBuffer,\n      opcode: 9,\n      readOnly,\n      rsv1: false\n    };\n    if (this._deflating) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n  /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  pong(data, mask2, cb) {\n    let byteLength;\n    let readOnly;\n    if (typeof data === \"string\") {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer$1(data);\n      byteLength = data.length;\n      readOnly = toBuffer$1.readOnly;\n    }\n    if (byteLength > 125) {\n      throw new RangeError(\"The data size must not be greater than 125 bytes\");\n    }\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask: mask2,\n      maskBuffer: this._maskBuffer,\n      opcode: 10,\n      readOnly,\n      rsv1: false\n    };\n    if (this._deflating) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n  /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  send(data, options, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate$2.extensionName];\n    let opcode = options.binary ? 2 : 1;\n    let rsv1 = options.compress;\n    let byteLength;\n    let readOnly;\n    if (typeof data === \"string\") {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer$1(data);\n      byteLength = data.length;\n      readOnly = toBuffer$1.readOnly;\n    }\n    if (this._firstFragment) {\n      this._firstFragment = false;\n      if (rsv1 && perMessageDeflate && perMessageDeflate.params[perMessageDeflate._isServer ? \"server_no_context_takeover\" : \"client_no_context_takeover\"]) {\n        rsv1 = byteLength >= perMessageDeflate._threshold;\n      }\n      this._compress = rsv1;\n    } else {\n      rsv1 = false;\n      opcode = 0;\n    }\n    if (options.fin)\n      this._firstFragment = true;\n    if (perMessageDeflate) {\n      const opts = {\n        [kByteLength]: byteLength,\n        fin: options.fin,\n        generateMask: this._generateMask,\n        mask: options.mask,\n        maskBuffer: this._maskBuffer,\n        opcode,\n        readOnly,\n        rsv1\n      };\n      if (this._deflating) {\n        this.enqueue([this.dispatch, data, this._compress, opts, cb]);\n      } else {\n        this.dispatch(data, this._compress, opts, cb);\n      }\n    } else {\n      this.sendFrame(\n        Sender.frame(data, {\n          [kByteLength]: byteLength,\n          fin: options.fin,\n          generateMask: this._generateMask,\n          mask: options.mask,\n          maskBuffer: this._maskBuffer,\n          opcode,\n          readOnly,\n          rsv1: false\n        }),\n        cb\n      );\n    }\n  }\n  /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  dispatch(data, compress, options, cb) {\n    if (!compress) {\n      this.sendFrame(Sender.frame(data, options), cb);\n      return;\n    }\n    const perMessageDeflate = this._extensions[PerMessageDeflate$2.extensionName];\n    this._bufferedBytes += options[kByteLength];\n    this._deflating = true;\n    perMessageDeflate.compress(data, options.fin, (_, buf) => {\n      if (this._socket.destroyed) {\n        const err = new Error(\n          \"The socket was closed while data was being compressed\"\n        );\n        if (typeof cb === \"function\")\n          cb(err);\n        for (let i = 0; i < this._queue.length; i++) {\n          const params = this._queue[i];\n          const callback = params[params.length - 1];\n          if (typeof callback === \"function\")\n            callback(err);\n        }\n        return;\n      }\n      this._bufferedBytes -= options[kByteLength];\n      this._deflating = false;\n      options.readOnly = false;\n      this.sendFrame(Sender.frame(buf, options), cb);\n      this.dequeue();\n    });\n  }\n  /**\n   * Executes queued send operations.\n   *\n   * @private\n   */\n  dequeue() {\n    while (!this._deflating && this._queue.length) {\n      const params = this._queue.shift();\n      this._bufferedBytes -= params[3][kByteLength];\n      Reflect.apply(params[0], this, params.slice(1));\n    }\n  }\n  /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */\n  enqueue(params) {\n    this._bufferedBytes += params[3][kByteLength];\n    this._queue.push(params);\n  }\n  /**\n   * Sends a frame.\n   *\n   * @param {Buffer[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  sendFrame(list, cb) {\n    if (list.length === 2) {\n      this._socket.cork();\n      this._socket.write(list[0]);\n      this._socket.write(list[1], cb);\n      this._socket.uncork();\n    } else {\n      this._socket.write(list[0], cb);\n    }\n  }\n};\nvar sender = Sender$1;\nconst sender$1 = /* @__PURE__ */ getDefaultExportFromCjs(sender);\nconst { kForOnEventAttribute: kForOnEventAttribute$1, kListener: kListener$1 } = constants;\nconst kCode = Symbol(\"kCode\");\nconst kData = Symbol(\"kData\");\nconst kError = Symbol(\"kError\");\nconst kMessage = Symbol(\"kMessage\");\nconst kReason = Symbol(\"kReason\");\nconst kTarget = Symbol(\"kTarget\");\nconst kType = Symbol(\"kType\");\nconst kWasClean = Symbol(\"kWasClean\");\nclass Event {\n  /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */\n  constructor(type) {\n    this[kTarget] = null;\n    this[kType] = type;\n  }\n  /**\n   * @type {*}\n   */\n  get target() {\n    return this[kTarget];\n  }\n  /**\n   * @type {String}\n   */\n  get type() {\n    return this[kType];\n  }\n}\nObject.defineProperty(Event.prototype, \"target\", { enumerable: true });\nObject.defineProperty(Event.prototype, \"type\", { enumerable: true });\nclass CloseEvent extends Event {\n  /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */\n  constructor(type, options = {}) {\n    super(type);\n    this[kCode] = options.code === void 0 ? 0 : options.code;\n    this[kReason] = options.reason === void 0 ? \"\" : options.reason;\n    this[kWasClean] = options.wasClean === void 0 ? false : options.wasClean;\n  }\n  /**\n   * @type {Number}\n   */\n  get code() {\n    return this[kCode];\n  }\n  /**\n   * @type {String}\n   */\n  get reason() {\n    return this[kReason];\n  }\n  /**\n   * @type {Boolean}\n   */\n  get wasClean() {\n    return this[kWasClean];\n  }\n}\nObject.defineProperty(CloseEvent.prototype, \"code\", { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, \"reason\", { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, \"wasClean\", { enumerable: true });\nclass ErrorEvent extends Event {\n  /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */\n  constructor(type, options = {}) {\n    super(type);\n    this[kError] = options.error === void 0 ? null : options.error;\n    this[kMessage] = options.message === void 0 ? \"\" : options.message;\n  }\n  /**\n   * @type {*}\n   */\n  get error() {\n    return this[kError];\n  }\n  /**\n   * @type {String}\n   */\n  get message() {\n    return this[kMessage];\n  }\n}\nObject.defineProperty(ErrorEvent.prototype, \"error\", { enumerable: true });\nObject.defineProperty(ErrorEvent.prototype, \"message\", { enumerable: true });\nclass MessageEvent extends Event {\n  /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */\n  constructor(type, options = {}) {\n    super(type);\n    this[kData] = options.data === void 0 ? null : options.data;\n  }\n  /**\n   * @type {*}\n   */\n  get data() {\n    return this[kData];\n  }\n}\nObject.defineProperty(MessageEvent.prototype, \"data\", { enumerable: true });\nconst EventTarget = {\n  /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */\n  addEventListener(type, handler, options = {}) {\n    for (const listener of this.listeners(type)) {\n      if (!options[kForOnEventAttribute$1] && listener[kListener$1] === handler && !listener[kForOnEventAttribute$1]) {\n        return;\n      }\n    }\n    let wrapper;\n    if (type === \"message\") {\n      wrapper = function onMessage(data, isBinary) {\n        const event = new MessageEvent(\"message\", {\n          data: isBinary ? data : data.toString()\n        });\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === \"close\") {\n      wrapper = function onClose(code, message) {\n        const event = new CloseEvent(\"close\", {\n          code,\n          reason: message.toString(),\n          wasClean: this._closeFrameReceived && this._closeFrameSent\n        });\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === \"error\") {\n      wrapper = function onError(error2) {\n        const event = new ErrorEvent(\"error\", {\n          error: error2,\n          message: error2.message\n        });\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === \"open\") {\n      wrapper = function onOpen() {\n        const event = new Event(\"open\");\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else {\n      return;\n    }\n    wrapper[kForOnEventAttribute$1] = !!options[kForOnEventAttribute$1];\n    wrapper[kListener$1] = handler;\n    if (options.once) {\n      this.once(type, wrapper);\n    } else {\n      this.on(type, wrapper);\n    }\n  },\n  /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */\n  removeEventListener(type, handler) {\n    for (const listener of this.listeners(type)) {\n      if (listener[kListener$1] === handler && !listener[kForOnEventAttribute$1]) {\n        this.removeListener(type, listener);\n        break;\n      }\n    }\n  }\n};\nvar eventTarget = {\n  CloseEvent,\n  ErrorEvent,\n  Event,\n  EventTarget,\n  MessageEvent\n};\nfunction callListener(listener, thisArg, event) {\n  if (typeof listener === \"object\" && listener.handleEvent) {\n    listener.handleEvent.call(listener, event);\n  } else {\n    listener.call(thisArg, event);\n  }\n}\nconst { tokenChars: tokenChars$1 } = validationExports;\nfunction push(dest, name, elem) {\n  if (dest[name] === void 0)\n    dest[name] = [elem];\n  else\n    dest[name].push(elem);\n}\nfunction parse$2(header) {\n  const offers = /* @__PURE__ */ Object.create(null);\n  let params = /* @__PURE__ */ Object.create(null);\n  let mustUnescape = false;\n  let isEscaping = false;\n  let inQuotes = false;\n  let extensionName;\n  let paramName;\n  let start = -1;\n  let code = -1;\n  let end = -1;\n  let i = 0;\n  for (; i < header.length; i++) {\n    code = header.charCodeAt(i);\n    if (extensionName === void 0) {\n      if (end === -1 && tokenChars$1[code] === 1) {\n        if (start === -1)\n          start = i;\n      } else if (i !== 0 && (code === 32 || code === 9)) {\n        if (end === -1 && start !== -1)\n          end = i;\n      } else if (code === 59 || code === 44) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (end === -1)\n          end = i;\n        const name = header.slice(start, end);\n        if (code === 44) {\n          push(offers, name, params);\n          params = /* @__PURE__ */ Object.create(null);\n        } else {\n          extensionName = name;\n        }\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else if (paramName === void 0) {\n      if (end === -1 && tokenChars$1[code] === 1) {\n        if (start === -1)\n          start = i;\n      } else if (code === 32 || code === 9) {\n        if (end === -1 && start !== -1)\n          end = i;\n      } else if (code === 59 || code === 44) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (end === -1)\n          end = i;\n        push(params, header.slice(start, end), true);\n        if (code === 44) {\n          push(offers, extensionName, params);\n          params = /* @__PURE__ */ Object.create(null);\n          extensionName = void 0;\n        }\n        start = end = -1;\n      } else if (code === 61 && start !== -1 && end === -1) {\n        paramName = header.slice(start, i);\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else {\n      if (isEscaping) {\n        if (tokenChars$1[code] !== 1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (start === -1)\n          start = i;\n        else if (!mustUnescape)\n          mustUnescape = true;\n        isEscaping = false;\n      } else if (inQuotes) {\n        if (tokenChars$1[code] === 1) {\n          if (start === -1)\n            start = i;\n        } else if (code === 34 && start !== -1) {\n          inQuotes = false;\n          end = i;\n        } else if (code === 92) {\n          isEscaping = true;\n        } else {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n      } else if (code === 34 && header.charCodeAt(i - 1) === 61) {\n        inQuotes = true;\n      } else if (end === -1 && tokenChars$1[code] === 1) {\n        if (start === -1)\n          start = i;\n      } else if (start !== -1 && (code === 32 || code === 9)) {\n        if (end === -1)\n          end = i;\n      } else if (code === 59 || code === 44) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (end === -1)\n          end = i;\n        let value = header.slice(start, end);\n        if (mustUnescape) {\n          value = value.replace(/\\\\/g, \"\");\n          mustUnescape = false;\n        }\n        push(params, paramName, value);\n        if (code === 44) {\n          push(offers, extensionName, params);\n          params = /* @__PURE__ */ Object.create(null);\n          extensionName = void 0;\n        }\n        paramName = void 0;\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    }\n  }\n  if (start === -1 || inQuotes || code === 32 || code === 9) {\n    throw new SyntaxError(\"Unexpected end of input\");\n  }\n  if (end === -1)\n    end = i;\n  const token = header.slice(start, end);\n  if (extensionName === void 0) {\n    push(offers, token, params);\n  } else {\n    if (paramName === void 0) {\n      push(params, token, true);\n    } else if (mustUnescape) {\n      push(params, paramName, token.replace(/\\\\/g, \"\"));\n    } else {\n      push(params, paramName, token);\n    }\n    push(offers, extensionName, params);\n  }\n  return offers;\n}\nfunction format$1(extensions) {\n  return Object.keys(extensions).map((extension2) => {\n    let configurations = extensions[extension2];\n    if (!Array.isArray(configurations))\n      configurations = [configurations];\n    return configurations.map((params) => {\n      return [extension2].concat(\n        Object.keys(params).map((k) => {\n          let values = params[k];\n          if (!Array.isArray(values))\n            values = [values];\n          return values.map((v) => v === true ? k : `${k}=${v}`).join(\"; \");\n        })\n      ).join(\"; \");\n    }).join(\", \");\n  }).join(\", \");\n}\nvar extension$1 = { format: format$1, parse: parse$2 };\nconst EventEmitter$1 = require$$0$3;\nconst https = require$$1$1;\nconst http$1 = require$$2;\nconst net = require$$3;\nconst tls = require$$4;\nconst { randomBytes, createHash: createHash$1 } = require$$5;\nconst { URL } = require$$7;\nconst PerMessageDeflate$1 = permessageDeflate;\nconst Receiver2 = receiver;\nconst Sender2 = sender;\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  GUID: GUID$1,\n  kForOnEventAttribute,\n  kListener,\n  kStatusCode,\n  kWebSocket: kWebSocket$1,\n  NOOP\n} = constants;\nconst {\n  EventTarget: { addEventListener, removeEventListener }\n} = eventTarget;\nconst { format, parse: parse$1 } = extension$1;\nconst { toBuffer } = bufferUtilExports;\nconst closeTimeout = 30 * 1e3;\nconst kAborted = Symbol(\"kAborted\");\nconst protocolVersions = [8, 13];\nconst readyStates = [\"CONNECTING\", \"OPEN\", \"CLOSING\", \"CLOSED\"];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\nlet WebSocket$1 = class WebSocket extends EventEmitter$1 {\n  /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */\n  constructor(address, protocols, options) {\n    super();\n    this._binaryType = BINARY_TYPES[0];\n    this._closeCode = 1006;\n    this._closeFrameReceived = false;\n    this._closeFrameSent = false;\n    this._closeMessage = EMPTY_BUFFER;\n    this._closeTimer = null;\n    this._extensions = {};\n    this._paused = false;\n    this._protocol = \"\";\n    this._readyState = WebSocket.CONNECTING;\n    this._receiver = null;\n    this._sender = null;\n    this._socket = null;\n    if (address !== null) {\n      this._bufferedAmount = 0;\n      this._isServer = false;\n      this._redirects = 0;\n      if (protocols === void 0) {\n        protocols = [];\n      } else if (!Array.isArray(protocols)) {\n        if (typeof protocols === \"object\" && protocols !== null) {\n          options = protocols;\n          protocols = [];\n        } else {\n          protocols = [protocols];\n        }\n      }\n      initAsClient(this, address, protocols, options);\n    } else {\n      this._isServer = true;\n    }\n  }\n  /**\n   * This deviates from the WHATWG interface since ws doesn't support the\n   * required default \"blob\" type (instead we define a custom \"nodebuffer\"\n   * type).\n   *\n   * @type {String}\n   */\n  get binaryType() {\n    return this._binaryType;\n  }\n  set binaryType(type) {\n    if (!BINARY_TYPES.includes(type))\n      return;\n    this._binaryType = type;\n    if (this._receiver)\n      this._receiver._binaryType = type;\n  }\n  /**\n   * @type {Number}\n   */\n  get bufferedAmount() {\n    if (!this._socket)\n      return this._bufferedAmount;\n    return this._socket._writableState.length + this._sender._bufferedBytes;\n  }\n  /**\n   * @type {String}\n   */\n  get extensions() {\n    return Object.keys(this._extensions).join();\n  }\n  /**\n   * @type {Boolean}\n   */\n  get isPaused() {\n    return this._paused;\n  }\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onclose() {\n    return null;\n  }\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onerror() {\n    return null;\n  }\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onopen() {\n    return null;\n  }\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onmessage() {\n    return null;\n  }\n  /**\n   * @type {String}\n   */\n  get protocol() {\n    return this._protocol;\n  }\n  /**\n   * @type {Number}\n   */\n  get readyState() {\n    return this._readyState;\n  }\n  /**\n   * @type {String}\n   */\n  get url() {\n    return this._url;\n  }\n  /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {(net.Socket|tls.Socket)} socket The network socket between the\n   *     server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */\n  setSocket(socket, head, options) {\n    const receiver2 = new Receiver2({\n      binaryType: this.binaryType,\n      extensions: this._extensions,\n      isServer: this._isServer,\n      maxPayload: options.maxPayload,\n      skipUTF8Validation: options.skipUTF8Validation\n    });\n    this._sender = new Sender2(socket, this._extensions, options.generateMask);\n    this._receiver = receiver2;\n    this._socket = socket;\n    receiver2[kWebSocket$1] = this;\n    socket[kWebSocket$1] = this;\n    receiver2.on(\"conclude\", receiverOnConclude);\n    receiver2.on(\"drain\", receiverOnDrain);\n    receiver2.on(\"error\", receiverOnError);\n    receiver2.on(\"message\", receiverOnMessage);\n    receiver2.on(\"ping\", receiverOnPing);\n    receiver2.on(\"pong\", receiverOnPong);\n    socket.setTimeout(0);\n    socket.setNoDelay();\n    if (head.length > 0)\n      socket.unshift(head);\n    socket.on(\"close\", socketOnClose);\n    socket.on(\"data\", socketOnData);\n    socket.on(\"end\", socketOnEnd);\n    socket.on(\"error\", socketOnError$1);\n    this._readyState = WebSocket.OPEN;\n    this.emit(\"open\");\n  }\n  /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */\n  emitClose() {\n    if (!this._socket) {\n      this._readyState = WebSocket.CLOSED;\n      this.emit(\"close\", this._closeCode, this._closeMessage);\n      return;\n    }\n    if (this._extensions[PerMessageDeflate$1.extensionName]) {\n      this._extensions[PerMessageDeflate$1.extensionName].cleanup();\n    }\n    this._receiver.removeAllListeners();\n    this._readyState = WebSocket.CLOSED;\n    this.emit(\"close\", this._closeCode, this._closeMessage);\n  }\n  /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */\n  close(code, data) {\n    if (this.readyState === WebSocket.CLOSED)\n      return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = \"WebSocket was closed before the connection was established\";\n      abortHandshake$1(this, this._req, msg);\n      return;\n    }\n    if (this.readyState === WebSocket.CLOSING) {\n      if (this._closeFrameSent && (this._closeFrameReceived || this._receiver._writableState.errorEmitted)) {\n        this._socket.end();\n      }\n      return;\n    }\n    this._readyState = WebSocket.CLOSING;\n    this._sender.close(code, data, !this._isServer, (err) => {\n      if (err)\n        return;\n      this._closeFrameSent = true;\n      if (this._closeFrameReceived || this._receiver._writableState.errorEmitted) {\n        this._socket.end();\n      }\n    });\n    this._closeTimer = setTimeout(\n      this._socket.destroy.bind(this._socket),\n      closeTimeout\n    );\n  }\n  /**\n   * Pause the socket.\n   *\n   * @public\n   */\n  pause() {\n    if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n      return;\n    }\n    this._paused = true;\n    this._socket.pause();\n  }\n  /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */\n  ping(data, mask2, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n    }\n    if (typeof data === \"function\") {\n      cb = data;\n      data = mask2 = void 0;\n    } else if (typeof mask2 === \"function\") {\n      cb = mask2;\n      mask2 = void 0;\n    }\n    if (typeof data === \"number\")\n      data = data.toString();\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n    if (mask2 === void 0)\n      mask2 = !this._isServer;\n    this._sender.ping(data || EMPTY_BUFFER, mask2, cb);\n  }\n  /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */\n  pong(data, mask2, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n    }\n    if (typeof data === \"function\") {\n      cb = data;\n      data = mask2 = void 0;\n    } else if (typeof mask2 === \"function\") {\n      cb = mask2;\n      mask2 = void 0;\n    }\n    if (typeof data === \"number\")\n      data = data.toString();\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n    if (mask2 === void 0)\n      mask2 = !this._isServer;\n    this._sender.pong(data || EMPTY_BUFFER, mask2, cb);\n  }\n  /**\n   * Resume the socket.\n   *\n   * @public\n   */\n  resume() {\n    if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n      return;\n    }\n    this._paused = false;\n    if (!this._receiver._writableState.needDrain)\n      this._socket.resume();\n  }\n  /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */\n  send(data, options, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n    }\n    if (typeof options === \"function\") {\n      cb = options;\n      options = {};\n    }\n    if (typeof data === \"number\")\n      data = data.toString();\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n    const opts = {\n      binary: typeof data !== \"string\",\n      mask: !this._isServer,\n      compress: true,\n      fin: true,\n      ...options\n    };\n    if (!this._extensions[PerMessageDeflate$1.extensionName]) {\n      opts.compress = false;\n    }\n    this._sender.send(data || EMPTY_BUFFER, opts, cb);\n  }\n  /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */\n  terminate() {\n    if (this.readyState === WebSocket.CLOSED)\n      return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = \"WebSocket was closed before the connection was established\";\n      abortHandshake$1(this, this._req, msg);\n      return;\n    }\n    if (this._socket) {\n      this._readyState = WebSocket.CLOSING;\n      this._socket.destroy();\n    }\n  }\n};\nObject.defineProperty(WebSocket$1, \"CONNECTING\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CONNECTING\")\n});\nObject.defineProperty(WebSocket$1.prototype, \"CONNECTING\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CONNECTING\")\n});\nObject.defineProperty(WebSocket$1, \"OPEN\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"OPEN\")\n});\nObject.defineProperty(WebSocket$1.prototype, \"OPEN\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"OPEN\")\n});\nObject.defineProperty(WebSocket$1, \"CLOSING\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CLOSING\")\n});\nObject.defineProperty(WebSocket$1.prototype, \"CLOSING\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CLOSING\")\n});\nObject.defineProperty(WebSocket$1, \"CLOSED\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CLOSED\")\n});\nObject.defineProperty(WebSocket$1.prototype, \"CLOSED\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CLOSED\")\n});\n[\n  \"binaryType\",\n  \"bufferedAmount\",\n  \"extensions\",\n  \"isPaused\",\n  \"protocol\",\n  \"readyState\",\n  \"url\"\n].forEach((property) => {\n  Object.defineProperty(WebSocket$1.prototype, property, { enumerable: true });\n});\n[\"open\", \"error\", \"close\", \"message\"].forEach((method) => {\n  Object.defineProperty(WebSocket$1.prototype, `on${method}`, {\n    enumerable: true,\n    get() {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute])\n          return listener[kListener];\n      }\n      return null;\n    },\n    set(handler) {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) {\n          this.removeListener(method, listener);\n          break;\n        }\n      }\n      if (typeof handler !== \"function\")\n        return;\n      this.addEventListener(method, handler, {\n        [kForOnEventAttribute]: true\n      });\n    }\n  });\n});\nWebSocket$1.prototype.addEventListener = addEventListener;\nWebSocket$1.prototype.removeEventListener = removeEventListener;\nvar websocket = WebSocket$1;\nfunction initAsClient(websocket2, address, protocols, options) {\n  const opts = {\n    protocolVersion: protocolVersions[1],\n    maxPayload: 100 * 1024 * 1024,\n    skipUTF8Validation: false,\n    perMessageDeflate: true,\n    followRedirects: false,\n    maxRedirects: 10,\n    ...options,\n    createConnection: void 0,\n    socketPath: void 0,\n    hostname: void 0,\n    protocol: void 0,\n    timeout: void 0,\n    method: \"GET\",\n    host: void 0,\n    path: void 0,\n    port: void 0\n  };\n  if (!protocolVersions.includes(opts.protocolVersion)) {\n    throw new RangeError(\n      `Unsupported protocol version: ${opts.protocolVersion} (supported versions: ${protocolVersions.join(\", \")})`\n    );\n  }\n  let parsedUrl;\n  if (address instanceof URL) {\n    parsedUrl = address;\n    websocket2._url = address.href;\n  } else {\n    try {\n      parsedUrl = new URL(address);\n    } catch (e) {\n      throw new SyntaxError(`Invalid URL: ${address}`);\n    }\n    websocket2._url = address;\n  }\n  const isSecure = parsedUrl.protocol === \"wss:\";\n  const isIpcUrl = parsedUrl.protocol === \"ws+unix:\";\n  let invalidUrlMessage;\n  if (parsedUrl.protocol !== \"ws:\" && !isSecure && !isIpcUrl) {\n    invalidUrlMessage = `The URL's protocol must be one of \"ws:\", \"wss:\", or \"ws+unix:\"`;\n  } else if (isIpcUrl && !parsedUrl.pathname) {\n    invalidUrlMessage = \"The URL's pathname is empty\";\n  } else if (parsedUrl.hash) {\n    invalidUrlMessage = \"The URL contains a fragment identifier\";\n  }\n  if (invalidUrlMessage) {\n    const err = new SyntaxError(invalidUrlMessage);\n    if (websocket2._redirects === 0) {\n      throw err;\n    } else {\n      emitErrorAndClose(websocket2, err);\n      return;\n    }\n  }\n  const defaultPort = isSecure ? 443 : 80;\n  const key = randomBytes(16).toString(\"base64\");\n  const request = isSecure ? https.request : http$1.request;\n  const protocolSet = /* @__PURE__ */ new Set();\n  let perMessageDeflate;\n  opts.createConnection = isSecure ? tlsConnect : netConnect;\n  opts.defaultPort = opts.defaultPort || defaultPort;\n  opts.port = parsedUrl.port || defaultPort;\n  opts.host = parsedUrl.hostname.startsWith(\"[\") ? parsedUrl.hostname.slice(1, -1) : parsedUrl.hostname;\n  opts.headers = {\n    ...opts.headers,\n    \"Sec-WebSocket-Version\": opts.protocolVersion,\n    \"Sec-WebSocket-Key\": key,\n    Connection: \"Upgrade\",\n    Upgrade: \"websocket\"\n  };\n  opts.path = parsedUrl.pathname + parsedUrl.search;\n  opts.timeout = opts.handshakeTimeout;\n  if (opts.perMessageDeflate) {\n    perMessageDeflate = new PerMessageDeflate$1(\n      opts.perMessageDeflate !== true ? opts.perMessageDeflate : {},\n      false,\n      opts.maxPayload\n    );\n    opts.headers[\"Sec-WebSocket-Extensions\"] = format({\n      [PerMessageDeflate$1.extensionName]: perMessageDeflate.offer()\n    });\n  }\n  if (protocols.length) {\n    for (const protocol of protocols) {\n      if (typeof protocol !== \"string\" || !subprotocolRegex.test(protocol) || protocolSet.has(protocol)) {\n        throw new SyntaxError(\n          \"An invalid or duplicated subprotocol was specified\"\n        );\n      }\n      protocolSet.add(protocol);\n    }\n    opts.headers[\"Sec-WebSocket-Protocol\"] = protocols.join(\",\");\n  }\n  if (opts.origin) {\n    if (opts.protocolVersion < 13) {\n      opts.headers[\"Sec-WebSocket-Origin\"] = opts.origin;\n    } else {\n      opts.headers.Origin = opts.origin;\n    }\n  }\n  if (parsedUrl.username || parsedUrl.password) {\n    opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n  }\n  if (isIpcUrl) {\n    const parts = opts.path.split(\":\");\n    opts.socketPath = parts[0];\n    opts.path = parts[1];\n  }\n  let req;\n  if (opts.followRedirects) {\n    if (websocket2._redirects === 0) {\n      websocket2._originalIpc = isIpcUrl;\n      websocket2._originalSecure = isSecure;\n      websocket2._originalHostOrSocketPath = isIpcUrl ? opts.socketPath : parsedUrl.host;\n      const headers = options && options.headers;\n      options = { ...options, headers: {} };\n      if (headers) {\n        for (const [key2, value] of Object.entries(headers)) {\n          options.headers[key2.toLowerCase()] = value;\n        }\n      }\n    } else if (websocket2.listenerCount(\"redirect\") === 0) {\n      const isSameHost = isIpcUrl ? websocket2._originalIpc ? opts.socketPath === websocket2._originalHostOrSocketPath : false : websocket2._originalIpc ? false : parsedUrl.host === websocket2._originalHostOrSocketPath;\n      if (!isSameHost || websocket2._originalSecure && !isSecure) {\n        delete opts.headers.authorization;\n        delete opts.headers.cookie;\n        if (!isSameHost)\n          delete opts.headers.host;\n        opts.auth = void 0;\n      }\n    }\n    if (opts.auth && !options.headers.authorization) {\n      options.headers.authorization = \"Basic \" + Buffer.from(opts.auth).toString(\"base64\");\n    }\n    req = websocket2._req = request(opts);\n    if (websocket2._redirects) {\n      websocket2.emit(\"redirect\", websocket2.url, req);\n    }\n  } else {\n    req = websocket2._req = request(opts);\n  }\n  if (opts.timeout) {\n    req.on(\"timeout\", () => {\n      abortHandshake$1(websocket2, req, \"Opening handshake has timed out\");\n    });\n  }\n  req.on(\"error\", (err) => {\n    if (req === null || req[kAborted])\n      return;\n    req = websocket2._req = null;\n    emitErrorAndClose(websocket2, err);\n  });\n  req.on(\"response\", (res) => {\n    const location = res.headers.location;\n    const statusCode = res.statusCode;\n    if (location && opts.followRedirects && statusCode >= 300 && statusCode < 400) {\n      if (++websocket2._redirects > opts.maxRedirects) {\n        abortHandshake$1(websocket2, req, \"Maximum redirects exceeded\");\n        return;\n      }\n      req.abort();\n      let addr;\n      try {\n        addr = new URL(location, address);\n      } catch (e) {\n        const err = new SyntaxError(`Invalid URL: ${location}`);\n        emitErrorAndClose(websocket2, err);\n        return;\n      }\n      initAsClient(websocket2, addr, protocols, options);\n    } else if (!websocket2.emit(\"unexpected-response\", req, res)) {\n      abortHandshake$1(\n        websocket2,\n        req,\n        `Unexpected server response: ${res.statusCode}`\n      );\n    }\n  });\n  req.on(\"upgrade\", (res, socket, head) => {\n    websocket2.emit(\"upgrade\", res);\n    if (websocket2.readyState !== WebSocket$1.CONNECTING)\n      return;\n    req = websocket2._req = null;\n    if (res.headers.upgrade.toLowerCase() !== \"websocket\") {\n      abortHandshake$1(websocket2, socket, \"Invalid Upgrade header\");\n      return;\n    }\n    const digest = createHash$1(\"sha1\").update(key + GUID$1).digest(\"base64\");\n    if (res.headers[\"sec-websocket-accept\"] !== digest) {\n      abortHandshake$1(websocket2, socket, \"Invalid Sec-WebSocket-Accept header\");\n      return;\n    }\n    const serverProt = res.headers[\"sec-websocket-protocol\"];\n    let protError;\n    if (serverProt !== void 0) {\n      if (!protocolSet.size) {\n        protError = \"Server sent a subprotocol but none was requested\";\n      } else if (!protocolSet.has(serverProt)) {\n        protError = \"Server sent an invalid subprotocol\";\n      }\n    } else if (protocolSet.size) {\n      protError = \"Server sent no subprotocol\";\n    }\n    if (protError) {\n      abortHandshake$1(websocket2, socket, protError);\n      return;\n    }\n    if (serverProt)\n      websocket2._protocol = serverProt;\n    const secWebSocketExtensions = res.headers[\"sec-websocket-extensions\"];\n    if (secWebSocketExtensions !== void 0) {\n      if (!perMessageDeflate) {\n        const message = \"Server sent a Sec-WebSocket-Extensions header but no extension was requested\";\n        abortHandshake$1(websocket2, socket, message);\n        return;\n      }\n      let extensions;\n      try {\n        extensions = parse$1(secWebSocketExtensions);\n      } catch (err) {\n        const message = \"Invalid Sec-WebSocket-Extensions header\";\n        abortHandshake$1(websocket2, socket, message);\n        return;\n      }\n      const extensionNames = Object.keys(extensions);\n      if (extensionNames.length !== 1 || extensionNames[0] !== PerMessageDeflate$1.extensionName) {\n        const message = \"Server indicated an extension that was not requested\";\n        abortHandshake$1(websocket2, socket, message);\n        return;\n      }\n      try {\n        perMessageDeflate.accept(extensions[PerMessageDeflate$1.extensionName]);\n      } catch (err) {\n        const message = \"Invalid Sec-WebSocket-Extensions header\";\n        abortHandshake$1(websocket2, socket, message);\n        return;\n      }\n      websocket2._extensions[PerMessageDeflate$1.extensionName] = perMessageDeflate;\n    }\n    websocket2.setSocket(socket, head, {\n      generateMask: opts.generateMask,\n      maxPayload: opts.maxPayload,\n      skipUTF8Validation: opts.skipUTF8Validation\n    });\n  });\n  if (opts.finishRequest) {\n    opts.finishRequest(req, websocket2);\n  } else {\n    req.end();\n  }\n}\nfunction emitErrorAndClose(websocket2, err) {\n  websocket2._readyState = WebSocket$1.CLOSING;\n  websocket2.emit(\"error\", err);\n  websocket2.emitClose();\n}\nfunction netConnect(options) {\n  options.path = options.socketPath;\n  return net.connect(options);\n}\nfunction tlsConnect(options) {\n  options.path = void 0;\n  if (!options.servername && options.servername !== \"\") {\n    options.servername = net.isIP(options.host) ? \"\" : options.host;\n  }\n  return tls.connect(options);\n}\nfunction abortHandshake$1(websocket2, stream2, message) {\n  websocket2._readyState = WebSocket$1.CLOSING;\n  const err = new Error(message);\n  Error.captureStackTrace(err, abortHandshake$1);\n  if (stream2.setHeader) {\n    stream2[kAborted] = true;\n    stream2.abort();\n    if (stream2.socket && !stream2.socket.destroyed) {\n      stream2.socket.destroy();\n    }\n    process.nextTick(emitErrorAndClose, websocket2, err);\n  } else {\n    stream2.destroy(err);\n    stream2.once(\"error\", websocket2.emit.bind(websocket2, \"error\"));\n    stream2.once(\"close\", websocket2.emitClose.bind(websocket2));\n  }\n}\nfunction sendAfterClose(websocket2, data, cb) {\n  if (data) {\n    const length = toBuffer(data).length;\n    if (websocket2._socket)\n      websocket2._sender._bufferedBytes += length;\n    else\n      websocket2._bufferedAmount += length;\n  }\n  if (cb) {\n    const err = new Error(\n      `WebSocket is not open: readyState ${websocket2.readyState} (${readyStates[websocket2.readyState]})`\n    );\n    process.nextTick(cb, err);\n  }\n}\nfunction receiverOnConclude(code, reason) {\n  const websocket2 = this[kWebSocket$1];\n  websocket2._closeFrameReceived = true;\n  websocket2._closeMessage = reason;\n  websocket2._closeCode = code;\n  if (websocket2._socket[kWebSocket$1] === void 0)\n    return;\n  websocket2._socket.removeListener(\"data\", socketOnData);\n  process.nextTick(resume, websocket2._socket);\n  if (code === 1005)\n    websocket2.close();\n  else\n    websocket2.close(code, reason);\n}\nfunction receiverOnDrain() {\n  const websocket2 = this[kWebSocket$1];\n  if (!websocket2.isPaused)\n    websocket2._socket.resume();\n}\nfunction receiverOnError(err) {\n  const websocket2 = this[kWebSocket$1];\n  if (websocket2._socket[kWebSocket$1] !== void 0) {\n    websocket2._socket.removeListener(\"data\", socketOnData);\n    process.nextTick(resume, websocket2._socket);\n    websocket2.close(err[kStatusCode]);\n  }\n  websocket2.emit(\"error\", err);\n}\nfunction receiverOnFinish() {\n  this[kWebSocket$1].emitClose();\n}\nfunction receiverOnMessage(data, isBinary) {\n  this[kWebSocket$1].emit(\"message\", data, isBinary);\n}\nfunction receiverOnPing(data) {\n  const websocket2 = this[kWebSocket$1];\n  websocket2.pong(data, !websocket2._isServer, NOOP);\n  websocket2.emit(\"ping\", data);\n}\nfunction receiverOnPong(data) {\n  this[kWebSocket$1].emit(\"pong\", data);\n}\nfunction resume(stream2) {\n  stream2.resume();\n}\nfunction socketOnClose() {\n  const websocket2 = this[kWebSocket$1];\n  this.removeListener(\"close\", socketOnClose);\n  this.removeListener(\"data\", socketOnData);\n  this.removeListener(\"end\", socketOnEnd);\n  websocket2._readyState = WebSocket$1.CLOSING;\n  let chunk;\n  if (!this._readableState.endEmitted && !websocket2._closeFrameReceived && !websocket2._receiver._writableState.errorEmitted && (chunk = websocket2._socket.read()) !== null) {\n    websocket2._receiver.write(chunk);\n  }\n  websocket2._receiver.end();\n  this[kWebSocket$1] = void 0;\n  clearTimeout(websocket2._closeTimer);\n  if (websocket2._receiver._writableState.finished || websocket2._receiver._writableState.errorEmitted) {\n    websocket2.emitClose();\n  } else {\n    websocket2._receiver.on(\"error\", receiverOnFinish);\n    websocket2._receiver.on(\"finish\", receiverOnFinish);\n  }\n}\nfunction socketOnData(chunk) {\n  if (!this[kWebSocket$1]._receiver.write(chunk)) {\n    this.pause();\n  }\n}\nfunction socketOnEnd() {\n  const websocket2 = this[kWebSocket$1];\n  websocket2._readyState = WebSocket$1.CLOSING;\n  websocket2._receiver.end();\n  this.end();\n}\nfunction socketOnError$1() {\n  const websocket2 = this[kWebSocket$1];\n  this.removeListener(\"error\", socketOnError$1);\n  this.on(\"error\", NOOP);\n  if (websocket2) {\n    websocket2._readyState = WebSocket$1.CLOSING;\n    this.destroy();\n  }\n}\nconst WebSocket$2 = /* @__PURE__ */ getDefaultExportFromCjs(websocket);\nconst { tokenChars } = validationExports;\nfunction parse(header) {\n  const protocols = /* @__PURE__ */ new Set();\n  let start = -1;\n  let end = -1;\n  let i = 0;\n  for (i; i < header.length; i++) {\n    const code = header.charCodeAt(i);\n    if (end === -1 && tokenChars[code] === 1) {\n      if (start === -1)\n        start = i;\n    } else if (i !== 0 && (code === 32 || code === 9)) {\n      if (end === -1 && start !== -1)\n        end = i;\n    } else if (code === 44) {\n      if (start === -1) {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n      if (end === -1)\n        end = i;\n      const protocol2 = header.slice(start, end);\n      if (protocols.has(protocol2)) {\n        throw new SyntaxError(`The \"${protocol2}\" subprotocol is duplicated`);\n      }\n      protocols.add(protocol2);\n      start = end = -1;\n    } else {\n      throw new SyntaxError(`Unexpected character at index ${i}`);\n    }\n  }\n  if (start === -1 || end !== -1) {\n    throw new SyntaxError(\"Unexpected end of input\");\n  }\n  const protocol = header.slice(start, i);\n  if (protocols.has(protocol)) {\n    throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n  }\n  protocols.add(protocol);\n  return protocols;\n}\nvar subprotocol$1 = { parse };\nconst EventEmitter = require$$0$3;\nconst http = require$$2;\nconst { createHash } = require$$5;\nconst extension = extension$1;\nconst PerMessageDeflate2 = permessageDeflate;\nconst subprotocol = subprotocol$1;\nconst WebSocket2 = websocket;\nconst { GUID, kWebSocket } = constants;\nconst keyRegex = /^[+/0-9A-Za-z]{22}==$/;\nconst RUNNING = 0;\nconst CLOSING = 1;\nconst CLOSED = 2;\nclass WebSocketServer extends EventEmitter {\n  /**\n   * Create a `WebSocketServer` instance.\n   *\n   * @param {Object} options Configuration options\n   * @param {Number} [options.backlog=511] The maximum length of the queue of\n   *     pending connections\n   * @param {Boolean} [options.clientTracking=true] Specifies whether or not to\n   *     track clients\n   * @param {Function} [options.handleProtocols] A hook to handle protocols\n   * @param {String} [options.host] The hostname where to bind the server\n   * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n   *     size\n   * @param {Boolean} [options.noServer=false] Enable no server mode\n   * @param {String} [options.path] Accept only connections matching this path\n   * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable\n   *     permessage-deflate\n   * @param {Number} [options.port] The port where to bind the server\n   * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S\n   *     server to use\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @param {Function} [options.verifyClient] A hook to reject connections\n   * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`\n   *     class to use. It must be the `WebSocket` class or class that extends it\n   * @param {Function} [callback] A listener for the `listening` event\n   */\n  constructor(options, callback) {\n    super();\n    options = {\n      maxPayload: 100 * 1024 * 1024,\n      skipUTF8Validation: false,\n      perMessageDeflate: false,\n      handleProtocols: null,\n      clientTracking: true,\n      verifyClient: null,\n      noServer: false,\n      backlog: null,\n      // use default (511 as implemented in net.js)\n      server: null,\n      host: null,\n      path: null,\n      port: null,\n      WebSocket: WebSocket2,\n      ...options\n    };\n    if (options.port == null && !options.server && !options.noServer || options.port != null && (options.server || options.noServer) || options.server && options.noServer) {\n      throw new TypeError(\n        'One and only one of the \"port\", \"server\", or \"noServer\" options must be specified'\n      );\n    }\n    if (options.port != null) {\n      this._server = http.createServer((req, res) => {\n        const body = http.STATUS_CODES[426];\n        res.writeHead(426, {\n          \"Content-Length\": body.length,\n          \"Content-Type\": \"text/plain\"\n        });\n        res.end(body);\n      });\n      this._server.listen(\n        options.port,\n        options.host,\n        options.backlog,\n        callback\n      );\n    } else if (options.server) {\n      this._server = options.server;\n    }\n    if (this._server) {\n      const emitConnection = this.emit.bind(this, \"connection\");\n      this._removeListeners = addListeners(this._server, {\n        listening: this.emit.bind(this, \"listening\"),\n        error: this.emit.bind(this, \"error\"),\n        upgrade: (req, socket, head) => {\n          this.handleUpgrade(req, socket, head, emitConnection);\n        }\n      });\n    }\n    if (options.perMessageDeflate === true)\n      options.perMessageDeflate = {};\n    if (options.clientTracking) {\n      this.clients = /* @__PURE__ */ new Set();\n      this._shouldEmitClose = false;\n    }\n    this.options = options;\n    this._state = RUNNING;\n  }\n  /**\n   * Returns the bound address, the address family name, and port of the server\n   * as reported by the operating system if listening on an IP socket.\n   * If the server is listening on a pipe or UNIX domain socket, the name is\n   * returned as a string.\n   *\n   * @return {(Object|String|null)} The address of the server\n   * @public\n   */\n  address() {\n    if (this.options.noServer) {\n      throw new Error('The server is operating in \"noServer\" mode');\n    }\n    if (!this._server)\n      return null;\n    return this._server.address();\n  }\n  /**\n   * Stop the server from accepting new connections and emit the `'close'` event\n   * when all existing connections are closed.\n   *\n   * @param {Function} [cb] A one-time listener for the `'close'` event\n   * @public\n   */\n  close(cb) {\n    if (this._state === CLOSED) {\n      if (cb) {\n        this.once(\"close\", () => {\n          cb(new Error(\"The server is not running\"));\n        });\n      }\n      process.nextTick(emitClose, this);\n      return;\n    }\n    if (cb)\n      this.once(\"close\", cb);\n    if (this._state === CLOSING)\n      return;\n    this._state = CLOSING;\n    if (this.options.noServer || this.options.server) {\n      if (this._server) {\n        this._removeListeners();\n        this._removeListeners = this._server = null;\n      }\n      if (this.clients) {\n        if (!this.clients.size) {\n          process.nextTick(emitClose, this);\n        } else {\n          this._shouldEmitClose = true;\n        }\n      } else {\n        process.nextTick(emitClose, this);\n      }\n    } else {\n      const server = this._server;\n      this._removeListeners();\n      this._removeListeners = this._server = null;\n      server.close(() => {\n        emitClose(this);\n      });\n    }\n  }\n  /**\n   * See if a given request should be handled by this server instance.\n   *\n   * @param {http.IncomingMessage} req Request object to inspect\n   * @return {Boolean} `true` if the request is valid, else `false`\n   * @public\n   */\n  shouldHandle(req) {\n    if (this.options.path) {\n      const index = req.url.indexOf(\"?\");\n      const pathname = index !== -1 ? req.url.slice(0, index) : req.url;\n      if (pathname !== this.options.path)\n        return false;\n    }\n    return true;\n  }\n  /**\n   * Handle a HTTP Upgrade request.\n   *\n   * @param {http.IncomingMessage} req The request object\n   * @param {(net.Socket|tls.Socket)} socket The network socket between the\n   *     server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @public\n   */\n  handleUpgrade(req, socket, head, cb) {\n    socket.on(\"error\", socketOnError);\n    const key = req.headers[\"sec-websocket-key\"];\n    const version = +req.headers[\"sec-websocket-version\"];\n    if (req.method !== \"GET\") {\n      const message = \"Invalid HTTP method\";\n      abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);\n      return;\n    }\n    if (req.headers.upgrade.toLowerCase() !== \"websocket\") {\n      const message = \"Invalid Upgrade header\";\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n    if (!key || !keyRegex.test(key)) {\n      const message = \"Missing or invalid Sec-WebSocket-Key header\";\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n    if (version !== 8 && version !== 13) {\n      const message = \"Missing or invalid Sec-WebSocket-Version header\";\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n    if (!this.shouldHandle(req)) {\n      abortHandshake(socket, 400);\n      return;\n    }\n    const secWebSocketProtocol = req.headers[\"sec-websocket-protocol\"];\n    let protocols = /* @__PURE__ */ new Set();\n    if (secWebSocketProtocol !== void 0) {\n      try {\n        protocols = subprotocol.parse(secWebSocketProtocol);\n      } catch (err) {\n        const message = \"Invalid Sec-WebSocket-Protocol header\";\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n    const secWebSocketExtensions = req.headers[\"sec-websocket-extensions\"];\n    const extensions = {};\n    if (this.options.perMessageDeflate && secWebSocketExtensions !== void 0) {\n      const perMessageDeflate = new PerMessageDeflate2(\n        this.options.perMessageDeflate,\n        true,\n        this.options.maxPayload\n      );\n      try {\n        const offers = extension.parse(secWebSocketExtensions);\n        if (offers[PerMessageDeflate2.extensionName]) {\n          perMessageDeflate.accept(offers[PerMessageDeflate2.extensionName]);\n          extensions[PerMessageDeflate2.extensionName] = perMessageDeflate;\n        }\n      } catch (err) {\n        const message = \"Invalid or unacceptable Sec-WebSocket-Extensions header\";\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n    if (this.options.verifyClient) {\n      const info = {\n        origin: req.headers[`${version === 8 ? \"sec-websocket-origin\" : \"origin\"}`],\n        secure: !!(req.socket.authorized || req.socket.encrypted),\n        req\n      };\n      if (this.options.verifyClient.length === 2) {\n        this.options.verifyClient(info, (verified, code, message, headers) => {\n          if (!verified) {\n            return abortHandshake(socket, code || 401, message, headers);\n          }\n          this.completeUpgrade(\n            extensions,\n            key,\n            protocols,\n            req,\n            socket,\n            head,\n            cb\n          );\n        });\n        return;\n      }\n      if (!this.options.verifyClient(info))\n        return abortHandshake(socket, 401);\n    }\n    this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n  }\n  /**\n   * Upgrade the connection to WebSocket.\n   *\n   * @param {Object} extensions The accepted extensions\n   * @param {String} key The value of the `Sec-WebSocket-Key` header\n   * @param {Set} protocols The subprotocols\n   * @param {http.IncomingMessage} req The request object\n   * @param {(net.Socket|tls.Socket)} socket The network socket between the\n   *     server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @throws {Error} If called more than once with the same socket\n   * @private\n   */\n  completeUpgrade(extensions, key, protocols, req, socket, head, cb) {\n    if (!socket.readable || !socket.writable)\n      return socket.destroy();\n    if (socket[kWebSocket]) {\n      throw new Error(\n        \"server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration\"\n      );\n    }\n    if (this._state > RUNNING)\n      return abortHandshake(socket, 503);\n    const digest = createHash(\"sha1\").update(key + GUID).digest(\"base64\");\n    const headers = [\n      \"HTTP/1.1 101 Switching Protocols\",\n      \"Upgrade: websocket\",\n      \"Connection: Upgrade\",\n      `Sec-WebSocket-Accept: ${digest}`\n    ];\n    const ws = new this.options.WebSocket(null);\n    if (protocols.size) {\n      const protocol = this.options.handleProtocols ? this.options.handleProtocols(protocols, req) : protocols.values().next().value;\n      if (protocol) {\n        headers.push(`Sec-WebSocket-Protocol: ${protocol}`);\n        ws._protocol = protocol;\n      }\n    }\n    if (extensions[PerMessageDeflate2.extensionName]) {\n      const params = extensions[PerMessageDeflate2.extensionName].params;\n      const value = extension.format({\n        [PerMessageDeflate2.extensionName]: [params]\n      });\n      headers.push(`Sec-WebSocket-Extensions: ${value}`);\n      ws._extensions = extensions;\n    }\n    this.emit(\"headers\", headers, req);\n    socket.write(headers.concat(\"\\r\\n\").join(\"\\r\\n\"));\n    socket.removeListener(\"error\", socketOnError);\n    ws.setSocket(socket, head, {\n      maxPayload: this.options.maxPayload,\n      skipUTF8Validation: this.options.skipUTF8Validation\n    });\n    if (this.clients) {\n      this.clients.add(ws);\n      ws.on(\"close\", () => {\n        this.clients.delete(ws);\n        if (this._shouldEmitClose && !this.clients.size) {\n          process.nextTick(emitClose, this);\n        }\n      });\n    }\n    cb(ws, req);\n  }\n}\nvar websocketServer = WebSocketServer;\nfunction addListeners(server, map) {\n  for (const event of Object.keys(map))\n    server.on(event, map[event]);\n  return function removeListeners() {\n    for (const event of Object.keys(map)) {\n      server.removeListener(event, map[event]);\n    }\n  };\n}\nfunction emitClose(server) {\n  server._state = CLOSED;\n  server.emit(\"close\");\n}\nfunction socketOnError() {\n  this.destroy();\n}\nfunction abortHandshake(socket, code, message, headers) {\n  message = message || http.STATUS_CODES[code];\n  headers = {\n    Connection: \"close\",\n    \"Content-Type\": \"text/html\",\n    \"Content-Length\": Buffer.byteLength(message),\n    ...headers\n  };\n  socket.once(\"finish\", socket.destroy);\n  socket.end(\n    `HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\\r\n` + Object.keys(headers).map((h) => `${h}: ${headers[h]}`).join(\"\\r\\n\") + \"\\r\\n\\r\\n\" + message\n  );\n}\nfunction abortHandshakeOrEmitwsClientError(server, req, socket, code, message) {\n  if (server.listenerCount(\"wsClientError\")) {\n    const err = new Error(message);\n    Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);\n    server.emit(\"wsClientError\", err, socket, req);\n  } else {\n    abortHandshake(socket, code, message);\n  }\n}\nconst websocketServer$1 = /* @__PURE__ */ getDefaultExportFromCjs(websocketServer);\nexport {\n  receiver$1 as Receiver,\n  sender$1 as Sender,\n  WebSocket$2 as WebSocket,\n  websocketServer$1 as WebSocketServer,\n  stream$1 as createWebSocketStream,\n  WebSocket$2 as default\n};\n"], "names": ["getDefaultExportFromCjs", "x", "getAugmentedNamespace", "n", "f", "a", "a2", "args", "Ctor", "k", "d", "Duplex", "require$$0", "emitClose$1", "stream2", "duplexOnEnd", "duplexOnError", "err", "createWebSocketStream", "ws", "options", "terminateOnDestroy", "duplex", "msg", "isBinary", "data", "callback", "called", "err2", "chunk", "encoding", "stream", "stream$1", "bufferUtil$1", "constants", "unmask$1", "mask", "EMPTY_BUFFER$3", "FastBuffer$2", "concat$1", "list", "totalLength", "target", "offset", "buf", "_mask", "source", "mask2", "output", "length", "i", "_unmask", "buffer", "toArrayBuffer$1", "toBuffer$2", "bufferUtil2", "bufferUtilExports", "kDone", "kRun", "Limiter$1", "concurrency", "job", "limiter", "zlib", "require$$0$1", "bufferUtil", "Limiter2", "kStatusCode$2", "FastBuffer$1", "TRAILER", "kPerMessageDeflate", "kTotalLength", "kCallback", "kBuffers", "kError$1", "zlibLimiter", "PerMessageDeflate$4", "isServer", "maxPayload", "params", "configurations", "offers", "opts", "accepted", "response", "key", "value", "num", "fin", "done", "result", "endpoint", "windowBits", "inflateOnError", "inflateOnData", "data2", "deflateOnData", "permessageDeflate", "validation", "__viteOptionalPeerDep_utf8Validate_ws", "__viteOptionalPeerDep_utf8Validate_ws$1", "require$$1", "isValidUTF8_1", "isUtf8", "require$$0$2", "tokenChars$2", "isValidStatusCode$2", "code", "_isValidUTF8", "len", "isValidUTF82", "validationExports", "Writable", "PerMessageDeflate$3", "BINARY_TYPES$1", "EMPTY_BUFFER$2", "kStatusCode$1", "kWebSocket$2", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unmask", "isValidStatusCode$1", "isValidUTF8", "FastBuffer", "GET_INFO", "GET_PAYLOAD_LENGTH_16", "GET_PAYLOAD_LENGTH_64", "GET_MASK", "GET_DATA", "INFLATING", "Receiver$1", "cb", "dst", "error", "compressed", "er", "messageLength", "fragments", "receiver", "ErrorCtor", "message", "prefix", "statusCode", "errorCode", "receiver$1", "randomFillSync", "require$$5", "PerMessageDeflate$2", "EMPTY_BUFFER$1", "isValidStatusCode", "applyMask", "toBuffer$1", "kByteLength", "<PERSON><PERSON><PERSON><PERSON>", "Sender$1", "Sender", "socket", "extensions", "generateMask", "merge", "skipMasking", "dataLength", "payloadLength", "byteLength", "readOnly", "perMessageDeflate", "opcode", "rsv1", "compress", "_", "sender", "sender$1", "kForOnEventAttribute$1", "kListener$1", "kCode", "kData", "kError", "kMessage", "kReason", "kTarget", "kType", "kWasClean", "Event", "type", "CloseEvent", "ErrorEvent", "MessageEvent", "EventTarget", "handler", "listener", "wrapper", "event", "callListener", "error2", "eventTarget", "thisArg", "tokenChars$1", "push", "dest", "name", "elem", "parse$2", "header", "mustUnescape", "isEscaping", "inQuotes", "extensionName", "paramName", "start", "end", "token", "format$1", "extension2", "values", "v", "extension$1", "EventEmitter$1", "require$$0$3", "https", "require$$1$1", "http$1", "require$$2", "net", "require$$3", "tls", "require$$4", "randomBytes", "createHash$1", "URL", "require$$7", "PerMessageDeflate$1", "Receiver2", "Sender2", "BINARY_TYPES", "EMPTY_BUFFER", "GUID$1", "kForOnEventAttribute", "kListener", "kStatusCode", "kWebSocket$1", "NOOP", "addEventListener", "removeEventListener", "format", "parse$1", "<PERSON><PERSON><PERSON><PERSON>", "closeTimeout", "kAborted", "protocolVersions", "readyStates", "subprotocolRegex", "WebSocket$1", "WebSocket", "address", "protocols", "initAsClient", "head", "receiver2", "receiverOnConclude", "receiverOnDrain", "receiver<PERSON>n<PERSON><PERSON><PERSON>", "receiverOnMessage", "receiverOnPing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socketOnClose", "socketOnData", "socketOnEnd", "socketOnError$1", "abortHandshake$1", "sendAfterClose", "property", "method", "websocket", "websocket2", "parsedUrl", "isSecure", "isIpcUrl", "invalidUrlMessage", "emitErrorAndClose", "defaultPort", "request", "protocolSet", "tlsConnect", "netConnect", "protocol", "parts", "req", "headers", "key2", "isSameHost", "res", "location", "addr", "digest", "serverProt", "protError", "secWebSocketExtensions", "extensionNames", "reason", "resume", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WebSocket$2", "tokenChars", "parse", "protocol2", "subprotocol$1", "EventEmitter", "http", "createHash", "extension", "PerMessageDeflate2", "subprotocol", "WebSocket2", "GUID", "kWebSocket", "keyRegex", "RUNNING", "CLOSING", "CLOSED", "WebSocketServer", "body", "emitConnection", "addListeners", "emitClose", "server", "index", "socketOnError", "version", "abortHandshakeOrEmitwsClientError", "abortHandshake", "secWebSocketProtocol", "info", "verified", "websocketServer", "map", "h", "websocketServer$1"], "mappings": "qDAUA,SAASA,EAAwBC,EAAG,CAClC,OAAOA,GAAKA,EAAE,YAAc,OAAO,UAAU,eAAe,KAAKA,EAAG,SAAS,EAAIA,EAAE,QAAaA,CAClG,CACA,SAASC,GAAsBC,EAAG,CAChC,GAAIA,EAAE,WACJ,OAAOA,EACT,IAAIC,EAAID,EAAE,QACV,GAAI,OAAOC,GAAK,WAAY,CAC1B,IAAIC,EAAI,SAASC,GAAK,CACpB,GAAI,gBAAgBA,EAAI,CACtB,IAAIC,EAAO,CAAC,IAAI,EAChBA,EAAK,KAAK,MAAMA,EAAM,SAAS,EAC/B,IAAIC,EAAO,SAAS,KAAK,MAAMJ,EAAGG,CAAI,EACtC,OAAO,IAAIC,EAEb,OAAOJ,EAAE,MAAM,KAAM,SAAS,CACpC,EACIC,EAAE,UAAYD,EAAE,eAEhBC,EAAI,CAAA,EACN,cAAO,eAAeA,EAAG,aAAc,CAAE,MAAO,EAAI,CAAE,EACtD,OAAO,KAAKF,CAAC,EAAE,QAAQ,SAASM,EAAG,CACjC,IAAIC,EAAI,OAAO,yBAAyBP,EAAGM,CAAC,EAC5C,OAAO,eAAeJ,EAAGI,EAAGC,EAAE,IAAMA,EAAI,CACtC,WAAY,GACZ,IAAK,UAAW,CACd,OAAOP,EAAEM,CAAC,CACX,CACP,CAAK,CACL,CAAG,EACMJ,CACT,CACA,KAAM,CAAE,OAAAM,EAAQ,EAAGC,EACnB,SAASC,GAAYC,EAAS,CAC5BA,EAAQ,KAAK,OAAO,CACtB,CACA,SAASC,IAAc,CACjB,CAAC,KAAK,WAAa,KAAK,eAAe,UACzC,KAAK,QAAO,CAEhB,CACA,SAASC,GAAcC,EAAK,CAC1B,KAAK,eAAe,QAASD,EAAa,EAC1C,KAAK,QAAO,EACR,KAAK,cAAc,OAAO,IAAM,GAClC,KAAK,KAAK,QAASC,CAAG,CAE1B,CACA,SAASC,GAAsBC,EAAIC,EAAS,CAC1C,IAAIC,EAAqB,GACzB,MAAMC,EAAS,IAAIX,GAAO,CACxB,GAAGS,EACH,YAAa,GACb,UAAW,GACX,WAAY,GACZ,mBAAoB,EACxB,CAAG,EACD,OAAAD,EAAG,GAAG,UAAW,SAAiBI,EAAKC,EAAU,CAC/C,MAAMC,EAAO,CAACD,GAAYF,EAAO,eAAe,WAAaC,EAAI,SAAU,EAAGA,EACzED,EAAO,KAAKG,CAAI,GACnBN,EAAG,MAAK,CACd,CAAG,EACDA,EAAG,KAAK,QAAS,SAAgBF,EAAK,CAChCK,EAAO,YAEXD,EAAqB,GACrBC,EAAO,QAAQL,CAAG,EACtB,CAAG,EACDE,EAAG,KAAK,QAAS,UAAiB,CAC5BG,EAAO,WAEXA,EAAO,KAAK,IAAI,CACpB,CAAG,EACDA,EAAO,SAAW,SAASL,EAAKS,EAAU,CACxC,GAAIP,EAAG,aAAeA,EAAG,OAAQ,CAC/BO,EAAST,CAAG,EACZ,QAAQ,SAASJ,GAAaS,CAAM,EACpC,OAEF,IAAIK,EAAS,GACbR,EAAG,KAAK,QAAS,SAAgBS,EAAM,CACrCD,EAAS,GACTD,EAASE,CAAI,CACnB,CAAK,EACDT,EAAG,KAAK,QAAS,UAAiB,CAC3BQ,GACHD,EAAST,CAAG,EACd,QAAQ,SAASJ,GAAaS,CAAM,CAC1C,CAAK,EACGD,GACFF,EAAG,UAAS,CAClB,EACEG,EAAO,OAAS,SAASI,EAAU,CACjC,GAAIP,EAAG,aAAeA,EAAG,WAAY,CACnCA,EAAG,KAAK,OAAQ,UAAgB,CAC9BG,EAAO,OAAOI,CAAQ,CAC9B,CAAO,EACD,OAEEP,EAAG,UAAY,OAEfA,EAAG,QAAQ,eAAe,UAC5BO,IACIJ,EAAO,eAAe,YACxBA,EAAO,QAAO,IAEhBH,EAAG,QAAQ,KAAK,SAAU,UAAkB,CAC1CO,GACR,CAAO,EACDP,EAAG,MAAK,GAEd,EACEG,EAAO,MAAQ,UAAW,CACpBH,EAAG,UACLA,EAAG,OAAM,CACf,EACEG,EAAO,OAAS,SAASO,EAAOC,EAAUJ,EAAU,CAClD,GAAIP,EAAG,aAAeA,EAAG,WAAY,CACnCA,EAAG,KAAK,OAAQ,UAAgB,CAC9BG,EAAO,OAAOO,EAAOC,EAAUJ,CAAQ,CAC/C,CAAO,EACD,OAEFP,EAAG,KAAKU,EAAOH,CAAQ,CAC3B,EACEJ,EAAO,GAAG,MAAOP,EAAW,EAC5BO,EAAO,GAAG,QAASN,EAAa,EACzBM,CACT,CACA,IAAIS,GAASb,GACR,MAACc,GAA2BhC,EAAwB+B,EAAM,EAC/D,IAAIE,GAAe,CAAE,QAAS,CAAA,GAC1BC,EAAY,CACd,aAAc,CAAC,aAAc,cAAe,WAAW,EACvD,aAAc,OAAO,MAAM,CAAC,EAC5B,KAAM,uCACN,qBAAsB,OAAO,wBAAwB,EACrD,UAAW,OAAO,WAAW,EAC7B,YAAa,OAAO,aAAa,EACjC,WAAY,OAAO,WAAW,EAC9B,KAAM,IAAM,CACX,CACH,EACIC,GACAC,GACJ,KAAM,CAAE,aAAcC,EAAgB,EAAGH,EACnCI,GAAe,OAAO,OAAO,OAAO,EAC1C,SAASC,GAASC,EAAMC,EAAa,CACnC,GAAID,EAAK,SAAW,EAClB,OAAOH,GACT,GAAIG,EAAK,SAAW,EAClB,OAAOA,EAAK,CAAC,EACf,MAAME,EAAS,OAAO,YAAYD,CAAW,EAC7C,IAAIE,EAAS,EACb,QAAS,EAAI,EAAG,EAAIH,EAAK,OAAQ,IAAK,CACpC,MAAMI,EAAMJ,EAAK,CAAC,EAClBE,EAAO,IAAIE,EAAKD,CAAM,EACtBA,GAAUC,EAAI,OAEhB,OAAID,EAASF,EACJ,IAAIH,GAAaI,EAAO,OAAQA,EAAO,WAAYC,CAAM,EAE3DD,CACT,CACA,SAASG,GAAMC,EAAQC,EAAOC,EAAQL,EAAQM,EAAQ,CACpD,QAASC,EAAI,EAAGA,EAAID,EAAQC,IAC1BF,EAAOL,EAASO,CAAC,EAAIJ,EAAOI,CAAC,EAAIH,EAAMG,EAAI,CAAC,CAEhD,CACA,SAASC,GAAQC,EAAQL,EAAO,CAC9B,QAASG,EAAI,EAAGA,EAAIE,EAAO,OAAQF,IACjCE,EAAOF,CAAC,GAAKH,EAAMG,EAAI,CAAC,CAE5B,CACA,SAASG,GAAgBT,EAAK,CAC5B,OAAIA,EAAI,SAAWA,EAAI,OAAO,WACrBA,EAAI,OAENA,EAAI,OAAO,MAAMA,EAAI,WAAYA,EAAI,WAAaA,EAAI,MAAM,CACrE,CACA,SAASU,GAAW7B,EAAM,CAExB,GADA6B,GAAW,SAAW,GAClB,OAAO,SAAS7B,CAAI,EACtB,OAAOA,EACT,IAAImB,EACJ,OAAInB,aAAgB,YAClBmB,EAAM,IAAIN,GAAab,CAAI,EAClB,YAAY,OAAOA,CAAI,EAChCmB,EAAM,IAAIN,GAAab,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,GAEpEmB,EAAM,OAAO,KAAKnB,CAAI,EACtB6B,GAAW,SAAW,IAEjBV,CACT,CACAX,GAAa,QAAU,CACrB,OAAQM,GACR,KAAMM,GACN,cAAeQ,GACf,SAAUC,GACV,OAAQH,EACV,EACA,GAAI,CAAC,CAAA,EAAY,kBACf,GAAI,CACF,MAAMI,EAAc,QAAQ,YAAY,EACxCnB,GAAOH,GAAa,QAAQ,KAAO,SAASa,EAAQC,EAAOC,EAAQL,EAAQM,EAAQ,CAC7EA,EAAS,GACXJ,GAAMC,EAAQC,EAAOC,EAAQL,EAAQM,CAAM,EAE3CM,EAAY,KAAKT,EAAQC,EAAOC,EAAQL,EAAQM,CAAM,CAC9D,EACId,GAAWF,GAAa,QAAQ,OAAS,SAASmB,EAAQL,EAAO,CAC3DK,EAAO,OAAS,GAClBD,GAAQC,EAAQL,CAAK,EAErBQ,EAAY,OAAOH,EAAQL,CAAK,CACxC,CACG,MAAC,CACD,CAEH,IAAIS,GAAoBvB,GAAa,QACrC,MAAMwB,GAAQ,OAAO,OAAO,EACtBC,GAAO,OAAO,MAAM,EAC1B,IAAIC,GAAY,KAAc,CAO5B,YAAYC,EAAa,CACvB,KAAKH,EAAK,EAAI,IAAM,CAClB,KAAK,UACL,KAAKC,EAAI,GACf,EACI,KAAK,YAAcE,GAAe,IAClC,KAAK,KAAO,GACZ,KAAK,QAAU,CAChB,CAOD,IAAIC,EAAK,CACP,KAAK,KAAK,KAAKA,CAAG,EAClB,KAAKH,EAAI,GACV,CAMD,CAACA,EAAI,GAAI,CACP,GAAI,KAAK,UAAY,KAAK,aAEtB,KAAK,KAAK,OAAQ,CACpB,MAAMG,EAAM,KAAK,KAAK,MAAK,EAC3B,KAAK,UACLA,EAAI,KAAKJ,EAAK,CAAC,EAElB,CACH,EACA,IAAIK,GAAUH,GACd,MAAMI,EAAOC,EACPC,GAAaT,GACbU,GAAWJ,GACX,CAAE,YAAaK,EAAe,EAAGjC,EACjCkC,GAAe,OAAO,OAAO,OAAO,EACpCC,GAAU,OAAO,KAAK,CAAC,EAAG,EAAG,IAAK,GAAG,CAAC,EACtCC,GAAqB,OAAO,oBAAoB,EAChDC,EAAe,OAAO,cAAc,EACpCC,EAAY,OAAO,UAAU,EAC7BC,EAAW,OAAO,SAAS,EAC3BC,EAAW,OAAO,OAAO,EAC/B,IAAIC,EACAC,GAAsB,KAAwB,CAyBhD,YAAYxD,EAASyD,EAAUC,EAAY,CAQzC,GAPA,KAAK,YAAcA,EAAa,EAChC,KAAK,SAAW1D,GAAW,GAC3B,KAAK,WAAa,KAAK,SAAS,YAAc,OAAS,KAAK,SAAS,UAAY,KACjF,KAAK,UAAY,CAAC,CAACyD,EACnB,KAAK,SAAW,KAChB,KAAK,SAAW,KAChB,KAAK,OAAS,KACV,CAACF,EAAa,CAChB,MAAMf,EAAc,KAAK,SAAS,mBAAqB,OAAS,KAAK,SAAS,iBAAmB,GACjGe,EAAc,IAAIT,GAASN,CAAW,EAEzC,CAID,WAAW,eAAgB,CACzB,MAAO,oBACR,CAOD,OAAQ,CACN,MAAMmB,EAAS,CAAA,EACf,OAAI,KAAK,SAAS,0BAChBA,EAAO,2BAA6B,IAElC,KAAK,SAAS,0BAChBA,EAAO,2BAA6B,IAElC,KAAK,SAAS,sBAChBA,EAAO,uBAAyB,KAAK,SAAS,qBAE5C,KAAK,SAAS,oBAChBA,EAAO,uBAAyB,KAAK,SAAS,oBACrC,KAAK,SAAS,qBAAuB,OAC9CA,EAAO,uBAAyB,IAE3BA,CACR,CAQD,OAAOC,EAAgB,CACrB,OAAAA,EAAiB,KAAK,gBAAgBA,CAAc,EACpD,KAAK,OAAS,KAAK,UAAY,KAAK,eAAeA,CAAc,EAAI,KAAK,eAAeA,CAAc,EAChG,KAAK,MACb,CAMD,SAAU,CAKR,GAJI,KAAK,WACP,KAAK,SAAS,QACd,KAAK,SAAW,MAEd,KAAK,SAAU,CACjB,MAAMtD,EAAW,KAAK,SAAS8C,CAAS,EACxC,KAAK,SAAS,QACd,KAAK,SAAW,KACZ9C,GACFA,EACE,IAAI,MACF,8DACD,CACX,EAGG,CAQD,eAAeuD,EAAQ,CACrB,MAAMC,EAAO,KAAK,SACZC,EAAWF,EAAO,KAAMF,GACxB,EAAAG,EAAK,0BAA4B,IAASH,EAAO,4BAA8BA,EAAO,yBAA2BG,EAAK,sBAAwB,IAAS,OAAOA,EAAK,qBAAwB,UAAYA,EAAK,oBAAsBH,EAAO,yBAA2B,OAAOG,EAAK,qBAAwB,UAAY,CAACH,EAAO,uBAIjU,EACD,GAAI,CAACI,EACH,MAAM,IAAI,MAAM,8CAA8C,EAEhE,OAAID,EAAK,0BACPC,EAAS,2BAA6B,IAEpCD,EAAK,0BACPC,EAAS,2BAA6B,IAEpC,OAAOD,EAAK,qBAAwB,WACtCC,EAAS,uBAAyBD,EAAK,qBAErC,OAAOA,EAAK,qBAAwB,SACtCC,EAAS,uBAAyBD,EAAK,qBAC9BC,EAAS,yBAA2B,IAAQD,EAAK,sBAAwB,KAClF,OAAOC,EAAS,uBAEXA,CACR,CAQD,eAAeC,EAAU,CACvB,MAAML,EAASK,EAAS,CAAC,EACzB,GAAI,KAAK,SAAS,0BAA4B,IAASL,EAAO,2BAC5D,MAAM,IAAI,MAAM,mDAAmD,EAErE,GAAI,CAACA,EAAO,uBACN,OAAO,KAAK,SAAS,qBAAwB,WAC/CA,EAAO,uBAAyB,KAAK,SAAS,6BAEvC,KAAK,SAAS,sBAAwB,IAAS,OAAO,KAAK,SAAS,qBAAwB,UAAYA,EAAO,uBAAyB,KAAK,SAAS,oBAC/J,MAAM,IAAI,MACR,0DACR,EAEI,OAAOA,CACR,CAQD,gBAAgBC,EAAgB,CAC9B,OAAAA,EAAe,QAASD,GAAW,CACjC,OAAO,KAAKA,CAAM,EAAE,QAASM,GAAQ,CACnC,IAAIC,EAAQP,EAAOM,CAAG,EACtB,GAAIC,EAAM,OAAS,EACjB,MAAM,IAAI,MAAM,cAAcD,kCAAoC,EAGpE,GADAC,EAAQA,EAAM,CAAC,EACXD,IAAQ,0BACV,GAAIC,IAAU,GAAM,CAClB,MAAMC,EAAM,CAACD,EACb,GAAI,CAAC,OAAO,UAAUC,CAAG,GAAKA,EAAM,GAAKA,EAAM,GAC7C,MAAM,IAAI,UACR,gCAAgCF,OAASC,GACzD,EAEYA,EAAQC,UACC,CAAC,KAAK,UACf,MAAM,IAAI,UACR,gCAAgCF,OAASC,GACvD,UAEmBD,IAAQ,yBAA0B,CAC3C,MAAME,EAAM,CAACD,EACb,GAAI,CAAC,OAAO,UAAUC,CAAG,GAAKA,EAAM,GAAKA,EAAM,GAC7C,MAAM,IAAI,UACR,gCAAgCF,OAASC,GACvD,EAEUA,EAAQC,UACCF,IAAQ,8BAAgCA,IAAQ,8BACzD,GAAIC,IAAU,GACZ,MAAM,IAAI,UACR,gCAAgCD,OAASC,GACvD,MAGU,OAAM,IAAI,MAAM,sBAAsBD,IAAM,EAE9CN,EAAOM,CAAG,EAAIC,CACtB,CAAO,CACP,CAAK,EACMN,CACR,CASD,WAAWvD,EAAM+D,EAAK9D,EAAU,CAC9BiD,EAAY,IAAKc,GAAS,CACxB,KAAK,YAAYhE,EAAM+D,EAAK,CAACvE,EAAKyE,IAAW,CAC3CD,IACA/D,EAAST,EAAKyE,CAAM,CAC5B,CAAO,CACP,CAAK,CACF,CASD,SAASjE,EAAM+D,EAAK9D,EAAU,CAC5BiD,EAAY,IAAKc,GAAS,CACxB,KAAK,UAAUhE,EAAM+D,EAAK,CAACvE,EAAKyE,IAAW,CACzCD,IACA/D,EAAST,EAAKyE,CAAM,CAC5B,CAAO,CACP,CAAK,CACF,CASD,YAAYjE,EAAM+D,EAAK9D,EAAU,CAC/B,MAAMiE,EAAW,KAAK,UAAY,SAAW,SAC7C,GAAI,CAAC,KAAK,SAAU,CAClB,MAAMN,EAAM,GAAGM,oBACTC,EAAa,OAAO,KAAK,OAAOP,CAAG,GAAM,SAAWtB,EAAK,qBAAuB,KAAK,OAAOsB,CAAG,EACrG,KAAK,SAAWtB,EAAK,iBAAiB,CACpC,GAAG,KAAK,SAAS,mBACjB,WAAA6B,CACR,CAAO,EACD,KAAK,SAAStB,EAAkB,EAAI,KACpC,KAAK,SAASC,CAAY,EAAI,EAC9B,KAAK,SAASE,CAAQ,EAAI,GAC1B,KAAK,SAAS,GAAG,QAASoB,EAAc,EACxC,KAAK,SAAS,GAAG,OAAQC,EAAa,EAExC,KAAK,SAAStB,CAAS,EAAI9C,EAC3B,KAAK,SAAS,MAAMD,CAAI,EACpB+D,GACF,KAAK,SAAS,MAAMnB,EAAO,EAC7B,KAAK,SAAS,MAAM,IAAM,CACxB,MAAMpD,EAAM,KAAK,SAASyD,CAAQ,EAClC,GAAIzD,EAAK,CACP,KAAK,SAAS,QACd,KAAK,SAAW,KAChBS,EAAST,CAAG,EACZ,OAEF,MAAM8E,EAAQ9B,GAAW,OACvB,KAAK,SAASQ,CAAQ,EACtB,KAAK,SAASF,CAAY,CAClC,EACU,KAAK,SAAS,eAAe,YAC/B,KAAK,SAAS,QACd,KAAK,SAAW,OAEhB,KAAK,SAASA,CAAY,EAAI,EAC9B,KAAK,SAASE,CAAQ,EAAI,GACtBe,GAAO,KAAK,OAAO,GAAGG,uBAA8B,GACtD,KAAK,SAAS,SAGlBjE,EAAS,KAAMqE,CAAK,CAC1B,CAAK,CACF,CASD,UAAUtE,EAAM+D,EAAK9D,EAAU,CAC7B,MAAMiE,EAAW,KAAK,UAAY,SAAW,SAC7C,GAAI,CAAC,KAAK,SAAU,CAClB,MAAMN,EAAM,GAAGM,oBACTC,EAAa,OAAO,KAAK,OAAOP,CAAG,GAAM,SAAWtB,EAAK,qBAAuB,KAAK,OAAOsB,CAAG,EACrG,KAAK,SAAWtB,EAAK,iBAAiB,CACpC,GAAG,KAAK,SAAS,mBACjB,WAAA6B,CACR,CAAO,EACD,KAAK,SAASrB,CAAY,EAAI,EAC9B,KAAK,SAASE,CAAQ,EAAI,GAC1B,KAAK,SAAS,GAAG,OAAQuB,EAAa,EAExC,KAAK,SAASxB,CAAS,EAAI9C,EAC3B,KAAK,SAAS,MAAMD,CAAI,EACxB,KAAK,SAAS,MAAMsC,EAAK,aAAc,IAAM,CAC3C,GAAI,CAAC,KAAK,SACR,OAEF,IAAIgC,EAAQ9B,GAAW,OACrB,KAAK,SAASQ,CAAQ,EACtB,KAAK,SAASF,CAAY,CAClC,EACUiB,IACFO,EAAQ,IAAI3B,GAAa2B,EAAM,OAAQA,EAAM,WAAYA,EAAM,OAAS,CAAC,GAE3E,KAAK,SAASvB,CAAS,EAAI,KAC3B,KAAK,SAASD,CAAY,EAAI,EAC9B,KAAK,SAASE,CAAQ,EAAI,GACtBe,GAAO,KAAK,OAAO,GAAGG,uBAA8B,GACtD,KAAK,SAAS,QAEhBjE,EAAS,KAAMqE,CAAK,CAC1B,CAAK,CACF,CACH,EACA,IAAIE,GAAoBrB,GACxB,SAASoB,GAAcnE,EAAO,CAC5B,KAAK4C,CAAQ,EAAE,KAAK5C,CAAK,EACzB,KAAK0C,CAAY,GAAK1C,EAAM,MAC9B,CACA,SAASiE,GAAcjE,EAAO,CAE5B,GADA,KAAK0C,CAAY,GAAK1C,EAAM,OACxB,KAAKyC,EAAkB,EAAE,YAAc,GAAK,KAAKC,CAAY,GAAK,KAAKD,EAAkB,EAAE,YAAa,CAC1G,KAAKG,CAAQ,EAAE,KAAK5C,CAAK,EACzB,OAEF,KAAK6C,CAAQ,EAAI,IAAI,WAAW,2BAA2B,EAC3D,KAAKA,CAAQ,EAAE,KAAO,oCACtB,KAAKA,CAAQ,EAAEP,EAAa,EAAI,KAChC,KAAK,eAAe,OAAQ2B,EAAa,EACzC,KAAK,MAAK,CACZ,CACA,SAASD,GAAe5E,EAAK,CAC3B,KAAKqD,EAAkB,EAAE,SAAW,KACpCrD,EAAIkD,EAAa,EAAI,KACrB,KAAKK,CAAS,EAAEvD,CAAG,CACrB,CACA,IAAIiF,GAAa,CAAE,QAAS,CAAA,GAC5B,MAAMC,GAAwC,CAAA,EACxCC,GAA0D,OAAO,OAAuB,OAAO,eAAe,CAClH,UAAW,KACX,QAASD,EACX,EAAG,OAAO,YAAa,CAAE,MAAO,QAAQ,CAAE,CAAC,EACrCE,GAA6BnG,GAAsBkG,EAAuC,EAChG,IAAIE,GACJ,KAAM,CAAE,OAAAC,EAAQ,EAAGC,EACbC,GAAe,CACnB,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,CAEF,EACA,SAASC,GAAoBC,EAAM,CACjC,OAAOA,GAAQ,KAAOA,GAAQ,MAAQA,IAAS,MAAQA,IAAS,MAAQA,IAAS,MAAQA,GAAQ,KAAOA,GAAQ,IAClH,CACA,SAASC,GAAahE,EAAK,CACzB,MAAMiE,EAAMjE,EAAI,OAChB,IAAIM,EAAI,EACR,KAAOA,EAAI2D,GACT,GAAK,EAAAjE,EAAIM,CAAC,EAAI,KACZA,aACUN,EAAIM,CAAC,EAAI,OAAS,IAAK,CACjC,GAAIA,EAAI,IAAM2D,IAAQjE,EAAIM,EAAI,CAAC,EAAI,OAAS,MAAQN,EAAIM,CAAC,EAAI,OAAS,IACpE,MAAO,GAETA,GAAK,WACKN,EAAIM,CAAC,EAAI,OAAS,IAAK,CACjC,GAAIA,EAAI,GAAK2D,IAAQjE,EAAIM,EAAI,CAAC,EAAI,OAAS,MAAQN,EAAIM,EAAI,CAAC,EAAI,OAAS,KAAON,EAAIM,CAAC,IAAM,MAAQN,EAAIM,EAAI,CAAC,EAAI,OAAS,KACzHN,EAAIM,CAAC,IAAM,MAAQN,EAAIM,EAAI,CAAC,EAAI,OAAS,IACvC,MAAO,GAETA,GAAK,WACKN,EAAIM,CAAC,EAAI,OAAS,IAAK,CACjC,GAAIA,EAAI,GAAK2D,IAAQjE,EAAIM,EAAI,CAAC,EAAI,OAAS,MAAQN,EAAIM,EAAI,CAAC,EAAI,OAAS,MAAQN,EAAIM,EAAI,CAAC,EAAI,OAAS,KAAON,EAAIM,CAAC,IAAM,MAAQN,EAAIM,EAAI,CAAC,EAAI,OAAS,KACvJN,EAAIM,CAAC,IAAM,KAAON,EAAIM,EAAI,CAAC,EAAI,KAAON,EAAIM,CAAC,EAAI,IAC7C,MAAO,GAETA,GAAK,MAEL,OAAO,GAGX,MAAO,EACT,CACAgD,GAAW,QAAU,CACnB,kBAAmBQ,GACnB,YAAaE,GACb,WAAYH,EACd,EACA,GAAIF,GACFD,GAAgBJ,GAAW,QAAQ,YAAc,SAAStD,EAAK,CAC7D,OAAOA,EAAI,OAAS,GAAKgE,GAAahE,CAAG,EAAI2D,GAAO3D,CAAG,CAC3D,UACW,CAAC,CAAA,EAAY,qBACtB,GAAI,CACF,MAAMkE,EAAeT,GACrBC,GAAgBJ,GAAW,QAAQ,YAAc,SAAStD,EAAK,CAC7D,OAAOA,EAAI,OAAS,GAAKgE,GAAahE,CAAG,EAAIkE,EAAalE,CAAG,CACnE,CACG,MAAC,CACD,CAEH,IAAImE,GAAoBb,GAAW,QACnC,KAAM,CAAE,SAAAc,EAAU,EAAGpG,EACfqG,GAAsBhB,GACtB,CACJ,aAAciB,GACd,aAAcC,GACd,YAAaC,GACb,WAAYC,EACd,EAAInF,EACE,CAAE,OAAAoF,GAAQ,cAAAC,GAAe,OAAAC,EAAM,EAAKhE,GACpC,CAAE,kBAAmBiE,GAAqB,YAAAC,EAAW,EAAKX,GAC1DY,EAAa,OAAO,OAAO,OAAO,EAClCC,EAAW,EACXC,GAAwB,EACxBC,GAAwB,EACxBC,GAAW,EACXC,GAAW,EACXC,GAAY,EAClB,IAAIC,GAAa,cAAuBlB,EAAS,CAc/C,YAAY5F,EAAU,GAAI,CACxB,QACA,KAAK,YAAcA,EAAQ,YAAc8F,GAAe,CAAC,EACzD,KAAK,YAAc9F,EAAQ,YAAc,CAAA,EACzC,KAAK,UAAY,CAAC,CAACA,EAAQ,SAC3B,KAAK,YAAcA,EAAQ,WAAa,EACxC,KAAK,oBAAsB,CAAC,CAACA,EAAQ,mBACrC,KAAKiG,EAAY,EAAI,OACrB,KAAK,eAAiB,EACtB,KAAK,SAAW,GAChB,KAAK,YAAc,GACnB,KAAK,eAAiB,EACtB,KAAK,MAAQ,OACb,KAAK,YAAc,EACnB,KAAK,QAAU,GACf,KAAK,KAAO,GACZ,KAAK,QAAU,EACf,KAAK,oBAAsB,EAC3B,KAAK,eAAiB,EACtB,KAAK,WAAa,GAClB,KAAK,OAASO,EACd,KAAK,MAAQ,EACd,CASD,OAAO/F,EAAOC,EAAUqG,EAAI,CAC1B,GAAI,KAAK,UAAY,GAAK,KAAK,QAAUP,EACvC,OAAOO,EAAE,EACX,KAAK,gBAAkBtG,EAAM,OAC7B,KAAK,SAAS,KAAKA,CAAK,EACxB,KAAK,UAAUsG,CAAE,CAClB,CAQD,QAAQhI,EAAG,CAET,GADA,KAAK,gBAAkBA,EACnBA,IAAM,KAAK,SAAS,CAAC,EAAE,OACzB,OAAO,KAAK,SAAS,QACvB,GAAIA,EAAI,KAAK,SAAS,CAAC,EAAE,OAAQ,CAC/B,MAAMyC,EAAM,KAAK,SAAS,CAAC,EAC3B,YAAK,SAAS,CAAC,EAAI,IAAI+E,EACrB/E,EAAI,OACJA,EAAI,WAAazC,EACjByC,EAAI,OAASzC,CACrB,EACa,IAAIwH,EAAW/E,EAAI,OAAQA,EAAI,WAAYzC,CAAC,EAErD,MAAMiI,EAAM,OAAO,YAAYjI,CAAC,EAChC,EAAG,CACD,MAAMyC,EAAM,KAAK,SAAS,CAAC,EACrBD,EAASyF,EAAI,OAASjI,EACxBA,GAAKyC,EAAI,OACXwF,EAAI,IAAI,KAAK,SAAS,MAAK,EAAIzF,CAAM,GAErCyF,EAAI,IAAI,IAAI,WAAWxF,EAAI,OAAQA,EAAI,WAAYzC,CAAC,EAAGwC,CAAM,EAC7D,KAAK,SAAS,CAAC,EAAI,IAAIgF,EACrB/E,EAAI,OACJA,EAAI,WAAazC,EACjByC,EAAI,OAASzC,CACvB,GAEMA,GAAKyC,EAAI,aACFzC,EAAI,GACb,OAAOiI,CACR,CAOD,UAAUD,EAAI,CACZ,IAAIlH,EACJ,KAAK,MAAQ,GACb,EACE,QAAQ,KAAK,OAAM,CACjB,KAAK2G,EACH3G,EAAM,KAAK,UACX,MACF,KAAK4G,GACH5G,EAAM,KAAK,qBACX,MACF,KAAK6G,GACH7G,EAAM,KAAK,qBACX,MACF,KAAK8G,GACH,KAAK,QAAO,EACZ,MACF,KAAKC,GACH/G,EAAM,KAAK,QAAQkH,CAAE,EACrB,MACF,QACE,KAAK,MAAQ,GACb,MACH,OACM,KAAK,OACdA,EAAGlH,CAAG,CACP,CAOD,SAAU,CACR,GAAI,KAAK,eAAiB,EAAG,CAC3B,KAAK,MAAQ,GACb,OAEF,MAAM2B,EAAM,KAAK,QAAQ,CAAC,EAC1B,GAAKA,EAAI,CAAC,EAAI,GACZ,YAAK,MAAQ,GACNyF,EACL,WACA,8BACA,GACA,KACA,2BACR,EAEI,MAAMC,GAAc1F,EAAI,CAAC,EAAI,MAAQ,GACrC,GAAI0F,GAAc,CAAC,KAAK,YAAYrB,GAAoB,aAAa,EACnE,YAAK,MAAQ,GACNoB,EACL,WACA,qBACA,GACA,KACA,yBACR,EAKI,GAHA,KAAK,MAAQzF,EAAI,CAAC,EAAI,OAAS,IAC/B,KAAK,QAAUA,EAAI,CAAC,EAAI,GACxB,KAAK,eAAiBA,EAAI,CAAC,EAAI,IAC3B,KAAK,UAAY,EAAG,CACtB,GAAI0F,EACF,YAAK,MAAQ,GACND,EACL,WACA,qBACA,GACA,KACA,yBACV,EAEM,GAAI,CAAC,KAAK,YACR,YAAK,MAAQ,GACNA,EACL,WACA,mBACA,GACA,KACA,uBACV,EAEM,KAAK,QAAU,KAAK,oBACX,KAAK,UAAY,GAAK,KAAK,UAAY,EAAG,CACnD,GAAI,KAAK,YACP,YAAK,MAAQ,GACNA,EACL,WACA,kBAAkB,KAAK,UACvB,GACA,KACA,uBACV,EAEM,KAAK,YAAcC,UACV,KAAK,QAAU,GAAK,KAAK,QAAU,GAAI,CAChD,GAAI,CAAC,KAAK,KACR,YAAK,MAAQ,GACND,EACL,WACA,kBACA,GACA,KACA,qBACV,EAEM,GAAIC,EACF,YAAK,MAAQ,GACND,EACL,WACA,qBACA,GACA,KACA,yBACV,EAEM,GAAI,KAAK,eAAiB,KAAO,KAAK,UAAY,GAAK,KAAK,iBAAmB,EAC7E,YAAK,MAAQ,GACNA,EACL,WACA,0BAA0B,KAAK,iBAC/B,GACA,KACA,uCACV,MAGM,aAAK,MAAQ,GACNA,EACL,WACA,kBAAkB,KAAK,UACvB,GACA,KACA,uBACR,EAKI,GAHI,CAAC,KAAK,MAAQ,CAAC,KAAK,cACtB,KAAK,YAAc,KAAK,SAC1B,KAAK,SAAWzF,EAAI,CAAC,EAAI,OAAS,IAC9B,KAAK,WACP,GAAI,CAAC,KAAK,QACR,YAAK,MAAQ,GACNyF,EACL,WACA,mBACA,GACA,KACA,sBACV,UAEe,KAAK,QACd,YAAK,MAAQ,GACNA,EACL,WACA,qBACA,GACA,KACA,wBACR,EAEI,GAAI,KAAK,iBAAmB,IAC1B,KAAK,OAASR,WACP,KAAK,iBAAmB,IAC/B,KAAK,OAASC,OAEd,QAAO,KAAK,YACf,CAOD,oBAAqB,CACnB,GAAI,KAAK,eAAiB,EAAG,CAC3B,KAAK,MAAQ,GACb,OAEF,YAAK,eAAiB,KAAK,QAAQ,CAAC,EAAE,aAAa,CAAC,EAC7C,KAAK,YACb,CAOD,oBAAqB,CACnB,GAAI,KAAK,eAAiB,EAAG,CAC3B,KAAK,MAAQ,GACb,OAEF,MAAMlF,EAAM,KAAK,QAAQ,CAAC,EACpB2C,EAAM3C,EAAI,aAAa,CAAC,EAC9B,OAAI2C,EAAM,KAAK,IAAI,EAAG,GAAK,EAAE,EAAI,GAC/B,KAAK,MAAQ,GACN8C,EACL,WACA,yDACA,GACA,KACA,wCACR,IAEI,KAAK,eAAiB9C,EAAM,KAAK,IAAI,EAAG,EAAE,EAAI3C,EAAI,aAAa,CAAC,EACzD,KAAK,aACb,CAOD,YAAa,CACX,GAAI,KAAK,gBAAkB,KAAK,QAAU,IACxC,KAAK,qBAAuB,KAAK,eAC7B,KAAK,oBAAsB,KAAK,aAAe,KAAK,YAAc,GACpE,YAAK,MAAQ,GACNyF,EACL,WACA,4BACA,GACA,KACA,mCACV,EAGQ,KAAK,QACP,KAAK,OAASN,GAEd,KAAK,OAASC,EACjB,CAMD,SAAU,CACR,GAAI,KAAK,eAAiB,EAAG,CAC3B,KAAK,MAAQ,GACb,OAEF,KAAK,MAAQ,KAAK,QAAQ,CAAC,EAC3B,KAAK,OAASA,EACf,CAQD,QAAQG,EAAI,CACV,IAAI1G,EAAO0F,GACX,GAAI,KAAK,eAAgB,CACvB,GAAI,KAAK,eAAiB,KAAK,eAAgB,CAC7C,KAAK,MAAQ,GACb,OAEF1F,EAAO,KAAK,QAAQ,KAAK,cAAc,EACnC,KAAK,SAAY,KAAK,MAAM,CAAC,EAAI,KAAK,MAAM,CAAC,EAAI,KAAK,MAAM,CAAC,EAAI,KAAK,MAAM,CAAC,GAC/E+F,GAAO/F,EAAM,KAAK,KAAK,EAG3B,GAAI,KAAK,QAAU,EACjB,OAAO,KAAK,eAAeA,CAAI,EACjC,GAAI,KAAK,YAAa,CACpB,KAAK,OAASwG,GACd,KAAK,WAAWxG,EAAM0G,CAAE,EACxB,OAEF,OAAI1G,EAAK,SACP,KAAK,eAAiB,KAAK,oBAC3B,KAAK,WAAW,KAAKA,CAAI,GAEpB,KAAK,aACb,CAQD,WAAWA,EAAM0G,EAAI,CACO,KAAK,YAAYlB,GAAoB,aAAa,EAC1D,WAAWxF,EAAM,KAAK,KAAM,CAACR,EAAK2B,IAAQ,CAC1D,GAAI3B,EACF,OAAOkH,EAAGlH,CAAG,EACf,GAAI2B,EAAI,OAAQ,CAEd,GADA,KAAK,gBAAkBA,EAAI,OACvB,KAAK,eAAiB,KAAK,aAAe,KAAK,YAAc,EAC/D,OAAOuF,EACLE,EACE,WACA,4BACA,GACA,KACA,mCACD,CACb,EAEQ,KAAK,WAAW,KAAKzF,CAAG,EAE1B,MAAM2F,EAAK,KAAK,cAChB,GAAIA,EACF,OAAOJ,EAAGI,CAAE,EACd,KAAK,UAAUJ,CAAE,CACvB,CAAK,CACF,CAOD,aAAc,CACZ,GAAI,KAAK,KAAM,CACb,MAAMK,EAAgB,KAAK,eACrBC,EAAY,KAAK,WAKvB,GAJA,KAAK,oBAAsB,EAC3B,KAAK,eAAiB,EACtB,KAAK,YAAc,EACnB,KAAK,WAAa,GACd,KAAK,UAAY,EAAG,CACtB,IAAIhH,EACA,KAAK,cAAgB,aACvBA,EAAO6F,GAAOmB,EAAWD,CAAa,EAC7B,KAAK,cAAgB,cAC9B/G,EAAO8F,GAAcD,GAAOmB,EAAWD,CAAa,CAAC,EAErD/G,EAAOgH,EAET,KAAK,KAAK,UAAWhH,EAAM,EAAI,MAC1B,CACL,MAAMmB,EAAM0E,GAAOmB,EAAWD,CAAa,EAC3C,GAAI,CAAC,KAAK,qBAAuB,CAACd,GAAY9E,CAAG,EAC/C,YAAK,MAAQ,GACNyF,EACL,MACA,yBACA,GACA,KACA,qBACZ,EAEQ,KAAK,KAAK,UAAWzF,EAAK,EAAK,GAGnC,KAAK,OAASgF,CACf,CAQD,eAAenG,EAAM,CACnB,GAAI,KAAK,UAAY,EAEnB,GADA,KAAK,MAAQ,GACTA,EAAK,SAAW,EAClB,KAAK,KAAK,WAAY,KAAM0F,EAAc,EAC1C,KAAK,IAAG,MACH,CACL,MAAMR,EAAOlF,EAAK,aAAa,CAAC,EAChC,GAAI,CAACgG,GAAoBd,CAAI,EAC3B,OAAO0B,EACL,WACA,uBAAuB1B,IACvB,GACA,KACA,2BACZ,EAEQ,MAAM/D,EAAM,IAAI+E,EACdlG,EAAK,OACLA,EAAK,WAAa,EAClBA,EAAK,OAAS,CACxB,EACQ,GAAI,CAAC,KAAK,qBAAuB,CAACiG,GAAY9E,CAAG,EAC/C,OAAOyF,EACL,MACA,yBACA,GACA,KACA,qBACZ,EAEQ,KAAK,KAAK,WAAY1B,EAAM/D,CAAG,EAC/B,KAAK,IAAG,OAED,KAAK,UAAY,EAC1B,KAAK,KAAK,OAAQnB,CAAI,EAEtB,KAAK,KAAK,OAAQA,CAAI,EAExB,KAAK,OAASmG,CACf,CACH,EACA,IAAIc,GAAWR,GACf,SAASG,EAAMM,EAAWC,EAASC,EAAQC,EAAYC,EAAW,CAChE,MAAM9H,EAAM,IAAI0H,EACdE,EAAS,4BAA4BD,IAAYA,CACrD,EACE,aAAM,kBAAkB3H,EAAKoH,CAAK,EAClCpH,EAAI,KAAO8H,EACX9H,EAAImG,EAAa,EAAI0B,EACd7H,CACT,CACK,MAAC+H,GAA6BhJ,EAAwB0I,EAAQ,EAC7D,CAAE,eAAAO,EAAgB,EAAGC,EACrBC,GAAsBlD,GACtB,CAAE,aAAcmD,EAAgB,EAAGlH,EACnC,CAAE,kBAAAmH,EAAmB,EAAGtC,GACxB,CAAE,KAAMuC,GAAW,SAAUC,CAAU,EAAK/F,GAC5CgG,EAAc,OAAO,aAAa,EAClCC,GAAa,OAAO,MAAM,CAAC,EACjC,IAAIC,GAAW,MAAMC,CAAO,CAS1B,YAAYC,EAAQC,EAAYC,EAAc,CAC5C,KAAK,YAAcD,GAAc,GAC7BC,IACF,KAAK,cAAgBA,EACrB,KAAK,YAAc,OAAO,MAAM,CAAC,GAEnC,KAAK,QAAUF,EACf,KAAK,eAAiB,GACtB,KAAK,UAAY,GACjB,KAAK,eAAiB,EACtB,KAAK,WAAa,GAClB,KAAK,OAAS,EACf,CAsBD,OAAO,MAAMnI,EAAML,EAAS,CAC1B,IAAI2B,EACAgH,EAAQ,GACRpH,EAAS,EACTqH,EAAc,GACd5I,EAAQ,OACV2B,EAAQ3B,EAAQ,YAAcqI,GAC1BrI,EAAQ,aACVA,EAAQ,aAAa2B,CAAK,EAE1BkG,GAAelG,EAAO,EAAG,CAAC,EAE5BiH,GAAejH,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAIA,EAAM,CAAC,KAAO,EAC9DJ,EAAS,GAEX,IAAIsH,EACA,OAAOxI,GAAS,UACb,CAACL,EAAQ,MAAQ4I,IAAgB5I,EAAQoI,CAAW,IAAM,OAC7DS,EAAa7I,EAAQoI,CAAW,GAEhC/H,EAAO,OAAO,KAAKA,CAAI,EACvBwI,EAAaxI,EAAK,SAGpBwI,EAAaxI,EAAK,OAClBsI,EAAQ3I,EAAQ,MAAQA,EAAQ,UAAY,CAAC4I,GAE/C,IAAIE,EAAgBD,EAChBA,GAAc,OAChBtH,GAAU,EACVuH,EAAgB,KACPD,EAAa,MACtBtH,GAAU,EACVuH,EAAgB,KAElB,MAAMxH,EAAS,OAAO,YAAYqH,EAAQE,EAAatH,EAASA,CAAM,EAWtE,OAVAD,EAAO,CAAC,EAAItB,EAAQ,IAAMA,EAAQ,OAAS,IAAMA,EAAQ,OACrDA,EAAQ,OACVsB,EAAO,CAAC,GAAK,IACfA,EAAO,CAAC,EAAIwH,EACRA,IAAkB,IACpBxH,EAAO,cAAcuH,EAAY,CAAC,EACzBC,IAAkB,MAC3BxH,EAAO,CAAC,EAAIA,EAAO,CAAC,EAAI,EACxBA,EAAO,YAAYuH,EAAY,EAAG,CAAC,GAEhC7I,EAAQ,MAEbsB,EAAO,CAAC,GAAK,IACbA,EAAOC,EAAS,CAAC,EAAII,EAAM,CAAC,EAC5BL,EAAOC,EAAS,CAAC,EAAII,EAAM,CAAC,EAC5BL,EAAOC,EAAS,CAAC,EAAII,EAAM,CAAC,EAC5BL,EAAOC,EAAS,CAAC,EAAII,EAAM,CAAC,EACxBiH,EACK,CAACtH,EAAQjB,CAAI,EAClBsI,GACFT,GAAU7H,EAAMsB,EAAOL,EAAQC,EAAQsH,CAAU,EAC1C,CAACvH,CAAM,IAEhB4G,GAAU7H,EAAMsB,EAAOtB,EAAM,EAAGwI,CAAU,EACnC,CAACvH,EAAQjB,CAAI,IAbX,CAACiB,EAAQjB,CAAI,CAcvB,CAUD,MAAMkF,EAAMlF,EAAMsB,EAAOoF,EAAI,CAC3B,IAAIvF,EACJ,GAAI+D,IAAS,OACX/D,EAAMwG,OACD,IAAI,OAAOzC,GAAS,UAAY,CAAC0C,GAAkB1C,CAAI,EAC5D,MAAM,IAAI,UAAU,kDAAkD,EACjE,GAAIlF,IAAS,QAAU,CAACA,EAAK,OAClCmB,EAAM,OAAO,YAAY,CAAC,EAC1BA,EAAI,cAAc+D,EAAM,CAAC,MACpB,CACL,MAAM1D,EAAS,OAAO,WAAWxB,CAAI,EACrC,GAAIwB,EAAS,IACX,MAAM,IAAI,WAAW,gDAAgD,EAEvEL,EAAM,OAAO,YAAY,EAAIK,CAAM,EACnCL,EAAI,cAAc+D,EAAM,CAAC,EACrB,OAAOlF,GAAS,SAClBmB,EAAI,MAAMnB,EAAM,CAAC,EAEjBmB,EAAI,IAAInB,EAAM,CAAC,GAGnB,MAAML,EAAU,CACd,CAACoI,CAAW,EAAG5G,EAAI,OACnB,IAAK,GACL,aAAc,KAAK,cACnB,KAAMG,EACN,WAAY,KAAK,YACjB,OAAQ,EACR,SAAU,GACV,KAAM,EACZ,EACQ,KAAK,WACP,KAAK,QAAQ,CAAC,KAAK,SAAUH,EAAK,GAAOxB,EAAS+G,CAAE,CAAC,EAErD,KAAK,UAAUwB,EAAO,MAAM/G,EAAKxB,CAAO,EAAG+G,CAAE,CAEhD,CASD,KAAK1G,EAAMsB,EAAOoF,EAAI,CACpB,IAAIgC,EACAC,EASJ,GARI,OAAO3I,GAAS,UAClB0I,EAAa,OAAO,WAAW1I,CAAI,EACnC2I,EAAW,KAEX3I,EAAO8H,EAAW9H,CAAI,EACtB0I,EAAa1I,EAAK,OAClB2I,EAAWb,EAAW,UAEpBY,EAAa,IACf,MAAM,IAAI,WAAW,kDAAkD,EAEzE,MAAM/I,EAAU,CACd,CAACoI,CAAW,EAAGW,EACf,IAAK,GACL,aAAc,KAAK,cACnB,KAAMpH,EACN,WAAY,KAAK,YACjB,OAAQ,EACR,SAAAqH,EACA,KAAM,EACZ,EACQ,KAAK,WACP,KAAK,QAAQ,CAAC,KAAK,SAAU3I,EAAM,GAAOL,EAAS+G,CAAE,CAAC,EAEtD,KAAK,UAAUwB,EAAO,MAAMlI,EAAML,CAAO,EAAG+G,CAAE,CAEjD,CASD,KAAK1G,EAAMsB,EAAOoF,EAAI,CACpB,IAAIgC,EACAC,EASJ,GARI,OAAO3I,GAAS,UAClB0I,EAAa,OAAO,WAAW1I,CAAI,EACnC2I,EAAW,KAEX3I,EAAO8H,EAAW9H,CAAI,EACtB0I,EAAa1I,EAAK,OAClB2I,EAAWb,EAAW,UAEpBY,EAAa,IACf,MAAM,IAAI,WAAW,kDAAkD,EAEzE,MAAM/I,EAAU,CACd,CAACoI,CAAW,EAAGW,EACf,IAAK,GACL,aAAc,KAAK,cACnB,KAAMpH,EACN,WAAY,KAAK,YACjB,OAAQ,GACR,SAAAqH,EACA,KAAM,EACZ,EACQ,KAAK,WACP,KAAK,QAAQ,CAAC,KAAK,SAAU3I,EAAM,GAAOL,EAAS+G,CAAE,CAAC,EAEtD,KAAK,UAAUwB,EAAO,MAAMlI,EAAML,CAAO,EAAG+G,CAAE,CAEjD,CAiBD,KAAK1G,EAAML,EAAS+G,EAAI,CACtB,MAAMkC,EAAoB,KAAK,YAAYlB,GAAoB,aAAa,EAC5E,IAAImB,EAASlJ,EAAQ,OAAS,EAAI,EAC9BmJ,EAAOnJ,EAAQ,SACf+I,EACAC,EAqBJ,GApBI,OAAO3I,GAAS,UAClB0I,EAAa,OAAO,WAAW1I,CAAI,EACnC2I,EAAW,KAEX3I,EAAO8H,EAAW9H,CAAI,EACtB0I,EAAa1I,EAAK,OAClB2I,EAAWb,EAAW,UAEpB,KAAK,gBACP,KAAK,eAAiB,GAClBgB,GAAQF,GAAqBA,EAAkB,OAAOA,EAAkB,UAAY,6BAA+B,4BAA4B,IACjJE,EAAOJ,GAAcE,EAAkB,YAEzC,KAAK,UAAYE,IAEjBA,EAAO,GACPD,EAAS,GAEPlJ,EAAQ,MACV,KAAK,eAAiB,IACpBiJ,EAAmB,CACrB,MAAMnF,EAAO,CACX,CAACsE,CAAW,EAAGW,EACf,IAAK/I,EAAQ,IACb,aAAc,KAAK,cACnB,KAAMA,EAAQ,KACd,WAAY,KAAK,YACjB,OAAAkJ,EACA,SAAAF,EACA,KAAAG,CACR,EACU,KAAK,WACP,KAAK,QAAQ,CAAC,KAAK,SAAU9I,EAAM,KAAK,UAAWyD,EAAMiD,CAAE,CAAC,EAE5D,KAAK,SAAS1G,EAAM,KAAK,UAAWyD,EAAMiD,CAAE,OAG9C,KAAK,UACHwB,EAAO,MAAMlI,EAAM,CACjB,CAAC+H,CAAW,EAAGW,EACf,IAAK/I,EAAQ,IACb,aAAc,KAAK,cACnB,KAAMA,EAAQ,KACd,WAAY,KAAK,YACjB,OAAAkJ,EACA,SAAAF,EACA,KAAM,EAChB,CAAS,EACDjC,CACR,CAEG,CAwBD,SAAS1G,EAAM+I,EAAUpJ,EAAS+G,EAAI,CACpC,GAAI,CAACqC,EAAU,CACb,KAAK,UAAUb,EAAO,MAAMlI,EAAML,CAAO,EAAG+G,CAAE,EAC9C,OAEF,MAAMkC,EAAoB,KAAK,YAAYlB,GAAoB,aAAa,EAC5E,KAAK,gBAAkB/H,EAAQoI,CAAW,EAC1C,KAAK,WAAa,GAClBa,EAAkB,SAAS5I,EAAML,EAAQ,IAAK,CAACqJ,EAAG7H,IAAQ,CACxD,GAAI,KAAK,QAAQ,UAAW,CAC1B,MAAM3B,EAAM,IAAI,MACd,uDACV,EACY,OAAOkH,GAAO,YAChBA,EAAGlH,CAAG,EACR,QAASiC,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IAAK,CAC3C,MAAM6B,EAAS,KAAK,OAAO7B,CAAC,EACtBxB,EAAWqD,EAAOA,EAAO,OAAS,CAAC,EACrC,OAAOrD,GAAa,YACtBA,EAAST,CAAG,EAEhB,OAEF,KAAK,gBAAkBG,EAAQoI,CAAW,EAC1C,KAAK,WAAa,GAClBpI,EAAQ,SAAW,GACnB,KAAK,UAAUuI,EAAO,MAAM/G,EAAKxB,CAAO,EAAG+G,CAAE,EAC7C,KAAK,QAAO,CAClB,CAAK,CACF,CAMD,SAAU,CACR,KAAO,CAAC,KAAK,YAAc,KAAK,OAAO,QAAQ,CAC7C,MAAMpD,EAAS,KAAK,OAAO,MAAK,EAChC,KAAK,gBAAkBA,EAAO,CAAC,EAAEyE,CAAW,EAC5C,QAAQ,MAAMzE,EAAO,CAAC,EAAG,KAAMA,EAAO,MAAM,CAAC,CAAC,EAEjD,CAOD,QAAQA,EAAQ,CACd,KAAK,gBAAkBA,EAAO,CAAC,EAAEyE,CAAW,EAC5C,KAAK,OAAO,KAAKzE,CAAM,CACxB,CAQD,UAAUvC,EAAM2F,EAAI,CACd3F,EAAK,SAAW,GAClB,KAAK,QAAQ,OACb,KAAK,QAAQ,MAAMA,EAAK,CAAC,CAAC,EAC1B,KAAK,QAAQ,MAAMA,EAAK,CAAC,EAAG2F,CAAE,EAC9B,KAAK,QAAQ,UAEb,KAAK,QAAQ,MAAM3F,EAAK,CAAC,EAAG2F,CAAE,CAEjC,CACH,EACA,IAAIuC,GAAShB,GACR,MAACiB,GAA2B3K,EAAwB0K,EAAM,EACzD,CAAE,qBAAsBE,EAAwB,UAAWC,EAAW,EAAK3I,EAC3E4I,GAAQ,OAAO,OAAO,EACtBC,GAAQ,OAAO,OAAO,EACtBC,GAAS,OAAO,QAAQ,EACxBC,GAAW,OAAO,UAAU,EAC5BC,GAAU,OAAO,SAAS,EAC1BC,EAAU,OAAO,SAAS,EAC1BC,GAAQ,OAAO,OAAO,EACtBC,GAAY,OAAO,WAAW,EACpC,MAAMC,CAAM,CAOV,YAAYC,EAAM,CAChB,KAAKJ,CAAO,EAAI,KAChB,KAAKC,EAAK,EAAIG,CACf,CAID,IAAI,QAAS,CACX,OAAO,KAAKJ,CAAO,CACpB,CAID,IAAI,MAAO,CACT,OAAO,KAAKC,EAAK,CAClB,CACH,CACA,OAAO,eAAeE,EAAM,UAAW,SAAU,CAAE,WAAY,EAAI,CAAE,EACrE,OAAO,eAAeA,EAAM,UAAW,OAAQ,CAAE,WAAY,EAAI,CAAE,EACnE,MAAME,UAAmBF,CAAM,CAc7B,YAAYC,EAAMnK,EAAU,GAAI,CAC9B,MAAMmK,CAAI,EACV,KAAKT,EAAK,EAAI1J,EAAQ,OAAS,OAAS,EAAIA,EAAQ,KACpD,KAAK8J,EAAO,EAAI9J,EAAQ,SAAW,OAAS,GAAKA,EAAQ,OACzD,KAAKiK,EAAS,EAAIjK,EAAQ,WAAa,OAAS,GAAQA,EAAQ,QACjE,CAID,IAAI,MAAO,CACT,OAAO,KAAK0J,EAAK,CAClB,CAID,IAAI,QAAS,CACX,OAAO,KAAKI,EAAO,CACpB,CAID,IAAI,UAAW,CACb,OAAO,KAAKG,EAAS,CACtB,CACH,CACA,OAAO,eAAeG,EAAW,UAAW,OAAQ,CAAE,WAAY,EAAI,CAAE,EACxE,OAAO,eAAeA,EAAW,UAAW,SAAU,CAAE,WAAY,EAAI,CAAE,EAC1E,OAAO,eAAeA,EAAW,UAAW,WAAY,CAAE,WAAY,EAAI,CAAE,EAC5E,MAAMC,WAAmBH,CAAM,CAU7B,YAAYC,EAAMnK,EAAU,GAAI,CAC9B,MAAMmK,CAAI,EACV,KAAKP,EAAM,EAAI5J,EAAQ,QAAU,OAAS,KAAOA,EAAQ,MACzD,KAAK6J,EAAQ,EAAI7J,EAAQ,UAAY,OAAS,GAAKA,EAAQ,OAC5D,CAID,IAAI,OAAQ,CACV,OAAO,KAAK4J,EAAM,CACnB,CAID,IAAI,SAAU,CACZ,OAAO,KAAKC,EAAQ,CACrB,CACH,CACA,OAAO,eAAeQ,GAAW,UAAW,QAAS,CAAE,WAAY,EAAI,CAAE,EACzE,OAAO,eAAeA,GAAW,UAAW,UAAW,CAAE,WAAY,EAAI,CAAE,EAC3E,MAAMC,WAAqBJ,CAAM,CAS/B,YAAYC,EAAMnK,EAAU,GAAI,CAC9B,MAAMmK,CAAI,EACV,KAAKR,EAAK,EAAI3J,EAAQ,OAAS,OAAS,KAAOA,EAAQ,IACxD,CAID,IAAI,MAAO,CACT,OAAO,KAAK2J,EAAK,CAClB,CACH,CACA,OAAO,eAAeW,GAAa,UAAW,OAAQ,CAAE,WAAY,EAAI,CAAE,EAC1E,MAAMC,GAAc,CAalB,iBAAiBJ,EAAMK,EAASxK,EAAU,CAAA,EAAI,CAC5C,UAAWyK,KAAY,KAAK,UAAUN,CAAI,EACxC,GAAI,CAACnK,EAAQwJ,CAAsB,GAAKiB,EAAShB,EAAW,IAAMe,GAAW,CAACC,EAASjB,CAAsB,EAC3G,OAGJ,IAAIkB,EACJ,GAAIP,IAAS,UACXO,EAAU,SAAmBrK,EAAMD,EAAU,CAC3C,MAAMuK,EAAQ,IAAIL,GAAa,UAAW,CACxC,KAAMlK,EAAWC,EAAOA,EAAK,SAAU,CACjD,CAAS,EACDsK,EAAMZ,CAAO,EAAI,KACjBa,EAAaJ,EAAS,KAAMG,CAAK,CACzC,UACeR,IAAS,QAClBO,EAAU,SAAiBnF,EAAMiC,EAAS,CACxC,MAAMmD,EAAQ,IAAIP,EAAW,QAAS,CACpC,KAAA7E,EACA,OAAQiC,EAAQ,SAAU,EAC1B,SAAU,KAAK,qBAAuB,KAAK,eACrD,CAAS,EACDmD,EAAMZ,CAAO,EAAI,KACjBa,EAAaJ,EAAS,KAAMG,CAAK,CACzC,UACeR,IAAS,QAClBO,EAAU,SAAiBG,EAAQ,CACjC,MAAMF,EAAQ,IAAIN,GAAW,QAAS,CACpC,MAAOQ,EACP,QAASA,EAAO,OAC1B,CAAS,EACDF,EAAMZ,CAAO,EAAI,KACjBa,EAAaJ,EAAS,KAAMG,CAAK,CACzC,UACeR,IAAS,OAClBO,EAAU,UAAkB,CAC1B,MAAMC,EAAQ,IAAIT,EAAM,MAAM,EAC9BS,EAAMZ,CAAO,EAAI,KACjBa,EAAaJ,EAAS,KAAMG,CAAK,CACzC,MAEM,QAEFD,EAAQlB,CAAsB,EAAI,CAAC,CAACxJ,EAAQwJ,CAAsB,EAClEkB,EAAQjB,EAAW,EAAIe,EACnBxK,EAAQ,KACV,KAAK,KAAKmK,EAAMO,CAAO,EAEvB,KAAK,GAAGP,EAAMO,CAAO,CAExB,EAQD,oBAAoBP,EAAMK,EAAS,CACjC,UAAWC,KAAY,KAAK,UAAUN,CAAI,EACxC,GAAIM,EAAShB,EAAW,IAAMe,GAAW,CAACC,EAASjB,CAAsB,EAAG,CAC1E,KAAK,eAAeW,EAAMM,CAAQ,EAClC,MAGL,CACH,EACA,IAAIK,GAAc,CAChB,WAAAV,EACA,WAAAC,GACA,MAAAH,EACA,YAAAK,GACA,aAAAD,EACF,EACA,SAASM,EAAaH,EAAUM,EAASJ,EAAO,CAC1C,OAAOF,GAAa,UAAYA,EAAS,YAC3CA,EAAS,YAAY,KAAKA,EAAUE,CAAK,EAEzCF,EAAS,KAAKM,EAASJ,CAAK,CAEhC,CACA,KAAM,CAAE,WAAYK,CAAc,EAAGrF,GACrC,SAASsF,EAAKC,EAAMC,EAAMC,EAAM,CAC1BF,EAAKC,CAAI,IAAM,OACjBD,EAAKC,CAAI,EAAI,CAACC,CAAI,EAElBF,EAAKC,CAAI,EAAE,KAAKC,CAAI,CACxB,CACA,SAASC,GAAQC,EAAQ,CACvB,MAAMzH,EAAyB,OAAO,OAAO,IAAI,EACjD,IAAIF,EAAyB,OAAO,OAAO,IAAI,EAC3C4H,EAAe,GACfC,EAAa,GACbC,EAAW,GACXC,EACAC,EACAC,EAAQ,GACRrG,EAAO,GACPsG,EAAM,GACN/J,EAAI,EACR,KAAOA,EAAIwJ,EAAO,OAAQxJ,IAExB,GADAyD,EAAO+F,EAAO,WAAWxJ,CAAC,EACtB4J,IAAkB,OACpB,GAAIG,IAAQ,IAAMb,EAAazF,CAAI,IAAM,EACnCqG,IAAU,KACZA,EAAQ9J,WACDA,IAAM,IAAMyD,IAAS,IAAMA,IAAS,GACzCsG,IAAQ,IAAMD,IAAU,KAC1BC,EAAM/J,WACCyD,IAAS,IAAMA,IAAS,GAAI,CACrC,GAAIqG,IAAU,GACZ,MAAM,IAAI,YAAY,iCAAiC9J,GAAG,EAExD+J,IAAQ,KACVA,EAAM/J,GACR,MAAMqJ,EAAOG,EAAO,MAAMM,EAAOC,CAAG,EAChCtG,IAAS,IACX0F,EAAKpH,EAAQsH,EAAMxH,CAAM,EACzBA,EAAyB,OAAO,OAAO,IAAI,GAE3C+H,EAAgBP,EAElBS,EAAQC,EAAM,OAEd,OAAM,IAAI,YAAY,iCAAiC/J,GAAG,UAEnD6J,IAAc,OACvB,GAAIE,IAAQ,IAAMb,EAAazF,CAAI,IAAM,EACnCqG,IAAU,KACZA,EAAQ9J,WACDyD,IAAS,IAAMA,IAAS,EAC7BsG,IAAQ,IAAMD,IAAU,KAC1BC,EAAM/J,WACCyD,IAAS,IAAMA,IAAS,GAAI,CACrC,GAAIqG,IAAU,GACZ,MAAM,IAAI,YAAY,iCAAiC9J,GAAG,EAExD+J,IAAQ,KACVA,EAAM/J,GACRmJ,EAAKtH,EAAQ2H,EAAO,MAAMM,EAAOC,CAAG,EAAG,EAAI,EACvCtG,IAAS,KACX0F,EAAKpH,EAAQ6H,EAAe/H,CAAM,EAClCA,EAAyB,OAAO,OAAO,IAAI,EAC3C+H,EAAgB,QAElBE,EAAQC,EAAM,WACLtG,IAAS,IAAMqG,IAAU,IAAMC,IAAQ,GAChDF,EAAYL,EAAO,MAAMM,EAAO9J,CAAC,EACjC8J,EAAQC,EAAM,OAEd,OAAM,IAAI,YAAY,iCAAiC/J,GAAG,UAGxD0J,EAAY,CACd,GAAIR,EAAazF,CAAI,IAAM,EACzB,MAAM,IAAI,YAAY,iCAAiCzD,GAAG,EAExD8J,IAAU,GACZA,EAAQ9J,EACAyJ,IACRA,EAAe,IACjBC,EAAa,WACJC,EACT,GAAIT,EAAazF,CAAI,IAAM,EACrBqG,IAAU,KACZA,EAAQ9J,WACDyD,IAAS,IAAMqG,IAAU,GAClCH,EAAW,GACXI,EAAM/J,UACGyD,IAAS,GAClBiG,EAAa,OAEb,OAAM,IAAI,YAAY,iCAAiC1J,GAAG,UAEnDyD,IAAS,IAAM+F,EAAO,WAAWxJ,EAAI,CAAC,IAAM,GACrD2J,EAAW,WACFI,IAAQ,IAAMb,EAAazF,CAAI,IAAM,EAC1CqG,IAAU,KACZA,EAAQ9J,WACD8J,IAAU,KAAOrG,IAAS,IAAMA,IAAS,GAC9CsG,IAAQ,KACVA,EAAM/J,WACCyD,IAAS,IAAMA,IAAS,GAAI,CACrC,GAAIqG,IAAU,GACZ,MAAM,IAAI,YAAY,iCAAiC9J,GAAG,EAExD+J,IAAQ,KACVA,EAAM/J,GACR,IAAIoC,EAAQoH,EAAO,MAAMM,EAAOC,CAAG,EAC/BN,IACFrH,EAAQA,EAAM,QAAQ,MAAO,EAAE,EAC/BqH,EAAe,IAEjBN,EAAKtH,EAAQgI,EAAWzH,CAAK,EACzBqB,IAAS,KACX0F,EAAKpH,EAAQ6H,EAAe/H,CAAM,EAClCA,EAAyB,OAAO,OAAO,IAAI,EAC3C+H,EAAgB,QAElBC,EAAY,OACZC,EAAQC,EAAM,OAEd,OAAM,IAAI,YAAY,iCAAiC/J,GAAG,EAIhE,GAAI8J,IAAU,IAAMH,GAAYlG,IAAS,IAAMA,IAAS,EACtD,MAAM,IAAI,YAAY,yBAAyB,EAE7CsG,IAAQ,KACVA,EAAM/J,GACR,MAAMgK,EAAQR,EAAO,MAAMM,EAAOC,CAAG,EACrC,OAAIH,IAAkB,OACpBT,EAAKpH,EAAQiI,EAAOnI,CAAM,GAEtBgI,IAAc,OAChBV,EAAKtH,EAAQmI,EAAO,EAAI,EACfP,EACTN,EAAKtH,EAAQgI,EAAWG,EAAM,QAAQ,MAAO,EAAE,CAAC,EAEhDb,EAAKtH,EAAQgI,EAAWG,CAAK,EAE/Bb,EAAKpH,EAAQ6H,EAAe/H,CAAM,GAE7BE,CACT,CACA,SAASkI,GAAStD,EAAY,CAC5B,OAAO,OAAO,KAAKA,CAAU,EAAE,IAAKuD,GAAe,CACjD,IAAIpI,EAAiB6E,EAAWuD,CAAU,EAC1C,OAAK,MAAM,QAAQpI,CAAc,IAC/BA,EAAiB,CAACA,CAAc,GAC3BA,EAAe,IAAKD,GAClB,CAACqI,CAAU,EAAE,OAClB,OAAO,KAAKrI,CAAM,EAAE,IAAKtE,GAAM,CAC7B,IAAI4M,EAAStI,EAAOtE,CAAC,EACrB,OAAK,MAAM,QAAQ4M,CAAM,IACvBA,EAAS,CAACA,CAAM,GACXA,EAAO,IAAKC,GAAMA,IAAM,GAAO7M,EAAI,GAAGA,KAAK6M,GAAG,EAAE,KAAK,IAAI,CAC1E,CAAS,CACT,EAAQ,KAAK,IAAI,CACZ,EAAE,KAAK,IAAI,CAChB,CAAG,EAAE,KAAK,IAAI,CACd,CACA,IAAIC,GAAc,CAAE,OAAQJ,GAAU,MAAOV,EAAO,EACpD,MAAMe,GAAiBC,EACjBC,GAAQC,EACRC,GAASC,EACTC,GAAMC,EACNC,GAAMC,EACN,CAAE,YAAAC,GAAa,WAAYC,EAAY,EAAKjF,EAC5C,CAAE,IAAAkF,EAAK,EAAGC,EACVC,EAAsBrI,GACtBsI,GAAY7F,GACZ8F,GAAU9D,GACV,CACJ,aAAA+D,GACA,aAAAC,EACA,KAAMC,GACN,qBAAAC,GACA,UAAAC,GACA,YAAAC,GACA,WAAYC,EACZ,KAAAC,EACF,EAAI9M,EACE,CACJ,YAAa,CAAE,iBAAA+M,GAAkB,oBAAAC,EAAqB,CACxD,EAAIhD,GACE,CAAE,OAAAiD,GAAQ,MAAOC,EAAO,EAAK7B,GAC7B,CAAE,SAAA8B,EAAU,EAAG7L,GACf8L,GAAe,GAAK,IACpBC,GAAW,OAAO,UAAU,EAC5BC,GAAmB,CAAC,EAAG,EAAE,EACzBC,EAAc,CAAC,aAAc,OAAQ,UAAW,QAAQ,EACxDC,GAAmB,iCACzB,IAAIC,EAAc,MAAMC,UAAkBpC,EAAe,CAQvD,YAAYqC,EAASC,EAAW1O,EAAS,CACvC,QACA,KAAK,YAAcqN,GAAa,CAAC,EACjC,KAAK,WAAa,KAClB,KAAK,oBAAsB,GAC3B,KAAK,gBAAkB,GACvB,KAAK,cAAgBC,EACrB,KAAK,YAAc,KACnB,KAAK,YAAc,GACnB,KAAK,QAAU,GACf,KAAK,UAAY,GACjB,KAAK,YAAckB,EAAU,WAC7B,KAAK,UAAY,KACjB,KAAK,QAAU,KACf,KAAK,QAAU,KACXC,IAAY,MACd,KAAK,gBAAkB,EACvB,KAAK,UAAY,GACjB,KAAK,WAAa,EACdC,IAAc,OAChBA,EAAY,CAAA,EACF,MAAM,QAAQA,CAAS,IAC7B,OAAOA,GAAc,UAAYA,IAAc,MACjD1O,EAAU0O,EACVA,EAAY,CAAA,GAEZA,EAAY,CAACA,CAAS,GAG1BC,GAAa,KAAMF,EAASC,EAAW1O,CAAO,GAE9C,KAAK,UAAY,EAEpB,CAQD,IAAI,YAAa,CACf,OAAO,KAAK,WACb,CACD,IAAI,WAAWmK,EAAM,CACdkD,GAAa,SAASlD,CAAI,IAE/B,KAAK,YAAcA,EACf,KAAK,YACP,KAAK,UAAU,YAAcA,GAChC,CAID,IAAI,gBAAiB,CACnB,OAAK,KAAK,QAEH,KAAK,QAAQ,eAAe,OAAS,KAAK,QAAQ,eADhD,KAAK,eAEf,CAID,IAAI,YAAa,CACf,OAAO,OAAO,KAAK,KAAK,WAAW,EAAE,KAAI,CAC1C,CAID,IAAI,UAAW,CACb,OAAO,KAAK,OACb,CAKD,IAAI,SAAU,CACZ,OAAO,IACR,CAKD,IAAI,SAAU,CACZ,OAAO,IACR,CAKD,IAAI,QAAS,CACX,OAAO,IACR,CAKD,IAAI,WAAY,CACd,OAAO,IACR,CAID,IAAI,UAAW,CACb,OAAO,KAAK,SACb,CAID,IAAI,YAAa,CACf,OAAO,KAAK,WACb,CAID,IAAI,KAAM,CACR,OAAO,KAAK,IACb,CAeD,UAAU3B,EAAQoG,EAAM5O,EAAS,CAC/B,MAAM6O,EAAY,IAAI1B,GAAU,CAC9B,WAAY,KAAK,WACjB,WAAY,KAAK,YACjB,SAAU,KAAK,UACf,WAAYnN,EAAQ,WACpB,mBAAoBA,EAAQ,kBAClC,CAAK,EACD,KAAK,QAAU,IAAIoN,GAAQ5E,EAAQ,KAAK,YAAaxI,EAAQ,YAAY,EACzE,KAAK,UAAY6O,EACjB,KAAK,QAAUrG,EACfqG,EAAUlB,CAAY,EAAI,KAC1BnF,EAAOmF,CAAY,EAAI,KACvBkB,EAAU,GAAG,WAAYC,EAAkB,EAC3CD,EAAU,GAAG,QAASE,EAAe,EACrCF,EAAU,GAAG,QAASG,EAAe,EACrCH,EAAU,GAAG,UAAWI,EAAiB,EACzCJ,EAAU,GAAG,OAAQK,EAAc,EACnCL,EAAU,GAAG,OAAQM,EAAc,EACnC3G,EAAO,WAAW,CAAC,EACnBA,EAAO,WAAU,EACboG,EAAK,OAAS,GAChBpG,EAAO,QAAQoG,CAAI,EACrBpG,EAAO,GAAG,QAAS4G,EAAa,EAChC5G,EAAO,GAAG,OAAQ6G,EAAY,EAC9B7G,EAAO,GAAG,MAAO8G,EAAW,EAC5B9G,EAAO,GAAG,QAAS+G,EAAe,EAClC,KAAK,YAAcf,EAAU,KAC7B,KAAK,KAAK,MAAM,CACjB,CAMD,WAAY,CACV,GAAI,CAAC,KAAK,QAAS,CACjB,KAAK,YAAcA,EAAU,OAC7B,KAAK,KAAK,QAAS,KAAK,WAAY,KAAK,aAAa,EACtD,OAEE,KAAK,YAAYtB,EAAoB,aAAa,GACpD,KAAK,YAAYA,EAAoB,aAAa,EAAE,QAAO,EAE7D,KAAK,UAAU,qBACf,KAAK,YAAcsB,EAAU,OAC7B,KAAK,KAAK,QAAS,KAAK,WAAY,KAAK,aAAa,CACvD,CAqBD,MAAMjJ,EAAMlF,EAAM,CAChB,GAAI,KAAK,aAAemO,EAAU,OAElC,IAAI,KAAK,aAAeA,EAAU,WAAY,CAC5C,MAAMrO,EAAM,6DACZqP,EAAiB,KAAM,KAAK,KAAMrP,CAAG,EACrC,OAEF,GAAI,KAAK,aAAeqO,EAAU,QAAS,CACrC,KAAK,kBAAoB,KAAK,qBAAuB,KAAK,UAAU,eAAe,eACrF,KAAK,QAAQ,MAEf,OAEF,KAAK,YAAcA,EAAU,QAC7B,KAAK,QAAQ,MAAMjJ,EAAMlF,EAAM,CAAC,KAAK,UAAYR,GAAQ,CACnDA,IAEJ,KAAK,gBAAkB,IACnB,KAAK,qBAAuB,KAAK,UAAU,eAAe,eAC5D,KAAK,QAAQ,MAErB,CAAK,EACD,KAAK,YAAc,WACjB,KAAK,QAAQ,QAAQ,KAAK,KAAK,OAAO,EACtCqO,EACN,EACG,CAMD,OAAQ,CACF,KAAK,aAAeM,EAAU,YAAc,KAAK,aAAeA,EAAU,SAG9E,KAAK,QAAU,GACf,KAAK,QAAQ,QACd,CASD,KAAKnO,EAAMsB,EAAOoF,EAAI,CACpB,GAAI,KAAK,aAAeyH,EAAU,WAChC,MAAM,IAAI,MAAM,kDAAkD,EAWpE,GATI,OAAOnO,GAAS,YAClB0G,EAAK1G,EACLA,EAAOsB,EAAQ,QACN,OAAOA,GAAU,aAC1BoF,EAAKpF,EACLA,EAAQ,QAEN,OAAOtB,GAAS,WAClBA,EAAOA,EAAK,YACV,KAAK,aAAemO,EAAU,KAAM,CACtCiB,GAAe,KAAMpP,EAAM0G,CAAE,EAC7B,OAEEpF,IAAU,SACZA,EAAQ,CAAC,KAAK,WAChB,KAAK,QAAQ,KAAKtB,GAAQiN,EAAc3L,EAAOoF,CAAE,CAClD,CASD,KAAK1G,EAAMsB,EAAOoF,EAAI,CACpB,GAAI,KAAK,aAAeyH,EAAU,WAChC,MAAM,IAAI,MAAM,kDAAkD,EAWpE,GATI,OAAOnO,GAAS,YAClB0G,EAAK1G,EACLA,EAAOsB,EAAQ,QACN,OAAOA,GAAU,aAC1BoF,EAAKpF,EACLA,EAAQ,QAEN,OAAOtB,GAAS,WAClBA,EAAOA,EAAK,YACV,KAAK,aAAemO,EAAU,KAAM,CACtCiB,GAAe,KAAMpP,EAAM0G,CAAE,EAC7B,OAEEpF,IAAU,SACZA,EAAQ,CAAC,KAAK,WAChB,KAAK,QAAQ,KAAKtB,GAAQiN,EAAc3L,EAAOoF,CAAE,CAClD,CAMD,QAAS,CACH,KAAK,aAAeyH,EAAU,YAAc,KAAK,aAAeA,EAAU,SAG9E,KAAK,QAAU,GACV,KAAK,UAAU,eAAe,WACjC,KAAK,QAAQ,SAChB,CAgBD,KAAKnO,EAAML,EAAS+G,EAAI,CACtB,GAAI,KAAK,aAAeyH,EAAU,WAChC,MAAM,IAAI,MAAM,kDAAkD,EAQpE,GANI,OAAOxO,GAAY,aACrB+G,EAAK/G,EACLA,EAAU,CAAA,GAER,OAAOK,GAAS,WAClBA,EAAOA,EAAK,YACV,KAAK,aAAemO,EAAU,KAAM,CACtCiB,GAAe,KAAMpP,EAAM0G,CAAE,EAC7B,OAEF,MAAMjD,EAAO,CACX,OAAQ,OAAOzD,GAAS,SACxB,KAAM,CAAC,KAAK,UACZ,SAAU,GACV,IAAK,GACL,GAAGL,CACT,EACS,KAAK,YAAYkN,EAAoB,aAAa,IACrDpJ,EAAK,SAAW,IAElB,KAAK,QAAQ,KAAKzD,GAAQiN,EAAcxJ,EAAMiD,CAAE,CACjD,CAMD,WAAY,CACV,GAAI,KAAK,aAAeyH,EAAU,OAElC,IAAI,KAAK,aAAeA,EAAU,WAAY,CAC5C,MAAMrO,EAAM,6DACZqP,EAAiB,KAAM,KAAK,KAAMrP,CAAG,EACrC,OAEE,KAAK,UACP,KAAK,YAAcqO,EAAU,QAC7B,KAAK,QAAQ,WAEhB,CACH,EACA,OAAO,eAAeD,EAAa,aAAc,CAC/C,WAAY,GACZ,MAAOF,EAAY,QAAQ,YAAY,CACzC,CAAC,EACD,OAAO,eAAeE,EAAY,UAAW,aAAc,CACzD,WAAY,GACZ,MAAOF,EAAY,QAAQ,YAAY,CACzC,CAAC,EACD,OAAO,eAAeE,EAAa,OAAQ,CACzC,WAAY,GACZ,MAAOF,EAAY,QAAQ,MAAM,CACnC,CAAC,EACD,OAAO,eAAeE,EAAY,UAAW,OAAQ,CACnD,WAAY,GACZ,MAAOF,EAAY,QAAQ,MAAM,CACnC,CAAC,EACD,OAAO,eAAeE,EAAa,UAAW,CAC5C,WAAY,GACZ,MAAOF,EAAY,QAAQ,SAAS,CACtC,CAAC,EACD,OAAO,eAAeE,EAAY,UAAW,UAAW,CACtD,WAAY,GACZ,MAAOF,EAAY,QAAQ,SAAS,CACtC,CAAC,EACD,OAAO,eAAeE,EAAa,SAAU,CAC3C,WAAY,GACZ,MAAOF,EAAY,QAAQ,QAAQ,CACrC,CAAC,EACD,OAAO,eAAeE,EAAY,UAAW,SAAU,CACrD,WAAY,GACZ,MAAOF,EAAY,QAAQ,QAAQ,CACrC,CAAC,EACD,CACE,aACA,iBACA,aACA,WACA,WACA,aACA,KACF,EAAE,QAASqB,GAAa,CACtB,OAAO,eAAenB,EAAY,UAAWmB,EAAU,CAAE,WAAY,EAAI,CAAE,CAC7E,CAAC,EACD,CAAC,OAAQ,QAAS,QAAS,SAAS,EAAE,QAASC,GAAW,CACxD,OAAO,eAAepB,EAAY,UAAW,KAAKoB,IAAU,CAC1D,WAAY,GACZ,KAAM,CACJ,UAAWlF,KAAY,KAAK,UAAUkF,CAAM,EAC1C,GAAIlF,EAAS+C,EAAoB,EAC/B,OAAO/C,EAASgD,EAAS,EAE7B,OAAO,IACR,EACD,IAAIjD,EAAS,CACX,UAAWC,KAAY,KAAK,UAAUkF,CAAM,EAC1C,GAAIlF,EAAS+C,EAAoB,EAAG,CAClC,KAAK,eAAemC,EAAQlF,CAAQ,EACpC,MAGA,OAAOD,GAAY,YAEvB,KAAK,iBAAiBmF,EAAQnF,EAAS,CACrC,CAACgD,EAAoB,EAAG,EAChC,CAAO,CACF,CACL,CAAG,CACH,CAAC,EACDe,EAAY,UAAU,iBAAmBV,GACzCU,EAAY,UAAU,oBAAsBT,GAC5C,IAAI8B,GAAYrB,EAChB,SAASI,GAAakB,EAAYpB,EAASC,EAAW1O,EAAS,CAC7D,MAAM8D,EAAO,CACX,gBAAiBsK,GAAiB,CAAC,EACnC,WAAY,UACZ,mBAAoB,GACpB,kBAAmB,GACnB,gBAAiB,GACjB,aAAc,GACd,GAAGpO,EACH,iBAAkB,OAClB,WAAY,OACZ,SAAU,OACV,SAAU,OACV,QAAS,OACT,OAAQ,MACR,KAAM,OACN,KAAM,OACN,KAAM,MACV,EACE,GAAI,CAACoO,GAAiB,SAAStK,EAAK,eAAe,EACjD,MAAM,IAAI,WACR,iCAAiCA,EAAK,wCAAwCsK,GAAiB,KAAK,IAAI,IAC9G,EAEE,IAAI0B,EACJ,GAAIrB,aAAmBzB,GACrB8C,EAAYrB,EACZoB,EAAW,KAAOpB,EAAQ,SACrB,CACL,GAAI,CACFqB,EAAY,IAAI9C,GAAIyB,CAAO,CAC5B,MAAC,CACA,MAAM,IAAI,YAAY,gBAAgBA,GAAS,CAChD,CACDoB,EAAW,KAAOpB,EAEpB,MAAMsB,EAAWD,EAAU,WAAa,OAClCE,EAAWF,EAAU,WAAa,WACxC,IAAIG,EAQJ,GAPIH,EAAU,WAAa,OAAS,CAACC,GAAY,CAACC,EAChDC,EAAoB,iEACXD,GAAY,CAACF,EAAU,SAChCG,EAAoB,8BACXH,EAAU,OACnBG,EAAoB,0CAElBA,EAAmB,CACrB,MAAMpQ,EAAM,IAAI,YAAYoQ,CAAiB,EAC7C,GAAIJ,EAAW,aAAe,EAC5B,MAAMhQ,EAENqQ,GAAkBL,EAAYhQ,CAAG,EACjC,OAGJ,MAAMsQ,EAAcJ,EAAW,IAAM,GAC/B9L,EAAM6I,GAAY,EAAE,EAAE,SAAS,QAAQ,EACvCsD,EAAUL,EAAWzD,GAAM,QAAUE,GAAO,QAC5C6D,EAA8B,IAAI,IACxC,IAAIpH,EAwBJ,GAvBAnF,EAAK,iBAAmBiM,EAAWO,GAAaC,GAChDzM,EAAK,YAAcA,EAAK,aAAeqM,EACvCrM,EAAK,KAAOgM,EAAU,MAAQK,EAC9BrM,EAAK,KAAOgM,EAAU,SAAS,WAAW,GAAG,EAAIA,EAAU,SAAS,MAAM,EAAG,EAAE,EAAIA,EAAU,SAC7FhM,EAAK,QAAU,CACb,GAAGA,EAAK,QACR,wBAAyBA,EAAK,gBAC9B,oBAAqBG,EACrB,WAAY,UACZ,QAAS,WACb,EACEH,EAAK,KAAOgM,EAAU,SAAWA,EAAU,OAC3ChM,EAAK,QAAUA,EAAK,iBAChBA,EAAK,oBACPmF,EAAoB,IAAIiE,EACtBpJ,EAAK,oBAAsB,GAAOA,EAAK,kBAAoB,CAAE,EAC7D,GACAA,EAAK,UACX,EACIA,EAAK,QAAQ,0BAA0B,EAAIiK,GAAO,CAChD,CAACb,EAAoB,aAAa,EAAGjE,EAAkB,MAAO,CACpE,CAAK,GAECyF,EAAU,OAAQ,CACpB,UAAW8B,KAAY9B,EAAW,CAChC,GAAI,OAAO8B,GAAa,UAAY,CAAClC,GAAiB,KAAKkC,CAAQ,GAAKH,EAAY,IAAIG,CAAQ,EAC9F,MAAM,IAAI,YACR,oDACV,EAEMH,EAAY,IAAIG,CAAQ,EAE1B1M,EAAK,QAAQ,wBAAwB,EAAI4K,EAAU,KAAK,GAAG,EAY7D,GAVI5K,EAAK,SACHA,EAAK,gBAAkB,GACzBA,EAAK,QAAQ,sBAAsB,EAAIA,EAAK,OAE5CA,EAAK,QAAQ,OAASA,EAAK,SAG3BgM,EAAU,UAAYA,EAAU,YAClChM,EAAK,KAAO,GAAGgM,EAAU,YAAYA,EAAU,YAE7CE,EAAU,CACZ,MAAMS,EAAQ3M,EAAK,KAAK,MAAM,GAAG,EACjCA,EAAK,WAAa2M,EAAM,CAAC,EACzB3M,EAAK,KAAO2M,EAAM,CAAC,EAErB,IAAIC,EACJ,GAAI5M,EAAK,gBAAiB,CACxB,GAAI+L,EAAW,aAAe,EAAG,CAC/BA,EAAW,aAAeG,EAC1BH,EAAW,gBAAkBE,EAC7BF,EAAW,0BAA4BG,EAAWlM,EAAK,WAAagM,EAAU,KAC9E,MAAMa,EAAU3Q,GAAWA,EAAQ,QAEnC,GADAA,EAAU,CAAE,GAAGA,EAAS,QAAS,CAAE,CAAA,EAC/B2Q,EACF,SAAW,CAACC,EAAM1M,CAAK,IAAK,OAAO,QAAQyM,CAAO,EAChD3Q,EAAQ,QAAQ4Q,EAAK,YAAa,CAAA,EAAI1M,UAGjC2L,EAAW,cAAc,UAAU,IAAM,EAAG,CACrD,MAAMgB,EAAab,EAAWH,EAAW,aAAe/L,EAAK,aAAe+L,EAAW,0BAA4B,GAAQA,EAAW,aAAe,GAAQC,EAAU,OAASD,EAAW,2BACvL,CAACgB,GAAchB,EAAW,iBAAmB,CAACE,KAChD,OAAOjM,EAAK,QAAQ,cACpB,OAAOA,EAAK,QAAQ,OACf+M,GACH,OAAO/M,EAAK,QAAQ,KACtBA,EAAK,KAAO,QAGZA,EAAK,MAAQ,CAAC9D,EAAQ,QAAQ,gBAChCA,EAAQ,QAAQ,cAAgB,SAAW,OAAO,KAAK8D,EAAK,IAAI,EAAE,SAAS,QAAQ,GAErF4M,EAAMb,EAAW,KAAOO,EAAQtM,CAAI,EAChC+L,EAAW,YACbA,EAAW,KAAK,WAAYA,EAAW,IAAKa,CAAG,OAGjDA,EAAMb,EAAW,KAAOO,EAAQtM,CAAI,EAElCA,EAAK,SACP4M,EAAI,GAAG,UAAW,IAAM,CACtBlB,EAAiBK,EAAYa,EAAK,iCAAiC,CACzE,CAAK,EAEHA,EAAI,GAAG,QAAU7Q,GAAQ,CACnB6Q,IAAQ,MAAQA,EAAIvC,EAAQ,IAEhCuC,EAAMb,EAAW,KAAO,KACxBK,GAAkBL,EAAYhQ,CAAG,EACrC,CAAG,EACD6Q,EAAI,GAAG,WAAaI,GAAQ,CAC1B,MAAMC,EAAWD,EAAI,QAAQ,SACvBpJ,EAAaoJ,EAAI,WACvB,GAAIC,GAAYjN,EAAK,iBAAmB4D,GAAc,KAAOA,EAAa,IAAK,CAC7E,GAAI,EAAEmI,EAAW,WAAa/L,EAAK,aAAc,CAC/C0L,EAAiBK,EAAYa,EAAK,4BAA4B,EAC9D,OAEFA,EAAI,MAAK,EACT,IAAIM,EACJ,GAAI,CACFA,EAAO,IAAIhE,GAAI+D,EAAUtC,CAAO,CACjC,MAAC,CACA,MAAM5O,EAAM,IAAI,YAAY,gBAAgBkR,GAAU,EACtDb,GAAkBL,EAAYhQ,CAAG,EACjC,MACD,CACD8O,GAAakB,EAAYmB,EAAMtC,EAAW1O,CAAO,OACvC6P,EAAW,KAAK,sBAAuBa,EAAKI,CAAG,GACzDtB,EACEK,EACAa,EACA,+BAA+BI,EAAI,YAC3C,CAEA,CAAG,EACDJ,EAAI,GAAG,UAAW,CAACI,EAAKtI,EAAQoG,IAAS,CAEvC,GADAiB,EAAW,KAAK,UAAWiB,CAAG,EAC1BjB,EAAW,aAAetB,EAAY,WACxC,OAEF,GADAmC,EAAMb,EAAW,KAAO,KACpBiB,EAAI,QAAQ,QAAQ,YAAW,IAAO,YAAa,CACrDtB,EAAiBK,EAAYrH,EAAQ,wBAAwB,EAC7D,OAEF,MAAMyI,EAASlE,GAAa,MAAM,EAAE,OAAO9I,EAAMsJ,EAAM,EAAE,OAAO,QAAQ,EACxE,GAAIuD,EAAI,QAAQ,sBAAsB,IAAMG,EAAQ,CAClDzB,EAAiBK,EAAYrH,EAAQ,qCAAqC,EAC1E,OAEF,MAAM0I,EAAaJ,EAAI,QAAQ,wBAAwB,EACvD,IAAIK,EAUJ,GATID,IAAe,OACZb,EAAY,KAELA,EAAY,IAAIa,CAAU,IACpCC,EAAY,sCAFZA,EAAY,mDAILd,EAAY,OACrBc,EAAY,8BAEVA,EAAW,CACb3B,EAAiBK,EAAYrH,EAAQ2I,CAAS,EAC9C,OAEED,IACFrB,EAAW,UAAYqB,GACzB,MAAME,GAAyBN,EAAI,QAAQ,0BAA0B,EACrE,GAAIM,KAA2B,OAAQ,CACrC,GAAI,CAACnI,EAAmB,CAEtBuG,EAAiBK,EAAYrH,EADb,8EAC4B,EAC5C,OAEF,IAAIC,GACJ,GAAI,CACFA,GAAauF,GAAQoD,EAAsB,CAC5C,MAAC,CAEA5B,EAAiBK,EAAYrH,EADb,yCAC4B,EAC5C,MACD,CACD,MAAM6I,GAAiB,OAAO,KAAK5I,EAAU,EAC7C,GAAI4I,GAAe,SAAW,GAAKA,GAAe,CAAC,IAAMnE,EAAoB,cAAe,CAE1FsC,EAAiBK,EAAYrH,EADb,sDAC4B,EAC5C,OAEF,GAAI,CACFS,EAAkB,OAAOR,GAAWyE,EAAoB,aAAa,CAAC,CACvE,MAAC,CAEAsC,EAAiBK,EAAYrH,EADb,yCAC4B,EAC5C,MACD,CACDqH,EAAW,YAAY3C,EAAoB,aAAa,EAAIjE,EAE9D4G,EAAW,UAAUrH,EAAQoG,EAAM,CACjC,aAAc9K,EAAK,aACnB,WAAYA,EAAK,WACjB,mBAAoBA,EAAK,kBAC/B,CAAK,CACL,CAAG,EACGA,EAAK,cACPA,EAAK,cAAc4M,EAAKb,CAAU,EAElCa,EAAI,IAAG,CAEX,CACA,SAASR,GAAkBL,EAAYhQ,EAAK,CAC1CgQ,EAAW,YAActB,EAAY,QACrCsB,EAAW,KAAK,QAAShQ,CAAG,EAC5BgQ,EAAW,UAAS,CACtB,CACA,SAASU,GAAWvQ,EAAS,CAC3B,OAAAA,EAAQ,KAAOA,EAAQ,WAChB0M,GAAI,QAAQ1M,CAAO,CAC5B,CACA,SAASsQ,GAAWtQ,EAAS,CAC3B,OAAAA,EAAQ,KAAO,OACX,CAACA,EAAQ,YAAcA,EAAQ,aAAe,KAChDA,EAAQ,WAAa0M,GAAI,KAAK1M,EAAQ,IAAI,EAAI,GAAKA,EAAQ,MAEtD4M,GAAI,QAAQ5M,CAAO,CAC5B,CACA,SAASwP,EAAiBK,EAAYnQ,EAAS8H,EAAS,CACtDqI,EAAW,YAActB,EAAY,QACrC,MAAM1O,EAAM,IAAI,MAAM2H,CAAO,EAC7B,MAAM,kBAAkB3H,EAAK2P,CAAgB,EACzC9P,EAAQ,WACVA,EAAQyO,EAAQ,EAAI,GACpBzO,EAAQ,MAAK,EACTA,EAAQ,QAAU,CAACA,EAAQ,OAAO,WACpCA,EAAQ,OAAO,UAEjB,QAAQ,SAASwQ,GAAmBL,EAAYhQ,CAAG,IAEnDH,EAAQ,QAAQG,CAAG,EACnBH,EAAQ,KAAK,QAASmQ,EAAW,KAAK,KAAKA,EAAY,OAAO,CAAC,EAC/DnQ,EAAQ,KAAK,QAASmQ,EAAW,UAAU,KAAKA,CAAU,CAAC,EAE/D,CACA,SAASJ,GAAeI,EAAYxP,EAAM0G,EAAI,CAC5C,GAAI1G,EAAM,CACR,MAAMwB,EAASoM,GAAS5N,CAAI,EAAE,OAC1BwP,EAAW,QACbA,EAAW,QAAQ,gBAAkBhO,EAErCgO,EAAW,iBAAmBhO,EAElC,GAAIkF,EAAI,CACN,MAAMlH,EAAM,IAAI,MACd,qCAAqCgQ,EAAW,eAAexB,EAAYwB,EAAW,UAAU,IACtG,EACI,QAAQ,SAAS9I,EAAIlH,CAAG,EAE5B,CACA,SAASiP,GAAmBvJ,EAAM+L,EAAQ,CACxC,MAAMzB,EAAa,KAAKlC,CAAY,EACpCkC,EAAW,oBAAsB,GACjCA,EAAW,cAAgByB,EAC3BzB,EAAW,WAAatK,EACpBsK,EAAW,QAAQlC,CAAY,IAAM,SAEzCkC,EAAW,QAAQ,eAAe,OAAQR,EAAY,EACtD,QAAQ,SAASkC,GAAQ1B,EAAW,OAAO,EACvCtK,IAAS,KACXsK,EAAW,MAAK,EAEhBA,EAAW,MAAMtK,EAAM+L,CAAM,EACjC,CACA,SAASvC,IAAkB,CACzB,MAAMc,EAAa,KAAKlC,CAAY,EAC/BkC,EAAW,UACdA,EAAW,QAAQ,QACvB,CACA,SAASb,GAAgBnP,EAAK,CAC5B,MAAMgQ,EAAa,KAAKlC,CAAY,EAChCkC,EAAW,QAAQlC,CAAY,IAAM,SACvCkC,EAAW,QAAQ,eAAe,OAAQR,EAAY,EACtD,QAAQ,SAASkC,GAAQ1B,EAAW,OAAO,EAC3CA,EAAW,MAAMhQ,EAAI6N,EAAW,CAAC,GAEnCmC,EAAW,KAAK,QAAShQ,CAAG,CAC9B,CACA,SAAS2R,IAAmB,CAC1B,KAAK7D,CAAY,EAAE,WACrB,CACA,SAASsB,GAAkB5O,EAAMD,EAAU,CACzC,KAAKuN,CAAY,EAAE,KAAK,UAAWtN,EAAMD,CAAQ,CACnD,CACA,SAAS8O,GAAe7O,EAAM,CAC5B,MAAMwP,EAAa,KAAKlC,CAAY,EACpCkC,EAAW,KAAKxP,EAAM,CAACwP,EAAW,UAAWjC,EAAI,EACjDiC,EAAW,KAAK,OAAQxP,CAAI,CAC9B,CACA,SAAS8O,GAAe9O,EAAM,CAC5B,KAAKsN,CAAY,EAAE,KAAK,OAAQtN,CAAI,CACtC,CACA,SAASkR,GAAO7R,EAAS,CACvBA,EAAQ,OAAM,CAChB,CACA,SAAS0P,IAAgB,CACvB,MAAMS,EAAa,KAAKlC,CAAY,EACpC,KAAK,eAAe,QAASyB,EAAa,EAC1C,KAAK,eAAe,OAAQC,EAAY,EACxC,KAAK,eAAe,MAAOC,EAAW,EACtCO,EAAW,YAActB,EAAY,QACrC,IAAI9N,EACA,CAAC,KAAK,eAAe,YAAc,CAACoP,EAAW,qBAAuB,CAACA,EAAW,UAAU,eAAe,eAAiBpP,EAAQoP,EAAW,QAAQ,KAAM,KAAM,MACrKA,EAAW,UAAU,MAAMpP,CAAK,EAElCoP,EAAW,UAAU,MACrB,KAAKlC,CAAY,EAAI,OACrB,aAAakC,EAAW,WAAW,EAC/BA,EAAW,UAAU,eAAe,UAAYA,EAAW,UAAU,eAAe,aACtFA,EAAW,UAAS,GAEpBA,EAAW,UAAU,GAAG,QAAS2B,EAAgB,EACjD3B,EAAW,UAAU,GAAG,SAAU2B,EAAgB,EAEtD,CACA,SAASnC,GAAa5O,EAAO,CACtB,KAAKkN,CAAY,EAAE,UAAU,MAAMlN,CAAK,GAC3C,KAAK,MAAK,CAEd,CACA,SAAS6O,IAAc,CACrB,MAAMO,EAAa,KAAKlC,CAAY,EACpCkC,EAAW,YAActB,EAAY,QACrCsB,EAAW,UAAU,MACrB,KAAK,IAAG,CACV,CACA,SAASN,IAAkB,CACzB,MAAMM,EAAa,KAAKlC,CAAY,EACpC,KAAK,eAAe,QAAS4B,EAAe,EAC5C,KAAK,GAAG,QAAS3B,EAAI,EACjBiC,IACFA,EAAW,YAActB,EAAY,QACrC,KAAK,QAAO,EAEhB,CACK,MAACkD,GAA8B7S,EAAwBgR,EAAS,EAC/D,CAAE,WAAA8B,EAAY,EAAG/L,GACvB,SAASgM,GAAMrG,EAAQ,CACrB,MAAMoD,EAA4B,IAAI,IACtC,IAAI9C,EAAQ,GACRC,EAAM,GACN,EAAI,EACR,IAAK,EAAG,EAAIP,EAAO,OAAQ,IAAK,CAC9B,MAAM/F,EAAO+F,EAAO,WAAW,CAAC,EAChC,GAAIO,IAAQ,IAAM6F,GAAWnM,CAAI,IAAM,EACjCqG,IAAU,KACZA,EAAQ,WACD,IAAM,IAAMrG,IAAS,IAAMA,IAAS,GACzCsG,IAAQ,IAAMD,IAAU,KAC1BC,EAAM,WACCtG,IAAS,GAAI,CACtB,GAAIqG,IAAU,GACZ,MAAM,IAAI,YAAY,iCAAiC,GAAG,EAExDC,IAAQ,KACVA,EAAM,GACR,MAAM+F,EAAYtG,EAAO,MAAMM,EAAOC,CAAG,EACzC,GAAI6C,EAAU,IAAIkD,CAAS,EACzB,MAAM,IAAI,YAAY,QAAQA,8BAAsC,EAEtElD,EAAU,IAAIkD,CAAS,EACvBhG,EAAQC,EAAM,OAEd,OAAM,IAAI,YAAY,iCAAiC,GAAG,EAG9D,GAAID,IAAU,IAAMC,IAAQ,GAC1B,MAAM,IAAI,YAAY,yBAAyB,EAEjD,MAAM2E,EAAWlF,EAAO,MAAMM,EAAO,CAAC,EACtC,GAAI8C,EAAU,IAAI8B,CAAQ,EACxB,MAAM,IAAI,YAAY,QAAQA,8BAAqC,EAErE,OAAA9B,EAAU,IAAI8B,CAAQ,EACf9B,CACT,CACA,IAAImD,GAAgB,CAAE,MAAAF,IACtB,MAAMG,GAAezF,EACf0F,GAAOtF,EACP,CAAE,WAAAuF,EAAY,EAAGlK,EACjBmK,GAAY9F,GACZ+F,EAAqBrN,GACrBsN,GAAcN,GACdO,GAAaxC,GACb,CAAE,KAAAyC,GAAM,WAAAC,EAAY,EAAGxR,EACvByR,GAAW,wBACXC,GAAU,EACVC,GAAU,EACVC,GAAS,EACf,MAAMC,WAAwBb,EAAa,CA2BzC,YAAY9R,EAASM,EAAU,CAmB7B,GAlBA,QACAN,EAAU,CACR,WAAY,IAAM,KAAO,KACzB,mBAAoB,GACpB,kBAAmB,GACnB,gBAAiB,KACjB,eAAgB,GAChB,aAAc,KACd,SAAU,GACV,QAAS,KAET,OAAQ,KACR,KAAM,KACN,KAAM,KACN,KAAM,KACN,UAAWoS,GACX,GAAGpS,CACT,EACQA,EAAQ,MAAQ,MAAQ,CAACA,EAAQ,QAAU,CAACA,EAAQ,UAAYA,EAAQ,MAAQ,OAASA,EAAQ,QAAUA,EAAQ,WAAaA,EAAQ,QAAUA,EAAQ,SAC5J,MAAM,IAAI,UACR,mFACR,EAoBI,GAlBIA,EAAQ,MAAQ,MAClB,KAAK,QAAU+R,GAAK,aAAa,CAACrB,EAAKI,IAAQ,CAC7C,MAAM8B,EAAOb,GAAK,aAAa,GAAG,EAClCjB,EAAI,UAAU,IAAK,CACjB,iBAAkB8B,EAAK,OACvB,eAAgB,YAC1B,CAAS,EACD9B,EAAI,IAAI8B,CAAI,CACpB,CAAO,EACD,KAAK,QAAQ,OACX5S,EAAQ,KACRA,EAAQ,KACRA,EAAQ,QACRM,CACR,GACeN,EAAQ,SACjB,KAAK,QAAUA,EAAQ,QAErB,KAAK,QAAS,CAChB,MAAM6S,EAAiB,KAAK,KAAK,KAAK,KAAM,YAAY,EACxD,KAAK,iBAAmBC,GAAa,KAAK,QAAS,CACjD,UAAW,KAAK,KAAK,KAAK,KAAM,WAAW,EAC3C,MAAO,KAAK,KAAK,KAAK,KAAM,OAAO,EACnC,QAAS,CAACpC,EAAKlI,EAAQoG,IAAS,CAC9B,KAAK,cAAc8B,EAAKlI,EAAQoG,EAAMiE,CAAc,CACrD,CACT,CAAO,EAEC7S,EAAQ,oBAAsB,KAChCA,EAAQ,kBAAoB,IAC1BA,EAAQ,iBACV,KAAK,QAA0B,IAAI,IACnC,KAAK,iBAAmB,IAE1B,KAAK,QAAUA,EACf,KAAK,OAASwS,EACf,CAUD,SAAU,CACR,GAAI,KAAK,QAAQ,SACf,MAAM,IAAI,MAAM,4CAA4C,EAE9D,OAAK,KAAK,QAEH,KAAK,QAAQ,UADX,IAEV,CAQD,MAAMzL,EAAI,CACR,GAAI,KAAK,SAAW2L,GAAQ,CACtB3L,GACF,KAAK,KAAK,QAAS,IAAM,CACvBA,EAAG,IAAI,MAAM,2BAA2B,CAAC,CACnD,CAAS,EAEH,QAAQ,SAASgM,EAAW,IAAI,EAChC,OAIF,GAFIhM,GACF,KAAK,KAAK,QAASA,CAAE,EACnB,KAAK,SAAW0L,GAGpB,GADA,KAAK,OAASA,GACV,KAAK,QAAQ,UAAY,KAAK,QAAQ,OACpC,KAAK,UACP,KAAK,iBAAgB,EACrB,KAAK,iBAAmB,KAAK,QAAU,MAErC,KAAK,QACF,KAAK,QAAQ,KAGhB,KAAK,iBAAmB,GAFxB,QAAQ,SAASM,EAAW,IAAI,EAKlC,QAAQ,SAASA,EAAW,IAAI,MAE7B,CACL,MAAMC,EAAS,KAAK,QACpB,KAAK,iBAAgB,EACrB,KAAK,iBAAmB,KAAK,QAAU,KACvCA,EAAO,MAAM,IAAM,CACjBD,EAAU,IAAI,CACtB,CAAO,EAEJ,CAQD,aAAarC,EAAK,CAChB,GAAI,KAAK,QAAQ,KAAM,CACrB,MAAMuC,EAAQvC,EAAI,IAAI,QAAQ,GAAG,EAEjC,IADiBuC,IAAU,GAAKvC,EAAI,IAAI,MAAM,EAAGuC,CAAK,EAAIvC,EAAI,OAC7C,KAAK,QAAQ,KAC5B,MAAO,GAEX,MAAO,EACR,CAWD,cAAcA,EAAKlI,EAAQoG,EAAM7H,EAAI,CACnCyB,EAAO,GAAG,QAAS0K,EAAa,EAChC,MAAMjP,EAAMyM,EAAI,QAAQ,mBAAmB,EACrCyC,EAAU,CAACzC,EAAI,QAAQ,uBAAuB,EACpD,GAAIA,EAAI,SAAW,MAAO,CAExB0C,EAAkC,KAAM1C,EAAKlI,EAAQ,IADrC,qBACiD,EACjE,OAEF,GAAIkI,EAAI,QAAQ,QAAQ,YAAW,IAAO,YAAa,CAErD0C,EAAkC,KAAM1C,EAAKlI,EAAQ,IADrC,wBACiD,EACjE,OAEF,GAAI,CAACvE,GAAO,CAACsO,GAAS,KAAKtO,CAAG,EAAG,CAE/BmP,EAAkC,KAAM1C,EAAKlI,EAAQ,IADrC,6CACiD,EACjE,OAEF,GAAI2K,IAAY,GAAKA,IAAY,GAAI,CAEnCC,EAAkC,KAAM1C,EAAKlI,EAAQ,IADrC,iDACiD,EACjE,OAEF,GAAI,CAAC,KAAK,aAAakI,CAAG,EAAG,CAC3B2C,EAAe7K,EAAQ,GAAG,EAC1B,OAEF,MAAM8K,EAAuB5C,EAAI,QAAQ,wBAAwB,EACjE,IAAIhC,EAA4B,IAAI,IACpC,GAAI4E,IAAyB,OAC3B,GAAI,CACF5E,EAAYyD,GAAY,MAAMmB,CAAoB,CACnD,MAAC,CAEAF,EAAkC,KAAM1C,EAAKlI,EAAQ,IADrC,uCACiD,EACjE,MACD,CAEH,MAAM4I,EAAyBV,EAAI,QAAQ,0BAA0B,EAC/DjI,EAAa,CAAA,EACnB,GAAI,KAAK,QAAQ,mBAAqB2I,IAA2B,OAAQ,CACvE,MAAMnI,EAAoB,IAAIiJ,EAC5B,KAAK,QAAQ,kBACb,GACA,KAAK,QAAQ,UACrB,EACM,GAAI,CACF,MAAMrO,EAASoO,GAAU,MAAMb,CAAsB,EACjDvN,EAAOqO,EAAmB,aAAa,IACzCjJ,EAAkB,OAAOpF,EAAOqO,EAAmB,aAAa,CAAC,EACjEzJ,EAAWyJ,EAAmB,aAAa,EAAIjJ,EAElD,MAAC,CAEAmK,EAAkC,KAAM1C,EAAKlI,EAAQ,IADrC,yDACiD,EACjE,MACD,EAEH,GAAI,KAAK,QAAQ,aAAc,CAC7B,MAAM+K,EAAO,CACX,OAAQ7C,EAAI,QAAQ,GAAGyC,IAAY,EAAI,uBAAyB,UAAU,EAC1E,OAAQ,CAAC,EAAEzC,EAAI,OAAO,YAAcA,EAAI,OAAO,WAC/C,IAAAA,CACR,EACM,GAAI,KAAK,QAAQ,aAAa,SAAW,EAAG,CAC1C,KAAK,QAAQ,aAAa6C,EAAM,CAACC,EAAUjO,EAAMiC,EAASmJ,IAAY,CACpE,GAAI,CAAC6C,EACH,OAAOH,EAAe7K,EAAQjD,GAAQ,IAAKiC,EAASmJ,CAAO,EAE7D,KAAK,gBACHlI,EACAxE,EACAyK,EACAgC,EACAlI,EACAoG,EACA7H,CACZ,CACA,CAAS,EACD,OAEF,GAAI,CAAC,KAAK,QAAQ,aAAawM,CAAI,EACjC,OAAOF,EAAe7K,EAAQ,GAAG,EAErC,KAAK,gBAAgBC,EAAYxE,EAAKyK,EAAWgC,EAAKlI,EAAQoG,EAAM7H,CAAE,CACvE,CAeD,gBAAgB0B,EAAYxE,EAAKyK,EAAWgC,EAAKlI,EAAQoG,EAAM7H,EAAI,CACjE,GAAI,CAACyB,EAAO,UAAY,CAACA,EAAO,SAC9B,OAAOA,EAAO,UAChB,GAAIA,EAAO8J,EAAU,EACnB,MAAM,IAAI,MACR,2GACR,EAEI,GAAI,KAAK,OAASE,GAChB,OAAOa,EAAe7K,EAAQ,GAAG,EAEnC,MAAMmI,EAAU,CACd,mCACA,qBACA,sBACA,yBALaqB,GAAW,MAAM,EAAE,OAAO/N,EAAMoO,EAAI,EAAE,OAAO,QAAQ,GAMxE,EACUtS,EAAK,IAAI,KAAK,QAAQ,UAAU,IAAI,EAC1C,GAAI2O,EAAU,KAAM,CAClB,MAAM8B,EAAW,KAAK,QAAQ,gBAAkB,KAAK,QAAQ,gBAAgB9B,EAAWgC,CAAG,EAAIhC,EAAU,OAAM,EAAG,KAAM,EAAC,MACrH8B,IACFG,EAAQ,KAAK,2BAA2BH,GAAU,EAClDzQ,EAAG,UAAYyQ,GAGnB,GAAI/H,EAAWyJ,EAAmB,aAAa,EAAG,CAChD,MAAMvO,EAAS8E,EAAWyJ,EAAmB,aAAa,EAAE,OACtDhO,EAAQ+N,GAAU,OAAO,CAC7B,CAACC,EAAmB,aAAa,EAAG,CAACvO,CAAM,CACnD,CAAO,EACDgN,EAAQ,KAAK,6BAA6BzM,GAAO,EACjDnE,EAAG,YAAc0I,EAEnB,KAAK,KAAK,UAAWkI,EAASD,CAAG,EACjClI,EAAO,MAAMmI,EAAQ,OAAO;AAAA,CAAM,EAAE,KAAK;AAAA,CAAM,CAAC,EAChDnI,EAAO,eAAe,QAAS0K,EAAa,EAC5CnT,EAAG,UAAUyI,EAAQoG,EAAM,CACzB,WAAY,KAAK,QAAQ,WACzB,mBAAoB,KAAK,QAAQ,kBACvC,CAAK,EACG,KAAK,UACP,KAAK,QAAQ,IAAI7O,CAAE,EACnBA,EAAG,GAAG,QAAS,IAAM,CACnB,KAAK,QAAQ,OAAOA,CAAE,EAClB,KAAK,kBAAoB,CAAC,KAAK,QAAQ,MACzC,QAAQ,SAASgT,EAAW,IAAI,CAE1C,CAAO,GAEHhM,EAAGhH,EAAI2Q,CAAG,CACX,CACH,CACA,IAAI+C,GAAkBd,GACtB,SAASG,GAAaE,EAAQU,EAAK,CACjC,UAAW/I,KAAS,OAAO,KAAK+I,CAAG,EACjCV,EAAO,GAAGrI,EAAO+I,EAAI/I,CAAK,CAAC,EAC7B,OAAO,UAA2B,CAChC,UAAWA,KAAS,OAAO,KAAK+I,CAAG,EACjCV,EAAO,eAAerI,EAAO+I,EAAI/I,CAAK,CAAC,CAE7C,CACA,CACA,SAASoI,EAAUC,EAAQ,CACzBA,EAAO,OAASN,GAChBM,EAAO,KAAK,OAAO,CACrB,CACA,SAASE,IAAgB,CACvB,KAAK,QAAO,CACd,CACA,SAASG,EAAe7K,EAAQjD,EAAMiC,EAASmJ,EAAS,CACtDnJ,EAAUA,GAAWuK,GAAK,aAAaxM,CAAI,EAC3CoL,EAAU,CACR,WAAY,QACZ,eAAgB,YAChB,iBAAkB,OAAO,WAAWnJ,CAAO,EAC3C,GAAGmJ,CACP,EACEnI,EAAO,KAAK,SAAUA,EAAO,OAAO,EACpCA,EAAO,IACL,YAAYjD,KAAQwM,GAAK,aAAaxM,CAAI;AAAA,EAC1C,OAAO,KAAKoL,CAAO,EAAE,IAAKgD,GAAM,GAAGA,MAAMhD,EAAQgD,CAAC,GAAG,EAAE,KAAK;AAAA,CAAM,EAAI;AAAA;AAAA,EAAanM,CACvF,CACA,CACA,SAAS4L,EAAkCJ,EAAQtC,EAAKlI,EAAQjD,EAAMiC,EAAS,CAC7E,GAAIwL,EAAO,cAAc,eAAe,EAAG,CACzC,MAAMnT,EAAM,IAAI,MAAM2H,CAAO,EAC7B,MAAM,kBAAkB3H,EAAKuT,CAAiC,EAC9DJ,EAAO,KAAK,gBAAiBnT,EAAK2I,EAAQkI,CAAG,OAE7C2C,EAAe7K,EAAQjD,EAAMiC,CAAO,CAExC,CACK,MAACoM,GAAoChV,EAAwB6U,EAAe"}