import{r as b}from"./__vite-browser-external-DFe-p4yY.js";var P={};function ie(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}function jt(r){if(r.__esModule)return r;var e=r.default;if(typeof e=="function"){var t=function s(){return this instanceof s?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};t.prototype=e.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(r).forEach(function(s){var i=Object.getOwnPropertyDescriptor(r,s);Object.defineProperty(t,s,i.get?i:{enumerable:!0,get:function(){return r[s]}})}),t}const{Duplex:Gt}=b;function Ge(r){r.emit("close")}function Vt(){!this.destroyed&&this._writableState.finished&&this.destroy()}function yt(r){this.removeListener("error",yt),this.destroy(),this.listenerCount("error")===0&&this.emit("error",r)}function qt(r,e){let t=!0;const s=new Gt({...e,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return r.on("message",function(n,o){const a=!o&&s._readableState.objectMode?n.toString():n;s.push(a)||r.pause()}),r.once("error",function(n){s.destroyed||(t=!1,s.destroy(n))}),r.once("close",function(){s.destroyed||s.push(null)}),s._destroy=function(i,n){if(r.readyState===r.CLOSED){n(i),process.nextTick(Ge,s);return}let o=!1;r.once("error",function(f){o=!0,n(f)}),r.once("close",function(){o||n(i),process.nextTick(Ge,s)}),t&&r.terminate()},s._final=function(i){if(r.readyState===r.CONNECTING){r.once("open",function(){s._final(i)});return}r._socket!==null&&(r._socket._writableState.finished?(i(),s._readableState.endEmitted&&s.destroy()):(r._socket.once("finish",function(){i()}),r.close()))},s._read=function(){r.isPaused&&r.resume()},s._write=function(i,n,o){if(r.readyState===r.CONNECTING){r.once("open",function(){s._write(i,n,o)});return}r.send(i,o)},s.on("end",Vt),s.on("error",yt),s}var Ht=qt;const Ss=ie(Ht);var _e={exports:{}},j={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}},oe={exports:{}},ae={exports:{}};function zt(r){throw new Error('Could not dynamically require "'+r+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var ke,Ve;function Yt(){if(Ve)return ke;Ve=1;var r=b,e=b,t=b,s=typeof __webpack_require__=="function"?__non_webpack_require__:zt,i=process.config&&process.config.variables||{},n=!!P.PREBUILDS_ONLY,o=process.versions.modules,a=xe()?"electron":X()?"node-webkit":"node",f=P.npm_config_arch||t.arch(),l=P.npm_config_platform||t.platform(),h=P.LIBC||(Mt(l)?"musl":"glibc"),c=P.ARM_VERSION||(f==="arm64"?"8":i.arm_version)||"",g=(process.versions.uv||"").split(".")[0];ke=p;function p(u){return s(p.resolve(u))}p.resolve=p.path=function(u){u=e.resolve(u||".");try{var m=s(e.join(u,"package.json")).name.toUpperCase().replace(/-/g,"_");P[m+"_PREBUILD"]&&(u=P[m+"_PREBUILD"])}catch{}if(!n){var _=d(e.join(u,"build/Release"),x);if(_)return _;var O=d(e.join(u,"build/Debug"),x);if(O)return O}var V=We(u);if(V)return V;var w=We(e.dirname(process.execPath));if(w)return w;var Dt=["platform="+l,"arch="+f,"runtime="+a,"abi="+o,"uv="+g,c?"armv="+c:"","libc="+h,"node="+process.versions.node,process.versions.electron?"electron="+process.versions.electron:"",typeof __webpack_require__=="function"?"webpack=true":""].filter(Boolean).join(" ");throw new Error("No native build was found for "+Dt+`
    loaded from: `+u+`
`);function We(we){var Wt=v(e.join(we,"prebuilds")).map(R),Ae=Wt.filter(D(l,f)).sort($)[0];if(Ae){var Fe=e.join(we,"prebuilds",Ae.name),At=v(Fe).map(N),Ft=At.filter(z(a,o)),je=Ft.sort(K(a))[0];if(je)return e.join(Fe,je.file)}}};function v(u){try{return r.readdirSync(u)}catch{return[]}}function d(u,m){var _=v(u).filter(m);return _[0]&&e.join(u,_[0])}function x(u){return/\.node$/.test(u)}function R(u){var m=u.split("-");if(m.length===2){var _=m[0],O=m[1].split("+");if(_&&O.length&&O.every(Boolean))return{name:u,platform:_,architectures:O}}}function D(u,m){return function(_){return _==null||_.platform!==u?!1:_.architectures.includes(m)}}function $(u,m){return u.architectures.length-m.architectures.length}function N(u){var m=u.split("."),_=m.pop(),O={file:u,specificity:0};if(_==="node"){for(var V=0;V<m.length;V++){var w=m[V];if(w==="node"||w==="electron"||w==="node-webkit")O.runtime=w;else if(w==="napi")O.napi=!0;else if(w.slice(0,3)==="abi")O.abi=w.slice(3);else if(w.slice(0,2)==="uv")O.uv=w.slice(2);else if(w.slice(0,4)==="armv")O.armv=w.slice(4);else if(w==="glibc"||w==="musl")O.libc=w;else continue;O.specificity++}return O}}function z(u,m){return function(_){return!(_==null||_.runtime!==u&&!Y(_)||_.abi!==m&&!_.napi||_.uv&&_.uv!==g||_.armv&&_.armv!==c||_.libc&&_.libc!==h)}}function Y(u){return u.runtime==="node"&&u.napi}function K(u){return function(m,_){return m.runtime!==_.runtime?m.runtime===u?-1:1:m.abi!==_.abi?m.abi?-1:1:m.specificity!==_.specificity?m.specificity>_.specificity?-1:1:0}}function X(){return!!(process.versions&&process.versions.nw)}function xe(){return process.versions&&process.versions.electron||P.ELECTRON_RUN_AS_NODE?!0:typeof window<"u"&&window.process&&window.process.type==="renderer"}function Mt(u){return u==="linux"&&r.existsSync("/etc/alpine-release")}return p.parseTags=N,p.matchTags=z,p.compareTags=K,p.parseTuple=R,p.matchTuple=D,p.compareTuples=$,ke}var qe;function Kt(){return qe||(qe=1,typeof process.addon=="function"?ae.exports=process.addon.bind(process):ae.exports=Yt()),ae.exports}var Oe,He;function Xt(){return He||(He=1,Oe={mask:(t,s,i,n,o)=>{for(var a=0;a<o;a++)i[n+a]=t[a]^s[a&3]},unmask:(t,s)=>{const i=t.length;for(var n=0;n<i;n++)t[n]^=s[n&3]}}),Oe}var ze;function Zt(){if(ze)return oe.exports;ze=1;try{oe.exports=Kt()(__dirname)}catch{oe.exports=Xt()}return oe.exports}var Qt,Jt;const{EMPTY_BUFFER:er}=j,$e=Buffer[Symbol.species];function tr(r,e){if(r.length===0)return er;if(r.length===1)return r[0];const t=Buffer.allocUnsafe(e);let s=0;for(let i=0;i<r.length;i++){const n=r[i];t.set(n,s),s+=n.length}return s<e?new $e(t.buffer,t.byteOffset,s):t}function St(r,e,t,s,i){for(let n=0;n<i;n++)t[s+n]=r[n]^e[n&3]}function Et(r,e){for(let t=0;t<r.length;t++)r[t]^=e[t&3]}function rr(r){return r.length===r.buffer.byteLength?r.buffer:r.buffer.slice(r.byteOffset,r.byteOffset+r.length)}function Ie(r){if(Ie.readOnly=!0,Buffer.isBuffer(r))return r;let e;return r instanceof ArrayBuffer?e=new $e(r):ArrayBuffer.isView(r)?e=new $e(r.buffer,r.byteOffset,r.byteLength):(e=Buffer.from(r),Ie.readOnly=!1),e}_e.exports={concat:tr,mask:St,toArrayBuffer:rr,toBuffer:Ie,unmask:Et};if(!P.WS_NO_BUFFER_UTIL)try{const r=Zt();Jt=_e.exports.mask=function(e,t,s,i,n){n<48?St(e,t,s,i,n):r.mask(e,t,s,i,n)},Qt=_e.exports.unmask=function(e,t){e.length<32?Et(e,t):r.unmask(e,t)}}catch{}var ge=_e.exports;const Ye=Symbol("kDone"),Te=Symbol("kRun");let sr=class{constructor(e){this[Ye]=()=>{this.pending--,this[Te]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[Te]()}[Te](){if(this.pending!==this.concurrency&&this.jobs.length){const e=this.jobs.shift();this.pending++,e(this[Ye])}}};var ir=sr;const Z=b,Ke=ge,nr=ir,{kStatusCode:bt}=j,or=Buffer[Symbol.species],ar=Buffer.from([0,0,255,255]),pe=Symbol("permessage-deflate"),B=Symbol("total-length"),re=Symbol("callback"),I=Symbol("buffers"),ue=Symbol("error");let le,lr=class{constructor(e,t,s){if(this._maxPayload=s|0,this._options=e||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,!le){const i=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;le=new nr(i)}}static get extensionName(){return"permessage-deflate"}offer(){const e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){const e=this._deflate[re];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){const t=this._options,s=e.find(i=>!(t.serverNoContextTakeover===!1&&i.server_no_context_takeover||i.server_max_window_bits&&(t.serverMaxWindowBits===!1||typeof t.serverMaxWindowBits=="number"&&t.serverMaxWindowBits>i.server_max_window_bits)||typeof t.clientMaxWindowBits=="number"&&!i.client_max_window_bits));if(!s)throw new Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),typeof t.serverMaxWindowBits=="number"&&(s.server_max_window_bits=t.serverMaxWindowBits),typeof t.clientMaxWindowBits=="number"?s.client_max_window_bits=t.clientMaxWindowBits:(s.client_max_window_bits===!0||t.clientMaxWindowBits===!1)&&delete s.client_max_window_bits,s}acceptAsClient(e){const t=e[0];if(this._options.clientNoContextTakeover===!1&&t.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!t.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(t.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return t}normalizeParams(e){return e.forEach(t=>{Object.keys(t).forEach(s=>{let i=t[s];if(i.length>1)throw new Error(`Parameter "${s}" must have only a single value`);if(i=i[0],s==="client_max_window_bits"){if(i!==!0){const n=+i;if(!Number.isInteger(n)||n<8||n>15)throw new TypeError(`Invalid value for parameter "${s}": ${i}`);i=n}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${s}": ${i}`)}else if(s==="server_max_window_bits"){const n=+i;if(!Number.isInteger(n)||n<8||n>15)throw new TypeError(`Invalid value for parameter "${s}": ${i}`);i=n}else if(s==="client_no_context_takeover"||s==="server_no_context_takeover"){if(i!==!0)throw new TypeError(`Invalid value for parameter "${s}": ${i}`)}else throw new Error(`Unknown parameter "${s}"`);t[s]=i})}),e}decompress(e,t,s){le.add(i=>{this._decompress(e,t,(n,o)=>{i(),s(n,o)})})}compress(e,t,s){le.add(i=>{this._compress(e,t,(n,o)=>{i(),s(n,o)})})}_decompress(e,t,s){const i=this._isServer?"client":"server";if(!this._inflate){const n=`${i}_max_window_bits`,o=typeof this.params[n]!="number"?Z.Z_DEFAULT_WINDOWBITS:this.params[n];this._inflate=Z.createInflateRaw({...this._options.zlibInflateOptions,windowBits:o}),this._inflate[pe]=this,this._inflate[B]=0,this._inflate[I]=[],this._inflate.on("error",cr),this._inflate.on("data",xt)}this._inflate[re]=s,this._inflate.write(e),t&&this._inflate.write(ar),this._inflate.flush(()=>{const n=this._inflate[ue];if(n){this._inflate.close(),this._inflate=null,s(n);return}const o=Ke.concat(this._inflate[I],this._inflate[B]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[B]=0,this._inflate[I]=[],t&&this.params[`${i}_no_context_takeover`]&&this._inflate.reset()),s(null,o)})}_compress(e,t,s){const i=this._isServer?"server":"client";if(!this._deflate){const n=`${i}_max_window_bits`,o=typeof this.params[n]!="number"?Z.Z_DEFAULT_WINDOWBITS:this.params[n];this._deflate=Z.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:o}),this._deflate[B]=0,this._deflate[I]=[],this._deflate.on("data",fr)}this._deflate[re]=s,this._deflate.write(e),this._deflate.flush(Z.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let n=Ke.concat(this._deflate[I],this._deflate[B]);t&&(n=new or(n.buffer,n.byteOffset,n.length-4)),this._deflate[re]=null,this._deflate[B]=0,this._deflate[I]=[],t&&this.params[`${i}_no_context_takeover`]&&this._deflate.reset(),s(null,n)})}};var ye=lr;function fr(r){this[I].push(r),this[B]+=r.length}function xt(r){if(this[B]+=r.length,this[pe]._maxPayload<1||this[B]<=this[pe]._maxPayload){this[I].push(r);return}this[ue]=new RangeError("Max payload size exceeded"),this[ue].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[ue][bt]=1009,this.removeListener("data",xt),this.reset()}function cr(r){this[pe]._inflate=null,r[bt]=1007,this[re](r)}var me={exports:{}};const hr={},ur=Object.freeze(Object.defineProperty({__proto__:null,default:hr},Symbol.toStringTag,{value:"Module"})),dr=jt(ur);var Xe;const{isUtf8:Ze}=b,_r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function pr(r){return r>=1e3&&r<=1014&&r!==1004&&r!==1005&&r!==1006||r>=3e3&&r<=4999}function Me(r){const e=r.length;let t=0;for(;t<e;)if(!(r[t]&128))t++;else if((r[t]&224)===192){if(t+1===e||(r[t+1]&192)!==128||(r[t]&254)===192)return!1;t+=2}else if((r[t]&240)===224){if(t+2>=e||(r[t+1]&192)!==128||(r[t+2]&192)!==128||r[t]===224&&(r[t+1]&224)===128||r[t]===237&&(r[t+1]&224)===160)return!1;t+=3}else if((r[t]&248)===240){if(t+3>=e||(r[t+1]&192)!==128||(r[t+2]&192)!==128||(r[t+3]&192)!==128||r[t]===240&&(r[t+1]&240)===128||r[t]===244&&r[t+1]>143||r[t]>244)return!1;t+=4}else return!1;return!0}me.exports={isValidStatusCode:pr,isValidUTF8:Me,tokenChars:_r};if(Ze)Xe=me.exports.isValidUTF8=function(r){return r.length<24?Me(r):Ze(r)};else if(!P.WS_NO_UTF_8_VALIDATE)try{const r=dr;Xe=me.exports.isValidUTF8=function(e){return e.length<32?Me(e):r(e)}}catch{}var Se=me.exports;const{Writable:mr}=b,Qe=ye,{BINARY_TYPES:vr,EMPTY_BUFFER:Je,kStatusCode:gr,kWebSocket:yr}=j,{concat:Ce,toArrayBuffer:Sr,unmask:Er}=ge,{isValidStatusCode:br,isValidUTF8:et}=Se,fe=Buffer[Symbol.species],Q=0,tt=1,rt=2,st=3,Ne=4,xr=5;let wr=class extends mr{constructor(e={}){super(),this._binaryType=e.binaryType||vr[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=e.maxPayload|0,this._skipUTF8Validation=!!e.skipUTF8Validation,this[yr]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._state=Q,this._loop=!1}_write(e,t,s){if(this._opcode===8&&this._state==Q)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){const s=this._buffers[0];return this._buffers[0]=new fe(s.buffer,s.byteOffset+e,s.length-e),new fe(s.buffer,s.byteOffset,e)}const t=Buffer.allocUnsafe(e);do{const s=this._buffers[0],i=t.length-e;e>=s.length?t.set(this._buffers.shift(),i):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),i),this._buffers[0]=new fe(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){let t;this._loop=!0;do switch(this._state){case Q:t=this.getInfo();break;case tt:t=this.getPayloadLength16();break;case rt:t=this.getPayloadLength64();break;case st:this.getMask();break;case Ne:t=this.getData(e);break;default:this._loop=!1;return}while(this._loop);e(t)}getInfo(){if(this._bufferedBytes<2){this._loop=!1;return}const e=this.consume(2);if(e[0]&48)return this._loop=!1,E(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");const t=(e[0]&64)===64;if(t&&!this._extensions[Qe.extensionName])return this._loop=!1,E(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(this._fin=(e[0]&128)===128,this._opcode=e[0]&15,this._payloadLength=e[1]&127,this._opcode===0){if(t)return this._loop=!1,E(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(!this._fragmented)return this._loop=!1,E(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented)return this._loop=!1,E(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");this._compressed=t}else if(this._opcode>7&&this._opcode<11){if(!this._fin)return this._loop=!1,E(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");if(t)return this._loop=!1,E(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1)return this._loop=!1,E(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH")}else return this._loop=!1,E(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(e[1]&128)===128,this._isServer){if(!this._masked)return this._loop=!1,E(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK")}else if(this._masked)return this._loop=!1,E(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");if(this._payloadLength===126)this._state=tt;else if(this._payloadLength===127)this._state=rt;else return this.haveLength()}getPayloadLength16(){if(this._bufferedBytes<2){this._loop=!1;return}return this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength()}getPayloadLength64(){if(this._bufferedBytes<8){this._loop=!1;return}const e=this.consume(8),t=e.readUInt32BE(0);return t>Math.pow(2,21)-1?(this._loop=!1,E(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH")):(this._payloadLength=t*Math.pow(2,32)+e.readUInt32BE(4),this.haveLength())}haveLength(){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return this._loop=!1,E(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");this._masked?this._state=st:this._state=Ne}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=Ne}getData(e){let t=Je;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3]&&Er(t,this._mask)}if(this._opcode>7)return this.controlMessage(t);if(this._compressed){this._state=xr,this.decompress(t,e);return}return t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage()}decompress(e,t){this._extensions[Qe.extensionName].decompress(e,this._fin,(i,n)=>{if(i)return t(i);if(n.length){if(this._messageLength+=n.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return t(E(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(n)}const o=this.dataMessage();if(o)return t(o);this.startLoop(t)})}dataMessage(){if(this._fin){const e=this._messageLength,t=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let s;this._binaryType==="nodebuffer"?s=Ce(t,e):this._binaryType==="arraybuffer"?s=Sr(Ce(t,e)):s=t,this.emit("message",s,!0)}else{const s=Ce(t,e);if(!this._skipUTF8Validation&&!et(s))return this._loop=!1,E(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");this.emit("message",s,!1)}}this._state=Q}controlMessage(e){if(this._opcode===8)if(this._loop=!1,e.length===0)this.emit("conclude",1005,Je),this.end();else{const t=e.readUInt16BE(0);if(!br(t))return E(RangeError,`invalid status code ${t}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");const s=new fe(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!et(s))return E(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");this.emit("conclude",t,s),this.end()}else this._opcode===9?this.emit("ping",e):this.emit("pong",e);this._state=Q}};var wt=wr;function E(r,e,t,s,i){const n=new r(t?`Invalid WebSocket frame: ${e}`:e);return Error.captureStackTrace(n,E),n.code=i,n[gr]=s,n}const ws=ie(wt),{randomFillSync:kr}=b,it=ye,{EMPTY_BUFFER:Or}=j,{isValidStatusCode:Tr}=Se,{mask:nt,toBuffer:q}=ge,C=Symbol("kByteLength"),Cr=Buffer.alloc(4);let Nr=class A{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let s,i=!1,n=2,o=!1;t.mask&&(s=t.maskBuffer||Cr,t.generateMask?t.generateMask(s):kr(s,0,4),o=(s[0]|s[1]|s[2]|s[3])===0,n=6);let a;typeof e=="string"?(!t.mask||o)&&t[C]!==void 0?a=t[C]:(e=Buffer.from(e),a=e.length):(a=e.length,i=t.mask&&t.readOnly&&!o);let f=a;a>=65536?(n+=8,f=127):a>125&&(n+=2,f=126);const l=Buffer.allocUnsafe(i?a+n:n);return l[0]=t.fin?t.opcode|128:t.opcode,t.rsv1&&(l[0]|=64),l[1]=f,f===126?l.writeUInt16BE(a,2):f===127&&(l[2]=l[3]=0,l.writeUIntBE(a,4,6)),t.mask?(l[1]|=128,l[n-4]=s[0],l[n-3]=s[1],l[n-2]=s[2],l[n-1]=s[3],o?[l,e]:i?(nt(e,s,l,n,a),[l]):(nt(e,s,e,0,a),[l,e])):[l,e]}close(e,t,s,i){let n;if(e===void 0)n=Or;else{if(typeof e!="number"||!Tr(e))throw new TypeError("First argument must be a valid error code number");if(t===void 0||!t.length)n=Buffer.allocUnsafe(2),n.writeUInt16BE(e,0);else{const a=Buffer.byteLength(t);if(a>123)throw new RangeError("The message must not be greater than 123 bytes");n=Buffer.allocUnsafe(2+a),n.writeUInt16BE(e,0),typeof t=="string"?n.write(t,2):n.set(t,2)}}const o={[C]:n.length,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,n,!1,o,i]):this.sendFrame(A.frame(n,o),i)}ping(e,t,s){let i,n;if(typeof e=="string"?(i=Buffer.byteLength(e),n=!1):(e=q(e),i=e.length,n=q.readOnly),i>125)throw new RangeError("The data size must not be greater than 125 bytes");const o={[C]:i,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:n,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,s]):this.sendFrame(A.frame(e,o),s)}pong(e,t,s){let i,n;if(typeof e=="string"?(i=Buffer.byteLength(e),n=!1):(e=q(e),i=e.length,n=q.readOnly),i>125)throw new RangeError("The data size must not be greater than 125 bytes");const o={[C]:i,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:n,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,s]):this.sendFrame(A.frame(e,o),s)}send(e,t,s){const i=this._extensions[it.extensionName];let n=t.binary?2:1,o=t.compress,a,f;if(typeof e=="string"?(a=Buffer.byteLength(e),f=!1):(e=q(e),a=e.length,f=q.readOnly),this._firstFragment?(this._firstFragment=!1,o&&i&&i.params[i._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(o=a>=i._threshold),this._compress=o):(o=!1,n=0),t.fin&&(this._firstFragment=!0),i){const l={[C]:a,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:n,readOnly:f,rsv1:o};this._deflating?this.enqueue([this.dispatch,e,this._compress,l,s]):this.dispatch(e,this._compress,l,s)}else this.sendFrame(A.frame(e,{[C]:a,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:n,readOnly:f,rsv1:!1}),s)}dispatch(e,t,s,i){if(!t){this.sendFrame(A.frame(e,s),i);return}const n=this._extensions[it.extensionName];this._bufferedBytes+=s[C],this._deflating=!0,n.compress(e,s.fin,(o,a)=>{if(this._socket.destroyed){const f=new Error("The socket was closed while data was being compressed");typeof i=="function"&&i(f);for(let l=0;l<this._queue.length;l++){const h=this._queue[l],c=h[h.length-1];typeof c=="function"&&c(f)}return}this._bufferedBytes-=s[C],this._deflating=!1,s.readOnly=!1,this.sendFrame(A.frame(a,s),i),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){const e=this._queue.shift();this._bufferedBytes-=e[3][C],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][C],this._queue.push(e)}sendFrame(e,t){e.length===2?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}};var kt=Nr;const ks=ie(kt),{kForOnEventAttribute:J,kListener:Le}=j,ot=Symbol("kCode"),at=Symbol("kData"),lt=Symbol("kError"),ft=Symbol("kMessage"),ct=Symbol("kReason"),H=Symbol("kTarget"),ht=Symbol("kType"),ut=Symbol("kWasClean");class G{constructor(e){this[H]=null,this[ht]=e}get target(){return this[H]}get type(){return this[ht]}}Object.defineProperty(G.prototype,"target",{enumerable:!0});Object.defineProperty(G.prototype,"type",{enumerable:!0});class ne extends G{constructor(e,t={}){super(e),this[ot]=t.code===void 0?0:t.code,this[ct]=t.reason===void 0?"":t.reason,this[ut]=t.wasClean===void 0?!1:t.wasClean}get code(){return this[ot]}get reason(){return this[ct]}get wasClean(){return this[ut]}}Object.defineProperty(ne.prototype,"code",{enumerable:!0});Object.defineProperty(ne.prototype,"reason",{enumerable:!0});Object.defineProperty(ne.prototype,"wasClean",{enumerable:!0});class Ee extends G{constructor(e,t={}){super(e),this[lt]=t.error===void 0?null:t.error,this[ft]=t.message===void 0?"":t.message}get error(){return this[lt]}get message(){return this[ft]}}Object.defineProperty(Ee.prototype,"error",{enumerable:!0});Object.defineProperty(Ee.prototype,"message",{enumerable:!0});class De extends G{constructor(e,t={}){super(e),this[at]=t.data===void 0?null:t.data}get data(){return this[at]}}Object.defineProperty(De.prototype,"data",{enumerable:!0});const Lr={addEventListener(r,e,t={}){for(const i of this.listeners(r))if(!t[J]&&i[Le]===e&&!i[J])return;let s;if(r==="message")s=function(n,o){const a=new De("message",{data:o?n:n.toString()});a[H]=this,ce(e,this,a)};else if(r==="close")s=function(n,o){const a=new ne("close",{code:n,reason:o.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});a[H]=this,ce(e,this,a)};else if(r==="error")s=function(n){const o=new Ee("error",{error:n,message:n.message});o[H]=this,ce(e,this,o)};else if(r==="open")s=function(){const n=new G("open");n[H]=this,ce(e,this,n)};else return;s[J]=!!t[J],s[Le]=e,t.once?this.once(r,s):this.on(r,s)},removeEventListener(r,e){for(const t of this.listeners(r))if(t[Le]===e&&!t[J]){this.removeListener(r,t);break}}};var Pr={CloseEvent:ne,ErrorEvent:Ee,Event:G,EventTarget:Lr,MessageEvent:De};function ce(r,e,t){typeof r=="object"&&r.handleEvent?r.handleEvent.call(r,t):r.call(e,t)}const{tokenChars:ee}=Se;function L(r,e,t){r[e]===void 0?r[e]=[t]:r[e].push(t)}function Rr(r){const e=Object.create(null);let t=Object.create(null),s=!1,i=!1,n=!1,o,a,f=-1,l=-1,h=-1,c=0;for(;c<r.length;c++)if(l=r.charCodeAt(c),o===void 0)if(h===-1&&ee[l]===1)f===-1&&(f=c);else if(c!==0&&(l===32||l===9))h===-1&&f!==-1&&(h=c);else if(l===59||l===44){if(f===-1)throw new SyntaxError(`Unexpected character at index ${c}`);h===-1&&(h=c);const p=r.slice(f,h);l===44?(L(e,p,t),t=Object.create(null)):o=p,f=h=-1}else throw new SyntaxError(`Unexpected character at index ${c}`);else if(a===void 0)if(h===-1&&ee[l]===1)f===-1&&(f=c);else if(l===32||l===9)h===-1&&f!==-1&&(h=c);else if(l===59||l===44){if(f===-1)throw new SyntaxError(`Unexpected character at index ${c}`);h===-1&&(h=c),L(t,r.slice(f,h),!0),l===44&&(L(e,o,t),t=Object.create(null),o=void 0),f=h=-1}else if(l===61&&f!==-1&&h===-1)a=r.slice(f,c),f=h=-1;else throw new SyntaxError(`Unexpected character at index ${c}`);else if(i){if(ee[l]!==1)throw new SyntaxError(`Unexpected character at index ${c}`);f===-1?f=c:s||(s=!0),i=!1}else if(n)if(ee[l]===1)f===-1&&(f=c);else if(l===34&&f!==-1)n=!1,h=c;else if(l===92)i=!0;else throw new SyntaxError(`Unexpected character at index ${c}`);else if(l===34&&r.charCodeAt(c-1)===61)n=!0;else if(h===-1&&ee[l]===1)f===-1&&(f=c);else if(f!==-1&&(l===32||l===9))h===-1&&(h=c);else if(l===59||l===44){if(f===-1)throw new SyntaxError(`Unexpected character at index ${c}`);h===-1&&(h=c);let p=r.slice(f,h);s&&(p=p.replace(/\\/g,""),s=!1),L(t,a,p),l===44&&(L(e,o,t),t=Object.create(null),o=void 0),a=void 0,f=h=-1}else throw new SyntaxError(`Unexpected character at index ${c}`);if(f===-1||n||l===32||l===9)throw new SyntaxError("Unexpected end of input");h===-1&&(h=c);const g=r.slice(f,h);return o===void 0?L(e,g,t):(a===void 0?L(t,g,!0):s?L(t,a,g.replace(/\\/g,"")):L(t,a,g),L(e,o,t)),e}function Br(r){return Object.keys(r).map(e=>{let t=r[e];return Array.isArray(t)||(t=[t]),t.map(s=>[e].concat(Object.keys(s).map(i=>{let n=s[i];return Array.isArray(n)||(n=[n]),n.map(o=>o===!0?i:`${i}=${o}`).join("; ")})).join("; ")).join(", ")}).join(", ")}var Ot={format:Br,parse:Rr};const Ur=b,$r=b,Ir=b,Tt=b,Mr=b,{randomBytes:Dr,createHash:Wr}=b,{URL:Pe}=b,M=ye,Ar=wt,Fr=kt,{BINARY_TYPES:dt,EMPTY_BUFFER:he,GUID:jr,kForOnEventAttribute:Re,kListener:Gr,kStatusCode:Vr,kWebSocket:k,NOOP:Ct}=j,{EventTarget:{addEventListener:qr,removeEventListener:Hr}}=Pr,{format:zr,parse:Yr}=Ot,{toBuffer:Kr}=ge,Xr=30*1e3,Nt=Symbol("kAborted"),Be=[8,13],U=["CONNECTING","OPEN","CLOSING","CLOSED"],Zr=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;let S=class y extends Ur{constructor(e,t,s){super(),this._binaryType=dt[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=he,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=y.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,e!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,t===void 0?t=[]:Array.isArray(t)||(typeof t=="object"&&t!==null?(s=t,t=[]):t=[t]),Pt(this,e,t,s)):this._isServer=!0}get binaryType(){return this._binaryType}set binaryType(e){dt.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){const i=new Ar({binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation});this._sender=new Fr(e,this._extensions,s.generateMask),this._receiver=i,this._socket=e,i[k]=this,e[k]=this,i.on("conclude",es),i.on("drain",ts),i.on("error",rs),i.on("message",ss),i.on("ping",is),i.on("pong",ns),e.setTimeout(0),e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",Bt),e.on("data",be),e.on("end",Ut),e.on("error",$t),this._readyState=y.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=y.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[M.extensionName]&&this._extensions[M.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=y.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==y.CLOSED){if(this.readyState===y.CONNECTING){T(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===y.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=y.CLOSING,this._sender.close(e,t,!this._isServer,s=>{s||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),Xr)}}pause(){this.readyState===y.CONNECTING||this.readyState===y.CLOSED||(this._paused=!0,this._socket.pause())}ping(e,t,s){if(this.readyState===y.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(s=e,e=t=void 0):typeof t=="function"&&(s=t,t=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==y.OPEN){Ue(this,e,s);return}t===void 0&&(t=!this._isServer),this._sender.ping(e||he,t,s)}pong(e,t,s){if(this.readyState===y.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(s=e,e=t=void 0):typeof t=="function"&&(s=t,t=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==y.OPEN){Ue(this,e,s);return}t===void 0&&(t=!this._isServer),this._sender.pong(e||he,t,s)}resume(){this.readyState===y.CONNECTING||this.readyState===y.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,s){if(this.readyState===y.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof t=="function"&&(s=t,t={}),typeof e=="number"&&(e=e.toString()),this.readyState!==y.OPEN){Ue(this,e,s);return}const i={binary:typeof e!="string",mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[M.extensionName]||(i.compress=!1),this._sender.send(e||he,i,s)}terminate(){if(this.readyState!==y.CLOSED){if(this.readyState===y.CONNECTING){T(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=y.CLOSING,this._socket.destroy())}}};Object.defineProperty(S,"CONNECTING",{enumerable:!0,value:U.indexOf("CONNECTING")});Object.defineProperty(S.prototype,"CONNECTING",{enumerable:!0,value:U.indexOf("CONNECTING")});Object.defineProperty(S,"OPEN",{enumerable:!0,value:U.indexOf("OPEN")});Object.defineProperty(S.prototype,"OPEN",{enumerable:!0,value:U.indexOf("OPEN")});Object.defineProperty(S,"CLOSING",{enumerable:!0,value:U.indexOf("CLOSING")});Object.defineProperty(S.prototype,"CLOSING",{enumerable:!0,value:U.indexOf("CLOSING")});Object.defineProperty(S,"CLOSED",{enumerable:!0,value:U.indexOf("CLOSED")});Object.defineProperty(S.prototype,"CLOSED",{enumerable:!0,value:U.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(r=>{Object.defineProperty(S.prototype,r,{enumerable:!0})});["open","error","close","message"].forEach(r=>{Object.defineProperty(S.prototype,`on${r}`,{enumerable:!0,get(){for(const e of this.listeners(r))if(e[Re])return e[Gr];return null},set(e){for(const t of this.listeners(r))if(t[Re]){this.removeListener(r,t);break}typeof e=="function"&&this.addEventListener(r,e,{[Re]:!0})}})});S.prototype.addEventListener=qr;S.prototype.removeEventListener=Hr;var Lt=S;function Pt(r,e,t,s){const i={protocolVersion:Be[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...s,createConnection:void 0,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(!Be.includes(i.protocolVersion))throw new RangeError(`Unsupported protocol version: ${i.protocolVersion} (supported versions: ${Be.join(", ")})`);let n;if(e instanceof Pe)n=e,r._url=e.href;else{try{n=new Pe(e)}catch{throw new SyntaxError(`Invalid URL: ${e}`)}r._url=e}const o=n.protocol==="wss:",a=n.protocol==="ws+unix:";let f;if(n.protocol!=="ws:"&&!o&&!a?f=`The URL's protocol must be one of "ws:", "wss:", or "ws+unix:"`:a&&!n.pathname?f="The URL's pathname is empty":n.hash&&(f="The URL contains a fragment identifier"),f){const d=new SyntaxError(f);if(r._redirects===0)throw d;de(r,d);return}const l=o?443:80,h=Dr(16).toString("base64"),c=o?$r.request:Ir.request,g=new Set;let p;if(i.createConnection=o?Jr:Qr,i.defaultPort=i.defaultPort||l,i.port=n.port||l,i.host=n.hostname.startsWith("[")?n.hostname.slice(1,-1):n.hostname,i.headers={...i.headers,"Sec-WebSocket-Version":i.protocolVersion,"Sec-WebSocket-Key":h,Connection:"Upgrade",Upgrade:"websocket"},i.path=n.pathname+n.search,i.timeout=i.handshakeTimeout,i.perMessageDeflate&&(p=new M(i.perMessageDeflate!==!0?i.perMessageDeflate:{},!1,i.maxPayload),i.headers["Sec-WebSocket-Extensions"]=zr({[M.extensionName]:p.offer()})),t.length){for(const d of t){if(typeof d!="string"||!Zr.test(d)||g.has(d))throw new SyntaxError("An invalid or duplicated subprotocol was specified");g.add(d)}i.headers["Sec-WebSocket-Protocol"]=t.join(",")}if(i.origin&&(i.protocolVersion<13?i.headers["Sec-WebSocket-Origin"]=i.origin:i.headers.Origin=i.origin),(n.username||n.password)&&(i.auth=`${n.username}:${n.password}`),a){const d=i.path.split(":");i.socketPath=d[0],i.path=d[1]}let v;if(i.followRedirects){if(r._redirects===0){r._originalIpc=a,r._originalSecure=o,r._originalHostOrSocketPath=a?i.socketPath:n.host;const d=s&&s.headers;if(s={...s,headers:{}},d)for(const[x,R]of Object.entries(d))s.headers[x.toLowerCase()]=R}else if(r.listenerCount("redirect")===0){const d=a?r._originalIpc?i.socketPath===r._originalHostOrSocketPath:!1:r._originalIpc?!1:n.host===r._originalHostOrSocketPath;(!d||r._originalSecure&&!o)&&(delete i.headers.authorization,delete i.headers.cookie,d||delete i.headers.host,i.auth=void 0)}i.auth&&!s.headers.authorization&&(s.headers.authorization="Basic "+Buffer.from(i.auth).toString("base64")),v=r._req=c(i),r._redirects&&r.emit("redirect",r.url,v)}else v=r._req=c(i);i.timeout&&v.on("timeout",()=>{T(r,v,"Opening handshake has timed out")}),v.on("error",d=>{v===null||v[Nt]||(v=r._req=null,de(r,d))}),v.on("response",d=>{const x=d.headers.location,R=d.statusCode;if(x&&i.followRedirects&&R>=300&&R<400){if(++r._redirects>i.maxRedirects){T(r,v,"Maximum redirects exceeded");return}v.abort();let D;try{D=new Pe(x,e)}catch{const N=new SyntaxError(`Invalid URL: ${x}`);de(r,N);return}Pt(r,D,t,s)}else r.emit("unexpected-response",v,d)||T(r,v,`Unexpected server response: ${d.statusCode}`)}),v.on("upgrade",(d,x,R)=>{if(r.emit("upgrade",d),r.readyState!==S.CONNECTING)return;if(v=r._req=null,d.headers.upgrade.toLowerCase()!=="websocket"){T(r,x,"Invalid Upgrade header");return}const D=Wr("sha1").update(h+jr).digest("base64");if(d.headers["sec-websocket-accept"]!==D){T(r,x,"Invalid Sec-WebSocket-Accept header");return}const $=d.headers["sec-websocket-protocol"];let N;if($!==void 0?g.size?g.has($)||(N="Server sent an invalid subprotocol"):N="Server sent a subprotocol but none was requested":g.size&&(N="Server sent no subprotocol"),N){T(r,x,N);return}$&&(r._protocol=$);const z=d.headers["sec-websocket-extensions"];if(z!==void 0){if(!p){T(r,x,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let Y;try{Y=Yr(z)}catch{T(r,x,"Invalid Sec-WebSocket-Extensions header");return}const K=Object.keys(Y);if(K.length!==1||K[0]!==M.extensionName){T(r,x,"Server indicated an extension that was not requested");return}try{p.accept(Y[M.extensionName])}catch{T(r,x,"Invalid Sec-WebSocket-Extensions header");return}r._extensions[M.extensionName]=p}r.setSocket(x,R,{generateMask:i.generateMask,maxPayload:i.maxPayload,skipUTF8Validation:i.skipUTF8Validation})}),i.finishRequest?i.finishRequest(v,r):v.end()}function de(r,e){r._readyState=S.CLOSING,r.emit("error",e),r.emitClose()}function Qr(r){return r.path=r.socketPath,Tt.connect(r)}function Jr(r){return r.path=void 0,!r.servername&&r.servername!==""&&(r.servername=Tt.isIP(r.host)?"":r.host),Mr.connect(r)}function T(r,e,t){r._readyState=S.CLOSING;const s=new Error(t);Error.captureStackTrace(s,T),e.setHeader?(e[Nt]=!0,e.abort(),e.socket&&!e.socket.destroyed&&e.socket.destroy(),process.nextTick(de,r,s)):(e.destroy(s),e.once("error",r.emit.bind(r,"error")),e.once("close",r.emitClose.bind(r)))}function Ue(r,e,t){if(e){const s=Kr(e).length;r._socket?r._sender._bufferedBytes+=s:r._bufferedAmount+=s}if(t){const s=new Error(`WebSocket is not open: readyState ${r.readyState} (${U[r.readyState]})`);process.nextTick(t,s)}}function es(r,e){const t=this[k];t._closeFrameReceived=!0,t._closeMessage=e,t._closeCode=r,t._socket[k]!==void 0&&(t._socket.removeListener("data",be),process.nextTick(Rt,t._socket),r===1005?t.close():t.close(r,e))}function ts(){const r=this[k];r.isPaused||r._socket.resume()}function rs(r){const e=this[k];e._socket[k]!==void 0&&(e._socket.removeListener("data",be),process.nextTick(Rt,e._socket),e.close(r[Vr])),e.emit("error",r)}function _t(){this[k].emitClose()}function ss(r,e){this[k].emit("message",r,e)}function is(r){const e=this[k];e.pong(r,!e._isServer,Ct),e.emit("ping",r)}function ns(r){this[k].emit("pong",r)}function Rt(r){r.resume()}function Bt(){const r=this[k];this.removeListener("close",Bt),this.removeListener("data",be),this.removeListener("end",Ut),r._readyState=S.CLOSING;let e;!this._readableState.endEmitted&&!r._closeFrameReceived&&!r._receiver._writableState.errorEmitted&&(e=r._socket.read())!==null&&r._receiver.write(e),r._receiver.end(),this[k]=void 0,clearTimeout(r._closeTimer),r._receiver._writableState.finished||r._receiver._writableState.errorEmitted?r.emitClose():(r._receiver.on("error",_t),r._receiver.on("finish",_t))}function be(r){this[k]._receiver.write(r)||this.pause()}function Ut(){const r=this[k];r._readyState=S.CLOSING,r._receiver.end(),this.end()}function $t(){const r=this[k];this.removeListener("error",$t),this.on("error",Ct),r&&(r._readyState=S.CLOSING,this.destroy())}const Os=ie(Lt),{tokenChars:os}=Se;function as(r){const e=new Set;let t=-1,s=-1,i=0;for(i;i<r.length;i++){const o=r.charCodeAt(i);if(s===-1&&os[o]===1)t===-1&&(t=i);else if(i!==0&&(o===32||o===9))s===-1&&t!==-1&&(s=i);else if(o===44){if(t===-1)throw new SyntaxError(`Unexpected character at index ${i}`);s===-1&&(s=i);const a=r.slice(t,s);if(e.has(a))throw new SyntaxError(`The "${a}" subprotocol is duplicated`);e.add(a),t=s=-1}else throw new SyntaxError(`Unexpected character at index ${i}`)}if(t===-1||s!==-1)throw new SyntaxError("Unexpected end of input");const n=r.slice(t,i);if(e.has(n))throw new SyntaxError(`The "${n}" subprotocol is duplicated`);return e.add(n),e}var ls={parse:as};const fs=b,ve=b,{createHash:cs}=b,pt=Ot,W=ye,hs=ls,us=Lt,{GUID:ds,kWebSocket:_s}=j,ps=/^[+/0-9A-Za-z]{22}==$/,mt=0,vt=1,It=2;class ms extends fs{constructor(e,t){if(super(),e={maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:us,...e},e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(e.port!=null?(this._server=ve.createServer((s,i)=>{const n=ve.STATUS_CODES[426];i.writeHead(426,{"Content-Length":n.length,"Content-Type":"text/plain"}),i.end(n)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){const s=this.emit.bind(this,"connection");this._removeListeners=gs(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(i,n,o)=>{this.handleUpgrade(i,n,o,s)}})}e.perMessageDeflate===!0&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=mt}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(this._state===It){e&&this.once("close",()=>{e(new Error("The server is not running"))}),process.nextTick(te,this);return}if(e&&this.once("close",e),this._state!==vt)if(this._state=vt,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(te,this):process.nextTick(te,this);else{const t=this._server;this._removeListeners(),this._removeListeners=this._server=null,t.close(()=>{te(this)})}}shouldHandle(e){if(this.options.path){const t=e.url.indexOf("?");if((t!==-1?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,i){t.on("error",gt);const n=e.headers["sec-websocket-key"],o=+e.headers["sec-websocket-version"];if(e.method!=="GET"){F(this,e,t,405,"Invalid HTTP method");return}if(e.headers.upgrade.toLowerCase()!=="websocket"){F(this,e,t,400,"Invalid Upgrade header");return}if(!n||!ps.test(n)){F(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(o!==8&&o!==13){F(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){se(t,400);return}const a=e.headers["sec-websocket-protocol"];let f=new Set;if(a!==void 0)try{f=hs.parse(a)}catch{F(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}const l=e.headers["sec-websocket-extensions"],h={};if(this.options.perMessageDeflate&&l!==void 0){const c=new W(this.options.perMessageDeflate,!0,this.options.maxPayload);try{const g=pt.parse(l);g[W.extensionName]&&(c.accept(g[W.extensionName]),h[W.extensionName]=c)}catch{F(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){const c={origin:e.headers[`${o===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(c,(g,p,v,d)=>{if(!g)return se(t,p||401,v,d);this.completeUpgrade(h,n,f,e,t,s,i)});return}if(!this.options.verifyClient(c))return se(t,401)}this.completeUpgrade(h,n,f,e,t,s,i)}completeUpgrade(e,t,s,i,n,o,a){if(!n.readable||!n.writable)return n.destroy();if(n[_s])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>mt)return se(n,503);const l=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${cs("sha1").update(t+ds).digest("base64")}`],h=new this.options.WebSocket(null);if(s.size){const c=this.options.handleProtocols?this.options.handleProtocols(s,i):s.values().next().value;c&&(l.push(`Sec-WebSocket-Protocol: ${c}`),h._protocol=c)}if(e[W.extensionName]){const c=e[W.extensionName].params,g=pt.format({[W.extensionName]:[c]});l.push(`Sec-WebSocket-Extensions: ${g}`),h._extensions=e}this.emit("headers",l,i),n.write(l.concat(`\r
`).join(`\r
`)),n.removeListener("error",gt),h.setSocket(n,o,{maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(h),h.on("close",()=>{this.clients.delete(h),this._shouldEmitClose&&!this.clients.size&&process.nextTick(te,this)})),a(h,i)}}var vs=ms;function gs(r,e){for(const t of Object.keys(e))r.on(t,e[t]);return function(){for(const s of Object.keys(e))r.removeListener(s,e[s])}}function te(r){r._state=It,r.emit("close")}function gt(){this.destroy()}function se(r,e,t,s){t=t||ve.STATUS_CODES[e],s={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(t),...s},r.once("finish",r.destroy),r.end(`HTTP/1.1 ${e} ${ve.STATUS_CODES[e]}\r
`+Object.keys(s).map(i=>`${i}: ${s[i]}`).join(`\r
`)+`\r
\r
`+t)}function F(r,e,t,s,i){if(r.listenerCount("wsClientError")){const n=new Error(i);Error.captureStackTrace(n,F),r.emit("wsClientError",n,t,e)}else se(t,s,i)}const Ts=ie(vs);export{ws as Receiver,ks as Sender,Os as WebSocket,Ts as WebSocketServer,Ss as createWebSocketStream,Os as default};
//# sourceMappingURL=wrapper-CviSselG-DCvi549i.js.map
