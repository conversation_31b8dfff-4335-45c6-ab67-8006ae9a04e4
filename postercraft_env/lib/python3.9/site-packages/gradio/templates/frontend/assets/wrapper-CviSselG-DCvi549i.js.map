{"version": 3, "file": "wrapper-CviSselG-DCvi549i.js", "sources": ["../../../../client/js/dist/wrapper-CviSselG.js"], "sourcesContent": ["import require$$0 from \"stream\";\nimport require$$0$2 from \"zlib\";\nimport require$$0$1 from \"fs\";\nimport require$$1$1 from \"path\";\nimport require$$2 from \"os\";\nimport require$$0$3 from \"buffer\";\nimport require$$3 from \"net\";\nimport require$$4 from \"tls\";\nimport require$$5 from \"crypto\";\nimport require$$0$4 from \"events\";\nimport require$$1$2 from \"https\";\nimport require$$2$1 from \"http\";\nimport require$$7 from \"url\";\nfunction getDefaultExportFromCjs(x) {\n  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, \"default\") ? x[\"default\"] : x;\n}\nfunction getAugmentedNamespace(n) {\n  if (n.__esModule)\n    return n;\n  var f = n.default;\n  if (typeof f == \"function\") {\n    var a = function a2() {\n      if (this instanceof a2) {\n        return Reflect.construct(f, arguments, this.constructor);\n      }\n      return f.apply(this, arguments);\n    };\n    a.prototype = f.prototype;\n  } else\n    a = {};\n  Object.defineProperty(a, \"__esModule\", { value: true });\n  Object.keys(n).forEach(function(k) {\n    var d = Object.getOwnPropertyDescriptor(n, k);\n    Object.defineProperty(a, k, d.get ? d : {\n      enumerable: true,\n      get: function() {\n        return n[k];\n      }\n    });\n  });\n  return a;\n}\nconst { Duplex } = require$$0;\nfunction emitClose$1(stream2) {\n  stream2.emit(\"close\");\n}\nfunction duplexOnEnd() {\n  if (!this.destroyed && this._writableState.finished) {\n    this.destroy();\n  }\n}\nfunction duplexOnError(err) {\n  this.removeListener(\"error\", duplexOnError);\n  this.destroy();\n  if (this.listenerCount(\"error\") === 0) {\n    this.emit(\"error\", err);\n  }\n}\nfunction createWebSocketStream(ws, options) {\n  let terminateOnDestroy = true;\n  const duplex = new Duplex({\n    ...options,\n    autoDestroy: false,\n    emitClose: false,\n    objectMode: false,\n    writableObjectMode: false\n  });\n  ws.on(\"message\", function message(msg, isBinary) {\n    const data = !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;\n    if (!duplex.push(data))\n      ws.pause();\n  });\n  ws.once(\"error\", function error2(err) {\n    if (duplex.destroyed)\n      return;\n    terminateOnDestroy = false;\n    duplex.destroy(err);\n  });\n  ws.once(\"close\", function close() {\n    if (duplex.destroyed)\n      return;\n    duplex.push(null);\n  });\n  duplex._destroy = function(err, callback) {\n    if (ws.readyState === ws.CLOSED) {\n      callback(err);\n      process.nextTick(emitClose$1, duplex);\n      return;\n    }\n    let called = false;\n    ws.once(\"error\", function error2(err2) {\n      called = true;\n      callback(err2);\n    });\n    ws.once(\"close\", function close() {\n      if (!called)\n        callback(err);\n      process.nextTick(emitClose$1, duplex);\n    });\n    if (terminateOnDestroy)\n      ws.terminate();\n  };\n  duplex._final = function(callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once(\"open\", function open() {\n        duplex._final(callback);\n      });\n      return;\n    }\n    if (ws._socket === null)\n      return;\n    if (ws._socket._writableState.finished) {\n      callback();\n      if (duplex._readableState.endEmitted)\n        duplex.destroy();\n    } else {\n      ws._socket.once(\"finish\", function finish() {\n        callback();\n      });\n      ws.close();\n    }\n  };\n  duplex._read = function() {\n    if (ws.isPaused)\n      ws.resume();\n  };\n  duplex._write = function(chunk, encoding, callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once(\"open\", function open() {\n        duplex._write(chunk, encoding, callback);\n      });\n      return;\n    }\n    ws.send(chunk, callback);\n  };\n  duplex.on(\"end\", duplexOnEnd);\n  duplex.on(\"error\", duplexOnError);\n  return duplex;\n}\nvar stream = createWebSocketStream;\nconst stream$1 = /* @__PURE__ */ getDefaultExportFromCjs(stream);\nvar bufferUtil$1 = { exports: {} };\nvar constants = {\n  BINARY_TYPES: [\"nodebuffer\", \"arraybuffer\", \"fragments\"],\n  EMPTY_BUFFER: Buffer.alloc(0),\n  GUID: \"258EAFA5-E914-47DA-95CA-C5AB0DC85B11\",\n  kForOnEventAttribute: Symbol(\"kIsForOnEventAttribute\"),\n  kListener: Symbol(\"kListener\"),\n  kStatusCode: Symbol(\"status-code\"),\n  kWebSocket: Symbol(\"websocket\"),\n  NOOP: () => {\n  }\n};\nvar bufferutil = { exports: {} };\nvar nodeGypBuild$1 = { exports: {} };\nfunction commonjsRequire(path) {\n  throw new Error('Could not dynamically require \"' + path + '\". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.');\n}\nvar nodeGypBuild;\nvar hasRequiredNodeGypBuild$1;\nfunction requireNodeGypBuild$1() {\n  if (hasRequiredNodeGypBuild$1)\n    return nodeGypBuild;\n  hasRequiredNodeGypBuild$1 = 1;\n  var fs = require$$0$1;\n  var path = require$$1$1;\n  var os = require$$2;\n  var runtimeRequire = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : commonjsRequire;\n  var vars = process.config && process.config.variables || {};\n  var prebuildsOnly = !!process.env.PREBUILDS_ONLY;\n  var abi = process.versions.modules;\n  var runtime = isElectron() ? \"electron\" : isNwjs() ? \"node-webkit\" : \"node\";\n  var arch = process.env.npm_config_arch || os.arch();\n  var platform = process.env.npm_config_platform || os.platform();\n  var libc = process.env.LIBC || (isAlpine(platform) ? \"musl\" : \"glibc\");\n  var armv = process.env.ARM_VERSION || (arch === \"arm64\" ? \"8\" : vars.arm_version) || \"\";\n  var uv = (process.versions.uv || \"\").split(\".\")[0];\n  nodeGypBuild = load;\n  function load(dir) {\n    return runtimeRequire(load.resolve(dir));\n  }\n  load.resolve = load.path = function(dir) {\n    dir = path.resolve(dir || \".\");\n    try {\n      var name = runtimeRequire(path.join(dir, \"package.json\")).name.toUpperCase().replace(/-/g, \"_\");\n      if (process.env[name + \"_PREBUILD\"])\n        dir = process.env[name + \"_PREBUILD\"];\n    } catch (err) {\n    }\n    if (!prebuildsOnly) {\n      var release = getFirst(path.join(dir, \"build/Release\"), matchBuild);\n      if (release)\n        return release;\n      var debug = getFirst(path.join(dir, \"build/Debug\"), matchBuild);\n      if (debug)\n        return debug;\n    }\n    var prebuild = resolve(dir);\n    if (prebuild)\n      return prebuild;\n    var nearby = resolve(path.dirname(process.execPath));\n    if (nearby)\n      return nearby;\n    var target = [\n      \"platform=\" + platform,\n      \"arch=\" + arch,\n      \"runtime=\" + runtime,\n      \"abi=\" + abi,\n      \"uv=\" + uv,\n      armv ? \"armv=\" + armv : \"\",\n      \"libc=\" + libc,\n      \"node=\" + process.versions.node,\n      process.versions.electron ? \"electron=\" + process.versions.electron : \"\",\n      typeof __webpack_require__ === \"function\" ? \"webpack=true\" : \"\"\n      // eslint-disable-line\n    ].filter(Boolean).join(\" \");\n    throw new Error(\"No native build was found for \" + target + \"\\n    loaded from: \" + dir + \"\\n\");\n    function resolve(dir2) {\n      var tuples = readdirSync(path.join(dir2, \"prebuilds\")).map(parseTuple);\n      var tuple = tuples.filter(matchTuple(platform, arch)).sort(compareTuples)[0];\n      if (!tuple)\n        return;\n      var prebuilds = path.join(dir2, \"prebuilds\", tuple.name);\n      var parsed = readdirSync(prebuilds).map(parseTags);\n      var candidates = parsed.filter(matchTags(runtime, abi));\n      var winner = candidates.sort(compareTags(runtime))[0];\n      if (winner)\n        return path.join(prebuilds, winner.file);\n    }\n  };\n  function readdirSync(dir) {\n    try {\n      return fs.readdirSync(dir);\n    } catch (err) {\n      return [];\n    }\n  }\n  function getFirst(dir, filter) {\n    var files = readdirSync(dir).filter(filter);\n    return files[0] && path.join(dir, files[0]);\n  }\n  function matchBuild(name) {\n    return /\\.node$/.test(name);\n  }\n  function parseTuple(name) {\n    var arr = name.split(\"-\");\n    if (arr.length !== 2)\n      return;\n    var platform2 = arr[0];\n    var architectures = arr[1].split(\"+\");\n    if (!platform2)\n      return;\n    if (!architectures.length)\n      return;\n    if (!architectures.every(Boolean))\n      return;\n    return { name, platform: platform2, architectures };\n  }\n  function matchTuple(platform2, arch2) {\n    return function(tuple) {\n      if (tuple == null)\n        return false;\n      if (tuple.platform !== platform2)\n        return false;\n      return tuple.architectures.includes(arch2);\n    };\n  }\n  function compareTuples(a, b) {\n    return a.architectures.length - b.architectures.length;\n  }\n  function parseTags(file) {\n    var arr = file.split(\".\");\n    var extension2 = arr.pop();\n    var tags = { file, specificity: 0 };\n    if (extension2 !== \"node\")\n      return;\n    for (var i = 0; i < arr.length; i++) {\n      var tag = arr[i];\n      if (tag === \"node\" || tag === \"electron\" || tag === \"node-webkit\") {\n        tags.runtime = tag;\n      } else if (tag === \"napi\") {\n        tags.napi = true;\n      } else if (tag.slice(0, 3) === \"abi\") {\n        tags.abi = tag.slice(3);\n      } else if (tag.slice(0, 2) === \"uv\") {\n        tags.uv = tag.slice(2);\n      } else if (tag.slice(0, 4) === \"armv\") {\n        tags.armv = tag.slice(4);\n      } else if (tag === \"glibc\" || tag === \"musl\") {\n        tags.libc = tag;\n      } else {\n        continue;\n      }\n      tags.specificity++;\n    }\n    return tags;\n  }\n  function matchTags(runtime2, abi2) {\n    return function(tags) {\n      if (tags == null)\n        return false;\n      if (tags.runtime !== runtime2 && !runtimeAgnostic(tags))\n        return false;\n      if (tags.abi !== abi2 && !tags.napi)\n        return false;\n      if (tags.uv && tags.uv !== uv)\n        return false;\n      if (tags.armv && tags.armv !== armv)\n        return false;\n      if (tags.libc && tags.libc !== libc)\n        return false;\n      return true;\n    };\n  }\n  function runtimeAgnostic(tags) {\n    return tags.runtime === \"node\" && tags.napi;\n  }\n  function compareTags(runtime2) {\n    return function(a, b) {\n      if (a.runtime !== b.runtime) {\n        return a.runtime === runtime2 ? -1 : 1;\n      } else if (a.abi !== b.abi) {\n        return a.abi ? -1 : 1;\n      } else if (a.specificity !== b.specificity) {\n        return a.specificity > b.specificity ? -1 : 1;\n      } else {\n        return 0;\n      }\n    };\n  }\n  function isNwjs() {\n    return !!(process.versions && process.versions.nw);\n  }\n  function isElectron() {\n    if (process.versions && process.versions.electron)\n      return true;\n    if (process.env.ELECTRON_RUN_AS_NODE)\n      return true;\n    return typeof window !== \"undefined\" && window.process && window.process.type === \"renderer\";\n  }\n  function isAlpine(platform2) {\n    return platform2 === \"linux\" && fs.existsSync(\"/etc/alpine-release\");\n  }\n  load.parseTags = parseTags;\n  load.matchTags = matchTags;\n  load.compareTags = compareTags;\n  load.parseTuple = parseTuple;\n  load.matchTuple = matchTuple;\n  load.compareTuples = compareTuples;\n  return nodeGypBuild;\n}\nvar hasRequiredNodeGypBuild;\nfunction requireNodeGypBuild() {\n  if (hasRequiredNodeGypBuild)\n    return nodeGypBuild$1.exports;\n  hasRequiredNodeGypBuild = 1;\n  if (typeof process.addon === \"function\") {\n    nodeGypBuild$1.exports = process.addon.bind(process);\n  } else {\n    nodeGypBuild$1.exports = requireNodeGypBuild$1();\n  }\n  return nodeGypBuild$1.exports;\n}\nvar fallback;\nvar hasRequiredFallback;\nfunction requireFallback() {\n  if (hasRequiredFallback)\n    return fallback;\n  hasRequiredFallback = 1;\n  const mask2 = (source, mask3, output, offset, length) => {\n    for (var i = 0; i < length; i++) {\n      output[offset + i] = source[i] ^ mask3[i & 3];\n    }\n  };\n  const unmask2 = (buffer, mask3) => {\n    const length = buffer.length;\n    for (var i = 0; i < length; i++) {\n      buffer[i] ^= mask3[i & 3];\n    }\n  };\n  fallback = { mask: mask2, unmask: unmask2 };\n  return fallback;\n}\nvar hasRequiredBufferutil;\nfunction requireBufferutil() {\n  if (hasRequiredBufferutil)\n    return bufferutil.exports;\n  hasRequiredBufferutil = 1;\n  try {\n    bufferutil.exports = requireNodeGypBuild()(__dirname);\n  } catch (e) {\n    bufferutil.exports = requireFallback();\n  }\n  return bufferutil.exports;\n}\nvar unmask$1;\nvar mask;\nconst { EMPTY_BUFFER: EMPTY_BUFFER$3 } = constants;\nconst FastBuffer$2 = Buffer[Symbol.species];\nfunction concat$1(list, totalLength) {\n  if (list.length === 0)\n    return EMPTY_BUFFER$3;\n  if (list.length === 1)\n    return list[0];\n  const target = Buffer.allocUnsafe(totalLength);\n  let offset = 0;\n  for (let i = 0; i < list.length; i++) {\n    const buf = list[i];\n    target.set(buf, offset);\n    offset += buf.length;\n  }\n  if (offset < totalLength) {\n    return new FastBuffer$2(target.buffer, target.byteOffset, offset);\n  }\n  return target;\n}\nfunction _mask(source, mask2, output, offset, length) {\n  for (let i = 0; i < length; i++) {\n    output[offset + i] = source[i] ^ mask2[i & 3];\n  }\n}\nfunction _unmask(buffer, mask2) {\n  for (let i = 0; i < buffer.length; i++) {\n    buffer[i] ^= mask2[i & 3];\n  }\n}\nfunction toArrayBuffer$1(buf) {\n  if (buf.length === buf.buffer.byteLength) {\n    return buf.buffer;\n  }\n  return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\nfunction toBuffer$2(data) {\n  toBuffer$2.readOnly = true;\n  if (Buffer.isBuffer(data))\n    return data;\n  let buf;\n  if (data instanceof ArrayBuffer) {\n    buf = new FastBuffer$2(data);\n  } else if (ArrayBuffer.isView(data)) {\n    buf = new FastBuffer$2(data.buffer, data.byteOffset, data.byteLength);\n  } else {\n    buf = Buffer.from(data);\n    toBuffer$2.readOnly = false;\n  }\n  return buf;\n}\nbufferUtil$1.exports = {\n  concat: concat$1,\n  mask: _mask,\n  toArrayBuffer: toArrayBuffer$1,\n  toBuffer: toBuffer$2,\n  unmask: _unmask\n};\nif (!process.env.WS_NO_BUFFER_UTIL) {\n  try {\n    const bufferUtil2 = requireBufferutil();\n    mask = bufferUtil$1.exports.mask = function(source, mask2, output, offset, length) {\n      if (length < 48)\n        _mask(source, mask2, output, offset, length);\n      else\n        bufferUtil2.mask(source, mask2, output, offset, length);\n    };\n    unmask$1 = bufferUtil$1.exports.unmask = function(buffer, mask2) {\n      if (buffer.length < 32)\n        _unmask(buffer, mask2);\n      else\n        bufferUtil2.unmask(buffer, mask2);\n    };\n  } catch (e) {\n  }\n}\nvar bufferUtilExports = bufferUtil$1.exports;\nconst kDone = Symbol(\"kDone\");\nconst kRun = Symbol(\"kRun\");\nlet Limiter$1 = class Limiter {\n  /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */\n  constructor(concurrency) {\n    this[kDone] = () => {\n      this.pending--;\n      this[kRun]();\n    };\n    this.concurrency = concurrency || Infinity;\n    this.jobs = [];\n    this.pending = 0;\n  }\n  /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */\n  add(job) {\n    this.jobs.push(job);\n    this[kRun]();\n  }\n  /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */\n  [kRun]() {\n    if (this.pending === this.concurrency)\n      return;\n    if (this.jobs.length) {\n      const job = this.jobs.shift();\n      this.pending++;\n      job(this[kDone]);\n    }\n  }\n};\nvar limiter = Limiter$1;\nconst zlib = require$$0$2;\nconst bufferUtil = bufferUtilExports;\nconst Limiter2 = limiter;\nconst { kStatusCode: kStatusCode$2 } = constants;\nconst FastBuffer$1 = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([0, 0, 255, 255]);\nconst kPerMessageDeflate = Symbol(\"permessage-deflate\");\nconst kTotalLength = Symbol(\"total-length\");\nconst kCallback = Symbol(\"callback\");\nconst kBuffers = Symbol(\"buffers\");\nconst kError$1 = Symbol(\"error\");\nlet zlibLimiter;\nlet PerMessageDeflate$4 = class PerMessageDeflate {\n  /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */\n  constructor(options, isServer, maxPayload) {\n    this._maxPayload = maxPayload | 0;\n    this._options = options || {};\n    this._threshold = this._options.threshold !== void 0 ? this._options.threshold : 1024;\n    this._isServer = !!isServer;\n    this._deflate = null;\n    this._inflate = null;\n    this.params = null;\n    if (!zlibLimiter) {\n      const concurrency = this._options.concurrencyLimit !== void 0 ? this._options.concurrencyLimit : 10;\n      zlibLimiter = new Limiter2(concurrency);\n    }\n  }\n  /**\n   * @type {String}\n   */\n  static get extensionName() {\n    return \"permessage-deflate\";\n  }\n  /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */\n  offer() {\n    const params = {};\n    if (this._options.serverNoContextTakeover) {\n      params.server_no_context_takeover = true;\n    }\n    if (this._options.clientNoContextTakeover) {\n      params.client_no_context_takeover = true;\n    }\n    if (this._options.serverMaxWindowBits) {\n      params.server_max_window_bits = this._options.serverMaxWindowBits;\n    }\n    if (this._options.clientMaxWindowBits) {\n      params.client_max_window_bits = this._options.clientMaxWindowBits;\n    } else if (this._options.clientMaxWindowBits == null) {\n      params.client_max_window_bits = true;\n    }\n    return params;\n  }\n  /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */\n  accept(configurations) {\n    configurations = this.normalizeParams(configurations);\n    this.params = this._isServer ? this.acceptAsServer(configurations) : this.acceptAsClient(configurations);\n    return this.params;\n  }\n  /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */\n  cleanup() {\n    if (this._inflate) {\n      this._inflate.close();\n      this._inflate = null;\n    }\n    if (this._deflate) {\n      const callback = this._deflate[kCallback];\n      this._deflate.close();\n      this._deflate = null;\n      if (callback) {\n        callback(\n          new Error(\n            \"The deflate stream was closed while data was being processed\"\n          )\n        );\n      }\n    }\n  }\n  /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsServer(offers) {\n    const opts = this._options;\n    const accepted = offers.find((params) => {\n      if (opts.serverNoContextTakeover === false && params.server_no_context_takeover || params.server_max_window_bits && (opts.serverMaxWindowBits === false || typeof opts.serverMaxWindowBits === \"number\" && opts.serverMaxWindowBits > params.server_max_window_bits) || typeof opts.clientMaxWindowBits === \"number\" && !params.client_max_window_bits) {\n        return false;\n      }\n      return true;\n    });\n    if (!accepted) {\n      throw new Error(\"None of the extension offers can be accepted\");\n    }\n    if (opts.serverNoContextTakeover) {\n      accepted.server_no_context_takeover = true;\n    }\n    if (opts.clientNoContextTakeover) {\n      accepted.client_no_context_takeover = true;\n    }\n    if (typeof opts.serverMaxWindowBits === \"number\") {\n      accepted.server_max_window_bits = opts.serverMaxWindowBits;\n    }\n    if (typeof opts.clientMaxWindowBits === \"number\") {\n      accepted.client_max_window_bits = opts.clientMaxWindowBits;\n    } else if (accepted.client_max_window_bits === true || opts.clientMaxWindowBits === false) {\n      delete accepted.client_max_window_bits;\n    }\n    return accepted;\n  }\n  /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsClient(response) {\n    const params = response[0];\n    if (this._options.clientNoContextTakeover === false && params.client_no_context_takeover) {\n      throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n    }\n    if (!params.client_max_window_bits) {\n      if (typeof this._options.clientMaxWindowBits === \"number\") {\n        params.client_max_window_bits = this._options.clientMaxWindowBits;\n      }\n    } else if (this._options.clientMaxWindowBits === false || typeof this._options.clientMaxWindowBits === \"number\" && params.client_max_window_bits > this._options.clientMaxWindowBits) {\n      throw new Error(\n        'Unexpected or invalid parameter \"client_max_window_bits\"'\n      );\n    }\n    return params;\n  }\n  /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */\n  normalizeParams(configurations) {\n    configurations.forEach((params) => {\n      Object.keys(params).forEach((key) => {\n        let value = params[key];\n        if (value.length > 1) {\n          throw new Error(`Parameter \"${key}\" must have only a single value`);\n        }\n        value = value[0];\n        if (key === \"client_max_window_bits\") {\n          if (value !== true) {\n            const num = +value;\n            if (!Number.isInteger(num) || num < 8 || num > 15) {\n              throw new TypeError(\n                `Invalid value for parameter \"${key}\": ${value}`\n              );\n            }\n            value = num;\n          } else if (!this._isServer) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else if (key === \"server_max_window_bits\") {\n          const num = +value;\n          if (!Number.isInteger(num) || num < 8 || num > 15) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n          value = num;\n        } else if (key === \"client_no_context_takeover\" || key === \"server_no_context_takeover\") {\n          if (value !== true) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else {\n          throw new Error(`Unknown parameter \"${key}\"`);\n        }\n        params[key] = value;\n      });\n    });\n    return configurations;\n  }\n  /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  decompress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._decompress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n  /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  compress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._compress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n  /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _decompress(data, fin, callback) {\n    const endpoint = this._isServer ? \"client\" : \"server\";\n    if (!this._inflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n      this._inflate = zlib.createInflateRaw({\n        ...this._options.zlibInflateOptions,\n        windowBits\n      });\n      this._inflate[kPerMessageDeflate] = this;\n      this._inflate[kTotalLength] = 0;\n      this._inflate[kBuffers] = [];\n      this._inflate.on(\"error\", inflateOnError);\n      this._inflate.on(\"data\", inflateOnData);\n    }\n    this._inflate[kCallback] = callback;\n    this._inflate.write(data);\n    if (fin)\n      this._inflate.write(TRAILER);\n    this._inflate.flush(() => {\n      const err = this._inflate[kError$1];\n      if (err) {\n        this._inflate.close();\n        this._inflate = null;\n        callback(err);\n        return;\n      }\n      const data2 = bufferUtil.concat(\n        this._inflate[kBuffers],\n        this._inflate[kTotalLength]\n      );\n      if (this._inflate._readableState.endEmitted) {\n        this._inflate.close();\n        this._inflate = null;\n      } else {\n        this._inflate[kTotalLength] = 0;\n        this._inflate[kBuffers] = [];\n        if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n          this._inflate.reset();\n        }\n      }\n      callback(null, data2);\n    });\n  }\n  /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _compress(data, fin, callback) {\n    const endpoint = this._isServer ? \"server\" : \"client\";\n    if (!this._deflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n      this._deflate = zlib.createDeflateRaw({\n        ...this._options.zlibDeflateOptions,\n        windowBits\n      });\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n      this._deflate.on(\"data\", deflateOnData);\n    }\n    this._deflate[kCallback] = callback;\n    this._deflate.write(data);\n    this._deflate.flush(zlib.Z_SYNC_FLUSH, () => {\n      if (!this._deflate) {\n        return;\n      }\n      let data2 = bufferUtil.concat(\n        this._deflate[kBuffers],\n        this._deflate[kTotalLength]\n      );\n      if (fin) {\n        data2 = new FastBuffer$1(data2.buffer, data2.byteOffset, data2.length - 4);\n      }\n      this._deflate[kCallback] = null;\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n      if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n        this._deflate.reset();\n      }\n      callback(null, data2);\n    });\n  }\n};\nvar permessageDeflate = PerMessageDeflate$4;\nfunction deflateOnData(chunk) {\n  this[kBuffers].push(chunk);\n  this[kTotalLength] += chunk.length;\n}\nfunction inflateOnData(chunk) {\n  this[kTotalLength] += chunk.length;\n  if (this[kPerMessageDeflate]._maxPayload < 1 || this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload) {\n    this[kBuffers].push(chunk);\n    return;\n  }\n  this[kError$1] = new RangeError(\"Max payload size exceeded\");\n  this[kError$1].code = \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\";\n  this[kError$1][kStatusCode$2] = 1009;\n  this.removeListener(\"data\", inflateOnData);\n  this.reset();\n}\nfunction inflateOnError(err) {\n  this[kPerMessageDeflate]._inflate = null;\n  err[kStatusCode$2] = 1007;\n  this[kCallback](err);\n}\nvar validation = { exports: {} };\nconst __viteOptionalPeerDep_utf8Validate_ws = {};\nconst __viteOptionalPeerDep_utf8Validate_ws$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  default: __viteOptionalPeerDep_utf8Validate_ws\n}, Symbol.toStringTag, { value: \"Module\" }));\nconst require$$1 = /* @__PURE__ */ getAugmentedNamespace(__viteOptionalPeerDep_utf8Validate_ws$1);\nvar isValidUTF8_1;\nconst { isUtf8 } = require$$0$3;\nconst tokenChars$2 = [\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  // 0 - 15\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  // 16 - 31\n  0,\n  1,\n  0,\n  1,\n  1,\n  1,\n  1,\n  1,\n  0,\n  0,\n  1,\n  1,\n  0,\n  1,\n  1,\n  0,\n  // 32 - 47\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  0,\n  0,\n  0,\n  0,\n  0,\n  0,\n  // 48 - 63\n  0,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  // 64 - 79\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  0,\n  0,\n  0,\n  1,\n  1,\n  // 80 - 95\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  // 96 - 111\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  1,\n  0,\n  1,\n  0,\n  1,\n  0\n  // 112 - 127\n];\nfunction isValidStatusCode$2(code) {\n  return code >= 1e3 && code <= 1014 && code !== 1004 && code !== 1005 && code !== 1006 || code >= 3e3 && code <= 4999;\n}\nfunction _isValidUTF8(buf) {\n  const len = buf.length;\n  let i = 0;\n  while (i < len) {\n    if ((buf[i] & 128) === 0) {\n      i++;\n    } else if ((buf[i] & 224) === 192) {\n      if (i + 1 === len || (buf[i + 1] & 192) !== 128 || (buf[i] & 254) === 192) {\n        return false;\n      }\n      i += 2;\n    } else if ((buf[i] & 240) === 224) {\n      if (i + 2 >= len || (buf[i + 1] & 192) !== 128 || (buf[i + 2] & 192) !== 128 || buf[i] === 224 && (buf[i + 1] & 224) === 128 || // Overlong\n      buf[i] === 237 && (buf[i + 1] & 224) === 160) {\n        return false;\n      }\n      i += 3;\n    } else if ((buf[i] & 248) === 240) {\n      if (i + 3 >= len || (buf[i + 1] & 192) !== 128 || (buf[i + 2] & 192) !== 128 || (buf[i + 3] & 192) !== 128 || buf[i] === 240 && (buf[i + 1] & 240) === 128 || // Overlong\n      buf[i] === 244 && buf[i + 1] > 143 || buf[i] > 244) {\n        return false;\n      }\n      i += 4;\n    } else {\n      return false;\n    }\n  }\n  return true;\n}\nvalidation.exports = {\n  isValidStatusCode: isValidStatusCode$2,\n  isValidUTF8: _isValidUTF8,\n  tokenChars: tokenChars$2\n};\nif (isUtf8) {\n  isValidUTF8_1 = validation.exports.isValidUTF8 = function(buf) {\n    return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n  };\n} else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n  try {\n    const isValidUTF82 = require$$1;\n    isValidUTF8_1 = validation.exports.isValidUTF8 = function(buf) {\n      return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF82(buf);\n    };\n  } catch (e) {\n  }\n}\nvar validationExports = validation.exports;\nconst { Writable } = require$$0;\nconst PerMessageDeflate$3 = permessageDeflate;\nconst {\n  BINARY_TYPES: BINARY_TYPES$1,\n  EMPTY_BUFFER: EMPTY_BUFFER$2,\n  kStatusCode: kStatusCode$1,\n  kWebSocket: kWebSocket$2\n} = constants;\nconst { concat, toArrayBuffer, unmask } = bufferUtilExports;\nconst { isValidStatusCode: isValidStatusCode$1, isValidUTF8 } = validationExports;\nconst FastBuffer = Buffer[Symbol.species];\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nlet Receiver$1 = class Receiver extends Writable {\n  /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */\n  constructor(options = {}) {\n    super();\n    this._binaryType = options.binaryType || BINARY_TYPES$1[0];\n    this._extensions = options.extensions || {};\n    this._isServer = !!options.isServer;\n    this._maxPayload = options.maxPayload | 0;\n    this._skipUTF8Validation = !!options.skipUTF8Validation;\n    this[kWebSocket$2] = void 0;\n    this._bufferedBytes = 0;\n    this._buffers = [];\n    this._compressed = false;\n    this._payloadLength = 0;\n    this._mask = void 0;\n    this._fragmented = 0;\n    this._masked = false;\n    this._fin = false;\n    this._opcode = 0;\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragments = [];\n    this._state = GET_INFO;\n    this._loop = false;\n  }\n  /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */\n  _write(chunk, encoding, cb) {\n    if (this._opcode === 8 && this._state == GET_INFO)\n      return cb();\n    this._bufferedBytes += chunk.length;\n    this._buffers.push(chunk);\n    this.startLoop(cb);\n  }\n  /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */\n  consume(n) {\n    this._bufferedBytes -= n;\n    if (n === this._buffers[0].length)\n      return this._buffers.shift();\n    if (n < this._buffers[0].length) {\n      const buf = this._buffers[0];\n      this._buffers[0] = new FastBuffer(\n        buf.buffer,\n        buf.byteOffset + n,\n        buf.length - n\n      );\n      return new FastBuffer(buf.buffer, buf.byteOffset, n);\n    }\n    const dst = Buffer.allocUnsafe(n);\n    do {\n      const buf = this._buffers[0];\n      const offset = dst.length - n;\n      if (n >= buf.length) {\n        dst.set(this._buffers.shift(), offset);\n      } else {\n        dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n        this._buffers[0] = new FastBuffer(\n          buf.buffer,\n          buf.byteOffset + n,\n          buf.length - n\n        );\n      }\n      n -= buf.length;\n    } while (n > 0);\n    return dst;\n  }\n  /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  startLoop(cb) {\n    let err;\n    this._loop = true;\n    do {\n      switch (this._state) {\n        case GET_INFO:\n          err = this.getInfo();\n          break;\n        case GET_PAYLOAD_LENGTH_16:\n          err = this.getPayloadLength16();\n          break;\n        case GET_PAYLOAD_LENGTH_64:\n          err = this.getPayloadLength64();\n          break;\n        case GET_MASK:\n          this.getMask();\n          break;\n        case GET_DATA:\n          err = this.getData(cb);\n          break;\n        default:\n          this._loop = false;\n          return;\n      }\n    } while (this._loop);\n    cb(err);\n  }\n  /**\n   * Reads the first two bytes of a frame.\n   *\n   * @return {(RangeError|undefined)} A possible error\n   * @private\n   */\n  getInfo() {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n    const buf = this.consume(2);\n    if ((buf[0] & 48) !== 0) {\n      this._loop = false;\n      return error(\n        RangeError,\n        \"RSV2 and RSV3 must be clear\",\n        true,\n        1002,\n        \"WS_ERR_UNEXPECTED_RSV_2_3\"\n      );\n    }\n    const compressed = (buf[0] & 64) === 64;\n    if (compressed && !this._extensions[PerMessageDeflate$3.extensionName]) {\n      this._loop = false;\n      return error(\n        RangeError,\n        \"RSV1 must be clear\",\n        true,\n        1002,\n        \"WS_ERR_UNEXPECTED_RSV_1\"\n      );\n    }\n    this._fin = (buf[0] & 128) === 128;\n    this._opcode = buf[0] & 15;\n    this._payloadLength = buf[1] & 127;\n    if (this._opcode === 0) {\n      if (compressed) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"RSV1 must be clear\",\n          true,\n          1002,\n          \"WS_ERR_UNEXPECTED_RSV_1\"\n        );\n      }\n      if (!this._fragmented) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"invalid opcode 0\",\n          true,\n          1002,\n          \"WS_ERR_INVALID_OPCODE\"\n        );\n      }\n      this._opcode = this._fragmented;\n    } else if (this._opcode === 1 || this._opcode === 2) {\n      if (this._fragmented) {\n        this._loop = false;\n        return error(\n          RangeError,\n          `invalid opcode ${this._opcode}`,\n          true,\n          1002,\n          \"WS_ERR_INVALID_OPCODE\"\n        );\n      }\n      this._compressed = compressed;\n    } else if (this._opcode > 7 && this._opcode < 11) {\n      if (!this._fin) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"FIN must be set\",\n          true,\n          1002,\n          \"WS_ERR_EXPECTED_FIN\"\n        );\n      }\n      if (compressed) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"RSV1 must be clear\",\n          true,\n          1002,\n          \"WS_ERR_UNEXPECTED_RSV_1\"\n        );\n      }\n      if (this._payloadLength > 125 || this._opcode === 8 && this._payloadLength === 1) {\n        this._loop = false;\n        return error(\n          RangeError,\n          `invalid payload length ${this._payloadLength}`,\n          true,\n          1002,\n          \"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH\"\n        );\n      }\n    } else {\n      this._loop = false;\n      return error(\n        RangeError,\n        `invalid opcode ${this._opcode}`,\n        true,\n        1002,\n        \"WS_ERR_INVALID_OPCODE\"\n      );\n    }\n    if (!this._fin && !this._fragmented)\n      this._fragmented = this._opcode;\n    this._masked = (buf[1] & 128) === 128;\n    if (this._isServer) {\n      if (!this._masked) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"MASK must be set\",\n          true,\n          1002,\n          \"WS_ERR_EXPECTED_MASK\"\n        );\n      }\n    } else if (this._masked) {\n      this._loop = false;\n      return error(\n        RangeError,\n        \"MASK must be clear\",\n        true,\n        1002,\n        \"WS_ERR_UNEXPECTED_MASK\"\n      );\n    }\n    if (this._payloadLength === 126)\n      this._state = GET_PAYLOAD_LENGTH_16;\n    else if (this._payloadLength === 127)\n      this._state = GET_PAYLOAD_LENGTH_64;\n    else\n      return this.haveLength();\n  }\n  /**\n   * Gets extended payload length (7+16).\n   *\n   * @return {(RangeError|undefined)} A possible error\n   * @private\n   */\n  getPayloadLength16() {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n    this._payloadLength = this.consume(2).readUInt16BE(0);\n    return this.haveLength();\n  }\n  /**\n   * Gets extended payload length (7+64).\n   *\n   * @return {(RangeError|undefined)} A possible error\n   * @private\n   */\n  getPayloadLength64() {\n    if (this._bufferedBytes < 8) {\n      this._loop = false;\n      return;\n    }\n    const buf = this.consume(8);\n    const num = buf.readUInt32BE(0);\n    if (num > Math.pow(2, 53 - 32) - 1) {\n      this._loop = false;\n      return error(\n        RangeError,\n        \"Unsupported WebSocket frame: payload length > 2^53 - 1\",\n        false,\n        1009,\n        \"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH\"\n      );\n    }\n    this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n    return this.haveLength();\n  }\n  /**\n   * Payload length has been read.\n   *\n   * @return {(RangeError|undefined)} A possible error\n   * @private\n   */\n  haveLength() {\n    if (this._payloadLength && this._opcode < 8) {\n      this._totalPayloadLength += this._payloadLength;\n      if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n        this._loop = false;\n        return error(\n          RangeError,\n          \"Max payload size exceeded\",\n          false,\n          1009,\n          \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\"\n        );\n      }\n    }\n    if (this._masked)\n      this._state = GET_MASK;\n    else\n      this._state = GET_DATA;\n  }\n  /**\n   * Reads mask bytes.\n   *\n   * @private\n   */\n  getMask() {\n    if (this._bufferedBytes < 4) {\n      this._loop = false;\n      return;\n    }\n    this._mask = this.consume(4);\n    this._state = GET_DATA;\n  }\n  /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */\n  getData(cb) {\n    let data = EMPTY_BUFFER$2;\n    if (this._payloadLength) {\n      if (this._bufferedBytes < this._payloadLength) {\n        this._loop = false;\n        return;\n      }\n      data = this.consume(this._payloadLength);\n      if (this._masked && (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0) {\n        unmask(data, this._mask);\n      }\n    }\n    if (this._opcode > 7)\n      return this.controlMessage(data);\n    if (this._compressed) {\n      this._state = INFLATING;\n      this.decompress(data, cb);\n      return;\n    }\n    if (data.length) {\n      this._messageLength = this._totalPayloadLength;\n      this._fragments.push(data);\n    }\n    return this.dataMessage();\n  }\n  /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */\n  decompress(data, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate$3.extensionName];\n    perMessageDeflate.decompress(data, this._fin, (err, buf) => {\n      if (err)\n        return cb(err);\n      if (buf.length) {\n        this._messageLength += buf.length;\n        if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n          return cb(\n            error(\n              RangeError,\n              \"Max payload size exceeded\",\n              false,\n              1009,\n              \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\"\n            )\n          );\n        }\n        this._fragments.push(buf);\n      }\n      const er = this.dataMessage();\n      if (er)\n        return cb(er);\n      this.startLoop(cb);\n    });\n  }\n  /**\n   * Handles a data message.\n   *\n   * @return {(Error|undefined)} A possible error\n   * @private\n   */\n  dataMessage() {\n    if (this._fin) {\n      const messageLength = this._messageLength;\n      const fragments = this._fragments;\n      this._totalPayloadLength = 0;\n      this._messageLength = 0;\n      this._fragmented = 0;\n      this._fragments = [];\n      if (this._opcode === 2) {\n        let data;\n        if (this._binaryType === \"nodebuffer\") {\n          data = concat(fragments, messageLength);\n        } else if (this._binaryType === \"arraybuffer\") {\n          data = toArrayBuffer(concat(fragments, messageLength));\n        } else {\n          data = fragments;\n        }\n        this.emit(\"message\", data, true);\n      } else {\n        const buf = concat(fragments, messageLength);\n        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n          this._loop = false;\n          return error(\n            Error,\n            \"invalid UTF-8 sequence\",\n            true,\n            1007,\n            \"WS_ERR_INVALID_UTF8\"\n          );\n        }\n        this.emit(\"message\", buf, false);\n      }\n    }\n    this._state = GET_INFO;\n  }\n  /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */\n  controlMessage(data) {\n    if (this._opcode === 8) {\n      this._loop = false;\n      if (data.length === 0) {\n        this.emit(\"conclude\", 1005, EMPTY_BUFFER$2);\n        this.end();\n      } else {\n        const code = data.readUInt16BE(0);\n        if (!isValidStatusCode$1(code)) {\n          return error(\n            RangeError,\n            `invalid status code ${code}`,\n            true,\n            1002,\n            \"WS_ERR_INVALID_CLOSE_CODE\"\n          );\n        }\n        const buf = new FastBuffer(\n          data.buffer,\n          data.byteOffset + 2,\n          data.length - 2\n        );\n        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n          return error(\n            Error,\n            \"invalid UTF-8 sequence\",\n            true,\n            1007,\n            \"WS_ERR_INVALID_UTF8\"\n          );\n        }\n        this.emit(\"conclude\", code, buf);\n        this.end();\n      }\n    } else if (this._opcode === 9) {\n      this.emit(\"ping\", data);\n    } else {\n      this.emit(\"pong\", data);\n    }\n    this._state = GET_INFO;\n  }\n};\nvar receiver = Receiver$1;\nfunction error(ErrorCtor, message, prefix, statusCode, errorCode) {\n  const err = new ErrorCtor(\n    prefix ? `Invalid WebSocket frame: ${message}` : message\n  );\n  Error.captureStackTrace(err, error);\n  err.code = errorCode;\n  err[kStatusCode$1] = statusCode;\n  return err;\n}\nconst receiver$1 = /* @__PURE__ */ getDefaultExportFromCjs(receiver);\nconst { randomFillSync } = require$$5;\nconst PerMessageDeflate$2 = permessageDeflate;\nconst { EMPTY_BUFFER: EMPTY_BUFFER$1 } = constants;\nconst { isValidStatusCode } = validationExports;\nconst { mask: applyMask, toBuffer: toBuffer$1 } = bufferUtilExports;\nconst kByteLength = Symbol(\"kByteLength\");\nconst maskBuffer = Buffer.alloc(4);\nlet Sender$1 = class Sender {\n  /**\n   * Creates a Sender instance.\n   *\n   * @param {(net.Socket|tls.Socket)} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */\n  constructor(socket, extensions, generateMask) {\n    this._extensions = extensions || {};\n    if (generateMask) {\n      this._generateMask = generateMask;\n      this._maskBuffer = Buffer.alloc(4);\n    }\n    this._socket = socket;\n    this._firstFragment = true;\n    this._compress = false;\n    this._bufferedBytes = 0;\n    this._deflating = false;\n    this._queue = [];\n  }\n  /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */\n  static frame(data, options) {\n    let mask2;\n    let merge = false;\n    let offset = 2;\n    let skipMasking = false;\n    if (options.mask) {\n      mask2 = options.maskBuffer || maskBuffer;\n      if (options.generateMask) {\n        options.generateMask(mask2);\n      } else {\n        randomFillSync(mask2, 0, 4);\n      }\n      skipMasking = (mask2[0] | mask2[1] | mask2[2] | mask2[3]) === 0;\n      offset = 6;\n    }\n    let dataLength;\n    if (typeof data === \"string\") {\n      if ((!options.mask || skipMasking) && options[kByteLength] !== void 0) {\n        dataLength = options[kByteLength];\n      } else {\n        data = Buffer.from(data);\n        dataLength = data.length;\n      }\n    } else {\n      dataLength = data.length;\n      merge = options.mask && options.readOnly && !skipMasking;\n    }\n    let payloadLength = dataLength;\n    if (dataLength >= 65536) {\n      offset += 8;\n      payloadLength = 127;\n    } else if (dataLength > 125) {\n      offset += 2;\n      payloadLength = 126;\n    }\n    const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n    target[0] = options.fin ? options.opcode | 128 : options.opcode;\n    if (options.rsv1)\n      target[0] |= 64;\n    target[1] = payloadLength;\n    if (payloadLength === 126) {\n      target.writeUInt16BE(dataLength, 2);\n    } else if (payloadLength === 127) {\n      target[2] = target[3] = 0;\n      target.writeUIntBE(dataLength, 4, 6);\n    }\n    if (!options.mask)\n      return [target, data];\n    target[1] |= 128;\n    target[offset - 4] = mask2[0];\n    target[offset - 3] = mask2[1];\n    target[offset - 2] = mask2[2];\n    target[offset - 1] = mask2[3];\n    if (skipMasking)\n      return [target, data];\n    if (merge) {\n      applyMask(data, mask2, target, offset, dataLength);\n      return [target];\n    }\n    applyMask(data, mask2, data, 0, dataLength);\n    return [target, data];\n  }\n  /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  close(code, data, mask2, cb) {\n    let buf;\n    if (code === void 0) {\n      buf = EMPTY_BUFFER$1;\n    } else if (typeof code !== \"number\" || !isValidStatusCode(code)) {\n      throw new TypeError(\"First argument must be a valid error code number\");\n    } else if (data === void 0 || !data.length) {\n      buf = Buffer.allocUnsafe(2);\n      buf.writeUInt16BE(code, 0);\n    } else {\n      const length = Buffer.byteLength(data);\n      if (length > 123) {\n        throw new RangeError(\"The message must not be greater than 123 bytes\");\n      }\n      buf = Buffer.allocUnsafe(2 + length);\n      buf.writeUInt16BE(code, 0);\n      if (typeof data === \"string\") {\n        buf.write(data, 2);\n      } else {\n        buf.set(data, 2);\n      }\n    }\n    const options = {\n      [kByteLength]: buf.length,\n      fin: true,\n      generateMask: this._generateMask,\n      mask: mask2,\n      maskBuffer: this._maskBuffer,\n      opcode: 8,\n      readOnly: false,\n      rsv1: false\n    };\n    if (this._deflating) {\n      this.enqueue([this.dispatch, buf, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(buf, options), cb);\n    }\n  }\n  /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  ping(data, mask2, cb) {\n    let byteLength;\n    let readOnly;\n    if (typeof data === \"string\") {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer$1(data);\n      byteLength = data.length;\n      readOnly = toBuffer$1.readOnly;\n    }\n    if (byteLength > 125) {\n      throw new RangeError(\"The data size must not be greater than 125 bytes\");\n    }\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask: mask2,\n      maskBuffer: this._maskBuffer,\n      opcode: 9,\n      readOnly,\n      rsv1: false\n    };\n    if (this._deflating) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n  /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  pong(data, mask2, cb) {\n    let byteLength;\n    let readOnly;\n    if (typeof data === \"string\") {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer$1(data);\n      byteLength = data.length;\n      readOnly = toBuffer$1.readOnly;\n    }\n    if (byteLength > 125) {\n      throw new RangeError(\"The data size must not be greater than 125 bytes\");\n    }\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask: mask2,\n      maskBuffer: this._maskBuffer,\n      opcode: 10,\n      readOnly,\n      rsv1: false\n    };\n    if (this._deflating) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n  /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  send(data, options, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate$2.extensionName];\n    let opcode = options.binary ? 2 : 1;\n    let rsv1 = options.compress;\n    let byteLength;\n    let readOnly;\n    if (typeof data === \"string\") {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer$1(data);\n      byteLength = data.length;\n      readOnly = toBuffer$1.readOnly;\n    }\n    if (this._firstFragment) {\n      this._firstFragment = false;\n      if (rsv1 && perMessageDeflate && perMessageDeflate.params[perMessageDeflate._isServer ? \"server_no_context_takeover\" : \"client_no_context_takeover\"]) {\n        rsv1 = byteLength >= perMessageDeflate._threshold;\n      }\n      this._compress = rsv1;\n    } else {\n      rsv1 = false;\n      opcode = 0;\n    }\n    if (options.fin)\n      this._firstFragment = true;\n    if (perMessageDeflate) {\n      const opts = {\n        [kByteLength]: byteLength,\n        fin: options.fin,\n        generateMask: this._generateMask,\n        mask: options.mask,\n        maskBuffer: this._maskBuffer,\n        opcode,\n        readOnly,\n        rsv1\n      };\n      if (this._deflating) {\n        this.enqueue([this.dispatch, data, this._compress, opts, cb]);\n      } else {\n        this.dispatch(data, this._compress, opts, cb);\n      }\n    } else {\n      this.sendFrame(\n        Sender.frame(data, {\n          [kByteLength]: byteLength,\n          fin: options.fin,\n          generateMask: this._generateMask,\n          mask: options.mask,\n          maskBuffer: this._maskBuffer,\n          opcode,\n          readOnly,\n          rsv1: false\n        }),\n        cb\n      );\n    }\n  }\n  /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  dispatch(data, compress, options, cb) {\n    if (!compress) {\n      this.sendFrame(Sender.frame(data, options), cb);\n      return;\n    }\n    const perMessageDeflate = this._extensions[PerMessageDeflate$2.extensionName];\n    this._bufferedBytes += options[kByteLength];\n    this._deflating = true;\n    perMessageDeflate.compress(data, options.fin, (_, buf) => {\n      if (this._socket.destroyed) {\n        const err = new Error(\n          \"The socket was closed while data was being compressed\"\n        );\n        if (typeof cb === \"function\")\n          cb(err);\n        for (let i = 0; i < this._queue.length; i++) {\n          const params = this._queue[i];\n          const callback = params[params.length - 1];\n          if (typeof callback === \"function\")\n            callback(err);\n        }\n        return;\n      }\n      this._bufferedBytes -= options[kByteLength];\n      this._deflating = false;\n      options.readOnly = false;\n      this.sendFrame(Sender.frame(buf, options), cb);\n      this.dequeue();\n    });\n  }\n  /**\n   * Executes queued send operations.\n   *\n   * @private\n   */\n  dequeue() {\n    while (!this._deflating && this._queue.length) {\n      const params = this._queue.shift();\n      this._bufferedBytes -= params[3][kByteLength];\n      Reflect.apply(params[0], this, params.slice(1));\n    }\n  }\n  /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */\n  enqueue(params) {\n    this._bufferedBytes += params[3][kByteLength];\n    this._queue.push(params);\n  }\n  /**\n   * Sends a frame.\n   *\n   * @param {Buffer[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  sendFrame(list, cb) {\n    if (list.length === 2) {\n      this._socket.cork();\n      this._socket.write(list[0]);\n      this._socket.write(list[1], cb);\n      this._socket.uncork();\n    } else {\n      this._socket.write(list[0], cb);\n    }\n  }\n};\nvar sender = Sender$1;\nconst sender$1 = /* @__PURE__ */ getDefaultExportFromCjs(sender);\nconst { kForOnEventAttribute: kForOnEventAttribute$1, kListener: kListener$1 } = constants;\nconst kCode = Symbol(\"kCode\");\nconst kData = Symbol(\"kData\");\nconst kError = Symbol(\"kError\");\nconst kMessage = Symbol(\"kMessage\");\nconst kReason = Symbol(\"kReason\");\nconst kTarget = Symbol(\"kTarget\");\nconst kType = Symbol(\"kType\");\nconst kWasClean = Symbol(\"kWasClean\");\nclass Event {\n  /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */\n  constructor(type) {\n    this[kTarget] = null;\n    this[kType] = type;\n  }\n  /**\n   * @type {*}\n   */\n  get target() {\n    return this[kTarget];\n  }\n  /**\n   * @type {String}\n   */\n  get type() {\n    return this[kType];\n  }\n}\nObject.defineProperty(Event.prototype, \"target\", { enumerable: true });\nObject.defineProperty(Event.prototype, \"type\", { enumerable: true });\nclass CloseEvent extends Event {\n  /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */\n  constructor(type, options = {}) {\n    super(type);\n    this[kCode] = options.code === void 0 ? 0 : options.code;\n    this[kReason] = options.reason === void 0 ? \"\" : options.reason;\n    this[kWasClean] = options.wasClean === void 0 ? false : options.wasClean;\n  }\n  /**\n   * @type {Number}\n   */\n  get code() {\n    return this[kCode];\n  }\n  /**\n   * @type {String}\n   */\n  get reason() {\n    return this[kReason];\n  }\n  /**\n   * @type {Boolean}\n   */\n  get wasClean() {\n    return this[kWasClean];\n  }\n}\nObject.defineProperty(CloseEvent.prototype, \"code\", { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, \"reason\", { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, \"wasClean\", { enumerable: true });\nclass ErrorEvent extends Event {\n  /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */\n  constructor(type, options = {}) {\n    super(type);\n    this[kError] = options.error === void 0 ? null : options.error;\n    this[kMessage] = options.message === void 0 ? \"\" : options.message;\n  }\n  /**\n   * @type {*}\n   */\n  get error() {\n    return this[kError];\n  }\n  /**\n   * @type {String}\n   */\n  get message() {\n    return this[kMessage];\n  }\n}\nObject.defineProperty(ErrorEvent.prototype, \"error\", { enumerable: true });\nObject.defineProperty(ErrorEvent.prototype, \"message\", { enumerable: true });\nclass MessageEvent extends Event {\n  /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */\n  constructor(type, options = {}) {\n    super(type);\n    this[kData] = options.data === void 0 ? null : options.data;\n  }\n  /**\n   * @type {*}\n   */\n  get data() {\n    return this[kData];\n  }\n}\nObject.defineProperty(MessageEvent.prototype, \"data\", { enumerable: true });\nconst EventTarget = {\n  /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */\n  addEventListener(type, handler, options = {}) {\n    for (const listener of this.listeners(type)) {\n      if (!options[kForOnEventAttribute$1] && listener[kListener$1] === handler && !listener[kForOnEventAttribute$1]) {\n        return;\n      }\n    }\n    let wrapper;\n    if (type === \"message\") {\n      wrapper = function onMessage(data, isBinary) {\n        const event = new MessageEvent(\"message\", {\n          data: isBinary ? data : data.toString()\n        });\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === \"close\") {\n      wrapper = function onClose(code, message) {\n        const event = new CloseEvent(\"close\", {\n          code,\n          reason: message.toString(),\n          wasClean: this._closeFrameReceived && this._closeFrameSent\n        });\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === \"error\") {\n      wrapper = function onError(error2) {\n        const event = new ErrorEvent(\"error\", {\n          error: error2,\n          message: error2.message\n        });\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === \"open\") {\n      wrapper = function onOpen() {\n        const event = new Event(\"open\");\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else {\n      return;\n    }\n    wrapper[kForOnEventAttribute$1] = !!options[kForOnEventAttribute$1];\n    wrapper[kListener$1] = handler;\n    if (options.once) {\n      this.once(type, wrapper);\n    } else {\n      this.on(type, wrapper);\n    }\n  },\n  /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */\n  removeEventListener(type, handler) {\n    for (const listener of this.listeners(type)) {\n      if (listener[kListener$1] === handler && !listener[kForOnEventAttribute$1]) {\n        this.removeListener(type, listener);\n        break;\n      }\n    }\n  }\n};\nvar eventTarget = {\n  CloseEvent,\n  ErrorEvent,\n  Event,\n  EventTarget,\n  MessageEvent\n};\nfunction callListener(listener, thisArg, event) {\n  if (typeof listener === \"object\" && listener.handleEvent) {\n    listener.handleEvent.call(listener, event);\n  } else {\n    listener.call(thisArg, event);\n  }\n}\nconst { tokenChars: tokenChars$1 } = validationExports;\nfunction push(dest, name, elem) {\n  if (dest[name] === void 0)\n    dest[name] = [elem];\n  else\n    dest[name].push(elem);\n}\nfunction parse$2(header) {\n  const offers = /* @__PURE__ */ Object.create(null);\n  let params = /* @__PURE__ */ Object.create(null);\n  let mustUnescape = false;\n  let isEscaping = false;\n  let inQuotes = false;\n  let extensionName;\n  let paramName;\n  let start = -1;\n  let code = -1;\n  let end = -1;\n  let i = 0;\n  for (; i < header.length; i++) {\n    code = header.charCodeAt(i);\n    if (extensionName === void 0) {\n      if (end === -1 && tokenChars$1[code] === 1) {\n        if (start === -1)\n          start = i;\n      } else if (i !== 0 && (code === 32 || code === 9)) {\n        if (end === -1 && start !== -1)\n          end = i;\n      } else if (code === 59 || code === 44) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (end === -1)\n          end = i;\n        const name = header.slice(start, end);\n        if (code === 44) {\n          push(offers, name, params);\n          params = /* @__PURE__ */ Object.create(null);\n        } else {\n          extensionName = name;\n        }\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else if (paramName === void 0) {\n      if (end === -1 && tokenChars$1[code] === 1) {\n        if (start === -1)\n          start = i;\n      } else if (code === 32 || code === 9) {\n        if (end === -1 && start !== -1)\n          end = i;\n      } else if (code === 59 || code === 44) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (end === -1)\n          end = i;\n        push(params, header.slice(start, end), true);\n        if (code === 44) {\n          push(offers, extensionName, params);\n          params = /* @__PURE__ */ Object.create(null);\n          extensionName = void 0;\n        }\n        start = end = -1;\n      } else if (code === 61 && start !== -1 && end === -1) {\n        paramName = header.slice(start, i);\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else {\n      if (isEscaping) {\n        if (tokenChars$1[code] !== 1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (start === -1)\n          start = i;\n        else if (!mustUnescape)\n          mustUnescape = true;\n        isEscaping = false;\n      } else if (inQuotes) {\n        if (tokenChars$1[code] === 1) {\n          if (start === -1)\n            start = i;\n        } else if (code === 34 && start !== -1) {\n          inQuotes = false;\n          end = i;\n        } else if (code === 92) {\n          isEscaping = true;\n        } else {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n      } else if (code === 34 && header.charCodeAt(i - 1) === 61) {\n        inQuotes = true;\n      } else if (end === -1 && tokenChars$1[code] === 1) {\n        if (start === -1)\n          start = i;\n      } else if (start !== -1 && (code === 32 || code === 9)) {\n        if (end === -1)\n          end = i;\n      } else if (code === 59 || code === 44) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (end === -1)\n          end = i;\n        let value = header.slice(start, end);\n        if (mustUnescape) {\n          value = value.replace(/\\\\/g, \"\");\n          mustUnescape = false;\n        }\n        push(params, paramName, value);\n        if (code === 44) {\n          push(offers, extensionName, params);\n          params = /* @__PURE__ */ Object.create(null);\n          extensionName = void 0;\n        }\n        paramName = void 0;\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    }\n  }\n  if (start === -1 || inQuotes || code === 32 || code === 9) {\n    throw new SyntaxError(\"Unexpected end of input\");\n  }\n  if (end === -1)\n    end = i;\n  const token = header.slice(start, end);\n  if (extensionName === void 0) {\n    push(offers, token, params);\n  } else {\n    if (paramName === void 0) {\n      push(params, token, true);\n    } else if (mustUnescape) {\n      push(params, paramName, token.replace(/\\\\/g, \"\"));\n    } else {\n      push(params, paramName, token);\n    }\n    push(offers, extensionName, params);\n  }\n  return offers;\n}\nfunction format$1(extensions) {\n  return Object.keys(extensions).map((extension2) => {\n    let configurations = extensions[extension2];\n    if (!Array.isArray(configurations))\n      configurations = [configurations];\n    return configurations.map((params) => {\n      return [extension2].concat(\n        Object.keys(params).map((k) => {\n          let values = params[k];\n          if (!Array.isArray(values))\n            values = [values];\n          return values.map((v) => v === true ? k : `${k}=${v}`).join(\"; \");\n        })\n      ).join(\"; \");\n    }).join(\", \");\n  }).join(\", \");\n}\nvar extension$1 = { format: format$1, parse: parse$2 };\nconst EventEmitter$1 = require$$0$4;\nconst https = require$$1$2;\nconst http$1 = require$$2$1;\nconst net = require$$3;\nconst tls = require$$4;\nconst { randomBytes, createHash: createHash$1 } = require$$5;\nconst { URL } = require$$7;\nconst PerMessageDeflate$1 = permessageDeflate;\nconst Receiver2 = receiver;\nconst Sender2 = sender;\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  GUID: GUID$1,\n  kForOnEventAttribute,\n  kListener,\n  kStatusCode,\n  kWebSocket: kWebSocket$1,\n  NOOP\n} = constants;\nconst {\n  EventTarget: { addEventListener, removeEventListener }\n} = eventTarget;\nconst { format, parse: parse$1 } = extension$1;\nconst { toBuffer } = bufferUtilExports;\nconst closeTimeout = 30 * 1e3;\nconst kAborted = Symbol(\"kAborted\");\nconst protocolVersions = [8, 13];\nconst readyStates = [\"CONNECTING\", \"OPEN\", \"CLOSING\", \"CLOSED\"];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\nlet WebSocket$1 = class WebSocket extends EventEmitter$1 {\n  /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */\n  constructor(address, protocols, options) {\n    super();\n    this._binaryType = BINARY_TYPES[0];\n    this._closeCode = 1006;\n    this._closeFrameReceived = false;\n    this._closeFrameSent = false;\n    this._closeMessage = EMPTY_BUFFER;\n    this._closeTimer = null;\n    this._extensions = {};\n    this._paused = false;\n    this._protocol = \"\";\n    this._readyState = WebSocket.CONNECTING;\n    this._receiver = null;\n    this._sender = null;\n    this._socket = null;\n    if (address !== null) {\n      this._bufferedAmount = 0;\n      this._isServer = false;\n      this._redirects = 0;\n      if (protocols === void 0) {\n        protocols = [];\n      } else if (!Array.isArray(protocols)) {\n        if (typeof protocols === \"object\" && protocols !== null) {\n          options = protocols;\n          protocols = [];\n        } else {\n          protocols = [protocols];\n        }\n      }\n      initAsClient(this, address, protocols, options);\n    } else {\n      this._isServer = true;\n    }\n  }\n  /**\n   * This deviates from the WHATWG interface since ws doesn't support the\n   * required default \"blob\" type (instead we define a custom \"nodebuffer\"\n   * type).\n   *\n   * @type {String}\n   */\n  get binaryType() {\n    return this._binaryType;\n  }\n  set binaryType(type) {\n    if (!BINARY_TYPES.includes(type))\n      return;\n    this._binaryType = type;\n    if (this._receiver)\n      this._receiver._binaryType = type;\n  }\n  /**\n   * @type {Number}\n   */\n  get bufferedAmount() {\n    if (!this._socket)\n      return this._bufferedAmount;\n    return this._socket._writableState.length + this._sender._bufferedBytes;\n  }\n  /**\n   * @type {String}\n   */\n  get extensions() {\n    return Object.keys(this._extensions).join();\n  }\n  /**\n   * @type {Boolean}\n   */\n  get isPaused() {\n    return this._paused;\n  }\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onclose() {\n    return null;\n  }\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onerror() {\n    return null;\n  }\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onopen() {\n    return null;\n  }\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onmessage() {\n    return null;\n  }\n  /**\n   * @type {String}\n   */\n  get protocol() {\n    return this._protocol;\n  }\n  /**\n   * @type {Number}\n   */\n  get readyState() {\n    return this._readyState;\n  }\n  /**\n   * @type {String}\n   */\n  get url() {\n    return this._url;\n  }\n  /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {(net.Socket|tls.Socket)} socket The network socket between the\n   *     server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */\n  setSocket(socket, head, options) {\n    const receiver2 = new Receiver2({\n      binaryType: this.binaryType,\n      extensions: this._extensions,\n      isServer: this._isServer,\n      maxPayload: options.maxPayload,\n      skipUTF8Validation: options.skipUTF8Validation\n    });\n    this._sender = new Sender2(socket, this._extensions, options.generateMask);\n    this._receiver = receiver2;\n    this._socket = socket;\n    receiver2[kWebSocket$1] = this;\n    socket[kWebSocket$1] = this;\n    receiver2.on(\"conclude\", receiverOnConclude);\n    receiver2.on(\"drain\", receiverOnDrain);\n    receiver2.on(\"error\", receiverOnError);\n    receiver2.on(\"message\", receiverOnMessage);\n    receiver2.on(\"ping\", receiverOnPing);\n    receiver2.on(\"pong\", receiverOnPong);\n    socket.setTimeout(0);\n    socket.setNoDelay();\n    if (head.length > 0)\n      socket.unshift(head);\n    socket.on(\"close\", socketOnClose);\n    socket.on(\"data\", socketOnData);\n    socket.on(\"end\", socketOnEnd);\n    socket.on(\"error\", socketOnError$1);\n    this._readyState = WebSocket.OPEN;\n    this.emit(\"open\");\n  }\n  /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */\n  emitClose() {\n    if (!this._socket) {\n      this._readyState = WebSocket.CLOSED;\n      this.emit(\"close\", this._closeCode, this._closeMessage);\n      return;\n    }\n    if (this._extensions[PerMessageDeflate$1.extensionName]) {\n      this._extensions[PerMessageDeflate$1.extensionName].cleanup();\n    }\n    this._receiver.removeAllListeners();\n    this._readyState = WebSocket.CLOSED;\n    this.emit(\"close\", this._closeCode, this._closeMessage);\n  }\n  /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */\n  close(code, data) {\n    if (this.readyState === WebSocket.CLOSED)\n      return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = \"WebSocket was closed before the connection was established\";\n      abortHandshake$1(this, this._req, msg);\n      return;\n    }\n    if (this.readyState === WebSocket.CLOSING) {\n      if (this._closeFrameSent && (this._closeFrameReceived || this._receiver._writableState.errorEmitted)) {\n        this._socket.end();\n      }\n      return;\n    }\n    this._readyState = WebSocket.CLOSING;\n    this._sender.close(code, data, !this._isServer, (err) => {\n      if (err)\n        return;\n      this._closeFrameSent = true;\n      if (this._closeFrameReceived || this._receiver._writableState.errorEmitted) {\n        this._socket.end();\n      }\n    });\n    this._closeTimer = setTimeout(\n      this._socket.destroy.bind(this._socket),\n      closeTimeout\n    );\n  }\n  /**\n   * Pause the socket.\n   *\n   * @public\n   */\n  pause() {\n    if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n      return;\n    }\n    this._paused = true;\n    this._socket.pause();\n  }\n  /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */\n  ping(data, mask2, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n    }\n    if (typeof data === \"function\") {\n      cb = data;\n      data = mask2 = void 0;\n    } else if (typeof mask2 === \"function\") {\n      cb = mask2;\n      mask2 = void 0;\n    }\n    if (typeof data === \"number\")\n      data = data.toString();\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n    if (mask2 === void 0)\n      mask2 = !this._isServer;\n    this._sender.ping(data || EMPTY_BUFFER, mask2, cb);\n  }\n  /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */\n  pong(data, mask2, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n    }\n    if (typeof data === \"function\") {\n      cb = data;\n      data = mask2 = void 0;\n    } else if (typeof mask2 === \"function\") {\n      cb = mask2;\n      mask2 = void 0;\n    }\n    if (typeof data === \"number\")\n      data = data.toString();\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n    if (mask2 === void 0)\n      mask2 = !this._isServer;\n    this._sender.pong(data || EMPTY_BUFFER, mask2, cb);\n  }\n  /**\n   * Resume the socket.\n   *\n   * @public\n   */\n  resume() {\n    if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n      return;\n    }\n    this._paused = false;\n    if (!this._receiver._writableState.needDrain)\n      this._socket.resume();\n  }\n  /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */\n  send(data, options, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n    }\n    if (typeof options === \"function\") {\n      cb = options;\n      options = {};\n    }\n    if (typeof data === \"number\")\n      data = data.toString();\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n    const opts = {\n      binary: typeof data !== \"string\",\n      mask: !this._isServer,\n      compress: true,\n      fin: true,\n      ...options\n    };\n    if (!this._extensions[PerMessageDeflate$1.extensionName]) {\n      opts.compress = false;\n    }\n    this._sender.send(data || EMPTY_BUFFER, opts, cb);\n  }\n  /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */\n  terminate() {\n    if (this.readyState === WebSocket.CLOSED)\n      return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = \"WebSocket was closed before the connection was established\";\n      abortHandshake$1(this, this._req, msg);\n      return;\n    }\n    if (this._socket) {\n      this._readyState = WebSocket.CLOSING;\n      this._socket.destroy();\n    }\n  }\n};\nObject.defineProperty(WebSocket$1, \"CONNECTING\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CONNECTING\")\n});\nObject.defineProperty(WebSocket$1.prototype, \"CONNECTING\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CONNECTING\")\n});\nObject.defineProperty(WebSocket$1, \"OPEN\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"OPEN\")\n});\nObject.defineProperty(WebSocket$1.prototype, \"OPEN\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"OPEN\")\n});\nObject.defineProperty(WebSocket$1, \"CLOSING\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CLOSING\")\n});\nObject.defineProperty(WebSocket$1.prototype, \"CLOSING\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CLOSING\")\n});\nObject.defineProperty(WebSocket$1, \"CLOSED\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CLOSED\")\n});\nObject.defineProperty(WebSocket$1.prototype, \"CLOSED\", {\n  enumerable: true,\n  value: readyStates.indexOf(\"CLOSED\")\n});\n[\n  \"binaryType\",\n  \"bufferedAmount\",\n  \"extensions\",\n  \"isPaused\",\n  \"protocol\",\n  \"readyState\",\n  \"url\"\n].forEach((property) => {\n  Object.defineProperty(WebSocket$1.prototype, property, { enumerable: true });\n});\n[\"open\", \"error\", \"close\", \"message\"].forEach((method) => {\n  Object.defineProperty(WebSocket$1.prototype, `on${method}`, {\n    enumerable: true,\n    get() {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute])\n          return listener[kListener];\n      }\n      return null;\n    },\n    set(handler) {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) {\n          this.removeListener(method, listener);\n          break;\n        }\n      }\n      if (typeof handler !== \"function\")\n        return;\n      this.addEventListener(method, handler, {\n        [kForOnEventAttribute]: true\n      });\n    }\n  });\n});\nWebSocket$1.prototype.addEventListener = addEventListener;\nWebSocket$1.prototype.removeEventListener = removeEventListener;\nvar websocket = WebSocket$1;\nfunction initAsClient(websocket2, address, protocols, options) {\n  const opts = {\n    protocolVersion: protocolVersions[1],\n    maxPayload: 100 * 1024 * 1024,\n    skipUTF8Validation: false,\n    perMessageDeflate: true,\n    followRedirects: false,\n    maxRedirects: 10,\n    ...options,\n    createConnection: void 0,\n    socketPath: void 0,\n    hostname: void 0,\n    protocol: void 0,\n    timeout: void 0,\n    method: \"GET\",\n    host: void 0,\n    path: void 0,\n    port: void 0\n  };\n  if (!protocolVersions.includes(opts.protocolVersion)) {\n    throw new RangeError(\n      `Unsupported protocol version: ${opts.protocolVersion} (supported versions: ${protocolVersions.join(\", \")})`\n    );\n  }\n  let parsedUrl;\n  if (address instanceof URL) {\n    parsedUrl = address;\n    websocket2._url = address.href;\n  } else {\n    try {\n      parsedUrl = new URL(address);\n    } catch (e) {\n      throw new SyntaxError(`Invalid URL: ${address}`);\n    }\n    websocket2._url = address;\n  }\n  const isSecure = parsedUrl.protocol === \"wss:\";\n  const isIpcUrl = parsedUrl.protocol === \"ws+unix:\";\n  let invalidUrlMessage;\n  if (parsedUrl.protocol !== \"ws:\" && !isSecure && !isIpcUrl) {\n    invalidUrlMessage = `The URL's protocol must be one of \"ws:\", \"wss:\", or \"ws+unix:\"`;\n  } else if (isIpcUrl && !parsedUrl.pathname) {\n    invalidUrlMessage = \"The URL's pathname is empty\";\n  } else if (parsedUrl.hash) {\n    invalidUrlMessage = \"The URL contains a fragment identifier\";\n  }\n  if (invalidUrlMessage) {\n    const err = new SyntaxError(invalidUrlMessage);\n    if (websocket2._redirects === 0) {\n      throw err;\n    } else {\n      emitErrorAndClose(websocket2, err);\n      return;\n    }\n  }\n  const defaultPort = isSecure ? 443 : 80;\n  const key = randomBytes(16).toString(\"base64\");\n  const request = isSecure ? https.request : http$1.request;\n  const protocolSet = /* @__PURE__ */ new Set();\n  let perMessageDeflate;\n  opts.createConnection = isSecure ? tlsConnect : netConnect;\n  opts.defaultPort = opts.defaultPort || defaultPort;\n  opts.port = parsedUrl.port || defaultPort;\n  opts.host = parsedUrl.hostname.startsWith(\"[\") ? parsedUrl.hostname.slice(1, -1) : parsedUrl.hostname;\n  opts.headers = {\n    ...opts.headers,\n    \"Sec-WebSocket-Version\": opts.protocolVersion,\n    \"Sec-WebSocket-Key\": key,\n    Connection: \"Upgrade\",\n    Upgrade: \"websocket\"\n  };\n  opts.path = parsedUrl.pathname + parsedUrl.search;\n  opts.timeout = opts.handshakeTimeout;\n  if (opts.perMessageDeflate) {\n    perMessageDeflate = new PerMessageDeflate$1(\n      opts.perMessageDeflate !== true ? opts.perMessageDeflate : {},\n      false,\n      opts.maxPayload\n    );\n    opts.headers[\"Sec-WebSocket-Extensions\"] = format({\n      [PerMessageDeflate$1.extensionName]: perMessageDeflate.offer()\n    });\n  }\n  if (protocols.length) {\n    for (const protocol of protocols) {\n      if (typeof protocol !== \"string\" || !subprotocolRegex.test(protocol) || protocolSet.has(protocol)) {\n        throw new SyntaxError(\n          \"An invalid or duplicated subprotocol was specified\"\n        );\n      }\n      protocolSet.add(protocol);\n    }\n    opts.headers[\"Sec-WebSocket-Protocol\"] = protocols.join(\",\");\n  }\n  if (opts.origin) {\n    if (opts.protocolVersion < 13) {\n      opts.headers[\"Sec-WebSocket-Origin\"] = opts.origin;\n    } else {\n      opts.headers.Origin = opts.origin;\n    }\n  }\n  if (parsedUrl.username || parsedUrl.password) {\n    opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n  }\n  if (isIpcUrl) {\n    const parts = opts.path.split(\":\");\n    opts.socketPath = parts[0];\n    opts.path = parts[1];\n  }\n  let req;\n  if (opts.followRedirects) {\n    if (websocket2._redirects === 0) {\n      websocket2._originalIpc = isIpcUrl;\n      websocket2._originalSecure = isSecure;\n      websocket2._originalHostOrSocketPath = isIpcUrl ? opts.socketPath : parsedUrl.host;\n      const headers = options && options.headers;\n      options = { ...options, headers: {} };\n      if (headers) {\n        for (const [key2, value] of Object.entries(headers)) {\n          options.headers[key2.toLowerCase()] = value;\n        }\n      }\n    } else if (websocket2.listenerCount(\"redirect\") === 0) {\n      const isSameHost = isIpcUrl ? websocket2._originalIpc ? opts.socketPath === websocket2._originalHostOrSocketPath : false : websocket2._originalIpc ? false : parsedUrl.host === websocket2._originalHostOrSocketPath;\n      if (!isSameHost || websocket2._originalSecure && !isSecure) {\n        delete opts.headers.authorization;\n        delete opts.headers.cookie;\n        if (!isSameHost)\n          delete opts.headers.host;\n        opts.auth = void 0;\n      }\n    }\n    if (opts.auth && !options.headers.authorization) {\n      options.headers.authorization = \"Basic \" + Buffer.from(opts.auth).toString(\"base64\");\n    }\n    req = websocket2._req = request(opts);\n    if (websocket2._redirects) {\n      websocket2.emit(\"redirect\", websocket2.url, req);\n    }\n  } else {\n    req = websocket2._req = request(opts);\n  }\n  if (opts.timeout) {\n    req.on(\"timeout\", () => {\n      abortHandshake$1(websocket2, req, \"Opening handshake has timed out\");\n    });\n  }\n  req.on(\"error\", (err) => {\n    if (req === null || req[kAborted])\n      return;\n    req = websocket2._req = null;\n    emitErrorAndClose(websocket2, err);\n  });\n  req.on(\"response\", (res) => {\n    const location = res.headers.location;\n    const statusCode = res.statusCode;\n    if (location && opts.followRedirects && statusCode >= 300 && statusCode < 400) {\n      if (++websocket2._redirects > opts.maxRedirects) {\n        abortHandshake$1(websocket2, req, \"Maximum redirects exceeded\");\n        return;\n      }\n      req.abort();\n      let addr;\n      try {\n        addr = new URL(location, address);\n      } catch (e) {\n        const err = new SyntaxError(`Invalid URL: ${location}`);\n        emitErrorAndClose(websocket2, err);\n        return;\n      }\n      initAsClient(websocket2, addr, protocols, options);\n    } else if (!websocket2.emit(\"unexpected-response\", req, res)) {\n      abortHandshake$1(\n        websocket2,\n        req,\n        `Unexpected server response: ${res.statusCode}`\n      );\n    }\n  });\n  req.on(\"upgrade\", (res, socket, head) => {\n    websocket2.emit(\"upgrade\", res);\n    if (websocket2.readyState !== WebSocket$1.CONNECTING)\n      return;\n    req = websocket2._req = null;\n    if (res.headers.upgrade.toLowerCase() !== \"websocket\") {\n      abortHandshake$1(websocket2, socket, \"Invalid Upgrade header\");\n      return;\n    }\n    const digest = createHash$1(\"sha1\").update(key + GUID$1).digest(\"base64\");\n    if (res.headers[\"sec-websocket-accept\"] !== digest) {\n      abortHandshake$1(websocket2, socket, \"Invalid Sec-WebSocket-Accept header\");\n      return;\n    }\n    const serverProt = res.headers[\"sec-websocket-protocol\"];\n    let protError;\n    if (serverProt !== void 0) {\n      if (!protocolSet.size) {\n        protError = \"Server sent a subprotocol but none was requested\";\n      } else if (!protocolSet.has(serverProt)) {\n        protError = \"Server sent an invalid subprotocol\";\n      }\n    } else if (protocolSet.size) {\n      protError = \"Server sent no subprotocol\";\n    }\n    if (protError) {\n      abortHandshake$1(websocket2, socket, protError);\n      return;\n    }\n    if (serverProt)\n      websocket2._protocol = serverProt;\n    const secWebSocketExtensions = res.headers[\"sec-websocket-extensions\"];\n    if (secWebSocketExtensions !== void 0) {\n      if (!perMessageDeflate) {\n        const message = \"Server sent a Sec-WebSocket-Extensions header but no extension was requested\";\n        abortHandshake$1(websocket2, socket, message);\n        return;\n      }\n      let extensions;\n      try {\n        extensions = parse$1(secWebSocketExtensions);\n      } catch (err) {\n        const message = \"Invalid Sec-WebSocket-Extensions header\";\n        abortHandshake$1(websocket2, socket, message);\n        return;\n      }\n      const extensionNames = Object.keys(extensions);\n      if (extensionNames.length !== 1 || extensionNames[0] !== PerMessageDeflate$1.extensionName) {\n        const message = \"Server indicated an extension that was not requested\";\n        abortHandshake$1(websocket2, socket, message);\n        return;\n      }\n      try {\n        perMessageDeflate.accept(extensions[PerMessageDeflate$1.extensionName]);\n      } catch (err) {\n        const message = \"Invalid Sec-WebSocket-Extensions header\";\n        abortHandshake$1(websocket2, socket, message);\n        return;\n      }\n      websocket2._extensions[PerMessageDeflate$1.extensionName] = perMessageDeflate;\n    }\n    websocket2.setSocket(socket, head, {\n      generateMask: opts.generateMask,\n      maxPayload: opts.maxPayload,\n      skipUTF8Validation: opts.skipUTF8Validation\n    });\n  });\n  if (opts.finishRequest) {\n    opts.finishRequest(req, websocket2);\n  } else {\n    req.end();\n  }\n}\nfunction emitErrorAndClose(websocket2, err) {\n  websocket2._readyState = WebSocket$1.CLOSING;\n  websocket2.emit(\"error\", err);\n  websocket2.emitClose();\n}\nfunction netConnect(options) {\n  options.path = options.socketPath;\n  return net.connect(options);\n}\nfunction tlsConnect(options) {\n  options.path = void 0;\n  if (!options.servername && options.servername !== \"\") {\n    options.servername = net.isIP(options.host) ? \"\" : options.host;\n  }\n  return tls.connect(options);\n}\nfunction abortHandshake$1(websocket2, stream2, message) {\n  websocket2._readyState = WebSocket$1.CLOSING;\n  const err = new Error(message);\n  Error.captureStackTrace(err, abortHandshake$1);\n  if (stream2.setHeader) {\n    stream2[kAborted] = true;\n    stream2.abort();\n    if (stream2.socket && !stream2.socket.destroyed) {\n      stream2.socket.destroy();\n    }\n    process.nextTick(emitErrorAndClose, websocket2, err);\n  } else {\n    stream2.destroy(err);\n    stream2.once(\"error\", websocket2.emit.bind(websocket2, \"error\"));\n    stream2.once(\"close\", websocket2.emitClose.bind(websocket2));\n  }\n}\nfunction sendAfterClose(websocket2, data, cb) {\n  if (data) {\n    const length = toBuffer(data).length;\n    if (websocket2._socket)\n      websocket2._sender._bufferedBytes += length;\n    else\n      websocket2._bufferedAmount += length;\n  }\n  if (cb) {\n    const err = new Error(\n      `WebSocket is not open: readyState ${websocket2.readyState} (${readyStates[websocket2.readyState]})`\n    );\n    process.nextTick(cb, err);\n  }\n}\nfunction receiverOnConclude(code, reason) {\n  const websocket2 = this[kWebSocket$1];\n  websocket2._closeFrameReceived = true;\n  websocket2._closeMessage = reason;\n  websocket2._closeCode = code;\n  if (websocket2._socket[kWebSocket$1] === void 0)\n    return;\n  websocket2._socket.removeListener(\"data\", socketOnData);\n  process.nextTick(resume, websocket2._socket);\n  if (code === 1005)\n    websocket2.close();\n  else\n    websocket2.close(code, reason);\n}\nfunction receiverOnDrain() {\n  const websocket2 = this[kWebSocket$1];\n  if (!websocket2.isPaused)\n    websocket2._socket.resume();\n}\nfunction receiverOnError(err) {\n  const websocket2 = this[kWebSocket$1];\n  if (websocket2._socket[kWebSocket$1] !== void 0) {\n    websocket2._socket.removeListener(\"data\", socketOnData);\n    process.nextTick(resume, websocket2._socket);\n    websocket2.close(err[kStatusCode]);\n  }\n  websocket2.emit(\"error\", err);\n}\nfunction receiverOnFinish() {\n  this[kWebSocket$1].emitClose();\n}\nfunction receiverOnMessage(data, isBinary) {\n  this[kWebSocket$1].emit(\"message\", data, isBinary);\n}\nfunction receiverOnPing(data) {\n  const websocket2 = this[kWebSocket$1];\n  websocket2.pong(data, !websocket2._isServer, NOOP);\n  websocket2.emit(\"ping\", data);\n}\nfunction receiverOnPong(data) {\n  this[kWebSocket$1].emit(\"pong\", data);\n}\nfunction resume(stream2) {\n  stream2.resume();\n}\nfunction socketOnClose() {\n  const websocket2 = this[kWebSocket$1];\n  this.removeListener(\"close\", socketOnClose);\n  this.removeListener(\"data\", socketOnData);\n  this.removeListener(\"end\", socketOnEnd);\n  websocket2._readyState = WebSocket$1.CLOSING;\n  let chunk;\n  if (!this._readableState.endEmitted && !websocket2._closeFrameReceived && !websocket2._receiver._writableState.errorEmitted && (chunk = websocket2._socket.read()) !== null) {\n    websocket2._receiver.write(chunk);\n  }\n  websocket2._receiver.end();\n  this[kWebSocket$1] = void 0;\n  clearTimeout(websocket2._closeTimer);\n  if (websocket2._receiver._writableState.finished || websocket2._receiver._writableState.errorEmitted) {\n    websocket2.emitClose();\n  } else {\n    websocket2._receiver.on(\"error\", receiverOnFinish);\n    websocket2._receiver.on(\"finish\", receiverOnFinish);\n  }\n}\nfunction socketOnData(chunk) {\n  if (!this[kWebSocket$1]._receiver.write(chunk)) {\n    this.pause();\n  }\n}\nfunction socketOnEnd() {\n  const websocket2 = this[kWebSocket$1];\n  websocket2._readyState = WebSocket$1.CLOSING;\n  websocket2._receiver.end();\n  this.end();\n}\nfunction socketOnError$1() {\n  const websocket2 = this[kWebSocket$1];\n  this.removeListener(\"error\", socketOnError$1);\n  this.on(\"error\", NOOP);\n  if (websocket2) {\n    websocket2._readyState = WebSocket$1.CLOSING;\n    this.destroy();\n  }\n}\nconst WebSocket$2 = /* @__PURE__ */ getDefaultExportFromCjs(websocket);\nconst { tokenChars } = validationExports;\nfunction parse(header) {\n  const protocols = /* @__PURE__ */ new Set();\n  let start = -1;\n  let end = -1;\n  let i = 0;\n  for (i; i < header.length; i++) {\n    const code = header.charCodeAt(i);\n    if (end === -1 && tokenChars[code] === 1) {\n      if (start === -1)\n        start = i;\n    } else if (i !== 0 && (code === 32 || code === 9)) {\n      if (end === -1 && start !== -1)\n        end = i;\n    } else if (code === 44) {\n      if (start === -1) {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n      if (end === -1)\n        end = i;\n      const protocol2 = header.slice(start, end);\n      if (protocols.has(protocol2)) {\n        throw new SyntaxError(`The \"${protocol2}\" subprotocol is duplicated`);\n      }\n      protocols.add(protocol2);\n      start = end = -1;\n    } else {\n      throw new SyntaxError(`Unexpected character at index ${i}`);\n    }\n  }\n  if (start === -1 || end !== -1) {\n    throw new SyntaxError(\"Unexpected end of input\");\n  }\n  const protocol = header.slice(start, i);\n  if (protocols.has(protocol)) {\n    throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n  }\n  protocols.add(protocol);\n  return protocols;\n}\nvar subprotocol$1 = { parse };\nconst EventEmitter = require$$0$4;\nconst http = require$$2$1;\nconst { createHash } = require$$5;\nconst extension = extension$1;\nconst PerMessageDeflate2 = permessageDeflate;\nconst subprotocol = subprotocol$1;\nconst WebSocket2 = websocket;\nconst { GUID, kWebSocket } = constants;\nconst keyRegex = /^[+/0-9A-Za-z]{22}==$/;\nconst RUNNING = 0;\nconst CLOSING = 1;\nconst CLOSED = 2;\nclass WebSocketServer extends EventEmitter {\n  /**\n   * Create a `WebSocketServer` instance.\n   *\n   * @param {Object} options Configuration options\n   * @param {Number} [options.backlog=511] The maximum length of the queue of\n   *     pending connections\n   * @param {Boolean} [options.clientTracking=true] Specifies whether or not to\n   *     track clients\n   * @param {Function} [options.handleProtocols] A hook to handle protocols\n   * @param {String} [options.host] The hostname where to bind the server\n   * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n   *     size\n   * @param {Boolean} [options.noServer=false] Enable no server mode\n   * @param {String} [options.path] Accept only connections matching this path\n   * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable\n   *     permessage-deflate\n   * @param {Number} [options.port] The port where to bind the server\n   * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S\n   *     server to use\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @param {Function} [options.verifyClient] A hook to reject connections\n   * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`\n   *     class to use. It must be the `WebSocket` class or class that extends it\n   * @param {Function} [callback] A listener for the `listening` event\n   */\n  constructor(options, callback) {\n    super();\n    options = {\n      maxPayload: 100 * 1024 * 1024,\n      skipUTF8Validation: false,\n      perMessageDeflate: false,\n      handleProtocols: null,\n      clientTracking: true,\n      verifyClient: null,\n      noServer: false,\n      backlog: null,\n      // use default (511 as implemented in net.js)\n      server: null,\n      host: null,\n      path: null,\n      port: null,\n      WebSocket: WebSocket2,\n      ...options\n    };\n    if (options.port == null && !options.server && !options.noServer || options.port != null && (options.server || options.noServer) || options.server && options.noServer) {\n      throw new TypeError(\n        'One and only one of the \"port\", \"server\", or \"noServer\" options must be specified'\n      );\n    }\n    if (options.port != null) {\n      this._server = http.createServer((req, res) => {\n        const body = http.STATUS_CODES[426];\n        res.writeHead(426, {\n          \"Content-Length\": body.length,\n          \"Content-Type\": \"text/plain\"\n        });\n        res.end(body);\n      });\n      this._server.listen(\n        options.port,\n        options.host,\n        options.backlog,\n        callback\n      );\n    } else if (options.server) {\n      this._server = options.server;\n    }\n    if (this._server) {\n      const emitConnection = this.emit.bind(this, \"connection\");\n      this._removeListeners = addListeners(this._server, {\n        listening: this.emit.bind(this, \"listening\"),\n        error: this.emit.bind(this, \"error\"),\n        upgrade: (req, socket, head) => {\n          this.handleUpgrade(req, socket, head, emitConnection);\n        }\n      });\n    }\n    if (options.perMessageDeflate === true)\n      options.perMessageDeflate = {};\n    if (options.clientTracking) {\n      this.clients = /* @__PURE__ */ new Set();\n      this._shouldEmitClose = false;\n    }\n    this.options = options;\n    this._state = RUNNING;\n  }\n  /**\n   * Returns the bound address, the address family name, and port of the server\n   * as reported by the operating system if listening on an IP socket.\n   * If the server is listening on a pipe or UNIX domain socket, the name is\n   * returned as a string.\n   *\n   * @return {(Object|String|null)} The address of the server\n   * @public\n   */\n  address() {\n    if (this.options.noServer) {\n      throw new Error('The server is operating in \"noServer\" mode');\n    }\n    if (!this._server)\n      return null;\n    return this._server.address();\n  }\n  /**\n   * Stop the server from accepting new connections and emit the `'close'` event\n   * when all existing connections are closed.\n   *\n   * @param {Function} [cb] A one-time listener for the `'close'` event\n   * @public\n   */\n  close(cb) {\n    if (this._state === CLOSED) {\n      if (cb) {\n        this.once(\"close\", () => {\n          cb(new Error(\"The server is not running\"));\n        });\n      }\n      process.nextTick(emitClose, this);\n      return;\n    }\n    if (cb)\n      this.once(\"close\", cb);\n    if (this._state === CLOSING)\n      return;\n    this._state = CLOSING;\n    if (this.options.noServer || this.options.server) {\n      if (this._server) {\n        this._removeListeners();\n        this._removeListeners = this._server = null;\n      }\n      if (this.clients) {\n        if (!this.clients.size) {\n          process.nextTick(emitClose, this);\n        } else {\n          this._shouldEmitClose = true;\n        }\n      } else {\n        process.nextTick(emitClose, this);\n      }\n    } else {\n      const server = this._server;\n      this._removeListeners();\n      this._removeListeners = this._server = null;\n      server.close(() => {\n        emitClose(this);\n      });\n    }\n  }\n  /**\n   * See if a given request should be handled by this server instance.\n   *\n   * @param {http.IncomingMessage} req Request object to inspect\n   * @return {Boolean} `true` if the request is valid, else `false`\n   * @public\n   */\n  shouldHandle(req) {\n    if (this.options.path) {\n      const index = req.url.indexOf(\"?\");\n      const pathname = index !== -1 ? req.url.slice(0, index) : req.url;\n      if (pathname !== this.options.path)\n        return false;\n    }\n    return true;\n  }\n  /**\n   * Handle a HTTP Upgrade request.\n   *\n   * @param {http.IncomingMessage} req The request object\n   * @param {(net.Socket|tls.Socket)} socket The network socket between the\n   *     server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @public\n   */\n  handleUpgrade(req, socket, head, cb) {\n    socket.on(\"error\", socketOnError);\n    const key = req.headers[\"sec-websocket-key\"];\n    const version = +req.headers[\"sec-websocket-version\"];\n    if (req.method !== \"GET\") {\n      const message = \"Invalid HTTP method\";\n      abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);\n      return;\n    }\n    if (req.headers.upgrade.toLowerCase() !== \"websocket\") {\n      const message = \"Invalid Upgrade header\";\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n    if (!key || !keyRegex.test(key)) {\n      const message = \"Missing or invalid Sec-WebSocket-Key header\";\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n    if (version !== 8 && version !== 13) {\n      const message = \"Missing or invalid Sec-WebSocket-Version header\";\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n    if (!this.shouldHandle(req)) {\n      abortHandshake(socket, 400);\n      return;\n    }\n    const secWebSocketProtocol = req.headers[\"sec-websocket-protocol\"];\n    let protocols = /* @__PURE__ */ new Set();\n    if (secWebSocketProtocol !== void 0) {\n      try {\n        protocols = subprotocol.parse(secWebSocketProtocol);\n      } catch (err) {\n        const message = \"Invalid Sec-WebSocket-Protocol header\";\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n    const secWebSocketExtensions = req.headers[\"sec-websocket-extensions\"];\n    const extensions = {};\n    if (this.options.perMessageDeflate && secWebSocketExtensions !== void 0) {\n      const perMessageDeflate = new PerMessageDeflate2(\n        this.options.perMessageDeflate,\n        true,\n        this.options.maxPayload\n      );\n      try {\n        const offers = extension.parse(secWebSocketExtensions);\n        if (offers[PerMessageDeflate2.extensionName]) {\n          perMessageDeflate.accept(offers[PerMessageDeflate2.extensionName]);\n          extensions[PerMessageDeflate2.extensionName] = perMessageDeflate;\n        }\n      } catch (err) {\n        const message = \"Invalid or unacceptable Sec-WebSocket-Extensions header\";\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n    if (this.options.verifyClient) {\n      const info = {\n        origin: req.headers[`${version === 8 ? \"sec-websocket-origin\" : \"origin\"}`],\n        secure: !!(req.socket.authorized || req.socket.encrypted),\n        req\n      };\n      if (this.options.verifyClient.length === 2) {\n        this.options.verifyClient(info, (verified, code, message, headers) => {\n          if (!verified) {\n            return abortHandshake(socket, code || 401, message, headers);\n          }\n          this.completeUpgrade(\n            extensions,\n            key,\n            protocols,\n            req,\n            socket,\n            head,\n            cb\n          );\n        });\n        return;\n      }\n      if (!this.options.verifyClient(info))\n        return abortHandshake(socket, 401);\n    }\n    this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n  }\n  /**\n   * Upgrade the connection to WebSocket.\n   *\n   * @param {Object} extensions The accepted extensions\n   * @param {String} key The value of the `Sec-WebSocket-Key` header\n   * @param {Set} protocols The subprotocols\n   * @param {http.IncomingMessage} req The request object\n   * @param {(net.Socket|tls.Socket)} socket The network socket between the\n   *     server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @throws {Error} If called more than once with the same socket\n   * @private\n   */\n  completeUpgrade(extensions, key, protocols, req, socket, head, cb) {\n    if (!socket.readable || !socket.writable)\n      return socket.destroy();\n    if (socket[kWebSocket]) {\n      throw new Error(\n        \"server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration\"\n      );\n    }\n    if (this._state > RUNNING)\n      return abortHandshake(socket, 503);\n    const digest = createHash(\"sha1\").update(key + GUID).digest(\"base64\");\n    const headers = [\n      \"HTTP/1.1 101 Switching Protocols\",\n      \"Upgrade: websocket\",\n      \"Connection: Upgrade\",\n      `Sec-WebSocket-Accept: ${digest}`\n    ];\n    const ws = new this.options.WebSocket(null);\n    if (protocols.size) {\n      const protocol = this.options.handleProtocols ? this.options.handleProtocols(protocols, req) : protocols.values().next().value;\n      if (protocol) {\n        headers.push(`Sec-WebSocket-Protocol: ${protocol}`);\n        ws._protocol = protocol;\n      }\n    }\n    if (extensions[PerMessageDeflate2.extensionName]) {\n      const params = extensions[PerMessageDeflate2.extensionName].params;\n      const value = extension.format({\n        [PerMessageDeflate2.extensionName]: [params]\n      });\n      headers.push(`Sec-WebSocket-Extensions: ${value}`);\n      ws._extensions = extensions;\n    }\n    this.emit(\"headers\", headers, req);\n    socket.write(headers.concat(\"\\r\\n\").join(\"\\r\\n\"));\n    socket.removeListener(\"error\", socketOnError);\n    ws.setSocket(socket, head, {\n      maxPayload: this.options.maxPayload,\n      skipUTF8Validation: this.options.skipUTF8Validation\n    });\n    if (this.clients) {\n      this.clients.add(ws);\n      ws.on(\"close\", () => {\n        this.clients.delete(ws);\n        if (this._shouldEmitClose && !this.clients.size) {\n          process.nextTick(emitClose, this);\n        }\n      });\n    }\n    cb(ws, req);\n  }\n}\nvar websocketServer = WebSocketServer;\nfunction addListeners(server, map) {\n  for (const event of Object.keys(map))\n    server.on(event, map[event]);\n  return function removeListeners() {\n    for (const event of Object.keys(map)) {\n      server.removeListener(event, map[event]);\n    }\n  };\n}\nfunction emitClose(server) {\n  server._state = CLOSED;\n  server.emit(\"close\");\n}\nfunction socketOnError() {\n  this.destroy();\n}\nfunction abortHandshake(socket, code, message, headers) {\n  message = message || http.STATUS_CODES[code];\n  headers = {\n    Connection: \"close\",\n    \"Content-Type\": \"text/html\",\n    \"Content-Length\": Buffer.byteLength(message),\n    ...headers\n  };\n  socket.once(\"finish\", socket.destroy);\n  socket.end(\n    `HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\\r\n` + Object.keys(headers).map((h) => `${h}: ${headers[h]}`).join(\"\\r\\n\") + \"\\r\\n\\r\\n\" + message\n  );\n}\nfunction abortHandshakeOrEmitwsClientError(server, req, socket, code, message) {\n  if (server.listenerCount(\"wsClientError\")) {\n    const err = new Error(message);\n    Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);\n    server.emit(\"wsClientError\", err, socket, req);\n  } else {\n    abortHandshake(socket, code, message);\n  }\n}\nconst websocketServer$1 = /* @__PURE__ */ getDefaultExportFromCjs(websocketServer);\nexport {\n  receiver$1 as Receiver,\n  sender$1 as Sender,\n  WebSocket$2 as WebSocket,\n  websocketServer$1 as WebSocketServer,\n  stream$1 as createWebSocketStream,\n  WebSocket$2 as default\n};\n"], "names": ["getDefaultExportFromCjs", "x", "getAugmentedNamespace", "n", "f", "a", "a2", "k", "d", "Duplex", "require$$0", "emitClose$1", "stream2", "duplexOnEnd", "duplexOnError", "err", "createWebSocketStream", "ws", "options", "terminateOnDestroy", "duplex", "msg", "isBinary", "data", "callback", "called", "err2", "chunk", "encoding", "stream", "stream$1", "bufferUtil$1", "constants", "bufferutil", "nodeGypBuild$1", "commonjsRequire", "path", "nodeGypBuild", "hasRequiredNodeGypBuild$1", "requireNodeGypBuild$1", "fs", "require$$0$1", "require$$1$1", "os", "require$$2", "runtimeRequire", "vars", "prebuildsOnly", "define_process_env_default", "abi", "runtime", "isElectron", "isNwjs", "arch", "platform", "libc", "isAlpine", "armv", "uv", "load", "dir", "name", "release", "get<PERSON><PERSON><PERSON>", "matchBuild", "debug", "prebuild", "resolve", "nearby", "target", "dir2", "tuples", "readdirSync", "parseTuple", "tuple", "matchTuple", "compareTuples", "prebuilds", "parsed", "parseTags", "candidates", "matchTags", "winner", "compareTags", "filter", "files", "arr", "platform2", "architectures", "arch2", "b", "file", "extension2", "tags", "i", "tag", "runtime2", "abi2", "runtimeAgnostic", "hasRequiredNodeGypBuild", "requireNodeGypBuild", "fallback", "hasRequiredFallback", "<PERSON><PERSON><PERSON><PERSON>", "source", "mask3", "output", "offset", "length", "buffer", "hasRequiredBufferutil", "requireBufferutil", "unmask$1", "mask", "EMPTY_BUFFER$3", "FastBuffer$2", "concat$1", "list", "totalLength", "buf", "_mask", "mask2", "_unmask", "toArrayBuffer$1", "toBuffer$2", "bufferUtil2", "bufferUtilExports", "kDone", "kRun", "Limiter$1", "concurrency", "job", "limiter", "zlib", "require$$0$2", "bufferUtil", "Limiter2", "kStatusCode$2", "FastBuffer$1", "TRAILER", "kPerMessageDeflate", "kTotalLength", "kCallback", "kBuffers", "kError$1", "zlibLimiter", "PerMessageDeflate$4", "isServer", "maxPayload", "params", "configurations", "offers", "opts", "accepted", "response", "key", "value", "num", "fin", "done", "result", "endpoint", "windowBits", "inflateOnError", "inflateOnData", "data2", "deflateOnData", "permessageDeflate", "validation", "__viteOptionalPeerDep_utf8Validate_ws", "__viteOptionalPeerDep_utf8Validate_ws$1", "require$$1", "isValidUTF8_1", "isUtf8", "require$$0$3", "tokenChars$2", "isValidStatusCode$2", "code", "_isValidUTF8", "len", "isValidUTF82", "validationExports", "Writable", "PerMessageDeflate$3", "BINARY_TYPES$1", "EMPTY_BUFFER$2", "kStatusCode$1", "kWebSocket$2", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unmask", "isValidStatusCode$1", "isValidUTF8", "FastBuffer", "GET_INFO", "GET_PAYLOAD_LENGTH_16", "GET_PAYLOAD_LENGTH_64", "GET_MASK", "GET_DATA", "INFLATING", "Receiver$1", "cb", "dst", "error", "compressed", "er", "messageLength", "fragments", "receiver", "ErrorCtor", "message", "prefix", "statusCode", "errorCode", "receiver$1", "randomFillSync", "require$$5", "PerMessageDeflate$2", "EMPTY_BUFFER$1", "isValidStatusCode", "applyMask", "toBuffer$1", "kByteLength", "<PERSON><PERSON><PERSON><PERSON>", "Sender$1", "Sender", "socket", "extensions", "generateMask", "merge", "skipMasking", "dataLength", "payloadLength", "byteLength", "readOnly", "perMessageDeflate", "opcode", "rsv1", "compress", "_", "sender", "sender$1", "kForOnEventAttribute$1", "kListener$1", "kCode", "kData", "kError", "kMessage", "kReason", "kTarget", "kType", "kWasClean", "Event", "type", "CloseEvent", "ErrorEvent", "MessageEvent", "EventTarget", "handler", "listener", "wrapper", "event", "callListener", "error2", "eventTarget", "thisArg", "tokenChars$1", "push", "dest", "elem", "parse$2", "header", "mustUnescape", "isEscaping", "inQuotes", "extensionName", "paramName", "start", "end", "token", "format$1", "values", "v", "extension$1", "EventEmitter$1", "require$$0$4", "https", "require$$1$2", "http$1", "require$$2$1", "net", "require$$3", "tls", "require$$4", "randomBytes", "createHash$1", "URL", "require$$7", "PerMessageDeflate$1", "Receiver2", "Sender2", "BINARY_TYPES", "EMPTY_BUFFER", "GUID$1", "kForOnEventAttribute", "kListener", "kStatusCode", "kWebSocket$1", "NOOP", "addEventListener", "removeEventListener", "format", "parse$1", "<PERSON><PERSON><PERSON><PERSON>", "closeTimeout", "kAborted", "protocolVersions", "readyStates", "subprotocolRegex", "WebSocket$1", "WebSocket", "address", "protocols", "initAsClient", "head", "receiver2", "receiverOnConclude", "receiverOnDrain", "receiver<PERSON>n<PERSON><PERSON><PERSON>", "receiverOnMessage", "receiverOnPing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socketOnClose", "socketOnData", "socketOnEnd", "socketOnError$1", "abortHandshake$1", "sendAfterClose", "property", "method", "websocket", "websocket2", "parsedUrl", "isSecure", "isIpcUrl", "invalidUrlMessage", "emitErrorAndClose", "defaultPort", "request", "protocolSet", "tlsConnect", "netConnect", "protocol", "parts", "req", "headers", "key2", "isSameHost", "res", "location", "addr", "digest", "serverProt", "protError", "secWebSocketExtensions", "extensionNames", "reason", "resume", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WebSocket$2", "tokenChars", "parse", "protocol2", "subprotocol$1", "EventEmitter", "http", "createHash", "extension", "PerMessageDeflate2", "subprotocol", "WebSocket2", "GUID", "kWebSocket", "keyRegex", "RUNNING", "CLOSING", "CLOSED", "WebSocketServer", "body", "emitConnection", "addListeners", "emitClose", "server", "index", "socketOnError", "version", "abortHandshakeOrEmitwsClientError", "abortHandshake", "secWebSocketProtocol", "info", "verified", "websocketServer", "map", "h", "websocketServer$1"], "mappings": "mEAaA,SAASA,GAAwBC,EAAG,CAClC,OAAOA,GAAKA,EAAE,YAAc,OAAO,UAAU,eAAe,KAAKA,EAAG,SAAS,EAAIA,EAAE,QAAaA,CAClG,CACA,SAASC,GAAsBC,EAAG,CAChC,GAAIA,EAAE,WACG,OAAAA,EACT,IAAIC,EAAID,EAAE,QACN,GAAA,OAAOC,GAAK,WAAY,CACtB,IAAAC,EAAI,SAASC,GAAK,CACpB,OAAI,gBAAgBA,EACX,QAAQ,UAAUF,EAAG,UAAW,KAAK,WAAW,EAElDA,EAAE,MAAM,KAAM,SAAS,CAAA,EAEhCC,EAAE,UAAYD,EAAE,SAClB,MACEC,EAAI,CAAA,EACN,cAAO,eAAeA,EAAG,aAAc,CAAE,MAAO,GAAM,EACtD,OAAO,KAAKF,CAAC,EAAE,QAAQ,SAASI,EAAG,CACjC,IAAIC,EAAI,OAAO,yBAAyBL,EAAGI,CAAC,EAC5C,OAAO,eAAeF,EAAGE,EAAGC,EAAE,IAAMA,EAAI,CACtC,WAAY,GACZ,IAAK,UAAW,CACd,OAAOL,EAAEI,CAAC,CACZ,CAAA,CACD,CAAA,CACF,EACMF,CACT,CACA,KAAM,CAAE,OAAAI,EAAW,EAAAC,EACnB,SAASC,GAAYC,EAAS,CAC5BA,EAAQ,KAAK,OAAO,CACtB,CACA,SAASC,IAAc,CACjB,CAAC,KAAK,WAAa,KAAK,eAAe,UACzC,KAAK,QAAQ,CAEjB,CACA,SAASC,GAAcC,EAAK,CACrB,KAAA,eAAe,QAASD,EAAa,EAC1C,KAAK,QAAQ,EACT,KAAK,cAAc,OAAO,IAAM,GAC7B,KAAA,KAAK,QAASC,CAAG,CAE1B,CACA,SAASC,GAAsBC,EAAIC,EAAS,CAC1C,IAAIC,EAAqB,GACnB,MAAAC,EAAS,IAAIX,GAAO,CACxB,GAAGS,EACH,YAAa,GACb,UAAW,GACX,WAAY,GACZ,mBAAoB,EAAA,CACrB,EACD,OAAAD,EAAG,GAAG,UAAW,SAAiBI,EAAKC,EAAU,CACzC,MAAAC,EAAO,CAACD,GAAYF,EAAO,eAAe,WAAaC,EAAI,SAAa,EAAAA,EACzED,EAAO,KAAKG,CAAI,GACnBN,EAAG,MAAM,CAAA,CACZ,EACDA,EAAG,KAAK,QAAS,SAAgBF,EAAK,CAChCK,EAAO,YAEUD,EAAA,GACrBC,EAAO,QAAQL,CAAG,EAAA,CACnB,EACEE,EAAA,KAAK,QAAS,UAAiB,CAC5BG,EAAO,WAEXA,EAAO,KAAK,IAAI,CAAA,CACjB,EACMA,EAAA,SAAW,SAASL,EAAKS,EAAU,CACpC,GAAAP,EAAG,aAAeA,EAAG,OAAQ,CAC/BO,EAAST,CAAG,EACJ,QAAA,SAASJ,GAAaS,CAAM,EACpC,MACF,CACA,IAAIK,EAAS,GACbR,EAAG,KAAK,QAAS,SAAgBS,EAAM,CAC5BD,EAAA,GACTD,EAASE,CAAI,CAAA,CACd,EACET,EAAA,KAAK,QAAS,UAAiB,CAC3BQ,GACHD,EAAST,CAAG,EACN,QAAA,SAASJ,GAAaS,CAAM,CAAA,CACrC,EACGD,GACFF,EAAG,UAAU,CAAA,EAEVG,EAAA,OAAS,SAASI,EAAU,CAC7B,GAAAP,EAAG,aAAeA,EAAG,WAAY,CAChCA,EAAA,KAAK,OAAQ,UAAgB,CAC9BG,EAAO,OAAOI,CAAQ,CAAA,CACvB,EACD,MACF,CACIP,EAAG,UAAY,OAEfA,EAAG,QAAQ,eAAe,UACnBO,IACLJ,EAAO,eAAe,YACxBA,EAAO,QAAQ,IAEjBH,EAAG,QAAQ,KAAK,SAAU,UAAkB,CACjCO,GAAA,CACV,EACDP,EAAG,MAAM,GACX,EAEFG,EAAO,MAAQ,UAAW,CACpBH,EAAG,UACLA,EAAG,OAAO,CAAA,EAEdG,EAAO,OAAS,SAASO,EAAOC,EAAUJ,EAAU,CAC9C,GAAAP,EAAG,aAAeA,EAAG,WAAY,CAChCA,EAAA,KAAK,OAAQ,UAAgB,CACvBG,EAAA,OAAOO,EAAOC,EAAUJ,CAAQ,CAAA,CACxC,EACD,MACF,CACGP,EAAA,KAAKU,EAAOH,CAAQ,CAAA,EAElBJ,EAAA,GAAG,MAAOP,EAAW,EACrBO,EAAA,GAAG,QAASN,EAAa,EACzBM,CACT,CACA,IAAIS,GAASb,GACP,MAAAc,MAAmDD,EAAM,EAC/D,IAAIE,GAAe,CAAE,QAAS,CAAA,GAC1BC,EAAY,CACd,aAAc,CAAC,aAAc,cAAe,WAAW,EACvD,aAAc,OAAO,MAAM,CAAC,EAC5B,KAAM,uCACN,qBAAsB,OAAO,wBAAwB,EACrD,UAAW,OAAO,WAAW,EAC7B,YAAa,OAAO,aAAa,EACjC,WAAY,OAAO,WAAW,EAC9B,KAAM,IAAM,CACZ,CACF,EACIC,GAAa,CAAE,QAAS,CAAA,GACxBC,GAAiB,CAAE,QAAS,CAAA,GAChC,SAASC,GAAgBC,EAAM,CAC7B,MAAM,IAAI,MAAM,kCAAoCA,EAAO,2JAA2J,CACxN,CACA,IAAIC,GACAC,GACJ,SAASC,IAAwB,CAC3B,GAAAD,GACK,OAAAD,GACmBC,GAAA,EAC5B,IAAIE,EAAKC,EACLL,EAAOM,EACPC,EAAKC,EACLC,EAAiB,OAAO,qBAAwB,WAAa,wBAA0BV,GACvFW,EAAO,QAAQ,QAAU,QAAQ,OAAO,WAAa,GACrDC,EAAgB,CAAC,CAACC,EAAY,eAC9BC,EAAM,QAAQ,SAAS,QACvBC,EAAUC,KAAe,WAAaC,IAAW,cAAgB,OACjEC,EAAOL,EAAY,iBAAmBL,EAAG,KAAK,EAC9CW,EAAWN,EAAY,qBAAuBL,EAAG,SAAS,EAC1DY,EAAOP,EAAY,OAASQ,GAASF,CAAQ,EAAI,OAAS,SAC1DG,EAAOT,EAAY,cAAgBK,IAAS,QAAU,IAAMP,EAAK,cAAgB,GACjFY,GAAM,QAAQ,SAAS,IAAM,IAAI,MAAM,GAAG,EAAE,CAAC,EAClCrB,GAAAsB,EACf,SAASA,EAAKC,EAAK,CACjB,OAAOf,EAAec,EAAK,QAAQC,CAAG,CAAC,CACzC,CACAD,EAAK,QAAUA,EAAK,KAAO,SAASC,EAAK,CACjCA,EAAAxB,EAAK,QAAQwB,GAAO,GAAG,EACzB,GAAA,CACF,IAAIC,EAAOhB,EAAeT,EAAK,KAAKwB,EAAK,cAAc,CAAC,EAAE,KAAK,YAAc,EAAA,QAAQ,KAAM,GAAG,EAC1FZ,EAAYa,EAAO,WAAW,IAC1BD,EAAAZ,EAAYa,EAAO,WAAW,QAC1B,CACd,CACA,GAAI,CAACd,EAAe,CAClB,IAAIe,EAAUC,EAAS3B,EAAK,KAAKwB,EAAK,eAAe,EAAGI,CAAU,EAC9D,GAAAF,EACK,OAAAA,EACT,IAAIG,EAAQF,EAAS3B,EAAK,KAAKwB,EAAK,aAAa,EAAGI,CAAU,EAC1D,GAAAC,EACK,OAAAA,CACX,CACI,IAAAC,EAAWC,GAAQP,CAAG,EACtB,GAAAM,EACK,OAAAA,EACT,IAAIE,EAASD,GAAQ/B,EAAK,QAAQ,QAAQ,QAAQ,CAAC,EAC/C,GAAAgC,EACK,OAAAA,EACT,IAAIC,GAAS,CACX,YAAcf,EACd,QAAUD,EACV,WAAaH,EACb,OAASD,EACT,MAAQS,EACRD,EAAO,QAAUA,EAAO,GACxB,QAAUF,EACV,QAAU,QAAQ,SAAS,KAC3B,QAAQ,SAAS,SAAW,YAAc,QAAQ,SAAS,SAAW,GACtE,OAAO,qBAAwB,WAAa,eAAiB,EAE7D,EAAA,OAAO,OAAO,EAAE,KAAK,GAAG,EAC1B,MAAM,IAAI,MAAM,iCAAmCc,GAAS;AAAA,mBAAwBT,EAAM;AAAA,CAAI,EAC9F,SAASO,GAAQG,GAAM,CACjB,IAAAC,GAASC,EAAYpC,EAAK,KAAKkC,GAAM,WAAW,CAAC,EAAE,IAAIG,CAAU,EACjEC,GAAQH,GAAO,OAAOI,EAAWrB,EAAUD,CAAI,CAAC,EAAE,KAAKuB,CAAa,EAAE,CAAC,EAC3E,GAAKF,GAEL,KAAIG,GAAYzC,EAAK,KAAKkC,GAAM,YAAaI,GAAM,IAAI,EACnDI,GAASN,EAAYK,EAAS,EAAE,IAAIE,CAAS,EAC7CC,GAAaF,GAAO,OAAOG,EAAU/B,EAASD,CAAG,CAAC,EAClDiC,GAASF,GAAW,KAAKG,EAAYjC,CAAO,CAAC,EAAE,CAAC,EAChD,GAAAgC,GACF,OAAO9C,EAAK,KAAKyC,GAAWK,GAAO,IAAI,EAC3C,CAAA,EAEF,SAASV,EAAYZ,EAAK,CACpB,GAAA,CACK,OAAApB,EAAG,YAAYoB,CAAG,OACb,CACZ,MAAO,EACT,CACF,CACS,SAAAG,EAASH,EAAKwB,EAAQ,CAC7B,IAAIC,EAAQb,EAAYZ,CAAG,EAAE,OAAOwB,CAAM,EACnC,OAAAC,EAAM,CAAC,GAAKjD,EAAK,KAAKwB,EAAKyB,EAAM,CAAC,CAAC,CAC5C,CACA,SAASrB,EAAWH,EAAM,CACjB,MAAA,UAAU,KAAKA,CAAI,CAC5B,CACA,SAASY,EAAWZ,EAAM,CACpB,IAAAyB,EAAMzB,EAAK,MAAM,GAAG,EACxB,GAAIyB,EAAI,SAAW,EAEf,KAAAC,EAAYD,EAAI,CAAC,EACjBE,EAAgBF,EAAI,CAAC,EAAE,MAAM,GAAG,EACpC,GAAKC,GAEAC,EAAc,QAEdA,EAAc,MAAM,OAAO,EAEhC,MAAO,CAAE,KAAA3B,EAAM,SAAU0B,EAAW,cAAAC,CAAc,EACpD,CACS,SAAAb,EAAWY,EAAWE,EAAO,CACpC,OAAO,SAASf,EAAO,CAGrB,OAFIA,GAAS,MAETA,EAAM,WAAaa,EACd,GACFb,EAAM,cAAc,SAASe,CAAK,CAAA,CAE7C,CACS,SAAAb,EAAcvE,EAAGqF,EAAG,CAC3B,OAAOrF,EAAE,cAAc,OAASqF,EAAE,cAAc,MAClD,CACA,SAASX,EAAUY,EAAM,CACnB,IAAAL,EAAMK,EAAK,MAAM,GAAG,EACpBC,EAAaN,EAAI,MACjBO,EAAO,CAAE,KAAAF,EAAM,YAAa,CAAE,EAClC,GAAIC,IAAe,OAEnB,SAASE,EAAI,EAAGA,EAAIR,EAAI,OAAQQ,IAAK,CAC/B,IAAAC,EAAMT,EAAIQ,CAAC,EACf,GAAIC,IAAQ,QAAUA,IAAQ,YAAcA,IAAQ,cAClDF,EAAK,QAAUE,UACNA,IAAQ,OACjBF,EAAK,KAAO,WACHE,EAAI,MAAM,EAAG,CAAC,IAAM,MACxBF,EAAA,IAAME,EAAI,MAAM,CAAC,UACbA,EAAI,MAAM,EAAG,CAAC,IAAM,KACxBF,EAAA,GAAKE,EAAI,MAAM,CAAC,UACZA,EAAI,MAAM,EAAG,CAAC,IAAM,OACxBF,EAAA,KAAOE,EAAI,MAAM,CAAC,UACdA,IAAQ,SAAWA,IAAQ,OACpCF,EAAK,KAAOE,MAEZ,UAEGF,EAAA,aACP,CACO,OAAAA,EACT,CACS,SAAAZ,EAAUe,EAAUC,EAAM,CACjC,OAAO,SAASJ,EAAM,CAWhB,MAVA,EAAAA,GAAQ,MAERA,EAAK,UAAYG,GAAY,CAACE,EAAgBL,CAAI,GAElDA,EAAK,MAAQI,GAAQ,CAACJ,EAAK,MAE3BA,EAAK,IAAMA,EAAK,KAAOnC,GAEvBmC,EAAK,MAAQA,EAAK,OAASpC,GAE3BoC,EAAK,MAAQA,EAAK,OAAStC,EAExB,CAEX,CACA,SAAS2C,EAAgBL,EAAM,CACtB,OAAAA,EAAK,UAAY,QAAUA,EAAK,IACzC,CACA,SAASV,EAAYa,EAAU,CACtB,OAAA,SAAS3F,EAAGqF,EAAG,CAChB,OAAArF,EAAE,UAAYqF,EAAE,QACXrF,EAAE,UAAY2F,EAAW,GAAK,EAC5B3F,EAAE,MAAQqF,EAAE,IACdrF,EAAE,IAAM,GAAK,EACXA,EAAE,cAAgBqF,EAAE,YACtBrF,EAAE,YAAcqF,EAAE,YAAc,GAAK,EAErC,CACT,CAEJ,CACA,SAAStC,GAAS,CAChB,MAAO,CAAC,EAAE,QAAQ,UAAY,QAAQ,SAAS,GACjD,CACA,SAASD,IAAa,CAGpB,OAFI,QAAQ,UAAY,QAAQ,SAAS,UAErCH,EAAY,qBACP,GACF,OAAO,OAAW,KAAe,OAAO,SAAW,OAAO,QAAQ,OAAS,UACpF,CACA,SAASQ,GAAS+B,EAAW,CAC3B,OAAOA,IAAc,SAAW/C,EAAG,WAAW,qBAAqB,CACrE,CACA,OAAAmB,EAAK,UAAYoB,EACjBpB,EAAK,UAAYsB,EACjBtB,EAAK,YAAcwB,EACnBxB,EAAK,WAAac,EAClBd,EAAK,WAAagB,EAClBhB,EAAK,cAAgBiB,EACdvC,EACT,CACA,IAAI8D,GACJ,SAASC,IAAsB,CACzB,OAAAD,KAEsBA,GAAA,EACtB,OAAO,QAAQ,OAAU,WAC3BjE,GAAe,QAAU,QAAQ,MAAM,KAAK,OAAO,EAEnDA,GAAe,QAAUK,MAEpBL,GAAe,OACxB,CACA,IAAImE,GACAC,GACJ,SAASC,IAAkB,CACrB,OAAAD,KAEkBA,GAAA,EAYtBD,GAAW,CAAE,KAXC,CAACG,EAAQC,EAAOC,EAAQC,EAAQC,IAAW,CACvD,QAASd,EAAI,EAAGA,EAAIc,EAAQd,IACnBY,EAAAC,EAASb,CAAC,EAAIU,EAAOV,CAAC,EAAIW,EAAMX,EAAI,CAAC,CAC9C,EAQwB,OANV,CAACe,EAAQJ,IAAU,CACjC,MAAMG,EAASC,EAAO,OACtB,QAASf,EAAI,EAAGA,EAAIc,EAAQd,IAC1Be,EAAOf,CAAC,GAAKW,EAAMX,EAAI,CAAC,CAC1B,CAEwC,GACnCO,EACT,CACA,IAAIS,GACJ,SAASC,IAAoB,CACvB,GAAAD,GACF,OAAO7E,GAAW,QACI6E,GAAA,EACpB,GAAA,CACS7E,GAAA,QAAUmE,GAAoB,EAAE,SAAS,OAC1C,CACVnE,GAAW,QAAUsE,IACvB,CACA,OAAOtE,GAAW,OACpB,CACA,IAAI+E,GACAC,GACJ,KAAM,CAAE,aAAcC,EAAmB,EAAAlF,EACnCmF,GAAe,OAAO,OAAO,OAAO,EAC1C,SAASC,GAASC,EAAMC,EAAa,CACnC,GAAID,EAAK,SAAW,EACX,OAAAH,GACT,GAAIG,EAAK,SAAW,EAClB,OAAOA,EAAK,CAAC,EACT,MAAAhD,EAAS,OAAO,YAAYiD,CAAW,EAC7C,IAAIX,EAAS,EACb,QAAS,EAAI,EAAG,EAAIU,EAAK,OAAQ,IAAK,CAC9B,MAAAE,EAAMF,EAAK,CAAC,EACXhD,EAAA,IAAIkD,EAAKZ,CAAM,EACtBA,GAAUY,EAAI,MAChB,CACA,OAAIZ,EAASW,EACJ,IAAIH,GAAa9C,EAAO,OAAQA,EAAO,WAAYsC,CAAM,EAE3DtC,CACT,CACA,SAASmD,GAAMhB,EAAQiB,EAAOf,EAAQC,EAAQC,EAAQ,CACpD,QAASd,EAAI,EAAGA,EAAIc,EAAQd,IACnBY,EAAAC,EAASb,CAAC,EAAIU,EAAOV,CAAC,EAAI2B,EAAM3B,EAAI,CAAC,CAEhD,CACA,SAAS4B,GAAQb,EAAQY,EAAO,CAC9B,QAAS3B,EAAI,EAAGA,EAAIe,EAAO,OAAQf,IACjCe,EAAOf,CAAC,GAAK2B,EAAM3B,EAAI,CAAC,CAE5B,CACA,SAAS6B,GAAgBJ,EAAK,CAC5B,OAAIA,EAAI,SAAWA,EAAI,OAAO,WACrBA,EAAI,OAENA,EAAI,OAAO,MAAMA,EAAI,WAAYA,EAAI,WAAaA,EAAI,MAAM,CACrE,CACA,SAASK,GAAWrG,EAAM,CAEpB,GADJqG,GAAW,SAAW,GAClB,OAAO,SAASrG,CAAI,EACf,OAAAA,EACL,IAAAgG,EACJ,OAAIhG,aAAgB,YACZgG,EAAA,IAAIJ,GAAa5F,CAAI,EAClB,YAAY,OAAOA,CAAI,EAChCgG,EAAM,IAAIJ,GAAa5F,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,GAE9DgG,EAAA,OAAO,KAAKhG,CAAI,EACtBqG,GAAW,SAAW,IAEjBL,CACT,CACAxF,GAAa,QAAU,CACrB,OAAQqF,GACR,KAAMI,GACN,cAAeG,GACf,SAAUC,GACV,OAAQF,EACV,EACA,GAAI,CAAC1E,EAAY,kBACX,GAAA,CACF,MAAM6E,EAAcd,KACbE,GAAAlF,GAAa,QAAQ,KAAO,SAASyE,EAAQiB,EAAOf,EAAQC,EAAQC,EAAQ,CAC7EA,EAAS,GACXY,GAAMhB,EAAQiB,EAAOf,EAAQC,EAAQC,CAAM,EAE3CiB,EAAY,KAAKrB,EAAQiB,EAAOf,EAAQC,EAAQC,CAAM,CAAA,EAE1DI,GAAWjF,GAAa,QAAQ,OAAS,SAAS8E,EAAQY,EAAO,CAC3DZ,EAAO,OAAS,GAClBa,GAAQb,EAAQY,CAAK,EAETI,EAAA,OAAOhB,EAAQY,CAAK,CAAA,OAE1B,CACZ,CAEF,IAAIK,GAAoB/F,GAAa,QACrC,MAAMgG,GAAQ,OAAO,OAAO,EACtBC,GAAO,OAAO,MAAM,EAC1B,IAAIC,GAAY,KAAc,CAO5B,YAAYC,EAAa,CAClB,KAAAH,EAAK,EAAI,IAAM,CACb,KAAA,UACL,KAAKC,EAAI,GAAE,EAEb,KAAK,YAAcE,GAAe,IAClC,KAAK,KAAO,GACZ,KAAK,QAAU,CACjB,CAOA,IAAIC,EAAK,CACF,KAAA,KAAK,KAAKA,CAAG,EAClB,KAAKH,EAAI,GACX,CAMA,CAACA,EAAI,GAAI,CACH,GAAA,KAAK,UAAY,KAAK,aAEtB,KAAK,KAAK,OAAQ,CACd,MAAAG,EAAM,KAAK,KAAK,MAAM,EACvB,KAAA,UACDA,EAAA,KAAKJ,EAAK,CAAC,CACjB,CACF,CACF,EACA,IAAIK,GAAUH,GACd,MAAMI,EAAOC,EACPC,GAAaT,GACbU,GAAWJ,GACX,CAAE,YAAaK,EAAkB,EAAAzG,EACjC0G,GAAe,OAAO,OAAO,OAAO,EACpCC,GAAU,OAAO,KAAK,CAAC,EAAG,EAAG,IAAK,GAAG,CAAC,EACtCC,GAAqB,OAAO,oBAAoB,EAChDC,EAAe,OAAO,cAAc,EACpCC,GAAY,OAAO,UAAU,EAC7BC,EAAW,OAAO,SAAS,EAC3BC,GAAW,OAAO,OAAO,EAC/B,IAAIC,GACAC,GAAsB,KAAwB,CAyBhD,YAAYhI,EAASiI,EAAUC,EAAY,CAQzC,GAPA,KAAK,YAAcA,EAAa,EAC3B,KAAA,SAAWlI,GAAW,GAC3B,KAAK,WAAa,KAAK,SAAS,YAAc,OAAS,KAAK,SAAS,UAAY,KAC5E,KAAA,UAAY,CAAC,CAACiI,EACnB,KAAK,SAAW,KAChB,KAAK,SAAW,KAChB,KAAK,OAAS,KACV,CAACF,GAAa,CAChB,MAAMf,EAAc,KAAK,SAAS,mBAAqB,OAAS,KAAK,SAAS,iBAAmB,GACnFe,GAAA,IAAIT,GAASN,CAAW,CACxC,CACF,CAIA,WAAW,eAAgB,CAClB,MAAA,oBACT,CAOA,OAAQ,CACN,MAAMmB,EAAS,CAAA,EACX,OAAA,KAAK,SAAS,0BAChBA,EAAO,2BAA6B,IAElC,KAAK,SAAS,0BAChBA,EAAO,2BAA6B,IAElC,KAAK,SAAS,sBACTA,EAAA,uBAAyB,KAAK,SAAS,qBAE5C,KAAK,SAAS,oBACTA,EAAA,uBAAyB,KAAK,SAAS,oBACrC,KAAK,SAAS,qBAAuB,OAC9CA,EAAO,uBAAyB,IAE3BA,CACT,CAQA,OAAOC,EAAgB,CACJ,OAAAA,EAAA,KAAK,gBAAgBA,CAAc,EAC/C,KAAA,OAAS,KAAK,UAAY,KAAK,eAAeA,CAAc,EAAI,KAAK,eAAeA,CAAc,EAChG,KAAK,MACd,CAMA,SAAU,CAKR,GAJI,KAAK,WACP,KAAK,SAAS,QACd,KAAK,SAAW,MAEd,KAAK,SAAU,CACX,MAAA9H,EAAW,KAAK,SAASsH,EAAS,EACxC,KAAK,SAAS,QACd,KAAK,SAAW,KACZtH,GACFA,EACE,IAAI,MACF,8DACF,CAAA,CAGN,CACF,CAQA,eAAe+H,EAAQ,CACrB,MAAMC,EAAO,KAAK,SACZC,EAAWF,EAAO,KAAMF,GACxB,EAAAG,EAAK,0BAA4B,IAASH,EAAO,4BAA8BA,EAAO,yBAA2BG,EAAK,sBAAwB,IAAS,OAAOA,EAAK,qBAAwB,UAAYA,EAAK,oBAAsBH,EAAO,yBAA2B,OAAOG,EAAK,qBAAwB,UAAY,CAACH,EAAO,uBAIjU,EACD,GAAI,CAACI,EACG,MAAA,IAAI,MAAM,8CAA8C,EAEhE,OAAID,EAAK,0BACPC,EAAS,2BAA6B,IAEpCD,EAAK,0BACPC,EAAS,2BAA6B,IAEpC,OAAOD,EAAK,qBAAwB,WACtCC,EAAS,uBAAyBD,EAAK,qBAErC,OAAOA,EAAK,qBAAwB,SACtCC,EAAS,uBAAyBD,EAAK,qBAC9BC,EAAS,yBAA2B,IAAQD,EAAK,sBAAwB,KAClF,OAAOC,EAAS,uBAEXA,CACT,CAQA,eAAeC,EAAU,CACjB,MAAAL,EAASK,EAAS,CAAC,EACzB,GAAI,KAAK,SAAS,0BAA4B,IAASL,EAAO,2BACtD,MAAA,IAAI,MAAM,mDAAmD,EAEjE,GAAA,CAACA,EAAO,uBACN,OAAO,KAAK,SAAS,qBAAwB,WACxCA,EAAA,uBAAyB,KAAK,SAAS,6BAEvC,KAAK,SAAS,sBAAwB,IAAS,OAAO,KAAK,SAAS,qBAAwB,UAAYA,EAAO,uBAAyB,KAAK,SAAS,oBAC/J,MAAM,IAAI,MACR,0DAAA,EAGG,OAAAA,CACT,CAQA,gBAAgBC,EAAgB,CACf,OAAAA,EAAA,QAASD,GAAW,CACjC,OAAO,KAAKA,CAAM,EAAE,QAASM,GAAQ,CAC/B,IAAAC,EAAQP,EAAOM,CAAG,EAClB,GAAAC,EAAM,OAAS,EACjB,MAAM,IAAI,MAAM,cAAcD,CAAG,iCAAiC,EAGpE,GADAC,EAAQA,EAAM,CAAC,EACXD,IAAQ,0BACV,GAAIC,IAAU,GAAM,CAClB,MAAMC,EAAM,CAACD,EACT,GAAA,CAAC,OAAO,UAAUC,CAAG,GAAKA,EAAM,GAAKA,EAAM,GAC7C,MAAM,IAAI,UACR,gCAAgCF,CAAG,MAAMC,CAAK,EAAA,EAG1CA,EAAAC,CAAA,SACC,CAAC,KAAK,UACf,MAAM,IAAI,UACR,gCAAgCF,CAAG,MAAMC,CAAK,EAAA,UAGzCD,IAAQ,yBAA0B,CAC3C,MAAME,EAAM,CAACD,EACT,GAAA,CAAC,OAAO,UAAUC,CAAG,GAAKA,EAAM,GAAKA,EAAM,GAC7C,MAAM,IAAI,UACR,gCAAgCF,CAAG,MAAMC,CAAK,EAAA,EAG1CA,EAAAC,CACC,SAAAF,IAAQ,8BAAgCA,IAAQ,8BACzD,GAAIC,IAAU,GACZ,MAAM,IAAI,UACR,gCAAgCD,CAAG,MAAMC,CAAK,EAAA,MAIlD,OAAM,IAAI,MAAM,sBAAsBD,CAAG,GAAG,EAE9CN,EAAOM,CAAG,EAAIC,CAAA,CACf,CAAA,CACF,EACMN,CACT,CASA,WAAW/H,EAAMuI,EAAKtI,EAAU,CAClByH,GAAA,IAAKc,GAAS,CACxB,KAAK,YAAYxI,EAAMuI,EAAK,CAAC/I,EAAKiJ,IAAW,CACtCD,IACLvI,EAAST,EAAKiJ,CAAM,CAAA,CACrB,CAAA,CACF,CACH,CASA,SAASzI,EAAMuI,EAAKtI,EAAU,CAChByH,GAAA,IAAKc,GAAS,CACxB,KAAK,UAAUxI,EAAMuI,EAAK,CAAC/I,EAAKiJ,IAAW,CACpCD,IACLvI,EAAST,EAAKiJ,CAAM,CAAA,CACrB,CAAA,CACF,CACH,CASA,YAAYzI,EAAMuI,EAAKtI,EAAU,CACzB,MAAAyI,EAAW,KAAK,UAAY,SAAW,SACzC,GAAA,CAAC,KAAK,SAAU,CACZ,MAAAN,EAAM,GAAGM,CAAQ,mBACjBC,EAAa,OAAO,KAAK,OAAOP,CAAG,GAAM,SAAWtB,EAAK,qBAAuB,KAAK,OAAOsB,CAAG,EAChG,KAAA,SAAWtB,EAAK,iBAAiB,CACpC,GAAG,KAAK,SAAS,mBACjB,WAAA6B,CAAA,CACD,EACI,KAAA,SAAStB,EAAkB,EAAI,KAC/B,KAAA,SAASC,CAAY,EAAI,EACzB,KAAA,SAASE,CAAQ,EAAI,GACrB,KAAA,SAAS,GAAG,QAASoB,EAAc,EACnC,KAAA,SAAS,GAAG,OAAQC,EAAa,CACxC,CACK,KAAA,SAAStB,EAAS,EAAItH,EACtB,KAAA,SAAS,MAAMD,CAAI,EACpBuI,GACG,KAAA,SAAS,MAAMnB,EAAO,EACxB,KAAA,SAAS,MAAM,IAAM,CAClB,MAAA5H,EAAM,KAAK,SAASiI,EAAQ,EAClC,GAAIjI,EAAK,CACP,KAAK,SAAS,QACd,KAAK,SAAW,KAChBS,EAAST,CAAG,EACZ,MACF,CACA,MAAMsJ,EAAQ9B,GAAW,OACvB,KAAK,SAASQ,CAAQ,EACtB,KAAK,SAASF,CAAY,CAAA,EAExB,KAAK,SAAS,eAAe,YAC/B,KAAK,SAAS,QACd,KAAK,SAAW,OAEX,KAAA,SAASA,CAAY,EAAI,EACzB,KAAA,SAASE,CAAQ,EAAI,GACtBe,GAAO,KAAK,OAAO,GAAGG,CAAQ,sBAAsB,GACtD,KAAK,SAAS,SAGlBzI,EAAS,KAAM6I,CAAK,CAAA,CACrB,CACH,CASA,UAAU9I,EAAMuI,EAAKtI,EAAU,CACvB,MAAAyI,EAAW,KAAK,UAAY,SAAW,SACzC,GAAA,CAAC,KAAK,SAAU,CACZ,MAAAN,EAAM,GAAGM,CAAQ,mBACjBC,EAAa,OAAO,KAAK,OAAOP,CAAG,GAAM,SAAWtB,EAAK,qBAAuB,KAAK,OAAOsB,CAAG,EAChG,KAAA,SAAWtB,EAAK,iBAAiB,CACpC,GAAG,KAAK,SAAS,mBACjB,WAAA6B,CAAA,CACD,EACI,KAAA,SAASrB,CAAY,EAAI,EACzB,KAAA,SAASE,CAAQ,EAAI,GACrB,KAAA,SAAS,GAAG,OAAQuB,EAAa,CACxC,CACK,KAAA,SAASxB,EAAS,EAAItH,EACtB,KAAA,SAAS,MAAMD,CAAI,EACxB,KAAK,SAAS,MAAM8G,EAAK,aAAc,IAAM,CACvC,GAAA,CAAC,KAAK,SACR,OAEF,IAAIgC,EAAQ9B,GAAW,OACrB,KAAK,SAASQ,CAAQ,EACtB,KAAK,SAASF,CAAY,CAAA,EAExBiB,IACMO,EAAA,IAAI3B,GAAa2B,EAAM,OAAQA,EAAM,WAAYA,EAAM,OAAS,CAAC,GAEtE,KAAA,SAASvB,EAAS,EAAI,KACtB,KAAA,SAASD,CAAY,EAAI,EACzB,KAAA,SAASE,CAAQ,EAAI,GACtBe,GAAO,KAAK,OAAO,GAAGG,CAAQ,sBAAsB,GACtD,KAAK,SAAS,QAEhBzI,EAAS,KAAM6I,CAAK,CAAA,CACrB,CACH,CACF,EACA,IAAIE,GAAoBrB,GACxB,SAASoB,GAAc3I,EAAO,CACvB,KAAAoH,CAAQ,EAAE,KAAKpH,CAAK,EACpB,KAAAkH,CAAY,GAAKlH,EAAM,MAC9B,CACA,SAASyI,GAAczI,EAAO,CAExB,GADC,KAAAkH,CAAY,GAAKlH,EAAM,OACxB,KAAKiH,EAAkB,EAAE,YAAc,GAAK,KAAKC,CAAY,GAAK,KAAKD,EAAkB,EAAE,YAAa,CACrG,KAAAG,CAAQ,EAAE,KAAKpH,CAAK,EACzB,MACF,CACA,KAAKqH,EAAQ,EAAI,IAAI,WAAW,2BAA2B,EACtD,KAAAA,EAAQ,EAAE,KAAO,oCACjB,KAAAA,EAAQ,EAAEP,EAAa,EAAI,KAC3B,KAAA,eAAe,OAAQ2B,EAAa,EACzC,KAAK,MAAM,CACb,CACA,SAASD,GAAepJ,EAAK,CACtB,KAAA6H,EAAkB,EAAE,SAAW,KACpC7H,EAAI0H,EAAa,EAAI,KAChB,KAAAK,EAAS,EAAE/H,CAAG,CACrB,CACA,IAAIyJ,GAAa,CAAE,QAAS,CAAA,GAC5B,MAAMC,GAAwC,CAAA,EACxCC,GAA0D,OAAO,OAAuB,OAAO,eAAe,CAClH,UAAW,KACX,QAASD,EACX,EAAG,OAAO,YAAa,CAAE,MAAO,QAAA,CAAU,CAAC,EACrCE,MAAmDD,EAAuC,EAChG,IAAIE,GACJ,KAAM,CAAE,OAAAC,EAAW,EAAAC,EACbC,GAAe,CACnB,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAEA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,CAEF,EACA,SAASC,GAAoBC,EAAM,CACjC,OAAOA,GAAQ,KAAOA,GAAQ,MAAQA,IAAS,MAAQA,IAAS,MAAQA,IAAS,MAAQA,GAAQ,KAAOA,GAAQ,IAClH,CACA,SAASC,GAAa3D,EAAK,CACzB,MAAM4D,EAAM5D,EAAI,OAChB,IAAIzB,EAAI,EACR,KAAOA,EAAIqF,GACT,GAAK,EAAA5D,EAAIzB,CAAC,EAAI,KACZA,aACUyB,EAAIzB,CAAC,EAAI,OAAS,IAAK,CACjC,GAAIA,EAAI,IAAMqF,IAAQ5D,EAAIzB,EAAI,CAAC,EAAI,OAAS,MAAQyB,EAAIzB,CAAC,EAAI,OAAS,IAC7D,MAAA,GAEJA,GAAA,CACK,UAAAyB,EAAIzB,CAAC,EAAI,OAAS,IAAK,CAC7B,GAAAA,EAAI,GAAKqF,IAAQ5D,EAAIzB,EAAI,CAAC,EAAI,OAAS,MAAQyB,EAAIzB,EAAI,CAAC,EAAI,OAAS,KAAOyB,EAAIzB,CAAC,IAAM,MAAQyB,EAAIzB,EAAI,CAAC,EAAI,OAAS,KACzHyB,EAAIzB,CAAC,IAAM,MAAQyB,EAAIzB,EAAI,CAAC,EAAI,OAAS,IAChC,MAAA,GAEJA,GAAA,CACK,UAAAyB,EAAIzB,CAAC,EAAI,OAAS,IAAK,CACjC,GAAIA,EAAI,GAAKqF,IAAQ5D,EAAIzB,EAAI,CAAC,EAAI,OAAS,MAAQyB,EAAIzB,EAAI,CAAC,EAAI,OAAS,MAAQyB,EAAIzB,EAAI,CAAC,EAAI,OAAS,KAAOyB,EAAIzB,CAAC,IAAM,MAAQyB,EAAIzB,EAAI,CAAC,EAAI,OAAS,KACvJyB,EAAIzB,CAAC,IAAM,KAAOyB,EAAIzB,EAAI,CAAC,EAAI,KAAOyB,EAAIzB,CAAC,EAAI,IACtC,MAAA,GAEJA,GAAA,CAAA,KAEE,OAAA,GAGJ,MAAA,EACT,CACA0E,GAAW,QAAU,CACnB,kBAAmBQ,GACnB,YAAaE,GACb,WAAYH,EACd,EACA,GAAIF,GACFD,GAAgBJ,GAAW,QAAQ,YAAc,SAASjD,EAAK,CAC7D,OAAOA,EAAI,OAAS,GAAK2D,GAAa3D,CAAG,EAAIsD,GAAOtD,CAAG,CAAA,UAEhD,CAACvE,EAAY,qBAClB,GAAA,CACF,MAAMoI,EAAeT,GACrBC,GAAgBJ,GAAW,QAAQ,YAAc,SAASjD,EAAK,CAC7D,OAAOA,EAAI,OAAS,GAAK2D,GAAa3D,CAAG,EAAI6D,EAAa7D,CAAG,CAAA,OAErD,CACZ,CAEF,IAAI8D,GAAoBb,GAAW,QACnC,KAAM,CAAE,SAAAc,EAAa,EAAA5K,EACf6K,GAAsBhB,GACtB,CACJ,aAAciB,GACd,aAAcC,GACd,YAAaC,GACb,WAAYC,EACd,EAAI3J,EACE,CAAE,OAAA4J,GAAQ,cAAAC,GAAe,OAAAC,EAAA,EAAWhE,GACpC,CAAE,kBAAmBiE,GAAqB,YAAAC,EAAA,EAAgBX,GAC1DY,GAAa,OAAO,OAAO,OAAO,EAClCC,EAAW,EACXC,GAAwB,EACxBC,GAAwB,EACxBC,GAAW,EACXC,GAAW,EACXC,GAAY,EAClB,IAAIC,GAAa,cAAuBlB,EAAS,CAc/C,YAAYpK,EAAU,GAAI,CAClB,QACN,KAAK,YAAcA,EAAQ,YAAcsK,GAAe,CAAC,EACpD,KAAA,YAActK,EAAQ,YAAc,CAAA,EACpC,KAAA,UAAY,CAAC,CAACA,EAAQ,SACtB,KAAA,YAAcA,EAAQ,WAAa,EACnC,KAAA,oBAAsB,CAAC,CAACA,EAAQ,mBACrC,KAAKyK,EAAY,EAAI,OACrB,KAAK,eAAiB,EACtB,KAAK,SAAW,GAChB,KAAK,YAAc,GACnB,KAAK,eAAiB,EACtB,KAAK,MAAQ,OACb,KAAK,YAAc,EACnB,KAAK,QAAU,GACf,KAAK,KAAO,GACZ,KAAK,QAAU,EACf,KAAK,oBAAsB,EAC3B,KAAK,eAAiB,EACtB,KAAK,WAAa,GAClB,KAAK,OAASO,EACd,KAAK,MAAQ,EACf,CASA,OAAOvK,EAAOC,EAAU6K,EAAI,CAC1B,GAAI,KAAK,UAAY,GAAK,KAAK,QAAUP,EACvC,OAAOO,EAAG,EACZ,KAAK,gBAAkB9K,EAAM,OACxB,KAAA,SAAS,KAAKA,CAAK,EACxB,KAAK,UAAU8K,CAAE,CACnB,CAQA,QAAQtM,EAAG,CAET,GADA,KAAK,gBAAkBA,EACnBA,IAAM,KAAK,SAAS,CAAC,EAAE,OAClB,OAAA,KAAK,SAAS,QACvB,GAAIA,EAAI,KAAK,SAAS,CAAC,EAAE,OAAQ,CACzB,MAAAoH,EAAM,KAAK,SAAS,CAAC,EACtB,YAAA,SAAS,CAAC,EAAI,IAAI0E,GACrB1E,EAAI,OACJA,EAAI,WAAapH,EACjBoH,EAAI,OAASpH,CAAA,EAER,IAAI8L,GAAW1E,EAAI,OAAQA,EAAI,WAAYpH,CAAC,CACrD,CACM,MAAAuM,EAAM,OAAO,YAAYvM,CAAC,EAC7B,EAAA,CACK,MAAAoH,EAAM,KAAK,SAAS,CAAC,EACrBZ,EAAS+F,EAAI,OAASvM,EACxBA,GAAKoH,EAAI,OACXmF,EAAI,IAAI,KAAK,SAAS,MAAA,EAAS/F,CAAM,GAEjC+F,EAAA,IAAI,IAAI,WAAWnF,EAAI,OAAQA,EAAI,WAAYpH,CAAC,EAAGwG,CAAM,EACxD,KAAA,SAAS,CAAC,EAAI,IAAIsF,GACrB1E,EAAI,OACJA,EAAI,WAAapH,EACjBoH,EAAI,OAASpH,CAAA,GAGjBA,GAAKoH,EAAI,MAAA,OACFpH,EAAI,GACN,OAAAuM,CACT,CAOA,UAAUD,EAAI,CACR,IAAA1L,EACJ,KAAK,MAAQ,GACV,EACD,QAAQ,KAAK,OAAQ,CACnB,KAAKmL,EACHnL,EAAM,KAAK,UACX,MACF,KAAKoL,GACHpL,EAAM,KAAK,qBACX,MACF,KAAKqL,GACHrL,EAAM,KAAK,qBACX,MACF,KAAKsL,GACH,KAAK,QAAQ,EACb,MACF,KAAKC,GACGvL,EAAA,KAAK,QAAQ0L,CAAE,EACrB,MACF,QACE,KAAK,MAAQ,GACb,MACJ,OACO,KAAK,OACdA,EAAG1L,CAAG,CACR,CAOA,SAAU,CACJ,GAAA,KAAK,eAAiB,EAAG,CAC3B,KAAK,MAAQ,GACb,MACF,CACM,MAAAwG,EAAM,KAAK,QAAQ,CAAC,EAC1B,GAAKA,EAAI,CAAC,EAAI,GACZ,YAAK,MAAQ,GACNoF,EACL,WACA,8BACA,GACA,KACA,2BAAA,EAGJ,MAAMC,GAAcrF,EAAI,CAAC,EAAI,MAAQ,GACrC,GAAIqF,GAAc,CAAC,KAAK,YAAYrB,GAAoB,aAAa,EACnE,YAAK,MAAQ,GACNoB,EACL,WACA,qBACA,GACA,KACA,yBAAA,EAMA,GAHJ,KAAK,MAAQpF,EAAI,CAAC,EAAI,OAAS,IAC1B,KAAA,QAAUA,EAAI,CAAC,EAAI,GACnB,KAAA,eAAiBA,EAAI,CAAC,EAAI,IAC3B,KAAK,UAAY,EAAG,CACtB,GAAIqF,EACF,YAAK,MAAQ,GACND,EACL,WACA,qBACA,GACA,KACA,yBAAA,EAGA,GAAA,CAAC,KAAK,YACR,YAAK,MAAQ,GACNA,EACL,WACA,mBACA,GACA,KACA,uBAAA,EAGJ,KAAK,QAAU,KAAK,WAAA,SACX,KAAK,UAAY,GAAK,KAAK,UAAY,EAAG,CACnD,GAAI,KAAK,YACP,YAAK,MAAQ,GACNA,EACL,WACA,kBAAkB,KAAK,OAAO,GAC9B,GACA,KACA,uBAAA,EAGJ,KAAK,YAAcC,CAAA,SACV,KAAK,QAAU,GAAK,KAAK,QAAU,GAAI,CAC5C,GAAA,CAAC,KAAK,KACR,YAAK,MAAQ,GACND,EACL,WACA,kBACA,GACA,KACA,qBAAA,EAGJ,GAAIC,EACF,YAAK,MAAQ,GACND,EACL,WACA,qBACA,GACA,KACA,yBAAA,EAGA,GAAA,KAAK,eAAiB,KAAO,KAAK,UAAY,GAAK,KAAK,iBAAmB,EAC7E,YAAK,MAAQ,GACNA,EACL,WACA,0BAA0B,KAAK,cAAc,GAC7C,GACA,KACA,uCAAA,CAEJ,KAEA,aAAK,MAAQ,GACNA,EACL,WACA,kBAAkB,KAAK,OAAO,GAC9B,GACA,KACA,uBAAA,EAMJ,GAHI,CAAC,KAAK,MAAQ,CAAC,KAAK,cACtB,KAAK,YAAc,KAAK,SAC1B,KAAK,SAAWpF,EAAI,CAAC,EAAI,OAAS,IAC9B,KAAK,WACH,GAAA,CAAC,KAAK,QACR,YAAK,MAAQ,GACNoF,EACL,WACA,mBACA,GACA,KACA,sBAAA,UAGK,KAAK,QACd,YAAK,MAAQ,GACNA,EACL,WACA,qBACA,GACA,KACA,wBAAA,EAGJ,GAAI,KAAK,iBAAmB,IAC1B,KAAK,OAASR,WACP,KAAK,iBAAmB,IAC/B,KAAK,OAASC,OAEd,QAAO,KAAK,YAChB,CAOA,oBAAqB,CACf,GAAA,KAAK,eAAiB,EAAG,CAC3B,KAAK,MAAQ,GACb,MACF,CACA,YAAK,eAAiB,KAAK,QAAQ,CAAC,EAAE,aAAa,CAAC,EAC7C,KAAK,YACd,CAOA,oBAAqB,CACf,GAAA,KAAK,eAAiB,EAAG,CAC3B,KAAK,MAAQ,GACb,MACF,CACM,MAAA7E,EAAM,KAAK,QAAQ,CAAC,EACpBsC,EAAMtC,EAAI,aAAa,CAAC,EAC9B,OAAIsC,EAAM,KAAK,IAAI,EAAG,EAAO,EAAI,GAC/B,KAAK,MAAQ,GACN8C,EACL,WACA,yDACA,GACA,KACA,wCAAA,IAGC,KAAA,eAAiB9C,EAAM,KAAK,IAAI,EAAG,EAAE,EAAItC,EAAI,aAAa,CAAC,EACzD,KAAK,aACd,CAOA,YAAa,CACX,GAAI,KAAK,gBAAkB,KAAK,QAAU,IACxC,KAAK,qBAAuB,KAAK,eAC7B,KAAK,oBAAsB,KAAK,aAAe,KAAK,YAAc,GACpE,YAAK,MAAQ,GACNoF,EACL,WACA,4BACA,GACA,KACA,mCAAA,EAIF,KAAK,QACP,KAAK,OAASN,GAEd,KAAK,OAASC,EAClB,CAMA,SAAU,CACJ,GAAA,KAAK,eAAiB,EAAG,CAC3B,KAAK,MAAQ,GACb,MACF,CACK,KAAA,MAAQ,KAAK,QAAQ,CAAC,EAC3B,KAAK,OAASA,EAChB,CAQA,QAAQG,EAAI,CACV,IAAIlL,EAAOkK,GACX,GAAI,KAAK,eAAgB,CACnB,GAAA,KAAK,eAAiB,KAAK,eAAgB,CAC7C,KAAK,MAAQ,GACb,MACF,CACOlK,EAAA,KAAK,QAAQ,KAAK,cAAc,EACnC,KAAK,SAAY,KAAK,MAAM,CAAC,EAAI,KAAK,MAAM,CAAC,EAAI,KAAK,MAAM,CAAC,EAAI,KAAK,MAAM,CAAC,GACxEuK,GAAAvK,EAAM,KAAK,KAAK,CAE3B,CACA,GAAI,KAAK,QAAU,EACV,OAAA,KAAK,eAAeA,CAAI,EACjC,GAAI,KAAK,YAAa,CACpB,KAAK,OAASgL,GACT,KAAA,WAAWhL,EAAMkL,CAAE,EACxB,MACF,CACA,OAAIlL,EAAK,SACP,KAAK,eAAiB,KAAK,oBACtB,KAAA,WAAW,KAAKA,CAAI,GAEpB,KAAK,aACd,CAQA,WAAWA,EAAMkL,EAAI,CACO,KAAK,YAAYlB,GAAoB,aAAa,EAC1D,WAAWhK,EAAM,KAAK,KAAM,CAACR,EAAKwG,IAAQ,CACtD,GAAAxG,EACF,OAAO0L,EAAG1L,CAAG,EACf,GAAIwG,EAAI,OAAQ,CAEd,GADA,KAAK,gBAAkBA,EAAI,OACvB,KAAK,eAAiB,KAAK,aAAe,KAAK,YAAc,EACxD,OAAAkF,EACLE,EACE,WACA,4BACA,GACA,KACA,mCACF,CAAA,EAGC,KAAA,WAAW,KAAKpF,CAAG,CAC1B,CACM,MAAAsF,EAAK,KAAK,cACZ,GAAAA,EACF,OAAOJ,EAAGI,CAAE,EACd,KAAK,UAAUJ,CAAE,CAAA,CAClB,CACH,CAOA,aAAc,CACZ,GAAI,KAAK,KAAM,CACb,MAAMK,EAAgB,KAAK,eACrBC,EAAY,KAAK,WAKnB,GAJJ,KAAK,oBAAsB,EAC3B,KAAK,eAAiB,EACtB,KAAK,YAAc,EACnB,KAAK,WAAa,GACd,KAAK,UAAY,EAAG,CAClB,IAAAxL,EACA,KAAK,cAAgB,aAChBA,EAAAqK,GAAOmB,EAAWD,CAAa,EAC7B,KAAK,cAAgB,cAC9BvL,EAAOsK,GAAcD,GAAOmB,EAAWD,CAAa,CAAC,EAE9CvL,EAAAwL,EAEJ,KAAA,KAAK,UAAWxL,EAAM,EAAI,CAAA,KAC1B,CACC,MAAAgG,EAAMqE,GAAOmB,EAAWD,CAAa,EAC3C,GAAI,CAAC,KAAK,qBAAuB,CAACd,GAAYzE,CAAG,EAC/C,YAAK,MAAQ,GACNoF,EACL,MACA,yBACA,GACA,KACA,qBAAA,EAGC,KAAA,KAAK,UAAWpF,EAAK,EAAK,CACjC,CACF,CACA,KAAK,OAAS2E,CAChB,CAQA,eAAe3K,EAAM,CACf,GAAA,KAAK,UAAY,EAEf,GADJ,KAAK,MAAQ,GACTA,EAAK,SAAW,EACb,KAAA,KAAK,WAAY,KAAMkK,EAAc,EAC1C,KAAK,IAAI,MACJ,CACC,MAAAR,EAAO1J,EAAK,aAAa,CAAC,EAC5B,GAAA,CAACwK,GAAoBd,CAAI,EACpB,OAAA0B,EACL,WACA,uBAAuB1B,CAAI,GAC3B,GACA,KACA,2BAAA,EAGJ,MAAM1D,EAAM,IAAI0E,GACd1K,EAAK,OACLA,EAAK,WAAa,EAClBA,EAAK,OAAS,CAAA,EAEhB,GAAI,CAAC,KAAK,qBAAuB,CAACyK,GAAYzE,CAAG,EACxC,OAAAoF,EACL,MACA,yBACA,GACA,KACA,qBAAA,EAGC,KAAA,KAAK,WAAY1B,EAAM1D,CAAG,EAC/B,KAAK,IAAI,CACX,MACS,KAAK,UAAY,EACrB,KAAA,KAAK,OAAQhG,CAAI,EAEjB,KAAA,KAAK,OAAQA,CAAI,EAExB,KAAK,OAAS2K,CAChB,CACF,EACA,IAAIc,GAAWR,GACf,SAASG,EAAMM,EAAWC,EAASC,EAAQC,EAAYC,EAAW,CAChE,MAAMtM,EAAM,IAAIkM,EACdE,EAAS,4BAA4BD,CAAO,GAAKA,CAAA,EAE7C,aAAA,kBAAkBnM,EAAK4L,CAAK,EAClC5L,EAAI,KAAOsM,EACXtM,EAAI2K,EAAa,EAAI0B,EACdrM,CACT,CACM,MAAAuM,MAAqDN,EAAQ,EAC7D,CAAE,eAAAO,EAAmB,EAAAC,EACrBC,GAAsBlD,GACtB,CAAE,aAAcmD,EAAmB,EAAA1L,EACnC,CAAE,kBAAA2L,EAAsB,EAAAtC,GACxB,CAAE,KAAMuC,GAAW,SAAUC,GAAe/F,GAC5CgG,EAAc,OAAO,aAAa,EAClCC,GAAa,OAAO,MAAM,CAAC,EACjC,IAAIC,GAAW,MAAMC,CAAO,CAS1B,YAAYC,EAAQC,EAAYC,EAAc,CACvC,KAAA,YAAcD,GAAc,GAC7BC,IACF,KAAK,cAAgBA,EAChB,KAAA,YAAc,OAAO,MAAM,CAAC,GAEnC,KAAK,QAAUF,EACf,KAAK,eAAiB,GACtB,KAAK,UAAY,GACjB,KAAK,eAAiB,EACtB,KAAK,WAAa,GAClB,KAAK,OAAS,EAChB,CAsBA,OAAO,MAAM3M,EAAML,EAAS,CACtB,IAAAuG,EACA4G,EAAQ,GACR1H,EAAS,EACT2H,EAAc,GACdpN,EAAQ,OACVuG,EAAQvG,EAAQ,YAAc6M,GAC1B7M,EAAQ,aACVA,EAAQ,aAAauG,CAAK,EAEX8F,GAAA9F,EAAO,EAAG,CAAC,EAEb6G,GAAA7G,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAIA,EAAM,CAAC,KAAO,EACrDd,EAAA,GAEP,IAAA4H,EACA,OAAOhN,GAAS,UACb,CAACL,EAAQ,MAAQoN,IAAgBpN,EAAQ4M,CAAW,IAAM,OAC7DS,EAAarN,EAAQ4M,CAAW,GAEzBvM,EAAA,OAAO,KAAKA,CAAI,EACvBgN,EAAahN,EAAK,SAGpBgN,EAAahN,EAAK,OAClB8M,EAAQnN,EAAQ,MAAQA,EAAQ,UAAY,CAACoN,GAE/C,IAAIE,EAAgBD,EAChBA,GAAc,OACN5H,GAAA,EACM6H,EAAA,KACPD,EAAa,MACZ5H,GAAA,EACM6H,EAAA,KAElB,MAAMnK,EAAS,OAAO,YAAYgK,EAAQE,EAAa5H,EAASA,CAAM,EAWtE,OAVAtC,EAAO,CAAC,EAAInD,EAAQ,IAAMA,EAAQ,OAAS,IAAMA,EAAQ,OACrDA,EAAQ,OACVmD,EAAO,CAAC,GAAK,IACfA,EAAO,CAAC,EAAImK,EACRA,IAAkB,IACbnK,EAAA,cAAckK,EAAY,CAAC,EACzBC,IAAkB,MAC3BnK,EAAO,CAAC,EAAIA,EAAO,CAAC,EAAI,EACjBA,EAAA,YAAYkK,EAAY,EAAG,CAAC,GAEhCrN,EAAQ,MAEbmD,EAAO,CAAC,GAAK,IACbA,EAAOsC,EAAS,CAAC,EAAIc,EAAM,CAAC,EAC5BpD,EAAOsC,EAAS,CAAC,EAAIc,EAAM,CAAC,EAC5BpD,EAAOsC,EAAS,CAAC,EAAIc,EAAM,CAAC,EAC5BpD,EAAOsC,EAAS,CAAC,EAAIc,EAAM,CAAC,EACxB6G,EACK,CAACjK,EAAQ9C,CAAI,EAClB8M,GACFT,GAAUrM,EAAMkG,EAAOpD,EAAQsC,EAAQ4H,CAAU,EAC1C,CAAClK,CAAM,IAEhBuJ,GAAUrM,EAAMkG,EAAOlG,EAAM,EAAGgN,CAAU,EACnC,CAAClK,EAAQ9C,CAAI,IAbX,CAAC8C,EAAQ9C,CAAI,CAcxB,CAUA,MAAM0J,EAAM1J,EAAMkG,EAAOgF,EAAI,CACvB,IAAAlF,EACJ,GAAI0D,IAAS,OACL1D,EAAAmG,WACG,OAAOzC,GAAS,UAAY,CAAC0C,GAAkB1C,CAAI,EACtD,MAAA,IAAI,UAAU,kDAAkD,EAC7D,GAAA1J,IAAS,QAAU,CAACA,EAAK,OAC5BgG,EAAA,OAAO,YAAY,CAAC,EACtBA,EAAA,cAAc0D,EAAM,CAAC,MACpB,CACC,MAAArE,EAAS,OAAO,WAAWrF,CAAI,EACrC,GAAIqF,EAAS,IACL,MAAA,IAAI,WAAW,gDAAgD,EAEjEW,EAAA,OAAO,YAAY,EAAIX,CAAM,EAC/BW,EAAA,cAAc0D,EAAM,CAAC,EACrB,OAAO1J,GAAS,SACdgG,EAAA,MAAMhG,EAAM,CAAC,EAEbgG,EAAA,IAAIhG,EAAM,CAAC,CAEnB,EACA,MAAML,EAAU,CACd,CAAC4M,CAAW,EAAGvG,EAAI,OACnB,IAAK,GACL,aAAc,KAAK,cACnB,KAAME,EACN,WAAY,KAAK,YACjB,OAAQ,EACR,SAAU,GACV,KAAM,EAAA,EAEJ,KAAK,WACF,KAAA,QAAQ,CAAC,KAAK,SAAUF,EAAK,GAAOrG,EAASuL,CAAE,CAAC,EAErD,KAAK,UAAUwB,EAAO,MAAM1G,EAAKrG,CAAO,EAAGuL,CAAE,CAEjD,CASA,KAAKlL,EAAMkG,EAAOgF,EAAI,CAChB,IAAAgC,EACAC,EASJ,GARI,OAAOnN,GAAS,UACLkN,EAAA,OAAO,WAAWlN,CAAI,EACxBmN,EAAA,KAEXnN,EAAOsM,EAAWtM,CAAI,EACtBkN,EAAalN,EAAK,OAClBmN,EAAWb,EAAW,UAEpBY,EAAa,IACT,MAAA,IAAI,WAAW,kDAAkD,EAEzE,MAAMvN,EAAU,CACd,CAAC4M,CAAW,EAAGW,EACf,IAAK,GACL,aAAc,KAAK,cACnB,KAAMhH,EACN,WAAY,KAAK,YACjB,OAAQ,EACR,SAAAiH,EACA,KAAM,EAAA,EAEJ,KAAK,WACF,KAAA,QAAQ,CAAC,KAAK,SAAUnN,EAAM,GAAOL,EAASuL,CAAE,CAAC,EAEtD,KAAK,UAAUwB,EAAO,MAAM1M,EAAML,CAAO,EAAGuL,CAAE,CAElD,CASA,KAAKlL,EAAMkG,EAAOgF,EAAI,CAChB,IAAAgC,EACAC,EASJ,GARI,OAAOnN,GAAS,UACLkN,EAAA,OAAO,WAAWlN,CAAI,EACxBmN,EAAA,KAEXnN,EAAOsM,EAAWtM,CAAI,EACtBkN,EAAalN,EAAK,OAClBmN,EAAWb,EAAW,UAEpBY,EAAa,IACT,MAAA,IAAI,WAAW,kDAAkD,EAEzE,MAAMvN,EAAU,CACd,CAAC4M,CAAW,EAAGW,EACf,IAAK,GACL,aAAc,KAAK,cACnB,KAAMhH,EACN,WAAY,KAAK,YACjB,OAAQ,GACR,SAAAiH,EACA,KAAM,EAAA,EAEJ,KAAK,WACF,KAAA,QAAQ,CAAC,KAAK,SAAUnN,EAAM,GAAOL,EAASuL,CAAE,CAAC,EAEtD,KAAK,UAAUwB,EAAO,MAAM1M,EAAML,CAAO,EAAGuL,CAAE,CAElD,CAiBA,KAAKlL,EAAML,EAASuL,EAAI,CACtB,MAAMkC,EAAoB,KAAK,YAAYlB,GAAoB,aAAa,EACxE,IAAAmB,EAAS1N,EAAQ,OAAS,EAAI,EAC9B2N,EAAO3N,EAAQ,SACfuN,EACAC,EAqBJ,GApBI,OAAOnN,GAAS,UACLkN,EAAA,OAAO,WAAWlN,CAAI,EACxBmN,EAAA,KAEXnN,EAAOsM,EAAWtM,CAAI,EACtBkN,EAAalN,EAAK,OAClBmN,EAAWb,EAAW,UAEpB,KAAK,gBACP,KAAK,eAAiB,GAClBgB,GAAQF,GAAqBA,EAAkB,OAAOA,EAAkB,UAAY,6BAA+B,4BAA4B,IACjJE,EAAOJ,GAAcE,EAAkB,YAEzC,KAAK,UAAYE,IAEVA,EAAA,GACED,EAAA,GAEP1N,EAAQ,MACV,KAAK,eAAiB,IACpByN,EAAmB,CACrB,MAAMnF,EAAO,CACX,CAACsE,CAAW,EAAGW,EACf,IAAKvN,EAAQ,IACb,aAAc,KAAK,cACnB,KAAMA,EAAQ,KACd,WAAY,KAAK,YACjB,OAAA0N,EACA,SAAAF,EACA,KAAAG,CAAA,EAEE,KAAK,WACF,KAAA,QAAQ,CAAC,KAAK,SAAUtN,EAAM,KAAK,UAAWiI,EAAMiD,CAAE,CAAC,EAE5D,KAAK,SAASlL,EAAM,KAAK,UAAWiI,EAAMiD,CAAE,CAC9C,MAEK,KAAA,UACHwB,EAAO,MAAM1M,EAAM,CACjB,CAACuM,CAAW,EAAGW,EACf,IAAKvN,EAAQ,IACb,aAAc,KAAK,cACnB,KAAMA,EAAQ,KACd,WAAY,KAAK,YACjB,OAAA0N,EACA,SAAAF,EACA,KAAM,EAAA,CACP,EACDjC,CAAA,CAGN,CAwBA,SAASlL,EAAMuN,EAAU5N,EAASuL,EAAI,CACpC,GAAI,CAACqC,EAAU,CACb,KAAK,UAAUb,EAAO,MAAM1M,EAAML,CAAO,EAAGuL,CAAE,EAC9C,MACF,CACA,MAAMkC,EAAoB,KAAK,YAAYlB,GAAoB,aAAa,EACvE,KAAA,gBAAkBvM,EAAQ4M,CAAW,EAC1C,KAAK,WAAa,GAClBa,EAAkB,SAASpN,EAAML,EAAQ,IAAK,CAAC6N,EAAGxH,IAAQ,CACpD,GAAA,KAAK,QAAQ,UAAW,CAC1B,MAAMxG,EAAM,IAAI,MACd,uDAAA,EAEE,OAAO0L,GAAO,YAChBA,EAAG1L,CAAG,EACR,QAAS+E,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IAAK,CACrC,MAAAuD,EAAS,KAAK,OAAOvD,CAAC,EACtBtE,EAAW6H,EAAOA,EAAO,OAAS,CAAC,EACrC,OAAO7H,GAAa,YACtBA,EAAST,CAAG,CAChB,CACA,MACF,CACK,KAAA,gBAAkBG,EAAQ4M,CAAW,EAC1C,KAAK,WAAa,GAClB5M,EAAQ,SAAW,GACnB,KAAK,UAAU+M,EAAO,MAAM1G,EAAKrG,CAAO,EAAGuL,CAAE,EAC7C,KAAK,QAAQ,CAAA,CACd,CACH,CAMA,SAAU,CACR,KAAO,CAAC,KAAK,YAAc,KAAK,OAAO,QAAQ,CACvC,MAAApD,EAAS,KAAK,OAAO,MAAM,EACjC,KAAK,gBAAkBA,EAAO,CAAC,EAAEyE,CAAW,EACpC,QAAA,MAAMzE,EAAO,CAAC,EAAG,KAAMA,EAAO,MAAM,CAAC,CAAC,CAChD,CACF,CAOA,QAAQA,EAAQ,CACd,KAAK,gBAAkBA,EAAO,CAAC,EAAEyE,CAAW,EACvC,KAAA,OAAO,KAAKzE,CAAM,CACzB,CAQA,UAAUhC,EAAMoF,EAAI,CACdpF,EAAK,SAAW,GAClB,KAAK,QAAQ,OACb,KAAK,QAAQ,MAAMA,EAAK,CAAC,CAAC,EAC1B,KAAK,QAAQ,MAAMA,EAAK,CAAC,EAAGoF,CAAE,EAC9B,KAAK,QAAQ,UAEb,KAAK,QAAQ,MAAMpF,EAAK,CAAC,EAAGoF,CAAE,CAElC,CACF,EACA,IAAIuC,GAAShB,GACP,MAAAiB,MAAmDD,EAAM,EACzD,CAAE,qBAAsBE,EAAwB,UAAWC,IAAgBnN,EAC3EoN,GAAQ,OAAO,OAAO,EACtBC,GAAQ,OAAO,OAAO,EACtBC,GAAS,OAAO,QAAQ,EACxBC,GAAW,OAAO,UAAU,EAC5BC,GAAU,OAAO,SAAS,EAC1BC,EAAU,OAAO,SAAS,EAC1BC,GAAQ,OAAO,OAAO,EACtBC,GAAY,OAAO,WAAW,EACpC,MAAMC,CAAM,CAOV,YAAYC,EAAM,CAChB,KAAKJ,CAAO,EAAI,KAChB,KAAKC,EAAK,EAAIG,CAChB,CAIA,IAAI,QAAS,CACX,OAAO,KAAKJ,CAAO,CACrB,CAIA,IAAI,MAAO,CACT,OAAO,KAAKC,EAAK,CACnB,CACF,CACA,OAAO,eAAeE,EAAM,UAAW,SAAU,CAAE,WAAY,GAAM,EACrE,OAAO,eAAeA,EAAM,UAAW,OAAQ,CAAE,WAAY,GAAM,EACnE,MAAME,WAAmBF,CAAM,CAc7B,YAAYC,EAAM3O,EAAU,GAAI,CAC9B,MAAM2O,CAAI,EACV,KAAKT,EAAK,EAAIlO,EAAQ,OAAS,OAAS,EAAIA,EAAQ,KACpD,KAAKsO,EAAO,EAAItO,EAAQ,SAAW,OAAS,GAAKA,EAAQ,OACzD,KAAKyO,EAAS,EAAIzO,EAAQ,WAAa,OAAS,GAAQA,EAAQ,QAClE,CAIA,IAAI,MAAO,CACT,OAAO,KAAKkO,EAAK,CACnB,CAIA,IAAI,QAAS,CACX,OAAO,KAAKI,EAAO,CACrB,CAIA,IAAI,UAAW,CACb,OAAO,KAAKG,EAAS,CACvB,CACF,CACA,OAAO,eAAeG,GAAW,UAAW,OAAQ,CAAE,WAAY,GAAM,EACxE,OAAO,eAAeA,GAAW,UAAW,SAAU,CAAE,WAAY,GAAM,EAC1E,OAAO,eAAeA,GAAW,UAAW,WAAY,CAAE,WAAY,GAAM,EAC5E,MAAMC,WAAmBH,CAAM,CAU7B,YAAYC,EAAM3O,EAAU,GAAI,CAC9B,MAAM2O,CAAI,EACV,KAAKP,EAAM,EAAIpO,EAAQ,QAAU,OAAS,KAAOA,EAAQ,MACzD,KAAKqO,EAAQ,EAAIrO,EAAQ,UAAY,OAAS,GAAKA,EAAQ,OAC7D,CAIA,IAAI,OAAQ,CACV,OAAO,KAAKoO,EAAM,CACpB,CAIA,IAAI,SAAU,CACZ,OAAO,KAAKC,EAAQ,CACtB,CACF,CACA,OAAO,eAAeQ,GAAW,UAAW,QAAS,CAAE,WAAY,GAAM,EACzE,OAAO,eAAeA,GAAW,UAAW,UAAW,CAAE,WAAY,GAAM,EAC3E,MAAMC,WAAqBJ,CAAM,CAS/B,YAAYC,EAAM3O,EAAU,GAAI,CAC9B,MAAM2O,CAAI,EACV,KAAKR,EAAK,EAAInO,EAAQ,OAAS,OAAS,KAAOA,EAAQ,IACzD,CAIA,IAAI,MAAO,CACT,OAAO,KAAKmO,EAAK,CACnB,CACF,CACA,OAAO,eAAeW,GAAa,UAAW,OAAQ,CAAE,WAAY,GAAM,EAC1E,MAAMC,GAAc,CAalB,iBAAiBJ,EAAMK,EAAShP,EAAU,CAAA,EAAI,CAC5C,UAAWiP,KAAY,KAAK,UAAUN,CAAI,EACpC,GAAA,CAAC3O,EAAQgO,CAAsB,GAAKiB,EAAShB,EAAW,IAAMe,GAAW,CAACC,EAASjB,CAAsB,EAC3G,OAGA,IAAAkB,EACJ,GAAIP,IAAS,UACDO,EAAA,SAAmB7O,EAAMD,EAAU,CACrC,MAAA+O,EAAQ,IAAIL,GAAa,UAAW,CACxC,KAAM1O,EAAWC,EAAOA,EAAK,SAAS,CAAA,CACvC,EACD8O,EAAMZ,CAAO,EAAI,KACJa,GAAAJ,EAAS,KAAMG,CAAK,CAAA,UAE1BR,IAAS,QACRO,EAAA,SAAiBnF,EAAMiC,EAAS,CAClC,MAAAmD,EAAQ,IAAIP,GAAW,QAAS,CACpC,KAAA7E,EACA,OAAQiC,EAAQ,SAAS,EACzB,SAAU,KAAK,qBAAuB,KAAK,eAAA,CAC5C,EACDmD,EAAMZ,CAAO,EAAI,KACJa,GAAAJ,EAAS,KAAMG,CAAK,CAAA,UAE1BR,IAAS,QACRO,EAAA,SAAiBG,EAAQ,CAC3B,MAAAF,EAAQ,IAAIN,GAAW,QAAS,CACpC,MAAOQ,EACP,QAASA,EAAO,OAAA,CACjB,EACDF,EAAMZ,CAAO,EAAI,KACJa,GAAAJ,EAAS,KAAMG,CAAK,CAAA,UAE1BR,IAAS,OAClBO,EAAU,UAAkB,CACpB,MAAAC,EAAQ,IAAIT,EAAM,MAAM,EAC9BS,EAAMZ,CAAO,EAAI,KACJa,GAAAJ,EAAS,KAAMG,CAAK,CAAA,MAGnC,QAEFD,EAAQlB,CAAsB,EAAI,CAAC,CAAChO,EAAQgO,CAAsB,EAClEkB,EAAQjB,EAAW,EAAIe,EACnBhP,EAAQ,KACL,KAAA,KAAK2O,EAAMO,CAAO,EAElB,KAAA,GAAGP,EAAMO,CAAO,CAEzB,EAQA,oBAAoBP,EAAMK,EAAS,CACjC,UAAWC,KAAY,KAAK,UAAUN,CAAI,EACxC,GAAIM,EAAShB,EAAW,IAAMe,GAAW,CAACC,EAASjB,CAAsB,EAAG,CACrE,KAAA,eAAeW,EAAMM,CAAQ,EAClC,KACF,CAEJ,CACF,EACA,IAAIK,GAAc,CAChB,WAAAV,GACA,WAAAC,GACA,MAAAH,EACA,YAAAK,GACA,aAAAD,EACF,EACA,SAASM,GAAaH,EAAUM,EAASJ,EAAO,CAC1C,OAAOF,GAAa,UAAYA,EAAS,YAClCA,EAAA,YAAY,KAAKA,EAAUE,CAAK,EAEhCF,EAAA,KAAKM,EAASJ,CAAK,CAEhC,CACA,KAAM,CAAE,WAAYK,EAAiB,EAAArF,GACrC,SAASsF,EAAKC,EAAM/M,EAAMgN,EAAM,CAC1BD,EAAK/M,CAAI,IAAM,OACZ+M,EAAA/M,CAAI,EAAI,CAACgN,CAAI,EAEbD,EAAA/M,CAAI,EAAE,KAAKgN,CAAI,CACxB,CACA,SAASC,GAAQC,EAAQ,CACjB,MAAAxH,EAAgC,OAAA,OAAO,IAAI,EAC7C,IAAAF,EAAgC,OAAA,OAAO,IAAI,EAC3C2H,EAAe,GACfC,EAAa,GACbC,EAAW,GACXC,EACAC,EACAC,EAAQ,GACRpG,EAAO,GACPqG,EAAM,GACNxL,EAAI,EACD,KAAAA,EAAIiL,EAAO,OAAQjL,IAExB,GADOmF,EAAA8F,EAAO,WAAWjL,CAAC,EACtBqL,IAAkB,OACpB,GAAIG,IAAQ,IAAMZ,GAAazF,CAAI,IAAM,EACnCoG,IAAU,KACJA,EAAAvL,WACDA,IAAM,IAAMmF,IAAS,IAAMA,IAAS,GACzCqG,IAAQ,IAAMD,IAAU,KACpBC,EAAAxL,WACCmF,IAAS,IAAMA,IAAS,GAAI,CACrC,GAAIoG,IAAU,GACZ,MAAM,IAAI,YAAY,iCAAiCvL,CAAC,EAAE,EAExDwL,IAAQ,KACJA,EAAAxL,GACR,MAAMjC,EAAOkN,EAAO,MAAMM,EAAOC,CAAG,EAChCrG,IAAS,IACN0F,EAAApH,EAAQ1F,EAAMwF,CAAM,EACAA,EAAA,OAAO,OAAO,IAAI,GAE3B8H,EAAAtN,EAElBwN,EAAQC,EAAM,EAAA,KAEd,OAAM,IAAI,YAAY,iCAAiCxL,CAAC,EAAE,UAEnDsL,IAAc,OACvB,GAAIE,IAAQ,IAAMZ,GAAazF,CAAI,IAAM,EACnCoG,IAAU,KACJA,EAAAvL,WACDmF,IAAS,IAAMA,IAAS,EAC7BqG,IAAQ,IAAMD,IAAU,KACpBC,EAAAxL,WACCmF,IAAS,IAAMA,IAAS,GAAI,CACrC,GAAIoG,IAAU,GACZ,MAAM,IAAI,YAAY,iCAAiCvL,CAAC,EAAE,EAExDwL,IAAQ,KACJA,EAAAxL,GACR6K,EAAKtH,EAAQ0H,EAAO,MAAMM,EAAOC,CAAG,EAAG,EAAI,EACvCrG,IAAS,KACN0F,EAAApH,EAAQ4H,EAAe9H,CAAM,EACTA,EAAA,OAAO,OAAO,IAAI,EAC3B8H,EAAA,QAElBE,EAAQC,EAAM,EAAA,SACLrG,IAAS,IAAMoG,IAAU,IAAMC,IAAQ,GACpCF,EAAAL,EAAO,MAAMM,EAAOvL,CAAC,EACjCuL,EAAQC,EAAM,OAEd,OAAM,IAAI,YAAY,iCAAiCxL,CAAC,EAAE,UAGxDmL,EAAY,CACV,GAAAP,GAAazF,CAAI,IAAM,EACzB,MAAM,IAAI,YAAY,iCAAiCnF,CAAC,EAAE,EAExDuL,IAAU,GACJA,EAAAvL,EACAkL,IACOA,EAAA,IACJC,EAAA,WACJC,EACL,GAAAR,GAAazF,CAAI,IAAM,EACrBoG,IAAU,KACJA,EAAAvL,WACDmF,IAAS,IAAMoG,IAAU,GACvBH,EAAA,GACLI,EAAAxL,UACGmF,IAAS,GACLgG,EAAA,OAEb,OAAM,IAAI,YAAY,iCAAiCnL,CAAC,EAAE,UAEnDmF,IAAS,IAAM8F,EAAO,WAAWjL,EAAI,CAAC,IAAM,GAC1CoL,EAAA,WACFI,IAAQ,IAAMZ,GAAazF,CAAI,IAAM,EAC1CoG,IAAU,KACJA,EAAAvL,WACDuL,IAAU,KAAOpG,IAAS,IAAMA,IAAS,GAC9CqG,IAAQ,KACJA,EAAAxL,WACCmF,IAAS,IAAMA,IAAS,GAAI,CACrC,GAAIoG,IAAU,GACZ,MAAM,IAAI,YAAY,iCAAiCvL,CAAC,EAAE,EAExDwL,IAAQ,KACJA,EAAAxL,GACR,IAAI8D,EAAQmH,EAAO,MAAMM,EAAOC,CAAG,EAC/BN,IACMpH,EAAAA,EAAM,QAAQ,MAAO,EAAE,EAChBoH,EAAA,IAEZL,EAAAtH,EAAQ+H,EAAWxH,CAAK,EACzBqB,IAAS,KACN0F,EAAApH,EAAQ4H,EAAe9H,CAAM,EACTA,EAAA,OAAO,OAAO,IAAI,EAC3B8H,EAAA,QAENC,EAAA,OACZC,EAAQC,EAAM,EAAA,KAEd,OAAM,IAAI,YAAY,iCAAiCxL,CAAC,EAAE,EAIhE,GAAIuL,IAAU,IAAMH,GAAYjG,IAAS,IAAMA,IAAS,EAChD,MAAA,IAAI,YAAY,yBAAyB,EAE7CqG,IAAQ,KACJA,EAAAxL,GACR,MAAMyL,EAAQR,EAAO,MAAMM,EAAOC,CAAG,EACrC,OAAIH,IAAkB,OACfR,EAAApH,EAAQgI,EAAOlI,CAAM,GAEtB+H,IAAc,OACXT,EAAAtH,EAAQkI,EAAO,EAAI,EACfP,EACTL,EAAKtH,EAAQ+H,EAAWG,EAAM,QAAQ,MAAO,EAAE,CAAC,EAE3CZ,EAAAtH,EAAQ+H,EAAWG,CAAK,EAE1BZ,EAAApH,EAAQ4H,EAAe9H,CAAM,GAE7BE,CACT,CACA,SAASiI,GAASrD,EAAY,CAC5B,OAAO,OAAO,KAAKA,CAAU,EAAE,IAAKvI,GAAe,CAC7C,IAAA0D,EAAiB6E,EAAWvI,CAAU,EACtC,OAAC,MAAM,QAAQ0D,CAAc,IAC/BA,EAAiB,CAACA,CAAc,GAC3BA,EAAe,IAAKD,GAClB,CAACzD,CAAU,EAAE,OAClB,OAAO,KAAKyD,CAAM,EAAE,IAAK9I,GAAM,CACzB,IAAAkR,EAASpI,EAAO9I,CAAC,EACjB,OAAC,MAAM,QAAQkR,CAAM,IACvBA,EAAS,CAACA,CAAM,GACXA,EAAO,IAAKC,GAAMA,IAAM,GAAOnR,EAAI,GAAGA,CAAC,IAAImR,CAAC,EAAE,EAAE,KAAK,IAAI,CAAA,CACjE,CAAA,EACD,KAAK,IAAI,CACZ,EAAE,KAAK,IAAI,CAAA,CACb,EAAE,KAAK,IAAI,CACd,CACA,IAAIC,GAAc,CAAE,OAAQH,GAAU,MAAOV,EAAQ,EACrD,MAAMc,GAAiBC,EACjBC,GAAQC,EACRC,GAASC,EACTC,GAAMC,EACNC,GAAMC,EACN,CAAE,YAAAC,GAAa,WAAYC,EAAA,EAAiB/E,EAC5C,CAAE,IAAAgF,EAAQ,EAAAC,EACVC,EAAsBnI,GACtBoI,GAAY3F,GACZ4F,GAAU5D,GACV,CACJ,aAAA6D,GACA,aAAAC,GACA,KAAMC,GACN,qBAAAC,GACA,UAAAC,GACA,YAAAC,GACA,WAAYC,EACZ,KAAAC,EACF,EAAIpR,EACE,CACJ,YAAa,CAAE,iBAAAqR,GAAkB,oBAAAC,EAAoB,CACvD,EAAI9C,GACE,CAAE,OAAA+C,GAAQ,MAAOC,EAAA,EAAY7B,GAC7B,CAAE,SAAA8B,EAAa,EAAA3L,GACf4L,GAAe,GAAK,IACpBC,GAAW,OAAO,UAAU,EAC5BC,GAAmB,CAAC,EAAG,EAAE,EACzBC,EAAc,CAAC,aAAc,OAAQ,UAAW,QAAQ,EACxDC,GAAmB,iCACzB,IAAIC,EAAc,MAAMC,UAAkBpC,EAAe,CAQvD,YAAYqC,EAASC,EAAWhT,EAAS,CACjC,QACD,KAAA,YAAc2R,GAAa,CAAC,EACjC,KAAK,WAAa,KAClB,KAAK,oBAAsB,GAC3B,KAAK,gBAAkB,GACvB,KAAK,cAAgBC,GACrB,KAAK,YAAc,KACnB,KAAK,YAAc,GACnB,KAAK,QAAU,GACf,KAAK,UAAY,GACjB,KAAK,YAAckB,EAAU,WAC7B,KAAK,UAAY,KACjB,KAAK,QAAU,KACf,KAAK,QAAU,KACXC,IAAY,MACd,KAAK,gBAAkB,EACvB,KAAK,UAAY,GACjB,KAAK,WAAa,EACdC,IAAc,OAChBA,EAAY,CAAA,EACF,MAAM,QAAQA,CAAS,IAC7B,OAAOA,GAAc,UAAYA,IAAc,MACvChT,EAAAgT,EACVA,EAAY,CAAA,GAEZA,EAAY,CAACA,CAAS,GAGbC,GAAA,KAAMF,EAASC,EAAWhT,CAAO,GAE9C,KAAK,UAAY,EAErB,CAQA,IAAI,YAAa,CACf,OAAO,KAAK,WACd,CACA,IAAI,WAAW2O,EAAM,CACdgD,GAAa,SAAShD,CAAI,IAE/B,KAAK,YAAcA,EACf,KAAK,YACP,KAAK,UAAU,YAAcA,GACjC,CAIA,IAAI,gBAAiB,CACnB,OAAK,KAAK,QAEH,KAAK,QAAQ,eAAe,OAAS,KAAK,QAAQ,eADhD,KAAK,eAEhB,CAIA,IAAI,YAAa,CACf,OAAO,OAAO,KAAK,KAAK,WAAW,EAAE,KAAK,CAC5C,CAIA,IAAI,UAAW,CACb,OAAO,KAAK,OACd,CAKA,IAAI,SAAU,CACL,OAAA,IACT,CAKA,IAAI,SAAU,CACL,OAAA,IACT,CAKA,IAAI,QAAS,CACJ,OAAA,IACT,CAKA,IAAI,WAAY,CACP,OAAA,IACT,CAIA,IAAI,UAAW,CACb,OAAO,KAAK,SACd,CAIA,IAAI,YAAa,CACf,OAAO,KAAK,WACd,CAIA,IAAI,KAAM,CACR,OAAO,KAAK,IACd,CAeA,UAAU3B,EAAQkG,EAAMlT,EAAS,CACzB,MAAAmT,EAAY,IAAI1B,GAAU,CAC9B,WAAY,KAAK,WACjB,WAAY,KAAK,YACjB,SAAU,KAAK,UACf,WAAYzR,EAAQ,WACpB,mBAAoBA,EAAQ,kBAAA,CAC7B,EACD,KAAK,QAAU,IAAI0R,GAAQ1E,EAAQ,KAAK,YAAahN,EAAQ,YAAY,EACzE,KAAK,UAAYmT,EACjB,KAAK,QAAUnG,EACfmG,EAAUlB,CAAY,EAAI,KAC1BjF,EAAOiF,CAAY,EAAI,KACbkB,EAAA,GAAG,WAAYC,EAAkB,EACjCD,EAAA,GAAG,QAASE,EAAe,EAC3BF,EAAA,GAAG,QAASG,EAAe,EAC3BH,EAAA,GAAG,UAAWI,EAAiB,EAC/BJ,EAAA,GAAG,OAAQK,EAAc,EACzBL,EAAA,GAAG,OAAQM,EAAc,EACnCzG,EAAO,WAAW,CAAC,EACnBA,EAAO,WAAW,EACdkG,EAAK,OAAS,GAChBlG,EAAO,QAAQkG,CAAI,EACdlG,EAAA,GAAG,QAAS0G,EAAa,EACzB1G,EAAA,GAAG,OAAQ2G,EAAY,EACvB3G,EAAA,GAAG,MAAO4G,EAAW,EACrB5G,EAAA,GAAG,QAAS6G,EAAe,EAClC,KAAK,YAAcf,EAAU,KAC7B,KAAK,KAAK,MAAM,CAClB,CAMA,WAAY,CACN,GAAA,CAAC,KAAK,QAAS,CACjB,KAAK,YAAcA,EAAU,OAC7B,KAAK,KAAK,QAAS,KAAK,WAAY,KAAK,aAAa,EACtD,MACF,CACI,KAAK,YAAYtB,EAAoB,aAAa,GACpD,KAAK,YAAYA,EAAoB,aAAa,EAAE,QAAQ,EAE9D,KAAK,UAAU,qBACf,KAAK,YAAcsB,EAAU,OAC7B,KAAK,KAAK,QAAS,KAAK,WAAY,KAAK,aAAa,CACxD,CAqBA,MAAM/I,EAAM1J,EAAM,CACZ,GAAA,KAAK,aAAeyS,EAAU,OAE9B,IAAA,KAAK,aAAeA,EAAU,WAAY,CAE3BgB,EAAA,KAAM,KAAK,KADhB,4DACyB,EACrC,MACF,CACI,GAAA,KAAK,aAAehB,EAAU,QAAS,CACrC,KAAK,kBAAoB,KAAK,qBAAuB,KAAK,UAAU,eAAe,eACrF,KAAK,QAAQ,MAEf,MACF,CACA,KAAK,YAAcA,EAAU,QACxB,KAAA,QAAQ,MAAM/I,EAAM1J,EAAM,CAAC,KAAK,UAAYR,GAAQ,CACnDA,IAEJ,KAAK,gBAAkB,IACnB,KAAK,qBAAuB,KAAK,UAAU,eAAe,eAC5D,KAAK,QAAQ,MACf,CACD,EACD,KAAK,YAAc,WACjB,KAAK,QAAQ,QAAQ,KAAK,KAAK,OAAO,EACtC2S,EAAA,EAEJ,CAMA,OAAQ,CACF,KAAK,aAAeM,EAAU,YAAc,KAAK,aAAeA,EAAU,SAG9E,KAAK,QAAU,GACf,KAAK,QAAQ,QACf,CASA,KAAKzS,EAAMkG,EAAOgF,EAAI,CAChB,GAAA,KAAK,aAAeuH,EAAU,WAC1B,MAAA,IAAI,MAAM,kDAAkD,EAWhE,GATA,OAAOzS,GAAS,YACbkL,EAAAlL,EACLA,EAAOkG,EAAQ,QACN,OAAOA,GAAU,aACrBgF,EAAAhF,EACGA,EAAA,QAEN,OAAOlG,GAAS,WAClBA,EAAOA,EAAK,YACV,KAAK,aAAeyS,EAAU,KAAM,CACvBiB,GAAA,KAAM1T,EAAMkL,CAAE,EAC7B,MACF,CACIhF,IAAU,SACZA,EAAQ,CAAC,KAAK,WAChB,KAAK,QAAQ,KAAKlG,GAAQuR,GAAcrL,EAAOgF,CAAE,CACnD,CASA,KAAKlL,EAAMkG,EAAOgF,EAAI,CAChB,GAAA,KAAK,aAAeuH,EAAU,WAC1B,MAAA,IAAI,MAAM,kDAAkD,EAWhE,GATA,OAAOzS,GAAS,YACbkL,EAAAlL,EACLA,EAAOkG,EAAQ,QACN,OAAOA,GAAU,aACrBgF,EAAAhF,EACGA,EAAA,QAEN,OAAOlG,GAAS,WAClBA,EAAOA,EAAK,YACV,KAAK,aAAeyS,EAAU,KAAM,CACvBiB,GAAA,KAAM1T,EAAMkL,CAAE,EAC7B,MACF,CACIhF,IAAU,SACZA,EAAQ,CAAC,KAAK,WAChB,KAAK,QAAQ,KAAKlG,GAAQuR,GAAcrL,EAAOgF,CAAE,CACnD,CAMA,QAAS,CACH,KAAK,aAAeuH,EAAU,YAAc,KAAK,aAAeA,EAAU,SAG9E,KAAK,QAAU,GACV,KAAK,UAAU,eAAe,WACjC,KAAK,QAAQ,SACjB,CAgBA,KAAKzS,EAAML,EAASuL,EAAI,CAClB,GAAA,KAAK,aAAeuH,EAAU,WAC1B,MAAA,IAAI,MAAM,kDAAkD,EAQhE,GANA,OAAO9S,GAAY,aAChBuL,EAAAvL,EACLA,EAAU,CAAA,GAER,OAAOK,GAAS,WAClBA,EAAOA,EAAK,YACV,KAAK,aAAeyS,EAAU,KAAM,CACvBiB,GAAA,KAAM1T,EAAMkL,CAAE,EAC7B,MACF,CACA,MAAMjD,EAAO,CACX,OAAQ,OAAOjI,GAAS,SACxB,KAAM,CAAC,KAAK,UACZ,SAAU,GACV,IAAK,GACL,GAAGL,CAAA,EAEA,KAAK,YAAYwR,EAAoB,aAAa,IACrDlJ,EAAK,SAAW,IAElB,KAAK,QAAQ,KAAKjI,GAAQuR,GAActJ,EAAMiD,CAAE,CAClD,CAMA,WAAY,CACN,GAAA,KAAK,aAAeuH,EAAU,OAE9B,IAAA,KAAK,aAAeA,EAAU,WAAY,CAE3BgB,EAAA,KAAM,KAAK,KADhB,4DACyB,EACrC,MACF,CACI,KAAK,UACP,KAAK,YAAchB,EAAU,QAC7B,KAAK,QAAQ,WAEjB,CACF,EACA,OAAO,eAAeD,EAAa,aAAc,CAC/C,WAAY,GACZ,MAAOF,EAAY,QAAQ,YAAY,CACzC,CAAC,EACD,OAAO,eAAeE,EAAY,UAAW,aAAc,CACzD,WAAY,GACZ,MAAOF,EAAY,QAAQ,YAAY,CACzC,CAAC,EACD,OAAO,eAAeE,EAAa,OAAQ,CACzC,WAAY,GACZ,MAAOF,EAAY,QAAQ,MAAM,CACnC,CAAC,EACD,OAAO,eAAeE,EAAY,UAAW,OAAQ,CACnD,WAAY,GACZ,MAAOF,EAAY,QAAQ,MAAM,CACnC,CAAC,EACD,OAAO,eAAeE,EAAa,UAAW,CAC5C,WAAY,GACZ,MAAOF,EAAY,QAAQ,SAAS,CACtC,CAAC,EACD,OAAO,eAAeE,EAAY,UAAW,UAAW,CACtD,WAAY,GACZ,MAAOF,EAAY,QAAQ,SAAS,CACtC,CAAC,EACD,OAAO,eAAeE,EAAa,SAAU,CAC3C,WAAY,GACZ,MAAOF,EAAY,QAAQ,QAAQ,CACrC,CAAC,EACD,OAAO,eAAeE,EAAY,UAAW,SAAU,CACrD,WAAY,GACZ,MAAOF,EAAY,QAAQ,QAAQ,CACrC,CAAC,EACD,CACE,aACA,iBACA,aACA,WACA,WACA,aACA,KACF,EAAE,QAASqB,GAAa,CACtB,OAAO,eAAenB,EAAY,UAAWmB,EAAU,CAAE,WAAY,GAAM,CAC7E,CAAC,EACD,CAAC,OAAQ,QAAS,QAAS,SAAS,EAAE,QAASC,GAAW,CACxD,OAAO,eAAepB,EAAY,UAAW,KAAKoB,CAAM,GAAI,CAC1D,WAAY,GACZ,KAAM,CACJ,UAAWhF,KAAY,KAAK,UAAUgF,CAAM,EAC1C,GAAIhF,EAAS6C,EAAoB,EAC/B,OAAO7C,EAAS8C,EAAS,EAEtB,OAAA,IACT,EACA,IAAI/C,EAAS,CACX,UAAWC,KAAY,KAAK,UAAUgF,CAAM,EACtC,GAAAhF,EAAS6C,EAAoB,EAAG,CAC7B,KAAA,eAAemC,EAAQhF,CAAQ,EACpC,KACF,CAEE,OAAOD,GAAY,YAElB,KAAA,iBAAiBiF,EAAQjF,EAAS,CACrC,CAAC8C,EAAoB,EAAG,EAAA,CACzB,CACH,CAAA,CACD,CACH,CAAC,EACDe,EAAY,UAAU,iBAAmBV,GACzCU,EAAY,UAAU,oBAAsBT,GAC5C,IAAI8B,GAAYrB,EAChB,SAASI,GAAakB,EAAYpB,EAASC,EAAWhT,EAAS,CAC7D,MAAMsI,EAAO,CACX,gBAAiBoK,GAAiB,CAAC,EACnC,WAAY,UACZ,mBAAoB,GACpB,kBAAmB,GACnB,gBAAiB,GACjB,aAAc,GACd,GAAG1S,EACH,iBAAkB,OAClB,WAAY,OACZ,SAAU,OACV,SAAU,OACV,QAAS,OACT,OAAQ,MACR,KAAM,OACN,KAAM,OACN,KAAM,MAAA,EAER,GAAI,CAAC0S,GAAiB,SAASpK,EAAK,eAAe,EACjD,MAAM,IAAI,WACR,iCAAiCA,EAAK,eAAe,yBAAyBoK,GAAiB,KAAK,IAAI,CAAC,GAAA,EAGzG,IAAA0B,EACJ,GAAIrB,aAAmBzB,GACT8C,EAAArB,EACZoB,EAAW,KAAOpB,EAAQ,SACrB,CACD,GAAA,CACUqB,EAAA,IAAI9C,GAAIyB,CAAO,OACjB,CACV,MAAM,IAAI,YAAY,gBAAgBA,CAAO,EAAE,CACjD,CACAoB,EAAW,KAAOpB,CACpB,CACM,MAAAsB,EAAWD,EAAU,WAAa,OAClCE,EAAWF,EAAU,WAAa,WACpC,IAAAG,EAQJ,GAPIH,EAAU,WAAa,OAAS,CAACC,GAAY,CAACC,EAC5BC,EAAA,iEACXD,GAAY,CAACF,EAAU,SACZG,EAAA,8BACXH,EAAU,OACCG,EAAA,0CAElBA,EAAmB,CACf,MAAA1U,EAAM,IAAI,YAAY0U,CAAiB,EACzC,GAAAJ,EAAW,aAAe,EACtB,MAAAtU,EAEN2U,GAAkBL,EAAYtU,CAAG,EACjC,MAEJ,CACM,MAAA4U,EAAcJ,EAAW,IAAM,GAC/B5L,EAAM2I,GAAY,EAAE,EAAE,SAAS,QAAQ,EACvCsD,EAAUL,EAAWzD,GAAM,QAAUE,GAAO,QAC5C6D,MAAkC,IACpC,IAAAlH,EAwBJ,GAvBKnF,EAAA,iBAAmB+L,EAAWO,GAAaC,GAC3CvM,EAAA,YAAcA,EAAK,aAAemM,EAClCnM,EAAA,KAAO8L,EAAU,MAAQK,EAC9BnM,EAAK,KAAO8L,EAAU,SAAS,WAAW,GAAG,EAAIA,EAAU,SAAS,MAAM,EAAG,EAAE,EAAIA,EAAU,SAC7F9L,EAAK,QAAU,CACb,GAAGA,EAAK,QACR,wBAAyBA,EAAK,gBAC9B,oBAAqBG,EACrB,WAAY,UACZ,QAAS,WAAA,EAENH,EAAA,KAAO8L,EAAU,SAAWA,EAAU,OAC3C9L,EAAK,QAAUA,EAAK,iBAChBA,EAAK,oBACPmF,EAAoB,IAAI+D,EACtBlJ,EAAK,oBAAsB,GAAOA,EAAK,kBAAoB,CAAC,EAC5D,GACAA,EAAK,UAAA,EAEFA,EAAA,QAAQ,0BAA0B,EAAI+J,GAAO,CAChD,CAACb,EAAoB,aAAa,EAAG/D,EAAkB,MAAM,CAAA,CAC9D,GAECuF,EAAU,OAAQ,CACpB,UAAW8B,KAAY9B,EAAW,CAC5B,GAAA,OAAO8B,GAAa,UAAY,CAAClC,GAAiB,KAAKkC,CAAQ,GAAKH,EAAY,IAAIG,CAAQ,EAC9F,MAAM,IAAI,YACR,oDAAA,EAGJH,EAAY,IAAIG,CAAQ,CAC1B,CACAxM,EAAK,QAAQ,wBAAwB,EAAI0K,EAAU,KAAK,GAAG,CAC7D,CAWA,GAVI1K,EAAK,SACHA,EAAK,gBAAkB,GACpBA,EAAA,QAAQ,sBAAsB,EAAIA,EAAK,OAEvCA,EAAA,QAAQ,OAASA,EAAK,SAG3B8L,EAAU,UAAYA,EAAU,YAClC9L,EAAK,KAAO,GAAG8L,EAAU,QAAQ,IAAIA,EAAU,QAAQ,IAErDE,EAAU,CACZ,MAAMS,EAAQzM,EAAK,KAAK,MAAM,GAAG,EAC5BA,EAAA,WAAayM,EAAM,CAAC,EACpBzM,EAAA,KAAOyM,EAAM,CAAC,CACrB,CACI,IAAAC,EACJ,GAAI1M,EAAK,gBAAiB,CACpB,GAAA6L,EAAW,aAAe,EAAG,CAC/BA,EAAW,aAAeG,EAC1BH,EAAW,gBAAkBE,EAC7BF,EAAW,0BAA4BG,EAAWhM,EAAK,WAAa8L,EAAU,KACxE,MAAAa,EAAUjV,GAAWA,EAAQ,QAEnC,GADAA,EAAU,CAAE,GAAGA,EAAS,QAAS,CAAG,CAAA,EAChCiV,EACF,SAAW,CAACC,EAAMxM,CAAK,IAAK,OAAO,QAAQuM,CAAO,EAChDjV,EAAQ,QAAQkV,EAAK,YAAa,CAAA,EAAIxM,CAGjC,SAAAyL,EAAW,cAAc,UAAU,IAAM,EAAG,CACrD,MAAMgB,EAAab,EAAWH,EAAW,aAAe7L,EAAK,aAAe6L,EAAW,0BAA4B,GAAQA,EAAW,aAAe,GAAQC,EAAU,OAASD,EAAW,2BACvL,CAACgB,GAAchB,EAAW,iBAAmB,CAACE,KAChD,OAAO/L,EAAK,QAAQ,cACpB,OAAOA,EAAK,QAAQ,OACf6M,GACH,OAAO7M,EAAK,QAAQ,KACtBA,EAAK,KAAO,OAEhB,CACIA,EAAK,MAAQ,CAACtI,EAAQ,QAAQ,gBACxBA,EAAA,QAAQ,cAAgB,SAAW,OAAO,KAAKsI,EAAK,IAAI,EAAE,SAAS,QAAQ,GAE/E0M,EAAAb,EAAW,KAAOO,EAAQpM,CAAI,EAChC6L,EAAW,YACbA,EAAW,KAAK,WAAYA,EAAW,IAAKa,CAAG,CACjD,MAEMA,EAAAb,EAAW,KAAOO,EAAQpM,CAAI,EAElCA,EAAK,SACH0M,EAAA,GAAG,UAAW,IAAM,CACLlB,EAAAK,EAAYa,EAAK,iCAAiC,CAAA,CACpE,EAECA,EAAA,GAAG,QAAUnV,GAAQ,CACnBmV,IAAQ,MAAQA,EAAIvC,EAAQ,IAEhCuC,EAAMb,EAAW,KAAO,KACxBK,GAAkBL,EAAYtU,CAAG,EAAA,CAClC,EACGmV,EAAA,GAAG,WAAaI,GAAQ,CACpB,MAAAC,EAAWD,EAAI,QAAQ,SACvBlJ,EAAakJ,EAAI,WACvB,GAAIC,GAAY/M,EAAK,iBAAmB4D,GAAc,KAAOA,EAAa,IAAK,CAC7E,GAAI,EAAEiI,EAAW,WAAa7L,EAAK,aAAc,CAC9BwL,EAAAK,EAAYa,EAAK,4BAA4B,EAC9D,MACF,CACAA,EAAI,MAAM,EACN,IAAAM,EACA,GAAA,CACKA,EAAA,IAAIhE,GAAI+D,EAAUtC,CAAO,OACtB,CACV,MAAMlT,EAAM,IAAI,YAAY,gBAAgBwV,CAAQ,EAAE,EACtDb,GAAkBL,EAAYtU,CAAG,EACjC,MACF,CACaoT,GAAAkB,EAAYmB,EAAMtC,EAAWhT,CAAO,CAAA,MACvCmU,EAAW,KAAK,sBAAuBa,EAAKI,CAAG,GACzDtB,EACEK,EACAa,EACA,+BAA+BI,EAAI,UAAU,EAAA,CAEjD,CACD,EACDJ,EAAI,GAAG,UAAW,CAACI,EAAKpI,EAAQkG,IAAS,CAEnC,GADOiB,EAAA,KAAK,UAAWiB,CAAG,EAC1BjB,EAAW,aAAetB,EAAY,WACxC,OAEF,GADAmC,EAAMb,EAAW,KAAO,KACpBiB,EAAI,QAAQ,QAAQ,YAAA,IAAkB,YAAa,CACpCtB,EAAAK,EAAYnH,EAAQ,wBAAwB,EAC7D,MACF,CACM,MAAAuI,EAASlE,GAAa,MAAM,EAAE,OAAO5I,EAAMoJ,EAAM,EAAE,OAAO,QAAQ,EACxE,GAAIuD,EAAI,QAAQ,sBAAsB,IAAMG,EAAQ,CACjCzB,EAAAK,EAAYnH,EAAQ,qCAAqC,EAC1E,MACF,CACM,MAAAwI,EAAaJ,EAAI,QAAQ,wBAAwB,EACnD,IAAAK,EAUJ,GATID,IAAe,OACZb,EAAY,KAELA,EAAY,IAAIa,CAAU,IACxBC,EAAA,sCAFAA,EAAA,mDAILd,EAAY,OACTc,EAAA,8BAEVA,EAAW,CACI3B,EAAAK,EAAYnH,EAAQyI,CAAS,EAC9C,MACF,CACID,IACFrB,EAAW,UAAYqB,GACnB,MAAAE,EAAyBN,EAAI,QAAQ,0BAA0B,EACrE,GAAIM,IAA2B,OAAQ,CACrC,GAAI,CAACjI,EAAmB,CAELqG,EAAAK,EAAYnH,EADb,8EAC4B,EAC5C,MACF,CACI,IAAAC,EACA,GAAA,CACFA,EAAaqF,GAAQoD,CAAsB,OAC/B,CAEK5B,EAAAK,EAAYnH,EADb,yCAC4B,EAC5C,MACF,CACM,MAAA2I,EAAiB,OAAO,KAAK1I,CAAU,EAC7C,GAAI0I,EAAe,SAAW,GAAKA,EAAe,CAAC,IAAMnE,EAAoB,cAAe,CAEzEsC,EAAAK,EAAYnH,EADb,sDAC4B,EAC5C,MACF,CACI,GAAA,CACFS,EAAkB,OAAOR,EAAWuE,EAAoB,aAAa,CAAC,OAC1D,CAEKsC,EAAAK,EAAYnH,EADb,yCAC4B,EAC5C,MACF,CACWmH,EAAA,YAAY3C,EAAoB,aAAa,EAAI/D,CAC9D,CACW0G,EAAA,UAAUnH,EAAQkG,EAAM,CACjC,aAAc5K,EAAK,aACnB,WAAYA,EAAK,WACjB,mBAAoBA,EAAK,kBAAA,CAC1B,CAAA,CACF,EACGA,EAAK,cACFA,EAAA,cAAc0M,EAAKb,CAAU,EAElCa,EAAI,IAAI,CAEZ,CACA,SAASR,GAAkBL,EAAYtU,EAAK,CAC1CsU,EAAW,YAActB,EAAY,QAC1BsB,EAAA,KAAK,QAAStU,CAAG,EAC5BsU,EAAW,UAAU,CACvB,CACA,SAASU,GAAW7U,EAAS,CAC3B,OAAAA,EAAQ,KAAOA,EAAQ,WAChBgR,GAAI,QAAQhR,CAAO,CAC5B,CACA,SAAS4U,GAAW5U,EAAS,CAC3B,OAAAA,EAAQ,KAAO,OACX,CAACA,EAAQ,YAAcA,EAAQ,aAAe,KAChDA,EAAQ,WAAagR,GAAI,KAAKhR,EAAQ,IAAI,EAAI,GAAKA,EAAQ,MAEtDkR,GAAI,QAAQlR,CAAO,CAC5B,CACA,SAAS8T,EAAiBK,EAAYzU,EAASsM,EAAS,CACtDmI,EAAW,YAActB,EAAY,QAC/B,MAAAhT,EAAM,IAAI,MAAMmM,CAAO,EACvB,MAAA,kBAAkBnM,EAAKiU,CAAgB,EACzCpU,EAAQ,WACVA,EAAQ+S,EAAQ,EAAI,GACpB/S,EAAQ,MAAM,EACVA,EAAQ,QAAU,CAACA,EAAQ,OAAO,WACpCA,EAAQ,OAAO,UAET,QAAA,SAAS8U,GAAmBL,EAAYtU,CAAG,IAEnDH,EAAQ,QAAQG,CAAG,EACnBH,EAAQ,KAAK,QAASyU,EAAW,KAAK,KAAKA,EAAY,OAAO,CAAC,EAC/DzU,EAAQ,KAAK,QAASyU,EAAW,UAAU,KAAKA,CAAU,CAAC,EAE/D,CACA,SAASJ,GAAeI,EAAY9T,EAAMkL,EAAI,CAC5C,GAAIlL,EAAM,CACF,MAAAqF,EAAS6M,GAASlS,CAAI,EAAE,OAC1B8T,EAAW,QACbA,EAAW,QAAQ,gBAAkBzO,EAErCyO,EAAW,iBAAmBzO,CAClC,CACA,GAAI6F,EAAI,CACN,MAAM1L,EAAM,IAAI,MACd,qCAAqCsU,EAAW,UAAU,KAAKxB,EAAYwB,EAAW,UAAU,CAAC,GAAA,EAE3F,QAAA,SAAS5I,EAAI1L,CAAG,CAC1B,CACF,CACA,SAASuT,GAAmBrJ,EAAM6L,EAAQ,CAClC,MAAAzB,EAAa,KAAKlC,CAAY,EACpCkC,EAAW,oBAAsB,GACjCA,EAAW,cAAgByB,EAC3BzB,EAAW,WAAapK,EACpBoK,EAAW,QAAQlC,CAAY,IAAM,SAE9BkC,EAAA,QAAQ,eAAe,OAAQR,EAAY,EAC9C,QAAA,SAASkC,GAAQ1B,EAAW,OAAO,EACvCpK,IAAS,KACXoK,EAAW,MAAM,EAENA,EAAA,MAAMpK,EAAM6L,CAAM,EACjC,CACA,SAASvC,IAAkB,CACnB,MAAAc,EAAa,KAAKlC,CAAY,EAC/BkC,EAAW,UACdA,EAAW,QAAQ,QACvB,CACA,SAASb,GAAgBzT,EAAK,CACtB,MAAAsU,EAAa,KAAKlC,CAAY,EAChCkC,EAAW,QAAQlC,CAAY,IAAM,SAC5BkC,EAAA,QAAQ,eAAe,OAAQR,EAAY,EAC9C,QAAA,SAASkC,GAAQ1B,EAAW,OAAO,EAChCA,EAAA,MAAMtU,EAAImS,EAAW,CAAC,GAExBmC,EAAA,KAAK,QAAStU,CAAG,CAC9B,CACA,SAASiW,IAAmB,CACrB,KAAA7D,CAAY,EAAE,WACrB,CACA,SAASsB,GAAkBlT,EAAMD,EAAU,CACzC,KAAK6R,CAAY,EAAE,KAAK,UAAW5R,EAAMD,CAAQ,CACnD,CACA,SAASoT,GAAenT,EAAM,CACtB,MAAA8T,EAAa,KAAKlC,CAAY,EACpCkC,EAAW,KAAK9T,EAAM,CAAC8T,EAAW,UAAWjC,EAAI,EACtCiC,EAAA,KAAK,OAAQ9T,CAAI,CAC9B,CACA,SAASoT,GAAepT,EAAM,CAC5B,KAAK4R,CAAY,EAAE,KAAK,OAAQ5R,CAAI,CACtC,CACA,SAASwV,GAAOnW,EAAS,CACvBA,EAAQ,OAAO,CACjB,CACA,SAASgU,IAAgB,CACjB,MAAAS,EAAa,KAAKlC,CAAY,EAC/B,KAAA,eAAe,QAASyB,EAAa,EACrC,KAAA,eAAe,OAAQC,EAAY,EACnC,KAAA,eAAe,MAAOC,EAAW,EACtCO,EAAW,YAActB,EAAY,QACjC,IAAApS,EACA,CAAC,KAAK,eAAe,YAAc,CAAC0T,EAAW,qBAAuB,CAACA,EAAW,UAAU,eAAe,eAAiB1T,EAAQ0T,EAAW,QAAQ,UAAY,MAC1JA,EAAA,UAAU,MAAM1T,CAAK,EAElC0T,EAAW,UAAU,MACrB,KAAKlC,CAAY,EAAI,OACrB,aAAakC,EAAW,WAAW,EAC/BA,EAAW,UAAU,eAAe,UAAYA,EAAW,UAAU,eAAe,aACtFA,EAAW,UAAU,GAEVA,EAAA,UAAU,GAAG,QAAS2B,EAAgB,EACtC3B,EAAA,UAAU,GAAG,SAAU2B,EAAgB,EAEtD,CACA,SAASnC,GAAalT,EAAO,CACtB,KAAKwR,CAAY,EAAE,UAAU,MAAMxR,CAAK,GAC3C,KAAK,MAAM,CAEf,CACA,SAASmT,IAAc,CACf,MAAAO,EAAa,KAAKlC,CAAY,EACpCkC,EAAW,YAActB,EAAY,QACrCsB,EAAW,UAAU,MACrB,KAAK,IAAI,CACX,CACA,SAASN,IAAkB,CACnB,MAAAM,EAAa,KAAKlC,CAAY,EAC/B,KAAA,eAAe,QAAS4B,EAAe,EACvC,KAAA,GAAG,QAAS3B,EAAI,EACjBiC,IACFA,EAAW,YAActB,EAAY,QACrC,KAAK,QAAQ,EAEjB,CACM,MAAAkD,MAAsD7B,EAAS,EAC/D,CAAE,WAAA8B,EAAe,EAAA7L,GACvB,SAAS8L,GAAMpG,EAAQ,CACf,MAAAmD,MAAgC,IACtC,IAAI7C,EAAQ,GACRC,EAAM,GACN,EAAI,EACR,IAAK,EAAG,EAAIP,EAAO,OAAQ,IAAK,CACxB,MAAA9F,EAAO8F,EAAO,WAAW,CAAC,EAChC,GAAIO,IAAQ,IAAM4F,GAAWjM,CAAI,IAAM,EACjCoG,IAAU,KACJA,EAAA,WACD,IAAM,IAAMpG,IAAS,IAAMA,IAAS,GACzCqG,IAAQ,IAAMD,IAAU,KACpBC,EAAA,WACCrG,IAAS,GAAI,CACtB,GAAIoG,IAAU,GACZ,MAAM,IAAI,YAAY,iCAAiC,CAAC,EAAE,EAExDC,IAAQ,KACJA,EAAA,GACR,MAAM8F,EAAYrG,EAAO,MAAMM,EAAOC,CAAG,EACrC,GAAA4C,EAAU,IAAIkD,CAAS,EACzB,MAAM,IAAI,YAAY,QAAQA,CAAS,6BAA6B,EAEtElD,EAAU,IAAIkD,CAAS,EACvB/F,EAAQC,EAAM,EAAA,KAEd,OAAM,IAAI,YAAY,iCAAiC,CAAC,EAAE,CAE9D,CACI,GAAAD,IAAU,IAAMC,IAAQ,GACpB,MAAA,IAAI,YAAY,yBAAyB,EAEjD,MAAM0E,EAAWjF,EAAO,MAAMM,EAAO,CAAC,EAClC,GAAA6C,EAAU,IAAI8B,CAAQ,EACxB,MAAM,IAAI,YAAY,QAAQA,CAAQ,6BAA6B,EAErE,OAAA9B,EAAU,IAAI8B,CAAQ,EACf9B,CACT,CACA,IAAImD,GAAgB,CAAE,MAAAF,IACtB,MAAMG,GAAezF,EACf0F,GAAOtF,EACP,CAAE,WAAAuF,EAAe,EAAAhK,EACjBiK,GAAY9F,GACZ+F,EAAqBnN,GACrBoN,GAAcN,GACdO,GAAaxC,GACb,CAAE,KAAAyC,GAAM,WAAAC,EAAe,EAAA9V,EACvB+V,GAAW,wBACXC,GAAU,EACVC,GAAU,EACVC,GAAS,EACf,MAAMC,WAAwBb,EAAa,CA2BzC,YAAYpW,EAASM,EAAU,CAmB7B,GAlBM,QACIN,EAAA,CACR,WAAY,IAAM,KAAO,KACzB,mBAAoB,GACpB,kBAAmB,GACnB,gBAAiB,KACjB,eAAgB,GAChB,aAAc,KACd,SAAU,GACV,QAAS,KAET,OAAQ,KACR,KAAM,KACN,KAAM,KACN,KAAM,KACN,UAAW0W,GACX,GAAG1W,CAAA,EAEDA,EAAQ,MAAQ,MAAQ,CAACA,EAAQ,QAAU,CAACA,EAAQ,UAAYA,EAAQ,MAAQ,OAASA,EAAQ,QAAUA,EAAQ,WAAaA,EAAQ,QAAUA,EAAQ,SAC5J,MAAM,IAAI,UACR,mFAAA,EAqBJ,GAlBIA,EAAQ,MAAQ,MAClB,KAAK,QAAUqW,GAAK,aAAa,CAACrB,EAAKI,IAAQ,CACvC,MAAA8B,EAAOb,GAAK,aAAa,GAAG,EAClCjB,EAAI,UAAU,IAAK,CACjB,iBAAkB8B,EAAK,OACvB,eAAgB,YAAA,CACjB,EACD9B,EAAI,IAAI8B,CAAI,CAAA,CACb,EACD,KAAK,QAAQ,OACXlX,EAAQ,KACRA,EAAQ,KACRA,EAAQ,QACRM,CAAA,GAEON,EAAQ,SACjB,KAAK,QAAUA,EAAQ,QAErB,KAAK,QAAS,CAChB,MAAMmX,EAAiB,KAAK,KAAK,KAAK,KAAM,YAAY,EACnD,KAAA,iBAAmBC,GAAa,KAAK,QAAS,CACjD,UAAW,KAAK,KAAK,KAAK,KAAM,WAAW,EAC3C,MAAO,KAAK,KAAK,KAAK,KAAM,OAAO,EACnC,QAAS,CAACpC,EAAKhI,EAAQkG,IAAS,CAC9B,KAAK,cAAc8B,EAAKhI,EAAQkG,EAAMiE,CAAc,CACtD,CAAA,CACD,CACH,CACInX,EAAQ,oBAAsB,KAChCA,EAAQ,kBAAoB,IAC1BA,EAAQ,iBACL,KAAA,YAA8B,IACnC,KAAK,iBAAmB,IAE1B,KAAK,QAAUA,EACf,KAAK,OAAS8W,EAChB,CAUA,SAAU,CACJ,GAAA,KAAK,QAAQ,SACT,MAAA,IAAI,MAAM,4CAA4C,EAE9D,OAAK,KAAK,QAEH,KAAK,QAAQ,UADX,IAEX,CAQA,MAAMvL,EAAI,CACJ,GAAA,KAAK,SAAWyL,GAAQ,CACtBzL,GACG,KAAA,KAAK,QAAS,IAAM,CACpBA,EAAA,IAAI,MAAM,2BAA2B,CAAC,CAAA,CAC1C,EAEK,QAAA,SAAS8L,GAAW,IAAI,EAChC,MACF,CAGA,GAFI9L,GACG,KAAA,KAAK,QAASA,CAAE,EACnB,KAAK,SAAWwL,GAGpB,GADA,KAAK,OAASA,GACV,KAAK,QAAQ,UAAY,KAAK,QAAQ,OACpC,KAAK,UACP,KAAK,iBAAiB,EACjB,KAAA,iBAAmB,KAAK,QAAU,MAErC,KAAK,QACF,KAAK,QAAQ,KAGhB,KAAK,iBAAmB,GAFhB,QAAA,SAASM,GAAW,IAAI,EAK1B,QAAA,SAASA,GAAW,IAAI,MAE7B,CACL,MAAMC,EAAS,KAAK,QACpB,KAAK,iBAAiB,EACjB,KAAA,iBAAmB,KAAK,QAAU,KACvCA,EAAO,MAAM,IAAM,CACjBD,GAAU,IAAI,CAAA,CACf,CACH,CACF,CAQA,aAAarC,EAAK,CACZ,GAAA,KAAK,QAAQ,KAAM,CACrB,MAAMuC,EAAQvC,EAAI,IAAI,QAAQ,GAAG,EAE7B,IADauC,IAAU,GAAKvC,EAAI,IAAI,MAAM,EAAGuC,CAAK,EAAIvC,EAAI,OAC7C,KAAK,QAAQ,KACrB,MAAA,EACX,CACO,MAAA,EACT,CAWA,cAAcA,EAAKhI,EAAQkG,EAAM3H,EAAI,CAC5ByB,EAAA,GAAG,QAASwK,EAAa,EAC1B,MAAA/O,EAAMuM,EAAI,QAAQ,mBAAmB,EACrCyC,EAAU,CAACzC,EAAI,QAAQ,uBAAuB,EAChD,GAAAA,EAAI,SAAW,MAAO,CAExB0C,EAAkC,KAAM1C,EAAKhI,EAAQ,IADrC,qBACiD,EACjE,MACF,CACA,GAAIgI,EAAI,QAAQ,QAAQ,YAAA,IAAkB,YAAa,CAErD0C,EAAkC,KAAM1C,EAAKhI,EAAQ,IADrC,wBACiD,EACjE,MACF,CACA,GAAI,CAACvE,GAAO,CAACoO,GAAS,KAAKpO,CAAG,EAAG,CAE/BiP,EAAkC,KAAM1C,EAAKhI,EAAQ,IADrC,6CACiD,EACjE,MACF,CACI,GAAAyK,IAAY,GAAKA,IAAY,GAAI,CAEnCC,EAAkC,KAAM1C,EAAKhI,EAAQ,IADrC,iDACiD,EACjE,MACF,CACA,GAAI,CAAC,KAAK,aAAagI,CAAG,EAAG,CAC3B2C,GAAe3K,EAAQ,GAAG,EAC1B,MACF,CACM,MAAA4K,EAAuB5C,EAAI,QAAQ,wBAAwB,EAC7D,IAAAhC,MAAgC,IACpC,GAAI4E,IAAyB,OACvB,GAAA,CACU5E,EAAAyD,GAAY,MAAMmB,CAAoB,OACtC,CAEZF,EAAkC,KAAM1C,EAAKhI,EAAQ,IADrC,uCACiD,EACjE,MACF,CAEI,MAAA0I,EAAyBV,EAAI,QAAQ,0BAA0B,EAC/D/H,EAAa,CAAA,EACnB,GAAI,KAAK,QAAQ,mBAAqByI,IAA2B,OAAQ,CACvE,MAAMjI,EAAoB,IAAI+I,EAC5B,KAAK,QAAQ,kBACb,GACA,KAAK,QAAQ,UAAA,EAEX,GAAA,CACI,MAAAnO,EAASkO,GAAU,MAAMb,CAAsB,EACjDrN,EAAOmO,EAAmB,aAAa,IACzC/I,EAAkB,OAAOpF,EAAOmO,EAAmB,aAAa,CAAC,EACtDvJ,EAAAuJ,EAAmB,aAAa,EAAI/I,QAErC,CAEZiK,EAAkC,KAAM1C,EAAKhI,EAAQ,IADrC,yDACiD,EACjE,MACF,CACF,CACI,GAAA,KAAK,QAAQ,aAAc,CAC7B,MAAM6K,EAAO,CACX,OAAQ7C,EAAI,QAAQ,GAAGyC,IAAY,EAAI,uBAAyB,QAAQ,EAAE,EAC1E,OAAQ,CAAC,EAAEzC,EAAI,OAAO,YAAcA,EAAI,OAAO,WAC/C,IAAAA,CAAA,EAEF,GAAI,KAAK,QAAQ,aAAa,SAAW,EAAG,CAC1C,KAAK,QAAQ,aAAa6C,EAAM,CAACC,EAAU/N,EAAMiC,EAASiJ,IAAY,CACpE,GAAI,CAAC6C,EACH,OAAOH,GAAe3K,EAAQjD,GAAQ,IAAKiC,EAASiJ,CAAO,EAExD,KAAA,gBACHhI,EACAxE,EACAuK,EACAgC,EACAhI,EACAkG,EACA3H,CAAA,CACF,CACD,EACD,MACF,CACA,GAAI,CAAC,KAAK,QAAQ,aAAasM,CAAI,EAC1B,OAAAF,GAAe3K,EAAQ,GAAG,CACrC,CACA,KAAK,gBAAgBC,EAAYxE,EAAKuK,EAAWgC,EAAKhI,EAAQkG,EAAM3H,CAAE,CACxE,CAeA,gBAAgB0B,EAAYxE,EAAKuK,EAAWgC,EAAKhI,EAAQkG,EAAM3H,EAAI,CACjE,GAAI,CAACyB,EAAO,UAAY,CAACA,EAAO,SAC9B,OAAOA,EAAO,UACZ,GAAAA,EAAO4J,EAAU,EACnB,MAAM,IAAI,MACR,2GAAA,EAGJ,GAAI,KAAK,OAASE,GACT,OAAAa,GAAe3K,EAAQ,GAAG,EAEnC,MAAMiI,EAAU,CACd,mCACA,qBACA,sBACA,yBALaqB,GAAW,MAAM,EAAE,OAAO7N,EAAMkO,EAAI,EAAE,OAAO,QAAQ,CAKnC,EAAA,EAE3B5W,EAAK,IAAI,KAAK,QAAQ,UAAU,IAAI,EAC1C,GAAIiT,EAAU,KAAM,CAClB,MAAM8B,EAAW,KAAK,QAAQ,gBAAkB,KAAK,QAAQ,gBAAgB9B,EAAWgC,CAAG,EAAIhC,EAAU,OAAO,EAAE,KAAO,EAAA,MACrH8B,IACMG,EAAA,KAAK,2BAA2BH,CAAQ,EAAE,EAClD/U,EAAG,UAAY+U,EAEnB,CACI,GAAA7H,EAAWuJ,EAAmB,aAAa,EAAG,CAChD,MAAMrO,EAAS8E,EAAWuJ,EAAmB,aAAa,EAAE,OACtD9N,EAAQ6N,GAAU,OAAO,CAC7B,CAACC,EAAmB,aAAa,EAAG,CAACrO,CAAM,CAAA,CAC5C,EACO8M,EAAA,KAAK,6BAA6BvM,CAAK,EAAE,EACjD3I,EAAG,YAAckN,CACnB,CACK,KAAA,KAAK,UAAWgI,EAASD,CAAG,EACjChI,EAAO,MAAMiI,EAAQ,OAAO;AAAA,CAAM,EAAE,KAAK;AAAA,CAAM,CAAC,EACzCjI,EAAA,eAAe,QAASwK,EAAa,EACzCzX,EAAA,UAAUiN,EAAQkG,EAAM,CACzB,WAAY,KAAK,QAAQ,WACzB,mBAAoB,KAAK,QAAQ,kBAAA,CAClC,EACG,KAAK,UACF,KAAA,QAAQ,IAAInT,CAAE,EAChBA,EAAA,GAAG,QAAS,IAAM,CACd,KAAA,QAAQ,OAAOA,CAAE,EAClB,KAAK,kBAAoB,CAAC,KAAK,QAAQ,MACjC,QAAA,SAASsX,GAAW,IAAI,CAClC,CACD,GAEH9L,EAAGxL,EAAIiV,CAAG,CACZ,CACF,CACA,IAAI+C,GAAkBd,GACtB,SAASG,GAAaE,EAAQU,EAAK,CACtB,UAAA7I,KAAS,OAAO,KAAK6I,CAAG,EACjCV,EAAO,GAAGnI,EAAO6I,EAAI7I,CAAK,CAAC,EAC7B,OAAO,UAA2B,CAChC,UAAWA,KAAS,OAAO,KAAK6I,CAAG,EACjCV,EAAO,eAAenI,EAAO6I,EAAI7I,CAAK,CAAC,CACzC,CAEJ,CACA,SAASkI,GAAUC,EAAQ,CACzBA,EAAO,OAASN,GAChBM,EAAO,KAAK,OAAO,CACrB,CACA,SAASE,IAAgB,CACvB,KAAK,QAAQ,CACf,CACA,SAASG,GAAe3K,EAAQjD,EAAMiC,EAASiJ,EAAS,CAC5CjJ,EAAAA,GAAWqK,GAAK,aAAatM,CAAI,EACjCkL,EAAA,CACR,WAAY,QACZ,eAAgB,YAChB,iBAAkB,OAAO,WAAWjJ,CAAO,EAC3C,GAAGiJ,CAAA,EAEEjI,EAAA,KAAK,SAAUA,EAAO,OAAO,EAC7BA,EAAA,IACL,YAAYjD,CAAI,IAAIsM,GAAK,aAAatM,CAAI,CAAC;AAAA,EAC3C,OAAO,KAAKkL,CAAO,EAAE,IAAKgD,GAAM,GAAGA,CAAC,KAAKhD,EAAQgD,CAAC,CAAC,EAAE,EAAE,KAAK;AAAA,CAAM,EAAI;AAAA;AAAA,EAAajM,CAAA,CAEvF,CACA,SAAS0L,EAAkCJ,EAAQtC,EAAKhI,EAAQjD,EAAMiC,EAAS,CACzE,GAAAsL,EAAO,cAAc,eAAe,EAAG,CACnC,MAAAzX,EAAM,IAAI,MAAMmM,CAAO,EACvB,MAAA,kBAAkBnM,EAAK6X,CAAiC,EAC9DJ,EAAO,KAAK,gBAAiBzX,EAAKmN,EAAQgI,CAAG,CAAA,MAE9B2C,GAAA3K,EAAQjD,EAAMiC,CAAO,CAExC,CACM,MAAAkM,MAA4DH,EAAe"}