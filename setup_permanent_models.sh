#!/bin/bash

# PosterCraft 永久模型存储设置脚本
# 解决HuggingFace缓存清理导致模型丢失的问题

echo "🎨 PosterCraft 永久模型存储设置"
echo "=================================="

# 1. 创建永久模型存储目录
PERMANENT_MODEL_DIR="$HOME/AI_Models/PosterCraft"
echo "📁 创建永久模型目录: $PERMANENT_MODEL_DIR"
mkdir -p "$PERMANENT_MODEL_DIR"

# 2. 设置HuggingFace缓存目录环境变量
echo "🔧 设置环境变量..."
export HF_HOME="$PERMANENT_MODEL_DIR"
export HUGGINGFACE_HUB_CACHE="$PERMANENT_MODEL_DIR"
export TRANSFORMERS_CACHE="$PERMANENT_MODEL_DIR"

# 3. 将环境变量写入配置文件
echo "📝 写入环境变量到 ~/.zshrc"
cat >> ~/.zshrc << 'EOF'

# PosterCraft 永久模型存储配置
export HF_HOME="$HOME/AI_Models/PosterCraft"
export HUGGINGFACE_HUB_CACHE="$HOME/AI_Models/PosterCraft"
export TRANSFORMERS_CACHE="$HOME/AI_Models/PosterCraft"
EOF

# 4. 检查现有模型
echo "🔍 检查现有模型..."
CACHE_DIR="$HOME/.cache/huggingface/hub"
if [ -d "$CACHE_DIR" ]; then
    echo "发现现有HuggingFace缓存目录"
    
    # 移动现有模型到永久位置
    if [ -d "$CACHE_DIR/models--black-forest-labs--FLUX.1-dev" ]; then
        echo "📦 移动 FLUX.1-dev 模型..."
        mv "$CACHE_DIR/models--black-forest-labs--FLUX.1-dev" "$PERMANENT_MODEL_DIR/"
    fi
    
    if [ -d "$CACHE_DIR/models--Qwen--Qwen3-8B" ]; then
        echo "📦 移动 Qwen3-8B 模型..."
        mv "$CACHE_DIR/models--Qwen--Qwen3-8B" "$PERMANENT_MODEL_DIR/"
    fi
    
    if [ -d "$CACHE_DIR/models--Qwen--Qwen2.5-3B" ]; then
        echo "📦 移动 Qwen2.5-3B 模型..."
        mv "$CACHE_DIR/models--Qwen--Qwen2.5-3B" "$PERMANENT_MODEL_DIR/"
    fi
    
    if [ -d "$CACHE_DIR/models--black-forest-labs--FLUX.1-schnell" ]; then
        echo "📦 移动 FLUX.1-schnell 模型..."
        mv "$CACHE_DIR/models--black-forest-labs--FLUX.1-schnell" "$PERMANENT_MODEL_DIR/"
    fi
fi

# 5. 创建PosterCraft配置文件
echo "⚙️  创建PosterCraft配置文件..."
cat > "$PERMANENT_MODEL_DIR/postercraft_config.py" << 'EOF'
"""
PosterCraft 永久模型配置
使用本地模型路径，避免缓存清理问题
"""
import os

# 永久模型存储路径
MODEL_BASE_PATH = os.path.expanduser("~/AI_Models/PosterCraft")

# 模型路径配置
MODEL_PATHS = {
    "flux_dev": f"{MODEL_BASE_PATH}/models--black-forest-labs--FLUX.1-dev",
    "flux_schnell": f"{MODEL_BASE_PATH}/models--black-forest-labs--FLUX.1-schnell", 
    "qwen3_8b": f"{MODEL_BASE_PATH}/models--Qwen--Qwen3-8B",
    "qwen2_5_3b": f"{MODEL_BASE_PATH}/models--Qwen--Qwen2.5-3B",
    "postercraft_v1_rl": "PosterCraft/PosterCraft-v1_RL"  # 这个仍需从HF下载
}

def get_model_path(model_name):
    """获取模型路径"""
    return MODEL_PATHS.get(model_name, model_name)

def setup_environment():
    """设置环境变量"""
    os.environ["HF_HOME"] = MODEL_BASE_PATH
    os.environ["HUGGINGFACE_HUB_CACHE"] = MODEL_BASE_PATH
    os.environ["TRANSFORMERS_CACHE"] = MODEL_BASE_PATH
    print(f"✅ 环境变量已设置，模型存储路径: {MODEL_BASE_PATH}")
EOF

# 6. 显示结果
echo ""
echo "✅ 设置完成！"
echo ""
echo "📊 配置摘要:"
echo "  • 永久模型目录: $PERMANENT_MODEL_DIR"
echo "  • 环境变量已添加到 ~/.zshrc"
echo "  • 现有模型已移动到永久位置"
echo ""
echo "🔄 请运行以下命令重新加载环境:"
echo "  source ~/.zshrc"
echo ""
echo "💡 优势:"
echo "  • 模型不会因清理缓存而丢失"
echo "  • 可以在多个项目间共享模型"
echo "  • 便于管理和备份"
echo ""
echo "📁 查看已安装的模型:"
echo "  ls -la $PERMANENT_MODEL_DIR"
