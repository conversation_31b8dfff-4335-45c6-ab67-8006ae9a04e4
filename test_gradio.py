#!/usr/bin/env python3
"""
Test Gradio interface to verify basic functionality
"""

import gradio as gr
import torch
import sys
import os

def test_basic_functionality():
    """Test basic system functionality"""
    info = []
    info.append(f"Python version: {sys.version}")
    info.append(f"PyTorch version: {torch.__version__}")
    info.append(f"CUDA available: {torch.cuda.is_available()}")
    info.append(f"Current working directory: {os.getcwd()}")
    
    try:
        import diffusers
        info.append(f"Diffusers version: {diffusers.__version__}")
    except ImportError:
        info.append("Diffusers: Not available")
    
    try:
        import transformers
        info.append(f"Transformers version: {transformers.__version__}")
    except ImportError:
        info.append("Transformers: Not available")
    
    return "\n".join(info)

def simple_text_processor(text):
    """Simple text processing function"""
    if not text:
        return "Please enter some text!"
    
    return f"You entered: {text}\nLength: {len(text)} characters"

def create_test_interface():
    """Create a simple test interface"""
    with gr.Blocks(title="PosterCraft Test") as demo:
        gr.Markdown("# 🧪 PosterCraft System Test")
        gr.Markdown("Testing basic functionality before launching the full application")
        
        with gr.Tab("System Info"):
            info_btn = gr.Button("Get System Information")
            info_output = gr.Textbox(label="System Information", lines=10)
            info_btn.click(test_basic_functionality, outputs=info_output)
        
        with gr.Tab("Text Test"):
            text_input = gr.Textbox(label="Enter some text", placeholder="Type something here...")
            text_btn = gr.Button("Process Text")
            text_output = gr.Textbox(label="Output")
            text_btn.click(simple_text_processor, inputs=text_input, outputs=text_output)
    
    return demo

if __name__ == "__main__":
    print("🧪 Starting PosterCraft test interface...")
    demo = create_test_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=True,
        show_error=True
    )
